#pragma once

#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoTableGroupByMixer : public CommonRecoBaseMixer {
 public:
  CommonRecoTableGroupByMixer() {}
  ~CommonRecoTableGroupByMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    from_table_name_ = config()->GetString("from_table", "");
    to_table_name_ = config()->GetString("to_table", "");
    key_attr_ = config()->GetString("by_attr", "");
    reason_ = config()->GetInt("reason", 0);

    auto copy_attrs = config()->Get("copy_attrs");
    if (!RecoUtil::ParseAttrsConfig(copy_attrs, &copy_attrs_)) {
      return false;
    }

    save_key_as_ = config()->GetString("concat_source_table_key_as", "");
    return true;
  }

 private:
  std::string from_table_name_;
  std::string to_table_name_;
  std::string key_attr_;
  std::string save_key_as_;
  int reason_ = 0;
  folly::F14FastMap<std::string, AttrConfig> copy_attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTableGroupByMixer);
};

}  // namespace platform
}  // namespace ks
