#include "dragon/src/processor/common/mixer/common_reco_table_join_mixer.h"

namespace ks {
namespace platform {

void CommonRecoTableJoinMixer::Mix(AddibleRecoContextInterface *context) {
  auto left_table = context->GetOrInsertDataTable(left_table_name_);
  auto right_table = context->GetOrInsertDataTable(right_table_name_);

  auto left_results = left_table->GetCommonRecoResults();
  auto right_results = right_table->GetCommonRecoResults();

  AttrValue *left_on_attr = nullptr;
  AttrValue *right_on_attr = nullptr;
  if (!join_attr_left_.empty()) {
    left_on_attr = left_table->GetOrInsertAttr(join_attr_left_);
  }
  if (!join_attr_right_.empty()) {
    right_on_attr = right_table->GetOrInsertAttr(join_attr_right_);
  }

  // 检查 on_attr 类型合法性
  bool left_attr_is_list = false;
  bool right_attr_is_list = false;
  if (left_on_attr) {
    switch (left_on_attr->value_type) {
      case AttrType::INT:
      case AttrType::STRING:
        break;
      case AttrType::INT_LIST:
      case AttrType::STRING_LIST:
        left_attr_is_list = true;
        break;
      default:
        CL_LOG_ERROR("table_join", "type_error")
            << "Unsupport join on attr type ! " << (int)left_on_attr->value_type;
        return;
    }
  }
  if (right_on_attr) {
    switch (right_on_attr->value_type) {
      case AttrType::INT:
      case AttrType::STRING:
        break;
      case AttrType::INT_LIST:
      case AttrType::STRING_LIST:
        right_attr_is_list = true;
        break;
      default:
        CL_LOG_ERROR("table_join", "type_error")
            << "Unsupport join on attr type ! " << (int)right_on_attr->value_type;
        return;
    }
  }

  if (left_attr_is_list && right_attr_is_list) {
    CL_LOG_ERROR("table_join", "list_error") << "Cannot join on list and list ! ";
    return;
  }

  // 记录匹配的行
  // 当 left_on_attr 是 list 类型时，copy_results 大小和 left_results 大小一致，copy_results[i] 保存
  // left_results[i] 匹配到的 right_results；
  // 当 right_on_attr 是 list 类型、或者 left_on_attr 和 right_on_attr 均为单值类型时， copy_results 大小和
  // right_results 大小一致，copy_results[i] 保存 right_results[i] 匹配到的 left_results；
  std::vector<std::vector<CommonRecoResult *>> copy_results;

  absl::flat_hash_map<absl::string_view, std::vector<CommonRecoResult *>> string_item_map;
  absl::flat_hash_map<int64, std::vector<CommonRecoResult *>> int_item_map;
  if (left_attr_is_list) {
    copy_results.resize(left_results.size());
    int_item_map.reserve(right_results.size());
    string_item_map.reserve(right_results.size());

    for (auto &right_result : right_results) {
      if (!right_on_attr) {
        int_item_map[right_result.item_key].push_back(&right_result);
      } else if (right_on_attr->value_type == AttrType::INT) {
        auto p = context->GetIntItemAttr(right_result, right_on_attr);
        if (p) {
          int_item_map[p.value()].push_back(&right_result);
        }
      } else {
        auto p = context->GetStringItemAttr(right_result, right_on_attr);
        if (p) {
          string_item_map[p.value()].push_back(&right_result);
        }
      }
    }

    for (int i = 0; i < left_results.size(); i++) {
      auto &copy_result_list = copy_results[i];
      if (left_on_attr->value_type == AttrType::INT_LIST) {
        auto p = context->GetIntListItemAttr(left_results[i], left_on_attr);
        if (p) {
          std::for_each(p->begin(), p->end(), [&](int64 int_v) {
            for (CommonRecoResult *item : int_item_map[int_v]) {
              copy_result_list.push_back(item);
            }
          });
        }
      } else {
        auto p = context->GetStringListItemAttr(left_results[i], left_on_attr);
        if (p) {
          std::for_each(p->begin(), p->end(), [&](absl::string_view str_v) {
            for (CommonRecoResult *item : string_item_map[str_v]) {
              copy_result_list.push_back(item);
            }
          });
        }
      }
    }
  } else {
    copy_results.resize(right_results.size());
    int_item_map.reserve(left_results.size());
    string_item_map.reserve(left_results.size());

    for (auto &left_result : left_results) {
      if (!left_on_attr) {
        int_item_map[left_result.item_key].push_back(&left_result);
      } else if (left_on_attr->value_type == AttrType::INT) {
        auto p = context->GetIntItemAttr(left_result, left_on_attr);
        if (p) {
          int_item_map[p.value()].push_back(&left_result);
        }
      } else {
        auto p = context->GetStringItemAttr(left_result, left_on_attr);
        if (p) {
          string_item_map[p.value()].push_back(&left_result);
        }
      }
    }

    if (right_attr_is_list) {
      for (int i = 0; i < right_results.size(); i++) {
        auto &copy_result_list = copy_results[i];
        if (right_on_attr->value_type == AttrType::INT_LIST) {
          auto p = context->GetIntListItemAttr(right_results[i], right_on_attr);
          if (p) {
            std::for_each(p->begin(), p->end(), [&](int64 int_v) {
              for (CommonRecoResult *item : int_item_map[int_v]) {
                copy_result_list.push_back(item);
              }
            });
          }
        } else {
          auto p = context->GetStringListItemAttr(right_results[i], right_on_attr);
          if (p) {
            std::for_each(p->begin(), p->end(), [&](absl::string_view str_v) {
              for (CommonRecoResult *item : string_item_map[str_v]) {
                copy_result_list.push_back(item);
              }
            });
          }
        }
      }
    } else {
      for (int i = 0; i < right_results.size(); i++) {
        auto &copy_result_list = copy_results[i];
        if (!right_on_attr) {
          for (CommonRecoResult *item : int_item_map[right_results[i].item_key]) {
            copy_result_list.push_back(item);
          }
        } else if (right_on_attr->value_type == AttrType::INT) {
          auto p = context->GetIntItemAttr(right_results[i], right_on_attr);
          if (p) {
            for (CommonRecoResult *item : int_item_map[p.value()]) {
              copy_result_list.push_back(item);
            }
          }
        } else {
          auto p = context->GetStringItemAttr(right_results[i], right_on_attr);
          if (p) {
            for (CommonRecoResult *item : string_item_map[p.value()]) {
              copy_result_list.push_back(item);
            }
          }
        }
      }
    }
  }

  // 数据写入 left_table
  for (const auto &pr : copy_attrs_) {
    auto left_attr_accessor = left_table->GetOrInsertAttr(pr.second.alias);
    auto right_attr_accessor = right_table->GetOrInsertAttr(pr.first);
    for (int i = 0; i < copy_results.size(); i++) {
      const auto &copy_list = copy_results[i];
      for (auto result : copy_list) {
        if (left_attr_is_list) {
          CopyAttrValue(context, right_attr_accessor, *result, left_attr_accessor, left_results[i],
                        pr.second.copy_mode);
        } else {
          CopyAttrValue(context, right_attr_accessor, right_results[i], left_attr_accessor, *result,
                        pr.second.copy_mode);
        }
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTableJoinMixer, CommonRecoTableJoinMixer);

}  // namespace platform
}  // namespace ks
