#include "dragon/src/processor/common/mixer/common_reco_table_union_update_mixer.h"

namespace ks {
namespace platform {

void CommonRecoTableUnionUpdateMixer::Mix(AddibleRecoContextInterface *context) {
  auto from_table = context->GetOrInsertDataTable(from_table_name_);
  auto to_table = context->GetOrInsertDataTable(to_table_name_);

  auto target_accessor = from_table->GetOrInsertAttr(target_attr_);

  std::vector<CommonRecoResult> from_results;
  std::vector<CommonRecoResult> to_results;
  for (const auto &result : from_table->GetCommonRecoResults()) {
    auto target_value = context->GetIntItemAttr(result, target_accessor);
    if (target_attr_.empty() || (target_value && target_value.value())) {
      auto new_result =
          to_table->AddCommonRecoResult(result.item_key, result.reason, result.score, result.channel);
      to_results.emplace_back(new_result);
      from_results.emplace_back(result);
    }
  }

  for (const auto &pr : copy_attrs_) {
    auto from_attr_accessor = from_table->GetOrInsertAttr(pr.first);
    auto to_attr_accessor = to_table->GetOrInsertAttr(pr.second.alias);
    if (to_attr_accessor->IsReadOnly()) {
      continue;
    }
    if (pr.second.copy_mode == CopyMode::CONCAT) {
      to_attr_accessor->ConvertSingularValueToListValue();
    }
    CopyAttrValues(context, from_attr_accessor, from_results, to_attr_accessor, to_results,
                   pr.second.copy_mode);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTableUnionUpdateMixer, CommonRecoTableUnionUpdateMixer);

}  // namespace platform
}  // namespace ks
