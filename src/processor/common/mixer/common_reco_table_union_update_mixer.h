#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoTableUnionUpdateMixer : public CommonRecoBaseMixer {
 public:
  CommonRecoTableUnionUpdateMixer() {}
  ~CommonRecoTableUnionUpdateMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    from_table_name_ = config()->GetString("from_table", "");
    to_table_name_ = config()->GetString("to_table", "");
    target_attr_ = config()->GetString("if_attr", "");

    auto copy_attrs = config()->Get("copy_attrs");
    if (copy_attrs && copy_attrs->IsArray()) {
      for (const auto attr_config : copy_attrs->array()) {
        if (attr_config->IsString()) {
          auto attr_name = attr_config->StringValue();
          AttrConfig cfg;
          cfg.alias = attr_name;
          cfg.copy_mode = CopyMode::OVERWRITE;
          copy_attrs_.insert({attr_name, cfg});
        } else if (attr_config->IsObject()) {
          auto attr_name = attr_config->GetString("name", "");
          auto copy_mode = attr_config->GetString("copy_mode", "OVERWRITE");
          auto alias = attr_config->GetString("as", attr_name);
          AttrConfig cfg;
          cfg.alias = alias;
          cfg.copy_mode = TransCopyModeFromString(copy_mode);
          copy_attrs_.insert({attr_name, cfg});
          if (cfg.copy_mode == CopyMode::UNKNOWN) {
            LOG(ERROR) << "CommonRecoTableUnionUpdateMixer init failed! 'copy_mode' must be OVERWRITE, MAX "
                          "or CONCAT.";
            return false;
          }
        } else {
          LOG(ERROR) << "CommonRecoTableUnionUpdateMixer init failed! 'copy_attrs' must be list of string or "
                        "dict.";
          return false;
        }
      }
    }
    return true;
  }

 private:
  std::string from_table_name_;
  std::string to_table_name_;
  std::string target_attr_;
  folly::F14FastMap<std::string, AttrConfig> copy_attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTableUnionUpdateMixer);
};

}  // namespace platform
}  // namespace ks
