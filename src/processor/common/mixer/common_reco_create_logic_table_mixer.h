#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoCreateLogicTableMixer : public CommonRecoBaseMixer {
 public:
  CommonRecoCreateLogicTableMixer() {}
  ~CommonRecoCreateLogicTableMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    logic_table_name_ = config()->GetString("logic_table", "");
    RecoUtil::ExtractStringListFromJsonConfig(config()->Get("select_attr"), &attrs_);
    remove_selected_items_ = config()->GetBoolean("remove_selected_items", false);
    return true;
  }

 private:
  bool remove_selected_items_ = false;
  std::string from_table_name_;
  std::string logic_table_name_;
  std::vector<std::string> attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCreateLogicTableMixer);
};

}  // namespace platform
}  // namespace ks
