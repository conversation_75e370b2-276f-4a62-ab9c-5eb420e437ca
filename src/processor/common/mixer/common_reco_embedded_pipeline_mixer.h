#pragma once

#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace platform {

/**
 * 一个会根据配置执行嵌套 pipeline 的 Mixer，只用于单元测试中
 */
class CommonRecoEmbeddedPipelineMixer : public CommonRecoBaseMixerEmbedPipeline {
 public:
  CommonRecoEmbeddedPipelineMixer() {}
  ~CommonRecoEmbeddedPipelineMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoEmbeddedPipelineMixer);
};

}  // namespace platform
}  // namespace ks
