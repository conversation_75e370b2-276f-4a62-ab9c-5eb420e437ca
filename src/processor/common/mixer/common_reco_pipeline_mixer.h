#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/core/single_pipeline_executor.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/util/scope_exit.h"

DECLARE_bool(enable_pthread_affinity);

namespace ks {
namespace platform {

class CommonRecoPipelineMixer : public CommonRecoBaseMixer {
 public:
  CommonRecoPipelineMixer() {}
  ~CommonRecoPipelineMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;
  bool IsAsync() const override {
    return true;
  }
  void OnPipelineExit(ReadableRecoContextInterface *context) final {
    executor_.HandlePipelineExit();
  }

 private:
  bool InitProcessor() override {
    pipeline_name_ = config()->GetString("flow_name", "");
    if (pipeline_name_.empty()) {
      LOG(ERROR) << "CommonRecoPipelineMixer init failed! Missing 'flow_name' config";
      return false;
    }

    // 初始化 SinglePipelineExecutor
    base::Json *pipeline_manager_config =
        GlobalHolder::GetDynamicJsonConfig()->Get("pipeline_manager_config");
    if (!pipeline_manager_config) {
      LOG(ERROR) << "pipeline_manager_config is null, you should set user_static_flags: "
                 << "use_fake_json_config_first=true when using SampleServer, "
                 << "because GlobalHolder::GetDynamicJsonConfig()="
                 << base::JsonToString(GlobalHolder::GetDynamicJsonConfig()->get(), 2);
    }
    if (!executor_.Initialize(pipeline_manager_config, pipeline_name_)) {
      LOG(ERROR) << "CommonRecoPipelineMixer init failed! Pipeline " << pipeline_name_
                 << " initialize failed.";
      return false;
    }

    RecoUtil::ParseTableAttrs(config()->Get("input_tables"), &input_tables_);
    RecoUtil::ParseTableAttrs(config()->Get("enrich_tables"), &enrich_tables_);
    RecoUtil::ParseTableAttrs(config()->Get("retrieve_tables"), &retrieve_tables_);

    auto *copy_common_attrs = config()->Get("pass_common_attrs");
    RecoUtil::ExtractStringSetFromJsonConfig(copy_common_attrs, &copy_common_attrs_);

    if (!RecoUtil::ParseAttrsConfig(config()->Get("merge_common_attrs"), &return_common_attrs_)) {
      LOG(ERROR) << "CommonRecoPipelineMixer init failed! merge_common_attrs parse error.";
      return false;
    }

    fill_common_attrs_from_request_ = config()->GetBoolean("pass_common_attrs_in_request", true);
    fill_browse_set_from_request_ = config()->GetBoolean("pass_browse_set", true);
    merge_and_overwrite_ = config()->GetBoolean("merge_and_overwrite", false);

    deep_copy_ = config()->GetBoolean("deep_copy", false);
    SubFlowThreadPoolManager::GetInstance()->Resize(config()->GetInt("task_queue_id", 0) + 1);

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
    // NOTE(caohongjin): 危险！不建议使用
    // 注意：开启此选项后导致的线程安全问题由使用者负责（自行保证 input_tables 同时只有一个地方进行读写）
    // 开启后的效果:
    // 1. subflow 直接引用主 flow 的 input_table 指针, 业务方自行保证线程安全
    // 2. 浅拷贝模式下，copy_common_attrs_ 被清空，从而避免浅拷贝开销。 subflow 的 context 额外备份一份主 flow
    // 的 common_data_ 指针，读操作获取不到某个 common attr 时使用备份指针进行二次查找
    direct_access_parent_flow_data_ = config()->GetBoolean("direct_access_parent_flow_data", false);
    if (direct_access_parent_flow_data_) {
      if (!deep_copy_) {
        copy_common_attrs_.clear();
      }
    }
#endif
    return true;
  }

  void CloneContext(ReadableRecoContextInterface *context);

  void DereferenceAttrs(CommonRecoContext *context);

  folly::F14FastMap<std::string, AttrConfig> FillAttrConfigFromAttrs(const std::vector<std::string> &attrs);

 private:
  std::string common_attrs_in_list_;
  std::string pipeline_name_;
  std::vector<TableAttrs> input_tables_;
  std::vector<TableAttrs> enrich_tables_;
  std::vector<TableAttrs> retrieve_tables_;
  folly::F14FastSet<std::string> copy_common_attrs_;
  std::unordered_map<std::string, std::string> return_common_attrs_;
  bool fill_common_attrs_from_request_ = true;
  bool fill_browse_set_from_request_ = true;
  bool merge_and_overwrite_ = false;
  bool deep_copy_ = true;
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  bool direct_access_parent_flow_data_ = false;
#endif
  SinglePipelineExecutor executor_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPipelineMixer);
};

}  // namespace platform
}  // namespace ks
