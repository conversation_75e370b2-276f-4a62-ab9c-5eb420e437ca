#include "dragon/src/processor/common/mixer/common_reco_embedded_pipeline_mixer.h"

#include <string>

namespace ks {
namespace platform {

void CommonRecoEmbeddedPipelineMixer::Mix(AddibleRecoContextInterface *context) {
  auto pipelines_to_run = GetStringListProcessorParameter(context, "run_embedded_pipelines");
  for (const auto &name : pipelines_to_run) {
    CL_LOG(INFO) << "Run embedded pipeline: " << name;
    if (!RunEmbeddedPipeline(context, std::string(name))) {
      CL_LOG(INFO) << "Run embedded pipeline " << name << " returned false";
      return;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoEmbeddedPipelineMixer, CommonRecoEmbeddedPipelineMixer);

}  // namespace platform
}  // namespace ks
