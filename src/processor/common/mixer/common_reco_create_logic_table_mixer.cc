#include "dragon/src/processor/common/mixer/common_reco_create_logic_table_mixer.h"

namespace ks {
namespace platform {

void CommonRecoCreateLogicTableMixer::Mix(AddibleRecoContextInterface *context) {
  auto from_table = context->GetOrInsertDataTable(GetTableName());
  auto success = context->CreateLogicalTable(logic_table_name_, from_table);
  if (!success) {
    CL_LOG_ERROR("create_logic_table", "create_table_error:" + logic_table_name_) << "create_table_error !";
    return;
  }

  AttrTable *to_table = context->GetMutableTable(logic_table_name_);
  for (const auto &attr : attrs_) {
    auto from_attr = from_table->GetOrInsertAttr(attr);
    auto to_attr = to_table->GetOrBorrowAttr(from_attr);
    if (from_attr != to_attr) {
      CL_LOG_ERROR("create_logic_table",
                   "reference_attr_error:" + attr + ",from:" + from_table_name_ + ",to:" + logic_table_name_)
        << "reference_attr_error !";
      return;
    }
  }

  auto from_results = from_table->GetRecoResults();
  auto first = from_results->begin();
  auto last = from_results->end();
  std::vector<CommonRecoResult> target_items;
  if (auto *select_root = GetSelectItemRoot()) {
    if (LoadFilterTree(context, select_root, "select")) {
      int type_mismatch_count = 0;
      for (auto iter = from_results->begin(); iter != from_results->end();) {
        if (CheckItemMeetConditions(context, *iter, select_root, "select", &type_mismatch_count)) {
          target_items.emplace_back(*iter);
          if (remove_selected_items_) {
            iter = from_results->erase(iter);
            continue;
          }
        }
        ++iter;
      }
    }
    first = target_items.begin();
    last = target_items.end();
  }
  to_table->CloneTargetResults(from_table, first, last);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCreateLogicTableRetriever, CommonRecoCreateLogicTableMixer);
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCreateLogicTableMixer, CommonRecoCreateLogicTableMixer);

}  // namespace platform
}  // namespace ks
