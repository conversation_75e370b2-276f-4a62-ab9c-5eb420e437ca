#pragma once

#include <utility>
#include <string>
#include <vector>

#include "absl/container/flat_hash_set.h"
#include "dragon/src/common_reco_handler.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoTableJoinMixer : public CommonRecoBaseMixer {
 public:
  CommonRecoTableJoinMixer() {}
  ~CommonRecoTableJoinMixer() {}
  void Mix(AddibleRecoContextInterface *context) override;

 private:
  bool InitProcessor() override {
    auto copy_attrs = config()->Get("copy_attrs");
    if (!RecoUtil::ParseAttrsConfig(copy_attrs, &copy_attrs_)) {
      return false;
    }

    left_table_name_ = config()->GetString("left_table", "");
    right_table_name_ = config()->GetString("right_table", "");

    join_attr_left_ = config()->GetString("join_attr_left", "");
    join_attr_right_ = config()->GetString("join_attr_right", "");
    return true;
  }

 private:
  std::string left_table_name_;
  std::string right_table_name_;
  std::string target_attr_;
  folly::F14FastMap<std::string, AttrConfig> copy_attrs_;

  std::string join_attr_left_;
  std::string join_attr_right_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTableJoinMixer);
};

}  // namespace platform
}  // namespace ks
