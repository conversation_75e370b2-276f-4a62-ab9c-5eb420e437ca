#include "dragon/src/processor/common/mixer/common_reco_pipeline_mixer.h"

#include "dragon/src/util/perf_report_util.h"

namespace ks {
namespace platform {

void CommonRecoPipelineMixer::Mix(AddibleRecoContextInterface *context) {
  int64 start_ts = base::GetTimestamp();

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  executor_.Reset(context, fill_common_attrs_from_request_, fill_browse_set_from_request_, nullptr,
                  GetTableName(), direct_access_parent_flow_data_);
#else
  executor_.Reset(context, fill_common_attrs_from_request_, fill_browse_set_from_request_, nullptr,
                  GetTableName());
#endif

  CloneContext(context);

  std::string request_type = context->GetRequestType();
  int64 put_in_pool_ts = base::GetTimestamp();
  int task_queue_id = GetIntProcessorParameter(context, "task_queue_id", 0);
  auto *sub_flow_thread_pool = executor_.GetAsyncPool(task_queue_id);
  if (!sub_flow_thread_pool) {
    CL_LOG_EXCEPTION("no_thread_pool_for_sub_flow")
        << "PipelineEnricher " << GetName() << " task_queue_id: " << task_queue_id
        << " get sub flow thread pool failed";
    return;
  }
  auto future = sub_flow_thread_pool->Async(
      context->GetRequestType(), GetName(),
      [this, put_in_pool_ts, request_type = std::move(request_type),
       enable_perf_report = PerfReportUtil::IsEnabled()]() {
        SetPerfEnabledOneshot set_perf_enabled(enable_perf_report);
        int64 execute_start_ts = base::GetTimestamp();
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            execute_start_ts - put_in_pool_ts, kPerfNs, "sub_flow_queue_time",
            GlobalHolder::GetServiceIdentifier(), request_type, pipeline_name_);
        executor_.Run();
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            base::GetTimestamp() - execute_start_ts, kPerfNs, "sub_flow_run_time",
            GlobalHolder::GetServiceIdentifier(), request_type, pipeline_name_);
        return executor_.GetContext();
      },
      GetDownstreamProcessor());

  CommonRecoContext *main_context = static_cast<CommonRecoContext *>(context);
  if (!main_context) return;

  auto callback = [this, main_context](CommonRecoContext *merge_context) {
    merge_context->SetMainTable(GetTableName());

    main_context->MergeCommonAttrs(merge_context, return_common_attrs_, merge_and_overwrite_);

    for (const auto &table_attrs : enrich_tables_) {
      auto attrs_cfg = FillAttrConfigFromAttrs(table_attrs.attrs);
      main_context->MergeTable(merge_context, pipeline_name_, attrs_cfg, table_attrs.table_name,
                               merge_and_overwrite_, false, false);
    }

    for (const auto &table_attrs : retrieve_tables_) {
      auto attrs_cfg = FillAttrConfigFromAttrs(table_attrs.attrs);
      main_context->MergeTable(merge_context, pipeline_name_, attrs_cfg, table_attrs.table_name,
                               merge_and_overwrite_, true, false);
    }

    CL_LOG(INFO) << "PipelineMixer " << GetName() << " succeed, " << executor_.GetProcessorNum()
                 << " processors in pipeline " << pipeline_name_;
  };

  auto finally = [this, start_ts, main_context]() {
    executor_.Cancel();
    DereferenceAttrs(main_context);
    int64 duration_ms = base::GetTimestamp() - start_ts;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(duration_ms, kPerfNs, "sub_flow_total_time",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      main_context->GetRequestType(), pipeline_name_);
  };

  auto timeout_cb = [executor_context = executor_.GetContext()]() { executor_context->MarkInvalid(); };

  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", 0);
  RegisterLocalAsyncCallback(context, std::move(future), std::move(callback), std::move(finally),
                             "sub_flow:" + pipeline_name_, timeout_ms, std::move(timeout_cb));
}

folly::F14FastMap<std::string, AttrConfig> CommonRecoPipelineMixer::FillAttrConfigFromAttrs(
    const std::vector<std::string> &attrs) {
  folly::F14FastMap<std::string, AttrConfig> res;
  for (const std::string &attr : attrs) {
    AttrConfig cfg;
    cfg.alias = attr;
    cfg.copy_mode = CopyMode::OVERWRITE;
    res.insert({attr, cfg});
  }
  return res;
}

void CommonRecoPipelineMixer::CloneContext(ReadableRecoContextInterface *context) {
  // 克隆 common attr
  int64 start_ts = base::GetTimestamp();
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  if (!direct_access_parent_flow_data_) {
    for (const auto &attr_name : copy_common_attrs_) {
      executor_.GetContext()->CloneCommonAttr(from_context, attr_name, !deep_copy_, !deep_copy_);
    }

    for (const auto &table_attrs : input_tables_) {
      executor_.GetContext()->CloneTable(from_context, table_attrs.attrs, table_attrs.table_name, !deep_copy_,
                                         !deep_copy_);
    }
  } else {
    //  1. 直接拷贝 input table 的指针到 subflow, 使用者自行保证线程安全
    //  2. 备份主 context 的 common_data_ 指针，在主 common_data_ 找不到时，读操作使用 common_data_backup_
    //  进行二次查找
    for (const auto &table_attrs : input_tables_) {
      executor_.GetContext()->ShadowCloneItemAttrTable(context, table_attrs.table_name);
    }
    executor_.GetContext()->BackupCommonAttrTable(context);
    if (deep_copy_) {
      for (const auto &attr_name : copy_common_attrs_) {
        executor_.GetContext()->CloneCommonAttr(context, attr_name, !deep_copy_, !deep_copy_);
      }
    }
  }
#else
  for (const auto &attr_name : copy_common_attrs_) {
    executor_.GetContext()->CloneCommonAttr(from_context, attr_name, !deep_copy_, !deep_copy_);
  }

  for (const auto &table_attrs : input_tables_) {
    executor_.GetContext()->CloneTable(from_context, table_attrs.attrs, table_attrs.table_name, !deep_copy_,
                                       !deep_copy_);
  }
#endif

  executor_.GetContext()->ResetStageStartTime(from_context->GetStageStartTime());
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      base::GetTimestamp() - start_ts, kPerfNs, "sub_flow_attr_pass_time",
      GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
}

void CommonRecoPipelineMixer::DereferenceAttrs(CommonRecoContext *context) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  if (direct_access_parent_flow_data_) {
    return;
  }
#endif

  for (const auto &attr_name : copy_common_attrs_) {
    context->DereferenceCommonAttr(attr_name);
  }

  if (!deep_copy_) {
    for (const auto &table_attrs : input_tables_) {
      auto *table = context->GetOrInsertDataTable(table_attrs.table_name);
      for (const auto &attr_name : table_attrs.attrs) {
        auto *attr = table->GetAttr(attr_name);
        if (attr) {
          attr->DescRefCount();
        }
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPipelineMixer, CommonRecoPipelineMixer);

}  // namespace platform
}  // namespace ks
