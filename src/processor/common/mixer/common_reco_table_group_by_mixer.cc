#include "dragon/src/processor/common/mixer/common_reco_table_group_by_mixer.h"

namespace ks {
namespace platform {

void CommonRecoTableGroupByMixer::Mix(AddibleRecoContextInterface *context) {
  auto from_table = context->GetOrInsertDataTable(from_table_name_);
  auto to_table = context->GetOrInsertDataTable(to_table_name_);

  auto key_accessor = from_table->GetOrInsertAttr(key_attr_);

  const std::vector<CommonRecoResult> &from_results = from_table->GetCommonRecoResults();
  std::vector<CommonRecoResult> to_results;
  std::vector<CommonRecoResult> real_from_results;
  for (const auto &result : from_results) {
    auto new_key = context->GetIntItemAttr(result, key_accessor);
    if (new_key) {
      auto new_result = to_table->NewCommonRecoResult(new_key.value(), reason_, 0.0, 0);
      to_results.emplace_back(new_result);
      real_from_results.emplace_back(result);
    }
  }

  for (const auto &pr : copy_attrs_) {
    auto from_attr_accessor = from_table->GetOrInsertAttr(pr.first);
    auto to_attr_accessor = to_table->GetOrInsertAttr(pr.second.alias);
    if (to_attr_accessor->IsReadOnly()) {
      continue;
    }
    if (pr.second.copy_mode == CopyMode::CONCAT) {
      to_attr_accessor->ConvertSingularValueToListValue();
    }
    CopyAttrValues(context, from_attr_accessor, real_from_results, to_attr_accessor, to_results,
                   pr.second.copy_mode);
  }

  if (!save_key_as_.empty()) {
    auto save_key_attr = to_table->GetOrInsertAttr(save_key_as_);
    for (int i = 0; i < real_from_results.size(); i++) {
      context->AppendIntListItemAttr(to_results[i], save_key_attr, real_from_results[i].key());
    }
  }

  std::unordered_set<uint64> key_set;
  std::for_each(to_results.begin(), to_results.end(), [&key_set, to_table](const CommonRecoResult &result) {
    if (!key_set.count(result.key())) {
      key_set.insert(result.key());
      to_table->AddCommonRecoResult(result.key(), result.reason, 0.0, 0);
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTableGroupByMixer, CommonRecoTableGroupByMixer);

}  // namespace platform
}  // namespace ks
