#include "dragon/src/processor/common/enricher/common_reco_delegate_enricher.h"

#include <algorithm>
#include <functional>
#include <future>
#include <unordered_set>
#include <utility>

#include "base/hash_function/city.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.generic_rpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "serving_base/server_base/kess_client.h"

DEFINE_bool(try_packed_item_attr_first, true, "try use packed item attr first");

namespace ks {
namespace platform {

// copy from common_reco_common_predict_item_attr_enricher.cc
bool CommonRecoDelegateEnricher::GenerateSampleListAttr(
    MutableRecoContextInterface *context,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attr_vct) {
  if (attr_vct == nullptr) {
    return false;
  }

  auto kuiba_user_attr_names = context->GetStringListCommonAttr(sample_list_common_attr_key_);
  const auto *kuiba_user_item =
      sample_list_ptr_attr_.empty()
          ? nullptr
          : context->GetProtoMessagePtrCommonAttr<::kuiba::PredictItem>(sample_list_ptr_attr_);
  if ((!kuiba_user_attr_names || kuiba_user_attr_names->empty()) &&
      (!kuiba_user_item || kuiba_user_item->attr_size() == 0)) {
    if (!sample_list_common_attr_key_.empty()) {
      CL_LOG_EVERY_N(WARNING, 1000) << "cannot find string list common attr [" << sample_list_common_attr_key_
                                    << "] or it's empty" << RecoUtil::GetRequestInfoForLog(context);
    }
    if (!sample_list_ptr_attr_.empty()) {
      CL_LOG_EVERY_N(WARNING, 1000) << "cannot find ptr common attr [" << sample_list_ptr_attr_
                                    << "] or it's empty" << RecoUtil::GetRequestInfoForLog(context);
    }
    return false;
  }

  kuiba::PredictItem kuiba_user_attrs;

  if (kuiba_user_item) {
    for (const auto &attr : kuiba_user_item->attr()) {
      const auto &name = attr.name();
      if (send_common_attrs_set_.count(name) || sample_list_attrs_.count(name)) {
        continue;
      }
      sample_list_attrs_.emplace(name);
      kuiba_user_attrs.mutable_attr()->Add()->CopyFrom(attr);
    }
  }

  if (kuiba_user_attr_names) {
    for (auto name : *kuiba_user_attr_names) {
      if (send_common_attrs_set_.count(name) || sample_list_attrs_.count(name)) {
        continue;
      }
      sample_list_attrs_.emplace(std::string(name.data(), name.size()));
      if (!interop::BuildSampleAttrFromCommonAttr(context, name, kuiba_user_attrs.mutable_attr())) {
        CL_LOG_WARNING_EVERY("delegate_enrich", "kuiba_user_attr_miss:" + std::string(name), 100)
            << "cannot find sample list common attr: " << name << RecoUtil::GetRequestInfoForLog(context);
        continue;
      }
    }
  }

  if (flatten_sample_list_attr_) {
    ::kuiba::SampleAttr *sample_attr = attr_vct->Add();
    sample_attr->set_name(flatten_sample_list_attr_to_);
    sample_attr->set_type(kuiba::CommonSampleEnum::STRING_ATTR);
    std::string *str_val = sample_attr->mutable_string_value();
    kuiba_user_attrs.SerializeToString(str_val);
  } else {
    for (auto &attr : *kuiba_user_attrs.mutable_attr()) {
      attr_vct->Add()->Swap(&attr);
    }
  }
  return true;
}

bool CommonRecoDelegateEnricher::FillCommonAttrFromSampleList(MutableRecoContextInterface *context) {
  int pre_attr_num = request_->common_attr_size();
  if (!GenerateSampleListAttr(context, request_->mutable_common_attr())) {
    CL_LOG_WARNING_EVERY("delegate_enrich", "generate_sample_list_attr_fail:" + GetName(), 1000)
        << "delegate enrich request cancelled: failed to generate sample list attr."
        << RecoUtil::GetRequestInfoForLog(context);
    return false;
  }
  CL_LOG_EVERY_N(INFO, 1000) << "injected " << (request_->common_attr_size() - pre_attr_num)
                             << " sample list attrs, flatten_sample_list_attr: " << flatten_sample_list_attr_;
  return true;
}

void CommonRecoDelegateEnricher::FillCommonAttrFromRequest(MutableRecoContextInterface *context) {
  const CommonRecoRequest *request = context->GetRequest();
  absl::flat_hash_set<std::string> send_common_attrs_in_request;
  for (const auto &attr : request->common_attr()) {
    if (send_common_attrs_set_.count(attr.name()) || sample_list_attrs_.count(attr.name()) ||
        exclude_common_attrs_.count(attr.name()) || send_common_attrs_in_request.count(attr.name())) {
      continue;
    }
    send_common_attrs_in_request.emplace(attr.name());
    request_->mutable_common_attr()->Add()->CopyFrom(attr);
    VLOG(1) << "common attr sent: " << attr.name();
  }
}

int64 CommonRecoDelegateEnricher::CheckAndGetTimeoutMs(
    MutableRecoContextInterface *context, const std::string& kess_service) {
  static constexpr int kDefaultTimeoutMs = 300;
  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", kDefaultTimeoutMs);
  if (!FLAGS_delegate_enrich_kess_blacklist_kconf.empty()) {
    const std::string &kess_blacklist_key = GetStringProcessorParameter(context, "kess_blacklist_key", "");
    timeout_ms = KessBlackListUtil::GetInstance()->GetKessBlackListTimeoutMs(
      context, kess_blacklist_key, kess_service, timeout_ms, GetName());
    if (timeout_ms == 0) {
      return 0;  // 命中服务控制列表，且配置的超时时间为 0 ，才会返回 0
    }
  }

  if (timeout_ms <= 0) {
    CL_LOG_ERROR_EVERY("delegate_enrich", "negative_timeout:" + GetName(), 100)
        << "timeout_ms=" << timeout_ms << " is too small, use default value " << kDefaultTimeoutMs
        << ", processor: " << GetName();
    timeout_ms = kDefaultTimeoutMs;
  } else if (timeout_ms > 2000) {
    CL_LOG_WARNING_EVERY("delegate_enrich", "timeout_too_large:" + std::to_string(timeout_ms), 100)
        << "timeout_ms=" << timeout_ms << " is too large, be careful!";
  }

  return timeout_ms;
}

void CommonRecoDelegateEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    CL_LOG_ERROR_EVERY("delegate_enrich", "no_kess_service:" + GetName(), 100)
        << "delegate enrich request cancelled: no kess_service! processor: " << GetName();
    return;
  }

  int64 timeout_ms = CheckAndGetTimeoutMs(context, kess_service);
  if (timeout_ms == 0) {  // 命中服务控制列表，且配置的超时时间为 0 ，才会满足这个条件，此时屏蔽该服务
    return;
  }

  auto timer = std::make_shared<serving_base::Timer>();
  timer = {timer.get(), [timer](void *) { CL_LOG(INFO) << timer->display(); }};

  if (send_item_attr_vec_.empty()) {
    send_item_attr_vec_.reserve(send_item_attrs_.size());
    for (const auto &attr : send_item_attrs_) {
      send_item_attr_vec_.push_back(SendItemAttr{
          .name = attr.first, .as = attr.second, .accessor = context->GetItemAttrAccessor(attr.first)});
    }
  } else {
    send_item_attr_vec_.resize(send_item_attrs_.size());
  }

  if (!send_item_attrs_in_name_list_.empty()) {
    auto attr_name_list = context->GetStringListCommonAttr(send_item_attrs_in_name_list_);
    if (attr_name_list) {
      for (const auto &attr_name : *attr_name_list) {
        send_item_attr_vec_.push_back(SendItemAttr{
            .name = attr_name, .as = attr_name, .accessor = context->GetItemAttrAccessor(attr_name)});
      }
    }
  }

  ResetRequest();

  // common attrs
  std::string request_type = GetStringProcessorParameter(context, "request_type");
  request_->set_request_type(request_type.empty() ? context->GetRequestType() : request_type);

  request_->set_user_id(context->GetUserId());
  request_->set_device_id(context->GetDeviceId());
  request_->set_time_ms(context->GetRequestTime());
  request_->set_request_id(context->GetRequestId());
  request_->set_debug(context->IsDebugRequest());
  request_->set_need_traceback(context->NeedTraceback());
  if (context->GetRequest()->has_abtest_mapping_id()) {
    request_->mutable_abtest_mapping_id()->CopyFrom(context->GetRequest()->abtest_mapping_id());
  }

  if (send_browse_set_) {
    const CommonRecoRequest *request = context->GetRequest();
    if (request->has_browse_set_package()) {
      request_->set_allocated_browse_set_package(
          const_cast<ks::platform::BrowseSetPackage *>(&request->browse_set_package()));
    }
    if (request->has_browse_set()) {
      request_->set_allocated_browse_set(const_cast<ks::reco::BrowseSet *>(&request->browse_set()));
    }
  }

  bool is_sim_request = false;
  if (auto val = context->GetIntCommonAttr(kSimulationRequest)) {
    is_sim_request = *val;
  }
  uint64 uId = 0;
  std::string dId = "";
  CommonAttr *uid_acc = context->GetCommonAttrAccessor("uId");
  CommonAttr *did_acc = context->GetCommonAttrAccessor("dId");
  if (is_sim_request) {
    auto sim_uid = context->GetIntCommonAttr(kSimulationUserId);
    auto sim_did = context->GetStringCommonAttr(kSimulationDeviceId);
    if (sim_uid && sim_did) {
      request_->set_user_id(*sim_uid);
      if (auto real_uid = context->GetIntCommonAttr(uid_acc)) {
        uId = *real_uid;
        context->SetIntCommonAttr(uid_acc, *sim_uid);
      }
      request_->set_device_id({sim_did->data(), sim_did->size()});
      if (auto real_did = context->GetStringCommonAttr(did_acc)) {
        dId = {real_did->data(), real_did->size()};
        context->SetStringCommonAttr(did_acc, {sim_did->data(), sim_did->size()});
      }
      ks::infra::PerfUtil::CountLogStash(1, kPerfNs, "delegate_enrich_simulation",
                                         GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                                         std::to_string(context->GetUserId()), context->GetDeviceId(),
                                         std::to_string(*sim_uid), {sim_did->data(), sim_did->size()});
    }
  }

  base::ScopeExit restore_uid_did([context, is_sim_request, uid_acc, did_acc, uId, &dId] {
    if (is_sim_request) {
      if (uId) {
        context->SetIntCommonAttr(uid_acc, uId);
      }
      if (!dId.empty()) {
        context->SetStringCommonAttr(did_acc, dId);
      }
    }
  });

  if (use_packed_item_attr_config_) {
    request_->set_use_packed_item_attr(
        GetBoolProcessorParameter(context, use_packed_item_attr_config_, false));
  } else if (recv_item_attrs_.empty() || !FLAGS_try_packed_item_attr_first) {
    // XXX(fangjianbing): recv_item_attrs_ 若为空则无法通过下游 response 自动判断是否支持 packed_item_attr,
    // 为保证兼容和安全性这里必须强制不使用 packed_item_attr
    request_->set_use_packed_item_attr(false);
  } else {
    bool use_packed_item_attr = packed_item_attr_no_support_services_.empty() ||
                                packed_item_attr_no_support_services_.count(kess_service) == 0;
    request_->set_use_packed_item_attr(use_packed_item_attr);
    if (!use_packed_item_attr) {
      CL_LOG_WARNING_EVERY("delegate_enrich", "packed_item_attr_not_support: " + kess_service, 100)
          << "found packed_item_attr no support service: " << kess_service << ", processor: " << GetName();
    }
  }

  for (const auto &attr_as : send_common_attrs_) {
    kuiba::SampleAttr *attr = interop::BuildSampleAttrFromCommonAttr(
        context, attr_as.name, request_->mutable_common_attr(), attr_as.alias, attr_as.readonly);

    if (!attr || attr->type() == kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
      CL_LOG_EVERY_N(WARNING, 1000) << "common attr " << attr_as.name << " is requested but not found";
    } else {
      VLOG(1) << "common attr sent: " << attr_as.name;
    }
  }

  sample_list_attrs_.clear();
  // 获得 samplelist attr, 失败情况下不发送请求
  if (use_sample_list_attr_flag_ && !FillCommonAttrFromSampleList(context)) {
    return;
  }

  // 将 request 中携带的 common_attr 发送下游
  if (send_common_attrs_in_request_) {
    FillCommonAttrFromRequest(context);
  }

  if (infer_output_type_ >= 0) {
    kuiba::SampleAttr *attr = request_->add_common_attr();
    attr->set_name(kInferOutputType);
    attr->set_type(kuiba::CommonSampleEnum::INT_ATTR);
    attr->set_int_value(infer_output_type_);
  }

  request_->set_return_required_attrs_only(true);

  for (const auto &attr_as : recv_item_attrs_) {
    request_->add_return_item_attrs(attr_as.first);
  }
  for (const auto &attr_as : recv_common_attrs_) {
    request_->add_return_common_attrs(attr_as.first);
  }

  ItemAttr *shard_by_item_attr_accessor = nullptr;
  if (!shard_by_item_attr_.empty()) {
    if (!context->HasItemAttr(shard_by_item_attr_)) {
      CL_LOG(WARNING) << "skip delegate as shard_by_item_attr is missing: " << shard_by_item_attr_;
      return;
    }
    shard_by_item_attr_accessor = context->GetItemAttrAccessor(shard_by_item_attr_);
  }

  timer->AppendCostMs("prepare");

  std::vector<std::vector<CommonRecoResult>> items_in_shard;
  items_in_shard.resize(shard_num_);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    uint64 key = result.item_key;
    if (shard_by_item_attr_accessor) {
      if (auto int_val = result.GetIntAttr(shard_by_item_attr_accessor, check_multi_table_)) {
        key = *int_val;
      } else if (auto str_val = result.GetStringAttr(shard_by_item_attr_accessor)) {
        key = base::CityHash64(str_val->data(), str_val->size());
      } else {
        VLOG(1) << "discard item due to missing shard attr " << shard_by_item_attr_accessor
                << ", item_key: " << result.item_key << ", item_id: " << result.GetId()
                << ", item_type: " << result.GetType();
        return;
      }
    }
    items_in_shard[key % items_in_shard.size()].emplace_back(result);
  });

  for (int shard = 0; shard < items_in_shard.size(); ++shard) {
    std::string kess_shard = "s" + std::to_string(shard_id_offset_ + shard);
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> item_key_mapping;
    FillRequestItems(context, items_in_shard[shard], &item_key_mapping);
    timer->AppendCostMs("fill_list_" + kess_shard);
    SendRequest(context, std::move(items_in_shard[shard]), kess_service, kess_shard, timeout_ms, timer,
                std::move(item_key_mapping));
    timer->AppendCostMs("send_request_" + kess_shard);
  }

  if (request_->has_browse_set_package()) {
    request_->release_browse_set_package();
  }

  if (request_->has_browse_set()) {
    request_->release_browse_set();
  }
}

void CommonRecoDelegateEnricher::FillRequestItems(
    MutableRecoContextInterface *context, const std::vector<CommonRecoResult> &items,
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> *item_key_mapping) {
  request_->clear_item_list();
  if (!use_item_id_in_attr_accessor_ && !use_item_id_in_attr_.empty()) {
    use_item_id_in_attr_accessor_ = context->GetItemAttrAccessor(use_item_id_in_attr_);
  }

  std::vector<CommonRecoResult> send_items;
  std::vector<uint64> send_item_keys;
  std::vector<int> fill_attr_succ_count;
  for (const CommonRecoResult &result : items) {
    uint64 item_key = result.item_key;
    uint64 item_id = result.GetId();
    int item_type = result.GetType();
    if (use_item_id_in_attr_accessor_) {
      if (auto p = result.GetIntAttr(use_item_id_in_attr_accessor_, check_multi_table_)) {
        item_id = Util::GetId(*p);
        item_type = Util::GetType(*p);
        item_key = *p;
      } else {
        CL_LOG_EVERY_N(ERROR, 1000) << "Item ignored, cannot get id from attr " << use_item_id_in_attr_
                                    << ", item_id: " << result.GetId();
        continue;
      }
    }
    if (use_item_id_in_attr_accessor_ || check_multi_table_) {
      (*item_key_mapping)[item_key].push_back(result);
    }

    auto *item = request_->add_item_list();
    item->set_item_id(item_id);
    item->set_item_type(item_type);
    item->set_reason(result.reason);
    item->set_score(result.score);

    if (request_->use_packed_item_attr()) {
      send_items.push_back(result);
      send_item_keys.push_back(item_key);
    } else {
      if (fill_attr_succ_count.empty()) {
        fill_attr_succ_count.resize(send_item_attr_vec_.size(), 0);
      }
      for (int i = 0; i < send_item_attr_vec_.size(); ++i) {
        const auto &send_item_attr = send_item_attr_vec_[i];
        kuiba::SampleAttr *attr = interop::BuildSampleAttrFromItemAttr(
            result, send_item_attr.accessor, item->mutable_item_attr(), send_item_attr.as);
        if (attr && attr->type() != kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
          ++fill_attr_succ_count[i];
          VLOG(1) << "[" << result.item_key << "] item attr sent: " << send_item_attr.name;
        }
      }
    }
  }

  if (request_->use_packed_item_attr()) {
    std::vector<ItemAttr *> send_item_attr_accessors;
    std::vector<absl::string_view> rename_attrs;
    send_item_attr_accessors.reserve(send_item_attr_vec_.size());
    rename_attrs.reserve(send_item_attr_vec_.size());
    for (const auto &item : send_item_attr_vec_) {
      send_item_attr_accessors.push_back(item.accessor);
      rename_attrs.push_back(item.as);
    }

    fill_attr_succ_count = RecoUtil::BuildPackedItemAttrFromItems(
        send_items.cbegin(), send_items.cend(), send_item_attr_accessors, request_->mutable_item_attr(),
        &rename_attrs, &send_item_keys, check_multi_table_);
  }

  // expect that all items are returned
  int send_item_num = request_->item_list_size();
  request_->set_request_num(send_item_num);

  for (int i = 0; i < fill_attr_succ_count.size(); ++i) {
    int miss_count = send_item_num - fill_attr_succ_count[i];
    if (miss_count > 0) {
      CL_LOG_EVERY_N(WARNING, 100) << miss_count << " out of " << send_item_num << " items missing item attr "
                                   << send_item_attr_vec_[i].name;
    }
  }
}

void CommonRecoDelegateEnricher::SendRequest(
    MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
    const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
    std::shared_ptr<serving_base::Timer> timer,
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_key_mapping) {
  // skip send request if both item_list and return_common_attrs is empty
  if (request_->item_list_size() == 0 && request_->return_common_attrs_size() == 0) {
    CL_LOG(INFO) << "delegate enrich cancelled, both item_list and return_common_attrs are empty";
    return;
  }

  if (context->GetRpcType() == RpcType::BRPC) {
    SendBrpcRequest(context, std::move(items), kess_service, kess_shard, timeout_ms, std::move(timer),
                    std::move(item_key_mapping));
  } else {
    SendGrpcRequest(context, std::move(items), kess_service, kess_shard, timeout_ms, std::move(timer),
                    std::move(item_key_mapping));
  }
}

void CommonRecoDelegateEnricher::SendGrpcRequest(
    MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
    const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
    std::shared_ptr<serving_base::Timer> timer,
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_key_mapping) {
  auto resp = response_pool_.AcquireShared();
  std::string kess_group = GetStringProcessorParameter(context, "kess_group");

  const auto &pr = [&]() {
    if (consistent_hash_) {
      uint64 user_hash;
      std::string hash_id = GetStringProcessorParameter(context, "hash_id");
      if (!hash_id.empty()) {
        user_hash = base::CityHash64(hash_id.data(), hash_id.size());
      } else {
        user_hash = RecoUtil::GenUserHash(context->GetUserId(), context->GetDeviceId());
      }
      HASHED_KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN_BY_GROUP(
          kess_service, kess_cluster_, kess_group, kess_shard, timeout_ms, *request_.get(), resp.get(),
          kess::CommonRecoLeafService, AsyncRecommend, user_hash);

    } else {
      KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN_BY_GROUP(kess_service, kess_cluster_, kess_group, kess_shard,
                                                      timeout_ms, *request_.get(), resp.get(),
                                                      kess::CommonRecoLeafService, AsyncRecommend);
    }
  }();

  const std::string request_info = "kess_service: " + kess_service + ", " + request_info_ +
                                   ", timeout_ms: " + std::to_string(timeout_ms) +
                                   ", kess_shard: " + kess_shard;

  if (!pr.first) {
    CL_LOG_ERROR_EVERY("delegate_enrich", "send_request_fail: " + kess_service, 1000)
        << "failed to send delegate enrich request! " << request_info
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  base::perfutil::PerfUtilWrapper::CountLogStash(1, kPerfNs, "delegate_enrich_call",
                                                 GlobalHolder::GetServiceIdentifier(),
                                                 request_->request_type(), GetName(), kess_service);
  RegisterAsyncCallback(
      context, std::move(pr.second),
      [this, context, kess_service, items = std::move(items), kess_shard, timer,
       item_key_mapping = std::move(item_key_mapping), resp = std::move(resp)](CommonRecoResponse *response) {
        HandleResponse(context, kess_service, items, response, kess_shard, std::move(timer),
                       item_key_mapping);
      },
      request_info);
}

void CommonRecoDelegateEnricher::SendBrpcRequest(
    MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
    const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
    std::shared_ptr<serving_base::Timer> timer,
    folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_key_mapping) {
  auto resp = response_pool_.AcquireShared();
  auto brpc_promise = std::make_shared<std::promise<CommonRecoResponse *>>();

  bool success = [&]() {
    KESS_BRPC_MUTABLE_LAMBDA_ASYNC_RETURN(
        kess_service, kess_cluster_, kess_shard, timeout_ms, request_.get(), resp,
        [brpc_promise](std::shared_ptr<::brpc::Controller> cntl,
                       std::shared_ptr<CommonRecoResponse> response) mutable {
          if (cntl->Failed()) {
            brpc_promise->set_value(nullptr);
          } else {
            brpc_promise->set_value(response.get());
          }
        },
        ::ks::platform::generic_rpc::CommonRecoLeafService_Stub, Recommend);
  }();

  const std::string request_info = "kess_service: " + kess_service + ", " + request_info_ +
                                   ", timeout_ms: " + std::to_string(timeout_ms) +
                                   ", kess_shard: " + kess_shard;

  if (!success) {
    CL_LOG_ERROR_EVERY("delegate_enrich", "send_request_fail: " + kess_service, 1000)
        << "failed to send delegate enrich request! " << request_info
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }
  base::perfutil::PerfUtilWrapper::CountLogStash(1, kPerfNs, "delegate_enrich_call",
                                                 GlobalHolder::GetServiceIdentifier(),
                                                 request_->request_type(), GetName(), kess_service);
  RegisterLocalAsyncCallback(
      context, brpc_promise->get_future(),
      [this, context, kess_service, items = std::move(items), timer,
       item_key_mapping = std::move(item_key_mapping), resp = std::move(resp), brpc_promise,
       kess_shard](CommonRecoResponse *response) {
        HandleResponse(context, kess_service, items, response, kess_shard, std::move(timer),
                       item_key_mapping);
      },
      request_info);
}

void CommonRecoDelegateEnricher::HandleResponse(
    MutableRecoContextInterface *context, const std::string &kess_service,
    const std::vector<CommonRecoResult> &items, CommonRecoResponse *response, const std::string &kess_shard,
    std::shared_ptr<serving_base::Timer> timer,
    const folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &item_key_mapping) {
  timer->AppendCostMs("wait_response_" + kess_shard);
  if (response->status_code() != 0) {
    CL_LOG_ERROR("delegate_enrich",
                 absl::StrCat("response status_code=", response->status_code(), ": ", kess_service))
        << "CommonRecoDelegateEnricher failed as non-zero status_code: " << response->status_code();
    return;
  }
  VLOG(1) << "Delegate response:" << response->ShortDebugString();

  if (!recv_common_attrs_.empty()) {
    for (const auto &attr : response->common_attr()) {
      auto it = recv_common_attrs_.find(attr.name());
      if (it != recv_common_attrs_.end()) {
        interop::SaveSampleAttrToCommonAttr(context, it->second, attr);
      }
    }
  }

  folly::F14FastMap<std::string, StatisticInfo> pxtr_counter;
  int valid_item_num = 0;
  int wrong_item_key_num = 0;
  bool has_plain_item_attr = false;
  folly::F14FastMap<uint64, int> item_key_pos;
  for (const auto &item : response->item()) {
    uint64 item_key = Util::GenKeysign(item.item_type(), item.item_id());
    const CommonRecoResult *result = nullptr;
    if (!item_key_mapping.empty()) {
      auto it = item_key_mapping.find(item_key);
      int &pos = item_key_pos[item_key];
      if (it != item_key_mapping.end() && it->second.size() > pos) {
        result = &(it->second[pos]);
        pos++;
      } else {
        ++wrong_item_key_num;
        CL_LOG_EVERY_N(WARNING, 1000) << "Returned item_key=" << item_key << " not found in key mapping!";
        continue;
      }
    }

    ++valid_item_num;

    if (!recv_item_attrs_.empty()) {
      for (const auto &sample_attr : item.item_attr()) {
        has_plain_item_attr = true;
        auto it = recv_item_attrs_.find(sample_attr.name());
        if (it != recv_item_attrs_.end()) {
          interop::SaveSampleAttrToItemAttr(context, item_key, it->second, sample_attr);
          if (for_predict_ && sample_attr.type() == ::kuiba::CommonSampleEnum::FLOAT_ATTR) {
            pxtr_counter[it->second].AddValue(sample_attr.float_value());
          }
        } else {
          VLOG(1) << sample_attr.name() << " is ignored as it's not in recv_item_attrs";
        }
      }
    }
  }

  CL_LOG_WARNING_COUNT(wrong_item_key_num, "delegate_enrich", "returned wrong item_key: " + kess_service)
      << "Returned " << wrong_item_key_num << " wrong item_keys that not found in key mapping!";

  if (response->has_item_attr() && !recv_item_attrs_.empty()) {
    const std::vector<CommonRecoResult> *p_items = &items;
    std::vector<CommonRecoResult> aligned_items;
    if (RecoUtil::AlignPackedItemAttrNum(response->item_attr(), items, context, &aligned_items,
                                         &item_key_mapping)) {
      p_items = &aligned_items;
      VLOG(100) << "using aligned fake items due to packed_item_num and request item num mismatch: "
                << aligned_items.size() << " vs " << items.size() << ", shard: " << kess_shard;
    }

    for (const auto &attr_value : response->item_attr().attr_values()) {
      auto it = recv_item_attrs_.find(attr_value.name());
      if (it == recv_item_attrs_.end()) {
        VLOG(1) << attr_value.name() << " is ignored as it's not in recv_item_attrs";
        continue;
      }
      auto *attr_accessor = context->GetItemAttrAccessor(it->second);
      auto stat = RecoUtil::ExtractPackedItemAttrToContext(attr_accessor, attr_value, p_items, false,
                                                           check_multi_table_);
      if (for_predict_ && (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64 ||
                           attr_value.type() == ::kuiba::CommonSampleEnum::FLOAT_ATTR)) {
        pxtr_counter[it->second] = std::move(stat);
      }
    }
  }

  if (!use_packed_item_attr_config_ && !recv_item_attrs_.empty()) {
    if (FLAGS_try_packed_item_attr_first && !response->has_item_attr() && has_plain_item_attr) {
      packed_item_attr_no_support_services_.insert(kess_service);
    }
  }

  if (for_predict_) {
    if (items.size() > 0) {
      double filling_rate = (double)valid_item_num / items.size();
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1000.0 * filling_rate, kPerfNs, "predict.item_hit",
                                                        GlobalHolder::GetServiceIdentifier(),
                                                        context->GetRequestType(), GetName());
    }
    base::perfutil::PerfUtilWrapper::IntervalLogStash(items.size(), kPerfNs, "predict.item_total",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
    for (const auto &counter : pxtr_counter) {
      const auto &attr_name = counter.first;
      const auto &stat = counter.second;
      if (items.size() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000.0 * stat.count() / items.size(), kPerfNs, "predict.pxtr_hit",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
      }
      if (stat.count() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.avg(), kPerfNs, "predict.pxtr_avg",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.max(), kPerfNs, "predict.pxtr_max",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
      }
    }
  }

  timer->AppendCostMs("fill_response_" + kess_shard);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDelegateEnricher, CommonRecoDelegateEnricher)

}  // namespace platform
}  // namespace ks
