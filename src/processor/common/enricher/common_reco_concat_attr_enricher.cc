#include "dragon/src/processor/common/enricher/common_reco_concat_attr_enricher.h"

#include <utility>
#include <vector>
#include <string>
#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoConcatAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if ((!output_common_attr_.empty() && input_common_attrs_.empty())
     || (!output_item_attr_.empty() && input_item_attrs_.empty())) {
    CL_LOG(WARNING) << "CommonRecoConcatAttrEnricher Enrich cancelled, empty input";
    return;
  }

#define INIT_ATTR_TYPE(ITEM_COMMON, GET_FROM, input_attrs, output_attr)                    \
  do {                                                                                     \
    if (!output_attr.empty()) {                                                            \
      for (const auto &key : input_attrs) {                                                \
        AttrType tmp_type = context->Get##GET_FROM##AttrType(key);                         \
        if (tmp_type == AttrType::UNKNOWN) continue;                                       \
        if (type_##ITEM_COMMON == AttrType::UNKNOWN) {                                     \
          type_##ITEM_COMMON = tmp_type;                                                   \
          continue;                                                                        \
        }                                                                                  \
        if (type_##ITEM_COMMON != tmp_type) {                                              \
          CL_LOG(ERROR) << "CommonRecoConcatAttrEnricher Enrich fail! AttrType mismatch, " \
               << static_cast<int>(type_##ITEM_COMMON) << "(" << input_attrs[0] << "):"    \
               << static_cast<int>(tmp_type) << "(" << key << ")";                         \
             return;                                                                       \
        }                                                                                  \
      }                                                                                    \
    }                                                                                      \
  } while (0)

  AttrType type_common = AttrType::UNKNOWN;
  AttrType type_item = AttrType::UNKNOWN;
  INIT_ATTR_TYPE(item, Item, input_item_attrs_, output_item_attr_);
  INIT_ATTR_TYPE(common, Common, input_common_attrs_, output_common_attr_);
#undef INIT_ATTR_TYPE
  if (!output_item_attr_.empty() && type_item == AttrType::UNKNOWN) {
    CL_LOG(WARNING) << "CommonRecoConcatAttrEnricher Enrich, item is all NONE";
  }
  if (!output_common_attr_.empty() && type_common == AttrType::UNKNOWN) {
    CL_LOG(WARNING) << "CommonRecoConcatAttrEnricher Enrich, common is all NONE";
  }

#define SET_CONCAT_ATTR_VALUE_ONE(DATA_TYPE, ITEM_COMMON)                    \
  concat_data.emplace_back(*t);                                              \
  if (!output_##ITEM_COMMON##attr_name_.empty()) {                           \
    if (output_attr_name_save_as_int_) {                                     \
      attr_name_int.emplace_back(ITEM_COMMON##attr_name_save_as_int_[loop]); \
    } else {                                                                 \
      attr_name_string.emplace_back(attr_name_list[loop]);                   \
    }                                                                        \
  }                                                                          \

// string 的处理较特殊，
// 不能 concat_data.insert(concat_data.end(), t->begin(), t->end())
#define SET_CONCAT_ATTR_VALUE_LIST(DATA_TYPE, ITEM_COMMON)               \
  concat_data.reserve(concat_data.size() + t->size());                   \
  for (const auto &key : (*t)) {                                         \
    concat_data.emplace_back(key);                                       \
  }                                                                      \
  if (!output_##ITEM_COMMON##attr_name_.empty()) {                       \
    if (output_attr_name_save_as_int_) {                                 \
      attr_name_int.reserve(attr_name_int.size() + t->size());           \
      std::fill_n(std::back_inserter(attr_name_int),                     \
                  t->size(), ITEM_COMMON##attr_name_save_as_int_[loop]); \
    } else {                                                             \
      attr_name_string.reserve(attr_name_string.size() + t->size());     \
      std::fill_n(std::back_inserter(attr_name_string),                  \
                  t->size(), attr_name_list[loop]);                      \
    }                                                                    \
  }                                                                      \

#define CONCAT_ITEM_ATTR_VALUE(ONE_LIST, GET_ATTR_TYPE, SET_ATTR_TYPE, DATA_TYPE)   \
  do {                                                                              \
    std::vector<ItemAttr *> attr_accessors;                                         \
    for (const auto &key : input_item_attrs_) {                                     \
      attr_accessors.push_back(context->GetItemAttrAccessor(key));                  \
    }                                                                               \
    const auto & attr_name_list = input_item_attrs_;                                \
    size_t reserve_size = 10;                                                       \
    std::for_each(begin, end, [&](const CommonRecoResult &result) {                 \
      std::vector<DATA_TYPE> concat_data;                                           \
      std::vector<int64> attr_name_int;                                             \
      std::vector<std::string> attr_name_string;                                    \
      concat_data.reserve(reserve_size);                                            \
      if (!output_item_attr_name_.empty()) {                                        \
        if (output_attr_name_save_as_int_) {                                        \
          attr_name_int.reserve(reserve_size);                                      \
        } else {                                                                    \
          attr_name_string.reserve(reserve_size);                                   \
        }                                                                           \
      }                                                                             \
      int loop = 0;                                                                 \
      for (const auto &accessor : attr_accessors) {                                 \
        auto t = result.Get##GET_ATTR_TYPE##Attr(accessor);                         \
        if (t) {                                                                    \
          SET_CONCAT_ATTR_VALUE_##ONE_LIST(DATA_TYPE, item_)                        \
        }                                                                           \
        ++loop;                                                                     \
      }                                                                             \
      reserve_size = std::max(concat_data.size(), reserve_size);                    \
      context->Set##SET_ATTR_TYPE##ItemAttr(result.item_key, output_item_attr_,     \
                                            std::move(concat_data));                \
      if (!output_item_attr_name_.empty()) {                                        \
        if (output_attr_name_save_as_int_) {                                        \
          context->SetIntListItemAttr(result.item_key,                              \
                    output_item_attr_name_, std::move(attr_name_int));              \
        } else {                                                                    \
          context->SetStringListItemAttr(result.item_key,                           \
                    output_item_attr_name_, std::move(attr_name_string));           \
        }                                                                           \
      }                                                                             \
    });                                                                             \
  } while (0)

#define CONCAT_COMMON_ATTR_VALUE(ONE_LIST, GET_ATTR_TYPE, SET_ATTR_TYPE, DATA_TYPE) \
  do {                                                                              \
    std::vector<DATA_TYPE> concat_data;                                             \
    std::vector<int64> attr_name_int;                                               \
    std::vector<std::string> attr_name_string;                                      \
    const auto & attr_name_list = input_common_attrs_;                              \
    int loop = 0;                                                                   \
    for (const auto & attr_name : input_common_attrs_) {                            \
      auto t = context->Get##GET_ATTR_TYPE##CommonAttr(attr_name);                  \
      if (t) {                                                                      \
        SET_CONCAT_ATTR_VALUE_##ONE_LIST(DATA_TYPE, common_)                        \
      }                                                                             \
      ++loop;                                                                       \
    }                                                                               \
    context->Set##SET_ATTR_TYPE##CommonAttr(output_common_attr_,                    \
                                          std::move(concat_data));                  \
    if (!output_common_attr_name_.empty()) {                                        \
      if (output_attr_name_save_as_int_) {                                          \
        context->SetIntListCommonAttr(                                              \
                  output_common_attr_name_, std::move(attr_name_int));              \
      } else {                                                                      \
        context->SetStringListCommonAttr(                                           \
                  output_common_attr_name_, std::move(attr_name_string));           \
      }                                                                             \
    }                                                                               \
  } while (0)


  switch (type_common) {
    case AttrType::INT_LIST:    CONCAT_COMMON_ATTR_VALUE(LIST, IntList, IntList, int64);             break;
    case AttrType::FLOAT_LIST:  CONCAT_COMMON_ATTR_VALUE(LIST, DoubleList, DoubleList, double);      break;
    case AttrType::STRING_LIST: CONCAT_COMMON_ATTR_VALUE(LIST, StringList, StringList, std::string); break;
    case AttrType::STRING:      CONCAT_COMMON_ATTR_VALUE(ONE,  String, StringList, std::string);     break;
    case AttrType::FLOAT:       CONCAT_COMMON_ATTR_VALUE(ONE,  Double, DoubleList, double);          break;
    case AttrType::INT:         CONCAT_COMMON_ATTR_VALUE(ONE,  Int, IntList, int64);                 break;
    case AttrType::EXTRA:  // 暂不支持
    default:
      break;
  }
  switch (type_item) {
    case AttrType::INT_LIST:    CONCAT_ITEM_ATTR_VALUE(LIST, IntList, IntList, int64);             break;
    case AttrType::FLOAT_LIST:  CONCAT_ITEM_ATTR_VALUE(LIST, DoubleList, DoubleList, double);      break;
    case AttrType::STRING_LIST: CONCAT_ITEM_ATTR_VALUE(LIST, StringList, StringList, std::string); break;
    case AttrType::STRING:      CONCAT_ITEM_ATTR_VALUE(ONE,  String, StringList, std::string);     break;
    case AttrType::FLOAT:       CONCAT_ITEM_ATTR_VALUE(ONE,  Double, DoubleList, double);          break;
    case AttrType::INT:         CONCAT_ITEM_ATTR_VALUE(ONE,  Int, IntList, int64);                 break;
    case AttrType::EXTRA:  // 暂不支持
    default:
      break;
  }

#undef CONCAT_COMMON_ATTR_VALUE
#undef CONCAT_ITEM_ATTR_VALUE
#undef SET_CONCAT_ATTR_VALUE_LIST
#undef SET_CONCAT_ATTR_VALUE_ONE
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoConcatAttrEnricher, CommonRecoConcatAttrEnricher)

}  // namespace platform
}  // namespace ks
