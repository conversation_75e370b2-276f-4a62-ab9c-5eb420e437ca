#include "dragon/src/processor/common/enricher/common_reco_remote_embedding_attr_lite_enricher.h"

#include "dragon/src/core/common_reco_shared_gflags.h"
#include "dragon/src/util/logging_util.h"
#include "serving_base/util/hash.h"
#include "serving_base/utility/system_util.h"

namespace ks {
namespace platform {

void CommonRecoRemoteEmbeddingAttrLiteEnricher::Enrich(MutableRecoContextInterface *context,
                                                       RecoResultConstIter begin, RecoResultConstIter end) {
  std::string kess_service;
  if (protocol_ == 0) {
    kess_service = GetStringProcessorParameter(context, "kess_service");
    if (kess_service.empty()) {
      CL_LOG_ERROR_EVERY("remote_embedding", "empty_kess_service", 100)
          << "remote embedding request cancelled: empty kess_service!";
      return;
    }
  }
  timer_.Start();

  ClearRequests();

  timer_.AppendCostMs("clear");

  exit_handler_ = std::shared_ptr<int>(nullptr, [this, context](void *ptr) {
    for (auto &store : attr_stores_) {
      store.Save(context, output_attr_name_);
    }
    timer_.AppendCostMs("save_to_context");

    CL_LOG(INFO) << timer_.display();
  });

  if (query_source_type_ == QuerySourceType::kItemKey) {
    attr_stores_.reserve(std::distance(begin, end));
    std::for_each(begin, end, [this, context, &kess_service](const CommonRecoResult &item) {
      auto &store = NewItemAttrStore(item.item_key, 1);
      AddSign(item.item_key, store.MutableAddrGetter(0), context, kess_service);
    });
  } else if (query_source_type_ == QuerySourceType::kItemId) {
    attr_stores_.reserve(std::distance(begin, end));
    std::for_each(begin, end, [this, context, &kess_service](const CommonRecoResult &item) {
      auto &store = NewItemAttrStore(item.item_key, 1);
      AddSign(item.GetId(), store.MutableAddrGetter(0), context, kess_service);
    });
  } else if (query_source_type_ == QuerySourceType::kItemAttr) {
    attr_stores_.reserve(std::distance(begin, end));
    auto accessor = context->GetItemAttrAccessor(input_attr_name_);
    std::for_each(begin, end, [this, accessor, context, &kess_service](const CommonRecoResult &item) {
      if (auto int_val = item.GetIntAttr(accessor)) {
        auto &store = NewItemAttrStore(item.item_key, 1);
        AddSign(*int_val, store.MutableAddrGetter(0), context, kess_service);
      } else if (auto int_list_val = item.GetIntListAttr(accessor)) {
        auto &store = NewItemAttrStore(item.item_key, int_list_val->size());
        for (size_t offset = 0; offset < int_list_val->size(); offset++) {
          AddSign(int_list_val->at(offset), store.MutableAddrGetter(offset * size_), context, kess_service);
        }
      }
    });
  } else {
    size_t num_resp = 1;
    absl::optional<int64> int_val;
    absl::optional<absl::Span<const int64>> int_list_val;
    if (query_source_type_ == QuerySourceType::kCommonAttr) {
      if ((int_val = context->GetIntCommonAttr(input_attr_name_))) {
        num_resp = 1;
      } else if ((int_list_val = context->GetIntListCommonAttr(input_attr_name_))) {
        num_resp = int_list_val->size();
      } else {
        num_resp = 0;
      }
    }

    if (num_resp > 0) {
      auto &store = NewCommonAttrStore(num_resp);

      size_t offset = 0;

      if (query_source_type_ == QuerySourceType::kUserId) {
        uint64_t user_id = context->GetUserId();
        auto addr_getter = store.MutableAddrGetter(size_ * (offset++));
        if (user_id > 0) {
          AddSign(user_id, addr_getter, context, kess_service);
        }
      } else if (query_source_type_ == QuerySourceType::kDeviceId) {
        const std::string &device_id = context->GetDeviceId();
        auto addr_getter = store.MutableAddrGetter(size_ * (offset++));
        if (!device_id.empty()) {
          AddSign(base::GetHash(device_id), addr_getter, context, kess_service);
        }
      } else if (query_source_type_ == QuerySourceType::kCommonAttr) {
        if (int_val) {
          AddSign(*int_val, store.MutableAddrGetter(size_ * (offset++)), context, kess_service);
        } else if (int_list_val) {
          for (int64 val : *int_list_val) {
            AddSign(val, store.MutableAddrGetter(size_ * (offset++)), context, kess_service);
          }
        }
      }

      if (num_resp != offset) {
        CL_LOG_ERROR("remote_embedding_lite", "invalid resp offset")
            << "Incorrect offset: " << num_resp << " vs " << offset;
        return;
      }
    }
  }

  timer_.AppendCostMs("collect_signs");

  SendRequests(context, kess_service);

  exit_handler_.reset();
}

void CommonRecoRemoteEmbeddingAttrLiteEnricher::SendColossusdbRequests(
    MutableRecoContextInterface *context, const std::vector<uint64_t> &request_signs) {
  clsdb_resp_.reset();
  if (request_signs.empty()) {
    return;
  }
  auto clsdb_client = GetColossusdbClient();
  if (!clsdb_client) {
    CL_LOG(ERROR) << "GetColossusdbClient failed!";
    return;
  }
  std::future<std::unique_ptr<colossusdb::EmbeddingMapResponse>> future =
      clsdb_client->BatchMapGetAsync(request_signs);
  CL_LOG(INFO) << "sending embedding remote query request, sign_num: "
               << std::to_string(request_signs.size());
  timer_.AppendCostMs("send_requests");
  int64 sent_ts = base::GetTimestamp();
  auto merger = [this,
                 sent_ts](std::future<std::unique_ptr<colossusdb::EmbeddingMapResponse>> future) -> bool * {
    clsdb_resp_ = future.get();
    double rpc_duration = base::GetTimestamp() - sent_ts;
    VLOG(1) << "Receive Result costs " << rpc_duration / 1000.0 << " ms";
    return &clsdb_resp_->succ;
  };

  std::future<bool *> merge_future = std::async(std::launch::deferred, merger, std::move(future));

  RegisterLocalAsyncCallback(context, std::move(merge_future), [this, handler = exit_handler_](bool *succ) {
    timer_.AppendCostMs("wait_for_callback_colossusdb");
    if (!*succ) {
      CL_LOG_EVERY_N(WARNING, 100) << "request colossusdb failed.";
      return;
    }

    if (is_raw_data_) {
      if (raw_data_type_ == RawDataType::kUnsignedInt8) {
        SaveRawDataAttr<uint8>();
      } else if (raw_data_type_ == RawDataType::kUnsignedInt16) {
        SaveRawDataAttr<uint16>();
      } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
        SaveRawDataAttr<uint32>();
      } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
        SaveRawDataAttr<uint64>();
      } else if (raw_data_type_ == RawDataType::kSignedInt8) {
        SaveRawDataAttr<int8>();
      } else if (raw_data_type_ == RawDataType::kSignedInt16) {
        SaveRawDataAttr<int16>();
      } else if (raw_data_type_ == RawDataType::kSignedInt32) {
        SaveRawDataAttr<int32>();
      } else if (raw_data_type_ == RawDataType::kSignedInt64) {
        SaveRawDataAttr<int64>();
      } else if (raw_data_type_ == RawDataType::kFloat32) {
        SaveRawDataAttr<float>();
      } else {
        CL_LOG_ERROR("remote_embedding", "invalid_raw_data_type")
            << "Unexpected raw_data_type: " << int(raw_data_type_);
      }
    } else {
      SaveEmbeddingAttr();
    }

    timer_.AppendCostMs("handle_response_colossusdb");
  });
}

void CommonRecoRemoteEmbeddingAttrLiteEnricher::SendRequestToShard(
    MutableRecoContextInterface *context, const ks::reco::bt_embd_s::BatchEmbeddingsRequest &request,
    std::string shard_name, const std::string &kess_service) {
  std::string request_info = "kess_service: " + kess_service + ", shard: " + shard_name +
                             ", timeout_ms: " + std::to_string(timeout_ms_);
  CL_LOG(INFO) << "sending embedding remote query request, sign_num: " << std::to_string(request.signs_size())
               << ", " << request_info;

  if (!request.signs_size()) {
    CL_LOG(INFO) << "skip shard " << shard_name << " as empty signs";
    return;
  }

  auto *resp = response_pool_.Acquire();

  std::pair<bool, ks::kess::rpc::grpc::Future<::ks::reco::bt_embd_s::BatchEmbeddingsResponse *>> pr;
  std::string hash_input = GetStringProcessorParameter(context, "hash_input");
  if (hash_input.empty()) {
    if (FLAGS_dragon_grpc_use_multi_eventloop) {
      pr = [&]() {
        KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_service, kess_cluster_, shard_name, timeout_ms_, request,
                                               resp, ks::reco::bt_embd_s::kess::BtEmbeddingService,
                                               AsyncGetBatchEmbeddings);
      }();
    } else {
      pr = [&]() {
        KESS_GRPC_ASYNC_RETURN(kess_service, kess_cluster_, shard_name, timeout_ms_, request, resp,
                               ks::reco::bt_embd_s::kess::BtEmbeddingService, AsyncGetBatchEmbeddings);
      }();
    }
  } else {
    uint64 hashed_value = base::CityHash64(hash_input.c_str(), hash_input.length());
    if (FLAGS_dragon_grpc_use_multi_eventloop) {
      pr = [&]() {
        HASHED_KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(
            kess_service, kess_cluster_, shard_name, timeout_ms_, request, resp,
            ks::reco::bt_embd_s::kess::BtEmbeddingService, AsyncGetBatchEmbeddings, hashed_value);
      }();
    } else {
      pr = [&]() {
        HASHED_KESS_GRPC_ASYNC_RETURN(kess_service, kess_cluster_, shard_name, timeout_ms_, request, resp,
                                      ks::reco::bt_embd_s::kess::BtEmbeddingService, AsyncGetBatchEmbeddings,
                                      hashed_value);
      }();
    }
  }

  if (!pr.first) {
    CL_LOG_ERROR_EVERY("remote_embedding", "send_req_failed", 100)
        << "embedding server send request failed, skip response.";
    return;
  }

  timer_.AppendCostMs("send_requests_" + shard_name);

  auto callback = [this, context, shard_name](ks::reco::bt_embd_s::BatchEmbeddingsResponse *resp) {
    timer_.AppendCostMs("wait_for_callback_" + shard_name);

    for (auto iter = resp->items().begin(); iter != resp->items().end(); iter++) {
      const std::string &ele = iter->second;
      if (ele.size() <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      int dim = ele.size() / sizeof(int16);
      VLOG(1) << "sign: " << sign << ", dim: " << dim;
      if (dim != size_) {
        CL_LOG(WARNING) << "Invalid dim for " << sign << ": " << dim;
        continue;
      }

      auto it = mapping_sign_to_addr_getter_.find(sign);
      if (it == mapping_sign_to_addr_getter_.end()) {
        CL_LOG(WARNING) << "Invalid sign found: " << sign;
        continue;
      }

      for (auto &addr_getter : it->second) {
        auto *weights = reinterpret_cast<const int16 *>(ele.data());
        double *addr = addr_getter();
        for (int j = 0; j < dim; j++) {
          addr[j] = ks::reco::WeightToFloat(weights[j]);
        }
      }
    }

    timer_.AppendCostMs("handle_response_" + shard_name);
  };

  auto finally = [this, resp, shard_name, handler = exit_handler_]() {
    response_pool_.Recycle(resp);
    timer_.AppendCostMs("recycle_response_" + shard_name);
  };

  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), std::move(finally), request_info);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRemoteEmbeddingAttrLiteEnricher,
                 CommonRecoRemoteEmbeddingAttrLiteEnricher)
}  // namespace platform
}  // namespace ks
