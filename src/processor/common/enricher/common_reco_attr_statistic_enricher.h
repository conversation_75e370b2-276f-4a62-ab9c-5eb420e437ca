#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoAttrStatisticEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAttrStatisticEnricher() {
    stats_type_map_["count"] = StatsType::Count;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class StatsType { Count = 0 };
  struct StatisticConfig {
    std::string attr_name;
    StatsType stats_type;
    std::string save_values_to;
    std::string save_results_to;
  };

  bool InitProcessor() override {
    check_point_ = config()->GetString("check_point", "");

    save_as_json_to_ = config()->GetString("save_as_json_to", "");

    auto *attr_stats_config = config()->Get("statistic_config");
    if (attr_stats_config) {
      if (!attr_stats_config->IsArray()) {
        LOG(ERROR) << "CommonRecoAttrStatisticEnricher init failed! "
                      "'statistic_config' should be an array!";
        return false;
      }
      for (const auto *attr_stats : attr_stats_config->array()) {
        StatisticConfig attr_stats_config;
        attr_stats_config.attr_name = attr_stats->GetString("attr_name");
        if (attr_stats_config.attr_name.empty()) {
          LOG(ERROR) << "CommonRecoAttrStatisticEnricher init failed! "
                        "'statistic_config.attr_name' should "
                        "not be empty!";
          return false;
        }

        std::string stats_type = attr_stats->GetString("aggregator", "count");
        auto it = stats_type_map_.find(stats_type);
        if (it == stats_type_map_.end()) {
          LOG(ERROR) << "CommonRecoAttrStatisticEnricher init failed! "
                        "'statistic_config.aggregator' only "
                        "support \"count\" ";
          return false;
        }
        attr_stats_config.stats_type = it->second;
        attr_stats_config.save_values_to = attr_stats->GetString("save_values_to");
        attr_stats_config.save_results_to = attr_stats->GetString("save_results_to");
        attr_stats_configs_.emplace_back(attr_stats_config);
      }
    }

    return true;
  }

 private:
  std::string save_as_json_to_;
  std::string check_point_;
  std::vector<StatisticConfig> attr_stats_configs_;
  folly::F14FastMap<std::string, StatsType> stats_type_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAttrStatisticEnricher);
};

}  // namespace platform
}  // namespace ks
