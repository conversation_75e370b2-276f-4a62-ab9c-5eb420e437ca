#pragma once

#include <memory>
#include <string>
#include <utility>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "infra/memcached_client/src/memcached_client/memc_client.h"
#include "infra/memcached_client/src/memcached_client/memc_options.h"

namespace ks {
namespace platform {

class CommonRecoMemcachedCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoMemcachedCommonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  uint64_t timeout_ms_ = 0;
  const base::Json *query_key_ = nullptr;
  std::shared_ptr<ks::infra::memc::MemcClient> mc_client_;
  std::string cluster_name_;
  std::string key_prefix_;
  std::string output_attr_name_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoMemcachedCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
