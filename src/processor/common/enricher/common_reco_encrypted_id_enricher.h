#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoEncryptedIdEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoEncryptedIdEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    const std::string mode = config()->GetString("mode");
    if (mode == "encrypt_photo_id") {
      mode_ = Mode::kEncryptPhotoId;
    } else if (mode == "decrypt_photo_id") {
      mode_ = Mode::kDecryptPhotoId;
    } else if (mode == "encrypt_live_id") {
      mode_ = Mode::kEncryptLiveId;
    } else if (mode == "decrypt_live_id") {
      mode_ = Mode::kDecryptLiveId;
    } else if (mode == "decrypt_id") {
      mode_ = Mode::kDecryptId;
    } else {
      LOG(ERROR) << "CommonRecoEncryptedIdEnricher init failed!"
                 << " 'mode' not supported: " << mode;
      return false;
    }

    input_attr_ = config()->GetString("input_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoEncryptedIdEnricher init failed!"
                 << " Missing 'input_attr' config.";
      return false;
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoEncryptedIdEnricher init failed!"
                 << " Missing 'output_attr' config.";
      return false;
    }

    is_common_attr_ = config()->GetBoolean("is_common_attr", is_common_attr_);

    return true;
  }

  void ProcessItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);

  void ProcessCommonAttr(MutableRecoContextInterface *context);

  bool IsValidInputAttrType(AttrType input_attr_type);

  int64 FixedDecryptPhotoId(const std::string &raw_id);

 private:
  std::string input_attr_;
  std::string output_attr_;
  bool reset_errno_ = false;

  enum class Mode {
    kEncryptPhotoId,
    kDecryptPhotoId,
    kEncryptLiveId,
    kDecryptLiveId,
    kDecryptId,
  } mode_;

  bool is_common_attr_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoEncryptedIdEnricher);
};

}  // namespace platform
}  // namespace ks
