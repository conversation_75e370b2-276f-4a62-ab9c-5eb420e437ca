#include "dragon/src/processor/common/enricher/common_reco_select_sign_enricher.h"

#include <utility>
#include <vector>
#include <string>
#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoSelectSignEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessCommonAttrs(context);
  } else {
    ProcessItemAttrs(context, begin, end);
  }
}

void CommonRecoSelectSignEnricher::ProcessCommonAttrs(MutableRecoContextInterface *context) {
  auto slots = context->GetIntListCommonAttr(input_slot_attr_);
  auto signs = context->GetIntListCommonAttr(input_sign_attr_);
  if (!slots || !signs) {
      CL_LOG(ERROR) << "input_slot_attr or input_sign_attr missing " << input_slot_attr_ << " | "
                    << input_sign_attr_;
      return;
  }
  if (slots->size() != signs->size()) {
      CL_LOG(ERROR) << "input_slot_attr and input_sign_attr size not match" << input_slot_attr_ << " | "
                    << input_sign_attr_ << ", size: " << slots->size() << " | " << signs->size();
      return;
  }
  std::vector<std::vector<int64>> collected_data(select_slot_number_);
  if (reserve_size_ > 0) {
    for (int i = 0; i < select_slot_number_; ++i) {
      collected_data[i].reserve(reserve_size_);
    }
  }
  for (int i = 0; i < slots->size(); ++i) {
    auto iter = select_slots_id_to_idx_.find((*slots)[i]);
    if (iter == select_slots_id_to_idx_.end()) {
      continue;
    }
    collected_data[iter->second].emplace_back((*signs)[i]);
  }
  for (int i = 0; i < select_slot_number_; ++i) {
    context->SetIntListCommonAttr(output_sign_attrs_[i], std::move(collected_data[i]));
  }
}

void CommonRecoSelectSignEnricher::ProcessItemAttrs(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  ItemAttr *slot_accessor = context->GetItemAttr(input_slot_attr_);
  ItemAttr *sign_accessor = context->GetItemAttr(input_sign_attr_);
  if (!slot_accessor || !sign_accessor) {
    CL_LOG(ERROR) << "missing attr: " << input_slot_attr_ << " or " << input_sign_attr_;
    return;
  }

  std::vector<ItemAttr *> output_accessors;
  output_accessors.reserve(select_slot_number_);
  for (int i = 0; i < select_slot_number_; ++i) {
    ItemAttr *input_attr_accessor = context->GetItemAttrAccessor(output_sign_attrs_[i]);
    output_accessors.push_back(input_attr_accessor);
  }

  std::for_each(begin, end, [&](const CommonRecoResult &item) {
    auto slots = context->GetIntListItemAttr(item, slot_accessor);
    auto signs = context->GetIntListItemAttr(item, sign_accessor);
    if (!slots.has_value() || !signs.has_value()) {
      return;
    }
    if (slots->size() != signs->size()) {
      CL_LOG(ERROR) << "not match: " << slots->size() << " vs " << signs->size();
      return;
    }

    std::vector<std::vector<int64>> collected_data(select_slot_number_);
    if (reserve_size_ > 0) {
      for (int i = 0; i < select_slot_number_; ++i) {
        collected_data[i].reserve(reserve_size_);
      }
    }
    for (int i = 0; i < slots->size(); ++i) {
      auto iter = select_slots_id_to_idx_.find((*slots)[i]);
      if (iter == select_slots_id_to_idx_.end()) {
        continue;
      }
      collected_data[iter->second].emplace_back((*signs)[i]);
    }
    for (int i = 0; i < select_slot_number_; ++i) {
      context->SetIntListItemAttr(item, output_accessors[i], std::move(collected_data[i]));
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoSelectSignEnricher, CommonRecoSelectSignEnricher)

}  // namespace platform
}  // namespace ks
