#pragma once

#include <string>
#include <unordered_set>
#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoItemAttrMissingRateEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemAttrMissingRateEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  struct AttrConfig {
    std::string item_attr;
    std::string save_rate_to;
  };

 private:
  bool InitProcessor() override {
    auto *checks = config()->Get("check");
    if (!checks || !checks->IsArray()) {
      LOG(ERROR) << "CommonRecoItemAttrMissingRateEnricher init failed!"
                 << " \"check\" config does not exist or \"check\" config is not an array.";
      return false;
    }

    for (const auto *check : checks->array()) {
      if (!check->IsObject()) {
        LOG(ERROR) << "CommonRecoItemAttrMissingRateEnricher init failed! Item of check should be a dict!"
                   << " Value found: " << check->ToString();
        return false;
      }
      AttrConfig attr_config;
      attr_config.item_attr = check->GetString("item_attr");
      attr_config.save_rate_to = check->GetString("save_rate_to");
      attrs_.emplace_back(std::move(attr_config));
    }
    return true;
  }

 private:
  std::vector<AttrConfig> attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemAttrMissingRateEnricher);
};

}  // namespace platform
}  // namespace ks
