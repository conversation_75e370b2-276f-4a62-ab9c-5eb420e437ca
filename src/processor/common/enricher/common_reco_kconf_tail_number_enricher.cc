#include "dragon/src/processor/common/enricher/common_reco_kconf_tail_number_enricher.h"

namespace ks {
namespace platform {

void CommonRecoKconfTailNumberEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  int64 test_value = 0;
  if (TryGetIntProcessorParameter(context, test_value_, &test_value)) {
    bool is_hit = ks_config_->Get()->IsOnFor(test_value);
    context->SetIntCommonAttr(output_attr_name_, is_hit ? 1 : 0);
  } else {
    CL_LOG_EVERY_N(WARNING, 100) << "cannot get tail number test value: " << test_value_->ToString();
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoKconfTailNumberEnricher, CommonRecoKconfTailNumberEnricher)

}  // namespace platform
}  // namespace ks
