#include "dragon/src/processor/common/enricher/common_reco_normalization_enricher.h"

namespace ks {
namespace platform {

void CommonRecoNormalizationEnricher::Enrich(MutableRecoContextInterface *context,
                                             RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  const int total_size = std::distance(begin, end);
  if (total_size <= 0) return;

  input_attr_accessor_ = context->GetItemAttrAccessor(input_attr_);
  std::vector<double> scores;
  for (auto it = begin; it != end; ++it) {
    scores.push_back(it->GetDoubleAttr(input_attr_accessor_).value_or(default_val_));
  }

  std::vector<double> scaled_scores;
  switch (mode_) {
    case MIN_MAX_SCALE:
      MaxMinScaled(scores, &scaled_scores);
      break;
    case MEAN_BASED_SCALE:
      MeanScaled(scores, &scaled_scores);
      break;
    case MIN_BASED_SCALE:
      MinRatioScaled(scores, &scaled_scores);
      break;
    case STDEV_BASED_SCALE:
      Standardization(scores, &scaled_scores);
      break;
    case MAX_BASED_SCALE:
      MaxScaled(scores, &scaled_scores);
      break;
    default:
      MaxMinScaled(scores, &scaled_scores);
      break;
  }

  int i = 0;
  ItemAttr *output_attr_accessor = context->GetItemAttrAccessor(output_attr_);
  for (auto it = begin; it != end; ++it) {
    if (i >= scaled_scores.size()) {
      break;
    }
    it->SetDoubleAttr(output_attr_accessor, scaled_scores[i]);
    i++;
  }
}

void CommonRecoNormalizationEnricher::MaxMinScaled(const std::vector<double> &scores,
                                                   std::vector<double> *scaled_scores) {
  double max_score = *std::max_element(std::begin(scores), std::end(scores));
  double min_score = *std::min_element(std::begin(scores), std::end(scores));
  double difference = max_score - min_score;
  for (auto score : scores) {
    double scaled_score = std::abs(difference) > eps_ ? (score - min_score) / difference : 1.0;
    if (!base::IsZero(alpha_, eps_)) {
      scaled_score += alpha_;
    }
    scaled_scores->push_back(scaled_score);
  }
}

void CommonRecoNormalizationEnricher::MeanScaled(const std::vector<double> &scores,
                                                 std::vector<double> *scaled_scores) {
  double sum = std::accumulate(std::begin(scores), std::end(scores), 0.0);
  double mean = sum / scores.size();
  double max_score = *std::max_element(std::begin(scores), std::end(scores));
  double min_score = *std::min_element(std::begin(scores), std::end(scores));
  double difference = max_score - min_score;
  for (auto score : scores) {
    double scaled_score = std::abs(difference) > eps_ ? (score - mean) / difference : 1.0;
    if (!base::IsZero(alpha_, eps_)) {
      scaled_score += alpha_;
    }
    scaled_scores->push_back(scaled_score);
  }
}

void CommonRecoNormalizationEnricher::MinRatioScaled(const std::vector<double> &scores,
                                                     std::vector<double> *scaled_scores) {
  double min_score = *std::min_element(std::begin(scores), std::end(scores));
  for (auto score : scores) {
    double scaled_score = std::abs(min_score) > eps_ ? (score - min_score) / min_score : 1.0;
    if (!base::IsZero(alpha_, eps_)) {
      scaled_score += alpha_;
    }
    scaled_scores->push_back(scaled_score);
  }
}

void CommonRecoNormalizationEnricher::Standardization(const std::vector<double> &scores,
                                                      std::vector<double> *scaled_scores) {
  if (scores.size() <= 1) return;
  double sum = std::accumulate(std::begin(scores), std::end(scores), 0.0);
  double mean = sum / scores.size();
  double stdev = sqrt(sum / (scores.size() - 1));
  for (auto score : scores) {
    double scaled_score = std::abs(stdev) > eps_ ? (score - mean) / stdev : 1.0;
    if (!base::IsZero(alpha_, eps_)) {
      scaled_score += alpha_;
    }
    scaled_scores->push_back(scaled_score);
  }
}

void CommonRecoNormalizationEnricher::MaxScaled(const std::vector<double> &scores,
                                                   std::vector<double> *scaled_scores) {
  double max_score = *std::max_element(std::begin(scores), std::end(scores));
  double min_score = *std::min_element(std::begin(scores), std::end(scores));
  max_score = std::max(std::abs(max_score), std::abs(min_score));
  for (auto score : scores) {
    double scaled_score = max_score > 0 ? score / max_score : 1.0;
    scaled_scores->push_back(scaled_score);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoNormalizationEnricher, CommonRecoNormalizationEnricher)

}  // namespace platform
}  // namespace ks
