#pragma once

#include <cmath>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/id_mapping_util.h"
#include "ks/algo-engine/formula1/src/formula1.h"
#include "third_party/abseil/absl/strings/string_view.h"

namespace ks {
namespace platform {

struct Formula1AttrConfig {
  std::string name;
  std::string as;
  bool to_common = false;
  AttrValue *accessor = nullptr;
  AttrType default_val_type = AttrType::FLOAT;
  double default_double = 0.0;
  std::string default_string;
  bool to_int = false;
  bool IsSame(const Formula1AttrConfig &other) const {
    return name == other.name && as == other.as && to_common == other.to_common &&
           default_val_type == other.default_val_type && default_double == other.default_double &&
           default_string == other.default_string && to_int == other.to_int;
  }
};

class CommonRecoFormulaOneEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoFormulaOneEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    std::string kconf_key = config()->GetString("kconf_key");
    std::string base_json = config()->GetString("base_json_str");

    if (!(kconf_key.empty() ^ base_json.empty())) {
      LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Only one of 'kconf_key' and 'base_json' "
                    "should exist";
      return false;
    }

    if (!kconf_key.empty()) {
      std::string biz_name = config()->GetString("abtest_biz_name");
      if (biz_name.empty()) {
        LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Missing 'abtest_biz_name' "
                   << "or it is empty";
        return false;
      }
      biz_ = ks::GetAbtestBizByName(biz_name);
      if (biz_ == ks::AbtestBiz::NULL_BIZ) {
        LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed!"
                   << " Invalid abtest biz_name: " << biz_name;
        return false;
      }
      formula1_ = std::make_unique<ks::reco::formula1::Formula1>(kconf_key);
    }

    if (!base_json.empty()) {
      is_json_from_kconf_ = false;
      formula1_ = std::make_unique<ks::reco::formula1::Formula1>(base_json, false);
    }
    if (!formula1_->HasInit()) {
      LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Parsing kconf failed. Check your kconf "
                 << kconf_key;
      return false;
    }

    auto *common_attr_config = config()->Get("import_common_attr");
    if (common_attr_config && !ParseAttrsConfig(common_attr_config, &import_common_attrs_)) {
      LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Failed to extract 'import_common_attr'!";
      return false;
    }

    auto *item_attr_config = config()->Get("import_item_attr");
    if (item_attr_config && !ParseAttrsConfig(item_attr_config, &import_item_attrs_)) {
      LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Failed to extract 'import_item_attr'!";
      return false;
    }

    auto *export_item_config = config()->Get("export_formula_value");
    if (export_item_config && !ParseAttrsConfig(export_item_config, &export_attrs_)) {
      LOG(ERROR) << "CommonRecoFormulaOneEnricher init failed! Failed to extract 'export_formula_value'!";
      return false;
    }

    enable_pruning_opt_ = config()->Get("enable_pruning_opt");

    prioritized_suffix_ = config()->Get("prioritized_suffix");

    return true;
  }

  bool ParseAttrsConfig(const base::Json *attrs_config, std::vector<Formula1AttrConfig> *attrs) {
    if (attrs_config && attrs_config->IsArray()) {
      double default_double = 0.0;
      std::string default_string = "";
      attrs->reserve(attrs_config->array().size());
      for (const auto *attr : attrs_config->array()) {
        Formula1AttrConfig f1_config;
        if (attr->IsObject()) {
          std::string attr_name = attr->GetString("name");
          if (attr_name.empty()) {
            return false;
          }
          std::string alias = attr->GetString("as", attr_name);
          f1_config.name = attr_name;
          f1_config.as = alias;
          if (attr->GetNumber("default_val", &default_double)) {
            f1_config.default_double = default_double;
          } else if (attr->GetString("default_val", &default_string)) {
            f1_config.default_val_type = AttrType::STRING;
            f1_config.default_string = default_string;
          }
          f1_config.to_common = attr->GetBoolean("to_common", false);
          f1_config.to_int = attr->GetBoolean("to_int", false);
        } else if (attr->IsString()) {
          const std::string &attr_name = attr->StringValue();
          if (attr_name.empty()) {
            return false;
          }
          f1_config.name = attr_name;
          f1_config.as = attr_name;
        }
        // 这里 trick: 判断指针 attrs 是否指向 import_item_attrs_ 或 import_common_attrs_
        // 来决定是将 item_attr 还是 common_attr 的 as 和 name 映射到对应的映射表中
        attrs->push_back(std::move(f1_config));
        if (attrs == &import_item_attrs_) {
          item_attr_import_mapping_[f1_config.as] = &attrs->back();
        } else if (attrs == &import_common_attrs_) {
          common_attr_import_mapping_[f1_config.as] = &attrs->back();
        }
      }
    }
    return true;
  }

  void ImportCommonAttr(MutableRecoContextInterface *context, const Formula1AttrConfig &common_attr);
  void ImportItemAttrType(MutableRecoContextInterface *context, const Formula1AttrConfig &item_attr);
  void ImportItemAttr(MutableRecoContextInterface *context, const Formula1AttrConfig &item_attr,
                      RecoResultConstIter begin, RecoResultConstIter end);

  void ExportFormulaValue(MutableRecoContextInterface *context, RecoResultConstIter begin,
                          RecoResultConstIter end);

  Formula1AttrConfig TranslateFormulaConfig(const reco::formula1::Formula1ImportConfig &import_config,
                                            MutableRecoContextInterface *context, bool is_common);

 private:
  std::unique_ptr<ks::reco::formula1::Formula1> formula1_;
  ks::AbtestBiz biz_ = ks::AbtestBiz::NULL_BIZ;
  bool init_accessors_ = false;
  std::vector<Formula1AttrConfig> import_common_attrs_;
  std::vector<Formula1AttrConfig> import_item_attrs_;
  std::vector<Formula1AttrConfig> export_attrs_;
  // key is item_attr.as, value is item_attr.name
  std::unordered_map<std::string, Formula1AttrConfig*> item_attr_import_mapping_;
  std::unordered_map<std::string, Formula1AttrConfig*> common_attr_import_mapping_;
  const base::Json *prioritized_suffix_ = nullptr;
  bool is_json_from_kconf_ = true;
  const base::Json *enable_pruning_opt_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoFormulaOneEnricher);
};

}  // namespace platform
}  // namespace ks
