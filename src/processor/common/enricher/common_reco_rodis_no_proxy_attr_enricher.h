#pragma once

#include <string>
#include <memory>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/ds/user_profile/rodis_sdk/rodis_sdk.h"
#include "teams/reco-arch/colossusdb/common/status.h"

namespace ks {
namespace platform {

class CommonRecoRodisNoProxyAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRodisNoProxyAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const {
    return true;
  }

 private:
  bool InitProcessor() override {
    kess_name_ = config()->GetString("kess_name");
    if (kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoRodisNoProxyAttrEnricher init failed! kess_name is required.";
      return false;
    }

    domain_ = config()->GetString("domain");
    if (domain_.empty()) {
      LOG(ERROR) << "CommonRecoRodisNoProxyAttrEnricher init failed! domain is required.";
      return false;
    }

    payload_id_ = config()->GetInt("payload_id", payload_id_);
    if (payload_id_ < 0) {
      LOG(ERROR) << "CommonRecoRodisNoProxyAttrEnricher init failed! payload_id is required.";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", timeout_ms_);

    key_attr_ = config()->GetString("key_attr");
    if (key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisNoProxyAttrEnricher init failed! key_attr is required.";
      return false;
    }

    value_attr_ = config()->GetString("value_attr");
    if (value_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisNoProxyAttrEnricher init failed! value_attr is required.";
      return false;
    }

    client_options_.kess_name = kess_name_;
    client_options_.caller_name = config()->GetString("caller_name");
    client_options_.caller_port = 0;
    client_options_.need_register_kess = false;
    req_options_.timeout_millis = timeout_ms_;

    valid_duration_ms_ = config()->GetInt("valid_duration_ms", valid_duration_ms_);

    request_.set_domain(domain_);
    request_.set_payload_id(payload_id_);
    request_.set_limit(1);
    request_.set_min_len(1);

    auto s = Rodis::sdk::Client::CreateClient(client_options_, &client_);
    if (!s.ok() || nullptr == client_) {
      return false;
    }

    return true;
  }

 private:
  std::string kess_name_;
  std::string domain_;
  int payload_id_ = -1;
  int timeout_ms_ = 10;
  int valid_duration_ms_ = -1;
  std::string key_attr_, value_attr_;
  ::Rodis::sdk::ClientOptions client_options_;
  ::Rodis::sdk::RequestOptions req_options_;
  ::kuaishou::ds::TimeListGetRequest request_;

  std::shared_ptr<Rodis::sdk::Client> client_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRodisNoProxyAttrEnricher);
};

}  // namespace platform
}  // namespace ks
