#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/flat_index_holder.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/module/photo_store_manager.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/common/enricher/common_reco_distributed_index_item_attr_enricher.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/photo_store/dynamic_photo_store_fetcher.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/flattened_attr_kv_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/flattened_attr_kv_photo_store_item.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

DECLARE_double(attr_perf_sample_rate);
DECLARE_bool(photo_store_fetch_required_attr_only);

// 和 CommonRecoDistributedIndexItemAttrEnricher 相比，这里默认使用 dynamic_photo_store
class CommonRecoDistributedFlatIndexEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoDistributedFlatIndexEnricher() : random_(base::GetTimestamp()) {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    { // 增加这部分功能, 用于判断是否启动 flat photo store
      if (!enable_kconf_key_.empty()) {
        context->SetIntCommonAttr("enable_flat_photo_store", enable_);
        if (!enable_) return;
      }
    }
    SetAttrMetaInfo(context);
    photo_store_rpc_req_cache_rate_ =
        GetDoubleProcessorParameter(context, "photo_store_rpc_req_cache_rate", 100.0);
    data_set_tags_bit_ = GetIntListProcessorParameter(context, "data_set_tags_bit");
    if (data_set_tags_bit_.size() == 0) {
      data_set_tags_bit_ = photo_store::DefaultDataSetTagsBit();
    }
    // 记录每个 attr accessor
    if (!has_attr_accessor_init_) {
      if (!item_id_attr_.empty()) {
        item_id_attr_accessor_ = context->GetItemAttrAccessor(item_id_attr_);
      }
      for (auto &counter : attr_counter_data_) {
        counter.accessor = context->GetItemAttrAccessor(counter.name);
      }

      has_attr_accessor_init_ = true;
    }

    // 导入正确的 item key
    index_item_keys_.clear();
    target_items_.clear();
    std::for_each(begin, end, [this, context](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      if (item_id_attr_accessor_ == nullptr) {
        uint64 id = result.GetId();
        index_item_keys_.push_back(ItemIdToIndexItemKey(item_key, id));
        target_items_.push_back(result);
      } else {
        auto p = result.GetIntAttr(item_id_attr_accessor_);
        if (p) {
          uint64 id = *p;
          index_item_keys_.push_back(id);
          target_items_.push_back(result);
        } else {
          CL_LOG_EVERY_N(WARNING, 1000)
              << "item ignored: cannot get id from int attr " << item_id_attr_ << ", item_key: " << item_key
              << RecoUtil::GetRequestInfoForLog(context);
        }
      }
    });

    if (include_browse_set_items_) {
      auto browsed_items = context->GetLatestBrowsedItems(include_browse_set_item_count_);
      for (const auto &item_key : browsed_items) {
        uint64 id = Util::GetId(item_key);
        index_item_keys_.push_back(ItemIdToIndexItemKey(item_key, id));
        target_items_.emplace_back(context->NewCommonRecoResult(item_key));
      }
    }

    for (const auto &attr_name : source_common_attrs_) {
      if (auto p = context->GetIntCommonAttr(attr_name)) {
        uint64 id = Util::GetId(*p);
        index_item_keys_.push_back(ItemIdToIndexItemKey(*p, id));
        target_items_.emplace_back(context->NewCommonRecoResult(*p));
      } else if (auto p = context->GetIntListCommonAttr(attr_name)) {
        for (const auto v : *p) {
          uint64 id = Util::GetId(v);
          index_item_keys_.push_back(ItemIdToIndexItemKey(v, id));
          target_items_.emplace_back(context->NewCommonRecoResult(v));
        }
      } else {
        CL_LOG(WARNING) << "cannot find int/int_list common_attr: " << attr_name;
      }
    }

    if (index_item_keys_.empty()) {
      CL_LOG(INFO) << "distributed index request cancelled with empty item list: " << request_info_;
      return;
    }

    QueryDynamicPhotoStore(context);
  }

 private:
  struct AttrCounter {
    std::string name;
    ItemAttr *accessor = nullptr;
    int attr_cnt = 0;
    int attr_total_size = 0;
  };
  struct AttrAlias {
    std::string alias;
    std::string ab_alternative;
  };

  void SetAttrMetaInfo(MutableRecoContextInterface *context);

  void PerfAttrInfo(MutableRecoContextInterface *context, const std::vector<CommonRecoResult> &target_items,
                    int item_hit);

  ks::photo_store::DynamicPhotoStore *GetDynamicPhotoStore();

  void SavePhotoStoreItem(const CommonRecoResult &result, ks::photo_store::Item *item);

  void BackFillContext(MutableRecoContextInterface *context,
                       const std::vector<CommonRecoResult> &target_items);

  uint64 ItemIdToIndexItemKey(uint64 item_key, uint64 item_id);

  bool InitProcessor() override {
    {  // 增加这部分功能，判断是否启动 flat photo store
      enable_kconf_key_ = config()->GetString("enable_kconf_key", "");
      if (!enable_kconf_key_.empty()) {
        enable_ = ks::infra::KConf().Get(enable_kconf_key_, true)->Get();
        if (!enable_) {
          LOG(WARNING) << "CommonRecoDistributedFlatIndex donot need init!";
          return true;  // 直接返回
        }
      }
    }

    packed_key_ = config()->GetString("packed_key", "");

    use_flat_kv_ = config()->GetBoolean("use_flat_kv", false);

    if (use_flat_kv_) {
      packed_key_ = "__packed_key__";  // 这里设置一个占位符，避免 packed_key_ 为空
    }

    // 获取 photo_store
    auto *dynamic_photo_store = GetDynamicPhotoStore();
    if (!dynamic_photo_store) {
      LOG(ERROR) << "CommonRecoDistributedFlatIndexEnricher init failed!"
                 << " failed to initialize photo store";
      return false;
    }
    dynamic_photo_store_fetcher_.Init(dynamic_photo_store);

    field_replacer_ = GetFieldReplacerFromPhotoStoreConfig(config()->GetString("photo_store_kconf_key", ""));

    request_info_ = "kess_service: " + dynamic_photo_store->GetConfig().service_name +
                    ", timeout_ms: " + std::to_string(dynamic_photo_store->GetConfig().grpc_timeout.count()) +
                    "ms";

    perf_log_ = config()->GetString("perf_log", "distributed_index");

    // 初始化 additional_item_source
    auto *item_source = config()->Get("additional_item_source");
    if (item_source) {
      if (!item_source->IsObject()) {
        LOG(ERROR) << "CommonRecoDistributedFlatIndexEnricher"
                   << " init failed! \"additional_item_source\" should"
                   << " be a dict";
        return false;
      }
      auto *common_attr = item_source->Get("common_attr");
      if (common_attr &&
          !RecoUtil::ExtractStringListFromJsonConfig(common_attr, &source_common_attrs_, true, true)) {
        LOG(ERROR) << "CommonRecoDistributedFlatIndexEnricher init failed! "
                   << " 'common_attr' should be a string array!"
                   << " Value found: " << common_attr->ToString();
        return false;
      }

      auto *latest_browse_set_item = item_source->Get("latest_browse_set_item");
      if (latest_browse_set_item) {
        include_browse_set_items_ = true;
        if (!latest_browse_set_item->IntValue(&include_browse_set_item_count_)) {
          LOG(ERROR) << "CommonRecoDistributedFlatIndexEnricher init failed!"
                     << " 'latest_browse_set_item' should be an int! Value found: "
                     << latest_browse_set_item->ToString();
          return false;
        }
      } else {
        include_browse_set_items_ = false;
      }
    }

    // 初始化 attrs
    attr_counter_data_.clear();
    auto *attrs = config()->Get("attrs");
    auto *attrs_with_type = config()->Get("attr_name_types");
    std::unordered_map<std::string, std::string> attr_names;
    if (attrs) {
      if (!RecoUtil::ParseAttrsConfig(attrs, &attr_names)) {
        LOG(ERROR) << "CommonRecoDistributedFlatIndexEnricher init failed!"
                   << " attrs parses error ";
        return false;
      }
      for (const auto &pr : attr_names) {
        // 使用原 attr 名从 flat index 中查询，使用 alias 名来查找 attr 和上报。
        auto it = attr_meta_data_->find(pr.first);
        if (it != attr_meta_data_->end()) {
          attr_counter_data_.push_back(AttrCounter{.name = pr.second});
        } else {
          LOG(WARNING) << "cannot find attr " << pr.first << " in flat_index";
        }
      }
    }
    if (attrs_with_type) {
      for (const auto pair : attrs_with_type->objects()) {
        std::string attr_name = pair.first;
        attr_names.insert({attr_name, attr_name});
        auto it = attr_meta_data_->find(attr_name);
        if (it != attr_meta_data_->end()) {
          attr_counter_data_.push_back(AttrCounter{.name = it->first});
        } else {
          LOG(WARNING) << "cannot find attr " << attr_name << " in flat_index";
        }
      }
    }

    attr_names_.clear();
    for (auto &pair : attr_names) {
      std::string ab_alternative;
      if (field_replacer_ && field_replacer_->IsReplacedField(pair.first, &ab_alternative)) {
        attr_names_.insert({pair.first, {pair.second, ab_alternative}});
      } else {
        // 没有指定替换逻辑的情况下， ab_alternative 和 attr 原名一致。
        attr_names_.insert({pair.first, {pair.second, pair.first}});
      }
    }

    item_id_attr_ = config()->GetString("item_id_attr", "");
    request_data_set_tags_attr_ = config()->GetString("photo_store_request_data_set_tags_attr", "");
    return true;
  }

  void QueryDynamicPhotoStore(MutableRecoContextInterface *context) {
    auto dynamic_item_vector = std::make_shared<ks::photo_store::DynamicPhotoStoreFetcher::ItemVector>();
    std::unordered_set<int32> request_data_set_tags =
        GetRequestDataSetTags(context, request_data_set_tags_attr_);
    RegisterAsyncCallback(
        context,
        dynamic_photo_store_fetcher_.AsyncMultiGet(index_item_keys_, dynamic_item_vector.get(), false,
                                                   perf_log_, nullptr, photo_store_rpc_req_cache_rate_,
                                                   request_data_set_tags, nullptr, data_set_tags_bit_),
        [this, context, dynamic_item_vector, target_items = std::move(target_items_)](
            const ks::photo_store::DynamicPhotoStoreFetcher::ItemVector *item_infos) {
          if (!item_infos) {
            CL_LOG_ERROR("distributed_index", "null_response")
                << "fail to get response from distributed index server, " << request_info_;
            return;
          }

          if (item_infos->size() != target_items.size()) {
            CL_LOG_ERROR("distributed_index", "response_size_mismatch")
                << "item list size mismatch, request num: " << target_items.size()
                << ", response num: " << item_infos->size() << ", requst_info: " << request_info_;
            return;
          }

          int item_hit = 0;
          for (int i = 0; i < item_infos->size(); ++i) {
            const auto &item = item_infos->at(i);
            const auto &result = target_items[i];
            if (!item) {
              CL_LOG_EVERY_N(WARNING, 1000) << "item miss in photo_store, key: " << result.item_key
                                            << ", id: " << result.GetId() << ", type: " << result.GetType();
              continue;
            }
            ++item_hit;
            SavePhotoStoreItem(result, const_cast<photo_store::Item *>(item));
          }

          BackFillContext(context, target_items);

          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              kPerfBase * item_hit / target_items.size(), kPerfNs, "forward_index.item_hit",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              target_items.size(), kPerfNs, "forward_index.item_total", GlobalHolder::GetServiceIdentifier(),
              context->GetRequestType(), GetName());

          if (random_.GetDouble() < FLAGS_attr_perf_sample_rate) {
            PerfAttrInfo(context, target_items, item_hit);
          }

          CL_LOG(INFO) << "received response from distributed index server, request num: "
                       << target_items.size() << ", response num: " << item_infos->size();
        },
        request_info_);
  }

 private:
  bool enable_ = true;
  bool has_attr_accessor_init_ = false;
  bool use_flat_kv_ = false;

  std::vector<std::string> source_common_attrs_;
  std::string enable_kconf_key_;
  std::string request_info_;
  std::string item_id_attr_;
  ItemAttr *item_id_attr_accessor_ = nullptr;
  bool include_browse_set_items_ = false;
  std::string packed_key_;
  int64 include_browse_set_item_count_ = 0;
  base::PseudoRandom random_;

  std::vector<uint64> index_item_keys_;
  std::vector<CommonRecoResult> target_items_;

  ks::photo_store::DynamicPhotoStoreFetcher dynamic_photo_store_fetcher_;
  std::string perf_log_;

  std::vector<AttrCounter> attr_counter_data_;
  std::unordered_map<std::string, AttrAlias> attr_names_;
  std::shared_ptr<folly::F14FastMap<std::string, ks::reco::protoutil::AttrMetaData>> attr_meta_data_;

  double photo_store_rpc_req_cache_rate_ = 100.0;
  std::string request_data_set_tags_attr_;
  static std::once_flag attr_meta_data_init_flag;
  static constexpr float kPerfBase = 1000.0;
  std::vector<int64> data_set_tags_bit_;

  std::shared_ptr<colossusdb::FieldReplaceKconfHolder> field_replacer_;
};

}  // namespace platform
}  // namespace ks
