#include "dragon/src/processor/common/enricher/common_reco_streaming_status_enricher.h"

namespace ks {
namespace platform {

void CommonRecoStreamingStatusEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  int status = GetIntProcessorParameter(context, "status", 0);
  if (status >= 0) {
    context->SetStreamingStatus(static_cast<StreamingStatus>(status));
  }
  if (!save_streaming_status_to_.empty()) {
    context->SetIntCommonAttr(save_streaming_status_to_, status);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStreamingStatusEnricher, CommonRecoStreamingStatusEnricher)
}  // namespace platform
}  // namespace ks

