#pragma once

#include <mutex>
#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "geoinfo/geoinfo.h"

DEFINE_int32(warmup_infra_geoinfo_wait_time_ms, 30000, "time to wait for warming up infra geoinfo");

namespace ks {
namespace platform {

class CommonRecoGeoInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoGeoInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    lat_attr_ = config()->GetString("lat_attr");
    CHECK(!lat_attr_.empty()) << "lat_attr should not be empty";
    lon_attr_ = config()->GetString("lon_attr");
    CHECK(!lon_attr_.empty()) << "lon_attr should not be empty";
    save_bdcode_to_attr_ = config()->GetString("save_bdcode_to_attr");
    save_adcode_to_attr_ = config()->GetString("save_adcode_to_attr");
    save_nation_to_attr_ = config()->GetString("save_nation_to_attr");
    save_province_to_attr_ = config()->GetString("save_province_to_attr");
    save_city_to_attr_ = config()->GetString("save_city_to_attr");
    save_county_to_attr_ = config()->GetString("save_county_to_attr");
    save_bdname_to_attr_ = config()->GetString("save_bdname_to_attr");

    std::call_once(warmup_geoinfo_once_flag_, []() {
      CHECK(ks::infra::geoinfo::GeoInfo::WarmUp(FLAGS_warmup_infra_geoinfo_wait_time_ms))
          << "Fail to warm up infra geoinfo after wait for " << FLAGS_warmup_infra_geoinfo_wait_time_ms
          << " ms";
    });

    return true;
  }

  void HandleCommonLevel(MutableRecoContextInterface *context);
  void HandleItemLevel(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);
  bool IsValideLatitude(double lat) const;
  bool IsValideLongitude(double lon) const;

 private:
  bool is_common_attr_ = true;
  std::string lat_attr_;
  std::string lon_attr_;
  std::string save_bdcode_to_attr_;
  std::string save_adcode_to_attr_;
  std::string save_nation_to_attr_;
  std::string save_province_to_attr_;
  std::string save_city_to_attr_;
  std::string save_county_to_attr_;
  std::string save_bdname_to_attr_;
  static std::once_flag warmup_geoinfo_once_flag_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoGeoInfoEnricher);
};

}  // namespace platform
}  // namespace ks
