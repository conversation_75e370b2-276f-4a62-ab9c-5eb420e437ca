#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoCopyAttrEnricher : public CommonRecoBaseEnricher {
 public:
  struct AttrConfig {
    std::string from_common;
    std::string from_item;
    std::string to_common;
    std::string to_item;
    bool overwrite = true;
  };

  CommonRecoCopyAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override {
    auto *attrs = config()->Get("attrs");
    if (!attrs || !attrs->IsArray()) {
      LOG(ERROR) << "CommonRecoCopyAttrEnricher init failed!"
                 << " \"attrs\" config does not exist or \"attrs\" config is not an array.";
      return false;
    }

    for (const auto *attr : attrs->array()) {
      if (!attr->IsObject()) {
        LOG(ERROR) << "CommonRecoCopyAttrEnricher init failed! Item of attrs should be a dict!"
                   << " Value found: " << attr->ToString();
        return false;
      }
      AttrConfig attr_config;
      attr_config.from_common = attr->GetString("from_common", "");
      attr_config.from_item = attr->GetString("from_item", "");
      attr_config.to_common = attr->GetString("to_common", "");
      attr_config.to_item = attr->GetString("to_item", "");
      attr_config.overwrite = attr->GetBoolean("overwrite", true);

      if (!(attr_config.from_common.empty() ^ attr_config.from_item.empty())) {
        LOG(ERROR) << "CommonRecoCopyAttrEnricher init failed! Item of attrs should only has one from attr!"
                   << " Value found: " << attr->ToString();
        return false;
      }

      if (!(attr_config.to_common.empty() ^ attr_config.to_item.empty())) {
        LOG(ERROR) << "CommonRecoCopyAttrEnricher init failed! Item of attrs should only has one to attr!"
                   << " Value found: " << attr->ToString();
        return false;
      }

      attrs_.emplace_back(std::move(attr_config));
    }
    return true;
  }

  void CopyCommonAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end, const std::string &from_common, const std::string &to_common,
                      const std::string &to_item, bool overwrite);
  void CopyItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                    const std::string &from_item, const std::string &to_common, const std::string &to_item,
                    bool overwrite);

 private:
  std::vector<AttrConfig> attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCopyAttrEnricher);
};

}  // namespace platform
}  // namespace ks
