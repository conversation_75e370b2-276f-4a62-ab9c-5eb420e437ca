#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class CommonRecoNormAndDiscreteEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoNormAndDiscreteEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  void HandleCommonAttr(MutableRecoContextInterface *context, const std::vector<double> &quantile_list);
  void HandleItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end, const std::vector<double> &quantile_list);
  std::pair<int, double> BinarySearch(double data, const std::vector<double> &quantile_list);

  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    input_attr_ = config()->GetString("input_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoNormAndDiscreteEnricher init failed!"
                 << " Missing 'input_attr' config.";
      return false;
    }
    output_norm_attr_ = config()->GetString("output_norm_attr");
    output_discrete_attr_ = config()->GetString("output_discrete_attr");
    return true;
  }
  void InitItemAttrAccessors(MutableRecoContextInterface *context) {
    if (!is_common_attr_) {
      if (!output_norm_attr_.empty()) {
        output_item_norm_attr_accessor_ = context->GetItemAttrAccessor(output_norm_attr_);
      }
      if (!output_discrete_attr_.empty()) {
        output_item_discrete_attr_accessor_ = context->GetItemAttrAccessor(output_discrete_attr_);
      }
    }
  }

 private:
  bool is_common_attr_ = false;
  std::string input_attr_;
  std::string output_norm_attr_;
  std::string output_discrete_attr_;
  ItemAttr *output_item_norm_attr_accessor_ = nullptr;
  ItemAttr *output_item_discrete_attr_accessor_ = nullptr;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoNormAndDiscreteEnricher);
};

}  // namespace platform
}  // namespace ks
