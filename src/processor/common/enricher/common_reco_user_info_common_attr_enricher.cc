#include "dragon/src/processor/common/enricher/common_reco_user_info_common_attr_enricher.h"

#include <utility>
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

void CommonRecoUserInfoCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  bool new_user = GetBoolProcessorParameter(context, "new_user", false);

  uint64 user_id = GetIntProcessorParameter(context, user_id_, context->GetUserId());
  std::string device_id = GetStringProcessorParameter(context, device_id_, context->GetDeviceId());

  request_.set_user_id(user_id);
  request_.set_device_id(device_id);
  request_.set_new_user(new_user);
  if (!biz_name_.empty()) {
    request_.set_biz_name(biz_name_);
  }
  if (product_ > 0) {
    request_.mutable_request_param()->set_product(::kuaishou::reco::Product(product_));
  }

  const auto &pr = [&]() {
    KESS_GRPC_ASYNC_RETURN(kess_service_, service_group_, "s0", timeout_ms_, request_, &response_,
                           kuaishou::reco::kess::RecoUserProfile, AsyncLoad);
  }();

  if (!pr.first) {
    CL_LOG(WARNING) << "failed to send user profile request: " << request_info_;
    return;
  }
  // 注册 callback 函数
  RegisterAsyncCallback(
      context, std::move(pr.second),
      [this, context](kuaishou::reco::RecoUserProfileResponse *response) {
        context->SetStringCommonAttr(save_to_common_attr_, response->user_info());
      },
      request_info_);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoUserInfoCommonAttrEnricher, CommonRecoUserInfoCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
