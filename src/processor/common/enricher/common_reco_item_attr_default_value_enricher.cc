#include "dragon/src/processor/common/enricher/common_reco_item_attr_default_value_enricher.h"

#include <utility>
#include <vector>

namespace ks {
namespace platform {

void CommonRecoItemAttrDefaultValueEnricher::Enrich(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  for (auto &attr_value_config : common_attr_value_configs_) {
    auto *common_attr = context->GetCommonAttrAccessor(attr_value_config.attr_name);
    if (common_attr->value_type != AttrType::UNKNOWN &&
        common_attr->value_type != attr_value_config.attr_type) {
      CL_LOG_ERROR("set_attr_value", "type_mismatch")
          << "type_mismatch for common attr: " << attr_value_config.attr_name;
      continue;
    }
    if (no_overwrite_ && common_attr->value_type != AttrType::UNKNOWN) {
      continue;
    }
    SetCommonAttrValues(context, attr_value_config, common_attr);
  }

  for (auto &attr_value_config : item_attr_value_configs_) {
    attr_value_config.accessor = context->GetItemAttrAccessor(attr_value_config.attr_name);
    if (attr_value_config.accessor->value_type != AttrType::UNKNOWN &&
        attr_value_config.accessor->value_type != attr_value_config.attr_type) {
      CL_LOG_ERROR("set_attr_value", "type_mismatch")
          << "type_mismatch for item attr: " << attr_value_config.attr_name;
      continue;
    }
    for (auto itr = begin; itr != end; ++itr) {
      SetItemAttrValues(context, attr_value_config, *itr);
    }
  }
}

void CommonRecoItemAttrDefaultValueEnricher::SetCommonAttrValues(MutableRecoContextInterface *context,
                                                                 const AttrValueConfig &attr_value_config,
                                                                 CommonAttr *common_attr) {
  switch (attr_value_config.attr_type) {
    case AttrType::INT: {
      context->SetIntCommonAttr(common_attr, attr_value_config.int_value, no_overwrite_);
      break;
    }
    case AttrType::FLOAT: {
      context->SetDoubleCommonAttr(common_attr, attr_value_config.double_value, no_overwrite_);
      break;
    }
    case AttrType::STRING: {
      context->SetStringCommonAttr(common_attr, attr_value_config.string_value, no_overwrite_);
      break;
    }
    case AttrType::INT_LIST: {
      auto list_value = attr_value_config.int_list_value;
      context->SetIntListCommonAttr(common_attr, std::move(list_value), no_overwrite_);
      break;
    }
    case AttrType::FLOAT_LIST: {
      auto list_value = attr_value_config.double_list_value;
      context->SetDoubleListCommonAttr(common_attr, std::move(list_value), no_overwrite_);
      break;
    }
    case AttrType::STRING_LIST: {
      auto list_value = attr_value_config.string_list_value;
      context->SetStringListCommonAttr(common_attr, std::move(list_value), no_overwrite_);
      break;
    }
    default:
      break;
  }
}

void CommonRecoItemAttrDefaultValueEnricher::SetItemAttrValues(MutableRecoContextInterface *context,
                                                               const AttrValueConfig &attr_value_config,
                                                               const CommonRecoResult &result) {
  if (no_overwrite_ && result.HasAttr(attr_value_config.accessor)) return;
  switch (attr_value_config.attr_type) {
    case AttrType::INT: {
      context->SetIntItemAttr(result, attr_value_config.accessor, attr_value_config.int_value, no_overwrite_);
      break;
    }
    case AttrType::FLOAT: {
      context->SetDoubleItemAttr(result, attr_value_config.accessor, attr_value_config.double_value,
                                 no_overwrite_);
      break;
    }
    case AttrType::STRING: {
      context->SetStringItemAttr(result, attr_value_config.accessor, attr_value_config.string_value,
                                 no_overwrite_);
      break;
    }
    case AttrType::INT_LIST: {
      auto list_value = attr_value_config.int_list_value;
      context->SetIntListItemAttr(result, attr_value_config.accessor, std::move(list_value), no_overwrite_);
      break;
    }
    case AttrType::FLOAT_LIST: {
      auto list_value = attr_value_config.double_list_value;
      context->SetDoubleListItemAttr(result, attr_value_config.accessor, std::move(list_value),
                                     no_overwrite_);
      break;
    }
    case AttrType::STRING_LIST: {
      auto list_value = attr_value_config.string_list_value;
      context->SetStringListItemAttr(result, attr_value_config.accessor, std::move(list_value),
                                     no_overwrite_);
      break;
    }
    default:
      break;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemAttrDefaultValueEnricher,
                 CommonRecoItemAttrDefaultValueEnricher)

}  // namespace platform
}  // namespace ks
