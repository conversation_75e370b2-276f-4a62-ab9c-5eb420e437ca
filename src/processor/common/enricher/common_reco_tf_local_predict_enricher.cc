#include "dragon/src/processor/common/enricher/common_reco_tf_local_predict_enricher.h"

#include <algorithm>
#include <unordered_map>
#include <utility>
#include <vector>

DEFINE_bool(enable_tf_local_predict_enricher_profiling, false, "enable tf local predict enricher profiling");
DEFINE_int64(tf_local_predict_enricher_network_update_interval, 300, "network update interval");

namespace ks {
namespace platform {

void CommonRecoTfLocalPredictEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  std::vector<tensorflow::Tensor> feed_tensors;
  std::vector<tensorflow::Tensor> fetch_tensors;
  tensorflow::Status status;
  // 准备预估参数
  for (const auto &tf_input : tf_inputs_) {
    if (tf_input.attr_type == "pb" || tf_input.attr_type == "PB") {
      const auto *ptr = context->GetPtrCommonAttr<::google::protobuf::Message>(tf_input.attr_name);
      if (ptr == nullptr) {
        CL_LOG_ERROR("tf_local_predict", "get_pb_fail")
            << "CommonRecoTfLocalPredictEnricher failed to get pb message from common attr: "
            << tf_input.attr_name;
        return;
      }
      std::string pb_str;
      ptr->SerializeToString(&pb_str);
      auto input = tensorflow::Tensor(tensorflow::DataType::DT_STRING, {1});
      input.vec<std::string>()(0) = pb_str;
      feed_tensors.push_back(input);
    }
  }
  if (!feed_tensors_placeholder_.size()) {
    feed_tensors_placeholder_ = feed_tensors;
  }

  std::shared_ptr<tensorflow::RunMetadata> run_metadata;
  if (FLAGS_enable_tf_local_predict_enricher_profiling) {
    run_metadata = std::make_shared<tensorflow::RunMetadata>();
  }
  int64 start_ts = base::GetTimestamp();
  status = session_->RunCallable(callable_handle_, feed_tensors, &fetch_tensors, run_metadata.get());
  CL_LOG(INFO) << "tf_local_predict RunCallable finished, time cost: "
               << (base::GetTimestamp() - start_ts) / 1000.0 << "ms";

  if (!status.ok()) {
    CL_LOG_ERROR("tf_local_predict", "RunCallable_fail")
        << "CommonRecoTfLocalPredictEnricher Failed to run callable: " << status.ToString();
    return;
  }

  if (fetch_tensors.size() < output_tensor_aliases_.size()) {
    CL_LOG_ERROR("tf_local_predict", "invalid_fetch_tensors_size")
        << "CommonRecoTfLocalPredictEnricher expect " << output_tensor_aliases_.size() << " fetch_tensors "
        << "while got " << fetch_tensors.size();
    return;
  }

  if (FLAGS_enable_tf_local_predict_enricher_profiling) {
    tensorflow::StatSummarizerOptions sop;
    tensorflow::StatSummarizer stat_summarizer(sop);
    auto &step_stats = run_metadata->step_stats();
    stat_summarizer.ProcessStepStats(step_stats);
    stat_summarizer.PrintStepStats();
  }

  // PULL_MODEL_VARIABLE 是最后一个 tensor，只取前 output_tensor_aliases_.size() 个 tensor 即可
  int num_items = std::distance(begin, end);
  for (int output_offset = 0; output_offset < output_tensor_aliases_.size(); output_offset++) {
    const auto &output_tensor = fetch_tensors[output_offset];
    const std::string &output_attr_name = output_tensor_aliases_[output_offset];

    if (output_tensor.dtype() != tensorflow::DataType::DT_FLOAT) {
      CL_LOG_ERROR("tf_local_predict", "invalid_fetch_tensor_dtype")
          << "CommonRecoTfLocalPredictEnricher returns a tensor " << output_attr_name << "[" << output_offset
          << "] with dtype other than float: " << output_tensor.dtype();
      continue;
    }

    if (save_to_common_attr_) {
      if (output_tensor.dims() == 1 && output_tensor.dim_size(0) == 1) {
        const auto &vec = output_tensor.vec<float>();
        context->SetDoubleCommonAttr(output_attr_name, vec(0));
      } else if (output_tensor.dims() == 2 && output_tensor.dim_size(0) == 1) {
        const auto &matrix = output_tensor.matrix<float>();
        std::vector<double> v(matrix.dimension(1));
        for (int offset = 0; offset < matrix.dimension(1); offset++) {
          v[offset] = matrix(0, offset);
        }
        context->SetDoubleListCommonAttr(output_attr_name, std::move(v));
      } else {
        CL_LOG_ERROR("tf_local_predict", "invalid_fetch_tensor_shape")
            << "CommonRecoTfLocalPredictEnricher returns a tensor " << output_attr_name
            << " (offset:" << output_offset
            << ") with supported shape for common attr: " << output_tensor.shape();
      }
    } else {
      if (output_tensor.dims() == 1 && output_tensor.dim_size(0) == num_items) {
        const auto &vec = output_tensor.vec<float>();
        auto result_iter = begin;
        auto *accessor = context->GetItemAttrAccessor(output_attr_name);
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          result_iter->SetDoubleAttr(accessor, vec(item_offset));
          result_iter = std::next(result_iter);
        }
      } else if (output_tensor.dims() == 2 && output_tensor.dim_size(0) == num_items) {
        const auto &matrix = output_tensor.matrix<float>();
        auto result_iter = begin;
        auto *accessor = context->GetItemAttrAccessor(output_attr_name);
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          std::vector<double> v(matrix.dimension(1));
          for (int offset = 0; offset < matrix.dimension(1); offset++) {
            v[offset] = matrix(item_offset, offset);
          }
          result_iter->SetDoubleListAttr(accessor, std::move(v));
          result_iter = std::next(result_iter);
        }
      } else {
        CL_LOG_ERROR("tf_local_predict", "invalid_fetch_tensor_shape")
            << "CommonRecoTfLocalPredictEnricher returns a tensor " << output_attr_name
            << " (offset:" << output_offset
            << ") with supported shape for item attr: " << output_tensor.shape();
      }
    }
  }
}

void CommonRecoTfLocalPredictEnricher::UpdateNetwork() {
  const int64 kInterval = FLAGS_tf_local_predict_enricher_network_update_interval * 1000l * 1000l;
  int64 last_update_ts = 0;
  while (!stop_) {
    auto current_ts = base::GetTimestamp();
    if (feed_tensors_placeholder_.size() == 0 || current_ts < last_update_ts + kInterval) {
      base::SleepForSeconds(1);
      continue;
    }

    std::vector<tensorflow::Tensor> fetch_tensors;
    int64 start_ts = base::GetTimestamp();
    bool success = false;
    while (!success) {
      tensorflow::Status status = session_->RunCallable(update_network_callable_handle_,
                                                        feed_tensors_placeholder_, &fetch_tensors, nullptr);
      if (!status.ok()) {
        CL_LOG(INFO) << "UpdateNetwork failed " << status.ToString() << " retry";
        base::SleepForMilliseconds(100);
        continue;
      }
      success = true;
    }
    CL_LOG(INFO) << "UpdateNetwork finished, time cost: " << (base::GetTimestamp() - start_ts) / 1000.0
                 << "ms";
    last_update_ts = base::GetTimestamp();
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTfLocalPredictEnricher, CommonRecoTfLocalPredictEnricher)

}  // namespace platform
}  // namespace ks
