#pragma once

#include <curl/curl.h>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

struct BodyJson {
  std::vector<BodyJson> childrens_array_;
  std::map<std::string, BodyJson> childrens_map_;
  std::map<std::string, const base::Json *> value_map_;
  bool is_array_ = false;

  bool InitBodyJson(const base::J<PERSON> *json);
};

class CommonRecoHttpCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoHttpCommonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  typedef std::map<std::string, std::string> HeaderFields;

  // 使用单例确保 curl_global_init 和 curl_global_cleanup 只执行一次
  class CurlSingletonManager {
   public:
    static CurlSingletonManager *Instance() {
      static CurlSingletonManager instance;
      return &instance;
    }

    bool CheckCurlCode() {
      return res_ == CURLE_OK;
    }
    CurlSingletonManager(const CurlSingletonManager &) = delete;
    CurlSingletonManager &operator=(const CurlSingletonManager &) = delete;

   private:
    CurlSingletonManager() {
      res_ = curl_global_init(CURL_GLOBAL_ALL);
    }

    ~CurlSingletonManager() {
      curl_global_cleanup();
    }

    CURLcode res_;
  };

  struct Response {
    int code = 0;
    std::string body;
    HeaderFields headers;
  };

  class HTTPClient {
   public:
    explicit HTTPClient(const std::string &baseUrl, bool keep_alive, bool forbid_reuse);

    ~HTTPClient();

    void SetHeaders(const std::string &key, const std::string &value);

    Response get(const std::string &path);

    Response post(const std::string &path, const std::string &data);

    bool CheckCurlHandle();

    void SetTimeout(int timeout_ms);

    void ClearHeaders();

   private:
    CURL *curlHandle_ = nullptr;
    std::string baseUrl_;
    HeaderFields headerFields_;
    int timeout_ms_ = 0;
    bool keep_alive_ = true;
    bool forbid_reuse_ = false;

    Response PerformCurlRequest(const std::string &path);
  };

 private:
  struct HttpConfigJson {
    std::string name;
    const base::Json *value_config = nullptr;
  };

 private:
  Json::Value serialize(MutableRecoContextInterface *context, const BodyJson &body);

  void OnExit(ReadableRecoContextInterface *context) override {
    if (client_) {
      client_->ClearHeaders();
    }
  }

  bool InitProcessor() override {
    CurlSingletonManager *singleton = CurlSingletonManager::Instance();
    if (!singleton) {
      LOG(ERROR) << "CommonRecoHttpCommonAttrEnricher init failed!"
                 << " Failed to create CurlSingletonManager.";
      return false;
    }
    if (!singleton->CheckCurlCode()) {
      LOG(ERROR) << "CommonRecoHttpCommonAttrEnricher init failed!"
                 << " curl_global_init failed.";
      return false;
    }

    url_ = config()->GetString("url", "");
    if (url_.empty()) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                 << " Missing 'host' config.";
      return false;
    }

    path_ = config()->GetString("path", "");
    if (path_.empty()) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                 << " Missing 'path' config.";
      return false;
    }

    method_ = config()->GetString("method", "");
    if (method_.empty()) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                 << " Missing 'method' config.";
      return false;
    }

    output_common_attr_ = config()->GetString("output_common_attr", "");
    if (output_common_attr_.empty()) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                 << " Missing 'output_common_attr' config.";
      return false;
    }

    auto *headers = config()->Get("headers");
    if (headers && headers->IsArray()) {
      for (const auto *cfg : headers->array()) {
        if (!cfg || !cfg->IsObject()) {
          LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                     << " 'headers' config should be an array of objects.";
          return false;
        }
        HttpConfigJson http_header;
        http_header.name = cfg->GetString("name");
        if (http_header.name.empty()) {
          LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                     << " Missing 'name' in 'header' config.";
          return false;
        } else {
          http_header.value_config = cfg->Get("value");
          http_headers_.push_back(http_header);
        }
      }
    }

    auto *body = config()->Get("body");
    if (body && !http_body_.InitBodyJson(body)) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!";
      return false;
    }

    keep_alive_ = config()->GetBoolean("keep_alive", true);
    forbid_reuse_ = config()->GetBoolean("forbid_reuse", false);

    client_ = std::make_unique<HTTPClient>(url_, keep_alive_, forbid_reuse_);
    if (!client_ || !client_->CheckCurlHandle()) {
      LOG(ERROR) << "CommonRecoHttpsCommonAttrEnricher init failed!"
                 << " Failed to create HTTPClient.";
      return false;
    }

    return true;
  }

 private:
  std::string url_;
  std::string path_;
  std::string method_;
  std::string output_common_attr_;
  int timeout_ms_ = 0;
  bool keep_alive_ = true;
  bool forbid_reuse_ = false;

  std::vector<HttpConfigJson> http_headers_;
  BodyJson http_body_;
  std::unique_ptr<HTTPClient> client_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoHttpCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
