#include "dragon/src/processor/common/enricher/common_reco_item_attr_pack_enricher.h"

#include <algorithm>
#include <limits>

#include "dragon/src/interop/util.h"

#define CONCAT_INT_LIST_VALUE(BEGIN)                                                                 \
  {                                                                                                  \
    auto begin = BEGIN;                                                                              \
    int offset =                                                                                     \
        mapping.item_attr_limit > 0 ? std::min<int>(mapping.item_attr_limit, p->size()) : p->size(); \
    auto end = begin + offset;                                                                       \
    for (auto it = begin; it != end; ++it) {                                                         \
      if (CheckDup(mapping.dedup_to_common_attr, &dedup_set, *it)) {                                 \
        context->AppendIntListCommonAttr(mapping.to_common_attr, *it);                               \
      }                                                                                              \
    }                                                                                                \
  }

#define CONCAT_DOUBLE_LIST_VALUE(BEGIN)                                                              \
  {                                                                                                  \
    auto begin = BEGIN;                                                                              \
    int offset =                                                                                     \
        mapping.item_attr_limit > 0 ? std::min<int>(mapping.item_attr_limit, p->size()) : p->size(); \
    auto end = begin + offset;                                                                       \
    for (auto it = begin; it != end; ++it) {                                                         \
      context->AppendDoubleListCommonAttr(mapping.to_common_attr, *it);                              \
    }                                                                                                \
  }

#define CONCAT_STRING_LIST_VALUE(BEGIN)                                                                   \
  {                                                                                                       \
    auto begin = BEGIN;                                                                                   \
    int offset =                                                                                          \
        mapping.item_attr_limit > 0 ? std::min<int>(mapping.item_attr_limit, p->size()) : p->size();      \
    auto end = begin + offset;                                                                            \
    for (auto it = begin; it != end; ++it) {                                                              \
      if (CheckDup(mapping.dedup_to_common_attr, &dedup_set, base::CityHash64(it->data(), it->size()))) { \
        context->AppendStringListCommonAttr(mapping.to_common_attr, std::string(it->data(), it->size())); \
      }                                                                                                   \
    }                                                                                                     \
  }

namespace ks {
namespace platform {

void CommonRecoItemAttrPackEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  for (auto &mapping : attr_convert_mapping_) {
    mapping.item_attr_limit =
        GetIntProcessorParameter(context, mapping.dynamic_item_attr_limit, (int64)0);
  }
  int total_limit = GetIntProcessorParameter(context, total_limit_cfg_, (int64)0);

  all_items_.clear();
  if (include_reco_results_) {
    for (auto it = begin; it != end; ++it) {
      if (total_limit > 0 && all_items_.size() >= total_limit) {
        break;
      }
      all_items_.push_back(it->item_key);
    }
  }

  if (include_browse_set_) {
    auto browsed_items = context->GetLatestBrowsedItems(latest_browsed_num_);
    for (const auto key : browsed_items) {
      if (total_limit > 0 && all_items_.size() >= total_limit) {
        break;
      }
      all_items_.push_back(key);
    }
  }

  for (const auto &attr_name : source_common_attrs_) {
    if (total_limit > 0 && all_items_.size() >= total_limit) {
      break;
    }
    if (auto p = context->GetIntCommonAttr(attr_name)) {
      all_items_.push_back(*p);
    } else if (auto p = context->GetIntListCommonAttr(attr_name)) {
      if (reverse_list_order_) {
        int len = p->size();
        if (single_limit_ > 0) {
          len = std::min<int>(single_limit_, len);
        }
        if (total_limit > 0) {
          len = std::min<int>(total_limit - all_items_.size(), len);
        }
        auto rend = p->rbegin() + len;
        std::copy(p->rbegin(), rend, std::back_inserter(all_items_));
      } else {
        for (int i = 0; i < p->size(); ++i) {
          if (single_limit_ > 0 && i >= single_limit_) {
            break;
          }
          if (total_limit > 0 && all_items_.size() >= total_limit) {
            break;
          }
          all_items_.push_back(p->at(i));
        }
      }
    } else {
      VLOG(100) << "cannot find int/int_list common_attr: " << attr_name;
    }
  }

  for (const auto &mapping : attr_convert_mapping_) {
    HandleAttrMapping(context, all_items_, mapping);
  }
}

void CommonRecoItemAttrPackEnricher::HandleAttrMapping(MutableRecoContextInterface *context,
                                                       const std::vector<uint64> &item_keys,
                                                       const AttrPackMapping &mapping) {
  switch (mapping.aggregator) {
    case Aggregator::CONCAT:
      AppendItemAttrs(context, item_keys, mapping);
      break;
    case Aggregator::COPY:
      CopyItemAttr(context, item_keys, mapping);
      break;
    case Aggregator::AVG:
    case Aggregator::SUM:
    case Aggregator::DEV:
    case Aggregator::MIN:
    case Aggregator::MAX:
      AggregateItemAttrs(context, item_keys, mapping);
      break;
    default:
      CL_LOG_ERROR("item_attr_pack", "invalid_aggregator")
          << "invalid aggregator value: " << (int)mapping.aggregator;
      break;
  }
}

void CommonRecoItemAttrPackEnricher::CopyItemAttr(MutableRecoContextInterface *context,
                                                  const std::vector<uint64> &item_keys,
                                                  const AttrPackMapping &mapping) {
  if (mapping.from_item_attr.empty()) {
    CL_LOG_WARNING("item_attr_pack", "empty_from_item_attr")
        << "item_attr_pack cancelled for common_attr " << mapping.to_common_attr
        << ", due to empty from_item_attr";
    return;
  }
  if (item_keys.empty()) {
    CL_LOG_WARNING("item_attr_pack", "empty_item_keys")
        << "item_attr_pack cancelled for common_attr " << mapping.to_common_attr
        << ", due to empty item_keys";
    return;
  }
  if (mapping.reset_to_common_attr) {
    context->ClearCommonAttr(mapping.to_common_attr);
  }
  interop::CopyItemAttrToCommonAttr(context, item_keys.front(), mapping.from_item_attr,
                                    mapping.to_common_attr);
}

void CommonRecoItemAttrPackEnricher::AggregateItemAttrs(MutableRecoContextInterface *context,
                                                        const std::vector<uint64> &item_keys,
                                                        const AttrPackMapping &mapping) {
  if (mapping.from_item_attr.empty()) {
    CL_LOG_WARNING("item_attr_pack", "empty_from_item_attr")
        << "item_attr_pack cancelled for common_attr " << mapping.to_common_attr
        << ", due to empty from_item_attr";
    return;
  }

  if (mapping.reset_to_common_attr) {
    context->ClearCommonAttr(mapping.to_common_attr);
  }
  const AttrType value_type = context->GetItemAttrType(mapping.from_item_attr);

  double double_sum = 0.0;
  double double_square_sum = 0.0;
  std::vector<double> double_vec_sum;

  int64 int_sum = 0;
  std::vector<int64> int_vec_sum;

  int64 int_min = std::numeric_limits<int64>::max();
  int64 int_max = std::numeric_limits<int64>::lowest();
  double double_min = std::numeric_limits<double>::max();
  double double_max = std::numeric_limits<double>::lowest();

  int count = 0;
  for (const uint64 item_key : item_keys) {
    if (!mapping.pack_if.empty()) {
      auto condition_attr = context->GetIntItemAttr(item_key, mapping.pack_if);
      if (!condition_attr || *condition_attr <= 0) {
        continue;
      }
    }

    if (value_type == AttrType::FLOAT_LIST) {
      auto p = context->GetDoubleListItemAttr(item_key, mapping.from_item_attr);
      if (!p) continue;
      if (double_vec_sum.empty()) {
        double_vec_sum.assign(p->begin(), p->end());
        ++count;
      } else {
        if (p->size() != double_vec_sum.size()) {
          CL_LOG_EVERY_N(WARNING, 1000) << "double list item_attr size mismatch, "
                                        << "expect: " << double_vec_sum.size() << ", actual: " << p->size()
                                        << ", item_key: " << item_key;
          continue;
        }
        for (int i = 0; i < p->size(); ++i) {
          double_vec_sum[i] += p->at(i);
        }
        ++count;
      }
    } else if (value_type == AttrType::FLOAT) {
      auto p = context->GetDoubleItemAttr(item_key, mapping.from_item_attr);
      if (p) {
        double_sum += *p;
        double_square_sum += std::pow(*p, 2);
        double_min = std::min(double_min, *p);
        double_max = std::max(double_max, *p);
        ++count;
      } else if (mapping.default_val_type != AttrType::UNKNOWN) {
        if (mapping.default_val_type == AttrType::FLOAT) {
          double_sum += mapping.default_double_val;
          double_square_sum += (mapping.default_double_val * mapping.default_double_val);
          double_min = std::min(double_min, mapping.default_double_val);
          double_max = std::max(double_max, mapping.default_double_val);
          ++count;
        } else {
          CL_LOG_WARNING("item_attr_pack", "invalid_default_val")
              << "invalid default_val, should be a number.";
        }
      }
    } else if (value_type == AttrType::INT_LIST) {
      auto p = context->GetIntListItemAttr(item_key, mapping.from_item_attr);
      if (!p) continue;
      if (int_vec_sum.empty()) {
        int_vec_sum.assign(p->begin(), p->end());
        ++count;
      } else {
        if (p->size() != int_vec_sum.size()) {
          CL_LOG_EVERY_N(WARNING, 1000) << "int list item_attr size mismatch, expect: " << int_vec_sum.size()
                                        << ", actual: " << p->size() << ", item_key: " << item_key;
          continue;
        }
        for (int i = 0; i < p->size(); ++i) {
          int_vec_sum[i] += p->at(i);
        }
        ++count;
      }
    } else if (value_type == AttrType::INT) {
      auto p = context->GetIntItemAttr(item_key, mapping.from_item_attr);
      if (p) {
        int_sum += *p;
        int_min = std::min(int_min, *p);
        int_max = std::max(int_max, *p);
        ++count;
      } else if (mapping.default_val_type != AttrType::UNKNOWN) {
        if (mapping.default_val_type == AttrType::INT) {
          int_sum += mapping.default_int_val;
          int_min = std::min(int_min, mapping.default_int_val);
          int_max = std::max(int_max, mapping.default_int_val);
          ++count;
        } else {
          CL_LOG_WARNING("item_attr_pack", "invalid_default_val") << "invalid default_val, should be a int.";
        }
      }
    } else {
      CL_LOG_EVERY_N(WARNING, 100) << "cannot get double/double_list attr of " << mapping.from_item_attr
                                   << " for item_key: " << item_key;
    }
  }

  if (value_type == AttrType::FLOAT_LIST) {
    if (mapping.aggregator == Aggregator::SUM) {
      context->SetDoubleListCommonAttr(mapping.to_common_attr, std::move(double_vec_sum));
    } else if (mapping.aggregator == Aggregator::AVG) {
      if (count > 0) {
        for (int i = 0; i < double_vec_sum.size(); ++i) {
          double_vec_sum[i] /= count;
        }
      }
      context->SetDoubleListCommonAttr(mapping.to_common_attr, std::move(double_vec_sum));
    } else {
      CL_LOG_WARNING("item_attr_pack", "unsupported_aggregator")
          << "unsupported aggregator for double_list attr: " << mapping.from_item_attr;
    }
  } else if (value_type == AttrType::FLOAT) {
    if (count > 0) {
      if (mapping.aggregator == Aggregator::SUM) {
        context->SetDoubleCommonAttr(mapping.to_common_attr, double_sum);
      } else if (mapping.aggregator == Aggregator::AVG) {
        context->SetDoubleCommonAttr(mapping.to_common_attr, double_sum / count);
      } else if (mapping.aggregator == Aggregator::MIN) {
        context->SetDoubleCommonAttr(mapping.to_common_attr, double_min);
      } else if (mapping.aggregator == Aggregator::MAX) {
        context->SetDoubleCommonAttr(mapping.to_common_attr, double_max);
      } else if (mapping.aggregator == Aggregator::DEV) {
        context->SetDoubleCommonAttr(mapping.to_common_attr,
            double_square_sum / count - std::pow(double_sum / count, 2));
      } else {
        CL_LOG_WARNING("item_attr_pack", "unsupported_aggregator")
            << "unsupported aggregator for double attr: " << mapping.from_item_attr;
      }
    }
  } else if (value_type == AttrType::INT_LIST) {
    if (mapping.aggregator == Aggregator::SUM) {
      context->SetIntListCommonAttr(mapping.to_common_attr, std::move(int_vec_sum));
    } else {
      CL_LOG_WARNING("item_attr_pack", "unsupported_aggregator")
          << "unsupported aggregator for int_list attr: " << mapping.from_item_attr;
    }
  } else if (value_type == AttrType::INT) {
    if (count > 0) {
      if (mapping.aggregator == Aggregator::SUM) {
        context->SetIntCommonAttr(mapping.to_common_attr, int_sum);
      } else if (mapping.aggregator == Aggregator::MIN) {
        context->SetIntCommonAttr(mapping.to_common_attr, int_min);
      } else if (mapping.aggregator == Aggregator::MAX) {
        context->SetIntCommonAttr(mapping.to_common_attr, int_max);
      } else {
        CL_LOG_WARNING("item_attr_pack", "unsupported_aggregator")
            << "unsupported aggregator for int attr: " << mapping.from_item_attr;
      }
    }
  }
}

void CommonRecoItemAttrPackEnricher::AppendItemAttrs(MutableRecoContextInterface *context,
                                                     const std::vector<uint64> &item_keys,
                                                     const AttrPackMapping &mapping) {
  if (mapping.reset_to_common_attr) {
    context->ClearCommonAttr(mapping.to_common_attr);
  }
  folly::F14FastSet<uint64> dedup_set;
  for (const uint64 item_key : item_keys) {
    if (!mapping.pack_if.empty()) {
      auto condition_attr = context->GetIntItemAttr(item_key, mapping.pack_if);
      if (!condition_attr || *condition_attr <= 0) {
        continue;
      }
    }

    if (mapping.from_item_attr.empty()) {
      if (CheckDup(mapping.dedup_to_common_attr, &dedup_set, item_key)) {
        context->AppendIntListCommonAttr(mapping.to_common_attr, item_key);
      }
      continue;
    }

    switch (context->GetItemAttrType(mapping.from_item_attr)) {
      case AttrType::INT: {
        auto p = context->GetIntItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          if (CheckDup(mapping.dedup_to_common_attr, &dedup_set, *p)) {
            context->AppendIntListCommonAttr(mapping.to_common_attr, *p);
          }
        } else if (mapping.default_val_type != AttrType::UNKNOWN) {
          if (mapping.default_val_type == AttrType::INT) {
            context->AppendIntListCommonAttr(mapping.to_common_attr, mapping.default_int_val);
          } else {
            CL_LOG_WARNING("item_attr_pack", "invalid_default_val")
                << "invalid default_val, should be a int.";
          }
        }
        break;
      }
      case AttrType::FLOAT: {
        auto p = context->GetDoubleItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          context->AppendDoubleListCommonAttr(mapping.to_common_attr, *p);
        } else if (mapping.default_val_type != AttrType::UNKNOWN) {
          if (mapping.default_val_type == AttrType::FLOAT) {
            context->AppendDoubleListCommonAttr(mapping.to_common_attr, mapping.default_double_val);
          } else {
            CL_LOG_WARNING("item_attr_pack", "invalid_default_val")
                << "invalid default_val, should be a number.";
          }
        }
        break;
      }
      case AttrType::STRING: {
        auto p = context->GetStringItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          if (CheckDup(mapping.dedup_to_common_attr, &dedup_set, base::CityHash64(p->data(), p->size()))) {
            context->AppendStringListCommonAttr(mapping.to_common_attr, std::string(p->data(), p->size()));
          }
        } else if (mapping.default_val_type != AttrType::UNKNOWN) {
          if (mapping.default_val_type == AttrType::STRING) {
            context->AppendStringListCommonAttr(mapping.to_common_attr, mapping.default_string_val);
          } else {
            CL_LOG_WARNING("item_attr_pack", "invalid_default_val")
                << "invalid default_val, should be a string.";
          }
        }
        break;
      }
      case AttrType::INT_LIST: {
        auto p = context->GetIntListItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          if (reverse_list_order_) {
            CONCAT_INT_LIST_VALUE(p->rbegin());
          } else {
            CONCAT_INT_LIST_VALUE(p->begin());
          }
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        auto p = context->GetDoubleListItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          if (reverse_list_order_) {
            CONCAT_DOUBLE_LIST_VALUE(p->rbegin());
          } else {
            CONCAT_DOUBLE_LIST_VALUE(p->begin());
          }
        }
        break;
      }
      case AttrType::STRING_LIST: {
        auto p = context->GetStringListItemAttr(item_key, mapping.from_item_attr);
        if (p) {
          if (reverse_list_order_) {
            CONCAT_STRING_LIST_VALUE(p->rbegin());
          } else {
            CONCAT_STRING_LIST_VALUE(p->begin());
          }
        }
        break;
      }
      default:
        VLOG(100) << "cannot get int/double/string attr of " << mapping.from_item_attr
                  << " for item_key: " << item_key;
        break;
    }
  }
}

bool CommonRecoItemAttrPackEnricher::CheckDup(bool dedup_to_common_attr, folly::F14FastSet<uint64> *dedup_set,
                                              uint64 value) {
  if (!dedup_to_common_attr) {
    return true;
  }
  if (dedup_set->find(value) != dedup_set->end()) {
    return false;
  }
  dedup_set->insert(value);
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemAttrPackEnricher, CommonRecoItemAttrPackEnricher)

}  // namespace platform
}  // namespace ks
