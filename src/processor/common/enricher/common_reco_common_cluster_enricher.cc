#include "dragon/src/processor/common/enricher/common_reco_common_cluster_enricher.h"

#include <utility>
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

void CommonRecoCommonClusterEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.common_cluster",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "empty_kess_service");
    CL_LOG_EVERY_N(WARNING, 100) << "common cluster request cancelled: empty kess_service!";
    return;
  }

  std::string bucket = GetStringProcessorParameter(context, "bucket");
  if (bucket.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.common_cluster",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "empty_bucket");
    CL_LOG_EVERY_N(WARNING, 100) << "common cluster request cancelled: empty bucket!";
    return;
  }

  std::string request_info = "kess_service: " + kess_service + ", service_group: " + service_group_ +
                             ", timeout_ms: " + std::to_string(timeout_ms_) + ", bucket: " + bucket
                             + ", using_item_embeding_from_request: "
                             + std::to_string(!add_item_embeddings_from_attr_name_.empty());

  if (std::distance(begin, end) <= 0) {
    CL_LOG(INFO) << "common cluster request cancelled: empty item list! " << request_info;
    return;
  }

  thread_local GetCommonClusterRequest request;
  request.Clear();
  request.set_bucket(bucket);
  request.set_using_item_embeding_from_request(!add_item_embeddings_from_attr_name_.empty());

  thread_local folly::F14FastMap<uint64, uint64> item_id_to_key_map;
  item_id_to_key_map.clear();

  ItemAttr *item_embedding_attr_accessor = nullptr;
  if (!add_item_embeddings_from_attr_name_.empty()) {
    item_embedding_attr_accessor = context->GetItemAttrAccessor(add_item_embeddings_from_attr_name_);
  }
  std::for_each(begin, end, [this, item_embedding_attr_accessor](const CommonRecoResult &result) {
    uint64 id = result.GetId();
    request.add_item_key(id);
    // push embedding
    if (item_embedding_attr_accessor) {
      ::ks::platform::ItemEmbedding* item_embedding_builder = request.add_item_embedding();
      PushVectorToRequestEmbedding(result, item_embedding_attr_accessor, item_embedding_builder);
    }
    item_id_to_key_map[id] = result.item_key;
  });

  CL_LOG(INFO) << "sending common cluster request, item num: " << request.item_key_size() << ", "
               << request_info;
  auto pair = common_cluster_client_.AsyncCommonCluster(kess_service, service_group_, "s0", timeout_ms_,
                                                        request, nullptr);
  if (!pair.first) {
    CL_LOG_ERROR("common_cluster", "send_request_fail")
        << "failed to send common cluster request! " << request_info
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  auto callback = [this, context, request_info,
                   item_id_to_key_map = std::move(item_id_to_key_map)](GetCommonClusterResponse *resp) {
    int mismatched_id_count = 0;
    for (const auto &item : resp->result_item()) {
      auto it = item_id_to_key_map.find(item.item_key());
      if (it != item_id_to_key_map.end()) {
        context->SetIntItemAttr(it->second, output_attr_, item.cluster_id());
      } else {
        ++mismatched_id_count;
      }
    }

    if (mismatched_id_count > 0) {
      base::perfutil::PerfUtilWrapper::CountLogStash(mismatched_id_count, kPerfNs, "error.common_cluster",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), "mismatched_id_count");
    }
    CL_LOG(INFO) << "common cluster response received, response result_item_size: "
                 << resp->result_item_size() << ", mismatched id count: " << mismatched_id_count;
  };

  RegisterAsyncCallback(context, std::move(pair.second), std::move(callback), request_info);
}

void CommonRecoCommonClusterEnricher::PushVectorToRequestEmbedding(const CommonRecoResult &reco_result,
                                      ItemAttr *attr_accessor,
                                      ::ks::platform::ItemEmbedding* item_embedding_builder) {
  if (auto float_list_val = reco_result.GetDoubleListAttr(attr_accessor)) {
    for (auto val : *float_list_val) {
      item_embedding_builder->add_element(val);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCommonClusterEnricher, CommonRecoCommonClusterEnricher)

}  // namespace platform
}  // namespace ks
