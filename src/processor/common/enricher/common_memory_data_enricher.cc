#include "dragon/src/processor/common/enricher/common_memory_data_enricher.h"

#include <vector>

#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

base::Lock CommonMemoryDataEnricher::lock_;

folly::F14FastMap<std::string, CommonMemoryDataEnricher::DataType>
    CommonMemoryDataEnricher::DATA_TYPE_MAP = {
        {"uint64", DataType::BASE_UINT64},
        {"int32", DataType::BASE_INT32},
        {"double", DataType::BASE_DOUBLE},
        {"string", DataType::BASE_STRING},
        {"uint64_vector", DataType::UINT64_VECTOR},
        {"string_vector", DataType::STRING_VECTOR},
        {"double_vector", DataType::DOUBLE_VECTOR},
        {"unit64_double_pair_vector", DataType::UINT64_DOUBLE_PAIR_VECTOR},
        {"uint64_set", DataType::UINT64_SET},
        {"int32_set", DataType::INT32_SET},
        {"string_set", DataType::STRING_SET},
        {"double_set", DataType::DOUBLE_SET},
        {"string_vector_vector", DataType::STRING_VECTOR_VECTOR},
        {"uint64_vector_vector", DataType::UINT64_VECTOR_VECTOR},
        {"string_string_map", DataType::STRING_STRING_MAP},
        {"string_uint64_map", DataType::STRING_UINT64_MAP},
        {"string_int32_map", DataType::STRING_INT32_MAP},
        {"string_double_map", DataType::STRING_DOUBLE_MAP},
        {"int64_int64_map", DataType::INT64_INT64_MAP},
        {"int32_int32_map", DataType::INT32_INT32_MAP},
        {"int64_int32_map", DataType::INT64_INT32_MAP},
        {"uint64_string_map", DataType::UINT64_STRING_MAP},
        {"uint64_uint64_map", DataType::UINT64_UINT64_MAP},
        {"uint64_double_map", DataType::UINT64_DOUBLE_MAP},
        {"string_uint64_uint64_pair_map", DataType::STRING_UINT64_UINT64_PAIR_MAP},
        {"string_uint64_vector_map", DataType::STRING_UINT64_VECTOR_MAP},
        {"string_string_vector_map", DataType::STRING_STRING_VECTOR_MAP},
        {"string_double_vector_map", DataType::STRING_DOUBLE_VECTOR_MAP},
        {"uint64_string_vector_map", DataType::UINT64_STRING_VECTOR_MAP},
        {"int32_uint64_vector_map", DataType::INT32_UINT64_VECTOR_MAP},
        {"uint64_double_vector_map", DataType::UINT64_DOUBLE_VECTOR_MAP},
        {"uint64_uint64_vector_map", DataType::UINT64_UINT64_VECTOR_MAP},
        {"int32_int32_vector_map", DataType::INT32_INT32_VECTOR_MAP},
        {"string_uint64_double_pair_vector_map", DataType::STRING_UINT64_DOUBLE_PAIR_VECTOR_MAP},
        {"string_uint64_uint64_pair_vector_map", DataType::STRING_UINT64_UINT64_PAIR_VECTOR_MAP},
        {"uint64_uint64_double_pair_vector_map", DataType::UINT64_UINT64_DOUBLE_PAIR_VECTOR_MAP},
        {"string_uint64_set_map", DataType::STRING_UINT64_SET_MAP},
        {"uint64_uint64_set_map", DataType::UINT64_UINT64_SET_MAP},
        {"string_uint64_double_map_map", DataType::STRING_UINT64_DOUBLE_MAP_MAP},
        {"uint64_uint64_double_map_map", DataType::UINT64_UINT64_DOUBLE_MAP_MAP},
};

void CommonMemoryDataEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  data_key_ = GetStringProcessorParameter(context, "data_key");
  if (data_key_.empty()) {
    CL_LOG_ERROR_EVERY("common_memory_data_enricher", "empty_data_key", 1000) << "Empty data key";
    return;
  }

  auto iter = DATA_TYPE_MAP.find(data_type_);
  if (iter == DATA_TYPE_MAP.end()) {
    CL_LOG_WARNING_EVERY("common_memory_data_enricher", "wrong_data_type: " + data_type_, 1000)
        << "Wrong data type: " << data_type_;
    return;
  }

  bool flag = false;
  switch (iter->second) {
    case DataType::BASE_UINT64:
      flag = GetDataAndSave<uint64>(context);
      break;

    case DataType::BASE_INT32:
      flag = GetDataAndSave<int32>(context);
      break;

    case DataType::BASE_DOUBLE:
      flag = GetDataAndSave<double>(context);
      break;

    case DataType::BASE_STRING:
      flag = GetDataAndSave<std::string>(context);
      break;

    case DataType::UINT64_VECTOR:
      flag = GetDataAndSave<std::vector<uint64>>(context);
      break;

    case DataType::STRING_VECTOR:
      flag = GetDataAndSave<std::vector<std::string>>(context);
      break;

    case DataType::DOUBLE_VECTOR:
      flag = GetDataAndSave<std::vector<double>>(context);
      break;

    case DataType::UINT64_DOUBLE_PAIR_VECTOR:
      flag = GetDataAndSave<std::vector<std::pair<uint64, double>>>(context);
      break;

    case DataType::UINT64_SET:
      flag = GetDataAndSave<folly::F14FastSet<uint64>>(context);
      break;

    case DataType::INT32_SET:
      flag = GetDataAndSave<folly::F14FastSet<int32>>(context);
      break;

    case DataType::STRING_SET:
      flag = GetDataAndSave<folly::F14FastSet<std::string>>(context);
      break;

    case DataType::DOUBLE_SET:
      flag = GetDataAndSave<folly::F14FastSet<double>>(context);
      break;

    case DataType::STRING_VECTOR_VECTOR:
      flag = GetDataAndSave<std::vector<std::vector<std::string>>>(context);
      break;

    case DataType::UINT64_VECTOR_VECTOR:
      flag = GetDataAndSave<std::vector<std::vector<uint64>>>(context);
      break;

    case DataType::STRING_STRING_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::string>>(context);
      break;

    case DataType::STRING_UINT64_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, uint64>>(context);
      break;

    case DataType::STRING_INT32_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, int32>>(context);
      break;

    case DataType::STRING_DOUBLE_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, double>>(context);
      break;

    case DataType::INT64_INT64_MAP:
      flag = GetDataAndSave<folly::F14FastMap<int64, int64>>(context);
      break;

    case DataType::INT32_INT32_MAP:
      flag = GetDataAndSave<folly::F14FastMap<int32, int32>>(context);
      break;

    case DataType::INT64_INT32_MAP:
      flag = GetDataAndSave<folly::F14FastMap<int64, int32>>(context);
      break;

    case DataType::UINT64_STRING_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, std::string>>(context);
      break;

    case DataType::UINT64_UINT64_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, uint64>>(context);
      break;

    case DataType::UINT64_DOUBLE_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, double>>(context);
      break;

    case DataType::STRING_UINT64_UINT64_PAIR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::pair<uint64, uint64>>>(context);
      break;

    case DataType::STRING_UINT64_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::vector<uint64>>>(context);
      break;

    case DataType::STRING_STRING_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::vector<std::string>>>(context);
      break;

    case DataType::STRING_DOUBLE_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::vector<double>>>(context);
      break;

    case DataType::INT32_UINT64_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<int32, std::vector<uint64>>>(context);
      break;

    case DataType::UINT64_DOUBLE_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, std::vector<double>>>(context);
      break;

    case DataType::UINT64_STRING_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, std::vector<std::string>>>(context);
      break;

    case DataType::UINT64_UINT64_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, std::vector<uint64>>>(context);
      break;

    case DataType::INT32_INT32_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<int32, std::vector<int32>>>(context);
      break;

    case DataType::STRING_UINT64_DOUBLE_PAIR_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::vector<std::pair<uint64, double>>>>(context);
      break;

    case DataType::STRING_UINT64_UINT64_PAIR_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, std::vector<std::pair<uint64, uint64>>>>(context);
      break;

    case DataType::UINT64_UINT64_DOUBLE_PAIR_VECTOR_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, std::vector<std::pair<uint64, double>>>>(context);
      break;

    case DataType::STRING_UINT64_SET_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, folly::F14FastSet<uint64>>>(context);
      break;

    case DataType::UINT64_UINT64_SET_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, folly::F14FastSet<uint64>>>(context);
      break;

    case DataType::STRING_UINT64_DOUBLE_MAP_MAP:
      flag = GetDataAndSave<folly::F14FastMap<std::string, folly::F14FastMap<uint64, double>>>(context);
      break;

    case DataType::UINT64_UINT64_DOUBLE_MAP_MAP:
      flag = GetDataAndSave<folly::F14FastMap<uint64, folly::F14FastMap<uint64, double>>>(context);
      break;
  }

  if (!flag) {
    CL_LOG_WARNING_EVERY("common_memory_data_enricher", "bad_data_key: " + data_key_, 1000)
        << "Bad data key: " << data_key_ << ", data type: " << data_type_;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonMemoryDataEnricher, CommonMemoryDataEnricher)

}  // namespace platform
}  // namespace ks
