#include "dragon/src/processor/common/enricher/common_reco_local_attr_index_item_attr_enricher.h"

#include <utility>

#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_type.h"

namespace ks {
namespace platform {

void CommonRecoLocalAttrIndexItemAttrEnricher::GetItemAttrs(
    ks::reco::AttrIndex *attr_index, const CommonRecoResult &result, const std::vector<uint64> &attr_keys,
    const std::vector<CommonIndexEnum::AttrType> &attr_types,
    const std::vector<ks::reco::protoutil::AttrType> &read_attr_types) {
  auto reader = attr_index->GetKVAutoReader(result.item_key);
  if (!reader) {
    ++missing_item_count_;
    missing_item_key_ = result.item_key;
    return;
  }

  if (!reader->IsValid()) {
    CL_LOG_ERROR_EVERY("local_attr_index", "invalid_reader", 1000)
        << "Invalid attr reader. key:" << result.item_key << ", id:" << result.GetId()
        << ", type:" << result.GetType();
    ++missing_item_count_;
    missing_item_key_ = result.item_key;
    return;
  }

  int64 int_val = 0;
  float float_val = 0.0;
  std::string str_val;
  std::vector<int64> int_list_val;
  std::vector<double> double_list_val;
  std::vector<std::string> str_list_val;

  for (int i = 0; i < attr_name_list_.size(); ++i) {
    uint64 attr_key = attr_keys[i];
    CommonIndexEnum::AttrType attr_type = attr_types[i];
    CommonIndexEnum::AttrType read_attr_type = static_cast<CommonIndexEnum::AttrType>(read_attr_types[i]);

    const auto &attr_accessor = attr_accessors_[i];
    auto &perf_counter = attr_perf_count_[i];
    if (read_attr_type != attr_type) {
      ++perf_counter.attr_type_mismatch_count;
    }

    switch (attr_type) {
      case CommonIndexEnum::INT_ATTR:
        if (reader->GetIntValue(attr_key, &int_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetIntAttr(attr_accessor, int_val, no_overwrite_);
        }
        break;

      case CommonIndexEnum::FLOAT_ATTR:
        if (reader->GetFloatValue(attr_key, &float_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetDoubleAttr(attr_accessor, float_val, no_overwrite_);
        }
        break;

      case CommonIndexEnum::STRING_ATTR:
        str_val.clear();
        if (reader->GetStringValue(attr_key, &str_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetStringAttr(attr_accessor, std::move(str_val), no_overwrite_);
        }
        break;

      case CommonIndexEnum::INT_LIST_ATTR: {
        int_list_val.clear();
        if (reader->GetIntListValue(attr_key, &int_list_val)) {
          perf_counter.attr_total_size += int_list_val.size();
          ++perf_counter.attr_count;
          result.SetIntListAttr(attr_accessor, std::move(int_list_val), no_overwrite_);
        }
        break;
      }

      case CommonIndexEnum::FLOAT_LIST_ATTR: {
        double_list_val.clear();
        if (reader->GetFloatListValue(attr_key, &double_list_val)) {
          perf_counter.attr_total_size += double_list_val.size();
          ++perf_counter.attr_count;
          result.SetDoubleListAttr(attr_accessor, std::move(double_list_val), no_overwrite_);
        }
        break;
      }

      case CommonIndexEnum::STRING_LIST_ATTR: {
        str_list_val.clear();
        if (reader->GetStringListValue(attr_key, &str_list_val)) {
          perf_counter.attr_total_size += str_list_val.size();
          ++perf_counter.attr_count;
          result.SetStringListAttr(attr_accessor, std::move(str_list_val), no_overwrite_);
        }
        break;
      }

      default:
        ++perf_counter.unknown_attr_count;
        break;
    }
  }
}

void CommonRecoLocalAttrIndexItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  auto *attr_index = ks::reco::AttrIndexSingleton();
  if (!attr_index) {
    CL_LOG_ERROR_EVERY("local_attr_index", "no_index", 1000)
        << "cannot get item attrs from local index, no attr index available!";
    return;
  }

  thread_local std::vector<CommonRecoResult> additional_items;
  additional_items.clear();

  int total_item_num = 0;
  if (!EnrichInit(context, begin, end, &additional_items, &total_item_num)) {
    return;
  }

  std::vector<CommonIndexEnum::AttrType> attr_types(attr_name_list_.size(), CommonIndexEnum::UNKNOW_ATTR);

  std::vector<uint64> attr_keys(attr_name_list_.size(), 0);
  std::vector<ks::reco::protoutil::AttrType> read_attr_types(attr_name_list_.size(),
                                                             ks::reco::protoutil::AttrType::kInvalid);
  for (int i = 0; i < attr_name_list_.size(); ++i) {
    const auto &attr_name = attr_name_list_[i];
    attr_index->GetAttrKeyAndType(attr_name, &attr_keys[i], &read_attr_types[i]);
    attr_types[i] = attr_type_list_.size() > i ? RecoUtil::CastCommonIndexAttrType(attr_type_list_[i])
                                               : static_cast<CommonIndexEnum::AttrType>(read_attr_types[i]);
  }

  static_assert((int)ks::reco::protoutil::AttrType::kInvalid == CommonIndexEnum::UNKNOW_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kInt == CommonIndexEnum::INT_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kFloat == CommonIndexEnum::FLOAT_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kString == CommonIndexEnum::STRING_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kIntList == CommonIndexEnum::INT_LIST_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kFloatList == CommonIndexEnum::FLOAT_LIST_ATTR &&
                    (int)ks::reco::protoutil::AttrType::kStringList == CommonIndexEnum::STRING_LIST_ATTR,
                "enum value not match");

  missing_item_count_ = 0;
  missing_item_key_ = 0;
  for (auto &counter : attr_perf_count_) {
    counter.Clear();
  }

  int64 start_ts = base::GetTimestamp();

  if (include_reco_results_) {
    std::for_each(
        begin, end,
        [this, attr_index, &attr_keys, &attr_types, &read_attr_types](const CommonRecoResult &result) {
          GetItemAttrs(attr_index, result, attr_keys, attr_types, read_attr_types);
        });
  }

  for (const auto &result : additional_items) {
    GetItemAttrs(attr_index, result, attr_keys, attr_types, read_attr_types);
  }

  int64 duration = base::GetTimestamp() - start_ts;
  EnrichEndPerf(context, total_item_num, duration);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLocalAttrIndexItemAttrEnricher,
                 CommonRecoLocalAttrIndexItemAttrEnricher);

}  // namespace platform
}  // namespace ks
