#include "dragon/src/processor/common/enricher/common_reco_redis_common_attr_enricher.h"

#include <functional>
#include <memory>
#include <string>
#include <utility>
#include "third_party/abseil/absl/strings/str_cat.h"

namespace ks {
namespace platform {

void CommonRecoRedisCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  timeout_ms_ = CheckAndGetTimeoutMs(context);
  if (timeout_ms_ <= 0) {  // 如果超时时间为 0 毫秒，代表屏蔽此 redis 服务
    return;
  }

  if (!redis_client_) {
    redis_client_ = GetRedisClient();
    if (!redis_client_) {
      CL_LOG_ERROR_EVERY("get_common_attr_from_redis", "client_lazy_init_fail:" + cluster_name_, 100)
          << "redis client init failed, cluster name: " << cluster_name_ << ", cache_name: " << cache_name_
          << ", cache_bits: " << cache_bits_ << ", cache_delay_delete_ms: " << cache_delay_delete_ms_
          << ", cache_expire_second: " << cache_expire_second_;
      return;
    }
  }
  if (is_async_) {
    AsyncGetCommonAttrFromRedisResponse(context);
  } else {
    GetCommonAttrFromRedisResponse(context);
  }
}

void CommonRecoRedisCommonAttrEnricher::PrepareKeys(MutableRecoContextInterface *context,
                                                    std::vector<std::string> *string_keys,
                                                    std::vector<int> *key_sizes) {
  for (const auto &param : string_params_) {
    std::string key_prefix_value = GetStringProcessorParameter(context, param.key_prefix);
    const AttrType &attr_type = param.output_attr_type;
    if (attr_type == AttrType::STRING || attr_type == AttrType::INT || attr_type == AttrType::FLOAT) {
      std::string sv;
      int64 int_key = 0;
      if (TryGetStringProcessorParameter(context, param.key, &sv)) {
        string_keys->emplace_back(absl::StrCat(key_prefix_value, sv));
      } else if (TryGetIntProcessorParameter(context, param.key, &int_key)) {
        string_keys->emplace_back(absl::StrCat(key_prefix_value, int_key));
      } else {
        string_keys->emplace_back("");
      }
      key_sizes->push_back(1);
    } else if (attr_type == AttrType::STRING_LIST || attr_type == AttrType::INT_LIST ||
               attr_type == AttrType::FLOAT_LIST) {
      std::vector<absl::string_view> keys;
      if (TryGetStringListProcessorParameter(context, param.key, &keys)) {
        for (auto sv : keys) {
          string_keys->emplace_back(absl::StrCat(key_prefix_value, sv));
        }
        key_sizes->push_back(keys.size());
      } else {
        std::vector<int64> int_keys;
        if (TryGetIntListProcessorParameter(context, param.key, &int_keys)) {
          for (int64 key : int_keys) {
            string_keys->emplace_back(absl::StrCat(key_prefix_value, key));
          }
          key_sizes->push_back(int_keys.size());
        } else {
          key_sizes->push_back(0);
        }
      }
    } else {
      key_sizes->push_back(0);
      CL_LOG_WARNING("redis_common_attr", "unsupported_value_type")
          << "enrich redis common attr skipped for key: " << param.key->ToString();
    }
  }
}

void CommonRecoRedisCommonAttrEnricher::ParseValues(MutableRecoContextInterface *context,
                                                    const std::vector<std::string> &string_keys,
                                                    const std::vector<int> &key_sizes,
                                                    const std::vector<std::string> &string_values) {
  auto first_string_value = string_values.begin();
  auto current_string_key = string_keys.begin();
  for (int i = 0; i < string_params_.size(); i++) {
    const AttrType &attr_type = string_params_[i].output_attr_type;
    const std::string &attr_name = string_params_[i].output_attr_name;
    const int key_size = key_sizes[i];
    switch (attr_type) {
      case AttrType::STRING:
      case AttrType::STRING_LIST: {
        std::vector<std::string> values(key_size);
        std::move(first_string_value, first_string_value + key_size, values.begin());
        if (!values.empty()) {
          if (attr_type == AttrType::STRING_LIST) {
            context->SetStringListCommonAttr(attr_name, std::move(values));
          } else {
            context->SetStringCommonAttr(attr_name, std::move(values[0]));
          }
        }
        break;
      }
      case AttrType::INT:
      case AttrType::INT_LIST: {
        std::vector<int64> values;
        values.reserve(key_size);
        int convert_fail_count = 0;
        for (auto it = first_string_value; it < first_string_value + key_size; it++, current_string_key++) {
          if ((*it).empty() && skip_empty_) {
            continue;
          }
          int64 value = 0;
          bool convert_succ = base::StringToInt64(*it, &value);
          if (!convert_succ) {
            CL_LOG(WARNING) << "Failed to parse string to int, key: " << *current_string_key
                            << ", attr value: " << *it << ", cluster_name: " << cluster_name_;
            convert_fail_count++;
          }
          if (convert_succ || !skip_parse_fail_) {
            values.push_back(value);
          }
        }
        if (!values.empty()) {
          if (attr_type == AttrType::INT_LIST) {
            context->SetIntListCommonAttr(attr_name, std::move(values));
          } else {
            context->SetIntCommonAttr(attr_name, values[0]);
          }
        }
        if (convert_fail_count > 0) {
          CL_LOG_WARNING_COUNT(convert_fail_count, "redis_common_attr", GetName() + " convert_int_fail")
              << "failed to parse string to int, count = " << convert_fail_count
              << ", cluster_name: " << cluster_name_;
        }
        break;
      }
      case AttrType::FLOAT:
      case AttrType::FLOAT_LIST: {
        std::vector<double> values;
        values.reserve(key_size);
        int convert_fail_count = 0;
        for (auto it = first_string_value; it < first_string_value + key_size; it++, current_string_key++) {
          if ((*it).empty() && skip_empty_) {
            continue;
          }
          double value = 0;
          bool convert_succ = absl::SimpleAtod(*it, &value);
          if (!convert_succ) {
            CL_LOG(WARNING) << "Failed to parse string to int, key: " << *current_string_key
                            << ", attr value: " << *it << ", cluster_name: " << cluster_name_;
            convert_fail_count++;
          }
          if (convert_succ || !skip_parse_fail_) {
            values.push_back(value);
          }
        }
        if (!values.empty()) {
          if (attr_type == AttrType::FLOAT_LIST) {
            context->SetDoubleListCommonAttr(attr_name, std::move(values));
          } else {
            context->SetDoubleCommonAttr(attr_name, values[0]);
          }
        }
        if (convert_fail_count > 0) {
          CL_LOG_WARNING_COUNT(convert_fail_count, "redis_common_attr", GetName() + " convert_double_fail")
              << "failed to parse string to double, count = " << convert_fail_count
              << ", cluster_name: " << cluster_name_;
        }
        break;
      }
      default:
        CL_LOG_WARNING("redis_common_attr", "unsupported_value_type")
            << "unsupported value type, could be int/double/string/int_list/double_list/string_list"
            << ", cluster_name: " << cluster_name_;
    }
    std::advance(first_string_value, key_size);
  }
}

void CommonRecoRedisCommonAttrEnricher::GetCommonAttrFromRedisResponse(MutableRecoContextInterface *context) {
  if (string_params_.empty()) {
    return;
  }

  std::vector<std::string> string_keys;
  std::vector<int> key_sizes;
  key_sizes.reserve(string_params_.size());
  PrepareKeys(context, &string_keys, &key_sizes);

  if (string_keys.empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich common attr from redis cancelled: empty keys, processor: "
                              << GetName();
    return;
  }

  std::vector<std::string> string_values;
  string_values.reserve(string_keys.size());

  auto err_code = redis_client_->MGet(string_keys, &string_values, true, timeout_ms_);
  if (!save_err_code_to_.empty()) {
    context->SetIntCommonAttr(save_err_code_to_, static_cast<int>(err_code));
  }
  if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
      CL_LOG_WARNING("redis_common_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value timeout, err_code: " << err_code
          << ", err_msg: " << ks::infra::err2str(err_code);
    } else {
      CL_LOG_ERROR("redis_common_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value error, err_code: " << err_code << ", err_msg: " << ks::infra::err2str(err_code);
    }
    return;
  }

  if (string_values.size() != string_keys.size()) {
    CL_LOG_WARNING("redis_common_attr", "key_value_size_mismatch")
        << "redis key and value size mismatch: " << string_keys.size() << " vs " << string_values.size();
    return;
  }

  ParseValues(context, string_keys, key_sizes, string_values);
}

void CommonRecoRedisCommonAttrEnricher::AsyncGetCommonAttrFromRedisResponse(
    MutableRecoContextInterface *context) {
  if (string_params_.empty()) {
    return;
  }

  auto string_keys = std::make_shared<std::vector<std::string>>();
  auto key_sizes = std::make_shared<std::vector<int>>();
  key_sizes->reserve(string_params_.size());
  PrepareKeys(context, string_keys.get(), key_sizes.get());

  if (string_keys->empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich common attr from redis cancelled: empty keys, processor: "
                              << GetName();
    return;
  }

  std::shared_ptr<base::RedisMGetOpWaiter> async_mget_waiter =
      std::make_shared<base::RedisMGetOpWaiter>(redis_client_->AsyncMGet(*(string_keys.get())));

  async_mget_waiter->SetTimeout(timeout_ms_);

  auto redis_future =
      CommonRecoFutureActionWrapper<std::pair<ks::infra::RedisErrorCode, std::vector<std::string>>>(
          [this, context,
           async_mget_waiter]() -> std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> {
            std::vector<std::string> ret;
            auto status = async_mget_waiter->Get(&ret);
            auto async_cost = async_mget_waiter->GetAsyncCostUs();
            if (async_cost >= 0) {
              CL_PERF_INTERVAL(async_cost, kPerfNs, "processor_async_ready",
                               GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                               GetDownstreamProcessor(), "", GlobalHolder::GetJsonConfigVersion());
            }
            return {status, ret};
          });

  std::function<void(const std::vector<std::string> &)> callback =
      [this, context, string_keys, key_sizes](const std::vector<std::string> &string_values) {
        ParseValues(context, *string_keys, *key_sizes, string_values);
      };

  std::function<bool(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)> status_getter =
      [this, context,
       string_keys](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> bool {
    auto err_code = result.first;
    const auto &string_values = result.second;
    if (!save_err_code_to_.empty()) {
      context->SetIntCommonAttr(save_err_code_to_, static_cast<int>(err_code));
    }
    if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
      if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
        CL_LOG_WARNING("redis_common_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value timeout, err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      } else {
        CL_LOG_ERROR("redis_common_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value error, err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      }
      return false;
    }
    if (result.second.size() != string_keys->size()) {
      CL_LOG_WARNING("redis_common_attr", "key_value_size_mismatch")
          << "redis key and value size mismatch: " << string_keys->size() << " vs " << string_values.size();
      return false;
    }
    return true;
  };

  std::function<const std::vector<std::string> &(
      const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      payload_getter = [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result)
      -> const std::vector<std::string> & { return result.second; };

  std::function<std::string(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      err_msg_getter =
          [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> std::string {
    return ks::infra::err2str(result.first);
  };

  RegisterAsyncCallback(
      context, std::move(redis_future), std::move(callback), []() {}, std::move(status_getter),
      std::move(payload_getter), std::move(err_msg_getter), "");
}

CommonRecoRedisCommonAttrEnricher::RedisValueType CommonRecoRedisCommonAttrEnricher::ParseRedisValueType(
    const std::string &type) {
  auto it = redis_value_type_map_.find(type);
  return it == redis_value_type_map_.end() ? CommonRecoRedisCommonAttrEnricher::RedisValueType::UNKNOWN
                                           : it->second;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRedisCommonAttrEnricher, CommonRecoRedisCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
