#include <algorithm>

#include "dragon/src/processor/common/enricher/common_reco_norm_and_discrete_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoNormAndDiscreteEnricher::<PERSON>rich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  std::vector<double> quantile_list = GetDoubleListProcessorParameter(context, "quantile_list");
  if (quantile_list.size() < 2) {
    CL_LOG(WARNING) << "CommonRecoNormAndDiscreteEnricher quantile_list cannot be less than 2";
    return;
  }
  std::sort(quantile_list.begin(), quantile_list.end());
  if (is_common_attr_) {
    HandleCommonAttr(context, quantile_list);
  } else {
    HandleItemAttr(context, begin, end, quantile_list);
  }
}

void CommonRecoNormAndDiscreteEnricher::HandleCommonAttr(MutableRecoContextInterface *context,
                                                         const std::vector<double> &quantile_list) {
  auto *attr = context->GetCommonAttrAccessor(input_attr_);
  switch (attr->value_type) {
    case AttrType::INT:
      if (auto int_val = attr->GetIntValue()) {
        std::pair<int, double> pair = BinarySearch(static_cast<double>(*int_val), quantile_list);
        if (!output_discrete_attr_.empty()) {
          context->SetIntCommonAttr(output_discrete_attr_, pair.first);
        }
        if (!output_norm_attr_.empty()) {
          context->SetDoubleCommonAttr(output_norm_attr_, pair.second);
        }
      }
      break;
    case AttrType::FLOAT:
      if (auto double_val = attr->GetDoubleValue()) {
        std::pair<int, double> pair = BinarySearch(*double_val, quantile_list);
        if (!output_discrete_attr_.empty()) {
          context->SetIntCommonAttr(output_discrete_attr_, pair.first);
        }
        if (!output_norm_attr_.empty()) {
          context->SetDoubleCommonAttr(output_norm_attr_, pair.second);
        }
      }
      break;
    case AttrType::INT_LIST:
      if (auto int_list_val = attr->GetIntListValue()) {
        std::vector<int64> bucket_list;
        std::vector<double> norm_list;
        for (auto v : *int_list_val) {
          std::pair<int, double> pair = BinarySearch(static_cast<double>(v), quantile_list);
          bucket_list.push_back(pair.first);
          norm_list.push_back(pair.second);
        }
        if (!output_discrete_attr_.empty()) {
          context->SetIntListCommonAttr(output_discrete_attr_, std::move(bucket_list));
        }
        if (!output_norm_attr_.empty()) {
          context->SetDoubleListCommonAttr(output_norm_attr_, std::move(norm_list));
        }
      }
      break;
    case AttrType::FLOAT_LIST:
      if (auto double_list_val = attr->GetDoubleListValue()) {
        std::vector<int64> bucket_list;
        std::vector<double> norm_list;
        for (auto v : *double_list_val) {
          std::pair<int, double> pair = BinarySearch(v, quantile_list);
          bucket_list.push_back(pair.first);
          norm_list.push_back(pair.second);
        }
        if (!output_discrete_attr_.empty()) {
          context->SetIntListCommonAttr(output_discrete_attr_, std::move(bucket_list));
        }
        if (!output_norm_attr_.empty()) {
          context->SetDoubleListCommonAttr(output_norm_attr_, std::move(norm_list));
        }
      }
      break;
    default:
      CL_LOG(WARNING) << "CommonRecoNormAndDiscreteEnricher AttrType " << static_cast<int>(attr->value_type)
                      << " Not Supported, attr_name=" << input_attr_;
      break;
  }
}

void CommonRecoNormAndDiscreteEnricher::HandleItemAttr(MutableRecoContextInterface *context,
                                                       RecoResultConstIter begin, RecoResultConstIter end,
                                                       const std::vector<double> &quantile_list) {
  InitItemAttrAccessors(context);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto *attr = context->GetItemAttrAccessor(input_attr_);
    switch (attr->value_type) {
      case AttrType::INT:
        if (auto int_val = result.GetIntAttr(attr)) {
          std::pair<int, double> pair = BinarySearch(static_cast<double>(*int_val), quantile_list);
          if (output_item_discrete_attr_accessor_) {
            result.SetIntAttr(output_item_discrete_attr_accessor_, pair.first);
          }
          if (output_item_norm_attr_accessor_) {
            result.SetDoubleAttr(output_item_norm_attr_accessor_, pair.second);
          }
        }
        break;
      case AttrType::FLOAT:
        if (auto double_val = result.GetDoubleAttr(attr)) {
          std::pair<int, double> pair = BinarySearch(*double_val, quantile_list);
          if (output_item_discrete_attr_accessor_) {
            result.SetIntAttr(output_item_discrete_attr_accessor_, pair.first);
          }
          if (output_item_norm_attr_accessor_) {
            result.SetDoubleAttr(output_item_norm_attr_accessor_, pair.second);
          }
        }
        break;
      case AttrType::INT_LIST:
        if (auto int_list_val = result.GetIntListAttr(attr)) {
          std::vector<int64> bucket_list;
          std::vector<double> norm_list;
          for (auto v : *int_list_val) {
            std::pair<int, double> pair = BinarySearch(static_cast<double>(v), quantile_list);
            bucket_list.push_back(pair.first);
            norm_list.push_back(pair.second);
          }
          if (output_item_discrete_attr_accessor_) {
            result.SetIntListAttr(output_item_discrete_attr_accessor_, std::move(bucket_list));
          }
          if (output_item_norm_attr_accessor_) {
            result.SetDoubleListAttr(output_item_norm_attr_accessor_, std::move(norm_list));
          }
        }
        break;
      case AttrType::FLOAT_LIST:
        if (auto double_list_val = result.GetDoubleListAttr(attr)) {
          std::vector<int64> bucket_list;
          std::vector<double> norm_list;
          for (auto v : *double_list_val) {
            std::pair<int, double> pair = BinarySearch(v, quantile_list);
            bucket_list.push_back(pair.first);
            norm_list.push_back(pair.second);
          }
          if (output_item_discrete_attr_accessor_) {
            result.SetIntListAttr(output_item_discrete_attr_accessor_, std::move(bucket_list));
          }
          if (output_item_norm_attr_accessor_) {
            result.SetDoubleListAttr(output_item_norm_attr_accessor_, std::move(norm_list));
          }
        }
        break;
      default:
        CL_LOG(WARNING) << "CommonRecoNormAndDiscreteEnricher AttrType " << static_cast<int>(attr->value_type)
                        << " Not Supported, attr_name=" << input_attr_;
        break;
    }
  });
}

std::pair<int, double> CommonRecoNormAndDiscreteEnricher::BinarySearch(
    double data, const std::vector<double> &quantile_list) {
  int bucket_num = static_cast<int32>(quantile_list.size()) - 1;
  if (bucket_num <= 0) {
    return std::make_pair(0, 0.0);
  }
  if (data <= quantile_list[0]) {
    return std::make_pair(0, 0.0);
  }
  if (data >= quantile_list[bucket_num]) {
    return std::make_pair(bucket_num - 1, 1.0);
  }

  double gap = 1.0 / bucket_num;
  int bkt = std::upper_bound(quantile_list.begin(), quantile_list.end(), data) - quantile_list.begin() - 1;
  bkt = std::max(bkt, 0);
  return std::make_pair(
      bkt, (data - quantile_list[bkt]) * gap / std::max(quantile_list[bkt + 1] - quantile_list[bkt], 1e-5) +
               bkt * gap);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoNormAndDiscreteEnricher, CommonRecoNormAndDiscreteEnricher)

}  // namespace platform
}  // namespace ks
