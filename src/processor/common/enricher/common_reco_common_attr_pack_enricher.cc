#include "dragon/src/processor/common/enricher/common_reco_common_attr_pack_enricher.h"

#include <third_party/abseil/absl/strings/str_join.h>
#include <algorithm>
#include <unordered_set>

namespace ks {
namespace platform {

void CommonRecoCommonAttrPackEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  std::vector<int64> int_values;
  std::vector<double> double_values;
  std::vector<std::string> string_values;
  int limit_num = GetIntProcessorParameter(context, "limit_num", -1);
  if (limit_num > 0) {
    int_values.reserve(limit_num);
    double_values.reserve(limit_num);
    string_values.reserve(limit_num);
  }

  std::vector<absl::string_view> input_common_attrs =
      GetStringListProcessorParameter(context, "input_common_attrs");

  auto attr_type = AttrType::UNKNOWN;
  std::unordered_set<int64> existing_values;
  for (auto common_attr : input_common_attrs) {
    if (int_values.size() >= limit_num || string_values.size() >= limit_num ||
        double_values.size() >= limit_num) {
      break;
    }
    if (!context->HasCommonAttr(common_attr)) {
      continue;
    }
    if (auto p = context->GetIntCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::INT_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::INT_LIST;
      if (!deduplicate_ || existing_values.insert(*p).second) {
        int_values.emplace_back(*p);
      }
    } else if (auto p = context->GetIntListCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::INT_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::INT_LIST;
      for (const auto &int_value : *p) {
        if (int_values.size() >= limit_num) {
          break;
        }
        if (!deduplicate_ || existing_values.insert(int_value).second) {
          int_values.emplace_back(int_value);
        }
      }
    } else if (auto p = context->GetDoubleCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::FLOAT_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::FLOAT_LIST;
      double_values.emplace_back(*p);
    } else if (auto p = context->GetDoubleListCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::FLOAT_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::FLOAT_LIST;
      auto value_list_end =
          limit_num > 0 ? p->begin() + std::min(p->size(), limit_num - double_values.size()) : p->end();
      std::copy(p->begin(), value_list_end, std::back_inserter(double_values));
    } else if (auto p = context->GetStringCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::STRING_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::STRING_LIST;
      if (deduplicate_) {
        int64 hash_value = base::CityHash64(p->data(), p->size());
        if (existing_values.insert(hash_value).second) {
          string_values.emplace_back(p->data(), p->size());
        }
      } else {
        string_values.emplace_back(p->data(), p->size());
      }
    } else if (auto p = context->GetStringListCommonAttr(common_attr)) {
      if (attr_type != AttrType::UNKNOWN && attr_type != AttrType::STRING_LIST) {
        CL_LOG_EVERY_N(ERROR, 100) << "common attr type mismatch: " << common_attr;
        return;
      }
      attr_type = AttrType::STRING_LIST;
      for (const auto &string_value : *p) {
        if (string_values.size() >= limit_num) {
          break;
        }
        if (deduplicate_) {
          int64 hash_value = base::CityHash64(string_value.data(), string_value.size());
          if (existing_values.insert(hash_value).second) {
            string_values.emplace_back(string_value);
          }
        } else {
          string_values.emplace_back(string_value);
        }
      }
    }
  }
  if (attr_type == AttrType::INT_LIST) {
    context->SetIntListCommonAttr(output_common_attr_, std::move(int_values));
  } else if (attr_type == AttrType::FLOAT_LIST) {
    context->SetDoubleListCommonAttr(output_common_attr_, std::move(double_values));
  } else if (attr_type == AttrType::STRING_LIST) {
    context->SetStringListCommonAttr(output_common_attr_, std::move(string_values));
  } else {
    CL_LOG_EVERY_N(INFO, 100) << "common attrs not found: " << absl::StrJoin(input_common_attrs, ", ");
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCommonAttrPackEnricher, CommonRecoCommonAttrPackEnricher)

}  // namespace platform
}  // namespace ks
