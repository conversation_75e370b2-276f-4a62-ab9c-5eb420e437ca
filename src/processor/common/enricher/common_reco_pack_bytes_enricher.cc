#include "dragon/src/processor/common/enricher/common_reco_pack_bytes_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoPackBytesEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  if (is_common_) {
    HandleCommonAttr(context);
  } else {
    for (auto &part : schema_) {
      part.item_attr_accessor = context->GetItemAttr(part.attr_name);
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      HandleItemAttr(context, result, context->GetItemAttrAccessor(output_attr_name_));
    });
  }
}

void CommonRecoPackBytesEnricher::HandleCommonAttr(MutableRecoContextInterface *context) const {
  std::string bytes;
  for (auto &part : schema_) {
    bool success = false;
    switch (part.dtype) {
      case SchemaPart::DType::INT8:
        success = PackSingleBytes<int8_t>(&bytes, context->GetIntCommonAttr(part.attr_name)) ||
                  PackListBytes<int8_t>(&bytes, context->GetIntListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::INT16:
        success = PackSingleBytes<int16_t>(&bytes, context->GetIntCommonAttr(part.attr_name)) ||
                  PackListBytes<int16_t>(&bytes, context->GetIntListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::INT32:
        success = PackSingleBytes<int32_t>(&bytes, context->GetIntCommonAttr(part.attr_name)) ||
                  PackListBytes<int32_t>(&bytes, context->GetIntListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::INT64:
        success = PackSingleBytes<int64_t>(&bytes, context->GetIntCommonAttr(part.attr_name)) ||
                  PackListBytes<int64_t>(&bytes, context->GetIntListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::FP16:
        success = PackSingleBytes<float16_t>(&bytes, context->GetDoubleCommonAttr(part.attr_name)) ||
                  PackListBytes<float16_t>(&bytes, context->GetDoubleListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::FP32:
        success = PackSingleBytes<float>(&bytes, context->GetDoubleCommonAttr(part.attr_name)) ||
                  PackListBytes<float>(&bytes, context->GetDoubleListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::FP64:
        success = PackSingleBytes<double>(&bytes, context->GetDoubleCommonAttr(part.attr_name)) ||
                  PackListBytes<double>(&bytes, context->GetDoubleListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::SCALEINT8:
        success = PackScaleInt8Bytes(&bytes, context->GetDoubleListCommonAttr(part.attr_name));
        break;
      case SchemaPart::DType::BYTES:
        success = PackSingleBytes<absl::string_view>(&bytes, context->GetStringCommonAttr(part.attr_name)) ||
                  PackListBytes<absl::string_view>(&bytes, context->GetStringListCommonAttr(part.attr_name));
        break;
    }
    if (!success) {
      CL_LOG_WARNING("pack_bytes", "failed_to_pack_common_attr")
          << "Failed to pack common attr: " << part.attr_name;
    }
  }
  if (bytes.size() > 0) {
    context->SetStringCommonAttr(output_attr_name_, std::move(bytes));
  }
}

void CommonRecoPackBytesEnricher::HandleItemAttr(MutableRecoContextInterface *context,
                                                 const CommonRecoResult &result,
                                                 ItemAttr *output_attr_accessor) const {
  std::string bytes;
  for (auto &part : schema_) {
    bool success = false;
    if (!part.item_attr_accessor) {
      continue;
    }

    switch (part.dtype) {
      case SchemaPart::DType::INT8:
        success = PackSingleBytes<int8_t>(&bytes, context->GetIntItemAttr(result, part.item_attr_accessor)) ||
                  PackListBytes<int8_t>(&bytes, context->GetIntListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::INT16:
        success =
            PackSingleBytes<int16_t>(&bytes, context->GetIntItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<int16_t>(&bytes, context->GetIntListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::INT32:
        success =
            PackSingleBytes<int32_t>(&bytes, context->GetIntItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<int32_t>(&bytes, context->GetIntListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::INT64:
        success =
            PackSingleBytes<int64_t>(&bytes, context->GetIntItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<int64_t>(&bytes, context->GetIntListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::FP16:
        success =
            PackSingleBytes<float16_t>(&bytes, context->GetDoubleItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<float16_t>(&bytes, context->GetDoubleListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::FP32:
        success =
            PackSingleBytes<float>(&bytes, context->GetDoubleItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<float>(&bytes, context->GetDoubleListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::FP64:
        success =
            PackSingleBytes<double>(&bytes, context->GetDoubleItemAttr(result, part.item_attr_accessor)) ||
            PackListBytes<double>(&bytes, context->GetDoubleListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::SCALEINT8:
        success = PackScaleInt8Bytes(&bytes, context->GetDoubleListItemAttr(result, part.item_attr_accessor));
        break;
      case SchemaPart::DType::BYTES:
        success = PackSingleBytes<absl::string_view>(
                      &bytes, context->GetStringItemAttr(result, part.item_attr_accessor)) ||
                  PackListBytes<absl::string_view>(
                      &bytes, context->GetStringListItemAttr(result, part.item_attr_accessor));
        break;
    }
  }
  if (bytes.size() > 0) {
    context->SetStringItemAttr(result, output_attr_accessor, std::move(bytes));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPackBytesEnricher, CommonRecoPackBytesEnricher)

}  // namespace platform
}  // namespace ks
