#pragma once

#include <string>
#include <utility>
#include <vector>
#include <type_traits>

#include "base/encoding/base64.h"
#include "base/strings/string_split.h"
#include "folly/Conv.h"
#include "folly/container/F14Map.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "serving_base/utility/cpu_hierarchy.h"
#include "serving_base/utility/timer.h"

#define GOOGLE_LOGGING
#include "tensorflow/core/public/session.h"

namespace ks {
namespace platform {

class CommonRecoTensorFlowAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoTensorFlowAttrEnricher() {}
  ~CommonRecoTensorFlowAttrEnricher() {
    if (session_) {
      tensorflow::Status status = session_->Close();
      delete session_;
      session_ = nullptr;
    }
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class AttrSource : int { UNKNOWN = 0, COMMON, ITEM };
  struct Argument {
    std::string attr_name;
    AttrSource attr_source = AttrSource::UNKNOWN;
    int dim = -1;
    float default_value = 0.0;
    tensorflow::DataType data_type = tensorflow::DataType::DT_INVALID;
  };

 private:
  bool InitProcessor() override {
    output_common_attr_ = config()->GetBoolean("output_common_attr", false);

    compress_common_input_ = config()->GetBoolean("compress_common_input", false);

    auto *args_config = config()->Get("inputs");
    if (!args_config || !args_config->IsObject()) {
      LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! Missing config 'inputs' or not an object!";
      return false;
    }

    args_.clear();
    for (auto &pr : args_config->objects()) {
      auto *c = pr.second;

      const auto attr_name = c->GetString("attr_name");
      if (attr_name.empty()) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'attr_name' is required!";
        return false;
      }

      const auto &attr_source_str = c->GetString("attr_source");
      AttrSource attr_source;
      if (attr_source_str == "user") {
        attr_source = AttrSource::COMMON;
      } else if (attr_source_str == "item") {
        attr_source = AttrSource::ITEM;
      } else if (attr_source_str.empty()) {
        attr_source = AttrSource::UNKNOWN;
      } else {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'attr_source' should be either "
                   << "'user', 'item' or empty string!";
        return false;
      }

      const auto &data_type_str = c->GetString("data_type", "float");
      tensorflow::DataType data_type = tensorflow::DataType::DT_INVALID;
      if (!tensorflow::DataTypeFromString(data_type_str, &data_type)) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! Unsupported `data_type` found: "
                   << data_type_str;
        return false;
      }

      // -1 for scalar
      int dim = c->GetInt("dim", -1);

      float default_value = c->GetFloat("default_value", 0.0);

      args_.emplace(pr.first, Argument{.attr_name = attr_name,
                                       .attr_source = attr_source,
                                       .dim = dim,
                                       .default_value = default_value,
                                       .data_type = data_type});
    }

    std::string graph_uri = config()->GetString("graph");
    if (graph_uri.empty()) {
      LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'graph' cannot be empty!";
      return false;
    }

    static const std::string base64_prefix = "base64://";
    tensorflow::GraphDef graph_def;
    if (graph_uri.find(base64_prefix) == 0) {  // begin with base64_prefix
      std::string graph_base64 = graph_uri.substr(base64_prefix.length());
      std::string graph_pb;
      if (!base::Base64Decode(graph_base64, &graph_pb)) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'graph' cannot be parsed as base64: "
                   << graph_base64;
        return false;
      }

      if (!graph_def.ParseFromString(graph_pb)) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'graph' cannot be parsed as GraphDef!";
        return false;
      }
    } else {
      LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! 'graph' with unsupported scheme: "
                 << graph_uri;
      return false;
    }

    {
      tensorflow::SessionOptions options;
      int processor_num = serving_base::GetProcessorNum();
      options.config.set_intra_op_parallelism_threads(
          config()->GetInt("intra_op_parallelism_threads", processor_num));
      options.config.set_inter_op_parallelism_threads(
          config()->GetInt("inter_op_parallelism_threads", processor_num));
      options.config.set_use_per_session_threads(config()->GetBoolean("use_per_session_threads", false));
      tensorflow::Status status = tensorflow::NewSession(options, &session_);
      if (!status.ok()) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! Failed to create the Session: "
                   << status.ToString();
        return false;
      }
    }

    {
      tensorflow::Status status = session_->Create(graph_def);
      if (!status.ok()) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! Failed to load the GraphDef: "
                   << status.ToString();
        return false;
      }
    }

    {
      tensorflow::CallableOptions options;
      for (const auto &pr : args_) {
        options.add_feed(pr.first);
      }

      auto *output_config = config()->Get("outputs");
      if (!output_config || !output_config->IsObject()) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! "
                   << "Missing config 'output_config' or not an object!";
        return false;
      }

      for (const auto &oc : output_config->objects()) {
        const std::string &tensor_name = oc.first;
        std::string attr_name = oc.second->StringValue();
        outputs_.emplace_back(std::move(attr_name));
        options.add_fetch(tensor_name);
      }

      tensorflow::Status status = session_->MakeCallable(options, &callable_handle_);
      if (!status.ok()) {
        LOG(ERROR) << "CommonRecoTensorFlowAttrEnricher init failed! Failed to make callable: "
                   << status.ToString();
        return false;
      }
    }

    return true;
  }

  template <typename T>
  void GetArgumentValue(ReadableRecoContextInterface *context, const Argument &arg,
                        ItemAttr *item_attr_accessor, const CommonRecoResult *reco_result, T *val,
                        size_t len) {
    if (arg.attr_source == AttrSource::ITEM) {
      if (item_attr_accessor && reco_result &&
          GetValueFromItemAttr<T>(context, item_attr_accessor, *reco_result, val, len, arg.default_value)) {
        return;
      }
    } else if (arg.attr_source == AttrSource::COMMON) {
      if (GetValueFromCommonAttr<T>(context, arg.attr_name, val, len, arg.default_value)) {
        return;
      }
    } else {
      // 如果未指定 item_attr 或 common_attr 则都进行尝试获取
      if (item_attr_accessor && reco_result &&
          GetValueFromItemAttr<T>(context, item_attr_accessor, *reco_result, val, len, arg.default_value)) {
        return;
      }
      if (GetValueFromCommonAttr<T>(context, arg.attr_name, val, len, arg.default_value)) {
        return;
      }
    }
    for (int i = 0; i < len; i++) {
      val[i] = arg.default_value;
    }
  }

  template <typename T>
  bool GetValueFromItemAttr(ReadableRecoContextInterface *context, ItemAttr *item_attr_accessor,
                            const CommonRecoResult &reco_result, T *val, size_t len, T default_value) {
    if (reco_result.HasAttr(item_attr_accessor)) {
      if (auto float_val = reco_result.GetDoubleAttr(item_attr_accessor)) {
        val[0] = *float_val;
        for (int i = 1; i < len; i++) {
          val[i] = default_value;
        }
        return true;
      }
      if (auto int_val = reco_result.GetIntAttr(item_attr_accessor)) {
        val[0] = (float)*int_val;
        for (int i = 1; i < len; i++) {
          val[i] = default_value;
        }
        return true;
      }
      if (auto float_list_val = reco_result.GetDoubleListAttr(item_attr_accessor)) {
        for (int i = 0; i < len; i++) {
          if (i < float_list_val->size()) {
            val[i] = (*float_list_val)[i];
          } else {
            val[i] = default_value;
          }
        }
        return true;
      }
      if (auto int_list_val = reco_result.GetIntListAttr(item_attr_accessor)) {
        for (int i = 0; i < len; i++) {
          if (i < int_list_val->size()) {
            val[i] = (float)(*int_list_val)[i];
          } else {
            val[i] = default_value;
          }
        }
        return true;
      }
    }
    return false;
  }

  template <typename T>
  bool GetValueFromCommonAttr(ReadableRecoContextInterface *context, const std::string &attr_name, T *val,
                              size_t len, T default_value) {
    if (context->HasCommonAttr(attr_name)) {
      if (auto float_val = context->GetDoubleCommonAttr(attr_name)) {
        val[0] = *float_val;
        for (int i = 1; i < len; i++) {
          val[i] = default_value;
        }
        return true;
      }
      if (auto int_val = context->GetIntCommonAttr(attr_name)) {
        val[0] = (float)*int_val;
        for (int i = 1; i < len; i++) {
          val[i] = default_value;
        }
        return true;
      }
      if (auto float_list_val = context->GetDoubleListCommonAttr(attr_name)) {
        for (int i = 0; i < len; i++) {
          if (i < float_list_val->size()) {
            val[i] = (*float_list_val)[i];
          } else {
            val[i] = default_value;
          }
        }
        return true;
      }
      if (auto int_list_val = context->GetIntListCommonAttr(attr_name)) {
        for (int i = 0; i < len; i++) {
          if (i < int_list_val->size()) {
            val[i] = (float)(*int_list_val)[i];
          } else {
            val[i] = default_value;
          }
        }
        return true;
      }
    }
    return false;
  }

  bool AppendFeedTensor(ReadableRecoContextInterface *context, Argument argument, RecoResultConstIter begin,
                        RecoResultConstIter end, std::vector<tensorflow::Tensor> *feed_tensors) {
    switch (argument.data_type) {
#define CASE(DT)                 \
  case tensorflow::DataType::DT: \
    return AppendFeedTensorByDataType<tensorflow::DataType::DT>(context, argument, begin, end, feed_tensors);
      CASE(DT_FLOAT)
      CASE(DT_DOUBLE)
      CASE(DT_INT32)
      CASE(DT_UINT8)
      CASE(DT_INT16)
      CASE(DT_INT8)
      CASE(DT_INT64)
      CASE(DT_BOOL)
      CASE(DT_UINT16)
      CASE(DT_UINT32)
      CASE(DT_UINT64)
#undef CASE
      default:
        CL_LOG_ERROR("tensorflow", "unsupported_input_DataType")
            << "CommonRecoTensorFlowAttrEnricher unsupported input DataType: "
            << tensorflow::DataType_Name(argument.data_type);
        return false;
    }
  }

  template <tensorflow::DataType dt, typename data_type = typename tensorflow::EnumToDataType<dt>::Type>
  bool AppendFeedTensorByDataType(ReadableRecoContextInterface *context, Argument argument,
                                  RecoResultConstIter begin, RecoResultConstIter end,
                                  std::vector<tensorflow::Tensor> *feed_tensors) {
    int num_items = std::distance(begin, end);
    if (compress_common_input_ && argument.attr_source == AttrSource::COMMON) {
      if (argument.dim <= 0) {
        feed_tensors->push_back(tensorflow::Tensor(dt, {}));
        auto scalar = feed_tensors->back().scalar<data_type>();
        GetArgumentValue<data_type>(context, argument, nullptr, nullptr, &scalar(), 1);
      } else {
        feed_tensors->push_back(tensorflow::Tensor(dt, {argument.dim}));
        auto vec = feed_tensors->back().vec<data_type>();
        GetArgumentValue<data_type>(context, argument, nullptr, nullptr, &vec(0), argument.dim);
      }
    } else {
      ItemAttr *item_attr_accessor = nullptr;
      if (context->HasItemAttr(argument.attr_name)) {
        item_attr_accessor = context->GetItemAttrAccessor(argument.attr_name);
      }

      if (argument.dim <= 0) {
        feed_tensors->push_back(tensorflow::Tensor(dt, {num_items}));
        auto vec = feed_tensors->back().vec<data_type>();

        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          GetArgumentValue<data_type>(context, argument, item_attr_accessor, &*result_iter, &vec(item_offset),
                                      1);
          result_iter = std::next(result_iter);
        }
      } else {
        feed_tensors->push_back(tensorflow::Tensor(dt, {num_items, argument.dim}));
        auto matrix = feed_tensors->back().matrix<data_type>();

        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          GetArgumentValue<data_type>(context, argument, item_attr_accessor, &*result_iter,
                                      &matrix(item_offset, 0), argument.dim);
          result_iter = std::next(result_iter);
        }
      }
    }
    return true;
  }

  bool SaveFetchTensor(MutableRecoContextInterface *context, const std::string &attr_name,
                       RecoResultConstIter begin, RecoResultConstIter end,
                       const tensorflow::Tensor &fetch_tensor) {
    switch (fetch_tensor.dtype()) {
#define CASE(DT)                 \
  case tensorflow::DataType::DT: \
    return SaveFetchTensorByDataType<tensorflow::DataType::DT>(context, attr_name, begin, end, fetch_tensor);
      CASE(DT_FLOAT)
      CASE(DT_DOUBLE)
      CASE(DT_INT32)
      CASE(DT_UINT8)
      CASE(DT_INT16)
      CASE(DT_INT8)
      CASE(DT_INT64)
      CASE(DT_BOOL)
      CASE(DT_UINT16)
      CASE(DT_UINT32)
      CASE(DT_UINT64)
#undef CASE
      default:
        CL_LOG_ERROR("tensorflow", "unsupported_output_DataType")
            << "CommonRecoTensorFlowAttrEnricher unsupported output DataType: "
            << tensorflow::DataType_Name(fetch_tensor.dtype());
        return false;
    }
  }

  template <tensorflow::DataType dt, typename data_type = typename tensorflow::EnumToDataType<dt>::Type>
  std::enable_if_t<std::is_floating_point<data_type>::value, bool> SaveFetchTensorByDataType(
      MutableRecoContextInterface *context, const std::string &attr_name, RecoResultConstIter begin,
      RecoResultConstIter end, const tensorflow::Tensor &fetch_tensor) {
    if (output_common_attr_) {
      if (fetch_tensor.dims() == 0) {
        const auto &scalar = fetch_tensor.scalar<data_type>();
        context->SetDoubleCommonAttr(attr_name, scalar());
      } else if (fetch_tensor.dims() == 1) {
        const auto &vec = fetch_tensor.vec<data_type>();
        std::vector<double> double_list(vec.dimension(0));
        for (int offset = 0; offset < vec.dimension(0); offset++) {
          double_list[offset] = vec(offset);
        }
        context->SetDoubleListCommonAttr(attr_name, std::move(double_list));
      } else {
        CL_LOG_ERROR("tensorflow", "invalid_fetch_tensor_shape")
            << "CommonRecoTensorFlowAttrEnricher returns a tensor " << attr_name
            << " with supported shape for common attr: " << fetch_tensor.shape();
        return false;
      }
    } else {
      int num_items = std::distance(begin, end);
      auto *accessor = context->GetItemAttrAccessor(attr_name);
      if (fetch_tensor.dims() == 1 && fetch_tensor.dim_size(0) == num_items) {
        const auto &vec = fetch_tensor.vec<data_type>();
        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          result_iter->SetDoubleAttr(accessor, vec(item_offset));
          result_iter = std::next(result_iter);
        }
      } else if (fetch_tensor.dims() == 2 && fetch_tensor.dim_size(0) == num_items) {
        const auto &matrix = fetch_tensor.matrix<data_type>();
        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          std::vector<double> double_list(matrix.dimension(1));
          for (int offset = 0; offset < matrix.dimension(1); offset++) {
            double_list[offset] = matrix(item_offset, offset);
          }
          result_iter->SetDoubleListAttr(accessor, std::move(double_list));
          result_iter = std::next(result_iter);
        }
      } else {
        CL_LOG_ERROR("tensorflow", "invalid_fetch_tensor_shape")
            << "CommonRecoTensorFlowAttrEnricher returns a tensor " << attr_name
            << " with supported shape for item attr: " << fetch_tensor.shape();
        return false;
      }
    }
    return true;
  }

  template <tensorflow::DataType dt, typename data_type = typename tensorflow::EnumToDataType<dt>::Type>
  std::enable_if_t<std::is_integral<data_type>::value, bool> SaveFetchTensorByDataType(
      MutableRecoContextInterface *context, const std::string &attr_name, RecoResultConstIter begin,
      RecoResultConstIter end, const tensorflow::Tensor &fetch_tensor) {
    if (output_common_attr_) {
      if (fetch_tensor.dims() == 0) {
        const auto &scalar = fetch_tensor.scalar<data_type>();
        context->SetIntCommonAttr(attr_name, scalar());
      } else if (fetch_tensor.dims() == 1) {
        const auto &vec = fetch_tensor.vec<data_type>();
        std::vector<int64> int_list(vec.dimension(0));
        for (int offset = 0; offset < vec.dimension(0); offset++) {
          int_list[offset] = vec(offset);
        }
        context->SetIntListCommonAttr(attr_name, std::move(int_list));
      } else {
        CL_LOG_ERROR("tensorflow", "invalid_fetch_tensor_shape")
            << "CommonRecoTensorFlowAttrEnricher returns a tensor " << attr_name
            << " with supported shape for common attr: " << fetch_tensor.shape();
        return false;
      }
    } else {
      int num_items = std::distance(begin, end);
      auto *accessor = context->GetItemAttrAccessor(attr_name);
      if (fetch_tensor.dims() == 1 && fetch_tensor.dim_size(0) == num_items) {
        const auto &vec = fetch_tensor.vec<data_type>();
        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          result_iter->SetIntAttr(accessor, vec(item_offset));
          result_iter = std::next(result_iter);
        }
      } else if (fetch_tensor.dims() == 2 && fetch_tensor.dim_size(0) == num_items) {
        const auto &matrix = fetch_tensor.matrix<data_type>();
        auto result_iter = begin;
        for (int item_offset = 0; item_offset < num_items; item_offset++) {
          std::vector<int64> int_list(matrix.dimension(1));
          for (int offset = 0; offset < matrix.dimension(1); offset++) {
            int_list[offset] = matrix(item_offset, offset);
          }
          result_iter->SetIntListAttr(accessor, std::move(int_list));
          result_iter = std::next(result_iter);
        }
      } else {
        CL_LOG_ERROR("tensorflow", "invalid_fetch_tensor_shape")
            << "CommonRecoTensorFlowAttrEnricher returns a tensor " << attr_name
            << " with supported shape for item attr: " << fetch_tensor.shape();
        return false;
      }
    }
    return true;
  }

 private:
  tensorflow::Session *session_ = nullptr;
  tensorflow::Session::CallableHandle callable_handle_;
  // map tensor name to argument
  folly::F14FastMap<std::string, Argument> args_;
  std::vector<std::string> outputs_;
  bool output_common_attr_;
  bool compress_common_input_;

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTensorFlowAttrEnricher);
};

}  // namespace platform
}  // namespace ks
