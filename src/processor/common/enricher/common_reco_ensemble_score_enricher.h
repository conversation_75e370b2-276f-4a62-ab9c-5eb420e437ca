#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoEnsembleScoreEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoEnsembleScoreEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 protected:
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  struct EnsembleChannel {
    std::string name;
    const base::Json *enabled_config = nullptr;
    const base::Json *weight_config = nullptr;
    const base::Json *hyper_scala_config = nullptr;
    double weight = 0.0;
    double hyper_scala = 0.0;
    double hyper_division = 1.0;
    bool enabled = true;
    bool as_seq_value = false;
    ItemAttr *seq_attr = nullptr;
    std::string save_score_to;
    ItemAttr *save_score_to_accessor = nullptr;
  };

 private:
  bool InitProcessor() override {
    formula_version_ = config()->GetInt("formula_version", 0);

    const auto *channels = config()->Get("channels");
    if (!channels || !channels->IsArray()) {
      LOG(ERROR) << "CommonRecoEnsembleScoreEnricher init failed! Missing 'channels' config or not an array";
      return false;
    }

    for (const auto *cfg : channels->array()) {
      EnsembleChannel channel;
      channel.name = cfg->GetString("name");
      if (!channel.name.empty()) {
        channel.weight_config = cfg->Get("weight");
        channel.enabled_config = cfg->Get("enabled");
        channel.hyper_scala_config = cfg->Get("hyper_scala");
        channel.as_seq_value = cfg->GetBoolean("as_seq_value", false);
        if (formula_version_ == 2 && !channel.hyper_scala_config) {
          LOG(ERROR) << "CommonRecoEnsembleScoreEnricher init failed! Missing 'hyper_scala' config";
        }
        channel.save_score_to = cfg->GetString("save_score_to");
        ensemble_channels_.push_back(std::move(channel));
      }
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoEnsembleScoreEnricher init failed! Missing 'output_attr' config";
      return false;
    }

    regulator_config_ = config()->Get("regulator");
    smooth_config_ = config()->Get("smooth");
    if (formula_version_ == 0 && !smooth_config_) {
      LOG(ERROR) << "CommonRecoEnsembleScoreEnricher init failed! Missing 'smooth' config";
      return false;
    }
    if ((formula_version_ == 0 || formula_version_ == 1 || formula_version_ == 3) && !regulator_config_) {
      LOG(ERROR) << "CommonRecoEnsembleScoreEnricher init failed! Missing 'regulator' config";
      return false;
    }

    cliff_ratio_config_ = config()->Get("cliff_ratio");
    cliff_height_config_ = config()->Get("cliff_height");

    default_value_ = config()->GetNumber("default_value", 0.0);
    start_seq_ = config()->GetInt("start_seq", 0);
    stable_sort_ = config()->GetBoolean("stable_sort", false);
    allow_tied_seq_ = config()->GetBoolean("allow_tied_seq", false);
    continuous_tied_seq_ = config()->GetBoolean("continuous_tied_seq", false);
    epsilon_ = config()->GetNumber("epsilon", 1e-9);

    return true;
  }

  inline double GetDoubleValue(ItemAttr *attr_accessor, const CommonRecoResult &result) const;

 private:
  int formula_version_ = 0;
  const base::Json *regulator_config_ = nullptr;
  const base::Json *smooth_config_ = nullptr;
  const base::Json *cliff_ratio_config_ = nullptr;
  const base::Json *cliff_height_config_ = nullptr;
  std::vector<EnsembleChannel> ensemble_channels_;
  std::string output_attr_;
  double default_value_ = 0.0;
  int start_seq_ = 0;
  bool stable_sort_ = false;
  bool allow_tied_seq_ = false;
  bool continuous_tied_seq_ = false;
  double epsilon_ = 1e-9;

  std::vector<CommonRecoResult> item_list_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoEnsembleScoreEnricher);
};

}  // namespace platform
}  // namespace ks
