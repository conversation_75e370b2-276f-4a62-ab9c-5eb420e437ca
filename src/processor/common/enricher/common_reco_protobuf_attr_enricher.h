#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoProtobufAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrMapping {
    std::string proto_path;
    std::string attr_name;
  };

  bool InitProcessor() override {
    from_extra_var_ = config()->GetString("from_extra_var");
    if (from_extra_var_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufAttrEnricher init failed!"
                 << " Missing 'from_extra_var' config.";
      return false;
    }

    is_common_attr_ = config()->GetBoolean("is_common_attr", true);

    serialize_to_attr_ = config()->GetString("serialize_to_attr");

    pb_msg_max_depth_ = config()->GetInt("pb_msg_max_depth", -1);

    auto *attrs = config()->Get("attrs");
    if (attrs && attrs->IsArray()) {
      for (const auto *attr_json : attrs->array()) {
        MsgFieldPath msg_field_path;
        std::string attr_name;

        if (attr_json->IsString()) {
          msg_field_path.msg_path = attr_json->StringValue();
          attr_name = msg_field_path.msg_path;
        } else if (attr_json->IsObject()) {
          msg_field_path.msg_path = attr_json->GetString("path");
          msg_field_path.skip_unset_field = attr_json->GetBoolean("skip_unset_field", false);
          msg_field_path.repeat_align = attr_json->GetBoolean("repeat_align", false);
          attr_name = attr_json->GetString("name");
          msg_field_path.sample_attr_name = attr_json->GetString("sample_attr_name", "");
          msg_field_path.sample_attr_name_value = attr_json->GetInt("sample_attr_name_value", -1);
          if (attr_name.empty()) {
            if (!msg_field_path.sample_attr_name.empty()) {
              attr_name = msg_field_path.sample_attr_name;
            } else if (msg_field_path.sample_attr_name_value >= 0) {
              attr_name = std::to_string(msg_field_path.sample_attr_name_value);
            } else {
              attr_name = msg_field_path.msg_path;
            }
          }
          FillRepeatLimitMap(attr_json, msg_field_path.msg_path, &msg_field_path.field_repeated_limit);
        } else {
          LOG(ERROR) << "CommonRecoProtobufAttrEnricher init failed! Item of attrs should be a"
                     << " string! Value found: " << attr_json->ToString();
          return false;
        }
        bool inserted = attr_path_map_.emplace(attr_name, std::move(msg_field_path)).second;
        if (!inserted) {
          LOG(ERROR) << "CommonRecoProtobufAttrEnricher init failed! duplicated attr_name detected: "
                     << attr_name;
          return false;
        }
      }
    }

    save_all_fields_ = config()->GetBoolean("save_all_fields", false);

    if (save_all_fields_) {
      if (!attr_path_map_.empty()) {
        LOG(ERROR) << "CommonRecoProtobufAttrEnricher init failed!"
                   << " 'attrs' cannot be set when 'save_all_fields' is true.";
        return false;
      }
      CL_LOG_EVERY_N(WARNING, 100) << "CommonRecoProtobufAttrEnricher warning!"
                                   << " Enrich all protobuf fields will increase a lot of latency. Please "
                                      " DO NOT use it for online service!";
    } else if (attr_path_map_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufAttrEnricher init failed!"
                 << " 'attrs' cannot be empty when 'save_all_fields' is false.";
      return false;
    }

    save_attr_names_to_attr_ = config()->GetString("save_attr_names_to_attr");

    output_attr_prefix_ = config()->GetString("output_attr_prefix", "");

    return true;
  }

  // 此函数不再判断 proto path 是否有效，需要先保证 proto path 有效性。
  void FillRepeatLimitMap(const base::Json *attr_json, const std::string &msg_path,
                          std::vector<int> *repeat_limit) {
    const auto *repeated_mapping = attr_json->Get("repeat_limit");
    if (repeated_mapping == nullptr || !repeated_mapping->IsObject()) {
      return;
    }
    std::vector<std::string> msg_path_list = absl::StrSplit(msg_path, '.', absl::SkipEmpty());
    repeat_limit->resize(msg_path_list.size(), -1);
    bool has_valid_limit = false;
    for (const auto &pr : repeated_mapping->objects()) {
      std::vector<std::string> limit_path_list = absl::StrSplit(pr.first, '.', absl::SkipEmpty());
      if (limit_path_list.empty() || limit_path_list.size() > msg_path_list.size()) {
        CL_LOG(WARNING) << "ignored invalid repeat_limit path '" << pr.first << "'"
                        << " for msg_path " << msg_path << "'";
        continue;
      }
      int limit = pr.second->IntValue(-1);
      if (limit < 0) {
        continue;
      }
      if (!std::equal(limit_path_list.begin(), limit_path_list.end(), msg_path_list.begin())) {
        CL_LOG(WARNING) << "ignored invalid repeat_limit path '" << pr.first << "'"
                        << " for msg_path " << msg_path << "'";
        continue;
      }
      (*repeat_limit)[limit_path_list.size() - 1] = limit;
      has_valid_limit = true;
    }
    if (!has_valid_limit) {
      repeat_limit->clear();
    }
  }

 private:
  struct MsgFieldPath {
    std::vector<int> field_path;
    std::vector<int> field_repeated_limit;
    std::string msg_path;
    bool valid = true;
    ItemAttr *attr_accessor = nullptr;
    bool skip_unset_field = false;
    bool repeat_align = false;
    const interop::SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr;
    std::string sample_attr_name;
    int64 sample_attr_name_value = -1;
  };
  std::string from_extra_var_;
  std::string serialize_to_attr_;
  std::string save_attr_names_to_attr_;
  std::string output_attr_prefix_;
  int pb_msg_max_depth_;
  bool save_all_fields_ = false;
  bool is_common_attr_ = true;
  std::unordered_map<std::string, MsgFieldPath> attr_path_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufAttrEnricher);
};

}  // namespace platform
}  // namespace ks
