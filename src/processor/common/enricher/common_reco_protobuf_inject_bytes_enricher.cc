#include "dragon/src/processor/common/enricher/common_reco_protobuf_inject_bytes_enricher.h"

#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
namespace ks {
namespace platform {


void AppendVarint32(int value, std::string *output) {
  int size = google::protobuf::io::CodedOutputStream::VarintSize32(value);
  std::vector<uint8> buff(size);
  google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(value, buff.data());
  output->append(reinterpret_cast<const char *>(buff.data()), size);
}

void CommonRecoProtobufInjectBytesEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  if (!init_accessor_) {
    for (auto &append_config : append_item_configs_) {
      if (append_config.field_type == "double") {
        AppendVarint32(append_config.field << 3 | 1, &append_config.field_varint32);
      } else if (append_config.field_type == "float") {
        AppendVarint32(append_config.field << 3 | 5, &append_config.field_varint32);
      } else if (append_config.field_type == "int" || append_config.field_type == "int64") {
        AppendVarint32(append_config.field << 3 | 0, &append_config.field_varint32);
      } else {
        AppendVarint32(append_config.field << 3 | 2, &append_config.field_varint32);
      }
      append_config.attr_accessor = context->GetItemAttrAccessor(append_config.attr_name);
    }
    for (auto &append_config : append_common_configs_) {
      if (append_config.field_type == "double") {
        AppendVarint32(append_config.field << 3 | 1, &append_config.field_varint32);
      } else if (append_config.field_type == "float") {
        AppendVarint32(append_config.field << 3 | 5, &append_config.field_varint32);
      } else if (append_config.field_type == "int" || append_config.field_type == "int64") {
        AppendVarint32(append_config.field << 3 | 0, &append_config.field_varint32);
      } else {
        AppendVarint32(append_config.field << 3 | 2, &append_config.field_varint32);
      }
    }
    init_accessor_ = true;
  }
  if (!from_common_attr_.empty()) {
    auto input_attr = context->GetStringCommonAttr(from_common_attr_);
    if (!input_attr) {
      CL_LOG_ERROR("inject_pb_bytes", "attr_not_found:" + from_common_attr_)
          << "protobuf inject bytes common attr not found: " << from_common_attr_;
      return;
    }

    thread_local std::string payload;
    payload.clear();
    payload.append(input_attr->data(), input_attr->size());
    for (const auto &append_config : append_common_configs_) {
      if (append_config.field_type == "double") {
        absl::optional<double> append_msg = context->GetDoubleCommonAttr(append_config.attr_name);
        if (append_msg) {
          payload.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&payload);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "float") {
        absl::optional<double> append_msg = context->GetDoubleCommonAttr(append_config.attr_name);
        if (append_msg) {
          payload.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&payload);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteFloatNoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "int") {
        absl::optional<int64> append_msg = context->GetIntCommonAttr(append_config.attr_name);
        if (append_msg) {
          payload.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&payload);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteInt32NoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "int64") {
        absl::optional<int64> append_msg = context->GetIntCommonAttr(append_config.attr_name);
        if (append_msg) {
          payload.append(append_config.field_varint32);
          auto z = static_cast<uint64>(*append_msg);
          google::protobuf::io::StringOutputStream stream(&payload);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteInt64NoTag(*append_msg, &coded_output);
        }
      } else {
        auto append_msg = context->GetStringCommonAttr(append_config.attr_name);
        if (append_msg) {
          payload.append(append_config.field_varint32);
          AppendVarint32(append_msg->size(), &payload);
          payload.append(append_msg->data(), append_msg->size());
        }
      }
    }

    for (const auto &append_config : append_item_configs_) {
      if (append_config.field_type == "double") {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto append_msg = context->GetDoubleItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(*append_msg, &coded_output);
          }
        });
      } else if (append_config.field_type == "float") {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto append_msg = context->GetDoubleItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteFloatNoTag(*append_msg, &coded_output);
          }
        });
      } else if (append_config.field_type == "int") {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto append_msg = context->GetIntItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteInt32NoTag(*append_msg, &coded_output);
          }
        });
      } else if (append_config.field_type == "int64") {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto append_msg = context->GetIntItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteInt64NoTag(*append_msg, &coded_output);
          }
        });
      } else {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          auto append_msg = context->GetStringItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            AppendVarint32(append_msg->size(), &payload);
            payload.append(append_msg->data(), append_msg->size());
          }
        });
      }
    }
    context->SetStringCommonAttr(output_common_attr_, std::move(payload));
  } else {
    auto *item_attr_accessor = context->GetItemAttrAccessor(from_item_attr_);
    auto *output_attr_accessor =
        output_item_attr_.empty() ? nullptr : context->GetItemAttrAccessor(output_item_attr_);
    thread_local std::string append_common_msgs;
    append_common_msgs.clear();
    for (const auto &append_config : append_common_configs_) {
      if (append_config.field_type == "double") {
        absl::optional<double> append_msg = context->GetDoubleCommonAttr(append_config.attr_name);
        if (append_msg) {
          append_common_msgs.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&append_common_msgs);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "float") {
        absl::optional<double> append_msg = context->GetDoubleCommonAttr(append_config.attr_name);
        if (append_msg) {
          append_common_msgs.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&append_common_msgs);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteFloatNoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "int") {
        absl::optional<int64> append_msg = context->GetIntCommonAttr(append_config.attr_name);
        if (append_msg) {
          append_common_msgs.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&append_common_msgs);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteInt32NoTag(*append_msg, &coded_output);
        }
      } else if (append_config.field_type == "int64") {
        absl::optional<int64> append_msg = context->GetIntCommonAttr(append_config.attr_name);
        if (append_msg) {
          append_common_msgs.append(append_config.field_varint32);
          google::protobuf::io::StringOutputStream stream(&append_common_msgs);
          google::protobuf::io::CodedOutputStream coded_output(&stream);
          google::protobuf::internal::WireFormatLite::WriteInt64NoTag(*append_msg, &coded_output);
        }
      } else {
        auto append_msg = context->GetStringCommonAttr(append_config.attr_name);
        if (append_msg) {
          append_common_msgs.append(append_config.field_varint32);
          AppendVarint32(append_msg->size(), &append_common_msgs);
          append_common_msgs.append(append_msg->data(), append_msg->size());
        }
      }
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      auto input_attr = context->GetStringItemAttr(result, item_attr_accessor);
      if (!input_attr) {
        CL_LOG_ERROR_EVERY("inject_pb_bytes", "item_attr_not_found:" + from_item_attr_, 1000)
            << "inject pb bytes failed item attr not found: " << from_item_attr_ << " (" << item_key << ")";
        return;
      }
      thread_local std::string payload;
      payload.clear();
      payload.append(input_attr->data(), input_attr->size());
      payload.append(append_common_msgs);
      for (const auto &append_config : append_item_configs_) {
        if (append_config.field_type == "double") {
          auto append_msg = context->GetDoubleItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteDoubleNoTag(*append_msg, &coded_output);
          }
        } else if (append_config.field_type == "float") {
          auto append_msg = context->GetDoubleItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteFloatNoTag(*append_msg, &coded_output);
          }
        } else if (append_config.field_type == "int") {
          auto append_msg = context->GetIntItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteInt32NoTag(*append_msg, &coded_output);
          }
        } else if (append_config.field_type == "int64") {
          auto append_msg = context->GetIntItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            google::protobuf::io::StringOutputStream stream(&payload);
            google::protobuf::io::CodedOutputStream coded_output(&stream);
            google::protobuf::internal::WireFormatLite::WriteInt64NoTag(*append_msg, &coded_output);
          }
        } else {
          auto append_msg = context->GetStringItemAttr(result, append_config.attr_accessor);
          if (append_msg) {
            payload.append(append_config.field_varint32);
            AppendVarint32(append_msg->size(), &payload);
            payload.append(append_msg->data(), append_msg->size());
          }
        }
      }
      context->SetStringItemAttr(result, output_attr_accessor, std::move(payload));
    });
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoProtobufInjectBytesEnricher,
                 CommonRecoProtobufInjectBytesEnricher)

}  // namespace platform
}  // namespace ks
