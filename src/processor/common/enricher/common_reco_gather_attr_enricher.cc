#include "dragon/src/processor/common/enricher/common_reco_gather_attr_enricher.h"

#include <vector>

namespace ks {
namespace platform {

void CommonRecoGatherAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessCommonAttrs(context);
  } else {
    ProcessItemAttrs(context, begin, end);
  }
}

void CommonRecoGatherAttrEnricher::ProcessCommonAttrs(MutableRecoContextInterface *context) {
  absl::optional<absl::Span<const int64>> index_vector;
  std::vector<int64> singular_index_vector;
  bool output_singular_value = false;

  index_vector = context->GetIntListCommonAttr(index_attr_);
  if (!index_vector) {
    if (auto attr = context->GetIntCommonAttr(index_attr_)) {
      singular_index_vector.push_back(*attr);
      index_vector = singular_index_vector;
      output_singular_value = true;
    } else {
      CL_LOG(ERROR) << "missing index attr: " << index_attr_;
      return;
    }
  }

  int begin = 0, end = 0, range_size = 0;
  if (is_range_) {
    if (index_vector->size() < 2) {
      CL_LOG(ERROR) << "range need index attr list size >= 2";
      return;
    }
    begin = index_vector->at(0);
    end = index_vector->at(1);
    if (begin > end) {
      CL_LOG(ERROR) << "invalid range, begin should not bigger than end, begin: " << begin << " end: " << end;
      return;
    }
    range_size = end - begin;
  }

  size_t element_size = (!is_range_) ? index_vector->size() : range_size;

  for (const auto &output_attr_input_attr : map_output_attrs_to_input_attrs_) {
    const std::string &output_attr = output_attr_input_attr.first;
    const std::string &input_attr = output_attr_input_attr.second;
    if (auto int_list = context->GetIntListCommonAttr(input_attr)) {
      std::vector<int64> out;
      out.reserve(element_size);
      if (!is_range_) {
        for (int64 index : *index_vector) {
          if (index >= int_list->size()) {
            break;
          }
          out.push_back((*int_list)[index]);
        }
      } else {
        for (int index = begin; index < int_list->size() && index < end; ++index) {
          out.push_back((*int_list)[index]);
        }
      }
      if (out.size() == element_size) {
        if (output_singular_value && out.size() == 1) {
          context->SetIntCommonAttr(output_attr, out.at(0));
        } else {
          context->SetIntListCommonAttr(output_attr, std::move(out));
        }
      }
    } else if (auto double_list = context->GetDoubleListCommonAttr(input_attr)) {
      std::vector<double> out;
      out.reserve(element_size);
      if (!is_range_) {
        for (int64 index : *index_vector) {
          if (index >= double_list->size()) {
            break;
          }
          out.push_back((*double_list)[index]);
        }
      } else {
        for (int index = begin; index < double_list->size() && index < end; ++index) {
          out.push_back((*double_list)[index]);
        }
      }
      if (out.size() == index_vector->size()) {
        if (output_singular_value && out.size() == 1) {
          context->SetDoubleCommonAttr(output_attr, out.at(0));
        } else {
          context->SetDoubleListCommonAttr(output_attr, std::move(out));
        }
      }
    } else if (auto str_list = context->GetStringListCommonAttr(input_attr)) {
      std::vector<std::string> out;
      out.reserve(element_size);
      if (!is_range_) {
        for (int64 index : *index_vector) {
          if (index >= str_list->size()) {
            break;
          }
          out.push_back(std::string((*str_list)[index]));
        }
      } else {
        for (int index = begin; index < str_list->size() && index < end; ++index) {
          out.push_back(std::string((*str_list)[index]));
        }
      }
      if (out.size() == index_vector->size()) {
        if (output_singular_value && out.size() == 1) {
          context->SetStringCommonAttr(output_attr, out.at(0));
        } else {
          context->SetStringListCommonAttr(output_attr, std::move(out));
        }
      }
    }
  }
}

void CommonRecoGatherAttrEnricher::ProcessItemAttrs(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  if (!context->HasItemAttr(index_attr_)) {
    CL_LOG(ERROR) << "missing index attr: " << index_attr_;
    return;
  }

  auto index_vector_accessor = context->GetItemAttrAccessor(index_attr_);
  auto value_type = index_vector_accessor->value_type;
  if (value_type != AttrType::INT_LIST && value_type != AttrType::INT) {
    CL_LOG(ERROR) << "unsupported value type (only support int_list|int), index_attr: " << index_attr_ << " "
                  << index_vector_accessor->GetDebugString();
    return;
  }
  bool output_singular_value = (value_type == AttrType::INT);

  std::vector<std::pair<ItemAttr *, ItemAttr *>> output_accessors_and_input_accessors;

  output_accessors_and_input_accessors.reserve(map_output_attrs_to_input_attrs_.size());

  for (const auto &output_attr_input_attr : map_output_attrs_to_input_attrs_) {
    const std::string &output_attr = output_attr_input_attr.first;
    const std::string &input_attr = output_attr_input_attr.second;

    if (!context->HasItemAttr(input_attr)) {
      CL_LOG(ERROR) << "skip missing input attr: " << input_attr;
      return;
    }

    ItemAttr *input_attr_accessor = context->GetItemAttrAccessor(input_attr);
    AttrType input_type = input_attr_accessor->value_type;

    if (input_type != AttrType::INT_LIST && input_type != AttrType::FLOAT_LIST &&
        input_type != AttrType::STRING_LIST) {
      CL_LOG(ERROR) << "skip input attr " << input_attr << " with unsupported type "
                    << RecoUtil::GetAttrTypeName(input_type);
      return;
    }

    ItemAttr *output_attr_accessor = context->GetItemAttrAccessor(output_attr);
    output_accessors_and_input_accessors.push_back(std::make_pair(output_attr_accessor, input_attr_accessor));
  }

  std::vector<int64> singular_index_vector;
  std::for_each(begin, end, [&](const CommonRecoResult &item) {
    absl::optional<absl::Span<const int64>> index_vector;
    if (!output_singular_value) {
      index_vector = context->GetIntListItemAttr(item, index_vector_accessor);
    } else {
      if (auto attr = context->GetIntItemAttr(item, index_vector_accessor)) {
        singular_index_vector.clear();
        singular_index_vector.push_back(*attr);
        index_vector = singular_index_vector;
      }
    }

    if (!index_vector) return;

    for (auto &output_accessor_and_input_accessor : output_accessors_and_input_accessors) {
      ItemAttr *input_attr = output_accessor_and_input_accessor.second;
      ItemAttr *output_attr = output_accessor_and_input_accessor.first;
      if (auto int_list = item.GetIntListAttr(input_attr)) {
        std::vector<int64> out;
        out.reserve(index_vector->size());
        for (int64 index : *index_vector) {
          if (index >= int_list->size()) {
            break;
          }
          out.push_back((*int_list)[index]);
        }
        if (out.size() == index_vector->size()) {
          if (output_singular_value && out.size() == 1) {
            item.SetIntAttr(output_attr, out.at(0));
          } else {
            item.SetIntListAttr(output_attr, std::move(out));
          }
        }
      } else if (auto double_list = item.GetDoubleListAttr(input_attr)) {
        std::vector<double> out;
        out.reserve(index_vector->size());
        for (int64 index : *index_vector) {
          if (index >= double_list->size()) {
            break;
          }
          out.push_back((*double_list)[index]);
        }
        if (out.size() == index_vector->size()) {
          if (output_singular_value && out.size() == 1) {
            item.SetDoubleAttr(output_attr, out.at(0));
          } else {
            item.SetDoubleListAttr(output_attr, std::move(out));
          }
        }
      } else if (auto str_list = item.GetStringListAttr(input_attr)) {
        std::vector<std::string> out;
        out.reserve(index_vector->size());
        for (int64 index : *index_vector) {
          if (index >= str_list->size()) {
            break;
          }
          out.push_back(std::string((*str_list)[index]));
        }
        if (out.size() == index_vector->size()) {
          if (output_singular_value && out.size() == 1) {
            item.SetStringAttr(output_attr, out.at(0));
          } else {
            item.SetStringListAttr(output_attr, std::move(out));
          }
        }
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoGatherAttrEnricher, CommonRecoGatherAttrEnricher)

}  // namespace platform
}  // namespace ks
