#include "dragon/src/processor/common/enricher/common_reco_json_attr_enricher.h"

#include <memory>

#include "absl/types/span.h"
#include "absl/strings/string_view.h"

#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {
namespace {
void FollowJsonPath(const base::Json *json, absl::Span<const std::string> json_path,
                    std::vector<const base::Json *> *json_objs) {
  if (!json) {
    return;
  }

  if (json->IsArray()) {
    json_objs->reserve(json_objs->size() + json->size());
    for (const auto *sub_json : json->array()) {
      FollowJsonPath(sub_json, json_path, json_objs);
    }
    return;
  }

  if (json_path.empty()) {
    json_objs->push_back(json);
    return;
  }

  if (json->IsObject()) {
    FollowJsonPath(json->Get(json_path[0]), json_path.subspan(1), json_objs);
  }
}
}  // namespace

void CommonRecoJsonAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessCommonAttrs(context);
  } else {
    ProcessItemAttrs(context, begin, end);
  }
}

void CommonRecoJsonAttrEnricher::ProcessCommonAttrs(MutableRecoContextInterface *context) {
  std::vector<std::shared_ptr<base::Json>> jsons;
  if (auto str_val = context->GetStringCommonAttr(import_attr_)) {
    jsons.push_back(std::make_shared<base::Json>(base::BufferToJson(str_val->data(), str_val->size())));
  } else if (auto str_list_val = context->GetStringListCommonAttr(import_attr_)) {
    for (absl::string_view str_val : *str_list_val) {
      jsons.push_back(std::make_shared<base::Json>(base::BufferToJson(str_val.data(), str_val.size())));
    }
  }

  for (const auto &attr_name_attr_config : attr_configs_map_) {
    const std::string &attr_name = attr_name_attr_config.first;
    const auto &attr_config = attr_name_attr_config.second;

    std::vector<const base::Json *> json_objs;
    for (const auto &json_ptr : jsons) {
      FollowJsonPath(json_ptr.get(), attr_config.json_path, &json_objs);
    }

    if (json_objs.empty()) {
      continue;
    }

    switch (attr_config.output_type) {
      case AttrConfig::OutputType::kJsonStringList: {
        std::vector<std::string> str_list;
        str_list.reserve(json_objs.size());
        for (const base::Json *json : json_objs) {
          str_list.push_back(base::JsonToString(json->get()));
        }
        context->SetStringListCommonAttr(attr_name, std::move(str_list));
        break;
      }
      case AttrConfig::OutputType::kJsonString: {
        std::string str_val = base::JsonToString(json_objs[0]->get());
        context->SetStringCommonAttr(attr_name, std::move(str_val));
        break;
      }
      case AttrConfig::OutputType::kInt64: {
        for (const base::Json *json : json_objs) {
          int64 int_val;
          if (json->IntValue(&int_val)) {
            context->SetIntCommonAttr(attr_name, int_val);
            break;
          }
        }
        break;
      }
      case AttrConfig::OutputType::kDouble: {
        for (const base::Json *json : json_objs) {
          double double_val;
          if (json->NumberValue(&double_val)) {
            context->SetDoubleCommonAttr(attr_name, double_val);
            break;
          }
        }
        break;
      }
      case AttrConfig::OutputType::kString: {
        for (const base::Json *json : json_objs) {
          std::string str_val;
          if (json->StringValue(&str_val)) {
            context->SetStringCommonAttr(attr_name, std::move(str_val));
            break;
          }
        }
        break;
      }
      case AttrConfig::OutputType::kInt64List: {
        std::vector<int64> int_list;
        int_list.reserve(json_objs.size());
        for (const base::Json *json : json_objs) {
          int64 int_val;
          if (json->IntValue(&int_val)) {
            int_list.push_back(int_val);
          }
        }
        context->SetIntListCommonAttr(attr_name, std::move(int_list));
        break;
      }
      case AttrConfig::OutputType::kDoubleList: {
        std::vector<double> double_list;
        double_list.reserve(json_objs.size());
        for (const base::Json *json : json_objs) {
          double double_val;
          if (json->NumberValue(&double_val)) {
            double_list.push_back(double_val);
          }
        }
        context->SetDoubleListCommonAttr(attr_name, std::move(double_list));
        break;
      }
      case AttrConfig::OutputType::kStringList: {
        std::vector<std::string> str_list;
        str_list.reserve(json_objs.size());
        for (const base::Json *json : json_objs) {
          std::string str_val;
          if (json->StringValue(&str_val)) {
            str_list.push_back(std::move(str_val));
          }
        }
        context->SetStringListCommonAttr(attr_name, std::move(str_list));
        break;
      }
    }
  }
}

void CommonRecoJsonAttrEnricher::ProcessItemAttrs(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  ItemAttr *import_attr_accessor = context->GetItemAttrAccessor(import_attr_);
  if (!import_attr_accessor) {
    return;
  }

  for (auto &attr_name_attr_config : attr_configs_map_) {
    const std::string &attr_name = attr_name_attr_config.first;
    auto &attr_config = attr_name_attr_config.second;

    attr_config.accessor = context->GetItemAttrAccessor(attr_name);
  }

  std::for_each(begin, end, [&](const CommonRecoResult &item) {
    std::vector<std::shared_ptr<base::Json>> jsons;
    if (auto str_val = item.GetStringAttr(import_attr_accessor)) {
      jsons.push_back(std::make_shared<base::Json>(base::BufferToJson(str_val->data(), str_val->size())));
    } else if (auto str_list_val = item.GetStringListAttr(import_attr_accessor)) {
      for (absl::string_view str_val : *str_list_val) {
        jsons.push_back(std::make_shared<base::Json>(base::BufferToJson(str_val.data(), str_val.size())));
      }
    }

    for (const auto &attr_name_attr_config : attr_configs_map_) {
      const auto &attr_config = attr_name_attr_config.second;

      std::vector<const base::Json *> json_objs;
      for (const auto &json_ptr : jsons) {
        FollowJsonPath(json_ptr.get(), attr_config.json_path, &json_objs);
      }

      if (json_objs.empty()) {
        continue;
      }

      switch (attr_config.output_type) {
        case AttrConfig::OutputType::kJsonStringList: {
          std::vector<std::string> str_list;
          str_list.reserve(json_objs.size());
          for (const base::Json *json : json_objs) {
            str_list.push_back(base::JsonToString(json->get()));
          }
          item.SetStringListAttr(attr_config.accessor, std::move(str_list));
          break;
        }
        case AttrConfig::OutputType::kJsonString: {
          std::string str_val = base::JsonToString(json_objs[0]->get());
          item.SetStringAttr(attr_config.accessor, std::move(str_val));
          break;
        }
        case AttrConfig::OutputType::kInt64: {
          for (const base::Json *json : json_objs) {
            int64 int_val;
            if (json->IntValue(&int_val)) {
              item.SetIntAttr(attr_config.accessor, int_val);
              break;
            }
          }
          break;
        }
        case AttrConfig::OutputType::kDouble: {
          for (const base::Json *json : json_objs) {
            double double_val;
            if (json->NumberValue(&double_val)) {
              item.SetDoubleAttr(attr_config.accessor, double_val);
              break;
            }
          }
          break;
        }
        case AttrConfig::OutputType::kString: {
          for (const base::Json *json : json_objs) {
            std::string str_val;
            if (json->StringValue(&str_val)) {
              item.SetStringAttr(attr_config.accessor, str_val);
              break;
            }
          }
          break;
        }
        case AttrConfig::OutputType::kInt64List: {
          std::vector<int64> int_list;
          int_list.reserve(json_objs.size());
          for (const base::Json *json : json_objs) {
            int64 int_val;
            if (json->IntValue(&int_val)) {
              int_list.push_back(int_val);
            }
          }
          item.SetIntListAttr(attr_config.accessor, std::move(int_list));
          break;
        }
        case AttrConfig::OutputType::kDoubleList: {
          std::vector<double> double_list;
          double_list.reserve(json_objs.size());
          for (const base::Json *json : json_objs) {
            double double_val;
            if (json->NumberValue(&double_val)) {
              double_list.push_back(double_val);
            }
          }
          item.SetDoubleListAttr(attr_config.accessor, std::move(double_list));
          break;
        }
        case AttrConfig::OutputType::kStringList: {
          std::vector<std::string> str_list;
          str_list.reserve(json_objs.size());
          for (const base::Json *json : json_objs) {
            std::string str_val;
            if (json->StringValue(&str_val)) {
              str_list.push_back(std::move(str_val));
            }
          }
          item.SetStringListAttr(attr_config.accessor, std::move(str_list));
          break;
        }
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoJsonAttrEnricher, CommonRecoJsonAttrEnricher)

}  // namespace platform
}  // namespace ks
