#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/embed_calc/enricher/util.h"
namespace ks {
namespace platform {

class CommonRecoUnpackBytesEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoUnpackBytesEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct SchemaPartValue {
    enum class DType : int {
      INT8,
      INT16,
      INT32,
      INT64,
      FP16,
      FP32,
      FP64,
      SCALEINT8,
    } dtype;
    std::string attr_name;
    size_t byte_size;
    size_t offset;
    size_t dim;
    ItemAttr *item_attr_accessor;
  };

  bool InitProcessor() override {
    is_common_ = config()->GetBoolean("is_common", is_common_);
    is_array_ = config()->GetBoolean("is_array", is_array_);
    is_str_list_ = config()->GetBoolean("is_str_list", is_str_list_);
    auto *bytes_schema = config()->Get("schema");
    if (!bytes_schema || !bytes_schema->IsArray()) {
      CL_LOG_ERROR("unpack_bytes", "invalid_schema")
          << "CommonRecoUnpackBytesEnricher init failed! 'schema' should be an array";
      return false;
    }

    for (auto *part_config : bytes_schema->array()) {
      if (!part_config->IsObject()) {
        CL_LOG_ERROR("unpack_bytes", "invalid_schema_part")
            << "CommonRecoUnpackBytesEnricher init failed! 'schema' should be an array of objects";
        return false;
      }

      std::string attr_name = part_config->GetString("attr_name");
      if (attr_name.empty()) {
        CL_LOG_ERROR("unpack_bytes", "invalid_schema_part")
            << "CommonRecoUnpackBytesEnricher init failed! 'attr_name' is required for 'schema'";
        return false;
      }

      std::string dtype_string = part_config->GetString("dtype");
      size_t dim = part_config->GetInt("dim", 1);
      SchemaPartValue::DType dtype;
      size_t byte_size = 0;
      if (dtype_string == "int8") {
        dtype = SchemaPartValue::DType::INT8;
        byte_size = 1;
      } else if (dtype_string == "int16") {
        dtype = SchemaPartValue::DType::INT16;
        byte_size = 2;
      } else if (dtype_string == "int32") {
        dtype = SchemaPartValue::DType::INT32;
        byte_size = 4;
      } else if (dtype_string == "int64") {
        dtype = SchemaPartValue::DType::INT64;
        byte_size = 8;
      } else if (dtype_string == "fp16") {
        dtype = SchemaPartValue::DType::FP16;
        byte_size = 2;
      } else if (dtype_string == "fp32") {
        dtype = SchemaPartValue::DType::FP32;
        byte_size = 4;
      } else if (dtype_string == "fp64") {
        dtype = SchemaPartValue::DType::FP64;
        byte_size = 8;
      } else if (dtype_string == "scale_int8") {
        dtype = SchemaPartValue::DType::SCALEINT8;
        byte_size = 1;
        if (dim > 1) {
          byte_size = dim + sizeof(float);
        }
      } else {
        CL_LOG_ERROR("unpack_bytes", "invalid_schema_part_dtype")
            << "CommonRecoUnpackBytesEnricher init failed! Unsupported dtype found: " << dtype_string;
        return false;
      }
      if (dim > 1 && dtype != SchemaPartValue::DType::SCALEINT8) {
        CL_LOG_ERROR("unpack_bytes", "invalid unpack config") << "dim: " << dim << "dtype: " << dtype_string;
      }
      schema_.push_back(SchemaPartValue{.dtype = dtype,
                                        .attr_name = std::move(attr_name),
                                        .byte_size = byte_size,
                                        .offset = single_tuple_size_,
                                        .dim = dim});
      single_tuple_size_ += byte_size;
    }

    source_attr_name_ = config()->GetString("source_attr_name");
    if (source_attr_name_.empty()) {
      CL_LOG_ERROR("unpack_bytes", "missing_source_attr_name")
          << "CommonRecoUnpackBytesEnricher init failed! 'source_attr_name' is required.";
      return false;
    }
    return true;
  }

  void HandleCommonAttr(MutableRecoContextInterface *context) const;
  void HandleItemAttr(MutableRecoContextInterface *context, const CommonRecoResult &result,
                      ItemAttr *source_attr_accessor) const;
  template <typename T>
  inline bool UnpackIntBytes(MutableRecoContextInterface *context, const char *bytes,
                             const SchemaPartValue *part, size_t array_length,
                             const CommonRecoResult *result = nullptr) const {
    bool success = false;
    if (is_array_) {
      std::vector<int64> values;
      for (int i = 0; i < array_length; i++) {
        T value;
        memcpy(&value, bytes + part->offset + single_tuple_size_ * i, part->byte_size);
        values.push_back((int64)value);
      }
      if (is_common_) {
        success = context->SetIntListCommonAttr(part->attr_name, std::move(values));
      } else {
        success = context->SetIntListItemAttr(*result, part->item_attr_accessor, std::move(values));
      }
    } else {
      T value;
      memcpy(&value, bytes + part->offset, part->byte_size);
      if (is_common_) {
        success = context->SetIntCommonAttr(part->attr_name, (int64)value);
      } else {
        success = context->SetIntItemAttr(*result, part->item_attr_accessor, (int64)value);
      }
    }
    return success;
  }
  template <typename T>
  inline bool UnpackIntListBytes(MutableRecoContextInterface *context,
                                 absl::optional<std::vector<absl::string_view>> bytes_list,  // const char
                                                                                             // *bytes,
                                 const SchemaPartValue *part, size_t array_length,
                                 const CommonRecoResult *result = nullptr) const {
    bool success = false;
    if (is_array_) {
      std::vector<int64> values;
      for (int i = 0; i < array_length; i++) {
        auto bytes = (bytes_list->at(i)).data();
        size_t array_length_i = (bytes_list->at(i)).size() / single_tuple_size_;
        for (int j = 0; j < array_length_i; j++) {
          T value;
          memcpy(&value, bytes + part->offset + single_tuple_size_ * j, part->byte_size);
          values.push_back((int64)value);
        }
      }
      if (is_common_) {
        success = context->SetIntListCommonAttr(part->attr_name, std::move(values));
      } else {
        success = context->SetIntListItemAttr(*result, part->item_attr_accessor, std::move(values));
      }
    }
    return success;
  }
  template <typename T>
  inline bool UnpackDoubleBytes(MutableRecoContextInterface *context, const char *bytes,
                                const SchemaPartValue *part, int array_length,
                                const CommonRecoResult *result = nullptr) const {
    bool success = false;
    if (is_array_) {
      std::vector<double> values;
      for (int i = 0; i < array_length; i++) {
        T value;
        memcpy(&value, bytes + part->offset + single_tuple_size_ * i, part->byte_size);
        values.push_back(static_cast<double>(value));
      }
      if (is_common_) {
        success = context->SetDoubleListCommonAttr(part->attr_name, std::move(values));
      } else {
        success = context->SetDoubleListItemAttr(*result, part->item_attr_accessor, std::move(values));
      }
    } else {
      T value;
      memcpy(&value, bytes + part->offset, part->byte_size);
      if (is_common_) {
        success = context->SetDoubleCommonAttr(part->attr_name, (double)value);
      } else {
        success = context->SetDoubleItemAttr(*result, part->item_attr_accessor, (double)value);
      }
    }
    return success;
  }
  template <typename T>
  inline bool UnpackDoubleListBytes(MutableRecoContextInterface *context,
                                    absl::optional<std::vector<absl::string_view>> bytes_list,  // const char
                                                                                                // *bytes,
                                    const SchemaPartValue *part, size_t array_length,
                                    const CommonRecoResult *result = nullptr) const {
    bool success = false;
    if (is_array_) {
      std::vector<double> values;
      for (int i = 0; i < array_length; i++) {
        auto bytes = (bytes_list->at(i)).data();
        size_t array_length_i = (bytes_list->at(i)).size() / single_tuple_size_;
        for (int j = 0; j < array_length_i; j++) {
          T value;
          memcpy(&value, bytes + part->offset + single_tuple_size_ * j, part->byte_size);
          values.push_back(static_cast<double>(value));
        }
      }
      if (is_common_) {
        success = context->SetDoubleListCommonAttr(part->attr_name, std::move(values));
      } else {
        success = context->SetDoubleListItemAttr(*result, part->item_attr_accessor, std::move(values));
      }
    }
    return success;
  }
  inline bool UnpackScaleInt8Bytes(MutableRecoContextInterface *context, const char *bytes,
                                   const SchemaPartValue *part, int array_length,
                                   const CommonRecoResult *result = nullptr) const {
    bool success = false;
    std::vector<double> values;
    values.resize(part->dim);
    size_t dim = part->dim;
    if (array_length != 1) {
      CL_LOG_ERROR("unpack error", "multi tuple not support scaleint8 list yet");
      return false;
    }
    for (int j = 0; j < array_length; j++) {
      ScaleInt8ToDouble(reinterpret_cast<const int8_t *>(bytes + part->offset + single_tuple_size_ * j),
                        values.data(), dim);
    }
    if (is_common_) {
      success = context->SetDoubleListCommonAttr(part->attr_name, std::move(values));
    } else {
      success = context->SetDoubleListItemAttr(*result, part->item_attr_accessor, std::move(values));
    }
    return success;
  }

  inline bool UnpackScaleInt8ListBytes(
      MutableRecoContextInterface *context,
      absl::optional<std::vector<absl::string_view>> bytes_list,  // const char *bytes,
      const SchemaPartValue *part, size_t array_length, const CommonRecoResult *result = nullptr) const {
    bool success = false;
    std::vector<double> values;
    values.resize(array_length * part->dim);
    size_t dim = part->dim;
    for (int i = 0; i < array_length; i++) {
      auto bytes = (bytes_list->at(i)).data();
      size_t array_length_i = (bytes_list->at(i)).size() / single_tuple_size_;
      if (array_length_i != 1) {
        CL_LOG_ERROR("unpack error", "multi tuple not support scaleint8 list yet");
        return false;
      }
      for (int j = 0; j < array_length_i; j++) {
        ScaleInt8ToDouble(reinterpret_cast<const int8_t *>(bytes + part->offset + single_tuple_size_ * j),
                          values.data() + i * dim, dim);
      }
    }
    if (is_common_) {
      success = context->SetDoubleListCommonAttr(part->attr_name, std::move(values));
    } else {
      success = context->SetDoubleListItemAttr(*result, part->item_attr_accessor, std::move(values));
    }
    return success;
  }
  inline bool UnpackScaleInt8ListPtrBytes(MutableRecoContextInterface *context,
                                          const std::vector<std::string> *bytes_list,  // const char *bytes,
                                          const SchemaPartValue *part, size_t array_length,
                                          const CommonRecoResult *result = nullptr) const {
    bool success = false;
    std::vector<double> values;
    values.resize(array_length * part->dim);
    size_t dim = part->dim;
    for (int i = 0; i < array_length; i++) {
      auto bytes = (bytes_list->at(i)).data();
      size_t array_length_i = (bytes_list->at(i)).size() / single_tuple_size_;
      if (array_length_i != 1) {
        CL_LOG_ERROR("unpack error", "multi tuple not support scaleint8 list yet");
        return false;
      }
      for (int j = 0; j < array_length_i; j++) {
        ScaleInt8ToDouble(reinterpret_cast<const int8_t *>(bytes + part->offset + single_tuple_size_ * j),
                          values.data() + i * dim, dim);
      }
    }
    if (is_common_) {
      success = context->SetDoubleListCommonAttr(part->attr_name, std::move(values));
    } else {
      success = context->SetDoubleListItemAttr(*result, part->item_attr_accessor, std::move(values));
    }
    return success;
  }

 private:
  bool is_common_ = false;
  bool is_array_ = false;
  std::vector<SchemaPartValue> schema_;
  size_t single_tuple_size_ = 0;
  std::string source_attr_name_;
  bool is_str_list_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoUnpackBytesEnricher);
};

}  // namespace platform
}  // namespace ks
