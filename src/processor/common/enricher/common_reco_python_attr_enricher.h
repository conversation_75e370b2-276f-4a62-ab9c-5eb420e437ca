#pragma once

#include <string>
#include <vector>
#include <mutex>

#include "serving_base/utility/timer.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoPythonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPythonAttrEnricher();

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *common_attr_config = config()->Get("import_common_attr");
    if (common_attr_config) {
      if (!common_attr_config->IsArray()) {
        LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! 'import_common_attr' should be an array!";
        return false;
      }
      for (const auto *attr : common_attr_config->array()) {
        import_common_attrs_.push_back(attr->StringValue());
      }
    }

    auto *item_attr_config = config()->Get("import_item_attr");
    if (item_attr_config) {
      if (!item_attr_config->IsArray()) {
        LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! 'import_item_attr' should be an array!";
        return false;
      }
      for (const auto *attr : item_attr_config->array()) {
        import_item_attrs_.push_back(attr->StringValue());
      }
    }

    auto *export_common_config = config()->Get("export_common_attr");
    if (export_common_config) {
      if (!export_common_config->IsArray()) {
        LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! Missing config 'export_common_attr' or not "
                      "an array!";
        return false;
      }

      for (const auto *attr : export_common_config->array()) {
        export_common_attrs_.push_back(attr->StringValue());
      }
    }

    auto *export_item_config = config()->Get("export_item_attr");
    if (export_item_config) {
      if (!export_item_config->IsArray()) {
        LOG(ERROR)
            << "CommonRecoPythonAttrEnricher init failed! Missing config 'export_item_attr' or not an array!";
        return false;
      }

      for (const auto *attr : export_item_config->array()) {
        export_item_attrs_.push_back(attr->StringValue());
      }
    }

    if (!export_common_attrs_.empty() && !export_item_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! export_common_attrs and export_item_attrs "
                    "should not be set at the same time";
      return false;
    }

    if (!export_common_attrs_.empty() && !import_item_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! export_common_attrs and import_item_attrs "
                    "should not be set at the same time";
      return false;
    }

    if (export_common_attrs_.empty() && export_item_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! export_common_attrs or export_item_attrs "
                    "must be set";
      return false;
    }

    python_script_ = config()->GetString("python_script");
    if (python_script_.empty()) {
      LOG(ERROR) << "CommonRecoPythonAttrEnricher init failed! 'python_script' cannot be empty!";
      return false;
    }

    return true;
  }

 private:
  std::string python_script_;
  std::vector<std::string> import_common_attrs_;
  std::vector<std::string> import_item_attrs_;
  std::vector<std::string> export_common_attrs_;
  std::vector<std::string> export_item_attrs_;

  static std::once_flag python_init_flag_;

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPythonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
