#include "dragon/src/processor/common/enricher/common_reco_distributed_flat_index_enricher.h"

#include "kconf/kconf.h"
#include "kess/rpc/rpc_facade.h"
#include "ks/serving_util/kess_grpc_client.h"

namespace ks {
namespace platform {

DEFINE_double(attr_perf_sample_rate, 1.0, "sampling rate for attr info perf");

void CommonRecoDistributedFlatIndexEnricher::PerfAttrInfo(MutableRecoContextInterface *context,
                                                          const std::vector<CommonRecoResult> &target_items,
                                                          int item_hit) {
  if (use_flat_kv_) {
    return;
  }

  for (auto &counter : attr_counter_data_) {
    counter.attr_cnt = 0;
    counter.attr_total_size = 0;
  }

  for (auto &result : target_items) {
    for (auto &counter : attr_counter_data_) {
      if (result.HasAttr(counter.accessor)) {
        ++counter.attr_cnt;
        switch (counter.accessor->value_type) {
          case AttrType::INT:
          case AttrType::FLOAT:
          case AttrType::STRING: {
            counter.attr_total_size += 1;
            break;
          }
          case AttrType::INT_LIST: {
            auto int_list_value = result.GetIntListAttr(counter.accessor);
            if (int_list_value) {
              counter.attr_total_size += int_list_value->size();
            }
            break;
          }
          case AttrType::FLOAT_LIST: {
            auto double_list_value = result.GetDoubleListAttr(counter.accessor);
            if (double_list_value) {
              counter.attr_total_size += double_list_value->size();
            }
            break;
          }
          case AttrType::STRING_LIST: {
            auto string_list_value = result.GetStringListAttr(counter.accessor);
            if (string_list_value) {
              counter.attr_total_size += string_list_value->size();
            }
            break;
          }
          default:
            break;
        }
      }
    }
  }

  for (const auto &counter : attr_counter_data_) {
    const auto &attr_name = counter.name;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        kPerfBase * counter.attr_cnt / std::max(item_hit, 1), kPerfNs, "forward_index.attr_hit",
        GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        kPerfBase * counter.attr_total_size / std::max(counter.attr_cnt, 1), kPerfNs,
        "forward_index.attr_size", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
        attr_name);
  }
}

ks::photo_store::DynamicPhotoStore *CommonRecoDistributedFlatIndexEnricher::GetDynamicPhotoStore() {
  bool init_success = FlatIndexAttrMetaDataHolder::Singleton()->RegistAllAttrMetaData();
  if (!init_success) {
    return nullptr;
  }

  attr_meta_data_ = FlatIndexAttrMetaDataHolder::Singleton()->GetAttrMetaData();
  if (!attr_meta_data_) {
    LOG(ERROR)
        << "CommonRecoDistributedIndexItemAttrEnricher init failed! attr_meta_data is empty, with kconf key"
        << config()->GetString("photo_store_kconf_key", "");
    return nullptr;
  }

  // 初始化 photo store 需要传入构造好的 factory 类
  auto factory_ptr = std::make_shared<reco::protoutil::DynamicFlattenedAttrKVPhotoStoreItemFactory>(
      attr_meta_data_);
  if (use_flat_kv_) {
    factory_ptr->EnableFlatKvReader();
  }

  if (FLAGS_photo_store_fetch_required_attr_only) {
    std::set<std::string> attrs;
    for (const auto &pair : *attr_meta_data_) {
      attrs.insert(pair.first);
    }
    return DynamicPhotoStoreManager<
               ks::reco::protoutil::DynamicFlattenedAttrKVPhotoStoreItemFactory>::Singleton()
        ->GetDynamicPhotoStore(config()->GetString("photo_store_kconf_key", ""), factory_ptr, &attrs,
                               packed_key_);
  } else {
    return DynamicPhotoStoreManager<
               ks::reco::protoutil::DynamicFlattenedAttrKVPhotoStoreItemFactory>::Singleton()
        ->GetDynamicPhotoStore(config()->GetString("photo_store_kconf_key", ""), factory_ptr, nullptr,
                               packed_key_);
  }
}

// enrich 初始阶段，为 item attr 设置 readonly & flat_index 相关信息
void CommonRecoDistributedFlatIndexEnricher::SetAttrMetaInfo(MutableRecoContextInterface *context) {
  bool hit_ab_alternative = false;
  if (field_replacer_ && field_replacer_->IsHitAb(context->GetUserId(), context->GetDeviceId(), 0)) {
    hit_ab_alternative = true;
  }

  auto set_attr_info = [&](const std::string &attr_name, const AttrAlias &attr_alias) {
    // 查询 attr_meta_data_ 中的原名
    auto it = hit_ab_alternative ? attr_meta_data_->find(attr_alias.ab_alternative)
                                 : attr_meta_data_->find(attr_name);
    if (it == attr_meta_data_->end()) {
      CL_LOG_WARNING_EVERY("[WARNING] unrecognized attr ", attr_name, 100)
          << "[WARNING] unrecognized attr '" << attr_name;
    } else {
      const auto &meta_data = it->second;
      // 设置别名的 attr
      auto *accessor = context->GetItemAttrAccessor(attr_alias.alias);
      accessor->is_from_flat_index = true;
      accessor->MarkReadOnly();
      accessor->value_type =
          RecoUtil::CastAttrTypeFromCommonIndex(static_cast<CommonIndexEnum::AttrType>(meta_data.type));
      accessor->flat_index_order = meta_data.order;
      accessor->flat_index_offset = meta_data.offset;
    }
  };

  if (!packed_key_.empty()) {
    if (use_flat_kv_) {
      auto flat_kv_util = dynamic_photo_store_fetcher_.GetPhotoStore()->GetFlatKvUtil();
      if (!flat_kv_util) {
        CL_LOG_WARNING("flat_index_processor", "empty_flat_kv");
        return;
      }
      auto schema_pool = flat_kv_util->GetPool();
      if (!schema_pool) {
        CL_LOG_WARNING("flat_index_processor", "empty_schema_pool");
        return;
      }
      auto schema = flat_kv_util->GetSchema(
          schema_pool, dynamic_photo_store_fetcher_.GetPhotoStore()->GetConfig().flat_kv_schema_version);
      if (!schema) {
        CL_LOG_WARNING("flat_index_processor", "empty_schema");
        return;
      }
      for (int i = 0; i < schema->GetFieldCount(); i++) {
        auto field = schema->GetField(i);
        if (field) {
          auto *accessor = context->GetItemAttrAccessor(field->name);
          accessor->is_from_flat_index = true;
          accessor->MarkReadOnly();
          accessor->value_type = RecoUtil::CastAttrTypeFromFlatKv(field->type);
          accessor->flat_index_order = field->index;
          accessor->flat_index_offset = 0;
        }
      }

    } else {
      set_attr_info(packed_key_, {packed_key_, packed_key_});
    }
  } else {
    for (const auto &pr : attr_names_) {
      set_attr_info(pr.first, pr.second);
    }
  }
}

void CommonRecoDistributedFlatIndexEnricher::SavePhotoStoreItem(const CommonRecoResult &result,
                                                                ks::photo_store::Item *item_ptr) {
  auto *item = dynamic_cast<ks::reco::protoutil::FlattenedAttrKVPhotoStoreItem *>(item_ptr);
  if (!item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  auto item_data = item->GetRawItemData();
  if (!item_data) {
    CL_LOG_EVERY_N(WARNING, 1000) << "FlattenedAttrKVPhotoStoreItem has no value, item_key: "
                                  << result.item_key << ", id: " << result.GetId()
                                  << ", type: " << result.GetType();
    return;
  }

  const_cast<CommonRecoResult &>(result).SetFlatIndexItemAddr(item_data);
}

void CommonRecoDistributedFlatIndexEnricher::BackFillContext(
    MutableRecoContextInterface *context, const std::vector<CommonRecoResult> &target_items) {
  // flat index 需要在 enrich 最后阶段统一设置 result.flat_index_addr，设置
  // AttrTable.item_flat_index_addr_map_, 统计 attr 命中率
  CommonRecoContext *reco_context = static_cast<CommonRecoContext *>(context);
  auto *table = reco_context->GetOrInsertDataTable(GetTableName());
  for (auto &result : target_items) {
    table->SetItemFlatIndexAddr(result.item_key, result.GetFlatIndexItemAddr());
  }
  std::vector<CommonRecoResult> *reco_result = reco_context->GetRecoResults();
  for (auto &result : *reco_result) {
    result.SetFlatIndexItemAddr(table->GetItemFlatIndexAddr(result.item_key));
  }
}

uint64 CommonRecoDistributedFlatIndexEnricher::ItemIdToIndexItemKey(uint64 item_key, uint64 item_id) {
  return item_key;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedFlatIndexEnricher,
                 CommonRecoDistributedFlatIndexEnricher)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexFlatKvItemAttrEnricher,
                 CommonRecoDistributedFlatIndexEnricher)

}  // namespace platform
}  // namespace ks
