#include "dragon/src/processor/common/enricher/common_reco_remote_index_item_attr_enricher.h"

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include "folly/container/F14Set.h"
#include "serving_base/util/shm_list.h"

namespace ks {
namespace platform {

// workaround gcc5 issue
thread_local std::unique_ptr<int> fake_tls2;

void CommonRecoRemoteIndexItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  thread_local folly::F14FastSet<uint64> item_dedup_set;
  thread_local std::vector<CommonRecoResult> target_items;
  item_dedup_set.clear();
  target_items.clear();

  if (attr_accessors_.empty()) {
    for (const auto &attr_name : attr_name_list_) {
      attr_accessors_.push_back(context->GetItemAttrAccessor(attr_name));
    }
  }

  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  int final_shard_num = shard_num_;
  auto overlay_config = CommonQueryServiceGateway::GetInstance()->GetConfigForKess(kess_service);
  if (overlay_config.has_value()) {
    CL_LOG_EVERY_N(INFO, 10000) << "(Every 10000) found overlay for original kess:" << kess_service
                                << ", overlay:" << overlay_config->DebugString();
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "common_query_overlay_config",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), GetName(), kess_service);
    if (overlay_config->redirect_kess.has_value()) {
      kess_service = overlay_config->redirect_kess.value();
    }
    if (overlay_config->shard_num.has_value()) {
      final_shard_num = overlay_config->shard_num.value();
    }
  }

  if (kess_service.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.remote_index",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "empty_kess_service");
    CL_LOG_EVERY_N(WARNING, 100) << "remote index request cancelled: empty kess_service!";
    return;
  }

  if (include_reco_results_) {
    std::for_each(begin, end, [this](const CommonRecoResult &result) {
      if (item_type_ == -1 || Util::GetType(result.item_key) == item_type_) {
        if (item_dedup_set.find(result.item_key) == item_dedup_set.end()) {
          target_items.emplace_back(result);
        }
      }
    });
  }

  if (include_browse_set_items_) {
    auto browsed_items = context->GetLatestBrowsedItems(include_browse_set_item_count_);
    for (const auto &item_key : browsed_items) {
      if (item_dedup_set.find(item_key) == item_dedup_set.end()) {
        target_items.emplace_back(context->NewCommonRecoResult(item_key));
      }
    }
  }

  for (const auto &attr_name : source_common_attrs_) {
    switch (context->GetCommonAttrType(attr_name)) {
      case AttrType::INT_LIST: {
        auto p_list = context->GetIntListCommonAttr(attr_name);
        if (p_list) {
          for (auto item_key : *p_list) {
            if (item_dedup_set.find(item_key) == item_dedup_set.end()) {
              target_items.emplace_back(context->NewCommonRecoResult(item_key));
            }
          }
        }
        break;
      }
      case AttrType::INT: {
        auto p_int = context->GetIntCommonAttr(attr_name);
        if (p_int) {
          if (item_dedup_set.find(*p_int) == item_dedup_set.end()) {
            target_items.emplace_back(context->NewCommonRecoResult(*p_int));
          }
        }
        break;
      }
      default:
        VLOG(100) << "cannot find int/int_list common_attr: " << attr_name;
        break;
    }
  }

  for (const auto &attr_name : source_item_attrs_) {
    auto *attr_accessor = context->GetItemAttrAccessor(attr_name);
    switch (context->GetItemAttrType(attr_name)) {
      case AttrType::INT_LIST:
        std::for_each(begin, end, [context, attr_accessor](const CommonRecoResult &result) {
          auto p_list = result.GetIntListAttr(attr_accessor);
          if (p_list) {
            for (auto item_key : *p_list) {
              if (item_dedup_set.find(item_key) == item_dedup_set.end()) {
                target_items.emplace_back(context->NewCommonRecoResult(item_key));
              }
            }
          }
        });
        break;
      case AttrType::INT:
        std::for_each(begin, end, [context, attr_accessor](const CommonRecoResult &result) {
          auto p_int = result.GetIntAttr(attr_accessor);
          if (p_int) {
            if (item_dedup_set.find(*p_int) == item_dedup_set.end()) {
              target_items.emplace_back(context->NewCommonRecoResult(*p_int));
            }
          }
        });
        break;
      default:
        VLOG(100) << "no int/int_list item_attr: " << attr_name;
        break;
    }
  }

  std::string request_info = "kess_service: " + kess_service +
                             ", timeout_ms: " + base::IntToString(timeout_ms_) +
                             ", shard_num: " + base::IntToString(final_shard_num);

  if (target_items.empty()) {
    CL_LOG(INFO) << "common index attr remote query request cancelled: empty item list! " << request_info
                 << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  if (attr_name_list_.empty()) {
    CL_LOG(WARNING) << "common index attr remote query request cancelled: empty attr list! " << request_info
                    << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  thread_local std::vector<std::vector<CommonRecoResult>> items_in_shard;
  items_in_shard.resize(final_shard_num);
  for (auto &items : items_in_shard) {
    items.clear();
  }

  for (const auto &result : target_items) {
    if (!no_overwrite_ || !IsAllAttrsExisting(result)) {
      // XXX(fangjianbing): Java 里没有 uint64, cityhash 返回可能为负值, 直接取模会跟 c++ 侧不一致,
      // 所以这里只能把符号位抹掉与 Java 侧保持一致逻辑...
      uint64 key = result.item_key & 0x7fffffffffffffffL;
      items_in_shard[key % items_in_shard.size()].emplace_back(result);
    }
  }

  uint64 user_hash = RecoUtil::GenUserHash(context->GetUserId(), context->GetDeviceId());
  int send_item_count = 0;
  for (int i = 0; i < items_in_shard.size(); ++i) {
    std::string shard = shard_prefix_ + base::IntToString(i);
    int count = items_in_shard[i].size();
    if (SendRequestToShard(context, std::move(items_in_shard[i]), kess_service, shard, user_hash,
                           request_info)) {
      send_item_count += count;
    }
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(send_item_count, kPerfNs, "forward_index.item_total",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
}

bool CommonRecoRemoteIndexItemAttrEnricher::SendRequestToShard(MutableRecoContextInterface *context,
                                                               std::vector<CommonRecoResult> &&target_items,
                                                               const std::string &kess_service,
                                                               const std::string &shard, uint64 user_hash,
                                                               const std::string &info) {
  if (target_items.empty()) return false;

  int64 start_ts = base::GetTimestamp();

  ItemAttr *item_key_attr_accessor = nullptr;
  if (!item_key_attr_.empty()) {
    item_key_attr_accessor = context->GetItemAttrAccessor(item_key_attr_);
  }

  // 开始构造 query 请求
  request_builder_.ClearQueryKeysign();
  for (const auto &result : target_items) {
    if (item_key_attr_accessor) {
      auto item_key_p = result.GetIntAttr(item_key_attr_accessor);
      // item attr 没有取到 id 时，默认 key = -1
      request_builder_.AddQueryKeysign(item_key_p ? *item_key_p : -1);
    } else {
      request_builder_.AddQueryKeysign(result.item_key);
    }
  }

  thread_local CommonQueryFlattenRequest common_request;
  common_request.Clear();
  // NOTE(fangjianbing): Flush 同时不对 request_builder_ 进行 Clear
  request_builder_.FlushToRequest(&common_request, false);

  std::string request_info = info + ", shard: " + shard;
  CL_LOG(INFO) << "sending common index attr remote query request, item_num: "
               << std::to_string(target_items.size()) << ", " << request_info
               << RecoUtil::GetRequestInfoForLog(context);

  auto *resp = response_pool_.Acquire();
  // 远端索引请求, 使用一致性 hash 接口
  auto pr = consistent_hash_
                ? common_query_client_.AsyncCommonQueryFlatten(user_hash, kess_service, service_group_, shard,
                                                               timeout_ms_, common_request, resp)
                : common_query_client_.AsyncCommonQueryFlatten(kess_service, service_group_, shard,
                                                               timeout_ms_, common_request, resp);
  if (!pr.first) {
    response_pool_.Recycle(resp);
    CL_LOG_ERROR("remote_index", "send_request_fail")
        << "failed to send common index attr remote query request to remote index, item_num: "
        << std::to_string(target_items.size()) << ", " << request_info
        << RecoUtil::GetRequestInfoForLog(context);
    return false;
  }

  auto callback = [this, context, start_ts, request_info,
                   target_items = std::move(target_items)](CommonQueryFlattenResponse *response) {
    if (!response) {
      CL_LOG_ERROR("remote_index", "null_response") << "Null CommonQueryResponse, processor: " << GetName();
      return;
    }
    // parse response...
    CommonQueryFlattenResponseReader response_reader(*response);
    if (!response_reader.Valid()) {
      CL_LOG_ERROR("remote_index", "invalid_response")
          << "Invalid CommonQueryResponse, item_num: " << target_items.size()
          << ", attr_num: " << attr_name_list_.size() << ", response_reader size: " << response_reader.size()
          << "; int_attr_value_size: response=" << response->int_attr_value().size()
          << ", reader=" << response_reader.int_attr_value_size()
          << "; float_attr_value_size: response=" << response->float_attr_value().size()
          << ", reader=" << response_reader.float_attr_value_size()
          << "; string_attr_value_size: response=" << response->string_attr_value().size()
          << ", reader=" << response_reader.string_attr_value_size() << ", " << request_info
          << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    if (attr_type_list_.size() != attr_name_list_.size() &&
        response_reader.attr_type_size() != attr_name_list_.size()) {
      CL_LOG_ERROR("remote_index", "incomplete_attr_type")
          << "Invalid CommonQueryResponse, size not match, attr_num: " << attr_name_list_.size()
          << ", vs attr_type_size(response): " << response_reader.attr_type_size()
          << ", vs attr_type_size(json_config): " << attr_type_list_.size() << ", " << request_info;
      return;
    }

    int float_num = 0;
    int int_num = 0;
    int str_num = 0;
    base::ConstArray<int64> int_list;
    base::ConstArray<float> float_list;

    for (int i = 0; i < attr_name_list_.size(); ++i) {
      const auto &attr_name = attr_name_list_[i];
      const auto &attr_accessor = attr_accessors_[i];
      auto attr_type = CommonIndexEnum::UNKNOW_ATTR;
      if (attr_type_list_.size() > i) {
        attr_type = RecoUtil::CastCommonIndexAttrType(attr_type_list_[i]);
      } else if (response_reader.attr_type_size() > i) {
        attr_type = static_cast<CommonIndexEnum::AttrType>(response_reader.attr_type(i));
      }

      int attr_count = 0;
      int attr_total_size = 0;

      switch (attr_type) {
        case CommonIndexEnum::INT_ATTR:
          for (const auto &result : target_items) {
            if (int_num >= response_reader.int_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_int_attr_value_size")
                  << "Invalid int attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", int_attr_value_size: " << response_reader.int_attr_value_size();
              break;
            }
            int64 val = response_reader.int_attr_value(int_num++);
            result.SetIntAttr(attr_accessor, val, no_overwrite_);
            if (val != -1) {
              // common index 中 -1 是缺省值
              ++attr_count;
              ++attr_total_size;
            }
          }
          break;
        case CommonIndexEnum::FLOAT_ATTR:
          for (const auto &result : target_items) {
            if (float_num >= response_reader.float_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_float_attr_value_size")
                  << "Invalid float attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", float_attr_value_size: " << response_reader.float_attr_value_size();
              break;
            }
            float val = response_reader.float_attr_value(float_num++);
            result.SetDoubleAttr(attr_accessor, val, no_overwrite_);
            if (val != -1) {
              // common index 中 -1 是缺省值
              ++attr_count;
              ++attr_total_size;
            }
          }
          break;
        case CommonIndexEnum::STRING_ATTR:
          for (const auto &result : target_items) {
            if (str_num >= response_reader.string_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_string_attr_value_size")
                  << "Invalid string attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", string_attr_value_size: " << response_reader.string_attr_value_size();
              break;
            }
            base::Slice slice = response_reader.string_attr_slice(str_num++);
            if (max_value_bytes_ > 0 && slice.size() > max_value_bytes_) {
              CL_LOG_WARNING_EVERY("remote_index", "value_too_large:" + attr_name, 1000)
                  << "skipped string attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to value bytes too large: " << slice.size() << " > " << max_value_bytes_;
              continue;
            }
            if (!slice.empty()) {
              ++attr_count;
              ++attr_total_size;
            }
            if (slice.data() == nullptr) {
              CL_LOG_WARNING_EVERY("remote_index", "invalid_value:" + attr_name, 1000)
                  << "skipped int_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to invalid value bytes: data=" << slice.data() << " size=" << slice.size();
              continue;
            }
            result.SetStringAttr(attr_accessor, slice.as_string(), no_overwrite_);
          }
          break;
        case CommonIndexEnum::INT_LIST_ATTR:
          for (const auto &result : target_items) {
            if (str_num >= response_reader.string_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_string_attr_value_size")
                  << "Invalid string attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", string_attr_value_size: " << response_reader.string_attr_value_size();
              break;
            }
            base::Slice slice = response_reader.string_attr_slice(str_num++);
            if (max_value_bytes_ > 0 && slice.size() > max_value_bytes_) {
              CL_LOG_WARNING_EVERY("remote_index", "value_too_large:" + attr_name, 1000)
                  << "skipped int_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to value bytes too large: " << slice.size() << " > " << max_value_bytes_;
              continue;
            }
            int_list.SetData(slice.data(), slice.size());
            if (int_list.list == nullptr) {
              CL_LOG_WARNING_EVERY("remote_index", "invalid_value:" + attr_name, 1000)
                  << "skipped int_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to invalid value bytes: data=" << slice.data() << " size=" << slice.size();
              continue;
            }
            std::vector<int64> val(int_list.begin(), int_list.end());
            result.SetIntListAttr(attr_accessor, std::move(val), no_overwrite_);
            ++attr_count;
            attr_total_size += int_list.size;
          }
          break;
        case CommonIndexEnum::FLOAT_LIST_ATTR:
          for (const auto &result : target_items) {
            if (str_num >= response_reader.string_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_string_attr_value_size")
                  << "Invalid string attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", string_attr_value_size: " << response_reader.string_attr_value_size();
              break;
            }
            base::Slice slice = response_reader.string_attr_slice(str_num++);
            if (max_value_bytes_ > 0 && slice.size() > max_value_bytes_) {
              CL_LOG_WARNING_EVERY("remote_index", "value_too_large:" + attr_name, 1000)
                  << "skipped float_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to value bytes too large: " << slice.size() << " > " << max_value_bytes_;
              continue;
            }
            float_list.SetData(slice.data(), slice.size());
            if (float_list.list == nullptr) {
              CL_LOG_WARNING_EVERY("remote_index", "invalid_value:" + attr_name, 1000)
                  << "skipped float_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to invalid value bytes: data=" << slice.data() << " size=" << slice.size();
              continue;
            }
            std::vector<double> val(float_list.begin(), float_list.end());
            result.SetDoubleListAttr(attr_accessor, std::move(val), no_overwrite_);
            ++attr_count;
            attr_total_size += float_list.size;
          }
          break;
        case CommonIndexEnum::STRING_LIST_ATTR:
          for (const auto &result : target_items) {
            if (str_num >= response_reader.string_attr_value_size()) {
              CL_LOG_ERROR("remote_index", "invalid_string_attr_value_size")
                  << "Invalid string attr value size, attr: " << attr_name
                  << ", item_num: " << target_items.size()
                  << ", string_attr_value_size: " << response_reader.string_attr_value_size();
              break;
            }
            base::Slice attr_slice = response_reader.string_attr_slice(str_num++);
            if (max_value_bytes_ > 0 && attr_slice.size() > max_value_bytes_) {
              CL_LOG_WARNING_EVERY("remote_index", "value_too_large:" + attr_name, 1000)
                  << "skipped string_list attr " << attr_name << " for item_id=" << result.GetId()
                  << " due to value bytes too large: " << attr_slice.size() << " > " << max_value_bytes_;
              continue;
            }
            std::vector<std::string> val;
            if (attr_slice.size() > 0) {
              const auto *shm_list =
                  base::ShmList<std::string, int>::Read(attr_slice.data(), attr_slice.size());
              if (shm_list == nullptr) {
                CL_LOG_WARNING_EVERY("remote_index", "extract_string_list_fail:" + attr_name, 1000)
                    << "skipped string_list attr " << attr_name << " for item_id=" << result.GetId()
                    << " due to value bytes init ShmList failed, value_size=" << attr_slice.size();
                continue;
              }
              val.reserve(shm_list->value_size());
              for (int i = 0; i < shm_list->value_size(); ++i) {
                base::Slice slice = shm_list->slice(i);
                if (max_value_bytes_ > 0 && slice.size() > max_value_bytes_) {
                  CL_LOG_WARNING_EVERY("remote_index", "value_too_large:" + attr_name, 1000)
                      << "skipped one string in string_list attr " << attr_name
                      << " for item_id=" << result.GetId()
                      << " due to value bytes too large: " << slice.size() << " > " << max_value_bytes_;
                  val.emplace_back("");
                } else {
                  val.emplace_back(slice.data(), slice.size());
                }
              }
              ++attr_count;
              attr_total_size += shm_list->value_size();
            }
            result.SetStringListAttr(attr_accessor, std::move(val), no_overwrite_);
          }
          break;
        default:
          base::perfutil::PerfUtilWrapper::CountLogStash(
              kPerfNs, "error.remote_index", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
              "invalid_attr_type:" + attr_name + "=" + CommonIndexEnum::AttrType_Name(attr_type));
          CL_LOG_EVERY_N(WARNING, 1000)
              << "Unsupported item attr_type: " << CommonIndexEnum::AttrType_Name(attr_type)
              << ", attr_name: " << attr_name << RecoUtil::GetRequestInfoForLog(context);
          break;
      }

      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          1000.0 * attr_count / target_items.size(), kPerfNs, "forward_index.attr_hit",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          1000.0 * attr_total_size / std::max(attr_count, 1), kPerfNs, "forward_index.attr_size",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
    }

    int64 duration = base::GetTimestamp() - start_ts;
    CL_LOG(INFO) << "CommonQueryResponse received, time cost: " << (duration / 1000.0)
                 << " ms, item_num: " << target_items.size() << ", attr_num: " << attr_name_list_.size()
                 << ", float_num: " << float_num << ", int_num: " << int_num << ", str_num: " << str_num
                 << ", " << request_info;
  };

  auto finally = [this, resp]() { response_pool_.Recycle(resp); };

  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), std::move(finally), request_info);
  return true;
}

bool CommonRecoRemoteIndexItemAttrEnricher::IsAllAttrsExisting(const CommonRecoResult &result) const {
  for (const auto &attr_accessor : attr_accessors_) {
    if (!result.HasAttr(attr_accessor)) {
      return false;
    }
  }
  return true;
}

bool CommonRecoRemoteIndexItemAttrEnricher::FillQueryAttrs() {
  if (attr_type_list_.empty()) {
    for (const auto &attr_name : attr_name_list_) {
      request_builder_.AddQueryAttr(attr_name);
    }
    return true;
  } else {
    for (int i = 0; i < attr_name_list_.size(); ++i) {
      const auto &attr_name = attr_name_list_[i];
      auto type = i < attr_type_list_.size() ? attr_type_list_[i] : AttrType::UNKNOWN;
      if (type == AttrType::FLOAT) {
        request_builder_.AddQueryFloatAttr(attr_name);
      } else if (type == AttrType::INT) {
        request_builder_.AddQueryIntAttr(attr_name);
      } else if (type == AttrType::STRING || type == AttrType::INT_LIST || type == AttrType::FLOAT_LIST) {
        request_builder_.AddQueryStringAttr(attr_name);
      } else {
        LOG(ERROR) << "Unsupported attr_type: " << static_cast<int>(type);
        return false;
      }
    }
    return true;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRemoteIndexItemAttrEnricher,
                 CommonRecoRemoteIndexItemAttrEnricher)

}  // namespace platform
}  // namespace ks
