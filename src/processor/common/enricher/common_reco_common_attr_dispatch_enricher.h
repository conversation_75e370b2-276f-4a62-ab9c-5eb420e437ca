#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoCommonAttrDispatchEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCommonAttrDispatchEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct DispatchConfig {
    std::string from_common_attr;
    std::string to_item_attr;
    int by_list_size = 0;
  };

 private:
  bool InitProcessor() override {
    std::string from_common_attr = config()->GetString("from_common_attr");
    std::string to_item_attr = config()->GetString("to_item_attr");
    int by_list_size = config()->GetInt("by_list_size", 0);
    if (!from_common_attr.empty() && !to_item_attr.empty()) {
      dispatch_config_.emplace_back(DispatchConfig{
          .from_common_attr = from_common_attr, .to_item_attr = to_item_attr, .by_list_size = by_list_size});
    }

    const auto *dispatch_config = config()->Get("dispatch_config");
    if (dispatch_config && dispatch_config->IsArray()) {
      for (const auto *attr : dispatch_config->array()) {
        if (!attr->IsObject()) {
          LOG(ERROR) << "CommonRecoCommonAttrDispatchEnricher init failed. element in dispatch_config "
                        "should be object";
          return false;
        }
        std::string from = attr->GetString("from_common_attr");
        if (from.empty()) {
          LOG(ERROR) << "CommonRecoCommonAttrDispatchEnricher init failed. 'from_common_attr' in "
                        "dispatch_config should not be empty";
          return false;
        }
        std::string to = attr->GetString("to_item_attr");
        if (to.empty()) {
          LOG(ERROR) << "CommonRecoCommonAttrDispatchEnricher init failed. 'to_item_attr' int "
                        "dispatch_config should not be empty";
          return false;
        }
        int list_size = attr->GetInt("by_list_size", 0);
        dispatch_config_.emplace_back(
            DispatchConfig{.from_common_attr = from, .to_item_attr = to, .by_list_size = list_size});
      }
    }

    if (dispatch_config_.size() == 0) {
      LOG(ERROR) << "CommonRecoCommonAttrDispatchEnricher init failed. dispatch config is empty";
      return false;
    }
    return true;
  }

  void Dispatch(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                const DispatchConfig &dispatch_config);

 private:
  std::vector<DispatchConfig> dispatch_config_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCommonAttrDispatchEnricher);
};

}  // namespace platform
}  // namespace ks
