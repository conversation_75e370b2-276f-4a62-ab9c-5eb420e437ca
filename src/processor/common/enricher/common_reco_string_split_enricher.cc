#include "dragon/src/processor/common/enricher/common_reco_string_split_enricher.h"

#include <utility>
#include "base/strings/utf_char_iterator.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "third_party/abseil/absl/strings/str_split.h"

namespace ks {
namespace platform {

void CommonRecoStringSplitEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessCommonAttr(context);
  } else {
    ProcessItemAttr(context, begin, end);
  }
}

void CommonRecoStringSplitEnricher::ProcessCommonAttr(MutableRecoContextInterface *context) {
  auto input_str = context->GetStringCommonAttr(input_attr_);
  if (!input_str) {
    CL_LOG(INFO) << "split_string cancelled due to missing input: " << input_attr_;
    return;
  }

  const auto &output_strs = delimiters_.empty() ? StringSplitByChar(*input_str) : StringSplit(*input_str);

  if (parse_to_double_) {
    double val;
    std::vector<double> vec;
    vec.reserve(output_strs.size());
    int fail_count = 0;
    for (auto sv : output_strs) {
      if (sv.empty()) {
        continue;
      }
      if (absl::SimpleAtod(sv, &val)) {
        vec.push_back(val);
      } else {
        ++fail_count;
      }
    }
    context->SetDoubleListCommonAttr(output_attr_, std::move(vec));
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_double_fail: " + input_attr_)
        << "failed to parse double for " << fail_count << " string values from common attr: " << input_attr_;
  } else if (parse_to_int_) {
    int64 val;
    std::vector<int64> vec;
    vec.reserve(output_strs.size());
    int fail_count = 0;
    for (auto sv : output_strs) {
      if (sv.empty()) {
        continue;
      }
      if (absl::SimpleAtoi(sv, &val)) {
        vec.push_back(val);
      } else {
        ++fail_count;
      }
    }
    context->SetIntListCommonAttr(output_attr_, std::move(vec));
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_int_fail: " + input_attr_)
        << "failed to parse int for " << fail_count << " string values from common attr: " << input_attr_;
  } else {
    std::vector<std::string> vec;
    vec.reserve(output_strs.size());
    for (auto sv : output_strs) {
      vec.emplace_back(sv.data(), sv.size());
    }
    context->SetStringListCommonAttr(output_attr_, std::move(vec));
  }
}

void CommonRecoStringSplitEnricher::ProcessItemAttr(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  auto *input_attr_accessor = context->GetItemAttrAccessor(input_attr_);
  auto *output_attr_accessor = context->GetItemAttrAccessor(output_attr_);

  if (input_attr_accessor->value_type != AttrType::STRING) {
    CL_LOG(INFO) << "split_string cancelled due to input item_attr's attr_type is not string: " << input_attr_
                 << ", value_type: " << static_cast<int>(input_attr_accessor->value_type);
    return;
  }

  int fail_count = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto input_str = result.GetStringAttr(input_attr_accessor);
    if (!input_str) {
      return;
    }
    const auto &output_strs = delimiters_.empty() ? StringSplitByChar(*input_str) : StringSplit(*input_str);

    if (parse_to_double_) {
      double val;
      std::vector<double> vec;
      vec.reserve(output_strs.size());
      for (auto sv : output_strs) {
        if (sv.empty()) {
          continue;
        }
        if (absl::SimpleAtod(sv, &val)) {
          vec.push_back(val);
        } else {
          ++fail_count;
        }
      }
      result.SetDoubleListAttr(output_attr_accessor, std::move(vec));
    } else if (parse_to_int_) {
      int64 val;
      std::vector<int64> vec;
      vec.reserve(output_strs.size());
      for (auto sv : output_strs) {
        if (sv.empty()) {
          continue;
        }
        if (absl::SimpleAtoi(sv, &val)) {
          vec.push_back(val);
        } else {
          ++fail_count;
        }
      }
      result.SetIntListAttr(output_attr_accessor, std::move(vec));
    } else {
      std::vector<std::string> vec;
      vec.reserve(output_strs.size());
      for (auto sv : output_strs) {
        vec.emplace_back(sv.data(), sv.size());
      }
      result.SetStringListAttr(output_attr_accessor, std::move(vec));
    }
  });
  if (parse_to_double_) {
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_double_fail: " + input_attr_)
        << "failed to parse double for " << fail_count << " string values from item attr: " << input_attr_;
  } else if (parse_to_int_) {
    CL_LOG_WARNING_COUNT(fail_count, "split_string", "parse_int_fail: " + input_attr_)
        << "failed to parse int for " << fail_count << " string values from item attr: " << input_attr_;
  }
}

std::vector<absl::string_view> CommonRecoStringSplitEnricher::StringSplit(absl::string_view input_str) {
  std::vector<absl::string_view> output_strs;
  if (max_splits_ < 0) {
    if (skip_empty_tokens_) {
      if (trim_spaces_) {
        output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::SkipWhitespace());
      } else {
        output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::SkipEmpty());
      }
    } else {
      output_strs = absl::StrSplit(input_str, absl::ByAnyChar(delimiters_), absl::AllowEmpty());
    }
  } else {
    if (skip_empty_tokens_) {
      if (trim_spaces_) {
        output_strs = absl::StrSplit(input_str, absl::MaxSplits(absl::ByAnyChar(delimiters_), max_splits_),
                                     absl::SkipWhitespace());
      } else {
        output_strs = absl::StrSplit(input_str, absl::MaxSplits(absl::ByAnyChar(delimiters_), max_splits_),
                                     absl::SkipEmpty());
      }
    } else {
      output_strs = absl::StrSplit(input_str, absl::MaxSplits(absl::ByAnyChar(delimiters_), max_splits_),
                                   absl::AllowEmpty());
    }
    if (output_strs.size() > max_splits_) {
      output_strs.resize(max_splits_);
    }
  }

  if (strip_whitespaces_) {
    for (auto &sv : output_strs) {
      sv = absl::StripAsciiWhitespace(sv);
    }
  }

  return output_strs;
}

std::vector<absl::string_view> CommonRecoStringSplitEnricher::StringSplitByChar(absl::string_view input_str) {
  std::vector<absl::string_view> output_strs;
  base::UTF8CharIterator utf8_it(input_str.data(), input_str.size());
  int begin = 0, end = 0;
  while (utf8_it.Advance()) {
    end = utf8_it.array_pos();
    output_strs.emplace_back(input_str.data() + begin, end - begin);
    begin = end;
  }
  return output_strs;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStringSplitEnricher, CommonRecoStringSplitEnricher)

}  // namespace platform
}  // namespace ks
