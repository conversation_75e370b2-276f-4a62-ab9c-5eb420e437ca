#include "dragon/src/processor/common/enricher/common_reco_item_attr_missing_rate_enricher.h"

namespace ks {
namespace platform {

void CommonRecoItemAttrMissingRateEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = end - begin;
  if (item_num <= 0) {
    return;
  }
  for (const auto &attr_config : attrs_) {
    if (attr_config.item_attr.empty() || attr_config.save_rate_to.empty()) {
      continue;
    }
    auto *item_attr = context->GetItemAttrAccessor(attr_config.item_attr);
    auto *common_attr = context->GetCommonAttrAccessor(attr_config.save_rate_to);

    int missing_attr_count = std::count_if(
        begin, end, [item_attr](const CommonRecoResult &result) { return !result.HasAttr(item_attr); });
    context->SetDoubleCommonAttr(common_attr, (double)missing_attr_count / item_num);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemAttrMissingRateEnricher,
                 CommonRecoItemAttrMissingRateEnricher)

}  // namespace platform
}  // namespace ks
