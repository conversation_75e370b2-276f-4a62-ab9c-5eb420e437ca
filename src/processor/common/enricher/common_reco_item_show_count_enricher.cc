#include "dragon/src/processor/common/enricher/common_reco_item_show_count_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoItemShowCountEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  int total_show_num = std::min(context->GetRequestNum(), std::distance(begin, end));
  if (total_show_num <= 0) {
    return;
  }
  auto last = std::next(begin, total_show_num);
  int show_num = 0;
  for (auto it = begin; it != last; ++it) {
    const uint64 item_key = it->item_key;
    auto show = context->GetIntItemAttr(item_key, show_item_attr_);
    if (show && *show) {
      ++show_num;
    }
  }
  context->SetIntCommonAttr(common_attr_, show_num);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemShowCountEnricher, CommonRecoItemShowCountEnricher)
}  // namespace platform
}  // namespace ks
