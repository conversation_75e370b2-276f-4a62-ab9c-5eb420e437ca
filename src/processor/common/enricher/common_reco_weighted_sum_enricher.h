#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoWeightedSumEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoWeightedSumEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct WeightedSumChannel {
    std::string name;
    const base::Json *weight_config = nullptr;
    double weight = 0.0;
    ItemAttr *channel_attr = nullptr;
  };

 private:
  bool InitProcessor() override {
    formula_version_ = config()->GetInt("formula_version", 0);
    auto *channels = config()->Get("channels");
    if (!channels || !channels->IsArray()) {
      LOG(ERROR) << "CommonRecoWeightedSumEnricher init failed! Missing 'channels' config"
                 << " or it is not an array.";
      return false;
    }

    for (const auto *cfg : channels->array()) {
      WeightedSumChannel channel;
      channel.name = cfg->GetString("name");
      if (!channel.name.empty()) {
        channel.weight_config = cfg->Get("weight");
        weighted_sum_channels_.push_back(std::move(channel));
      } else {
        LOG(ERROR) << "CommonRecoWeightedSumEnricher init failed! Missing 'name' config"
                   << " or it is empty.";
      }
    }

    output_item_attr_ = config()->GetString("output_item_attr");
    if (output_item_attr_.empty()) {
      LOG(ERROR) << "CommonRecoWeightedSumEnricher init failed! Missing 'output_item_attr' config"
                 << " or it is empty string.";
      return false;
    }
    return true;
  }

  bool GetNumericValue(ItemAttr *attr_accessor,
                       const CommonRecoResult &result,
                       double *value) const;

 private:
  int formula_version_ = 0;
  std::vector<WeightedSumChannel> weighted_sum_channels_;
  std::string output_item_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoWeightedSumEnricher);
};

}  // namespace platform
}  // namespace ks
