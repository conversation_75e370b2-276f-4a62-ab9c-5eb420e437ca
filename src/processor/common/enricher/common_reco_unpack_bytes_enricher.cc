#include "dragon/src/processor/common/enricher/common_reco_unpack_bytes_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoUnpackBytesEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if (is_common_) {
    HandleCommonAttr(context);
  } else {
    for (auto &part : schema_) {
      part.item_attr_accessor = context->GetItemAttrAccessor(part.attr_name);
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      HandleItemAttr(context, result, context->GetItemAttr(source_attr_name_));
    });
  }
}

void CommonRecoUnpackBytesEnricher::HandleCommonAttr(MutableRecoContextInterface *context) const {
  if (!is_str_list_) {
    auto source_bytes = context->GetStringCommonAttr(source_attr_name_);
    if (!source_bytes || source_bytes->empty()) {
      CL_LOG_ERROR("unpack_bytes", "empty_source_attr")
          << "CommonRecoUnpackBytesEnricher failed! 'source_attr_name' is empty attr.";
      return;
    }
    if ((source_bytes->size() % single_tuple_size_) > 0) {
      CL_LOG_ERROR("unpack_bytes", "source_value_miss_match")
          << "CommonRecoUnpackBytesEnricher failed! source_value is not match schema size.";
      return;
    }
    size_t array_length = source_bytes->size() / single_tuple_size_;
    for (auto &part : schema_) {
      bool success = false;
      switch (part.dtype) {
        case SchemaPartValue::DType::INT8:
          success = UnpackIntBytes<int8_t>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::INT16:
          success = UnpackIntBytes<int16_t>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::INT32:
          success = UnpackIntBytes<int32_t>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::INT64:
          success = UnpackIntBytes<int64_t>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::FP16:
          success = UnpackDoubleBytes<float16_t>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::FP32:
          success = UnpackDoubleBytes<float>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::FP64:
          success = UnpackDoubleBytes<double>(context, source_bytes->data(), &part, array_length);
          break;
        case SchemaPartValue::DType::SCALEINT8:
          success = UnpackScaleInt8Bytes(context, source_bytes->data(), &part, array_length);
          break;
      }
      if (!success) {
        CL_LOG_WARNING("unpack_bytes", "failed_to_unpack_common_attr")
            << "Failed to unpack common attr: " << part.attr_name;
      }
    }
  } else {
    auto source_bytes = context->GetStringListCommonAttr(source_attr_name_);
    const std::vector<std::string> *source_ptr = nullptr;
    if (!source_bytes) {
      source_ptr = context->GetPtrCommonAttr<std::vector<std::string>>(source_attr_name_);
      if (!source_ptr) {
        CL_LOG_ERROR("unpack_bytes", "empty_source_attr") << "'source_attr_name' is empty attr";
        return;
      }
      for (auto &part : schema_) {
        bool success = false;
        switch (part.dtype) {
          case SchemaPartValue::DType::SCALEINT8:
            success = UnpackScaleInt8ListPtrBytes(context, source_ptr, &part, source_ptr->size());
            break;
          default:
            CL_LOG_WARNING("unpack_bytes", "failed_to_unpack_common_attr") << "unsupport dtype";
        }
        if (!success) {
          CL_LOG_WARNING("unpack_bytes", "failed_to_unpack_common_attr")
              << "Failed to unpack list common attr: " << part.attr_name;
        }
      }
      return;
    }
    if (!source_bytes || source_bytes->empty()) {
      CL_LOG_ERROR("unpack_bytes", "empty_source_attr")
          << "CommonRecoUnpackBytesListEnricher failed! 'source_attr_name' is empty attr.";
      return;
    }
    for (int i = 0; i < source_bytes->size(); ++i) {
      if ((source_bytes->at(i).size() % single_tuple_size_) > 0) {
        CL_LOG_ERROR("unpack_bytes", "source_value_miss_match")
            << "CommonRecoUnpackBytesListEnricher inner failed! source_value(list) is not match schema size."
            << source_bytes->at(i).size() << ", " << single_tuple_size_;
        return;
      }
    }
    for (auto &part : schema_) {
      bool success = false;
      switch (part.dtype) {
        case SchemaPartValue::DType::INT8:
          success = UnpackIntListBytes<int8_t>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::INT16:
          success = UnpackIntListBytes<int16_t>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::INT32:
          success = UnpackIntListBytes<int32_t>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::INT64:
          success = UnpackIntListBytes<int64_t>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::FP16:
          success = UnpackDoubleListBytes<float16_t>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::FP32:
          success = UnpackDoubleListBytes<float>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::FP64:
          success = UnpackDoubleListBytes<double>(context, source_bytes, &part, source_bytes->size());
          break;
        case SchemaPartValue::DType::SCALEINT8:
          success = UnpackScaleInt8ListBytes(context, source_bytes, &part, source_bytes->size());
          break;
      }
      if (!success) {
        CL_LOG_WARNING("unpack_bytes", "failed_to_unpack_common_attr")
            << "Failed to unpack list common attr: " << part.attr_name;
      }
    }
  }
}

void CommonRecoUnpackBytesEnricher::HandleItemAttr(MutableRecoContextInterface *context,
                                                   const CommonRecoResult &result,
                                                   ItemAttr *source_attr_accessor) const {
  auto source_bytes = context->GetStringItemAttr(result, source_attr_accessor);
  if (!source_bytes || source_bytes->empty()) {
    CL_LOG_ERROR("unpack_bytes", "empty_source_attr")
        << "CommonRecoUnpackBytesEnricher failed! 'source_attr_name' is empty attr.";
    return;
  }
  if ((source_bytes->size() % single_tuple_size_) > 0) {
    CL_LOG_ERROR("unpack_bytes", "source_value_miss_match")
        << "CommonRecoUnpackBytesEnricher failed! source_value size is not match schema size.";
    return;
  }
  size_t array_length = source_bytes->size() / single_tuple_size_;
  for (auto &part : schema_) {
    bool success = false;
    if (!part.item_attr_accessor) {
      continue;
    }

    switch (part.dtype) {
      case SchemaPartValue::DType::INT8:
        success = UnpackIntBytes<int8_t>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::INT16:
        success = UnpackIntBytes<int16_t>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::INT32:
        success = UnpackIntBytes<int32_t>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::INT64:
        success = UnpackIntBytes<int64_t>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::FP16:
        success = UnpackDoubleBytes<float16_t>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::FP32:
        success = UnpackDoubleBytes<float>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::FP64:
        success = UnpackDoubleBytes<double>(context, source_bytes->data(), &part, array_length, &result);
        break;
      case SchemaPartValue::DType::SCALEINT8:
        success = UnpackScaleInt8Bytes(context, source_bytes->data(), &part, array_length, &result);
        break;
    }
    if (!success) {
      CL_LOG_WARNING("unpack_bytes", "failed_to_unpack_item_attr")
          << "Failed to unpack item attr: " << part.attr_name;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoUnpackBytesEnricher, CommonRecoUnpackBytesEnricher)

}  // namespace platform
}  // namespace ks
