#include "dragon/src/processor/common/enricher/common_reco_ensemble_seq_num_enricher.h"

#include "serving_base/util/math.h"

namespace ks {
namespace platform {

void CommonRecoEnsembleSeqNumEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  double epsilon = GetDoubleProcessorParameter(context, config()->Get("epsilon"), 1e-9, true);
  bool continuous_tied_seq = GetBoolProcessorParameter(context, "continuous_tied_seq", false);

  const int item_num = std::distance(begin, end);
  if (item_num <= 0) return;

  for (auto &p : ensemble_items_) {
    SortOnAttr(context, begin, end, p.get());

    const auto &attr = p->attr;
    if (!context->HasItemAttr(attr)) {
      CL_LOG_WARNING("ensemble_seq", "attr_not_found:" + attr)
          << "calc seq num skipped for attr: " << attr << ", does not exist";
      continue;
    }

    auto *attr_accessor = context->GetItemAttrAccessor(attr + output_postfix_);
    auto &items = p->items;
    if (allow_tied_seq_) {
      int shift = 0;
      double current_val = 0.0;
      double last_val = 0.0;
      for (int i = 0; i < item_num; ++i) {
        current_val = items[i].value;
        if (i > 0 && base::IsEqual(current_val, last_val, epsilon)) {
          ++shift;
        } else if (!continuous_tied_seq) {
          shift = 0;
        }
        items[i].result->SetIntAttr(attr_accessor, start_seq_ + i - shift);
        last_val = current_val;
      }
    } else {
      for (int i = 0; i < item_num; ++i) {
        items[i].result->SetIntAttr(attr_accessor, start_seq_ + i);
      }
    }
  }

  CL_LOG(INFO) << "ensemble sort finished for " << ensemble_items_.size() << " attrs and " << item_num
               << " items";
}

void CommonRecoEnsembleSeqNumEnricher::SortOnAttr(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end,
                                                  EnsembleItemList *ensemble_items) {
  const auto &attr = ensemble_items->attr;
  if (!context->HasItemAttr(attr)) return;
  auto *attr_accessor = context->GetItemAttrAccessor(attr);

  auto &items = ensemble_items->items;
  const int item_num = std::distance(begin, end);
  if (item_num > items.size()) {
    items.resize(item_num);
  }

  int i = 0;
  for (auto it = begin; it != end; ++it) {
    items[i].result = &(*it);
    items[i].value = GetDoubleValue(attr_accessor, *it);
    ++i;
  }

  auto comparer = [this](const EnsembleItem &a, const EnsembleItem &b) { return desc_ ? a > b : a < b; };

  if (stable_sort_) {
    std::stable_sort(items.begin(), items.begin() + item_num, comparer);
  } else {
    std::sort(items.begin(), items.begin() + item_num, comparer);
  }
}

double CommonRecoEnsembleSeqNumEnricher::GetDoubleValue(ItemAttr *attr_accessor,
                                                        const CommonRecoResult &result) const {
  if (attr_accessor->value_type == AttrType::FLOAT) {
    auto double_val = result.GetDoubleAttr(attr_accessor);
    if (double_val) return *double_val;
  } else if (attr_accessor->value_type == AttrType::INT) {
    // 如果 double attr 不存在尝试读取 int attr
    auto int_val = result.GetIntAttr(attr_accessor);
    if (int_val) return *int_val;
  }
  // 缺省则返回 default value
  return default_value_;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoEnsembleSeqNumEnricher, CommonRecoEnsembleSeqNumEnricher)

}  // namespace platform
}  // namespace ks
