#include "dragon/src/processor/common/enricher/common_reco_common_attr_dispatch_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

#define DISPATCH_LIST_COMMON_ATTR(LIST_VALUE, TYPE, T)                                  \
  if (list_size > 0) {                                                                  \
    size_t list_num = LIST_VALUE->size() / list_size;                                   \
    size_t reminder = LIST_VALUE->size() % list_size;                                   \
    auto it = begin;                                                                    \
    for (int i = 0; i < std::min(list_num, num_item); ++i, ++it) {                      \
      auto list_begin = LIST_VALUE->begin() + (i * list_size);                          \
      auto list_end = list_begin + list_size;                                           \
      it->Set##TYPE##ListAttr(accessor, std::vector<T>{list_begin, list_end});          \
    }                                                                                   \
    if (it != end && reminder > 0) {                                                    \
      auto list_begin = LIST_VALUE->end() - reminder;                                   \
      it->Set##TYPE##ListAttr(accessor, std::vector<T>{list_begin, LIST_VALUE->end()}); \
    }                                                                                   \
  } else {                                                                              \
    auto it = begin;                                                                    \
    for (int i = 0; i < std::min(LIST_VALUE->size(), num_item); ++i, ++it) {            \
      it->Set##TYPE##Attr(accessor, T{LIST_VALUE->at(i)});                              \
    }                                                                                   \
  }

void CommonRecoCommonAttrDispatchEnricher::Dispatch(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end,
                                                    const DispatchConfig &dispatch_config) {
  size_t num_item = std::distance(begin, end);
  auto *accessor = context->GetItemAttrAccessor(dispatch_config.to_item_attr);
  const int list_size = dispatch_config.by_list_size;

  const auto &from_common_attr = dispatch_config.from_common_attr;
  if (auto int_list_val = context->GetIntListCommonAttr(from_common_attr)) {
    DISPATCH_LIST_COMMON_ATTR(int_list_val, Int, int64);
  } else if (auto double_list_val = context->GetDoubleListCommonAttr(from_common_attr)) {
    DISPATCH_LIST_COMMON_ATTR(double_list_val, Double, double);
  } else if (auto str_list_val = context->GetStringListCommonAttr(from_common_attr)) {
    DISPATCH_LIST_COMMON_ATTR(str_list_val, String, std::string);
  } else if (auto int_val = context->GetIntCommonAttr(from_common_attr)) {
    std::for_each(begin, end, [&](const CommonRecoResult &result) { result.SetIntAttr(accessor, *int_val); });
  } else if (auto double_val = context->GetDoubleCommonAttr(from_common_attr)) {
    std::for_each(begin, end,
                  [&](const CommonRecoResult &result) { result.SetDoubleAttr(accessor, *double_val); });
  } else if (auto str_val = context->GetStringCommonAttr(from_common_attr)) {
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      result.SetStringAttr(accessor, std::string(*str_val));
    });
  } else {
    CL_LOG_EVERY_N(WARNING, 100) << "dispatch common_attr skipped as common_attr is missing or not a list: "
                                 << from_common_attr;
  }
}

void CommonRecoCommonAttrDispatchEnricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  for (const auto &dc : dispatch_config_) {
    Dispatch(context, begin, end, dc);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCommonAttrDispatchEnricher, CommonRecoCommonAttrDispatchEnricher)

}  // namespace platform
}  // namespace ks
