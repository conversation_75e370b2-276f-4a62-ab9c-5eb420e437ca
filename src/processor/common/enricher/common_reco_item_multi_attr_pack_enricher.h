#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

/*
 * 将 item 的多个 attr 合并成一个新的 common attr, 支持 int/double/string attr 合并，支持多种合并方式
 * 1. 将所有 item 的多个 item attr 以 rowmajor 的方式合并成一个新的 common attr
 * 2. 将所有 item 的多个 item attr 以 colmajor 的方式合并成一个新的 common attr
 */

class CommonRecoItemMultiAttrPackEnricher : public CommonRecoBaseEnricher {
 public:
  enum PackMode {
    CONCAT_ROW_MAJOR = 0,
    CONCAT_COL_MAJOR,

    PACK_TYPE_LIMIT
  };

 public:
  CommonRecoItemMultiAttrPackEnricher()
      : pack_mode_(PACK_TYPE_LIMIT), float_default_value_(0.0), int_default_value_(0) {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool InitProcessor() override;

 private:
  bool CheckValueType(MutableRecoContextInterface *context);

  void ConcatItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end);

 private:
  PackMode pack_mode_;
  std::string to_attr_;
  std::vector<std::string> from_attrs_;
  std::string string_default_value_;
  double float_default_value_ = 0;
  int64 int_default_value_ = 0;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemMultiAttrPackEnricher);
};

}  // namespace platform
}  // namespace ks
