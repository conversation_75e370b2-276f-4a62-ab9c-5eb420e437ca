#include <memory>
#include "gtest/gtest.h"

#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/processor/common/enricher/common_reco_base64_enricher.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"

class CommonRecoBase64EnricherTest : public ::testing::Test {
 protected:
  void SetUp() override {
    exec_ = std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(R"json(
    {
      "base_pipeline": {
        "processor": {
          "base64_encode_00": {
            "mode": "encode",
            "is_common_attr": true,
            "input_attr": "common_attr_0",
            "output_attr": "common_attr_1",
            "type_name": "CommonRecoBase64Enricher"
          },
          "base64_encode_01": {
            "mode": "decode",
            "is_common_attr": true,
            "input_attr": "common_attr_1",
            "output_attr": "common_attr_2",
            "type_name": "CommonRecoBase64Enricher"
          },
          "base64_encode_10": {
            "mode": "encode",
            "is_common_attr": false,
            "input_attr": "item_attr_0",
            "output_attr": "item_attr_1",
            "type_name": "CommonRecoBase64Enricher"
          },
          "base64_encode_11": {
            "mode": "decode",
            "is_common_attr": false,
            "input_attr": "item_attr_1",
            "output_attr": "item_attr_2",
            "type_name": "CommonRecoBase64Enricher"
          }
        },
        "type_name": "CommonRecoPipeline"
      },
      "pipeline_map": {
        "common_test": {
          "__PARENT": "base_pipeline",
          "pipeline": [
            "base64_encode_00",
            "base64_encode_01"
          ]
        },
        "item_test": {
          "__PARENT": "base_pipeline",
          "pipeline": [
            "base64_encode_10",
            "base64_encode_11"
          ]
        }
      }
    }
    )json");
  }

  void CheckVector(const std::vector<absl::string_view> &v0, const std::vector<absl::string_view> &v1,
                   const std::string &case_prefix) {
    ASSERT_EQ(v1.size(), v0.size()) << case_prefix << ": size(v1) vs size(v0): " << v1.size() << " vs "
                                    << v0.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v1.at(i), v0.at(i)) << case_prefix << ": the `" << i << "` element is not equal";
    }
  }

  std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor> exec_;
};

TEST_F(CommonRecoBase64EnricherTest, CommonMsg) {
  // set common attrs
  std::shared_ptr<::google::protobuf::Message> request(new ks::platform::CommonRecoRequest);
  exec_->SetPtrCommonAttr("common_attr_0", request);
  // run
  exec_->Run("common_test");

  std::string exp = request->SerializeAsString();
  auto ans = exec_->GetString("common_attr_2");
  ASSERT_TRUE(ans.has_value()) << "common_attr_2 is null";
  EXPECT_EQ(exp, *ans);
}

TEST_F(CommonRecoBase64EnricherTest, CommonStr) {
  // set common attrs

  exec_->SetString("common_attr_0", "666");
  // run
  exec_->Run("common_test");

  auto ans = exec_->GetString("common_attr_2");
  ASSERT_TRUE(ans.has_value()) << "common_attr_2 is null";
  EXPECT_EQ("666", *ans);
}

TEST_F(CommonRecoBase64EnricherTest, CommonStrList) {
  // set common attrs
  exec_->SetStringList("common_attr_0", {"666", "777"});
  // run
  exec_->Run("common_test");

  auto ans = exec_->GetStringList("common_attr_2");
  ASSERT_TRUE(ans.has_value()) << "common_attr_2 is null";
  CheckVector(*exec_->GetStringList("common_attr_0"), *ans, "CommonStrList");
}

TEST_F(CommonRecoBase64EnricherTest, ItemStr) {
  // set item attrs
  auto item = exec_->AddItem(1);
  item.SetString("item_attr_0", "666");
  // run
  exec_->Run("item_test");
  auto ans = item.GetString("item_attr_2");
  ASSERT_TRUE(ans.has_value()) << "item_attr_2 is null";
  EXPECT_EQ("666", *ans);
}

TEST_F(CommonRecoBase64EnricherTest, ItemStrList) {
  // set item attrs
  auto item = exec_->AddItem(1);
  item.SetStringList("item_attr_0", {"666", "777"});
  // run
  exec_->Run("item_test");

  auto ans = item.GetStringList("item_attr_2");
  ASSERT_TRUE(ans.has_value()) << "item_attr_2 is null";
  CheckVector(*item.GetStringList("item_attr_0"), *ans, "ItemStrList");
}
