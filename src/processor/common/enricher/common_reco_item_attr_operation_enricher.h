#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoItemAttrOperationEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemAttrOperationEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    item_attr_a_attr_ = config()->GetString("item_attr_a", "");
    item_attr_b_attr_ = config()->GetString("item_attr_b", "");
    common_attr_b_config_ = config()->Get("common_attr_b");
    if (item_attr_a_attr_.empty()) {
      LOG(ERROR) << "CommonRecoItemAttrOperationEnricher init failed! Missing 'item_attr_a_attr' config"
                 << " or it is empty string.";
    }

    if (item_attr_b_attr_.empty() && common_attr_b_config_ == nullptr) {
      LOG(ERROR) << "CommonRecoItemAttrOperationEnricher init failed! Missing 'item_attr_b' or "
                    "'common_attr_b' config"
                 << " or it is empty string.";
    }

    operator_ = config()->GetString("operator", "");
    if (operator_.empty()) {
      LOG(ERROR) << "CommonRecoItemAttrOperationEnricher init failed! Missing 'operator' config"
                 << " or it is empty string.";
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoItemAttrOperationEnricher init failed! Missing 'output_attr' config"
                 << " or it is empty string.";
      return false;
    }
    return true;
  }

  bool GetNumericValue(MutableRecoContextInterface *context, ItemAttr *attr_accessor,
                       const CommonRecoResult &result, double *value) const;

 private:
  std::string item_attr_a_attr_;
  std::string item_attr_b_attr_;
  const base::Json *common_attr_b_config_ = nullptr;
  std::string operator_;
  std::string output_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemAttrOperationEnricher);
};

}  // namespace platform
}  // namespace ks
