#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_shared_gflags.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/async_task_thread_pool.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "third_party/abseil/absl/container/flat_hash_map.h"

DECLARE_int64(abtest_async_pool_queue_capacity);
DECLARE_int64(abtest_async_pool_thread_num);

namespace ks {
namespace platform {

class CommonRecoAbtestCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAbtestCommonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AbtestParam {
    std::string param_name;
    absl::flat_hash_map<std::string, std::string> param_name_for_suffix;
    std::string attr_name;
    std::string save_exp_id_to;
    std::string save_group_id_to;
    AbtestParamType type = AbtestParamType::UNKNOWN;
    CommonAttr *common_attr = nullptr;
    ItemAttr *item_attr = nullptr;
    const base::Json *report_ab_hit = nullptr;
    int64 default_int = 0;
    double default_double = 0.0;
    std::string default_string;
  };

  struct AbtestContextParam {
    uint64 user_id = 0;
    std::string device_id;
    std::string session_id;
    std::string product;
    std::string platform;
    std::string app_version;
    std::string explore_locale;
    std::string country;
    std::string province;
    std::string city;
    std::string photo_page;
    std::string browse_type;
    std::string network_type;
    std::string phone_model;
    std::string language;
    std::string isp;
    absl::optional<std::vector<absl::string_view>> user_tag_names;
    absl::optional<std::vector<absl::string_view>> user_tag_values;
  };

  bool InitProcessor() override {
    std::string biz_name = config()->GetString("biz_name", FLAGS_abtest_biz_name);
    if (!biz_name.empty()) {
      if (RecoUtil::IsDynamicParameter(biz_name)) {
        dynamic_biz_name_config_ = config()->Get("biz_name");
      } else {
        biz_ = ks::GetAbtestBizByName(biz_name);
        if (biz_ == ks::AbtestBiz::NULL_BIZ) {
          LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed!"
                     << " Invalid abtest biz_name: " << biz_name
                     << ", please contact abtest OnCall to add it to C++ sdk";
          return false;
        }
      }
    } else {
      int biz_seq_num = config()->GetInt("biz_seq_num", FLAGS_abtestbiz_seq_num);
      biz_ = static_cast<ks::AbtestBiz>(biz_seq_num);
      std::string biz_name = ks::GetAbtestBizName(biz_);
      if (biz_name.empty()) {
        LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed!"
                   << " biz_name and FLAGS_abtest_biz_name are empty, and found invalid abtest biz_seq_num: "
                   << biz_seq_num << ", please contact abtest OnCall to add it to C++ sdk";
        return false;
      }
    }

    ks::WarmupAbtest({biz_});

    for_item_level_ = config()->GetBoolean("for_item_level", false);
    if (for_item_level_) {
      item_level_user_id_attr_ = config()->GetString("item_level_user_id_attr", "");
      item_level_device_id_attr_ = config()->GetString("item_level_device_id_attr", "");
      item_level_session_id_attr_ = config()->GetString("item_level_session_id_attr", "");
    }

    auto *params = config()->Get("ab_params");
    if (!params || !params->IsArray()) {
      LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed!"
                 << " Missing \"ab_params\" config or it is not an array.";
      return false;
    }

    param_prefix_ = config()->Get("param_prefix");

    prioritized_suffix_ = config()->Get("prioritized_suffix");

    bool deduplicate = config()->GetBoolean("deduplicate", false);

    ab_params_.clear();
    std::set<std::string> param_attr_name_set;
    for (const auto *param_json : params->array()) {
      AbtestParam param;
      if (!param_json->IsObject()) {
        LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed! Item of ab_params should be a dict!"
                   << " Value found: " << param_json->ToString();
        return false;
      }
      const auto *param_name = param_json->Get("param_name");
      if (!param_name) {
        LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed! Missing config param_name!";
        return false;
      }

      if (param_name->IsString()) {
        param.param_name = param_name->StringValue();
      } else if (param_name->IsObject()) {
        RecoUtil::ExtractStringMapFromJsonConfig(param_name, &param.param_name_for_suffix);
      } else {
        LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed! param_name should be a string or map!";
        return false;
      }

      std::string type_str = param_json->GetString("param_type", "");
      param.type = RecoUtil::ParseAbtestParamType(type_str);
      if (param.type == AbtestParamType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed!"
                   << " Unknown abtest param_type: " << type_str;
        return false;
      }

      param.attr_name = param_json->GetString("attr_name", param.param_name);
      if (param.attr_name.empty()) {
        if (prioritized_suffix_ || param_prefix_ || !param.param_name_for_suffix.empty()) {
          LOG(ERROR) << "CommonRecoAbtestCommonAttrEnricher init failed!"
                     << " attr_name cannot be empty if prioritized_suffix or param_prefix "
                     << "or multiple param_name exists";
          return false;
        }
        param.attr_name = param.param_name;
      }
      param.save_exp_id_to = param_json->GetString("save_exp_id_to", "");
      param.save_group_id_to = param_json->GetString("save_group_id_to", "");
      if (deduplicate) {
        auto it = param_attr_name_set.insert(param.attr_name);
        if (!it.second) {
          LOG(INFO) << "CommonRecoAbtestCommonAttrEnricher param attr name is duplicated, name:"
                    << param.attr_name;
          continue;
        }
      }

      param.report_ab_hit = param_json->Get("report_ab_hit");

      const auto *default_value = param_json->Get("default_value");
      if (default_value) {
        switch (param.type) {
          case AbtestParamType::INT:
            param.default_int = default_value->IntValue((int64)0);
            break;
          case AbtestParamType::BOOLEAN: {
            bool val = false;
            if (!default_value->BooleanValue(&val)) {
              val = (bool)default_value->IntValue((int64)0);
            }
            param.default_int = val;
            break;
          }
          case AbtestParamType::DOUBLE:
            param.default_double = default_value->NumberValue(0.0);
            break;
          case AbtestParamType::STRING:
            param.default_string = default_value->StringValue();
            break;
          default:
            break;
        }
      }

      ab_params_.push_back(std::move(param));
    }

    parallel_get_ = config()->GetInt("parallel_get", 1);
    parallel_get_ = std::max(parallel_get_, 1);

    // 暂不支持 item level 多线程
    if (for_item_level_) {
      parallel_get_ = 1;
    }

    if (parallel_get_ > 1) {
      std::vector<int> partitions =
          RecoUtil::BalancedPartition(ab_params_.size() / parallel_get_ + 1, ab_params_.size());
      offsets_.push_back(0);
      for (int i = 0; i < partitions.size(); ++i) {
        offsets_.push_back(offsets_[i] + partitions[i]);
      }

      std::call_once(oc_, [&]() {
        abtest_param_thread_pool_ = std::make_unique<AsyncTaskThreadPool<bool>>(
            "AbtestParamParallelGet", FLAGS_abtest_async_pool_thread_num, FLAGS_abtest_async_pool_thread_num,
            FLAGS_abtest_async_pool_queue_capacity);
        abtest_param_thread_pool_->WaitForInitDone();
      });
    }

    return true;
  }

  std::string ConvertAbtestParamName(const AbtestParam &param,
                                     absl::string_view suffix,
                                     const std::string &prefix);

  int64 GetIntAbtestValue(MutableRecoContextInterface *context, const AbtestParam &param,
                          const std::vector<absl::string_view> &prioritized_suffix,
                          const ks::abtest::AbParamContext &ab_param_context,
                          const std::string &prefix);

  double GetDoubleAbtestValue(MutableRecoContextInterface *context, const AbtestParam &param,
                              const std::vector<absl::string_view> &prioritized_suffix,
                              const ks::abtest::AbParamContext &ab_param_context,
                              const std::string &prefix);

  std::string GetStringAbtestValue(MutableRecoContextInterface *context, const AbtestParam &param,
                                   const std::vector<absl::string_view> &prioritized_suffix,
                                   const ks::abtest::AbParamContext &ab_param_context,
                                   const std::string &prefix);

  bool GetBoolAbtestValue(MutableRecoContextInterface *context, const AbtestParam &param,
                          const std::vector<absl::string_view> &prioritized_suffix,
                          const ks::abtest::AbParamContext &ab_param_context,
                          const std::string &prefix);

  void GetAbtestForCommonLevel(MutableRecoContextInterface *context,
                               const std::vector<absl::string_view> &prioritized_suffix,
                               const std::string &param_prefix);

  void GetAbtestForCommonLevelBatch(MutableRecoContextInterface *context, int index_begin, int index_end,
                                    const std::vector<absl::string_view> &prioritized_suffix,
                                    const AbtestContextParam &abtest_context_param,
                                    const std::string &param_prefix);

  void GetAbtestForItemLevel(MutableRecoContextInterface *context, RecoResultConstIter begin,
                             RecoResultConstIter end,
                             const std::vector<absl::string_view> &prioritized_suffix,
                             const std::string &param_prefix);

  std::unordered_set<std::string> GetAbParamNameSet(int index_begin, int index_end,
                                                    const std::vector<absl::string_view> &prioritized_suffix,
                                                    const std::string &param_prefix);

  void SetExpInfo(MutableRecoContextInterface *context, const AbtestParam &param,
                  const ks::abtest::AbtestResult &ab_result);

 private:
  const base::Json *dynamic_biz_name_config_ = nullptr;
  ks::AbtestBiz biz_ = ks::AbtestBiz::NULL_BIZ;
  std::vector<AbtestParam> ab_params_;
  const base::Json *prioritized_suffix_ = nullptr;
  const base::Json *param_prefix_ = nullptr;
  bool for_item_level_ = false;
  std::string item_level_user_id_attr_;
  std::string item_level_device_id_attr_;
  std::string item_level_session_id_attr_;
  ItemAttr *user_id_item_attr_accessor_ = nullptr;
  ItemAttr *device_id_item_attr_accessor_ = nullptr;
  ItemAttr *session_id_item_attr_accessor_ = nullptr;

  int parallel_get_ = 1;
  std::vector<int> offsets_;
  static std::unique_ptr<AsyncTaskThreadPool<bool>> abtest_param_thread_pool_;
  static std::once_flag oc_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAbtestCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
