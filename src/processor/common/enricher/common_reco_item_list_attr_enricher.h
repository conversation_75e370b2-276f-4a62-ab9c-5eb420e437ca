#pragma once

#include <string>
#include <unordered_map>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoItemListAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemListAttrEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    items_in_item_attr_ = config()->GetString("items_in_item_attr", "");
    if (items_in_item_attr_.empty()) {
      LOG(ERROR) << "CommonRecoItemListAttrEnricher init failed! 'items_in_item_attr' cannot be empty!";
      return false;
    }

    const auto *mapping = config()->Get("item_attr_mapping");
    if (!mapping || !mapping->IsObject()) {
      LOG(ERROR) << "CommonRecoItemListAttrEnricher init failed! Missing 'item_attr_mapping' or it's"
                 << " not an object!";
      return false;
    }

    for (const auto &pr : mapping->objects()) {
      std::string val = pr.second->StringValue();
      if (!val.empty()) {
        item_attr_mapping_[pr.first] = std::move(val);
      }
    }

    default_int_ = config()->GetInt("default_int_value", 0);
    default_double_ = config()->GetNumber("default_double_value", 0.0);
    default_string_ = config()->GetString("default_string_value");
    use_item_type_ = config()->GetInt("use_item_type", 0);

    return true;
  }

  void HandleItemAttrMapping(MutableRecoContextInterface *context, const CommonRecoResult &result,
                             ItemAttr *from_attr, ItemAttr *to_attr);

 private:
  std::string items_in_item_attr_;
  ItemAttr *items_in_item_attr_accessor_ = nullptr;
  std::unordered_map<std::string, std::string> item_attr_mapping_;

  int64 default_int_ = 0;
  double default_double_ = 0.0;
  std::string default_string_;

  int use_item_type_ = 0;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemListAttrEnricher);
};

}  // namespace platform
}  // namespace ks
