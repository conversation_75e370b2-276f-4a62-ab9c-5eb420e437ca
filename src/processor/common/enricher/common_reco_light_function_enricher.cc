#include "dragon/src/processor/common/enricher/common_reco_light_function_enricher.h"

namespace ks {
namespace platform {

void CommonRecoLightFunctionEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  if (import_common_attr_accessor_map_.empty()) {
    for (const auto &pair : import_common_attrs_) {
      import_common_attr_accessor_map_.emplace(pair.second, context->GetCommonAttrAccessor(pair.first));
    }
    light_function_context_.SetImportCommonAttrAccessorMap(import_common_attr_accessor_map_);
  }

  if (export_common_attr_accessor_map_.empty()) {
    for (const auto &pair : export_common_attrs_) {
      export_common_attr_accessor_map_.emplace(pair.first, context->GetCommonAttrAccessor(pair.second));
    }
    light_function_context_.SetExportCommonAttrAccessorMap(export_common_attr_accessor_map_);
  }

  if (import_item_attr_accessor_map_.empty()) {
    for (const auto &pair : import_item_attrs_) {
      import_item_attr_accessor_map_.emplace(pair.second, context->GetItemAttrAccessor(pair.first));
    }
    light_function_context_.SetImportItemAttrAccessorMap(import_item_attr_accessor_map_);
  }

  if (export_item_attr_accessor_map_.empty()) {
    for (const auto &pair : export_item_attrs_) {
      export_item_attr_accessor_map_.emplace(pair.first, context->GetItemAttrAccessor(pair.second));
    }
    light_function_context_.SetExportItemAttrAccessorMap(export_item_attr_accessor_map_);
  }

  if (!light_function_set_->Run(function_name_, light_function_context_, begin, end)) {
    CL_LOG_ERROR("light_function", "fun_fail: " + GetName() + "-" + function_name_)
        << "Failed to run the function: " << GetName() << ", " << function_name_;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLightFunctionEnricher, CommonRecoLightFunctionEnricher)

}  // namespace platform
}  // namespace ks
