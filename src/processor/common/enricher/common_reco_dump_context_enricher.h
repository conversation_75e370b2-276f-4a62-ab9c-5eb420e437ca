#pragma once
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "base/random/pseudo_random.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/module/traceback_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "redis_proxy_client/redis_pipeline.h"
#include "redis_proxy_client/redis_proxy_client.h"

namespace ks {
namespace platform {
class CommonRecoDumpContextEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoDumpContextEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *common_attrs = config()->Get("common_attrs");
    RecoUtil::ExtractStringListFromJsonConfig(common_attrs, &common_attrs_);
    auto *include_item_results = config()->Get("include_item_results");
    auto *item_attrs = config()->Get("item_attrs");
    if (item_attrs && item_attrs->IsString() && item_attrs->StringValue("") == "*") {
      dump_all_item_attrs_ = true;
    } else {
      RecoUtil::ExtractStringListFromJsonConfig(item_attrs, &item_attrs_);
    }

    if (include_item_results) {
      include_item_results_ = include_item_results->BooleanValue(!item_attrs_.empty());
    } else {
      include_item_results_ = !item_attrs_.empty();
    }

    dump_as_table_ = config()->GetBoolean("dump_as_table", false);

    if (common_attrs_.empty() && !include_item_results_) {
      LOG(ERROR) << "CommonRecoDumpContextEnricher init failed! \"common_attrs\"  and "
                    "\"include_item_results\" cannot be both empty!";
      return false;
    }
    dump_to_attr_ = config()->GetString("dump_to_attr", "");
    if (dump_to_attr_.empty()) {
      LOG(ERROR) << "CommonRecoDumpContextEnricher init failed! \"dump_to_attr\" cannot "
                 << "be empty!";
      return false;
    }
    dump_common_attrs_from_request_ = config()->GetBoolean("dump_common_attrs_from_request", false);
    return true;
  }

  void DumpItemTable(MutableRecoContextInterface *context, RecoResultConstIter begin,
                     RecoResultConstIter end);

  void DumpStepInfo(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

 private:
  bool dump_as_table_ = false;
  bool dump_all_item_attrs_ = false;
  bool dump_common_attrs_from_request_ = false;
  std::vector<std::string> common_attrs_;
  bool include_item_results_ = false;
  std::vector<std::string> item_attrs_;
  std::string dump_to_attr_ = "";
  DISALLOW_COPY_AND_ASSIGN(CommonRecoDumpContextEnricher);
};
}  // namespace platform
}  // namespace ks
