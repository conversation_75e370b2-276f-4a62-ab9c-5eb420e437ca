#include "dragon/src/processor/common/enricher/common_reco_lua_attr_enricher.h"

#include "dragon/src/util/perf_report_util.h"
#include "serving_base/util/scope_exit.h"

DEFINE_bool(enable_branch_condition_result_perflog, true, "perflog branch condition result");
namespace ks {
namespace platform {

static const char kLuaErrMsgLineNumPattern[] = "\"]:";

void CommonRecoLuaAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  if (!for_branch_control_) {
    if (import_attr_accessors_.empty()) {
      for (const auto &attr : import_item_attrs_) {
        import_attr_accessors_.push_back(context->GetItemAttrAccessor(attr));
      }
    }
    if (export_attr_accessors_.empty()) {
      for (const auto &attr : export_item_attrs_) {
        export_attr_accessors_.push_back(context->GetItemAttrAccessor(attr));
      }
    }
  }

  if (import_common_attr_accessors_.empty()) {
    for (const auto &attr : import_common_attrs_) {
      import_common_attr_accessors_.push_back(context->GetCommonAttrAccessor(attr));
    }
  }
  if (export_common_attr_accessors_.empty()) {
    for (const auto &attr : export_common_attrs_) {
      export_common_attr_accessors_.push_back(context->GetCommonAttrAccessor(attr));
    }
  }

  HandleCommonLevel(context);
  if (!export_item_attrs_.empty()) {
    HandleItemLevel(context, begin, end);
  }

  if (!for_branch_control_) {
    int mem_used_kb = lua_gc(lua_state_, LUA_GCCOUNT, 0);
    CL_PERF_INTERVAL(mem_used_kb, kPerfNs, "lua.mem_used_kb", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName());
  }
}

void CommonRecoLuaAttrEnricher::HandleCommonLevel(MutableRecoContextInterface *context) {
  base::ScopeExit scope_exit([this] { ClearLuaStack(); });
  ClearLuaStack();

  std::string function_for_common = for_branch_control_
                                        ? branch_control_func_name_
                                        : GetStringProcessorParameter(context, "function_for_common");

  int64 import_start = 0;
  if (!for_branch_control_) {
    import_start = base::GetTimestamp();
  }

  ImportCommonAttrs(context);

  const int export_num = export_common_attrs_.size();
  if (!for_branch_control_) {
    int64 import_time = base::GetTimestamp() - import_start;
    CL_PERF_INTERVAL(import_time, kPerfNs, "lua.import_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_common, "common");
    if (!HasLuaFunction(context, function_for_common)) {
      return;
    }
    CheckLuaStack(export_num);
  }

  lua_getglobal(lua_state_, function_for_common.data());
  int64 start_ts = 0;
  if (!for_branch_control_) {
    start_ts = base::GetTimestamp();
  }
  int err_code = lua_pcall(lua_state_, 0, LUA_MULTRET, 0);
  if (!for_branch_control_) {
    int64 run_time = base::GetTimestamp() - start_ts;
    CL_PERF_INTERVAL(run_time, kPerfNs, "lua.run_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_common, "common");
  }

  if (err_code) {
    if (for_branch_control_) {
      // 如果是用于分支判断, lua 执行失败时给 export_common_attrs 赋值 1, 以跳过后续 processor 执行
      for (auto *attr : export_common_attr_accessors_) {
        attr->SetIntValue(1);
      }
      if (!branch_code_info_.empty() && FLAGS_enable_branch_condition_result_perflog) {
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "branch_condition_result", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), "", "true", branch_code_info_);
      }
    }
    size_t len = 0;
    const char *message = lua_tolstring(lua_state_, -1, &len);
    auto line = GetErrorLine({message, len});
    CL_LOG_ERROR("lua", GetName() + " lua_error:" + message)
        << "failed to call lua function " << function_for_common << ", error code: " << err_code
        << ", message: " << message << ", error line:" << (line.empty() ? " (EMPTY)" : line)
        << ", processor: " << GetName();
    return;
  }

  // NOTE(fangjianbing): lua stack 的位置索引从 1 开始
  int return_num = lua_gettop(lua_state_);
  if (return_num != export_num) {
    CL_LOG_WARNING("lua", GetName() + " return_and_export_num_mismatch")
        << "inconsistent return num and export num: " << return_num << " vs " << export_num;
  }
  if (return_num > export_num) {
    lua_settop(lua_state_, export_num);
    return_num = export_num;
  }

  int64 export_start = 0;
  if (!for_branch_control_) {
    export_start = base::GetTimestamp();
  }

  for (int i = return_num - 1; i >= 0; --i) {
    base::ScopeExit must_pop([this] { lua_pop(lua_state_, 1); });
    auto *attr = export_common_attr_accessors_[i];
    int type = lua_type(lua_state_, -1);
    switch (type) {
      case LUA_TNUMBER:
        if (lua_isinteger(lua_state_, -1)) {
          attr->SetIntValue(lua_tointeger(lua_state_, -1));
        } else {
          attr->SetDoubleValue(lua_tonumber(lua_state_, -1));
        }
        break;
      case LUA_TSTRING:
        attr->SetStringValue(lua_tostring(lua_state_, -1));
        break;
      case LUA_TBOOLEAN: {
        bool lua_ret = lua_toboolean(lua_state_, -1);
        attr->SetIntValue(lua_ret);
        if (for_branch_control_ && !branch_code_info_.empty() && return_num == 1 &&
            FLAGS_enable_branch_condition_result_perflog) {
          base::perfutil::PerfUtilWrapper::CountLogStash(
              kPerfNs, "branch_condition_result", GlobalHolder::GetServiceIdentifier(),
              context->GetRequestType(), "", lua_ret ? "true" : "false", branch_code_info_);
          if (lua_ret == false && !branch_to_be_delete_.empty()) {
            base::perfutil::PerfUtilWrapper::CountLogStash(
                kPerfNs, "incorrect_to_be_delete", GlobalHolder::GetServiceIdentifier(),
                context->GetRequestType(), branch_to_be_delete_, branch_code_info_);
            CL_LOG(ERROR) << "you may mark to_be_delete=" << branch_to_be_delete_
                          << " by mistake in branch with code_info: " << branch_code_info_
                          << ", which is still in use!";
          }
        }
        break;
      }
      case LUA_TTABLE:
        // 处理数组情况
        ReadLuaTableToCommonAttr(attr);
        break;
      case LUA_TNIL:
      case LUA_TNONE:
        if (clear_attr_if_nil_) {
          attr->ClearValue();
        }
        break;
      default:
        CL_LOG_ERROR("lua", GetName() + " invalid_return_type:" + attr->name())
            << "the type of return value " << attr->name()
            << " should be integer / boolean / number / string or these arrays. Processor: " << GetName();
        break;
    }
  }

  if (!for_branch_control_) {
    int64 export_time = base::GetTimestamp() - export_start;
    CL_PERF_INTERVAL(export_time, kPerfNs, "lua.export_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_common, "common");
  }
}

void CommonRecoLuaAttrEnricher::HandleItemLevel(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  base::ScopeExit scope_exit([this] { ClearLuaStack(); });
  std::string function_for_item = GetStringProcessorParameter(context, "function_for_item");
  if (!HasLuaFunction(context, function_for_item)) {
    return;
  }

  std::vector<int> error_count;
  error_count.resize(export_item_attrs_.size(), 0);

  int64 import_time = 0;
  int64 export_time = 0;
  int64 run_time = 0;
  int64 import_start_ts = 0;
  int64 run_start_ts = 0;
  int64 export_start_ts = 0;

  const int64 item_num = std::distance(begin, end);
  const int64 perf_index = random_.GetInt(0, item_num - 1);
  int64 index = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    bool enable_perf = perf_index == index;
    // 保证每次循环前清空 lua stack
    ClearLuaStack();
    const int export_num = export_item_attrs_.size();
    if (enable_perf) {
      import_start_ts = base::GetTimestamp();
    }
    ImportItemAttrs(result);

    CheckLuaStack(export_num);
    lua_getglobal(lua_state_, function_for_item.data());
    lua_pushinteger(lua_state_, index++);
    lua_pushinteger(lua_state_, result.item_key);
    lua_pushinteger(lua_state_, result.reason);
    lua_pushnumber(lua_state_, result.score);

    if (enable_perf) {
      run_start_ts = base::GetTimestamp();
      import_time += (run_start_ts - import_start_ts);
    }

    int err_code = lua_pcall(lua_state_, 4, LUA_MULTRET, 0);

    if (enable_perf) {
      export_start_ts = base::GetTimestamp();
      run_time += (export_start_ts - run_start_ts);
    }

    if (err_code) {
      size_t len = 0;
      const char *message = lua_tolstring(lua_state_, -1, &len);
      auto line = GetErrorLine({message, len});
      CL_LOG_ERROR_EVERY("lua", GetName() + " lua_error:" + message, 1000)
          << "failed to call lua function " << function_for_item << ", item_key: " << result.item_key
          << ", error code: " << err_code << ", message: " << message
          << ", error line:" << (line.empty() ? "NONE" : line) << ", processor: " << GetName()
          << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    // NOTE(fangjianbing): lua stack 的位置索引从 1 开始
    int return_num = lua_gettop(lua_state_);
    if (return_num != export_num) {
      CL_LOG_WARNING_EVERY("lua", GetName() + " return_and_export_num_mismatch", 1000)
          << "inconsistent return num and export num: " << return_num << " vs " << export_num;
    }
    if (return_num > export_num) {
      lua_settop(lua_state_, export_num);
      return_num = export_num;
    }

    for (int i = return_num - 1; i >= 0; --i) {
      base::ScopeExit must_pop([this] { lua_pop(lua_state_, 1); });
      const auto &attr_accessor = export_attr_accessors_[i];
      int type = lua_type(lua_state_, -1);
      switch (type) {
        case LUA_TNUMBER:
          if (attr_accessor->value_type != AttrType::FLOAT && lua_isinteger(lua_state_, -1)) {
            result.SetIntAttr(attr_accessor, lua_tointeger(lua_state_, -1));
          } else {
            result.SetDoubleAttr(attr_accessor, lua_tonumber(lua_state_, -1));
          }
          break;
        case LUA_TSTRING:
          result.SetStringAttr(attr_accessor, lua_tostring(lua_state_, -1));
          break;
        case LUA_TBOOLEAN:
          result.SetIntAttr(attr_accessor, lua_toboolean(lua_state_, -1));
          break;
        case LUA_TTABLE:
          // 处理数组情况
          ReadLuaTableToItemAttr(attr_accessor, result);
          break;
        case LUA_TNIL:
        case LUA_TNONE:
          if (clear_attr_if_nil_) {
            result.ClearAttr(attr_accessor);
          }
          break;
        default:
          ++error_count[i];
          break;
      }
    }

    if (enable_perf) {
      export_time += (base::GetTimestamp() - export_start_ts);
    }
  });

  if (!for_branch_control_) {
    CL_PERF_INTERVAL(run_time * item_num, kPerfNs, "lua.run_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_item, "item");
    CL_PERF_INTERVAL(import_time * item_num, kPerfNs, "lua.import_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_item, "item");
    CL_PERF_INTERVAL(export_time * item_num, kPerfNs, "lua.export_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_item, "item");

    int item_data_num = std::distance(begin, end) * (import_item_attrs_.size() + export_item_attrs_.size());
    CL_PERF_INTERVAL(item_data_num, kPerfNs, "lua.item_data_num", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), function_for_item);
  }

  for (int i = 0; i < error_count.size(); ++i) {
    if (error_count[i] > 0) {
      const auto &attr_name = export_item_attrs_[i];
      CL_LOG_ERROR_COUNT(error_count[i], "lua", GetName() + " invalid_return_type:" + attr_name)
          << "the type of return value " << attr_name
          << " should be integer / boolean / number / string or these arrays. Processor: " << GetName()
          << RecoUtil::GetRequestInfoForLog(context);
    }
  }
}

bool CommonRecoLuaAttrEnricher::HasLuaFunction(ReadableRecoContextInterface *context,
                                               const std::string &function) {
  if (function.empty()) return false;
  base::ScopeExit scope_exit([this] { ClearLuaStack(); });
  lua_getglobal(lua_state_, function.data());
  bool is_function = lua_isfunction(lua_state_, -1);
  if (!is_function) {
    CL_LOG_ERROR("lua", GetName() + " not_a_function:" + function)
        << function << " is not a function in lua_script! Processor: " << GetName()
        << RecoUtil::GetRequestInfoForLog(context);
  }
  return is_function;
}

void CommonRecoLuaAttrEnricher::ImportCommonAttrs(ReadableRecoContextInterface *context) {
  for (const auto &attr : import_common_attr_accessors_) {
    bool has_value = false;
    switch (attr->value_type) {
      case AttrType::INT:
        if (auto int_val = attr->GetIntValue()) {
          lua_pushinteger(lua_state_, *int_val);
          has_value = true;
        }
        break;
      case AttrType::FLOAT:
        if (auto double_val = attr->GetDoubleValue()) {
          lua_pushnumber(lua_state_, *double_val);
          has_value = true;
        }
        break;
      case AttrType::STRING:
        if (auto string_val = attr->GetStringValue()) {
          lua_pushlstring(lua_state_, string_val->data(), string_val->size());
          has_value = true;
        }
        break;
      case AttrType::INT_LIST:
        if (auto int_list_val = attr->GetIntListValue()) {
          PushVectorToLuaTable(*int_list_val);
          has_value = true;
        }
        break;
      case AttrType::FLOAT_LIST:
        if (auto double_list_val = attr->GetDoubleListValue()) {
          PushVectorToLuaTable(*double_list_val);
          has_value = true;
        }
        break;
      case AttrType::STRING_LIST:
        if (auto string_list_val = attr->GetStringListValue()) {
          PushVectorToLuaTable(*string_list_val);
          has_value = true;
        }
        break;
      default:
        if (attr->HasValue()) {
          lua_pushboolean(lua_state_, true);
          has_value = true;
        }
        break;
    }

    if (!has_value) {
      lua_pushnil(lua_state_);
    }
    lua_setglobal(lua_state_, attr->name().data());
  }
}

void CommonRecoLuaAttrEnricher::ImportItemAttrs(const CommonRecoResult &result) {
  for (const auto &attr_accessor : import_attr_accessors_) {
    bool has_value = false;
    switch (attr_accessor->value_type) {
      case AttrType::INT:
        if (auto int_val = result.GetIntAttr(attr_accessor)) {
          lua_pushinteger(lua_state_, *int_val);
          has_value = true;
        }
        break;
      case AttrType::FLOAT:
        if (auto double_val = result.GetDoubleAttr(attr_accessor)) {
          lua_pushnumber(lua_state_, *double_val);
          has_value = true;
        }
        break;
      case AttrType::STRING:
        if (auto string_val = result.GetStringAttr(attr_accessor)) {
          lua_pushlstring(lua_state_, string_val->data(), string_val->size());
          has_value = true;
        }
        break;
      case AttrType::INT_LIST:
        if (auto int_list_val = result.GetIntListAttr(attr_accessor)) {
          PushVectorToLuaTable(*int_list_val);
          has_value = true;
        }
        break;
      case AttrType::FLOAT_LIST:
        if (auto double_list_val = result.GetDoubleListAttr(attr_accessor)) {
          PushVectorToLuaTable(*double_list_val);
          has_value = true;
        }
        break;
      case AttrType::STRING_LIST:
        if (auto string_list_val = result.GetStringListAttr(attr_accessor)) {
          PushVectorToLuaTable(*string_list_val);
          has_value = true;
        }
        break;
      default:
        break;
    }

    if (!has_value) {
      lua_pushnil(lua_state_);
    }
    lua_setglobal(lua_state_, attr_accessor->name().data());
  }
}

void CommonRecoLuaAttrEnricher::ReadLuaTableToCommonAttr(CommonAttr *attr) {
  int value_type = LUA_TNONE;
  bool is_integer = false;
  for (int i = 1;; ++i) {
    lua_rawgeti(lua_state_, -1, i);
    base::ScopeExit scope_exit([this] { lua_pop(lua_state_, 1); });

    const int type = lua_type(lua_state_, -1);
    if (i == 1) {
      // 用第一个值的类型作为数组的类型
      value_type = type;
    } else if (type != LUA_TNONE && type != LUA_TNIL) {
      if (type != value_type || (is_integer && !lua_isinteger(lua_state_, -1))) {
        CL_LOG_EVERY_N(WARNING, 100) << "inconsistent list value type for export common_attr: "
                                     << attr->name() << ", expect type: " << value_type
                                     << ", actual type: " << type;
        return;
      }
    }

    switch (type) {
      case LUA_TNUMBER:
        if (i == 1) {
          if (lua_isinteger(lua_state_, -1)) {
            is_integer = true;
            attr->ResetIntListValue();
          } else {
            attr->ResetDoubleListValue();
          }
        }
        if (is_integer) {
          attr->AppendIntListValue(lua_tointeger(lua_state_, -1));
        } else {
          attr->AppendDoubleListValue(lua_tonumber(lua_state_, -1));
        }
        break;
      case LUA_TSTRING:
        if (i == 1) attr->ResetStringListValue();
        attr->AppendStringListValue(lua_tostring(lua_state_, -1));
        break;
      case LUA_TBOOLEAN:
        if (i == 1) attr->ResetIntListValue();
        attr->AppendIntListValue(lua_toboolean(lua_state_, -1));
        break;
      case LUA_TNIL:
      case LUA_TNONE:
        if (i == 1) attr->ClearValue();
        return;
      default:
        CL_LOG_ERROR("lua", GetName() + " invalid_array_type:" + attr->name())
            << "the type of return array value " << attr->name()
            << " should be integer / boolean / number / string. Processor: " << GetName();
        return;
    }
  }
}

void CommonRecoLuaAttrEnricher::ReadLuaTableToItemAttr(ItemAttr *attr_accessor,
                                                       const CommonRecoResult &result) {
  int value_type = LUA_TNONE;
  bool is_integer = false;
  const std::string &attr_name = attr_accessor->name();
  for (int i = 1;; ++i) {
    lua_rawgeti(lua_state_, -1, i);
    base::ScopeExit scope_exit([this] { lua_pop(lua_state_, 1); });

    const int type = lua_type(lua_state_, -1);
    if (i == 1) {
      // 用第一个值的类型作为数组的类型
      value_type = type;
    } else if (type != LUA_TNONE && type != LUA_TNIL) {
      if (type != value_type || (is_integer && !lua_isinteger(lua_state_, -1))) {
        CL_LOG_EVERY_N(WARNING, 10000) << "inconsistent list value type for export item_attr: " << attr_name
                                       << ", expect type: " << value_type << ", actual type: " << type;
        return;
      }
    }

    switch (type) {
      case LUA_TNUMBER:
        if (i == 1) {
          if (lua_isinteger(lua_state_, -1)) {
            is_integer = true;
            result.ResetIntListAttr(attr_accessor);
          } else {
            result.ResetDoubleListAttr(attr_accessor);
          }
        }
        if (is_integer) {
          result.AppendIntListAttr(attr_accessor, lua_tointeger(lua_state_, -1));
        } else {
          result.AppendDoubleListAttr(attr_accessor, lua_tonumber(lua_state_, -1));
        }
        break;
      case LUA_TSTRING:
        if (i == 1) result.ResetStringListAttr(attr_accessor);
        result.AppendStringListAttr(attr_accessor, lua_tostring(lua_state_, -1));
        break;
      case LUA_TBOOLEAN:
        if (i == 1) result.ResetIntListAttr(attr_accessor);
        result.AppendIntListAttr(attr_accessor, lua_toboolean(lua_state_, -1));
        break;
      case LUA_TNIL:
      case LUA_TNONE:
        if (i == 1) result.ClearAttr(attr_accessor);
        return;
      default:
        CL_LOG_ERROR("lua", GetName() + " invalid_array_type:" + attr_name)
            << "the type of return array value " << attr_name
            << " should be integer / boolean / number / string. Processor: " << GetName();
        return;
    }
  }
}

absl::string_view CommonRecoLuaAttrEnricher::GetErrorLine(absl::string_view err_msg) {
  auto pos = err_msg.find(kLuaErrMsgLineNumPattern);
  if (pos == absl::string_view::npos) return absl::string_view();
  int line_num = std::atoi(err_msg.data() + pos + strlen(kLuaErrMsgLineNumPattern));
  if (line_num <= 0 || line_num > lua_script_lines_.size()) return absl::string_view();
  return lua_script_lines_[line_num - 1];
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLuaAttrEnricher, CommonRecoLuaAttrEnricher)

}  // namespace platform
}  // namespace ks
