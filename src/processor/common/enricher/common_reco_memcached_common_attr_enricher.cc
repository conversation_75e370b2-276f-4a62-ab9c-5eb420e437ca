#include <memory>
#include <vector>

#include "dragon/src/processor/common/enricher/common_reco_memcached_common_attr_enricher.h"
#include "third_party/abseil/absl/strings/str_cat.h"
#include "third_party/abseil/absl/strings/str_join.h"

namespace ks {
namespace platform {
using ks::infra::memc::ClientOptions;
using ks::infra::memc::MemcClient;
using ks::infra::memc::MemcErrorDescription;

void CommonRecoMemcachedCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  auto query_key = GetStringProcessorParameter(context, query_key_);
  if (query_key.empty()) {
    CL_LOG_WARNING("memcached", "empty_key")
        << "enrich common attr from memcached cancelled, no key found in common attr "
        << json_string_value(query_key_->get());
    return;
  }
  std::string mc_key = absl::StrCat(key_prefix_, query_key);
  std::string mc_value;
  auto ret = mc_client_->Get(mc_key, &mc_value, timeout_ms_);
  if (ret) {
    std::string category = absl::StrCat(cluster_name_, ": ", MemcErrorDescription(ret));
    CL_LOG_ERROR("memcached", category)
        << "failed to get value from memcached, key: " << mc_key << ", error: " << MemcErrorDescription(ret)
        << ", query_key_: " << json_string_value(query_key_->get());
    return;
  }
  context->SetStringCommonAttr(output_attr_name_, std::move(mc_value));
}

bool CommonRecoMemcachedCommonAttrEnricher::InitProcessor() {
  // parse config
  int timeout_ms = config()->GetInt("timeout_ms", 10);
  if (timeout_ms <= 0) {
    CL_LOG(ERROR) << "invalid timeout_ms: " << timeout_ms << ", must be greater than 0";
    return false;
  }
  timeout_ms_ = static_cast<uint64_t>(timeout_ms);

  std::vector<std::string> mc_cluster_names;
  auto name = config()->Get("cluster_name");
  if (name == nullptr) {
    CL_LOG(ERROR) << "enrich common attr from memcached cancelled, no cluster name";
    return false;
  }
  if (name->IsArray()) {
    for (auto it : name->array()) {
      if (!it->IsString()) {
        CL_LOG(ERROR) << "expect element of cluster_name as string";
        return false;
      }
      mc_cluster_names.push_back(it->StringValue());
    }
  } else if (name->IsString()) {
    mc_cluster_names.push_back(name->StringValue());
  } else {
    CL_LOG(ERROR) << "invalid cluster_name config, must be string or string array";
    return false;
  }
  cluster_name_ = absl::StrJoin(mc_cluster_names, ",");

  key_prefix_ = config()->GetString("key_prefix", "");

  output_attr_name_ = config()->GetString("output_attr_name", "");
  if (output_attr_name_.empty()) {
    CL_LOG(ERROR) << "output_attr_name is empty";
    return false;
  }

  query_key_ = config()->Get("query_key");
  if (query_key_ == nullptr) {
    CL_LOG(ERROR) << "query_key is not set";
    return false;
  }

  // create client
  std::unique_ptr<ClientOptions> mc_options = nullptr;
  if (mc_cluster_names.size() == 1) {
    mc_options = std::make_unique<ClientOptions>(mc_cluster_names[0]);
  } else if (mc_cluster_names.size() == 2) {
    mc_options = std::make_unique<ClientOptions>(std::make_pair(mc_cluster_names[0], mc_cluster_names[1]));
  } else {
    CL_LOG(ERROR) << "only support single or dual cluster names";
    return false;
  }

  int conn_pool_init_size = config()->GetInt("conn_pool_init_size", 32);
  int conn_pool_max_size = config()->GetInt("conn_pool_max_size", 64);
  mc_client_ = MemcClient::CreateSimplePool(*mc_options, conn_pool_init_size, conn_pool_max_size);
  if (mc_client_ == nullptr) {
    CL_LOG(ERROR) << "create memcached client failed for cluster " << cluster_name_;
    return false;
  }

  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoMemcachedCommonAttrEnricher,
                 CommonRecoMemcachedCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
