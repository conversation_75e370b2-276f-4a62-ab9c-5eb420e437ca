#include "dragon/src/processor/common/enricher/common_reco_dot_product_enricher.h"

#include <algorithm>
#include <utility>
#include <vector>

#include "abseil/absl/strings/string_view.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

void CommonRecoDotProductEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  if (!x_attr_accessor_) {
    x_attr_accessor_ = context->GetCommonAttrAccessor(x_attr_);
  }

  if (!y_is_common_attr_) {
    HandleItemLevel(context, begin, end);
  } else {
    HandleCommonLevel(context, begin, end);
  }
}

void CommonRecoDotProductEnricher::HandleItemLevel(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  y_attr_accessor_ = context->GetItemAttrAccessor(y_attr_);
  save_result_to_accessor_ = context->GetItemAttrAccessor(save_result_to_);

  std::for_each(begin, end, [this, context](const CommonRecoResult &item) {
    AttrType x_value_type = x_attr_accessor_->value_type;
    switch (x_value_type) {
      case AttrType::INT_LIST: {
        if (auto x_int_list = context->GetIntListCommonAttr(x_attr_accessor_)) {
          if (auto y_int_list = context->GetIntListItemAttr(item, y_attr_accessor_)) {
            if (x_int_list->size() == y_int_list->size()) {
              int64 result = 0;
              for (int i = 0; i < x_int_list->size(); ++i) {
                result += x_int_list->at(i) * y_int_list->at(i);
              }
              context->SetIntItemAttr(item, save_result_to_accessor_, result);
            } else {
              CL_LOG(ERROR) << "size miss match, x_attr size: " << x_int_list->size()
                            << " y_attr size: " << y_int_list->size();
            }
          } else if (auto y_float_list = context->GetDoubleListItemAttr(item, y_attr_accessor_)) {
            if (x_int_list->size() == y_float_list->size()) {
              double result = 0;
              for (int i = 0; i < x_int_list->size(); ++i) {
                result += x_int_list->at(i) * y_float_list->at(i);
              }
              context->SetDoubleItemAttr(item, save_result_to_accessor_, result);
            } else {
              CL_LOG(ERROR) << "size miss match, x_attr size: " << x_int_list->size()
                            << " y_attr size: " << y_int_list->size();
            }
          } else {
            CL_LOG(ERROR) << "unsupported value type for y_attr, expected int_list or double_list but got: "
                          << RecoUtil::GetAttrTypeName(y_attr_accessor_->value_type);
          }
        } else {
          CL_LOG(WARNING) << "x_attr is empty";
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        if (auto x_float_list = context->GetDoubleListCommonAttr(x_attr_accessor_)) {
          if (auto y_int_list = context->GetIntListItemAttr(item, y_attr_accessor_)) {
            if (x_float_list->size() == y_int_list->size()) {
              double result = 0;
              for (int i = 0; i < x_float_list->size(); ++i) {
                result += x_float_list->at(i) * y_int_list->at(i);
              }
              context->SetDoubleItemAttr(item, save_result_to_accessor_, result);
            } else {
              CL_LOG(ERROR) << "size miss match, x_attr size: " << x_float_list->size()
                            << " y_attr size: " << y_int_list->size();
            }
          } else if (auto y_float_list = context->GetDoubleListItemAttr(item, y_attr_accessor_)) {
            if (x_float_list->size() == y_float_list->size()) {
              double result = 0;
              for (int i = 0; i < x_float_list->size(); ++i) {
                result += x_float_list->at(i) * y_float_list->at(i);
              }
              context->SetDoubleItemAttr(item, save_result_to_accessor_, result);
            } else {
              CL_LOG(ERROR) << "size miss match, x_attr size: " << x_float_list->size()
                            << " y_attr size: " << y_float_list->size();
            }
          } else {
            CL_LOG(ERROR) << "unsupported value type for y_attr, expected int_list or double_list but got: "
                          << RecoUtil::GetAttrTypeName(y_attr_accessor_->value_type);
          }
        } else {
          CL_LOG(WARNING) << "x_attr is empty";
        }
        break;
      }
      default: {
        CL_LOG(ERROR) << "unsupported value type for x_attr, expected int_list or double_list but got: "
                      << RecoUtil::GetAttrTypeName(x_value_type);
      }
    }
  });
}

void CommonRecoDotProductEnricher::HandleCommonLevel(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
  y_attr_accessor_ = context->GetCommonAttrAccessor(y_attr_);
  save_result_to_accessor_ = context->GetCommonAttrAccessor(save_result_to_);

  AttrType x_value_type = x_attr_accessor_->value_type;
  switch (x_value_type) {
    case AttrType::INT_LIST: {
      if (auto x_int_list = context->GetIntListCommonAttr(x_attr_accessor_)) {
        if (auto y_int_list = context->GetIntListCommonAttr(y_attr_accessor_)) {
          if (x_int_list->size() == y_int_list->size()) {
            int64 result = 0;
            for (int i = 0; i < x_int_list->size(); ++i) {
              result += x_int_list->at(i) * y_int_list->at(i);
            }
            context->SetIntCommonAttr(save_result_to_accessor_, result);
          } else {
            CL_LOG(ERROR) << "size miss match, x_attr size: " << x_int_list->size()
                          << " y_attr size: " << y_int_list->size();
          }
        } else if (auto y_float_list = context->GetDoubleListCommonAttr(y_attr_accessor_)) {
          if (x_int_list->size() == y_float_list->size()) {
            double result = 0;
            for (int i = 0; i < x_int_list->size(); ++i) {
              result += x_int_list->at(i) * y_float_list->at(i);
            }
            context->SetDoubleCommonAttr(save_result_to_accessor_, result);
          } else {
            CL_LOG(ERROR) << "size miss match, x_attr size: " << x_int_list->size()
                          << " y_attr size: " << y_int_list->size();
          }
        } else {
          CL_LOG(ERROR) << "unsupported value type for y_attr, expected int_list or double_list but got: "
                        << RecoUtil::GetAttrTypeName(y_attr_accessor_->value_type);
        }
      } else {
        CL_LOG(WARNING) << "x_attr is empty";
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      if (auto x_float_list = context->GetDoubleListCommonAttr(x_attr_accessor_)) {
        if (auto y_int_list = context->GetIntListCommonAttr(y_attr_accessor_)) {
          if (x_float_list->size() == y_int_list->size()) {
            double result = 0;
            for (int i = 0; i < x_float_list->size(); ++i) {
              result += x_float_list->at(i) * y_int_list->at(i);
            }
            context->SetDoubleCommonAttr(save_result_to_accessor_, result);
          } else {
            CL_LOG(ERROR) << "size miss match, x_attr size: " << x_float_list->size()
                          << " y_attr size: " << y_int_list->size();
          }
        } else if (auto y_float_list = context->GetDoubleListCommonAttr(y_attr_accessor_)) {
          if (x_float_list->size() == y_float_list->size()) {
            double result = 0;
            for (int i = 0; i < x_float_list->size(); ++i) {
              result += x_float_list->at(i) * y_float_list->at(i);
            }
            context->SetDoubleCommonAttr(save_result_to_accessor_, result);
          } else {
            CL_LOG(ERROR) << "size miss match, x_attr size: " << x_float_list->size()
                          << " y_attr size: " << y_float_list->size();
          }
        } else {
          CL_LOG(ERROR) << "unsupported value type for y_attr, expected int_list or double_list but got: "
                        << RecoUtil::GetAttrTypeName(y_attr_accessor_->value_type);
        }
      } else {
        CL_LOG(WARNING) << "x_attr is empty";
      }
      break;
    }
    default: {
      CL_LOG(ERROR) << "unsupported value type for x_attr, expected int_list or double_list but got: "
                    << RecoUtil::GetAttrTypeName(x_value_type);
    }
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDotProductEnricher, CommonRecoDotProductEnricher)

}  // namespace platform
}  // namespace ks
