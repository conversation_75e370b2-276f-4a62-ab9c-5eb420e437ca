#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/service_black_list_util.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/kv_client_wrapper/redis_cache_client.h"
#include "serving_base/kv_client_wrapper/redis_client_waiter.h"

namespace ks {
namespace platform {

class CommonRecoRedisItemAttrBatchEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRedisItemAttrBatchEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const override {
    return is_async_;
  }

 private:
  struct RedisAttrConfig {
    std::string key_attr;
    std::string save_value_attr;
    const base::Json *key_prefix = nullptr;
  };
  bool InitProcessor() override {
    cluster_name_ = config()->GetString("cluster_name", "");
    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    is_async_ = config()->GetBoolean("is_async", false);

    std::string cache_name = config()->GetString("cache_name", cluster_name_);
    int cache_bits = config()->GetInt("cache_bits", 0);
    int cache_delay_delete_ms = config()->GetInt("cache_delay_delete_ms", 10 * 1000);
    int cache_expire_second = config()->GetInt("cache_expire_second", 60 * 60);

    auto attrs_config = config()->Get("attrs_config");
    if (!attrs_config || !attrs_config->IsArray()) {
      LOG(ERROR) << "OfflineKsibDenseMatchEnricher init failed! "
                 << "attrs_config should be an object array";
      return false;
    }
    for (const auto *attr_config : attrs_config->array()) {
      if (!attr_config->IsObject()) {
        LOG(ERROR) << "CommonRecoRedisItemAttrBatchEnricher init failed! Values of 'attrs_config' "
                   << "should be an object";
        return false;
      }
      RedisAttrConfig redis_attr_config;
      if (!ExtraceRedisAttrConfig(&redis_attr_config, attr_config)) {
        return false;
      }
      redis_attr_config_list_.emplace_back(std::move(redis_attr_config));
    }

    redis_client_ = std::make_unique<base::RedisCacheClient>();
    if (!redis_client_->Initialize(cluster_name_, timeout_ms_, cache_name, cache_bits, cache_delay_delete_ms,
                                   cache_expire_second)) {
      LOG(ERROR) << "CommonRecoRedisItemAttrBatchEnricher init failed!"
                 << "redis client init failed, cluster_name: " << cluster_name_
                 << ", cache_name: " << cache_name << ", cache_bits: " << cache_bits
                 << ", cache_delay_delete_ms: " << cache_delay_delete_ms
                 << ", cache_expire_second: " << cache_expire_second;
      return false;
    }

    return true;
  }

  bool ExtraceRedisAttrConfig(RedisAttrConfig *attr_config, const base::Json *config) {
    attr_config->key_attr = config->GetString("key_attr");
    if (attr_config->key_attr.empty()) {
      LOG(ERROR) << "CommonRecoRedisItemAttrBatchEnricher init failed! Values of 'key_attr' "
                 << "is not valid";
      return false;
    }

    attr_config->save_value_attr = config->GetString("value_attr");
    if (attr_config->save_value_attr.empty()) {
      LOG(ERROR) << "CommonRecoRedisItemAttrBatchEnricher init failed! Values of 'value_attr' "
                 << "is not valid";
      return false;
    }

    attr_config->key_prefix = config->Get("key_prefix");
    return true;
  }

  int CheckAndGetTimeoutMs(MutableRecoContextInterface *context) {
    const std::string &kess_blacklist_key = GetStringProcessorParameter(context, "kess_blacklist_key", "");
    int timeout_ms = KessBlackListUtil::GetInstance()->GetKessBlackListTimeoutMs(
      context, kess_blacklist_key, cluster_name_, config()->GetInt("timeout_ms", 10), GetName());
    return timeout_ms;
  }

  void PreRequest(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                  std::vector<std::string> *string_keys, std::vector<std::string> *value_attr_list,
                  std::vector<std::vector<std::pair<int, const CommonRecoResult>>> *index_to_result_list);

  void GetItemAttrFromRedisResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end);
  void AsyncGetItemAttrFromRedisResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end);

 private:
  std::string cluster_name_;
  int timeout_ms_;
  bool is_async_ = false;
  std::vector<RedisAttrConfig> redis_attr_config_list_;
  std::unique_ptr<base::RedisCacheClient> redis_client_;
  std::vector<std::vector<std::shared_ptr<::google::protobuf::Message>>> msgs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRedisItemAttrBatchEnricher);
};

}  // namespace platform
}  // namespace ks
