#include "dragon/src/processor/common/enricher/common_reco_rodis_no_proxy_attr_enricher.h"

#include <utility>

#include "absl/strings/str_format.h"

namespace ks {
namespace platform {

void CommonRecoRodisNoProxyAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }

  if (valid_duration_ms_ < 0) {
    request_.clear_after_ts();
  } else {
    request_.set_after_ts(context->GetRequestTime() - valid_duration_ms_);
  }
  request_.set_before_ts(context->GetRequestTime());

  request_.set_key(key);

  if (client_ == nullptr) {
    VLOG(1) << "empty client";
    return;
  }
  kuaishou::ds::TimeListGetResponse response;
  auto result = client_->TimeListGet(req_options_, &request_, &response);
  if (!result.ok()) {
    CL_LOG_WARNING("rodis_timelist", result.error_details())
        << "timelist get failed: " << result.error_details();
    return;
  }
  if (response.item_size() == 1) {
    const auto &item = response.item(0);
    VLOG(1) << "Get response: size=" << item.data().size() << ", ts=" << item.ts()
            << ", err_code=" << response.err_code();
    context->SetStringCommonAttr(value_attr_, item.data());
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisNoProxyAttrEnricher, CommonRecoRodisNoProxyAttrEnricher)

}  // namespace platform
}  // namespace ks
