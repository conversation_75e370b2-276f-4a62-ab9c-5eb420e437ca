#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "ks/reco/sphinx/client/parameter_client.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoSphinxParameterEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoSphinxParameterEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    reco_biz_ = config()->GetString("reco_biz", "");
    if (reco_biz_.empty()) {
      LOG(ERROR) << "CommonRecoSphinxParameterEnricher init failed: reco_biz can not be empty.";
      return false;
    }

    kess_service_ = config()->Get("kess_service");
    if (!kess_service_) {
      LOG(ERROR) << "CommonRecoSphinxParameterEnricher init failed: kess_service can not be empty.";
      return false;
    }

    model_name_ = config()->Get("model_name");
    if (!model_name_) {
      LOG(ERROR) << "CommonRecoSphinxParameterEnricher init failed: model_name can not be empty.";
      return false;
    }

    const base::Json *params_config = config()->Get("params");
    if (params_config) {
      if (!RecoUtil::ExtractAttrListFromJsonConfig(params_config, &params_)) {
        LOG(ERROR) << "CommonRecoSphinxParameterEnricher init failed: params extract failed."
                   << params_config->ToString();
        return false;
      }
    } else {
      LOG(ERROR) << "CommonRecoSphinxParameterEnricher init failed: params is empty.";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", 30);
    ignore_warning_ = config()->GetBoolean("ignore_warning", false);

    return true;
  }

 private:
  int timeout_ms_;
  const base::Json *kess_service_;
  const base::Json *model_name_;
  std::string reco_biz_;
  std::unordered_map<std::string, std::string> params_;
  ks::reco::sphinx::ParameterClient parameter_client_;
  bool ignore_warning_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoSphinxParameterEnricher);
};

}  // namespace platform
}  // namespace ks
