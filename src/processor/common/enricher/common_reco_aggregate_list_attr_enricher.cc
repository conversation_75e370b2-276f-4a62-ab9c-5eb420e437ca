#include "dragon/src/processor/common/enricher/common_reco_aggregate_list_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoAggregateListAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  for (const auto &mapping : attr_aggregate_mapping_) {
    HandleSingleAttrAggregate(context, begin, end, mapping);
  }
}

void CommonRecoAggregateListAttrEnricher::HandleSingleAttrAggregate(MutableRecoContextInterface *context,
                                                                    RecoResultConstIter begin,
                                                                    RecoResultConstIter end,
                                                                    const AttrAggregateMapping &mapping) {
  if (for_common_) {
    HandleCommonLevelAggregate(context, mapping);
  } else {
    HandleItemLevelAggregate(context, begin, end, mapping);
  }
}

void CommonRecoAggregateListAttrEnricher::HandleCommonLevelAggregate(MutableRecoContextInterface *context,
                                                                     const AttrAggregateMapping &mapping) {
  AttrType attr_value_type = context->GetCommonAttrType(mapping.from_attr);
  CommonAttr *getter = context->GetCommonAttrAccessor(mapping.from_attr);
  CommonAttr *setter = context->GetCommonAttrAccessor(mapping.to_attr);

  switch (attr_value_type) {
    case AttrType::INT_LIST: {
      auto p = context->GetIntListCommonAttr(getter);
      if (!p) return;
      switch (mapping.agg_type) {
        case AggType::COUNT:
          context->SetIntCommonAttr(setter, p->size());
          break;
        case AggType::MIN: {
          if (!p->empty()) {
            int64 min_value = *std::min_element(p->begin(), p->end());
            context->SetIntCommonAttr(setter, min_value);
          }
          break;
        }
        case AggType::MAX: {
          if (!p->empty()) {
            int64 max_value = *std::max_element(p->begin(), p->end());
            context->SetIntCommonAttr(setter, max_value);
          }
          break;
        }
        case AggType::SUM: {
          if (!p->empty()) {
            int64 sum_value = std::accumulate(p->begin(), p->end(), 0);
            context->SetIntCommonAttr(setter, sum_value);
          }
          break;
        }
        case AggType::AVG: {
          if (!p->empty()) {
            int64 avg_value = std::accumulate(p->begin(), p->end(), 0) / p->size();
            context->SetIntCommonAttr(setter, avg_value);
          }
          break;
        }
        default:
          CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate int_list common attr: " << mapping.from_attr
                                        << " with aggregate type: " << static_cast<int>(mapping.agg_type);
          break;
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      auto p = context->GetDoubleListCommonAttr(getter);
      if (!p) return;
      switch (mapping.agg_type) {
        case AggType::COUNT:
          context->SetIntCommonAttr(setter, p->size());
          break;
        case AggType::MIN: {
          if (!p->empty()) {
            double min_value = *std::min_element(p->begin(), p->end());
            context->SetDoubleCommonAttr(setter, min_value);
          }
          break;
        }
        case AggType::MAX: {
          if (!p->empty()) {
            double max_value = *std::max_element(p->begin(), p->end());
            context->SetDoubleCommonAttr(setter, max_value);
          }
          break;
        }
        case AggType::SUM: {
          if (!p->empty()) {
            double sum_value = std::accumulate(p->begin(), p->end(), 0.0);
            context->SetDoubleCommonAttr(setter, sum_value);
          }
          break;
        }
        case AggType::AVG: {
          if (!p->empty()) {
            double avg_value = std::accumulate(p->begin(), p->end(), 0.0) / p->size();
            context->SetDoubleCommonAttr(setter, avg_value);
          }
          break;
        }
        default:
          CL_LOG_EVERY_N(WARNING, 1000)
              << "Unsupported aggregate dobule_list common attr: " << mapping.from_attr
              << " with aggregate type: " << static_cast<int>(mapping.agg_type);
          break;
      }
      break;
    }
    case AttrType::STRING_LIST: {
      auto p = context->GetStringListCommonAttr(getter);
      if (!p) return;
      switch (mapping.agg_type) {
        case AggType::COUNT:
          context->SetIntCommonAttr(setter, p->size());
          break;
        default:
          CL_LOG_EVERY_N(WARNING, 1000)
              << "Unsupported aggregate string_list common attr: " << mapping.from_attr
              << " with aggregate type: " << static_cast<int>(mapping.agg_type);
          break;
      }
      break;
    }
    case AttrType::STRING: {
      auto p = context->GetStringCommonAttr(getter);
      if (!p) return;
      switch (mapping.agg_type) {
        case AggType::COUNT:
          context->SetIntCommonAttr(setter, p->size());
          break;
        default:
          CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate string common attr: " << mapping.from_attr
                                        << " with aggregate type: " << static_cast<int>(mapping.agg_type);
          break;
      }
      break;
    }
    default:
      CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate common attr type, name: " << mapping.from_attr;
      break;
  }
}

void CommonRecoAggregateListAttrEnricher::HandleItemLevelAggregate(MutableRecoContextInterface *context,
                                                                   RecoResultConstIter begin,
                                                                   RecoResultConstIter end,
                                                                   const AttrAggregateMapping &mapping) {
  AttrType attr_value_type = context->GetItemAttrType(mapping.from_attr);
  ItemAttr *getter = context->GetItemAttrAccessor(mapping.from_attr);
  ItemAttr *setter = context->GetItemAttrAccessor(mapping.to_attr);

  for (auto iter = begin; iter != end; ++iter) {
    switch (attr_value_type) {
      case AttrType::INT_LIST: {
        auto p = context->GetIntListItemAttr(*iter, getter);
        if (!p) break;
        switch (mapping.agg_type) {
          case AggType::COUNT:
            context->SetIntItemAttr(*iter, setter, p->size());
            break;
          case AggType::MIN: {
            if (!p->empty()) {
              int64 min_value = *std::min_element(p->begin(), p->end());
              context->SetIntItemAttr(*iter, setter, min_value);
            }
            break;
          }
          case AggType::MAX: {
            if (!p->empty()) {
              int64 max_value = *std::max_element(p->begin(), p->end());
              context->SetIntItemAttr(*iter, setter, max_value);
            }
            break;
          }
          case AggType::SUM: {
            if (!p->empty()) {
              int64 sum_value = std::accumulate(p->begin(), p->end(), 0);
              context->SetIntItemAttr(*iter, setter, sum_value);
            }
            break;
          }
          case AggType::AVG: {
            if (!p->empty()) {
              int64 avg_value = std::accumulate(p->begin(), p->end(), 0) / p->size();
              context->SetIntItemAttr(*iter, setter, avg_value);
            }
            break;
          }
          default:
            CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate int_list item attr: " << mapping.from_attr
                                          << " with aggregate type: " << static_cast<int>(mapping.agg_type);
            break;
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        auto p = context->GetDoubleListItemAttr(*iter, getter);
        if (!p) break;
        switch (mapping.agg_type) {
          case AggType::COUNT:
            context->SetIntItemAttr(*iter, setter, p->size());
            break;
          case AggType::MIN: {
            if (!p->empty()) {
              double min_value = *std::min_element(p->begin(), p->end());
              context->SetDoubleItemAttr(*iter, setter, min_value);
            }
            break;
          }
          case AggType::MAX: {
            if (!p->empty()) {
              double max_value = *std::max_element(p->begin(), p->end());
              context->SetDoubleItemAttr(*iter, setter, max_value);
            }
            break;
          }
          case AggType::SUM: {
            if (!p->empty()) {
              double sum_value = std::accumulate(p->begin(), p->end(), 0.0);
              context->SetDoubleItemAttr(*iter, setter, sum_value);
            }
            break;
          }
          case AggType::AVG: {
            if (!p->empty()) {
              double avg_value = std::accumulate(p->begin(), p->end(), 0.0) / p->size();
              context->SetDoubleItemAttr(*iter, setter, avg_value);
            }
            break;
          }
          default:
            CL_LOG_EVERY_N(WARNING, 1000)
                << "Unsupported aggregate double_list item attr: " << mapping.from_attr
                << " with aggregate type: " << static_cast<int>(mapping.agg_type);
            break;
        }
        break;
      }
      case AttrType::STRING_LIST: {
        auto p = context->GetStringListItemAttr(*iter, getter);
        if (!p) break;
        switch (mapping.agg_type) {
          case AggType::COUNT:
            context->SetIntItemAttr(*iter, setter, p->size());
            break;
          default:
            CL_LOG_EVERY_N(WARNING, 1000)
                << "Unsupported aggregate string_list item attr: " << mapping.from_attr
                << " with aggregate type: " << static_cast<int>(mapping.agg_type);
            break;
        }
        break;
      }
      case AttrType::STRING: {
        auto p = context->GetStringItemAttr(*iter, getter);
        if (!p) break;
        switch (mapping.agg_type) {
          case AggType::COUNT:
            context->SetIntItemAttr(*iter, setter, p->size());
            break;
          default:
            CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate string item attr: " << mapping.from_attr
                                          << " with aggregate type: " << static_cast<int>(mapping.agg_type);
            break;
        }
        break;
      }
      default:
        CL_LOG_EVERY_N(WARNING, 1000) << "Unsupported aggregate item attr type, name: " << mapping.from_attr;
        break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAggregateListAttrEnricher, CommonRecoAggregateListAttrEnricher)

}  // namespace platform
}  // namespace ks
