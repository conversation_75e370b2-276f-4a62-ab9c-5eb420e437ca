#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

/**
  {
    "type_name": "CommonRecoTransformItemAttrEnricher",
    "mappings": [{
      "check_attr_name": "cluster_id", // 带检查的属性名
      "check_attr_type": "int",
      "output_attr_name": "in_browsed_cluster",
      "output_attr_type": "int", // output_attr 的值类型,
                                  // 仅支持: int/double/string
      "output_default_value": -1, // 当所有的 rule 都无法匹配时将使用该默认值
      // 每个元素表示一个 mapping rule
      "rules": [{
        // check_values 中可包含 int 和 string,
        // string 如果为 {{ }} 格式则当作 CommonAttr 处理,
        // 如果有冲突, 后定义者有效
        "check_values": ["{{recent_browsed_cluster}}", 108],
        "output_value": 0,
      }, {
        // 当 check_attr_type 为 int/double 时可指定一个检查范围
        "check_range": {
          "lower_bound": 200, // 包含，可缺省
          "upper_bound": 500, // 不包含，可缺省
        },
        "output_value": 1,
      }]
    }]
  }
 */

class CommonRecoTransformItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoTransformItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct ValueRange {
    bool has_lower_bound = false;
    bool has_upper_bound = false;
    double lower_bound = 0.0;
    double upper_bound = 0.0;
  };

  // AttrMapping 用于记录配置中 mappings 各项的详细信息
  struct AttrMapping {
   public:
    std::string check_attr;
    AttrType check_attr_type = AttrType::UNKNOWN;
    std::string output_name;
    AttrType output_type = AttrType::UNKNOWN;
    base::Json *rules_config = nullptr;

    bool has_default_value = false;
    int64 default_int_value = 0;
    double default_double_value = 0.0;
    std::string default_string_value = "";

    ~AttrMapping() {
      if (rules_config) {
        delete rules_config;
      }
      rules_config = nullptr;
    }

    void ClearMappingValue() {
      int_values.Clear();
      double_values.Clear();
      string_values.Clear();
      int_ranges.clear();
      double_ranges.clear();
      string_ranges.clear();
    }

    void InsertValueMapping(int64 value, base::Json *output_value) {
      uint64 key = (uint64)value;
      if (output_type == AttrType::INT) {
        auto v = output_value->IntValue((int64)0);
        int_values.Insert(key, v);
      } else if (output_type == AttrType::FLOAT) {
        auto v = output_value->NumberValue(0.0);
        double_values.Insert(key, v);
      } else if (output_type == AttrType::STRING) {
        auto v = output_value->StringValue();
        string_values.Insert(key, v);
      }
    }

    void InsertValueMapping(absl::string_view key, base::Json *output_value) {
      InsertValueMapping(base::CityHash64(key.data(), key.size()), output_value);
    }

    void AddValueRange(const ValueRange &range, base::Json *output_value) {
      if (output_type == AttrType::INT) {
        auto v = output_value->IntValue((int64)0);
        int_ranges.emplace_back(range, v);
      } else if (output_type == AttrType::FLOAT) {
        auto v = output_value->NumberValue(0.0);
        double_ranges.emplace_back(range, v);
      } else if (output_type == AttrType::STRING) {
        auto v = output_value->StringValue();
        string_ranges.emplace_back(range, v);
      }
    }

    void GenItemAttr(int64 value, MutableRecoContextInterface *context, uint64 item_key) {
      GenItemAttrHelper((uint64)value, (double)value, context, item_key);
    }

    void GenItemAttr(double value, MutableRecoContextInterface *context, uint64 item_key) {
      GenItemAttrHelper(-1, value, context, item_key);
    }

    void GenItemAttr(absl::string_view value, MutableRecoContextInterface *context, uint64 item_key) {
      GenItemAttrHelper(base::CityHash64(value.data(), value.size()), 0.0, context, item_key);
    }

   private:
    base::DynamicDuplicateHash<int64> int_values;
    base::DynamicDuplicateHash<double> double_values;
    base::DynamicDuplicateHash<std::string> string_values;

    std::vector<std::pair<ValueRange, int64>> int_ranges;
    std::vector<std::pair<ValueRange, double>> double_ranges;
    std::vector<std::pair<ValueRange, std::string>> string_ranges;

    void GenItemAttrHelper(uint64 sign, double value, MutableRecoContextInterface *context, uint64 item_key) {
      if (output_type == AttrType::INT) {
        if (GenIntAttrFromValueSet(sign, context, item_key)) {
          return;
        }
        if (GenIntAttrFromValueRange(value, context, item_key)) {
          return;
        }
        if (has_default_value) {
          context->SetIntItemAttr(item_key, output_name, default_int_value);
          return;
        }
        VLOG(100) << "cannot get transform value for int item attr [" << output_name
                  << "], item key: " << item_key << ", checked attr: " << check_attr;
      } else if (output_type == AttrType::FLOAT) {
        if (GenDoubleAttrFromValueSet(sign, context, item_key)) {
          return;
        }
        if (GenDoubleAttrFromValueRange(value, context, item_key)) {
          return;
        }
        if (has_default_value) {
          context->SetDoubleItemAttr(item_key, output_name, default_double_value);
          return;
        }
        VLOG(100) << "cannot get transform value for double item attr [" << output_name
                  << "], item key: " << item_key << ", checked attr: " << check_attr;
      } else if (output_type == AttrType::STRING) {
        if (GenStringAttrFromValueSet(sign, context, item_key)) {
          return;
        }
        if (GenStringAttrFromValueRange(value, context, item_key)) {
          return;
        }
        if (has_default_value) {
          context->SetStringItemAttr(item_key, output_name, default_string_value);
          return;
        }
        VLOG(100) << "cannot get transform value for string item attr [" << output_name
                  << "], item key: " << item_key << ", checked attr: " << check_attr;
      }
    }

    bool GenIntAttrFromValueSet(uint64 key, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::STRING) {
        auto *value = int_values.Get(key);
        if (value) {
          context->SetIntItemAttr(item_key, output_name, *value);
          return true;
        }
      }
      return false;
    }

    bool GenDoubleAttrFromValueSet(uint64 key, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::STRING) {
        auto *value = double_values.Get(key);
        if (value) {
          context->SetDoubleItemAttr(item_key, output_name, *value);
          return true;
        }
      }
      return false;
    }

    bool GenStringAttrFromValueSet(uint64 key, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::STRING) {
        auto *value = string_values.Get(key);
        if (value) {
          context->SetStringItemAttr(item_key, output_name, *value);
          return true;
        }
      }
      return false;
    }

    bool GenIntAttrFromValueRange(double value, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::FLOAT) {
        for (const auto &pr : int_ranges) {
          const auto &range = pr.first;
          if (range.has_lower_bound && range.lower_bound > value) {
            continue;
          }
          if (range.has_upper_bound && range.upper_bound <= value) {
            continue;
          }
          context->SetIntItemAttr(item_key, output_name, pr.second);
          return true;
        }
      }
      return false;
    }

    bool GenDoubleAttrFromValueRange(double value, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::FLOAT) {
        for (const auto &pr : double_ranges) {
          const auto &range = pr.first;
          if (range.has_lower_bound && range.lower_bound > value) {
            continue;
          }
          if (range.has_upper_bound && range.upper_bound <= value) {
            continue;
          }
          context->SetDoubleItemAttr(item_key, output_name, pr.second);
          return true;
        }
      }
      return false;
    }

    bool GenStringAttrFromValueRange(double value, MutableRecoContextInterface *context, uint64 item_key) {
      if (check_attr_type == AttrType::INT || check_attr_type == AttrType::FLOAT) {
        for (const auto &pr : string_ranges) {
          const auto &range = pr.first;
          if (range.has_lower_bound && range.lower_bound > value) {
            continue;
          }
          if (range.has_upper_bound && range.upper_bound <= value) {
            continue;
          }
          context->SetStringItemAttr(item_key, output_name, pr.second);
          return true;
        }
      }
      return false;
    }
  };

  bool InitProcessor() override;

  bool CheckMappingConfig(base::Json *mapping_cfg);

  void ResolveValueSetMappingRules(AttrMapping *mapping, int index, ReadableRecoContextInterface *context);

  void ResolveValueRangeMappingRules(AttrMapping *mapping, int index, ReadableRecoContextInterface *context);

  void BuildAttrsMapping(ReadableRecoContextInterface *context);

  base::AutoDeleteHash<AttrMapping> attrs_mapping_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTransformItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
