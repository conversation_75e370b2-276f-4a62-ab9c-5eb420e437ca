#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoReadonlyAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoReadonlyAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    RecoUtil::ExtractStringListFromJsonConfig(config()->Get("common_attrs"), &common_attrs_);
    RecoUtil::ExtractStringListFromJsonConfig(config()->Get("item_attrs"), &item_attrs_);
    return true;
  }

 private:
  std::vector<std::string> common_attrs_;
  std::vector<std::string> item_attrs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoReadonlyAttrEnricher);
};

}  // namespace platform
}  // namespace ks
