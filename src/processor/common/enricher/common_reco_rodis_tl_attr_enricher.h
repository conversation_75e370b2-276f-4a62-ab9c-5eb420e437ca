#pragma once

#include <string>
#include <cstdint>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/ds/proto/userprofile/rodis.kess.grpc.pb.h"

namespace ks {
namespace platform {

class CommonRecoRodisTlAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRodisTlAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const {
    return true;
  }

 private:
  bool InitProcessor() override {
    kess_name_ = config()->GetString("kess_name");
    if (kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoRodisTlAttrEnricher init failed! kess_name is required.";
      return false;
    }

    domain_ = config()->GetString("domain");
    if (domain_.empty()) {
      LOG(ERROR) << "CommonRecoRodisTlAttrEnricher init failed! domain is required.";
      return false;
    }

    payload_id_ = config()->GetInt("payload_id", payload_id_);
    if (payload_id_ < 0) {
      LOG(ERROR) << "CommonRecoRodisTlAttrEnricher init failed! payload_id is required.";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", timeout_ms_);

    key_attr_ = config()->GetString("key_attr");
    if (key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisTlAttrEnricher init failed! key_attr is required.";
      return false;
    }

    value_attr_ = config()->GetString("value_attr");
    if (value_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisTlAttrEnricher init failed! value_attr is required.";
      return false;
    }

    valid_duration_ms_ = config()->GetInt("valid_duration_ms", valid_duration_ms_);

    len_limit_ = config()->GetInt("len_limit", len_limit_);

    after_ts_ = config()->GetInt("after_ts", after_ts_);

    before_ts_ = config()->GetInt("before_ts", before_ts_);

    min_len_ = config()->GetInt("min_len", min_len_);

    request_.set_domain(domain_);
    request_.set_payload_id(payload_id_);
    request_.set_limit(len_limit_);
    request_.set_min_len(min_len_);

    return true;
  }

 private:
  std::string kess_name_;
  std::string domain_;
  int payload_id_ = -1;
  int timeout_ms_ = 10;
  int valid_duration_ms_ = -1;
  std::string key_attr_, value_attr_;
  int len_limit_ = 10000;
  int min_len_ = 1;
  int64_t after_ts_ = 0;
  int64_t before_ts_ = 0;

  ::kuaishou::ds::TimeListGetRequest request_;
  ::kuaishou::ds::TimeListGetResponse response_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRodisTlAttrEnricher);
};

}  // namespace platform
}  // namespace ks
