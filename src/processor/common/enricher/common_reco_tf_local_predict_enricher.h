#pragma once

#include <memory>
#include <utility>
#include <vector>
#include <string>
#include <mutex>
#include <algorithm>

#include "base/thread/thread_pool.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

#define GOOGLE_LOGGING
#include "tensorflow/core/public/session.h"
#include "tensorflow/cc/saved_model/loader.h"
#include "tensorflow/cc/saved_model/constants.h"
#include "tensorflow/cc/saved_model/tag_constants.h"
#include "tensorflow/cc/saved_model/signature_constants.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/protobuf/meta_graph.pb.h"
#include "learning/kuiba/base/shell_reader.h"
#include "serving_base/util/file.h"
#include "base/file/file_util.h"
#include "base/encoding/base64.h"
#include "tensorflow/core/util/stat_summarizer.h"

DECLARE_bool(enable_tf_local_predict_enricher_profiling);

namespace ks {
namespace platform {

class CommonRecoTfLocalPredictEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoTfLocalPredictEnricher() {}
  ~CommonRecoTfLocalPredictEnricher() {
    stop_ = true;
    thread_pool_->JoinAll();
    if (session_ != nullptr) {
      tensorflow::Status status = session_->Close();
      LOG(INFO) << "session close status: " << status.ToString();
    }
    if (!model_tgz_base_dir_.empty()) {
      LOG(INFO) << "delete model dir: " << model_tgz_base_dir_;
      base::file_util::Delete(model_tgz_base_dir_, true);
    }
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct TfInput {
    std::string tensor_alias;
    std::string tensor_name;
    std::string attr_type;  // 目前只支持 pb
    std::string attr_name;  // 目前只支持从 common attr 中取
  };

  bool InitProcessor() override {
    model_dir_ = config()->GetString("model_dir");
    std::string model_tgz = config()->GetString("model_tgz");
    if (model_dir_.empty() && model_tgz.empty()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! "
                 << "Missing config 'model_dir' or 'model_tgz'!";
      return false;
    }

    if (model_dir_.empty()) {  // prepare saved_model dir from tgz file
      static const std::string base64_prefix = "base64://";
      if (model_tgz.find(base64_prefix) == 0) {  // begin with base64_prefix
        std::string model_tgz_base64 = model_tgz.substr(base64_prefix.length());
        std::string model_tgz_raw;
        if (!base::Base64Decode(model_tgz_base64, &model_tgz_raw)) {
          LOG(ERROR)
              << "CommonRecoTfLocalPredictEnricher init failed! 'model_tgz' cannot be parsed as base64";
          return false;
        } else {
          model_tgz_base_dir_ = "/tmp/saved_models/" + std::to_string(base::GetTimestamp());
          base::file_util::CreateDirectory(model_tgz_base_dir_);
          std::string tgz_file_path = model_tgz_base_dir_ + "/saved_model.tgz";
          base::file_util::SetString(tgz_file_path, model_tgz_raw, false);
          LOG(INFO) << "decompressing model tgz: " << tgz_file_path;
          std::vector<std::string> lines;
          kuiba::ShellReader reader("tar xvzf " + tgz_file_path + " -C " + model_tgz_base_dir_);
          reader.GetAll(&lines);
          std::for_each(lines.begin(), lines.end(), [](const auto &line) { LOG(INFO) << line; });
          model_dir_ = model_tgz_base_dir_ + "/" + lines[0];
          LOG(INFO) << "saved_model dir: " << model_dir_;
        }
      } else {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! 'model_tgz' with unsupported scheme: "
                   << model_tgz;
        return false;
      }
    }

    // prepare session
    tensorflow::SessionOptions sess_options;
    bool use_per_session_threads = config()->GetBoolean("use_per_session_threads", false);
    int32 inter_op_parallelism_threads = config()->GetInt("inter_op_parallelism_threads", 0);
    int32 intra_op_parallelism_threads = config()->GetInt("intra_op_parallelism_threads", 0);
    LOG(INFO) << "use_per_session_threads: " << use_per_session_threads
              << ", inter_op_parallelism_threads: " << inter_op_parallelism_threads
              << ", intra_op_parallelism_threads: " << intra_op_parallelism_threads;
    sess_options.config.set_use_per_session_threads(use_per_session_threads);
    sess_options.config.set_inter_op_parallelism_threads(inter_op_parallelism_threads);
    sess_options.config.set_intra_op_parallelism_threads(intra_op_parallelism_threads);
    tensorflow::RunOptions run_options;
    tensorflow::SavedModelBundle bundle;
    tensorflow::Status status = tensorflow::LoadSavedModel(sess_options, run_options, model_dir_,
                                                           {tensorflow::kSavedModelTagServe}, &bundle);
    if (!status.ok()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Failed to load SavedModel: "
                 << status.ToString();
      return false;
    }
    session_ = std::move(bundle.session);
    auto iter = bundle.meta_graph_def.signature_def().find(tensorflow::kDefaultServingSignatureDefKey);
    if (iter == bundle.meta_graph_def.signature_def().end()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Cannot find '"
                 << tensorflow::kDefaultServingSignatureDefKey << "' signature_def in the SavedModel";
      return false;
    }
    tensorflow::SignatureDef signature = iter->second;

    tensorflow::CallableOptions options;
    tensorflow::CallableOptions update_network_options;
    if (FLAGS_enable_tf_local_predict_enricher_profiling) {
      options.mutable_run_options()->set_trace_level(tensorflow::RunOptions::FULL_TRACE);
    }
    // inputs map, alias -> {"attr_type": "xx", "attr_name": "yy"}
    auto *inputs = config()->Get("inputs");
    if (!inputs || !inputs->IsObject()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! "
                 << "Missing config 'inputs'!";
      return false;
    }
    // convert to tf_inputs_
    for (const auto &input : inputs->objects()) {
      if (!input.second->IsObject() || input.second->GetString("attr_type").empty() ||
          input.second->GetString("attr_name").empty()) {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! inputs should be a map whose shape is "
                      "alias -> {'attr_type': 'xx', 'attr_name': 'yy'}";
        return false;
      }
      const std::string &alias = input.first;
      auto iter = signature.inputs().find(alias);
      if (iter == signature.inputs().end()) {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Cannot find input alias '" << alias
                   << "' in signature_def";
        return false;
      }
      TfInput tf_input;
      tf_input.tensor_alias = alias;
      tf_input.tensor_name = iter->second.name();
      tf_input.attr_type = input.second->GetString("attr_type");
      tf_input.attr_name = input.second->GetString("attr_name");
      if (tf_input.attr_type != "pb" && tf_input.attr_type != "PB") {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Only support 'pb' attr_type as tf input";
        return false;
      }
      tf_inputs_.emplace_back(tf_input);
      options.add_feed(iter->second.name());
      update_network_options.add_feed(iter->second.name());
    }

    // output aliases array
    auto *outputs = config()->Get("outputs");
    if (!outputs || !outputs->IsArray()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! "
                 << "Missing config 'outputs'!";
      return false;
    }
    // convert to output tensor names
    for (const auto *output : outputs->array()) {
      if (!output->IsString()) {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! outputs has non-string value";
        return false;
      }
      const std::string &alias = output->StringValue();
      auto iter = signature.outputs().find(alias);
      if (iter == signature.outputs().end()) {
        LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Cannot find output alias '" << alias
                   << "' in signature_def";
        return false;
      }
      output_tensor_aliases_.emplace_back(alias);
      options.add_fetch(iter->second.name());
    }

    status = session_->MakeCallable(options, &callable_handle_);
    if (!status.ok()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Failed to make callable: "
                 << status.ToString();
      return false;
    }

    // get PULL_MODEL_VARIABLE tensor name
    tf_kuiba_update_model_key_ = config()->GetString("tf_kuiba_update_model_key", "PULL_MODEL_VARIABLE");
    auto it = signature.outputs().find(tf_kuiba_update_model_key_);
    if (it == signature.outputs().end()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Cannot find output alias '"
                 << tf_kuiba_update_model_key_ << "' in signature_def";
      return false;
    }

    update_network_options.add_fetch(it->second.name());
    status = session_->MakeCallable(update_network_options, &update_network_callable_handle_);
    if (!status.ok()) {
      LOG(ERROR) << "CommonRecoTfLocalPredictEnricher init failed! Failed to make update network callable "
                 << status.ToString();
      return false;
    }

    save_to_common_attr_ = config()->GetBoolean("save_to_common_attr", false);

    thread_pool_ = std::make_unique<thread::ThreadPool>(1);
    thread_pool_->AddTask(NewCallback(this, &CommonRecoTfLocalPredictEnricher::UpdateNetwork));
    return true;
  }

 private:
  void UpdateNetwork();
  std::string model_dir_;
  std::string model_tgz_base_dir_;
  std::string pb_attr_;
  std::vector<TfInput> tf_inputs_;
  std::vector<std::string> output_tensor_aliases_;
  bool save_to_common_attr_;

  std::unique_ptr<tensorflow::Session> session_;
  tensorflow::Session::CallableHandle callable_handle_;
  tensorflow::Session::CallableHandle update_network_callable_handle_;

  std::string tf_kuiba_update_model_key_;

  std::vector<tensorflow::Tensor> feed_tensors_placeholder_;
  std::unique_ptr<thread::ThreadPool> thread_pool_;
  std::atomic<bool> stop_{false};

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTfLocalPredictEnricher);
};

}  // namespace platform
}  // namespace ks
