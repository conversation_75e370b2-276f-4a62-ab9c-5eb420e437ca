#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/embed_calc/enricher/util.h"
namespace ks {
namespace platform {

class CommonRecoPackBytesEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPackBytesEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct SchemaPart {
    enum class DType : int {
      INT8,
      INT16,
      INT32,
      INT64,
      FP16,
      FP32,
      FP64,
      BYTES,
      SCALEINT8,
    } dtype;
    std::string attr_name;

    ItemAttr *item_attr_accessor;
  };

  bool InitProcessor() override {
    is_common_ = config()->GetBoolean("is_common", is_common_);

    auto *bytes_schema = config()->Get("schema");
    if (!bytes_schema || !bytes_schema->IsArray()) {
      CL_LOG_ERROR("pack_bytes", "invalid_schema")
          << "CommonRecoPackBytesEnricher init failed! 'schema' should be an array";
      return false;
    }

    for (auto *part_config : bytes_schema->array()) {
      if (!part_config->IsObject()) {
        CL_LOG_ERROR("pack_bytes", "invalid_schema_part")
            << "CommonRecoPackBytesEnricher init failed! 'schema' should be an array of objects";
        return false;
      }

      std::string attr_name = part_config->GetString("attr_name");
      if (attr_name.empty()) {
        CL_LOG_ERROR("pack_bytes", "invalid_schema_part")
            << "CommonRecoPackBytesEnricher init failed! 'attr_name' is required for 'schema'";
        return false;
      }

      std::string dtype_string = part_config->GetString("dtype");

      SchemaPart::DType dtype;
      if (dtype_string == "int8") {
        dtype = SchemaPart::DType::INT8;
      } else if (dtype_string == "int16") {
        dtype = SchemaPart::DType::INT16;
      } else if (dtype_string == "int32") {
        dtype = SchemaPart::DType::INT32;
      } else if (dtype_string == "int64") {
        dtype = SchemaPart::DType::INT64;
      } else if (dtype_string == "fp16") {
        dtype = SchemaPart::DType::FP16;
      } else if (dtype_string == "fp32") {
        dtype = SchemaPart::DType::FP32;
      } else if (dtype_string == "fp64") {
        dtype = SchemaPart::DType::FP64;
      } else if (dtype_string == "bytes") {
        dtype = SchemaPart::DType::BYTES;
      } else if (dtype_string == "scale_int8") {
        dtype = SchemaPart::DType::SCALEINT8;
      } else {
        CL_LOG_ERROR("pack_bytes", "invalid_schema_part_dtype")
            << "CommonRecoPackBytesEnricher init failed! Unsupported dtype found: " << dtype_string;
        return false;
      }

      schema_.push_back(SchemaPart{
          .dtype = dtype,
          .attr_name = std::move(attr_name),
      });
    }

    output_attr_name_ = config()->GetString("output_attr_name");
    if (output_attr_name_.empty()) {
      CL_LOG_ERROR("pack_bytes", "missing_output_attr_name")
          << "CommonRecoPackBytesEnricher init failed! 'output_attr_name' is required.";
      return false;
    }

    return true;
  }

  void HandleCommonAttr(MutableRecoContextInterface *context) const;
  void HandleItemAttr(MutableRecoContextInterface *context, const CommonRecoResult &result,
                      ItemAttr *output_attr_accessor) const;

  template <typename T, typename D>
  inline static bool PackSingleBytes(std::string *bytes, absl::optional<D> value) {
    if (value) PackBytes<T>(bytes, static_cast<T>(*value));
    return value.has_value();
  }

  template <typename T, typename D>
  inline static bool PackListBytes(std::string *bytes, absl::optional<D> values) {
    if (values) {
      for (const auto &v : *values) {
        PackBytes<T>(bytes, static_cast<T>(v));
      }
    }
    return values.has_value();
  }

  template <typename D>
  inline static bool PackScaleInt8Bytes(std::string *bytes, absl::optional<D> values) {
    if (values) {
      std::vector<float> tmp_values;
      int zero_cnt = 0;
      for (const auto &v : *values) {
        if (v == 0.0) {
          zero_cnt++;
        }
        float tmp_score = v;
        if (v > 10.0) {
          tmp_score = 10;
        }
        if (v < -10.0) {
          tmp_score = -10;
        }
        tmp_values.push_back(tmp_score);
      }
      if (zero_cnt < values->size()) {
        std::vector<int8_t> quantized_data(tmp_values.size() + sizeof(float));
        FloatToScaleInt8(tmp_values.data(), quantized_data.data(), tmp_values.size());
        bytes->append(reinterpret_cast<char *>(quantized_data.data()), quantized_data.size());
      }
    }
    return values.has_value();
  }
  template <typename T>
  inline static void PackBytes(std::string *bytes, T value) {
    bytes->append(reinterpret_cast<char *>(&value), sizeof(T));
  }

 private:
  bool is_common_ = false;
  std::vector<SchemaPart> schema_;
  std::string output_attr_name_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPackBytesEnricher);
};

template <>
inline void CommonRecoPackBytesEnricher::PackBytes(std::string *bytes, absl::string_view value) {
  bytes->append(value.data(), value.size());
}
}  // namespace platform
}  // namespace ks
