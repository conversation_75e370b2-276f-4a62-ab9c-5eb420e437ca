#pragma once

#include <map>
#include <memory>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "ks/reco_proto/feature_pipe/tf_example.pb.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "learning/kuiba/predict_base/tf_serving_client.h"

namespace ks {
namespace platform {

class CommonRecoTfServingPredictEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoTfServingPredictEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  void GeneratePredictItemAttr(const std::vector<CommonRecoResult> &sent_items,
                               kuiba::CommonPredictRequest *common_request);
  bool BuildCommonPredictRequestByAttrs(MutableRecoContextInterface *context,
                                        const std::vector<CommonRecoResult> &sent_items,
                                        kuiba::CommonPredictRequest *predict_request);
  bool BuildTfPredictRequestBySequenceExample(MutableRecoContextInterface *context,
                                              ::tensorflow::serving::PredictRequest *tf_predict_request);

 private:
  std::string kess_division_;
  const base::Json *kess_service_;
  std::string service_group_;
  std::string kess_shard_;
  int timeout_ms_;

  std::vector<std::string> loss_functions_;
  std::string output_prefix_;
  std::vector<ItemAttr *> output_attr_accessors_;

  std::vector<float> default_loss_values_;  // 和 loss 个数需要一一对应
  std::string request_info_;

  kuiba::AsyncTfServingPredictClient client_;

  bool from_tf_sequence_example_;

  // 使用 context 中已有的 sequence example 进行预估
  std::string tf_sequence_example_attr_;

  std::unordered_set<std::string> exclude_common_attrs_;
  std::vector<std::string> extra_common_attrs_;
  // item extra attrs 相关
  std::vector<std::string> send_item_attrs_;
  std::vector<ItemAttr *> send_item_attr_accessors_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTfServingPredictEnricher);
};
// ks::kess::rpc::grpc::Future<::tensorflow::serving::PredictResponse *>

}  // namespace platform
}  // namespace ks
