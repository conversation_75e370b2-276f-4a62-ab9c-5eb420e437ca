#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/service_black_list_util.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"

DEFINE_string(delegate_enrich_kess_blacklist_kconf, "", "delegate enrich black list kconf");
namespace ks {
namespace platform {

struct SendCommonAttr {
  SendCommonAttr(const std::string &name, const std::string &alias, bool readonly = false)
      : name(name), alias(alias), readonly(readonly) {}

  std::string name;
  std::string alias;
  bool readonly = false;
};

class CommonRecoDelegateEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoDelegateEnricher() : random_(base::GetTimestamp()) {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  const base::Json *ItemFromTables() const final {
    return config()->Get("item_from_tables");
  }

 private:
  bool InitProcessor() override {
    kess_cluster_ = config()->GetString("kess_cluster", ks::kess::scheduler::Constants::DEFAULT_CLUSTER);

    shard_num_ = config()->GetInt("shard_num", 1);
    shard_id_offset_ = config()->GetInt("shard_id_offset", 0);
    shard_by_item_attr_ = config()->GetString("shard_by_item_attr");
    use_item_id_in_attr_ = config()->GetString("use_item_id_in_attr", "");
    consistent_hash_ = config()->GetBoolean("consistent_hash", false);

    if (shard_num_ <= 0) {
      LOG(ERROR) << "CommonRecoDelegateEnricher init failed! shard_num should be > 0";
      return false;
    }

    if (shard_id_offset_ < 0) {
      LOG(ERROR) << "CommonRecoDelegateEnricher init failed! shard_num should be >= 0";
      return false;
    }

    if (!ParseAttrsConfig("send_item_attrs", &send_item_attrs_, "No item attr will be sent") ||
        !ParseCommonAttrsConfig("send_common_attrs", &send_common_attrs_, "No common attr will be sent") ||
        !ParseAttrsConfig("recv_item_attrs", &recv_item_attrs_, "All item attrs will be dropped") ||
        !ParseAttrsConfig("recv_common_attrs", &recv_common_attrs_, "All common attrs will be dropped")) {
      return false;
    }

    for (const auto &attr : send_common_attrs_) {
      send_common_attrs_set_.emplace(attr.name);
    }

    if (ItemFromTables()) {
      check_multi_table_ = true;
    }

    send_item_attrs_in_name_list_ = config()->GetString("send_item_attrs_in_name_list", "");

    RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("exclude_common_attrs"), &exclude_common_attrs_);

    request_info_ = "kess_cluster: " + kess_cluster_ + ", shard_num: " + std::to_string(shard_num_) +
                    ", shard_id_offset: " + std::to_string(shard_id_offset_);

    // 是否使用 grpc_samplelist 的 user attr
    use_sample_list_attr_flag_ = config()->GetBoolean("use_sample_list_attr_flag", false);
    // use_sample_list_attr_flatten 为旧配置名称（含义取反了），需兼容处理
    flatten_sample_list_attr_ = config()->GetBoolean("flatten_sample_list_attr", false) ||
                                !config()->GetBoolean("use_sample_list_attr_flatten", true);
    sample_list_common_attr_key_ = config()->GetString("sample_list_common_attr_key");
    sample_list_ptr_attr_ = config()->GetString("sample_list_ptr_attr");
    flatten_sample_list_attr_to_ = config()->GetString("flatten_sample_list_attr_to", "kuiba_user_attrs");

    send_browse_set_ = config()->GetBoolean("send_browse_set", false);
    send_common_attrs_in_request_ = config()->GetBoolean("send_common_attrs_in_request", false);
    infer_output_type_ = config()->GetInt("infer_output_type", -1);
    for_predict_ = config()->GetBoolean("for_predict", true);

    use_packed_item_attr_config_ = config()->Get("use_packed_item_attr");

    int ttl_seconds = config()->GetInt("ttl_seconds", 3600);
    int random_shift_window = config()->GetInt("random_shift_window", 120);
    random_shift_window = std::min(random_shift_window, ttl_seconds);
    if (ttl_seconds > 0) {
      int random_shift = random_.GetInt(-random_shift_window / 2, random_shift_window / 2) *
                         base::Time::kMicrosecondsPerSecond;
      ttl_ = ttl_seconds * base::Time::kMicrosecondsPerSecond + random_shift;
    } else {
      ttl_ = 0;
    }

    return true;
  }

  // 初始化 request
  void ResetRequest() {
    if (!request_ || (ttl_ > 0 && base::GetTimestamp() - last_reset_ts_ > ttl_)) {
      request_ = std::make_unique<ks::platform::CommonRecoRequest>();
      last_reset_ts_ = base::GetTimestamp();
    } else {
      request_->Clear();
    }
  }

  bool ParseAttrsConfig(const std::string &config_name,
                        std::unordered_map<std::string, std::string> *attrs_map,
                        const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoDelegateEnricher init failed! name is required for " << config_name;
            return false;
          }

          const std::string &alias = c->GetString("as", attr_name);
          attrs_map->emplace(attr_name, alias);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoDelegateEnricher init failed! name should not be empty for "
                       << config_name;
            return false;
          }
          attrs_map->emplace(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(INFO, 100) << "delegate_enrich " << config_name << " is empty, "
                             << default_action_description;
    }
    return true;
  }

  bool ParseCommonAttrsConfig(const std::string &config_name, std::vector<SendCommonAttr> *attrs_vec,
                              const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoDelegateEnricher init failed! name is required for " << config_name;
            return false;
          }

          const std::string &alias = c->GetString("as", attr_name);
          bool readonly = c->GetBoolean("readonly", false);
          attrs_vec->emplace_back(attr_name, alias, readonly);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoDelegateEnricher init failed! name should not be empty for "
                       << config_name;
            return false;
          }
          attrs_vec->emplace_back(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(WARNING, 100) << "delegate_enrich " << config_name << " is empty, "
                                << default_action_description;
    }
    return true;
  }

  bool GenerateSampleListAttr(MutableRecoContextInterface *context,
                              ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attr_vc);
  void FillRequestItems(MutableRecoContextInterface *context, const std::vector<CommonRecoResult> &items,
                        folly::F14FastMap<uint64, std::vector<CommonRecoResult>> *item_id_mapping);

  void SendRequest(MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
                   const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
                   std::shared_ptr<serving_base::Timer> timer,
                   folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_id_mapping);
  void SendGrpcRequest(MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
                       const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
                       std::shared_ptr<serving_base::Timer> timer,
                       folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_id_mapping);
  void SendBrpcRequest(MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
                       const std::string &kess_service, const std::string &kess_shard, int64 timeout_ms,
                       std::shared_ptr<serving_base::Timer> timer,
                       folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &&item_id_mapping);

  void HandleResponse(MutableRecoContextInterface *context, const std::string &kess_service,
                      const std::vector<CommonRecoResult> &items, CommonRecoResponse *response,
                      const std::string &kess_shard, std::shared_ptr<serving_base::Timer> timer,
                      const folly::F14FastMap<uint64, std::vector<CommonRecoResult>> &item_id_mapping);
  void FillCommonAttrFromRequest(MutableRecoContextInterface *context);
  bool FillCommonAttrFromSampleList(MutableRecoContextInterface *context);
  int64 CheckAndGetTimeoutMs(MutableRecoContextInterface *context, const std::string &kess_service);

 private:
  struct SendItemAttr {
    absl::string_view name;
    absl::string_view as;
    ItemAttr *accessor = nullptr;
  };

  std::string kess_cluster_;

  int shard_num_;
  int shard_id_offset_;

  bool consistent_hash_ = false;

  // sample list attr 相关
  bool use_sample_list_attr_flag_ = false;
  bool flatten_sample_list_attr_ = false;
  std::string sample_list_common_attr_key_;
  std::string sample_list_ptr_attr_;
  std::string flatten_sample_list_attr_to_;

  // 是否使用 request 携带的 browseSet
  bool send_browse_set_ = false;
  // 是否使用 request 携带中的 common_attr
  bool send_common_attrs_in_request_ = false;
  const base::Json *use_packed_item_attr_config_ = nullptr;
  // 记录所有不支持 packed item attr 的下游服务名
  folly::F14FastSet<std::string> packed_item_attr_no_support_services_;
  // 指定 infer server 返回 pxtr 的 output_type 存储格式
  int infer_output_type_ = -1;

  bool check_multi_table_ = false;

  // request
  std::vector<SendCommonAttr> send_common_attrs_;
  std::unordered_map<std::string, std::string> send_item_attrs_;
  std::unordered_set<std::string> exclude_common_attrs_;
  absl::flat_hash_set<std::string> sample_list_attrs_;
  absl::flat_hash_set<std::string> send_common_attrs_set_;
  std::string shard_by_item_attr_;
  std::string use_item_id_in_attr_;
  std::string send_item_attrs_in_name_list_;

  // response
  std::unordered_map<std::string, std::string> recv_item_attrs_;
  std::unordered_map<std::string, std::string> recv_common_attrs_;

  std::vector<SendItemAttr> send_item_attr_vec_;
  ItemAttr *use_item_id_in_attr_accessor_ = nullptr;

  std::string request_info_;

  std::unique_ptr<ks::platform::CommonRecoRequest> request_;
  int64 ttl_ = 0;
  int64 last_reset_ts_ = 0;
  base::PseudoRandom random_;

  CommonRecoObjectPool<CommonRecoResponse> response_pool_;

  bool for_predict_ = true;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoDelegateEnricher);
};

}  // namespace platform
}  // namespace ks
