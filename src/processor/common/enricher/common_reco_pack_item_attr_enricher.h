#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_define.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoPackItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPackItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class Aggregator : int {
    UNKNOWN = 0,
    // 拼接
    CONCAT,
    // 拷贝 item attr 到 item attr
    COPY,
  } aggregator_;

  bool InitProcessor() override {
    auto *from_item_attrs_config = config()->Get("from_item_attrs");
    if (from_item_attrs_config) {
      if (!from_item_attrs_config->IsArray()) {
        CL_LOG_ERROR("pack_item_attr_to_item_attr", "invalid_from_item_attrs")
            << "CommonRecoPackItemAttrEnricher init failed! 'from_item_attrs_config' should be an array";
        return false;
      }
      for (const auto *conf : from_item_attrs_config->array()) {
        std::string attr_name = conf->StringValue();
        if (!attr_name.empty()) {
          from_item_attrs_.push_back(std::move(attr_name));
        }
      }
    }

    to_item_attr_ = config()->GetString("to_item_attr", "");

    const std::string &aggregator = config()->GetString("aggregator", "concat");
    if (aggregator == "concat") {
      aggregator_ = Aggregator::CONCAT;
    } else if (aggregator == "copy") {
      aggregator_ = Aggregator::COPY;
    } else {
      CL_LOG_ERROR("pack_item_attr_to_item_attr", "invalid_aggregator")
          << "CommonRecoPackItemAttrEnricher init failed! unsupported 'aggregator' found: " << aggregator;
      return false;
    }

    auto *default_val = config()->Get("default_val");
    if (default_val) {
      if (default_val->IntValue(&default_int_val_)) {
        default_val_type_ = AttrType::INT;
      } else if (default_val->NumberValue(&default_double_val_)) {
        default_val_type_ = AttrType::FLOAT;
      } else if (default_val->StringValue(&default_string_val_)) {
        default_val_type_ = AttrType::STRING;
      } else if (default_val->IsArray()) {
        const auto &array = default_val->array();
        if (array.size() == 0) {
          LOG(ERROR) << "default_val array must contain at least one int/double/string element";
          return false;
        }

        default_int_list_val_.clear();
        default_double_list_val_.clear();
        default_string_list_val_.clear();

        AttrType first_elem_type = AttrType::UNKNOWN;
        for (int i = 0; i < array.size(); ++i) {
          const auto *elem_json = array[i];
          AttrType elem_type = AttrType::UNKNOWN;
          if (elem_json->IsInteger()) {
            elem_type = AttrType::INT;
          } else if (elem_json->IsDouble()) {
            elem_type = AttrType::FLOAT;
          } else if (elem_json->IsString()) {
            elem_type = AttrType::STRING;
          } else {
            LOG(ERROR) << "Unsupported type in default_val array, only support in/double/string in array";
            return false;
          }

          if (first_elem_type == AttrType::UNKNOWN) {
            first_elem_type = elem_type;
            default_val_type_ = ConvertToListType(first_elem_type);
          } else {
            if (elem_type != first_elem_type) {
              LOG(ERROR) << "Unconsistent type int default_val array, first_elem_type is: "
                         << static_cast<int>(first_elem_type)
                         << " current elem_type: " << static_cast<int>(elem_type) << " pos: " << i;
              return false;
            }
          }

          switch (elem_type) {
            case AttrType::INT: {
              int64 int_val = 0;
              CHECK(elem_json->IntValue(&int_val));
              default_int_list_val_.emplace_back(int_val);
              break;
            }
            case AttrType::FLOAT: {
              double double_val = 0;
              CHECK(elem_json->NumberValue(&double_val));
              default_double_list_val_.emplace_back(double_val);
              break;
            }
            case AttrType::STRING: {
              std::string string_val;
              CHECK(elem_json->StringValue(&string_val));
              default_string_list_val_.emplace_back(std::move(string_val));
              break;
            }
            default:
              LOG(ERROR) << "Unsupported type in default_val array, only support in/double/string in array";
              return false;
          }
        }
      } else {
        CL_LOG_ERROR("pack_item_attr_to_item_attr", "invalid_value_type")
            << "CommonRecoPackItemAttrEnricher init failed! Invalid default_val type, only support "
               "int/double/string/int_list/double_list/string_list value";
        return false;
      }
    }

    fill_default_val_ = config()->GetBoolean("fill_default_val", true);
    dedup_to_item_attr_ = config()->GetBoolean("dedup_to_item_attr", false);

    return true;
  }

  void HandleAttr(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  void AppendItemAttrs(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);
  void CopyItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

  void Prepare(MutableRecoContextInterface *context);

 private:
  bool prepared_ = false;
  std::vector<std::string> from_item_attrs_;
  std::vector<ItemAttr *> from_item_attrs_accessor_;
  std::string to_item_attr_;
  ItemAttr *to_item_attr_accessor_ = nullptr;

  AttrType default_val_type_ = AttrType::UNKNOWN;
  int64 default_int_val_ = 0.0;
  double default_double_val_ = 0.0;
  std::string default_string_val_;
  std::vector<int64> default_int_list_val_;
  std::vector<double> default_double_list_val_;
  std::vector<std::string> default_string_list_val_;
  bool fill_default_val_ = true;
  bool dedup_to_item_attr_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPackItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
