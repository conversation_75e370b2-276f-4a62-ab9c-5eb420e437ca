#include "dragon/src/processor/common/enricher/common_reco_json_string_attr_enricher.h"
#include "dragon/src/interop/json_value.h"

namespace ks {
namespace platform {

void CommonRecoJsonStringAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  if (!json_from_item_attr_) {
    HandleCommonJson(context, begin, end);
  } else {
    HandleItemJson(context, begin, end);
  }
}

void CommonRecoJsonStringAttrEnricher::HandleCommonJson(MutableRecoContextInterface *context,
                                                        RecoResultConstIter begin, RecoResultConstIter end) {
  auto str_val = context->GetStringCommonAttr(json_attr_);
  if (!str_val) {
    CL_LOG(WARNING) << "CommonRecoJsonStringAttrEnricher get nullptr json string from common_attr: "
                    << json_attr_;
    return;
  }
  Json::Reader reader(Json::Features::strictMode());
  Json::Value root;

  if (!reader.parse(str_val->data(), str_val->data() + str_val->size(), root)) {
    return;
  }

  for (const auto &json_config : json_configs_) {
    if (json_config.is_common_attr) {
      ProcessCommonAttrFromCommonJson(context, json_config, root);
    } else {
      ProcessItemAttrFromCommonJson(context, begin, end, json_config, root);
    }
  }
}

// NOTE: json 来自 item attr 时
// - output common attr 只支持 list 类型
// - output item attr 只支持单值类型
// - json path 不支持从 item attr 动态生成
void CommonRecoJsonStringAttrEnricher::HandleItemJson(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  for (auto &json_config : json_configs_) {
    std::string json_path = GetStringProcessorParameter(context, json_config.json_path_config);
    if (json_path.empty()) {
      int64 int_val = 0;
      if (TryGetIntProcessorParameter(context, json_config.json_path_config, &int_val)) {
        json_path = std::to_string((uint64)int_val);
      }
    }
    SplitJsonPath(json_path, &json_config.path_vec);
    if (json_config.path_vec.empty()) {
      CL_LOG_ERROR("json", "invalid_json_path:" + json_path) << "invalid json_path: " << json_path;
    }
  }

  auto item_json_attr = context->GetItemAttrAccessor(json_attr_);
  std::for_each(
      begin, end, [&](const CommonRecoResult &result) {
        auto str_val = context->GetStringItemAttr(result, item_json_attr);
        if (!str_val) {
          CL_LOG_EVERY_N(WARNING, 100)
              << "CommonRecoJsonStringAttrEnricher get nullptr json string from item_attr: " << json_attr_
              << " item_key: " << result.item_key;
          return;
        }
        Json::Reader reader(Json::Features::strictMode());
        Json::Value root;
        if (!reader.parse(str_val->data(), str_val->data() + str_val->size(), root)) {
          CL_LOG_EVERY_N(ERROR, 100) << "CommonRecoJsonStringAttrEnricher failed parse json from item_attr: "
                                     << json_attr_ << " item_key: " << result.item_key;
          return;
        }
        for (const auto &json_config : json_configs_) {
          const auto &path_vec = json_config.path_vec;
          if (path_vec.empty()) {
            continue;
          }
          if (json_config.is_common_attr) {
            ProcessCommonAttrFromItemJson(context, json_config, root, path_vec);
          } else {
            ProcessItemAttrFromItemJson(context, result, json_config, root, path_vec);
          }
        }
      });
}

void CommonRecoJsonStringAttrEnricher::ProcessCommonAttrFromCommonJson(MutableRecoContextInterface *context,
                                                                       const JsonConfig &json_config,
                                                                       const Json::Value &root) {
  const std::string &export_common_attr = json_config.attr_name;
  std::string json_path = GetStringProcessorParameter(context, json_config.json_path_config);
  if (json_path.empty()) {
    int64 int_val = 0;
    if (TryGetIntProcessorParameter(context, json_config.json_path_config, &int_val)) {
      json_path = std::to_string((uint64)int_val);
    }
  }
  std::vector<std::string> path_vec;
  SplitJsonPath(json_path, &path_vec);
  if (path_vec.empty()) {
    CL_LOG_ERROR("json", "invalid_json_path:" + json_path)
        << "parse json string failed on common_attr " << export_common_attr;
    return;
  }

  int num = 0;
  // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
  // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
  try {
    if (json_config.to_json_str) {
      // NOTE(linyangxin): 将部分内容转化为 json 字符串。
      // 在 json 内容较多，且需要多次调用该 processor 解析时，可以通过获取 json 的部分内容来进行加速。
      // 实现方案参考 interop::SaveJsonValueToCommonAttr ，只修改了 return 部分
      num = SaveJsonStrToCommonAttr(context, export_common_attr, root, path_vec);
    } else {
      num = interop::SaveJsonValueToCommonAttr(context, export_common_attr, root, path_vec);
    }
  } catch (const std::exception &e) {
    CL_LOG_ERROR("json", "json_exception:" + std::string(e.what())) << "failed to parse json: " << e.what();
  }

  // 默认值兜底
  if (num == 0 && json_config.default_value) {
    const auto *default_value = json_config.default_value;
    if (default_value->IsInteger()) {
      context->SetIntCommonAttr(export_common_attr, default_value->IntValue(0L));
    } else if (default_value->IsDouble()) {
      context->SetDoubleCommonAttr(export_common_attr, default_value->FloatValue(0.0));
    } else if (default_value->IsString()) {
      context->SetStringCommonAttr(export_common_attr, default_value->StringValue());
    } else if (default_value->IsBoolean()) {
      context->SetIntCommonAttr(export_common_attr, default_value->BooleanValue(false));
    }
  }
}

void CommonRecoJsonStringAttrEnricher::ProcessItemAttrFromCommonJson(MutableRecoContextInterface *context,
                                                                     RecoResultConstIter begin,
                                                                     RecoResultConstIter end,
                                                                     const JsonConfig &json_config,
                                                                     const Json::Value &root) {
  const std::string &export_item_attr = json_config.attr_name;
  auto op = RecoUtil::ExtractCommonAttrFromExpr(json_config.json_path_config);
  if (!op) {
    return;
  }
  absl::string_view attr_name = *op;
  if (!context->HasItemAttr(attr_name)) {
    CL_LOG(WARNING) << "CommonRecoJsonStringAttrEnricher skip " << export_item_attr
                    << " for missing item attr: " << attr_name;
    return;
  }
  ItemAttr *input_accessor = context->GetItemAttrAccessor(attr_name);
  ItemAttr *output_accessor = context->GetItemAttrAccessor(export_item_attr);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    std::vector<std::string> path_vec;
    if (auto val = context->GetIntItemAttr(result, input_accessor)) {
      path_vec.push_back(std::to_string((uint64)*val));
    } else if (auto json_path = context->GetStringItemAttr(result, input_accessor)) {
      SplitJsonPath(*json_path, &path_vec);
    }
    if (path_vec.empty()) {
      CL_LOG_WARNING_EVERY("enrich_attr_by_json", "empty_json_path: " + input_accessor->name(), 1000)
          << "enrich attr by json failed due to empty json_path: " << input_accessor->name()
          << ", item_key: " << result.item_key;
      return;
    }

    int num = 0;
    // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
    // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
    try {
      num = interop::SaveJsonValueToItemAttr(result, output_accessor, root, path_vec);
    } catch (const std::exception &e) {
      CL_LOG_ERROR_EVERY("json", "json_exception:" + std::string(e.what()), 1000)
          << "failed to parse json: " << e.what();
    }
    if (num == 0 && json_config.default_value) {
      const auto *default_value = json_config.default_value;
      if (default_value->IsInteger()) {
        result.SetIntAttr(output_accessor, default_value->IntValue(0L));
      } else if (default_value->IsDouble()) {
        result.SetDoubleAttr(output_accessor, default_value->FloatValue(0.0));
      } else if (default_value->IsString()) {
        result.SetStringAttr(output_accessor, default_value->StringValue());
      } else if (default_value->IsBoolean()) {
        result.SetIntAttr(output_accessor, default_value->BooleanValue(false));
      }
    }
  });
}

void CommonRecoJsonStringAttrEnricher::ProcessCommonAttrFromItemJson(
    MutableRecoContextInterface *context, const JsonConfig &json_config, const Json::Value &root,
    const std::vector<std::string> &path_vec) {
  const std::string &export_common_attr = json_config.attr_name;
  int num = 0;
  // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
  // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
  try {
    num = interop::SaveJsonValueToCommonAttr(context, export_common_attr, root, path_vec,
                                             true /*append mode*/, json_config.default_value);
  } catch (const std::exception &e) {
    CL_LOG_ERROR("json", "json_exception:" + std::string(e.what())) << "failed to parse json: " << e.what();
  }

  // 默认值兜底
  if (num == 0 && json_config.default_value) {
    const auto *default_value = json_config.default_value;
    if (default_value->IsInteger()) {
      context->AppendIntListCommonAttr(export_common_attr, default_value->IntValue(0L));
    } else if (default_value->IsDouble()) {
      context->AppendDoubleListCommonAttr(export_common_attr, default_value->FloatValue(0.0));
    } else if (default_value->IsString()) {
      context->AppendStringListCommonAttr(export_common_attr, default_value->StringValue());
    } else if (default_value->IsBoolean()) {
      context->AppendIntListCommonAttr(export_common_attr, default_value->BooleanValue(false));
    }
  }
}

void CommonRecoJsonStringAttrEnricher::ProcessItemAttrFromItemJson(MutableRecoContextInterface *context,
                                                                   const CommonRecoResult &result,
                                                                   const JsonConfig &json_config,
                                                                   const Json::Value &root,
                                                                   const std::vector<std::string> &path_vec) {
  ItemAttr *output_accessor = context->GetItemAttrAccessor(json_config.attr_name);
  // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
  // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
  int num = 0;
  try {
    num =
        interop::SaveJsonValueToItemAttr(result, output_accessor, root, path_vec, json_config.default_value);
  } catch (const std::exception &e) {
    CL_LOG_ERROR_EVERY("json", "json_exception:" + std::string(e.what()), 1000)
        << "failed to parse json: " << e.what();
  }
  // 默认值兜底
  if (num == 0 && json_config.default_value) {
    const auto *default_value = json_config.default_value;
    if (default_value->IsInteger()) {
      context->SetIntItemAttr(result, output_accessor, default_value->IntValue(0L));
    } else if (default_value->IsDouble()) {
      context->SetDoubleItemAttr(result, output_accessor, default_value->FloatValue(0.0));
    } else if (default_value->IsString()) {
      context->SetStringItemAttr(result, output_accessor, default_value->StringValue());
    } else if (default_value->IsBoolean()) {
      context->SetIntItemAttr(result, output_accessor, default_value->BooleanValue(false));
    }
  }
}

size_t CommonRecoJsonStringAttrEnricher::SaveJsonStrToCommonAttr(MutableRecoContextInterface *context,
                                                                 const std::string &attr_name,
                                                                 const Json::Value &json_value,
                                                                 const std::vector<std::string> &json_path) {
  Json::Value value = interop::ExtractTargetField(json_value, json_path);
  if (value.isNull()) {
    return 0;
  }
  context->SetStringCommonAttr(attr_name, Json::FastWriter().write(value));
  return 1;
}

void CommonRecoJsonStringAttrEnricher::SplitJsonPath(absl::string_view json_path,
                                                     std::vector<std::string> *path_vec) {
  path_vec->clear();
  std::vector<absl::string_view> tmp_path_vec = absl::StrSplit(json_path, '.', absl::SkipEmpty());
  for (const auto &path : tmp_path_vec) {
    if (path.size() > 2 && absl::EndsWith(path, "[]")) {
      path_vec->push_back(std::string(path.substr(0, path.size() - 2)));
      path_vec->push_back("[]");
    } else {
      path_vec->push_back(std::string(path));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoJsonStringAttrEnricher, CommonRecoJsonStringAttrEnricher)
FACTORY_REGISTER(JsonFactoryClass, NrRecoParseJsonStringEnricher, CommonRecoJsonStringAttrEnricher)

}  // namespace platform
}  // namespace ks
