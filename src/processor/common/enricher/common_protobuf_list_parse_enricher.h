#pragma once

#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/pbutil.h"

namespace ks {
namespace platform {
class CommonProtobufListParseEnricher : public CommonRecoBaseEnricher {
 public:
  CommonProtobufListParseEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrMapping {
    std::string proto_path;
    std::string attr_name;
    std::vector<int> field_index;
  };

  bool InitProcessor() override {
    class_name_ = config()->GetString("class_name");

    msgs_.emplace_back(pbutil::NewMessageByName(class_name_));
    if (!msgs_.back()) {
      LOG(ERROR) << "CommonProtobufListParseEnricher init failed! Failed to create pb message: "
                 << class_name_;
      return false;
    }

    input_common_attr_ = config()->GetString("input_common_attr");

    auto *attrs = config()->Get("attrs");
    if (attrs && attrs->IsArray()) {
      std::unordered_set<std::string> attr_names;
      const auto *descriptor = msgs_.back()->GetDescriptor();
      for (const auto *attr_json : attrs->array()) {
        if (attr_json->IsObject()) {
          std::string attr_name = attr_json->GetString("name");
          bool inserted = attr_names.insert(attr_name).second;
          if (!inserted) {
            LOG(ERROR) << "CommonProtobufListParseEnricher init failed! duplicated attr_name detected: "
                       << attr_name;
            return false;
          }
          std::string path = attr_json->GetString("path");
          std::vector<int> field_index;
          if (!interop::ConvertMsgPathToFieldIndexPath(descriptor, path, &field_index)) {
            LOG(ERROR) << "CommonProtobufListParseEnricher init failed! invalid path for pb message "
                       << class_name_ << ": " << path;
            return false;
          }
          attr_mappings_.push_back({
              .proto_path = path,
              .attr_name = attr_name,
              .field_index = field_index,
          });
        }
      }
    }

    return true;
  }

 private:
  std::string input_common_attr_;
  std::string class_name_;
  std::vector<AttrMapping> attr_mappings_;
  std::vector<std::unique_ptr<::google::protobuf::Message>> msgs_;

  DISALLOW_COPY_AND_ASSIGN(CommonProtobufListParseEnricher);
};

}  // namespace platform
}  // namespace ks
