#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoEnsembleSeqNumEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoEnsembleSeqNumEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct EnsembleItem {
    const CommonRecoResult *result = nullptr;
    double value = 0.0;

    EnsembleItem() = default;
    EnsembleItem(CommonRecoResult *result, double value) : result(result), value(value) {}
    bool operator<(const EnsembleItem &item) const {
      return value < item.value;
    }
    bool operator>(const EnsembleItem &item) const {
      return value > item.value;
    }
  };

  struct EnsembleItemList {
    std::vector<EnsembleItem> items;
    std::string attr;

    explicit EnsembleItemList(const std::string &attr) : attr(attr) {}
  };

 private:
  bool InitProcessor() override {
    auto *attrs = config()->Get("ensemble_attrs");
    if (!attrs || !attrs->IsArray()) {
      LOG(ERROR) << "CommonRecoEnsembleSeqNumEnricher init failed! Missing 'ensemble_attrs' config"
                 << " or it is not an array.";
      return false;
    }

    for (const auto *attr_json : attrs->array()) {
      if (!attr_json->IsString()) {
        LOG(ERROR) << "CommonRecoEnsembleSeqNumEnricher init failed! 'ensemble_attrs' should contain"
                   << " string values only! Invalid value found: " << attr_json->ToString();
        return false;
      }
      std::string attr = attr_json->StringValue();
      if (!attr.empty()) {
        ensemble_items_.emplace_back(std::make_unique<EnsembleItemList>(attr));
      }
    }

    if (ensemble_items_.empty()) {
      LOG(ERROR) << "CommonRecoEnsembleSeqNumEnricher init failed! 'ensemble_attrs' cannot be empty!";
      return false;
    }

    output_postfix_ = config()->GetString("output_attr_postfix");
    if (output_postfix_.empty()) {
      LOG(ERROR) << "CommonRecoEnsembleSeqNumEnricher init failed! 'output_attr_postfix' cannot be empty!";
      return false;
    }

    default_value_ = config()->GetNumber("default_value", 0.0);
    start_seq_ = config()->GetInt("start_seq", 0);
    stable_sort_ = config()->GetBoolean("stable_sort", false);
    allow_tied_seq_ = config()->GetBoolean("allow_tied_seq", false);
    desc_ = config()->GetBoolean("desc", true);

    return true;
  }

  void SortOnAttr(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                  EnsembleItemList *ensemble_items);

  inline double GetDoubleValue(ItemAttr *attr_accessor, const CommonRecoResult &result) const;

 private:
  std::vector<std::unique_ptr<EnsembleItemList>> ensemble_items_;
  double default_value_ = 0.0;
  std::string output_postfix_;
  int start_seq_ = 0;
  bool stable_sort_ = false;
  bool allow_tied_seq_ = false;
  bool desc_ = true;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoEnsembleSeqNumEnricher);
};

}  // namespace platform
}  // namespace ks
