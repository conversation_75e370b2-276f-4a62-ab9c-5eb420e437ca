#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/pbutil.h"
#include "google/protobuf/message.h"

namespace ks {
namespace platform {

enum class FieldCopyType {
  UNKNOWN_TYPE = 0,
  TRIVIAL_FIELD = 1,
  PB_FIELD = 2
};

static bool FieldCopyType_Name(const FieldCopyType& field_copy_type, std::string* field_copy_name) {
  static std::unordered_map<FieldCopyType, std::string> FIELD_COPY_TYPE_TRANS_MAP = {
    { FieldCopyType::UNKNOWN_TYPE, "unknown" },
    { FieldCopyType::TRIVIAL_FIELD, "trivial_field" },
    { FieldCopyType::PB_FIELD, "pb_field" }
  };
  if (FIELD_COPY_TYPE_TRANS_MAP.count(field_copy_type) > 0) {
    *field_copy_name = FIELD_COPY_TYPE_TRANS_MAP[field_copy_type];
    return true;
  }
  *field_copy_name = "unknown";
  return false;
}

static bool FieldCopyType_Parse(const std::string& field_copy_name, FieldCopyType* field_copy_type) {
  static std::unordered_map<std::string, FieldCopyType> FIELD_COPY_NAME_TRANS_MAP = {
    { "unknown", FieldCopyType::UNKNOWN_TYPE },
    { "trivial_field", FieldCopyType::TRIVIAL_FIELD },
    { "pb_field", FieldCopyType::PB_FIELD }
  };
  if (FIELD_COPY_NAME_TRANS_MAP.count(field_copy_name) > 0) {
    *field_copy_type = FIELD_COPY_NAME_TRANS_MAP[field_copy_name];
    return true;
  }
  *field_copy_type = FieldCopyType::UNKNOWN_TYPE;
  return false;
}

class CommonRecoProtobufCopyAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufCopyAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
    RecoResultConstIter end) override {
    InitializeItemAccessor(context);
    if (is_common_attr_config_) {
      DoSaveCommonAttr2DestPB(context, begin, end);
    } else {
      DoSaveItemAttr2DestPB(context, begin, end);
    }
    if (VLOG_IS_ON(1)) {
      uint64_t space_used = 0;
      uint64_t byte_size = 0;
      for (const auto msg : msgs_) {
        // space_used += msg->SpaceUsedLong();
        space_used += msg->SpaceUsed();
        byte_size += msg->ByteSizeLong();
      }
      CL_LOG(INFO) << GetName() << " holds " << msgs_.size()
        << " messages and occupies total " << space_used
        << " bytes for payloads of total " << byte_size << " bytes.";
    }
    return;
  }

 private:
  bool InitProcessor() override {
    is_common_attr_config_ = config()->GetBoolean("is_common_attr", true);
    use_dynamic_proto_ = config()->GetBoolean("use_dynamic_proto", false);
    if (use_dynamic_proto_) {
      if (GlobalHolder::GetDynamicPool() == nullptr) {
        LOG(ERROR) << "Init Processor failed!GlobalHolder::GetDynamicPool() is nullptr.";
        return false;
      }
    }
    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "Init Processor failed!Field output_attr is required.";
      return false;
    }
    as_string_ = config()->GetBoolean("as_string", false);
    class_name_ = config()->GetString("class_name");
    if (class_name_.empty()) {
      LOG(ERROR) << "Init Processor failed!Arg class_name is required.";
      return false;
    }
    if (!ReserveMessages(1)) {
      LOG(ERROR) << "Init Processor failed!Failed to reserve pb message.";
      return false;
    }
    const google::protobuf::Descriptor *descriptor = nullptr;
    if (use_dynamic_proto_) {
      descriptor = GlobalHolder::GetDynamicPool()->FindMessageTypeByName(class_name_);
    } else {
      descriptor = ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(
        class_name_);
    }
    if (!descriptor) {
      LOG(ERROR) << "Init Processor failed!Failed to get descriptor by class_name_: "
                 << class_name_;
      return false;
    }
    auto *inputs_config = config()->Get("inputs");
    if (!inputs_config || !inputs_config->IsArray()) {
      LOG(ERROR) << "Init Processor failed!Failed to parse input config.";
      return false;
    }
    for (auto *input : inputs_config->array()) {
      if (!input->IsObject()) {
        LOG(ERROR) << "Init Processor failed!Failed to parse inputs_config array.";
        return false;
      }
      std::string copy_type_name = input->GetString("field_type");
      FieldCopyType copy_type_enum = FieldCopyType::UNKNOWN_TYPE;
      if (!FieldCopyType_Parse(copy_type_name, &copy_type_enum)) {
        LOG(ERROR) << "Init Processor failed!Failed to parse copyt_type.Input name: "
                   << copy_type_name;
        return false;
      }
      auto *build_list = input->Get("build_list");
      if (!build_list || !build_list->IsArray()) {
        LOG(ERROR) << "Init Processor failed!Failed to parse build list config.";
        return false;
      }
      if (is_common_attr_config_) {
        for (auto *common : build_list->array()) {
          InputCommonConfig common_config;
          common_config.type = copy_type_enum;
          common_config.attr_name = common->GetString("from_attr");
          common_config.append = common->GetBoolean("append", false);
          common_config.dest_path_str = common->GetString("to_path");
          if (!interop::ConvertMsgPathToFieldIndexPath(descriptor,
            common_config.dest_path_str, &common_config.dest_path)) {
            LOG(ERROR) << "Init Processor failed!Dest path convert failed! path: "
                       << common_config.dest_path_str;
            return false;
          }
          if (copy_type_enum == FieldCopyType::PB_FIELD) {
            std::string source_class = input->GetString("class_name");
            const google::protobuf::Descriptor *source_descriptor = nullptr;
            if (!source_class.empty()) {
              if (use_dynamic_proto_) {
                source_descriptor =
                  GlobalHolder::GetDynamicPool()->FindMessageTypeByName(source_class);
              } else {
                source_descriptor =
                  ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(
                    source_class);
              }
              if (!source_descriptor) {
                LOG(ERROR) << "Init Processor failed.Failed to get descriptor by class_name_: "
                           << source_class;
                return false;
              }
            }
            common_config.source_path_str = common->GetString("from_path");
            if (!interop::ConvertMsgPathToFieldIndexPath(source_descriptor,
              common_config.source_path_str, &common_config.source_path)) {
              LOG(ERROR) << "Init Processor failed!Source path convert failed! path: "
                << common_config.source_path_str;
              return false;
            }
          }
          input_common_configs_.emplace_back(std::move(common_config));
        }
      } else {
        for (auto *item : build_list->array()) {
          InputItemConfig item_config;
          item_config.type = copy_type_enum;
          item_config.attr_name = item->GetString("from_attr");
          item_config.append = item->GetBoolean("append", false);
          item_config.dest_path_str = item->GetString("to_path");
          if (!interop::ConvertMsgPathToFieldIndexPath(descriptor,
            item_config.dest_path_str, &item_config.dest_path)) {
            LOG(ERROR) << "Init Processor failed!Dest path convert failed! path: "
              << item_config.dest_path_str;
            return false;
          }
          if (copy_type_enum == FieldCopyType::PB_FIELD) {
            std::string source_class = input->GetString("class_name");
            const google::protobuf::Descriptor *source_descriptor = nullptr;
            if (!source_class.empty()) {
              if (use_dynamic_proto_) {
                source_descriptor =
                  GlobalHolder::GetDynamicPool()->FindMessageTypeByName(source_class);
              } else {
                source_descriptor =
                  ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(
                    source_class);
              }
              if (!source_descriptor) {
                LOG(ERROR) << "Init Processor failed.Failed to get descriptor by class_name_: "
                           << source_class;
                return false;
              }
            }
            item_config.source_path_str = item->GetString("from_path");
            if (!interop::ConvertMsgPathToFieldIndexPath(source_descriptor,
              item_config.source_path_str, &item_config.source_path)) {
              LOG(ERROR) << "Init Processor failed!Source path convert failed! path: "
                << item_config.source_path_str;
              return false;
            }
          }
          input_item_configs_.emplace_back(std::move(item_config));
        }
      }
    }
    return true;
  }

  bool ReserveMessages(size_t size) {
    size_t allocated_size = msgs_.size();
    if (size > allocated_size) {
      msgs_.resize(size);
      for (size_t i = allocated_size; i < size; i++) {
        if (use_dynamic_proto_) {
          msgs_[i].reset(pbutil::NewMessageByName(class_name_, GlobalHolder::GetDynamicPool(),
            GlobalHolder::GetDynamicMessageFactory()));
        } else {
          msgs_[i].reset(pbutil::NewMessageByName(class_name_));
        }
        if (!msgs_[i]) {
          return false;
        }
      }
    }
    return true;
  }

  bool InitializeItemAccessor(MutableRecoContextInterface *context) {
    if (!init_accessor) {
      if (!is_common_attr_config_) {
        output_attr_accessor_ = context->GetItemAttrAccessor(output_attr_);
      }
      for (auto &input_config : input_item_configs_) {
        input_config.attr_accessor = context->GetItemAttrAccessor(input_config.attr_name);
      }
      init_accessor = true;
    }
    return init_accessor;
  }

  void DoSaveItemAttr2DestPB(MutableRecoContextInterface *context,
    const RecoResultConstIter& begin, const RecoResultConstIter& end) {
    if (!as_string_) {
      CHECK(ReserveMessages(std::distance(begin, end)));
    }
    int i = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto msg = msgs_[as_string_ ? 0 : i++];
      msg->Clear();
      for (const auto &input_config : input_item_configs_) {
        switch (input_config.type) {
          case FieldCopyType::TRIVIAL_FIELD: {
            bool save_item_result = interop::SaveItemAttrToProtobufMessage(result,
              input_config.attr_accessor, msg.get(), input_config.dest_path,
              input_config.append, false);
            if (!save_item_result) {
              CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_item_trivial_field_error")
                << "Process copy item trivial_field to protobuf error!source_name: "
                << input_config.source_path_str
                << ", dest_name: " << input_config.dest_path_str
                << ", item_attr name: " << input_config.attr_name
                << ", attr_accessor ptr: " << input_config.attr_accessor;
            }
            break;
          }
          case FieldCopyType::PB_FIELD: {
            bool save_item_result = interop::SaveItemAttrToProtobufMessage(result,
              input_config.attr_accessor, input_config.attr_name, msg.get(),
              input_config.dest_path, input_config.source_path, input_config.append);
            if (!save_item_result) {
              CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_item_pb_field_error")
                << "Process copy item pb_field to protobuf error!source_name: "
                << input_config.source_path_str
                << ", dest_name: " << input_config.dest_path_str
                << ", item_attr name: " << input_config.attr_name
                << ", attr_accessor ptr: " << input_config.attr_accessor;
            }
            break;
          }
          default:
            std::string field_type_name;
            FieldCopyType_Name(input_config.type, &field_type_name);
            CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_item_unmatch_field_type")
              << "Unmatched process field type: " << field_type_name;
            break;
        }
      }
      if (as_string_) {
        std::string serialized_msg;
        msg->SerializeToString(&serialized_msg);
        result.SetStringAttr(output_attr_accessor_, std::move(serialized_msg));
      } else {
        result.SetPtrAttr(output_attr_accessor_, msg);
      }
    });
  }

  void DoSaveCommonAttr2DestPB(MutableRecoContextInterface *context,
    const RecoResultConstIter& begin, const RecoResultConstIter& end) {
    auto msg = msgs_[0];
    msg->Clear();
    for (const auto &input_config : input_common_configs_) {
      switch (input_config.type) {
        case FieldCopyType::TRIVIAL_FIELD: {
          bool save_common_result = interop::SaveCommonAttrToProtobufMessage(context,
            input_config.attr_name, msg.get(), input_config.dest_path,
            input_config.append, false);
          if (!save_common_result) {
            CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_common_trivial_field_error")
              << "Process copy common trivial_field to protobuf error!source_name: "
              << input_config.source_path_str
              << ", dest_name: " << input_config.dest_path_str
              << ", common_attr name: " << input_config.attr_name;
          }
          break;
        }
        case FieldCopyType::PB_FIELD: {
          bool save_common_result = interop::SaveCommonAttrToProtobufMessage(context,
            input_config.attr_name, input_config.source_path, msg.get(),
            input_config.dest_path, input_config.append);
          if (!save_common_result) {
            CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_common_trivial_field_error")
              << "Process copy common trivial_field to protobuf error!source_name: "
              << input_config.source_path_str
              << ", dest_name: " << input_config.dest_path_str
              << ", common_attr name: " << input_config.attr_name;
          }
          break;
        }
        default:
          std::string field_type_name;
          FieldCopyType_Name(input_config.type, &field_type_name);
          CL_LOG_WARNING(typeid(*this).name(), "cp_pb.save_common_unmatch_field_type")
            << "Unmatched process field type: " << field_type_name;
          break;
      }
    }
    if (as_string_) {
      std::string serialized_msg;
      msg->SerializeToString(&serialized_msg);
      context->SetStringCommonAttr(output_attr_, std::move(serialized_msg));
    } else {
      context->SetPtrCommonAttr(output_attr_, msg);
    }
  }

 private:
  struct InputItemConfig {
    std::string attr_name;
    ItemAttr *attr_accessor = nullptr;
    std::vector<int> dest_path;
    std::vector<int> source_path;
    std::string source_class_name;
    std::string source_path_str;
    std::string dest_path_str;
    FieldCopyType type = FieldCopyType::UNKNOWN_TYPE;
    bool append = false;
  };

  struct InputCommonConfig {
    std::string attr_name;
    std::vector<int> dest_path;
    std::vector<int> source_path;
    std::string source_class_name;
    std::string source_path_str;
    std::string dest_path_str;
    FieldCopyType type = FieldCopyType::UNKNOWN_TYPE;
    bool append = false;
  };

  bool is_common_attr_config_ = true;
  std::vector<InputItemConfig> input_item_configs_;
  std::vector<InputCommonConfig> input_common_configs_;
  std::string output_attr_;
  ItemAttr *output_attr_accessor_ = nullptr;
  bool init_accessor = false;

  bool as_string_ = false;

  std::vector<std::shared_ptr<::google::protobuf::Message>> msgs_;
  std::string class_name_;
  bool use_dynamic_proto_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufCopyAttrEnricher);
};

}   // namespace platform
}   // namespace ks
