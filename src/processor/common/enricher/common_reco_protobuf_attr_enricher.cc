#include "dragon/src/processor/common/enricher/common_reco_protobuf_attr_enricher.h"

#include <unordered_set>
#include "base/strings/string_split.h"

namespace ks {
namespace platform {

void CommonRecoProtobufAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  std::unordered_set<std::string> added_attrs;
  if (is_common_attr_) {
    const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(from_extra_var_);
    if (!message) {
      CL_LOG_WARNING("enrich_from_protobuf", "attr_not_found:" + from_extra_var_)
          << "enrich from protobuf failed as null pointer: " << from_extra_var_;
      return;
    }

    if (!serialize_to_attr_.empty()) {
      thread_local std::string payload;
      payload.clear();
      if (message->SerializeToString(&payload)) {
        context->SetStringCommonAttr(serialize_to_attr_, std::move(payload));
      } else {
        CL_LOG_ERROR("enrich_from_protobuf", "serialize_pb_fail:" + serialize_to_attr_)
            << "failed to serialize pb message to common attr " << serialize_to_attr_;
      }
    }

    if (save_all_fields_) {
      added_attrs = interop::SaveWholeProtobufMessageToCommonAttr(context, output_attr_prefix_, *message,
                                                                  pb_msg_max_depth_);
    } else {
      for (auto &name_path : attr_path_map_) {
        const auto &attr_name = name_path.first;
        auto &msg_field = name_path.second;
        if (!msg_field.valid) {
          CL_LOG_ERROR("enrich_from_protobuf", "invalid_path:" + msg_field.msg_path)
              << "failed to extract data to common attr " << attr_name
              << ", invalid pb json path: " << msg_field.msg_path;
          continue;
        }
        if (msg_field.field_path.empty()) {
          const auto *descriptor = message->GetDescriptor();
          if (!interop::ConvertMsgPathToFieldIndexPath(
                  descriptor, msg_field.msg_path, &msg_field.field_path,
                  !msg_field.sample_attr_name.empty() || msg_field.sample_attr_name_value >= 0
                      ? &msg_field.sample_attr_descriptor
                      : nullptr)) {
            msg_field.valid = false;
            continue;
          }
        }
        interop::SaveProtobufMessageToCommonAttr(
            context, attr_name, *message, msg_field.field_path, msg_field.skip_unset_field,
            &msg_field.field_repeated_limit, msg_field.repeat_align, msg_field.sample_attr_descriptor,
            msg_field.sample_attr_name, msg_field.sample_attr_name_value);
      }
    }
  } else {
    auto *extra_attr_accessor = context->GetItemAttrAccessor(from_extra_var_);
    auto *serialize_attr_accessor =
        serialize_to_attr_.empty() ? nullptr : context->GetItemAttrAccessor(serialize_to_attr_);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      const auto *message = result.GetPtrAttr<google::protobuf::Message>(extra_attr_accessor);
      if (!message) {
        CL_LOG_WARNING_EVERY("enrich_from_protobuf", "attr_not_found:" + from_extra_var_, 1000)
            << "enrich from protobuf failed as null pointer: " << from_extra_var_ << " (" << item_key << ")";
        return;
      }

      if (serialize_attr_accessor) {
        thread_local std::string payload;
        payload.clear();
        if (!message->SerializeToString(&payload)) {
          result.SetStringAttr(serialize_attr_accessor, std::move(payload));
        } else {
          CL_LOG_ERROR_EVERY("enrich_from_protobuf", "serialize_pb_fail:" + serialize_to_attr_, 1000)
              << "failed to serialize pb message to item attr " << serialize_to_attr_;
        }
      }

      if (save_all_fields_) {
        const auto &attrs = interop::SaveWholeProtobufMessageToItemAttr(
            context, item_key, output_attr_prefix_, *message, pb_msg_max_depth_);
        added_attrs.insert(attrs.begin(), attrs.end());
      } else {
        for (auto &name_path : attr_path_map_) {
          const auto &attr_name = name_path.first;
          auto &msg_field = name_path.second;
          if (!msg_field.valid) {
            CL_LOG_ERROR_EVERY("enrich_from_protobuf", "invalid_path:" + msg_field.msg_path, 1000)
                << "failed to extract data to item attr " << attr_name
                << ", invalid pb json path: " << msg_field.msg_path;
            continue;
          }
          if (msg_field.field_path.empty()) {
            const auto *descriptor = message->GetDescriptor();
            if (!interop::ConvertMsgPathToFieldIndexPath(
                    descriptor, msg_field.msg_path, &msg_field.field_path,
                    !msg_field.sample_attr_name.empty() || msg_field.sample_attr_name_value >= 0
                        ? &msg_field.sample_attr_descriptor
                        : nullptr)) {
              msg_field.valid = false;
              continue;
            }
          }
          if (msg_field.attr_accessor == nullptr) {
            msg_field.attr_accessor = context->GetItemAttrAccessor(attr_name);
          }
          interop::SaveProtobufMessageToItemAttr(
              result, msg_field.attr_accessor, *message, msg_field.field_path, msg_field.skip_unset_field,
              &msg_field.field_repeated_limit, msg_field.repeat_align, msg_field.sample_attr_descriptor,
              msg_field.sample_attr_name, msg_field.sample_attr_name_value);
        }
      }
    });
  }

  if (save_all_fields_) {
    CL_LOG_EVERY_N(INFO, 1000) << "Totally " << added_attrs.size() << " attrs was added into "
                               << (is_common_attr_ ? "common" : "item") << " attr";

    if (!save_attr_names_to_attr_.empty()) {
      std::vector<std::string> attrs(added_attrs.begin(), added_attrs.end());
      context->SetStringListCommonAttr(save_attr_names_to_attr_, std::move(attrs));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoProtobufAttrEnricher, CommonRecoProtobufAttrEnricher)

}  // namespace platform
}  // namespace ks
