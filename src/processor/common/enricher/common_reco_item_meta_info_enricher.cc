#include "dragon/src/processor/common/enricher/common_reco_item_meta_info_enricher.h"

namespace ks {
namespace platform {

void CommonRecoItemMetaInfoEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  auto *item_key_accessor = GetAttrAccessor(context, save_item_key_to_attr_);
  auto *item_id_accessor = GetAttrAccessor(context, save_item_id_to_attr_);
  auto *item_type_accessor = GetAttrAccessor(context, save_item_type_to_attr_);
  auto *reason_accessor = GetAttrAccessor(context, save_reason_to_attr_);
  auto *score_accessor = GetAttrAccessor(context, save_score_to_attr_);
  auto *in_browse_set_accessor = GetAttrAccessor(context, save_in_browse_set_to_attr_);
  auto *item_seq_accessor = GetAttrAccessor(context, save_item_seq_to_attr_);

  int i = 0;
  std::for_each(begin, end,
                [this, &i, context, item_key_accessor, item_id_accessor, item_type_accessor, reason_accessor,
                 score_accessor, in_browse_set_accessor, item_seq_accessor](const CommonRecoResult &result) {
                  if (item_key_accessor) {
                    result.SetIntAttr(item_key_accessor, result.item_key);
                  }
                  if (item_id_accessor) {
                    result.SetIntAttr(item_id_accessor, result.GetId());
                  }
                  if (item_type_accessor) {
                    result.SetIntAttr(item_type_accessor, result.GetType());
                  }
                  if (reason_accessor) {
                    result.SetIntAttr(reason_accessor, result.reason);
                  }
                  if (score_accessor) {
                    result.SetDoubleAttr(score_accessor, result.score);
                  }
                  if (in_browse_set_accessor) {
                    result.SetIntAttr(in_browse_set_accessor, context->InBrowseSet(result.item_key));
                  }
                  if (item_seq_accessor) {
                    result.SetIntAttr(item_seq_accessor, i);
                  }
                  ++i;
                });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemMetaInfoEnricher, CommonRecoItemMetaInfoEnricher)

}  // namespace platform
}  // namespace ks
