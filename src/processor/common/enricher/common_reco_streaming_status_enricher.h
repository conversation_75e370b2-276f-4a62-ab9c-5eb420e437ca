#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class CommonRecoStreamingStatusEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoStreamingStatusEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    save_streaming_status_to_ = config()->GetString("save_streaming_status_to", "");
    return true;
  }

  std::string save_streaming_status_to_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStreamingStatusEnricher);
};

}  // namespace platform
}  // namespace ks

