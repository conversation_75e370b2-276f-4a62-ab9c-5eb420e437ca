#pragma once

#include <vector>
#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoConcatAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoConcatAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    output_common_attr_ = config()->GetString("output_common_attr", "");
    output_item_attr_ = config()->GetString("output_item_attr", "");
    if (output_common_attr_.empty() && output_item_attr_.empty()) {
      LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! "
          << "output_common_attr or output_item_attr is required";
      return false;
    }
    // all_save_to_item_attr_ = config()->GetBoolean("all_save_to_item_attr", false);
    output_attr_name_save_as_int_ = config()->GetBoolean("output_attr_name_save_as_int", false);
    output_common_attr_name_ = config()->GetString("output_common_attr_name", "");
    output_item_attr_name_ = config()->GetString("output_item_attr_name", "");

    if ((output_common_attr_.empty() && !output_common_attr_name_.empty())
        || (output_item_attr_.empty() && !output_item_attr_name_.empty())) {
      LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! "
          << "output_xxx_attr 设置后 output_xxx_attr_name 才有效";
      return false;
    }

    if (!output_common_attr_.empty()) {
      auto * input_common_attrs = config()->Get("input_common_attrs");
      if (!input_common_attrs) {
        LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! input_common_attrs ERROR.";
        return false;
      }
      bool extra_res = RecoUtil::ExtractStringListFromJsonConfig(input_common_attrs, &input_common_attrs_);
      if (!extra_res || input_common_attrs_.empty()) {
        LOG(ERROR) << "empty input_common_attrs";
        return false;
      }
    }
    if (!output_item_attr_.empty()) {
      auto * input_item_attrs = config()->Get("input_item_attrs");
      if (!input_item_attrs) {
        LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! input_item_attrs ERROR.";
        return false;
      }
      bool extra_res = RecoUtil::ExtractStringListFromJsonConfig(input_item_attrs, &input_item_attrs_);
      if (!extra_res || input_item_attrs_.empty()) {
        LOG(ERROR) << "empty input_item_attrs";
        return false;
      }
    }

    if (input_common_attrs_.empty() && input_item_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! "
          << "input_common_attrs or input_item_attrs is required.";
      return false;
    }
    // 需要保存 attr_name 时，如果保存的输出到 intattr ，那么需要保证对应的 input_xxx_attrs_ 里面全部为数字
    if (output_attr_name_save_as_int_ == true &&
        (!output_common_attr_name_.empty() || !output_item_attr_name_.empty())) {
      auto str_to_int = [](const std::vector<std::string> &in, std::vector<int64> &out) {
        for (const auto &str : in) {
          int64 val = 0;
          if (!absl::SimpleAtoi(str, &val)) {
            LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! str_to_int failed: " << str;
            return false;
          }
          out.emplace_back(val);
        }
        return true;
      };
      bool format_common = str_to_int(input_common_attrs_, common_attr_name_save_as_int_);
      bool format_item = str_to_int(input_item_attrs_, item_attr_name_save_as_int_);
      if (!format_common || !format_item) {
        LOG(ERROR) << "CommonRecoConcatAttrEnricher init failed! "
            << "When save attr name, input_xxx_attr must be all numbers.";
        return false;
      }
    }
    // LOG(INFO) << "input_common_attrs size: " << input_common_attrs_.size()
    //          << "; input_item_attrs size: " << input_item_attrs_.size()
    //          << "; output_common_attr: " << output_common_attr_
    //          << "; output_item_attr: " << output_item_attr_
    //          << "; output_common_attr_name: " << output_common_attr_name_
    //          << "; output_item_attr_name: " << output_item_attr_name_;

    return true;
  }

 private:
  std::vector<std::string> input_common_attrs_;
  std::vector<std::string> input_item_attrs_;
  std::string output_common_attr_;
  std::string output_item_attr_;
  std::string output_common_attr_name_;
  std::string output_item_attr_name_;
  // bool all_save_to_item_attr_ = false;         // 所有的结果都保存到 item 侧
  bool output_attr_name_save_as_int_ = false;  // 把保存的 attr_name 存储成 int64

  //内部产生
  std::vector<int64> common_attr_name_save_as_int_;
  std::vector<int64> item_attr_name_save_as_int_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoConcatAttrEnricher);
};

}  // namespace platform
}  // namespace ks
