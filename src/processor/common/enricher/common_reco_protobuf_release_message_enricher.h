#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoProtobufReleaseMessageEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufReleaseMessageEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrMapping {
    std::string proto_path;
    std::string attr_name;
  };

  bool InitProcessor() override {
    from_common_attr_ = config()->GetString("from_common_attr");
    from_item_attr_ = config()->GetString("from_item_attr");
    if (from_common_attr_.empty() && from_item_attr_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufReleaseMessageEnricher init failed!"
                 << " Missing 'from_common_attr' config or Missing 'from_item_attr' config";
      return false;
    }

    auto *paths = config()->Get("paths");
    if (paths && paths->IsArray()) {
      for (const auto *path_json : paths->array()) {
        MsgFieldPath msg_field_path;

        if (path_json->IsString()) {
          msg_field_path.path = path_json->StringValue();
        } else {
          LOG(ERROR) << "CommonRecoProtobufReleaseMessageEnricher init failed! Item of paths should be a"
                     << " string! Value found: " << path_json->ToString();
          return false;
        }
        release_paths_.emplace_back(msg_field_path);
      }
    }

    if (release_paths_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufReleaseMessageEnricher init failed!"
                 << " 'paths' should not be empty.";
      return false;
    }

    return true;
  }

 private:
  struct MsgFieldPath {
    std::vector<int> field_path;
    std::string path;
    bool valid = true;
  };
  std::string from_common_attr_;
  std::string from_item_attr_;

  std::vector<MsgFieldPath> release_paths_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufReleaseMessageEnricher);
};

}  // namespace platform
}  // namespace ks
