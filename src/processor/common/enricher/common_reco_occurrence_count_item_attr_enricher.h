#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoOccurrenceCountItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoOccurrenceCountItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  // AttrCounter 用于记录配置中 mappings 各项的详细信息
  struct AttrCounter {
   public:
    std::string check_attr;
    std::string output_name;
    base::Json *check_values = nullptr;
    int single_limit = -1;
    int total_limit = -1;
    const base::Json *length_limit_for_check_attr_config = nullptr;
    int length_limit_for_check_attr = -1;
    int max_count = -1;

    ~AttrCounter() {
      if (check_values) {
        delete check_values;
      }
      check_values = nullptr;
    }

    void ClearCountMap() {
      value_count_map_.Clear();
    }

    void InsertValue(int64 value) {
      uint64 key = (uint64)value;
      auto &count = value_count_map_.GetOrInsert(key);
      if (max_count < 0 || count < max_count) count++;
    }

    void GenItemAttr(int64 value, MutableRecoContextInterface *context, uint64 item_key) {
      auto *count = value_count_map_.Get(value);
      context->SetIntItemAttr(item_key, output_name, count ? *count : 0);
    }

    void GenItemAttr(absl::Span<const int64> values, MutableRecoContextInterface *context, uint64 item_key) {
      int64 sum = 0;
      int check_length = (length_limit_for_check_attr > 0 && length_limit_for_check_attr < values.size())
                             ? length_limit_for_check_attr
                             : values.size();
      for (int i = 0; i < check_length; i++) {
        auto value = values[i];
        auto *count = value_count_map_.Get(value);
        sum += count ? *count : 0;
      }
      context->SetIntItemAttr(item_key, output_name, sum);
    }

    void GenItemAttr(absl::string_view value, MutableRecoContextInterface *context, uint64 item_key) {
      GenItemAttr(base::CityHash64(value.data(), value.size()), context, item_key);
    }

    void GenItemAttr(const std::vector<absl::string_view> &values, MutableRecoContextInterface *context,
                     uint64 item_key) {
      std::vector<int64> signs;
      int check_length = (length_limit_for_check_attr > 0 && length_limit_for_check_attr < values.size())
                             ? length_limit_for_check_attr
                             : values.size();
      signs.reserve(check_length);
      for (int i = 0; i < check_length; i++) {
        auto value = values[i];
        signs.push_back(base::CityHash64(value.data(), value.size()));
      }
      GenItemAttr(signs, context, item_key);
    }

   private:
    base::DynamicDuplicateHash<int64> value_count_map_;
  };

  bool InitProcessor() override;

  bool CheckCounterConfig(base::Json *counter_cfg);

  void BuildAttrCounter(ReadableRecoContextInterface *context);

  void ResolveValueSet(ReadableRecoContextInterface *context, AttrCounter *counter);

  base::AutoDeleteHash<AttrCounter> attr_counters_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoOccurrenceCountItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
