#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

class CommonRecoAggregateListAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAggregateListAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class AggType : int { UNKNOWN = 0, COUNT, MIN, MAX, AVG, SUM };

  struct AttrAggregateMapping {
    std::string from_attr;
    std::string to_attr;
    AggType agg_type = AggType::UNKNOWN;
  };

  bool InitProcessor() override {
    for_common_ = config()->GetBoolean("for_common", false);
    auto mapping_config = config()->Get("mappings");
    if (!mapping_config || !mapping_config->IsArray()) {
      LOG(ERROR) << "CommonRecoAggregateListAttrEnricher wrong mappings config";
      return false;
    }
    for (const auto *mapping_json : mapping_config->array()) {
      AttrAggregateMapping mapping;
      mapping.from_attr = mapping_json->GetString("from_attr", "");
      mapping.to_attr = mapping_json->GetString("to_attr", "");
      if (mapping.from_attr.empty() || mapping.to_attr.empty()) {
        LOG(ERROR) << "CommonRecoAggregateListAttrEnricher"
                   << " init failed! missing from / to attr!";
        return false;
      }

      std::string agg_str = mapping_json->GetString("aggregator", "count");
      if (agg_str == "count") {
        mapping.agg_type = AggType::COUNT;
      } else if (agg_str == "min") {
        mapping.agg_type = AggType::MIN;
      } else if (agg_str == "max") {
        mapping.agg_type = AggType::MAX;
      } else if (agg_str == "avg") {
        mapping.agg_type = AggType::AVG;
      } else if (agg_str == "sum") {
        mapping.agg_type = AggType::SUM;
      } else {
        mapping.agg_type = AggType::UNKNOWN;
      }
      if (mapping.agg_type == AggType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoAggregateListAttrEnricher"
                   << " init failed! wrong aggerator: " << agg_str;
        return false;
      }
      attr_aggregate_mapping_.push_back(mapping);
    }
    return true;
  }

  void HandleSingleAttrAggregate(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                 RecoResultConstIter end, const AttrAggregateMapping &mapping);
  void HandleCommonLevelAggregate(MutableRecoContextInterface *context, const AttrAggregateMapping &mapping);
  void HandleItemLevelAggregate(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                RecoResultConstIter end, const AttrAggregateMapping &mapping);

 private:
  bool for_common_ = false;
  std::vector<AttrAggregateMapping> attr_aggregate_mapping_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAggregateListAttrEnricher);
};

}  // namespace platform
}  // namespace ks
