#include "dragon/src/processor/common/enricher/common_reco_encrypted_id_enricher.h"

#include <utility>
#include <vector>

#include "absl/strings/string_view.h"
#include "dragon/src/core/common_reco_util.h"
#include "infra/utility/src/utility/encrypted_id_cipher.h"

namespace ks {
namespace platform {
bool CommonRecoEncryptedIdEnricher::IsValidInputAttrType(AttrType input_attr_type) {
  switch (input_attr_type) {
    case AttrType::UNKNOWN:
      CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as missing input";
      break;
    case AttrType::INT:
    case AttrType::INT_LIST:
      switch (mode_) {
        case Mode::kEncryptPhotoId:
        case Mode::kEncryptLiveId:
          return true;
        default:
          CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as invalid input attr type: "
                          << RecoUtil::GetAttrTypeName(input_attr_type);
      }
      break;
    case AttrType::STRING:
    case AttrType::STRING_LIST:
      switch (mode_) {
        case Mode::kDecryptPhotoId:
        case Mode::kDecryptLiveId:
        case Mode::kDecryptId:
          return true;
        default:
          CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as invalid input attr type: "
                          << RecoUtil::GetAttrTypeName(input_attr_type);
      }
      break;
    default:
      CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as invalid input attr type: "
                      << RecoUtil::GetAttrTypeName(input_attr_type);
  }
  return false;
}

void CommonRecoEncryptedIdEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  reset_errno_ = GetBoolProcessorParameter(context, "reset_errno", false);
  if (is_common_attr_) {
    ProcessCommonAttr(context);
  } else {
    ProcessItemAttr(context, begin, end);
  }
}

void CommonRecoEncryptedIdEnricher::ProcessItemAttr(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  if (!IsValidInputAttrType(context->GetItemAttrType(input_attr_))) {
    CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as invalid input item attr: " << input_attr_;
    return;
  }

  auto input_attr_accessor = context->GetItemAttrAccessor(input_attr_);
  auto output_attr_accessor = context->GetItemAttrAccessor(output_attr_);

  int error_count = 0;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    switch (mode_) {
      case Mode::kEncryptPhotoId: {
        if (auto int_val = result.GetIntAttr(input_attr_accessor)) {
          int64 encrypted_id;
          if (utility::EncryptPhotoId(*int_val, &encrypted_id)) {
            result.SetIntAttr(output_attr_accessor, encrypted_id);
          } else {
            ++error_count;
          }
        } else if (auto int_list_val = result.GetIntListAttr(input_attr_accessor)) {
          std::vector<int64> encrypted_ids;
          encrypted_ids.resize(int_list_val->size());
          for (size_t i = 0; i < int_list_val->size(); ++i) {
            if (!utility::EncryptPhotoId(int_list_val->at(i), &encrypted_ids[i])) {
              encrypted_ids[i] = 0;
              ++error_count;
            }
          }
          result.SetIntListAttr(output_attr_accessor, std::move(encrypted_ids));
        }
        break;
      }
      case Mode::kDecryptPhotoId: {
        if (auto str_val = result.GetStringAttr(input_attr_accessor)) {
          int64 decrypted_id;
          if ((decrypted_id = FixedDecryptPhotoId(std::string(*str_val)))) {
            result.SetIntAttr(output_attr_accessor, decrypted_id);
          } else {
            ++error_count;
          }
        } else if (auto str_list_val = result.GetStringListAttr(input_attr_accessor)) {
          std::vector<int64> decrypted_ids;
          decrypted_ids.resize(str_list_val->size());
          for (size_t i = 0; i < str_list_val->size(); ++i) {
            if (!(decrypted_ids[i] = FixedDecryptPhotoId(std::string(str_list_val->at(i))))) {
              decrypted_ids[i] = 0;
              ++error_count;
            }
          }
          result.SetIntListAttr(output_attr_accessor, std::move(decrypted_ids));
        }
        break;
      }
      case Mode::kEncryptLiveId: {
        if (auto int_val = result.GetIntAttr(input_attr_accessor)) {
          std::string encrypted_id;
          if (utility::EncryptLiveId(*int_val, &encrypted_id)) {
            result.SetStringAttr(output_attr_accessor, std::move(encrypted_id));
          } else {
            ++error_count;
          }
        } else if (auto int_list_val = result.GetIntListAttr(input_attr_accessor)) {
          std::vector<std::string> encrypted_ids;
          encrypted_ids.resize(int_list_val->size());
          for (size_t i = 0; i < int_list_val->size(); ++i) {
            if (!utility::EncryptLiveId(int_list_val->at(i), &encrypted_ids[i])) {
              encrypted_ids[i].clear();
              ++error_count;
            }
          }
          result.SetStringListAttr(output_attr_accessor, std::move(encrypted_ids));
        }
        break;
      }
      case Mode::kDecryptLiveId: {
        if (auto str_val = result.GetStringAttr(input_attr_accessor)) {
          int64 decrypted_id;
          if (utility::DecryptLiveId(std::string(*str_val), &decrypted_id)) {
            result.SetIntAttr(output_attr_accessor, decrypted_id);
          } else {
            ++error_count;
          }
        } else if (auto str_list_val = result.GetStringListAttr(input_attr_accessor)) {
          std::vector<int64> decrypted_ids;
          decrypted_ids.resize(str_list_val->size());
          for (size_t i = 0; i < str_list_val->size(); ++i) {
            if (!utility::DecryptLiveId(std::string(str_list_val->at(i)), &decrypted_ids[i])) {
              decrypted_ids[i] = 0;
              ++error_count;
            }
          }
          result.SetIntListAttr(output_attr_accessor, std::move(decrypted_ids));
        }
        break;
      }
      case Mode::kDecryptId: {
        if (auto str_val = result.GetStringAttr(input_attr_accessor)) {
          int64 decrypted_id;
          if ((decrypted_id = FixedDecryptPhotoId(std::string(*str_val)))) {
            LOG(INFO) << "decrypt as photo_id: " << *str_val;
            result.SetIntAttr(output_attr_accessor, decrypted_id);
          } else if (utility::DecryptLiveId(std::string(*str_val), &decrypted_id)) {
            LOG(INFO) << "decrypt as live_id: " << *str_val;
            result.SetIntAttr(output_attr_accessor, decrypted_id);
          } else {
            ++error_count;
          }
        } else if (auto str_list_val = result.GetStringListAttr(input_attr_accessor)) {
          std::vector<int64> decrypted_ids;
          decrypted_ids.resize(str_list_val->size());
          for (size_t i = 0; i < str_list_val->size(); ++i) {
            if (!(decrypted_ids[i] = FixedDecryptPhotoId(std::string(str_list_val->at(i)))) &&
                !utility::DecryptLiveId(std::string(str_list_val->at(i)), &decrypted_ids[i])) {
              decrypted_ids[i] = 0;
              ++error_count;
            }
          }
          result.SetIntListAttr(output_attr_accessor, std::move(decrypted_ids));
        }
        break;
      }
    }
  });

  std::string outline = (mode_ == Mode::kEncryptPhotoId   ? "encrypt_photo_id_failed"
                         : mode_ == Mode::kDecryptPhotoId ? "decrypt_photo_id_failed"
                         : mode_ == Mode::kEncryptLiveId  ? "encrypt_live_id_failed"
                         : mode_ == Mode::kDecryptLiveId  ? "decrypt_live_id_failed"
                         : mode_ == Mode::kDecryptId      ? "decrypt_id_failed"
                                                          : "unknown_mode");

  CL_LOG_ERROR_COUNT(error_count, "encrypted_id", outline) << "failed to encrypt or decrypt id";
}

void CommonRecoEncryptedIdEnricher::ProcessCommonAttr(MutableRecoContextInterface *context) {
  if (!IsValidInputAttrType(context->GetCommonAttrType(input_attr_))) {
    CL_LOG(WARNING) << "Skip CommonRecoEncryptedIdEnricher as invalid input common attr: " << input_attr_;
    return;
  }

  switch (mode_) {
    case Mode::kEncryptPhotoId: {
      if (auto int_val = context->GetIntCommonAttr(input_attr_)) {
        int64 encrypted_id;
        if (utility::EncryptPhotoId(*int_val, &encrypted_id)) {
          context->SetIntCommonAttr(output_attr_, encrypted_id);
        } else {
          CL_LOG_ERROR("encrypted_id", "encrypt_photo_id_failed") << "failed to encrypt photo id" << *int_val;
        }
      } else if (auto int_list_val = context->GetIntListCommonAttr(input_attr_)) {
        std::vector<int64> encrypted_ids;
        encrypted_ids.resize(int_list_val->size());
        for (size_t i = 0; i < int_list_val->size(); ++i) {
          if (!utility::EncryptPhotoId(int_list_val->at(i), &encrypted_ids[i])) {
            CL_LOG_ERROR("encrypted_id", "encrypt_photo_id_failed")
                << "failed to encrypt photo id" << int_list_val->at(i);
            encrypted_ids[i] = 0;
          }
        }
        context->SetIntListCommonAttr(output_attr_, std::move(encrypted_ids));
      }
      break;
    }
    case Mode::kDecryptPhotoId: {
      if (auto str_val = context->GetStringCommonAttr(input_attr_)) {
        int64 decrypted_id;
        if ((decrypted_id = FixedDecryptPhotoId(std::string(*str_val)))) {
          context->SetIntCommonAttr(output_attr_, decrypted_id);
        } else {
          CL_LOG_ERROR("encrypted_id", "decrypt_photo_id_failed") << "failed to decrypt photo id" << *str_val;
        }
      } else if (auto str_list_val = context->GetStringListCommonAttr(input_attr_)) {
        std::vector<int64> decrypted_ids;
        decrypted_ids.resize(str_list_val->size());
        for (size_t i = 0; i < str_list_val->size(); ++i) {
          if (!(decrypted_ids[i] = FixedDecryptPhotoId(std::string(str_list_val->at(i))))) {
            CL_LOG_ERROR("encrypted_id", "decrypt_photo_id_failed")
                << "failed to decrypt photo id" << str_list_val->at(i);
            decrypted_ids[i] = 0;
          }
        }
        context->SetIntListCommonAttr(output_attr_, std::move(decrypted_ids));
      }
      break;
    }
    case Mode::kEncryptLiveId: {
      if (auto int_val = context->GetIntCommonAttr(input_attr_)) {
        std::string encrypted_id;
        if (utility::EncryptLiveId(*int_val, &encrypted_id)) {
          context->SetStringCommonAttr(output_attr_, std::move(encrypted_id));
        } else {
          CL_LOG_ERROR("encrypted_id", "encrypt_live_id_failed") << "failed to encrypt live id" << *int_val;
        }
      } else if (auto int_list_val = context->GetIntListCommonAttr(input_attr_)) {
        std::vector<std::string> encrypted_ids;
        encrypted_ids.resize(int_list_val->size());
        for (size_t i = 0; i < int_list_val->size(); ++i) {
          if (!utility::EncryptLiveId(int_list_val->at(i), &encrypted_ids[i])) {
            CL_LOG_ERROR("encrypted_id", "encrypt_live_id_failed")
                << "failed to decrypt live id" << int_list_val->at(i);
            encrypted_ids[i].clear();
          }
        }
        context->SetStringListCommonAttr(output_attr_, std::move(encrypted_ids));
      }
      break;
    }
    case Mode::kDecryptLiveId: {
      if (auto str_val = context->GetStringCommonAttr(input_attr_)) {
        int64 decrypted_id;
        if (utility::DecryptLiveId(std::string(*str_val), &decrypted_id)) {
          context->SetIntCommonAttr(output_attr_, decrypted_id);
        } else {
          CL_LOG_ERROR("encrypted_id", "decrypt_live_id_failed") << "failed to decrypt live id" << *str_val;
        }
      } else if (auto str_list_val = context->GetStringListCommonAttr(input_attr_)) {
        std::vector<int64> decrypted_ids;
        decrypted_ids.resize(str_list_val->size());
        for (size_t i = 0; i < str_list_val->size(); ++i) {
          if (!utility::DecryptLiveId(std::string(str_list_val->at(i)), &decrypted_ids[i])) {
            CL_LOG_ERROR("encrypted_id", "decrypt_live_id_failed")
                << "failed to decrypt live id" << str_list_val->at(i);
            decrypted_ids[i] = 0;
          }
        }
        context->SetIntListCommonAttr(output_attr_, std::move(decrypted_ids));
      }
      break;
    }
    case Mode::kDecryptId: {
      if (auto str_val = context->GetStringCommonAttr(input_attr_)) {
        int64 decrypted_id;
        if ((decrypted_id = FixedDecryptPhotoId(std::string(*str_val)))) {
          LOG(INFO) << "decrypt as photo_id: " << *str_val;
          context->SetIntCommonAttr(output_attr_, decrypted_id);
        } else if (utility::DecryptLiveId(std::string(*str_val), &decrypted_id)) {
          LOG(INFO) << "decrypt as live_id: " << *str_val;
          context->SetIntCommonAttr(output_attr_, decrypted_id);
        } else {
          CL_LOG_ERROR("encrypted_id", "decrypt_id_failed") << "failed to decrypt id" << *str_val;
        }
      } else if (auto str_list_val = context->GetStringListCommonAttr(input_attr_)) {
        std::vector<int64> decrypted_ids;
        decrypted_ids.resize(str_list_val->size());
        for (size_t i = 0; i < str_list_val->size(); ++i) {
          if (!(decrypted_ids[i] = FixedDecryptPhotoId(std::string(str_list_val->at(i)))) &&
              !utility::DecryptLiveId(std::string(str_list_val->at(i)), &decrypted_ids[i])) {
            CL_LOG_ERROR("encrypted_id", "decrypt_id_failed")
                << "failed to decrypt id" << str_list_val->at(i);
            decrypted_ids[i] = 0;
          }
        }
        context->SetIntListCommonAttr(output_attr_, std::move(decrypted_ids));
      }
      break;
    }
  }
}

int64 CommonRecoEncryptedIdEnricher::FixedDecryptPhotoId(const std::string &raw_id) {
  if (reset_errno_) {
    errno = 0;
  }
  return utility::DecryptPhotoId(raw_id);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoEncryptedIdEnricher, CommonRecoEncryptedIdEnricher)

}  // namespace platform
}  // namespace ks
