#include "dragon/src/processor/common/enricher/common_reco_user_meta_info_enricher.h"

#include "base/time/timestamp.h"
#include "infra/utility/src/utility/net_utils.h"

namespace ks {
namespace platform {

void CommonRecoUserMetaInfoEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  if (!save_user_id_to_attr_.empty()) {
    context->SetIntCommonAttr(save_user_id_to_attr_, context->GetUserId());
  }
  if (!save_device_id_to_attr_.empty()) {
    context->SetStringCommonAttr(save_device_id_to_attr_, context->GetDeviceId());
  }
  if (!save_request_id_to_attr_.empty()) {
    context->SetStringCommonAttr(save_request_id_to_attr_, context->GetRequestId());
  }
  if (!save_request_type_to_attr_.empty()) {
    context->SetStringCommonAttr(save_request_type_to_attr_, context->GetRequestType());
  }
  if (!save_browse_set_size_to_attr_.empty()) {
    context->SetIntCommonAttr(save_browse_set_size_to_attr_, context->GetBrowseSetSize());
  }
  if (!save_result_size_to_attr_.empty()) {
    context->SetIntCommonAttr(save_result_size_to_attr_, std::distance(begin, end));
  }
  if (!save_request_time_to_attr_.empty()) {
    context->SetIntCommonAttr(save_request_time_to_attr_, context->GetRequestTime());
  }
  if (!save_request_num_to_attr_.empty()) {
    context->SetIntCommonAttr(save_request_num_to_attr_, context->GetRequestNum());
  }
  if (!save_current_time_ms_to_attr_.empty()) {
    context->SetIntCommonAttr(save_current_time_ms_to_attr_, base::GetTimestamp() / 1000);
  }
  if (!save_host_name_to_attr_.empty()) {
    context->SetStringCommonAttr(save_host_name_to_attr_, serving_base::GetHostName());
  }
  if (!save_host_ip_to_attr_.empty()) {
    static std::string host_ip = utility::server_private_ip();
    context->SetStringCommonAttr(save_host_ip_to_attr_, host_ip);
  }
  if (!save_elapsed_time_to_attr_.empty()) {
    context->SetIntCommonAttr(save_elapsed_time_to_attr_, context->GetElapsedTime());
  }
  if (!save_shard_no_to_attr_.empty()) {
    context->SetIntCommonAttr(save_shard_no_to_attr_, ks::platform::GlobalHolder::GetServiceShardNo());
  }
  if (!save_shard_num_to_attr_.empty()) {
    context->SetIntCommonAttr(save_shard_num_to_attr_, ks::platform::GlobalHolder::GetServiceShardNum());
  }
  if (!save_flow_cpu_cost_to_attr_.empty()) {
    auto &pipeline_cost = context->GetPipelineCpuCost();
    context->SetIntCommonAttr(save_flow_cpu_cost_to_attr_, pipeline_cost.TimingStep(GetPipelineName()));
  }
  if (!save_need_traceback_to_attr_.empty()) {
    context->SetIntCommonAttr(save_need_traceback_to_attr_, context->GetNeedStepInfo());
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoUserMetaInfoEnricher, CommonRecoUserMetaInfoEnricher)

}  // namespace platform
}  // namespace ks
