#include "dragon/src/processor/common/enricher/common_reco_tf_serving_predict_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

bool CommonRecoTfServingPredictEnricher::InitProcessor() {
  loss_functions_.clear();
  // loss 为数组
  const auto *loss_function_name = config()->Get("loss_function_name");
  if (!RecoUtil::ExtractStringListFromJsonConfig(loss_function_name, &loss_functions_, true, true)) {
    LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! Invalid loss_function_name config: "
               << (loss_function_name ? loss_function_name->ToString(2) : "null");
    return false;
  }
  if (loss_functions_.empty()) {
    LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! Empty loss config!";
    return false;
  }
  // loss default value 改为数组
  default_loss_values_.clear();
  base::Json *default_loss_value_json = config()->Get("loss_default_value");
  if (default_loss_value_json) {
    if (default_loss_value_json->IsArray()) {
      double value = 0.0;
      for (int i = 0; i < default_loss_value_json->size(); ++i) {
        if (default_loss_value_json->GetNumber(i, &value)) {
          default_loss_values_.push_back(value);
        }
      }
      if (default_loss_values_.size() != loss_functions_.size()) {
        LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! "
                   << "loss_default_value: " << default_loss_value_json->ToString()
                   << ". loss_default_value's size should be equal to loss_function_name's size or "
                      "loss_default_value should be a number";
        return false;
      }
    } else if (default_loss_value_json->IsDouble() || default_loss_value_json->IsInteger()) {
      double default_loss_value = config()->GetNumber("loss_default_value", -1.0f);
      default_loss_values_.resize(loss_functions_.size(), default_loss_value);
    } else {
      LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! "
                 << "loss_default_value shoule be an array or a number";
    }
  } else {
    LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! "
               << "you should set loss_default_value";
    return false;
  }

  kess_service_ = config()->Get("kess_service");
  kess_division_ = config()->GetString("kess_division", "");
  service_group_ = config()->GetString("service_group", "PRODUCTION");
  kess_shard_ = config()->GetString("kess_shard", "s0");
  timeout_ms_ = config()->GetInt("timeout_ms", 300);
  if (timeout_ms_ <= 0) {
    LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! "
               << "timeout ms: " << timeout_ms_ << ", it should be bigger than 0";
    return false;
  }
  output_prefix_ = config()->GetString("output_prefix", "");

  tf_sequence_example_attr_ = config()->GetString("tf_sequence_example_attr");
  from_tf_sequence_example_ = !tf_sequence_example_attr_.empty();

  RecoUtil::ExtractStringSetFromJsonConfig(config()->Get("exclude_common_attrs"), &exclude_common_attrs_);

  auto *extra_common_attrs = config()->Get("extra_common_attrs");
  if (extra_common_attrs &&
      !RecoUtil::ExtractStringListFromJsonConfig(extra_common_attrs, &extra_common_attrs_)) {
    LOG(ERROR) << "CommonRecoTfServingPredictEnricher init failed! "
               << "extra_common_attrs should be a string list: " << extra_common_attrs->ToString(2);
    return false;
  }

  // item attrs 配置解析
  send_item_attrs_.clear();
  auto extra_attrs_json = config()->Get("send_item_attrs");
  if (extra_attrs_json && extra_attrs_json->IsArray()) {
    for (auto attr : extra_attrs_json->array()) {
      if (attr->IsString()) {
        send_item_attrs_.push_back(attr->StringValue());
      }
    }
  }
  return true;
}

void CommonRecoTfServingPredictEnricher::GeneratePredictItemAttr(
    const std::vector<CommonRecoResult> &sent_items, kuiba::CommonPredictRequest *common_request) {
  std::vector<int> fail_count;
  fail_count.resize(send_item_attr_accessors_.size(), 0);
  for (const auto &result : sent_items) {
    ::kuiba::PredictItem *predict_item = common_request->add_item();
    for (int i = 0; i < send_item_attr_accessors_.size(); ++i) {
      auto *attr_accessor = send_item_attr_accessors_[i];
      if (!interop::BuildSampleAttrFromItemAttr(result, attr_accessor, predict_item->mutable_attr())) {
        ++fail_count[i];
      }
    }
  }
  for (int i = 0; i < fail_count.size(); ++i) {
    const auto &attr_name = send_item_attr_accessors_[i]->name();
    int count = fail_count[i];
    CL_LOG_WARNING("tf_serving", "build_item_attr_fail:" + attr_name)
        << "tf serving predict generate item attr " << attr_name << " failed for " << count << " items";
  }
}

void CommonRecoTfServingPredictEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();
  thread_local std::vector<CommonRecoResult> sent_items;
  sent_items.clear();
  std::copy(begin, end, std::back_inserter(sent_items));

  std::string kess_service = GetStringProcessorParameter(context, kess_service_, "");
  request_info_ = "kess_service: " + kess_service + ", service_group: " + service_group_ +
                  ", timeout_ms: " + std::to_string(timeout_ms_);
  if (sent_items.empty()) {
    CL_LOG(INFO) << "tf serving predict request cancelled: empty item list! " << request_info_
                 << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  if (send_item_attr_accessors_.empty()) {
    for (const auto &attr : send_item_attrs_) {
      send_item_attr_accessors_.push_back(context->GetItemAttrAccessor(attr));
    }
  }
  if (output_attr_accessors_.empty()) {
    for (const auto &loss : loss_functions_) {
      output_attr_accessors_.push_back(context->GetItemAttrAccessor(output_prefix_ + loss));
    }
  }

  std::pair<bool, ks::kess::rpc::grpc::Future<::tensorflow::serving::PredictResponse *>> pr;
  if (from_tf_sequence_example_) {
    ::tensorflow::serving::PredictRequest tf_predict_request;
    if (!BuildTfPredictRequestBySequenceExample(context, &tf_predict_request)) {
      return;
    }
    CL_LOG(INFO) << "sending request to tf serving predict by TfPredictRequest, item num: "
                 << sent_items.size() << ", tf_sequence_example_attr: " << tf_sequence_example_attr_
                 << ", loss num: " << loss_functions_.size() << ", " << request_info_;
    pr = client_.Predict(tf_predict_request, kess_division_, kess_service, service_group_, kess_shard_,
                         timeout_ms_, nullptr);

  } else {
    kuiba::CommonPredictRequest common_predict_request;
    if (!BuildCommonPredictRequestByAttrs(context, sent_items, &common_predict_request)) {
      return;
    }
    CL_LOG(INFO) << "sending request to tf serving predict by CommonRecoRequest, item num: "
                 << sent_items.size() << ", common attr num: " << common_predict_request.common_attr_size()
                 << ", loss num: " << loss_functions_.size() << ", " << request_info_;
    pr = client_.SendRequestAsync(common_predict_request, kess_division_, kess_service, service_group_,
                                  kess_shard_, timeout_ms_, nullptr);
  }

  if (!pr.first) {
    CL_LOG_ERROR("tf_serving", "client_send_request_fail")
        << "failed to send tf serving predict request! " << request_info_
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }
  auto callback = [this, start_ts,
                   items = std::move(sent_items)](::tensorflow::serving::PredictResponse *response) {
    //  从预估结果中获得数据，然后填充到 context item attr 中
    int success_loss_count = 0;
    std::vector<StatisticInfo> pxtr_stat;
    pxtr_stat.resize(loss_functions_.size());
    for (int loss_index = 0; loss_index < output_attr_accessors_.size(); ++loss_index) {
      auto *attr_accessor = output_attr_accessors_[loss_index];
      const auto &loss = loss_functions_[loss_index];
      auto outputs_itr = response->outputs().find(loss);
      if (outputs_itr != response->outputs().end() && outputs_itr->second.float_val_size() == items.size()) {
        success_loss_count++;
        for (int i = 0; i < items.size(); ++i) {
          float value = outputs_itr->second.float_val(i);
          pxtr_stat[loss_index].AddValue(value);
          items[i].SetDoubleAttr(attr_accessor, value);
        }
      } else {
        for (auto &item : items) {
          item.SetDoubleAttr(attr_accessor, default_loss_values_[loss_index]);
        }
        int loss_val_size =
            outputs_itr == response->outputs().end() ? 0 : outputs_itr->second.float_val_size();
        CL_LOG_ERROR("tf_serving", "return_invalid_loss:" + loss)
            << "loss not found or size mismatch (" << loss_val_size << " vs " << items.size()
            << ") in tf serving predict response! " << request_info_ << ", loss_function_name: " << loss
            << ", using default value: " << default_loss_values_[loss_index];
      }
    }

    base::perfutil::PerfUtilWrapper::IntervalLogStash(items.size(), kPerfNs, "predict.item_total",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      GlobalHolder::GetCurrentRequestType(), GetName());
    for (int i = 0; i < pxtr_stat.size(); ++i) {
      const auto &pxtr_name = output_attr_accessors_[i]->name();
      const auto &stat = pxtr_stat[i];
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          1000.0 * stat.count() / items.size(), kPerfNs, "predict.pxtr_hit",
          GlobalHolder::GetServiceIdentifier(), GlobalHolder::GetCurrentRequestType(), GetName(), pxtr_name);
      if (stat.count() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000000 * stat.avg(), kPerfNs, "predict.pxtr_avg", GlobalHolder::GetServiceIdentifier(),
            GlobalHolder::GetCurrentRequestType(), GetName(), pxtr_name);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000000 * stat.max(), kPerfNs, "predict.pxtr_max", GlobalHolder::GetServiceIdentifier(),
            GlobalHolder::GetCurrentRequestType(), GetName(), pxtr_name);
      }
    }

    CL_LOG(INFO) << "tf serving predict response received, outputs_size: " << response->outputs_size()
                 << ", success_loss_count: " << success_loss_count << ", predict_item count:" << items.size()
                 << ", total cost: " << (base::GetTimestamp() - start_ts) / 1000.0 << " ms";
  };
  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), request_info_);
}

bool CommonRecoTfServingPredictEnricher::BuildCommonPredictRequestByAttrs(
    MutableRecoContextInterface *context, const std::vector<CommonRecoResult> &sent_items,
    kuiba::CommonPredictRequest *predict_request) {
  for (const auto &loss_name : loss_functions_) {
    predict_request->add_loss(loss_name);
  }
  const auto &common_attrs_in_req = context->GetCommonAttrsInRequest();
  // 填充 common_attr
  for (const auto &common_attr : common_attrs_in_req) {
    if (exclude_common_attrs_.find(common_attr) != exclude_common_attrs_.end()) {
      // 如果是需要排除的 common attr
      continue;
    }

    if (!interop::BuildSampleAttrFromCommonAttr(context, common_attr,
                                                predict_request->mutable_common_attr())) {
      CL_LOG_ERROR("tf_serving", "build_common_attr_fail:" + common_attr)
          << "failed to build common attr: " << common_attr << ", " << request_info_
          << RecoUtil::GetRequestInfoForLog(context);
    }
  }
  for (const auto &common_attr : extra_common_attrs_) {
    if (common_attrs_in_req.find(common_attr) == common_attrs_in_req.end()) {
      if (!interop::BuildSampleAttrFromCommonAttr(context, common_attr,
                                                  predict_request->mutable_common_attr())) {
        CL_LOG_ERROR("tf_serving", "build_common_attr_fail:" + common_attr)
            << "failed to build extra common attr: " << common_attr << ", " << request_info_
            << RecoUtil::GetRequestInfoForLog(context);
      }
    }
  }

  if (!send_item_attrs_.empty()) {
    GeneratePredictItemAttr(sent_items, predict_request);
  }
  return true;
}

bool CommonRecoTfServingPredictEnricher::BuildTfPredictRequestBySequenceExample(
    MutableRecoContextInterface *context, ::tensorflow::serving::PredictRequest *tf_predict_request) {
  const auto *ptr = context->GetProtoMessagePtrCommonAttr<ks::reco::feature_pipe::SequenceExample>(
      tf_sequence_example_attr_);
  if (ptr == nullptr) {
    CL_LOG_ERROR("tf_serving", "build_tf_predict_request_fail")
        << "failed to get tf_sequence_example pb from common attr: " << tf_sequence_example_attr_;
    return false;
  }
  ::tensorflow::serving::ModelSpec *model_spec = tf_predict_request->mutable_model_spec();
  model_spec->set_signature_name("");
  model_spec->set_name("default");
  for (const auto &loss_name : loss_functions_) {
    tf_predict_request->add_output_filter(loss_name);
  }
  if (context->HasCommonAttr(FLAGS_tf_serving_client_pull_variable_predict_key)) {
    tf_predict_request->add_output_filter(FLAGS_tf_serving_client_pull_variable_predict_key);
  }

  ::tensorflow::TensorProto tensor_proto;
  tensor_proto.set_dtype(::tensorflow::DataType::DT_STRING);
  tensor_proto.mutable_tensor_shape()->add_dim()->set_size(1);
  ptr->SerializeToString(tensor_proto.add_string_val());
  (*tf_predict_request->mutable_inputs())["examples"] = tensor_proto;
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTfServingPredictEnricher, CommonRecoTfServingPredictEnricher)

}  // namespace platform
}  // namespace ks
