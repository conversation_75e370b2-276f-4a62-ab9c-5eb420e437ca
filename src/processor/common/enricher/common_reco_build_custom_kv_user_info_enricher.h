#pragma once

#include <memory>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/reco-arch/fdk/cpp/fdk.h"

namespace ks {
namespace platform {

class CommonRecoBuildCustomKVUserInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoBuildCustomKVUserInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

  fdk::proto::Metadata fdk_metadata_;
  fdk::BuilderOptions fdk_options_;
  std::unique_ptr<fdk::Builder> fdk_builder_;

  std::string kv_user_info_attr_;
  std::string save_result_to_attr_;
  absl::flat_hash_set<std::string> custom_user_info_key_set_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBuildCustomKVUserInfoEnricher);
};

}  // namespace platform
}  // namespace ks
