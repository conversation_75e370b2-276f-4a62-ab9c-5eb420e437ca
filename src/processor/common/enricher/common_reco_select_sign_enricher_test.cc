#include <memory>
#include <utility>
#include <vector>

#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/processor/common/enricher/common_reco_select_sign_enricher.h"
#include "gtest/gtest.h"

static const std::vector<int64> slot_list = {704, 1, 2, 704, 1, 2, 704, 2, 1, 704};
static const std::vector<int64> sign_list = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
static const std::vector<int64> select_sign_list_value = {0, 3, 6, 9};

class CommonRecoSelectSignEnricherTest : public ::testing::Test {
 protected:
  void SetUp() override {
    exec_ = std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(R"json(
    {
    "base_pipeline": {
      "processor": {
        "select_sign_common": {
          "input_sign_attr": "common_signs",
          "input_slot_attr": "common_slots",
          "is_common_attr": true,
          "output_sign_attrs": [
            "signs_704"
          ],
          "select_slots": [
            704
          ],
          "type_name": "CommonRecoSelectSignEnricher"
        },
        "select_sign_item": {
          "input_sign_attr": "item_signs",
          "input_slot_attr": "item_slots",
          "is_common_attr": false,
          "output_sign_attrs": [
            "signs_704"
          ],
          "select_slots": [
            704
          ],
          "type_name": "CommonRecoSelectSignEnricher"
        }
      },
      "type_name": "CommonRecoPipeline"
    },
    "pipeline_map": {
      "common_test": {
        "__PARENT": "base_pipeline",
        "pipeline": [
          "select_sign_common"
        ]
      },
      "item_test": {
        "__PARENT": "base_pipeline",
        "pipeline": [
          "select_sign_item"
        ]
      }
    }
    }
    )json");
  }
  void CheckIntList(const std::vector<int64> &v0, const absl::Span<const int64> &v1) {
    EXPECT_EQ(v0.size(), v1.size()) << ": size(v0) vs size(v1): " << v0.size() << " vs " << v1.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v0.at(i), v1.at(i)) << ": the `" << i << "` element is not equal, v0[i] vs v1[i]: " << v0[i]
                                    << " vs " << v1[i];
    }
  }

  std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor> exec_;
};


TEST_F(CommonRecoSelectSignEnricherTest, Common) {
  // set common attrs
  exec_->SetIntList("common_signs", sign_list);
  exec_->SetIntList("common_slots", slot_list);

  // run
  exec_->Run("common_test");

  auto int_list_attr = exec_->GetIntList("signs_704");
  ASSERT_TRUE(int_list_attr.has_value());
  CheckIntList(select_sign_list_value, int_list_attr.value());
}

TEST_F(CommonRecoSelectSignEnricherTest, Item) {
  // set item attrs
  auto item = exec_->AddItem(1);
  item.SetIntList("item_signs", sign_list);
  item.SetIntList("item_slots", slot_list);

  // run
  exec_->Run("item_test");

  auto int_list_attr = item.GetIntList("signs_704");
  ASSERT_TRUE(int_list_attr.has_value());
  CheckIntList(select_sign_list_value, int_list_attr.value());
}
