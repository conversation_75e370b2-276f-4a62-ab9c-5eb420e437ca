#pragma once

#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "dragon/src/module/matx/matx_dragon_context.h"
#include "dragon/src/module/matx/matx_function_manager.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoPyAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPyAttrEnricher() {}
  ~CommonRecoPyAttrEnricher() {
    func_ptr_ = nullptr;
    matx_dragon_context_ = nullptr;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *common_attr_config = config()->Get("import_common_attr");
    if (common_attr_config && !RecoUtil::ParseAttrsConfig(common_attr_config, &import_common_attrs_)) {
      LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Failed to extract 'import_common_attr'!";
      return false;
    }

    auto *item_attr_config = config()->Get("import_item_attr");
    if (item_attr_config && !RecoUtil::ParseAttrsConfig(item_attr_config, &import_item_attrs_)) {
      LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Failed to extract 'import_item_attr'!";
      return false;
    }

    auto *export_common_config = config()->Get("export_common_attr");
    if (export_common_config && !RecoUtil::ParseAttrsConfig(export_common_config, &export_common_attrs_)) {
      LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Failed to extract 'export_common_attr'!";
      return false;
    }

    auto *export_item_config = config()->Get("export_item_attr");
    if (export_item_config && !RecoUtil::ParseAttrsConfig(export_item_config, &export_item_attrs_)) {
      LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Failed to extract 'export_item_attr'!";
      return false;
    }

    auto *py_function_config = config()->Get("py_function");
    if (py_function_config) {
      if (!py_function_config->IsString()) {
        LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! "
                   << "Missing config 'py_function' or not a string!";
        return false;
      }
      py_function_ = py_function_config->StringValue();
    }

    auto *function_set_config = config()->Get("function_set");
    if (function_set_config) {
      if (!function_set_config->IsString()) {
        LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Missing config 'function_set' or not a string!";
        return false;
      }
      function_set_ = function_set_config->StringValue();
    }

    auto *static_compile_config = config()->Get("static_compile");
    bool static_compile = false;
    if (static_compile_config) {
      if (!static_compile_config->IsBoolean()) {
        LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! Missing config 'static_compile' or not a bool!";
        return false;
      }
      static_compile = static_compile_config->BooleanValue(false);
    }

    if (static_compile) {
      if (!MatxFunctionManager::GetInstance()->StaticInit()) {
        return false;
      }
      if (!function_set_.empty()) {
        MATXScriptBackendPackedCFunc init_func_ptr =
            MatxFunctionManager::GetInstance()->GetFunction(function_set_, "__init___wrapper");
        if (init_func_ptr == nullptr) {
          return false;
        }
        init_func_ptr(NULL, 0, &output_, NULL);
        matxscript::runtime::RTValue::CopyFromCHostToCHost(&output_, &inputs_[0]);
        matxscript::runtime::DragonflyContext_SharedView matx_dragon_context_shared_view =
            MatxDragonContext__F___init___wrapper(nullptr);
        matx_dragon_context_ = dynamic_cast<MatxDragonContext *>(matx_dragon_context_shared_view.ptr);
        (matx_dragon_context_shared_view.operator matxscript::runtime::RTValue()).CopyToCHost(&inputs_[1]);
        param_nums_ = 2;
      } else {
        function_set_ = py_function_;
        matxscript::runtime::DragonflyContext_SharedView matx_dragon_context_shared_view =
            MatxDragonContext__F___init___wrapper(nullptr);
        matx_dragon_context_ = dynamic_cast<MatxDragonContext *>(matx_dragon_context_shared_view.ptr);
        (matx_dragon_context_shared_view.operator matxscript::runtime::RTValue()).CopyToCHost(&inputs_[0]);
        param_nums_ = 1;
      }
      func_ptr_ = MatxFunctionManager::GetInstance()->GetFunction(function_set_, py_function_);
    } else {
      auto *compile_tool_config = config()->Get("compile_tool");
      if (compile_tool_config) {
        if (!compile_tool_config->IsString()) {
          LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! "
                     << "'compile_tool' not an string!";
          return false;
        }
      } else if (!compile_tool_config) {
        LOG(ERROR)
            << "CommonRecoPyAttrEnricher init failed! not static compile and 'compile_tool' not found!";
        return false;
      }
      std::string compile_tool = compile_tool_config->StringValue();

      std::string so_name_key = "_so_name";
      std::string remote_so_name_key = "_remote_so_name";
      if (compile_tool == "both") {
#if defined(__clang__)
        so_name_key = "clang" + so_name_key;
        remote_so_name_key = "clang" + remote_so_name_key;
#elif defined(__GNUC__)
        so_name_key = "gcc" + so_name_key;
        remote_so_name_key = "gcc" + remote_so_name_key;
#else
        LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! unknown compiler";
        return false;
#endif
      } else {
        if (compile_tool == "gcc") {
#if !defined(__GNUC__) || defined(__clang__)
          LOG(ERROR) << "compile_tool=gcc must use real GCC, current is "
#if defined(__clang__)
                     << "Clang";
#else
                     << "Unknown";
#endif
          return false;
#endif
        } else if (compile_tool == "clang") {
#if !defined(__clang__)
          LOG(ERROR) << "compile_tool=clang must use Clang, current is "
#if defined(__GNUC__)
                     << "GCC";
#else
                     << "Unknown";
#endif
          return false;
#endif
        } else {
          LOG(ERROR) << "compile_tool unknown";
          return false;
        }
        so_name_key = compile_tool + so_name_key;
        remote_so_name_key = compile_tool + remote_so_name_key;
      }

      auto *so_name_config = config()->Get(so_name_key);
      if (so_name_config) {
        if (!so_name_config->IsString()) {
          LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! " << so_name_key << " not an string!";
          return false;
        }
      } else {
        LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! not static compile and " << so_name_key
                   << " not found!";
        return false;
      }

      // 远程 so
      std::string remote_so_name;
      auto *remote_so_name_config = config()->Get(remote_so_name_key);
      if (remote_so_name_config) {
        if (!remote_so_name_config->IsString()) {
          LOG(ERROR) << "CommonRecoPyAttrEnricher init failed! " << remote_so_name_key << " not an string!";
          return false;
        }
        remote_so_name = remote_so_name_config->StringValue();
      }

      if (!function_set_.empty()) {
        if (!MatxFunctionManager::GetInstance()->DynamicInit(function_set_, so_name_config->StringValue(),
                                                             remote_so_name)) {
          return false;
        }
        MATXScriptBackendPackedCFunc init_func_ptr =
            MatxFunctionManager::GetInstance()->GetFunction(function_set_, "__init___wrapper");
        if (init_func_ptr == nullptr) {
          return false;
        }
        init_func_ptr(NULL, 0, &output_, NULL);
        matxscript::runtime::RTValue::CopyFromCHostToCHost(&output_, &inputs_[0]);
        matxscript::runtime::DragonflyContext_SharedView matx_dragon_context_shared_view =
            MatxDragonContext__F___init___wrapper(nullptr);
        matx_dragon_context_ = dynamic_cast<MatxDragonContext *>(matx_dragon_context_shared_view.ptr);
        (matx_dragon_context_shared_view.operator matxscript::runtime::RTValue()).CopyToCHost(&inputs_[1]);
        param_nums_ = 2;
      } else {
        function_set_ = py_function_;
        if (!MatxFunctionManager::GetInstance()->DynamicInit(function_set_, so_name_config->StringValue(),
                                                             remote_so_name)) {
          return false;
        }
        matxscript::runtime::DragonflyContext_SharedView matx_dragon_context_shared_view =
            MatxDragonContext__F___init___wrapper(nullptr);
        matx_dragon_context_ = dynamic_cast<MatxDragonContext *>(matx_dragon_context_shared_view.ptr);
        (matx_dragon_context_shared_view.operator matxscript::runtime::RTValue()).CopyToCHost(&inputs_[0]);
        param_nums_ = 1;
      }
      func_ptr_ = MatxFunctionManager::GetInstance()->GetFunction(function_set_, py_function_);
    }

    if (func_ptr_ == nullptr) {
      return false;
    }

    return true;
  }

  void ExtractPerfMessage(std::string *msg);

 private:
  std::unordered_map<std::string, std::string> import_common_attrs_;
  std::unordered_map<std::string, std::string> export_common_attrs_;
  std::unordered_map<std::string, std::string> import_item_attrs_;
  std::unordered_map<std::string, std::string> export_item_attrs_;
  absl::flat_hash_map<std::string, const CommonAttr *> import_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, CommonAttr *> export_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, const ItemAttr *> import_item_attr_accessor_map_;
  absl::flat_hash_map<std::string, ItemAttr *> export_item_attr_accessor_map_;
  int param_nums_ = 0;
  MATXScriptAny inputs_[2];
  MATXScriptAny output_;
  std::string function_set_;
  std::string py_function_;
  MATXScriptBackendPackedCFunc func_ptr_ = nullptr;
  MatxDragonContext *matx_dragon_context_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPyAttrEnricher);
};

}  // namespace platform
}  // namespace ks
