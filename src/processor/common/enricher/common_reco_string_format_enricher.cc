#include "dragon/src/processor/common/enricher/common_reco_string_format_enricher.h"

#include <regex>
#include <unordered_map>
#include <utility>

#include "base/encoding/base64.h"
#include "base/strings/string_printf.h"

namespace ks {
namespace platform {

namespace format_utils {
const std::unordered_map<std::string, std::unordered_map<std::string, StringFormatHelper::Type>>
    kStringFormatHelperTypeMap = {{"l",
                                   {
                                       {"d", StringFormatHelper::INT64},
                                       {"i", StringFormatHelper::INT64},
                                       {"u", StringFormatHelper::UINT64},
                                       {"o", StringFormatHelper::UINT64},
                                       {"x", StringFormatHelper::UINT64},
                                       {"X", StringFormatHelper::UINT64},
                                   }},
                                  {"ll",
                                   {
                                       {"d", StringFormatHelper::INT64},
                                       {"i", StringFormatHelper::INT64},
                                       {"u", StringFormatHelper::UINT64},
                                       {"o", StringFormatHelper::UINT64},
                                       {"x", StringFormatHelper::UINT64},
                                       {"X", StringFormatHelper::UINT64},
                                   }},
                                  {"",
                                   {{"f", StringFormatHelper::DOUBLE},
                                    {"F", StringFormatHelper::DOUBLE},
                                    {"e", StringFormatHelper::DOUBLE},
                                    {"E", StringFormatHelper::DOUBLE},
                                    {"g", StringFormatHelper::DOUBLE},
                                    {"G", StringFormatHelper::DOUBLE},
                                    {"a", StringFormatHelper::DOUBLE},
                                    {"A", StringFormatHelper::DOUBLE},
                                    {"s", StringFormatHelper::STRING}}}};

StringFormatHelper::Type StringFormatHelper::GetType(const std::string &length,
                                                     const std::string &specifier) {
  if (kStringFormatHelperTypeMap.find(length) == kStringFormatHelperTypeMap.end()) {
    return UNSUPPORTED;
  }
  const auto &sub_map = kStringFormatHelperTypeMap.at(length);
  if (sub_map.find(specifier) == sub_map.end()) {
    return UNSUPPORTED;
  }
  return sub_map.at(specifier);
}

bool StringFormatHelper::Split(const std::string &format_string, std::vector<std::string> *sub_format_strings,
                               std::vector<Type> *sub_types) {
  std::regex e("(%%)|(%)([-+ #0])?(\\d+|\\*)?(\\.\\d+|\\.\\*)?(hh|h|l|ll|j|z|t|L)?([diuoxXfFeEgGaAcspn])");
  //             pp   p  flags   width       precision       length              specifier
  std::smatch m;
  std::string s = format_string;

  while (std::regex_search(s, m, e)) {
    if (m.size() == 0) {
      CL_LOG_ERROR("format_utils", "match_size_zero: " + format_string)
          << "m.size() == 0, format_string: `" << format_string << "`, current s: `" << s << "`";
      return false;
    }
    if (m.position(0) > 0) {
      sub_format_strings->push_back(s.substr(0, m.position(0)));
      sub_types->push_back(Type::NONE);
    }
    auto &sub_s = m[0], &pp = m[1], &p = m[2], &flags = m[3], &width = m[4], &precision = m[5],
         &length = m[6], &specifier = m[7];
    if (sub_s.str().size()) {
      sub_format_strings->push_back(s.substr(m.position(0), sub_s.str().size()));
      if (p.str().empty()) {
        // %% case
        sub_types->push_back(Type::NONE);
      } else {
        sub_types->push_back(GetType(length.str(), specifier.str()));
      }

    } else {
      CL_LOG_ERROR("format_utils", "match_str_size_zero: " + format_string)
          << "m.size() == 0, format_string: `" << format_string << "`, current s: `" << s << "`";
      return false;
    }

    s = m.suffix();
  }
  if (s.size()) {
    sub_format_strings->push_back(s);
    sub_types->push_back(Type::NONE);
  }
  return true;
}

}  // namespace format_utils

void CommonRecoStringFormatEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  if (is_common_attr_) {
    if (DoFormat(context)) {
      return;
    }
    CL_LOG_ERROR("string_format", "process_common_fail")
        << "string_format fail, process_common_fail: " << GetName();
    return;
  }
  std::vector<ItemAttr *> src_attr_accs;
  for (const auto &input_attr : input_attrs_) {
    src_attr_accs.push_back(context->GetItemAttrAccessor(input_attr));
  }

  ItemAttr *tgt_attr_acc = context->GetItemAttrAccessor(output_attr_);
  uint32_t count = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (!DoFormat(context, &result, &src_attr_accs, tgt_attr_acc)) {
      ++count;
    }
  });
  CL_LOG_ERROR_COUNT(count, "string_format", "process_item_fail")
      << "string_format fail, process_item_fail: " << GetName();
}

bool CommonRecoStringFormatEnricher::DoFormat(MutableRecoContextInterface *context,
                                              const CommonRecoResult *result,
                                              const std::vector<ItemAttr *> *src_accs, ItemAttr *tgt_acc) {
  std::string tgt_str;
  int32_t position = 0;
  for (int i = 0; i < sub_format_strings_.size(); ++i) {
    const std::string &sub_format = sub_format_strings_[i];
    Type type = sub_types_[i];
    switch (type) {
      case Type::INT64:
        base::StringAppendF(&tgt_str, sub_format_strings_[i].data(),
                            GetInt64(context, position, result, src_accs ? src_accs->at(position) : nullptr));
        ++position;
        break;
      case Type::UINT64:
        base::StringAppendF(&tgt_str, sub_format_strings_[i].data(),
                            static_cast<uint64_t>(GetInt64(context, position, result,
                                                           src_accs ? src_accs->at(position) : nullptr)));
        ++position;
        break;
      case Type::DOUBLE:
        base::StringAppendF(
            &tgt_str, sub_format_strings_[i].data(),
            GetDouble(context, position, result, src_accs ? src_accs->at(position) : nullptr));
        ++position;
        break;
      case Type::STRING:
        base::StringAppendF(
            &tgt_str, sub_format_strings_[i].data(),
            GetString(context, position, result, src_accs ? src_accs->at(position) : nullptr));
        ++position;
        break;
      default:
        tgt_str.append(sub_format_strings_[i]);
        break;
    }
  }
  if (!result) {
    return context->SetStringCommonAttr(output_attr_, std::move(tgt_str));
  }
  return result->SetStringAttr(tgt_acc, std::move(tgt_str));
}

int64_t CommonRecoStringFormatEnricher::GetInt64(MutableRecoContextInterface *context, int32_t pos,
                                                 const CommonRecoResult *result, const ItemAttr *src_acc) {
  bool is_context = !result;
  if (is_context) {
    if (auto val = context->GetIntCommonAttr(input_attrs_[pos])) {
      return *val;
    }
    auto val_list = context->GetIntListCommonAttr(input_attrs_[pos]);
    if (val_list && val_list->size()) {
      return val_list->front();
    }
    CL_LOG_ERROR("string_format", "get_int_common_attr_fail: " + input_attrs_[pos])
        << "cannot get int64 from context." << input_attrs_[pos];
    return 0;
  } else {
    if (auto val = result->GetIntAttr(src_acc)) {
      return *val;
    }
    auto val_list = result->GetIntListAttr(src_acc);
    if (val_list && val_list->size()) {
      return val_list->front();
    }
    CL_LOG_ERROR("string_format", "get_int_item_attr_fail: " + input_attrs_[pos])
        << "cannot get int64 from item[item_id=" << result->GetId() << "]." << input_attrs_[pos];
    return 0;
  }
}

double CommonRecoStringFormatEnricher::GetDouble(MutableRecoContextInterface *context, int32_t pos,
                                                 const CommonRecoResult *result, const ItemAttr *src_acc) {
  bool is_context = !result;
  if (is_context) {
    if (auto val = context->GetDoubleCommonAttr(input_attrs_[pos])) {
      return *val;
    }
    auto val_list = context->GetDoubleListCommonAttr(input_attrs_[pos]);
    if (val_list && val_list->size()) {
      return val_list->front();
    }
    CL_LOG_ERROR("string_format", "get_double_common_attr_fail: " + input_attrs_[pos])
        << "cannot get double from context." << input_attrs_[pos];
    return 0;
  } else {
    if (auto val = result->GetDoubleAttr(src_acc)) {
      return *val;
    }
    auto val_list = result->GetDoubleListAttr(src_acc);
    if (val_list && val_list->size()) {
      return val_list->front();
    }
    CL_LOG_ERROR("string_format", "get_double_item_attr_fail: " + input_attrs_[pos])
        << "cannot get double from item[item_id=" << result->GetId() << "]." << input_attrs_[pos];
    return 0;
  }
}

const char *CommonRecoStringFormatEnricher::GetString(MutableRecoContextInterface *context, int32_t pos,
                                                      const CommonRecoResult *result,
                                                      const ItemAttr *src_acc) {
  bool is_context = !result;
  if (is_context) {
    if (auto val = context->GetStringCommonAttr(input_attrs_[pos])) {
      return val->data();
    }
    auto val_list = context->GetStringListCommonAttr(input_attrs_[pos]);
    if (val_list && val_list->size()) {
      return val_list->front().data();
    }
    CL_LOG_ERROR("string_format", "get_string_common_attr_fail: " + input_attrs_[pos])
        << "cannot get string from context." << input_attrs_[pos];
    return nullptr;
  } else {
    if (auto val = result->GetStringAttr(src_acc)) {
      return val->data();
    }
    auto val_list = result->GetStringListAttr(src_acc);
    if (val_list && val_list->size()) {
      return val_list->front().data();
    }
    CL_LOG_ERROR("string_format", "get_string_item_attr_fail: " + input_attrs_[pos])
        << "cannot get string from item[item_id=" << result->GetId() << "]." << input_attrs_[pos];
    return nullptr;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStringFormatEnricher, CommonRecoStringFormatEnricher)

}  // namespace platform
}  // namespace ks
