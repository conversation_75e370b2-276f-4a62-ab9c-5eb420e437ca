#include "dragon/src/processor/common/enricher/common_reco_item_attr_dispatch_enricher.h"

#include <algorithm>

namespace ks {
namespace platform {

void CommonRecoItemAttrDispatchEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  if (std::distance(begin, end) <= 0) {
    CL_LOG(INFO) << "dispatch_item_attr cancelled, empty items.";
    return;
  }

  if (mode_ == DispatchMode::RANGE) {
    HandleByRange(context, begin, end);
  } else if (mode_ == DispatchMode::PREV) {
    int prev_n = GetIntProcessorParameter(context, "prev_n", 0);
    if (prev_n <= 0) {
      CL_LOG(INFO) << "dispatch_item_attr cancelled, invalid prev_n: " << prev_n;
      return;
    }
    for (const auto &attr : attrs_) {
      if (!context->HasItemAttr(attr)) continue;
      HandleByPrev(context, begin, end, prev_n, attr);
    }
  }
}

void CommonRecoItemAttrDispatchEnricher::HandleByRange(MutableRecoContextInterface *context,
                                                       RecoResultConstIter begin, RecoResultConstIter end) {
  auto size = std::distance(begin, end);
  auto from_first = std::next(begin, std::min(from_range_.first, size));
  auto from_last = std::next(begin, std::min(from_range_.second, size));
  if (dispatch_to_common_attr_) {
    for (auto from_it = from_first; from_it != from_last; ++from_it) {
      for (const auto &attr : attrs_) {
        std::string target_name = output_prefix_ + std::to_string(std::distance(begin, from_it)) + "_" + attr;
        if (!interop::CopyItemAttrToCommonAttr(context, from_it->item_key, attr, target_name)) {
          CL_LOG_ERROR_EVERY("item_attr_dispatch", "item_attr_missing:" + attr, 1000)
              << "item attr dispatch failed, item attr missing: " << attr
              << ", item key: " << from_it->item_key << RecoUtil::GetRequestInfoForLog(context);
        }
      }
    }
  } else {
    if (size < to_range_.first) {
      CL_LOG(WARNING)
          << "CommonRecoItemAttrDispatchEnricher returned cause data size less than to_range's start";
      return;
    }

    auto to_first = std::next(begin, to_range_.first);
    auto to_last = to_range_.second == 0 ? end : std::next(begin, std::min(to_range_.second, size));

    for (auto from_it = from_first; from_it != from_last; ++from_it) {
      for (const auto &attr : attrs_) {
        std::string target_name = output_prefix_ + std::to_string(std::distance(begin, from_it)) + "_" + attr;
        for (auto to_it = to_first; to_it != to_last; ++to_it) {
          if (!interop::ItemAttrInnerCopy(context, from_it->item_key, attr, to_it->item_key, target_name)) {
            CL_LOG_ERROR_EVERY("item_attr_dispatch", "item_attr_missing:" + attr, 1000)
                << "item attr dispatch failed, item attr missing: " << attr
                << ", item key: " << from_it->item_key << RecoUtil::GetRequestInfoForLog(context);
            break;
          }
        }
      }
    }
  }
}

void CommonRecoItemAttrDispatchEnricher::HandleByPrev(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end,
                                                      int prev_n, const std::string &attr) {
  auto current = include_self_ ? begin : std::next(begin);
  for (; current != end; ++current) {
    int size = std::distance(begin, current);
    auto first = std::prev(current, std::min(prev_n, size));
    auto last = include_self_ ? std::next(current) : current;
    for (auto it = first; it != last; ++it) {
      std::string target_name = output_prefix_ + std::to_string(std::distance(first, it)) + "_" + attr;
      if (!interop::ItemAttrInnerCopy(context, it->item_key, attr, current->item_key, target_name)) {
        CL_LOG_ERROR_EVERY("item_attr_dispatch", "item_attr_missing:" + attr, 1000)
            << "item attr dispatch failed, item attr missing: " << attr << ", item key: " << it->item_key
            << RecoUtil::GetRequestInfoForLog(context);
        break;
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemAttrDispatchEnricher, CommonRecoItemAttrDispatchEnricher)

}  // namespace platform
}  // namespace ks
