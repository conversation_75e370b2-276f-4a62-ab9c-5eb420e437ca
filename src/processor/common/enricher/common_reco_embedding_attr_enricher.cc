#include "dragon/src/processor/common/enricher/common_reco_embedding_attr_enricher.h"

#include <utility>

#include "dragon/src/util/common_util.h"

namespace ks {
namespace platform {

bool CommonRecoEmbeddingAttrEnricher::InitProcessor() {
  ks::reco::bt_embd_s::EmbeddingTable::Config embed_config;
  embed_config.queue_prefix = config()->GetString("queue_prefix");
  embed_config.queue_shard_num = config()->GetInt("queue_shard_num", -1);
  embed_config.shard_offset = config()->GetInt("shard_offset", 0);
  embed_config.sign_format = config()->GetString("sign_format", "kuiba");
  embed_config.read_slots = config()->GetString("read_slots");
  embed_config.thread_num = config()->GetInt("thread_num", 10);
  embed_config.force_expire_timet = config()->GetInt("force_expire_timet", -1);
  embed_config.missed_key_sample_rate = config()->GetNumber("missed_key_sample_rate", 0.0);
  embed_config.missed_key_kafka_topic = config()->GetString("missed_key_kafka_topic");
  embed_config.service_name = GlobalHolder::GetServiceIdentifier();
  embed_config.shard_id = GlobalHolder::GetServiceShardNo();
  embed_config.shard_num = GlobalHolder::GetServiceShardNum();

  embed_table_->Initialize(embed_config);

  auto *parameters_inputs_config = config()->Get("parameters_inputs");
  if (parameters_inputs_config) {
    if (parameters_inputs_config->IsArray()) {
      for (auto *c : parameters_inputs_config->array()) {
        auto parameters_inputs_attr_name = c->StringValue();
        if (!parameters_inputs_attr_name.empty()) {
          parameters_inputs_.push_back(parameters_inputs_attr_name);
        } else {
          LOG(ERROR) << "CommonRecoEmbeddingAttrEnricher init failed!"
                     << "parameters_inputs must be an array of non-empty strings.";
          return false;
        }
      }
    } else {
      LOG(ERROR) << "CommonRecoEmbeddingAttrEnricher init failed!"
                 << "parameters_inputs must be an array.";
      return false;
    }
  }

  output_attr_ = config()->GetString("output_attr");
  if (output_attr_.empty()) {
    LOG(ERROR) << "CommonRecoEmbeddingAttrEnricher init failed!"
               << "output_attr is required.";
    return false;
  }

  is_common_attr_ = config()->GetBoolean("is_common_attr", false);

  const std::string &pooling_mode = config()->GetString("pooling_mode", "sum_pooling");
  if (pooling_mode == "sum_pooling") {
    pooling_mode_ = PoolingMode::kSumPooling;
  } else {
    pooling_mode_ = PoolingMode::kNone;
  }

  is_raw_data_ = config()->GetBoolean("is_raw_data", false);
  if (is_raw_data_) {
    const std::string &raw_data_type = config()->GetString("raw_data_type", "uint16");
    if (raw_data_type == "uint16") {
      raw_data_type_ = RawDataType::kUnsignedInt16;
    } else if (raw_data_type == "uint32") {
      raw_data_type_ = RawDataType::kUnsignedInt32;
    } else if (raw_data_type == "uint64") {
      raw_data_type_ = RawDataType::kUnsignedInt64;
    } else if (raw_data_type == "float32") {
      raw_data_type_ = RawDataType::kFloat32;
    } else {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! unsupported raw_data_type: "
                 << raw_data_type;
      return false;
    }

    is_raw_data_list_ = config()->GetBoolean("is_raw_data_list", true);
  }

  return true;
}

void CommonRecoEmbeddingAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  std::vector<ks::reco::bt_embd_s::EmbeddingTable::ItemInfo> item_infos;

  int total_signs = 0;
  int total_hits = 0;

  if (is_common_attr_) {
    std::vector<uint64> signs;
    for (const std::string &parameters_input : parameters_inputs_) {
      if (auto int_val = context->GetIntCommonAttr(parameters_input)) {
        signs.emplace_back(*int_val);
      } else if (auto int_list_val = context->GetIntListCommonAttr(parameters_input)) {
        signs.insert(signs.end(), int_list_val->begin(), int_list_val->end());
      }
    }

    total_signs += signs.size();

    if (signs.empty()) {
      return;
    }

    embed_table_->GetItemInfo(GlobalHolder::GetServiceIdentifier(), signs, &item_infos, false);

    total_hits += item_infos.size();

    if (is_raw_data_) {
      if (raw_data_type_ == RawDataType::kUnsignedInt16) {
        SaveRawDataCommonAttr<uint16>(context, output_attr_, item_infos, pooling_mode_);
      } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
        SaveRawDataCommonAttr<uint32>(context, output_attr_, item_infos, pooling_mode_);
      } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
        SaveRawDataCommonAttr<uint64>(context, output_attr_, item_infos, pooling_mode_);
      } else if (raw_data_type_ == RawDataType::kFloat32) {
        SaveRawDataCommonAttr<float>(context, output_attr_, item_infos, pooling_mode_);
      } else {
        CL_LOG_ERROR("local_embedding", "invalid_raw_data_type")
            << "Unexpected raw_data_type: " << int(raw_data_type_);
      }
    } else {
      SaveEmbeddingCommonAttr(context, output_attr_, item_infos, pooling_mode_);
    }
  } else {
    std::vector<ItemAttr *> parameter_accessors;
    parameter_accessors.reserve(parameters_inputs_.size());
    for (const std::string &parameters_input : parameters_inputs_) {
      auto *accessor = CHECK_NOTNULL(context->GetItemAttrAccessor(parameters_input));
      parameter_accessors.emplace_back(accessor);
    }

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::vector<uint64> signs;
      for (auto *access : parameter_accessors) {
        if (auto int_val = result.GetIntAttr(access)) {
          signs.emplace_back(*int_val);
        } else if (auto int_list_val = result.GetIntListAttr(access)) {
          signs.insert(signs.end(), int_list_val->begin(), int_list_val->end());
        }
      }

      total_signs += signs.size();

      if (signs.empty()) {
        return;
      }

      item_infos.clear();
      embed_table_->GetItemInfo(GlobalHolder::GetServiceIdentifier(), signs, &item_infos, false);

      total_hits += item_infos.size();

      auto item_key = result.item_key;

      if (is_raw_data_) {
        if (raw_data_type_ == RawDataType::kUnsignedInt16) {
          SaveRawDataItemAttr<uint16>(context, item_key, output_attr_, item_infos, pooling_mode_);
        } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
          SaveRawDataItemAttr<uint32>(context, item_key, output_attr_, item_infos, pooling_mode_);
        } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
          SaveRawDataItemAttr<uint64>(context, item_key, output_attr_, item_infos, pooling_mode_);
        } else if (raw_data_type_ == RawDataType::kFloat32) {
          SaveRawDataItemAttr<float>(context, item_key, output_attr_, item_infos, pooling_mode_);
        } else {
          CL_LOG_ERROR("remote_embedding", "invalid_raw_data_type")
              << "Unexpected raw_data_type: " << int(raw_data_type_);
        }
      } else {
        SaveEmbeddingItemAttr(context, item_key, output_attr_, item_infos, pooling_mode_);
      }
    });
  }

  if (total_signs > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        total_signs, kPerfNs,
        is_common_attr_ ? "embedding_fetcher.common_sign_total_num" : "embedding_fetcher.item_sign_total_num",
        GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        total_hits, kPerfNs,
        is_common_attr_ ? "embedding_fetcher.common_sign_hit_num" : "embedding_fetcher.item_sign_hit_num",
        GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoEmbeddingAttrEnricher, CommonRecoEmbeddingAttrEnricher)

}  // namespace platform
}  // namespace ks
