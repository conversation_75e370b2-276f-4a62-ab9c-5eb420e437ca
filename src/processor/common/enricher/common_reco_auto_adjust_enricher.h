#pragma once

#include <algorithm>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoAutoAdjustEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAutoAdjustEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto adjust_config = config()->Get("adjust_output");
    if (!adjust_config || !adjust_config->StringValue(&adjust_output_)) {
      LOG(ERROR) << "CommonRecoAutoAdjustEnricher Init failed, adjust shoule be a string";
      return false;
    }
    auto set_point_config = config()->Get("set_point");
    if (!set_point_config) {
      LOG(ERROR) << "CommonRecoAutoAdjustEnricher Init failed, set_point should be a double";
      return false;
    }
    adjust_function_ = config()->GetString("adjust_function");
    history_input_save_mod_ = config()->GetString("history_input_save_mod");
    group_name_ = config()->Get("group_name");

    if (history_input_save_mod_ == "customize") {
      fractions_attr_ = config()->GetString("fractions_attr");
      if (fractions_attr_.empty()) {
        LOG(ERROR) << "CommonRecoAutoAdjustEnricher Init failed, fractions_attr should not be empty while "
                      "history_input_save_mod = customize";
        return false;
      }
    }

    return true;
  }
  void LocalHistoryFractions(const std::string &holder_name, std::vector<double> *fractions);

  double PidControl(MutableRecoContextInterface *context, absl::Span<const double> fractions,
                    double set_point);

 private:
  std::string adjust_output_;
  std::string adjust_function_;
  std::string history_input_save_mod_;
  std::string fractions_attr_;
  double default_kp_ = 100.0;
  double default_ki_ = 10;
  double default_kd_ = 20;
  base::Json *group_name_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAutoAdjustEnricher);
};

}  // namespace platform
}  // namespace ks
