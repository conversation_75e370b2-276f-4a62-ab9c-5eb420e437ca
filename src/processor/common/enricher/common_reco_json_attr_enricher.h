#pragma once

#include <map>
#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoJsonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoJsonAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    import_attr_ = config()->GetString("import_attr");
    if (import_attr_.empty()) {
      LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed!"
                 << " Missing 'import_attr' config.";
      return false;
    }

    is_common_attr_ = config()->GetBoolean("is_common_attr", true);

    auto *attrs = config()->Get("attrs");
    if (attrs && attrs->IsArray()) {
      for (const auto *attr_json : attrs->array()) {
        if (!attr_json->IsObject()) {
          LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed! item of attrs should be an "
                     << "object! Unsupported value found: " << attr_json->ToString();
          return false;
        }

        std::string attr_name = attr_json->GetString("name");
        if (attr_name.empty()) {
          LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed! name is required for attr: "
                     << attr_json->ToString();
          return false;
        }

        AttrConfig attr_config;
        base::SplitString(attr_json->GetString("path"), ".", &attr_config.json_path);
        if (attr_config.json_path.empty()) {
          LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed! path is required for attr: "
                     << attr_json->ToString();
          return false;
        }

        const std::string &output_type_str = attr_json->GetString("output_type");
        if (output_type_str == "json_string") {
          attr_config.output_type = AttrConfig::OutputType::kJsonString;
        } else if (output_type_str == "json_string_list") {
          attr_config.output_type = AttrConfig::OutputType::kJsonStringList;
        } else if (output_type_str == "int64") {
          attr_config.output_type = AttrConfig::OutputType::kInt64;
        } else if (output_type_str == "double") {
          attr_config.output_type = AttrConfig::OutputType::kDouble;
        } else if (output_type_str == "string") {
          attr_config.output_type = AttrConfig::OutputType::kString;
        } else if (output_type_str == "int64_list") {
          attr_config.output_type = AttrConfig::OutputType::kInt64List;
        } else if (output_type_str == "double_list") {
          attr_config.output_type = AttrConfig::OutputType::kDoubleList;
        } else if (output_type_str == "string_list") {
          attr_config.output_type = AttrConfig::OutputType::kStringList;
        } else {
          LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed! Invalid output_type found: "
                     << output_type_str;
          return false;
        }

        bool inserted = attr_configs_map_.emplace(attr_name, std::move(attr_config)).second;
        if (!inserted) {
          LOG(ERROR) << "CommonRecoJsonAttrEnricher init failed! duplicated attr_name detected: "
                     << attr_name;
          return false;
        }
      }
    }

    return true;
  }

  void ProcessCommonAttrs(MutableRecoContextInterface *context);
  void ProcessItemAttrs(MutableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);

 private:
  struct AttrConfig {
    std::vector<std::string> json_path;
    enum class OutputType {
      kJsonString,
      kJsonStringList,
      kInt64,
      kDouble,
      kString,
      kInt64List,
      kDoubleList,
      kStringList,
    } output_type;

    // temp ptr for item accessor;
    ItemAttr *accessor = nullptr;
  };
  std::string import_attr_;
  bool is_common_attr_ = true;
  std::map<std::string, AttrConfig> attr_configs_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoJsonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
