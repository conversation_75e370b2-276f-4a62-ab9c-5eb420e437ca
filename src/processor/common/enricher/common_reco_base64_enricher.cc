#include "dragon/src/processor/common/enricher/common_reco_base64_enricher.h"

#include "base/encoding/base64.h"
#include "base/strings/string_split.h"

namespace ks {
namespace platform {

void CommonRecoBase64Enricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  thread_local std::string encoded;
  encoded.clear();
  if (is_common_attr_) {
    if (ProcessStrAttr(context) || ProcessMsgAttr(context) || ProcessStrListAttr(context)) {
      return;
    }
    CL_LOG_ERROR("base64_encode", "process_common_fail")
        << "process_common_fail, input: " << input_attr_ << ", output: " << output_attr_;
    return;
  }
  auto *input_attr_accessor = context->GetItemAttrAccessor(input_attr_);
  auto *output_attr_accessor = context->GetItemAttrAccessor(output_attr_);
  if (!input_attr_accessor) {
    CL_LOG_ERROR("base64_encode", "accessor_not_found:" + input_attr_)
        << "accessor_not_found: " << input_attr_;
    return;
  }
  if (!output_attr_accessor) {
    CL_LOG_ERROR("base64_encode", "accessor_not_found:" + output_attr_)
        << "accessor_not_found: " << output_attr_;
    return;
  }
  uint32_t count = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (ProcessStrAttr(context, &result, input_attr_accessor, output_attr_accessor) ||
        ProcessMsgAttr(context, &result, input_attr_accessor, output_attr_accessor) ||
        ProcessStrListAttr(context, &result, input_attr_accessor, output_attr_accessor)) {
      return;
    }
    ++count;
  });
  CL_LOG_ERROR_COUNT(count, "base64_encode", "process_item_fail")
      << "process_item_fail, input: " << input_attr_ << ", output: " << output_attr_;
}

bool CommonRecoBase64Enricher::Base64Encode(const char *input, size_t size, std::string *encoded) {
  if (!base::Base64Encode(input, size, encoded)) {
    CL_LOG_ERROR("base64_encode", "encode_fail") << "Failed to base64 encode val: " << input;
    return false;
  }
  return true;
}

bool CommonRecoBase64Enricher::Base64Decode(const char *input, size_t size, std::string *decoded) {
  if (!base::Base64Decode(input, size, decoded)) {
    CL_LOG_ERROR("base64_encode", "decode_fail") << "Failed to base64 decode val: " << input;
    return false;
  }
  return true;
}

bool CommonRecoBase64Enricher::SerializeToString(const google::protobuf::Message *message, std::string *val) {
  if (!message->SerializeToString(val)) {
    CL_LOG_ERROR("base64_encode", "serialize_fail") << "Failed to serialize pb message";
    return false;
  }
  return true;
}

bool CommonRecoBase64Enricher::ProcessStrAttr(MutableRecoContextInterface *context,
                                              const CommonRecoResult *result, ks::platform::ItemAttr *src_acc,
                                              ks::platform::ItemAttr *tgt_acc) {
  bool is_context = !result;
  std::string encoded;
  absl::optional<absl::string_view> str;
  if (is_context) {
    str = context->GetStringCommonAttr(input_attr_);
  } else {
    str = result->GetStringAttr(src_acc);
  }
  if (!str) {
    return false;
  }
  if (is_encoding_) {
    if (!Base64Encode(str->data(), str->size(), &encoded)) {
      return false;
    }
  } else {
    if (!Base64Decode(str->data(), str->size(), &encoded)) {
      return false;
    }
  }

  if (is_context) {
    return context->SetStringCommonAttr(output_attr_, std::move(encoded));
  } else {
    return result->SetStringAttr(tgt_acc, std::move(encoded));
  }
  return true;
}

bool CommonRecoBase64Enricher::ProcessStrListAttr(MutableRecoContextInterface *context,
                                                  const CommonRecoResult *result,
                                                  ks::platform::ItemAttr *src_acc,
                                                  ks::platform::ItemAttr *tgt_acc) {
  bool is_context = !result;
  std::string encoded;
  absl::optional<std::vector<absl::string_view>> str_list;
  if (is_context) {
    str_list = context->GetStringListCommonAttr(input_attr_);
  } else {
    str_list = result->GetStringListAttr(src_acc);
  }
  if (!str_list) {
    return false;
  }
  std::vector<std::string> encoded_list;
  for (auto sv : *str_list) {
    if (is_encoding_) {
      if (!Base64Encode(sv.data(), sv.size(), &encoded)) {
        return false;
      }
    } else {
      if (!Base64Decode(sv.data(), sv.size(), &encoded)) {
        return false;
      }
    }
    encoded_list.push_back(std::move(encoded));
  }

  if (is_context) {
    context->SetStringListCommonAttr(output_attr_, std::move(encoded_list));
  } else {
    result->SetStringListAttr(tgt_acc, std::move(encoded_list));
  }
  return true;
}

bool CommonRecoBase64Enricher::ProcessMsgAttr(MutableRecoContextInterface *context,
                                              const CommonRecoResult *result, ks::platform::ItemAttr *src_acc,
                                              ks::platform::ItemAttr *tgt_acc) {
  bool is_context = !result;
  std::string encoded;
  const google::protobuf::Message *message = nullptr;
  if (is_context) {
    message = context->GetPtrCommonAttr<google::protobuf::Message>(input_attr_);
  } else {
    message = result->GetPtrAttr<google::protobuf::Message>(src_acc);
  }
  if (!message) {
    return false;
  }
  std::string msg_str;
  if (!SerializeToString(message, &msg_str)) {
    return false;
  }
  if (is_encoding_) {
    if (!Base64Encode(msg_str.data(), msg_str.size(), &encoded)) {
      return false;
    }
  } else {
    // pb 不允许 decode
    return false;
  }
  if (is_context) {
    context->SetStringCommonAttr(output_attr_, std::move(encoded));
  } else {
    result->SetStringAttr(tgt_acc, std::move(encoded));
  }
  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoBase64Enricher, CommonRecoBase64Enricher)

}  // namespace platform
}  // namespace ks
