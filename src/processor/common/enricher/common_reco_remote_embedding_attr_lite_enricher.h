#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <algorithm>
#include <atomic>
#include <functional>
#include <memory>
#include <string>
#include <tuple>
#include <type_traits>
#include <typeinfo>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/common_reco/util/id_converter.h"
#include "ks/reco/bt_embedding_server/proto/bt_embedding_service.kess.grpc.pb.h"
#include "ks/reco_pub/reco/predict/base/public.h"
#include "serving_base/server_base/kess_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossusdb/client/emb/embedding_client.h"

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

// store attrs to be saved to context
class AttrStore {
 public:
  explicit AttrStore(size_t size) : is_common_attr_(true), item_key_(0), data_(size) {}
  AttrStore(uint64 item_key, size_t size) : is_common_attr_(false), item_key_(item_key), data_(size) {}

  void Save(MutableRecoContextInterface *context, const std::string &attr_name) {
    if (has_value_) {
      if (is_common_attr_) {
        context->SetDoubleListCommonAttr(attr_name, std::move(data_));
      } else {
        context->SetDoubleListItemAttr(item_key_, attr_name, std::move(data_));
      }
    }
  }

  std::function<double *(void)> MutableAddrGetter(size_t offset) {
    return [this, offset]() -> double * {
      has_value_ = true;
      return data_.data() + offset;
    };
  }

 private:
  bool is_common_attr_;
  uint64_t item_key_;
  std::vector<double> data_;

  bool has_value_ = false;
};

class CommonRecoRemoteEmbeddingAttrLiteEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRemoteEmbeddingAttrLiteEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  template <typename Value>
  using Set = folly::F14FastSet<Value>;

  template <typename Key, typename Value>
  using Map = folly::F14FastMap<Key, Value>;

  bool InitProcessor() override {
    protocol_ = config()->GetInt("protocol", 0);

    max_signs_per_request_ = config()->GetInt("max_signs_per_request", 0);
    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    if (timeout_ms_ <= 0) {
      CL_LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! timeout_ms must > 0";
      return false;
    }

    if (protocol_ == 0) {
      kess_cluster_ = config()->GetString("kess_cluster", "PRODUCTION");
      shards_ = config()->GetInt("shard_num", 1);
      if (shards_ < 1) {
        CL_LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! shard num must > 0";
        return false;
      }
      client_side_shard_ = config()->GetBoolean("client_side_shard", false);
      if (client_side_shard_) {
        requests_.resize(shards_);
      } else {
        requests_.resize(1);
      }
      shard_prefix_ = config()->GetString("shard_prefix", "s");
    } else if (protocol_ == 1) {
      clsdb_emb_client_config_.service_name = config()->GetString("colossusdb_embd_service_name");
      clsdb_emb_client_config_.table_name = config()->GetString("colossusdb_embd_table_name");
      clsdb_emb_client_config_.use_kconf_client = config()->GetBoolean("colossusdb_use_kconf_client", true);
      clsdb_emb_client_config_.timeout_ms = timeout_ms_;
      clsdb_emb_client_config_.max_signs_per_request = max_signs_per_request_;
    } else {
      LOG(FATAL) << "Unexpected protocol: " << protocol_;
    }

    const std::string &query_source_type = config()->GetString("query_source_type", "item_key");
    if (query_source_type == "item_key") {
      query_source_type_ = QuerySourceType::kItemKey;
    } else if (query_source_type == "item_id") {
      query_source_type_ = QuerySourceType::kItemId;
    } else if (query_source_type == "user_id") {
      query_source_type_ = QuerySourceType::kUserId;
    } else if (query_source_type == "device_id") {
      query_source_type_ = QuerySourceType::kDeviceId;
    } else if (query_source_type == "item_attr") {
      query_source_type_ = QuerySourceType::kItemAttr;
    } else if (query_source_type == "common_attr") {
      query_source_type_ = QuerySourceType::kCommonAttr;
    } else {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! Unexpected query_source_type: "
                 << query_source_type;
      return false;
    }

    auto *id_conv_config = config()->Get("id_converter");
    if (id_conv_config == nullptr || id_conv_config->GetString("type_name", "").empty()) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! id converter config error";
      return false;
    }
    id_conv_ = base::JsonFactoryClass::New<IdConverter>(id_conv_config);
    if (!id_conv_) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! invalid id converter config: "
                 << id_conv_config->ToString();
      return false;
    }

    slot_ = config()->GetInt("slot", 0);

    size_ = config()->GetInt("size", 0);
    if (size_ <= 0) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! size is required";
      return false;
    }

    output_attr_name_ = config()->GetString("output_attr_name");
    if (output_attr_name_.empty()) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! output_attr_name empty";
      return false;
    }

    input_attr_name_ = config()->GetString("input_attr_name");
    if (query_source_type_ == QuerySourceType::kItemAttr ||
        query_source_type_ == QuerySourceType::kCommonAttr) {
      if (input_attr_name_.empty()) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrLiteEnricher init failed! input_attr_name empty";
        return false;
      }
    } else {
      if (!input_attr_name_.empty()) {
        LOG(WARNING) << "input_attr_name(" << input_attr_name_
                     << ") is ignored as query_source_type: " << query_source_type;
      }
    }

    is_raw_data_ = config()->GetBoolean("is_raw_data", false);
    if (is_raw_data_) {
      const std::string &raw_data_type = config()->GetString("raw_data_type", "uint16");
      if (raw_data_type == "uint8") {
        raw_data_type_ = RawDataType::kUnsignedInt8;
      } else if (raw_data_type == "uint16") {
        raw_data_type_ = RawDataType::kUnsignedInt16;
      } else if (raw_data_type == "uint32") {
        raw_data_type_ = RawDataType::kUnsignedInt32;
      } else if (raw_data_type == "uint64") {
        raw_data_type_ = RawDataType::kUnsignedInt64;
      } else if (raw_data_type == "int8") {
        raw_data_type_ = RawDataType::kSignedInt8;
      } else if (raw_data_type == "int16") {
        raw_data_type_ = RawDataType::kSignedInt16;
      } else if (raw_data_type == "int32") {
        raw_data_type_ = RawDataType::kSignedInt32;
      } else if (raw_data_type == "int64") {
        raw_data_type_ = RawDataType::kSignedInt64;
      } else if (raw_data_type == "float32") {
        raw_data_type_ = RawDataType::kFloat32;
      } else {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! unsupported raw_data_type: "
                   << raw_data_type;
        return false;
      }
    }

    return true;
  }

  AttrStore &NewCommonAttrStore(size_t num) {
    attr_stores_.emplace_back(num * size_);
    return attr_stores_.back();
  }

  AttrStore &NewItemAttrStore(uint64_t item_key, size_t num) {
    attr_stores_.emplace_back(item_key, num * size_);
    return attr_stores_.back();
  }

  // add sign to request, may send request immediately if max_signs_per_request_ > 0
  void AddSign(uint64_t val, std::function<double *(void)> addr_getter, MutableRecoContextInterface *context,
               const std::string &kess_service) {
    uint64_t sign = id_conv_->ConvertKey(val, slot_, 1);
    auto it_inserted =
        mapping_sign_to_addr_getter_.emplace(sign, std::initializer_list<std::function<double *(void)>>{});
    if (it_inserted.second) {
      if (protocol_ == 1) {
        request_signs_.emplace_back(sign);
      } else {
        if (client_side_shard_) {
          int shard = sign % shards_;
          requests_[shard].add_signs(sign);
          if (max_signs_per_request_ > 0 && requests_[shard].signs_size() >= max_signs_per_request_) {
            SendRequestToShard(context, requests_[shard], shard_prefix_ + base::IntToString(shard),
                               kess_service);
            requests_[shard].Clear();
          }
        } else {
          requests_[0].add_signs(sign);
          if (max_signs_per_request_ > 0 && requests_[0].signs_size() >= max_signs_per_request_) {
            for (auto i = 0; i < shards_; i++) {
              SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
            }
            requests_[0].Clear();
          }
        }
      }
    }
    it_inserted.first->second.emplace_back(addr_getter);
  }

  void ClearRequests() {
    mapping_sign_to_addr_getter_.clear();
    attr_stores_.clear();
    if (protocol_ == 0) {
      for (auto &request : requests_) {
        request.Clear();
      }
    } else if (protocol_ == 1) {
      request_signs_.clear();
    } else {
      // avoid static check
      CL_LOG(ERROR) << "Unexpected protocol:" << protocol_;
      requests_.clear();
      size_ = 0;
      protocol_ = 2;
      raw_data_type_ = RawDataType::kSignedInt16;
      is_raw_data_ = false;
    }
  }

  void SendRequests(MutableRecoContextInterface *context, const std::string &kess_service) {
    if (protocol_ == 0) {
      for (auto i = 0; i < shards_; i++) {
        if (client_side_shard_) {
          if (requests_[i].signs_size() > 0) {
            SendRequestToShard(context, requests_[i], shard_prefix_ + base::IntToString(i), kess_service);
          }
        } else {
          if (requests_[0].signs_size() > 0) {
            SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
          }
        }
      }
    } else {
      SendColossusdbRequests(context, request_signs_);
    }
  }

  colossusdb::EmbeddingClient *GetColossusdbClient() {
    if (clsdb_emb_client_config_.use_kconf_client) {
      clsdb_client_ =
          colossusdb::EmbeddingClient::GetOrCreateEmbKconfClientSingleton(clsdb_emb_client_config_)
              ->GetClient();
      if (!clsdb_client_) {
        CL_LOG(ERROR) << "Get Embedding Kconf client failed! Check your colossusdb_embd config.";
        return nullptr;
      }
      return clsdb_client_.get();
    }
    return colossusdb::EmbeddingClient::GetOrCreateEmbClientSingleton(clsdb_emb_client_config_);
  }

 protected:
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  std::string kess_cluster_;
  int timeout_ms_;
  int shards_;
  std::string shard_prefix_;
  bool client_side_shard_;
  int max_signs_per_request_;

  int protocol_ = 0;
  size_t size_ = 0;

  // for protocol 0
  std::vector<AttrStore> attr_stores_;
  Map<uint64_t, std::vector<std::function<double *(void)>>> mapping_sign_to_addr_getter_;

  CommonRecoObjectPool<ks::reco::bt_embd_s::BatchEmbeddingsResponse> response_pool_;
  std::vector<ks::reco::bt_embd_s::BatchEmbeddingsRequest> requests_;
  // end for protocol 0

  // for protocol 1
  colossusdb::ClsdbEmbClientConfig clsdb_emb_client_config_;
  std::shared_ptr<colossusdb::EmbeddingClient> clsdb_client_;
  std::unique_ptr<colossusdb::EmbeddingMapResponse> clsdb_resp_;

  std::vector<uint64_t> request_signs_;
  // end for protocol 1

  bool is_raw_data_ = false;
  enum class RawDataType {
    kSignedInt8,
    kSignedInt16,
    kSignedInt32,
    kSignedInt64,
    kUnsignedInt8,
    kUnsignedInt16,
    kUnsignedInt32,
    kUnsignedInt64,
    kFloat32,
    kString,
    kScaledInt8,
  } raw_data_type_ = RawDataType::kSignedInt16;
  uint64 slot_;
  IdConverter *id_conv_ = nullptr;
  std::string output_attr_name_ = "";
  std::string input_attr_name_ = "";
  enum class QuerySourceType {
    kItemKey,
    kItemId,
    kUserId,
    kDeviceId,
    kItemAttr,
    kCommonAttr,
  } query_source_type_;

  serving_base::Timer timer_;

  std::shared_ptr<int> exit_handler_;

  void FillItemAttr(MutableRecoContextInterface *context,
                    const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                    const std::string &shard_name);

  void FillCommonAttr(MutableRecoContextInterface *context,
                      const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                      const std::string &shard_name);

  void SendRequestToShard(MutableRecoContextInterface *context,
                          const ks::reco::bt_embd_s::BatchEmbeddingsRequest &request, std::string shard_name,
                          const std::string &kess_service);

  void SendColossusdbRequests(MutableRecoContextInterface *context,
                              const std::vector<uint64_t> &request_signs);

  void SaveEmbeddingAttr() {
    if (!clsdb_resp_) {
      CL_LOG(ERROR) << "nullptr response!";
      return;
    }
    for (auto iter = clsdb_resp_->responses.begin(); iter != clsdb_resp_->responses.end(); iter++) {
      const colossusdb::EmbeddingValue &ele = iter->second;
      if (ele.size <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      int dim = ele.size / sizeof(int16);
      VLOG(1) << "sign: " << sign << ", dim: " << dim;
      if (dim != size_) {
        CL_LOG(WARNING) << "Invalid dim for " << sign << ": " << dim;
        continue;
      }

      auto it = mapping_sign_to_addr_getter_.find(sign);
      if (it == mapping_sign_to_addr_getter_.end()) {
        CL_LOG(WARNING) << "Invalid sign found: " << sign;
        continue;
      }

      for (auto &addr_getter : it->second) {
        auto *weights = reinterpret_cast<const int16 *>(ele.data);
        double *addr = addr_getter();
        for (int j = 0; j < dim; j++) {
          addr[j] = ks::reco::WeightToFloat(weights[j]);
        }
      }
    }
  }

  template <typename T>
  void SaveRawDataAttr() {
    if (!clsdb_resp_) {
      CL_LOG(ERROR) << "nullptr response!";
      return;
    }
    for (auto iter = clsdb_resp_->responses.begin(); iter != clsdb_resp_->responses.end(); iter++) {
      const colossusdb::EmbeddingValue &ele = iter->second;
      if (ele.size <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      size_t element_size = sizeof(T);
      int dim = ele.size / element_size;
      VLOG(1) << "sign: " << sign << ", dim: " << dim;
      if (dim != size_) {
        CL_LOG(WARNING) << "Invalid dim for " << sign << ": " << dim;
        continue;
      }
      if (dim * element_size != ele.size) {
        CL_LOG(ERROR) << "Unexpected size: " << ele.size << " for sizeof(" << typeid(T).name()
                      << ") = " << element_size;
        continue;
      }

      auto it = mapping_sign_to_addr_getter_.find(sign);
      if (it == mapping_sign_to_addr_getter_.end()) {
        CL_LOG(WARNING) << "Invalid sign found: " << sign;
        continue;
      }

      for (auto &addr_getter : it->second) {
        auto *weights = reinterpret_cast<const T *>(ele.data);
        double *addr = addr_getter();
        for (int j = 0; j < dim; j++) {
          addr[j] = static_cast<double>(weights[j]);  // int to double cast
        }
      }
    }
  }

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRemoteEmbeddingAttrLiteEnricher);
};
}  // namespace platform
}  // namespace ks
