#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoAttrDefaultValueEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAttrDefaultValueEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct DefaultValueConfig {
    AttrType attr_type = AttrType::UNKNOWN;
    const base::Json *value_config = nullptr;
    std::string attr_name;
    ItemAttr *accessor = nullptr;
    bool clear_existing_value = false;
  };
  bool InitProcessor() override {
    auto item_default_value_configs = config()->Get("item_attrs");
    if (item_default_value_configs && item_default_value_configs->IsArray()) {
      for (const auto *value_config : item_default_value_configs->array()) {
        if (!value_config->IsObject()) {
          LOG(ERROR) << "CommonRecoAttrDefaultValueEnricher init failed! Values of 'item_attrs' "
                     << "should be an object";
          return false;
        }
        DefaultValueConfig default_value_config;
        if (!ExtractDefaultValueConfigFromConfig(&default_value_config, value_config)) {
          return false;
        }
        item_default_value_configs_.emplace_back(std::move(default_value_config));
      }
    }

    return true;
  }

  bool ExtractDefaultValueConfigFromConfig(DefaultValueConfig *value, const base::Json *config) {
    value->attr_type = RecoUtil::ParseAttrType(config->GetString("type"));
    if (value->attr_type == AttrType::UNKNOWN) {
      LOG(ERROR) << "CommonRecoAttrDefaultValueEnricher init failed! Values of 'type' "
                 << "is not valid";
      return false;
    }
    value->attr_name = config->GetString("name");
    if (value->attr_name.empty()) {
      LOG(ERROR) << "CommonRecoAttrDefaultValueEnricher init failed! name should be a string";
      return false;
    }
    value->value_config = config->Get("value");
    if (!value->value_config) {
      LOG(ERROR) << "CommonRecoAttrDefaultValueEnricher init failed! value should not be emtpy";
      return false;
    }
    value->clear_existing_value = config->GetBoolean("clear_existing_value", false);
    return true;
  }

  void SetItemDefaultValues(MutableRecoContextInterface *context,
                            const DefaultValueConfig &default_value_config);

 private:
  std::vector<DefaultValueConfig> item_default_value_configs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAttrDefaultValueEnricher);
};

}  // namespace platform
}  // namespace ks
