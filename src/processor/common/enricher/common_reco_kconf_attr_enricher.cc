#include "dragon/src/processor/common/enricher/common_reco_kconf_attr_enricher.h"
#include "dragon/src/interop/json_value.h"
#include "dragon/src/interop/util.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

void CommonRecoKconfAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  for (const auto &kconf_config : kconf_configs_) {
    const auto &kconf_value = GetOrCreateKconfValue(context, kconf_config);
    switch (kconf_config.value_type) {
      case KconfParamType::INT64:
        context->SetIntCommonAttr(kconf_config.attr_name, kconf_value.int_value->Get());
        break;
      case KconfParamType::DOUBLE:
        context->SetDoubleCommonAttr(kconf_config.attr_name, kconf_value.double_value->Get());
        break;
      case KconfParamType::STRING:
        context->SetStringCommonAttr(kconf_config.attr_name, *kconf_value.string_value->Get());
        break;
      case KconfParamType::BOOLEAN:
        context->SetIntCommonAttr(kconf_config.attr_name, kconf_value.bool_value->Get());
        break;
      case KconfParamType::INT64_LIST: {
        auto val = *kconf_value.int_list_value->Get();
        context->SetIntListCommonAttr(kconf_config.attr_name, std::move(val));
        break;
      }
      case KconfParamType::DOUBLE_LIST: {
        auto val = *kconf_value.double_list_value->Get();
        context->SetDoubleListCommonAttr(kconf_config.attr_name, std::move(val));
        break;
      }
      case KconfParamType::STRING_LIST: {
        auto val = *kconf_value.string_list_value->Get();
        context->SetStringListCommonAttr(kconf_config.attr_name, std::move(val));
        break;
      }
      case KconfParamType::BOOLEAN_LIST: {
        auto p = kconf_value.bool_list_value->Get();
        std::vector<int64> val(p->begin(), p->end());
        context->SetIntListCommonAttr(kconf_config.attr_name, std::move(val));
        break;
      }
      case KconfParamType::JSON:
        if (kconf_config.is_common_attr) {
          std::string json_path = GetStringProcessorParameter(context, kconf_config.json_path);
          std::vector<std::string> path_vec = absl::StrSplit(json_path, '.', absl::SkipEmpty());
          int num = 0;
          if (path_vec.empty()) {
            if (!kconf_config.default_value) {
              CL_LOG_ERROR("kconf", "invalid_json_path:" + json_path)
                  << "Kconf enrich failed on common_attr " << kconf_config.attr_name
                  << ", invalid json_path: " << json_path;
            }
          } else {
            std::shared_ptr<Json::Value> json_value = kconf_value.json_value->Get();
            // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
            // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
            try {
              num =
                  interop::SaveJsonValueToCommonAttr(context, kconf_config.attr_name, *json_value, path_vec);
            } catch (const std::exception &e) {
              CL_LOG_ERROR("kconf", "json_exception:" + std::string(e.what()))
                  << "failed to parse kconf json: " << e.what()
                  << ", kconf key: " << kconf_config.kconf_key->StringValue();
            }
          }

          if (num == 0 && kconf_config.default_value) {
            const auto *default_value = kconf_config.default_value;
            if (default_value->IsInteger()) {
              context->SetIntCommonAttr(kconf_config.attr_name, default_value->IntValue(0L));
            } else if (default_value->IsDouble()) {
              context->SetDoubleCommonAttr(kconf_config.attr_name, default_value->FloatValue(0.0));
            } else if (default_value->IsString()) {
              context->SetStringCommonAttr(kconf_config.attr_name, default_value->StringValue());
            } else if (default_value->IsBoolean()) {
              context->SetIntCommonAttr(kconf_config.attr_name, default_value->BooleanValue(false));
            } else if (default_value->IsArray() && default_value->size() > 0) {
              auto it = default_value->array_begin();
              if ((*it)->IsInteger()) {
                std::vector<int64> val;
                for (; it != default_value->array_end(); it++) {
                  val.emplace_back((*it)->IntValue(0L));
                }
                context->SetIntListCommonAttr(kconf_config.attr_name, std::move(val));
              } else if ((*it)->IsDouble()) {
                std::vector<double> val;
                for (; it != default_value->array_end(); it++) {
                  val.emplace_back((*it)->NumberValue(0.0));
                }
                context->SetDoubleListCommonAttr(kconf_config.attr_name, std::move(val));
              } else if ((*it)->IsString()) {
                std::vector<std::string> val;
                for (; it != default_value->array_end(); it++) {
                  val.emplace_back((*it)->StringValue());
                }
                context->SetStringListCommonAttr(kconf_config.attr_name, std::move(val));
              }
            }
          }
        } else {
          auto op = RecoUtil::ExtractCommonAttrFromExpr(kconf_config.json_path);
          if (!op) {
            // should not reach here
            continue;
          }
          absl::string_view attr_name = *op;
          if (!context->HasItemAttr(attr_name)) {
            CL_LOG(WARNING) << "skip " << kconf_config.attr_name << " for missing item attr: " << attr_name;
            continue;
          }
          ItemAttr *input_accessor = context->GetItemAttrAccessor(attr_name);
          ItemAttr *output_accessor = context->GetItemAttrAccessor(kconf_config.attr_name);
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            auto json_path = result.GetStringAttr(input_accessor);
            if (json_path) {
              std::vector<std::string> path_vec = absl::StrSplit(*json_path, '.', absl::SkipEmpty());
              int num = 0;
              if (path_vec.empty()) {
                CL_LOG_ERROR_EVERY("kconf", "invalid_json_path:" + std::string(*json_path), 1000)
                    << "Kconf enrich failed on item_attr " << kconf_config.attr_name
                    << ", invalid json_path: " << *json_path << ", item_key: " << result.item_key;
              } else {
                std::shared_ptr<Json::Value> json_value = kconf_value.json_value->Get();
                // XXX(fangjianbing): JsonValue 如果存在超过 int64 范围的大数会触发 runtime exception,
                // 待 infra 升级 jsoncpp 库版本后删除 try/catch 逻辑
                try {
                  num = interop::SaveJsonValueToItemAttr(result, output_accessor, *json_value, path_vec);
                } catch (const std::exception &e) {
                  CL_LOG_ERROR_EVERY("kconf", "json_exception:" + std::string(e.what()), 1000)
                      << "failed to parse kconf json: " << e.what()
                      << ", kconf key: " << kconf_config.kconf_key->StringValue();
                }
              }
              if (num == 0 && kconf_config.default_value) {
                const auto *default_value = kconf_config.default_value;
                if (default_value->IsInteger()) {
                  result.SetIntAttr(output_accessor, default_value->IntValue(0L));
                } else if (default_value->IsDouble()) {
                  result.SetDoubleAttr(output_accessor, default_value->FloatValue(0.0));
                } else if (default_value->IsString()) {
                  result.SetStringAttr(output_accessor, default_value->StringValue());
                } else if (default_value->IsBoolean()) {
                  result.SetIntAttr(output_accessor, default_value->BooleanValue(false));
                } else if (default_value->IsArray() && default_value->size() > 0) {
                  auto it = default_value->array_begin();
                  if ((*it)->IsInteger()) {
                    std::vector<int64> val;
                    for (; it != default_value->array_end(); it++) {
                      val.emplace_back((*it)->IntValue(0L));
                    }
                    context->SetIntListItemAttr(result, output_accessor, std::move(val));
                  } else if ((*it)->IsDouble()) {
                    std::vector<double> val;
                    for (; it != default_value->array_end(); it++) {
                      val.emplace_back((*it)->NumberValue(0.0));
                    }
                    context->SetDoubleListItemAttr(result, output_accessor, std::move(val));
                  } else if ((*it)->IsString()) {
                    std::vector<std::string> val;
                    for (; it != default_value->array_end(); it++) {
                      val.emplace_back((*it)->StringValue());
                    }
                    context->SetStringListItemAttr(result, output_accessor, std::move(val));
                  }
                }
              }
            }
          });
        }
        break;
      default:
        CL_LOG_ERROR("kconf", "invalid_value_type:" + std::to_string((int)kconf_config.value_type))
            << "Unsupported value type: " << static_cast<int>(kconf_config.value_type)
            << ", kconf key: " << kconf_config.kconf_key->StringValue()
            << RecoUtil::GetRequestInfoForLog(context);
    }
  }
}

const CommonRecoKconfAttrEnricher::KconfValue &CommonRecoKconfAttrEnricher::GetOrCreateKconfValue(
    ReadableRecoContextInterface *context, const KconfConfig &kconf_config) {
  std::string kconf_key = GetStringProcessorParameter(context, kconf_config.kconf_key);
  auto it = kconf_value_map_.find(kconf_key);
  if (it != kconf_value_map_.end()) {
    return it->second;
  }

  const auto *default_value = kconf_config.default_value;
  KconfValue kconf_value;
  switch (kconf_config.value_type) {
    case KconfParamType::INT64: {
      int64 default_int_value = default_value ? default_value->IntValue((int64)0) : 0;
      kconf_value.int_value = ks::infra::KConf().Get(kconf_key, default_int_value);
      break;
    }
    case KconfParamType::DOUBLE: {
      double default_double_value = default_value ? default_value->NumberValue(0.0) : 0.0;
      kconf_value.double_value = ks::infra::KConf().Get(kconf_key, default_double_value);
      break;
    }
    case KconfParamType::STRING: {
      auto default_string_value =
          std::make_shared<std::string>(default_value ? default_value->StringValue() : "");
      kconf_value.string_value = ks::infra::KConf().Get(kconf_key, default_string_value);
      break;
    }
    case KconfParamType::BOOLEAN: {
      bool default_boolean_value = default_value ? default_value->BooleanValue(false) : false;
      kconf_value.bool_value = ks::infra::KConf().Get(kconf_key, default_boolean_value);
      break;
    }
    case KconfParamType::INT64_LIST: {
      auto default_list = std::make_shared<std::vector<int64>>();
      if (default_value && default_value->IsArray()) {
        int64 v = 0;
        for (const auto *val : default_value->array()) {
          if (val->IntValue(&v)) {
            default_list->push_back(v);
          }
        }
      }
      kconf_value.int_list_value = ks::infra::KConf().GetList(kconf_key, default_list);
      break;
    }
    case KconfParamType::DOUBLE_LIST: {
      auto default_list = std::make_shared<std::vector<double>>();
      if (default_value && default_value->IsArray()) {
        double v = 0.0;
        for (const auto *val : default_value->array()) {
          if (val->NumberValue(&v)) {
            default_list->push_back(v);
          }
        }
      }
      kconf_value.double_list_value = ks::infra::KConf().GetList(kconf_key, default_list);
      break;
    }
    case KconfParamType::STRING_LIST: {
      auto default_list = std::make_shared<std::vector<std::string>>();
      if (default_value && default_value->IsArray()) {
        std::string v;
        for (const auto *val : default_value->array()) {
          if (val->StringValue(&v)) {
            default_list->push_back(v);
          }
        }
      }
      kconf_value.string_list_value = ks::infra::KConf().GetList(kconf_key, default_list);
      break;
    }
    case KconfParamType::BOOLEAN_LIST: {
      auto default_list = std::make_shared<std::vector<bool>>();
      if (default_value && default_value->IsArray()) {
        bool v = false;
        for (const auto *val : default_value->array()) {
          if (val->BooleanValue(&v)) {
            default_list->push_back(v);
          }
        }
      }
      kconf_value.bool_list_value = ks::infra::KConf().GetList(kconf_key, default_list);
      break;
    }
    case KconfParamType::JSON: {
      kconf_value.json_value = ks::infra::KConf().Get(kconf_key, std::make_shared<Json::Value>());
      break;
    }
    default:
      break;
  }

  auto pr = kconf_value_map_.insert({kconf_key, kconf_value});
  return pr.first->second;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoKconfAttrEnricher, CommonRecoKconfAttrEnricher)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoKconfCommonAttrEnricher, CommonRecoKconfAttrEnricher)

}  // namespace platform
}  // namespace ks
