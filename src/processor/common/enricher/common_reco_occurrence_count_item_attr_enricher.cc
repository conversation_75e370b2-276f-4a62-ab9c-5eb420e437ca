#include "dragon/src/processor/common/enricher/common_reco_occurrence_count_item_attr_enricher.h"

#include "base/strings/string_util.h"
#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {

void CommonRecoOccurrenceCountItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                       RecoResultConstIter begin, RecoResultConstIter end) {
  BuildAttrCounter(context);
  for (auto *counter : attr_counters_) {
    for (auto it = begin; it != end; ++it) {
      uint64 item_key = it->item_key;
      if (auto val = context->GetIntItemAttr(item_key, counter->check_attr)) {
        counter->GenItemAttr(*val, context, item_key);
      } else if (auto val = context->GetStringItemAttr(item_key, counter->check_attr)) {
        counter->GenItemAttr(*val, context, item_key);
      } else if (auto val = context->GetIntListItemAttr(item_key, counter->check_attr)) {
        counter->GenItemAttr(*val, context, item_key);
      } else if (auto val = context->GetStringListItemAttr(item_key, counter->check_attr)) {
        counter->GenItemAttr(*val, context, item_key);
      }
    }
  }
}

bool CommonRecoOccurrenceCountItemAttrEnricher::InitProcessor() {
  auto *counters_config = config()->Get("counters");
  if (!counters_config || !counters_config->IsArray()) {
    LOG(ERROR) << "CommonRecoOccurrenceCountItemAttrEnricher"
               << " init failed! Missing \"counters\" config or it is"
               << " not an array.";
    return false;
  }

  for (auto *counter_cfg : counters_config->array()) {
    if (!CheckCounterConfig(counter_cfg)) {
      return false;
    }
  }

  return true;
}

bool CommonRecoOccurrenceCountItemAttrEnricher::CheckCounterConfig(base::Json *counter_cfg) {
  std::string output_attr = counter_cfg->GetString("output_attr_name");
  if (output_attr.empty()) {
    LOG(ERROR) << "CommonRecoOccurrenceCountItemAttrEnricher"
               << " init failed! Empty \"output_attr_name\"!";
    return false;
  }

  auto *counter = attr_counters_.Get(output_attr);
  if (counter == nullptr) {
    counter = new AttrCounter();
    attr_counters_.Insert(output_attr, counter);
  } else {
    LOG(ERROR) << "CommonRecoOccurrenceCountItemAttrEnricher"
               << " init failed! Duplicate \"output_attr_name\": " << output_attr;
    return false;
  }
  counter->output_name = output_attr;

  counter->check_attr = counter_cfg->GetString("check_attr_name");
  if (counter->check_attr.empty()) {
    LOG(ERROR) << "CommonRecoOccurrenceCountItemAttrEnricher"
               << " init failed! Empty \"check_attr_name\"!";
    return false;
  }

  auto *value_set = counter_cfg->Get("check_values");
  if (!value_set || !value_set->IsArray()) {
    LOG(ERROR) << "CommonRecoOccurrenceCountItemAttrEnricher"
               << " init failed! Missing config \"check_values\" or it's"
               << " not an array";
    return false;
  }
  counter->check_values = new base::Json(base::StringToJson(value_set->ToString()));

  counter->single_limit = counter_cfg->GetInt("single_limit", -1);
  counter->total_limit = counter_cfg->GetInt("total_limit", -1);
  counter->length_limit_for_check_attr_config = counter_cfg->Get("length_limit_for_check_attr");
  counter->max_count = counter_cfg->GetInt("max_count", -1);

  return true;
}

void CommonRecoOccurrenceCountItemAttrEnricher::BuildAttrCounter(ReadableRecoContextInterface *context) {
  for (auto *counter : attr_counters_) {
    counter->ClearCountMap();
    ResolveValueSet(context, counter);
  }
}

void CommonRecoOccurrenceCountItemAttrEnricher::ResolveValueSet(ReadableRecoContextInterface *context,
                                                                AttrCounter *counter) {
  auto *value_set = counter->check_values;
  if (!value_set) {
    CL_LOG(WARNING) << "cannot resolve value set, check_values is null";
    return;
  }
  counter->length_limit_for_check_attr =
      GetIntProcessorParameter(context, counter->length_limit_for_check_attr_config, -1);

  int single_limit = counter->single_limit;
  int total_limit = counter->total_limit;
  int total_insert = 0;
  for (auto *value : value_set->array()) {
    if (total_limit >= 0 && total_insert >= total_limit) {
      CL_LOG_EVERY_N(WARNING, 100) << "value counter total_limit reached: " << total_limit
                                   << ", you may lose some value count!";
      break;
    }
    int single_insert = 0;
    if (value->IsInteger()) {
      // 整数类型
      counter->InsertValue(value->IntValue(-1));
    } else if (value->IsString()) {
      if (auto op = RecoUtil::ExtractCommonAttrFromExpr(value)) {
        absl::string_view common_attr = *op;
        // 如果是 {{ }} 格式，则按 CommonAttr 处理
        // 依次从 int/string/int_list/string_list attr 里尝试获取
        if (auto p = context->GetIntCommonAttr(common_attr)) {
          counter->InsertValue(*p);
          single_insert++;
          total_insert++;
        } else if (auto p = context->GetStringCommonAttr(common_attr)) {
          counter->InsertValue(base::CityHash64(p->data(), p->size()));
          single_insert++;
          total_insert++;
        } else if (auto p = context->GetIntListCommonAttr(common_attr)) {
          for (const int64 key : *p) {
            if (total_limit >= 0 && total_insert >= total_limit) {
              CL_LOG_EVERY_N(WARNING, 100)
                  << "value counter total_limit reached: " << total_limit << ", int list common_attr "
                  << common_attr << " is partially read";
              break;
            }
            if (single_limit >= 0 && single_insert >= single_limit) {
              CL_LOG_EVERY_N(WARNING, 100)
                  << "value counter single_limit reached: " << single_limit << ", int list common_attr "
                  << common_attr << " is partially read";
              break;
            }
            counter->InsertValue(key);
            single_insert++;
            total_insert++;
          }
        } else if (auto p = context->GetStringListCommonAttr(common_attr)) {
          for (auto key : *p) {
            if (total_limit >= 0 && total_insert >= total_limit) {
              CL_LOG_EVERY_N(WARNING, 100)
                  << "value counter total_limit reached: " << total_limit << ", string list common_attr "
                  << common_attr << " is partially read";
              break;
            }
            if (single_limit >= 0 && single_insert >= single_limit) {
              CL_LOG_EVERY_N(WARNING, 100)
                  << "value counter single_limit reached: " << single_limit << ", string list common_attr "
                  << common_attr << " is partially read";
              break;
            }
            counter->InsertValue(base::CityHash64(key.data(), key.size()));
            single_insert++;
            total_insert++;
          }
        }
      } else {
        // 否则当一个普通值
        std::string value_text = value->StringValue();
        counter->InsertValue(base::CityHash64(value_text.data(), value_text.size()));
        single_insert++;
        total_insert++;
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoOccurrenceCountItemAttrEnricher,
                 CommonRecoOccurrenceCountItemAttrEnricher)

}  // namespace platform
}  // namespace ks
