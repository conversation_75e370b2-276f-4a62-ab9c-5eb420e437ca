#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoAttrTypeCastEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAttrTypeCastEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct ItemAttrTypeCastConfig {
    std::string from_item_attr;
    std::string to_item_attr;
    AttrType to_type = AttrType::UNKNOWN;
    ItemAttr *from_accessor = nullptr;
    ItemAttr *to_accessor = nullptr;
  };
  struct CommonAttrTypeCastConfig {
    std::string from_common_attr;
    std::string to_common_attr;
    AttrType to_type = AttrType::UNKNOWN;
  };

 private:
  bool InitProcessor() override {
    auto *configs = config()->Get("attr_type_cast_configs");
    if (!configs || !configs->IsArray()) {
      LOG(ERROR) << "CommonRecoAttrTypeCastEnricher init failed! Missing 'attr_type_cast_configs' config"
                 << " or it is not an array.";
      return false;
    }
    item_attr_type_cast_configs_.clear();
    common_attr_type_cast_configs_.clear();
    for (const auto *cfg : configs->array()) {
      if (!cfg->IsObject()) {
        LOG(ERROR) << "CommonRecoAttrTypeCastEnricher init failed! 'attr_type_cast_configs' config"
                   << " should be an array of object.";
        return false;
      }
      auto to_type = RecoUtil::ParseAttrType(cfg->GetString("to_type"));
      if (to_type == AttrType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoAttrTypeCastEnricher init failed! Missing 'to_type' config"
                   << " or it is empty string.";
        return false;
      }
      if (to_type != AttrType::INT && to_type != AttrType::FLOAT && to_type != AttrType::STRING
          && to_type != AttrType::FLOAT16 && to_type != AttrType::FLOAT32) {
        LOG(ERROR) << "CommonRecoAttrTypeCastEnricher init failed! 'to_type' config"
                   << " should be 'int', 'float' or 'string' or 'float16' or 'float32'.";
        return false;
      }
      if (cfg->Get("from_item_attr") && cfg->Get("to_item_attr")) {
        ItemAttrTypeCastConfig item_attr_type_cast_config;
        item_attr_type_cast_config.from_item_attr = cfg->GetString("from_item_attr");
        item_attr_type_cast_config.to_item_attr = cfg->GetString("to_item_attr");
        item_attr_type_cast_config.to_type = to_type;
        item_attr_type_cast_configs_.emplace_back(std::move(item_attr_type_cast_config));
      } else if (cfg->Get("from_common_attr") && cfg->Get("to_common_attr")) {
        CommonAttrTypeCastConfig common_attr_type_cast_config;
        common_attr_type_cast_config.from_common_attr = cfg->GetString("from_common_attr");
        common_attr_type_cast_config.to_common_attr = cfg->GetString("to_common_attr");
        common_attr_type_cast_config.to_type = to_type;
        common_attr_type_cast_configs_.emplace_back(std::move(common_attr_type_cast_config));
      } else {
        LOG(ERROR) << "CommonRecoAttrTypeCastEnricher init failed! Missing 'from_item_attr' and "
                      "'to_item_attr' config or 'from_common_attr' or 'to_common_attr' config";
        return false;
      }
    }
    return true;
  }

 private:
  std::vector<ItemAttrTypeCastConfig> item_attr_type_cast_configs_;
  std::vector<CommonAttrTypeCastConfig> common_attr_type_cast_configs_;
  bool initialized_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAttrTypeCastEnricher);
};

}  // namespace platform
}  // namespace ks
