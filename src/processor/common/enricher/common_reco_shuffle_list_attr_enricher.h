#pragma once

#include <string>
#include <utility>
#include <vector>

#include "base/random/pseudo_random.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoShuffleListAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoShuffleListAttrEnricher() : random_(base::GetTimestamp()) {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void ProcessCommonAttr(MutableRecoContextInterface *context);

  void ProcessItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    common_attr_ = config()->GetString("common_attr");
    item_attr_ = config()->GetString("item_attr");

    if (common_attr_.empty() && item_attr_.empty()) {
      LOG(ERROR) << "CommonRecoShuffleListAttrEnricher init failed! common_attr or item_attr is required";
      return false;
    }
    return true;
  }

 private:
  std::string common_attr_;
  std::string item_attr_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoShuffleListAttrEnricher);
};

}  // namespace platform
}  // namespace ks
