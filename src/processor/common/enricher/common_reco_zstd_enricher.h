#pragma once
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoZstdEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoZstdEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void ProcessStrCommonAttr(MutableRecoContextInterface *context);

  void ProcessStrItemAttr(MutableRecoContextInterface *context,
                          const RecoResultConstIter &begin, const RecoResultConstIter &end);

 private:
  bool InitProcessor() override {
    is_common_attr_ = !config()->GetString("input_common_attr", "").empty();
    const std::string mode = config()->GetString("mode");
    if (mode.empty()) {
      LOG(ERROR) << "CommonRecoZstdEnricher init failed!"
                 << " Missing 'mode' config.";
      return false;
    }
    if (mode == "compress") {
      is_compressing_ = true;
    } else if (mode == "decompress") {
      is_compressing_ = false;
    } else {
      LOG(ERROR) << "CommonRecoZstdEnricher init failed!"
                 << "'mode' should be 'compress' or 'decompress', value found: " << mode;
      return false;
    }
    input_attr_ = is_common_attr_ ? config()->GetString("input_common_attr")
                                  : config()->GetString("input_item_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoZstdEnricher init failed!"
                 << " Missing 'input_attr' config.";
      return false;
    }
    output_attr_ = is_common_attr_ ? config()->GetString("output_common_attr")
                                   : config()->GetString("output_item_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoZstdEnricher init failed!"
                 << " Missing 'output_attr' config.";
      return false;
    }
    return true;
  }

  inline int GetCompressionLevel(ReadableRecoContextInterface *context) {
    int compression_level = GetIntProcessorParameter(context, "compression_level", 0);
    if (compression_level < -5) {
      LOG(WARNING) << "'compression_level' must in range: [-5, 22], instead of " << compression_level;
      compression_level = -5;
    } else if (compression_level > 22) {
      LOG(WARNING) << "'compression_level' must in range: [-5, 22], instead of " << compression_level;
      compression_level = 22;
    }
    return compression_level;
  }

 private:
  bool is_compressing_ = true;
  std::string input_attr_;
  std::string output_attr_;
  bool is_common_attr_ = true;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoZstdEnricher);
};

}  // namespace platform
}  // namespace ks
