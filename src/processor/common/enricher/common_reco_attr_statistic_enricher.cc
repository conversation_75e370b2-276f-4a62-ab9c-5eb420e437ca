#include "dragon/src/processor/common/enricher/common_reco_attr_statistic_enricher.h"

#include "third_party/abseil/absl/container/flat_hash_map.h"

namespace ks {
namespace platform {

void CommonRecoAttrStatisticEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  base::JsonObject check_point_json;
  for (const auto &attr_stats_config : attr_stats_configs_) {
    const std::string &attr_name = attr_stats_config.attr_name;
    auto *accessor = context->GetItemAttrAccessor(attr_name);
    if (accessor->value_type != AttrType::INT && accessor->value_type != AttrType::STRING) {
      CL_LOG_WARNING("attr_statistic", "invalid_attr_type") << "only support \"INT&STRING\" type";
      continue;
    }
    base::JsonObject attr_stats_json;
    const StatsType &stats_type = attr_stats_config.stats_type;
    const std::string &save_values_to = attr_stats_config.save_values_to;
    const std::string &save_results_to = attr_stats_config.save_results_to;

    switch (stats_type) {
      case StatsType::Count: {
        if (accessor->value_type == AttrType::INT) {
          folly::F14FastMap<int64, int> attr_count;
          std::for_each(begin, end, [accessor, &attr_count](const CommonRecoResult &result) {
            auto val = result.GetIntAttr(accessor);
            if (val) {
              ++attr_count[*val];
            }
          });
          int attr_count_size = attr_count.size();
          std::vector<int64> attr_values;
          attr_values.reserve(attr_count_size);
          std::vector<int64> attr_results;
          attr_results.reserve(attr_count_size);
          for (const auto &pr : attr_count) {
            if (!save_as_json_to_.empty()) {
              attr_stats_json.set(std::to_string(pr.first), pr.second);
            }
            if (!save_values_to.empty()) {
              attr_values.emplace_back(pr.first);
            }
            if (!save_results_to.empty()) {
              attr_results.emplace_back(pr.second);
            }
          }
          if (!save_values_to.empty()) {
            context->SetIntListCommonAttr(save_values_to, std::move(attr_values));
          }
          if (!save_results_to.empty()) {
            context->SetIntListCommonAttr(save_results_to, std::move(attr_results));
          }
        } else if (accessor->value_type == AttrType::STRING) {
          absl::flat_hash_map<std::string, int> attr_count;
          std::for_each(begin, end, [accessor, &attr_count](const CommonRecoResult &result) {
            auto val = result.GetStringAttr(accessor);
            if (val) {
              ++attr_count[*val];
            }
          });
          int attr_count_size = attr_count.size();
          std::vector<std::string> attr_values;
          attr_values.reserve(attr_count_size);
          std::vector<int64> attr_results;
          attr_results.reserve(attr_count_size);
          for (const auto &pr : attr_count) {
            if (!save_as_json_to_.empty()) {
              attr_stats_json.set(pr.first, pr.second);
            }
            if (!save_values_to.empty()) {
              attr_values.emplace_back(pr.first);
            }
            if (!save_results_to.empty()) {
              attr_results.emplace_back(pr.second);
            }
          }
          if (!save_values_to.empty()) {
            context->SetStringListCommonAttr(save_values_to, std::move(attr_values));
          }
          if (!save_results_to.empty()) {
            context->SetIntListCommonAttr(save_results_to, std::move(attr_results));
          }
        }
        if (!save_as_json_to_.empty()) {
          check_point_json.set(attr_name, attr_stats_json);
        }
        break;
      }
      default: {
        CL_LOG_WARNING("attr_statistic", "invalid_aggregator") << "only support \"count\" type";
      }
    }
  }
  if (!save_as_json_to_.empty()) {
    auto val = context->GetStringCommonAttr(save_as_json_to_);
    if (val) {
      base::Json attr_stats(base::BufferToJson(val->data(), val->size()));
      attr_stats.set(check_point_, check_point_json);
      context->SetStringCommonAttr(save_as_json_to_, attr_stats.ToString());
    } else {
      base::JsonObject attr_stats;
      attr_stats.set(check_point_, check_point_json);
      context->SetStringCommonAttr(save_as_json_to_, attr_stats.ToString());
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAttrStatisticEnricher, CommonRecoAttrStatisticEnricher)

}  // namespace platform
}  // namespace ks
