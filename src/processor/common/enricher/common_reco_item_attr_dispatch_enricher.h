#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {

class CommonRecoItemAttrDispatchEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemAttrDispatchEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class DispatchMode : int { RANGE = 0, PREV };

  bool InitRangeBound(const base::Json *range_config, const std::string &bound_key,
                      const std::string &parent_key, bool use_default, int64 *bound) {
    base::Json *bound_config = range_config->Get(bound_key);
    if ((bound_config && !bound_config->IntValue(bound)) || (!bound_config && !use_default)) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. " << parent_key << " should have an int "
                 << bound_key;
      return false;
    }
    if (*bound < 0) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. " << parent_key << "'s " << bound_key
                 << " do not support negative number";
      return false;
    }
    return true;
  }

  bool InitRange(const base::Json *config, const std::string &key, std::pair<int64, int64> *range,
                 bool use_default_start, bool use_default_end) {
    base::Json *range_config = config->Get(key);
    if (!range_config || !range_config->IsObject()) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. "
                 << "should define " << key;
      return false;
    }
    if (!InitRangeBound(range_config, "start", key, use_default_start, &range->first) ||
        !InitRangeBound(range_config, "end", key, use_default_end, &range->second)) {
      return false;
    }
    if ((range->second != 0 && range->first >= range->second) || (range->second == 0 && !use_default_end)) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. " << key
                 << "'s start should be less than end";
      return false;
    }
    return true;
  }

  bool InitProcessor() override {
    if (!RecoUtil::ExtractStringListFromJsonConfig(config()->Get("attrs"), &attrs_)) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. "
                 << "'attrs' should be an array of string";
      return false;
    }

    auto *output_prefix = config()->Get("output_prefix");
    if (output_prefix && !output_prefix->StringValue(&output_prefix_)) {
      LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. "
                 << "'output_prefix' should be a string";
      return false;
    }

    auto *prev_n = config()->Get("prev_n");
    if (prev_n) {
      mode_ = DispatchMode::PREV;
      include_self_ = config()->GetBoolean("include_self", true);
    } else {
      mode_ = DispatchMode::RANGE;

      dispatch_to_common_attr_ = config()->GetBoolean("dispatch_to_common_attr", false);

      if (!InitRange(config(), "from_item_range", &from_range_, true, false)) {
        return false;
      }

      if (dispatch_to_common_attr_) {
        if (config()->Get("to_item_range")) {
          LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. "
                     << "to_item_range should be unset when dispatch_to_common_attr";
          return false;
        }
      } else {
        if (!InitRange(config(), "to_item_range", &to_range_, false, true)) {
          return false;
        }
      }

      if (!dispatch_to_common_attr_ && from_range_.second > to_range_.first) {
        LOG(ERROR) << "CommonRecoItemAttrDispatchEnricher init failed. "
                   << "'from_item_range' should be in front of to_item_range";
        return false;
      }
    }

    return true;
  }

  void HandleByRange(MutableRecoContextInterface *context, RecoResultConstIter begin,
                     RecoResultConstIter end);
  void HandleByPrev(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                    int prev_n, const std::string &attr);

 private:
  DispatchMode mode_ = DispatchMode::RANGE;

  std::pair<int64, int64> from_range_;
  std::pair<int64, int64> to_range_;
  bool dispatch_to_common_attr_;

  bool include_self_ = true;

  std::vector<std::string> attrs_;
  std::string output_prefix_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemAttrDispatchEnricher);
};

}  // namespace platform
}  // namespace ks
