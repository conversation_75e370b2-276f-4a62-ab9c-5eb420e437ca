#pragma once

#include <vector>
#include <string>
#include <unordered_map>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoSelectSignEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoSelectSignEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  void ProcessCommonAttrs(MutableRecoContextInterface *context);
  void ProcessItemAttrs(MutableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", false);
    reserve_size_ = config()->GetInt("reserve_size", 0);
    input_slot_attr_ = config()->GetString("input_slot_attr", "");
    input_sign_attr_ = config()->GetString("input_sign_attr", "");
    if (input_slot_attr_.empty() || input_sign_attr_.empty()) {
      LOG(ERROR) << "input_slot_attr or input_sign_attr is empty!";
      return false;
    }
    auto *select_slots = config()->Get("select_slots");
    auto *output_sign_attrs = config()->Get("output_sign_attrs");
    if (!select_slots || !output_sign_attrs) {
      LOG(ERROR) << "not set 'select_slots' or 'output_sign_attrs'";
      return false;
    }
    bool extra_res1 = RecoUtil::ExtractIntListFromJsonConfig(select_slots, &select_slots_);
    bool extra_res2 = RecoUtil::ExtractStringListFromJsonConfig(output_sign_attrs, &output_sign_attrs_);
    if (!extra_res1 || !extra_res2 || select_slots_.empty() ||
        select_slots_.size() != output_sign_attrs_.size()) {
      LOG(ERROR) << "select_slots & output_sign_attrs must set and with same length!";
      return false;
    }
    select_slot_number_ = select_slots_.size();
    for (int i = 0; i < select_slot_number_; ++i) {
      select_slots_id_to_idx_[select_slots_[i]] = i;
    }
    if (reserve_size_ < 0) {
      reserve_size_ = 0;
    }
    return true;
  }

 private:
  bool is_common_attr_ = false;
  int reserve_size_ = 0;
  std::string input_slot_attr_;
  std::string input_sign_attr_;
  std::vector<int64> select_slots_;
  std::vector<std::string> output_sign_attrs_;

  // 内部使用
  std::unordered_map<int64, int> select_slots_id_to_idx_;
  int select_slot_number_ = 0;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoSelectSignEnricher);
};

}  // namespace platform
}  // namespace ks
