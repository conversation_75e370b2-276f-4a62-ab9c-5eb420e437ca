#include "dragon/src/processor/common/enricher/common_reco_common_predict_item_attr_enricher.h"

#include "base/strings/string_util.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "learning/kuiba/base/constant.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

static const int kLogInterval = 100;

bool CommonRecoCommonPredictItemAttrEnricher::InitProcessor() {
  loss_.clear();
  // loss 为数组
  auto loss = config()->Get("loss_function_name");
  if (loss && loss->IsArray()) {
    std::string str;
    for (const auto loss_json : loss->array()) {
      if (loss_json->StringValue(&str)) {
        loss_.push_back(str);
      }
    }
  }

  if (loss_.empty()) {
    LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed! no "
               << "loss config!";
    return false;
  }

  // loss default value 改为数组
  default_loss_values_.clear();
  auto default_loss_value_json = config()->Get("loss_default_value");
  if (default_loss_value_json && (default_loss_value_json->IsArray())) {
    double value = 0.0;
    for (const auto loss_value_json : default_loss_value_json->array()) {
      if (loss_value_json->NumberValue(&value)) {
        default_loss_values_.push_back(value);
      }
    }
    if (default_loss_values_.size() != loss_.size()) {
      LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed! "
                 << "loss_default_value array_size != loss array size";
      return false;
    }
  } else {
    // 考虑对上一代的兼容
    default_loss_value_ = config()->GetNumber("loss_default_value", -1.0f);
    default_loss_values_.resize(loss_.size(), default_loss_value_);
  }

  service_group_ = config()->GetString("service_group", "PRODUCTION");
  timeout_ms_ = config()->GetInt("timeout_ms", 300);
  if (timeout_ms_ <= 0) {
    LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed! timeout_ms must be > 0";
    return false;
  }

  output_attr_prefix_ = config()->GetString("output_prefix", "");

  // attr_name_map
  static const std::string kTransformMapName = "attr_name_transform_map";
  const auto *attr_name_map_config = config()->Get(kTransformMapName);
  attr_name_transform_map_.clear();
  if (attr_name_map_config && attr_name_map_config->IsObject()) {
    for (auto &kv : attr_name_map_config->objects()) {
      const base::Json *val = kv.second;
      if (base::StartsWith(kv.first, "__", true)) {
        CL_LOG(INFO) << "skip private key name: " << kv.first;
        continue;
      }
      if (val == nullptr || !val->IsString()) {
        LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed! Config " << kTransformMapName
                   << ": value of key " << kv.first << " is not a string!";
        return false;
      }
      std::string val_str = val->StringValue("");
      if (val_str.empty()) {
        LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed! Config " << kTransformMapName
                   << ": value of key " << kv.first << " is empty string!";
        return false;
      }
      attr_name_transform_map_.insert(std::make_pair(kv.first, val_str));
    }
  }

  // 是否使用 grpc_samplelist 的 user attr
  use_sample_list_attr_flag_ = config()->GetBoolean("use_sample_list_attr_flag", false);
  use_sample_list_attr_flatten_ = config()->GetBoolean("use_sample_list_attr_flatten", false);
  sample_list_common_attr_key_ = config()->GetString("sample_list_common_attr_key");

  // 是否取消 warning
  disable_common_attr_missing_warning_ = config()->GetBoolean("disable_common_attr_missing_warning", false);

  // 是否使用 item_type
  auto *item_type_json = config()->Get("item_type");
  target_item_type_only_ = false;
  item_type_ = -1;
  if (item_type_json && (item_type_json->IntValue(&item_type_))) {
    target_item_type_only_ = true;
  }

  item_key_attr_ = config()->GetString("item_key_attr");

  // 是否对未登录用户过滤
  filter_unlogin_user_flag_ = config()->GetBoolean("filter_unlogin_user_flag", false);

  auto *exclude_attrs = config()->Get("exclude_common_attrs");
  if (exclude_attrs && exclude_attrs->IsArray()) {
    std::string str;
    for (const auto *attr : exclude_attrs->array()) {
      if (attr->StringValue(&str)) {
        exclude_common_attrs_.insert(str);
      }
    }
  }

  auto *extra_common_attrs = config()->Get("extra_common_attrs");
  if (extra_common_attrs && extra_common_attrs->IsArray()) {
    for (const auto *attr : extra_common_attrs->array()) {
      std::string str = attr->StringValue();
      if (!str.empty()) {
        extra_common_attrs_.insert(str);
      }
    }
  }

  auto *item_attrs = config()->Get("item_attrs");
  if (item_attrs) {
    if (!RecoUtil::ExtractAttrListFromJsonConfig(item_attrs, &send_item_attrs_)) {
      LOG(ERROR) << "CommonRecoCommonPredictItemAttrEnricher init failed: empty item attr name."
                 << item_attrs->ToString();
      return false;
    }
  }

  item_attrs_in_name_list_ = config()->GetString("item_attrs_in_name_list", "");

  return true;
}

bool CommonRecoCommonPredictItemAttrEnricher::GenerateCommonAttr(MutableRecoContextInterface *context,
                                                                 std::vector<kuiba::SampleAttr> *attr_vct) {
  if (attr_vct == nullptr) {
    return false;
  }

  // for each attr
  AddMultiCommonSampleAttrs(context, context->GetCommonAttrsInRequest(), attr_vct);
  AddMultiCommonSampleAttrs(context, extra_common_attrs_, attr_vct);
  return true;
}

void CommonRecoCommonPredictItemAttrEnricher::AddMultiCommonSampleAttrs(
    ReadableRecoContextInterface *context, const std::unordered_set<std::string> &attr_names,
    std::vector<kuiba::SampleAttr> *attr_vct) {
  for (const auto &attr_name : attr_names) {
    kuiba::SampleAttr sample_attr;
    if (AddCommonSampleAttr(context, attr_name, &sample_attr)) {
      attr_vct->push_back(std::move(sample_attr));
    }
  }
}

bool CommonRecoCommonPredictItemAttrEnricher::AddCommonSampleAttr(ReadableRecoContextInterface *context,
                                                                  const std::string &attr_name,
                                                                  kuiba::SampleAttr *attr) {
  if (exclude_common_attrs_.find(attr_name) != exclude_common_attrs_.end()) {
    // 如果是需要排除的 common attr
    return false;
  }
  if (!interop::LoadSampleAttrFromCommonAttr(context, attr_name, attr)) {
    if (!disable_common_attr_missing_warning_) {
      CL_LOG(WARNING) << "cannot find common attr: " << attr_name << RecoUtil::GetRequestInfoForLog(context);
    }
    return false;
  }
  auto iter = attr_name_transform_map_.find(attr_name);
  if (iter != attr_name_transform_map_.end()) {
    attr->set_name(iter->second);
  }
  return true;
}

bool CommonRecoCommonPredictItemAttrEnricher::GenerateSampleListAttr(
    MutableRecoContextInterface *context, std::vector<kuiba::SampleAttr> *attr_vct) {
  if (attr_vct == nullptr) {
    return false;
  }

  auto kuiba_user_attr_names = context->GetStringListCommonAttr(sample_list_common_attr_key_);
  if (!kuiba_user_attr_names || kuiba_user_attr_names->empty()) {
    CL_LOG_EVERY_N(WARNING, 1000) << "cannot find string list common attr [" << sample_list_common_attr_key_
                                  << "] or it's empty" << RecoUtil::GetRequestInfoForLog(context);
    return false;
  }

  kuiba::PredictItem kuiba_user_attrs;
  for (auto name : *kuiba_user_attr_names) {
    if (!interop::BuildSampleAttrFromCommonAttr(context, name, kuiba_user_attrs.mutable_attr())) {
      base::perfutil::PerfUtilWrapper::CountLogStash(
          kPerfNs, "error.common_predict", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
          "kuiba_attr_miss." + std::string(name));
      CL_LOG_EVERY_N(WARNING, 1000) << "cannot find common attr: " << name
                                    << RecoUtil::GetRequestInfoForLog(context);
      continue;
    }
  }

  if (!use_sample_list_attr_flatten_) {
    std::string kuiba_user_attrs_string;
    kuiba_user_attrs.SerializeToString(&kuiba_user_attrs_string);
    kuiba::SampleAttr sample_attr;
    // FIXME (liulijun)
    // 不允许用户配置，默认写死, 此处为了和 predict_server 对应起来
    sample_attr.set_name("kuiba_user_attrs");
    sample_attr.set_type(kuiba::CommonSampleEnum::STRING_ATTR);
    sample_attr.set_string_value(kuiba_user_attrs_string);
    attr_vct->push_back(std::move(sample_attr));
  } else {
    for (const auto &attr : kuiba_user_attrs.attr()) {
      attr_vct->push_back(attr);
    }
  }
  return true;
}

void CommonRecoCommonPredictItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
  request_items_.clear();
  sent_items_.clear();

  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    CL_LOG_ERROR_EVERY("common_predict", "no_kess_service", 100)
        << "common predict request cancelled: no kess_service";
    return;
  }

  if (output_attr_accessors_.empty()) {
    for (const auto &loss : loss_) {
      output_attr_accessors_.push_back(context->GetItemAttrAccessor(output_attr_prefix_ + loss));
    }
  }

  if (send_item_attr_vec_.empty()) {
    send_item_attr_vec_.reserve(send_item_attrs_.size());
    for (const auto &attr_pair : send_item_attrs_) {
      send_item_attr_vec_.push_back(SendItemAttr{.name = attr_pair.first,
                                                 .as = attr_pair.second,
                                                 .accessor = context->GetItemAttrAccessor(attr_pair.first)});
    }
  } else {
    send_item_attr_vec_.resize(send_item_attrs_.size());
  }

  if (!item_attrs_in_name_list_.empty()) {
    auto attr_name_list = context->GetStringListCommonAttr(item_attrs_in_name_list_);
    if (attr_name_list) {
      for (const auto &attr_name : *attr_name_list) {
        send_item_attr_vec_.push_back(SendItemAttr{
            .name = attr_name, .as = attr_name, .accessor = context->GetItemAttrAccessor(attr_name)});
      }
    }
  }

  if (!context->HasCommonAttr(kuiba::AttrNames::PREDICT_KESS_SERVICE)) {
    // 记录 kess_service 值给 leaf_show processor 用
    context->SetStringCommonAttr(kuiba::AttrNames::PREDICT_KESS_SERVICE, kess_service);
    CL_LOG(INFO) << "set string common attr " << kuiba::AttrNames::PREDICT_KESS_SERVICE << " to "
                 << kess_service;
  }

  kuiba::CompressCommonPredictRequest predict_request;
  auto *item_key_attr_accessor =
      item_key_attr_.empty() ? nullptr : context->GetItemAttrAccessor(item_key_attr_);
  std::for_each(begin, end, [this, item_key_attr_accessor](const CommonRecoResult &result) {
    if (target_item_type_only_ && result.GetType() != item_type_) return;
    uint64 key = result.item_key;
    if (item_key_attr_accessor) {
      auto p = result.GetIntAttr(item_key_attr_accessor);
      if (!p) {
        VLOG(100) << "discard common predict item, missing attr " << item_key_attr_
                  << ", item_key: " << result.item_key << ", item_type: " << result.GetType()
                  << ", item_id: " << result.GetId();
        return;
      }
      key = *p;
    }
    sent_items_.push_back(result);
    request_items_.push_back(key);
    VLOG(100) << "add common predict item " << key << ", item_key: " << result.item_key
              << ", item_type: " << result.GetType() << ", item_id: " << result.GetId();
  });

  std::string request_info = "kess_service: " + kess_service + ", service_group: " + service_group_ +
                             ", timeout_ms: " + std::to_string(timeout_ms_);

  if (sent_items_.empty()) {
    CL_LOG(INFO) << "common predict request cancelled: empty item list! " << request_info
                 << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  // 未登录用户
  if (PredictUtil::IsInvalidUser(context->GetUserId(), context->GetDeviceId()) ||
      (filter_unlogin_user_flag_ && PredictUtil::IsUnLoginUser(context->GetUserId()))) {
    for (int i = 0; i < output_attr_accessors_.size(); ++i) {
      for (const auto &result : sent_items_) {
        result.SetDoubleAttr(output_attr_accessors_[i], default_loss_values_[i]);
      }
    }
    CL_LOG(INFO) << "common predict request cancelled: unlogin user, user_id: " << context->GetUserId()
                 << ", device_id:" << context->GetDeviceId();
    return;
  }

  kuiba::CommonPredictRequestCompressor compressor(&predict_request);
  compressor.set_item_key(request_items_);
  compressor.set_loss(loss_);
  // NOTE(wanggang): 光旭确认，这里只需要填 loss_size 个，
  // 而不需要 key_size * loss_size
  compressor.set_default_value(default_loss_values_);

  thread_local std::vector<kuiba::SampleAttr> attr_vct;
  attr_vct.clear();
  GenerateCommonAttr(context, &attr_vct);
  // 获得 samplelist attr
  if (use_sample_list_attr_flag_) {
    int pre_attr_num = attr_vct.size();
    if (!GenerateSampleListAttr(context, &attr_vct)) {
      CL_LOG_WARNING_EVERY("common_predict", "generate_sample_list_attr_fail", 1000)
          << "common predict request cancelled: failed to generate sample list attr."
          << RecoUtil::GetRequestInfoForLog(context);
      return;
    }
    CL_LOG(INFO) << "injected " << (attr_vct.size() - pre_attr_num)
                 << " sample list attrs, use_sample_list_attr_flatten: " << use_sample_list_attr_flatten_;
  }

  compressor.set_common_attr(attr_vct);

  if (!send_item_attr_vec_.empty()) {
    kuiba::CompressItemListCompressor list_compressor(predict_request.mutable_item_list(),
                                                      sent_items_.size());
    std::vector<int> attr_miss_count(send_item_attr_vec_.size(), 0);
    thread_local kuiba::PredictItem attr_list;
    for (const auto &result : sent_items_) {
      attr_list.Clear();
      for (int i = 0; i < send_item_attr_vec_.size(); ++i) {
        auto &send_item_attr = send_item_attr_vec_[i];
        if (!interop::BuildSampleAttrFromItemAttr(result, send_item_attr.accessor, attr_list.mutable_attr(),
                                                  send_item_attr.as)) {
          ++attr_miss_count[i];
          VLOG(100) << "cannot find item attr: " << send_item_attr.name << ", item key: " << result.item_key
                    << " , rename: " << send_item_attr.as;
          continue;
        }
      }
      auto ret = list_compressor.AddItem(attr_list.attr());
      if (!ret) {
        CL_LOG_ERROR_EVERY("common_predict", "item_attr_compress_fail", 1000)
            << "failed to compress item attrs for item key: " << result.item_key
            << RecoUtil::GetRequestInfoForLog(context);
      }
    }

    for (int i = 0; i < attr_miss_count.size(); ++i) {
      if (attr_miss_count[i] > 0) {
        CL_LOG_EVERY_N(WARNING, 1000) << attr_miss_count[i] << " out of " << sent_items_.size()
                                      << " items missing item attr " << send_item_attr_vec_[i].name;
      }
    }
  }

  if (VLOG_IS_ON(200)) {
    std::ostringstream oss;
    oss << "\n============================= parameters for predict server "
           "=============================";
    oss << "\n\n[request keys size]\n" << sent_items_.size();
    oss << "\n\n[loss functions]\n";
    for (const auto &loss : loss_) {
      oss << loss << ", ";
    }
    oss << "\n\n[default loss values]\n";
    for (const auto val : default_loss_values_) {
      oss << val << ", ";
    }
    oss << "\n\n[common attr]\n";
    for (const auto &attr : attr_vct) {
      oss << attr.Utf8DebugString();
      oss << "---\n";
    }
    CL_LOG(INFO) << oss.str();
  }

  if (!compressor.Valid()) {
    CL_LOG_ERROR("common_predict", "invalid_request_compressor")
        << "common predict request cancelled: invalid request compressor!";
  }

  kuiba::CommonPredictRequestReader request_reader(predict_request);
  if (!request_reader.Valid()) {
    CL_LOG_ERROR("common_predict", "invalid_request_reader")
        << "common predict request cancelled: invalid request reader!";
  }

  CL_LOG(INFO) << "sending request to common predict server, item num: " << sent_items_.size()
               << ", common attr num: " << attr_vct.size() << ", loss num: " << loss_.size() << ", "
               << request_info;

  auto *resp = response_pool_.Acquire();
  auto pair = client_.SendRequestAsync(predict_request, kess_service, service_group_, timeout_ms_, resp);
  if (!pair.first) {
    response_pool_.Recycle(resp);
    CL_LOG_ERROR("common_predict", "client_send_request_fail")
        << "failed to send predict request! " << request_info << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  // 接收到 response 时执行的 callback 函数
  // loss_, sent_items_ 等变量在执行 callback 时可能已经不可用，这里需要进行拷贝捕获
  auto callback = [=, sent_items = std::move(sent_items_)](kuiba::CompressCommonPredictResponse *response) {
    // 从预估结果中获得数据
    kuiba::CommonPredictResponseReader reader(*response);
    if (!reader.Valid()) {
      CL_LOG_ERROR("common_predict", "invalid_predict_response")
          << "failed to read predict response, " << request_info << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    if (reader.status_code()) {
      CL_LOG_ERROR("common_predict", "invalid_status_code_" + base::Int64ToString(reader.status_code()))
          << "error_code: " << reader.status_code() << ", error_msg: " << reader.error_message() << ", "
          << request_info << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    if (sent_items.size() * loss_.size() != reader.result_size()) {
      CL_LOG_ERROR("common_predict", "response_size_mismatch")
          << "request_key_size:" << sent_items.size() << " * loss_size:" << loss_.size()
          << " != result_size:" << reader.result_size() << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    std::vector<StatisticInfo> pxtr_stat;
    std::vector<ItemAttr *> output_accessors;

    pxtr_stat.resize(reader.loss_size());
    output_accessors.reserve(reader.loss_size());
    for (const auto &loss : reader.loss()) {
      output_accessors.push_back(context->GetItemAttrAccessor(output_attr_prefix_ + loss));
    }

    for (int i = 0; i < sent_items.size(); ++i) {
      const auto &result = sent_items[i];
      auto float_list = reader.output(i);
      int j = 0;
      for (auto value : float_list) {
        result.SetDoubleAttr(output_accessors[j], value);
        if (!base::IsEqual(value, default_loss_values_[j], 1e-8)) {
          pxtr_stat[j].AddValue(value);
        }
        ++j;
      }
    }

    base::perfutil::PerfUtilWrapper::IntervalLogStash(sent_items.size(), kPerfNs, "predict.item_total",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
    for (int i = 0; i < pxtr_stat.size(); ++i) {
      const auto &attr_name = output_accessors[i]->name();
      const auto &stat = pxtr_stat[i];
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          1000.0 * stat.count() / sent_items.size(), kPerfNs, "predict.pxtr_hit",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
      if (stat.count() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.avg(), kPerfNs, "predict.pxtr_avg",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.max(), kPerfNs, "predict.pxtr_max",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
      }
    }

    CL_LOG(INFO) << "common predict response received, status_code: " << reader.status_code()
                 << ", msg: " << reader.error_message() << ", result_size: " << reader.result_size()
                 << ", dim: " << reader.dim() << ", is_valid: " << reader.Valid()
                 << ", loss_size: " << reader.loss_size() << ", offset_size: " << reader.offset_size();
  };

  auto finally = [this, resp]() { response_pool_.Recycle(resp); };
  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pair.second), std::move(callback), std::move(finally),
                        request_info);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCommonPredictItemAttrEnricher,
                 CommonRecoCommonPredictItemAttrEnricher)

}  // namespace platform
}  // namespace ks
