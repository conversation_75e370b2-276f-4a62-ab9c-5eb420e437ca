#include "dragon/src/processor/common/enricher/common_reco_copy_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoCopyAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  for (const auto &attr : attrs_) {
    if (!attr.from_common.empty()) {
      CopyCommonAttr(context, begin, end, attr.from_common, attr.to_common, attr.to_item, attr.overwrite);
    }
    if (!attr.from_item.empty()) {
      CopyItemAttr(context, begin, end, attr.from_item, attr.to_common, attr.to_item, attr.overwrite);
    }
  }
}

void CommonRecoCopyAttrEnricher::CopyCommonAttr(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end,
                                                const std::string &from_common, const std::string &to_common,
                                                const std::string &to_item, bool overwrite) {
  auto *from_common_acc = context->GetCommonAttrAccessor(from_common);
  ItemAttr *to_item_acc = nullptr;
  if (!to_item.empty()) {
    to_item_acc = context->GetItemAttrAccessor(to_item);
  }
  if (!overwrite && !to_common.empty()) {
    if (context->HasCommonAttr(to_common)) {
      return;
    }
  }
  switch (from_common_acc->value_type) {
    case AttrType::INT: {
      if (auto int_val = from_common_acc->GetIntValue()) {
        if (!to_common.empty()) {
          context->SetIntCommonAttr(to_common, *int_val);
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            context->SetIntItemAttr(result, to_item_acc, *int_val, !overwrite);
          });
        }
      }
      break;
    }
    case AttrType::FLOAT: {
      if (auto double_val = from_common_acc->GetDoubleValue()) {
        if (!to_common.empty()) {
          context->SetDoubleCommonAttr(to_common, *double_val);
        } else {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            context->SetDoubleItemAttr(result, to_item_acc, *double_val, !overwrite);
          });
        }
      }
      break;
    }
    case AttrType::STRING: {
      if (auto str_val = from_common_acc->GetStringValue()) {
        if (!to_common.empty()) {
          context->SetStringCommonAttr(to_common, {str_val->data(), str_val->size()});
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (!overwrite && context->GetStringItemAttr(result, to_item_acc)) {
              return;
            }
            result.SetStringAttr(to_item_acc, {str_val->data(), str_val->size()});
          });
        }
      }
      break;
    }
    case AttrType::INT_LIST: {
      if (auto int_list = from_common_acc->GetIntListValue()) {
        if (!to_common.empty()) {
          context->SetIntListCommonAttr(to_common, {int_list->begin(), int_list->end()});
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (!overwrite && context->GetIntListItemAttr(result, to_item_acc)) {
              return;
            }
            result.SetIntListAttr(to_item_acc, {int_list->begin(), int_list->end()});
          });
        }
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      if (auto double_list = from_common_acc->GetDoubleListValue()) {
        if (!to_common.empty()) {
          context->SetDoubleListCommonAttr(to_common, {double_list->begin(), double_list->end()});
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (!overwrite && context->GetDoubleListItemAttr(result, to_item_acc)) {
              return;
            }
            result.SetDoubleListAttr(to_item_acc, {double_list->begin(), double_list->end()});
          });
        }
      }
      break;
    }
    case AttrType::STRING_LIST: {
      if (auto str_list = from_common_acc->GetStringListValue()) {
        if (!to_common.empty()) {
          std::vector<std::string> vec;
          vec.reserve(str_list->size());
          for (const auto &str : *str_list) {
            vec.emplace_back(str.data(), str.size());
          }
          context->SetStringListCommonAttr(to_common, std::move(vec));
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (!overwrite && context->GetStringListItemAttr(result, to_item_acc)) {
              return;
            }
            std::vector<std::string> vec;
            vec.reserve(str_list->size());
            for (const auto &str : *str_list) {
              vec.emplace_back(str.data(), str.size());
            }
            result.SetStringListAttr(to_item_acc, std::move(vec));
          });
        }
      }
      break;
    }
    case AttrType::EXTRA: {
      const auto *extra_val = from_common_acc->GetExtraValue();
      if (extra_val) {
        if (!to_common.empty()) {
          auto val = *extra_val;
          context->SetExtraCommonAttr(to_common, std::move(val));
        } else if (to_item_acc) {
          std::for_each(begin, end, [&](const CommonRecoResult &result) {
            if (!overwrite && context->GetExtraItemAttr(result, to_item_acc)) {
              return;
            }
            auto val = *extra_val;
            result.SetExtraAttr(to_item_acc, std::move(val));
          });
        }
      }
      break;
    }
    default:
      CL_LOG(INFO) << "processor: " << GetName()
                   << " copy common_attr skipped as common_attr is missing: " << from_common;
      break;
  }
}

void CommonRecoCopyAttrEnricher::CopyItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end, const std::string &from_item,
                                              const std::string &to_common, const std::string &to_item,
                                              bool overwrite) {
  auto *from_item_acc = context->GetItemAttrAccessor(from_item);
  ItemAttr *to_item_acc = nullptr;
  if (!to_item.empty()) {
    to_item_acc = context->GetItemAttrAccessor(to_item);
  }
  if (!overwrite && !to_common.empty()) {
    if (context->HasCommonAttr(to_common)) {
      return;
    }
  }
  switch (from_item_acc->value_type) {
    case AttrType::INT: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto int_val = result.GetIntAttr(from_item_acc)) {
          if (!to_common.empty()) {
            context->SetIntCommonAttr(to_common, *int_val);
            return;
          }
          if (to_item_acc) {
            context->SetIntItemAttr(result, to_item_acc, *int_val, !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::FLOAT: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto double_val = result.GetDoubleAttr(from_item_acc)) {
          if (!to_common.empty()) {
            context->SetDoubleCommonAttr(to_common, *double_val);
            return;
          }
          if (to_item_acc) {
            context->SetDoubleItemAttr(result, to_item_acc, *double_val, !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::STRING: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto str_val = result.GetStringAttr(from_item_acc)) {
          if (!to_common.empty()) {
            context->SetStringCommonAttr(to_common, {str_val->data(), str_val->size()});
            return;
          }
          if (to_item_acc) {
            result.SetStringAttr(to_item_acc, {str_val->data(), str_val->size()}, !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::INT_LIST: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto int_list = result.GetIntListAttr(from_item_acc)) {
          if (!to_common.empty()) {
            context->SetIntListCommonAttr(to_common, {int_list->begin(), int_list->end()});
            return;
          }
          if (to_item_acc) {
            result.SetIntListAttr(to_item_acc, {int_list->begin(), int_list->end()}, !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto double_list = result.GetDoubleListAttr(from_item_acc)) {
          if (!to_common.empty()) {
            context->SetDoubleListCommonAttr(to_common, {double_list->begin(), double_list->end()});
            return;
          }
          if (to_item_acc) {
            result.SetDoubleListAttr(to_item_acc, {double_list->begin(), double_list->end()}, !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::STRING_LIST: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        if (auto str_list = result.GetStringListAttr(from_item_acc)) {
          if (!to_common.empty()) {
            std::vector<std::string> vec;
            vec.reserve(str_list->size());
            for (const auto &str : *str_list) {
              vec.emplace_back(str.data(), str.size());
            }
            context->SetStringListCommonAttr(to_common, std::move(vec));
            return;
          }
          if (to_item_acc) {
            std::vector<std::string> vec;
            vec.reserve(str_list->size());
            for (const auto &str : *str_list) {
              vec.emplace_back(str.data(), str.size());
            }
            result.SetStringListAttr(to_item_acc, std::move(vec), !overwrite);
          }
        }
      }
      break;
    }
    case AttrType::EXTRA: {
      for (auto it = begin; it != end; it++) {
        const CommonRecoResult &result = *it;
        const auto *extra_val = result.GetExtraAttr(from_item_acc);
        if (extra_val) {
          if (!to_common.empty()) {
            auto val = *extra_val;
            context->SetExtraCommonAttr(to_common, std::move(val));
            return;
          }
          if (to_item_acc) {
            auto val = *extra_val;
            result.SetExtraAttr(to_item_acc, std::move(val), !overwrite);
          }
        }
      }
      break;
    }
    default:
      CL_LOG(INFO) << "processor: " << GetName()
                   << "CommonRecoCopyAttrEnricher copy item_attr skipped as item_attr is missing: "
                   << from_item;
      break;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCopyAttrEnricher, CommonRecoCopyAttrEnricher)

}  // namespace platform
}  // namespace ks
