#include "dragon/src/processor/common/enricher/common_reco_author_exp_param_enricher.h"

namespace ks {
namespace platform {
void CommonRecoAuthorExpParamEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  auto *author_tail_accessor = context->GetItemAttrAccessor(author_tail_str_);
  for (auto &param : author_exp_params_) {
    param.item_attr = context->GetItemAttrAccessor(param.attr_name);
  }
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    int64 author_tail = context->GetIntItemAttr(result, author_tail_accessor).value_or(-1);
    for (const auto &param : author_exp_params_) {
      switch (param.type) {
        case AbtestParamType::INT: {
          int64 m_int_value = 0;
          AT_UTIL.GetIntConfig(param.param_name, author_tail, m_int_value, param.default_int);
          context->SetIntItemAttr(result, param.item_attr, m_int_value);
          break;
        }
        case AbtestParamType::DOUBLE: {
          double m_double_value = 0;
          AT_UTIL.GetDoubleConfig(param.param_name, author_tail, m_double_value, param.default_double);
          context->SetDoubleItemAttr(result, param.item_attr, m_double_value);
          break;
        }
        case AbtestParamType::STRING: {
          std::string m_string_value = "";
          AT_UTIL.GetStringConfig(param.param_name, author_tail, m_string_value, param.default_string);
          context->SetStringItemAttr(result, param.item_attr, std::move(m_string_value));
          break;
        }
        case AbtestParamType::BOOLEAN: {
          bool m_bool_value = false;
          AT_UTIL.GetBooleanConfig(param.param_name, author_tail, m_bool_value, param.default_int);
          context->SetIntItemAttr(result, param.item_attr, m_bool_value ? 1 : 0);
          break;
        }
        default: {
          CL_LOG_ERROR("get_author_exp_param", "invalid type")
              << "get_at_param use invalue list, type: " << static_cast<int>(param.type)
              << ", param name: " << param.attr_name;
          break;
        }  // case
      }    // switch (attr_type_)
    }      // for (const auto &param : author_exp_params_)
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAuthorExpParamEnricher, CommonRecoAuthorExpParamEnricher)

}  // namespace platform
}  // namespace ks
