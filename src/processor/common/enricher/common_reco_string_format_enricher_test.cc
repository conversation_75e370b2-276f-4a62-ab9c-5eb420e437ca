#include <memory>
#include "gtest/gtest.h"

#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/processor/common/enricher/common_reco_string_format_enricher.h"

using ks::platform::format_utils::StringFormatHelper;
using Type = ks::platform::format_utils::StringFormatHelper::Type;

class StringFormatHelperTest : public ::testing::Test {
 protected:
  template <typename T>
  void CheckVector(const std::vector<T> &v0, const std::vector<T> &v1, const std::string &case_prefix) {
    EXPECT_EQ(v1.size(), v0.size()) << case_prefix << ": size(v1) vs size(v0): " << v1.size() << " vs "
                                    << v0.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v1.at(i), v0.at(i)) << case_prefix << ": the `" << i << "` element is not equal";
    }
  }
};

TEST_F(StringFormatHelperTest, Split) {
  std::vector<std::string> test_cases = {
      "Name %d [%-10.10s]\n",
      "hello \\ketty %%d, %d%% x",
      "%d.%s\n",
      "%s %s %s %lu",
      "Hey I got 84.20%% in my final exams\n",
      "num in octal format: %o%x%X\n",
      "num (padded): %05d %05ld\n",
      "%20s\", str2=\"%-20s\"\n",
      "val = %.2f, num = %llx, NUM = %llX, Address of num is: %p",
      "%%%%%d",
  };
  std::vector<std::vector<std::string>> exp_sub_s = {
      {"Name ", "%d", " [", "%-10.10s", "]\n"},
      {"hello \\ketty ", "%%", "d, ", "%d", "%%", " x"},
      {"%d", ".", "%s", "\n"},
      {"%s", " ", "%s", " ", "%s", " ", "%lu"},
      {"Hey I got 84.20", "%%", " in my final exams\n"},
      {"num in octal format: ", "%o", "%x", "%X", "\n"},
      {"num (padded): ", "%05d", " ", "%05ld", "\n"},
      {"%20s", "\", str2=\"", "%-20s", "\"\n"},
      {"val = ", "%.2f", ", num = ", "%llx", ", NUM = ", "%llX", ", Address of num is: ", "%p"},
      {"%%", "%%", "%d"}};
  std::vector<std::vector<Type>> exp_sub_t = {
      {Type::NONE, Type::UNSUPPORTED, Type::NONE, Type::STRING, Type::NONE},
      {Type::NONE, Type::NONE, Type::NONE, Type::UNSUPPORTED, Type::NONE, Type::NONE},
      {Type::UNSUPPORTED, Type::NONE, Type::STRING, Type::NONE},
      {Type::STRING, Type::NONE, Type::STRING, Type::NONE, Type::STRING, Type::NONE, Type::UINT64},
      {Type::NONE, Type::NONE, Type::NONE},
      {Type::NONE, Type::UNSUPPORTED, Type::UNSUPPORTED, Type::UNSUPPORTED, Type::NONE},
      {Type::NONE, Type::UNSUPPORTED, Type::NONE, Type::INT64, Type::NONE},
      {Type::STRING, Type::NONE, Type::STRING, Type::NONE},
      {Type::NONE, Type::DOUBLE, Type::NONE, Type::UINT64, Type::NONE, Type::UINT64, Type::NONE,
       Type::UNSUPPORTED},
      {Type::NONE, Type::NONE, Type::UNSUPPORTED}};
  std::vector<Type> sub_types;
  std::vector<std::string> sub_s;
  for (int i = 0; i < test_cases.size(); ++i) {
    sub_types.clear();
    sub_s.clear();
    ASSERT_TRUE(StringFormatHelper::Split(test_cases[i], &sub_s, &sub_types));
    CheckVector<std::string>(exp_sub_s[i], sub_s, "SplitCase.sub_s." + std::to_string(i));
    CheckVector<Type>(exp_sub_t[i], sub_types, "SplitCase.sub_t." + std::to_string(i));
  }
}

class CommonRecoStringFormatEnricherTest : public ::testing::Test {
 protected:
  void SetUp() override {
    exec_ = std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(R"json(
    {
      "base_pipeline": {
        "processor": {
          "string_format0": {
            "is_common_attr": true,
            "input_attrs": ["common_attr_0", "common_attr_1"],
            "format_string": "%lu\t%s",
            "output_attr": "common_attr_2",
            "type_name": "CommonRecoStringFormatEnricher"
          },
          "string_format1": {
            "is_common_attr": false,
            "input_attrs": ["item_attr_0", "item_attr_1"],
            "format_string": "%lu\t0x%lx",
            "output_attr": "item_attr_2",
            "type_name": "CommonRecoStringFormatEnricher"
          }
        },
        "type_name": "CommonRecoPipeline"
      },
      "pipeline_map": {
        "common_test": {
          "__PARENT": "base_pipeline",
          "pipeline": [
            "string_format0"
          ]
        },
        "item_test": {
          "__PARENT": "base_pipeline",
          "pipeline": [
            "string_format1"
          ]
        }
      }
    }
    )json");
  }

  template <typename T>
  void CheckVector(const std::vector<T> &v0, const std::vector<T> &v1, const std::string &case_prefix) {
    ASSERT_EQ(v1.size(), v0.size()) << case_prefix << ": size(v1) vs size(v0): " << v1.size() << " vs "
                                    << v0.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v1.at(i), v0.at(i)) << case_prefix << ": the `" << i << "` element is not equal";
    }
  }

  std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor> exec_;
};

TEST_F(CommonRecoStringFormatEnricherTest, Common) {
  // set common attrs
  exec_->SetInt("common_attr_0", 666);
  exec_->SetString("common_attr_1", "zzz");
  // run
  exec_->Run("common_test");
  auto ans = exec_->GetString("common_attr_2");
  ASSERT_TRUE(ans.has_value()) << "common_attr_2 is null";
  EXPECT_EQ("666\tzzz", *ans);
}

TEST_F(CommonRecoStringFormatEnricherTest, Item) {
  // set item attrs
  auto item = exec_->AddItem(1);
  item.SetInt("item_attr_0", -1);
  item.SetInt("item_attr_1", -1);
  // run
  exec_->Run("item_test");
  auto ans = item.GetString("item_attr_2");
  ASSERT_TRUE(ans.has_value()) << "item_attr_2 is null";
  EXPECT_EQ("18446744073709551615\t0xffffffffffffffff", *ans);
}
