#include <utility>

#include "dragon/src/module/traceback_util.h"
#include "dragon/src/processor/common/enricher/common_reco_zstd_enricher.h"

namespace ks {
namespace platform {

void CommonRecoZstdEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessStrCommonAttr(context);
  } else {
    ProcessStrItemAttr(context, begin, end);
  }
}

// 处理 common attr
void CommonRecoZstdEnricher::ProcessStrCommonAttr(MutableRecoContextInterface *context) {
  auto str = context->GetStringCommonAttr(input_attr_);
  if (!str) {
    CL_LOG(INFO) << "zstd cancelled, input: " << input_attr_ << " not found.";
    return;
  }
  std::string payload;
  if (is_compressing_) {
    if (!ks::platform::traceback_util::zstd_util_compress(str->data(), str->size(), &payload,
                                                          GetCompressionLevel(context))) {
      CL_LOG_ERROR("zstd", "compress_fail:" + std::string(*str))
          << "Failed to zstd compress attr: " << input_attr_;
      return;
    }
  } else {
    if (!ks::platform::traceback_util::zstd_util_decompress(str->data(), str->size(), &payload)) {
      CL_LOG_ERROR("zstd", "decompress_fail:" + std::string(*str))
          << "Failed to zstd decompress attr: " << input_attr_;
      return;
    }
  }
  context->SetStringCommonAttr(output_attr_, std::move(payload));
}

// 处理 item attr
void CommonRecoZstdEnricher::ProcessStrItemAttr(MutableRecoContextInterface *context,
                                                const RecoResultConstIter &begin,
                                                const RecoResultConstIter &end) {
  auto *input_attr_accessor = context->GetItemAttrAccessor(input_attr_);
  auto *output_attr_accessor = context->GetItemAttrAccessor(output_attr_);
  if (input_attr_accessor->value_type == AttrType::UNKNOWN) {
    CL_LOG_ERROR("zstd", "attr_not_found:" + input_attr_)
        << (is_compressing_ ? "zstd compress" : "zstd decompress")
        << " cancelled due to input_attr not found: " << input_attr_;
    return;
  }
  uint32_t err_count = 0;
  const int compression_level = GetCompressionLevel(context);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto str = result.GetStringAttr(input_attr_accessor);
    if (!str) {
      return;
    }
    std::string payload;
    if (is_compressing_) {
      if (!ks::platform::traceback_util::zstd_util_compress(str->data(), str->size(), &payload,
                                                            compression_level)) {
        ++err_count;
        return;
      }
    } else {
      if (!ks::platform::traceback_util::zstd_util_decompress(str->data(), str->size(), &payload)) {
        ++err_count;
        return;
      }
    }  // end if (is_compressing_)
    result.SetStringAttr(output_attr_accessor, std::move(payload));
  });
  if (err_count > 0) {
    std::string fail_message = (is_compressing_ ? "compress_attr_fail:" : "decompress_attr_fail:")
                               + input_attr_;
    CL_LOG_WARNING_COUNT(err_count, "zstd", fail_message) << ", output_attr: " << output_attr_;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoZstdEnricher, CommonRecoZstdEnricher)

}  // namespace platform
}  // namespace ks
