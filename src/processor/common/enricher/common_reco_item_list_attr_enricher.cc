#include "dragon/src/processor/common/enricher/common_reco_item_list_attr_enricher.h"

#include <algorithm>
#include <utility>
#include <vector>

namespace ks {
namespace platform {

void CommonRecoItemListAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  if (!items_in_item_attr_accessor_) {
    items_in_item_attr_accessor_ = context->GetItemAttrAccessor(items_in_item_attr_);
  }

  for (const auto &mapping : item_attr_mapping_) {
    auto *from_attr = context->GetItemAttrAccessor(mapping.first);
    if (from_attr->value_type != AttrType::UNKNOWN) {
      auto *to_attr = context->GetItemAttrAccessor(mapping.second);
      std::for_each(begin, end, [this, context, from_attr, to_attr](const CommonRecoResult &result) {
        HandleItemAttrMapping(context, result, from_attr, to_attr);
      });
    }
  }
}

void CommonRecoItemListAttrEnricher::HandleItemAttrMapping(MutableRecoContextInterface *context,
                                                           const CommonRecoResult &result,
                                                           ItemAttr *from_attr, ItemAttr *to_attr) {
  absl::Span<const int64> item_list;
  std::vector<int64> single_key;
  if (auto val = result.GetIntListAttr(items_in_item_attr_accessor_)) {
    item_list = *val;
  } else if (auto val = result.GetIntAttr(items_in_item_attr_accessor_)) {
    single_key.push_back(*val);
    item_list = single_key;
  }

  if (item_list.empty()) {
    VLOG(100) << "can not find int_list common_attr: " << items_in_item_attr_accessor_->name()
              << " for item: " << result.item_key;
    return;
  }

  std::vector<int64> int_list;
  std::vector<double> double_list;
  std::vector<std::string> string_list;

  switch (from_attr->value_type) {
    case AttrType::INT:
      int_list.reserve(item_list.size());
      std::transform(item_list.begin(), item_list.end(), std::back_inserter(int_list), [&](uint64 sub_item) {
        uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
        auto p = context->GetIntItemAttr(sub_item_key, from_attr->name());
        return p ? *p : default_int_;
      });
      if (single_key.empty()) {
        result.SetIntListAttr(to_attr, std::move(int_list));
      } else {
        result.SetIntAttr(to_attr, int_list[0]);
      }
      break;

    case AttrType::FLOAT:
      double_list.reserve(item_list.size());
      std::transform(item_list.begin(), item_list.end(), std::back_inserter(double_list),
                     [&](uint64 sub_item) {
                       uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
                       auto p = context->GetDoubleItemAttr(sub_item_key, from_attr->name());
                       return p ? *p : default_double_;
                     });
      if (single_key.empty()) {
        result.SetDoubleListAttr(to_attr, std::move(double_list));
      } else {
        result.SetDoubleAttr(to_attr, double_list[0]);
      }
      break;

    case AttrType::STRING:
      string_list.reserve(item_list.size());
      std::transform(item_list.begin(), item_list.end(), std::back_inserter(string_list),
                     [&](uint64 sub_item) {
                       uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
                       auto p = context->GetStringItemAttr(sub_item_key, from_attr->name());
                       return p ? std::string(p->data(), p->size()) : default_string_;
                     });
      if (single_key.empty()) {
        result.SetStringListAttr(to_attr, std::move(string_list));
      } else {
        result.SetStringAttr(to_attr, std::move(string_list[0]));
      }
      break;

    case AttrType::INT_LIST:
      int_list.reserve(item_list.size());
      std::for_each(item_list.begin(), item_list.end(), [&](uint64 sub_item) {
        uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
        auto p = context->GetIntListItemAttr(sub_item_key, from_attr->name());
        if (p) {
          std::copy(p->begin(), p->end(), std::back_inserter(int_list));
        }
      });
      result.SetIntListAttr(to_attr, std::move(int_list));
      break;

    case AttrType::FLOAT_LIST:
      double_list.reserve(item_list.size());
      std::for_each(item_list.begin(), item_list.end(), [&](uint64 sub_item) {
        uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
        auto p = context->GetDoubleListItemAttr(sub_item_key, from_attr->name());
        if (p) {
          std::copy(p->begin(), p->end(), std::back_inserter(double_list));
        }
      });
      result.SetDoubleListAttr(to_attr, std::move(double_list));
      break;

    case AttrType::STRING_LIST:
      string_list.reserve(item_list.size());
      std::for_each(item_list.begin(), item_list.end(), [&](uint64 sub_item) {
        uint64 sub_item_key = Util::GenKeysign(use_item_type_, sub_item);
        auto p = context->GetStringListItemAttr(sub_item_key, from_attr->name());
        if (p) {
          std::transform(p->begin(), p->end(), std::back_inserter(string_list),
                         [](auto sv) { return std::string(sv.data(), sv.size()); });
        }
      });
      result.SetStringListAttr(to_attr, std::move(string_list));
      break;

    default:
      VLOG(100) << "unknown type for item_attr: " << from_attr->name();
      break;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemListAttrEnricher, CommonRecoItemListAttrEnricher)

}  // namespace platform
}  // namespace ks
