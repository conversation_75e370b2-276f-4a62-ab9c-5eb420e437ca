#pragma once

#include <string>
#include <utility>
#include <vector>
#include <set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {
class CommonRecoReverseListAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoReverseListAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void ProcessCommonAttr(MutableRecoContextInterface *context, std::string attr_name);

  void ProcessItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end, std::string attr_name);

 private:
  bool InitProcessor() override {
    const auto *common_attrs = config()->Get("common_attrs");
    if (common_attrs && !RecoUtil::ExtractStringSetFromJsonConfig(common_attrs, &common_attrs_)) {
      LOG(ERROR)
          << "CommonRecoReverseListAttrEnricher init failed! 'common_attrs' should be a string array.";
      return false;
    }

    const auto *item_attrs = config()->Get("item_attrs");
    if (item_attrs && !RecoUtil::ExtractStringSetFromJsonConfig(item_attrs, &item_attrs_)) {
      LOG(ERROR) << "CommonRecoReverseListAttrEnricher init failed! 'item_attrs' should be a string array.";
      return false;
    }

    const std::string common_attr = config()->GetString("common_attr");
    if (!common_attr.empty() && !common_attrs_.count(common_attr)) {
      common_attrs_.insert(common_attr);
    }

    const std::string item_attr = config()->GetString("item_attr");
    if (!item_attr.empty() && !item_attrs_.count(item_attr)) {
      item_attrs_.insert(item_attr);
    }
    if (common_attrs_.empty() && item_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoReverseListAttrEnricher init failed! common_attrs or item_attrs is required";
      return false;
    }
    return true;
  }

 private:
  std::set<std::string> common_attrs_;
  std::set<std::string> item_attrs_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoReverseListAttrEnricher);
};

}  // namespace platform
}  // namespace ks
