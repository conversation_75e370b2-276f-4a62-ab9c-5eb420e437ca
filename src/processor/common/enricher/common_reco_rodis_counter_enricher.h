#pragma once

#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/ds/user_profile/photo_count_client/count.h"
#include "ks/reco_proto/action/user_action_log.pb.h"

namespace ks {
namespace platform {
class CommonRecoRodisCounterEnricher : public CommonRecoBaseEnricher {
 public:
  struct ActionConfig {
    ks::action::ActionEnum::ActionType action;
    std::set<ks::action::ActionEnum::ChannelType> channel_set = {};
    std::string save_count_to = "";
    ItemAttr *save_count_accessor = nullptr;
  };
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);
  int64 GetRodisCounter(MutableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);
  int64 GetRodisCounterByGroup(MutableRecoContextInterface *context, RecoResultConstIter begin,
                               RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    auto action_list = config()->Get("action_list");
    if (!action_list || !action_list->IsArray()) {
      LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'action_list'!";
      return false;
    }
    for (const auto c : action_list->array()) {
      ActionConfig action_config;
      const std::string &action = c->GetString("action", "");
      if (action.empty()) {
        LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'action'! action is "
                      "empty! config: "
                   << c->ToString();
        return false;
      }

      if (!ks::action::ActionEnum::ActionType_Parse(action, &action_config.action)) {
        LOG(ERROR)
            << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'action'! illegal value: "
            << action;
        return false;
      }

      std::vector<std::string> channel_list;
      auto *channel_list_config = c->Get("channel_list");
      if (!channel_list_config || !channel_list_config->IsArray()) {
        LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'channel_list'! "
                      "channel_list is empty or not an array! config: "
                   << c->ToString();
        return false;
      }

      if (!RecoUtil::ExtractStringListFromJsonConfig(channel_list_config, &channel_list)) {
        LOG(ERROR)
            << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'channel_list'! illegal value: "
            << channel_list_config->ToString();
        return false;
      }

      if (channel_list.empty()) {
        LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'channel_list'!  "
                      "channel_list is empty! config: "
                   << c->ToString();
        return false;
      }

      for (const auto &channel_str : channel_list) {
        ks::action::ActionEnum::ChannelType channel;
        if (!ks::action::ActionEnum::ChannelType_Parse(channel_str, &channel)) {
          LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'channel_list'! "
                        "illegal value: "
                     << channel_str;
          return false;
        }
        action_config.channel_set.insert(channel);
      }

      action_config.save_count_to = c->GetString("save_count_to", "");
      if (action_config.save_count_to.empty()) {
        LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'save_count_to'! "
                      "save_count_to is empty! config: "
                   << c->ToString();
        return false;
      }
      action_list_.emplace_back(std::move(action_config));
    }

    group_by_ = config()->GetString("group_by", "");

    rodis_kess_name_ = config()->GetString("rodis_kess_name", "");
    if (rodis_kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'rodis_kess_name'! "
                    "rodis_kess_name is empty";
      return false;
    }
    rodis_domain_ = config()->GetString("rodis_domain", "");
    if (rodis_domain_.empty()) {
      LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'rodis_domain'! "
                    "rodis_domain is empty";
      return false;
    }
    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoRodisCounterEnricher init failed! Failed to extract 'timeout_ms'! timeout_ms "
                    "must be a positive integer ";
      return false;
    }

    return true;
  }

 private:
  std::unordered_map<int64_t, photo_count::Count> counts_;
  std::unordered_map<std::pair<int64_t, std::string>, photo_count::Count,
                     photo_count::PhotoCountService::pair_hash>
      groups_counts_;
  std::vector<int64> ids_;
  std::vector<ActionConfig> action_list_;
  folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> groups_set_;
  std::string rodis_kess_name_ = "";
  std::string rodis_domain_ = "";
  std::string group_by_ = "";
  int64 timeout_ms_ = 10;
};
}  // namespace platform
}  // namespace ks
