#include "dragon/src/processor/common/enricher/common_reco_pack_item_attr_enricher.h"

#include <algorithm>

#include "absl/hash/hash.h"
#include "absl/strings/string_view.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

void CommonRecoPackItemAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  if (to_item_attr_.empty() || from_item_attrs_.size() == 0) {
    return;
  }
  Prepare(context);
  switch (aggregator_) {
    case Aggregator::CONCAT:
      AppendItemAttrs(context, begin, end);
      break;
    case Aggregator::COPY:
      CopyItemAttr(context, begin, end);
      break;
    default:
      CL_LOG_ERROR("pack_item_attr_to_item_attr", "invalid_aggregator")
          << "invalid aggregator value: " << (int)aggregator_;
      break;
  }
}

void CommonRecoPackItemAttrEnricher::Prepare(MutableRecoContextInterface *context) {
  if (prepared_) return;
  std::for_each(from_item_attrs_.begin(), from_item_attrs_.end(),
                [this, context](const std::string &attr_name) {
                  from_item_attrs_accessor_.emplace_back(context->GetItemAttrAccessor(attr_name));
                });
  to_item_attr_accessor_ = context->GetItemAttrAccessor(to_item_attr_);
  prepared_ = true;
}

void CommonRecoPackItemAttrEnricher::AppendItemAttrs(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
  int limit_num = GetIntProcessorParameter(context, "limit_num", -1);
  if (limit_num == 0) return;
  int result_num = std::distance(begin, end);
  if (result_num == 0) return;
  int reserve_num = (limit_num > 0) ? limit_num : result_num;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    folly::F14FastSet<int64> int_dedup;
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> string_dedup;
    std::vector<int64> int_values;
    std::vector<double> double_values;
    std::vector<std::string> string_values;
    int_values.reserve(reserve_num);
    double_values.reserve(reserve_num);
    string_values.reserve(reserve_num);

    for (int i = 0; i < from_item_attrs_.size(); ++i) {
      if (limit_num > 0 && (int_values.size() >= limit_num || string_values.size() >= limit_num ||
                            double_values.size() >= limit_num)) {
        break;
      }

      const auto &from_item_attr_name = from_item_attrs_[i];
      const auto *from_item_attr = from_item_attrs_accessor_[i];

      switch (default_val_type_) {
        case AttrType::INT: {
          if (auto p = context->GetIntItemAttr(result, from_item_attr)) {
            auto int_value = *p;
            if (!dedup_to_item_attr_ || int_dedup.insert(int_value).second) {
              int_values.emplace_back(int_value);
            }
          } else {
            if (fill_default_val_) {
              if (!dedup_to_item_attr_ || int_dedup.insert(default_int_val_).second) {
                int_values.emplace_back(default_int_val_);
              }
            }
          }
          break;
        }
        case AttrType::INT_LIST: {
          if (auto p = context->GetIntListItemAttr(result, from_item_attr)) {
            for (int j = 0; j < p->size(); ++j) {
              if (limit_num > 0 && int_values.size() >= limit_num) break;
              auto int_value = p->at(j);
              if (!dedup_to_item_attr_ || int_dedup.insert(int_value).second) {
                int_values.emplace_back(int_value);
              }
            }
          } else {
            if (fill_default_val_) {
              for (int j = 0; j < default_int_list_val_.size(); ++j) {
                if (limit_num > 0 && int_values.size() >= limit_num) break;
                auto int_value = default_int_list_val_.at(j);
                if (!dedup_to_item_attr_ || int_dedup.insert(int_value).second) {
                  int_values.emplace_back(int_value);
                }
              }
            }
          }
          break;
        }
        case AttrType::FLOAT: {
          if (auto p = context->GetDoubleItemAttr(result, from_item_attr)) {
            double_values.emplace_back(*p);
          } else {
            if (fill_default_val_) {
              double_values.emplace_back(default_double_val_);
            }
          }
          break;
        }
        case AttrType::FLOAT_LIST: {
          if (auto p = context->GetDoubleListItemAttr(result, from_item_attr)) {
            for (int j = 0; j < p->size(); ++j) {
              if (limit_num > 0 && double_values.size() >= limit_num) break;
              double_values.emplace_back(p->at(j));
            }
          } else {
            if (fill_default_val_) {
              for (int j = 0; j < default_double_list_val_.size(); ++j) {
                if (limit_num > 0 && double_values.size() >= limit_num) break;
                double_values.emplace_back(default_double_list_val_.at(j));
              }
            }
          }
          break;
        }
        case AttrType::STRING: {
          if (auto p = context->GetStringItemAttr(result, from_item_attr)) {
            auto sv = *p;
            if (!dedup_to_item_attr_ || string_dedup.insert(sv).second) {
              string_values.emplace_back(sv.data(), sv.size());
            }
          } else {
            if (fill_default_val_) {
              if (!dedup_to_item_attr_ || string_dedup.insert(default_string_val_).second) {
                string_values.emplace_back(default_string_val_);
              }
            }
          }
          break;
        }
        case AttrType::STRING_LIST: {
          if (auto p = context->GetStringListItemAttr(result, from_item_attr)) {
            for (int j = 0; j < p->size(); ++j) {
              if (limit_num > 0 && string_values.size() >= limit_num) break;
              auto sv = p->at(j);
              if (!dedup_to_item_attr_ || string_dedup.insert(sv).second) {
                string_values.emplace_back(std::string(sv.data(), sv.size()));
              }
            }
          } else {
            if (fill_default_val_) {
              for (int j = 0; j < default_string_list_val_.size(); ++j) {
                if (limit_num > 0 && string_values.size() >= limit_num) break;
                const auto &string_value = default_string_list_val_.at(j);
                if (!dedup_to_item_attr_ || string_dedup.insert(string_value).second) {
                  string_values.emplace_back(string_value);
                }
              }
            }
          }
          break;
        }
        default:
          CL_LOG_ERROR("pack_item_attr_to_item_attr", "invalid_value_type")
              << "cannot get int/double/string/int_list/double_list/string_list attr of "
              << from_item_attr_name;
          break;
      }
    }

    if (!int_values.empty()) {
      context->SetIntListItemAttr(result, to_item_attr_accessor_, std::move(int_values));
    } else if (!double_values.empty()) {
      context->SetDoubleListItemAttr(result, to_item_attr_accessor_, std::move(double_values));
    } else if (!string_values.empty()) {
      context->SetStringListItemAttr(result, to_item_attr_accessor_, std::move(string_values));
    }
  });
}

void CommonRecoPackItemAttrEnricher::CopyItemAttr(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  if (from_item_attrs_accessor_.size() == 0) return;
  const auto *input_accessor = from_item_attrs_accessor_[0];
  auto *output_accessor = to_item_attr_accessor_;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    folly::F14FastSet<int64> int_dedup;
    folly::F14FastSet<absl::string_view, absl::Hash<absl::string_view>> string_dedup;
    switch (default_val_type_) {
      case AttrType::INT: {
        if (auto p = context->GetIntItemAttr(result, input_accessor)) {
          context->SetIntItemAttr(result, output_accessor, *p);
        } else {
          if (fill_default_val_) {
            context->SetIntItemAttr(result, output_accessor, default_int_val_);
          }
        }
        break;
      }
      case AttrType::INT_LIST: {
        if (auto p = context->GetIntListItemAttr(result, input_accessor)) {
          if (!dedup_to_item_attr_) {
            context->SetIntListItemAttr(result, output_accessor, {p->begin(), p->end()});
          } else {
            std::vector<int64> int_values;
            std::for_each(p->begin(), p->end(), [&int_values, &int_dedup](int64 value) {
              if (int_dedup.insert(value).second) {
                int_values.push_back(value);
              }
            });
            context->SetIntListItemAttr(result, output_accessor, std::move(int_values));
          }
        } else {
          if (fill_default_val_) {
            if (!dedup_to_item_attr_) {
              context->SetIntListItemAttr(result, output_accessor,
                                          {default_int_list_val_.begin(), default_int_list_val_.end()});
            } else {
              std::vector<int64> int_values;
              std::for_each(default_int_list_val_.begin(), default_int_list_val_.end(),
                            [&int_values, &int_dedup](int64 value) {
                              if (int_dedup.insert(value).second) {
                                int_values.push_back(value);
                              }
                            });
              context->SetIntListItemAttr(result, output_accessor, std::move(int_values));
            }
          }
        }
        break;
      }
      case AttrType::FLOAT: {
        if (auto p = context->GetDoubleItemAttr(result, input_accessor)) {
          context->SetDoubleItemAttr(result, output_accessor, *p);
        } else {
          if (fill_default_val_) {
            context->SetDoubleItemAttr(result, output_accessor, default_double_val_);
          }
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        if (auto p = context->GetDoubleListItemAttr(result, input_accessor)) {
          context->SetDoubleListItemAttr(result, output_accessor, {p->begin(), p->end()});
        } else {
          if (fill_default_val_) {
            context->SetDoubleListItemAttr(
                result, output_accessor, {default_double_list_val_.begin(), default_double_list_val_.end()});
          }
        }
        break;
      }
      case AttrType::STRING: {
        if (auto p = context->GetStringItemAttr(result, input_accessor)) {
          context->SetStringItemAttr(result, output_accessor, std::string(p->data(), p->size()));
        } else {
          if (fill_default_val_) {
            context->SetStringItemAttr(result, output_accessor, default_string_val_);
          }
        }
        break;
      }
      case AttrType::STRING_LIST: {
        if (auto p = context->GetStringListItemAttr(result, input_accessor)) {
          std::vector<std::string> string_values;
          string_values.reserve(p->size());
          std::for_each(p->begin(), p->end(),
                        [this, &string_values, &string_dedup](const absl::string_view &elem) {
                          if (!dedup_to_item_attr_) {
                            string_values.emplace_back(std::string(elem.data(), elem.size()));
                          } else {
                            if (string_dedup.insert(elem).second) {
                              string_values.emplace_back(std::string(elem.data(), elem.size()));
                            }
                          }
                        });
          context->SetStringListItemAttr(result, output_accessor, std::move(string_values));
        } else {
          if (fill_default_val_) {
            if (!dedup_to_item_attr_) {
              context->SetStringListItemAttr(
                  result, output_accessor,
                  {default_string_list_val_.begin(), default_string_list_val_.end()});
            } else {
              std::vector<std::string> string_values;
              std::for_each(default_string_list_val_.begin(), default_string_list_val_.end(),
                            [&string_values, &string_dedup](const std::string &elem) {
                              if (string_dedup.insert(elem).second) {
                                string_values.emplace_back(elem);
                              }
                            });
              context->SetStringListItemAttr(result, output_accessor, std::move(string_values));
            }
          }
        }
        break;
      }
      default:
        break;
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPackItemAttrEnricher, CommonRecoPackItemAttrEnricher)
FACTORY_REGISTER(JsonFactoryClass, NrRecoItemAttrPackEnricher, CommonRecoPackItemAttrEnricher)

}  // namespace platform
}  // namespace ks
