#pragma once

#include <map>
#include <memory>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "infra/kconf/src/kconf/kconf.h"
#include "ks/base/abtest2/proto/id_mapping/abtest_mapping_id.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "serving_base/server_base/kess_client.h"
#include "third_party/abseil/absl/strings/str_format.h"

namespace ks {
namespace platform {
namespace id_mapping_client {
class IdMappingClient {
 public:
  IdMappingClient() = default;

  bool Init(const std::string &kess_name_kconf_key, const std::string &req_type) {
    static auto default_kess_name = std::make_shared<std::string>("reco-id-mapping-service-dryrun");
    kess_name_kconf_ = ks::infra::KConf().Get(kess_name_kconf_key, default_kess_name);
    timeout_ms_kconf_ =
        ks::infra::KConf().Get("reco.rpc.idMappingClientDefaultTimeOutMs", static_cast<int64>(40));
    LOG(INFO) << "IdMappingClient init kess_name: " << *(kess_name_kconf_->Get())
              << " timeout_ms: " << timeout_ms_kconf_->Get();
    id_mapping_req_ = std::make_unique<ks::platform::CommonRecoRequest>();
    id_mapping_resp_ = std::make_unique<ks::platform::CommonRecoResponse>();
    if (!id_mapping_req_) {
      return false;
    }
    if (!id_mapping_resp_) {
      return false;
    }
    req_type_ = req_type;
    return true;
  }

  ks::abtest2::AbtestMappingId GetIdMapping(const std::string &request_id, uint64 uid, const std::string &did,
                                            int64 timeout_ms = 0) {
    int64 to_ms = timeout_ms;
    if (to_ms == 0) {
      to_ms = timeout_ms_kconf_->Get();
    }
    auto mapping_id = ReqAndParseMappingId(request_id, uid, did, to_ms);
    SetDefaultMappingId(uid, did, &mapping_id);
    return mapping_id;
  }

  // NOTE(zhaoyang09): 降级控制
  bool EnableRequestIdMapping(uint64 uid) {
    static auto OnOff = ks::infra::KConf().Get("reco.rpc.requestIdMappingOnlineSwitch",
                                               std::make_shared<ks::infra::TailNumber>());
    if (!OnOff || !OnOff->Get()) {
      return false;
    }
    return OnOff->Get()->IsOnFor(uid);
  }

 private:
  ks::abtest2::AbtestMappingId ReqAndParseMappingId(const std::string &request_id, uint64 uid,
                                                    const std::string &did, int64 timeout_ms = 0) {
    ks::abtest2::AbtestMappingId mapping_id;

    // NOTE(zhaoyang09): did 必须有值。
    if (did.empty()) {
      return mapping_id;
    }

    // NOTE(zhaoyang09): 降级处理。
    if (!EnableRequestIdMapping(uid)) {
      SetDefaultMappingId(uid, did, &mapping_id);
      return mapping_id;
    }

    id_mapping_req_->Clear();
    id_mapping_resp_->Clear();

    id_mapping_req_->set_request_type(req_type_);
    id_mapping_req_->set_user_id(uid);
    id_mapping_req_->set_device_id(did);
    id_mapping_req_->set_request_id(request_id);
    bool status = GetRequest(*id_mapping_req_, &*id_mapping_resp_, timeout_ms);
    if (!status) {
      VLOG(100) << "GetMappingIdMap InitAbTestIdMapping error";
      SetDefaultMappingId(uid, did, &mapping_id);
      return mapping_id;
    }
    if (id_mapping_resp_->common_attr_size() > 0) {
      std::string id_mapping_pb_str;
      for (const auto &attr : id_mapping_resp_->common_attr()) {
        if (attr.name() == "id_mapping_pb_str") {
          id_mapping_pb_str = attr.string_value();
        }
      }
      if (id_mapping_pb_str.empty()) {
        VLOG(100) << "GetMappingIdMap InitAbTestIdMapping error id_mapping_pb_str is empty";
        return mapping_id;
      }
      mapping_id.ParseFromString(id_mapping_pb_str);
      if (mapping_id.mapping_ids_size() == 0) {
        VLOG(100) << "GetMappingIdMap InitAbTestIdMapping error abtest_mapping_id is empty";
      }
    } else {
      VLOG(100) << "id mapping server is disable";
    }
    return mapping_id;
  }

 public:
  // NOTE(zhaoyang09): 兜底处理
  void SetDefaultMappingId(uint64 uid, const std::string &did, ::ks::abtest2::AbtestMappingId *mapping_id) {
    if (mapping_id->mapping_ids_size() > 0) {
      return;
    }
    static auto default_key = std::make_shared<std::string>("uid_did_mapping_v2");
    static auto mapping_key = ks::infra::KConf().Get("reco.rpc.idMappingDefaultKey", default_key);
    auto ab_single_mapping_id = ::ks::abtest2::AbtestSingleMappingId();
    if (uid == 0) {
      ab_single_mapping_id.set_mapping_user_id(did);
      ab_single_mapping_id.set_mapping_device_id(did);

    } else {
      std::string uid_str = absl::StrFormat("%d", uid);
      ab_single_mapping_id.set_mapping_user_id(uid_str);
      ab_single_mapping_id.set_mapping_device_id(uid_str);
    }
    mapping_id->mutable_mapping_ids()->insert({*(mapping_key->Get()), ab_single_mapping_id});
  }

 private:
  bool GetRequest(const ks::platform::CommonRecoRequest &req, ks::platform::CommonRecoResponse *res,
                  int64 timeout_ms) {
    auto server_name = kess_name_kconf_->Get();
    KESS_GRPC_RETURN(*server_name, "PRODUCTION", "s0", timeout_ms, req, res,
                     ks::platform::kess::CommonRecoLeafService, Recommend, false);
    return true;
  }

 private:
  std::string req_type_ = "offline_single";
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::string>>> kess_name_kconf_;
  std::shared_ptr<ks::infra::KsConfig<int64>> timeout_ms_kconf_;
  std::unique_ptr<ks::platform::CommonRecoRequest> id_mapping_req_;
  std::unique_ptr<ks::platform::CommonRecoResponse> id_mapping_resp_;
};
}  // namespace id_mapping_client

static const std::map<std::string, std::string> KESS_NAME_KCONF_MAP = {
    {"ONLINE", "reco.rpc.idMappingOnlineKessName"},
    {"OFFLINE", "reco.rpc.idMappingOfflineKessName"},
    {"SEARCH", "reco.rpc.idMappingSearchKessName"},
    {"PUSH", "reco.rpc.idMappingPushKessName"}};

static const std::map<std::string, std::string> REQ_TYPE_MAP = {{"ONLINE", "online_single"},
                                                                {"OFFLINE", "offline_single"},
                                                                {"SEARCH", "online_single"},
                                                                {"PUSH", "offline_single"}};

class CommonRecoSupplementIdMappingEnricher : public CommonRecoBaseEnricher {
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    auto *request = const_cast<ks::platform::CommonRecoRequest *>(context->GetRequest());

    if (!request->has_abtest_mapping_id() || request->abtest_mapping_id().mapping_ids_size() == 0) {
      auto mapping_id = id_mapping_client_.GetIdMapping(context->GetRequestId(), context->GetUserId(),
                                                        context->GetDeviceId());
      request->mutable_abtest_mapping_id()->CopyFrom(mapping_id);
    }
    return;
  }

 private:
  bool InitProcessor() override {
    std::string cluster = config()->GetString("cluster", "");
    if (cluster.empty()) {
      LOG(ERROR) << "CommonRecoSupplementIdMappingEnricher init failed! cluster is empty!";
      return false;
    }
    auto iter = KESS_NAME_KCONF_MAP.find(cluster);
    if (iter == KESS_NAME_KCONF_MAP.end()) {
      LOG(ERROR)
          << "CommonRecoSupplementIdMappingEnricher init failed! cluster not in (ONLINE, OFFLINE, SEARCH, "
             "PUSH) ! cluster: "
          << cluster;
      return false;
    }
    std::string kess_name_kconf_key = iter->second;
    iter = REQ_TYPE_MAP.find(cluster);
    if (iter == REQ_TYPE_MAP.end()) {
      LOG(ERROR)
          << "CommonRecoSupplementIdMappingEnricher init failed! cluster not in (ONLINE, OFFLINE, SEARCH, "
             "PUSH) ! cluster: "
          << cluster;
      return false;
    }
    std::string req_type = iter->second;
    if (!id_mapping_client_.Init(kess_name_kconf_key, req_type)) {
      LOG(ERROR) << "CommonRecoSupplementIdMappingEnricher init failed! id_mapping_client_ init failed! ";
      return false;
    }
    return true;
  }

 private:
  id_mapping_client::IdMappingClient id_mapping_client_;
};

}  // namespace platform
}  // namespace ks
