#pragma once

#include <memory>
#include <string>
#include <unordered_map>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.grpc.pb.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

using ClientReaderWriter =
    ::grpc::ClientReaderWriter<::ks::platform::CommonRecoRequest, ::ks::platform::CommonRecoResponse>;

class CommonRecoStreamingEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoStreamingEnricher() {}

  bool InitProcessor() override {
    service_ = config()->GetString("service", "");
    if (service_.empty()) {
      LOG(ERROR) << "CommonRecoStreamingEnricher init failed! Missing 'service' config";
      return false;
    }
    service_group_ = config()->GetString("service_group", "PRODUCTION");
    client_ = base::KessGrpcClient::Singleton()->GetClient2(service_, service_group_);
    if (client_ == nullptr) {
      LOG(ERROR) << "Fail to get client for service: " << service_ << " group: " << service_group_;
      return false;
    }
    timeout_ms_ = config()->GetInt("timeout_ms", 0);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "Invalid timeout_ms: " << timeout_ms_ << " config: " << config()->ToString();
      return false;
    }
    if (!ParseAttrsConfig("send_common_attrs", &send_common_attrs_, "No common attr will be sent") ||
        !ParseAttrsConfig("recv_common_attrs", &recv_common_attrs_, "All common attrs will be dropped")) {
      return false;
    }
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void OnPipelineExit(ReadableRecoContextInterface *context) override;

  bool PrepareStreaming(MutableRecoContextInterface *context);

  bool SendRequest(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

  void RecvResponse(MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end);

  bool ParseAttrsConfig(const std::string &config_name,
                        std::unordered_map<std::string, std::string> *attrs_map,
                        const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoStreamingEnricher init failed! name is required for " << config_name;
            return false;
          }

          const std::string &alias = c->GetString("as", attr_name);
          attrs_map->emplace(attr_name, alias);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "CommonRecoStreamingEnricher init failed! name should not be empty for "
                       << config_name;
            return false;
          }
          attrs_map->emplace(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(INFO, 100) << "streaming_enrich " << config_name << " is empty, "
                             << default_action_description;
    }
    return true;
  }

 private:
  std::string service_;
  std::string service_group_;
  std::shared_ptr<ks::kess::rpc::grpc::Client2> client_;
  int timeout_ms_ = 500;
  std::unordered_map<std::string, std::string> send_common_attrs_;
  std::unordered_map<std::string, std::string> recv_common_attrs_;
  std::unique_ptr<ClientReaderWriter> streaming_;
  std::unique_ptr<::grpc::ClientContext> client_context_;
  std::unique_ptr<ks::platform::CommonRecoLeafStreamingService::Stub> stub_;
};

}  // namespace platform
}  // namespace ks

