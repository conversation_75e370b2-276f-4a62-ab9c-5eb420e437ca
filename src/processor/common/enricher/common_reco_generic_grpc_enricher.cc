#include "dragon/src/processor/common/enricher/common_reco_generic_grpc_enricher.h"

#include <kess/rpc/grpc/event_loop.h>
#include <utility>

DECLARE_int32(kess_client_event_loop_nums);

namespace ks {
namespace platform {

void CommonRecoGenericGrpcEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  std::string service = GetStringProcessorParameter(context, kess_service_, "");
  if (service.empty()) {
    CL_LOG_WARNING_EVERY("grpc_enrich", "empty_service_name", 100)
        << "generic grpc request cancelled, empty kess_service name.";
    return;
  }

  const auto *request_message = context->GetPtrCommonAttr<google::protobuf::Message>(request_attr_);
  if (!request_message) {
    CL_LOG_ERROR("grpc_enrich", "attr_not_found:" + request_attr_)
        << "generic grpc request is null: " << request_attr_;
    return;
  }

  auto client = base::KessGrpcClient::Singleton()->GetClient2(service, service_group_);
  if (!client) {
    CL_LOG_ERROR_EVERY("grpc_enrich", "get_no_client:" + service, 1000)
        << "failed to get client for service: " << service << ", group: " << service_group_;
    return;
  }

  auto channel = client->All()->SelectOne();
  if (!channel) {
    CL_LOG_ERROR_EVERY("grpc_enrich", "no_alive_server:" + service, 1000)
        << "no grpc server alive: " << service << ", group: " << service_group_;
    return;
  }

  response_msg_->Clear();

  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", -1);
  if (timeout_ms > 0) {
    options_.SetTimeout(std::chrono::milliseconds(timeout_ms));
  } else {
    CL_LOG_EVERY_N(WARNING, 100) << "invalid timeout_ms=" << timeout_ms << " for service " << service
                                 << ", processor: " << GetName();
  }
  std::string request_info = "kess_service: " + service + ", kess_cluster: " + service_group_ +
                             ", timeout_ms: " + std::to_string(timeout_ms) + ", method_name: " + method_name_;

  // 这里的 down cast 并不改变 request_message|response_msg 的类型
  // 只是为了调用 AsyncCall. 因为 AsyncCall 要求的 request|response 必须是 google::protobuf::Message 的子类。
  auto req = google::protobuf::down_cast<const ks::platform::CommonRecoRequest *>(request_message);
  auto res = google::protobuf::down_cast<ks::platform::CommonRecoResponse *>(response_msg_.get());

  static ::ks::kess::rpc::grpc::EventLoopGroup event_loop_group(FLAGS_kess_client_event_loop_nums);
  ks::kess::rpc::grpc::CallHelper call_helper(*req, res);
  auto future =
      channel->AsyncCall(call_helper, method_name_, options_, *req, res, event_loop_group.SelectOne())
          .ThenApply([](const ::grpc::Status &status, ::google::protobuf::Message *response) {
            return std::make_tuple(
                status, google::protobuf::down_cast<::ks::platform::CommonRecoResponse *>(response));
          });
  auto callback = [this, context](::ks::platform::CommonRecoResponse *resp) {
    context->SetPtrCommonAttr(response_attr_, response_msg_);
  };
  RegisterAsyncCallback(context, std::move(future), callback, request_info);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoGenericGrpcEnricher, CommonRecoGenericGrpcEnricher)

}  // namespace platform
}  // namespace ks
