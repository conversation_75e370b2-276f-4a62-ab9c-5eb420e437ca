#include "dragon/src/processor/common/enricher/common_reco_abtest_experiment_info_enricher.h"

#include <unordered_map>
#include <unordered_set>

#include "dragon/src/util/id_mapping_util.h"

namespace ks {
namespace platform {

void CommonRecoAbtestExpInfoEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  std::unordered_set<std::string> ab_world_names;
  std::unordered_map<std::string, const AbExperimentInfo *> world_name_exp_map;

  for (const auto &exp : ab_experiments_) {
    const std::string &param = exp.ab_key;
    std::string world_name;
    if (!param.empty() && biz_ != ks::AbtestBiz::NULL_BIZ) {
      world_name = ks::abtest::AbtestInstance::GetWorldByKey(biz_, param);
    }
    if (world_name.empty()) {
      world_name = GetStringProcessorParameter(context, exp.world_config, "");
    }
    if (world_name.empty()) {
      continue;
    }
    ab_world_names.insert(world_name);
    world_name_exp_map.insert({std::move(world_name), &exp});
  }

  auto did = GetStringProcessorParameter(context, "device_id", context->GetDeviceId());
  auto uid = GetIntProcessorParameter(context, "user_id", context->GetUserId());

  auto mapping_id = GetABMappingIdFromRequest(context->GetRequest());
  auto exp_info = ks::abtest::GetExperimentInfo(uid, did, ab_world_names, mapping_id);

  for (const auto &info : exp_info) {
    auto it = world_name_exp_map.find(info.first);
    if (it != world_name_exp_map.end()) {
      const AbExperimentInfo &exp = *(it->second);
      if (!exp.save_exp_id_to.empty()) {
        context->SetStringCommonAttr(exp.save_exp_id_to, info.second.experimentId);
      }
      if (!exp.save_group_id_to.empty()) {
        context->SetStringCommonAttr(exp.save_group_id_to, info.second.groupId);
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAbtestExpInfoEnricher, CommonRecoAbtestExpInfoEnricher)

}  // namespace platform
}  // namespace ks
