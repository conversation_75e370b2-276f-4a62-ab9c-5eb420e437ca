#include "dragon/src/processor/common/enricher/common_reco_random_item_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoRandomItemAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  std::for_each(begin, end, [this, context](const CommonRecoResult &result) {
    uint64 item_key = result.item_key;
    if (attr_type_ == AttrType::INT) {
      context->SetIntItemAttr(item_key, attr_name_, random_.GetInt(0, 100));
    } else if (attr_type_ == AttrType::FLOAT) {
      context->SetDoubleItemAttr(item_key, attr_name_, random_.GetDouble());
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRandomItemAttrEnricher, CommonRecoRandomItemAttrEnricher)

}  // namespace platform
}  // namespace ks
