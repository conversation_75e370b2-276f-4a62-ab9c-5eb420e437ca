#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

class CommonRecoDotProductEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoDotProductEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  void HandleCommonLevel(MutableRecoContextInterface *context, RecoResultConstIter begin,
                         RecoResultConstIter end);

  void HandleItemLevel(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);

 private:
  bool InitProcessor() override {
    x_attr_ = config()->GetString("x_attr");
    if (x_attr_.empty()) {
      LOG(ERROR) << "CommonRecoDotProductEnricher init failed! Missing 'x_attr' config.";
      return false;
    }
    y_attr_ = config()->GetString("y_attr");
    if (y_attr_.empty()) {
      LOG(ERROR) << "CommonRecoDotProductEnricher init failed! Missing 'y_attr' config.";
      return false;
    }
    y_is_common_attr_ = config()->GetBoolean("y_is_common_attr", false);
    save_result_to_ = config()->GetString("save_result_to");
    if (save_result_to_.empty()) {
      LOG(ERROR) << "CommonRecoDotProductEnricher init failed! Missing 'save_result_to' config.";
      return false;
    }
    return true;
  }

 private:
  std::string x_attr_;
  std::string y_attr_;
  bool y_is_common_attr_ = false;
  std::string save_result_to_;

  CommonAttr *x_attr_accessor_ = nullptr;
  AttrValue *y_attr_accessor_ = nullptr;
  AttrValue *save_result_to_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoDotProductEnricher);
};

}  // namespace platform
}  // namespace ks
