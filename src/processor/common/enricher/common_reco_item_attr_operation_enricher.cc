#include "dragon/src/processor/common/enricher/common_reco_item_attr_operation_enricher.h"

#include "serving_base/util/math.h"

namespace ks {
namespace platform {

void CommonRecoItemAttrOperationEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  bool user_item_attr_b = (common_attr_b_config_ == nullptr);
  double common_attr_b_val = GetDoubleProcessorParameter(context, common_attr_b_config_, 0.0, true);

  auto *item_attr_a_accessor = context->GetItemAttrAccessor(item_attr_a_attr_);
  auto *item_attr_b_accessor = context->GetItemAttrAccessor(item_attr_b_attr_);
  auto *output_accessor = context->GetItemAttrAccessor(output_attr_);

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    // 读取
    double attr_a_val = 0.0;
    if (!GetNumericValue(context, item_attr_a_accessor, result, &attr_a_val)) {
      return;
    }

    double attr_b_val = common_attr_b_val;
    if (user_item_attr_b) {
      if (!GetNumericValue(context, item_attr_b_accessor, result, &attr_b_val)) {
        return;
      }
    }

    // 计算
    double output_attr_val = 0.0;
    if (operator_ == "+") {
      output_attr_val = attr_a_val + attr_b_val;
    } else if (operator_ == "-") {
      output_attr_val = attr_a_val - attr_b_val;
    } else if (operator_ == "*") {
      output_attr_val = attr_a_val * attr_b_val;
    } else if (operator_ == "/") {
      output_attr_val = attr_a_val / attr_b_val;
    } else if (operator_ == "&") {
      output_attr_val = static_cast<int>(attr_a_val) & static_cast<int>(attr_b_val);
    } else if (operator_ == "|") {
      output_attr_val = static_cast<int>(attr_a_val) | static_cast<int>(attr_b_val);
    } else if (operator_ == "^") {
      output_attr_val = static_cast<int>(attr_a_val) ^ static_cast<int>(attr_b_val);
    } else if (operator_ == "<<") {
      output_attr_val = static_cast<int>(attr_a_val) << static_cast<int>(attr_b_val);
    } else if (operator_ == ">>") {
      output_attr_val = static_cast<int>(attr_a_val) >> static_cast<int>(attr_b_val);
    } else if (operator_ == "pow") {
      output_attr_val = std::pow(attr_a_val, attr_b_val);
    }

    // 写入
    if (operator_ == "&" || operator_ == "|" || operator_ == "^" || operator_ == "<<" || operator_ == ">>") {
      context->SetIntItemAttr(result, output_accessor, output_attr_val);
    } else {
      context->SetDoubleItemAttr(result, output_accessor, output_attr_val);
    }
  });
}

bool CommonRecoItemAttrOperationEnricher::GetNumericValue(MutableRecoContextInterface *context,
                                                          ItemAttr *attr_accessor,
                                                          const CommonRecoResult &result,
                                                          double *value) const {
  if (!attr_accessor || !value) {
    return false;
  }
  if (attr_accessor->value_type == AttrType::FLOAT) {
    if (auto p = context->GetDoubleItemAttr(result, attr_accessor)) {
      *value = *p;
      return true;
    }
  } else if (attr_accessor->value_type == AttrType::INT) {
    if (auto p = context->GetIntItemAttr(result, attr_accessor)) {
      *value = *p;
      return true;
    }
  }
  return false;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemAttrOperationEnricher, CommonRecoItemAttrOperationEnricher)
FACTORY_REGISTER(JsonFactoryClass, NrItemAttrOperationEnricher, CommonRecoItemAttrOperationEnricher)

}  // namespace platform
}  // namespace ks
