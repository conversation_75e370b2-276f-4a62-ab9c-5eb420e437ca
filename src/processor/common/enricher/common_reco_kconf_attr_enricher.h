#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kconf/kconf.h"

namespace ks {
namespace platform {

class CommonRecoKconfAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoKconfAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct KconfConfig {
    const base::Json *config = nullptr;
    const base::Json *kconf_key = nullptr;
    const base::Json *json_path = nullptr;
    const base::Json *default_value = nullptr;
    KconfParamType value_type = KconfParamType::UNKNOWN;
    bool is_common_attr = true;
    std::string attr_name;

    KconfConfig(const base::Json *config, const base::Json *kconf_key)
        : config(config), kconf_key(kconf_key) {}
  };

  struct KconfValue {
    // kconf 需要用到的格式
    // std::shared_ptr<KsConfig<T>>
    std::shared_ptr<infra::KsConfig<bool>> bool_value;
    std::shared_ptr<infra::KsConfig<int64>> int_value;
    std::shared_ptr<infra::KsConfig<double>> double_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::string>>> string_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<::Json::Value>>> json_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::vector<int64>>>> int_list_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::vector<double>>>> double_list_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::vector<std::string>>>> string_list_value;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::vector<bool>>>> bool_list_value;
  };

  bool InitProcessor() override {
    const auto *kconf_configs = config()->Get("kconf_configs");
    if (!kconf_configs || !kconf_configs->IsArray()) {
      LOG(ERROR) << "CommonRecoKconfAttrEnricher"
                 << " init failed! Missing 'kconf_configs' or it is not an array";
      return false;
    }

    folly::F14FastMap<std::string, KconfParamType> kconf_type_map;
    for (const auto *config : kconf_configs->array()) {
      if (!config->IsObject()) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Values of 'kconf_configs' "
                   << "should be objects";
        return false;
      }
      std::string kconf_key = config->GetString("kconf_key");
      if (kconf_key.empty()) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Missing 'kconf_key' "
                   << "or it is empty";
        return false;
      }

      KconfConfig kconf_config(config, config->Get("kconf_key"));

      std::string type = config->GetString("value_type");
      if (!type.empty()) {
        kconf_config.value_type = RecoUtil::ParseKconfParamType(type);
        if (kconf_config.value_type == KconfParamType::UNKNOWN) {
          LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Unsupported value_type: " << type
                     << ", should be bool/int64/double/string/json/list_int64/list_double/list_string";
          return false;
        }
      }

      const auto *default_value = config->Get("default_value");
      if (default_value) {
        kconf_config.default_value = default_value;
        // 若配置了 default_value 则检查与 value_type 的类型一致性, 或做类型自动推断
        if (default_value->IsInteger()) {
          if (kconf_config.value_type != KconfParamType::UNKNOWN &&
              kconf_config.value_type != KconfParamType::INT64) {
            LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Mismatched value_type and default_value "
                       << "for kconf_key: " << kconf_key;
            return false;
          }
          kconf_config.value_type = KconfParamType::INT64;
        } else if (default_value->IsDouble()) {
          if (kconf_config.value_type != KconfParamType::UNKNOWN &&
              kconf_config.value_type != KconfParamType::DOUBLE) {
            LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Mismatched value_type and default_value "
                       << "for kconf_key: " << kconf_key;
            return false;
          }
          kconf_config.value_type = KconfParamType::DOUBLE;
        } else if (default_value->IsString()) {
          if (kconf_config.value_type != KconfParamType::UNKNOWN &&
              kconf_config.value_type != KconfParamType::STRING) {
            LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Mismatched value_type and default_value"
                       << ", kconf_key: " << kconf_key;
            return false;
          }
          kconf_config.value_type = KconfParamType::STRING;
        } else if (default_value->IsBoolean()) {
          if (kconf_config.value_type != KconfParamType::UNKNOWN &&
              kconf_config.value_type != KconfParamType::BOOLEAN) {
            LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Mismatched value_type and default_value"
                       << ", kconf_key: " << kconf_key;
            return false;
          }
          kconf_config.value_type = KconfParamType::BOOLEAN;
        } else if (default_value->IsArray()) {
          // do nothing
        } else {
          LOG(ERROR)
              << "CommonRecoKconfAttrEnricher init failed! Unsupported default_value type for kconf_key: "
              << kconf_key << ", should be bool/int64/double/string or array";
          return false;
        }
      }

      const auto *json_path = config->Get("json_path");
      if (json_path) {
        if (!json_path->IsString()) {
          LOG(ERROR)
              << "CommonRecoKconfAttrEnricher init failed! json_path should be a string for kconf_key: "
              << kconf_key << ", value found: " << json_path->ToString();
          return false;
        }
        kconf_config.json_path = json_path;
        // 若配置了 json_path 则强制类型为 json
        kconf_config.value_type = KconfParamType::JSON;
      }

      if (kconf_config.value_type == KconfParamType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Missing 'value_type' or 'json_path'"
                   << " or 'default_value' config, kconf_key: " << kconf_key;
        return false;
      }

      auto pr = kconf_type_map.insert({kconf_key, kconf_config.value_type});
      if (pr.first->second != kconf_config.value_type) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Found inconsistent value_type for kconf_key "
                   << kconf_key;
        return false;
      }

      std::string export_common_attr = config->GetString("export_common_attr");
      std::string export_item_attr = config->GetString("export_item_attr");
      if (export_common_attr.empty() && export_item_attr.empty()) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! "
                   << "Missing 'export_common_attr' and 'export_item_attr' "
                   << "or it is not a string, kconf_key: " << kconf_key;
        return false;
      }
      if (!export_common_attr.empty() && !export_item_attr.empty()) {
        LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! "
                   << "'export_common_attr' and 'export_item_attr' "
                   << "cannot be set at the same time, kconf_key: " << kconf_key;
        return false;
      }

      kconf_config.is_common_attr = !export_common_attr.empty();
      if (!kconf_config.is_common_attr) {
        if (kconf_config.value_type != KconfParamType::JSON ||
            !RecoUtil::ExtractCommonAttrFromExpr(json_path)) {
          LOG(ERROR) << "CommonRecoKconfAttrEnricher init failed! Only dynamic json path "
                     << " supported for export_item_attr, kconf_key: " << kconf_key;
          return false;
        }
      }

      kconf_config.attr_name = kconf_config.is_common_attr ? export_common_attr : export_item_attr;
      kconf_configs_.push_back(std::move(kconf_config));
    }

    return true;
  }

  const KconfValue &GetOrCreateKconfValue(ReadableRecoContextInterface *context,
                                          const KconfConfig &kconf_config);

 private:
  std::vector<KconfConfig> kconf_configs_;
  folly::F14FastMap<std::string, KconfValue> kconf_value_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoKconfAttrEnricher);
};

}  // namespace platform
}  // namespace ks
