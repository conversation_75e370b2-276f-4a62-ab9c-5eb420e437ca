#include "dragon/src/processor/common/enricher/common_reco_parse_from_custom_kv_user_info_enricher.h"

#include <memory>
#include <utility>

namespace ks {
namespace platform {

#define CUSTOM_KV_USER_INFO_SWITCH_FN(reader_fn, idx, context_fn, field_name) \
  {                                                                           \
    auto v = reader->reader_fn(idx);                                          \
    if (!v) {                                                                 \
      CL_LOG_EVERY_N(INFO, 1000) << "field nullopt: " << field_name;          \
      continue;                                                               \
    }                                                                         \
    context->context_fn(field_name, std::move(*v));                           \
  }                                                                           \
  break

#define CUSTOM_KV_USER_INFO_SWITCH_FLOAT_LIST_FN(idx, field_name)    \
  {                                                                  \
    auto v = reader->FastReadFloatListView(idx);                     \
    if (!v) {                                                        \
      CL_LOG_EVERY_N(INFO, 1000) << "field nullopt: " << field_name; \
      continue;                                                      \
    }                                                                \
    std::vector<double> v2(v->begin(), v->end());                    \
    context->SetDoubleListCommonAttr(field_name, std::move(v2));     \
  }                                                                  \
  break

void CommonRecoParseFromCustomKVUserInfoEnricher::Enrich(MutableRecoContextInterface *context,
                                                         RecoResultConstIter begin, RecoResultConstIter end) {
  auto maybe_user_info_str = context->GetStringCommonAttr(custom_user_info_from_);
  if (!maybe_user_info_str) {
    CL_LOG_ERROR("parse_from_custom_kv_user_info", "user_info_missing") << "user info miss";
    return;
  }
  std::unique_ptr<fdk::AdaptiveReader> reader =
      fdk::FDK::GetInstance()->CreateReader(fdk_options_, fdk_metadata_, *maybe_user_info_str);
  if (!reader || !reader->IsValid()) {
    CL_LOG_ERROR("parse_from_custom_kv_user_info", "create_reader_failed") << "fdk::FDK CreateReader failed";
    return;
  }
  for (const auto &field : fields_to_read_) {
    auto p = reader->GetFieldIndexAndValueType(field);
    if (p.first < 0) {
      CL_LOG_WARNING_EVERY("parse_from_custom_kv_user_info", "field_illegal." + field, 1000)
          << "field illegal: " << field;
      continue;
    }
    switch (p.second) {
      case ::fdk::proto::VALUE_TYPE_INT64:
        CUSTOM_KV_USER_INFO_SWITCH_FN(FastReadInt64, p.first, SetIntCommonAttr, field);
      case ::fdk::proto::VALUE_TYPE_FLOAT:
        CUSTOM_KV_USER_INFO_SWITCH_FN(FastReadFloat, p.first, SetDoubleCommonAttr, field);
      case ::fdk::proto::VALUE_TYPE_STRING:
        CUSTOM_KV_USER_INFO_SWITCH_FN(FastReadString, p.first, SetStringCommonAttr, field);
      case ::fdk::proto::VALUE_TYPE_INT64_LIST:
        CUSTOM_KV_USER_INFO_SWITCH_FN(FastReadInt64List, p.first, SetIntListCommonAttr, field);
      case ::fdk::proto::VALUE_TYPE_FLOAT_LIST:
        CUSTOM_KV_USER_INFO_SWITCH_FLOAT_LIST_FN(p.first, field);
      case ::fdk::proto::VALUE_TYPE_STRING_LIST:
        CUSTOM_KV_USER_INFO_SWITCH_FN(FastReadStringList, p.first, SetStringListCommonAttr, field);
      default:
        CL_LOG_ERROR("parse_from_custom_kv_user_info", "type_illegal")
            << "type illegal: field = " << field
            << ", type_value: " << ::fdk::proto::ValueType_Name(p.second);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoParseFromCustomKVUserInfoEnricher,
                 CommonRecoParseFromCustomKVUserInfoEnricher)
}  // namespace platform
}  // namespace ks
