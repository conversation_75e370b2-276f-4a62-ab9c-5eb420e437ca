#include "dragon/src/processor/common/enricher/common_reco_sphinx_parameter_enricher.h"

namespace ks {
namespace platform {

void CommonRecoSphinxParameterEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  std::string service = GetStringProcessorParameter(context, kess_service_, "");
  if (service.empty()) {
    CL_LOG_WARNING_EVERY("sphinx_parameter", "empty_service_name", 100)
        << "parameter enrich cancelled, kess_service empty.";
    return;
  }
  std::string model_name = GetStringProcessorParameter(context, model_name_, "");
  if (model_name.empty()) {
    CL_LOG_WARNING_EVERY("sphinx_parameter", "empty_model_name", 100)
        << "parameter enrich cancelled, model_name empty.";
    return;
  }

  uint64 user_id = context->GetUserId();
  const std::string &device_id = context->GetDeviceId();
  const std::string &request_id = context->GetRequestId();
  // TODO(qianlei): 目前 parameter server 没有 future 版的异步接口，暂时同步处理。后续可以改成异步
  parameter_client_.AsyncFetch(reco_biz_, model_name, user_id, device_id, request_id, "", true, service,
                               timeout_ms_);
  parameter_client_.AsyncWait();
  auto *resp = parameter_client_.Response();
  if (!resp) {
    CL_LOG_ERROR_EVERY("sphinx_parameter", "null_response", 100)
        << "parameter enrich cancelled, parameter response is nullptr.";
    return;
  }
  if (resp->parameter().empty()) {
    CL_LOG_WARNING_EVERY("sphinx_parameter", "get_empty_params", 100)
        << "parameter enrich cancelled, parameter response empty.";
    return;
  }

  base::Json parameter_json(base::StringToJson(resp->parameter()));
  if (parameter_json.objects().empty()) {
    CL_LOG(INFO) << "CommonRecoSphinxParameterEnricher cancelled: received base parameters";
    return;
  }
  double number = 0.0;
  std::vector<std::string> miss_params;
  for (const auto &param : params_) {
    if (parameter_json.GetNumber(param.first, &number)) {
      context->SetDoubleCommonAttr(param.second, number);
    } else {
      miss_params.push_back(param.first);
    }
  }
  if (!ignore_warning_) {
    for (const auto &name : miss_params) {
      CL_LOG_WARNING_EVERY("sphinx_parameter", "param_not_found:" + name, 1000)
          << "sphinx parameter '" << name << "' not found in response.";
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoSphinxParameterEnricher, CommonRecoSphinxParameterEnricher)

}  // namespace platform
}  // namespace ks
