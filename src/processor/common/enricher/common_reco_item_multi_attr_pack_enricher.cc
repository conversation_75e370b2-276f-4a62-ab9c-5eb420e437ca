#include "dragon/src/processor/common/enricher/common_reco_item_multi_attr_pack_enricher.h"

#include <string>
#include <utility>
#include <vector>

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

bool CommonRecoItemMultiAttrPackEnricher::InitProcessor() {
  bool ret = true;

  std::string pt_str = config()->GetString("pack_mode", "concat_rowmajor");
  if (pt_str == "concat_rowmajor") {
    pack_mode_ = CommonRecoItemMultiAttrPackEnricher::CONCAT_ROW_MAJOR;
  } else if (pt_str == "concat_colmajor") {
    pack_mode_ = CommonRecoItemMultiAttrPackEnricher::CONCAT_COL_MAJOR;
  } else {
    ret = false;
    CL_LOG(ERROR) << "pack mode error: " << pt_str;
  }

  to_attr_ = config()->GetString("to_attr", "");
  if (to_attr_.empty()) {
    ret = false;
    CL_LOG(ERROR) << "to_attr is empty ...";
  }

  from_attrs_.clear();
  auto froms = config()->Get("from_attrs");
  if (froms && froms->IsArray()) {
    for (auto e : froms->array()) {
      std::string tmp;
      if (e->IsString() && e->StringValue(&tmp) && !tmp.empty()) {
        from_attrs_.emplace_back(tmp);
      }
    }
  }

  if (from_attrs_.size() == 0) {
    ret = false;
    CL_LOG(ERROR) << "from_attrs is empty ...";
  }

  CL_LOG(INFO) << "from_attrs size: " << from_attrs_.size() << ", to_attr: " << to_attr_
               << ", pack_mode: " << static_cast<int>(pack_mode_);

  auto default_value = config()->Get("default_value");
  if (default_value->IsInteger() && default_value->IntValue(&int_default_value_)) {
    CL_LOG(INFO) << "default value: " << int_default_value_;
  } else if (default_value->IsDouble() && default_value->FloatValue(&float_default_value_)) {
    CL_LOG(INFO) << "default value: " << float_default_value_;
  } else if (default_value->IsString() && default_value->StringValue(&string_default_value_)) {
    CL_LOG(INFO) << "default value: " << string_default_value_;
  }

  return ret;
}

void CommonRecoItemMultiAttrPackEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  if (pack_mode_ == CommonRecoItemMultiAttrPackEnricher::CONCAT_ROW_MAJOR ||
      pack_mode_ == CommonRecoItemMultiAttrPackEnricher::CONCAT_COL_MAJOR) {
    ConcatItemAttr(context, begin, end);
  }
}

bool CommonRecoItemMultiAttrPackEnricher::CheckValueType(MutableRecoContextInterface *context) {
  auto value_type = context->GetItemAttrType(from_attrs_[0]);
  if (from_attrs_.size() > 1) {
    for (auto i = 1; i < from_attrs_.size(); ++i) {
      auto value_type_i = context->GetItemAttrType(from_attrs_[i]);
      if (value_type_i != value_type) {
        CL_LOG(ERROR) << "item attr[" << i << "] value type mismatch: " << static_cast<int>(value_type_i)
                      << ", expect: " << static_cast<int>(value_type);
        return false;
      }
    }
  }

  if (value_type != AttrType::INT && value_type != AttrType::FLOAT && value_type != AttrType::STRING) {
    CL_LOG(ERROR) << "unsupported item attr_type: " << static_cast<int>(value_type);
    return false;
  }

  return true;
}

#define PACK_ITEM_VALUE_ROW_MAJOR(attr_type, getter, default_value, setter) \
  do {                                                                      \
    std::vector<attr_type> packed_values;                                   \
    packed_values.reserve(item_num *accessors.size());                      \
    for (int i = 0; i < item_num; ++i) {                                    \
      for (auto accessor : accessors) {                                     \
        auto v = (begin + i)->getter(accessor);                             \
        packed_values.emplace_back(v ? *v : default_value);                 \
      }                                                                     \
    }                                                                       \
    context->setter(to_attr_, std::move(packed_values));                    \
  } while (0)

#define PACK_ITEM_VALUE_COL_MAJOR(attr_type, getter, default_value, setter) \
  do {                                                                      \
    std::vector<attr_type> packed_values;                                   \
    packed_values.reserve(item_num *accessors.size());                      \
    for (auto accessor : accessors) {                                       \
      for (int i = 0; i < item_num; ++i) {                                  \
        auto v = (begin + i)->getter(accessor);                             \
        packed_values.emplace_back(v ? *v : default_value);                 \
      }                                                                     \
    }                                                                       \
    context->setter(to_attr_, std::move(packed_values));                    \
  } while (0)

void CommonRecoItemMultiAttrPackEnricher::ConcatItemAttr(MutableRecoContextInterface *context,
                                                         RecoResultConstIter begin, RecoResultConstIter end) {
  if (!CheckValueType(context)) return;

  std::vector<ItemAttr *> accessors;
  for (const auto &attr : from_attrs_) {
    auto accessor = context->GetItemAttrAccessor(attr);
    if (accessor) accessors.push_back(accessor);
  }

  int item_num = std::distance(begin, end);
  auto value_type = context->GetItemAttrType(from_attrs_[0]);
  if (pack_mode_ == CommonRecoItemMultiAttrPackEnricher::CONCAT_ROW_MAJOR) {
    if (value_type == AttrType::INT) {
      PACK_ITEM_VALUE_ROW_MAJOR(int64, GetIntAttr, int_default_value_, SetIntListCommonAttr);
    } else if (value_type == AttrType::FLOAT) {
      PACK_ITEM_VALUE_ROW_MAJOR(double, GetDoubleAttr, float_default_value_, SetDoubleListCommonAttr);
    } else if (value_type == AttrType::STRING) {
      PACK_ITEM_VALUE_ROW_MAJOR(std::string, GetStringAttr, string_default_value_, SetStringListCommonAttr);
    }
  } else if (pack_mode_ == CommonRecoItemMultiAttrPackEnricher::CONCAT_COL_MAJOR) {
    if (value_type == AttrType::INT) {
      PACK_ITEM_VALUE_COL_MAJOR(int64, GetIntAttr, int_default_value_, SetIntListCommonAttr);
    } else if (value_type == AttrType::FLOAT) {
      PACK_ITEM_VALUE_COL_MAJOR(double, GetDoubleAttr, float_default_value_, SetDoubleListCommonAttr);
    } else if (value_type == AttrType::STRING) {
      PACK_ITEM_VALUE_COL_MAJOR(std::string, GetStringAttr, string_default_value_, SetStringListCommonAttr);
    }
  } else {
    CL_LOG(ERROR) << "unknow pack mode: " << static_cast<int>(pack_mode_);
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemMultiAttrPackEnricher, CommonRecoItemMultiAttrPackEnricher)

}  // namespace platform
}  // namespace ks
