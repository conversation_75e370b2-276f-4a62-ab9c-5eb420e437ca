#pragma once

#include <memory>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/service_black_list_util.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/kv_client_wrapper/redis_cache_client.h"

namespace ks {
namespace platform {

class CommonRecoRedisItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRedisItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const {
    return is_async_;
  }

 private:
  bool InitProcessor() override {
    cluster_name_ = config()->GetString("cluster_name", "");
    timeout_ms_ = config()->GetInt("timeout_ms", 10);

    std::string cache_name = config()->GetString("cache_name", cluster_name_);
    int cache_bits = config()->GetInt("cache_bits", 0);
    int cache_delay_delete_ms = config()->GetInt("cache_delay_delete_ms", 10 * 1000);
    int cache_expire_second = config()->GetInt("cache_expire_second", 60 * 60);
    is_async_ = config()->GetBoolean("is_async", false);

    redis_client_ = std::make_unique<base::RedisCacheClient>();
    if (!redis_client_->Initialize(cluster_name_, timeout_ms_, cache_name, cache_bits, cache_delay_delete_ms,
                                   cache_expire_second)) {
      LOG(ERROR) << "CommonRecoRedisItemAttrEnricher init failed! redis client init failed, cluster_name: "
                 << cluster_name_ << ", cache_name: " << cache_name << ", cache_bits: " << cache_bits
                 << ", cache_delay_delete_ms: " << cache_delay_delete_ms
                 << ", cache_expire_second: " << cache_expire_second;
      return false;
    }

    redis_key_ = config()->GetString("redis_key_from", "");
    if (redis_key_.empty()) {
      LOG(ERROR) << "CommonRecoRedisItemAttrEnricher init failed! redis_key_from is empty";
      return false;
    }
    redis_value_ = config()->GetString("save_value_to", "");
    if (redis_value_.empty()) {
      LOG(ERROR) << "CommonRecoRedisItemAttrEnricher init failed! save_value_to is empty";
      return false;
    }
    key_prefix = config()->Get("key_prefix");
    return true;
  }

  int CheckAndGetTimeoutMs(MutableRecoContextInterface *context) {
    const std::string &kess_blacklist_key = GetStringProcessorParameter(context, "kess_blacklist_key", "");
    int timeout_ms = KessBlackListUtil::GetInstance()->GetKessBlackListTimeoutMs(
      context, kess_blacklist_key, cluster_name_, config()->GetInt("timeout_ms", 10), GetName());
    return timeout_ms;
  }

  void GetItemAttrFromRedisResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end, ItemAttr *redis_key_accessor);
  void AsyncGetItemAttrFromRedisResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end, ItemAttr *redis_key_accessor);

 private:
  std::string cluster_name_;
  std::string redis_key_;
  std::string redis_value_;
  const base::Json *key_prefix = nullptr;
  int timeout_ms_;
  bool is_async_ = false;
  std::unique_ptr<base::RedisCacheClient> redis_client_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRedisItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
