#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/service_black_list_util.h"
#include "serving_base/kv_client_wrapper/redis_cache_client.h"

DECLARE_bool(lazy_init_redis_client);

namespace ks {
namespace platform {

class CommonRecoRedisCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRedisCommonAttrEnricher() {
    redis_value_type_map_.insert(std::make_pair("string", RedisValueType::STRING));
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsAsync() const {
    return is_async_;
  }

  enum class RedisValueType : int { UNKNOWN = 0, STRING };

  struct RedisParam {
    const base::Json *key = nullptr;
    RedisValueType redis_value_type = RedisValueType::UNKNOWN;
    const base::Json *key_prefix = nullptr;
    std::string output_attr_name;
    AttrType output_attr_type = AttrType::UNKNOWN;
  };

 private:
  bool InitProcessor() override {
    cluster_name_ = config()->GetString("cluster_name", "");
    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    cache_name_ = config()->GetString("cache_name", cluster_name_);
    cache_bits_ = config()->GetInt("cache_bits", 0);
    cache_delay_delete_ms_ = config()->GetInt("cache_delay_delete_ms", 10 * 1000);
    cache_expire_second_ = config()->GetInt("cache_expire_second", 60 * 60);
    save_err_code_to_ = config()->GetString("save_err_code_to", "");
    is_async_ = config()->GetBoolean("is_async", false);

    if (!FLAGS_lazy_init_redis_client) {
      redis_client_ = GetRedisClient();
      if (!redis_client_) {
        LOG(ERROR)
            << "CommonRecoRedisCommonAttrEnricher init failed! redis client init failed, cluster_name: "
            << cluster_name_ << ", cache_name: " << cache_name_ << ", cache_bits: " << cache_bits_
            << ", cache_delay_delete_ms: " << cache_delay_delete_ms_
            << ", cache_expire_second: " << cache_expire_second_;
        return false;
      }
    }

    auto *redis_params = config()->Get("redis_params");
    if (!redis_params || !redis_params->IsArray()) {
      LOG(ERROR) << "CommonRecoRedisCommonAttrEnricher init failed! invalid redis_params config";
      return false;
    }

    skip_empty_ = config()->GetBoolean("skip_empty", false);
    skip_parse_fail_ = config()->GetBoolean("skip_parse_fail", false);

    for (const auto *param : redis_params->array()) {
      RedisParam redis_param;
      redis_param.key = param->Get("redis_key");
      redis_param.redis_value_type = ParseRedisValueType(param->GetString("redis_value_type", "string"));
      redis_param.key_prefix = param->Get("key_prefix");
      redis_param.output_attr_name = param->GetString("output_attr_name", "");
      redis_param.output_attr_type = RecoUtil::ParseAttrType(param->GetString("output_attr_type", "string"));
      if (!redis_param.key || redis_param.output_attr_name.empty()) {
        LOG(ERROR) << "CommonRecoRedisCommonAttrEnricher init failed! invalid key or output_attr_name";
        return false;
      }
      if (redis_param.output_attr_type == AttrType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoRedisCommonAttrEnricher init failed! invalid output_attr_type ";
        return false;
      }
      switch (redis_param.redis_value_type) {
        case RedisValueType::STRING:
          string_params_.push_back(std::move(redis_param));
          break;
        default:
          LOG(ERROR) << "CommonRecoRedisCommonAttrEnricher init failed! invalid redis_value_type ";
          return false;
      }
    }
    return true;
  }

  std::unique_ptr<base::RedisCacheClient> GetRedisClient() {
    auto redis_client = std::make_unique<base::RedisCacheClient>();
    if (redis_client->Initialize(cluster_name_, timeout_ms_, cache_name_, cache_bits_, cache_delay_delete_ms_,
                                 cache_expire_second_)) {
      return std::move(redis_client);
    }
    return nullptr;
  }

  int CheckAndGetTimeoutMs(
    MutableRecoContextInterface *context) {
    const std::string &kess_blacklist_key = GetStringProcessorParameter(context, "kess_blacklist_key", "");
    int timeout_ms = KessBlackListUtil::GetInstance()->GetKessBlackListTimeoutMs(
      context, kess_blacklist_key, cluster_name_, config()->GetInt("timeout_ms", 10), GetName());
    return timeout_ms;
  }

  void PrepareKeys(MutableRecoContextInterface *context, std::vector<std::string> *string_keys,
                   std::vector<int> *key_sizes);

  void ParseValues(MutableRecoContextInterface *context, const std::vector<std::string> &string_keys,
                   const std::vector<int> &key_sizes, const std::vector<std::string> &string_values);

  void GetCommonAttrFromRedisResponse(MutableRecoContextInterface *context);
  void AsyncGetCommonAttrFromRedisResponse(MutableRecoContextInterface *context);

  RedisValueType ParseRedisValueType(const std::string &type);

 private:
  std::string cluster_name_;
  std::string cache_name_;
  int cache_bits_ = 0;
  int cache_delay_delete_ms_ = 10 * 1000;
  int cache_expire_second_ = 60 * 60;

  std::vector<RedisParam> string_params_;
  int timeout_ms_;
  std::unique_ptr<base::RedisCacheClient> redis_client_;
  std::unordered_map<std::string, RedisValueType> redis_value_type_map_;
  bool skip_empty_ = false;
  bool skip_parse_fail_ = false;
  bool is_async_ = false;
  std::string save_err_code_to_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRedisCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
