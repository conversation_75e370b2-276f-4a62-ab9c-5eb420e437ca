#include "dragon/src/processor/common/enricher/common_reco_sample_list_common_attr_enricher.h"

#include <string>
#include <utility>
#include <vector>
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "third_party/abseil/absl/strings/str_format.h"
#include "third_party/abseil/absl/strings/str_join.h"

namespace ks {
namespace platform {

void CommonRecoSampleListCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                    RecoResultConstIter begin, RecoResultConstIter end) {
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    CL_LOG_WARNING_EVERY("sample_list", "empty_kess_service", 1000)
        << "SampleList request cancelled: empty_kess_service" << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  ks::action::SampleListKessClient *sample_list_client = GetSampleListKessClient(kess_service);
  if (!sample_list_client) {
    CL_LOG_ERROR_EVERY("sample_list", "null_sample_list_client", 1000)
        << "SampleList request cancelled: null sample_list_client_"
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  sample_list_request_.Clear();
  if (!attr_config_.empty()) {
    sample_list_request_.set_attr_config(attr_config_);
  }

  int64 uid = GetIntProcessorParameter(context, "user_id", context->GetUserId());
  std::string did = GetStringProcessorParameter(context, "device_id", context->GetDeviceId());
  sample_list_request_.set_user_id(uid);
  sample_list_request_.set_device_id(did);

  std::string request_info = absl::StrFormat("kess_service: %s, timeout_ms: %d", kess_service, timeout_ms_);
  CL_LOG(INFO) << "sending request to sample_list service, " << request_info
               << RecoUtil::GetRequestInfoForLog(context);
  auto pr = sample_list_client->AsyncGetUserAttrs(sample_list_request_, nullptr);
  if (!pr.first) {
    CL_LOG_ERROR_EVERY("sample_list", "send_request_fail", 100)
        << "failed to send request to sample_list service, " << request_info
        << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  auto callback = [this, context](kuiba::PredictItem *response) {
    int64 start_ms = base::GetTimestamp();
    std::vector<std::string> attr_names;
    attr_names.reserve(include_attrs_.empty() ? response->attr_size() : include_attrs_.size());
    for (const auto &attr : response->attr()) {
      if (!include_attrs_.empty() && include_attrs_.find(attr.name()) == include_attrs_.end()) {
        continue;
      }
      interop::SaveSampleAttrToCommonAttr(context, attr, no_overwrite_);
      attr_names.push_back(attr.name());
    }
    int attr_num = attr_names.size();
    context->SetStringListCommonAttr(save_attr_names_to_attr_, std::move(attr_names));

    int64 duration = base::GetTimestamp() - start_ms;
    if (VLOG_IS_ON(100)) {
      std::string names;
      if (auto name_list = context->GetStringListCommonAttr(save_attr_names_to_attr_)) {
        names = absl::StrJoin(*name_list, ",");
      }
      CL_LOG(INFO) << "sample_list response received, callback executed in " << duration / 1000.0
                   << " ms, user attrs: [" << names << "]";
    } else {
      CL_LOG(INFO) << "sample_list response received, callback executed in " << duration / 1000.0
                   << " ms, user attr num: " << attr_num;
    }
  };
  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), request_info);
}

ks::action::SampleListKessClient *CommonRecoSampleListCommonAttrEnricher::GetSampleListKessClient(
    const std::string &kess_service) {
  auto it = sample_list_kess_client_map_.find(kess_service);
  if (it != sample_list_kess_client_map_.end() && it->second) {
    return it->second.get();
  }
  auto *client_ptr = new ks::action::SampleListKessClient(kess_service, service_group_);
  std::unique_ptr<ks::action::SampleListKessClient> client(client_ptr);
  client->SetTimeoutMs(timeout_ms_);
  sample_list_kess_client_map_[kess_service] = std::move(client);
  return client_ptr;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoSampleListCommonAttrEnricher,
                 CommonRecoSampleListCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
