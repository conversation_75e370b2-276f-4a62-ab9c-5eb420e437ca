#pragma once

#include <lua-5.4.4/src/lua.hpp>

#include <string>
#include <vector>

#include "base/random/pseudo_random.h"
#include "dragon/src/module/lua_c_functions.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "third_party/abseil/absl/strings/str_split.h"
#include "third_party/abseil/absl/strings/string_view.h"

namespace ks {
namespace platform {

class CommonRecoLuaAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoLuaAttrEnricher() : random_(base::GetTimestamp()) {}
  ~CommonRecoLuaAttrEnricher() {
    if (lua_state_) lua_close(lua_state_);
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  bool IsBranchController() const final {
    return for_branch_control_;
  }

 private:
  bool InitProcessor() override {
    auto *common_attr_config = config()->Get("import_common_attr");
    if (common_attr_config) {
      if (!common_attr_config->IsArray()) {
        LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! 'import_common_attr' should be an array!";
        return false;
      }
      for (const auto *attr : common_attr_config->array()) {
        import_common_attrs_.push_back(attr->StringValue());
      }
    }

    auto *item_attr_config = config()->Get("import_item_attr");
    if (item_attr_config) {
      if (!item_attr_config->IsArray()) {
        LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! 'import_item_attr' should be an array!";
        return false;
      }
      for (const auto *attr : item_attr_config->array()) {
        import_item_attrs_.push_back(attr->StringValue());
      }
    }

    auto *export_common_config = config()->Get("export_common_attr");
    if (export_common_config) {
      if (!export_common_config->IsArray()) {
        LOG(ERROR)
            << "CommonRecoLuaAttrEnricher init failed! Missing config 'export_common_attr' or not an array!";
        return false;
      }

      for (const auto *attr : export_common_config->array()) {
        export_common_attrs_.push_back(attr->StringValue());
      }
    }

    auto *export_item_config = config()->Get("export_item_attr");
    if (export_item_config) {
      if (!export_item_config->IsArray()) {
        LOG(ERROR)
            << "CommonRecoLuaAttrEnricher init failed! Missing config 'export_item_attr' or not an array!";
        return false;
      }

      for (const auto *attr : export_item_config->array()) {
        export_item_attrs_.push_back(attr->StringValue());
      }
    }

    clear_attr_if_nil_ = config()->GetBoolean("clear_attr_if_nil", false);
    for_branch_control_ = config()->GetBoolean("for_branch_control", false);
    if (for_branch_control_) {
      branch_control_func_name_ = config()->GetString("function_for_common");
      if (branch_control_func_name_.empty()) {
        LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! Missing config 'function_for_common'!";
        return false;
      }
    }

    absl::string_view lua_script;
    const auto *lua_script_cfg = config()->Get("lua_script");
    if (lua_script_cfg && json_is_string(lua_script_cfg->get())) {
      lua_script = json_string_value(lua_script_cfg->get());
    }

    if (lua_script.empty()) {
      LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! 'lua_script' cannot be empty!";
      return false;
    }

    if (for_branch_control_) {
      branch_code_info_ = config()->GetString("$code_info");
      branch_to_be_delete_ = config()->GetString("to_be_delete");
    }

    lua_state_ = luaL_newstate();
    if (!lua_state_) {
      LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! Failed to new lua_state!";
      return false;
    }

    luaL_openlibs(lua_state_);

    // TODO(fangjianbing): 后续改为 c lib 方式引入
    lua::RegisterCFunctions(lua_state_);

    if (luaL_loadstring(lua_state_, lua_script.data())) {
      LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! 'lua_script' load error: "
                 << lua_tostring(lua_state_, -1);
      return false;
    }

    if (lua_pcall(lua_state_, 0, 0, 0)) {
      LOG(ERROR) << "CommonRecoLuaAttrEnricher init failed! 'lua_script' execute error: "
                 << lua_tostring(lua_state_, -1);
      return false;
    }

    lua_script_lines_ = absl::StrSplit(lua_script, '\n');

    return true;
  }

  void ClearLuaStack() {
    lua_settop(lua_state_, 0);
  }

  void CheckLuaStack(int require_num) {
    CHECK(lua_checkstack(lua_state_, require_num))
        << "cannot allocate extra " << require_num << " slots in lua stack!";
  }

  bool HasLuaFunction(ReadableRecoContextInterface *context, const std::string &function);

  void HandleCommonLevel(MutableRecoContextInterface *context);

  void HandleItemLevel(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);

  void PushVectorToLuaTable(absl::Span<const int64> vec) {
    lua_createtable(lua_state_, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushinteger(lua_state_, vec[i]);
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state_, -2, i + 1);
    }
  }

  void PushVectorToLuaTable(absl::Span<const double> vec) {
    lua_createtable(lua_state_, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushnumber(lua_state_, vec[i]);
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state_, -2, i + 1);
    }
  }

  void PushVectorToLuaTable(const std::vector<absl::string_view> &vec) {
    lua_createtable(lua_state_, vec.size(), 0);
    for (int i = 0; i < vec.size(); ++i) {
      lua_pushlstring(lua_state_, vec[i].data(), vec[i].size());
      // lua 数组的下标习惯以 1 开始
      lua_rawseti(lua_state_, -2, i + 1);
    }
  }

  void ImportCommonAttrs(ReadableRecoContextInterface *context);

  void ImportItemAttrs(const CommonRecoResult &result);

  void ReadLuaTableToCommonAttr(CommonAttr *attr);

  void ReadLuaTableToItemAttr(ItemAttr *attr_accessor, const CommonRecoResult &result);

  absl::string_view GetErrorLine(absl::string_view err_msg);

 private:
  lua_State *lua_state_ = nullptr;
  std::vector<absl::string_view> lua_script_lines_;
  std::vector<std::string> import_common_attrs_;
  std::vector<std::string> import_item_attrs_;
  std::vector<ItemAttr *> import_attr_accessors_;
  std::vector<std::string> export_common_attrs_;
  std::vector<std::string> export_item_attrs_;
  std::vector<ItemAttr *> export_attr_accessors_;
  std::vector<CommonAttr *> import_common_attr_accessors_;
  std::vector<CommonAttr *> export_common_attr_accessors_;
  bool clear_attr_if_nil_ = false;
  bool for_branch_control_ = false;
  std::string branch_code_info_;
  std::string branch_to_be_delete_;
  std::string branch_control_func_name_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLuaAttrEnricher);
};

}  // namespace platform
}  // namespace ks
