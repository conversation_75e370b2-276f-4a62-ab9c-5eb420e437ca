#include "dragon/src/processor/common/enricher/common_reco_py_attr_enricher.h"

#include <exception>
#include "base/time/timestamp.h"

namespace ks {
namespace platform {

void CommonRecoPyAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  if (import_common_attr_accessor_map_.empty()) {
    for (const auto &pair : import_common_attrs_) {
      import_common_attr_accessor_map_.emplace(pair.second, context->GetCommonAttrAccessor(pair.first));
    }
    matx_dragon_context_->import_common_attr_accessor_map_ = import_common_attr_accessor_map_;
  }

  if (export_common_attr_accessor_map_.empty()) {
    for (const auto &pair : export_common_attrs_) {
      export_common_attr_accessor_map_.emplace(pair.first, context->GetCommonAttrAccessor(pair.second));
    }
    matx_dragon_context_->export_common_attr_accessor_map_ = export_common_attr_accessor_map_;
  }

  if (import_item_attr_accessor_map_.empty()) {
    for (const auto &pair : import_item_attrs_) {
      import_item_attr_accessor_map_.emplace(pair.second, context->GetItemAttrAccessor(pair.first));
    }
    matx_dragon_context_->import_item_attr_accessor_map_ = import_item_attr_accessor_map_;
  }

  if (export_item_attr_accessor_map_.empty()) {
    for (const auto &pair : export_item_attrs_) {
      export_item_attr_accessor_map_.emplace(pair.first, context->GetItemAttrAccessor(pair.second));
    }
    matx_dragon_context_->export_item_attr_accessor_map_ = export_item_attr_accessor_map_;
  }

  matx_dragon_context_->begin_ = begin;
  matx_dragon_context_->result_size_ = std::distance(begin, end);

  try {
    int start_ts = base::GetTimestamp();
    if (func_ptr_(inputs_, param_nums_, &output_, NULL) != 0) {
      CL_LOG_ERROR("py_udf", GetName() + " py_error: " + std::string(MATXScriptAPIGetLastError()))
          << GetName() << " function run error: " << std::string(MATXScriptAPIGetLastError());
    } else {
      int run_time = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(run_time, kPerfNs, "py_udf.run_time", GlobalHolder::GetServiceIdentifier(),
                       context->GetRequestType(), GetName(), function_set_, py_function_);
    }
  } catch (const std::exception &e) {
    std::string perf_msg = e.what();
    ExtractPerfMessage(&perf_msg);
    CL_LOG_ERROR("py_udf", GetName() + " py_exception: " + perf_msg)
        << GetName() << " function run exception: " << e.what();
  }
}

void CommonRecoPyAttrEnricher::ExtractPerfMessage(std::string *msg) {
  // 异常的格式为 [xx:xx:xx] file:line: perf_msg
  // 跳过日期，格式形如 [11:58:40]，后带有空格，因此长度为 11
  // perf 的不带 stack trace
  if (likely(msg->length() > 11 && (*msg)[0] == '[' && (*msg)[9] == ']')) {
    int colon_position = msg->find(":", 11);
    if (likely(colon_position != std::string::npos)) {
      int stack_position = msg->find("Stack trace:", 11);
      if (likely(stack_position != std::string::npos)) {
        *msg = msg->substr(11, stack_position - 12);
      } else {
        *msg = msg->substr(11);
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPyAttrEnricher, CommonRecoPyAttrEnricher)

}  // namespace platform
}  // namespace ks
