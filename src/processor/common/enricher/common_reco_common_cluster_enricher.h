#pragma once

#include <string>

#include "ks/common_reco/common_cluster/client/common_cluster_client.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoCommonClusterEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCommonClusterEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    service_group_ = config()->GetString("service_group", "PRODUCTION");
    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoCommonClusterEnricher init failed! 'timeout_ms' must be > 0";
      return false;
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoCommonClusterEnricher init failed! 'cluster_attr' cannot be emtpy!";
      return false;
    }

    add_item_embeddings_from_attr_name_ = config()->GetString("add_item_embeddings_from_attr");

    return true;
  }

  void PushVectorToRequestEmbedding(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                    ::ks::platform::ItemEmbedding* item_embedding_builder);

 private:
  std::string service_group_;
  int timeout_ms_ = 200;
  std::string output_attr_;
  std::string add_item_embeddings_from_attr_name_;

  CommonClusterClient common_cluster_client_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCommonClusterEnricher);
};

}  // namespace platform
}  // namespace ks
