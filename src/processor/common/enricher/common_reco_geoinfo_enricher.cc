#include "dragon/src/processor/common/enricher/common_reco_geoinfo_enricher.h"

#include "base/time/timestamp.h"
#include "infra/utility/src/utility/net_utils.h"

namespace ks {
namespace platform {

std::once_flag CommonRecoGeoInfoEnricher::warmup_geoinfo_once_flag_;

void CommonRecoGeoInfoEnricher::<PERSON><PERSON>(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  return is_common_attr_ ? HandleCommonLevel(context) : HandleItemLevel(context, begin, end);
}

void CommonRecoGeoInfoEnricher::HandleCommonLevel(MutableRecoContextInterface *context) {
  double lat = 0.0;
  if (auto lat_attr = context->GetDoubleCommonAttr(lat_attr_)) {
    lat = *lat_attr;
    if (!IsValideLatitude(lat)) {
      CL_LOG_ERROR("geoinfo", "geoinfo_lat_error") << "invalid lat: " << lat;
      return;
    }
  } else {
    CL_LOG(ERROR) << "missing lat_attr: " << lat_attr_;
    return;
  }

  double lon = 0.0;
  if (auto lon_attr = context->GetDoubleCommonAttr(lon_attr_)) {
    lon = *lon_attr;
    if (!IsValideLongitude(lon)) {
      CL_LOG_ERROR("geoinfo", "geoinfo_lon_error") << "invalid lon: " << lon;
      return;
    }
  } else {
    CL_LOG(ERROR) << "missing lon_attr: " << lon_attr_;
    return;
  }

  ks::infra::geoinfo::GeoInfoResult geoinfo;
  bool ok = ks::infra::geoinfo::GeoInfo::Search(lat, lon, &geoinfo);
  if (!ok) {
    CL_LOG_ERROR("geoinfo", "geoinfo_search_error") << "lat: " << lat << " lon: " << lon;
    return;
  }

  if (VLOG_IS_ON(10)) {
    LOG(INFO) << "lat: " << lat << " lon: " << lon << " -> Geo info nation: [" << geoinfo.nation
              << "] province: [" << geoinfo.province << "] city: [" << geoinfo.city << "] county: ["
              << geoinfo.county << "] bdname: [" << geoinfo.bdname << "] adcode: [" << geoinfo.adcode
              << "] bdcode: [" << geoinfo.bdcode << "]";
  }

  if (!save_bdcode_to_attr_.empty()) {
    context->SetIntCommonAttr(save_bdcode_to_attr_, geoinfo.bdcode);
  }
  if (!save_adcode_to_attr_.empty()) {
    context->SetIntCommonAttr(save_adcode_to_attr_, geoinfo.adcode);
  }
  if (!save_nation_to_attr_.empty()) {
    context->SetStringCommonAttr(save_nation_to_attr_, geoinfo.nation);
  }
  if (!save_province_to_attr_.empty()) {
    context->SetStringCommonAttr(save_province_to_attr_, geoinfo.province);
  }
  if (!save_city_to_attr_.empty()) {
    context->SetStringCommonAttr(save_city_to_attr_, geoinfo.city);
  }
  if (!save_county_to_attr_.empty()) {
    context->SetStringCommonAttr(save_county_to_attr_, geoinfo.county);
  }
  if (!save_bdname_to_attr_.empty()) {
    context->SetStringCommonAttr(save_bdname_to_attr_, geoinfo.bdname);
  }
  return;
}

void CommonRecoGeoInfoEnricher::HandleItemLevel(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  auto lat_attr_accessor = context->GetItemAttrAccessor(lat_attr_);
  auto lon_attr_accessor = context->GetItemAttrAccessor(lon_attr_);

  ItemAttr *bdcode_accessor =
      (!save_bdcode_to_attr_.empty()) ? context->GetItemAttrAccessor(save_bdcode_to_attr_) : nullptr;
  ItemAttr *adcode_accessor =
      (!save_adcode_to_attr_.empty()) ? context->GetItemAttrAccessor(save_adcode_to_attr_) : nullptr;
  ItemAttr *nation_accessor =
      (!save_nation_to_attr_.empty()) ? context->GetItemAttrAccessor(save_nation_to_attr_) : nullptr;
  ItemAttr *province_accessor =
      (!save_province_to_attr_.empty()) ? context->GetItemAttrAccessor(save_province_to_attr_) : nullptr;
  ItemAttr *city_accessor =
      (!save_city_to_attr_.empty()) ? context->GetItemAttrAccessor(save_city_to_attr_) : nullptr;
  ItemAttr *county_accessor =
      (!save_county_to_attr_.empty()) ? context->GetItemAttrAccessor(save_county_to_attr_) : nullptr;
  ItemAttr *bdname_accessor =
      (!save_bdname_to_attr_.empty()) ? context->GetItemAttrAccessor(save_bdname_to_attr_) : nullptr;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double lat = 0.0;
    if (auto lat_attr = context->GetDoubleItemAttr(result, lat_attr_accessor)) {
      lat = *lat_attr;
      if (!IsValideLatitude(lat)) {
        CL_LOG_ERROR("geoinfo", "geoinfo_lat_error") << "invalid lat: " << lat;
        return;
      }
    } else {
      CL_LOG(ERROR) << "missing lat_attr: " << lat_attr_;
      return;
    }

    double lon = 0.0;
    if (auto lon_attr = context->GetDoubleItemAttr(result, lon_attr_accessor)) {
      lon = *lon_attr;
      if (!IsValideLongitude(lon)) {
        CL_LOG_ERROR("geoinfo", "geoinfo_lon_error") << "invalid lon: " << lon;
        return;
      }
    } else {
      CL_LOG(ERROR) << "missing lon_attr: " << lon_attr_;
      return;
    }

    ks::infra::geoinfo::GeoInfoResult geoinfo;
    bool ok = ks::infra::geoinfo::GeoInfo::Search(lat, lon, &geoinfo);
    if (!ok) {
      CL_LOG_ERROR("geoinfo", "geoinfo_search_error") << "lat: " << lat << " lon: " << lon;
      return;
    }

    if (VLOG_IS_ON(10)) {
      LOG(INFO) << "lat: " << lat << " lon: " << lon << " -> Geo info nation: [" << geoinfo.nation
                << "] province: [" << geoinfo.province << "] city: [" << geoinfo.city << "] county: ["
                << geoinfo.county << "] bdname: [" << geoinfo.bdname << "] adcode: [" << geoinfo.adcode
                << "] bdcode: [" << geoinfo.bdcode << "]";
    }

    if (bdcode_accessor) {
      context->SetIntItemAttr(result, bdcode_accessor, geoinfo.bdcode);
    }
    if (adcode_accessor) {
      context->SetIntItemAttr(result, adcode_accessor, geoinfo.adcode);
    }
    if (nation_accessor) {
      context->SetStringItemAttr(result, nation_accessor, geoinfo.nation);
    }
    if (province_accessor) {
      context->SetStringItemAttr(result, province_accessor, geoinfo.province);
    }
    if (city_accessor) {
      context->SetStringItemAttr(result, city_accessor, geoinfo.city);
    }
    if (county_accessor) {
      context->SetStringItemAttr(result, county_accessor, geoinfo.county);
    }
    if (bdname_accessor) {
      context->SetStringItemAttr(result, bdname_accessor, geoinfo.bdname);
    }
  });
}

bool CommonRecoGeoInfoEnricher::IsValideLatitude(double lat) const {
  return base::IsGreaterEqual(lat, -90.0) && base::IsLessEqual(lat, 90.0);
}

bool CommonRecoGeoInfoEnricher::IsValideLongitude(double lon) const {
  return base::IsGreaterEqual(lon, -180.0) && base::IsLessEqual(lon, 180.0);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoGeoInfoEnricher, CommonRecoGeoInfoEnricher)

}  // namespace platform
}  // namespace ks
