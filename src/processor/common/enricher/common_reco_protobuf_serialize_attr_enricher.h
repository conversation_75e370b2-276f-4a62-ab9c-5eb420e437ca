#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <utility>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoProtobufSerializeAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufSerializeAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrMapping {
    std::string proto_path;
    std::string attr_name;
  };

  bool InitProcessor() override {
    from_common_attr_ = config()->GetString("from_common_attr");
    serialize_to_common_attr_ = config()->GetString("serialize_to_common_attr");
    from_item_attr_ = config()->GetString("from_item_attr");
    serialize_to_item_attr_ = config()->GetString("serialize_to_item_attr");
    if (!(from_common_attr_.empty() && serialize_to_common_attr_.empty() &&
          !from_item_attr_.empty() && !serialize_to_item_attr_.empty()) &&
        !(!from_common_attr_.empty() && !serialize_to_common_attr_.empty() &&
          from_item_attr_.empty() && serialize_to_item_attr_.empty())) {
      LOG(ERROR) << "CommonRecoProtobufSerializeAttrEnricher init failed!"
                 << ", from_common_attr = " << from_common_attr_
                 << ", serialize_to_common_attr = " << serialize_to_common_attr_
                 << ", from_item_attr = " << from_item_attr_
                 << ", serialize_to_item_attr = " << serialize_to_item_attr_;
      return false;
    }
    return true;
  }

 private:
  std::string from_common_attr_;
  std::string serialize_to_common_attr_;
  std::string from_item_attr_;
  std::string serialize_to_item_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufSerializeAttrEnricher);
};

}  // namespace platform
}  // namespace ks
