#pragma once

#include <grpcpp/impl/codegen/rpc_method.h>
#include <memory>
#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/pbutil.h"
#include "ks/serving_util/kess_grpc_client.h"

#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

class CommonRecoGenericGrpcEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoGenericGrpcEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 protected:
  // user 侧属性填充不允许分块处理
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  bool InitProcessor() override {
    kess_service_ = config()->Get("kess_service");
    if (!kess_service_) {
      LOG(ERROR) << "CommonRecoGenericGrpcEnricher init failed: kess_service can not be empty.";
      return false;
    }

    service_group_ = config()->GetString("service_group", "PRODUCTION");

    method_name_ = config()->GetString("method_name");
    if (method_name_.empty()) {
      LOG(ERROR) << "CommonRecoGenericGrpcEnricher init failed! \"method_name\" cannot be empty!";
      return false;
    }
    ::grpc::internal::RpcMethod(method_name_.c_str(), ::grpc::internal::RpcMethod::NORMAL_RPC);

    request_attr_ = config()->GetString("request_attr");
    if (request_attr_.empty()) {
      LOG(ERROR) << "CommonRecoGenericGrpcEnricher init failed! \"request_attr\" cannot be empty!";
      return false;
    }

    response_attr_ = config()->GetString("response_attr");
    if (response_attr_.empty()) {
      LOG(ERROR) << "CommonRecoGenericGrpcEnricher init failed! \"response_attr\" cannot be empty!";
      return false;
    }
    response_class_ = config()->GetString("response_class");
    if (response_class_.empty()) {
      LOG(ERROR) << "CommonRecoGenericGrpcEnricher init failed! \"response_class\" cannot be empty!";
      return false;
    }
    use_dynamic_proto_ = config()->GetBoolean("use_dynamic_proto", false);
    if (use_dynamic_proto_) {
      response_msg_.reset(pbutil::NewMessageByName(response_class_, GlobalHolder::GetDynamicPool(),
                                                   GlobalHolder::GetDynamicMessageFactory()));
    } else {
      response_msg_.reset(pbutil::NewMessageByName(response_class_));
    }

    options_.SetTimeout(std::chrono::milliseconds(200));
    return true;
  }

 private:
  const base::Json *kess_service_;
  std::string service_group_;
  std::string method_name_;
  std::string response_class_;
  bool use_dynamic_proto_ = false;
  std::shared_ptr<::google::protobuf::Message> response_msg_;

  std::string request_attr_;
  std::string response_attr_;

  ks::kess::rpc::grpc::Options options_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoGenericGrpcEnricher);
};

}  // namespace platform
}  // namespace ks
