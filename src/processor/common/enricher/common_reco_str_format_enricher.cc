#include "dragon/src/processor/common/enricher/common_reco_str_format_enricher.h"

#include <regex>
#include <unordered_map>
#include <utility>

#include "third_party/abseil/absl/strings/str_format.h"

namespace ks {
namespace platform {

void CommonRecoStrFormatEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  if (is_common_attr_) {
    FormatCommonAttr(context);
  } else {
    FormatItemAttr(context, begin, end);
  }
}

void CommonRecoStrFormatEnricher::FormatCommonAttr(MutableRecoContextInterface *context) {
  int64 get_int_fail = 0;
  int64 get_double_fail = 0;
  int64 get_string_fail = 0;
  int64 get_attr_fail = 0;

  std::vector<absl::FormatArg> args;
  args.reserve(input_attrs_.size());
  // NOTE(fangjianbing): args 持有变量的生命周期必须超过 absl::FormatUntyped() 的调用
  std::vector<int64> int_vals;
  std::vector<double> double_vals;
  std::vector<absl::string_view> str_vals;
  int_vals.reserve(input_attrs_.size());
  double_vals.reserve(input_attrs_.size());
  str_vals.reserve(input_attrs_.size());

  for (const auto &attr_name : input_attrs_) {
    auto *attr = context->GetCommonAttrAccessor(attr_name);
    switch (attr->value_type) {
      case AttrType::INT:
        if (auto val = attr->GetIntValue()) {
          int_vals.push_back(*val);
          args.emplace_back(int_vals.back());
        } else {
          ++get_int_fail;
          if (fill_default_val_) {
            int_vals.push_back(0);
            args.emplace_back(int_vals.back());
          } else {
            args.emplace_back(nullptr);
          }
        }
        break;
      case AttrType::FLOAT:
        if (auto val = attr->GetDoubleValue()) {
          double_vals.push_back(*val);
          args.emplace_back(double_vals.back());
        } else {
          ++get_double_fail;
          if (fill_default_val_) {
            double_vals.push_back(0.0);
            args.emplace_back(double_vals.back());
          } else {
            args.emplace_back(nullptr);
          }
        }
        break;
      case AttrType::STRING:
        if (auto val = attr->GetStringValue()) {
          str_vals.push_back(*val);
          args.emplace_back(str_vals.back());
        } else {
          ++get_string_fail;
          args.emplace_back(nullptr);
        }
        break;
      case AttrType::INT_LIST: {
        auto val = attr->GetIntListValue();
        if (val && !val->empty()) {
          int_vals.push_back(val->front());
          args.emplace_back(int_vals.back());
        } else {
          ++get_int_fail;
          if (fill_default_val_) {
            int_vals.push_back(0);
            args.emplace_back(int_vals.back());
          } else {
            args.emplace_back(nullptr);
          }
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        auto val = attr->GetDoubleListValue();
        if (val && !val->empty()) {
          double_vals.push_back(val->front());
          args.emplace_back(double_vals.back());
        } else {
          ++get_double_fail;
          if (fill_default_val_) {
            double_vals.push_back(0.0);
            args.emplace_back(double_vals.back());
          } else {
            args.emplace_back(nullptr);
          }
          args.emplace_back(nullptr);
        }
        break;
      }
      case AttrType::STRING_LIST: {
        auto val = attr->GetStringListValue();
        if (val && !val->empty()) {
          str_vals.push_back(val->front());
          args.emplace_back(str_vals.back());
        } else {
          ++get_string_fail;
          args.emplace_back(nullptr);
        }
        break;
      }
      default:
        ++get_attr_fail;
        args.emplace_back(nullptr);
        break;
    }
  }

  std::string output;
  if (absl::FormatUntyped(&output, *format_, args)) {
    context->SetStringCommonAttr(output_attr_, std::move(output));
  } else {
    CL_LOG_ERROR("string_format", "format_fail: " + format_string_)
        << "string format fail: " << format_string_;
  }

  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_int_attr_fail " << get_int_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_double_attr_fail " << get_double_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_string_attr_fail " << get_string_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_attr_fail " << get_attr_fail
                                << " times, format_string: " << format_string_;
}

void CommonRecoStrFormatEnricher::FormatItemAttr(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  std::vector<ItemAttr *> input_accessors;
  input_accessors.reserve(input_attrs_.size());
  for (const auto &input_attr : input_attrs_) {
    input_accessors.push_back(context->GetItemAttrAccessor(input_attr));
  }
  auto *output_accessor = context->GetItemAttrAccessor(output_attr_);

  int64 get_int_fail = 0;
  int64 get_double_fail = 0;
  int64 get_string_fail = 0;
  int64 get_attr_fail = 0;
  int64 format_fail = 0;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    std::vector<absl::FormatArg> args;
    args.reserve(input_attrs_.size());
    // NOTE(fangjianbing): args 持有变量的生命周期必须超过 absl::FormatUntyped() 的调用
    std::vector<int64> int_vals;
    std::vector<double> double_vals;
    std::vector<absl::string_view> str_vals;
    int_vals.reserve(input_attrs_.size());
    double_vals.reserve(input_attrs_.size());
    str_vals.reserve(input_attrs_.size());
    for (auto *attr : input_accessors) {
      switch (attr->value_type) {
        case AttrType::INT:
          if (auto val = result.GetIntAttr(attr)) {
            int_vals.push_back(*val);
            args.emplace_back(int_vals.back());
          } else {
            ++get_int_fail;
            if (fill_default_val_) {
              int_vals.push_back(0);
              args.emplace_back(int_vals.back());
            } else {
              args.emplace_back(nullptr);
            }
          }
          break;
        case AttrType::FLOAT:
          if (auto val = result.GetDoubleAttr(attr)) {
            double_vals.push_back(*val);
            args.emplace_back(double_vals.back());
          } else {
            ++get_double_fail;
            if (fill_default_val_) {
              double_vals.push_back(0.0);
              args.emplace_back(double_vals.back());
            } else {
              args.emplace_back(nullptr);
            }
          }
          break;
        case AttrType::STRING:
          if (auto val = result.GetStringAttr(attr)) {
            str_vals.push_back(*val);
            args.emplace_back(str_vals.back());
          } else {
            ++get_string_fail;
            args.emplace_back(nullptr);
          }
          break;
        case AttrType::INT_LIST: {
          auto val = result.GetIntListAttr(attr);
          if (val && !val->empty()) {
            int_vals.push_back(val->front());
            args.emplace_back(int_vals.back());
          } else {
            ++get_int_fail;
            if (fill_default_val_) {
              int_vals.push_back(0);
              args.emplace_back(int_vals.back());
            } else {
              args.emplace_back(nullptr);
            }
          }
          break;
        }
        case AttrType::FLOAT_LIST: {
          auto val = result.GetDoubleListAttr(attr);
          if (val && !val->empty()) {
            double_vals.push_back(val->front());
            args.emplace_back(double_vals.back());
          } else {
            ++get_double_fail;
            if (fill_default_val_) {
              double_vals.push_back(0.0);
              args.emplace_back(double_vals.back());
            } else {
              args.emplace_back(nullptr);
            }
          }
          break;
        }
        case AttrType::STRING_LIST: {
          auto val = result.GetStringListAttr(attr);
          if (val && !val->empty()) {
            str_vals.push_back(val->front());
            args.emplace_back(str_vals.back());
          } else {
            ++get_string_fail;
            args.emplace_back(nullptr);
          }
          break;
        }
        default:
          ++get_attr_fail;
          args.emplace_back(nullptr);
          break;
      }
    }

    std::string output;
    if (absl::FormatUntyped(&output, *format_, args)) {
      result.SetStringAttr(output_accessor, std::move(output));
    } else {
      ++format_fail;
    }
  });

  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_int_attr_fail " << get_int_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_double_attr_fail " << get_double_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_string_attr_fail " << get_string_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_EVERY_N(WARNING, 1000) << "string format get_attr_fail " << get_attr_fail
                                << " times, format_string: " << format_string_;
  CL_LOG_ERROR_COUNT(format_fail, "string_format", "format_fail: " + format_string_)
      << "string format fail: " << format_string_;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStrFormatEnricher, CommonRecoStrFormatEnricher)

}  // namespace platform
}  // namespace ks
