#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "third_party/abseil/absl/container/flat_hash_map.h"

namespace ks {
namespace platform {

class CommonRecoLightFunctionEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoLightFunctionEnricher() {}

  void Enrich(
      MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *common_attr_config = config()->Get("import_common_attr");
    if (common_attr_config && !RecoUtil::ParseAttrsConfig(common_attr_config, &import_common_attrs_)) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Failed to extract 'import_common_attr'!";
      return false;
    }

    auto *item_attr_config = config()->Get("import_item_attr");
    if (item_attr_config && !RecoUtil::ParseAttrsConfig(item_attr_config, &import_item_attrs_)) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Failed to extract 'import_item_attr'!";
      return false;
    }

    auto *export_common_config = config()->Get("export_common_attr");
    if (export_common_config
        && !RecoUtil::ParseAttrsConfig(export_common_config, &export_common_attrs_)) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Failed to extract 'export_common_attr'!";
      return false;
    }

    auto *export_item_config = config()->Get("export_item_attr");
    if (export_item_config
        && !RecoUtil::ParseAttrsConfig(export_item_config, &export_item_attrs_)) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Failed to extract 'export_item_attr'!";
      return false;
    }

    function_name_ = config()->GetString("function_name");
    if (function_name_.empty()) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Missing config 'function_name'!";
      return false;
    }

    std::string class_name = config()->GetString("class_name");
    if (class_name.empty()) {
      LOG(ERROR) << "CommonRecoLightFunctionEnricher init failed! Missing config 'class_name'!";
      return false;
    }

    light_function_set_.reset(base::Factory<CommonRecoBaseLightFunctionSet>::NewInstance(class_name));
    if (!light_function_set_) {
      LOG(ERROR) << "Failed to create light function set: " << class_name;
      return false;
    }

    return true;
  }

  std::unordered_map<std::string, std::string> import_common_attrs_;
  std::unordered_map<std::string, std::string> export_common_attrs_;
  std::unordered_map<std::string, std::string> import_item_attrs_;
  std::unordered_map<std::string, std::string> export_item_attrs_;
  std::string function_name_;
  std::unique_ptr<CommonRecoBaseLightFunctionSet> light_function_set_;
  absl::flat_hash_map<std::string, const CommonAttr *> import_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, CommonAttr *> export_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, const ItemAttr *> import_item_attr_accessor_map_;
  absl::flat_hash_map<std::string, ItemAttr *> export_item_attr_accessor_map_;
  CommonRecoLightFunctionContext light_function_context_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLightFunctionEnricher);
};




}  // namespace platform
}  // namespace ks
