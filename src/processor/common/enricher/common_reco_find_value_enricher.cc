#include "dragon/src/processor/common/enricher/common_reco_find_value_enricher.h"

#include <algorithm>
#include <utility>
#include <vector>

#include "abseil/absl/strings/string_view.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

template <typename T>
uint64_t FindIndexesForValue(const std::vector<T> &arr, T target, bool count, std::vector<int64> *indexes) {
  uint64_t cnt = 0;
  if (indexes) indexes->clear();
  for (size_t i = 0; i < arr.size(); ++i) {
    if (arr[i] == target) {
      cnt++;
      if (indexes) indexes->push_back(i);
      if (!count) break;
    }
  }
  return cnt;
}

template <>
uint64_t FindIndexesForValue(const std::vector<double> &arr, double target, bool count,
                             std::vector<int64> *indexes) {
  uint64_t cnt = 0;
  if (indexes) indexes->clear();
  for (size_t i = 0; i < arr.size(); ++i) {
    if (base::IsEqual(target, arr[i])) {
      cnt++;
      if (indexes) indexes->push_back(i);
      if (!count) break;
    }
  }
  return cnt;
}

void CommonRecoFindValueEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  uint64_t cnt = 0;
  std::vector<int64> int_list;
  std::vector<double> double_list;
  std::vector<absl::string_view> string_list;
  std::vector<int64> indexes;

  if (TryGetIntListProcessorParameter(context, input_, &int_list)) {
    int64 int_val = 0;
    if (TryGetIntProcessorParameter(context, value_, &int_val)) {
      cnt = FindIndexesForValue(int_list, int_val, count_, output_index_attr_.empty() ? nullptr : &indexes);
    } else {
      CL_LOG_ERROR("unsupport value type", value_->ToString())
          << "unsupport value type, expect int when input is int_list, value: " << value_->ToString();
    }
  } else if (TryGetDoubleListProcessorParameter(context, input_, &double_list)) {
    double double_val = 0;
    if (TryGetDoubleProcessorParameter(context, value_, &double_val)) {
      cnt = FindIndexesForValue(double_list, double_val, count_,
                                output_index_attr_.empty() ? nullptr : &indexes);
    } else {
      CL_LOG_ERROR("unsupport value type", value_->ToString())
          << "unsupport value type, expect double when input is double_list, value: " << value_->ToString();
    }
  } else if (TryGetStringListProcessorParameter(context, input_, &string_list)) {
    std::string string_val;
    if (TryGetStringProcessorParameter(context, value_, &string_val)) {
      cnt = FindIndexesForValue(string_list, absl::string_view(string_val), count_,
                                output_index_attr_.empty() ? nullptr : &indexes);
    } else {
      CL_LOG_ERROR("unsupport value type", value_->ToString())
          << "unsupport value type, expect string when input is string_list, value: " << value_->ToString();
    }
  } else {
    CL_LOG_ERROR("unsupport input type", input_->ToString())
        << "unsupport input type, expect int_list/double_list/string_list, input: " << input_->ToString();
  }

  context->SetIntCommonAttr(result_, cnt);
  if (!output_index_attr_.empty() && indexes.size() > 0) {
    if (!output_index_attr_accessor_) {
      output_index_attr_accessor_ = context->GetCommonAttrAccessor(output_index_attr_);
    }
    if (indexes.size() == 1) {
      context->SetIntCommonAttr(output_index_attr_accessor_, indexes[0]);
    } else {
      context->SetIntListCommonAttr(output_index_attr_accessor_, std::move(indexes));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoFindValueEnricher, CommonRecoFindValueEnricher)

}  // namespace platform
}  // namespace ks
