#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

// 应该改名为 CommonRecoItemAttrValueEnricher
class CommonRecoItemAttrDefaultValueEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemAttrDefaultValueEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrValueConfig {
    int64 int_value;
    double double_value;
    std::string string_value;
    std::vector<int64> int_list_value;
    std::vector<double> double_list_value;
    std::vector<std::string> string_list_value;
    AttrType attr_type;
    std::string attr_name;
    ItemAttr *accessor = nullptr;
  };

  bool InitProcessor() override {
    no_overwrite_ = config()->GetBoolean("no_overwrite", false);

    auto common_attr_value_configs = config()->Get("common_attrs");
    auto item_attr_value_configs = config()->Get("item_attrs");

    if (common_attr_value_configs && common_attr_value_configs->IsArray()) {
      for (const auto *value_config : common_attr_value_configs->array()) {
        if (!value_config->IsObject()) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! Values of 'item_attrs' "
                     << "should be an object";
          return false;
        }
        AttrValueConfig attr_value_config;
        if (!ExtractAttrValueConfigFromConfig(&attr_value_config, value_config)) {
          return false;
        }
        common_attr_value_configs_.emplace_back(std::move(attr_value_config));
      }
    }

    if (item_attr_value_configs && item_attr_value_configs->IsArray()) {
      for (const auto *value_config : item_attr_value_configs->array()) {
        if (!value_config->IsObject()) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! Values of 'item_attrs' "
                     << "should be an object";
          return false;
        }
        AttrValueConfig attr_value_config;
        if (!ExtractAttrValueConfigFromConfig(&attr_value_config, value_config)) {
          return false;
        }
        item_attr_value_configs_.emplace_back(std::move(attr_value_config));
      }
    }

    return true;
  }

  bool ExtractAttrValueConfigFromConfig(AttrValueConfig *value, const base::Json *config) {
    value->attr_type = RecoUtil::ParseAttrType(config->GetString("type"));
    if (value->attr_type == AttrType::UNKNOWN) {
      LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! Values of 'type' "
                 << "is not valid";
      return false;
    }
    value->attr_name = config->GetString("name");
    if (value->attr_name.empty()) {
      LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! name should be a string";
      return false;
    }
    auto value_json = config->Get("value");
    if (!value_json) {
      LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! value should not be emtpy";
      return false;
    }
    switch (value->attr_type) {
      case AttrType::INT:
        if (!value_json->IntValue(&value->int_value)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: int, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::FLOAT:
        if (!value_json->NumberValue(&value->double_value)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: float, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::STRING:
        if (!value_json->StringValue(&value->string_value)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: string, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::INT_LIST:
        if (!RecoUtil::ExtractIntListFromJsonConfig(value_json, &value->int_list_value, true)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: int_list, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::FLOAT_LIST:
        if (!RecoUtil::ExtractDoubleListFromJsonConfig(value_json, &value->double_list_value, true)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: float_list, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      case AttrType::STRING_LIST:
        if (!RecoUtil::ExtractStringListFromJsonConfig(value_json, &value->string_list_value, true)) {
          LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type and value's type "
                        "mismatch, type: string_list, value:"
                     << value_json->ToString();
          return false;
        }
        break;
      default:
        LOG(ERROR) << "CommonRecoItemAttrDefaultValueEnricher init failed! type not supported";
        return false;
    }
    return true;
  }
  void SetCommonAttrValues(MutableRecoContextInterface *context, const AttrValueConfig &attr_value_config,
                           CommonAttr *common_attr);
  void SetItemAttrValues(MutableRecoContextInterface *context, const AttrValueConfig &attr_value_config,
                         const CommonRecoResult &result);

 private:
  bool no_overwrite_ = false;
  std::vector<AttrValueConfig> common_attr_value_configs_;
  std::vector<AttrValueConfig> item_attr_value_configs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemAttrDefaultValueEnricher);
};

}  // namespace platform
}  // namespace ks
