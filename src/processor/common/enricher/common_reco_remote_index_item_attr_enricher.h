#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/common_query_service_gateway.h"
#include "ks/common_reco/index/common_query_client.h"
#include "ks/common_reco/util/common_reco_object_pool.h"

namespace ks {
namespace platform {

/**
 * 该 Enricher 为 CommonRecoLocalIndexItemAttrEnricher 的远程访问版本，
 * 用于从异地部署的 Common Index 服务获取正排属性
 */
class CommonRecoRemoteIndexItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRemoteIndexItemAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    CommonQueryServiceGateway::GetInstance();
    service_group_ = config()->GetString("service_group", "PRODUCTION");
    shard_prefix_ = config()->GetString("shard_prefix", "s");
    shard_num_ = config()->GetInt("shard_num", 1);
    if (shard_num_ < 1) {
      LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher init failed! 'shard_num' must be > 0";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", 300);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher init failed! 'timeout_ms' must be > 0";
      return false;
    }

    auto *attrs = config()->Get("attrs");
    if (!attrs || !attrs->IsArray()) {
      LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher init failed! Missing 'attrs' config or it is not"
                 << " an array.";
      return false;
    }

    attr_name_list_.clear();
    attr_type_list_.clear();
    for (const auto *attr_json : attrs->array()) {
      if (attr_json->IsObject()) {
        // 保持对旧格式的兼容: {"name": "xxx", "type": "yyy"}
        attr_name_list_.push_back(attr_json->GetString("name"));
        auto type = RecoUtil::ParseAttrType(attr_json->GetString("type"));
        if (type == AttrType::UNKNOWN) {
          LOG(ERROR) << "Unknown attr type: " << attr_json->GetString("type");
          return false;
        }
        if (type == AttrType::STRING_LIST) {
          LOG(ERROR) << "Attr type string_list is not supported!";
          return false;
        }
        attr_type_list_.push_back(type);
      } else if (attr_json->IsString()) {
        attr_name_list_.push_back(attr_json->StringValue());
      } else {
        LOG(ERROR) << "Item of attrs should be a string or dict! Value found: " << attr_json->ToString();
        return false;
      }
    }

    if (!FillQueryAttrs()) {
      return false;
    }

    auto *item_source = config()->Get("additional_item_source");
    if (item_source) {
      if (!item_source->IsObject()) {
        LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                   << " init failed! \"additional_item_source\" should"
                   << " be a dict";
        return false;
      }

      auto *latest_browse_set_item = item_source->Get("latest_browse_set_item");
      if (latest_browse_set_item) {
        include_browse_set_items_ = true;
        if (!latest_browse_set_item->IntValue(&include_browse_set_item_count_)) {
          LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                     << " init failed! \"latest_browse_set_item\" should"
                     << " be an int! Value found: " << latest_browse_set_item->ToString();
          return false;
        }
      } else {
        include_browse_set_items_ = false;
      }

      auto *common_attr = item_source->Get("common_attr");
      if (common_attr) {
        if (!common_attr->IsArray()) {
          LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                     << " init failed! \"common_attr\" should be an array!"
                     << " Value found: " << common_attr->ToString();
          return false;
        }
        for (const auto *attr : common_attr->array()) {
          if (!attr->IsString()) {
            LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                       << " init failed! Item of \"common_attr\" should be string!"
                       << " Value found: " << attr->ToString();
            return false;
          }
          std::string attr_name = attr->StringValue();
          if (!attr_name.empty()) {
            source_common_attrs_.push_back(attr_name);
          }
        }
      }

      auto *item_attr = item_source->Get("item_attr");
      if (item_attr) {
        if (!item_attr->IsArray()) {
          LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                     << " init failed! \"item_attr\" should be an array!"
                     << " Value found: " << item_attr->ToString();
          return false;
        }
        for (const auto *attr : item_attr->array()) {
          if (!attr->IsString()) {
            LOG(ERROR) << "CommonRecoRemoteIndexItemAttrEnricher"
                       << " init failed! Item of \"item_attr\" should be string!"
                       << " Value found: " << attr->ToString();
            return false;
          }
          std::string attr_name = attr->StringValue();
          if (!attr_name.empty()) {
            source_item_attrs_.push_back(attr_name);
          }
        }
      }

      include_reco_results_ = item_source->GetBoolean("reco_results", true);
    }

    item_type_ = config()->GetInt("item_type", -1);

    no_overwrite_ = config()->GetBoolean("no_overwrite", false);

    consistent_hash_ = config()->GetBoolean("consistent_hash", true);

    max_value_bytes_ = config()->GetInt("max_value_bytes", 1024 * 1024);

    item_key_attr_ = config()->GetString("item_key_attr", "");

    return true;
  }

 private:
  bool FillQueryAttrs();

  bool IsAllAttrsExisting(const CommonRecoResult &result) const;

  bool SendRequestToShard(MutableRecoContextInterface *context, std::vector<CommonRecoResult> &&items,
                          const std::string &kess_service, const std::string &shard, uint64 user_hash,
                          const std::string &info);

 private:
  std::string service_group_;
  std::string shard_prefix_;
  int shard_num_ = 1;
  int timeout_ms_ = 300;

  std::vector<std::string> attr_name_list_;  // 配置需要请求的 attr
  std::vector<AttrType> attr_type_list_;
  std::vector<ItemAttr *> attr_accessors_;
  bool include_browse_set_items_ = false;
  int64 include_browse_set_item_count_ = 0;
  std::vector<std::string> source_common_attrs_;
  std::vector<std::string> source_item_attrs_;
  bool include_reco_results_ = true;
  int item_type_ = -1;
  bool no_overwrite_ = false;
  bool consistent_hash_ = true;
  int max_value_bytes_ = 0;
  std::string item_key_attr_;

  CommonQueryFlattenRequestBuilder request_builder_;
  CommonQueryClient common_query_client_;
  CommonRecoObjectPool<CommonQueryFlattenResponse> response_pool_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRemoteIndexItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
