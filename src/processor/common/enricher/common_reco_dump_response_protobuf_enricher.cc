#include "dragon/src/processor/common/enricher/common_reco_dump_response_protobuf_enricher.h"

namespace ks {
namespace platform {

void CommonRecoBuildResponseEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  if (include_return_attrs_in_request_) {
    dump_common_attrs_.reserve(dump_common_attrs_.size() + context->GetRequest()->return_common_attrs_size());
    for (auto common_attr : context->GetRequest()->return_common_attrs()) {
      dump_common_attrs_.emplace_back(common_attr);
    }
    dump_item_attrs_.reserve(dump_item_attrs_.size() + context->GetRequest()->return_item_attrs_size());
    for (auto item_attr : context->GetRequest()->return_item_attrs()) {
      dump_item_attrs_.emplace_back(item_attr);
    }
  }
  int item_num = GetIntProcessorParameter(context, item_num_config_, -1);
  int total_size = std::distance(begin, end);
  auto new_end = std::next(begin, std::min(total_size, std::max(0, item_num)));
  BuildRecoResponse(context, begin, new_end);
}

void CommonRecoBuildResponseEnricher::BuildRecoResponse(MutableRecoContextInterface *context,
                                                        RecoResultConstIter begin, RecoResultConstIter end) {
  auto response = std::make_shared<CommonRecoResponse>();
  bool use_data_table = context->GetRequest()->use_data_table();
  auto request_type = context->GetRequestType();

  // fill common attrs
  if (use_data_table) {
    std::vector<AttrValue *> attr_accessors;
    attr_accessors.reserve(dump_common_attrs_.size());
    for (const auto &attr_name : dump_common_attrs_) {
      attr_accessors.push_back(context->GetCommonAttrAccessor(attr_name));
    }
    std::vector<CommonRecoResult> items;
    items.push_back(CommonRecoResult(0, 0, 0));
    RecoUtil::BuildPackedTableColumns(items.begin(), items.end(), attr_accessors,
                                      response->mutable_common_data());
  } else {
    folly::F14FastSet<std::string> dedup_set;
    dedup_set.reserve(dump_common_attrs_.size());
    for (const auto &attr_name : dump_common_attrs_) {
      auto pr = dedup_set.insert(attr_name);
      if (!pr.second) {
        continue;
      }
      interop::BuildSampleAttrFromCommonAttr(context, attr_name, response->mutable_common_attr());
    }
  }

  auto result_size = std::distance(begin, end);
  response->mutable_item()->Reserve(result_size);

  folly::F14FastMap<int, int> item_type_count;
  folly::F14FastMap<int, int> reason_count;

  std::vector<ItemAttr *> attr_accessors;
  attr_accessors.reserve(dump_item_attrs_.size());
  folly::F14FastSet<std::string> dedup_set;
  dedup_set.reserve(dump_item_attrs_.size());
  for (const auto &attr_name : dump_item_attrs_) {
    auto pr = dedup_set.insert(attr_name);
    if (pr.second) {
      attr_accessors.push_back(context->GetItemAttrAccessor(attr_name));
    }
  }

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    CommonRecoItem *item = response->add_item();
    item->set_item_id(result.GetId());
    item->set_item_type(result.GetType());
    item->set_reason(result.reason);
    item->set_score(result.score);
    if (!use_data_table && !attr_accessors.empty()) {
      item->mutable_item_attr()->Reserve(attr_accessors.size());
      for (auto *accessor : attr_accessors) {
        kuiba::SampleAttr *attr = item->add_item_attr();
        if (!interop::LoadSampleAttrFromItemAttr(result, accessor, attr)) {
          attr->set_name(accessor->name());
        }
      }
    }
    ++item_type_count[item->item_type()];
    ++reason_count[item->reason()];
  });

  if (use_data_table) {
    for (const auto &table_columns : context->GetRequest()->return_table_columns()) {
      auto *item_table = response->add_item_data();
      auto *table = context->GetOrInsertDataTable(table_columns.table());
      item_table->set_name(table->GetName());
      std::vector<AttrValue *> accessors;
      accessors.reserve(table_columns.columns().size());
      for (const auto &column : table_columns.columns()) {
        accessors.push_back(table->GetOrInsertAttr(column));
      }
      const auto &table_results = table->GetCommonRecoResults();
      RecoUtil::BuildPackedTableColumns(table_results.begin(), table_results.end(), accessors, item_table);
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          table_results.size(), kPerfNs, "processor_build_response", GlobalHolder::GetServiceIdentifier(),
          request_type, "response_size", table_columns.table());
    }
  }

  if (as_string_) {
    std::string serialize_string;
    if (response->SerializeToString(&serialize_string)) {
      context->SetStringCommonAttr(to_attr_, std::move(serialize_string));
    } else {
      CL_LOG_ERROR(to_attr_, "build_response_serialize_error")
          << "Context serialize to string failed in common attr " << to_attr_;
    }
  } else {
    context->SetPtrCommonAttr(to_attr_, response);
  }

  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      response->item_size(), kPerfNs, "processor_build_response", GlobalHolder::GetServiceIdentifier(),
      request_type, "response_size");
  for (const auto &pr : item_type_count) {
    base::perfutil::PerfUtilWrapper::CountLogStash(pr.second, kPerfNs, "processor_build_response",
                                                   GlobalHolder::GetServiceIdentifier(), request_type,
                                                   "item_type_count", base::IntToString(pr.first));
  }
  for (const auto &pr : reason_count) {
    base::perfutil::PerfUtilWrapper::CountLogStash(pr.second, kPerfNs, "processor_build_response",
                                                   GlobalHolder::GetServiceIdentifier(), request_type,
                                                   "reason_count", base::IntToString(pr.first));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoBuildResponseEnricher, CommonRecoBuildResponseEnricher)
}  // namespace platform
}  // namespace ks
