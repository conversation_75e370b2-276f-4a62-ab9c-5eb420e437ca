#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {

class CommonRecoCommonAttrPackEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCommonAttrPackEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    output_common_attr_ = config()->GetString("output_common_attr", "");
    if (output_common_attr_.empty()) {
      LOG(ERROR)
          << "CommonRecoCommonAttrPackEnricher init failed, output_common_attr should be an non-empty string";
      return false;
    }
    if (!config()->Get("input_common_attrs")) {
      LOG(ERROR) << "CommonRecoCommonAttrPackEnricher init failed, you should have input_common_attrs";
      return false;
    }
    deduplicate_ = config()->GetBoolean("deduplicate", false);
    return true;
  }

 private:
  std::string output_common_attr_;
  bool deduplicate_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCommonAttrPackEnricher);
};

}  // namespace platform
}  // namespace ks
