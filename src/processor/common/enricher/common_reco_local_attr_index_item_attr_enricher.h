#pragma once

#include <string>
#include <unordered_map>
#include <vector>

#include "ks/reco_pub/reco/distributed_photo_info/attr_index/attr_index_util.h"
#include "dragon/src/processor/common/enricher/common_reco_local_index_item_attr_enricher.h"

namespace ks {
namespace platform {

/**
 * 使用 attr index 的正排获取 processor
 *
 * 配置与 CommonRecoLocalIndexItemAttrEnricher 一样
 * 
 * 使用前请参考 ks/reco_pub/reco/distributed_photo_info/attr_index/attr_index_util.h 内容配置好 gflags
 */
class CommonRecoLocalAttrIndexItemAttrEnricher : public CommonRecoLocalIndexItemAttrEnricher {
 public:
  CommonRecoLocalAttrIndexItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  std::function<void()> Purge() override {
    return []() { ks::reco::AttrIndexSingleton()->Stop(); };
  }

 protected:
  const char *GetProcessorName() const override { return "CommonRecoLocalAttrIndexItemAttrEnricher"; }

  bool InitIndex() override {
    ks::reco::AttrIndexSingleton()->Initialize();
    return true;
  }

 private:
  void GetItemAttrs(ks::reco::AttrIndex *attr_index, const CommonRecoResult &result,
                    const std::vector<uint64> &attr_keys,
                    const std::vector<CommonIndexEnum::AttrType> &attr_types,
                    const std::vector<ks::reco::protoutil::AttrType> &read_attr_types);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLocalAttrIndexItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
