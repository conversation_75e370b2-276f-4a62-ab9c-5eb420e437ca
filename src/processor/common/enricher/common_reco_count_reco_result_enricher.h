#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoCountRecoResultEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCountRecoResultEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    save_result_size_to_common_attr_ = config()->GetString("save_result_size_to_common_attr");
    if (save_result_size_to_common_attr_.empty()) {
      LOG(ERROR) << "CommonRecoCountRecoResultEnricher init failed!"
                 << "save_result_size_to_common_attr is required";
      return false;
    }

    return true;
  }

 private:
  std::string save_result_size_to_common_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCountRecoResultEnricher);
};

}  // namespace platform
}  // namespace ks
