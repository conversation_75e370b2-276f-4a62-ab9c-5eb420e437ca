#include "dragon/src/processor/common/enricher/common_reco_wasm_enricher.h"

#include <iterator>
#include <utility>
#include <variant>

#include "base/file/file_util.h"

#include "dragon/src/util/logging_util.h"

namespace ks {
namespace platform {

bool CommonRecoWasmEnricher::InitProcessor() {
  // wat or wasm_file
  auto *json_wat = config()->Get("wat");
  auto *json_wasm_file = config()->Get("wasm_file");
  if ((!json_wat && !json_wasm_file) || (json_wat && json_wasm_file)) {
    LOG(ERROR)
        << "CommonRecoWasmEnricher init failed! 'wat' or 'wasm_file' must be configured, and only config one";
    return false;
  }

  clear_attr_if_null_ = config()->GetBoolean("clear_attr_if_null", false);
  set_empty_attr_if_null_ = config()->GetBoolean("set_empty_attr_if_null", false);

  bool need_alloc_func = false;

  // input_attrs
  auto *json_in_attrs = config()->Get("input_attrs");
  if (json_in_attrs) {
    if (!json_in_attrs->IsArray()) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'input_attrs' must be array";
      return false;
    }

    for (auto *json_in_attr : json_in_attrs->array()) {
      if (!json_in_attr->IsObject()) {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'input_attrs[x]' must be an object";
        return false;
      }

      std::string name = json_in_attr->GetString("name", "");
      if (name.empty()) {
        LOG(ERROR)
            << "CommonRecoWasmEnricher init failed! 'input_attrs[x].name' must be string and not empty";
        return false;
      }

      std::string type = json_in_attr->GetString("type", "common_attr");
      std::string wasm_var = json_in_attr->GetString("wasm_var", name);
      std::string value_type_str = json_in_attr->GetString("value_type", "");
      WatValueType value_type = GetValueTypeFromString(value_type_str);
      if (value_type == kUnknown) {
        LOG(ERROR)
            << "CommonRecoWasmEnricher init failed! 'input_attrs[x].value_type' must be one of \"i64\", "
               "\"f64\", \"string\", \"i64_list\", \"f64_list\"";
        return false;
      }

      if (type == "common_attr") {
        in_attrs_.push_back(InOutAttr{
            .type = kCommonAttr,
            .name = name,
            .accessor_index = AddCommonAttrAndGetIndex(name),
            .wasm_var = wasm_var,
            .value_type = value_type,
        });
      } else if (type == "item_attr") {
        in_attrs_.push_back(InOutAttr{
            .type = kItemAttr,
            .name = name,
            .accessor_index = AddItemAttrAndGetIndex(name),
            .wasm_var = wasm_var,
            .value_type = value_type,
        });
        need_alloc_func = true;
      } else {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'input_attrs[x].type' must be 'common_attr' or "
                      "'item_attr'";
        return false;
      }
    }
  }

  // output_attrs
  auto *json_out_attrs = config()->Get("output_attrs");
  if (json_out_attrs) {
    if (!json_out_attrs->IsArray()) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'output_attrs' must be array";
      return false;
    }

    for (auto *json_out_attr : json_out_attrs->array()) {
      if (!json_out_attr->IsObject()) {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'output_attrs[x]' must be an object";
        return false;
      }

      std::string name = json_out_attr->GetString("name", "");
      if (name.empty()) {
        LOG(ERROR)
            << "CommonRecoWasmEnricher init failed! 'output_attrs[x].name' must be string and not empty";
        return false;
      }

      std::string type = json_out_attr->GetString("type", "common_attr");
      std::string wasm_var = json_out_attr->GetString("wasm_var", name);
      std::string value_type_str = json_out_attr->GetString("value_type", "");
      WatValueType value_type = GetValueTypeFromString(value_type_str);
      if (value_type == kUnknown) {
        LOG(ERROR)
            << "CommonRecoWasmEnricher init failed! 'output_attrs[x].value_type' must be one of \"i64\", "
               "\"f64\", \"string\", \"i64_list\", \"f64_list\"";
        return false;
      }

      if (type == "common_attr") {
        out_attrs_.push_back(InOutAttr{
            .type = kCommonAttr,
            .name = name,
            .accessor_index = AddCommonAttrAndGetIndex(name),
            .wasm_var = wasm_var,
            .value_type = value_type,
        });
      } else if (type == "item_attr") {
        out_attrs_.push_back(InOutAttr{
            .type = kItemAttr,
            .name = name,
            .accessor_index = AddItemAttrAndGetIndex(name),
            .wasm_var = wasm_var,
            .value_type = value_type,
        });
        need_alloc_func = true;
      } else {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'output_attrs[x].type' must be 'common_attr' or "
                      "'item_attr'";
        return false;
      }
    }
  }

  for (int i = 0; !need_alloc_func && i < in_attrs_.size(); ++i) {
    auto &attr = in_attrs_[i];
    if (attr.value_type == kString || attr.value_type == kI64List || attr.value_type == kF64List) {
      need_alloc_func = true;
    }
  }

  for (int i = 0; !need_alloc_func && i < out_attrs_.size(); ++i) {
    auto &attr = out_attrs_[i];
    if (attr.value_type == kString || attr.value_type == kI64List || attr.value_type == kF64List) {
      need_alloc_func = true;
    }
  }

  bool in_attrs_contain_item_attr = false;
  for (auto &in_attr : in_attrs_) {
    if (in_attr.type == kItemAttr) {
      in_attrs_contain_item_attr = true;
      break;
    }
  }

  // calls
  auto *json_calls = config()->Get("calls");
  if (json_calls == nullptr || !json_calls->IsArray()) {
    LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'calls' must be presented and be array type";
    return false;
  }

  for (auto *json_call : json_calls->array()) {
    if (!json_call->IsObject()) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'calls' element must be dict type";
      return false;
    }

    Call call;
    call.func_name = json_call->GetString("name", "");
    if (call.func_name.empty()) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'calls' element must have 'name' field";
      return false;
    }

    auto json_params = json_call->Get("params");
    if (json_params) {
      if (!json_params->IsArray()) {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'calls[x].params' must be array type";
        return false;
      }
      for (auto *json_param : json_params->array()) {
        if (!json_param->IsString()) {
          LOG(ERROR) << "CommonRecoWasmEnricher init failed! 'calls[x].params' element must be string type";
          return false;
        }
        call.params.push_back(AddCommonAttrAndGetIndex(json_param->StringValue()));
      }
    }

    auto json_results = json_call->Get("results");
    if (json_results) {
      if (!json_results->IsArray()) {
        LOG(ERROR) << "CommonRecoWasmEnricher init failed, 'calls[x].results' must be array type";
        return false;
      }
      for (auto *json_result : json_results->array()) {
        if (!json_result->IsString()) {
          LOG(ERROR) << "CommonRecoWasmEnricher init failed, 'calls[x].results[x]' must be string type";
          return false;
        }
        call.results.push_back(AddCommonAttrAndGetIndex(json_result->StringValue()));
      }
    }
    calls_.push_back(call);
  }

  // create module
  if (json_wat) {
    auto mod_ret = wasmtime::Module::compile(engine_, json_wat->StringValue());
    if (!mod_ret) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, compile wat failed! err:" << mod_ret.err().message();
      return false;
    }
    module_ = mod_ret.ok();
  } else if (json_wasm_file) {
    std::string content;
    if (!base::file_util::ReadFileToString(json_wasm_file->StringValue(), &content)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't read file '" << json_wasm_file->StringValue()
                 << "'";
      return false;
    }
    wasmtime::Span<uint8_t> wasm(reinterpret_cast<uint8_t *>(content.data()), content.size());
    auto mod_ret = wasmtime::Module::compile(engine_, wasm);
    if (!mod_ret) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, compile wat failed! err:" << mod_ret.err().message();
      return false;
    }
    module_ = mod_ret.ok();
  } else {
    NOT_REACHED();
  }

  // create instance
  store_.emplace(engine_);
  auto instance_ret = wasmtime::Instance::create(store_.value(), module_.value(), {});
  if (!instance_ret) {
    LOG(ERROR) << "CommonRecoWasmEnricher init failed, create wasmtime instance failed! err:"
               << instance_ret.err().message();
    return false;
  }
  instance_ = instance_ret.ok();

  // get memory
  auto memory_ret = instance_->get(store_.value(), "memory");
  if (need_alloc_func && (!memory_ret || !std::holds_alternative<wasmtime::Memory>(*memory_ret))) {
    LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export 'memory'";
    return false;
  }
  if (memory_ret) {
    memory_ = std::get<wasmtime::Memory>(*memory_ret);
  }

  // try get globals in input_attrs
  for (auto &in_attr : in_attrs_) {
    auto ret = instance_->get(store_.value(), in_attr.wasm_var);
    if (!ret || !std::holds_alternative<wasmtime::Global>(*ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export global '" << in_attr.name
                 << "' which configured in in_attr config";
      return false;
    }
    in_attr.global_ptr = std::get<wasmtime::Global>(*ret).get(store_.value()).i32();
  }

  // try get globals in output_attrs
  for (auto &out_attr : out_attrs_) {
    auto ret = instance_->get(store_.value(), out_attr.wasm_var);
    if (!ret || !std::holds_alternative<wasmtime::Global>(*ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export global '" << out_attr.name
                 << "' which configured in out_attr config";
      return false;
    }
    out_attr.global_ptr = std::get<wasmtime::Global>(*ret).get(store_.value()).i32();
  }

  // try get item_num
  if (in_attrs_contain_item_attr) {
    auto ret = instance_->get(store_.value(), "item_num");
    if (!ret || !std::holds_alternative<wasmtime::Global>(*ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export global 'item_num' which must "
                    "exists for item attrs";
      return false;
    }
    item_num_ptr_ = std::get<wasmtime::Global>(*ret).get(store_.value()).i32();
  }

  // try get functions
  for (auto &call : calls_) {
    auto ret = instance_->get(store_.value(), call.func_name);
    if (!ret || !std::holds_alternative<wasmtime::Func>(*ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, invalid function '" << call.func_name
                 << "' in call!";
      return false;
    }

    funcs_.push_back(std::get<wasmtime::Func>(*ret));
  }

  // init func
  auto init_ret = instance_->get(store_.value(), "init");
  if (need_alloc_func && !init_ret) {
    LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export function 'init'";
    return false;
  } else if (init_ret) {
    if (!std::holds_alternative<wasmtime::Func>(*init_ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher init failed, can't find export function 'init'";
      return false;
    }
    init_func_ = std::get<wasmtime::Func>(*init_ret);
  }

  // alloc func
  if (need_alloc_func) {
    auto ret = instance_->get(store_.value(), "alloc");
    if (!ret || !std::holds_alternative<wasmtime::Func>(*ret)) {
      LOG(ERROR) << "CommonRecoWasmEnricher alloc failed, can't find export function 'alloc'";
      return false;
    }
    alloc_func_ = std::get<wasmtime::Func>(*ret);
  }

  common_attr_accessors_.resize(common_attr_names_.size());
  item_attr_accessors_.resize(item_attr_names_.size());

  LOG(INFO) << "CommonRecoWasmEnricher init success! calls count:" << calls_.size();
  return true;
}

int CommonRecoWasmEnricher::AddCommonAttrAndGetIndex(const std::string &common_attr) {
  auto iter = common_attr_name_index_map_.find(common_attr);
  if (iter != common_attr_name_index_map_.end()) {
    // already exists
    return iter->second;
  }
  // not exist, add new
  int index = common_attr_names_.size();
  common_attr_names_.push_back(common_attr);
  common_attr_name_index_map_[common_attr] = index;
  return index;
}

int CommonRecoWasmEnricher::AddItemAttrAndGetIndex(const std::string &item_attr) {
  auto iter = item_attr_name_index_map_.find(item_attr);
  if (iter != item_attr_name_index_map_.end()) {
    // already exists
    return iter->second;
  }
  // not exist, add new
  int index = item_attr_names_.size();
  item_attr_names_.push_back(item_attr);
  item_attr_name_index_map_[item_attr] = index;
  return index;
}

CommonRecoWasmEnricher::WatValueType CommonRecoWasmEnricher::GetValueTypeFromString(const std::string &str) {
  if (str == "i64") {
    return kI64;
  } else if (str == "f64") {
    return kF64;
  } else if (str == "string") {
    return kString;
  } else if (str == "i64_list") {
    return kI64List;
  } else if (str == "f64_list") {
    return kF64List;
  } else {
    return kUnknown;
  }
}

void CommonRecoWasmEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  int item_num = std::distance(begin, end);

  // init accessors
  for (int i = 0; i < common_attr_names_.size(); ++i) {
    auto accessor = context->GetCommonAttrAccessor(common_attr_names_[i]);
    if (accessor == nullptr) {
      LOG(ERROR) << "get accessor for common attr '" << common_attr_names_[i] << "' failed";
      return;
    }
    common_attr_accessors_[i] = accessor;
  }

  for (int i = 0; i < item_attr_names_.size(); ++i) {
    auto accessor = context->GetItemAttrAccessor(item_attr_names_[i]);
    if (accessor == nullptr) {
      LOG(ERROR) << "get accessor for item attr '" << item_attr_names_[i] << "' failed";
      return;
    }
    item_attr_accessors_[i] = accessor;
  }

  // call init
  if (init_func_.has_value()) {
    auto ret = init_func_->call(store_.value(), {});
    if (!ret) {
      CL_LOG_ERROR("wasm", GetName()) << "call function 'init' failed! err:" << ret.err().message();
      return;
    }
  }

  // get memory ptr
  uint8_t *memory_start = nullptr;
  if (memory_.has_value()) {
    memory_start = memory_->data(store_.value()).data();
  }
  auto alloc_memory_in_wasm = [this](int size) -> uint32_t {
    auto ret = alloc_func_->call(store_.value(), {size});
    if (!ret) {
      CL_LOG_ERROR("wasm", GetName()) << "call function 'alloc' failed! err:" << ret.err().message();
      return 0;
    }
    if (ret.ok().size() != 1) {
      CL_LOG_ERROR("wasm", GetName()) << "call function 'alloc' failed! return result count must be 1";
      return 0;
    }
    uint32_t alloc_result = static_cast<uint32_t>(ret.ok()[0].i32());
    if (alloc_result == 0) {
      CL_LOG_ERROR("wasm", GetName())
          << "call function 'alloc' failed! return nullptr, maybe exceeded memory limit 4GB";
      return 0;
    }
    return alloc_result;
  };

  // if contain item attr, set item_num
  if (item_num_ptr_) {
    *reinterpret_cast<int *>(memory_start + item_num_ptr_.value()) = item_num;
  }

  // handle input_attrs
  for (const auto &in_attr : in_attrs_) {
    switch (in_attr.type) {
      case InOutAttrType::kCommonAttr: {
        auto accessor = common_attr_accessors_[in_attr.accessor_index];
        DCHECK(accessor);
        bool success = false;
        switch (in_attr.value_type) {
          case WatValueType::kI64: {
            auto int_val = context->GetIntCommonAttr(accessor);
            *reinterpret_cast<int64_t *>(memory_start + in_attr.global_ptr) = int_val.value_or(0);
            success = true;
            break;
          }
          case WatValueType::kF64: {
            auto double_val = context->GetDoubleCommonAttr(accessor);
            *reinterpret_cast<double *>(memory_start + in_attr.global_ptr) = double_val.value_or(0);
            success = true;
            break;
          }
          case WatValueType::kString: {
            auto string_val = context->GetStringCommonAttr(accessor);
            int list_size = string_val ? string_val->size() : 0;
            int need_size = sizeof(int) + list_size * sizeof(char);
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            // write size
            *reinterpret_cast<int *>(memory_start + ptr) = list_size;
            // write data
            if (list_size > 0) {
              std::memcpy(memory_start + ptr + sizeof(int), string_val->data(), list_size * sizeof(char));
            }
            // save ptr to global
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kI64List: {
            auto int_list_val = context->GetIntListCommonAttr(accessor);
            int list_size = int_list_val ? int_list_val->size() : 0;
            int need_size = sizeof(int) + list_size * sizeof(int64_t);
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            // write size
            *reinterpret_cast<int *>(memory_start + ptr) = list_size;
            // write data
            if (list_size > 0) {
              std::memcpy(memory_start + ptr + sizeof(int), int_list_val->data(),
                          list_size * sizeof(int64_t));
            }
            // save ptr to global
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kF64List: {
            auto double_list_val = context->GetDoubleListCommonAttr(accessor);
            int list_size = double_list_val ? double_list_val->size() : 0;
            int need_size = sizeof(int) + list_size * sizeof(double);
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            // write size
            *reinterpret_cast<int *>(memory_start + ptr) = list_size;
            // write data
            if (list_size > 0) {
              std::memcpy(memory_start + ptr + sizeof(int), double_list_val->data(),
                          list_size * sizeof(double));
            }
            // save ptr to global
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kUnknown:
            break;
        }
        if (!success) {
          CL_LOG_ERROR("wasm", GetName()) << "import common attr '" << in_attr.name << "' failed!";
          return;
        }
        break;
      }
      case InOutAttrType::kItemAttr: {
        if (item_num == 0) {
          *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = 0;
          break;
        }
        auto accessor = item_attr_accessors_[in_attr.accessor_index];
        DCHECK(accessor);
        bool success = false;
        switch (in_attr.value_type) {
          case WatValueType::kI64: {
            int need_size = sizeof(int64_t) * item_num;
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            int index = 0;
            for (auto iter = begin; iter != end; ++iter, ++index) {
              auto int_val = context->GetIntItemAttr(*iter, accessor);
              *reinterpret_cast<int64_t *>(memory_start + ptr + sizeof(int64_t) * index) =
                  int_val.value_or(0);
            }
            // save ptr
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kF64: {
            int need_size = sizeof(double) * item_num;
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            int index = 0;
            for (auto iter = begin; iter != end; ++iter, ++index) {
              auto double_val = context->GetDoubleItemAttr(*iter, accessor);
              *reinterpret_cast<double *>(memory_start + ptr + sizeof(double) * index) =
                  double_val.value_or(0);
            }
            // save ptr
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kString: {
            int need_size = sizeof(int) * item_num;
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            int index = 0;
            for (auto iter = begin; iter != end; ++iter, ++index) {
              auto string_val = context->GetStringItemAttr(*iter, accessor);
              int list_size = string_val ? string_val->size() : 0;
              int item_need_size = sizeof(int) + list_size * sizeof(char);
              uint32_t item_ptr = alloc_memory_in_wasm(item_need_size);
              if (item_ptr == 0) break;
              // write size
              *reinterpret_cast<int *>(memory_start + item_ptr) = list_size;
              // write data
              if (list_size > 0) {
                std::memcpy(memory_start + item_ptr + sizeof(int), string_val->data(),
                            list_size * sizeof(char));
              }
              // save ptr
              *reinterpret_cast<int *>(memory_start + ptr + sizeof(int) * index) = item_ptr;
            }
            // save ptr
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kI64List: {
            int need_size = sizeof(int) * item_num;
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            int index = 0;
            for (auto iter = begin; iter != end; ++iter, ++index) {
              auto int_list_val = context->GetIntListItemAttr(*iter, accessor);
              int list_size = int_list_val ? int_list_val->size() : 0;
              int item_need_size = sizeof(int) + list_size * sizeof(int64_t);
              uint32_t item_ptr = alloc_memory_in_wasm(item_need_size);
              if (item_ptr == 0) break;
              // write size
              *reinterpret_cast<int *>(memory_start + item_ptr) = list_size;
              // write data
              if (list_size > 0) {
                std::memcpy(memory_start + item_ptr + sizeof(int), int_list_val->data(),
                            list_size * sizeof(int64_t));
              }
              // save ptr
              *reinterpret_cast<int *>(memory_start + ptr + sizeof(int) * index) = item_ptr;
            }
            // save ptr
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kF64List: {
            int need_size = sizeof(int) * item_num;
            uint32_t ptr = alloc_memory_in_wasm(need_size);
            if (ptr == 0) break;
            int index = 0;
            for (auto iter = begin; iter != end; ++iter, ++index) {
              auto double_list_val = context->GetDoubleListItemAttr(*iter, accessor);
              int list_size = double_list_val ? double_list_val->size() : 0;
              int item_need_size = sizeof(int) + list_size * sizeof(double);
              uint32_t item_ptr = alloc_memory_in_wasm(item_need_size);
              if (item_ptr == 0) break;
              // write size
              *reinterpret_cast<int *>(memory_start + item_ptr) = list_size;
              // write data
              if (list_size > 0) {
                std::memcpy(memory_start + item_ptr + sizeof(int), double_list_val->data(),
                            list_size * sizeof(double));
              }
              // save ptr
              *reinterpret_cast<int *>(memory_start + ptr + sizeof(int) * index) = item_ptr;
            }
            // save ptr
            *reinterpret_cast<int *>(memory_start + in_attr.global_ptr) = ptr;
            success = true;
            break;
          }
          case WatValueType::kUnknown:
            break;
        }
        if (!success) {
          CL_LOG_ERROR("wasm", GetName()) << "import item attr '" << in_attr.name << "' failed!";
          return;
        }
        break;
      }
    }
  }

  // handle item out attrs
  for (auto &out_attr : out_attrs_) {
    if (out_attr.type != kItemAttr) continue;
    if (item_num == 0) {
      *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = 0;
      continue;
    }
    auto accessor = item_attr_accessors_[out_attr.accessor_index];
    DCHECK(accessor);
    switch (out_attr.value_type) {
      case WatValueType::kI64: {
        uint32_t ptr = alloc_memory_in_wasm(item_num * sizeof(int64_t));
        std::memset(memory_start + ptr, 0, item_num * sizeof(int64_t));
        *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = ptr;
        break;
      }
      case WatValueType::kF64: {
        uint32_t ptr = alloc_memory_in_wasm(item_num * sizeof(double));
        std::memset(memory_start + ptr, 0, item_num * sizeof(double));
        *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = ptr;
        break;
      }
      case WatValueType::kString: {
        uint32_t ptr = alloc_memory_in_wasm(item_num * sizeof(int));
        std::memset(memory_start + ptr, 0, item_num * sizeof(int));
        *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = ptr;
        break;
      }
      case WatValueType::kI64List: {
        uint32_t ptr = alloc_memory_in_wasm(item_num * sizeof(int));
        std::memset(memory_start + ptr, 0, item_num * sizeof(int));
        *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = ptr;
        break;
      }
      case WatValueType::kF64List: {
        uint32_t ptr = alloc_memory_in_wasm(item_num * sizeof(int));
        std::memset(memory_start + ptr, 0, item_num * sizeof(int));
        *reinterpret_cast<int *>(memory_start + out_attr.global_ptr) = ptr;
        break;
      }
      case WatValueType::kUnknown:
        break;
    }
  }

  // call every functions
  int success_calls = 0;
  for (int call_index = 0; call_index < calls_.size(); ++call_index) {
    bool has_error = false;
    auto &call = calls_[call_index];
    auto &func = funcs_[call_index];

    std::vector<wasmtime::Val> params;
    params.reserve(call.params.size());
    for (int param_index = 0; param_index < call.params.size() && !has_error; ++param_index) {
      auto &attr_index = call.params[param_index];
      CommonAttr *accessor = common_attr_accessors_[attr_index];
      DCHECK(accessor);
      switch (accessor->value_type) {
        case AttrType::INT: {
          auto int_val = context->GetIntCommonAttr(accessor);
          params.push_back(int_val.value_or(0));
          break;
        }
        case AttrType::FLOAT: {
          auto double_val = context->GetDoubleCommonAttr(accessor);
          params.push_back(double_val.value_or(0));
          break;
        }
        default:
          CL_LOG_ERROR("wasm", GetName())
              << "func '" << call.func_name << "' does not support this attr '"
              << common_attr_names_[attr_index] << "' type:" << static_cast<int>(accessor->value_type);
          has_error = true;
          continue;
      }
    }

    if (has_error) continue;

    auto ret = func.call(store_.value(), std::move(params));
    if (!ret) {
      CL_LOG_ERROR("wasm", GetName())
          << "call function '" << call.func_name << "' failed! err:" << ret.err().message();
      return;
    }

    auto data = ret.ok();
    if (data.size() != call.results.size()) {
      CL_LOG_ERROR("wasm", GetName()) << "function '" << call.func_name << "' result has " << data.size()
                                      << " results, but configured " << call.results.size();
      return;
    }

    for (int i = 0; i < call.results.size() && !has_error; ++i) {
      auto &result_index = call.results[i];
      auto &val = data[i];
      CommonAttr *accessor = common_attr_accessors_[result_index];
      DCHECK(accessor);
      switch (val.kind()) {
        case wasmtime::ValKind::I32:
          context->SetIntCommonAttr(accessor, val.i32());
          break;
        case wasmtime::ValKind::I64:
          context->SetIntCommonAttr(accessor, val.i64());
          break;
        case wasmtime::ValKind::F32:
          context->SetDoubleCommonAttr(accessor, val.f32());
          break;
        case wasmtime::ValKind::F64:
          context->SetDoubleCommonAttr(accessor, val.f64());
          break;
        default:
          CL_LOG_ERROR("wasm", GetName())
              << "func '" << call.func_name << "' return data type is invalid, val type:" << val.kind();
          has_error = true;
          continue;
      }
    }
    if (has_error) continue;
    ++success_calls;
  }

  // handle output_attrs
  for (auto &out_attr : out_attrs_) {
    switch (out_attr.type) {
      case InOutAttrType::kCommonAttr: {
        auto accessor = common_attr_accessors_[out_attr.accessor_index];
        DCHECK(accessor);
        bool success = false;
        switch (out_attr.value_type) {
          case WatValueType::kI64: {
            int64_t val = *reinterpret_cast<const int64_t *>(memory_start + out_attr.global_ptr);
            context->SetIntCommonAttr(accessor, val);
            success = true;
            break;
          }
          case WatValueType::kF64: {
            double val = *reinterpret_cast<const double *>(memory_start + out_attr.global_ptr);
            context->SetDoubleCommonAttr(accessor, val);
            success = true;
            break;
          }
          case WatValueType::kString: {
            int ptr = *reinterpret_cast<const int *>(memory_start + out_attr.global_ptr);
            if (ptr == 0) {
              if (clear_attr_if_null_) context->ClearCommonAttr(accessor);
              if (set_empty_attr_if_null_) context->SetStringCommonAttr(accessor, {});
              success = true;
              break;
            } else if (ptr < 0) {
              break;
            }
            int size = *reinterpret_cast<const int *>(memory_start + ptr);
            const char *data = reinterpret_cast<const char *>(memory_start + ptr + sizeof(int));
            std::string val(data, size);
            context->SetStringCommonAttr(accessor, std::move(val));
            success = true;
            break;
          }
          case WatValueType::kI64List: {
            int ptr = *reinterpret_cast<const int *>(memory_start + out_attr.global_ptr);
            if (ptr == 0) {
              if (clear_attr_if_null_) context->ClearCommonAttr(accessor);
              if (set_empty_attr_if_null_) context->SetIntListCommonAttr(accessor, {});
              success = true;
              break;
            } else if (ptr < 0) {
              break;
            }
            int size = *reinterpret_cast<const int *>(memory_start + ptr);
            const int64_t *data = reinterpret_cast<const int64_t *>(memory_start + ptr + sizeof(int));
            std::vector<int64_t> val(data, data + size);
            context->SetIntListCommonAttr(accessor, std::move(val));
            success = true;
            break;
          }
          case WatValueType::kF64List: {
            int ptr = *reinterpret_cast<const int *>(memory_start + out_attr.global_ptr);
            if (ptr == 0) {
              if (clear_attr_if_null_) context->ClearCommonAttr(accessor);
              if (set_empty_attr_if_null_) context->SetDoubleListCommonAttr(accessor, {});
              success = true;
              break;
            } else if (ptr < 0) {
              break;
            }
            int size = *reinterpret_cast<const int *>(memory_start + ptr);
            const double *data = reinterpret_cast<const double *>(memory_start + ptr + sizeof(int));
            std::vector<double> val(data, data + size);
            context->SetDoubleListCommonAttr(accessor, std::move(val));
            success = true;
            break;
          }
          case WatValueType::kUnknown:
            break;
        }
        if (!success) {
          CL_LOG_ERROR("wasm", GetName()) << "write out common attr '" << out_attr.name
                                          << "' failed! out_attr:" << out_attr.DebugString();
        }
        break;
      }
      case InOutAttrType::kItemAttr: {
        if (item_num == 0) break;
        auto accessor = item_attr_accessors_[out_attr.accessor_index];
        DCHECK(accessor);
        bool success = false;
        int ptr = *reinterpret_cast<const int *>(memory_start + out_attr.global_ptr);
        switch (out_attr.value_type) {
          case WatValueType::kI64: {
            int item_index = 0;
            for (auto iter = begin; iter != end; ++iter, ++item_index) {
              int64_t val =
                  *reinterpret_cast<const int64_t *>(memory_start + ptr + item_index * sizeof(int64_t));
              context->SetIntItemAttr(*iter, accessor, val);
            }
            success = true;
            break;
          }
          case WatValueType::kF64: {
            int item_index = 0;
            for (auto iter = begin; iter != end; ++iter, ++item_index) {
              double val =
                  *reinterpret_cast<const double *>(memory_start + ptr + item_index * sizeof(double));
              context->SetDoubleItemAttr(*iter, accessor, val);
            }
            success = true;
            break;
          }
          case WatValueType::kString: {
            int item_index = 0;
            for (auto iter = begin; iter != end; ++iter, ++item_index) {
              int list_item_ptr =
                  *reinterpret_cast<const int *>(memory_start + ptr + item_index * sizeof(int));
              if (list_item_ptr > 0) {
                int size = *reinterpret_cast<const int *>(memory_start + list_item_ptr);
                const char *data = reinterpret_cast<const char *>(memory_start + list_item_ptr + sizeof(int));
                std::string val(data, size);
                context->SetStringItemAttr(*iter, accessor, std::move(val));
              } else {
                if (clear_attr_if_null_) context->ClearItemAttr(*iter, accessor);
                if (set_empty_attr_if_null_) context->SetStringItemAttr(*iter, accessor, {});
              }
            }
            success = true;
            break;
          }
          case WatValueType::kI64List: {
            int item_index = 0;
            for (auto iter = begin; iter != end; ++iter, ++item_index) {
              int list_item_ptr =
                  *reinterpret_cast<const int *>(memory_start + ptr + item_index * sizeof(int));
              if (list_item_ptr > 0) {
                int size = *reinterpret_cast<const int *>(memory_start + list_item_ptr);
                const int64_t *data =
                    reinterpret_cast<const int64_t *>(memory_start + list_item_ptr + sizeof(int));
                std::vector<int64_t> val(data, data + size);
                context->SetIntListItemAttr(*iter, accessor, std::move(val));
              } else {
                if (clear_attr_if_null_) context->ClearItemAttr(*iter, accessor);
                if (set_empty_attr_if_null_) context->SetIntListItemAttr(*iter, accessor, {});
              }
            }
            success = true;
            break;
          }
          case WatValueType::kF64List: {
            int item_index = 0;
            for (auto iter = begin; iter != end; ++iter, ++item_index) {
              int list_item_ptr =
                  *reinterpret_cast<const int *>(memory_start + ptr + item_index * sizeof(int));
              if (list_item_ptr > 0) {
                int size = *reinterpret_cast<const int *>(memory_start + list_item_ptr);
                const double *data =
                    reinterpret_cast<const double *>(memory_start + list_item_ptr + sizeof(int));
                std::vector<double> val(data, data + size);
                context->SetDoubleListItemAttr(*iter, accessor, std::move(val));
              } else {
                if (clear_attr_if_null_) context->ClearItemAttr(*iter, accessor);
                if (set_empty_attr_if_null_) context->SetDoubleListItemAttr(*iter, accessor, {});
              }
            }
            success = true;
            break;
          }
          case WatValueType::kUnknown:
            break;
        }
        if (!success) {
          CL_LOG_ERROR("wasm", GetName())
              << "write out attr '" << out_attr.name << "' failed! out_attr:" << out_attr.DebugString();
        }
        break;
      }
    }
  }

  DLOG(INFO) << "success calls:" << success_calls << "/" << calls_.size();
}  // NOLINT

std::string CommonRecoWasmEnricher::InOutAttr::DebugString() const {
  return absl::StrFormat("type:%d, name:'%s', accessor_index:%d, wasm_var:'%s', value_type:%d, global_ptr:%d",
                         static_cast<int>(type), name, accessor_index, wasm_var, static_cast<int>(value_type),
                         global_ptr);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoWasmEnricher, CommonRecoWasmEnricher)

}  // namespace platform
}  // namespace ks
