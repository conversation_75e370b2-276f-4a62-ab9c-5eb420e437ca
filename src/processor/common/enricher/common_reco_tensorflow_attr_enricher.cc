#include "dragon/src/processor/common/enricher/common_reco_tensorflow_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoTensorFlowAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  timer_.Start();

  std::vector<tensorflow::Tensor> feed_tensors;
  feed_tensors.reserve(args_.size());
  for (const auto &pr : args_) {
    if (!AppendFeedTensor(context, pr.second, begin, end, &feed_tensors)) {
      CL_LOG_ERROR("tensorflow", "AppendFeedTensor_failed_" + pr.first)
          << "CommonRecoTensorFlowAttrEnricher Failed to append feed tensor: " << pr.first;
      return;
    }
  }

  timer_.AppendCostMs("collect_inputs");

  std::vector<tensorflow::Tensor> fetch_tensors;
  tensorflow::Status status = session_->RunCallable(callable_handle_, feed_tensors, &fetch_tensors, nullptr);
  if (!status.ok()) {
    CL_LOG_ERROR("tensorflow", "RunCallable_fail")
        << "CommonRecoTensorFlowAttrEnricher Failed to run callable: " << status.ToString();
    return;
  }

  timer_.AppendCostMs("run_graph");

  if (fetch_tensors.size() != outputs_.size()) {
    CL_LOG_ERROR("tensorflow", "invalid_fetch_tensors_size")
        << "CommonRecoTensorFlowAttrEnricher expect " << outputs_.size() << " fetch_tensors "
        << "while got " << fetch_tensors.size();
    return;
  }

  for (int output_offset = 0; output_offset < outputs_.size(); output_offset++) {
    const auto &output_tensor = fetch_tensors[output_offset];
    const std::string &output_attr_name = outputs_[output_offset];

    if (!SaveFetchTensor(context, output_attr_name, begin, end, output_tensor)) {
      CL_LOG_ERROR("tensorflow", "SaveFetchTensor_failed_" + output_attr_name)
          << "CommonRecoTensorFlowAttrEnricher Failed to save fetch tensor: " << output_attr_name;
    }
  }

  timer_.AppendCostMs("save_outputs");

  CL_LOG(INFO) << timer_.display();
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTensorFlowAttrEnricher, CommonRecoTensorFlowAttrEnricher)

}  // namespace platform
}  // namespace ks
