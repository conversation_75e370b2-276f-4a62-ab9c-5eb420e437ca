#include "dragon/src/processor/common/enricher/common_reco_rodis_tl_attr_enricher.h"

#include <utility>
#include <vector>

#include "absl/strings/str_format.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

void CommonRecoRodisTlAttrEnricher::<PERSON>rich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }
  if (valid_duration_ms_ < 0) {
    request_.clear_after_ts();
  } else {
    request_.set_after_ts(context->GetRequestTime() - valid_duration_ms_);
  }
  if (after_ts_ > 0) {
    request_.set_after_ts(after_ts_);
  }
  if (before_ts_ > 0) {
    request_.set_before_ts(context->GetRequestTime() - before_ts_);
  } else {
    request_.set_before_ts(context->GetRequestTime());
  }

  request_.set_key(key);

  VLOG(1) << "request: " << request_.DebugString();

  auto pr = [&]() {
    KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_name_, "PRODUCTION", "s0", timeout_ms_, request_, &response_,
                                           ::kuaishou::ds::kess::RodisService, AsyncTimeListGet);
  }();

  if (!pr.first) {
    CL_LOG(WARNING) << "Request rodis service failed: " << kess_name_;
    return;
  }

  RegisterAsyncCallback(context, std::move(pr.second),
                        [this, context](::kuaishou::ds::TimeListGetResponse *response) {
                          VLOG(1) << "response: " << response->DebugString();
                          CL_LOG(INFO) << "item size: " << response->item_size()
                                       << ", err_code: " << response->err_code();
                          std::vector<std::string> vec;
                          vec.reserve(response->item_size());
                          for (const auto &item : response->item()) {
                            vec.emplace_back(item.data());
                          }
                          context->SetStringListCommonAttr(value_attr_, std::move(vec));
                        });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisTlAttrEnricher, CommonRecoRodisTlAttrEnricher)

}  // namespace platform
}  // namespace ks
