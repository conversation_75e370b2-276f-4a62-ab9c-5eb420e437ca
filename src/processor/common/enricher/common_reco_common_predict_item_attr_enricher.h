#pragma once

#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "learning/kuiba/predict_base/common_predict_client.h"
#include "learning/kuiba/predict_base/predict_base.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"

namespace ks {
namespace platform {

class CommonRecoCommonPredictItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoCommonPredictItemAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

  bool GenerateSampleListAttr(MutableRecoContextInterface *context, std::vector<kuiba::SampleAttr> *attr_vct);

  bool GenerateCommonAttr(MutableRecoContextInterface *context, std::vector<kuiba::SampleAttr> *attr_vct);

  void AddMultiCommonSampleAttrs(ReadableRecoContextInterface *context,
                                 const std::unordered_set<std::string> &attr_names,
                                 std::vector<kuiba::SampleAttr> *attr_vct);

  bool AddCommonSampleAttr(ReadableRecoContextInterface *context, const std::string &attr_name,
                           kuiba::SampleAttr *attr);

 private:
  struct SendItemAttr {
    absl::string_view name;
    absl::string_view as;
    ItemAttr *accessor = nullptr;
  };

  std::string service_group_;
  int timeout_ms_;

  std::vector<std::string> loss_;
  std::string output_attr_prefix_;
  std::vector<ItemAttr *> output_attr_accessors_;
  std::vector<float> default_loss_values_;  // 和 loss 个数需要一一对应
  float default_loss_value_;                // 后面可以废弃掉
  int64 item_type_;  // 当 item_type 赋值时，对指定 item_type 的 key 进行预估
  bool target_item_type_only_;
  std::string item_key_attr_;
  std::string item_attrs_in_name_list_;

  kuiba::AsyncCommonPredictClient client_;
  CommonRecoObjectPool<kuiba::CompressCommonPredictResponse> response_pool_;
  std::vector<uint64> request_items_;         // 保存实际发出的 item (可能是 key 可能是 id)
  std::vector<CommonRecoResult> sent_items_;  // 保存实际发出的 item

  // sample list attr 相关
  bool use_sample_list_attr_flag_ = false;
  bool use_sample_list_attr_flatten_ = false;
  std::string sample_list_common_attr_key_;

  // 过滤相关
  bool filter_unlogin_user_flag_ = false;
  // 取消 common attr miss warning
  bool disable_common_attr_missing_warning_ = false;

  std::unordered_map<std::string, std::string> send_item_attrs_;
  std::vector<SendItemAttr> send_item_attr_vec_;
  std::unordered_set<std::string> exclude_common_attrs_;
  std::unordered_set<std::string> extra_common_attrs_;
  std::unordered_map<std::string, std::string> attr_name_transform_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCommonPredictItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
