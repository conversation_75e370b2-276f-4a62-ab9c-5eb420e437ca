#include "dragon/src/processor/common/enricher/common_reco_clotho_attr_enricher.h"

#include <exception>
#include <future>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "third_party/abseil/absl/strings/str_format.h"
#include "third_party/abseil/absl/strings/str_join.h"

namespace ks {
namespace platform {

bool CommonRecoClothoAttrEnricher::BuildRequest(MutableRecoContextInterface *context) {
  request_.Clear();
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return false;
  }
  request_.add_keys(key);

  if (read_all_column_) {
    request_.set_read_whole_row(true);
  } else {
    for (const auto &kv : rename_map_) {
      auto column_req = request_.add_column_read_reqs();
      column_req->set_column(kv.first);
    }
  }
  request_.set_table(table_name_);
  request_.set_token(token_name_);
  request_.set_get_pb_result(true);
  return true;
}

void CommonRecoClothoAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  if (!BuildRequest(context)) return;
  if (clotho_client_ == nullptr) return;
  response_.Clear();
  auto pms = std::make_shared<std::promise<void *>>();
  std::future<void *> st_future(pms->get_future());
  auto clotho_callback = [this, pms, context](::reco::clotho::sdk::Status status,
                                              const ::reco::clotho::gateway::TableReadResponse &response) {
    if (status != ::reco::clotho::sdk::Status::STATUS_OK) {
      pms->set_value(nullptr);
      CL_LOG_WARNING_EVERY("clotho", "send_request_fail", 1000)
          << "Clotho read failed, status=" << StatusToString(status);
      return;
    }
    response_ = response;
    // nullptr will be special handle by dragonfly
    pms->set_value(reinterpret_cast<void *>(&response_));
  };

  auto status_async = clotho_client_->AsyncTableRead(request_, clotho_callback, timeout_ms_);
  if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
    // 这里主要是对 client 创建和参数是否正常进行的检查
    CL_LOG_WARNING_EVERY("clotho", "create_client_failed", 1000)
        << "Clotho client create failed" << RecoUtil::GetRequestInfoForLog(context);
    return;
  }

  RegisterLocalAsyncCallback(context, std::move(st_future), [this, context](void *resp) {
    if (resp == nullptr) return;
    if (response_.rows_size() != 1) {
      CL_LOG_WARNING_EVERY("clotho", "response fail", 1000)
          << "Clotho read failed size = " << response_.rows_size();
      return;
    }
    if (response_.rows(0).get_empty_row()) {
      CL_LOG_WARNING_EVERY("clotho", "whole_row_empty", 1000)
          << "the whole row is empty, key=" << response_.rows(0).key();
      return;
    }
    for (int i = 0; i < response_.rows(0).raw_data_size(); i++) {
      const auto &column_value = response_.rows(0).raw_data(i);
      if (column_value.is_null()) continue;
      if (rename_map_.find(column_value.name()) == rename_map_.end()) continue;
      std::string attr_name = rename_map_[column_value.name()];
      switch (column_value.type()) {
        case ::reco::clotho::sdk::FieldType::IntType: {
          context->SetIntCommonAttr(attr_name, column_value.int_value());
        } break;
        case ::reco::clotho::sdk::FieldType::FloatType: {
          context->SetDoubleCommonAttr(attr_name, column_value.float_value());
        } break;
        case ::reco::clotho::sdk::FieldType::StrType: {
          if (!column_value.str_value().empty()) {
            std::string str(reinterpret_cast<const char *>(column_value.str_value().data()),
                            column_value.str_value().size());
            context->SetStringCommonAttr(attr_name, std::move(str));
          }
        } break;
        case ::reco::clotho::sdk::FieldType::IntListType: {
          if (column_value.int_list_value_size() > 0) {
            std::vector<int64> res(column_value.int_list_value_size(), 0);
            for (int i = 0; i < column_value.int_list_value_size(); i++) {
              res[i] = column_value.int_list_value(i);
            }
            context->SetIntListCommonAttr(attr_name, std::move(res));
          }
        } break;
        case ::reco::clotho::sdk::FieldType::FloatListType: {
          if (column_value.float_list_value_size() > 0) {
            std::vector<double> res(column_value.float_list_value_size(), 0);
            for (int i = 0; i < column_value.float_list_value_size(); i++) {
              res[i] = static_cast<double>(column_value.float_list_value(i));
            }
            context->SetDoubleListCommonAttr(attr_name, std::move(res));
          }
        } break;
        case ::reco::clotho::sdk::FieldType::StrListType: {
          int length = column_value.str_list_value_size();
          if (length > 0) {
            std::vector<std::string> res;
            for (int i = 0; i < length; i++) {
              res.emplace_back(reinterpret_cast<const char *>(column_value.str_list_value(i).data()),
                               column_value.str_list_value(i).size());
            }
            context->SetStringListCommonAttr(attr_name, std::move(res));
          }
        } break;

        default:
          break;
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoClothoAttrEnricher, CommonRecoClothoAttrEnricher)

}  // namespace platform
}  // namespace ks
