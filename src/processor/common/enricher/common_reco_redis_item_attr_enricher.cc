#include "dragon/src/processor/common/enricher/common_reco_redis_item_attr_enricher.h"

#include <algorithm>
#include <iterator>
#include <memory>
#include <utility>
#include <vector>

namespace ks {
namespace platform {

void CommonRecoRedisItemAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  timeout_ms_ = CheckAndGetTimeoutMs(context);
  if (timeout_ms_ <= 0) {  // 如果超时时间为 0 毫秒，代表屏蔽此 redis 服务
    return;
  }
  const int item_num = std::distance(begin, end);
  if (item_num == 0) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich redis item attr cancelled: empty items";
    return;
  }

  auto *redis_key_accessor = context->GetItemAttrAccessor(redis_key_);
  if (redis_key_accessor->value_type == AttrType::UNKNOWN) {
    CL_LOG_WARNING("redis_item_attr", "attr_not_exist: " + redis_key_)
        << "redis key attr not exists, attr: " << redis_key_;
    return;
  }
  if (redis_key_accessor->value_type != AttrType::STRING) {
    CL_LOG_WARNING("redis_item_attr", "attr_not_string: " + redis_key_)
        << "redis key attr is not string, attr: " << redis_key_;
    return;
  }
  if (is_async_) {
    AsyncGetItemAttrFromRedisResponse(context, begin, end, redis_key_accessor);
  } else {
    GetItemAttrFromRedisResponse(context, begin, end, redis_key_accessor);
  }
}

void CommonRecoRedisItemAttrEnricher::GetItemAttrFromRedisResponse(MutableRecoContextInterface *context,
                                                                   RecoResultConstIter begin,
                                                                   RecoResultConstIter end,
                                                                   ItemAttr *redis_key_accessor) {
  std::vector<std::string> string_keys;
  std::vector<const CommonRecoResult *> has_key_results;

  int item_num = std::distance(begin, end);

  std::string key_prefix_value = GetStringProcessorParameter(context, key_prefix);
  string_keys.reserve(item_num);
  has_key_results.reserve(item_num);

  std::for_each(begin, end,
                [&string_keys, &has_key_results, redis_key_accessor,
                 &key_prefix_value](const CommonRecoResult &result) {
                  auto redis_key = result.GetStringAttr(redis_key_accessor);
                  if (redis_key) {
                    string_keys.emplace_back(absl::StrCat(key_prefix_value, *redis_key));
                    has_key_results.emplace_back(&result);
                  }
                });

  if (string_keys.empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich redis item attr cancelled: empty keys";
    return;
  }

  std::vector<std::string> string_values;
  string_values.reserve(string_keys.size());

  auto err_code = redis_client_->MGet(string_keys, &string_values, true, timeout_ms_);
  if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
      CL_LOG_WARNING("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value timeout, cluster_name: " << cluster_name_ << ", err_code: " << err_code
          << ", err_msg: " << ks::infra::err2str(err_code);
    } else {
      CL_LOG_ERROR("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value error, cluster_name: " << cluster_name_ << ", err_code: " << err_code
          << ", err_msg: " << ks::infra::err2str(err_code);
    }
    return;
  }

  if (string_values.size() != string_keys.size()) {
    CL_LOG_ERROR("redis_item_attr", "key_value_size_mismatch")
        << "cluster_name: " << cluster_name_ << ", redis key and value size mismatch: " << string_keys.size()
        << " vs " << string_values.size();
    return;
  }
  auto *redis_value_accessor = context->GetItemAttrAccessor(redis_value_);
  for (int i = 0; i < string_values.size(); ++i) {
    has_key_results.at(i)->SetStringAttr(redis_value_accessor, std::move(string_values[i]));
  }
}
void CommonRecoRedisItemAttrEnricher::AsyncGetItemAttrFromRedisResponse(MutableRecoContextInterface *context,
                                                                        RecoResultConstIter begin,
                                                                        RecoResultConstIter end,
                                                                        ItemAttr *redis_key_accessor) {
  auto string_keys = std::make_shared<std::vector<std::string>>();
  // 异步可能会跨过 arrange 等截断操作，这里只能深拷贝
  auto has_key_results = std::make_shared<std::vector<CommonRecoResult >>();

  int item_num = std::distance(begin, end);

  std::string key_prefix_value = GetStringProcessorParameter(context, key_prefix);
  string_keys->reserve(item_num);
  has_key_results->reserve(item_num);

  std::for_each(begin, end,
                [&string_keys, &has_key_results, redis_key_accessor,
                 &key_prefix_value](const CommonRecoResult &result) {
                  auto redis_key = result.GetStringAttr(redis_key_accessor);
                  if (redis_key) {
                    string_keys->emplace_back(absl::StrCat(key_prefix_value, *redis_key));
                    has_key_results->emplace_back(result);
                  }
                });

  if (string_keys->empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich redis item attr cancelled: empty keys";
    return;
  }

  std::shared_ptr<base::RedisMGetOpWaiter> async_mget_waiter =
      std::make_shared<base::RedisMGetOpWaiter>(redis_client_->AsyncMGet(*(string_keys.get())));

  async_mget_waiter->SetTimeout(timeout_ms_);

  auto redis_future =
      CommonRecoFutureActionWrapper<std::pair<ks::infra::RedisErrorCode, std::vector<std::string>>>(
          [this, context,
           async_mget_waiter]() -> std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> {
            std::vector<std::string> ret;
            auto status = async_mget_waiter->Get(&ret);
            auto async_cost = async_mget_waiter->GetAsyncCostUs();
            if (async_cost >= 0) {
              CL_PERF_INTERVAL(async_cost, kPerfNs, "processor_async_ready",
                               GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                               GetDownstreamProcessor(), "", GlobalHolder::GetJsonConfigVersion());
            }
            return {status, ret};
          });

  std::function<void(const std::vector<std::string> &)> callback =
      [this, context, string_keys, has_key_results](const std::vector<std::string> &string_values) {
        auto *redis_value_accessor = context->GetItemAttrAccessor(redis_value_);
        for (int i = 0; i < string_values.size(); ++i) {
          has_key_results->at(i).SetStringAttr(redis_value_accessor, std::move(string_values[i]));
        }
      };

  std::function<bool(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)> status_getter =
      [this, context,
       string_keys](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> bool {
    auto err_code = result.first;
    const auto &string_values = result.second;
    if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
      if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
        CL_LOG_WARNING("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value timeout, cluster_name: " << cluster_name_ << ", err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      } else {
        CL_LOG_ERROR("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value error, cluster_name: " << cluster_name_ << ", err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      }
      return false;
    }

    if (string_values.size() != string_keys->size()) {
      CL_LOG_ERROR("redis_item_attr", "key_value_size_mismatch")
          << "cluster_name: " << cluster_name_
          << ", redis key and value size mismatch: " << string_keys->size() << " vs " << string_values.size();
      return false;
    }
    return true;
  };
  std::function<const std::vector<std::string> &(
      const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      payload_getter = [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result)
      -> const std::vector<std::string> & { return result.second; };

  std::function<std::string(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      err_msg_getter =
          [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> std::string {
    return ks::infra::err2str(result.first);
  };
  RegisterAsyncCallback(
      context, std::move(redis_future), std::move(callback), []() {}, std::move(status_getter),
      std::move(payload_getter), std::move(err_msg_getter), "");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRedisItemAttrEnricher, CommonRecoRedisItemAttrEnricher)

}  // namespace platform
}  // namespace ks
