#include "dragon/src/processor/common/enricher/common_reco_kconf_lookup_enricher.h"
#include "dragon/src/interop/json_value.h"
#include "dragon/src/interop/util.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

void CommonRecoKconfLookupEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  for (const auto &kconf_config : kconf_configs_) {
    const auto &kconf_value = GetOrCreateKconfValue(context, kconf_config);
    if (kconf_config.is_common_attr) {
      LookupCommonAttr(context, kconf_value, kconf_config);
    } else {
      LookupItemAttr(context, begin, end, kconf_value, kconf_config);
    }
  }
}

const CommonRecoKconfLookupEnricher::KconfValue &CommonRecoKconfLookupEnricher::GetOrCreateKconfValue(
    ReadableRecoContextInterface *context, const KconfConfig &kconf_config) {
  std::string kconf_key = GetStringProcessorParameter(context, kconf_config.kconf_key);
  auto it = kconf_value_map_.find(kconf_key);
  if (it != kconf_value_map_.end()) {
    return it->second;
  }

  KconfValue kconf_value;
  switch (kconf_config.value_type) {
    case KconfParamType::INT64_SET: {
      static auto default_val = std::make_shared<std::set<int64>>();
      kconf_value.int_set = ks::infra::KConf().GetSet(kconf_key, default_val);
      break;
    }
    case KconfParamType::STRING_SET: {
      static auto default_val = std::make_shared<std::set<std::string>>();
      kconf_value.string_set = ks::infra::KConf().GetSet(kconf_key, default_val);
      break;
    }
    case KconfParamType::STRING_BOOL_MAP: {
      static auto default_val = std::make_shared<std::unordered_map<std::string, bool>>();
      kconf_value.string_bool_map = ks::infra::KConf().GetUnorderedMap(kconf_key, default_val, key_parser_);
      break;
    }
    case KconfParamType::STRING_INT64_MAP: {
      static auto default_val = std::make_shared<std::unordered_map<std::string, int64>>();
      kconf_value.string_int_map = ks::infra::KConf().GetUnorderedMap(kconf_key, default_val, key_parser_);
      break;
    }
    case KconfParamType::STRING_DOUBLE_MAP: {
      static auto default_val = std::make_shared<std::unordered_map<std::string, double>>();
      kconf_value.string_double_map = ks::infra::KConf().GetUnorderedMap(kconf_key, default_val, key_parser_);
      break;
    }
    case KconfParamType::STRING_STRING_MAP: {
      static auto default_val = std::make_shared<std::unordered_map<std::string, std::string>>();
      kconf_value.string_string_map = ks::infra::KConf().GetUnorderedMap(kconf_key, default_val, key_parser_);
      break;
    }
    case KconfParamType::TAIL_NUMBER: {
      static auto default_val = std::make_shared<ks::infra::TailNumber>();
      kconf_value.tail_number = ks::infra::KConf().Get(kconf_key, default_val);
      break;
    }
    default:
      break;
  }

  auto pr = kconf_value_map_.insert({kconf_key, kconf_value});
  return pr.first->second;
}

#define LOOKUP_COMMON_ATTR_IN_KCONF(MAP_FIELD, SET_METHOD_FOR_SINGULAR, SET_METHOD_FOR_LIST, VALUE_TYPE, \
                                    DEFAULT_VALUE)                                                       \
  do {                                                                                                   \
    std::string key;                                                                                     \
    if (auto val = context->GetStringCommonAttr(attr)) {                                                 \
      key = std::string(*val);                                                                           \
    } else if (auto val = context->GetIntCommonAttr(attr)) {                                             \
      key = std::to_string(*val);                                                                        \
    }                                                                                                    \
    if (!key.empty()) {                                                                                  \
      auto map = kconf_value.MAP_FIELD->Get();                                                           \
      auto it = map->find(key);                                                                          \
      if (it != map->end()) {                                                                            \
        context->SET_METHOD_FOR_SINGULAR(output, it->second);                                            \
      }                                                                                                  \
      break; /*如果单值处理成功则不再尝试 list 类型*/                                     \
    }                                                                                                    \
    std::vector<std::string> keys;                                                                       \
    std::vector<VALUE_TYPE> results;                                                                     \
    if (auto vals = context->GetStringListCommonAttr(attr)) {                                            \
      keys.reserve(vals->size());                                                                        \
      results.reserve(vals->size());                                                                     \
      for (const auto &val : *vals) {                                                                    \
        keys.emplace_back(std::string(val));                                                             \
      }                                                                                                  \
    } else if (auto vals = context->GetIntListCommonAttr(attr)) {                                        \
      keys.reserve(vals->size());                                                                        \
      results.reserve(vals->size());                                                                     \
      for (const auto val : *vals) {                                                                     \
        keys.emplace_back(std::to_string(val));                                                          \
      }                                                                                                  \
    }                                                                                                    \
    auto map = kconf_value.MAP_FIELD->Get();                                                             \
    for (const auto &key : keys) {                                                                       \
      auto it = map->find(key);                                                                          \
      results.push_back((it != map->end()) ? it->second : DEFAULT_VALUE);                                \
    }                                                                                                    \
    context->SET_METHOD_FOR_LIST(output, std::move(results));                                            \
  } while (0)

#define LOOKUP_ITEM_ATTR_IN_KCONF(MAP_FIELD, SET_METHOD)           \
  {                                                                \
    std::string key;                                               \
    if (auto val = context->GetStringItemAttr(result, attr)) {     \
      key = std::string(*val);                                     \
    } else if (auto val = context->GetIntItemAttr(result, attr)) { \
      key = std::to_string(*val);                                  \
    }                                                              \
    if (!key.empty()) {                                            \
      auto map = kconf_value.MAP_FIELD->Get();                     \
      auto it = map->find(key);                                    \
      if (it != map->end()) {                                      \
        context->SET_METHOD(result, output, it->second);           \
      }                                                            \
    }                                                              \
  }

void CommonRecoKconfLookupEnricher::LookupCommonAttr(MutableRecoContextInterface *context,
                                                     const KconfValue &kconf_value,
                                                     const KconfConfig &kconf_config) {
  auto *attr = context->GetCommonAttrAccessor(kconf_config.lookup_attr);
  auto *output = context->GetCommonAttrAccessor(kconf_config.output_attr);
  switch (kconf_config.value_type) {
    case KconfParamType::INT64_SET: {
      if (auto target = context->GetIntCommonAttr(attr)) {
        int64 found = kconf_value.int_set->Get()->count(*target);
        context->SetIntCommonAttr(output, found);
      } else if (auto targets = context->GetIntListCommonAttr(attr)) {
        std::vector<int64> results;
        results.reserve(targets->size());
        auto set = kconf_value.int_set->Get();
        for (const auto target : *targets) {
          int64 found = set->count(target);
          results.push_back(found);
        }
        context->SetIntListCommonAttr(output, std::move(results));
      } else {
        context->SetIntCommonAttr(output, 0);
      }
      break;
    }
    case KconfParamType::STRING_SET: {
      if (auto target = context->GetStringCommonAttr(attr)) {
        int64 found = kconf_value.string_set->Get()->count(std::string(*target));
        context->SetIntCommonAttr(output, found);
      } else if (auto targets = context->GetStringListCommonAttr(attr)) {
        std::vector<int64> results;
        results.reserve(targets->size());
        auto set = kconf_value.string_set->Get();
        for (const auto &target : *targets) {
          int64 found = set->count(std::string(target));
          results.push_back(found);
        }
        context->SetIntListCommonAttr(output, std::move(results));
      } else {
        context->SetIntCommonAttr(output, 0);
      }
      break;
    }
    case KconfParamType::STRING_BOOL_MAP: {
      LOOKUP_COMMON_ATTR_IN_KCONF(string_bool_map, SetIntCommonAttr, SetIntListCommonAttr, int64, 0);
      break;
    }
    case KconfParamType::STRING_INT64_MAP: {
      LOOKUP_COMMON_ATTR_IN_KCONF(string_int_map, SetIntCommonAttr, SetIntListCommonAttr, int64, 0);
      break;
    }
    case KconfParamType::STRING_DOUBLE_MAP: {
      LOOKUP_COMMON_ATTR_IN_KCONF(string_double_map, SetDoubleCommonAttr, SetDoubleListCommonAttr, double,
                                  0.0);
      break;
    }
    case KconfParamType::STRING_STRING_MAP: {
      LOOKUP_COMMON_ATTR_IN_KCONF(string_string_map, SetStringCommonAttr, SetStringListCommonAttr,
                                  std::string, "");
      break;
    }
    case KconfParamType::TAIL_NUMBER: {
      if (auto target = context->GetIntCommonAttr(attr)) {
        bool found = kconf_value.tail_number->Get()->IsOnFor(*target);
        context->SetIntCommonAttr(output, found ? 1 : 0);
      } else if (auto targets = context->GetIntListCommonAttr(attr)) {
        std::vector<int64> results;
        results.reserve(targets->size());
        auto tail_number = kconf_value.tail_number->Get();
        for (const auto target : *targets) {
          bool found = tail_number->IsOnFor(target);
          results.push_back(found ? 1 : 0);
        }
        context->SetIntListCommonAttr(output, std::move(results));
      } else {
        context->SetIntCommonAttr(output, 0);
      }
      break;
    }
    default:
      CL_LOG_ERROR("kconf_lookup", "invalid_value_type:" + std::to_string((int)kconf_config.value_type))
          << "Unsupported value type: " << static_cast<int>(kconf_config.value_type)
          << ", kconf key: " << kconf_config.kconf_key->StringValue();
  }
}

void CommonRecoKconfLookupEnricher::LookupItemAttr(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end,
                                                   const KconfValue &kconf_value,
                                                   const KconfConfig &kconf_config) {
  auto *attr = context->GetItemAttrAccessor(kconf_config.lookup_attr);
  auto *output = context->GetItemAttrAccessor(kconf_config.output_attr);
  std::for_each(begin, end, [&](const auto &result) {
    switch (kconf_config.value_type) {
      case KconfParamType::INT64_SET: {
        auto target = context->GetIntItemAttr(result, attr);
        bool found = target && kconf_value.int_set->Get()->count(*target);
        context->SetIntItemAttr(result, output, found ? 1 : 0);
        break;
      }
      case KconfParamType::STRING_SET: {
        auto target = context->GetStringItemAttr(result, attr);
        bool found = target && kconf_value.string_set->Get()->count(std::string(*target));
        context->SetIntItemAttr(result, output, found ? 1 : 0);
        break;
      }
      case KconfParamType::STRING_BOOL_MAP: {
        LOOKUP_ITEM_ATTR_IN_KCONF(string_bool_map, SetIntItemAttr);
        break;
      }
      case KconfParamType::STRING_INT64_MAP: {
        LOOKUP_ITEM_ATTR_IN_KCONF(string_int_map, SetIntItemAttr);
        break;
      }
      case KconfParamType::STRING_DOUBLE_MAP: {
        LOOKUP_ITEM_ATTR_IN_KCONF(string_double_map, SetDoubleItemAttr);
        break;
      }
      case KconfParamType::STRING_STRING_MAP: {
        LOOKUP_ITEM_ATTR_IN_KCONF(string_string_map, SetStringItemAttr);
        break;
      }
      case KconfParamType::TAIL_NUMBER: {
        auto target = context->GetIntItemAttr(result, attr);
        bool found = target && kconf_value.tail_number->Get()->IsOnFor(*target);
        context->SetIntItemAttr(result, output, found ? 1 : 0);
        break;
      }
      default:
        CL_LOG_ERROR("kconf_lookup", "invalid_value_type:" + std::to_string((int)kconf_config.value_type))
            << "Unsupported value type: " << static_cast<int>(kconf_config.value_type)
            << ", kconf key: " << kconf_config.kconf_key->StringValue();
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoKconfLookupEnricher, CommonRecoKconfLookupEnricher)

}  // namespace platform
}  // namespace ks
