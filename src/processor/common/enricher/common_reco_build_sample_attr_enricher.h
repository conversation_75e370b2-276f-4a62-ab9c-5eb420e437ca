#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoBuildSampleAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoBuildSampleAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    auto mapping_config = config()->Get("mappings");
    if (mapping_config && mapping_config->IsArray()) {
      for (auto *c : mapping_config->array()) {
        if (c->IsObject()) {
          sample_attr_mapping_.emplace_back();
          auto &mapping = sample_attr_mapping_.back();
          mapping.from_attr = c->GetString("from_attr");
          mapping.to_attr = c->GetString("to_attr");
          mapping.rename = c->GetString("rename");
          if (mapping.from_attr.empty()) {
            LOG(ERROR) << "CommonRecoBuildSampleAttrEnricher init failed!"
                       << "from attr should not be an empty string.";
            return false;
          }
          if (mapping.to_attr.empty()) {
            mapping.to_attr = mapping.from_attr;
          }
          if (mapping.rename.empty()) {
            mapping.rename = mapping.from_attr;
          }
        } else {
          LOG(ERROR) << "CommonRecoBuildSampleAttrEnricher init failed!"
                     << "mappings should be an array of objects.";
          return false;
        }
      }
    }
    return true;
  }

  bool ReserveMessages(size_t size) {
    size_t allocated_size = sample_attrs_.size();
    if (size > allocated_size) {
      sample_attrs_.resize(size);
      for (size_t i = allocated_size; i < size; i++) {
        sample_attrs_[i].reset(new kuiba::SampleAttr());
      }
    }
    return true;
  }

 private:
  struct SampleAttrMapping {
    std::string from_attr;
    std::string rename;
    std::string to_attr;
    ItemAttr *from_accessor = nullptr;
    ItemAttr *to_accessor = nullptr;
  };

 private:
  bool is_common_attr_ = true;
  std::vector<SampleAttrMapping> sample_attr_mapping_;
  std::vector<std::shared_ptr<::google::protobuf::Message>> sample_attrs_;
  bool init_processor_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBuildSampleAttrEnricher);
};

}  // namespace platform
}  // namespace ks
