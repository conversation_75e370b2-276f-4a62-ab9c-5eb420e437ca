#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "teams/reco-arch/fdk/cpp/fdk.h"

namespace ks {
namespace platform {

class CommonRecoParseFromCustomKVUserInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoParseFromCustomKVUserInfoEnricher() = default;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    std::string collection_name = config()->GetString("collection_name");
    if (collection_name.empty()) {
      LOG(ERROR) << "Miss `collection_name`";
      return false;
    }

    fdk_metadata_.set_collection_name(collection_name);
    fdk_metadata_.set_format(fdk::proto::FORMAT_SAMPLE_ATTR);

    custom_user_info_from_ = config()->GetString("custom_user_info_from");
    if (custom_user_info_from_.empty()) {
      LOG(ERROR) << "Miss `custom_user_info_from_`";
      return false;
    }
    if (!RecoUtil::ExtractStringListFromJsonConfig(config()->Get("fields_to_read"), &fields_to_read_)) {
      LOG(ERROR) << "Miss `fields_to_read`";
      return false;
    }

    return true;
  }

  fdk::proto::Metadata fdk_metadata_;
  fdk::ReaderOptions fdk_options_;

  std::string custom_user_info_from_;
  std::vector<std::string> fields_to_read_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoParseFromCustomKVUserInfoEnricher);
};

}  // namespace platform
}  // namespace ks
