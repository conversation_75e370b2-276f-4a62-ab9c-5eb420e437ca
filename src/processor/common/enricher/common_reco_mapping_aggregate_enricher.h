#pragma once

#include <algorithm>
#include <string>
#include <unordered_set>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoMappingAggregateEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoMappingAggregateEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum Function {
    count = 1,
    sum = 2,
    min = 3,
    max = 4,
  };
  struct AttrFunction {
    std::string attr_name;
    std::string export_item_attr_name;
    Function function;
  };
  bool InitProcessor() override {
    key_list_attr_ = config()->GetString("key_list_attr", "");
    if (key_list_attr_.empty()) {
      LOG(ERROR) << "CommonRecoMappingAggregateEnricher init failed: key_list_attr is empty!";
      return false;
    }

    match_key_in_item_attr_ = config()->GetString("match_key_in_item_attr", "");
    save_for_match_key_ = config()->GetBoolean("save_for_match_key", false);
    save_count_to_attr_ = config()->GetString("save_count_to", "");
    base::Json *aggregate_config = config()->Get("aggregate_config");
    if (aggregate_config && aggregate_config->IsArray()) {
      attr_functions_.reserve(aggregate_config->array().size());
      for (auto *config : aggregate_config->array()) {
        std::string attr_name = config->GetString("value_list_attr", "");
        std::string export_item_attr_name = config->GetString("save_result_to", "");
        std::string function = config->GetString("aggregator", "sum");
        AttrFunction attr_function;
        attr_function.attr_name = attr_name;
        attr_function.export_item_attr_name = export_item_attr_name;
        if (function == "sum") {
          attr_function.function = Function::sum;
        } else if (function == "min") {
          attr_function.function = Function::min;
        } else if (function == "max") {
          attr_function.function = Function::max;
        }
        attr_functions_.push_back(std::move(attr_function));
      }
    } else if (aggregate_config && !aggregate_config->IsArray()) {
      LOG(ERROR) << "CommonRecoMappingAggregateEnricher init failed: aggregate_config is not array!";
      return false;
    }

    return true;
  }

  std::string match_key_in_item_attr_;
  std::string key_list_attr_;
  std::string save_count_to_attr_;
  std::vector<AttrFunction> attr_functions_;
  bool save_for_match_key_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoMappingAggregateEnricher);
};

}  // namespace platform
}  // namespace ks
