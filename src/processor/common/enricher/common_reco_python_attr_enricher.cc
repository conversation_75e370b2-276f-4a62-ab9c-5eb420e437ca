#include "dragon/src/processor/common/enricher/common_reco_python_attr_enricher.h"

#include <unordered_map>

#include "pybind11/embed.h"
#include "pybind11/pybind11.h"
#include "pybind11/stl.h"

namespace py = pybind11;

namespace ks {
namespace platform {

std::once_flag CommonRecoPythonAttrEnricher::python_init_flag_;

CommonRecoPythonAttrEnricher::CommonRecoPythonAttrEnricher() {
  std::call_once(python_init_flag_, []() {
    if (!Py_IsInitialized()) {
      // NOTE(huiyiqun): to run this processor in python script
      py::initialize_interpreter(false);
    }
  });
  // XXX(huiyiqun): never finalized
}

void CommonRecoPythonAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  timer_.Start();

  py::dict locals;

  for (const std::string &attr : import_common_attrs_) {
    auto attr_handle = py::cast(attr);
    if (auto int_val = context->GetIntCommonAttr(attr)) {
      locals[attr_handle] = *int_val;
    } else if (auto double_val = context->GetDoubleCommonAttr(attr)) {
      locals[attr_handle] = *double_val;
    } else if (auto str_val = context->GetStringCommonAttr(attr)) {
      locals[attr_handle] = std::string(str_val->data(), str_val->size());
    } else if (auto int_list_val = context->GetIntListCommonAttr(attr)) {
      locals[attr_handle] = std::vector<int64>(int_list_val->begin(), int_list_val->end());
    } else if (auto double_list_val = context->GetDoubleListCommonAttr(attr)) {
      locals[attr_handle] = std::vector<double>(double_list_val->begin(), double_list_val->end());
    } else if (auto str_list_val = context->GetStringListCommonAttr(attr)) {
      std::vector<std::string> vec;
      vec.reserve(str_list_val->size());
      for (auto sv : *str_list_val) {
        vec.emplace_back(sv.data(), sv.size());
      }
      locals[attr_handle] = vec;
    } else {
      locals[attr_handle] = py::none();
    }
  }

  timer_.AppendCostMs("collect_common_input");

  if (!export_common_attrs_.empty()) {
    py::exec(python_script_, py::globals(), locals);
    timer_.AppendCostMs("run_common_script");

    for (const std::string &attr : export_common_attrs_) {
      const auto &val = locals[py::cast(attr)];

      if (py::isinstance<py::int_>(val)) {
        context->SetIntCommonAttr(attr, val.cast<int>());
      } else if (py::isinstance<py::float_>(val)) {
        context->SetDoubleCommonAttr(attr, val.cast<double>());
      } else if (py::isinstance<py::str>(val)) {
        context->SetStringCommonAttr(attr, val.cast<std::string>());
      } else {
        LOG(INFO) << "type for " << attr << " is not supported: " << val.get_type();
      }
    }
    timer_.AppendCostMs("save_common_output");
  } else {
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      auto item_locals = py::module::import("copy").attr("copy")(locals);
      for (const std::string &attr : import_item_attrs_) {
        auto attr_handle = py::cast(attr);
        if (auto int_val = context->GetIntItemAttr(item_key, attr)) {
          item_locals[attr_handle] = *int_val;
        } else if (auto double_val = context->GetDoubleItemAttr(item_key, attr)) {
          item_locals[attr_handle] = *double_val;
        } else if (auto str_val = context->GetStringItemAttr(item_key, attr)) {
          item_locals[attr_handle] = std::string(str_val->data(), str_val->size());
        } else if (auto int_list_val = context->GetIntListItemAttr(item_key, attr)) {
          item_locals[attr_handle] = std::vector<int64>(int_list_val->begin(), int_list_val->end());
        } else if (auto double_list_val = context->GetDoubleListItemAttr(item_key, attr)) {
          item_locals[attr_handle] = std::vector<double>(double_list_val->begin(), double_list_val->end());
        } else if (auto str_list_val = context->GetStringListItemAttr(item_key, attr)) {
          std::vector<std::string> vec;
          vec.reserve(str_list_val->size());
          for (auto sv : *str_list_val) {
            vec.emplace_back(sv.data(), sv.size());
          }
          item_locals[attr_handle] = vec;
        } else {
          item_locals[attr_handle] = py::none();
        }
      }

      timer_.AppendCostMs("collect_item_input");

      py::exec(python_script_, py::globals(), item_locals);

      timer_.AppendCostMs("run_item_script");

      for (const std::string &attr : export_item_attrs_) {
        const auto &val = item_locals[py::cast(attr)];

        if (py::isinstance<py::int_>(val)) {
          context->SetIntItemAttr(item_key, attr, val.cast<int>());
        } else if (py::isinstance<py::float_>(val)) {
          context->SetDoubleItemAttr(item_key, attr, val.cast<double>());
        } else if (py::isinstance<py::str>(val)) {
          context->SetStringItemAttr(item_key, attr, val.cast<std::string>());
        } else {
          LOG(INFO) << "type for " << attr << " is not supported: " << val.get_type();
        }
      }

      timer_.AppendCostMs("save_item_output");
    });
  }
  CL_LOG(INFO) << timer_.display();
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPythonAttrEnricher, CommonRecoPythonAttrEnricher)

}  // namespace platform
}  // namespace ks
