#include "dragon/src/processor/common/enricher/common_reco_weighted_sum_enricher.h"

#include "serving_base/util/math.h"

namespace ks {
namespace platform {

void CommonRecoWeightedSumEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  auto *output_accessor = context->GetItemAttrAccessor(output_item_attr_);
  for (auto &channel : weighted_sum_channels_) {
    channel.weight = GetDoubleProcessorParameter(context, channel.weight_config, 0.0, true);
    channel.channel_attr = context->GetItemAttrAccessor(channel.name);
  }
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double score = 0.0;
    for (const auto &channel : weighted_sum_channels_) {
      double double_val = 0.0;
      if (!GetNumericValue(channel.channel_attr, result, &double_val)) {
        continue;
      }
      if (formula_version_ == 0) {
        score += channel.weight * double_val;
      } else {
        if (double_val > 0.0) {
          score += channel.weight * std::log(double_val);
        }
      }
    }
    result.SetDoubleAttr(output_accessor, score);
  });
}

bool CommonRecoWeightedSumEnricher::GetNumericValue(ItemAttr *attr_accessor,
                      const CommonRecoResult &result,
                      double *value) const {
  if (!attr_accessor || !value) {
    return false;
  }

  if (attr_accessor->value_type == AttrType::FLOAT) {
    if (auto p = result.GetDoubleAttr(attr_accessor)) {
      *value = *p;
      return true;
    }
  } else if (attr_accessor->value_type == AttrType::INT) {
    if (auto p = result.GetIntAttr(attr_accessor)) {
      *value = *p;
      return true;
    }
  }
  return false;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoWeightedSumEnricher, CommonRecoWeightedSumEnricher)

}  // namespace platform
}  // namespace ks
