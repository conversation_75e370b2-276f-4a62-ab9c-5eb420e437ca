#include "dragon/src/processor/common/enricher/common_reco_transform_item_attr_enricher.h"

#include "base/strings/string_util.h"
#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {

void CommonRecoTransformItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  BuildAttrsMapping(static_cast<ReadableRecoContextInterface *>(context));
  for (auto *mapping : attrs_mapping_) {
    for (auto it = begin; it != end; ++it) {
      uint64 item_key = it->item_key;
      if (mapping->check_attr_type == AttrType::INT) {
        auto val = context->GetIntItemAttr(item_key, mapping->check_attr);
        if (!val) {
          VLOG(100) << "cannot get int item attr [" << mapping->check_attr << "], item key: " << item_key;
          continue;
        }
        mapping->GenItemAttr(*val, context, item_key);
      } else if (mapping->check_attr_type == AttrType::STRING) {
        auto val = context->GetStringItemAttr(item_key, mapping->check_attr);
        if (!val) {
          VLOG(100) << "cannot get string item attr [" << mapping->check_attr << "], item key: " << item_key;
          continue;
        }
        mapping->GenItemAttr(*val, context, item_key);
      } else if (mapping->check_attr_type == AttrType::FLOAT) {
        auto val = context->GetDoubleItemAttr(item_key, mapping->check_attr);
        if (!val) {
          VLOG(100) << "cannot get double item attr [" << mapping->check_attr << "], item key: " << item_key;
          continue;
        }
        mapping->GenItemAttr(*val, context, item_key);
      }
    }
  }
}

bool CommonRecoTransformItemAttrEnricher::InitProcessor() {
  auto *mappings_config = config()->Get("mappings");
  if (!mappings_config || !mappings_config->IsArray()) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Missing \"mappings\" config or it is"
               << " not an array.";
    return false;
  }

  for (auto *mapping_cfg : mappings_config->array()) {
    if (!CheckMappingConfig(mapping_cfg)) {
      return false;
    }
  }

  return true;
}

bool CommonRecoTransformItemAttrEnricher::CheckMappingConfig(base::Json *mapping_cfg) {
  std::string output_attr = mapping_cfg->GetString("output_attr_name");
  if (output_attr.empty()) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Empty \"output_attr_name\"!";
    return false;
  }

  auto *mapping = attrs_mapping_.Get(output_attr);
  if (mapping == nullptr) {
    mapping = new AttrMapping();
    attrs_mapping_.Insert(output_attr, mapping);
  } else {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Duplicate \"output_attr_name\": " << output_attr;
    return false;
  }
  mapping->output_name = output_attr;

  auto *rules_config = mapping_cfg->Get("rules");
  if (!rules_config) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Missing config \"rules\"!";
    return false;
  }
  if (!rules_config->IsArray()) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! \"rules\" should be"
                  " an array!";
    return false;
  }
  mapping->rules_config = new base::Json(base::StringToJson(rules_config->ToString()));

  mapping->check_attr = mapping_cfg->GetString("check_attr_name");
  if (mapping->check_attr.empty()) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Empty \"check_attr_name\"!";
    return false;
  }

  std::string check_attr_type = mapping_cfg->GetString("check_attr_type");
  mapping->check_attr_type = RecoUtil::ParseAttrType(check_attr_type);
  if (mapping->check_attr_type != AttrType::INT && mapping->check_attr_type != AttrType::FLOAT &&
      mapping->check_attr_type != AttrType::STRING) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Missing \"check_attr_type\" or it's not"
               << " int/double/string!";
    return false;
  }

  std::string output_attr_type = mapping_cfg->GetString("output_attr_type");
  mapping->output_type = RecoUtil::ParseAttrType(output_attr_type);
  if (mapping->output_type != AttrType::INT && mapping->output_type != AttrType::FLOAT &&
      mapping->output_type != AttrType::STRING) {
    LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
               << " init failed! Missing \"output_attr_type\" or it's not"
               << " int/double/string!";
    return false;
  }

  auto *default_value = mapping_cfg->Get("output_default_value");
  if (default_value) {
    mapping->has_default_value = true;
    bool mismatch = true;
    if (mapping->output_type == AttrType::INT && default_value->IntValue(&mapping->default_int_value)) {
      mismatch = false;
    } else if (mapping->output_type == AttrType::FLOAT &&
               default_value->NumberValue(&mapping->default_double_value)) {
      mismatch = false;
    } else if (mapping->output_type == AttrType::STRING &&
               default_value->StringValue(&mapping->default_string_value)) {
      mismatch = false;
    } else {
      mismatch = true;
    }
    if (mismatch) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! The type of \"default_value\" [" << default_value->ToString()
                 << "] is not matched with config \"output_attr_type\"!";
      return false;
    }
  }

  for (auto *rule : mapping->rules_config->array()) {
    auto value_set = rule->Get("check_values");
    auto value_range = rule->Get("check_range");
    if (!value_set && !value_range) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing \"check_values\" or"
                 << " \"check_range\" config.";
      return false;
    }
    if (value_set && value_range) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Cannot set both \"check_values\" and"
                 << " \"check_range\" at the same time.";
      return false;
    }
    if (mapping->check_attr_type == AttrType::FLOAT && !value_range) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing \"check_range\" when"
                 << " \"check_attr_type\" is double.";
      return false;
    }
    if (mapping->check_attr_type == AttrType::STRING && !value_set) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing \"check_values\" when"
                 << " \"check_attr_type\" is string.";
      return false;
    }
    if (value_set && !value_set->IsArray()) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing config \"check_values\" or it's"
                 << " not an array";
      return false;
    }
    if (value_range && !value_range->IsObject()) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing config \"check_range\" or it's"
                 << " not a dict";
      return false;
    }

    auto *output_value = rule->Get("output_value");
    if (!output_value ||
        (!output_value->IsInteger() && !output_value->IsDouble() && !output_value->IsString())) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! Missing \"output_value\" or it's not an"
                 << " int/double/string value!";
      return false;
    }

    if ((mapping->output_type == AttrType::INT && !output_value->IsInteger()) ||
        (mapping->output_type == AttrType::FLOAT && !output_value->IsDouble() &&
         !output_value->IsInteger()) ||
        (mapping->output_type == AttrType::STRING && !output_value->IsString())) {
      LOG(ERROR) << "CommonRecoTransformItemAttrEnricher"
                 << " init failed! The type of \"output_value\" [" << output_value->ToString()
                 << "] is not matched with config \"output_attr_type\"!";
      return false;
    }
  }

  return true;
}

void CommonRecoTransformItemAttrEnricher::BuildAttrsMapping(ReadableRecoContextInterface *context) {
  for (auto *mapping : attrs_mapping_) {
    mapping->ClearMappingValue();
    for (int i = 0; i < mapping->rules_config->size(); ++i) {
      ResolveValueSetMappingRules(mapping, i, context);
      ResolveValueRangeMappingRules(mapping, i, context);
    }
  }
}

void CommonRecoTransformItemAttrEnricher::ResolveValueSetMappingRules(AttrMapping *mapping, int index,
                                                                      ReadableRecoContextInterface *context) {
  auto *rule = mapping->rules_config->array()[index];
  auto *value_set = rule->Get("check_values");
  if (!value_set) {
    return;
  }

  auto *output_value = rule->Get("output_value");
  int single_limit = rule->GetInt("single_limit", -1);
  int total_limit = rule->GetInt("total_limit", -1);
  int total_insert = 0;
  for (auto *value : value_set->array()) {
    if (total_limit >= 0 && total_insert >= total_limit) {
      VLOG(100) << "value mapping total_limit reached: " << total_limit
                << ", you may lose some mapping values!";
      break;
    }
    int single_insert = 0;
    if (value->IsInteger()) {
      // 整数类型
      mapping->InsertValueMapping(value->IntValue(-1), output_value);
    } else if (value->IsString()) {
      if (auto op = RecoUtil::ExtractCommonAttrFromExpr(value)) {
        absl::string_view common_attr = *op;
        // 如果是 {{ }} 格式，则按 CommonAttr 处理
        // 依次从 int/string/int_list/string_list attr 里尝试获取
        if (auto p = context->GetIntCommonAttr(common_attr)) {
          mapping->InsertValueMapping(*p, output_value);
          single_insert++;
          total_insert++;
        } else if (auto p = context->GetStringCommonAttr(common_attr)) {
          mapping->InsertValueMapping(*p, output_value);
          single_insert++;
          total_insert++;
        } else if (auto p = context->GetIntListCommonAttr(common_attr)) {
          for (const int64 key : *p) {
            if (total_limit >= 0 && total_insert >= total_limit) {
              VLOG(100) << "value mapping total_limit reached: " << total_limit << ", int list common_attr "
                        << common_attr << " is partially read";
              break;
            }
            if (single_limit >= 0 && single_insert >= single_limit) {
              VLOG(100) << "value mapping single_limit reached: " << single_limit << ", int list common_attr "
                        << common_attr << " is partially read";
              break;
            }
            mapping->InsertValueMapping(key, output_value);
            single_insert++;
            total_insert++;
          }
        } else if (auto p = context->GetStringListCommonAttr(common_attr)) {
          for (auto key : *p) {
            if (total_limit >= 0 && total_insert >= total_limit) {
              VLOG(100) << "value mapping total_limit reached: " << total_limit
                        << ", string list common_attr " << common_attr << " is partially read";
              break;
            }
            if (single_limit >= 0 && single_insert >= single_limit) {
              VLOG(100) << "value mapping single_limit reached: " << single_limit
                        << ", string list common_attr " << common_attr << " is partially read";
              break;
            }
            mapping->InsertValueMapping(key, output_value);
            single_insert++;
            total_insert++;
          }
        }
      } else {
        // 否则当一个普通值
        std::string value_text = value->StringValue();
        mapping->InsertValueMapping(value_text, output_value);
        single_insert++;
        total_insert++;
      }
    }
  }
}

void CommonRecoTransformItemAttrEnricher::ResolveValueRangeMappingRules(
    AttrMapping *mapping, int index, ReadableRecoContextInterface *context) {
  auto *rule = mapping->rules_config->array()[index];
  auto *value_range = rule->Get("check_range");
  if (!value_range) {
    return;
  }
  ValueRange range;
  range.has_lower_bound =
      TryGetDoubleProcessorParameter(context, value_range->Get("lower_bound"), &range.lower_bound, true);
  range.has_upper_bound =
      TryGetDoubleProcessorParameter(context, value_range->Get("upper_bound"), &range.upper_bound, true);
  auto *output_value = rule->Get("output_value");
  mapping->AddValueRange(range, output_value);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTransformItemAttrEnricher, CommonRecoTransformItemAttrEnricher)

}  // namespace platform
}  // namespace ks
