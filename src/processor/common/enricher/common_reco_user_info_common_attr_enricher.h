#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_proto/proto/reco_user_profile.kess.grpc.pb.h"

namespace ks {
namespace platform {

class CommonRecoUserInfoCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoUserInfoCommonAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 protected:
  // user 侧属性填充不允许分块处理
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  bool InitProcessor() override {
    kess_service_ = config()->GetString("kess_service");
    if (kess_service_.empty()) {
      LOG(ERROR) << "CommonRecoUserInfoCommonAttrEnricher init failed! \"kess_service\" cannot be empty!";
      return false;
    }

    service_group_ = config()->GetString("service_group", "PRODUCTION");
    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoUserInfoCommonAttrEnricher init failed! \"timeout_ms\" must be more than 0";
      return false;
    }

    user_id_ = config()->Get("user_id");
    device_id_ = config()->Get("device_id");

    save_to_common_attr_ = config()->GetString("save_to_common_attr");
    if (save_to_common_attr_.empty()) {
      LOG(ERROR)
          << "CommonRecoUserInfoCommonAttrEnricher init failed! \"save_to_common_attr\" cannot be empty!";
      return false;
    }

    biz_name_ = config()->GetString("biz_name");

    product_ = config()->GetInt("product", 0);

    request_info_ = "kess_service: " + kess_service_ + ", kess_cluster: " + service_group_ +
                    ", timeout_ms: " + std::to_string(timeout_ms_) + ", biz_name: " + biz_name_ +
                    ", product: " + std::to_string(product_) +
                    ", save_to_common_attr_:" + save_to_common_attr_;

    return true;
  }

 private:
  int timeout_ms_ = 200;
  int product_ = 0;

  std::string kess_service_;
  std::string service_group_;
  std::string biz_name_;
  std::string request_info_;

  const base::Json *user_id_ = nullptr;
  const base::Json *device_id_ = nullptr;

  kuaishou::reco::RecoUserProfileRequest request_;
  kuaishou::reco::RecoUserProfileResponse response_;

  std::string save_to_common_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoUserInfoCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
