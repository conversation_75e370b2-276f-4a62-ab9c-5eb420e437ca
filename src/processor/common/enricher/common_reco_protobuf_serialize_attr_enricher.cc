#include "dragon/src/processor/common/enricher/common_reco_protobuf_serialize_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoProtobufSerializeAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
  if (!from_common_attr_.empty()) {
    const auto *message = context->GetPtrCommonAttr<google::protobuf::Message>(from_common_attr_);
    if (!message) {
      CL_LOG_ERROR("serialize_protobuf_attr", "attr_not_found:" + from_common_attr_)
          << "enrich from protobuf failed as null pointer: " << from_common_attr_;
      return;
    }

    thread_local std::string payload;
    payload.clear();
    if (message->SerializeToString(&payload)) {
      context->SetStringCommonAttr(serialize_to_common_attr_, std::move(payload));
    } else {
      CL_LOG_ERROR("serialize_protobuf_attr", "serialize_pb_fail:" + serialize_to_common_attr_)
          << "failed to serialize pb message to common attr " << serialize_to_common_attr_;
    }
  } else {
    auto *item_attr_accessor = context->GetItemAttrAccessor(from_item_attr_);
    auto *serialize_attr_accessor =
        serialize_to_item_attr_.empty() ? nullptr : context->GetItemAttrAccessor(serialize_to_item_attr_);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      const auto *message = result.GetPtrAttr<google::protobuf::Message>(item_attr_accessor);
      if (!message) {
        CL_LOG_ERROR_EVERY("serialize_protobuf_attr", "attr_not_found:" + from_item_attr_, 1000)
            << "enrich from protobuf failed as null pointer: " << from_item_attr_ << " (" << item_key << ")";
        return;
      }
      thread_local std::string payload;
      payload.clear();
      if (message->SerializeToString(&payload)) {
        result.SetStringAttr(serialize_attr_accessor, std::move(payload));
      } else {
        CL_LOG_ERROR_EVERY("serialize_protobuf_attr", "serialize_pb_fail:" + serialize_to_item_attr_, 1000)
            << "failed to serialize pb message to item attr " << serialize_to_item_attr_;
      }
    });
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoProtobufSerializeAttrEnricher,
                 CommonRecoProtobufSerializeAttrEnricher)

}  // namespace platform
}  // namespace ks
