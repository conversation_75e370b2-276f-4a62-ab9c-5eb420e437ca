#include "dragon/src/processor/common/enricher/common_reco_attr_default_value_enricher.h"

#include <utility>
#include <vector>

namespace ks {
namespace platform {

void CommonRecoAttrDefaultValueEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  for (auto &default_value_config : item_default_value_configs_) {
    default_value_config.accessor = context->GetItemAttrAccessor(default_value_config.attr_name);
    if (default_value_config.accessor->value_type != AttrType::UNKNOWN &&
        default_value_config.accessor->value_type != default_value_config.attr_type) {
      CL_LOG_ERROR("set_attr_default_value", "type_mismatch")
          << "type_mismatch for item attr: " << default_value_config.attr_name
          << " which has value_type: " << static_cast<int>(default_value_config.accessor->value_type)
          << " setting default value_type: " << static_cast<int>(default_value_config.attr_type);
      continue;
    }
    SetItemDefaultValues(context, default_value_config);
  }
}

void CommonRecoAttrDefaultValueEnricher::SetItemDefaultValues(
    MutableRecoContextInterface *context, const DefaultValueConfig &default_value_config) {
  auto accessor = default_value_config.accessor;
  auto &attr_name = default_value_config.attr_name;
  auto value_config = default_value_config.value_config;
  bool clear_existing_value = default_value_config.clear_existing_value;
  if (clear_existing_value) {
    accessor->ClearAllValues();
  }
  if (accessor) {
    switch (default_value_config.attr_type) {
      case AttrType::INT: {
        int64 int_value = 0;
        if (TryGetIntProcessorParameter(context, value_config, &int_value)) {
          accessor->SetDefaultIntValue(int_value);
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_int_value:" + attr_name)
              << "get value is not int for item attr: " << attr_name;
        }
        break;
      }
      case AttrType::FLOAT: {
        double float_value = 0;
        if (TryGetDoubleProcessorParameter(context, value_config, &float_value, true)) {
          accessor->SetDefaultDoubleValue(float_value);
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_double_value:" + attr_name)
              << "get value is not double for item attr: " << attr_name;
        }
        break;
      }
      case AttrType::STRING: {
        std::string string_value;
        if (TryGetStringProcessorParameter(context, value_config, &string_value)) {
          accessor->SetDefaultStringValue(string_value);
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_string_value:" + attr_name)
              << "get value is not string for item attr: " << attr_name;
        }
        break;
      }
      case AttrType::INT_LIST: {
        std::vector<int64> list_value;
        if (TryGetIntListProcessorParameter(context, value_config, &list_value)) {
          accessor->SetDefaultIntListValue(std::move(list_value));
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_int_list_value:" + attr_name)
              << "get value is not int list for item attr: " << attr_name;
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        std::vector<double> list_value;
        if (TryGetDoubleListProcessorParameter(context, value_config, &list_value)) {
          accessor->SetDefaultDoubleListValue(std::move(list_value));
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_double_list_value:" + attr_name)
              << "get value is not double list for item attr: " << attr_name;
        }
        break;
      }
      case AttrType::STRING_LIST: {
        std::vector<absl::string_view> list_value;
        if (TryGetStringListProcessorParameter(context, value_config, &list_value)) {
          std::vector<std::string> string_list_value(list_value.begin(), list_value.end());
          accessor->SetDefaultStringListValue(std::move(string_list_value));
        } else {
          CL_LOG_ERROR("set_attr_default_value", "not_string_list_value:" + attr_name)
              << "get value is not string list for item attr: " << attr_name;
        }
        break;
      }
      default:
        break;
    }
  } else {
    CL_LOG_ERROR("set_attr_default_value", "attr_not_exist:" + attr_name)
        << "attr not exist for item attr: " << attr_name;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAttrDefaultValueEnricher, CommonRecoAttrDefaultValueEnricher)

}  // namespace platform
}  // namespace ks
