#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

namespace format_utils {
// reference: https://www.cplusplus.com/reference/cstdio/fprintf/
class StringFormatHelper {
 public:
  enum Type { UNSUPPORTED = -1, NONE = 0, INT64 = 1, UINT64, DOUBLE, STRING };
  /**
   * @brief 将 format_string 切成多个 sub_format_string, 每个 sub_format_string 最多包含一个 specifier
   *
   * 例如 "Name %d [%-10.10s]\n" 将被分割成 [""]
   *
   * @param format_string
   * @param sub_format_strings
   */
  static bool Split(const std::string &format_string, std::vector<std::string> *sub_format_strings,
                    std::vector<Type> *sub_types);

  static Type GetType(const std::string &length, const std::string &specifier);
};
}  // namespace format_utils

class CommonRecoStringFormatEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoStringFormatEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  using Type = format_utils::StringFormatHelper::Type;

  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    format_string_ = config()->GetString("format_string", "");
    if (format_string_.empty()) {
      LOG(ERROR) << "CommonRecoStringFormatEnricher init failed! `format_string` not set or empty.";
      return false;
    }

    if (!format_utils::StringFormatHelper::Split(format_string_, &sub_format_strings_, &sub_types_) ||
        sub_format_strings_.size() != sub_types_.size()) {
      LOG(ERROR) << "CommonRecoStringFormatEnricher init failed! `format_string` cannot be parsed.";
      return false;
    }

    const auto *input_attrs_config = config()->Get("input_attrs");
    if (!input_attrs_config || !input_attrs_config->IsArray()) {
      LOG(ERROR) << "CommonRecoStringFormatEnricher init failed! `input_attrs` should be an array.";
      return false;
    }

    std::string attr;
    for (int i = 0; i < input_attrs_config->size(); ++i) {
      if (!input_attrs_config->GetString(i, &attr)) {
        LOG(ERROR) << "CommonRecoStringFormatEnricher init failed! the `" << i
                   << "` element of `input_attrs` is not string";
        return false;
      }
      input_attrs_.push_back(attr);
    }

    int32_t count_field = 0;
    for (int i = 0; i < sub_types_.size(); ++i) {
      auto type = sub_types_[i];
      if (type < 0) {
        LOG(ERROR) << "CommonRecoStringFormatEnricher init failed!"
                   << " unsupported part found in `format_string`(" << sub_format_strings_[i] << ")";
        return false;
      }
      if (type > 0) {
        count_field += 1;
      }
    }
    if (count_field != input_attrs_.size()) {
      LOG(ERROR) << "CommonRecoStringFormatEnricher init failed!"
                 << " number of specifier in `format_string`(" << count_field
                 << ") != size of `input_attrs`(" << input_attrs_.size() << ")";
      return false;
    }
    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoStringFormatEnricher init failed!"
                 << " Missing 'output_attr' config.";
      return false;
    }
    return true;
  }

  bool DoFormat(MutableRecoContextInterface *context, const CommonRecoResult *result = nullptr,
                const std::vector<ItemAttr *> *src_accs = nullptr, ItemAttr *tgt_acc = nullptr);

  int64_t GetInt64(MutableRecoContextInterface *context, int32_t pos, const CommonRecoResult *result,
                   const ItemAttr *src_acc);

  double GetDouble(MutableRecoContextInterface *context, int32_t pos, const CommonRecoResult *result,
                   const ItemAttr *src_acc);

  const char *GetString(MutableRecoContextInterface *context, int32_t pos, const CommonRecoResult *result,
                        const ItemAttr *src_acc);

 private:
  bool is_common_attr_;
  std::string format_string_;
  std::vector<std::string> input_attrs_;
  std::string output_attr_;
  std::vector<std::string> sub_format_strings_;
  std::vector<Type> sub_types_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStringFormatEnricher);
};

}  // namespace platform
}  // namespace ks
