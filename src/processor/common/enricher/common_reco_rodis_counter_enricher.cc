#include "dragon/src/processor/common/enricher/common_reco_rodis_counter_enricher.h"

#include <algorithm>
#include <iterator>
#include <vector>

namespace ks {
namespace platform {
void CommonRecoRodisCounterEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  int64 failed_count = 0;
  if (group_by_.empty()) {
    failed_count = GetRodisCounter(context, begin, end);
  } else {
    failed_count = GetRodisCounterByGroup(context, begin, end);
  }
  if (failed_count > 0) {
    CL_LOG_WARNING_COUNT(failed_count, "rodis_counter", "Partial results failed")
        << "CommonRecoRodisCounterEnricher Get Rodis Counter Partial results failed! rodis_kess_name: "
        << rodis_kess_name_ << " domain: " << rodis_domain_ << " number of failures: " << failed_count;
  }
}

int64 CommonRecoRodisCounterEnricher::GetRodisCounter(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  ids_.clear();
  counts_.clear();
  ids_.reserve(std::distance(begin, end));

  std::for_each(begin, end, [this](const CommonRecoResult &result) mutable { ids_.push_back(result.key()); });
  grpc::Status status = photo_count::PhotoCountService::getCountByIdsFrom(
      ids_, rodis_kess_name_, rodis_domain_, timeout_ms_, &counts_);
  if (!status.ok()) {
    CL_LOG_WARNING("rodis_counter", status.error_message())
        << "CommonRecoRodisCounterEnricher Get Rodis Counter Failed! rodis_kess_name: " << rodis_kess_name_
        << " domain: " << rodis_domain_ << " error_message: " << status.error_message();
    return 0;
  }
  int64 failed_count = 0;
  for (auto &action : action_list_) {
    action.save_count_accessor = context->GetItemAttrAccessor(action.save_count_to);
  }
  std::for_each(begin, end, [this, context, &failed_count](const CommonRecoResult &result) mutable {
    const auto &count_it = counts_.find(result.key());
    if (count_it != counts_.end()) {
      if (count_it->second.OK()) {
        for (const auto &action : action_list_) {
          context->SetIntItemAttr(
              result, action.save_count_accessor,
              count_it->second.getCountByActionChannels(action.action, action.channel_set));
        }
      } else {
        failed_count++;
      }
    } else {
      for (const auto &action : action_list_) {  // 未找到填 0
        context->SetIntItemAttr(result, action.save_count_accessor, 0);
      }
    }
  });

  return failed_count;
}

int64 CommonRecoRodisCounterEnricher::GetRodisCounterByGroup(MutableRecoContextInterface *context,
                                                             RecoResultConstIter begin,
                                                             RecoResultConstIter end) {
  ids_.clear();
  groups_counts_.clear();
  groups_set_.clear();
  ids_.reserve(std::distance(begin, end));

  ItemAttr *group_by_attr = context->GetItemAttrAccessor(group_by_);
  if (group_by_attr->value_type == AttrType::UNKNOWN) {
    CL_LOG_WARNING("get_rodis_counter", "get_group_by_failed")
        << "CommonRecoRodisCounterEnricher: get group_by attr failed, attr is null. group_by: " << group_by_;
    return 0;
  }

  if (group_by_attr->value_type != AttrType::STRING_LIST) {
    CL_LOG_WARNING("get_rodis_counter", "get_group_by_failed")
        << "CommonRecoRodisCounterEnricher: get group_by attr failed, attr is not string list. group_by: "
        << group_by_;
    return 0;
  }

  std::for_each(begin, end, [this, context, group_by_attr](const CommonRecoResult &result) mutable {
    ids_.push_back(result.key());
    const auto &item_groups = context->GetStringListItemAttr(result, group_by_attr);
    if (item_groups) {
      for (const auto &group : *item_groups) {
        groups_set_.insert(group);
      }
    }
  });

  std::vector<std::string> groups_list;
  groups_list.reserve(groups_set_.size());

  for (const auto &group : groups_set_) {
    groups_list.emplace_back(std::string(group));
  }

  grpc::Status status = photo_count::PhotoCountService::getCountByIdsFrom(
      ids_, groups_list, rodis_kess_name_, rodis_domain_, timeout_ms_, &groups_counts_);
  if (!status.ok()) {
    CL_LOG_WARNING("rodis_counter", status.error_message())
        << "CommonRecoRodisCounterEnricher Get Rodis Counter Failed! rodis_kess_name: " << rodis_kess_name_
        << " domain: " << rodis_domain_ << " error_message: " << status.error_message();
    return 0;
  }

  int64 failed_count = 0;
  for (auto &action : action_list_) {
    action.save_count_accessor = context->GetItemAttrAccessor(action.save_count_to);
  }

  std::for_each(
      begin, end, [this, context, &failed_count, group_by_attr](const CommonRecoResult &result) mutable {
        std::vector<std::vector<int64>> total_list;
        total_list.resize(action_list_.size());
        const auto &item_groups = context->GetStringListItemAttr(result, group_by_attr);
        if (item_groups) {
          for (auto &c : total_list) {
            c.resize(item_groups->size(), 0);
          }
          for (int j = 0; j < item_groups->size(); j++) {
            const auto &group = item_groups->at(j);
            const auto &count_it = groups_counts_.find({result.key(), std::string(group)});
            if (count_it == groups_counts_.end()) {
              continue;
            }
            if (count_it->second.OK()) {
              for (int i = 0; i < action_list_.size(); i++) {
                total_list[i][j] = count_it->second.getCountByActionChannels(action_list_[i].action,
                                                                             action_list_[i].channel_set);
              }
            } else {
              failed_count++;
            }
          }
        }
        for (int i = 0; i < action_list_.size(); i++) {
          context->SetIntListItemAttr(result, action_list_[i].save_count_accessor, std::move(total_list[i]));
        }
      });
  return failed_count;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisCounterEnricher, CommonRecoRodisCounterEnricher)
}  // namespace platform
}  // namespace ks
