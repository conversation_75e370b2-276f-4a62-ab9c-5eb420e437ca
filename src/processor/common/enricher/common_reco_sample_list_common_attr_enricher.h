#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/action/sample_list_kess_client.h"
#include "ks/reco_proto/action/sample_list.pb.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"

namespace ks {
namespace platform {

class CommonRecoSampleListCommonAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoSampleListCommonAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 protected:
  // user 侧属性填充不允许分块处理
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  bool InitProcessor() override {
    save_attr_names_to_attr_ = config()->GetString("save_attr_names_to_attr");
    if (save_attr_names_to_attr_.empty()) {
      LOG(ERROR) << "CommonRecoSampleListCommonAttrEnricher init failed! \"save_attr_names_to_attr\" cannot "
                 << "be empty!";
      return false;
    }

    attr_config_ = config()->GetString("attr_config");

    service_group_ = config()->GetString("service_group", "PRODUCTION");
    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoSampleListCommonAttrEnricher init failed! timeout_ms must be > 0";
      return false;
    }

    const auto *include_attrs_config = config()->Get("include_attrs");
    if (include_attrs_config && include_attrs_config->IsArray()) {
      for (const auto *a : include_attrs_config->array()) {
        include_attrs_.insert(a->StringValue());
      }
    }

    no_overwrite_ = config()->GetBoolean("no_overwrite", false);
    return true;
  }

  ks::action::SampleListKessClient *GetSampleListKessClient(const std::string &kess_service);

 private:
  std::string service_group_;
  int timeout_ms_ = 200;

  std::string attr_config_;
  std::string save_attr_names_to_attr_;
  std::unordered_set<std::string> include_attrs_;
  std::unordered_map<std::string, std::unique_ptr<ks::action::SampleListKessClient>>
      sample_list_kess_client_map_;
  kuaishou::reco::UserAttrsRequest sample_list_request_;
  bool no_overwrite_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoSampleListCommonAttrEnricher);
};

}  // namespace platform
}  // namespace ks
