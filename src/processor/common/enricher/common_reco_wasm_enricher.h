#pragma once

#include <iostream>
#include <string>
#include <vector>

#include "third_party/abseil/absl/container/flat_hash_map.h"
#include "third_party/abseil/absl/types/optional.h"
#include "third_party/wasmtime-cpp/include/wasmtime.hh"

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoWasmEnricher : public CommonRecoBaseEnricher {
 public:
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  int AddCommonAttrAndGetIndex(const std::string &common_attr);
  int AddItemAttrAndGetIndex(const std::string &item_attr);

 private:
  std::vector<std::string> common_attr_names_;
  std::vector<std::string> item_attr_names_;

  absl::flat_hash_map<std::string, int> common_attr_name_index_map_;
  absl::flat_hash_map<std::string, int> item_attr_name_index_map_;

  std::vector<CommonAttr *> common_attr_accessors_;
  std::vector<ItemAttr *> item_attr_accessors_;

  enum InOutAttrType {
    kCommonAttr,
    kItemAttr,
  };

  enum WatValueType {
    kUnknown,
    kI64,
    kF64,
    kString,
    kI64List,
    kF64List,
  };

  WatValueType GetValueTypeFromString(const std::string &str);

  struct InOutAttr {
    InOutAttrType type;
    std::string name;
    int accessor_index;
    std::string wasm_var;
    WatValueType value_type;
    int global_ptr = 0;

    std::string DebugString() const;
  };

  std::vector<InOutAttr> in_attrs_;
  std::vector<InOutAttr> out_attrs_;

  bool clear_attr_if_null_ = false;
  bool set_empty_attr_if_null_ = false;

  struct Call {
    std::string func_name;
    std::vector<int> params;
    std::vector<int> results;
  };

  std::vector<Call> calls_;

  wasmtime::Engine engine_;
  absl::optional<wasmtime::Module> module_;
  absl::optional<wasmtime::Store> store_;
  absl::optional<wasmtime::Instance> instance_;
  std::vector<wasmtime::Func> funcs_;
  absl::optional<wasmtime::Memory> memory_;
  absl::optional<wasmtime::Func> init_func_;
  absl::optional<wasmtime::Func> alloc_func_;
  absl::optional<int> item_num_ptr_;
};

}  // namespace platform
}  // namespace ks
