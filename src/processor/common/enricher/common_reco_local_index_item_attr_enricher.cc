#include "dragon/src/processor/common/enricher/common_reco_local_index_item_attr_enricher.h"

#include <algorithm>
#include <memory>
#include <utility>

#include "serving_base/util/shm_list.h"

namespace ks {
namespace platform {

// workaround gcc5 issue
thread_local std::unique_ptr<int> fake_tls1;

bool CommonRecoLocalIndexItemAttrEnricher::EnrichInit(MutableRecoContextInterface *context,
                                                      const RecoResultConstIter &begin,
                                                      const RecoResultConstIter &end,
                                                      std::vector<CommonRecoResult> *additional_items,
                                                      int *total_item_num) {
  item_miss_tag_accessors_ = nullptr;
  if (!item_miss_tag_.empty()) {
    item_miss_tag_accessors_ = context->GetItemAttrAccessor(item_miss_tag_);
  }

  if (attr_name_list_.empty()) {
    CL_LOG(WARNING) << "get item attr cancelled: emtpy attr list";
    return false;
  }

  if (attr_accessors_.empty()) {
    for (const auto &attr_name : attr_name_list_) {
      attr_accessors_.push_back(context->GetItemAttrAccessor(attr_name));
    }
  }

  if (include_browse_set_items_) {
    auto browsed_items = context->GetLatestBrowsedItems(include_browse_set_item_count_);
    std::transform(browsed_items.begin(), browsed_items.end(), std::back_inserter(*additional_items),
                   [context](uint64 item_key) { return context->NewCommonRecoResult(item_key); });
  }

  for (const auto &attr_name : source_common_attrs_) {
    switch (context->GetCommonAttrType(attr_name)) {
      case AttrType::INT_LIST: {
        auto p_list = context->GetIntListCommonAttr(attr_name);
        if (p_list) {
          std::transform(p_list->begin(), p_list->end(), std::back_inserter(*additional_items),
                         [context](uint64 item_key) { return context->NewCommonRecoResult(item_key); });
        }
        break;
      }
      case AttrType::INT: {
        auto p_int = context->GetIntCommonAttr(attr_name);
        if (p_int) additional_items->emplace_back(context->NewCommonRecoResult(*p_int));
        break;
      }
      default:
        VLOG(100) << "cannot find int/int_list common_attr: " << attr_name;
        break;
    }
  }

  for (const auto &attr_name : source_item_attrs_) {
    auto *attr_accessor = context->GetItemAttrAccessor(attr_name);
    switch (context->GetItemAttrType(attr_name)) {
      case AttrType::INT_LIST:
        std::for_each(begin, end, [context, attr_accessor, additional_items](const CommonRecoResult &result) {
          auto p_list = result.GetIntListAttr(attr_accessor);
          if (p_list) {
            std::transform(p_list->begin(), p_list->end(), std::back_inserter(*additional_items),
                           [context](uint64 item_key) { return context->NewCommonRecoResult(item_key); });
          }
        });
        break;
      case AttrType::INT:
        std::for_each(begin, end, [context, attr_accessor, additional_items](const CommonRecoResult &result) {
          auto p_int = result.GetIntAttr(attr_accessor);
          if (p_int) additional_items->emplace_back(context->NewCommonRecoResult(*p_int));
        });
        break;
      default:
        VLOG(100) << "no int/int_list item_attr: " << attr_name;
        break;
    }
  }

  *total_item_num = additional_items->size() + (include_reco_results_ ? std::distance(begin, end) : 0);
  if (*total_item_num == 0) {
    CL_LOG(INFO) << "get item attr cancelled: emtpy item list";
    return false;
  }

  if (!item_key_attr_.empty()) {
    item_key_attr_accessor_ = context->GetItemAttrAccessor(item_key_attr_);
  }

  return true;
}

void CommonRecoLocalIndexItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  auto *index_manager = CommonRecoIndexManager::Singleton()->GetIndexManager();
  if (!index_manager) {
    CL_LOG_ERROR_EVERY("local_index", "no_index_manager", 1000)
        << "cannot get item attrs from local index, no index_manager available!";
    return;
  }

  auto *current_index = index_manager->GetCurrent();
  if (!current_index) {
    CL_LOG_ERROR_EVERY("local_index", "no_current_index", 1000)
        << "cannot get item attrs from local index, no current index available!";
    return;
  }

  thread_local std::vector<CommonRecoResult> additional_items;
  additional_items.clear();

  int total_item_num = 0;

  if (!EnrichInit(context, begin, end, &additional_items, &total_item_num)) {
    return;
  }

  std::vector<CommonIndexEnum::AttrType> attr_types(attr_name_list_.size(), CommonIndexEnum::UNKNOW_ATTR);
  for (int i = 0; i < attr_name_list_.size(); ++i) {
    const auto &attr_name = attr_name_list_[i];
    attr_types[i] = attr_type_list_.size() > i
                        ? RecoUtil::CastCommonIndexAttrType(attr_type_list_[i])
                        : static_cast<CommonIndexEnum::AttrType>(index_manager->GetAttrTypeValue(attr_name));
  }

  missing_item_count_ = 0;
  missing_item_key_ = 0;
  for (auto &counter : attr_perf_count_) {
    counter.Clear();
  }

  int64 start_ts = base::GetTimestamp();

  if (include_reco_results_) {
    std::for_each(begin, end, [this, current_index, &attr_types](const CommonRecoResult &result) {
      GetItemAttrForItem(current_index, result, attr_types);
    });
  }

  for (const auto &result : additional_items) {
    GetItemAttrForItem(current_index, result, attr_types);
  }

  int64 duration = base::GetTimestamp() - start_ts;
  EnrichEndPerf(context, total_item_num, duration);
}

void CommonRecoLocalIndexItemAttrEnricher::EnrichEndPerf(MutableRecoContextInterface *context,
                                                         int total_item_num, int64 duration) {
  if (missing_item_count_ > 0) {
    CL_LOG_EVERY_N(WARNING, 1000) << missing_item_count_
                                  << " items not found in index, item_key: " << missing_item_key_
                                  << ", item_id: " << Util::GetId(missing_item_key_)
                                  << ", item_type: " << Util::GetType(missing_item_key_)
                                  << ", processor: " << GetName();
  }

  int hit_count = total_item_num - missing_item_count_;
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      1000.0 * hit_count / total_item_num, kPerfNs, "forward_index.item_hit",
      GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
  base::perfutil::PerfUtilWrapper::IntervalLogStash(total_item_num, kPerfNs, "forward_index.item_total",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  for (int i = 0; i < attr_name_list_.size(); ++i) {
    const auto &attr = attr_name_list_[i];
    const auto &perf_counter = attr_perf_count_[i];
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        1000.0 * perf_counter.attr_count / std::max(hit_count, 1), kPerfNs, "forward_index.attr_hit",
        GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        1000.0 * perf_counter.attr_total_size / std::max(perf_counter.attr_count, 1), kPerfNs,
        "forward_index.attr_size", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
        attr);
    if (perf_counter.attr_type_mismatch_count > 0) {
      base::perfutil::PerfUtilWrapper::CountLogStash(
          perf_counter.attr_type_mismatch_count, kPerfNs, "error.local_index",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), "attr_type_mismatch:" + attr);
      CL_LOG(ERROR) << "item attr " << attr << " type mismatch" << RecoUtil::GetRequestInfoForLog(context);
    }
    if (perf_counter.unknown_attr_count > 0) {
      base::perfutil::PerfUtilWrapper::CountLogStash(
          perf_counter.unknown_attr_count, kPerfNs, "error.local_index", GlobalHolder::GetServiceIdentifier(),
          context->GetRequestType(), "unknown_attr_type:" + attr);
      CL_LOG(ERROR) << "unknown attr_type for " << attr << RecoUtil::GetRequestInfoForLog(context);
    }
  }

  CL_LOG(INFO) << "get item attrs from local index finished, item_num: " << total_item_num
               << ", attr_num: " << attr_name_list_.size() << ", time cost: " << duration / 1000.0 << " ms";
}

void CommonRecoLocalIndexItemAttrEnricher::GetItemAttrForItem(
    adsindexing::Index *current_index, const CommonRecoResult &result,
    const std::vector<CommonIndexEnum::AttrType> &attr_types) {
  uint64 item_key = result.item_key;
  if (item_key_attr_accessor_) {
    auto item_key_p = result.GetIntAttr(item_key_attr_accessor_);
    if (item_key_p) {
      item_key = *item_key_p;
    } else {
      ++missing_item_count_;
      // item attr 没有取到 id 时，默认 key = -1
      missing_item_key_ = -1;
      return;
    }
  }

  auto doc_local_id = current_index->DocLocalIDFromKeySign(item_key);
  if (doc_local_id == -1) {
    if (item_miss_tag_accessors_) {
      result.SetIntAttr(item_miss_tag_accessors_, 1);
    }
    ++missing_item_count_;
    missing_item_key_ = item_key;
    return;
  }
  if (item_miss_tag_accessors_) {
    result.SetIntAttr(item_miss_tag_accessors_, 0);
  }

  int64 int_val = 0;
  float float_val = 0.0;
  thread_local std::string str_val;
  base::ConstArray<int64> int_list;
  base::ConstArray<float> float_list;
  adsindexing::CustomerAttrValue custom_attr_value;

  for (int i = 0; i < attr_name_list_.size(); ++i) {
    if (!current_index->GetDocCustomerAttr(attr_keysign_list_[i], doc_local_id, &custom_attr_value)) {
      continue;
    }

    const auto &attr_accessor = attr_accessors_[i];
    auto &perf_counter = attr_perf_count_[i];

    if (max_value_bytes_ > 0 && custom_attr_value.size > max_value_bytes_) {
      CL_LOG_WARNING_EVERY("local_index", "value_too_large:" + attr_accessor->name(), 1000)
          << "skipped attr " << attr_accessor->name() << " for item_id=" << result.GetId()
          << " due to value bytes too large: " << custom_attr_value.size << " > " << max_value_bytes_;
      continue;
    }

    switch (attr_types[i]) {
      case CommonIndexEnum::INT_ATTR:
        if (custom_attr_value.GetIntValue(&int_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetIntAttr(attr_accessor, int_val, no_overwrite_);
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      case CommonIndexEnum::FLOAT_ATTR:
        if (custom_attr_value.GetFloatValue(&float_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetDoubleAttr(attr_accessor, float_val, no_overwrite_);
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      case CommonIndexEnum::STRING_ATTR:
        if (custom_attr_value.GetStringValue(&str_val)) {
          ++perf_counter.attr_count;
          ++perf_counter.attr_total_size;
          result.SetStringAttr(attr_accessor, std::move(str_val), no_overwrite_);
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      case CommonIndexEnum::INT_LIST_ATTR:
        if (custom_attr_value.type == adsindexing::CustomerAttrValueType::kCustomerStringValue) {
          int_list.SetData(custom_attr_value.data, custom_attr_value.size);
          std::vector<int64> val(int_list.begin(), int_list.end());
          result.SetIntListAttr(attr_accessor, std::move(val), no_overwrite_);
          ++perf_counter.attr_count;
          perf_counter.attr_total_size += int_list.size;
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      case CommonIndexEnum::FLOAT_LIST_ATTR:
        if (custom_attr_value.type == adsindexing::CustomerAttrValueType::kCustomerStringValue) {
          float_list.SetData(custom_attr_value.data, custom_attr_value.size);
          std::vector<double> val(float_list.begin(), float_list.end());
          result.SetDoubleListAttr(attr_accessor, std::move(val), no_overwrite_);
          ++perf_counter.attr_count;
          perf_counter.attr_total_size += float_list.size;
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      case CommonIndexEnum::STRING_LIST_ATTR:
        if (custom_attr_value.type == adsindexing::CustomerAttrValueType::kCustomerStringValue) {
          std::vector<std::string> val;
          if (custom_attr_value.size > 0) {
            const auto *shm_list =
                base::ShmList<std::string, int>::Read(custom_attr_value.data, custom_attr_value.size);
            if (!shm_list) {
              CL_LOG_WARNING_EVERY("local_index", "extract_string_list_fail:" + attr_accessor->name(), 1000)
                  << "skipped string_list attr " << attr_accessor->name() << " for item_id=" << result.GetId()
                  << " due to value bytes init ShmList failed, value_size=" << custom_attr_value.size;
              continue;
            }
            val.reserve(shm_list->value_size());
            for (int i = 0; i < shm_list->value_size(); ++i) {
              base::Slice slice = shm_list->slice(i);
              if (max_value_bytes_ > 0 && slice.size() > max_value_bytes_) {
                CL_LOG_WARNING_EVERY("local_index", "value_too_large:" + attr_accessor->name(), 1000)
                    << "skipped one string in string_list attr " << attr_accessor->name()
                    << " for item_id=" << result.GetId() << " due to value bytes too large: " << slice.size()
                    << " > " << max_value_bytes_;
                val.emplace_back("");
              } else {
                val.emplace_back(slice.data(), slice.size());
              }
            }
          }
          result.SetStringListAttr(attr_accessor, std::move(val), no_overwrite_);
          ++perf_counter.attr_count;
          perf_counter.attr_total_size += val.size();
        } else {
          ++perf_counter.attr_type_mismatch_count;
        }
        break;

      default:
        ++perf_counter.unknown_attr_count;
        break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLocalIndexItemAttrEnricher, CommonRecoLocalIndexItemAttrEnricher)

}  // namespace platform
}  // namespace ks
