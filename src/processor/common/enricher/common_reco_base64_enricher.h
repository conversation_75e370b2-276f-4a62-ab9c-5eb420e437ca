#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoBase64Enricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoBase64Enricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    const std::string mode = config()->GetString("mode");
    if (mode.empty()) {
      LOG(ERROR) << "CommonRecoBase64Enricher init failed!"
                 << " Missing 'mode' config.";
      return false;
    }
    if (mode == "encode") {
      is_encoding_ = true;
    } else if (mode == "decode") {
      is_encoding_ = false;
    } else {
      LOG(ERROR) << "CommonRecoBase64Enricher init failed!"
                 << " 'mode' not supported: " << mode;
      return false;
    }
    input_attr_ = config()->GetString("input_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoBase64Enricher init failed!"
                 << " Missing 'input_attr' config.";
      return false;
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoBase64Enricher init failed!"
                 << " Missing 'output_attr' config.";
      return false;
    }
    return true;
  }

  bool ProcessStrAttr(MutableRecoContextInterface *context, const CommonRecoResult *result = nullptr,
                      ks::platform::ItemAttr *src_acc = nullptr, ks::platform::ItemAttr *tgt_acc = nullptr);

  bool ProcessStrListAttr(MutableRecoContextInterface *context, const CommonRecoResult *result = nullptr,
                          ks::platform::ItemAttr *src_acc = nullptr,
                          ks::platform::ItemAttr *tgt_acc = nullptr);

  bool ProcessMsgAttr(MutableRecoContextInterface *context, const CommonRecoResult *result = nullptr,
                      ks::platform::ItemAttr *src_acc = nullptr, ks::platform::ItemAttr *tgt_acc = nullptr);

  bool Base64Encode(const char *input, size_t size, std::string *encoded);
  bool Base64Decode(const char *input, size_t size, std::string *decoded);
  bool SerializeToString(const google::protobuf::Message *message, std::string *val);

 private:
  bool is_common_attr_;
  bool is_encoding_;
  std::string input_attr_;
  std::string output_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBase64Enricher);
};

}  // namespace platform
}  // namespace ks
