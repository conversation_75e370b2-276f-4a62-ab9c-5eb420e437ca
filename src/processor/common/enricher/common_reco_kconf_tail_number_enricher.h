#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kconf/kconf.h"

namespace ks {
namespace platform {

class CommonRecoKconfTailNumberEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoKconfTailNumberEnricher() = default;

  bool IsAsync() const override {
    return false;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    kconf_key_ = config()->GetString("kconf_key", "");
    if (kconf_key_.empty()) {
      LOG(ERROR) << "CommonRecoKconfTailNumberEnricher init failed. Missing config `kconf_key`";
      return false;
    }
    ks_config_ = ks::infra::KConf().Get(kconf_key_, std::make_shared<ks::infra::TailNumber>());
    test_value_ = config()->Get("test_value");
    if (!test_value_) {
      LOG(ERROR) << "CommonRecoKconfTailNumberEnricher init failed. Missing config `test_value`";
      return false;
    }
    output_attr_name_ = config()->GetString("output_to", "tail_number_output");
    return true;
  };

  std::string kconf_key_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<ks::infra::TailNumber>>> ks_config_;
  const base::Json *test_value_ = nullptr;
  std::string output_attr_name_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoKconfTailNumberEnricher);
};

}  // namespace platform
}  // namespace ks
