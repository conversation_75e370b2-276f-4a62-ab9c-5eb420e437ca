#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/pbutil.h"
#include "google/protobuf/message.h"

namespace ks {
namespace platform {

class CommonRecoProtobufBuildAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufBuildAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    if (!init_accessor) {
      if (!output_item_attr_.empty()) {
        output_attr_accessor_ = context->GetItemAttrAccessor(output_item_attr_);
      }
      for (auto &input_config : input_item_configs_) {
        input_config.attr_accessor = context->GetItemAttrAccessor(input_config.attr_name);
      }
      init_accessor = true;
    }
    if (!output_common_attr_.empty()) {
      auto msg = msgs_[0];
      msg->Clear();
      for (const auto &input_config : input_item_configs_) {
        std::for_each(begin, end, [&](const CommonRecoResult &result) {
          if (!interop::SaveItemAttrToProtobufMessage(result, input_config.attr_accessor, msg.get(),
                                                      input_config.path, input_config.append,
                                                      input_config.allocated)) {
            CL_LOG_EVERY_N(WARNING, 1000)
                << "Failed to save item attr to protobuf: " << input_config.attr_name;
          }
        });
      }
      for (const auto &input_config : input_common_configs_) {
        if (!interop::SaveCommonAttrToProtobufMessage(context, input_config.attr_name, msg.get(),
                                                      input_config.path, input_config.append,
                                                      input_config.allocated)) {
          CL_LOG(WARNING) << "Failed to save common attr to protobuf: " << input_config.attr_name;
        }
      }
      if (as_string_) {
        std::string serialized_msg;
        msg->SerializeToString(&serialized_msg);
        context->SetStringCommonAttr(output_common_attr_, std::move(serialized_msg));
      } else {
        context->SetPtrCommonAttr(output_common_attr_, msg);
      }
    } else {
      if (!as_string_) {
        CHECK(ReserveMessages(std::distance(begin, end)));
      }
      int i = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto msg = msgs_[as_string_ ? 0 : i++];
        msg->Clear();
        for (const auto &input_config : input_common_configs_) {
          if (!interop::SaveCommonAttrToProtobufMessage(context, input_config.attr_name, msg.get(),
                                                        input_config.path, input_config.append,
                                                        input_config.allocated)) {
            CL_LOG_EVERY_N(WARNING, 1000)
                << "Failed to save common attr to protobuf: " << input_config.attr_name;
          }
        }
        for (const auto &input_config : input_item_configs_) {
          if (!interop::SaveItemAttrToProtobufMessage(result, input_config.attr_accessor, msg.get(),
                                                      input_config.path, input_config.append,
                                                      input_config.allocated)) {
            CL_LOG_EVERY_N(WARNING, 1000)
                << "Failed to save item attr to protobuf: " << input_config.attr_name
                << ", item_key: " << result.item_key << ", item_id: " << result.GetId()
                << ", item_type: " << result.GetType();
          }
        }
        if (as_string_) {
          std::string serialized_msg;
          msg->SerializeToString(&serialized_msg);
          result.SetStringAttr(output_attr_accessor_, std::move(serialized_msg));
        } else {
          result.SetPtrAttr(output_attr_accessor_, msg);
        }
      });
    }
    if (VLOG_IS_ON(1)) {
      uint64 space_used = 0;
      uint64 byte_size = 0;
      for (const auto msg : msgs_) {
        // space_used += msg->SpaceUsedLong();
        space_used += msg->SpaceUsed();
        byte_size += msg->ByteSizeLong();
      }
      CL_LOG(INFO) << GetName() << " holds " << msgs_.size() << " messages and occupies total " << space_used
                   << " bytes for payloads of total " << byte_size << " bytes.";
    }
  }

 private:
  bool InitProcessor() override {
    auto is_common_attr_config = config()->Get("is_common_attr");
    if (is_common_attr_config) {
      if (is_common_attr_config->BooleanValue(true)) {
        output_common_attr_ = config()->GetString("output_attr");
      } else {
        output_item_attr_ = config()->GetString("output_attr");
      }
    } else {
      output_common_attr_ = config()->GetString("output_common_attr");
      output_item_attr_ = config()->GetString("output_item_attr");
    }

    if (output_item_attr_.empty() && output_common_attr_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed!"
                 << "all of output_attr and output_common_attr and output_item_attr are empty.";
      return false;
    }

    class_name_ = config()->GetString("class_name");
    use_dynamic_proto_ = config()->GetBoolean("use_dynamic_proto", false);
    if (class_name_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed!"
                 << "class_name is required.";
      return false;
    }

    if (!ReserveMessages(1)) {
      LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed!"
                 << "failed to allocate message.";
      return false;
    }
    const google::protobuf::Descriptor *descriptor = nullptr;
    if (use_dynamic_proto_) {
      descriptor = GlobalHolder::GetDynamicPool()->FindMessageTypeByName(class_name_);
    } else {
      descriptor = ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(class_name_);
    }
    auto *inputs_config = config()->Get("inputs");
    if (inputs_config && inputs_config->IsArray()) {
      for (auto *c : inputs_config->array()) {
        if (c->IsObject()) {
          std::vector<int> *field_index_path = nullptr;
          std::string *attr_name = nullptr;
          if (is_common_attr_config) {
            if (is_common_attr_config->BooleanValue(true)) {
              input_common_configs_.emplace_back();
              auto &input_common_config = input_common_configs_.back();
              field_index_path = &input_common_config.path;
              attr_name = &input_common_config.attr_name;
              input_common_config.append = c->GetBoolean("append", false);
              input_common_config.allocated = c->GetBoolean("allocated", false);
            } else {
              input_item_configs_.emplace_back();
              auto &input_item_config = input_item_configs_.back();
              field_index_path = &input_item_config.path;
              attr_name = &input_item_config.attr_name;
              input_item_config.append = c->GetBoolean("append", false);
              input_item_config.allocated = c->GetBoolean("allocated", false);
            }
            *attr_name = c->GetString("attr_name");
          } else {
            if (c->Get("common_attr")) {
              input_common_configs_.emplace_back();
              auto &input_common_config = input_common_configs_.back();
              input_common_config.attr_name = c->GetString("common_attr");
              input_common_config.append = c->GetBoolean("append", false);
              input_common_config.allocated = c->GetBoolean("allocated", false);
              field_index_path = &input_common_config.path;
              attr_name = &input_common_config.attr_name;
            } else if (c->Get("item_attr")) {
              input_item_configs_.emplace_back();
              auto &input_item_config = input_item_configs_.back();
              input_item_config.attr_name = c->GetString("item_attr");
              input_item_config.append = c->GetBoolean("append", false);
              input_item_config.allocated = c->GetBoolean("allocated", false);
              field_index_path = &input_item_config.path;
              attr_name = &input_item_config.attr_name;
            }
          }
          if (!attr_name || attr_name->empty()) {
            LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed!"
                       << "all of attr_name and common_attr and item_attr are emtpy";
            return false;
          }
          std::string path = c->GetString("path");
          if (!interop::ConvertMsgPathToFieldIndexPath(descriptor, path, field_index_path)) {
            LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed! Invalid path: " << path
                       << ", class name: " << class_name_;
            return false;
          }
        } else {
          LOG(ERROR) << "CommonRecoProtobufBuildAttrEnricher init failed!"
                     << " inputs should be an array of objects.";
          return false;
        }
      }
    }

    as_string_ = config()->GetBoolean("as_string", false);

    return true;
  }

  bool ReserveMessages(size_t size) {
    size_t allocated_size = msgs_.size();
    if (size > allocated_size) {
      msgs_.resize(size);
      for (size_t i = allocated_size; i < size; i++) {
        if (use_dynamic_proto_) {
          msgs_[i].reset(pbutil::NewMessageByName(class_name_, GlobalHolder::GetDynamicPool(),
                                                  GlobalHolder::GetDynamicMessageFactory()));
        } else {
          msgs_[i].reset(pbutil::NewMessageByName(class_name_));
        }
        if (!msgs_[i]) {
          return false;
        }
      }
    }
    return true;
  }

 private:
  struct InputItemConfig {
    std::string attr_name;
    ItemAttr *attr_accessor = nullptr;
    std::vector<int> path;
    bool append = false;
    bool allocated = false;
  };
  struct InputCommonConfig {
    std::string attr_name;
    std::vector<int> path;
    bool append = false;
    bool allocated = false;
  };
  std::vector<InputItemConfig> input_item_configs_;
  std::vector<InputCommonConfig> input_common_configs_;
  std::string output_common_attr_;
  std::string output_item_attr_;
  ItemAttr *output_attr_accessor_ = nullptr;
  bool init_accessor = false;

  bool as_string_;

  std::vector<std::shared_ptr<::google::protobuf::Message>> msgs_;
  std::string class_name_;
  bool use_dynamic_proto_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufBuildAttrEnricher);
};

}  // namespace platform
}  // namespace ks
