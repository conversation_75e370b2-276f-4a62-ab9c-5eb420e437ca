#include <memory>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/processor/common/enricher/common_reco_item_attr_default_value_enricher.h"
#include "gtest/gtest.h"

static const std::vector<int64> item_id_list = {10, 11, 12, 13, 14, 15, 16, 17};

static const int int_value = 1;
static const double double_value = 0.1;
static const char *string_value = "test";
static const std::vector<int64> int_list_value = {1, 2, 3};
static const std::vector<double> double_list_value = {0.1, 0.2, 0.3};
static const std::vector<std::string> string_list_value = {
    "test1",
    "test2",
    "test3",
};

class CommonRecoItemAttrDefaultValueEnricherTest : public ::testing::Test {
 protected:
  void SetUp() override {
    exec_ = std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(R"json(
    {
    "base_pipeline": {
      "processor": {
        "set_default_value_4F6852": {
          "$output_item_attrs": [],
          "common_attrs": [
            {
              "name": "int_no_override_test",
              "type": "int",
              "value": 2
            }
          ],
          "no_overwrite": true,
          "type_name": "CommonRecoItemAttrDefaultValueEnricher"
        },
        "set_default_value_6E8A2F": {
          "$output_item_attrs": [
            "double_list_test",
            "double_test",
            "int_list_test",
            "int_test",
            "string_list_test",
            "string_test"
          ],
          "common_attrs": [
            {
              "name": "int_test",
              "type": "int",
              "value": 1
            },
            {
              "name": "double_test",
              "type": "double",
              "value": 0.1
            },
            {
              "name": "string_test",
              "type": "string",
              "value": "test"
            },
            {
              "name": "int_list_test",
              "type": "int_list",
              "value": [
                1,
                2,
                3
              ]
            },
            {
              "name": "double_list_test",
              "type": "double_list",
              "value": [
                0.1,
                0.2,
                0.3
              ]
            },
            {
              "name": "string_list_test",
              "type": "string_list",
              "value": [
                "test1",
                "test2",
                "test3"
              ]
            }
          ],
          "item_attrs": [
            {
              "name": "int_test",
              "type": "int",
              "value": 1
            },
            {
              "name": "double_test",
              "type": "double",
              "value": 0.1
            },
            {
              "name": "string_test",
              "type": "string",
              "value": "test"
            },
            {
              "name": "int_list_test",
              "type": "int_list",
              "value": [
                1,
                2,
                3
              ]
            },
            {
              "name": "double_list_test",
              "type": "double_list",
              "value": [
                0.1,
                0.2,
                0.3
              ]
            },
            {
              "name": "string_list_test",
              "type": "string_list",
              "value": [
                "test1",
                "test2",
                "test3"
              ]
            }
          ],
          "type_name": "CommonRecoItemAttrDefaultValueEnricher"
        },
        "set_default_value_D34DEF": {
          "$output_item_attrs": [
            "int_no_override_test"
          ],
          "item_attrs": [
            {
              "name": "int_no_override_test",
              "type": "int",
              "value": 2
            }
          ],
          "no_overwrite": true,
          "type_name": "CommonRecoItemAttrDefaultValueEnricher"
        }
      },
      "type_name": "CommonRecoPipeline"
    },
    "pipeline_map": {
      "test": {
        "__PARENT": "base_pipeline",
        "pipeline": [
          "set_default_value_6E8A2F",
          "set_default_value_D34DEF",
          "set_default_value_4F6852"
        ]
      }
    }
    }
    )json");
  }
  void CheckIntList(const std::vector<int64> &v0, const absl::Span<const int64> &v1) {
    EXPECT_EQ(v0.size(), v1.size()) << ": size(v0) vs size(v1): " << v0.size() << " vs " << v1.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v0.at(i), v1.at(i)) << ": the `" << i << "` element is not equal, v0[i] vs v1[i]: " << v0[i]
                                    << " vs " << v1[i];
    }
  }
  void CheckDoubleList(const std::vector<double> &v0, const absl::Span<const double> &v1) {
    EXPECT_EQ(v0.size(), v1.size()) << ": size(v0) vs size(v1): " << v0.size() << " vs " << v1.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_NEAR(v0.at(i), v1.at(i), 1e-6)
          << ": the `" << i << "` element is not equal, v0[i] vs v1[i]: " << v0[i] << " vs " << v1[i];
    }
  }
  void CheckStringList(const std::vector<std::string> &v0, const std::vector<absl::string_view> &v1) {
    EXPECT_EQ(v0.size(), v1.size()) << ": size(v0) vs size(v1): " << v0.size() << " vs " << v1.size();
    for (int i = 0; i < v0.size(); ++i) {
      EXPECT_EQ(v0.at(i), v1.at(i)) << ": the `" << i
                                    << "` element is not equal, v0[i] vs v1[i]: " << v0.at(i) << " vs "
                                    << v1.at(i);
    }
  }

  std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor> exec_;
};

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, IntCommonAttr) {
  exec_->Run("test");
  auto int_common_attr = exec_->GetInt("int_test");
  ASSERT_TRUE(int_common_attr.has_value());
  EXPECT_EQ(int_value, int_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, DoubleCommonAttr) {
  exec_->Run("test");
  auto double_common_attr = exec_->GetDouble("double_test");
  ASSERT_TRUE(double_common_attr.has_value());
  EXPECT_NEAR(double_value, double_common_attr.value(), 1e-6);
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, StringCommonAttr) {
  exec_->Run("test");
  auto string_common_attr = exec_->GetString("string_test");
  ASSERT_TRUE(string_common_attr.has_value());
  EXPECT_EQ(string_value, string_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, IntListCommonAttr) {
  exec_->Run("test");
  auto int_list_common_attr = exec_->GetIntList("int_list_test");
  ASSERT_TRUE(int_list_common_attr.has_value());
  CheckIntList(int_list_value, int_list_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, DoubleListCommonAttr) {
  exec_->Run("test");
  auto double_list_common_attr = exec_->GetDoubleList("double_list_test");
  ASSERT_TRUE(double_list_common_attr.has_value());
  CheckDoubleList(double_list_value, double_list_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, StringListCommonAttr) {
  exec_->Run("test");
  auto string_list_common_attr = exec_->GetStringList("string_list_test");
  ASSERT_TRUE(string_list_common_attr.has_value());
  CheckStringList(string_list_value, string_list_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, IntItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetInt("int_test");
    ASSERT_TRUE(item_attr.has_value());
    EXPECT_EQ(int_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, DoubleItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetDouble("double_test");
    ASSERT_TRUE(item_attr.has_value());
    EXPECT_EQ(double_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, StringItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetString("string_test");
    ASSERT_TRUE(item_attr.has_value());
    EXPECT_EQ(string_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, IntListItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetIntList("int_list_test");
    ASSERT_TRUE(item_attr.has_value());
    CheckIntList(int_list_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, DoubleListItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetDoubleList("double_list_test");
    ASSERT_TRUE(item_attr.has_value());
    CheckDoubleList(double_list_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, StringListItemAttr) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetStringList("string_list_test");
    ASSERT_TRUE(item_attr.has_value());
    CheckStringList(string_list_value, item_attr.value());
  }
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, NoOverrideIntCommonAttr) {
  exec_->SetInt("int_no_override_test", 1);
  exec_->Run("test");
  auto int_common_attr = exec_->GetInt("int_no_override_test");
  ASSERT_TRUE(int_common_attr.has_value());
  EXPECT_EQ(int_value, int_common_attr.value());
}

TEST_F(CommonRecoItemAttrDefaultValueEnricherTest, NoOverrideIntItemAttr) {
  for (int i = 0; i < 20; i++) {
    auto tmp = exec_->AddItem(i);
    tmp.SetInt("int_no_override_test", 1);
  }
  exec_->Run("test");
  auto item_list = exec_->GetItemList();
  for (const auto &item : item_list) {
    auto item_attr = item.GetInt("int_no_override_test");
    ASSERT_TRUE(item_attr.has_value());
    EXPECT_EQ(int_value, item_attr.value());
  }
}
