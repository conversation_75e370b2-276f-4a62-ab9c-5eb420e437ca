#include "dragon/src/processor/common/enricher/common_reco_execution_status_enricher.h"

namespace ks {
namespace platform {

void CommonRecoExecutionStatusEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  if (status_code_ != ExecutionStatus::UNKNOWN) {
    context->SetExecutionStatus(status_code_);
    CL_LOG(INFO) << "RESET EXECUTION_STATUS=" << static_cast<int>(status_code_) << ", terminating pipeline! "
                 << message_;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoExecutionStatusEnricher, CommonRecoExecutionStatusEnricher)

}  // namespace platform
}  // namespace ks
