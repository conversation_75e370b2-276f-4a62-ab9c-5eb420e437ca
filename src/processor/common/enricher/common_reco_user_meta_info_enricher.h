#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoUserMetaInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoUserMetaInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    save_user_id_to_attr_ = config()->GetString("save_user_id_to_attr");
    save_device_id_to_attr_ = config()->GetString("save_device_id_to_attr");
    save_request_id_to_attr_ = config()->GetString("save_request_id_to_attr");
    save_request_type_to_attr_ = config()->GetString("save_request_type_to_attr");
    save_browse_set_size_to_attr_ = config()->GetString("save_browse_set_size_to_attr");
    save_result_size_to_attr_ = config()->GetString("save_result_size_to_attr");
    save_request_time_to_attr_ = config()->GetString("save_request_time_to_attr");
    save_request_num_to_attr_ = config()->GetString("save_request_num_to_attr");
    save_current_time_ms_to_attr_ = config()->GetString("save_current_time_ms_to_attr");
    save_host_name_to_attr_ = config()->GetString("save_host_name_to_attr");
    save_host_ip_to_attr_ = config()->GetString("save_host_ip_to_attr");
    save_elapsed_time_to_attr_ = config()->GetString("save_elapsed_time_to_attr");
    save_shard_num_to_attr_ = config()->GetString("save_shard_num_to_attr");
    save_shard_no_to_attr_ = config()->GetString("save_shard_no_to_attr");
    save_flow_cpu_cost_to_attr_ = config()->GetString("save_flow_cpu_cost_to_attr");
    save_need_traceback_to_attr_ = config()->GetString("save_need_traceback_to_attr");
    return true;
  }

 private:
  std::string save_user_id_to_attr_;
  std::string save_device_id_to_attr_;
  std::string save_request_id_to_attr_;
  std::string save_request_type_to_attr_;
  std::string save_browse_set_size_to_attr_;
  std::string save_result_size_to_attr_;
  std::string save_request_time_to_attr_;
  std::string save_request_num_to_attr_;
  std::string save_current_time_ms_to_attr_;
  std::string save_host_name_to_attr_;
  std::string save_host_ip_to_attr_;
  std::string save_elapsed_time_to_attr_;
  std::string save_shard_num_to_attr_;
  std::string save_shard_no_to_attr_;
  std::string save_flow_cpu_cost_to_attr_;
  std::string save_need_traceback_to_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoUserMetaInfoEnricher);
};

}  // namespace platform
}  // namespace ks
