#pragma once

#include <google/protobuf/message.h>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoProtobufInjectBytesEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufInjectBytesEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AttrMapping {
    std::string proto_path;
    std::string attr_name;
  };

  bool InitProcessor() override {
    from_common_attr_ = config()->GetString("from_common_attr");
    output_common_attr_ = config()->GetString("output_common_attr");
    from_item_attr_ = config()->GetString("from_item_attr");
    output_item_attr_ = config()->GetString("output_item_attr");
    if (!(from_common_attr_.empty() && output_common_attr_.empty() &&
          !from_item_attr_.empty() && !output_item_attr_.empty()) &&
        !(!from_common_attr_.empty() && !output_common_attr_.empty() &&
          from_item_attr_.empty() && output_item_attr_.empty())) {
      LOG(ERROR) << "CommonRecoProtobufInjectBytesEnricher init failed!"
                 << ", from_common_attr = " << from_common_attr_
                 << ", output_common_attr = " << output_common_attr_
                 << ", from_item_attr = " << from_item_attr_
                 << ", output_item_attr = " << output_item_attr_;
      return false;
    }
    auto* append_config = config()->Get("append_msgs");
    if (append_config && append_config->IsArray()) {
      for (auto *c : append_config->array()) {
        if (c->IsObject()) {
          if (c->Get("common_attr")) {
            append_common_configs_.emplace_back();
            auto &append_common_config = append_common_configs_.back();
            append_common_config.attr_name = c->GetString("common_attr");
            append_common_config.field = c->GetInt("field", 0);
            append_common_config.field_type = c->GetString("field_type", "");
          } else if (c->Get("item_attr")) {
            append_item_configs_.emplace_back();
            auto &append_item_config = append_item_configs_.back();
            append_item_config.attr_name = c->GetString("item_attr");
            append_item_config.field = c->GetInt("field", 0);
            append_item_config.field_type = c->GetString("field_type", "");
          }
        }
      }
    }

    return true;
  }

 private:
  struct AppendItemConfig {
    std::string attr_name;
    ItemAttr *attr_accessor = nullptr;
    int field = 0;
    std::string field_type = "";
    std::string field_varint32 = "";
  };
  struct AppendCommonConfig {
    std::string attr_name;
    int field = 0;
    std::string field_type = "";
    std::string field_varint32 = "";
  };
  bool init_accessor_ = false;
  std::vector<AppendItemConfig> append_item_configs_;
  std::vector<AppendCommonConfig> append_common_configs_;
  std::string from_common_attr_;
  std::string output_common_attr_;
  std::string from_item_attr_;
  std::string output_item_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufInjectBytesEnricher);
};

}  // namespace platform
}  // namespace ks
