#include "dragon/src/processor/common/enricher/common_reco_redis_item_attr_batch_enricher.h"
#include "dragon/src/core/common_reco_util.h"

#include <algorithm>
#include <memory>
#include <utility>
#include <vector>
#include "third_party/abseil/absl/container/flat_hash_map.h"

namespace ks {
namespace platform {

void CommonRecoRedisItemAttrBatchEnricher::Enrich(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  timeout_ms_ = CheckAndGetTimeoutMs(context);
  if (timeout_ms_ <= 0) {  // 如果超时时间为 0 毫秒，代表屏蔽此 redis 服务
    return;
  }

  if (is_async_) {
    AsyncGetItemAttrFromRedisResponse(context, begin, end);
  } else {
    GetItemAttrFromRedisResponse(context, begin, end);
  }
}

void CommonRecoRedisItemAttrBatchEnricher::PreRequest(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
    std::vector<std::string> *string_keys, std::vector<std::string> *value_attr_list,
    std::vector<std::vector<std::pair<int, const CommonRecoResult>>> *index_to_result_list) {
  // 1. 汇总所有 redis key 并去重
  // 去重后的 key 编号，相同 key 有唯一编号 写 value 时根据编号查找对应的 value
  int item_num = std::distance(begin, end);
  absl::flat_hash_map<std::string, int> key_index_map;
  string_keys->reserve(redis_attr_config_list_.size() * item_num);
  value_attr_list->reserve(redis_attr_config_list_.size());
  for (auto &attr_config : redis_attr_config_list_) {
    auto *redis_key_accessor = context->GetItemAttrAccessor(attr_config.key_attr);
    if (redis_key_accessor->value_type == AttrType::UNKNOWN) {
      CL_LOG_WARNING("attr_config.key_attr", "attr_not_exist: " + attr_config.key_attr)
          << "redis key attr not exists, attr: " << attr_config.key_attr;
      continue;
    }
    if (redis_key_accessor->value_type != AttrType::STRING) {
      CL_LOG_WARNING("attr_config.key_attr", "attr_not_string: " + attr_config.key_attr)
          << "redis key attr is not string, attr: " << attr_config.key_attr;
      continue;
    }

    std::string key_prefix_value = GetStringProcessorParameter(context, attr_config.key_prefix);

    std::vector<std::pair<int, const CommonRecoResult >> key_to_result;
    key_to_result.reserve(item_num);
    std::for_each(begin, end,
                  [&key_index_map, &string_keys, &key_to_result, redis_key_accessor,
                   &key_prefix_value](const CommonRecoResult &result) {
                    if (auto redis_key = result.GetStringAttr(redis_key_accessor)) {
                      auto redis_key_data = absl::StrCat(key_prefix_value, *redis_key);
                      auto insert_res =
                          key_index_map.insert(std::make_pair(redis_key_data, string_keys->size()));
                      if (insert_res.second) {
                        string_keys->emplace_back(redis_key_data);
                      }
                      key_to_result.emplace_back(std::make_pair(insert_res.first->second, result));
                    }
                  });
    value_attr_list->emplace_back(attr_config.save_value_attr);
    index_to_result_list->emplace_back(std::move(key_to_result));
  }
  if (value_attr_list->size() != index_to_result_list->size()) {
    CL_LOG_ERROR("redis_item_attr", "value_attr_list and index_to_result_list size mismatch");
  }
}

void CommonRecoRedisItemAttrBatchEnricher::GetItemAttrFromRedisResponse(MutableRecoContextInterface *context,
                                                                        RecoResultConstIter begin,
                                                                        RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  if (item_num == 0) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich redis item attr cancelled: empty items";
    return;
  }

  std::vector<std::string> string_keys;
  std::vector<std::string> value_attr_list;
  std::vector<std::vector<std::pair<int, const CommonRecoResult >>> index_to_result_list;

  PreRequest(context, begin, end, &string_keys, &value_attr_list, &index_to_result_list);

  // 2. 查询 redis，并建立 key-value 的 map
  std::vector<std::string> string_values;
  string_values.reserve(string_keys.size());
  auto err_code = redis_client_->MGet(string_keys, &string_values, true, timeout_ms_);
  if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
      CL_LOG_WARNING("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value timeout, cluster_name: " << cluster_name_ << ", err_code: " << err_code
          << ", err_msg: " << ks::infra::err2str(err_code);
    } else {
      CL_LOG_ERROR("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
          << "redis get value error, cluster_name: " << cluster_name_ << ", err_code: " << err_code
          << ", err_msg: " << ks::infra::err2str(err_code);
    }
    return;
  }
  if (string_values.size() != string_keys.size()) {
    CL_LOG_ERROR("redis_item_attr", "key_value_size_mismatch")
        << "cluster_name: " << cluster_name_ << ", redis key and value size mismatch: " << string_keys.size()
        << " vs " << string_values.size();
    return;
  }

  // 3. 结果写入 item attr
  for (int i = 0; i < value_attr_list.size(); ++i) {
    auto *value_attr_accessor = context->GetItemAttrAccessor(value_attr_list[i]);
    for (auto &p : index_to_result_list[i]) {
      if (p.first >= 0 && p.first < string_values.size()) {
        p.second.SetStringAttr(value_attr_accessor, string_values[p.first]);
      } else {
        CL_LOG_ERROR("redis_item_attr", "index_out_of_range")
            << "cluster_name: " << cluster_name_ << ", index out of range: index=" << p.first
            << " range=" << string_values.size();
      }
    }
  }
}

void CommonRecoRedisItemAttrBatchEnricher::AsyncGetItemAttrFromRedisResponse(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  if (std::distance(begin, end) == 0) {
    CL_LOG_EVERY_N(INFO, 100) << "enrich redis item attr cancelled: empty items";
    return;
  }

  auto string_keys = std::make_shared<std::vector<std::string>>();
  auto value_attr_list = std::make_shared<std::vector<std::string>>();
  auto index_to_result_list =
      std::make_shared<std::vector<std::vector<std::pair<int, const CommonRecoResult >>>>();

  PreRequest(context, begin, end, string_keys.get(), value_attr_list.get(), index_to_result_list.get());

  // 2. 查询 redis，并建立 key-value 的 map
  std::shared_ptr<base::RedisMGetOpWaiter> async_mget_waiter =
      std::make_shared<base::RedisMGetOpWaiter>(redis_client_->AsyncMGet(*(string_keys.get())));

  async_mget_waiter->SetTimeout(timeout_ms_);

  auto redis_future =
      CommonRecoFutureActionWrapper<std::pair<ks::infra::RedisErrorCode, std::vector<std::string>>>(
          [this, context,
           async_mget_waiter]() -> std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> {
            std::vector<std::string> ret;
            auto status = async_mget_waiter->Get(&ret);
            auto async_cost = async_mget_waiter->GetAsyncCostUs();
            if (async_cost >= 0) {
              CL_PERF_INTERVAL(async_cost, kPerfNs, "processor_async_ready",
                               GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                               GetDownstreamProcessor(), "", GlobalHolder::GetJsonConfigVersion());
            }
            return {status, ret};
          });

  std::function<void(const std::vector<std::string> &)> callback =
      [this, context, value_attr_list, index_to_result_list](const std::vector<std::string> &string_values) {
        for (int i = 0; i < value_attr_list->size(); ++i) {
          auto *value_attr_accessor = context->GetItemAttrAccessor(value_attr_list->at(i));
          for (auto &p : index_to_result_list->at(i)) {
            if (p.first >= 0 && p.first < string_values.size()) {
              p.second.SetStringAttr(value_attr_accessor, string_values[p.first]);
            } else {
              CL_LOG_ERROR("redis_item_attr", "index_out_of_range")
                  << "cluster_name: " << cluster_name_ << ", index out of range: index=" << p.first
                  << " range=" << string_values.size();
            }
          }
        }
      };

  std::function<bool(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)> status_getter =
      [this, context,
       string_keys](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> bool {
    auto err_code = result.first;
    const auto &string_values = result.second;
    if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
      if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
        CL_LOG_WARNING("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value timeout, cluster_name: " << cluster_name_ << ", err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      } else {
        CL_LOG_ERROR("redis_item_attr", cluster_name_ + ":" + ks::infra::err2str(err_code))
            << "redis get value error, cluster_name: " << cluster_name_ << ", err_code: " << err_code
            << ", err_msg: " << ks::infra::err2str(err_code);
      }
      return false;
    }
    if (string_values.size() != string_keys->size()) {
      CL_LOG_ERROR("redis_item_attr", "key_value_size_mismatch")
          << "cluster_name: " << cluster_name_
          << ", redis key and value size mismatch: " << string_keys->size() << " vs " << string_values.size();
      return false;
    }
    return true;
  };

  std::function<const std::vector<std::string> &(
      const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      payload_getter = [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result)
      -> const std::vector<std::string> & { return result.second; };

  std::function<std::string(const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &)>
      err_msg_getter =
          [](const std::pair<ks::infra::RedisErrorCode, std::vector<std::string>> &result) -> std::string {
    return ks::infra::err2str(result.first);
  };
  RegisterAsyncCallback(
      context, std::move(redis_future), std::move(callback), []() {}, std::move(status_getter),
      std::move(payload_getter), std::move(err_msg_getter), "");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRedisItemAttrBatchEnricher, CommonRecoRedisItemAttrBatchEnricher)

}  // namespace platform
}  // namespace ks
