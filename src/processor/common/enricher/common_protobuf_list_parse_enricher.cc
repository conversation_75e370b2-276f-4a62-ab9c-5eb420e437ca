#include "dragon/src/processor/common/enricher/common_protobuf_list_parse_enricher.h"

namespace ks {
namespace platform {
void CommonProtobufListParseEnricher::Enrich(ks::platform::MutableRecoContextInterface *context,
                                             ks::platform::RecoResultConstIter begin,
                                             ks::platform::RecoResultConstIter end) {
  auto str_list_attr = context->GetStringListCommonAttr(input_common_attr_);
  if (!str_list_attr || str_list_attr->empty()) {
    return;
  }

  while (msgs_.size() < str_list_attr->size()) {
    msgs_.emplace_back(pbutil::NewMessageByName(class_name_));
    if (!msgs_.back()) {
      CL_LOG(ERROR) << "CommonProtobufListParseEnricher new pb message failed: " << class_name_;
      return;
    }
  }

  for (int i = 0; i < str_list_attr->size(); i++) {
    auto sv = str_list_attr->at(i);
    if (!sv.empty()) {
      msgs_[i]->ParseFromArray(sv.data(), sv.size());
    }
  }

  for (const auto &mapping : attr_mappings_) {
    for (int i = 0; i < str_list_attr->size(); i++) {
      interop::AppendProtobufMessageToListCommonAttr(context, mapping.attr_name, *(msgs_[i]),
                                                     mapping.field_index);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonProtobufListParseEnricher, CommonProtobufListParseEnricher)
FACTORY_REGISTER(JsonFactoryClass, MerchantProtobufListParseEnricher, CommonProtobufListParseEnricher)
}  // namespace platform
}  // namespace ks
