#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoExecutionStatusEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoExecutionStatusEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    int code = config()->GetInt("status_code", static_cast<int>(ExecutionStatus::UNKNOWN));
    status_code_ = static_cast<ExecutionStatus>(code);
    message_ = config()->GetString("message");
    return true;
  }

 private:
  ExecutionStatus status_code_ = ExecutionStatus::UNKNOWN;
  std::string message_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoExecutionStatusEnricher);
};

}  // namespace platform
}  // namespace ks
