#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/metrics/abtest_metric.h"

namespace ks {
namespace platform {

class CommonRecoAbtestExpInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAbtestExpInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AbExperimentInfo {
    const base::Json *world_config = nullptr;
    std::string ab_key;
    std::string save_exp_id_to;
    std::string save_group_id_to;
  };

  bool InitProcessor() override {
    auto *experiments = config()->Get("exp_info");
    if (!experiments || !experiments->IsArray()) {
      LOG(ERROR) << "CommonRecoAbtestExpInfoEnricher init failed!"
                 << " Missing \"exp_info\" config or it is not an array.";
      return false;
    }

    std::string biz_name = config()->GetString("biz_name");
    if (!biz_name.empty()) {
      biz_ = ks::GetAbtestBizByName(biz_name);
    }

    ab_experiments_.reserve(experiments->size());
    for (const auto *exp_json : experiments->array()) {
      if (!exp_json->IsObject()) {
        LOG(ERROR) << "CommonRecoAbtestExpInfoEnricher init failed! Item of ab_experiments should be a dict!"
                   << " Value found: " << exp_json->ToString();
        return false;
      }

      AbExperimentInfo exp;
      exp.world_config = exp_json->Get("world");
      exp.ab_key = exp_json->GetString("ab_key");
      if ((!exp.world_config || !exp.world_config->IsString()) && exp.ab_key.empty()) {
        LOG(ERROR) << "CommonRecoAbtestExpInfoEnricher init failed!"
                   << " Missing \"world\" and \"ab_key\" config or it is not a string.";
        return false;
      }

      exp.save_exp_id_to = exp_json->GetString("save_exp_id_to", "");
      exp.save_group_id_to = exp_json->GetString("save_group_id_to", "");

      ab_experiments_.emplace_back(std::move(exp));
    }
    return true;
  }

 private:
  std::vector<AbExperimentInfo> ab_experiments_;
  ks::AbtestBiz biz_ = ks::AbtestBiz::NULL_BIZ;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAbtestExpInfoEnricher);
};

}  // namespace platform
}  // namespace ks
