#pragma once

#include <memory>
#include <vector>
#include <string>
#include <algorithm>

#include "serving_base/util/math.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

enum NormType {
  MIN_MAX_SCALE,
  MEAN_BASED_SCALE,
  MIN_BASED_SCALE,
  STDEV_BASED_SCALE,
  MAX_BASED_SCALE,
};

class CommonRecoNormalizationEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoNormalizationEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    input_attr_ = config()->GetString("input_attr", "");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoNormalizationEnricher init failed, input_attr empty";
      return false;
    }

    output_attr_ = config()->GetString("output_attr", "");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoNormalizationEnricher init failed, output_attr empty";
      return false;
    }

    std::string mode = config()->GetString("mode", "");
    if (mode == "min_max_scale") {
      mode_ = MIN_MAX_SCALE;
    } else if (mode == "mean_based_scale") {
      mode_ = MEAN_BASED_SCALE;
    } else if (mode == "min_based_scale") {
      mode_ = MIN_BASED_SCALE;
    } else if (mode == "stdev_based_scale") {
      mode_ = STDEV_BASED_SCALE;
    } else if (mode == "max_based_scale") {
      mode_ = MAX_BASED_SCALE;
    } else {
      mode_ = MIN_MAX_SCALE;
    }

    default_val_ = config()->GetNumber("default_val", 0.0);
    eps_ = config()->GetNumber("eps", 0.0000001);
    alpha_ = config()->GetNumber("alpha", 0.0);

    return true;
  }

  void MaxMinScaled(const std::vector<double> &scores,
                    std::vector<double> *scaled_scores);

  void MeanScaled(const std::vector<double> &scores,
                  std::vector<double> *scaled_scores);

  void MinRatioScaled(const std::vector<double> &scores,
                      std::vector<double> *scaled_scores);

  void Standardization(const std::vector<double> &scores,
                       std::vector<double> *scaled_scores);

  void MaxScaled(const std::vector<double> &scores,
                       std::vector<double> *scaled_scores);

 private:
  std::string input_attr_;
  ItemAttr *input_attr_accessor_ = nullptr;

  std::string output_attr_;
  ItemAttr *output_attr_accessor_ = nullptr;

  NormType mode_ = MIN_MAX_SCALE;

  double default_val_ = 0.0;
  double eps_ = 0.0000001;
  double alpha_ = 0.0;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoNormalizationEnricher);
};

}  // namespace platform
}  // namespace ks
