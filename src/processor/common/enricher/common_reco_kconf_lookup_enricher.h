#pragma once

#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kconf/kconf.h"

namespace ks {
namespace platform {

class CommonRecoKconfLookupEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoKconfLookupEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct KconfConfig {
    const base::Json *kconf_key = nullptr;
    KconfParamType value_type = KconfParamType::UNKNOWN;
    bool is_common_attr = true;
    std::string lookup_attr;
    std::string output_attr;

    explicit KconfConfig(const base::Json *kconf_key) : kconf_key(kconf_key) {}
  };

  struct KconfValue {
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::set<int64>>>> int_set;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::set<std::string>>>> string_set;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::unordered_map<std::string, bool>>>> string_bool_map;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::unordered_map<std::string, int64>>>> string_int_map;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::unordered_map<std::string, double>>>>
        string_double_map;
    std::shared_ptr<infra::KsConfig<std::shared_ptr<std::unordered_map<std::string, std::string>>>>
        string_string_map;
    std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<ks::infra::TailNumber>>> tail_number;
  };

  bool InitProcessor() override {
    const auto *kconf_configs = config()->Get("kconf_configs");
    if (!kconf_configs || !kconf_configs->IsArray()) {
      LOG(ERROR) << "CommonRecoKconfLookupEnricher"
                 << " init failed! Missing 'kconf_configs' or it is not an array";
      return false;
    }

    for (const auto *config : kconf_configs->array()) {
      if (!config->IsObject()) {
        LOG(ERROR) << "CommonRecoKconfLookupEnricher init failed! Values of 'kconf_configs' "
                   << "should be objects";
        return false;
      }
      std::string kconf_key = config->GetString("kconf_key");
      if (kconf_key.empty()) {
        LOG(ERROR) << "CommonRecoKconfLookupEnricher init failed! Missing 'kconf_key' "
                   << "or it is empty";
        return false;
      }

      KconfConfig kconf_config(config->Get("kconf_key"));

      std::string type = config->GetString("value_type");
      if (type == "set_int64" || type == "set_int") {
        kconf_config.value_type = KconfParamType::INT64_SET;
      } else if (type == "set_string") {
        kconf_config.value_type = KconfParamType::STRING_SET;
      } else if (type == "map_string_bool") {
        kconf_config.value_type = KconfParamType::STRING_BOOL_MAP;
      } else if (type == "map_string_int64" || type == "map_string_int") {
        kconf_config.value_type = KconfParamType::STRING_INT64_MAP;
      } else if (type == "map_string_double") {
        kconf_config.value_type = KconfParamType::STRING_DOUBLE_MAP;
      } else if (type == "map_string_string") {
        kconf_config.value_type = KconfParamType::STRING_STRING_MAP;
      } else if (type == "tail_number") {
        kconf_config.value_type = KconfParamType::TAIL_NUMBER;
      } else {
        LOG(ERROR) << "CommonRecoKconfLookupEnricher init failed! Unsupported value_type: " << type
                   << ", should be set_int64/set_string/map_string_bool/map_string_int64/map_string_double/"
                      "map_string_string/tail_number";
        return false;
      }

      kconf_config.lookup_attr = config->GetString("lookup_attr");
      if (kconf_config.lookup_attr.empty()) {
        LOG(ERROR) << "CommonRecoKconfLookupEnricher init failed! "
                   << "Missing 'lookup_attr' config, kconf_key: " << kconf_key;
        return false;
      }
      kconf_config.output_attr = config->GetString("output_attr");
      if (kconf_config.output_attr.empty()) {
        LOG(ERROR) << "CommonRecoKconfLookupEnricher init failed! "
                   << "Missing 'output_attr' config, kconf_key: " << kconf_key;
        return false;
      }

      kconf_config.is_common_attr = config->GetBoolean("is_common_attr", true);

      kconf_configs_.push_back(std::move(kconf_config));
    }

    key_parser_ = [](const std::string &key, std::string *val) -> bool {
      *val = key;
      return true;
    };

    return true;
  }

  const KconfValue &GetOrCreateKconfValue(ReadableRecoContextInterface *context,
                                          const KconfConfig &kconf_config);

  void LookupCommonAttr(MutableRecoContextInterface *context, const KconfValue &kconf_value,
                        const KconfConfig &kconf_config);

  void LookupItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end, const KconfValue &kconf_value,
                      const KconfConfig &kconf_config);

 private:
  std::vector<KconfConfig> kconf_configs_;
  folly::F14FastMap<std::string, KconfValue> kconf_value_map_;
  ks::infra::KeyParser<std::string> key_parser_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoKconfLookupEnricher);
};

}  // namespace platform
}  // namespace ks
