#include "dragon/src/processor/common/enricher/common_reco_build_custom_kv_user_info_enricher.h"

#include <utility>
#include <vector>

#include "ks/algo-engine-proto/common_sample_log.pb.h"

namespace ks {
namespace platform {

bool CommonRecoBuildCustomKVUserInfoEnricher::InitProcessor() {
  std::string collection_name = config()->GetString("collection_name");
  if (collection_name.empty()) {
    LOG(ERROR) << "CommonRecoBuildCustomKVUserInfoEnricher init failed. "
        << "Miss \"collection_name\".";
    return false;
  }

  fdk_metadata_.set_collection_name(collection_name);
  fdk_metadata_.set_format(fdk::proto::FORMAT_SAMPLE_ATTR);
  fdk_builder_ = fdk::FDK::GetInstance()->CreateBuilder(fdk_options_, fdk_metadata_);
  if (!fdk_builder_) {
    LOG(ERROR) << "CommonRecoBuildCustomKVUserInfoEnricher init failed. "
        << "Failed to create fdk builder.";
    return false;
  }

  kv_user_info_attr_ = config()->GetString("kv_user_info_attr");
  if (kv_user_info_attr_.empty()) {
    LOG(ERROR) << "CommonRecoBuildCustomKVUserInfoEnricher init failed. "
        << "Miss \"kv_user_info_attr\".";
    return false;
  }

  save_result_to_attr_ = config()->GetString("save_result_to_attr");
  if (save_result_to_attr_.empty()) {
    LOG(ERROR) << "CommonRecoBuildCustomKVUserInfoEnricher init failed. "
        << "Miss \"save_result_to_attr\".";
    return false;
  }

  return true;
}

void CommonRecoBuildCustomKVUserInfoEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  const auto *kv_user_info = context->GetProtoMessagePtrCommonAttr<kuiba::PredictItem>(kv_user_info_attr_);
  if (!kv_user_info) {
    CL_LOG(ERROR) << "Empty kv user info!";
    return;
  }

  const auto custom_user_info_keys = GetStringListProcessorParameter(context, "custom_user_info_keys");
  if (custom_user_info_keys.empty()) {
    CL_LOG(WARNING) << "Empty custom user info keys!";
    return;
  }

  fdk_builder_->Clear();
  custom_user_info_key_set_.clear();
  custom_user_info_key_set_.reserve(custom_user_info_keys.size());
  for (const auto &key : custom_user_info_keys) {
    custom_user_info_key_set_.insert(std::string(key));
  }
  for (const auto &attr : kv_user_info->attr()) {
    if (custom_user_info_key_set_.find(attr.name()) != custom_user_info_key_set_.end()) {
      switch (attr.type()) {
        case kuiba::CommonSampleEnum::INT_ATTR: {
          fdk_builder_->AddValue(attr.name(), attr.int_value());
          break;
        }
        case kuiba::CommonSampleEnum::FLOAT_ATTR: {
          fdk_builder_->AddValue(attr.name(), attr.float_value());
          break;
        }
        case kuiba::CommonSampleEnum::STRING_ATTR: {
          fdk_builder_->AddValue(attr.name(), attr.string_value());
          break;
        }
        case kuiba::CommonSampleEnum::INT_LIST_ATTR: {
          fdk_builder_->AddValue(attr.name(), attr.int_list_value());
          break;
        }
        case kuiba::CommonSampleEnum::FLOAT_LIST_ATTR: {
          fdk_builder_->AddValue(attr.name(), attr.float_list_value());
          break;
        }
        case kuiba::CommonSampleEnum::STRING_LIST_ATTR: {
          std::vector<std::string> tmp(attr.string_list_value().begin(), attr.string_list_value().end());
          fdk_builder_->AddValue(attr.name(), std::move(tmp));
          break;
        }
        default: {
          CL_LOG(WARNING) << "Invalid attr type!";
        }
      }
    }
  }

  if (fdk_builder_->GetFieldCount() > 0) {
    std::string result;
    if (fdk_builder_->SerializeToString(&result)) {
      context->SetStringCommonAttr(save_result_to_attr_, result);
    } else {
      CL_LOG(ERROR) << "Failed to serialize the result!";
    }
  } else {
    CL_LOG(INFO) << "None attr is added.";
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoBuildCustomKVUserInfoEnricher,
                 CommonRecoBuildCustomKVUserInfoEnricher)

}  // namespace platform
}  // namespace ks
