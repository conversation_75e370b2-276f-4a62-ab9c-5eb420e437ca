#include "dragon/src/processor/common/enricher/common_reco_predict_fetcher_item_attr_enricher.h"

#include <algorithm>
#include <tuple>

#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_pub/reco/predict/clients/common_reco_predict_client.h"
#include "ks/reco_pub/reco/util/predict_util.h"
#include "learning/kuiba/base/constant.h"

namespace ks {
namespace platform {

#define ENRICH_PREDICT_RESULT(XTR)                                       \
  {                                                                      \
    bool include = include_pxtrs_.empty() || include_pxtrs_[pxtr_index]; \
    if (include && results.XTR##_size() > i) { /* NOLINT */              \
      auto xtr = results.XTR(i);                                         \
      result.SetDoubleAttr(output_attr_accessors_[pxtr_index], xtr);     \
      pxtr_stat[pxtr_index].AddValue(xtr);                               \
    }                                                                    \
    ++pxtr_index;                                                        \
  }

void CommonRecoPredictFetcherItemAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  item_ids_.clear();
  item_id_to_result_map_.clear();

  if (output_attr_accessors_.empty()) {
    for (const auto &pxtr : PXTR_LIST) {
      output_attr_accessors_.push_back(context->GetItemAttrAccessor(output_attr_prefix_ + pxtr));
    }
  }

  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.predict_fetcher",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "empty_kess_service");
    CL_LOG_EVERY_N(WARNING, 100) << "predict fetcher request cancelled: empty kess_service!";
    return;
  }

  std::string request_info = "kess_service: " + kess_service + ", service_group: " + service_group_ +
                             ", timeout_ms: " + std::to_string(timeout_ms_) +
                             ", thread_num: " + std::to_string(thread_num_);

  const auto *user_info = context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(user_info_attr_);
  ks::reco::UserInfo ks_user_info;
  if (!user_info && try_parse_user_info_) {
    auto user_info_str = context->GetStringCommonAttr(user_info_attr_);
    if (!user_info_str) {
      base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.predict_fetcher",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), "no_user_info");
      CL_LOG_EVERY_N(WARNING, 100) << "predict fetcher request cancelled: cannot get user info from "
                                   << "string or extra common attr: " << user_info_attr_ << ". "
                                   << request_info << RecoUtil::GetRequestInfoForLog(context);
      return;
    }
    ks_user_info.Clear();
    ks_user_info.ParseFromArray(user_info_str->data(), user_info_str->size());
    user_info = &ks_user_info;
  }

  auto *item_id_accessor = item_id_attr_.empty() ? nullptr : context->GetItemAttrAccessor(item_id_attr_);
  std::for_each(begin, end, [this, item_id_accessor](const CommonRecoResult &result) {
    if (!item_id_accessor) {
      uint64 id = result.GetId();
      item_ids_.push_back(id);
      item_id_to_result_map_.insert(std::make_pair(id, result));
    } else {
      auto p = result.GetIntAttr(item_id_accessor);
      if (p) {
        uint64 id = *p;
        item_ids_.push_back(id);
        item_id_to_result_map_.insert(std::make_pair(id, result));
      } else {
        CL_LOG_EVERY_N(WARNING, 1000) << "item ignored: cannot get id from int attr " << item_id_attr_
                                      << ", item_key: " << result.item_key;
      }
    }
  });

  if (item_ids_.empty()) {
    CL_LOG(INFO) << "predict fetcher request cancelled: empty item list! " << request_info;
    return;
  }

  GetRemotePxtr(context, user_info, kess_service, request_info);
}

void CommonRecoPredictFetcherItemAttrEnricher::GetRemotePxtr(MutableRecoContextInterface *context,
                                                             const ks::reco::UserInfo *user_info,
                                                             const std::string &kess_service,
                                                             const std::string &request_info) {
  std::vector<ks::reco::PredictKessFetcher::PredictFuture> sub_futures;

  int item_num = item_ids_.size();
  CL_LOG(INFO) << "sending predict fetcher request, item num: " << item_num << ", " << request_info;
  ks::reco::PredictKessFetcher predict_kess_fetcher;
  predict_kess_fetcher.AsyncGetRelevancePredictResult(*user_info, item_ids_, kess_service, service_group_,
                                                      thread_num_, &sub_futures, nullptr, timeout_ms_);

  if (sub_futures.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.predict_fetcher",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "no_future_constructed");
    CL_LOG_EVERY_N(WARNING, 100) << "predict fetcher request cancelled: no future constructed! "
                                 << request_info;
    return;
  }

  auto callback = [this, context, item_num, item_map = std::move(item_id_to_result_map_),
                   request_info](ks::reco::PredictResponse *resp) {
    this->EnrichPredictResult(context, item_num, item_map, request_info, resp);
  };

  for (auto &future : sub_futures) {
    // 注册 callback 函数
    RegisterAsyncCallback(context, std::move(future), callback, request_info);
  }
}

void CommonRecoPredictFetcherItemAttrEnricher::EnrichPredictResult(
    MutableRecoContextInterface *context, int request_item_num,
    const folly::F14FastMap<uint64, CommonRecoResult> &item_map, const std::string &request_info,
    const ks::reco::PredictResponse *resp) {
  auto item_num = request_item_num;

  int64 start_ms = base::GetTimestamp();
  int item_count = 0;
  const auto &results = resp->packed_results();
  std::vector<StatisticInfo> pxtr_stat;
  pxtr_stat.resize(output_attr_accessors_.size());
  for (int i = 0; i < results.photo_id_size(); ++i) {
    auto it = item_map.find(results.photo_id(i));
    if (it == item_map.end()) continue;
    const auto &result = it->second;
    int pxtr_index = 0;
    // NOTE(fangjianbing): 增删或调序下面的 ENRICH_PREDICT_RESULT 要同步修改文件顶部的 PXTR_LIST 变量！
    ENRICH_PREDICT_RESULT(pctr);
    ENRICH_PREDICT_RESULT(pltr);
    ENRICH_PREDICT_RESULT(pwtr);
    ENRICH_PREDICT_RESULT(plvtr);
    ENRICH_PREDICT_RESULT(psvtr);
    ENRICH_PREDICT_RESULT(pftr);
    ENRICH_PREDICT_RESULT(pshowtr);
    ENRICH_PREDICT_RESULT(plvtr2);
    ENRICH_PREDICT_RESULT(ptr);
    ENRICH_PREDICT_RESULT(pwatch_time);
    ENRICH_PREDICT_RESULT(pepstr);
    ENRICH_PREDICT_RESULT(pcmtr);
    ENRICH_PREDICT_RESULT(pecstr);
    ENRICH_PREDICT_RESULT(plivingtr);
    ENRICH_PREDICT_RESULT(pcestr);
    ENRICH_PREDICT_RESULT(plttr);
    ENRICH_PREDICT_RESULT(pwttr);
    ENRICH_PREDICT_RESULT(pdtr);
    ENRICH_PREDICT_RESULT(pelivingtr);
    ENRICH_PREDICT_RESULT(pcotr);
    ENRICH_PREDICT_RESULT(pfostr);
    ENRICH_PREDICT_RESULT(pwtd);
    ENRICH_PREDICT_RESULT(pclk_cmt);
    ENRICH_PREDICT_RESULT(pswptr);
    ENRICH_PREDICT_RESULT(pswptr_after);
    ENRICH_PREDICT_RESULT(pcltr);
    ENRICH_PREDICT_RESULT(phtr);
    ++item_count;
  }

  if (item_num > 0) {
    double filling_rate = (double)item_count / item_num;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(1000.0 * filling_rate, kPerfNs, "predict.item_hit",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
  }
  base::perfutil::PerfUtilWrapper::IntervalLogStash(item_num, kPerfNs, "predict.item_total",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  for (int i = 0; i < pxtr_stat.size(); ++i) {
    if (include_pxtrs_.empty() || include_pxtrs_[i]) {
      const auto &stat = pxtr_stat[i];
      const auto &attr_name = output_attr_accessors_[i]->name();
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          1000.0 * stat.count() / std::max(1, item_count), kPerfNs, "predict.pxtr_hit",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
      if (stat.count() > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.avg(), kPerfNs, "predict.pxtr_avg",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1000000 * stat.max(), kPerfNs, "predict.pxtr_max",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), attr_name);
      }
    }
  }

  int64 duration = base::GetTimestamp() - start_ms;
  CL_LOG(INFO) << "predict fetcher response received, callback executed in " << duration / 1000.0
               << " ms, request item num: " << item_num
               << ", response photo_id_size: " << results.photo_id_size()
               << ", actually enriched item num: " << item_count << ", " << request_info;
}

CommonRecoPredictFetcherV2ItemAttrEnricher::CommonRecoPredictFetcherV2ItemAttrEnricher() {
  common_reco_predict_client_ = std::make_shared<ks::reco::CommonRecoPredictClient>();
}

void CommonRecoPredictFetcherV2ItemAttrEnricher::GetRemotePxtr(MutableRecoContextInterface *context,
                                                               const ks::reco::UserInfo *user_info,
                                                               const std::string &kess_service,
                                                               const std::string &request_info) {
  std::vector<ks::reco::CommonRecoPredictClient::PredictFuture> sub_futures;

  int item_num = item_ids_.size();
  CL_LOG(INFO) << "sending predict fetcher request, item num: " << item_num << ", " << request_info;

  std::string tmp_user_info_str;
  auto user_info_str = context->GetStringCommonAttr(user_info_attr_);
  if (user_info_str && !user_info_str->empty()) {
    tmp_user_info_str.assign(user_info_str->data(), user_info_str->size());
  } else if (user_info) {
    user_info->SerializeToString(&tmp_user_info_str);
    CL_LOG_EVERY_N(INFO, 100) << " using user info : " << user_info;
  }

  std::string tower_request_type = GetStringProcessorParameter(context, "tower_request_type");
  common_reco_predict_client_->AsyncGetRelevancePredictResult(
      context->GetUserId(), tmp_user_info_str, item_ids_, tower_request_type, kess_service, service_group_,
      &sub_futures, return_pxtr_label_, return_pxtr_value_, timeout_ms_);

  if (sub_futures.empty()) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "error.predict_fetcher",
                                                   GlobalHolder::GetServiceIdentifier(),
                                                   context->GetRequestType(), "no_future_constructed");
    CL_LOG_EVERY_N(WARNING, 100) << "predict fetcher request cancelled: no future constructed! "
                                 << request_info;
    return;
  }

  typedef std::tuple<::grpc::Status, CommonRecoResponse *> TValue;
  auto callback = [this, context, item_num, item_map = std::move(item_id_to_result_map_),
                   request_info](const ks::platform::CommonRecoResponse *resp) {
    ks::reco::PredictResponse response;
    ks::reco::PredictUtil::CommonRecoResponseToPredictResponse(resp, &response, return_pxtr_label_,
                                                               return_pxtr_value_);
    this->EnrichPredictResult(context, item_num, item_map, request_info, &response);
  };

  for (auto &future : sub_futures) {
    // 注册 callback 函数
    RegisterAsyncCallback<CommonRecoResponse *, TValue>(
        context, std::move(future), callback,
        /* finally */
        []() {},
        /*status_getter=*/
        [](const TValue &value) { return std::get<0>(value).ok(); },
        /*payload_getter=*/
        [](const TValue &value) -> CommonRecoResponse *const & { return std::get<1>(value); },
        /*err_msg_getter=*/
        [](const TValue &value) -> std::string {
          const ::grpc::Status &status = std::get<0>(value);
          return "grpc_err_" + std::to_string(status.error_code()) + "-" + status.error_message();
        },
        request_info);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPredictFetcherItemAttrEnricher,
                 CommonRecoPredictFetcherItemAttrEnricher)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPredictFetcherV2ItemAttrEnricher,
                 CommonRecoPredictFetcherV2ItemAttrEnricher)

}  // namespace platform
}  // namespace ks
