#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/reco_pub/author_abtest_util/util.h"

#define AT_UTIL ks::reco::AuthorABTestParameterUtilSingleton::Singleton()->GetAuthorABTestParameterUtil()

namespace ks {
namespace platform {

class CommonRecoAuthorExpParamEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoAuthorExpParamEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct AuthorExpParam {
    std::string param_name;
    std::string attr_name;
    AbtestParamType type = AbtestParamType::UNKNOWN;
    ItemAttr *item_attr = nullptr;
    int64 default_int = 0;
    double default_double = 0.0;
    std::string default_string;
  };

  bool InitProcessor() override {
    ks::reco::AuthorABTestParameterUtilSingleton::Singleton()->Init();
    author_tail_str_ = config()->GetString("author_tail");
    if (author_tail_str_.empty()) {
      LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed! author_tail_str_ empty";
      return false;
    }

    auto *params = config()->Get("author_exp_params");
    if (!params || !params->IsArray()) {
      LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed!"
                 << " Missing \"ab_params\" config or it is not an array.";
      return false;
    }
    author_exp_params_.clear();

    for (const auto *param_json : params->array()) {
      if (param_json == nullptr) {
        LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed! param_json is nullptr!";
        return false;
      }
      AuthorExpParam param;
      if (!param_json->IsObject()) {
        LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed! author_exp_params should be a dict!"
                   << " Value found: " << param_json->ToString();
        return false;
      }

      param.param_name = param_json->GetString("param_name", "");
      if (param.param_name.empty()) {
        LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed! param_name empty";
        return false;
      }

      param.attr_name = param_json->GetString("attr_name", "");
      if (param.attr_name.empty()) {
        LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed! attr_name empty";
        return false;
      }

      std::string type_str = param_json->GetString("attr_type", "");
      param.type = RecoUtil::ParseAbtestParamType(type_str);
      if (param.type == AbtestParamType::UNKNOWN) {
        LOG(ERROR) << "CommonRecoAuthorExpParamEnricher init failed!"
                   << " Unknown attr_type: " << type_str;
        return false;
      }

      const auto *default_value = param_json->Get("default_value");
      if (default_value) {
        switch (param.type) {
          case AbtestParamType::INT:
            param.default_int = default_value->IntValue((int64)0);
            break;
          case AbtestParamType::BOOLEAN: {
            bool val = false;
            if (!default_value->BooleanValue(&val)) {
              val = (bool)default_value->IntValue((int64)0);
            }
            param.default_int = val;
            break;
          }
          case AbtestParamType::DOUBLE:
            param.default_double = default_value->NumberValue(0.0);
            break;
          case AbtestParamType::STRING:
            param.default_string = default_value->StringValue();
            break;
          default:
            break;
        }
      }

      author_exp_params_.emplace_back(std::move(param));
    }
    return true;
  }

 private:
  std::string author_tail_str_;
  std::vector<AuthorExpParam> author_exp_params_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAuthorExpParamEnricher);
};

}  // namespace platform
}  // namespace ks
