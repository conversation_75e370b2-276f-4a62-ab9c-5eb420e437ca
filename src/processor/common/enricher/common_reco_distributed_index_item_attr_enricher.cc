#include "dragon/src/processor/common/enricher/common_reco_distributed_index_item_attr_enricher.h"

#include "kconf/kconf.h"
#include "kess/rpc/rpc_facade.h"
#include "serving_base/server_base/kess_client.h"

#include "ks/action/kuiba_predict_photo_store_item.h"
#include "ks/reco_proto/distributed_photo_info/photo_info_service.kess.grpc.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#ifdef BUILD_AD_INFER_SERVER
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/ad_photo_store_item.h"
#endif
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_defaults.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"

namespace ks {
namespace platform {

DEFINE_bool(common_leaf_use_dynamic_photo_store, false, "common leaf 是否使用 dynamic photo store");
DEFINE_bool(distributed_index_skip_init_failed_for_attr, false,
            "skip init failed when some attrs have unknown type");

// specialization for PhotoInfoItem
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::PhotoInfoItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_);
}

// specialization for NewPhotoInfoItem 支持删除操作
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::NewPhotoInfoItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_);
}

// specialization for MerchantPhotoStoreItem
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::MerchantPhotoStoreItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_);
}

// specialization for MerchantLivingPhotoInfoItem
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::MerchantLivingPhotoStoreItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_);
}

// specialization for PhotoInfoItem
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::DynamicPhotoInfoItemFactory>::Singleton()->GetDynamicPhotoStore(
      photo_store_kconf_key_);
}

// specialization for NewPhotoInfoItem 支持删除操作
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::DynamicNewPhotoInfoItemFactory>::Singleton()
      ->GetDynamicPhotoStore(photo_store_kconf_key_);
}

// specialization for MerchantPhotoStoreItem
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::DynamicMerchantPhotoStoreItemFactory>::Singleton()
      ->GetDynamicPhotoStore(photo_store_kconf_key_);
}

// specialization for MerchantLivingPhotoStoreItem
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::DynamicMerchantLivingPhotoStoreItemFactory>::Singleton()
      ->GetDynamicPhotoStore(photo_store_kconf_key_);
}

template <>
std::function<void()> CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::Purge() {
  return [this]() {
    if (this->use_dynamic_photo_store_) {
      DynamicPhotoStoreManager<ks::reco::DynamicPhotoInfoItemFactory>::Singleton()->Stop();
    } else {
      PhotoStoreManager<ks::reco::PhotoInfoItemFactory>::Singleton()->Stop();
    }
  };
}

template <>
std::function<void()> CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::Purge() {
  return [this]() {
    if (this->use_dynamic_photo_store_) {
      DynamicPhotoStoreManager<ks::reco::DynamicNewPhotoInfoItemFactory>::Singleton()->Stop();
    } else {
      PhotoStoreManager<ks::reco::NewPhotoInfoItemFactory>::Singleton()->Stop();
    }
  };
}

template <>
std::function<void()> CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::Purge() {
  return [this]() {
    if (this->use_dynamic_photo_store_) {
      DynamicPhotoStoreManager<ks::reco::DynamicMerchantPhotoStoreItemFactory>::Singleton()->Stop();
    } else {
      PhotoStoreManager<ks::reco::MerchantPhotoStoreItemFactory>::Singleton()->Stop();
    }
  };
}

template <>
std::function<void()>
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::Purge() {
  return [this]() {
    if (this->use_dynamic_photo_store_) {
      DynamicPhotoStoreManager<ks::reco::DynamicMerchantLivingPhotoStoreItemFactory>::Singleton()->Stop();
    } else {
      PhotoStoreManager<ks::reco::MerchantLivingPhotoStoreItemFactory>::Singleton()->Stop();
    }
  };
}

template <>
uint64 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return ks::reco::ItemMemoryInfo::GenKeysign(ks::reco::RecoEnum::ITEM_TYPE_PHOTO,
                                              ks::reco::RecoEnum::PAGE_TYPE_HOT, item_id);
}

template <>
uint64 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return ks::reco::ItemMemoryInfo::GenKeysign(ks::reco::RecoEnum::ITEM_TYPE_PHOTO,
                                              ks::reco::RecoEnum::PAGE_TYPE_HOT, item_id);
}

template <>
uint64 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return ks::reco::ItemMemoryInfo::GenKeysign(ks::reco::RecoEnum::ITEM_TYPE_PHOTO,
                                              ks::reco::RecoEnum::UNKNOWN_PAGE_TYPE, item_id);
}

template <>
uint64
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return ks::reco::ItemMemoryInfo::GenKeysign(ks::reco::RecoEnum::ITEM_TYPE_AUTHOR,
                                              ks::reco::RecoEnum::UNKNOWN_PAGE_TYPE, item_id);
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  auto *photo_info_item = dynamic_cast<ks::reco::PhotoInfoItem *>(item.get());
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "memory_info is null, item_id: " << item->GetId();
    return;
  }

  const ks::reco::PhotoInfo *photo_info = photo_info_item_memory_info->GetPhotoInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "photo_info is null, item_id: " << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *photo_info_item = dynamic_cast<ks::reco::PhotoInfoItem *>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "memory_info is null, item_id: " << item->GetId();
    return;
  }

  const ks::reco::PhotoInfo *photo_info = photo_info_item_memory_info->GetPhotoInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "photo_info is null, item_id: " << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  auto *photo_info_item = dynamic_cast<ks::reco::NewPhotoInfoItem *>(item.get());
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "memory_info is null, item_id: " << item->GetId();
    return;
  }

  const ks::reco::PhotoInfo *photo_info = photo_info_item_memory_info->GetPhotoInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "photo_info is null, item_id: " << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *photo_info_item = dynamic_cast<ks::reco::NewPhotoInfoItem *>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "memory_info is null, item_id: " << item->GetId();
    return;
  }

  const ks::reco::PhotoInfo *photo_info = photo_info_item_memory_info->GetPhotoInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "photo_info is null, item_id: " << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  auto *photo_info_item = dynamic_cast<ks::reco::MerchantPhotoStoreItem *>(item.get());
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info memory_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  const ks::reco::MerchantGoodsInfo *photo_info = photo_info_item_memory_info->GetMerchantGoodsInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info photo_info is null, item_id: "
                                  << item->GetId();
    return;
  }
  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *photo_info_item = dynamic_cast<ks::reco::MerchantPhotoStoreItem *>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info memory_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  const ks::reco::MerchantGoodsInfo *photo_info = photo_info_item_memory_info->GetMerchantGoodsInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_photo_store_info photo_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  auto *photo_info_item = dynamic_cast<ks::reco::MerchantLivingPhotoStoreItem *>(item.get());
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info unexpected type, item_id: "
                                  << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info memory_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  const ks::reco::MerchantPhotoLivingInfo *photo_info =
      photo_info_item_memory_info->GetMerchantPhotoLivingInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info photo_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *photo_info_item = dynamic_cast<ks::reco::MerchantLivingPhotoStoreItem *>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info unexpected type, item_id: "
                                  << item->GetId();
    return;
  }

  auto *photo_info_item_memory_info = photo_info_item->GetMemoryItem();
  if (!photo_info_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info memory_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  const ks::reco::MerchantPhotoLivingInfo *photo_info =
      photo_info_item_memory_info->GetMerchantPhotoLivingInfo();
  if (!photo_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "merchant_living_photo_store_info photo_info is null, item_id: "
                                  << item->GetId();
    return;
  }

  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *photo_info, name_path.path);
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          photo_info, [item = std::move(item)](const void *) {
                            // photo_info is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release photo_info.
                          }));
  }
}

template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::protoutil::AttrKVItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_, nullptr);
}

template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::GetDynamicPhotoStore() {
  bool init_success = FlatIndexAttrMetaDataHolder::Singleton()->RegistAllAttrMetaData();
  if (!init_success) {
    return nullptr;
  }
  auto attr_meta_data = FlatIndexAttrMetaDataHolder::Singleton()->GetAttrMetaData();
  // 初始化 photo store 需要传入构造好的 factory 类
  auto factory_ptr = std::make_shared<reco::protoutil::DynamicAttrKVItemFactory>(attr_meta_data);
  if (FLAGS_photo_store_fetch_required_attr_only) {
    std::set<std::string> attrs;
    for (const auto &pair : *attr_meta_data) {
      attrs.insert(pair.first);
    }
    return DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAttrKVItemFactory>::Singleton()
        ->GetDynamicPhotoStore(photo_store_kconf_key_, factory_ptr, &attrs);
  } else {
    return DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAttrKVItemFactory>::Singleton()
        ->GetDynamicPhotoStore(photo_store_kconf_key_, factory_ptr);
  }
}

// kv 算子，purge 时不 stop cache
template <>
std::function<void()> CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::Purge() {
  return []() {};
}

template <>
uint64 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return item_key;
}

bool CalculateAttrKey(const std::string &attr_name, const std::string &attr_type_str, uint32 *attr_key) {
  using ks::reco::protoutil::ConvertAttrNameAndTypeToAttrKey;
  if (attr_type_str == "int") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kInt);
  } else if (attr_type_str == "float") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kFloat);
  } else if (attr_type_str == "string") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kString);
  } else if (attr_type_str == "int_list") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kIntList);
  } else if (attr_type_str == "float_list") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kFloatList);
  } else if (attr_type_str == "string_list") {
    *attr_key = ConvertAttrNameAndTypeToAttrKey(attr_name, ks::reco::protoutil::AttrType::kStringList);
  } else {
    LOG(ERROR)
        << "CommonRecoDistributedIndexItemAttrEnricher init failed! Invalid attr type str, must be type "
           "string, please check config. name:"
        << attr_name << " str:" << attr_type_str
        << " which should be one of 'int', 'float', 'string', 'int_list', 'float_list', 'string_list'";
    return false;
  }
  return true;
}

template <>
bool CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::SpecializedInitProcess() {
  // Older version does not support "attrs" config, because attr type is required to be known.
  // The distributed index server now support fetch all attr name and types, we can use this feature.
  // Only one method should be used 'attrs' or 'attr_name_types'
  auto *attr_without_types = config()->Get("attrs");
  bool attrs_config_exists = attr_without_types && attr_without_types->IsArray();
  if (attrs_config_exists) {
    std::shared_ptr<std::unordered_map<std::string, int>> attr_types =
        FlatIndexAttrMetaDataHolder::Singleton()->GetAttrTypesByKconfKey(photo_store_kconf_key_);
    if (!attr_types) {
      LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Invalid attr types map. ";
      return false;
    }

    for (const auto *attr : attr_without_types->array()) {
      std::string attr_name;
      std::string attr_type_str;
      std::string attr_name_alias;
      if (attr->IsString()) {
        attr_name = attr->StringValue();
        attr_name_alias = attr_name;
      } else if (attr->IsObject()) {
        attr_name = attr->GetString("name");
        attr_type_str = attr->GetString("type");
        attr_name_alias = attr->GetString("as", attr_name);
        if (attr_name.empty()) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Invalid 'attrs' config. "
                        "must as {\"name\": \"attr_name\", \"type\":\"int\" } format";
          return false;
        }
      }

      if (attr_name.empty()) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Invalid 'attrs' config.";
        return false;
      }

      uint32 attr_key = 0;
      uint32 alternative_attr_key = 0;
      bool attr_has_alternative = false;
      std::string ab_alternative;
      if (field_replacer_ && field_replacer_->IsReplacedField(attr_name, &ab_alternative)) {
        attr_has_alternative = true;
      }

      if (attr_type_str.empty()) {
        // Get type from response
        auto iter = attr_types->find(attr_name);
        if (iter == attr_types->end()) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Can't find attr '"
                     << attr_name << "' type.";
          if (FLAGS_distributed_index_skip_init_failed_for_attr) {
            base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "process_exception",
                                                           GlobalHolder::GetServiceIdentifier(), "",
                                                           "distributed_index_attr_init_failed:" + attr_name);
            continue;
          } else {
            return false;
          }
        }

        // When add new attr type, this should change too
        if (iter->second <= static_cast<int>(ks::reco::protoutil::AttrType::kInvalid) ||
            iter->second > static_cast<int>(ks::reco::protoutil::AttrType::kStringList)) {
          LOG(ERROR)
              << "CommonRecoDistributedIndexItemAttrEnricher init failed! AttrType invalid! unknown value:"
              << iter->second;
          return false;
        }

        attr_key = ks::reco::protoutil::ConvertAttrNameAndTypeToAttrKey(
            attr_name, static_cast<ks::reco::protoutil::AttrType>(iter->second));
        alternative_attr_key = attr_key;
        if (attr_has_alternative) {
          alternative_attr_key = ks::reco::protoutil::ConvertAttrNameAndTypeToAttrKey(
              ab_alternative, static_cast<ks::reco::protoutil::AttrType>(iter->second));
        }
      } else {
        if (!CalculateAttrKey(attr_name, attr_type_str, &attr_key)) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!";
          return false;
        }
        alternative_attr_key = attr_key;
        if (attr_has_alternative && !CalculateAttrKey(ab_alternative, attr_type_str, &alternative_attr_key)) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!";
          return false;
        }
      }

      // key 使用原 attr 名从 reader 中查询，此处使用 alias 用来查找 attr 和上报。
      attr_kv_special_data_.emplace_back(AttrKVSpecialData{.name = attr_name_alias,
                                                           .key = attr_key,
                                                           .ab_alternative_key = alternative_attr_key,
                                                           .accessor = nullptr});
    }
  }

  auto *attrs = config()->Get("attr_name_types");
  bool attr_name_types_exists = attrs && attrs->IsObject();
  if ((attrs_config_exists && attr_name_types_exists) || (!attrs_config_exists && !attr_name_types_exists)) {
    LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Either one config should exists "
                  "'attrs' or 'attr_name_types'";
    return false;
  }

  if (attr_name_types_exists) {
    for (const auto pair : attrs->objects()) {
      std::string attr_name = pair.first;
      auto attr_type_json = pair.second;

      if (!attr_type_json->IsString()) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Invalid 'attr_name_types' "
                      "config, must be name type pair.";
        return false;
      }

      uint32 attr_key = 0;
      auto alternative_attr_key = attr_key;
      std::string ab_alternative;
      std::string attr_type_str = attr_type_json->StringValue();
      if (field_replacer_ && field_replacer_->IsReplacedField(attr_name, &ab_alternative)) {
        if (!CalculateAttrKey(ab_alternative, attr_type_str, &alternative_attr_key)) {
          return false;
        }
      }
      if (!CalculateAttrKey(attr_name, attr_type_str, &attr_key)) {
        return false;
      }

      attr_kv_special_data_.emplace_back(AttrKVSpecialData{.name = attr_name,
                                                           .key = attr_key,
                                                           .ab_alternative_key = alternative_attr_key,
                                                           .accessor = nullptr});
    }
  }

  // For perf
  attr_path_config_.clear();
  attr_path_config_.reserve(attr_kv_special_data_.size());
  for (auto &data : attr_kv_special_data_) {
    attr_path_config_.emplace_back(
        PhotoInfoAttrConfig{.name = data.name, .path = std::vector<int>(), .accessor = nullptr});
  }

  return true;
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::SpecializedEnrichInit(
    MutableRecoContextInterface *context) {
  for (auto &data : attr_kv_special_data_) {
    data.accessor = context->GetItemAttrAccessor(data.name);
  }
  if (field_replacer_ && field_replacer_->IsHitAb(context->GetUserId(), context->GetDeviceId(), 0)) {
    hit_ab_alternative_ = true;
  } else {
    hit_ab_alternative_ = false;
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item_ptr) {
  auto *item = dynamic_cast<ks::reco::protoutil::AttrKVItem *>(item_ptr.get());
  if (!item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  auto item_data = item->GetRawItemData();
  if (!item_data || !item_data->reader) {
    CL_LOG_EVERY_N(WARNING, 1000) << "AttrKV item no reader, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  int64 int_val = 0;
  float float_val = 0.0;
  std::string str_val;
  std::vector<int64> int_list_val;
  std::vector<double> double_list_val;
  std::vector<std::string> str_list_val;

  for (int i = 0; i < attr_kv_special_data_.size(); ++i) {
    const auto &data = attr_kv_special_data_[i];
    uint32 attr_key = hit_ab_alternative_ ? data.ab_alternative_key : data.key;
    auto attr_type = ks::reco::protoutil::GetAttrTypeFromAttrKey(attr_key);
    auto accessor = data.accessor;
    auto reader = item_data->reader;
    auto message_it = item_data->message_map.find(attr_key);

    switch (attr_type) {
      case ks::reco::protoutil::AttrType::kInt:
        if (reader->GetIntValue(attr_key, &int_val)) {
          result.SetIntAttr(accessor, int_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kFloat:
        if (reader->GetFloatValue(attr_key, &float_val)) {
          result.SetDoubleAttr(accessor, float_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kString:
        str_val.clear();
        if (message_it != item_data->message_map.end() && message_it->second) {
          result.SetPtrAttr(accessor, message_it->second);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else if (reader->GetStringValue(attr_key, &str_val)) {
          result.SetStringAttr(accessor, std::move(str_val), no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kIntList:
        int_list_val.clear();
        if (reader->GetIntListValue(attr_key, &int_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += int_list_val.size();
          result.SetIntListAttr(accessor, std::move(int_list_val), no_overwrite_);
        }
        break;

      case ks::reco::protoutil::AttrType::kFloatList:
        double_list_val.clear();
        if (reader->GetFloatListValue(attr_key, &double_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += double_list_val.size();
          result.SetDoubleListAttr(accessor, std::move(double_list_val), no_overwrite_);
        }
        break;

      case ks::reco::protoutil::AttrType::kStringList:
        str_list_val.clear();
        if (reader->GetStringListValue(attr_key, &str_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += str_list_val.size();
          result.SetStringListAttr(accessor, std::move(str_list_val), no_overwrite_);
        }
        break;

      default:
        break;
    }
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item_ptr) {
  auto *item = dynamic_cast<ks::reco::protoutil::AttrKVItem *>(item_ptr);
  if (!item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  auto item_data = item->GetItemData();
  if (!item_data || !item_data->reader) {
    CL_LOG_EVERY_N(WARNING, 1000) << "AttrKV item no reader, item_key: " << result.item_key
                                  << ", id: " << result.GetId() << ", type: " << result.GetType();
    return;
  }

  int64 int_val = 0;
  float float_val = 0.0;
  std::string str_val;
  std::vector<int64> int_list_val;
  std::vector<double> double_list_val;
  std::vector<std::string> str_list_val;

  for (int i = 0; i < attr_kv_special_data_.size(); ++i) {
    const auto &data = attr_kv_special_data_[i];
    uint32 attr_key = hit_ab_alternative_ ? data.ab_alternative_key : data.key;
    auto attr_type = ks::reco::protoutil::GetAttrTypeFromAttrKey(attr_key);
    auto accessor = data.accessor;
    auto reader = item_data->reader;
    auto message_it = item_data->message_map.find(attr_key);

    switch (attr_type) {
      case ks::reco::protoutil::AttrType::kInt:
        if (reader->GetIntValue(attr_key, &int_val)) {
          result.SetIntAttr(accessor, int_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kFloat:
        if (reader->GetFloatValue(attr_key, &float_val)) {
          result.SetDoubleAttr(accessor, float_val, no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kString:
        str_val.clear();
        if (message_it != item_data->message_map.end() && message_it->second) {
          result.SetPtrAttr(accessor, message_it->second);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        } else if (reader->GetStringValue(attr_key, &str_val)) {
          result.SetStringAttr(accessor, std::move(str_val), no_overwrite_);
          ++attrs_cnt_[i];
          attrs_total_size_[i] += 1;
        }
        break;

      case ks::reco::protoutil::AttrType::kIntList:
        int_list_val.clear();
        if (reader->GetIntListValue(attr_key, &int_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += int_list_val.size();
          result.SetIntListAttr(accessor, std::move(int_list_val), no_overwrite_);
        }
        break;

      case ks::reco::protoutil::AttrType::kFloatList:
        double_list_val.clear();
        if (reader->GetFloatListValue(attr_key, &double_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += double_list_val.size();
          result.SetDoubleListAttr(accessor, std::move(double_list_val), no_overwrite_);
        }
        break;

      case ks::reco::protoutil::AttrType::kStringList:
        str_list_val.clear();
        if (reader->GetStringListValue(attr_key, &str_list_val)) {
          ++attrs_cnt_[i];
          attrs_total_size_[i] += str_list_val.size();
          result.SetStringListAttr(accessor, std::move(str_list_val), no_overwrite_);
        }
        break;

      default:
        break;
    }
  }
}

// specialization for KuibaPredictPhotoStoreItem
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::GetPhotoStore() {
  LOG(INFO) << "GetPhotoStore for KuibaPredictPhotoStoreItem is not supported, "
               "please use GetDynamicPhotoStore";
  return nullptr;
}

// specialization for KuibaPredictPhotoStoreItem
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::DynamicKuibaPredictPhotoStoreItemFactory>::Singleton()
      ->GetDynamicPhotoStore(photo_store_kconf_key_);
}

template <>
std::function<void()>
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::Purge() {
  return []() {
    DynamicPhotoStoreManager<ks::reco::DynamicKuibaPredictPhotoStoreItemFactory>::Singleton()->Stop();
  };
}

template <>
uint64 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return ks::reco::ItemMemoryInfo::GenKeysign(kuiba_predict_item_type_,
                                              ks::reco::RecoEnum::PAGE_TYPE_KUIBA_PREDICT, item_id);
}

template <>
bool CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::AttrsFromPb() {
  return false;
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *kuiba_predict_photo_store_item = dynamic_cast<ks::reco::KuibaPredictPhotoStoreItem *>(item);

  if (!kuiba_predict_photo_store_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }

  auto *predict_item_memory_info = kuiba_predict_photo_store_item->GetMemoryItem();
  if (!predict_item_memory_info) {
    CL_LOG_EVERY_N(WARNING, 1000) << "memory_info is null, item_id: " << item->GetId();
    return;
  }

  const kuiba::PredictItem *predict_item =
      static_cast<const kuiba::PredictItem *>(predict_item_memory_info->GetItemInfo());

  folly::F14FastMap<std::string, std::pair<ItemAttr *, int>> required_attributes;
  for (int i = 0; i < attr_path_config_.size(); ++i) {
    required_attributes[attr_path_config_[i].name] = std::make_pair(attr_path_config_[i].accessor, i);
  }

  for (auto attr : predict_item->attr()) {
    auto it = required_attributes.find(attr.name());
    if (it == required_attributes.end()) {
      continue;
    }
    auto accessor = it->second.first;
    int index = it->second.second;
    ++attrs_cnt_[index];
    attrs_total_size_[index] += 1;
    switch (attr.type()) {
      case ::kuiba::CommonSampleEnum_AttrType_INT_ATTR:
        result.SetIntAttr(accessor, attr.int_value(), no_overwrite_);
        break;
      case ::kuiba::CommonSampleEnum_AttrType_FLOAT_ATTR:
        result.SetDoubleAttr(accessor, attr.float_value(), no_overwrite_);
        break;
      case ::kuiba::CommonSampleEnum_AttrType_STRING_ATTR:
        result.SetStringAttr(accessor, attr.string_value(), no_overwrite_);
        break;
      case ::kuiba::CommonSampleEnum_AttrType_INT_LIST_ATTR:
        result.SetIntListAttr(accessor,
                              std::vector<int64>(attr.int_list_value().begin(), attr.int_list_value().end()),
                              no_overwrite_);
        break;
      case ::kuiba::CommonSampleEnum_AttrType_FLOAT_LIST_ATTR:
        result.SetDoubleListAttr(
            accessor, std::vector<double>(attr.float_list_value().begin(), attr.float_list_value().end()),
            no_overwrite_);
        break;
      case ::kuiba::CommonSampleEnum_AttrType_STRING_LIST_ATTR:
        result.SetStringListAttr(
            accessor,
            std::vector<std::string>(attr.string_list_value().begin(), attr.string_list_value().end()),
            no_overwrite_);
        break;
      default:
        break;
    }
  }

  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          predict_item, [item = std::move(item)](const void *) {
                            // predict_item is owned by item, we capture item to make
                            // it safe to be used in this request.
                            //
                            // there is no need to release predict_item.
                          }));
  }
}

#ifdef BUILD_AD_INFER_SERVER
// specialization for AdProtoStoreItem
template <>
ks::photo_store::PhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::GetPhotoStore() {
  return PhotoStoreManager<ks::reco::protoutil::AdItemFactory>::Singleton()->GetPhotoStore(
      photo_store_kconf_key_);
}

// specialization for PhotoInfoItem 支持删除操作
template <>
ks::photo_store::DynamicPhotoStore *
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::GetDynamicPhotoStore() {
  return DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAdItemFactory>::Singleton()
      ->GetDynamicPhotoStore(photo_store_kconf_key_);
}

template <>
std::function<void()>
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::Purge() {
  return [this]() {
    if (this->use_dynamic_photo_store_) {
      DynamicPhotoStoreManager<ks::reco::protoutil::DynamicAdItemFactory>::Singleton()->Stop();
    } else {
      PhotoStoreManager<ks::reco::protoutil::AdItemFactory>::Singleton()->Stop();
    }
  };
}

template <>
uint64
CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::ItemIdToIndexItemKey(
    uint64 item_key, uint64 item_id) {
  return item_id;
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, ks::photo_store::Item *item) {
  auto *photo_info_item = dynamic_cast<ks::reco::protoutil::AdProtoStoreItem *>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }
  auto ad_item_doc = photo_info_item->GetAdItem();
  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *ad_item_doc, name_path.path);
    CL_LOG(ERROR) << "AdProtoStoreItem not support attr_path_config_ yet";
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }
  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          dynamic_cast<const ::google::protobuf::Message *>(ad_item_doc.get()),
                          [item = std::move(item)](const void *) {}));
  }
}

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  auto photo_info_item = std::dynamic_pointer_cast<ks::reco::protoutil::AdProtoStoreItem>(item);
  if (!photo_info_item) {
    CL_LOG_EVERY_N(WARNING, 1000) << "unexpected type, item_id: " << item->GetId();
    return;
  }
  auto ad_item_doc = photo_info_item->GetAdItem();
  for (int i = 0; i < attr_path_config_.size(); ++i) {
    const auto &name_path = attr_path_config_[i];
    size_t cnt =
        interop::SaveProtobufMessageToItemAttr(result, name_path.accessor, *ad_item_doc, name_path.path);
    CL_LOG(ERROR) << "AdProtoStoreItem not support attr_path_config_ yet";
    if (cnt > 0) {
      ++attrs_cnt_[i];
      attrs_total_size_[i] += cnt;
    }
  }
  if (save_item_info_to_attr_accessor_) {
    result.SetPtrAttr(save_item_info_to_attr_accessor_,
                      std::shared_ptr<const ::google::protobuf::Message>(
                          dynamic_cast<const ::google::protobuf::Message *>(ad_item_doc.get()),
                          [item = std::move(item)](const void *) {}));
  }
}
#endif

template <>
void CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>::SavePhotoStoreItem(
    const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
  LOG(ERROR) << "SavePhotoStoreItem for KuibaPredictPhotoStoreItem shared ptr is not supported. "
                "It is used by photo store only, please use dynamic photo store instead";
}

// processor 注册集中放置在最后, 方便查阅
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::PhotoInfoItem>)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexNewPhotoInfoItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::NewPhotoInfoItem>)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexAttrKVItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AttrKVItem>)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexKuibaPredictItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::KuibaPredictPhotoStoreItem>)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexMerchantItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantPhotoStoreItem>)
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexMerchantLivingItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::MerchantLivingPhotoStoreItem>)
#ifdef BUILD_AD_INFER_SERVER
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDistributedIndexAdPredictItemAttrEnricher,
                 CommonRecoDistributedIndexItemAttrEnricher<ks::reco::protoutil::AdProtoStoreItem>)
#endif
}  // namespace platform
}  // namespace ks
