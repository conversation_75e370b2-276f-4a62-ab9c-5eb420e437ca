#include "dragon/src/processor/common/enricher/common_reco_count_reco_result_enricher.h"

#include <iterator>

namespace ks {
namespace platform {

void CommonRecoCountRecoResultEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  context->SetIntCommonAttr(save_result_size_to_common_attr_, std::distance(begin, end));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCountRecoResultEnricher, CommonRecoCountRecoResultEnricher)

}  // namespace platform
}  // namespace ks
