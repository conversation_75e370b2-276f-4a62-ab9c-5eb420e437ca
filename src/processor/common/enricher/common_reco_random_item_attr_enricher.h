#pragma once

#include <string>

#include "base/random/pseudo_random.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoRandomItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRandomItemAttrEnricher() : random_(base::GetTimestamp()) {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    attr_name_ = config()->GetString("attr_name", "");
    std::string type = config()->GetString("attr_type", "double");
    if (attr_name_.empty()) {
      LOG(ERROR) << "CommonRecoRandomItemAttrEnricher"
                 << " init failed! \"attr_name\" cannot be empty!";
      return false;
    }

    attr_type_ = RecoUtil::ParseAttrType(type);
    if (attr_type_ != AttrType::INT && attr_type_ != AttrType::FLOAT) {
      LOG(ERROR) << "CommonRecoRandomItemAttrEnricher"
                 << " init failed! Unsupported \"attr_type\": " << type
                 << ", could be: \"int\" or \"double\"";
      return false;
    }
    return true;
  }

 private:
  std::string attr_name_;
  AttrType attr_type_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRandomItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
