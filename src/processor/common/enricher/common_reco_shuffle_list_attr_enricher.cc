#include "dragon/src/processor/common/enricher/common_reco_shuffle_list_attr_enricher.h"

#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {
void CommonRecoShuffleListAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  if (!common_attr_.empty()) {
    ProcessCommonAttr(context);
  }
  if (!item_attr_.empty()) {
    ProcessItemAttr(context, begin, end);
  }
}

void CommonRecoShuffleListAttrEnricher::ProcessCommonAttr(MutableRecoContextInterface *context) {
  auto *accessor = context->GetCommonAttrAccessor(common_attr_);
  switch (accessor->value_type) {
    case AttrType::INT_LIST:
      if (auto int_list_values = context->GetIntListCommonAttr(common_attr_)) {
        std::vector<int64> values(int_list_values->begin(), int_list_values->end());
        for (int i = 0; i < values.size(); ++i) {
          std::swap(values[i], values[random_.GetInt(0, i)]);
        }
        context->SetIntListCommonAttr(common_attr_, std::move(values));
      }
      break;
    case AttrType::FLOAT_LIST: {
      if (auto double_list_values = context->GetDoubleListCommonAttr(common_attr_)) {
        std::vector<double> values(double_list_values->begin(), double_list_values->end());
        for (int i = 0; i < values.size(); ++i) {
          std::swap(values[i], values[random_.GetInt(0, i)]);
        }
        context->SetDoubleListCommonAttr(common_attr_, std::move(values));
      }
      break;
    }
    case AttrType::STRING_LIST: {
      if (auto string_list_values = context->GetStringListCommonAttr(common_attr_)) {
        for (int i = 0; i < string_list_values->size(); ++i) {
          std::swap((*string_list_values)[i], (*string_list_values)[random_.GetInt(0, i)]);
        }
        std::vector<std::string> values;
        values.reserve(string_list_values->size());
        for (auto sv : *string_list_values) {
          values.emplace_back(sv.data(), sv.size());
        }
        context->SetStringListCommonAttr(common_attr_, std::move(values));
      }
      break;
    }
    case AttrType::UNKNOWN: {
      CL_LOG(INFO) << "common_attr: " << common_attr_
                   << " is empty, shuffle common_attr cancelled, processor: " << GetName();
      break;
    }
    default: {
      const auto &attr_type = RecoUtil::GetAttrTypeName(accessor->value_type);
      CL_LOG_ERROR("shuffle_list_attr", "not_a_list:" + attr_type)
          << "common_attr: " << common_attr_ << " is not a list, type: " << attr_type;
      break;
    }
  }
}

void CommonRecoShuffleListAttrEnricher::ProcessItemAttr(MutableRecoContextInterface *context,
                                                        RecoResultConstIter begin, RecoResultConstIter end) {
  auto *accessor = context->GetItemAttrAccessor(item_attr_);
  switch (accessor->value_type) {
    case AttrType::INT_LIST:
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto int_list_values = context->GetIntListItemAttr(result, accessor)) {
          std::vector<int64> values(int_list_values->begin(), int_list_values->end());
          for (int i = 0; i < values.size(); ++i) {
            std::swap(values[i], values[random_.GetInt(0, i)]);
          }
          context->SetIntListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    case AttrType::FLOAT_LIST: {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto double_list_values = context->GetDoubleListItemAttr(result, accessor)) {
          std::vector<double> values(double_list_values->begin(), double_list_values->end());
          for (int i = 0; i < values.size(); ++i) {
            std::swap(values[i], values[random_.GetInt(0, i)]);
          }
          context->SetDoubleListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    }
    case AttrType::STRING_LIST: {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto string_list_values = context->GetStringListItemAttr(result, accessor)) {
          for (int i = 0; i < string_list_values->size(); ++i) {
            std::swap((*string_list_values)[i], (*string_list_values)[random_.GetInt(0, i)]);
          }
          std::vector<std::string> values;
          values.reserve(string_list_values->size());
          for (auto sv : *string_list_values) {
            values.emplace_back(sv.data(), sv.size());
          }
          context->SetStringListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    }
    case AttrType::UNKNOWN: {
      CL_LOG(INFO) << "item_attr: " << item_attr_
                   << " is empty, shuffle item_attr cancelled, processor: " << GetName();
      break;
    }
    default: {
      const auto &attr_type = RecoUtil::GetAttrTypeName(accessor->value_type);
      CL_LOG_ERROR("shuffle_list_attr", "not_a_list:" + attr_type)
          << "item_attr: " << item_attr_ << " is not a list, type: " << attr_type;
      break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoShuffleListAttrEnricher, CommonRecoShuffleListAttrEnricher)

}  // namespace platform
}  // namespace ks
