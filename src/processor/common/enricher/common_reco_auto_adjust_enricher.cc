#include "dragon/src/processor/common/enricher/common_reco_auto_adjust_enricher.h"

namespace ks {
namespace platform {

void CommonRecoAutoAdjustEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  absl::Span<const double> fractions;
  std::vector<double> fractions_value;
  std::string group_name = GetStringProcessorParameter(context, group_name_, "");
  std::string holder_name = group_name.empty() ? adjust_output_ : group_name + ":" + adjust_output_;
  if (history_input_save_mod_ == "local") {
    LocalHistoryFractions(holder_name, &fractions_value);
    fractions = fractions_value;
  } else if (history_input_save_mod_ == "customize") {
    auto fractions_opt = context->GetDoubleListCommonAttr(fractions_attr_);
    if (fractions_opt) {
      fractions = fractions_opt.value();
    } else {
      CL_LOG_EVERY_N(INFO, 100) << "Auto adjust break, fractions attr " << fractions_attr_ << " is empty.";
      return;
    }
  }

  if (fractions.empty()) {
    CL_LOG_EVERY_N(INFO, 100) << "Auto adjust break, fractions is empty.";
    return;
  }

  double set_point = GetDoubleProcessorParameter(context, "set_point");
  double adjust_output = 0.0f;
  if (adjust_function_ == "pid") {
    adjust_output = PidControl(context, fractions, set_point);
  }
  context->SetDoubleCommonAttr(adjust_output_, adjust_output);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(set_point * 1e6, kPerfNs, "global_holder",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), holder_name + ":set_point");
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      adjust_output * 1e3, kPerfNs, "auto_adjust", GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), holder_name + ":adjust_output");
}

void CommonRecoAutoAdjustEnricher::LocalHistoryFractions(const std::string &holder_name,
                                                         std::vector<double> *fractions) {
  const auto *fractions_ptr = GlobalHolder::WindowFraction(holder_name);
  if (fractions_ptr) {
    *fractions = *fractions_ptr;
  }
}

double CommonRecoAutoAdjustEnricher::PidControl(MutableRecoContextInterface *context,
                                                absl::Span<const double> fractions, double set_point) {
  double kp = GetDoubleProcessorParameter(context, "kp", default_kp_);
  double ki = GetDoubleProcessorParameter(context, "ki", default_ki_);
  double kd = GetDoubleProcessorParameter(context, "kd", default_kd_);
  double derivative = fractions.size() > 1 ? fractions.front() - fractions[1] : 0.0;
  double integral = 0;
  for (const auto fraction : fractions) {
    integral += fraction;
  }
  double error = fractions.front();
  double adjust_score = -kp * error - ki * integral - kd * derivative;
  CL_LOG_EVERY_N(INFO, 100) << "error=" << error << ", kp=" << kp << ", ki=" << ki
                            << ", integral=" << integral << ", kd=" << kd << ", derivative=" << derivative
                            << ", set_point=" << set_point << ", fractions.front()=" << fractions.front()
                            << ", adjust_score=" << adjust_score;
  return adjust_score;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAutoAdjustEnricher, CommonRecoAutoAdjustEnricher)

}  // namespace platform
}  // namespace ks
