#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "serving_base/util/scope_exit.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/flat_index_holder.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/module/photo_store_manager.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/photo_store/dynamic_photo_store_fetcher.h"
#include "ks/photo_store/photo_store_fetcher.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/merchant_living_photo_store_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/merchant_photo_store_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/new_photo_info_item.h"
#include "ks/reco_pub/reco/distributed_photo_info/photo_map_cache_api/photo_info_item.h"
#ifdef BUILD_AD_INFER_SERVER
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/ad_photo_store_item.h"
#endif
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"

namespace ks {
namespace platform {

DECLARE_bool(common_leaf_use_dynamic_photo_store);

template <typename PhotoItemType>
class CommonRecoDistributedIndexItemAttrEnricher : public CommonRecoBaseEnricher {
 protected:
  template <typename T>
  struct static_false : std::false_type {};

 public:
  CommonRecoDistributedIndexItemAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  std::function<void()> Purge() override {
    static_assert(static_false<PhotoItemType>::value, "Purge must be specialized");
    return []() {};
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    SpecializedEnrichInit(context);
    photo_store_rpc_req_cache_rate_ =
        GetDoubleProcessorParameter(context, "photo_store_rpc_req_cache_rate", 100.0);
    data_set_tags_bit_ = GetIntListProcessorParameter(context, "data_set_tags_bit");
    if (data_set_tags_bit_.size() == 0) {
      data_set_tags_bit_ = photo_store::DefaultDataSetTagsBit();
    }

    if (!has_attr_accessor_init_) {
      if (!save_item_info_to_attr_.empty()) {
        save_item_info_to_attr_accessor_ = context->GetItemAttrAccessor(save_item_info_to_attr_);
      }
      if (!item_id_attr_.empty()) {
        item_id_attr_accessor_ = context->GetItemAttrAccessor(item_id_attr_);
      }

      for (auto &name_path : attr_path_config_) {
        name_path.accessor = context->GetItemAttrAccessor(name_path.name);
      }

      attrs_cnt_.resize(attr_path_config_.size(), 0);
      attrs_total_size_.resize(attr_path_config_.size(), 0);

      has_attr_accessor_init_ = true;
    }

    index_item_keys_.clear();
    target_items_.clear();

    std::for_each(begin, end, [this, context](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      if (item_id_attr_accessor_ == nullptr) {
        uint64 id = result.GetId();
        index_item_keys_.push_back(ItemIdToIndexItemKey(item_key, id));
        target_items_.push_back(result);
      } else {
        auto p = result.GetIntAttr(item_id_attr_accessor_);
        if (p) {
          uint64 id = *p;
          index_item_keys_.push_back(id);
          target_items_.push_back(result);
        } else {
          CL_LOG_EVERY_N(WARNING, 1000)
              << "item ignored: cannot get id from int attr " << item_id_attr_ << ", item_key: " << item_key
              << RecoUtil::GetRequestInfoForLog(context);
        }
      }
    });

    if (include_browse_set_items_) {
      auto browsed_items = context->GetLatestBrowsedItems(include_browse_set_item_count_);
      for (const auto &item_key : browsed_items) {
        uint64 id = Util::GetId(item_key);
        index_item_keys_.push_back(ItemIdToIndexItemKey(item_key, id));
        target_items_.emplace_back(context->NewCommonRecoResult(item_key));
      }
    }

    for (const auto &attr_name : source_common_attrs_) {
      if (auto p = context->GetIntCommonAttr(attr_name)) {
        uint64 id = Util::GetId(*p);
        index_item_keys_.push_back(ItemIdToIndexItemKey(*p, id));
        target_items_.emplace_back(context->NewCommonRecoResult(*p));
      } else if (auto p = context->GetIntListCommonAttr(attr_name)) {
        for (const auto v : *p) {
          uint64 id = Util::GetId(v);
          index_item_keys_.push_back(ItemIdToIndexItemKey(v, id));
          target_items_.emplace_back(context->NewCommonRecoResult(v));
        }
      } else {
        CL_LOG(WARNING) << "cannot find int/int_list common_attr: " << attr_name;
      }
    }

    if (index_item_keys_.empty()) {
      CL_LOG(INFO) << "distributed index request cancelled with empty item list: " << request_info_;
      return;
    }

    if (use_dynamic_photo_store_) {
      QueryDynamicPhotoStore(context);
    } else {
      QueryPhotoStore(context);
    }
  }

 private:
  bool InitProcessor() override {
    no_overwrite_ = config()->GetBoolean("no_overwrite", false);

    photo_store_kconf_key_ = config()->GetString("photo_store_kconf_key", "");
    field_replacer_ = GetFieldReplacerFromPhotoStoreConfig(config()->GetString("photo_store_kconf_key", ""));

    use_dynamic_photo_store_ =
        config()->GetBoolean("use_dynamic_photo_store", false) || FLAGS_common_leaf_use_dynamic_photo_store;
    if (use_dynamic_photo_store_) {
      auto *dynamic_photo_store = GetDynamicPhotoStore();
      if (!dynamic_photo_store) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!"
                   << " failed to initialize photo store";
        return false;
      }
      dynamic_photo_store_fetcher_.Init(dynamic_photo_store);
      request_info_ = "kess_service: " + dynamic_photo_store->GetConfig().service_name + ", timeout_ms: " +
                      std::to_string(dynamic_photo_store->GetConfig().grpc_timeout.count()) + "ms";
    } else {
      auto *photo_store = GetPhotoStore();

      if (!photo_store) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!"
                   << " failed to initialize photo store";
        return false;
      }
      photo_store_fetcher_.Init(photo_store);
      request_info_ = "kess_service: " + photo_store->GetConfig().service_name +
                      ", timeout_ms: " + std::to_string(photo_store->GetConfig().grpc_timeout.count()) + "ms";
    }

    perf_log_ = config()->GetString("perf_log", "distributed_index");

    auto *attrs = config()->Get("attrs");
    // TODO(qianlei): 删掉这坨判断
#ifdef BUILD_AD_INFER_SERVER
    if (attrs && attrs->IsArray() && !std::is_same<PhotoItemType, ks::reco::protoutil::AttrKVItem>::value &&
        !std::is_same<PhotoItemType, ks::reco::protoutil::AdProtoStoreItem>::value) {
#else
    if (attrs && attrs->IsArray() && !std::is_same<PhotoItemType, ks::reco::protoutil::AttrKVItem>::value) {
#endif
      // TODO(huiyiqun): for PhotoInfo only
      std::string kPhotoInfoMessageType = "ks.reco.PhotoInfo";
      // NOTE(zhangsukun): for 单列 电商索引 MerchantPhotoStoreItem and MerchantLivingPhotoStoreItem
      if (std::is_same<PhotoItemType, ks::reco::MerchantPhotoStoreItem>::value) {
        kPhotoInfoMessageType = "ks.reco.MerchantGoodsInfo";
      } else if (std::is_same<PhotoItemType, ks::reco::MerchantLivingPhotoStoreItem>::value) {
        kPhotoInfoMessageType = "ks.reco.MerchantPhotoLivingInfo";
      }
      static const google::protobuf::Descriptor *descriptor = CHECK_NOTNULL(
          ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(kPhotoInfoMessageType));
      for (const auto *attr_json : attrs->array()) {
        std::string pb_path_str;
        std::string attr_name;
        if (!AttrsFromPb()) {
          // 目前 ks::reco::KuibaPredictPhotoStoreItem 专用
          if (attr_json->IsString()) {
            attr_name = attr_json->StringValue();
            attr_path_config_.emplace_back(PhotoInfoAttrConfig{
                .name = std::move(attr_name), .path = std::vector<int>(), .accessor = nullptr});
          } else {
            LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Item of attrs should be a"
                       << " string! Value found: " << attr_json->ToString();
            return false;
          }
          continue;
        }

        if (attr_json->IsString()) {
          pb_path_str = attr_json->StringValue();
          attr_name = pb_path_str;
        } else if (attr_json->IsObject()) {
          pb_path_str = attr_json->GetString("path");
          attr_name = attr_json->GetString("name");
          if (attr_name.empty()) {
            attr_name = pb_path_str;
          }
        } else {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! Item of attrs should be a"
                     << " string! Value found: " << attr_json->ToString();
          return false;
        }
        std::vector<int> field_path;
        if (!interop::ConvertMsgPathToFieldIndexPath(descriptor, pb_path_str, &field_path)) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! attr_name: " << attr_name
                     << ", invalid msg path for " << kPhotoInfoMessageType << ": " << pb_path_str;
          return false;
        }

        attr_path_config_.emplace_back(PhotoInfoAttrConfig{
            .name = std::move(attr_name), .path = std::move(field_path), .accessor = nullptr});
      }
    }

    save_item_info_to_attr_ = config()->GetString("save_item_info_to_attr");

    if (attr_path_config_.empty() && save_item_info_to_attr_.empty() &&
        !std::is_same<PhotoItemType, ks::reco::protoutil::AttrKVItem>::value) {
      LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! either attr or "
                    "save_item_info_to_attr or attr_name_types is required.";
      return false;
    }

    auto *item_source = config()->Get("additional_item_source");
    if (item_source) {
      if (!item_source->IsObject()) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher"
                   << " init failed! \"additional_item_source\" should"
                   << " be a dict";
        return false;
      }
      auto *common_attr = item_source->Get("common_attr");
      if (common_attr &&
          !RecoUtil::ExtractStringListFromJsonConfig(common_attr, &source_common_attrs_, true, true)) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed! "
                   << " 'common_attr' should be a string array!"
                   << " Value found: " << common_attr->ToString();
        return false;
      }

      auto *latest_browse_set_item = item_source->Get("latest_browse_set_item");
      if (latest_browse_set_item) {
        include_browse_set_items_ = true;
        if (!latest_browse_set_item->IntValue(&include_browse_set_item_count_)) {
          LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!"
                     << " 'latest_browse_set_item' should be an int! Value found: "
                     << latest_browse_set_item->ToString();
          return false;
        }
      } else {
        include_browse_set_items_ = false;
      }
    }

    item_id_attr_ = config()->GetString("item_id_attr", "");

    std::string kuiba_predict_item_type_str =
        config()->GetString("kuiba_predict_item_type", "ITEM_TYPE_LIVESTREAM");
    bool parse_success = RecoEnum_ItemType_Parse(kuiba_predict_item_type_str, &kuiba_predict_item_type_);
    if (!parse_success) {
      LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!"
                 << " failed to parse 'kuiba_predict_item_type' " << kuiba_predict_item_type_str;
      return false;
    }

    request_data_set_tags_attr_ = config()->GetString("photo_store_request_data_set_tags_attr", "");

    return SpecializedInitProcess();
  }

  void QueryDynamicPhotoStore(MutableRecoContextInterface *context) {
    auto dynamic_item_vector = std::make_shared<ks::photo_store::DynamicPhotoStoreFetcher::ItemVector>();
    std::unordered_set<int32> request_data_set_tags =
        GetRequestDataSetTags(context, request_data_set_tags_attr_);
    RegisterAsyncCallback(
        context,
        dynamic_photo_store_fetcher_.AsyncMultiGet(index_item_keys_, dynamic_item_vector.get(), false,
                                                   perf_log_, nullptr, photo_store_rpc_req_cache_rate_,
                                                   request_data_set_tags, nullptr, data_set_tags_bit_),
        [this, context, dynamic_item_vector, target_items = std::move(target_items_)](
            const ks::photo_store::DynamicPhotoStoreFetcher::ItemVector *item_infos) {
          if (!item_infos) {
            CL_LOG_ERROR("distributed_index", "null_response")
                << "fail to get response from distributed index server, " << request_info_;
            return;
          }

          if (item_infos->size() != target_items.size()) {
            CL_LOG_ERROR("distributed_index", "response_size_mismatch")
                << "item list size mismatch, request num: " << target_items.size()
                << ", response num: " << item_infos->size() << ", requst_info: " << request_info_;
            return;
          }

          std::fill(attrs_total_size_.begin(), attrs_total_size_.end(), 0);
          std::fill(attrs_cnt_.begin(), attrs_cnt_.end(), 0);

          int item_hit = 0;
          for (int i = 0; i < item_infos->size(); ++i) {
            const auto &item = item_infos->at(i);
            const auto &result = target_items[i];
            if (!item) {
              CL_LOG_EVERY_N(WARNING, 1000) << "item miss in photo_store, key: " << result.item_key
                                            << ", id: " << result.GetId() << ", type: " << result.GetType();
              continue;
            }
            ++item_hit;
            SavePhotoStoreItem(result, const_cast<photo_store::Item *>(item));
          }
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              kPerfBase * item_hit / target_items.size(), kPerfNs, "forward_index.item_hit",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              target_items.size(), kPerfNs, "forward_index.item_total", GlobalHolder::GetServiceIdentifier(),
              context->GetRequestType(), GetName());

          for (int i = 0; i < attr_path_config_.size(); ++i) {
            const auto &attr_name = attr_path_config_[i].name;
            base::perfutil::PerfUtilWrapper::IntervalLogStash(
                kPerfBase * attrs_cnt_[i] / std::max(item_hit, 1), kPerfNs, "forward_index.attr_hit",
                GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
            base::perfutil::PerfUtilWrapper::IntervalLogStash(
                kPerfBase * attrs_total_size_[i] / std::max(attrs_cnt_[i], 1), kPerfNs,
                "forward_index.attr_size", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                GetName(), attr_name);
          }

          CL_LOG(INFO) << "received response from distributed index server, request num: "
                       << target_items.size() << ", response num: " << item_infos->size();
        },
        request_info_);
  }

  void QueryPhotoStore(MutableRecoContextInterface *context) {
    auto item_vector = std::make_shared<ks::photo_store::PhotoStoreFetcher::ItemVector>();
    RegisterAsyncCallback(
        context,
        photo_store_fetcher_.AsyncMultiGet(index_item_keys_, item_vector.get(), false, perf_log_, nullptr,
                                           photo_store_rpc_req_cache_rate_),
        [this, context, item_vector, target_items = std::move(target_items_)](
            const ks::photo_store::PhotoStoreFetcher::ItemVector *item_infos) {
          if (!item_infos) {
            CL_LOG_ERROR("distributed_index", "null_response")
                << "fail to get response from distributed index server, requst_info: " << request_info_;
            return;
          }

          if (item_infos->size() != target_items.size()) {
            CL_LOG_ERROR("distributed_index", "response_size_mismatch")
                << "item list size mismatch, request num: " << target_items.size()
                << ", response num: " << item_infos->size() << ", requst_info: " << request_info_;
            return;
          }

          std::fill(attrs_total_size_.begin(), attrs_total_size_.end(), 0);
          std::fill(attrs_cnt_.begin(), attrs_cnt_.end(), 0);

          int item_hit = 0;
          for (int i = 0; i < item_infos->size(); ++i) {
            const auto &item = item_infos->at(i);
            const auto &result = target_items[i];
            if (!item) {
              CL_LOG_EVERY_N(WARNING, 1000) << "item miss in photo_store, key: " << result.item_key
                                            << ", id: " << result.GetId() << ", type: " << result.GetType();
              continue;
            }
            ++item_hit;
            SavePhotoStoreItem(result, item);
          }

          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              kPerfBase * item_hit / target_items.size(), kPerfNs, "forward_index.item_hit",
              GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
          base::perfutil::PerfUtilWrapper::IntervalLogStash(
              target_items.size(), kPerfNs, "forward_index.item_total", GlobalHolder::GetServiceIdentifier(),
              context->GetRequestType(), GetName());

          for (int i = 0; i < attr_path_config_.size(); ++i) {
            const auto &attr_name = attr_path_config_[i].name;
            base::perfutil::PerfUtilWrapper::IntervalLogStash(
                kPerfBase * attrs_cnt_[i] / std::max(item_hit, 1), kPerfNs, "forward_index.attr_hit",
                GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(), attr_name);
            base::perfutil::PerfUtilWrapper::IntervalLogStash(
                kPerfBase * attrs_total_size_[i] / std::max(attrs_cnt_[i], 1), kPerfNs,
                "forward_index.attr_size", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                GetName(), attr_name);
          }

          CL_LOG(INFO) << "received response from distributed index server, request num: "
                       << target_items.size() << ", response num: " << item_infos->size();
        },
        request_info_);
  }

 private:
  // following member method must be specialized
  ks::photo_store::PhotoStore *GetPhotoStore() {
    // return PhotoStoreManager<PhotoInfoItemFactoryType>::Singleton()->GetPhotoStore();
    static_assert(static_false<PhotoItemType>::value, "GetPhotoStore must be specialized");
    return nullptr;
  }

  // following member method must be specialized
  ks::photo_store::DynamicPhotoStore *GetDynamicPhotoStore() {
    // return PhotoStoreManager<PhotoInfoItemFactoryType>::Singleton()->GetPhotoStore();
    static_assert(static_false<PhotoItemType>::value, "GetPhotoStore must be specialized");
    return nullptr;
  }

  void SavePhotoStoreItem(const CommonRecoResult &result, std::shared_ptr<ks::photo_store::Item> item) {
    static_assert(static_false<PhotoItemType>::value, "SavePhotoStoreItem must be specialized");
  }

  void SavePhotoStoreItem(const CommonRecoResult &result, ks::photo_store::Item *item) {
    static_assert(static_false<PhotoItemType>::value, "SavePhotoStoreItem must be specialized");
  }

  uint64 ItemIdToIndexItemKey(uint64 item_key, uint64 item_id) {
    static_assert(static_false<PhotoItemType>::value, "ItemIdToIndexItemKey must be specialized");
    return 0;
  }

  bool SpecializedInitProcess() {
    return true;
  }
  bool AttrsFromPb() {
    return true;
  }
  void SpecializedEnrichInit(MutableRecoContextInterface *context) {}

 protected:
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    if (use_dynamic_photo_store_) {
      return GetIntProcessorParameter(context, "partition_size", 0);
    } else {
      return 0;
    }
  }

 private:
  struct PhotoInfoAttrConfig {
    std::string name;
    std::vector<int> path;
    ItemAttr *accessor = nullptr;
  };

  // use when PhotoItemType = ks::reco::protoutil::AttrKVItem
  struct AttrKVSpecialData {
    std::string name;
    uint32 key = 0;
    uint32 ab_alternative_key = 0;
    ItemAttr *accessor = nullptr;
  };

 private:
  bool has_attr_accessor_init_ = false;
  bool hit_ab_alternative_ = false;
  std::vector<PhotoInfoAttrConfig> attr_path_config_;
  std::vector<std::string> source_common_attrs_;
  std::string save_item_info_to_attr_;
  ItemAttr *save_item_info_to_attr_accessor_ = nullptr;
  std::string request_info_;
  std::string item_id_attr_;
  std::string request_data_set_tags_attr_;
  ItemAttr *item_id_attr_accessor_ = nullptr;
  bool include_browse_set_items_ = false;
  int64 include_browse_set_item_count_ = 0;
  bool use_dynamic_photo_store_ = false;
  double photo_store_rpc_req_cache_rate_ = 100.0;
  bool no_overwrite_ = false;
  std::string photo_store_kconf_key_ = "";
  std::vector<int64> data_set_tags_bit_;

  std::vector<uint64> index_item_keys_;
  std::vector<CommonRecoResult> target_items_;

  ks::photo_store::PhotoStoreFetcher photo_store_fetcher_;
  ks::photo_store::DynamicPhotoStoreFetcher dynamic_photo_store_fetcher_;
  std::string perf_log_;

  std::vector<int> attrs_cnt_, attrs_total_size_;

  std::vector<AttrKVSpecialData> attr_kv_special_data_;

  std::vector<std::string> attr_names_;

  ks::reco::RecoEnum_ItemType kuiba_predict_item_type_ = ks::reco::RecoEnum::ITEM_TYPE_LIVESTREAM;

  static constexpr float kPerfBase = 1000.0;

  std::shared_ptr<colossusdb::FieldReplaceKconfHolder> field_replacer_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoDistributedIndexItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
