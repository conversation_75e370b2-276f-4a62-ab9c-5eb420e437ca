#pragma once

#include <string>
#include <vector>

#include "dragon/src/common_reco_web_service.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/common_reco/index/common_query_web_service.h"
#include "ks/common_reco/index/common_reco_index_manager.h"

DECLARE_string(index_dir);
DECLARE_string(index_queue);
DECLARE_int32(index_query_thread_num);

namespace ks {
namespace platform {

class CommonRecoLocalIndexItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoLocalIndexItemAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  std::function<void()> Purge() override {
    return []() {
      if (CommonRecoIndexManager::Singleton()->HasIndexLoaded()) {
        CommonRecoIndexManager::Singleton()->StopAndWait();
        LOG(INFO) << "CommonIndexManager stopped.";
        ::google::FlushLogFiles(::google::INFO);
      }
    };
  }

 protected:
  virtual const char *GetProcessorName() const {
    return "CommonRecoLocalIndexItemAttrEnricher";
  }

  bool InitConfigs() {
    item_miss_tag_ = config()->GetString("item_miss_tag");
    auto *attrs = config()->Get("attrs");
    if (!attrs || !attrs->IsArray()) {
      LOG(ERROR) << GetProcessorName() << " init failed! Missing \"attrs\" config or it is not"
                 << " an array.";
      return false;
    }

    attr_name_list_.clear();
    attr_type_list_.clear();
    for (const auto *attr_json : attrs->array()) {
      if (attr_json->IsObject()) {
        // 保持对旧格式的兼容: {"name": "xxx", "type": "yyy"}
        attr_name_list_.push_back(attr_json->GetString("name"));
        auto type = RecoUtil::ParseAttrType(attr_json->GetString("type"));
        if (type == AttrType::UNKNOWN) {
          LOG(ERROR) << "Unknown attr type: " << attr_json->GetString("type");
          return false;
        }
        attr_type_list_.push_back(type);
      } else if (attr_json->IsString()) {
        attr_name_list_.push_back(attr_json->StringValue());
      } else {
        LOG(ERROR) << "Item of attrs should be a string or dict! Value found: " << attr_json->ToString();
        return false;
      }
    }

    for (const auto &attr : attr_name_list_) {
      attr_keysign_list_.push_back(base::CalcTermSign(attr.data(), attr.size()));
      attr_perf_count_.push_back(AttrPerfCount());
    }

    auto *item_source = config()->Get("additional_item_source");
    if (item_source) {
      if (!item_source->IsObject()) {
        LOG(ERROR) << GetProcessorName() << " init failed! \"additional_item_source\" should"
                   << " be a dict";
        return false;
      }

      auto *latest_browse_set_item = item_source->Get("latest_browse_set_item");
      if (latest_browse_set_item) {
        include_browse_set_items_ = true;
        if (!latest_browse_set_item->IntValue(&include_browse_set_item_count_)) {
          LOG(ERROR) << GetProcessorName() << " init failed! \"latest_browse_set_item\" should"
                     << " be an int! Value found: " << latest_browse_set_item->ToString();
          return false;
        }
      } else {
        include_browse_set_items_ = false;
      }

      auto *common_attr = item_source->Get("common_attr");
      if (common_attr) {
        if (!common_attr->IsArray()) {
          LOG(ERROR) << GetProcessorName() << " init failed! \"common_attr\" should be an array!"
                     << " Value found: " << common_attr->ToString();
          return false;
        }
        for (const auto *attr : common_attr->array()) {
          if (!attr->IsString()) {
            LOG(ERROR) << GetProcessorName() << " init failed! Item of \"common_attr\" should be string!"
                       << " Value found: " << attr->ToString();
            return false;
          }
          std::string attr_name = attr->StringValue();
          if (!attr_name.empty()) {
            source_common_attrs_.push_back(attr_name);
          }
        }
      }

      auto *item_attr = item_source->Get("item_attr");
      if (item_attr) {
        if (!item_attr->IsArray()) {
          LOG(ERROR) << GetProcessorName() << " init failed! \"item_attr\" should be an array!"
                     << " Value found: " << item_attr->ToString();
          return false;
        }
        for (const auto *attr : item_attr->array()) {
          if (!attr->IsString()) {
            LOG(ERROR) << GetProcessorName() << " init failed! Item of \"item_attr\" should be string!"
                       << " Value found: " << attr->ToString();
            return false;
          }
          std::string attr_name = attr->StringValue();
          if (!attr_name.empty()) {
            source_item_attrs_.push_back(attr_name);
          }
        }
      }

      include_reco_results_ = item_source->GetBoolean("reco_results", true);
    }

    no_overwrite_ = config()->GetBoolean("no_overwrite", false);

    max_value_bytes_ = config()->GetInt("max_value_bytes", 1024 * 1024);

    return true;
  }

  virtual bool InitIndex() {
    int query_thread_num =
        FLAGS_index_query_thread_num <= 0 ? GlobalHolder::GetWorkerThreadNum() : FLAGS_index_query_thread_num;
    CommonRecoIndexManager::Singleton()->LoadIndex(
        FLAGS_index_queue, FLAGS_index_dir, GlobalHolder::GetServiceIdentifier(), query_thread_num, true);
    if (!CommonRecoIndexManager::Singleton()->HasIndexLoaded()) {
      LOG(ERROR) << "CommonRecoLocalIndexItemAttrEnricher init failed! Common index not load!";
      return false;
    }

    auto *index_manager = CommonRecoIndexManager::Singleton()->GetIndexManager();
    if (index_manager) {
      static std::once_flag once;
      std::call_once(once, [index_manager]() {
        // common index 的 web 查询接口
        CommonRecoWebService::Instance()->RegisterWebHandler("/status",
                                                             new CommonStatusHandler(index_manager));
        CommonRecoWebService::Instance()->RegisterWebHandler("/doc_info",
                                                             new CommonDocInfoHandler(index_manager));
        CommonRecoWebService::Instance()->RegisterWebHandler("/query", new QueryTestHandler(index_manager));
        CommonRecoWebService::Instance()->RegisterWebHandler("/common_index/status",
                                                             new CommonStatusHandler(index_manager));
        CommonRecoWebService::Instance()->RegisterWebHandler("/common_index/doc_info",
                                                             new CommonDocInfoApiHandler(index_manager));
        CommonRecoWebService::Instance()->RegisterWebHandler("/common_index/query",
                                                             new QueryTestApiHandler(index_manager));
      });
    }

    return true;
  }

  bool EnrichInit(MutableRecoContextInterface *context, const RecoResultConstIter &begin,
                  const RecoResultConstIter &end, std::vector<CommonRecoResult> *target_items,
                  int *total_item_num);

  void EnrichEndPerf(MutableRecoContextInterface *context, int total_item_num, int64 duration);

 private:
  bool InitProcessor() override {
    if (!InitConfigs()) {
      return false;
    }

    if (!InitIndex()) {
      return false;
    }

    item_key_attr_ = config()->GetString("item_key_attr", "");

    return true;
  }

 private:
  void GetItemAttrForItem(adsindexing::Index *current_index, const CommonRecoResult &result,
                          const std::vector<CommonIndexEnum::AttrType> &attr_types);

 protected:
  struct AttrPerfCount {
    int attr_count = 0;
    int attr_total_size = 0;
    int attr_type_mismatch_count = 0;
    int unknown_attr_count = 0;

    void Clear() {
      attr_count = 0;
      attr_total_size = 0;
      attr_type_mismatch_count = 0;
      unknown_attr_count = 0;
    }
  };

 protected:
  std::string item_miss_tag_;
  ItemAttr *item_miss_tag_accessors_ = nullptr;
  std::vector<std::string> attr_name_list_;
  std::vector<uint64> attr_keysign_list_;
  std::vector<AttrType> attr_type_list_;
  std::vector<ItemAttr *> attr_accessors_;
  bool include_browse_set_items_ = false;
  int64 include_browse_set_item_count_ = 0;
  std::vector<std::string> source_common_attrs_;
  std::vector<std::string> source_item_attrs_;
  bool include_reco_results_ = true;
  bool no_overwrite_ = false;
  int max_value_bytes_ = 0;
  std::string item_key_attr_;
  ItemAttr *item_key_attr_accessor_ = nullptr;

  int missing_item_count_ = 0;
  uint64 missing_item_key_ = 0;
  std::vector<AttrPerfCount> attr_perf_count_;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoLocalIndexItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
