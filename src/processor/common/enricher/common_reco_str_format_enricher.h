#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoStrFormatEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoStrFormatEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);
    fill_default_val_ = config()->GetBoolean("fill_default_val", true);
    format_string_ = config()->GetString("format_string", "");
    if (format_string_.empty()) {
      LOG(ERROR) << "CommonRecoStrFormatEnricher init failed! `format_string` not set or empty.";
      return false;
    }

    format_ = std::make_unique<absl::UntypedFormatSpec>(format_string_);

    const auto *input_attrs_config = config()->Get("input_attrs");
    if (!RecoUtil::ExtractStringListFromJsonConfig(input_attrs_config, &input_attrs_, false, true)) {
      LOG(ERROR) << "CommonRecoStrFormatEnricher init failed! `input_attrs` should be a string array.";
      return false;
    }

    output_attr_ = config()->GetString("output_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoStrFormatEnricher init failed! Missing 'output_attr' config.";
      return false;
    }
    return true;
  }

  void FormatCommonAttr(MutableRecoContextInterface *context);

  void FormatItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end);

 private:
  bool is_common_attr_ = true;
  bool fill_default_val_ = true;
  std::string format_string_;
  std::string output_attr_;
  std::vector<std::string> input_attrs_;
  std::unique_ptr<absl::UntypedFormatSpec> format_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStrFormatEnricher);
};

}  // namespace platform
}  // namespace ks
