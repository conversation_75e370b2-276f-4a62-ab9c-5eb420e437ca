#include "dragon/src/processor/common/enricher/common_reco_protobuf_release_message_enricher.h"

#include <unordered_set>
#include "base/strings/string_split.h"

namespace ks {
namespace platform {

void CommonRecoProtobufReleaseMessageEnricher::Enrich(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  std::unordered_set<std::string> added_attrs;
  if (!from_common_attr_.empty()) {
    auto *message = const_cast<google::protobuf::Message *>(
        context->GetPtrCommonAttr<google::protobuf::Message>(from_common_attr_));
    if (!message) {
      CL_LOG_ERROR("release_protobuf_msg", "common_attr_not_found:" + from_common_attr_)
          << "enrich from protobuf failed as null pointer: " << from_common_attr_;
      return;
    }

    for (auto &release_path : release_paths_) {
      if (!release_path.valid) {
        CL_LOG_ERROR("release_protobuf_msg", "invalid_path:" + release_path.path)
            << "failed to release protobuf message, invalid pb json path: " << release_path.path;
        continue;
      }
      if (release_path.field_path.empty()) {
        const auto *descriptor = message->GetDescriptor();
        if (!interop::ConvertMsgPathToFieldIndexPath(descriptor, release_path.path,
                                                     &release_path.field_path)) {
          release_path.valid = false;
          continue;
        }
      }
      interop::ReleaseProtobufMessage(message, release_path.path, release_path.field_path);
    }
  } else {
    auto *item_attr_accessor = context->GetItemAttrAccessor(from_item_attr_);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      uint64 item_key = result.item_key;
      auto *message = const_cast<google::protobuf::Message *>(
          result.GetPtrAttr<google::protobuf::Message>(item_attr_accessor));
      if (!message) {
        CL_LOG_ERROR_EVERY("release_protobuf_msg", "item_attr_not_found:" + from_item_attr_, 1000)
            << "enrich from protobuf failed as null pointer: " << from_item_attr_ << " (" << item_key << ")";
        return;
      }

      for (auto &release_path : release_paths_) {
        if (!release_path.valid) {
          CL_LOG_ERROR_EVERY("release_protobuf_msg", "invalid_path:" + release_path.path, 1000)
              << "failed to release protobuf message, invalid pb json path: " << release_path.path;
          continue;
        }
        if (release_path.field_path.empty()) {
          const auto *descriptor = message->GetDescriptor();
          if (!interop::ConvertMsgPathToFieldIndexPath(descriptor, release_path.path,
                                                       &release_path.field_path)) {
            release_path.valid = false;
            continue;
          }
        }
        interop::ReleaseProtobufMessage(message, release_path.path, release_path.field_path);
      }
    });
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoProtobufReleaseMessageEnricher,
                 CommonRecoProtobufReleaseMessageEnricher)

}  // namespace platform
}  // namespace ks
