#include "dragon/src/processor/common/enricher/common_reco_build_sample_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoBuildSampleAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  if (!init_processor_) {
    if (is_common_attr_) {
      ReserveMessages(sample_attr_mapping_.size());
    } else {
      for (auto &mapping : sample_attr_mapping_) {
        mapping.from_accessor = context->GetItemAttrAccessor(mapping.from_attr);
        mapping.to_accessor = context->GetItemAttrAccessor(mapping.to_attr);
      }
    }
    init_processor_ = true;
  }
  if (is_common_attr_) {
    int index = 0;
    for (const auto &mapping : sample_attr_mapping_) {
      sample_attrs_[index]->Clear();
      interop::LoadSampleAttrFromCommonAttr(context, mapping.from_attr,
                                            dynamic_cast<kuiba::SampleAttr *>(sample_attrs_[index].get()),
                                            mapping.rename);
      context->SetPtrCommonAttr(mapping.to_attr, sample_attrs_[index]);
      index++;
    }
  } else {
    ReserveMessages(std::distance(begin, end) * sample_attr_mapping_.size());
    int index = 0;
    for (const auto &mapping : sample_attr_mapping_) {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        sample_attrs_[index]->Clear();
        interop::LoadSampleAttrFromItemAttr(result, mapping.from_accessor,
                                            dynamic_cast<kuiba::SampleAttr *>(sample_attrs_[index].get()),
                                            mapping.rename);
        result.SetPtrAttr(mapping.to_accessor, sample_attrs_[index]);
        index++;
      });
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoBuildSampleAttrEnricher, CommonRecoBuildSampleAttrEnricher)

}  // namespace platform
}  // namespace ks
