#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <algorithm>
#include <atomic>
#include <functional>
#include <memory>
#include <string>
#include <tuple>
#include <type_traits>
#include <typeinfo>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/common_reco/util/id_converter.h"
#include "ks/reco/bt_embedding_server/proto/bt_embedding_service.kess.grpc.pb.h"
#include "ks/reco_pub/reco/predict/base/public.h"
#include "serving_base/server_base/kess_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossusdb/client/emb/embedding_client.h"

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

enum class RawDataType {
  kSignedInt8,
  kSignedInt16,
  kSignedInt32,
  kSignedInt64,
  kUnsignedInt8,
  kUnsignedInt16,
  kUnsignedInt32,
  kUnsignedInt64,
  kFloat32
};

// store attrs to be saved to context
class RawAttrStore {
 public:
  explicit RawAttrStore(size_t size, RawDataType raw_type, bool is_raw_data)
      : is_common_attr_(true), item_key_(0), raw_data_type_(raw_type), is_raw_data_(is_raw_data) {
    Init(size);
  }
  RawAttrStore(uint64 item_key, size_t size, RawDataType raw_type, bool is_raw_data)
      : is_common_attr_(false), item_key_(item_key), raw_data_type_(raw_type), is_raw_data_(is_raw_data) {
    Init(size);
  }

  void Init(size_t size) {
    if (!is_raw_data_ || raw_data_type_ == RawDataType::kFloat32) {
      is_double_data_type_ = true;
      double_data_.resize(size);
    } else {
      is_double_data_type_ = false;
      int_data_.resize(size);
    }
  }

  void Save(MutableRecoContextInterface *context, const std::string &attr_name) {
    if (has_value_) {
      if (is_common_attr_) {
        if (is_double_data_type_) {
          context->SetDoubleListCommonAttr(attr_name, std::move(double_data_));
        } else {
          context->SetIntListCommonAttr(attr_name, std::move(int_data_));
        }
      } else {
        if (is_double_data_type_) {
          context->SetDoubleListItemAttr(item_key_, attr_name, std::move(double_data_));
        } else {
          context->SetIntListItemAttr(item_key_, attr_name, std::move(int_data_));
        }
      }
    }
  }

  std::function<void(const char *data, size_t data_size)> MutableAddrSetter(size_t offset) {
    return [this, offset](const char *data, size_t data_size) -> void {
      has_value_ = true;
      if (is_raw_data_) {
        if (raw_data_type_ == RawDataType::kUnsignedInt8) {
          SaveRawDataAttr<uint8>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kUnsignedInt16) {
          SaveRawDataAttr<uint16>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
          SaveRawDataAttr<uint32>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
          SaveRawDataAttr<uint64>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kSignedInt8) {
          SaveRawDataAttr<int8>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kSignedInt16) {
          SaveRawDataAttr<int16>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kSignedInt32) {
          SaveRawDataAttr<int32>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kSignedInt64) {
          SaveRawDataAttr<int64>(data, data_size, offset);
        } else if (raw_data_type_ == RawDataType::kFloat32) {
          SaveRawDataAttr<float>(data, data_size, offset);
        } else {
          CL_LOG_ERROR("remote_embedding_lite", "invalid_raw_data_type")
              << "Unexpected raw_data_type: " << int(raw_data_type_);
        }
      } else {
        SaveEmbeddingAttr(data, data_size, offset);
      }
    };
  }

 private:
  void SaveEmbeddingAttr(const char *data, size_t data_size, size_t offset) {
    int dim = data_size / sizeof(int16);
    const int16 *weights = reinterpret_cast<const int16 *>(data);
    for (int i = 0; i < dim; i++) {
      double_data_[offset + i] = ks::reco::WeightToFloat(weights[i]);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value> SaveRawDataAttr(const char *data, size_t data_size,
                                                               size_t offset) {
    size_t element_size = sizeof(T);
    int dim = data_size / element_size;
    const T *raw_data = reinterpret_cast<const T *>(data);

    for (int i = 0; i < dim; i++) {
      int_data_[offset + i] = raw_data[i];
    }
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value> SaveRawDataAttr(const char *data, size_t data_size,
                                                                     size_t offset) {
    size_t element_size = sizeof(T);
    int dim = data_size / element_size;
    const T *raw_data = reinterpret_cast<const T *>(data);

    for (int i = 0; i < dim; i++) {
      double_data_[offset + i] = raw_data[i];
    }
  }

  bool is_common_attr_;
  bool is_raw_data_;
  bool is_double_data_type_ = false;
  uint64_t item_key_;
  std::vector<int64> int_data_;
  std::vector<double> double_data_;

  RawDataType raw_data_type_;

  bool has_value_ = false;
};

class CommonRecoRemoteEmbeddingAttrRawLiteEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRemoteEmbeddingAttrRawLiteEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  template <typename Value>
  using Set = folly::F14FastSet<Value>;

  template <typename Key, typename Value>
  using Map = folly::F14FastMap<Key, Value>;

  bool InitProcessor() override {
    protocol_ = config()->GetInt("protocol", 0);
    if (protocol_ == 0) {
      kess_cluster_ = config()->GetString("kess_cluster", "PRODUCTION");
      shards_ = config()->GetInt("shard_num", 1);
      if (shards_ < 1) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! shard num must > 0";
        return false;
      }

      client_side_shard_ = config()->GetBoolean("client_side_shard", false);
      if (client_side_shard_) {
        requests_.resize(shards_);
      } else {
        requests_.resize(1);
      }
      shard_prefix_ = config()->GetString("shard_prefix", "s");
    } else if (protocol_ == 1) {
      clsdb_emb_client_config_.service_name = config()->GetString("colossusdb_embd_service_name");
      clsdb_emb_client_config_.table_name = config()->GetString("colossusdb_embd_table_name");
      clsdb_emb_client_config_.use_kconf_client = config()->GetBoolean("colossusdb_use_kconf_client", true);
      clsdb_emb_client_config_.timeout_ms = timeout_ms_;
      clsdb_emb_client_config_.max_signs_per_request = max_signs_per_request_;
    } else {
      LOG(FATAL) << "Unexpected protocol: " << protocol_;
    }

    max_signs_per_request_ = config()->GetInt("max_signs_per_request", 0);

    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! timeout_ms must > 0";
      return false;
    }

    const std::string &query_source_type = config()->GetString("query_source_type", "item_key");
    if (query_source_type == "item_key") {
      query_source_type_ = QuerySourceType::kItemKey;
    } else if (query_source_type == "item_id") {
      query_source_type_ = QuerySourceType::kItemId;
    } else if (query_source_type == "user_id") {
      query_source_type_ = QuerySourceType::kUserId;
    } else if (query_source_type == "device_id") {
      query_source_type_ = QuerySourceType::kDeviceId;
    } else if (query_source_type == "item_attr") {
      query_source_type_ = QuerySourceType::kItemAttr;
    } else if (query_source_type == "common_attr") {
      query_source_type_ = QuerySourceType::kCommonAttr;
    } else {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! Unexpected query_source_type: "
                 << query_source_type;
      return false;
    }

    auto *id_conv_config = config()->Get("id_converter");
    if (id_conv_config == nullptr || id_conv_config->GetString("type_name", "").empty()) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! id converter config error";
      return false;
    }
    id_conv_ = base::JsonFactoryClass::New<IdConverter>(id_conv_config);
    if (!id_conv_) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! invalid id converter config: "
                 << id_conv_config->ToString();
      return false;
    }

    slot_ = config()->GetInt("slot", 0);

    dim_size_ = config()->GetInt("size", 0);
    if (dim_size_ <= 0) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! size is required";
      return false;
    }

    output_attr_name_ = config()->GetString("output_attr_name");
    if (output_attr_name_.empty()) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! output_attr_name empty";
      return false;
    }

    input_attr_name_ = config()->GetString("input_attr_name");
    if (query_source_type_ == QuerySourceType::kItemAttr ||
        query_source_type_ == QuerySourceType::kCommonAttr) {
      if (input_attr_name_.empty()) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! input_attr_name empty";
        return false;
      }
    } else {
      if (!input_attr_name_.empty()) {
        LOG(WARNING) << "input_attr_name(" << input_attr_name_
                     << ") is ignored as query_source_type: " << query_source_type;
      }
    }

    is_raw_data_ = config()->GetBoolean("is_raw_data", false);
    if (is_raw_data_) {
      const std::string &raw_data_type = config()->GetString("raw_data_type", "uint16");
      if (raw_data_type == "uint8") {
        raw_data_type_ = RawDataType::kUnsignedInt8;
        raw_size_ = dim_size_ * sizeof(uint8);
      } else if (raw_data_type == "uint16") {
        raw_data_type_ = RawDataType::kUnsignedInt16;
        raw_size_ = dim_size_ * sizeof(uint16);
      } else if (raw_data_type == "uint32") {
        raw_data_type_ = RawDataType::kUnsignedInt32;
        raw_size_ = dim_size_ * sizeof(uint32);
      } else if (raw_data_type == "uint64") {
        raw_data_type_ = RawDataType::kUnsignedInt64;
        raw_size_ = dim_size_ * sizeof(uint64);
      } else if (raw_data_type == "int8") {
        raw_data_type_ = RawDataType::kSignedInt8;
        raw_size_ = dim_size_ * sizeof(int8);
      } else if (raw_data_type == "int16") {
        raw_data_type_ = RawDataType::kSignedInt16;
        raw_size_ = dim_size_ * sizeof(int16);
      } else if (raw_data_type == "int32") {
        raw_data_type_ = RawDataType::kSignedInt32;
        raw_size_ = dim_size_ * sizeof(int32);
      } else if (raw_data_type == "int64") {
        raw_data_type_ = RawDataType::kSignedInt64;
        raw_size_ = dim_size_ * sizeof(int64);
      } else if (raw_data_type == "float32") {
        raw_data_type_ = RawDataType::kFloat32;
        raw_size_ = dim_size_ * sizeof(float);
      } else {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrRawLiteEnricher init failed! unsupported raw_data_type: "
                   << raw_data_type;
        return false;
      }
    } else {
      raw_data_type_ = RawDataType::kSignedInt16;
      raw_size_ = dim_size_ * sizeof(int16);
    }

    return true;
  }

  RawAttrStore &NewCommonRawAttrStore(size_t num) {
    attr_stores_.emplace_back(num * dim_size_, raw_data_type_, is_raw_data_);
    return attr_stores_.back();
  }

  RawAttrStore &NewItemRawAttrStore(uint64_t item_key, size_t num) {
    attr_stores_.emplace_back(item_key, num * dim_size_, raw_data_type_, is_raw_data_);
    return attr_stores_.back();
  }

  // add sign to request, may send request immediately if max_signs_per_request_ > 0
  void AddSign(uint64_t val, std::function<void(const char *data, size_t data_size)> addr_getter,
               MutableRecoContextInterface *context, const std::string &kess_service) {
    uint64_t sign = id_conv_->ConvertKey(val, slot_, 1);
    auto it_inserted = mapping_sign_to_addr_getter_.emplace(
        sign, std::initializer_list<std::function<void(const char *data, size_t data_size)>>{});
    if (it_inserted.second) {
      if (protocol_ == 1) {
        request_signs_.emplace_back(sign);
      } else {
        if (client_side_shard_) {
          int shard = sign % shards_;
          requests_[shard].add_signs(sign);
          if (max_signs_per_request_ > 0 && requests_[shard].signs_size() >= max_signs_per_request_) {
            SendRequestToShard(context, requests_[shard], shard_prefix_ + base::IntToString(shard),
                               kess_service);
            requests_[shard].Clear();
          }
        } else {
          requests_[0].add_signs(sign);
          if (max_signs_per_request_ > 0 && requests_[0].signs_size() >= max_signs_per_request_) {
            for (auto i = 0; i < shards_; i++) {
              SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
            }
            requests_[0].Clear();
          }
        }
      }
    }
    it_inserted.first->second.emplace_back(addr_getter);
  }

  void ClearRequests() {
    mapping_sign_to_addr_getter_.clear();
    attr_stores_.clear();
    if (protocol_ == 0) {
      for (auto &request : requests_) {
        request.Clear();
      }
    } else if (protocol_ == 1) {
      request_signs_.clear();
    } else {
      // avoid static check
      requests_.clear();
      dim_size_ = 0;
      raw_size_ = 0;
      protocol_ = 2;
    }
  }

  void SendRequests(MutableRecoContextInterface *context, const std::string &kess_service) {
    if (protocol_ == 0) {
      for (auto i = 0; i < shards_; i++) {
        if (client_side_shard_) {
          if (requests_[i].signs_size() > 0) {
            SendRequestToShard(context, requests_[i], shard_prefix_ + base::IntToString(i), kess_service);
          }
        } else {
          if (requests_[0].signs_size() > 0) {
            SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
          }
        }
      }
    } else {
      SendColossusdbRequests(context, request_signs_);
    }
  }

  colossusdb::EmbeddingClient *GetColossusdbClient() {
    if (clsdb_emb_client_config_.use_kconf_client) {
      clsdb_client_ =
          colossusdb::EmbeddingClient::GetOrCreateEmbKconfClientSingleton(clsdb_emb_client_config_)
              ->GetClient();
      if (!clsdb_client_) {
        CL_LOG(ERROR) << "Get Embedding Kconf client failed! Check your colossusdb_embd config.";
        return nullptr;
      }
      return clsdb_client_.get();
    }
    return colossusdb::EmbeddingClient::GetOrCreateEmbClientSingleton(clsdb_emb_client_config_);
  }

 protected:
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }

 private:
  std::string kess_cluster_;
  int timeout_ms_ = 10;
  int shards_ = 1;
  std::string shard_prefix_;
  bool client_side_shard_ = false;
  int max_signs_per_request_ = 0;
  bool is_raw_data_ = false;

  uint64 slot_ = 0;
  IdConverter *id_conv_ = nullptr;
  std::string output_attr_name_ = "";
  std::string input_attr_name_ = "";
  RawDataType raw_data_type_ = RawDataType::kSignedInt16;
  enum class QuerySourceType {
    kItemKey,
    kItemId,
    kUserId,
    kDeviceId,
    kItemAttr,
    kCommonAttr,
  } query_source_type_ = QuerySourceType::kItemKey;

  size_t dim_size_ = 0;
  size_t raw_size_ = 0;

  int protocol_ = 0;
  // for protocol 0
  std::vector<RawAttrStore> attr_stores_;
  Map<uint64_t, std::vector<std::function<void(const char *data, size_t data_size)>>>
      mapping_sign_to_addr_getter_;

  CommonRecoObjectPool<ks::reco::bt_embd_s::BatchEmbeddingsResponse> response_pool_;
  std::vector<ks::reco::bt_embd_s::BatchEmbeddingsRequest> requests_;

  // end for protocol 0

  // for protocol 1
  colossusdb::ClsdbEmbClientConfig clsdb_emb_client_config_;
  std::shared_ptr<colossusdb::EmbeddingClient> clsdb_client_;
  std::unique_ptr<colossusdb::EmbeddingMapResponse> clsdb_resp_;

  std::vector<uint64_t> request_signs_;
  // end for protocol 1

  serving_base::Timer timer_;

  std::shared_ptr<int> exit_handler_;

  void FillItemAttr(MutableRecoContextInterface *context,
                    const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                    const std::string &shard_name);

  void FillCommonAttr(MutableRecoContextInterface *context,
                      const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                      const std::string &shard_name);

  void SendRequestToShard(MutableRecoContextInterface *context,
                          const ks::reco::bt_embd_s::BatchEmbeddingsRequest &request, std::string shard_name,
                          const std::string &kess_service);

  void SendColossusdbRequests(MutableRecoContextInterface *context,
                              const std::vector<uint64_t> &request_signs);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRemoteEmbeddingAttrRawLiteEnricher);
};
}  // namespace platform
}  // namespace ks
