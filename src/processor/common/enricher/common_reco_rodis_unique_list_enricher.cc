#include "dragon/src/processor/common/enricher/common_reco_rodis_unique_list_enricher.h"

#include <utility>
#include <vector>

#include "absl/strings/str_format.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

void CommonRecoRodisUniqueListEnricher::Enrich(MutableRecoContextInterface *context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }
  request_.clear_key();
  request_.clear_payload_des();
  request_.add_key(key);
  auto *payload_des = request_.add_payload_des();
  payload_des->set_payload_id(payload_id_);
  VLOG(1) << "request: " << request_.DebugString();
  auto pr = [&]() {
    KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_name_, "PRODUCTION", "s0", timeout_ms_, request_, &response_,
                                           ::kuaishou::ds::kess::RodisService, AsyncMultiGet);
  }();
  if (!pr.first) {
    CL_LOG(WARNING) << "request rodis service failed: " << kess_name_;
    return;
  }

  RegisterAsyncCallback(context, std::move(pr.second),
                        [this, context](::kuaishou::ds::MultiGetResponse *response) {
                          VLOG(1) << "response: " << response->DebugString();
                          if (response->payload_size() == 1) {
                            const auto &payload = response->payload(0);
                            const int size = payload.unique_list().item_size();
                            VLOG(1) << "get response: size=" << size << ", err_code=" << response->err_code();
                            std::vector<std::string> unique_key_list;
                            std::vector<std::string> sort_key_list;
                            std::vector<std::string> data_list;
                            for (const auto &item : payload.unique_list().item()) {
                                unique_key_list.emplace_back(item.unique_key());
                                sort_key_list.emplace_back(item.sort_key());
                                data_list.emplace_back(item.data());
                            }
                            context->SetStringListCommonAttr(unique_key_attr_, std::move(unique_key_list));
                            context->SetStringListCommonAttr(sort_key_attr_, std::move(sort_key_list));
                            context->SetStringListCommonAttr(data_attr_, std::move(data_list));
                          } else {
                            CL_LOG(WARNING) << "unexpected payload size: " << response->payload_size()
                                            << ", err_code" << response->err_code();
                          }
                        });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisUniqueListEnricher, CommonRecoRodisUniqueListEnricher)

}  // namespace platform
}  // namespace ks
