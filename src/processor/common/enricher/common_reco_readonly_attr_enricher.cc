#include "dragon/src/processor/common/enricher/common_reco_readonly_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoReadonlyAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                            RecoResultConstIter end) {
  for (const auto &name : common_attrs_) {
    auto *attr = context->GetCommonAttrAccessor(name);
    attr-><PERSON><PERSON><PERSON><PERSON>n<PERSON>();
  }

  for (const auto &name : item_attrs_) {
    auto *attr = context->GetItemAttrAccessor(name);
    attr-><PERSON><PERSON><PERSON>Only();
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoReadonlyAttrEnricher, CommonRecoReadonlyAttrEnricher)

}  // namespace platform
}  // namespace ks
