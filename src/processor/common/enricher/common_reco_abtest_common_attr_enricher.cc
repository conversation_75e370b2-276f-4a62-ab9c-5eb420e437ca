#include "dragon/src/processor/common/enricher/common_reco_abtest_common_attr_enricher.h"

DEFINE_bool(abtest_common_attr_enricher_get_only_suffix, false, "this processor get only suffix params");
DEFINE_int64(abtest_async_pool_queue_capacity, 1024, "queue capacity of abtest async pool");
DEFINE_int64(abtest_async_pool_thread_num, 128, "thread num of abtest async pool");

#define GET_ABTEST_PARAM_VALUE(AB_FUNC, DEFAULT_VAL, PREFIX)                                  \
  {                                                                                           \
    auto val = DEFAULT_VAL;                                                                   \
    bool report_ab_hit = GetBoolProcessorParameter(context, param.report_ab_hit, false);      \
    if (!param.param_name.empty() &&                                                          \
        (prioritized_suffix.empty() || !FLAGS_abtest_common_attr_enricher_get_only_suffix)) { \
      auto ab_result =                                                                        \
          ab_param_context.GetResult(biz_, absl::StrCat(PREFIX, param.param_name));           \
      val = ab_result.AB_FUNC(val);                                                           \
      SetExpInfo(context, param, ab_result);                                                  \
      if (report_ab_hit) {                                                                    \
        ab_result.Report();                                                                   \
      }                                                                                       \
    }                                                                                         \
    for (auto it = prioritized_suffix.rbegin(); it != prioritized_suffix.rend(); ++it) {      \
      std::string param_name = ConvertAbtestParamName(param, *it, PREFIX);                    \
      if (param_name.empty()) continue;                                                       \
      auto ab_result = ab_param_context.GetResult(biz_, param_name);                          \
      val = ab_result.AB_FUNC(val);                                                           \
      SetExpInfo(context, param, ab_result);                                                  \
      if (report_ab_hit) {                                                                    \
        ab_result.Report();                                                                   \
      }                                                                                       \
    }                                                                                         \
    return val;                                                                               \
  }

namespace ks {
namespace platform {

std::unique_ptr<AsyncTaskThreadPool<bool>> CommonRecoAbtestCommonAttrEnricher::abtest_param_thread_pool_ =
    nullptr;
std::once_flag CommonRecoAbtestCommonAttrEnricher::oc_;

void CommonRecoAbtestCommonAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  if (dynamic_biz_name_config_) {
    std::string biz_name =
        GetStringProcessorParameter(context, dynamic_biz_name_config_, FLAGS_abtest_biz_name);
    biz_ = ks::GetAbtestBizByName(biz_name);
    if (biz_ == ks::AbtestBiz::NULL_BIZ) {
      CL_LOG_ERROR("get_abtest_params", "invalid_biz_name: " + biz_name)
          << "CommonRecoAbtestCommonAttrEnricher gets invalid abtest biz_name: '" << biz_name
          << "' from dynamic param: " << dynamic_biz_name_config_->StringValue()
          << ", default_value will be saved to common attr";
    }
  }

  std::string param_prefix = GetStringProcessorParameter(context, param_prefix_, "");

  std::vector<absl::string_view> prioritized_suffix =
      GetStringListProcessorParameter(context, prioritized_suffix_);

  if (for_item_level_) {
    GetAbtestForItemLevel(context, begin, end, prioritized_suffix, param_prefix);
  } else {
    GetAbtestForCommonLevel(context, prioritized_suffix, param_prefix);
  }
}

std::string CommonRecoAbtestCommonAttrEnricher::ConvertAbtestParamName(const AbtestParam &param,
                                                                       absl::string_view suffix,
                                                                       const std::string &prefix) {
  if (param.param_name_for_suffix.empty()) {
    return absl::StrCat(prefix, param.param_name, suffix);
  }

  auto it = param.param_name_for_suffix.find(suffix);
  if (it != param.param_name_for_suffix.end()) {
    return absl::StrCat(prefix, it->second);
  } else {
    return "";
  }
}

void CommonRecoAbtestCommonAttrEnricher::GetAbtestForCommonLevel(
    ks::platform::MutableRecoContextInterface *context,
    const std::vector<absl::string_view> &prioritized_suffix,
    const std::string &param_prefix) {
  for (auto &param : ab_params_) {
    param.common_attr = context->GetCommonAttrAccessor(param.attr_name);
  }

  AbtestContextParam abtest_context_param;
  abtest_context_param.user_id = GetIntProcessorParameter(context, "user_id", context->GetUserId());
  abtest_context_param.device_id = GetStringProcessorParameter(context, "device_id", context->GetDeviceId());
  abtest_context_param.session_id = GetStringProcessorParameter(context, "session_id", "");

  abtest_context_param.product = GetStringProcessorParameter(context, "product", "");
  abtest_context_param.platform = GetStringProcessorParameter(context, "platform", "");
  abtest_context_param.app_version = GetStringProcessorParameter(context, "app_version", "");
  abtest_context_param.explore_locale = GetStringProcessorParameter(context, "explore_locale");
  abtest_context_param.country = GetStringProcessorParameter(context, "country");
  abtest_context_param.province = GetStringProcessorParameter(context, "province");
  abtest_context_param.city = GetStringProcessorParameter(context, "city");
  abtest_context_param.photo_page = GetStringProcessorParameter(context, "photo_page");
  abtest_context_param.browse_type = GetStringProcessorParameter(context, "browse_type");
  abtest_context_param.network_type = GetStringProcessorParameter(context, "network_type");
  abtest_context_param.phone_model = GetStringProcessorParameter(context, "phone_model");
  abtest_context_param.language = GetStringProcessorParameter(context, "language");
  abtest_context_param.isp = GetStringProcessorParameter(context, "isp");

  abtest_context_param.user_tag_names = context->GetStringListCommonAttr(kAbtestUserTagNames);
  abtest_context_param.user_tag_values = context->GetStringListCommonAttr(kAbtestUserTagValues);

  if (parallel_get_ > 1 && abtest_param_thread_pool_) {
    std::vector<std::future<bool>> future_vec;
    for (int thread_index = 1; thread_index < offsets_.size(); ++thread_index) {
      std::future<bool> future =
          abtest_param_thread_pool_->AsyncWithBlock(context->GetRequestType(), GetName(), [&, thread_index] {
            auto cpu_timer = context->GetIncrCpuTimer();
            GetAbtestForCommonLevelBatch(context, offsets_[thread_index - 1], offsets_[thread_index],
                                         prioritized_suffix, abtest_context_param, param_prefix);
            return true;
          });
      future_vec.push_back(std::move(future));
    }
    for (int i = 0; i < future_vec.size(); ++i) {
      future_vec[i].get();
    }
  } else {
    GetAbtestForCommonLevelBatch(context, 0, ab_params_.size(),
                                 prioritized_suffix, abtest_context_param, param_prefix);
  }
}

void CommonRecoAbtestCommonAttrEnricher::GetAbtestForCommonLevelBatch(
    MutableRecoContextInterface *context, int index_begin, int index_end,
    const std::vector<absl::string_view> &prioritized_suffix,
    const AbtestContextParam &abtest_context_param, const std::string &param_prefix) {
  std::unordered_set<std::string> ab_param_names =
      GetAbParamNameSet(index_begin, index_end, prioritized_suffix, param_prefix);

  auto builder = std::make_unique<ks::abtest::AbParamContext::Builder>(
      abtest_context_param.user_id, abtest_context_param.device_id, abtest_context_param.session_id);
  if (context->GetRequest()->has_abtest_mapping_id()) {
    builder->SetMappingId(context->GetRequest()->abtest_mapping_id());
  }
  builder->AddBizParams(biz_, ab_param_names);

  if (!abtest_context_param.product.empty()) builder->SetProduct(abtest_context_param.product);
  if (!abtest_context_param.platform.empty()) builder->SetPlatform(abtest_context_param.platform);
  if (!abtest_context_param.app_version.empty()) builder->SetAppVer(abtest_context_param.app_version);
  if (!abtest_context_param.explore_locale.empty())
    builder->SetExploreLocale(abtest_context_param.explore_locale);
  if (!abtest_context_param.country.empty()) builder->SetCountry(abtest_context_param.country);
  if (!abtest_context_param.province.empty()) builder->SetProvince(abtest_context_param.province);
  if (!abtest_context_param.city.empty()) builder->SetCity(abtest_context_param.city);
  if (!abtest_context_param.photo_page.empty()) builder->SetPhotoPage(abtest_context_param.photo_page);
  if (!abtest_context_param.browse_type.empty()) builder->SetBrowseType(abtest_context_param.browse_type);
  if (!abtest_context_param.network_type.empty()) builder->SetNetWorkType(abtest_context_param.network_type);
  if (!abtest_context_param.phone_model.empty()) builder->SetPhoneModel(abtest_context_param.phone_model);
  if (!abtest_context_param.language.empty()) builder->SetLanguage(abtest_context_param.language);
  if (!abtest_context_param.isp.empty()) builder->SetIsp(abtest_context_param.isp);

  auto user_tag_names = abtest_context_param.user_tag_names;
  auto user_tag_values = abtest_context_param.user_tag_values;
  if (user_tag_names.has_value() && user_tag_values.has_value() &&
      user_tag_names->size() == user_tag_values->size()) {
    for (int i = 0; i < user_tag_names->size(); ++i) {
      builder->SetUserTag(std::string(user_tag_names->at(i)), std::string(user_tag_values->at(i)));
    }
  }

  auto ab_param_context = builder->Build();
  for (int i = index_begin; i < index_end; ++i) {
    auto &param = ab_params_[i];
    switch (param.type) {
      case AbtestParamType::INT: {
        int64 int_value =
            GetIntAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
        param.common_attr->SetIntValue(int_value);
        break;
      }
      case AbtestParamType::DOUBLE: {
        double double_value =
            GetDoubleAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
        param.common_attr->SetDoubleValue(double_value);
        break;
      }
      case AbtestParamType::STRING: {
        std::string str_value =
            GetStringAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
        param.common_attr->SetStringValue(std::move(str_value));
        break;
      }
      case AbtestParamType::BOOLEAN: {
        bool bool_value =
            GetBoolAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
        param.common_attr->SetIntValue(bool_value ? 1 : 0);
        break;
      }
      default:
        CL_LOG_ERROR("abtest", absl::StrCat("invalid_param_type:", static_cast<int>(param.type)))
            << "Unsupported param type: " << static_cast<int>(param.type)
            << ", param name: " << param.attr_name << RecoUtil::GetRequestInfoForLog(context);
        break;
    }
  }
}

void CommonRecoAbtestCommonAttrEnricher::GetAbtestForItemLevel(
    ks::platform::MutableRecoContextInterface *context, ks::platform::RecoResultConstIter begin,
    ks::platform::RecoResultConstIter end, const std::vector<absl::string_view> &prioritized_suffix,
    const std::string &param_prefix) {
  if (user_id_item_attr_accessor_ == nullptr && !item_level_user_id_attr_.empty()) {
    user_id_item_attr_accessor_ = context->GetItemAttrAccessor(item_level_user_id_attr_);
  }

  if (device_id_item_attr_accessor_ == nullptr && !item_level_device_id_attr_.empty()) {
    device_id_item_attr_accessor_ = context->GetItemAttrAccessor(item_level_device_id_attr_);
  }

  if (session_id_item_attr_accessor_ == nullptr && !item_level_session_id_attr_.empty()) {
    session_id_item_attr_accessor_ = context->GetItemAttrAccessor(item_level_session_id_attr_);
  }

  std::unordered_set<std::string> ab_param_names =
      GetAbParamNameSet(0, ab_params_.size(), prioritized_suffix, param_prefix);

  for (auto &param : ab_params_) {
    param.item_attr = context->GetItemAttrAccessor(param.attr_name);
  }

  std::string product = GetStringProcessorParameter(context, "product", "");
  std::string platform = GetStringProcessorParameter(context, "platform", "");
  std::string app_version = GetStringProcessorParameter(context, "app_version", "");

  std::for_each(
      begin, end,
      [this, context, &ab_param_names, &prioritized_suffix, &product, &platform,
       &app_version, &param_prefix](const CommonRecoResult &result) {
        uint64 user_id = 0;
        std::string device_id = "";
        std::string session_id = "";

        if (user_id_item_attr_accessor_) {
          if (auto val = result.GetIntAttr(user_id_item_attr_accessor_)) {
            user_id = *val;
          }
        }
        if (device_id_item_attr_accessor_) {
          if (auto val = result.GetStringAttr(device_id_item_attr_accessor_)) {
            device_id = std::string(*val);
          }
        }
        if (session_id_item_attr_accessor_) {
          if (auto val = result.GetStringAttr(session_id_item_attr_accessor_)) {
            session_id = std::string(*val);
          }
        }

        auto builder = std::make_unique<ks::abtest::AbParamContext::Builder>(user_id, device_id, session_id);
        if (context->GetRequest()->has_abtest_mapping_id()) {
          builder->SetMappingId(context->GetRequest()->abtest_mapping_id());
        }
        builder->AddBizParams(biz_, ab_param_names);
        if (!product.empty()) builder->SetProduct(product);
        if (!platform.empty()) builder->SetPlatform(platform);
        if (!app_version.empty()) builder->SetAppVer(app_version);
        auto ab_param_context = builder->Build();

        for (const auto &param : ab_params_) {
          switch (param.type) {
            case AbtestParamType::INT: {
              int64 int_value =
                  GetIntAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
              result.SetIntAttr(param.item_attr, int_value);
              break;
            }
            case AbtestParamType::DOUBLE: {
              double double_value =
                  GetDoubleAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
              result.SetDoubleAttr(param.item_attr, double_value);
              break;
            }
            case AbtestParamType::STRING: {
              std::string str_value =
                  GetStringAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
              result.SetStringAttr(param.item_attr, std::move(str_value));
              break;
            }
            case AbtestParamType::BOOLEAN: {
              bool bool_value =
                  GetBoolAbtestValue(context, param, prioritized_suffix, *ab_param_context, param_prefix);
              result.SetIntAttr(param.item_attr, bool_value ? 1 : 0);
              break;
            }
            default:
              CL_LOG_ERROR("abtest", absl::StrCat("invalid_param_type:", static_cast<int>(param.type)))
                  << "Unsupported param type: " << static_cast<int>(param.type)
                  << ", param name: " << param.attr_name << RecoUtil::GetRequestInfoForLog(context);
              break;
          }
        }
      });
}

std::unordered_set<std::string> CommonRecoAbtestCommonAttrEnricher::GetAbParamNameSet(
    int index_begin, int index_end,
    const std::vector<absl::string_view> &prioritized_suffix, const std::string &param_prefix) {
  std::unordered_set<std::string> ab_param_names;
  ab_param_names.reserve((1 + prioritized_suffix.size()) * (index_end - index_begin));

  for (int i = index_begin; i < index_end; ++i) {
    const auto &param = ab_params_[i];
    if (!param.param_name.empty()) {
        ab_param_names.insert(absl::StrCat(param_prefix, param.param_name));
    }
    if (param.param_name_for_suffix.empty()) {
      for (auto sv : prioritized_suffix) {
        ab_param_names.insert(absl::StrCat(param_prefix, param.param_name, sv));
      }
    } else {
      for (auto sv : prioritized_suffix) {
        auto it = param.param_name_for_suffix.find(sv);
        if (it != param.param_name_for_suffix.end() && !it->second.empty()) {
          ab_param_names.insert(absl::StrCat(param_prefix, it->second));
        }
      }
    }
  }
  return ab_param_names;
}

int64 CommonRecoAbtestCommonAttrEnricher::GetIntAbtestValue(
    MutableRecoContextInterface *context, const AbtestParam &param,
    const std::vector<absl::string_view> &prioritized_suffix,
    const ks::abtest::AbParamContext &ab_param_context, const std::string &param_prefix) {
  GET_ABTEST_PARAM_VALUE(GetInteger, param.default_int, param_prefix);
}

double CommonRecoAbtestCommonAttrEnricher::GetDoubleAbtestValue(
    MutableRecoContextInterface *context, const AbtestParam &param,
    const std::vector<absl::string_view> &prioritized_suffix,
    const ks::abtest::AbParamContext &ab_param_context, const std::string &param_prefix) {
  GET_ABTEST_PARAM_VALUE(GetDouble, param.default_double, param_prefix);
}

std::string CommonRecoAbtestCommonAttrEnricher::GetStringAbtestValue(
    MutableRecoContextInterface *context, const AbtestParam &param,
    const std::vector<absl::string_view> &prioritized_suffix,
    const ks::abtest::AbParamContext &ab_param_context, const std::string &param_prefix) {
  GET_ABTEST_PARAM_VALUE(GetString, param.default_string, param_prefix);
}

bool CommonRecoAbtestCommonAttrEnricher::GetBoolAbtestValue(
    MutableRecoContextInterface *context, const AbtestParam &param,
    const std::vector<absl::string_view> &prioritized_suffix,
    const ks::abtest::AbParamContext &ab_param_context, const std::string &param_prefix) {
  GET_ABTEST_PARAM_VALUE(GetBoolean, param.default_int, param_prefix);
}

void CommonRecoAbtestCommonAttrEnricher::SetExpInfo(MutableRecoContextInterface *context,
                                                    const AbtestParam &param,
                                                    const ks::abtest::AbtestResult &ab_result) {
  auto group_hit = ab_result.GetGroupHit();
  if (!for_item_level_ && !param.save_exp_id_to.empty()) {
    context->SetStringCommonAttr(param.save_exp_id_to, group_hit.GetExperiment());
  }
  if (!for_item_level_ && !param.save_group_id_to.empty()) {
    context->SetStringCommonAttr(param.save_group_id_to, group_hit.GetGroup());
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAbtestCommonAttrEnricher, CommonRecoAbtestCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
