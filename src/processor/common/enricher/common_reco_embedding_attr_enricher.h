#pragma once

#include <vector>
#include <string>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "ks/reco/bt_embedding_server/src/embedding_table.h"
#include "ks/reco_pub/reco/predict/base/public.h"
#include "base/common/basic_types.h"

namespace ks {
namespace platform {

class CommonRecoEmbeddingAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoEmbeddingAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  enum class PoolingMode { kSumPooling, kNone };

  enum class RawDataType {
    kUnsignedInt16,
    kUnsignedInt32,
    kUnsignedInt64,
    kFloat32,
  };

  using ItemInfo = ks::reco::bt_embd_s::EmbeddingTable::ItemInfo;

 private:
  bool InitProcessor() override;

  std::vector<double> BuildValueListNormally(const std::vector<ItemInfo> &item_infos,
                                             PoolingMode pooling_mode) {
    std::vector<double> value_list;
    if (item_infos.size() == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty embedding";
      return value_list;
    }

    size_t element_size = sizeof(int16);
    int dim = item_infos[0].size / element_size;
    if (dim * element_size != item_infos[0].size) {
      CL_LOG_ERROR("build_value_list", "unexpected_size")
          << "Unexpected size: " << item_infos[0].size << " for sizeof(int16) = " << element_size;
      return value_list;
    }

    int expected_size = dim * element_size;
    int size_error_count = 0;
    std::ostringstream oss;

    if (pooling_mode == PoolingMode::kSumPooling) {
      value_list.resize(dim);
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const int16 *raw_data = reinterpret_cast<const int16 *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list[i] += ks::reco::WeightToFloat(raw_data[i]);
        }
      }
    } else if (pooling_mode == PoolingMode::kNone) {
      value_list.reserve(dim * item_infos.size());
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const int16 *raw_data = reinterpret_cast<const int16 *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list.push_back(ks::reco::WeightToFloat(raw_data[i]));
        }
      }
    }
    CL_LOG_ERROR_COUNT(size_error_count, "build_value_list", "unexpected_size")
        << "expected_size: " << expected_size << ", error size info: " << oss.str();
    return value_list;
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, std::vector<double>> BuildValueList(
      const std::vector<ItemInfo> &item_infos, PoolingMode pooling_mode) {
    std::vector<double> value_list;
    if (item_infos.size() == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty raw data";
      return value_list;
    }

    size_t element_size = sizeof(T);
    int dim = item_infos[0].size / element_size;
    if (dim * element_size != item_infos[0].size) {
      CL_LOG_ERROR("build_value_list", "unexpected_size")
          << "Unexpected size: " << item_infos[0].size << " for sizeof(" << typeid(T).name()
          << ") = " << element_size;
      return value_list;
    }

    int expected_size = dim * element_size;
    int size_error_count = 0;
    std::ostringstream oss;

    if (pooling_mode == PoolingMode::kSumPooling) {
      value_list.resize(dim);
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const T *raw_data = reinterpret_cast<const T *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list[i] += raw_data[i];
        }
      }
    } else if (pooling_mode == PoolingMode::kNone) {
      value_list.reserve(dim * item_infos.size());
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const T *raw_data = reinterpret_cast<const T *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list.push_back(raw_data[i]);
        }
      }
    }
    CL_LOG_ERROR_COUNT(size_error_count, "build_value_list", "unexpected_size")
        << "expected_size: " << expected_size << ", error size info: " << oss.str();
    return value_list;
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, std::vector<int64>> BuildValueList(
      const std::vector<ItemInfo> &item_infos, PoolingMode pooling_mode) {
    std::vector<int64> value_list;
    if (item_infos.size() == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty raw data";
      return value_list;
    }

    size_t element_size = sizeof(T);
    int dim = item_infos[0].size / element_size;
    if (dim * element_size != item_infos[0].size) {
      CL_LOG_ERROR("build_value_list", "unexpected_size")
          << "Unexpected size: " << item_infos[0].size << " for sizeof(" << typeid(T).name()
          << ") = " << element_size;
      return value_list;
    }

    int expected_size = dim * element_size;
    int size_error_count = 0;
    std::ostringstream oss;

    if (pooling_mode == PoolingMode::kSumPooling) {
      value_list.resize(dim);
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const T *raw_data = reinterpret_cast<const T *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list[i] += raw_data[i];
        }
      }
    } else if (pooling_mode == PoolingMode::kNone) {
      value_list.reserve(dim * item_infos.size());
      for (const auto &item_info : item_infos) {
        if (item_info.size != expected_size) {
          size_error_count++;
          oss << item_info.sign << ":" << item_info.size << ", ";
          continue;
        }
        const T *raw_data = reinterpret_cast<const T *>(item_info.data);
        for (int i = 0; i < dim; i++) {
          value_list.push_back(raw_data[i]);
        }
      }
    }
    CL_LOG_ERROR_COUNT(size_error_count, "build_value_list", "unexpected_size")
        << "expected_size: " << expected_size << ", error size info: " << oss.str();
    return value_list;
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value> SaveRawDataItemAttr(
      MutableRecoContextInterface *context, uint64 item_key, const std::string &attr_name,
      const std::vector<ItemInfo> &item_infos, PoolingMode pooling_mode) {
    std::vector<double> value_list = this->template BuildValueList<T>(item_infos, pooling_mode);
    if (is_raw_data_list_) {
      context->SetDoubleListItemAttr(item_key, attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetDoubleItemAttr(item_key, attr_name, value_list[0]);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value> SaveRawDataItemAttr(MutableRecoContextInterface *context,
                                                                   uint64 item_key,
                                                                   const std::string &attr_name,
                                                                   const std::vector<ItemInfo> &item_infos,
                                                                   PoolingMode pooling_mode) {
    std::vector<int64> value_list = this->template BuildValueList<T>(item_infos, pooling_mode);
    if (is_raw_data_list_) {
      context->SetIntListItemAttr(item_key, attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetIntItemAttr(item_key, attr_name, value_list[0]);
    }
  }

  void SaveEmbeddingItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                             const std::string &attr_name, const std::vector<ItemInfo> &item_infos,
                             PoolingMode pooling_mode) {
    std::vector<double> value_list = BuildValueListNormally(item_infos, pooling_mode);
    context->SetDoubleListItemAttr(item_key, attr_name, std::move(value_list));
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value> SaveRawDataCommonAttr(
      MutableRecoContextInterface *context, const std::string &attr_name,
      const std::vector<ItemInfo> &item_infos, PoolingMode pooling_mode) {
    std::vector<double> value_list = this->template BuildValueList<T>(item_infos, pooling_mode);
    if (is_raw_data_list_) {
      context->SetDoubleListCommonAttr(attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetDoubleCommonAttr(attr_name, value_list[0]);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value> SaveRawDataCommonAttr(MutableRecoContextInterface *context,
                                                                     const std::string &attr_name,
                                                                     const std::vector<ItemInfo> &item_infos,
                                                                     PoolingMode pooling_mode) {
    std::vector<int64> value_list = this->template BuildValueList<T>(item_infos, pooling_mode);
    if (is_raw_data_list_) {
      context->SetIntListCommonAttr(attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetIntCommonAttr(attr_name, value_list[0]);
    }
  }

  void SaveEmbeddingCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                               const std::vector<ItemInfo> &item_infos, PoolingMode pooling_mode) {
    std::vector<double> value_list = BuildValueListNormally(item_infos, pooling_mode);
    context->SetDoubleListCommonAttr(attr_name, std::move(value_list));
  }

 private:
  ks::reco::bt_embd_s::EmbeddingTable *const embed_table_ = ks::reco::bt_embd_s::EmbeddingTable::Instance();

  std::vector<std::string> parameters_inputs_;
  std::string output_attr_;
  bool is_common_attr_;
  PoolingMode pooling_mode_;

  bool is_raw_data_ = false;
  bool is_raw_data_list_ = true;
  RawDataType raw_data_type_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoEmbeddingAttrEnricher);
};

}  // namespace platform
}  // namespace ks
