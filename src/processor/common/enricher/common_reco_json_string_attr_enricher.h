#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoJsonStringAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoJsonStringAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct JsonConfig {
    std::string attr_name;
    bool is_common_attr = true;
    bool to_json_str = false;
    const base::Json *json_path_config = nullptr;
    const base::Json *default_value = nullptr;
    std::vector<std::string> path_vec;
  };

  bool InitProcessor() override {
    json_attr_ = config()->GetString("json_attr");
    json_from_item_attr_ = config()->GetBoolean("json_from_item_attr", false);

    const base::Json *json_configs = config()->Get("json_configs");
    if (!json_configs || !json_configs->IsArray()) {
      LOG(ERROR) << "CommonRecoJsonStringAttrEnricher"
                 << " init failed! Missing 'json_configs' or it is not an array";
      return false;
    }

    for (const auto *config : json_configs->array()) {
      if (!config->IsObject()) {
        LOG(ERROR) << "CommonRecoJsonStringAttrEnricher init failed! Values of 'json_configs' "
                   << "should be objects";
      }

      JsonConfig json_config;

      const base::Json *json_path_config = config->Get("json_path");
      if (!json_path_config) {
        LOG(ERROR) << "CommonRecoJsonStringAttrEnricher init failed! Missing 'json_path' "
                   << "or it is empty";
        return false;
      }
      json_config.json_path_config = json_path_config;

      json_config.default_value = config->Get("default_value");

      json_config.to_json_str = config->GetBoolean("to_json_str", false);

      std::string common_attr_name = config->GetString("export_common_attr");
      std::string item_attr_name = config->GetString("export_item_attr");

      if (!common_attr_name.empty()) {
        json_config.attr_name = common_attr_name;
        json_config.is_common_attr = true;
      } else {
        if (!item_attr_name.empty()) {
          json_config.attr_name = item_attr_name;
          json_config.is_common_attr = false;
        } else {
          LOG(ERROR) << "CommonRecoJsonStringAttrEnricher init failed! Missing 'export_common_attr' or "
                        "'export_item_attr'";
          return false;
        }
      }
      json_configs_.emplace_back(std::move(json_config));
    }
    return true;
  }

  void HandleCommonJson(MutableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);
  void HandleItemJson(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end);

  void ProcessCommonAttrFromCommonJson(MutableRecoContextInterface *context, const JsonConfig &json_config,
                                       const Json::Value &root);

  void ProcessItemAttrFromCommonJson(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                     RecoResultConstIter end, const JsonConfig &json_config,
                                     const Json::Value &root);

  void ProcessCommonAttrFromItemJson(MutableRecoContextInterface *context, const JsonConfig &json_config,
                                     const Json::Value &root, const std::vector<std::string> &path_vec);

  void ProcessItemAttrFromItemJson(MutableRecoContextInterface *context, const CommonRecoResult &result,
                                   const JsonConfig &json_config, const Json::Value &root,
                                   const std::vector<std::string> &path_vec);

  size_t SaveJsonStrToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                 const Json::Value &json_value, const std::vector<std::string> &json_path);
  void SplitJsonPath(absl::string_view json_path, std::vector<std::string> *json_path_vec);

 private:
  std::string json_attr_;
  bool json_from_item_attr_ = false;
  std::vector<JsonConfig> json_configs_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoJsonStringAttrEnricher);
};

}  // namespace platform
}  // namespace ks
