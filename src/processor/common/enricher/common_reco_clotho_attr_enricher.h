#pragma once

#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "json/json.h"
#include "kconf/kconf.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/cpp/clotho_client_internal.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/cpp/status.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/proto/algofea.pb.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/proto/gateway_service.kess.grpc.pb.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/proto/gateway_service.pb.h"
#include "teams/reco-arch/colossusdb/clotho-sdk/proto/input_data.pb.h"

namespace ks {
namespace platform {

class CommonRecoClothoAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoClothoAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 protected:
  int GetPartitionSize(ReadableRecoContextInterface *context) const final {
    return 0;
  }
  bool BuildRequest(MutableRecoContextInterface *context);

 private:
  bool InitProcessor() override {
    kess_name_ = config()->GetString("kess_name");
    if (kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich init failed! kess_name is required.";
      return false;
    }

    table_name_ = config()->GetString("table_name");
    if (table_name_.empty()) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich init failed! table name is required.";
      return false;
    }

    token_name_ = config()->GetString("token_name", "");

    timeout_ms_ = config()->GetInt("timeout_ms", 200);
    if (timeout_ms_ <= 0) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich init failed! timeout_ms must be > 0";
      return false;
    }

    key_attr_ = config()->GetString("key_attr");
    if (key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich init failed! key_attr is required.";
      return false;
    }
    read_all_column_ = config()->GetBoolean("read_all_column", false);
    std::string kconf_key = config()->GetString("kconf_key", "");
    std::unordered_set<std::string> valid_column;
    auto clotho_kconf_key_config = ks::infra::KConf().Get(kconf_key, std::make_shared<::Json::Value>());
    if (!clotho_kconf_key_config || !clotho_kconf_key_config->Get()) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich init clotho kconf failed!";
      return false;
    }
    ::Json::Value default_value;
    auto clotho_columns_config = clotho_kconf_key_config->Get()->get("columns", default_value);
    if (!clotho_columns_config.isArray() || clotho_columns_config.empty()) {
      LOG(ERROR) << " clotho columns config type is not array!";
      return false;
    }

    for (auto column : clotho_columns_config) {
      auto column_name = column.get("name", "").asString();
      if (!column_name.empty()) {
        valid_column.insert(column_name);
      }
    }

    auto *attrs = config()->Get("value_attr");
    if (attrs && attrs->IsArray()) {
      for (const auto *attr_json : attrs->array()) {
        if (attr_json == nullptr) continue;
        if (attr_json->IsString()) {
          std::string name = attr_json->StringValue();
          if (!name.empty() && valid_column.find(name) != valid_column.end()) rename_map_[name] = name;
        } else if (attr_json->IsObject()) {
          std::string from_name = attr_json->GetString("from");
          std::string to_name = attr_json->GetString("to");
          if (valid_column.find(from_name) != valid_column.end() && !from_name.empty() && !to_name.empty())
            rename_map_[from_name] = to_name;
        } else {
          LOG(ERROR) << "value attr init failed! Item of attrs should be a"
                     << " string! Value found: " << attr_json->ToString();
          return false;
        }
      }
    }
    ::reco::clotho::sdk::ClothoOptions opt;
    opt.self_kess_name = kess_name_;
    using ClothoClient = ::reco::clotho::sdk::ClothoClientInternal;
    clotho_client_ = ::reco::clotho::sdk::ClothoClientBase::GetInstance<ClothoClient>();
    if (clotho_client_ == nullptr) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich Client init nullptr";
      return false;
    }
    if (!clotho_client_->Init(opt)) {
      LOG(ERROR) << "CommonRecoClothoAttrEnrich Client init failed! ";
      return false;
    }
    return true;
  }

 private:
  int timeout_ms_ = 200;
  std::string kess_name_;
  std::string table_name_ = "";
  std::string token_name_ = "";
  std::string key_attr_;
  std::unordered_map<std::string, std::string> rename_map_;
  bool read_all_column_ = false;
  ::reco::clotho::sdk::ClothoClientBase *clotho_client_ = nullptr;
  ::reco::clotho::gateway::TableReadRequest request_;
  ::reco::clotho::gateway::TableReadResponse response_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoClothoAttrEnricher);
};

}  // namespace platform
}  // namespace ks
