#pragma once

#include <memory>
#include <string>
#include <vector>

#include "base/time/time.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/pbutil.h"
#include "google/protobuf/message.h"

namespace ks {
namespace platform {

class CommonRecoProtobufParseAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoProtobufParseAttrEnricher() : random_(base::GetTimestamp()) {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override {
    if (ttl_ == 0 || (ttl_ > 0 && base::GetTimestamp() - last_reset_ts_ > ttl_)) {
      CHECK(ResetMessages());
    }

    if (is_common_attr_) {
      auto str_attr = context->GetStringCommonAttr(input_attr_);
      if (str_attr) {
        auto msg = msgs_[0];
        if (msg->ParseFromArray(str_attr->data(), str_attr->size())) {
          context->SetPtrCommonAttr(output_attr_, msg);
        } else {
          CL_LOG_ERROR("parse_pb_str", "parse_failed:" + class_name_)
              << "Failed to parse protobuf " << class_name_ << " from common attr: " << input_attr_;
        }
      } else {
        CL_LOG_ERROR("parse_pb_str", "missing_common_attr:" + input_attr_)
            << "string common attr not found: " << input_attr_;
      }
    } else {
      CHECK(ReserveMessages(std::distance(begin, end)));
      int missing_attr = 0, parse_failed = 0;
      int i = 0;
      auto *input_accessor = context->GetItemAttrAccessor(input_attr_);
      auto *output_accessor = context->GetItemAttrAccessor(output_attr_);
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto str_attr = result.GetStringAttr(input_accessor);
        if (str_attr) {
          auto msg = msgs_[i++];
          if (msg->ParseFromArray(str_attr->data(), str_attr->size())) {
            result.SetPtrAttr(output_accessor, msg);
          } else {
            ++parse_failed;
          }
        } else {
          ++missing_attr;
        }
      });
      if (parse_failed > 0) {
        CL_LOG_ERROR_COUNT(parse_failed, "parse_pb_str", "parse_failed:" + class_name_)
            << "Failed to parse protobuf from item attr: " << input_attr_ << ", count: " << parse_failed;
      }
      if (missing_attr > 0) {
        CL_LOG_ERROR_COUNT(missing_attr, "parse_pb_str", "missing_item_attr:" + input_attr_)
            << "string item attr not found: " << input_attr_ << ", count: " << missing_attr;
      }
    }

    if (VLOG_IS_ON(1)) {
      uint64 space_used = 0;
      uint64 byte_size = 0;
      for (const auto msg : msgs_) {
        // space_used += msg->SpaceUsedLong();
        space_used += msg->SpaceUsed();
        byte_size += msg->ByteSizeLong();
      }
      CL_LOG(INFO) << GetName() << " holds " << msgs_.size() << " " << class_name_
                   << " messages and occupies total " << space_used << " bytes for payloads of total "
                   << byte_size << " bytes.";
    }
  }

 private:
  bool InitProcessor() override {
    is_common_attr_ = config()->GetBoolean("is_common_attr", true);

    input_attr_ = config()->GetString("input_attr");
    output_attr_ = config()->GetString("output_attr");
    use_dynamic_proto_ = config()->GetBoolean("use_dynamic_proto", false);

    class_name_ = config()->GetString("class_name");
    if (class_name_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufParseAttrEnricher init failed!"
                 << " class_name is required.";
      return false;
    }

    if (!ReserveMessages(1)) {
      LOG(ERROR) << "CommonRecoProtobufParseAttrEnricher init failed!"
                 << " failed to allocate message: " << class_name_;
      return false;
    }

    if (input_attr_.empty() || output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoProtobufParseAttrEnricher init failed!"
                 << " input_attr and output_attr are required.";
      return false;
    }

    int ttl_seconds = config()->GetInt("ttl_seconds", -1);
    if (ttl_seconds > 0) {
      int random_shift = random_.GetInt(0, 120 * base::Time::kMicrosecondsPerSecond);
      ttl_ = ttl_seconds * base::Time::kMicrosecondsPerSecond + random_shift;
    } else {
      ttl_ = ttl_seconds;
    }

    return true;
  }

  bool ReserveMessages(size_t size) {
    size_t allocated_size = msgs_.size();
    if (size > allocated_size) {
      msgs_.resize(size);
      if (!ResetMessages(allocated_size)) {
        return false;
      }
    }
    return true;
  }

  bool ResetMessages(int start = 0) {
    for (size_t i = start; i < msgs_.size(); ++i) {
      if (use_dynamic_proto_) {
        msgs_[i].reset(pbutil::NewMessageByName(class_name_, GlobalHolder::GetDynamicPool(),
                                                GlobalHolder::GetDynamicMessageFactory()));
      } else {
        msgs_[i].reset(pbutil::NewMessageByName(class_name_));
      }
      if (!msgs_[i]) {
        return false;
      }
    }
    last_reset_ts_ = base::GetTimestamp();

    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        msgs_.size() - start, kPerfNs, "pb_msg_reset_num", GlobalHolder::GetServiceIdentifier(),
        GlobalHolder::GetCurrentRequestType(), GetName(), class_name_);
    return true;
  }

 private:
  bool is_common_attr_;
  std::string input_attr_, output_attr_;

  std::vector<std::shared_ptr<::google::protobuf::Message>> msgs_;
  std::string class_name_;
  bool use_dynamic_proto_ = false;

  int64 ttl_ = -1;
  int64 last_reset_ts_ = 0;

  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoProtobufParseAttrEnricher);
};

}  // namespace platform
}  // namespace ks
