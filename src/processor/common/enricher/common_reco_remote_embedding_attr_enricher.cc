//
// Created by ya<PERSON><PERSON><PERSON> on 2020/9/9.
//
#include "dragon/src/processor/common/enricher/common_reco_remote_embedding_attr_enricher.h"

#include <mutex>

#include "dragon/src/core/common_reco_shared_gflags.h"
#include "dragon/src/processor/ext/embed_calc/enricher/util.h"
#include "dragon/src/util/logging_util.h"
#include "serving_base/util/hash.h"
#include "serving_base/utility/system_util.h"
#include "teams/reco-arch/colossusdb/client/emb/embedding_client.h"

namespace ks {
namespace platform {

void CommonRecoRemoteEmbeddingAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                   RecoResultConstIter begin, RecoResultConstIter end) {
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    CL_LOG_ERROR_EVERY("remote_embedding", "empty_kess_service", 100)
        << "remote embedding request cancelled: empty kess_service!";
    return;
  }
  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", 10);
  if (timeout_ms <= 0) {
    CL_LOG_WARNING_EVERY("remote_embedding", "invalid_timeout", 100)
        << "timeout_ms <= 0, using last timeout_ms: " << timeout_ms_;
  } else {
    timeout_ms_ = timeout_ms;
  }
  timer_.Start();

  exit_handler_ = std::shared_ptr<int>(nullptr, [this](void *ptr) { CL_LOG(INFO) << timer_.display(); });

  // request 初始化，保证每个 shard 至少有一个可用的 request
  if (adaptive_shard_num_) {
    shards_ = GetShardNumFromKess(kess_service);
    if (client_side_shard_ && requests_.size() < shards_) {
      requests_.resize(shards_);
    }
  }

  ClearRequests();

  timer_.AppendCostMs("clear_requests");

  std::string converter_type = GetStringProcessorParameter(context, type_name_config_);
  id_conv_ = GetOrInsertConverter(converter_type);
  if (!id_conv_) {
    CL_LOG_ERROR_EVERY("remote_embedding", "invalid_id_converter: " + std::string(converter_type), 1000)
        << "invalid id converter type : " << converter_type;
    return;
  }

  mapping_sign_to_item_keys_.clear();
  if (query_source_type_ == QuerySourceType::kItemAttr) {
    auto item_attr_accessor = context->GetItemAttrAccessor(query_source_item_attr_);
    std::for_each(begin, end,
                  [this, item_attr_accessor, context, &kess_service](const CommonRecoResult &item) {
                    auto item_attr_value = item.GetIntAttr(item_attr_accessor);
                    if (item_attr_value) {
                      uint64 key_sign = id_conv_->ConvertKey(*item_attr_value, slot_, 1);
                      mapping_sign_to_item_keys_[key_sign].emplace(item.item_key);
                      AddSign(key_sign, context, kess_service);
                    } else {
                      CL_LOG_ERROR_EVERY("remote_embedding", "query_source_item_attr", 1000)
                          << "remote embedding query_source_item_attr: " << query_source_item_attr_
                          << " does not exist.";
                    }
                  });
  } else if (query_source_type_ == QuerySourceType::kItemKey) {
    std::for_each(begin, end, [this, context, &kess_service](const CommonRecoResult &item) {
      uint64 key_sign = id_conv_->ConvertKey(item.item_key, slot_, 1);
      mapping_sign_to_item_keys_[key_sign].emplace(item.item_key);
      AddSign(key_sign, context, kess_service);
    });
  } else if (query_source_type_ == QuerySourceType::kItemId) {
    std::for_each(begin, end, [this, context, &kess_service](const CommonRecoResult &item) {
      uint64 key_sign = id_conv_->ConvertKey(item.GetId(), slot_, 1);
      mapping_sign_to_item_keys_[key_sign].emplace(item.item_key);
      AddSign(key_sign, context, kess_service);
    });
  } else {
    uint64_t user_id = context->GetUserId();
    if ((query_source_type_ == QuerySourceType::kUserId ||
         query_source_type_ == QuerySourceType::kUserIdAndDeviceId) &&
        user_id != 0) {
      AddSign(id_conv_->ConvertKey(user_id, slot_, 1), context, kess_service);
    }

    const std::string &device_id = context->GetDeviceId();
    if ((query_source_type_ == QuerySourceType::kDeviceId ||
         query_source_type_ == QuerySourceType::kUserIdAndDeviceId) &&
        !device_id.empty()) {
      AddSign(id_conv_->ConvertKey(base::GetHash(device_id), slot_, 1), context, kess_service);
    }
  }

  timer_.AppendCostMs("collect_item_signs");

  if (colossusdb_embd_table_name_.empty()) {
    SendRequests(context, kess_service);
  } else {
    SendColossusdbRequests(context, kess_service);
  }

  exit_handler_.reset();
}

void CommonRecoRemoteEmbeddingAttrEnricher::SendRequestToShard(
    MutableRecoContextInterface *context, const ks::reco::bt_embd_s::BatchEmbeddingsRequest &request,
    std::string shard_name, const std::string &kess_service) {
  std::string request_info = "kess_service: " + kess_service + ", shard: " + shard_name +
                             ", timeout_ms: " + std::to_string(timeout_ms_);
  CL_LOG(INFO) << "sending embedding remote query request, sign_num: " << std::to_string(request.signs_size())
               << ", " << request_info;

  auto *resp = response_pool_.Acquire();

  std::pair<bool, ks::kess::rpc::grpc::Future<::ks::reco::bt_embd_s::BatchEmbeddingsResponse *>> pr;
  if (FLAGS_dragon_grpc_use_multi_eventloop) {
    pr = [&]() {
      KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_service, kess_cluster_, shard_name, timeout_ms_, request,
                                             resp, ks::reco::bt_embd_s::kess::BtEmbeddingService,
                                             AsyncGetBatchEmbeddings);
    }();
  } else {
    pr = [&]() {
      KESS_GRPC_ASYNC_RETURN(kess_service, kess_cluster_, shard_name, timeout_ms_, request, resp,
                             ks::reco::bt_embd_s::kess::BtEmbeddingService, AsyncGetBatchEmbeddings);
    }();
  }

  if (!pr.first) {
    CL_LOG_ERROR_EVERY("remote_embedding", "send_req_failed", 100)
        << "embedding server send request failed, skip response.";
    return;
  }

  timer_.AppendCostMs("send_requests_" + shard_name);

  auto callback = [this, context, shard_name](ks::reco::bt_embd_s::BatchEmbeddingsResponse *resp) {
    timer_.AppendCostMs("wait_for_callback_" + shard_name);

    if (save_to_common_attr_) {
      FillCommonAttr(context, *resp, shard_name);
      timer_.AppendCostMs("fill_common_attr_" + shard_name);
    } else {
      FillItemAttr(context, *resp, shard_name);
      timer_.AppendCostMs("fill_item_attr_" + shard_name);
    }
  };

  auto finally = [this, resp, shard_name, handler = exit_handler_]() {
    response_pool_.Recycle(resp);
    timer_.AppendCostMs("recycle_response_" + shard_name);
  };

  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), std::move(finally), request_info);
}

void CommonRecoRemoteEmbeddingAttrEnricher::SendColossusdbRequests(MutableRecoContextInterface *context,
                                                                   const std::string &kess_service) {
  CHECK(!colossusdb_embd_table_name_.empty());
  if (requests_[0].signs_size() == 0) {
    return;
  }
  if (colossusdb_embd_client_map_.find(kess_service) == colossusdb_embd_client_map_.end()) {
    colossusdb_embd_client_map_[kess_service] = std::make_unique<colossusdb::EmbeddingClient>(
        kess_service, colossusdb_embd_table_name_, timeout_ms_, max_signs_per_request_);
  }

  auto *resp = response_pool_.Acquire();
  std::future<std::unique_ptr<colossusdb::BatchEmbeddingsResponseWrapper>> wrapper_future =
      colossusdb_embd_client_map_[kess_service]->BatchWrapperGetAsync(
          resp, {requests_[0].signs().begin(), requests_[0].signs().end()});
  timer_.AppendCostMs("send_requests");

  auto get_response =
      [](std::future<std::unique_ptr<colossusdb::BatchEmbeddingsResponseWrapper>> wrapper_future) -> bool * {
    if (!wrapper_future.valid()) {
      CL_LOG_ERROR("remote_embedding", "invalid_wrapper_future") << "invalid colossusdb wrapper future!";
      return nullptr;
    }

    std::unique_ptr<colossusdb::BatchEmbeddingsResponseWrapper> wrapper = wrapper_future.get();
    if (!wrapper) {
      CL_LOG_WARNING_EVERY("remote_embedding", "null wrapper", 100) << "colossusdb response wrapper is null";
      return nullptr;
    }
    return &wrapper->succ;
  };

  std::future<bool *> future = std::async(std::launch::deferred, get_response, std::move(wrapper_future));

  auto callback = [this, resp, context](bool *succ) {
    if (!*succ) {
      CL_LOG_WARNING_EVERY("remote_embedding", "get_resp_failed", 100) << "all requests are failed";
      return;
    }
    timer_.AppendCostMs("colossusdb_wait_for_callback");
    if (save_to_common_attr_) {
      FillCommonAttr(context, *resp, "colossusdb");
      timer_.AppendCostMs("fill_common_attr");
    } else {
      FillItemAttr(context, *resp, "colossusdb");
      timer_.AppendCostMs("fill_item_attr");
    }
  };

  auto finally = [this, resp, handler = exit_handler_]() { response_pool_.Recycle(resp); };

  std::string request_info = "kess_service: " + kess_service + ", timeout_ms: " + std::to_string(timeout_ms_);
  RegisterLocalAsyncCallback(context, std::move(future), std::move(callback), std::move(finally),
                             request_info);
}

void CommonRecoRemoteEmbeddingAttrEnricher::FillCommonAttr(
    MutableRecoContextInterface *context, const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
    const std::string &shard_name) {
  if (sub_response.items_size() == 0) {
    CL_LOG(INFO) << "shard: " << shard_name << " gets empty sub_response";
    return;
  }

  if (is_raw_data_) {
    if (raw_data_type_ == RawDataType::kUnsignedInt8) {
      SaveRawDataCommonAttr<uint8>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kUnsignedInt16) {
      SaveRawDataCommonAttr<uint16>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
      SaveRawDataCommonAttr<uint32>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
      SaveRawDataCommonAttr<uint64>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kSignedInt8) {
      SaveRawDataCommonAttr<int8>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kSignedInt16) {
      SaveRawDataCommonAttr<int16>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kSignedInt32) {
      SaveRawDataCommonAttr<int32>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kSignedInt64) {
      SaveRawDataCommonAttr<int64>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kFloat32) {
      SaveRawDataCommonAttr<float>(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kString) {
      SaveRawDataStringCommonAttr(context, sub_response);
    } else if (raw_data_type_ == RawDataType::kScaledInt8) {
      SaveRawDataCommonAttr<float>(
          context, sub_response, [&](const std::string &src, std::vector<float> *dst) -> void {
            int size = (src.size() - sizeof(float)) / sizeof(uint8);
            dst->resize(size);
            ScaleInt8ToFloat(reinterpret_cast<const int8_t *>(src.data()), dst->data(), size);
          });
    } else {
      CL_LOG_ERROR("remote_embedding", "invalid_raw_data_type")
          << "Unexpected raw_data_type: " << int(raw_data_type_);
    }
  } else {
    SaveEmbeddingCommonAttr(context, sub_response);
  }
}

void CommonRecoRemoteEmbeddingAttrEnricher::FillItemAttr(
    MutableRecoContextInterface *context, const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
    const std::string &shard_name) {
  if (sub_response.items_size() == 0) {
    CL_LOG(INFO) << "shard: " << shard_name << " gets empty sub_response";
    return;
  }

  for (auto iter = sub_response.items().begin(); iter != sub_response.items().end(); iter++) {
    const std::string &ele = iter->second;
    if (ele.size() <= 0) {
      CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
          << "item sign: " << iter->first << ", item's embedding empty";
      continue;
    }
    auto sign = iter->first;

    for (uint64_t item_key : mapping_sign_to_item_keys_[sign]) {
      if (is_raw_data_) {
        if (raw_data_type_ == RawDataType::kUnsignedInt8) {
          SaveRawDataItemAttr<uint8>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kUnsignedInt16) {
          SaveRawDataItemAttr<uint16>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kUnsignedInt32) {
          SaveRawDataItemAttr<uint32>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kUnsignedInt64) {
          SaveRawDataItemAttr<uint64>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kSignedInt8) {
          SaveRawDataItemAttr<int8>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kSignedInt16) {
          SaveRawDataItemAttr<int16>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kSignedInt32) {
          SaveRawDataItemAttr<int32>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kSignedInt64) {
          SaveRawDataItemAttr<int64>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kFloat32) {
          SaveRawDataItemAttr<float>(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kString) {
          SaveRawDataStringItemAttr(context, item_key, output_attr_name_, ele.data(), ele.size());
        } else if (raw_data_type_ == RawDataType::kScaledInt8) {
          std::vector<float> result;
          int size = (ele.size() - sizeof(float)) / sizeof(uint8);
          result.resize(size);
          ScaleInt8ToFloat(reinterpret_cast<const int8_t *>(ele.data()), result.data(), size);
          SaveRawDataItemAttr<float>(context, item_key, output_attr_name_, result.data(), result.size()
            * sizeof(float));
        } else {
          CL_LOG_ERROR("remote_embedding", "invalid_raw_data_type")
              << "Unexpected raw_data_type: " << int(raw_data_type_);
        }
      } else {
        SaveEmbeddingItemAttr(context, item_key, output_attr_name_, ele.data(), ele.size());
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRemoteEmbeddingAttrEnricher,
                 CommonRecoRemoteEmbeddingAttrEnricher)
}  // namespace platform
}  // namespace ks
