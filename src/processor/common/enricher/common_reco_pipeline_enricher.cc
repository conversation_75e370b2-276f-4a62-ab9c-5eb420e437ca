#include "dragon/src/processor/common/enricher/common_reco_pipeline_enricher.h"

#include <kenv/context.h>
#include <utility>

#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/util/perf_report_util.h"
#include "ks/common_reco/util/key_sign_util.h"

namespace ks {
namespace platform {

void CommonRecoPipelineEnricher::MergeSubflowStepInfo(AddibleRecoContextInterface *context) {
  for (int i = 0; i < executor_.GetProcessorStepInfosCount(); ++i) {
    auto *info = context->GetNewStepInfo();
    info->Swap(executor_.GetProcessorStepInfo(i));
  }
}

void CommonRecoPipelineEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                        RecoResultConstIter end) {
  int64 start_ts = base::GetTimestamp();

  executor_.Reset(context, fill_common_attrs_from_request_, fill_browse_set_from_request_,
                  &copy_common_attrs_, GetTableName());

  CloneContext(context, begin, end);
  bool need_subflow_traceback =
      traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.1") >= 0;
  if (need_subflow_traceback) {
    traceback_util::InitTraceback(context, executor_.GetContext());
  }
  std::string request_type = context->GetRequestType();
  int64 put_in_pool_ts = base::GetTimestamp();
  int task_queue_id = GetIntProcessorParameter(context, "task_queue_id", 0);
  auto *sub_flow_thread_pool = executor_.GetAsyncPool(task_queue_id);
  if (!sub_flow_thread_pool) {
    CL_LOG_EXCEPTION("no_thread_pool_for_sub_flow")
        << "PipelineEnricher " << GetName() << " task_queue_id: " << task_queue_id
        << " get sub flow thread pool failed";
    return;
  }

  auto waiting_task_num = sub_flow_thread_pool->GetQueueSize();
  base::perfutil::PerfUtilWrapper::IntervalLogStash(waiting_task_num, kPerfNs, "sub_flow_waiting_task_num",
                                                    GlobalHolder::GetServiceIdentifier(), request_type,
                                                    absl::StrCat("task_queue: ", task_queue_id));

  auto future = sub_flow_thread_pool->Async(
      context->GetRequestType(), GetName(),
      [this, put_in_pool_ts, request_type = std::move(request_type),
       enable_perf_report = PerfReportUtil::IsEnabled()]() {
        SetPerfEnabledOneshot set_perf_enabled(enable_perf_report);
        int64 execute_start_ts = base::GetTimestamp();
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            execute_start_ts - put_in_pool_ts, kPerfNs, "sub_flow_queue_time",
            GlobalHolder::GetServiceIdentifier(), request_type, pipeline_name_);
        executor_.Run();
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            base::GetTimestamp() - execute_start_ts, kPerfNs, "sub_flow_run_time",
            GlobalHolder::GetServiceIdentifier(), request_type, pipeline_name_);
        return executor_.GetContext();
      },
      GetDownstreamProcessor());

  CommonRecoContext *main_context = static_cast<CommonRecoContext *>(context);
  if (!main_context) return;

  auto callback = [this, main_context, need_subflow_traceback](CommonRecoContext *merge_context) {
    int64 callback_start_ts = base::GetTimestamp();
    merge_context->SetMainTable(GetTableName());
    main_context->MergeContext(merge_context, -1, false, return_common_attrs_, return_item_attrs_,
                               pipeline_name_, merge_and_overwrite_, false, merge_item_attr_for_all_items_);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        base::GetTimestamp() - callback_start_ts, kPerfNs, "merge_item_attr_time",
        GlobalHolder::GetServiceIdentifier(), main_context->GetRequestType(), pipeline_name_);
    if (need_subflow_traceback) {
      MergeSubflowStepInfo(main_context);
    }
    if (!save_results_to_.empty()) {
      main_context->SetPtrCommonAttr(save_results_to_, &(merge_context->GetCommonRecoResults()));
    }
    CL_LOG(INFO) << "PipelineEnricher " << GetName() << " succeed, " << executor_.GetProcessorNum()
                 << " processors in pipeline " << pipeline_name_;
  };

  auto finally = [this, start_ts, main_context]() {
    executor_.Cancel();
    DereferenceAttrs(main_context);
    int64 duration_ms = base::GetTimestamp() - start_ts;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(duration_ms, kPerfNs, "sub_flow_total_time",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      main_context->GetRequestType(), pipeline_name_);
  };

  auto timeout_cb = [executor_context = executor_.GetContext()]() { executor_context->MarkInvalid(); };

  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", 0);
  RegisterLocalAsyncCallback(context, std::move(future), std::move(callback), std::move(finally),
                             "sub_flow:" + pipeline_name_, timeout_ms, std::move(timeout_cb));
}

bool CommonRecoPipelineEnricher::CloneContext(ReadableRecoContextInterface *context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
  // 克隆结果集
  CommonRecoContext *main_context = static_cast<CommonRecoContext *>(context);
  executor_.GetContext()->CloneTargetResults(main_context, begin, end);
  // 克隆 item attr
  for (const auto &attr_name : copy_item_attrs_) {
    executor_.GetContext()->CloneItemAttr(context, attr_name, !deep_copy_, !deep_copy_);
  }
  // 克隆 common attr
  for (const auto &attr_name : copy_common_attrs_) {
    executor_.GetContext()->CloneCommonAttr(context, attr_name, !deep_copy_, !deep_copy_);
  }
  executor_.GetContext()->ResetStageStartTime(main_context->GetStageStartTime());
  return true;
}

void CommonRecoPipelineEnricher::DereferenceAttrs(CommonRecoContext *context) {
  if (!deep_copy_) {
    for (const auto &attr_name : copy_item_attrs_) {
      context->DereferenceItemAttr(attr_name);
    }
    for (const auto &attr_name : copy_common_attrs_) {
      context->DereferenceCommonAttr(attr_name);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPipelineEnricher, CommonRecoPipelineEnricher);

}  // namespace platform
}  // namespace ks
