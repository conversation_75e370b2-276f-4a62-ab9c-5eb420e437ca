#include "dragon/src/processor/common/enricher/common_reco_dump_context_enricher.h"

namespace ks {
namespace platform {
void CommonRecoDumpContextEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if (dump_as_table_) {
    DumpItemTable(context, begin, end);
  } else {
    DumpStepInfo(context, begin, end);
  }
}

void CommonRecoDumpContextEnricher::DumpItemTable(MutableRecoContextInterface *context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  thread_local DataTable table_info;
  table_info.Clear();

  table_info.set_name(GetTableName());
  std::vector<AttrValue *> attrs;
  if (dump_all_item_attrs_) {
    attrs = context->GetAllItemAttrs();
  } else {
    for (const auto &attr_name : item_attrs_) {
      attrs.push_back(context->GetItemAttrAccessor(attr_name));
    }
  }
  RecoUtil::BuildPackedTableColumns(begin, end, attrs, &table_info);

  thread_local std::string serialize_string;
  serialize_string.clear();
  if (table_info.SerializeToString(&serialize_string)) {
    CL_LOG(INFO) << "dump_context succeed to common attr " << dump_to_attr_
                 << ", string size: " << serialize_string.size()
                 << ", item num: " << std::distance(begin, end) << ", item_attr num: " << attrs.size();
    context->SetStringCommonAttr(dump_to_attr_, std::move(serialize_string));
  } else {
    CL_LOG_ERROR("dump_context", "failed_to_serialize")
        << "Context serialize to string failed in common attr " << dump_to_attr_;
  }
}

void CommonRecoDumpContextEnricher::DumpStepInfo(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  thread_local CommonRecoStepInfo info;
  info.Clear();
  // save common_attrs
  int dump_item_size = 0;
  int dump_item_attr_num = 0;
  int dump_common_attr_num = 0;

  auto load_common_attr = [&](const std::string &attr_name) {
    auto *attr = info.add_common_attrs();
    if (!interop::LoadSampleAttrFromCommonAttr(context, attr_name, attr)) {
      CL_LOG_EVERY_N(WARNING, 100) << "save common attr to step info failed, attr_name: " << attr_name
                                   << ", processor_name: " << GetName();
    } else {
      dump_common_attr_num++;
    }
  };
  std::for_each(common_attrs_.begin(), common_attrs_.end(), load_common_attr);

  if (dump_common_attrs_from_request_) {
    auto common_attrs_from_reqeust = context->GetCommonAttrsInRequest();
    std::for_each(common_attrs_from_reqeust.begin(), common_attrs_from_reqeust.end(), load_common_attr);
  }

  if (include_item_results_) {
    // save item
    dump_item_size = std::distance(begin, end);
    info.set_post_item_num(dump_item_size);
    auto *compressed_item = info.mutable_compressed_items();
    auto *compressed_item_ids = compressed_item->mutable_compressed_item_ids();
    auto *compressed_item_types = compressed_item->mutable_compressed_item_types();
    auto *compressed_reasons = compressed_item->mutable_compressed_reasons();
    auto *compressed_scores = compressed_item->mutable_compressed_scores();
    auto *compressed_attrs = compressed_item->mutable_compressed_attrs();

    compressed_item_ids->reserve(dump_item_size * sizeof(uint64));
    compressed_item_types->reserve(dump_item_size * sizeof(int32));
    compressed_reasons->reserve(dump_item_size * sizeof(int32));
    compressed_scores->reserve(dump_item_size * sizeof(float));
    uint64 uint64_val = 0;
    int64 int64_val = 0;
    int32 int32_val = 0;
    float float_val = 0.0;
    for (auto result = begin; result != end; ++result) {
      uint64_val = result->GetId();
      compressed_item_ids->append(reinterpret_cast<const char *>(&uint64_val), sizeof(uint64));
      int32_val = result->GetType();
      compressed_item_types->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
      int32_val = result->reason;
      compressed_reasons->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
      float_val = result->score;
      compressed_scores->append(reinterpret_cast<const char *>(&float_val), sizeof(float));
    }
    // save item_attrs
    std::vector<AttrValue *> attrs;
    if (dump_all_item_attrs_) {
      attrs = context->GetAllItemAttrs();
    } else {
      for (const auto &attr_name : item_attrs_) {
        attrs.push_back(context->GetItemAttrAccessor(attr_name));
      }
    }

    for (AttrValue *accessor : attrs) {
      const auto &attr_name = accessor->name();
      if (accessor->value_type == AttrType::UNKNOWN) {
        CL_LOG(INFO) << "Failed dump item_name " << attr_name
                     << ". Because unknown type or this item_attr not exist.";
        continue;
      }
      dump_item_attr_num++;
      auto *attr_value = compressed_item->add_packed_item_attr();
      std::for_each(begin, end, [this, accessor, attr_value](const CommonRecoResult &result) {
        if (accessor->value_type == AttrType::INT) {
          traceback_util::BuildIntPackedTracebackData(attr_value, result.GetIntAttr(accessor));
        } else if (accessor->value_type == AttrType::FLOAT) {
          traceback_util::BuildDoublePackedTracebackData(attr_value, result.GetDoubleAttr(accessor));
        } else if (accessor->value_type == AttrType::STRING) {
          traceback_util::BuildStringPackedTracebackData(attr_value, result.GetStringAttr(accessor));
        } else if (accessor->value_type == AttrType::INT_LIST) {
          traceback_util::BuildIntListPackedTracebackData(attr_value, result.GetIntListAttr(accessor));
        } else if (accessor->value_type == AttrType::FLOAT_LIST) {
          traceback_util::BuildDoubleListPackedTracebackData(attr_value, result.GetDoubleListAttr(accessor));
        } else if (accessor->value_type == AttrType::STRING_LIST) {
          traceback_util::BuildStringListPackedTracebackData(attr_value, result.GetStringListAttr(accessor));
        }
      });
      bool compress_success = traceback_util::CompressPackedTracebackData(attr_value);
      if (!compressed_attrs->empty()) {
        compressed_attrs->append(kTracebackAttrNameSeparator);
      }
      compressed_attrs->append(attr_name);
    }
  }
  thread_local std::string serialize_string;
  serialize_string.clear();
  if (info.SerializeToString(&serialize_string)) {
    CL_LOG(INFO) << "dump_context succeed to common attr " << dump_to_attr_
                 << ", string size: " << serialize_string.size() << ", item num: " << dump_item_size
                 << ", item_attr num: " << dump_item_attr_num
                 << ", common_attr num: " << dump_common_attr_num;
    context->SetStringCommonAttr(dump_to_attr_, std::move(serialize_string));
  } else {
    CL_LOG_ERROR("dump_context", "failed_to_serialize")
        << "Context serialize to string failed in common attr " << dump_to_attr_;
  }
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDumpContextEnricher, CommonRecoDumpContextEnricher)
}  // namespace platform
}  // namespace ks
