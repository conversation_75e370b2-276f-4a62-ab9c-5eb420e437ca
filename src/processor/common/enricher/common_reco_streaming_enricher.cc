#include "dragon/src/processor/common/enricher/common_reco_streaming_enricher.h"

namespace ks {
namespace platform {

bool CommonRecoStreamingEnricher::PrepareStreaming(MutableRecoContextInterface *context) {
  auto kess_channel =
      std::dynamic_pointer_cast<ks::kess::rpc::grpc::SingleChannel>(client_->All()->SelectOne());
  if (!kess_channel) {
    CL_LOG(ERROR) << "invalid kess_channel, service: " << service_ << " group: " << service_group_;
    context->SetStreamingStatus(StreamingStatus::INVALID);
    return false;
  }
  auto grpc_channel = kess_channel->GrpcChannel();
  if (!grpc_channel) {
    CL_LOG(ERROR) << "invalid grpc_channel, service: " << service_ << " group: " << service_group_;
    context->SetStreamingStatus(StreamingStatus::INVALID);
    return false;
  }
  stub_ = ks::platform::CommonRecoLeafStreamingService::NewStub(grpc_channel);
  client_context_ = std::make_unique<::grpc::ClientContext>();
  std::chrono::time_point deadline =
      std::chrono::system_clock::now() + std::chrono::milliseconds(timeout_ms_);
  client_context_->set_deadline(deadline);
  streaming_ = stub_->StreamingRecommend(client_context_.get());
  if (!streaming_) {
    CL_LOG(ERROR) << "invalid streaming, service: " << service_ << " group: " << service_group_;
    context->SetStreamingStatus(StreamingStatus::INVALID);
    return false;
  }
  return true;
}

bool CommonRecoStreamingEnricher::SendRequest(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  ks::platform::CommonRecoRequest request;

  std::string request_type = GetStringProcessorParameter(context, "request_type");
  request.set_request_type(request_type.empty() ? context->GetRequestType() : request_type);
  request.set_user_id(context->GetUserId());
  request.set_device_id(context->GetDeviceId());
  request.set_time_ms(context->GetRequestTime());
  request.set_request_id(context->GetRequestId());
  request.set_debug(context->IsDebugRequest());
  request.set_need_traceback(context->NeedTraceback());
  if (context->GetRequest()->has_abtest_mapping_id()) {
    request.mutable_abtest_mapping_id()->CopyFrom(context->GetRequest()->abtest_mapping_id());
  }

  for (const auto &attr_as : recv_common_attrs_) {
    request.add_return_common_attrs(attr_as.first);
  }
  for (const auto &attr_as : send_common_attrs_) {
    kuiba::SampleAttr *attr = interop::BuildSampleAttrFromCommonAttr(
        context, attr_as.first, request.mutable_common_attr(), attr_as.second);
    if (!attr || attr->type() == kuiba::CommonSampleEnum::UNKNOWN_ATTR) {
      CL_LOG_EVERY_N(WARNING, 1000) << "common attr " << attr_as.first << " is requested but not found";
    } else {
      VLOG(1) << "common attr sent: " << attr_as.first;
    }
  }

  VLOG(10) << "Streaming Request: " << request.Utf8DebugString();
  if (!streaming_->Write(request, ::grpc::WriteOptions().set_write_through())) {
    CL_LOG(ERROR) << "Streaming write failed, streaming finish";
    context->SetStreamingStatus(StreamingStatus::INVALID);
    return false;
  }
  return true;
}

void CommonRecoStreamingEnricher::RecvResponse(MutableRecoContextInterface *context,
                                               RecoResultConstIter begin, RecoResultConstIter end) {
  ks::platform::CommonRecoResponse response;
  if (!streaming_->Read(&response)) {
    CL_LOG(ERROR) << "Streaming read failed, mark as invalid";
    context->SetStreamingStatus(StreamingStatus::INVALID);
    return;
  }

  if (!recv_common_attrs_.empty()) {
    for (const auto &attr : response.common_attr()) {
      auto it = recv_common_attrs_.find(attr.name());
      if (it != recv_common_attrs_.end()) {
        interop::SaveSampleAttrToCommonAttr(context, it->second, attr);
      }
    }
  }

  if (response.streaming_finish()) {
    VLOG(10) << "Streaming will finish by downstream";
    context->SetStreamingStatus(StreamingStatus::FINISH);
    return;
  }
}

void CommonRecoStreamingEnricher::OnPipelineExit(ReadableRecoContextInterface *context) {
  if (client_context_) client_context_->TryCancel();
  if (streaming_) streaming_->Finish();
  stub_.reset();
  client_context_.reset();
  streaming_.reset();
}

void CommonRecoStreamingEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                         RecoResultConstIter end) {
  if (context->GetStreamingLoopIndex() == 0) {
    if (!PrepareStreaming(context)) return;
    if (!SendRequest(context, begin, end)) return;
  }
  RecvResponse(context, begin, end);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStreamingEnricher, CommonRecoStreamingEnricher)

}  // namespace platform
}  // namespace ks
