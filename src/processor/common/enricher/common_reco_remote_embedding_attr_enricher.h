//
// Created by ya<PERSON><PERSON><PERSON> on 2020/9/9.
//

#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <algorithm>
#include <atomic>
#include <memory>
#include <string>
#include <tuple>
#include <type_traits>
#include <typeinfo>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/common_reco/util/id_converter.h"
#include "ks/reco/bt_embedding_server/proto/bt_embedding_service.kess.grpc.pb.h"
#include "ks/reco_pub/reco/predict/base/public.h"
#include "serving_base/server_base/kess_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossusdb/client/emb/embedding_client.h"

namespace ks {
namespace platform {

class CommonRecoRemoteEmbeddingAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoRemoteEmbeddingAttrEnricher() {}

  ~CommonRecoRemoteEmbeddingAttrEnricher() {
    for (auto pair : converter_map_) {
      if (pair.second) {
        delete pair.second;
      }
    }
  }

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    kess_cluster_ = config()->GetString("kess_cluster", "PRODUCTION");
    colossusdb_embd_table_name_ = config()->GetString("colossusdb_embd_table_name", "");
    shards_ = config()->GetInt("shard_num", 1);
    if (!colossusdb_embd_table_name_.empty()) {
      CHECK_EQ(shards_, 1) << "shard num must be 1 in colossudb mode";
    }
    if (shards_ < 0) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! shard num must >= 0";
      return false;
    } else if (shards_ == 0) {
      adaptive_shard_num_ = true;
    } else {
      adaptive_shard_num_ = false;
    }

    client_side_shard_ = config()->GetBoolean("client_side_shard", false);
    if (client_side_shard_) {
      requests_.resize(shards_);
    } else {
      requests_.resize(1);
    }

    max_signs_per_request_ = config()->GetInt("max_signs_per_request", 0);

    shard_prefix_ = config()->GetString("shard_prefix", "s");

    save_to_common_attr_ = config()->GetBoolean("save_to_common_attr", false);
    const std::string &query_source_type = config()->GetString("query_source_type", "item_key");
    if (query_source_type == "item_attr") {
      query_source_type_ = QuerySourceType::kItemAttr;
      query_source_item_attr_ = config()->GetString("query_source_item_attr", "");
      if (query_source_item_attr_.empty()) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! query_source_item_attr"
                   << " must not be empty when query_source_type is item_attr";
        return false;
      }
    } else if (query_source_type == "item_key") {
      query_source_type_ = QuerySourceType::kItemKey;
    } else if (query_source_type == "item_id") {
      query_source_type_ = QuerySourceType::kItemId;
    } else {
      if (!save_to_common_attr_) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! save_to_common_attr must be true "
                      "when query_source_type is not item_key";
        return false;
      }
      if (query_source_type == "user_id") {
        query_source_type_ = QuerySourceType::kUserId;
      } else if (query_source_type == "device_id") {
        query_source_type_ = QuerySourceType::kDeviceId;
      } else if (query_source_type == "user_id_and_device_id") {
        query_source_type_ = QuerySourceType::kUserIdAndDeviceId;
      } else {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! Unexpected query_source_type: "
                   << query_source_type;
        return false;
      }
    }

    id_conv_config_ = config()->Get("id_converter");
    if (id_conv_config_ == nullptr || id_conv_config_->GetString("type_name", "").empty()) {
      LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! id converter config error";
      return false;
    }
    type_name_config_ = id_conv_config_->Get("type_name");

    slot_ = config()->GetInt("slot", 0);

    if (save_to_common_attr_) {
      output_item_list_attr_ = config()->GetString("output_item_list_attr", "");
      if (output_item_list_attr_.empty()) {
        LOG(WARNING) << "output_item_list_attr is empty, output item list will be dropped.";
      }

      output_embedding_list_attr_ = config()->GetString("output_embedding_list_attr", "");
      if (output_embedding_list_attr_.empty()) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! output_embedding_list_attr empty";
        return false;
      }
    } else {
      output_attr_name_ = config()->GetString("output_attr_name", "");
      if (output_attr_name_.empty()) {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! output_attr_name empty";
        return false;
      }
    }

    is_raw_data_ = config()->GetBoolean("is_raw_data", false);
    if (is_raw_data_) {
      const std::string &raw_data_type = config()->GetString("raw_data_type", "uint16");
      if (raw_data_type == "uint8") {
        raw_data_type_ = RawDataType::kUnsignedInt8;
      } else if (raw_data_type == "uint16") {
        raw_data_type_ = RawDataType::kUnsignedInt16;
      } else if (raw_data_type == "uint32") {
        raw_data_type_ = RawDataType::kUnsignedInt32;
      } else if (raw_data_type == "uint64") {
        raw_data_type_ = RawDataType::kUnsignedInt64;
      } else if (raw_data_type == "int8") {
        raw_data_type_ = RawDataType::kSignedInt8;
      } else if (raw_data_type == "int16") {
        raw_data_type_ = RawDataType::kSignedInt16;
      } else if (raw_data_type == "int32") {
        raw_data_type_ = RawDataType::kSignedInt32;
      } else if (raw_data_type == "int64") {
        raw_data_type_ = RawDataType::kSignedInt64;
      } else if (raw_data_type == "float32") {
        raw_data_type_ = RawDataType::kFloat32;
      } else if (raw_data_type == "string") {
        raw_data_type_ = RawDataType::kString;
      } else if (raw_data_type == "scale_int8") {
        raw_data_type_ = RawDataType::kScaledInt8;
      } else {
        LOG(ERROR) << "CommonRecoRemoteEmbeddingAttrEnricher init failed! unsupported raw_data_type: "
                   << raw_data_type;
        return false;
      }

      is_raw_data_list_ = config()->GetBoolean("is_raw_data_list", true);
    }

    return true;
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value> SaveRawDataItemAttr(MutableRecoContextInterface *context,
                                                                         uint64 item_key,
                                                                         const std::string &attr_name,
                                                                         const void *data, size_t size) {
    if (size == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty raw data: " << item_key;
      return;
    }

    size_t element_size = sizeof(T);
    int dim = size / element_size;
    if (dim * element_size != size) {
      CL_LOG(ERROR) << "Unexpected size: " << size << " for sizeof(" << typeid(T).name()
                    << ") = " << element_size;
      return;
    }
    const T *raw_data = reinterpret_cast<const T *>(data);

    std::vector<double> value_list;
    value_list.reserve(dim);
    for (int i = 0; i < dim; i++) {
      value_list.push_back(raw_data[i]);
    }
    if (is_raw_data_list_) {
      context->SetDoubleListItemAttr(item_key, attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetDoubleItemAttr(item_key, attr_name, value_list[0]);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value> SaveRawDataItemAttr(MutableRecoContextInterface *context,
                                                                   uint64 item_key,
                                                                   const std::string &attr_name,
                                                                   const void *data, size_t size) {
    if (size == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty raw data: " << item_key;
      return;
    }

    size_t element_size = sizeof(T);
    int dim = size / element_size;
    if (dim * element_size != size) {
      CL_LOG(ERROR) << "Unexpected size: " << size << " for sizeof(" << typeid(T).name()
                    << ") = " << element_size;
      return;
    }
    const T *raw_data = reinterpret_cast<const T *>(data);

    std::vector<int64> value_list;
    value_list.reserve(dim);
    for (int i = 0; i < dim; i++) {
      value_list.push_back(raw_data[i]);
    }
    if (is_raw_data_list_) {
      context->SetIntListItemAttr(item_key, attr_name, std::move(value_list));
    } else {
      if (value_list.size() != 1) {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
        return;
      }
      context->SetIntItemAttr(item_key, attr_name, value_list[0]);
    }
  }

  void SaveRawDataStringItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                 const std::string &attr_name, const void *data, size_t size) {
    if (size == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty raw data: " << item_key;
      return;
    }

    context->SetStringItemAttr(item_key, attr_name, std::string(static_cast<const char *>(data), size));
  }

  void SaveEmbeddingItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                             const std::string &attr_name, const void *data, size_t size) {
    if (size == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "Empty embedding data: " << item_key;
      return;
    }

    size_t element_size = sizeof(int16);
    int dim = size / element_size;
    VLOG(1) << "item_key: " << item_key << ", dim: " << dim;
    if (dim * element_size != size) {
      CL_LOG(ERROR) << "Unexpected embedding size: " << size;
      return;
    }
    const int16 *weights = reinterpret_cast<const int16 *>(data);

    std::vector<double> embedding;
    embedding.reserve(dim);
    for (int j = 0; j < dim; j++) {
      embedding.push_back(ks::reco::WeightToFloat(weights[j]));
    }
    context->SetDoubleListItemAttr(item_key, attr_name, std::move(embedding));
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value> SaveRawDataCommonAttr(
      MutableRecoContextInterface *context,
      const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response) {
    std::vector<int64> ids;
    std::vector<int64> value_list;
    for (auto iter = sub_response.items().begin(); iter != sub_response.items().end(); iter++) {
      const std::string &ele = iter->second;
      if (ele.size() <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      auto id = id_conv_->ConvertId(sign);
      size_t element_size = sizeof(T);
      int dim = ele.size() / element_size;
      VLOG(1) << "sign: " << sign << ", dim: " << dim;
      if (dim * element_size != ele.size()) {
        CL_LOG(ERROR) << "Unexpected size: " << ele.size() << " for sizeof(" << typeid(T).name()
                      << ") = " << element_size;
        continue;
      }
      auto *raw_data = reinterpret_cast<const T *>(ele.data());
      ids.push_back(id);
      for (int j = 0; j < dim; j++) {
        value_list.push_back(raw_data[j]);
      }
    }

    if (ids.size() == 0 || value_list.size() == 0 || (value_list.size() % ids.size() > 0)) {
      CL_LOG_ERROR("remote_embedding", "size_fail")
          << "value_list size: " << value_list.size() << " mismatch ids size: " << ids.size();
      return;
    }
    if (is_raw_data_list_) {
      if (!context->SetIntListCommonAttr(output_embedding_list_attr_, std::move(value_list))) {
        CL_LOG_ERROR("remote_embedding", "set_embedding_failed")
            << "set value_list failed, output_embedding_list_attr: " << output_embedding_list_attr_;
      }
    } else {
      if (value_list.size() == 1) {
        context->SetIntCommonAttr(output_embedding_list_attr_, value_list[0]);
      } else {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
      }
    }
    if (!output_item_list_attr_.empty()) {
      if (!context->SetIntListCommonAttr(output_item_list_attr_, std::move(ids))) {
        CL_LOG_ERROR("remote_embedding", "set_id_failed")
            << "set item ids failed, output_item_list_attr: " << output_item_list_attr_;
      }
    }
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value> SaveRawDataCommonAttr(
      MutableRecoContextInterface *context, const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
      std::function<void(const std::string &, std::vector<float> *)> scale_func = nullptr) {
    std::vector<int64> ids;
    std::vector<double> value_list;
    for (auto iter = sub_response.items().begin(); iter != sub_response.items().end(); iter++) {
      const std::string &ele = iter->second;
      if (ele.size() <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      auto id = id_conv_->ConvertId(sign);
      if (scale_func) {
        std::vector<float> result;
        scale_func(ele, &result);
        VLOG(1) << "sign: " << sign << ", dim: " << result.size();
        ids.push_back(id);
        for (int j = 0; j < result.size(); j++) {
          value_list.push_back(result[j]);
        }
      } else {
        size_t element_size = sizeof(T);
        int dim = ele.size() / element_size;
        VLOG(1) << "sign: " << sign << ", dim: " << dim;
        if (dim * element_size != ele.size()) {
          CL_LOG(ERROR) << "Unexpected size: " << ele.size() << " for sizeof(" << typeid(T).name()
                        << ") = " << element_size;
          continue;
        }
        auto *raw_data = reinterpret_cast<const T *>(ele.data());
        ids.push_back(id);
        for (int j = 0; j < dim; j++) {
          value_list.push_back(raw_data[j]);
        }
      }
    }

    if (ids.size() == 0 || value_list.size() == 0 || (value_list.size() % ids.size() > 0)) {
      CL_LOG_ERROR("remote_embedding", "size_fail")
          << "value_list size: " << value_list.size() << " mismatch ids size: " << ids.size();
      return;
    }
    if (is_raw_data_list_) {
      if (!context->SetDoubleListCommonAttr(output_embedding_list_attr_, std::move(value_list))) {
        CL_LOG_ERROR("remote_embedding", "set_embedding_failed")
            << "set value_list failed, output_embedding_list_attr: " << output_embedding_list_attr_;
      }
    } else {
      if (value_list.size() == 1) {
        context->SetDoubleCommonAttr(output_embedding_list_attr_, value_list[0]);
      } else {
        CL_LOG(WARNING) << "Unexpected value_list size: " << value_list.size();
      }
    }
    if (!output_item_list_attr_.empty()) {
      if (!context->SetIntListCommonAttr(output_item_list_attr_, std::move(ids))) {
        CL_LOG_ERROR("remote_embedding", "set_id_failed")
            << "set item ids failed, output_item_list_attr: " << output_item_list_attr_;
      }
    }
  }

  void SaveRawDataStringCommonAttr(MutableRecoContextInterface *context,
                                   const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response) {
    std::vector<int64> ids;
    std::vector<std::string> str_vals;
    ids.reserve(sub_response.items_size());
    str_vals.reserve(sub_response.items_size());
    for (auto iter = sub_response.items().begin(); iter != sub_response.items().end(); iter++) {
      const std::string &ele = iter->second;
      if (ele.size() <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      auto id = id_conv_->ConvertId(sign);
      VLOG(1) << "sign: " << sign << ", size: " << ele.size();
      ids.push_back(id);
      str_vals.push_back(ele);
    }

    if (ids.size() == 0 || str_vals.size() == 0 || str_vals.size() != ids.size()) {
      CL_LOG_ERROR("remote_embedding", "size_fail")
          << "str_vals size: " << str_vals.size() << " mismatch ids size: " << ids.size();
      return;
    }
    if (is_raw_data_list_) {
      if (!context->SetStringListCommonAttr(output_embedding_list_attr_, std::move(str_vals))) {
        CL_LOG_ERROR("remote_embedding", "set_embedding_failed")
            << "set str_vals failed, output_embedding_list_attr: " << output_embedding_list_attr_;
      }
    } else {
      if (str_vals.size() == 1) {
        context->SetStringCommonAttr(output_embedding_list_attr_, str_vals[0]);
      } else {
        CL_LOG(WARNING) << "Unexpected str_vals size: " << str_vals.size();
      }
    }
    if (!output_item_list_attr_.empty()) {
      if (!context->SetIntListCommonAttr(output_item_list_attr_, std::move(ids))) {
        CL_LOG_ERROR("remote_embedding", "set_id_failed")
            << "set item ids failed, output_item_list_attr: " << output_item_list_attr_;
      }
    }
  }

  void SaveEmbeddingCommonAttr(MutableRecoContextInterface *context,
                               const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response) {
    std::vector<int64> ids;
    std::vector<double> embeddings;
    for (auto iter = sub_response.items().begin(); iter != sub_response.items().end(); iter++) {
      const std::string &ele = iter->second;
      if (ele.size() <= 0) {
        CL_LOG_ERROR_EVERY("remote_embedding", "item_no_embedding", 100)
            << "item sign: " << iter->first << ", item's embedding empty";
        continue;
      }
      auto sign = iter->first;
      auto id = id_conv_->ConvertId(sign);
      int dim = ele.size() / sizeof(int16);
      VLOG(1) << "sign: " << sign << ", dim: " << dim;
      auto *weights = reinterpret_cast<const int16 *>(ele.data());
      ids.push_back(id);
      for (int j = 0; j < dim; j++) {
        embeddings.push_back(ks::reco::WeightToFloat(weights[j]));
      }
    }

    if (ids.size() == 0 || embeddings.size() == 0 || (embeddings.size() % ids.size() > 0)) {
      CL_LOG_ERROR("remote_embedding", "size_fail")
          << "embeddings size: " << embeddings.size() << " mismatch ids size: " << ids.size();
      return;
    }
    if (!context->SetDoubleListCommonAttr(output_embedding_list_attr_, std::move(embeddings))) {
      CL_LOG_ERROR("remote_embedding", "set_embedding_failed")
          << "set embeddings failed, output_embedding_list_attr: " << output_embedding_list_attr_;
    }
    if (!output_item_list_attr_.empty()) {
      if (!context->SetIntListCommonAttr(output_item_list_attr_, std::move(ids))) {
        CL_LOG_ERROR("remote_embedding", "set_id_failed")
            << "set item ids failed, output_item_list_attr: " << output_item_list_attr_;
      }
    }
  }

  // add sign to request, may send request immediately if max_signs_per_request_ > 0
  void AddSign(uint64_t sign, MutableRecoContextInterface *context, const std::string &kess_service) {
    if (client_side_shard_) {
      int shard = sign % shards_;
      requests_[shard].add_signs(sign);
      if (max_signs_per_request_ > 0 && requests_[shard].signs_size() >= max_signs_per_request_) {
        SendRequestToShard(context, requests_[shard], shard_prefix_ + base::IntToString(shard), kess_service);
        requests_[shard].Clear();
      }
    } else {
      requests_[0].add_signs(sign);
      if (max_signs_per_request_ > 0 && requests_[0].signs_size() >= max_signs_per_request_) {
        for (auto i = 0; i < shards_; i++) {
          SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
        }
        requests_[0].Clear();
      }
    }
  }

  void ClearRequests() {
    for (auto &request : requests_) {
      request.Clear();
    }
  }

  int GetShardNumFromKess(const std::string &service_name, const std::string &service_group = "PRODUCTION") {
    auto client = base::KessGrpcClient::Singleton()->GetClient2(service_name, service_group);
    if (!client->GetAllShards()) {
      CL_LOG_EVERY_N(ERROR, 100) << "no shards available, service: " << service_name
                                 << ", cluster: " << service_group;
      return 1;
    }

    int shard_num = client->GetAllShards()->size();
    if (shard_num == 0) {
      shard_num = 1;
    }
    return shard_num;
  }

  void SendRequests(MutableRecoContextInterface *context, const std::string &kess_service) {
    for (auto i = 0; i < shards_; i++) {
      if (client_side_shard_) {
        if (requests_[i].signs_size() > 0) {
          SendRequestToShard(context, requests_[i], shard_prefix_ + base::IntToString(i), kess_service);
        }
      } else {
        if (requests_[0].signs_size() > 0) {
          SendRequestToShard(context, requests_[0], shard_prefix_ + base::IntToString(i), kess_service);
        }
      }
    }
  }

  void SendColossusdbRequests(MutableRecoContextInterface *context, const std::string &kess_service);

  IdConverter *GetOrInsertConverter(const std::string &converter_type) {
    auto converter_it = converter_map_.find(converter_type);
    if (converter_it != converter_map_.end()) {
      CHECK(converter_it->second);
      return converter_it->second;
    }

    base::Json json_config(base::StringToJson(id_conv_config_->ToString()));
    json_config.set("type_name", converter_type.data());
    IdConverter *id_conv = base::JsonFactoryClass::New<IdConverter>(&json_config);
    if (!id_conv) {
      return nullptr;
    }
    converter_map_.insert({converter_type, id_conv});
    return id_conv;
  }

 private:
  std::string kess_cluster_;
  std::string colossusdb_embd_table_name_;
  int64 timeout_ms_ = 10;
  int shards_;
  std::string shard_prefix_;
  bool client_side_shard_;
  int max_signs_per_request_;

  uint64 slot_;
  std::string output_item_list_attr_ = "";
  std::string output_embedding_list_attr_ = "";
  std::string query_source_item_attr_ = "";
  IdConverter *id_conv_ = nullptr;
  bool save_to_common_attr_ = false;
  std::string output_attr_name_ = "";
  bool is_raw_data_ = false;
  bool is_raw_data_list_ = true;
  bool adaptive_shard_num_ = false;
  const base::Json *id_conv_config_ = nullptr;
  const base::Json *type_name_config_ = nullptr;
  enum class RawDataType {
    kSignedInt8,
    kSignedInt16,
    kSignedInt32,
    kSignedInt64,
    kUnsignedInt8,
    kUnsignedInt16,
    kUnsignedInt32,
    kUnsignedInt64,
    kFloat32,
    kString,
    kScaledInt8,
  } raw_data_type_;
  enum class QuerySourceType {
    kItemKey,
    kItemId,
    kItemAttr,
    kUserId,
    kDeviceId,
    kUserIdAndDeviceId,
  } query_source_type_;

  std::unordered_map<std::string, std::unique_ptr<colossusdb::EmbeddingClient>> colossusdb_embd_client_map_;

  CommonRecoObjectPool<ks::reco::bt_embd_s::BatchEmbeddingsResponse> response_pool_;

  std::vector<ks::reco::bt_embd_s::BatchEmbeddingsRequest> requests_;

  std::unordered_map<uint64_t, std::unordered_set<uint64_t>> mapping_sign_to_item_keys_;

  folly::F14FastMap<std::string, IdConverter *> converter_map_;

  serving_base::Timer timer_;

  std::shared_ptr<int> exit_handler_;

  void FillItemAttr(MutableRecoContextInterface *context,
                    const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                    const std::string &shard_name);

  void FillCommonAttr(MutableRecoContextInterface *context,
                      const ks::reco::bt_embd_s::BatchEmbeddingsResponse &sub_response,
                      const std::string &shard_name);

  void SendRequestToShard(MutableRecoContextInterface *context,
                          const ks::reco::bt_embd_s::BatchEmbeddingsRequest &request, std::string shard_name,
                          const std::string &kess_service);

  void SendColossusdbRequest(MutableRecoContextInterface *context, const std::string &kess_service);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRemoteEmbeddingAttrEnricher);
};
}  // namespace platform
}  // namespace ks
