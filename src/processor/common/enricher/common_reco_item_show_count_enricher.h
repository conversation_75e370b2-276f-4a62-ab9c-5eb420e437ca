#pragma once

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include <string>

namespace ks {
namespace platform {

class CommonRecoItemShowCountEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemShowCountEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    auto *show_item_attr_config = config()->Get("show_item_attr");
    if (!show_item_attr_config || !show_item_attr_config->IsString()) {
      LOG(ERROR) << "CommonRecoItemShowCountEnricher Init failed, show_item_attr should be a string";
      return false;
    }
    show_item_attr_config->StringValue(&show_item_attr_);

    auto *common_attr_config = config()->Get("common_attr");
    if (!common_attr_config || !common_attr_config->IsString()) {
      LOG(ERROR) << "CommonRecoItemShowCountEnricher Init failed, common_attr should be a string";
      return false;
    }
    common_attr_config->StringValue(&common_attr_);
    return true;
  }

  std::string show_item_attr_;
  std::string common_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemShowCountEnricher);
};

}  // namespace platform
}  // namespace ks
