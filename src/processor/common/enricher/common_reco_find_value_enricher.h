#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

class CommonRecoFindValueEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoFindValueEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    input_ = config()->Get("input");
    if (!input_) {
      LOG(ERROR) << "CommonRecoFindValueEnricher init failed! Missing 'input' config.";
      return false;
    }
    value_ = config()->Get("value");
    if (!value_) {
      LOG(ERROR) << "CommonRecoFindValueEnricher init failed! Missing 'value' config.";
      return false;
    }
    result_ = config()->GetString("result");
    if (result_.empty()) {
      LOG(ERROR) << "CommonRecoFindValueEnricher init failed! Missing 'result' config.";
      return false;
    }
    count_ = config()->GetBoolean("count", false);
    output_index_attr_ = config()->GetString("output_index");
    return true;
  }

 private:
  const base::Json *input_ = nullptr;
  const base::Json *value_ = nullptr;
  std::string result_;
  bool count_ = false;
  std::string output_index_attr_;
  CommonAttr *output_index_attr_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoFindValueEnricher);
};

}  // namespace platform
}  // namespace ks
