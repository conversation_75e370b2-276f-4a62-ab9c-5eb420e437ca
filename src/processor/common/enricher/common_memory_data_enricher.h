#pragma once

#include <memory>
#include <string>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "ks/reco/memory_data_map/util.h"

namespace ks {
namespace platform {

class CommonMemoryDataEnricher : public CommonRecoBaseEnricher {
 public:
  CommonMemoryDataEnricher() {}

  bool IsAsync() const override {
    return false;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    {
      base::AutoLock lock(lock_);
      if (!ks::reco::MemoryDataMapSingleton::Singleton()->Init()) {
        LOG(ERROR) << "CommonMemoryDataEnricher: MemoryDataMap init failed!";
        return false;
      }
    }
    LOG(INFO) << "CommonMemoryDataEnricher: MemoryDataMap initialized.";

    data_type_ = config()->GetString("data_type", "");
    if (data_type_.empty()) {
      LOG(ERROR) << "CommonMemoryDataEnricher: empty data_type!";
      return false;
    }

    save_data_ptr_to_attr_ = config()->GetString("save_data_ptr_to_attr", "");
    if (save_data_ptr_to_attr_.empty()) {
      LOG(ERROR) << "CommonMemoryDataEnricher: empty save_data_ptr_to_attr!";
      return false;
    }

    return true;
  }

  template <typename T>
  bool GetDataAndSave(MutableRecoContextInterface *context) {
    std::shared_ptr<const T> t;
    if (ks::reco::MemoryDataMapSingleton::Singleton()->GetMemoryDataMap().GetData(data_key_, t)) {
      context->SetPtrCommonAttr(save_data_ptr_to_attr_, std::move(t));
      return true;
    }

    return false;
  }

  enum class DataType {
    STRING_STRING_MAP,
    STRING_UINT64_MAP,
    STRING_INT32_MAP,
    STRING_DOUBLE_MAP,
    INT64_INT64_MAP,
    INT32_INT32_MAP,
    INT64_INT32_MAP,
    UINT64_STRING_MAP,
    UINT64_UINT64_MAP,
    UINT64_DOUBLE_MAP,
    STRING_UINT64_UINT64_PAIR_MAP,
    STRING_UINT64_VECTOR_MAP,
    UINT64_UINT64_VECTOR_MAP,
    INT32_INT32_VECTOR_MAP,
    UINT64_STRING_VECTOR_MAP,
    STRING_UINT64_DOUBLE_PAIR_VECTOR_MAP,
    STRING_UINT64_UINT64_PAIR_VECTOR_MAP,
    UINT64_UINT64_DOUBLE_PAIR_VECTOR_MAP,
    STRING_STRING_VECTOR_MAP,
    STRING_DOUBLE_VECTOR_MAP,
    UINT64_DOUBLE_VECTOR_MAP,
    INT32_UINT64_VECTOR_MAP,
    STRING_UINT64_SET_MAP,  // folly::F14FastMap<int32, folly::F14FastSet<uint64>>>
    UINT64_UINT64_SET_MAP,
    STRING_UINT64_DOUBLE_MAP_MAP,
    UINT64_UINT64_DOUBLE_MAP_MAP,

    // std::vector
    UINT64_VECTOR,
    STRING_VECTOR,
    DOUBLE_VECTOR,
    UINT64_DOUBLE_PAIR_VECTOR,

    STRING_VECTOR_VECTOR,
    UINT64_VECTOR_VECTOR,

    // folly::F14FastSet
    UINT64_SET,
    INT32_SET,
    STRING_SET,
    DOUBLE_SET,

    //基础类型
    BASE_UINT64,
    BASE_INT32,
    BASE_DOUBLE,
    BASE_STRING,

    // 禁止有 Set<Map> Set<Set> Set<Vector> Vector<Map> Vector<Set> 这种数据类型！！！
  };

  static folly::F14FastMap<std::string, DataType> DATA_TYPE_MAP;

  std::string data_key_;
  std::string data_type_;
  std::string save_data_ptr_to_attr_;

  static base::Lock lock_;

  DISALLOW_COPY_AND_ASSIGN(CommonMemoryDataEnricher);
};

}  // namespace platform
}  // namespace ks
