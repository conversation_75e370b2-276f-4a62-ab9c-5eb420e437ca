//
// Created by ya<PERSON><PERSON><PERSON> on 2020/10/10.
//

#pragma once

#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "ks/common_reco/ann_retrieve/server/common_reco_ann_retrieve.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "learning/kuiba/predict_base/common_predict_client.h"

namespace ks {
namespace platform {

class CommonRecoLocalAnnEmbeddingAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoLocalAnnEmbeddingAttrEnricher() {}

  std::function<void()> Purge() override {
    return []() {
      if (CommonRecoAnnRetrieve::Singleton()->HasAnnLoaded()) {
        CommonRecoAnnRetrieve::Singleton()->StopAndWait();
        LOG(INFO) << "CommonAnnRetrieve stopped.";
        ::google::FlushLogFiles(::google::INFO);
      }
    };
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    item_type_ = config()->GetInt("item_type", 0);
    dim_ = config()->GetInt("dim", 0);
    if (dim_ <= 0) {
      LOG(ERROR) << "CommonRecoLocalAnnEmbeddingEnricher init failed! dim : " << dim_ << " must > 0";
      return false;
    }

    save_to_common_attr_ = config()->GetBoolean("save_to_common_attr", false);
    if (save_to_common_attr_) {
      item_list_output_attr_ = config()->GetString("item_list_output_attr", "");
      if (item_list_output_attr_.empty()) {
        LOG(ERROR) << "CommonRecoLocalAnnEmbeddingEnricher init failed! item_list_output_attr empty";
        return false;
      }
      embedding_list_output_attr_ = config()->GetString("embedding_list_output_attr", "");
      if (embedding_list_output_attr_.empty()) {
        LOG(ERROR) << "CommonRecoLocalAnnEmbeddingEnricher init failed! embedding_list_output_attr empty";
        return false;
      }
      data_type_list_output_attr_ = config()->GetString("data_type_list_output_attr", "");
    } else {
      data_type_item_attr_ = config()->GetString("data_type_item_attr", "");
      embedding_item_attr_ = config()->GetString("embedding_item_attr", "");
      if (embedding_item_attr_.empty()) {
        LOG(ERROR) << "CommonRecoLocalAnnEmbeddingEnricher init failed! embedding_item_attr empty";
        return false;
      }
    }

    save_as_ptr_ = config()->GetBoolean("save_as_ptr", false);
    auto root_config = GlobalHolder::GetDynamicJsonConfig();
    auto local_ann_conf = root_config->Get("ann_config");
    if (nullptr == local_ann_conf) {
      LOG(ERROR) << "CommonRecoLocalAnnEmbeddingAttrEnricher init failed!"
                 << "\"ann_config\" is empty!";
      return false;
    }
    static std::once_flag load_ann_once_flag;
    std::call_once(load_ann_once_flag, [&, local_ann_conf]() {
      LOG(INFO) << "load ann in processor";
      ks::platform::CommonRecoAnnRetrieve::Singleton()->LoadAnn(
          local_ann_conf, ks::platform::GlobalHolder::GetServiceShardNo(),
          ks::platform::GlobalHolder::GetServiceShardNum());
      ks::platform::CommonRecoAnnRetrieve::Singleton()->Run();
    });
    if (!CommonRecoAnnRetrieve::Singleton()->HasAnnLoaded()) {
      LOG(ERROR) << "CommonRecoLocalAnnRetriever init failed!"
                 << " Ann is not loaded!";
      return false;
    }
    CommonRecoAnnRetrieve::Singleton()->WaitAvailable();

    return true;
  }

 private:
  int item_type_;
  int dim_;

  bool save_to_common_attr_;
  std::string item_list_output_attr_;
  std::string embedding_list_output_attr_;
  std::string data_type_list_output_attr_;

  std::string data_type_item_attr_;
  std::string embedding_item_attr_;

  bool save_as_ptr_ = false;

  std::string src_data_type_;

  serving_base::Timer timer_;

  std::vector<const float *> kv_result_;

  void FillItemAttr(MutableRecoContextInterface *context, int dim, RecoResultConstIter begin,
                    RecoResultConstIter end) const;

  void FillCommonAttr(MutableRecoContextInterface *context, int dim, RecoResultConstIter begin,
                      RecoResultConstIter end) const;

  void SendRequest(MutableRecoContextInterface *context, const std::vector<uint64> &nids,
                   RecoResultConstIter begin, RecoResultConstIter end);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLocalAnnEmbeddingAttrEnricher);
};

}  // namespace platform
}  // namespace ks
