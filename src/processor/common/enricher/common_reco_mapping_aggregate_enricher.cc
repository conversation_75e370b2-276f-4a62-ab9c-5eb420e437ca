#include "dragon/src/processor/common/enricher/common_reco_mapping_aggregate_enricher.h"

namespace ks {
namespace platform {

void CommonRecoMappingAggregateEnricher::Enrich(ks::platform::MutableRecoContextInterface *context,
                                                ks::platform::RecoResultConstIter begin,
                                                ks::platform::RecoResultConstIter end) {
  // 截断为所有 int_list_attr 最短的一个
  int list_length = INT32_MAX;
  for (auto attr_function : attr_functions_) {
    if (auto p = context->GetIntListCommonAttr(attr_function.attr_name)) {
      if (p->size() < list_length) {
        list_length = p->size();
      }
    } else {
      list_length = 0;
      CL_LOG(ERROR) << "Int list common attr " << attr_function.attr_name
                 << " not found! CrossAttrAnalysis skip.";
      return;
    }
  }

  auto common_key_ptr = context->GetIntListCommonAttr(key_list_attr_);
  if (!common_key_ptr) {
    CL_LOG(ERROR) << "Common key attr " << key_list_attr_ << " not found! CrossAttrAnalysis skip.";
    return;
  }

  // 按 common key 维度计算并缓存所有结果
  std::unordered_map<int64, std::vector<int64>> analysis_result_map;
  std::unordered_map<int64, int> key_attr_count_map;
  list_length = std::min((int)common_key_ptr->size(), list_length);
  std::unordered_set<int64> common_key_set;
  for (int i = 0; i < list_length; i++) {
    int64 common_key = common_key_ptr->at(i);
    if (common_key_set.find(common_key) == common_key_set.end()) {
      std::vector<int64> analysis_result;
      analysis_result.reserve(attr_functions_.size());
      analysis_result_map[common_key] = analysis_result;
      key_attr_count_map[common_key] = 1;
      common_key_set.insert(common_key);
    } else {
      key_attr_count_map[common_key]++;
    }
  }

  for (auto attr_function : attr_functions_) {
    auto common_attr_ptr = context->GetIntListCommonAttr(attr_function.attr_name);
    for (auto common_key : common_key_set) {
      int64 function_result = (attr_function.function == Function::min)
                                  ? INT64_MAX
                                  : ((attr_function.function == Function::max) ? INT64_MIN : 0);
      for (int i = 0; i < list_length; i++) {
        if (common_key == common_key_ptr->at(i)) {
          switch (attr_function.function) {
            case Function::max: {
              if (common_attr_ptr->at(i) > function_result) {
                function_result = common_attr_ptr->at(i);
              }
              break;
            }
            case Function::min: {
              if (common_attr_ptr->at(i) < function_result) {
                function_result = common_attr_ptr->at(i);
              }
              break;
            }
            case Function::sum: {
              function_result += common_attr_ptr->at(i);
              break;
            }
            default:
              break;
          }
        }
      }
      analysis_result_map[common_key].push_back(function_result);
    }
  }

  ItemAttr *match_key_attr_accessor =
      match_key_in_item_attr_.empty() ? nullptr : context->GetItemAttrAccessor(match_key_in_item_attr_);
  ItemAttr *count_attr_accessor = context->GetItemAttrAccessor(save_count_to_attr_);
  std::vector<ItemAttr *> export_attr_accessor_list;
  for (auto attr_function : attr_functions_) {
    export_attr_accessor_list.push_back(context->GetItemAttrAccessor(attr_function.export_item_attr_name));
  }

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    int64 match_key = result.item_key;
    if (match_key_attr_accessor) {
      auto match_key_p = result.GetIntAttr(match_key_attr_accessor);
      if (!match_key_p) {
        return;
      }
      match_key = *match_key_p;
    }

    if (!save_count_to_attr_.empty()) {
      const auto it = key_attr_count_map.find(match_key);
      if (save_for_match_key_) {
        context->SetIntItemAttr(match_key, count_attr_accessor,
                                it != key_attr_count_map.end() ? it->second : 0);
      } else {
        result.SetIntAttr(count_attr_accessor, it != key_attr_count_map.end() ? it->second : 0);
      }
    }

    const auto it = analysis_result_map.find(match_key);
    if (it != analysis_result_map.end()) {
      const std::vector<int64> &result_list = it->second;
      for (int i = 0; i < export_attr_accessor_list.size(); i++) {
        if (save_for_match_key_) {
          context->SetIntItemAttr(match_key, export_attr_accessor_list.at(i), result_list.at(i));
        } else {
          result.SetIntAttr(export_attr_accessor_list.at(i), result_list.at(i));
        }
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;

FACTORY_REGISTER(JsonFactoryClass, CommonRecoMappingAggregateEnricher, CommonRecoMappingAggregateEnricher)
}  // namespace platform
}  // namespace ks
