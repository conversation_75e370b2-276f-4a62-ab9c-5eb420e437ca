#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"

namespace ks {
namespace platform {
class CommonRecoBuildResponseEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoBuildResponseEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    item_num_config_ = config()->Get("item_num");
    to_attr_ = config()->GetString("to_attr", "");
    if (to_attr_.empty()) {
      CL_LOG_ERROR("to_attr_empty", "build_response_init_error")
          << "CommonRecoBuildResponseEnricher init failed! \"to_attr\" cannot be empty!";
      return false;
    }
    as_string_ = config()->GetBoolean("as_string", false);
    include_return_attrs_in_request_ = config()->GetBoolean("include_return_attrs_in_request", false);
    auto *common_attrs = config()->Get("common_attrs");
    RecoUtil::ExtractStringListFromJsonConfig(common_attrs, &dump_common_attrs_);
    auto *item_attrs = config()->Get("item_attrs");
    RecoUtil::ExtractStringListFromJsonConfig(item_attrs, &dump_item_attrs_);
    return true;
  }

  void BuildRecoResponse(MutableRecoContextInterface *context, RecoResultConstIter begin,
                         RecoResultConstIter end);

 private:
  std::vector<std::string> dump_common_attrs_;
  std::vector<std::string> dump_item_attrs_;
  const base::Json *item_num_config_ = nullptr;
  std::string to_attr_;
  bool include_return_attrs_in_request_ = false;
  bool as_string_ = false;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBuildResponseEnricher);
};
}  // namespace platform
}  // namespace ks
