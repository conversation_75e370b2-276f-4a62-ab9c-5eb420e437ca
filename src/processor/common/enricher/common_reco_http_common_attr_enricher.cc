#include <curl/curl.h>
#include <algorithm>
#include <map>
#include <string>
#include <utility>

#include "dragon/src/processor/common/enricher/common_reco_http_common_attr_enricher.h"

namespace ks {
namespace platform {

bool BodyJson::InitBodyJson(const base::Json *json) {
  if (!json) {
    return false;
  }

  if (json->IsArray()) {
    this->is_array_ = true;
    for (const auto *json_child : json->array()) {
      if (!json_child || (!json_child->IsArray() && !json_child->IsObject())) {
        LOG(ERROR) << "json array config invalid";
        return false;
      }
      BodyJson child;
      if (!child.InitBodyJson(json_child)) {
        return false;
      }
      this->childrens_array_.emplace_back(std::move(child));
    }
  } else if (json->IsObject()) {
    for (auto iter = json->object_begin(); iter != json->object_end(); ++iter) {
      std::string name = iter->first;
      if (name.empty()) {
        LOG(ERROR) << "json object config invalid: empty name";
        return false;
      }
      auto value = iter->second;
      if (!value) {
        LOG(ERROR) << "json object config invalid: " << name;
        return false;
      }
      if (value->IsString()) {
        this->value_map_.emplace(std::move(name), std::move(value));
      } else if (value->IsArray() || value->IsObject()) {
        BodyJson child;
        if (!child.InitBodyJson(value)) {
          LOG(ERROR) << "json object config invalid: " << name;
          return false;
        }
        childrens_map_.emplace(std::move(name), std::move(child));
      } else {
        LOG(ERROR) << "json object config invalid: " << name;
        return false;
      }
    }
  } else {
    LOG(ERROR) << "json config invalid: not array or object";
    return false;
  }
  return true;
}

Json::Value CommonRecoHttpCommonAttrEnricher::serialize(MutableRecoContextInterface *context,
                                                        const BodyJson &body) {
  if (!context) {
    return Json::Value();
  }
  if (body.is_array_) {
    Json::Value json_list(Json::arrayValue);
    for (const auto &child : body.childrens_array_) {
      json_list.append(serialize(context, child));
    }
    return json_list;
  }
  Json::Value json;
  for (const auto &child : body.childrens_map_) {
    json[child.first] = serialize(context, child.second);
  }
  for (const auto &child : body.value_map_) {
    std::string value = GetStringProcessorParameter(context, child.second, "");
    if (value.empty()) {
      CL_LOG_WARNING_EVERY("https_common_attr_enricher", "body_value_empty", 100)
          << "body value empty:" << child.first;
    } else {
      json[child.first] = value;
    }
  }
  return json;
}

size_t WriteCallback(void *data, size_t size, size_t nmemb, void *userdata) {
  if (userdata == nullptr || data == nullptr) {
    return 0;
  }
  CommonRecoHttpCommonAttrEnricher::Response *r;
  r = reinterpret_cast<CommonRecoHttpCommonAttrEnricher::Response *>(userdata);
  r->body.append(reinterpret_cast<char *>(data), size * nmemb);
  return (size * nmemb);
}

static inline std::string *ltrim(std::string *s) {
  if (s == nullptr) {
    return s;
  }
  s->erase(s->begin(), std::find_if(s->begin(), s->end(), [](int c) { return !std::isspace(c); }));
  return s;
}

static inline std::string *rtrim(std::string *s) {
  if (s == nullptr) {
    return s;
  }
  s->erase(std::find_if(s->rbegin(), s->rend(), [](int c) { return !std::isspace(c); }).base(), s->end());
  return s;
}

static inline void trim(std::string *s) {
  ltrim(rtrim(s));
  return;
}

size_t header_callback(void *data, size_t size, size_t nmemb, void *userdata) {
  if (userdata == nullptr || data == nullptr) {
    return 0;
  }
  CommonRecoHttpCommonAttrEnricher::Response *r =
      reinterpret_cast<CommonRecoHttpCommonAttrEnricher::Response *>(userdata);
  std::string header(reinterpret_cast<char *>(data), size * nmemb);
  size_t separator = header.find_first_of(':');
  if (separator == std::string::npos) {
    trim(&header);
    if (!header.empty()) {
      r->headers[header] = "present";
    }
  } else {
    std::string key = header.substr(0, separator);
    trim(&key);
    std::string value = header.substr(separator + 1);
    trim(&value);
    r->headers[key] = value;
  }
  return size * nmemb;
}

CommonRecoHttpCommonAttrEnricher::HTTPClient::HTTPClient(const std::string &baseUrl, bool keep_alive,
                                                         bool forbid_reuse) {
  curlHandle_ = curl_easy_init();
  if (!curlHandle_) {
    CL_LOG_ERROR_EVERY("https_common_attr_enricher", "curl_init_fail", 100)
        << "Couldn't initialize curl handle";
  }
  baseUrl_ = baseUrl;
  timeout_ms_ = 0;
  keep_alive_ = keep_alive;
  forbid_reuse_ = forbid_reuse;
  headerFields_.clear();
}

CommonRecoHttpCommonAttrEnricher::HTTPClient::~HTTPClient() {
  if (curlHandle_) {
    curl_easy_cleanup(curlHandle_);
  }
}

void CommonRecoHttpCommonAttrEnricher::HTTPClient::SetHeaders(const std::string &key,
                                                              const std::string &value) {
  headerFields_.emplace(key, value);
}

void CommonRecoHttpCommonAttrEnricher::HTTPClient::SetTimeout(int timeout_ms) {
  timeout_ms_ = timeout_ms;
}

CommonRecoHttpCommonAttrEnricher::Response CommonRecoHttpCommonAttrEnricher::HTTPClient::get(
    const std::string &path) {
  return PerformCurlRequest(path);
}

bool CommonRecoHttpCommonAttrEnricher::HTTPClient::CheckCurlHandle() {
  return curlHandle_ != nullptr;
}

CommonRecoHttpCommonAttrEnricher::Response CommonRecoHttpCommonAttrEnricher::HTTPClient::post(
    const std::string &path, const std::string &data) {
  curl_easy_setopt(curlHandle_, CURLOPT_POST, 1L);
  curl_easy_setopt(curlHandle_, CURLOPT_POSTFIELDS, data.c_str());
  curl_easy_setopt(curlHandle_, CURLOPT_POSTFIELDSIZE, data.size());
  return PerformCurlRequest(path);
}

CommonRecoHttpCommonAttrEnricher::Response CommonRecoHttpCommonAttrEnricher::HTTPClient::PerformCurlRequest(
    const std::string &path) {
  Response ret = {};
  ret.code = 0;
  ret.body.clear();
  ret.headers.clear();

  std::string url = std::string(baseUrl_ + path);
  std::string headerString;
  CURLcode res = CURLE_OK;
  curl_slist *headerList = NULL;

  curl_easy_setopt(curlHandle_, CURLOPT_URL, url.c_str());

  curl_easy_setopt(curlHandle_, CURLOPT_WRITEFUNCTION, WriteCallback);

  curl_easy_setopt(curlHandle_, CURLOPT_WRITEDATA, &ret);

  curl_easy_setopt(curlHandle_, CURLOPT_HEADERFUNCTION, header_callback);

  curl_easy_setopt(curlHandle_, CURLOPT_HEADERDATA, &ret);

  for (CommonRecoHttpCommonAttrEnricher::HeaderFields::const_iterator it = headerFields_.begin();
       it != headerFields_.end(); ++it) {
    headerString = it->first;
    headerString += ": ";
    headerString += it->second;
    headerList = curl_slist_append(headerList, headerString.c_str());
  }
  curl_easy_setopt(curlHandle_, CURLOPT_HTTPHEADER, headerList);

  if (timeout_ms_) {
    curl_easy_setopt(curlHandle_, CURLOPT_TIMEOUT_MS, timeout_ms_);
  }

  curl_easy_setopt(curlHandle_, CURLOPT_NOSIGNAL, 1L);

  if (keep_alive_) {
    curl_easy_setopt(curlHandle_, CURLOPT_TCP_KEEPALIVE, 1L);
  } else {
    curl_easy_setopt(curlHandle_, CURLOPT_TCP_KEEPALIVE, 0L);
  }

  if (forbid_reuse_) {
    curl_easy_setopt(curlHandle_, CURLOPT_FORBID_REUSE, 1L);
  } else {
    curl_easy_setopt(curlHandle_, CURLOPT_FORBID_REUSE, 0L);
  }

  res = curl_easy_perform(curlHandle_);
  if (res != CURLE_OK) {
    switch (res) {
      case CURLE_OPERATION_TIMEDOUT:
        ret.code = res;
        ret.body = "Operation Timeout.";
        break;
      case CURLE_SSL_CERTPROBLEM:
        ret.code = res;
        ret.body = curl_easy_strerror(res);
        break;
      default:
        ret.body = curl_easy_strerror(res);
        ret.code = res;
    }
  } else {
    int64_t http_code = 0;
    curl_easy_getinfo(curlHandle_, CURLINFO_RESPONSE_CODE, &http_code);
    ret.code = static_cast<int>(http_code);
  }

  curl_slist_free_all(headerList);
  return ret;
}

void CommonRecoHttpCommonAttrEnricher::HTTPClient::ClearHeaders() {
  this->headerFields_.clear();
}

void CommonRecoHttpCommonAttrEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  if (!client_) {
    CL_LOG_ERROR_EVERY("https_common_attr_enricher", "client_not_init", 100)
        << "client not init";
    return;
  }
  timeout_ms_ = GetIntProcessorParameter(context, "timeout_ms", 100);
  client_->SetTimeout(timeout_ms_);
  for (auto &http_header : http_headers_) {
    std::string value = GetStringProcessorParameter(context, http_header.value_config, "");
    if (value.empty()) {
      CL_LOG_ERROR_EVERY("https_common_attr_enricher", "header_value_empty", 100)
          << "header value empty:" << http_header.name;
      return;
    }
    client_->SetHeaders(http_header.name, value);
  }
  client_->SetHeaders("Content-Type", "application/json");

  if (method_ == "GET") {
    CommonRecoHttpCommonAttrEnricher::Response res = client_->get(path_);
    context->SetStringCommonAttr(output_common_attr_, std::move(res.body));
  } else if (method_ == "POST") {
    ::Json::FastWriter fw;
    ::Json::Value root = serialize(context, http_body_);
    std::string body = fw.write(root);
    CommonRecoHttpCommonAttrEnricher::Response res = client_->post(path_, body);
    context->SetStringCommonAttr(output_common_attr_, std::move(res.body));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoHttpCommonAttrEnricher, CommonRecoHttpCommonAttrEnricher)

}  // namespace platform
}  // namespace ks
