#include "dragon/src/processor/common/enricher/common_reco_reverse_list_attr_enricher.h"
#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {
void CommonRecoReverseListAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                 RecoResultConstIter begin, RecoResultConstIter end) {
  if (!common_attrs_.empty()) {
    for (const std::string attr_name : common_attrs_) {
      ProcessCommonAttr(context, attr_name);
    }
  }

  if (!item_attrs_.empty()) {
    for (const std::string attr_name : item_attrs_) {
      ProcessItemAttr(context, begin, end, attr_name);
    }
  }
}
void CommonRecoReverseListAttrEnricher::ProcessCommonAttr(MutableRecoContextInterface *context,
                                                            std::string attr_name) {
  auto *accessor = context->GetCommonAttrAccessor(attr_name);
  switch (accessor->value_type) {
    case AttrType::INT_LIST:
      if (auto int_list_values = context->GetIntListCommonAttr(accessor)) {
        std::vector<int64> values(int_list_values->begin(), int_list_values->end());
        std::reverse(values.begin(), values.end());
        context->SetIntListCommonAttr(accessor, std::move(values));
      }
      break;
    case AttrType::FLOAT_LIST: {
      if (auto double_list_values = context->GetDoubleListCommonAttr(accessor)) {
        std::vector<double> values(double_list_values->begin(), double_list_values->end());
        std::reverse(values.begin(), values.end());
        context->SetDoubleListCommonAttr(accessor, std::move(values));
      }
      break;
    }
    case AttrType::STRING_LIST: {
      if (auto string_list_values = context->GetStringListCommonAttr(accessor)) {
        std::vector<std::string> values;
        values.reserve(string_list_values->size());
        for (auto sv : *string_list_values) {
          values.emplace_back(sv.data(), sv.size());
        }
        std::reverse(values.begin(), values.end());
        context->SetStringListCommonAttr(accessor, std::move(values));
      }
      break;
    }
    case AttrType::UNKNOWN: {
      CL_LOG(INFO) << "common_attr: " << attr_name
                   << " is empty, reverse common_attr cancelled, processor: " << GetName();
      break;
    }
    default: {
      const auto &attr_type = RecoUtil::GetAttrTypeName(accessor->value_type);
      CL_LOG_ERROR("reverse_list_attr", "not_a_list:" + attr_type)
          << "common_attr: " << attr_name << " is not a list, type: " << attr_type;
      break;
    }
  }
}

void CommonRecoReverseListAttrEnricher::ProcessItemAttr(MutableRecoContextInterface *context,
                                                          RecoResultConstIter begin, RecoResultConstIter end,
                                                          std::string attr_name) {
  auto *accessor = context->GetItemAttrAccessor(attr_name);
  switch (accessor->value_type) {
    case AttrType::INT_LIST:
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto int_list_values = context->GetIntListItemAttr(result, accessor)) {
          std::vector<int64> values(int_list_values->begin(), int_list_values->end());
          std::reverse(values.begin(), values.end());
          context->SetIntListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    case AttrType::FLOAT_LIST: {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto double_list_values = context->GetDoubleListItemAttr(result, accessor)) {
          std::vector<double> values(double_list_values->begin(), double_list_values->end());
          std::reverse(values.begin(), values.end());
          context->SetDoubleListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    }
    case AttrType::STRING_LIST: {
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto string_list_values = context->GetStringListItemAttr(result, accessor)) {
          std::vector<std::string> values;
          values.reserve(string_list_values->size());
          for (auto sv : *string_list_values) {
            values.emplace_back(sv.data(), sv.size());
          }
          std::reverse(values.begin(), values.end());
          context->SetStringListItemAttr(result, accessor, std::move(values));
        }
      });
      break;
    }
    case AttrType::UNKNOWN: {
      CL_LOG(INFO) << "item_attr: " << attr_name
                   << " is empty, reverse item_attr cancelled, processor: " << GetName();
      break;
    }
    default: {
      const auto &attr_type = RecoUtil::GetAttrTypeName(accessor->value_type);
      CL_LOG_ERROR("reverse_list_attr", "not_a_list:" + attr_type)
          << "item_attr: " << attr_name << " is not a list, type: " << attr_type;
      break;
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoReverseListAttrEnricher, CommonRecoReverseListAttrEnricher);
FACTORY_REGISTER(JsonFactoryClass, MerchantRecoReverseListAttrEnricher, CommonRecoReverseListAttrEnricher);


}  // namespace platform
}  // namespace ks
