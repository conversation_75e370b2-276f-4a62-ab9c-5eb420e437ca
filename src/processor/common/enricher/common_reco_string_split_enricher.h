#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoStringSplitEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoStringSplitEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

  void ProcessCommonAttr(MutableRecoContextInterface *context);
  void ProcessItemAttr(MutableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end);

  std::vector<absl::string_view> StringSplitByChar(absl::string_view input_str);
  std::vector<absl::string_view> StringSplit(absl::string_view input_str);

 private:
  bool InitProcessor() override {
    is_common_attr_ = !config()->GetString("input_common_attr", "").empty();
    input_attr_ =
        is_common_attr_ ? config()->GetString("input_common_attr") : config()->GetString("input_item_attr");
    if (input_attr_.empty()) {
      LOG(ERROR) << "CommonRecoStringSplitEnricher init failed! input_attr is required";
      return false;
    }

    output_attr_ =
        is_common_attr_ ? config()->GetString("output_common_attr") : config()->GetString("output_item_attr");
    if (output_attr_.empty()) {
      LOG(ERROR) << "CommonRecoStringSplitEnricher init failed! output_attr is required";
      return false;
    }

    bool has_delimiters = config()->GetString("delimiters", &delimiters_);
    if (!has_delimiters) {
      LOG(ERROR) << "CommonRecoStringSplitEnricher init failed!"
                 << " Missing \"delimiters\" or it is not a string.";
      return false;
    }

    trim_spaces_ = config()->GetBoolean("trim_spaces", false);
    skip_empty_tokens_ = config()->GetBoolean("skip_empty_tokens", false);
    strip_whitespaces_ = config()->GetBoolean("strip_whitespaces", false);
    parse_to_int_ = config()->GetBoolean("parse_to_int", false);
    parse_to_double_ = config()->GetBoolean("parse_to_double", false);
    max_splits_ = config()->GetInt("max_splits", -1);

    return true;
  }

 private:
  std::string input_attr_;
  std::string output_attr_;
  bool is_common_attr_ = true;

  std::string delimiters_;
  bool trim_spaces_ = false;
  bool skip_empty_tokens_ = false;
  bool strip_whitespaces_ = false;
  bool parse_to_int_ = false;
  bool parse_to_double_ = false;
  int max_splits_ = -1;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStringSplitEnricher);
};

}  // namespace platform
}  // namespace ks
