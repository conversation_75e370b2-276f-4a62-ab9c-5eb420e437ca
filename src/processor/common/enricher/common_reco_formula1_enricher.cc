#include "dragon/src/processor/common/enricher/common_reco_formula1_enricher.h"
#include <algorithm>
#include "dragon/src/core/common_reco_define.h"
namespace ks {
namespace platform {

void CommonRecoFormulaOneEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  if (export_attrs_.empty()) {
    CL_LOG_EVERY_N(WARNING, 1000)
        << "Formula1 canceled calculation due to no configured export formula value";
    return;
  }

  if (!init_accessors_) {
    for (auto &config : import_common_attrs_) {
      config.accessor = context->GetCommonAttrAccessor(config.name);
    }

    for (auto &config : import_item_attrs_) {
      config.accessor = context->GetItemAttrAccessor(config.name);
    }

    for (auto &config : export_attrs_) {
      if (config.to_common) {
        config.accessor = context->GetCommonAttrAccessor(config.as);
      } else {
        config.accessor = context->GetItemAttrAccessor(config.as);
      }
    }
    init_accessors_ = true;
  }

  std::vector<absl::string_view> prioritized_suffix =
      GetStringListProcessorParameter(context, prioritized_suffix_);

  formula1_->Reset();

  bool enable_pruning_opt = GetBoolProcessorParameter(context, enable_pruning_opt_);
  if (enable_pruning_opt) {
    for (auto &config : export_attrs_) {
      formula1_->AddOutputAttr(config.name);
    }
  }

  for (const auto &common_config : import_common_attrs_) {
    ImportCommonAttr(context, common_config);
  }
  for (const auto &item_config : import_item_attrs_) {
    if (enable_pruning_opt) {
      ImportItemAttrType(context, item_config);
    } else {
      ImportItemAttr(context, item_config, begin, end);
    }
  }
  std::vector<Formula1AttrConfig> kconf_import_item_attr_configs;
  if (is_json_from_kconf_) {
    const auto &import_common_config = formula1_->GetImportCommonAttrConfig();
    for (const auto &common_config : import_common_config) {
      Formula1AttrConfig f1_config = TranslateFormulaConfig(common_config, context, true);
      auto it = common_attr_import_mapping_.find(f1_config.as);
      if (it != common_attr_import_mapping_.end()) {
        if (it->second->name != f1_config.name) {
          CL_LOG_WARNING("formula1", "common_import_conflict: " + f1_config.as +
                                         "; scenario: " + formula1_->GetScenario())
              << "common_attr import mapping conflict: " << f1_config.as << ", "
              << "json: " << it->second->name << ", kconf: " << f1_config.name;
        } else {
          if (it->second->IsSame(f1_config)) {
            continue;  // 如果 kconf 和 json 中的 as 相同且 name 也相同，则跳过
          } else {
            CL_LOG_WARNING("formula1", "common_import_type_conflict: " + f1_config.as +
                                           "; scenario: " + formula1_->GetScenario())
                << "common_attr import mapping type conflict: " << f1_config.as << ", "
                << "json: " << it->second->name << ", kconf: " << f1_config.name;
          }
        }
      }
      ImportCommonAttr(context, f1_config);
    }
    const auto &import_item_config = formula1_->GetImportItemAttrConfig();
    kconf_import_item_attr_configs.reserve(import_item_config.size());
    for (const auto &item_config : import_item_config) {
      Formula1AttrConfig f1_config = TranslateFormulaConfig(item_config, context, false);
      // 检测 item_attr_import_mapping_ 冲突
      auto it = item_attr_import_mapping_.find(f1_config.as);
      if (it != item_attr_import_mapping_.end()) {
        if (it->second->name != f1_config.name) {
          CL_LOG_WARNING("formula1",
                         "item_import_conflict: " + f1_config.as + "; scenario: " + formula1_->GetScenario())
              << "item_attr import mapping conflict: " << f1_config.as << ", "
              << "json: " << it->second->name << ", kconf: " << f1_config.name;
        } else {
          if (it->second->IsSame(f1_config)) {
            // 如果 kconf 和 json 中的配置完全相同，则跳过
            continue;
          } else {
            CL_LOG_WARNING("formula1", "item_import_type_conflict: " + f1_config.as +
                                           "; scenario: " + formula1_->GetScenario())
                << "item_attr import mapping type conflict: " << f1_config.as << ", "
                << "json: " << it->second->name << ", kconf: " << f1_config.name;
          }
        }
      }
      if (enable_pruning_opt) {
        kconf_import_item_attr_configs.push_back(f1_config);
        ImportItemAttrType(context, kconf_import_item_attr_configs.back());
      } else {
        ImportItemAttr(context, f1_config, begin, end);
      }
    }
  }
  if (enable_pruning_opt) {
    const int item_num = std::distance(begin, end);
    formula1_->SetItemNum(item_num);
  }
  if (!prioritized_suffix.empty()) {
    std::vector<std::string> suffix_list;
    std::transform(prioritized_suffix.begin(), prioritized_suffix.end(), std::back_inserter(suffix_list),
                   [](const absl::string_view &view) { return std::string(view); });
    formula1_->SetSuffixPriorityList(std::move(suffix_list));
  }

  uint64 user_id = GetIntProcessorParameter(context, "user_id", context->GetUserId());
  std::string device_id = GetStringProcessorParameter(context, "device_id", context->GetDeviceId());
  auto ab_mapping_id = GetABMappingIdFromRequest(context->GetRequest());
  std::string session_id = GetStringProcessorParameter(context, "session_id", "");
  std::string perf_tag = GetStringProcessorParameter(context, "perf_tag");
  if (enable_pruning_opt) {
    formula1_->PreparePruneCalc(biz_, user_id, device_id, DebugLogEnabled(), ab_mapping_id, session_id,
                                perf_tag, true);
    // attr import 剪枝
    for (const auto &item_config : import_item_attrs_) {
      if (formula1_->ShouldImportItemAttr(item_config.as)) {
        ImportItemAttr(context, item_config, begin, end);
      }
    }
    for (const auto &item_config : kconf_import_item_attr_configs) {
      if (formula1_->ShouldImportItemAttr(item_config.as)) {
        ImportItemAttr(context, item_config, begin, end);
      }
    }
    formula1_->CalcPrunedFormula(DebugLogEnabled(), perf_tag, config()->GetString("debug_log_to"));
  } else {
    formula1_->Calc(biz_, user_id, device_id, DebugLogEnabled(), ab_mapping_id, session_id, perf_tag,
                    config()->GetString("debug_log_to"), false);
  }
  ExportFormulaValue(context, begin, end);
}

inline double IsFiniteOrDefault(absl::optional<double> value, double default_val) {
  return value && std::isfinite(*value) ? *value : default_val;
}

void CommonRecoFormulaOneEnricher::ImportCommonAttr(MutableRecoContextInterface *context,
                                                    const Formula1AttrConfig &common_config) {
  const auto &common_attr = common_config.accessor;
  switch (common_attr->value_type) {
    case AttrType::INT: {
      auto int_val = context->GetIntCommonAttr(common_attr);
      formula1_->SetCommonData(common_config.as, int_val.value_or(common_config.default_double));
    } break;
    case AttrType::FLOAT: {
      auto double_val = context->GetDoubleCommonAttr(common_attr);
      formula1_->SetCommonData(common_config.as, IsFiniteOrDefault(double_val, common_config.default_double));
    } break;
    case AttrType::STRING:
      if (auto string_val = context->GetStringCommonAttr(common_attr)) {
        formula1_->SetCommonData(common_config.as, std::string(*string_val));
      } else {
        formula1_->SetCommonData(common_config.as, common_config.default_string);
      }
      break;
    case AttrType::UNKNOWN:
      if (common_config.default_val_type == AttrType::FLOAT) {
        formula1_->SetCommonData(common_config.as, common_config.default_double);
      } else if (common_config.default_val_type == AttrType::STRING) {
        formula1_->SetCommonData(common_config.as, common_config.default_string);
      }
      break;
    default:
      CL_LOG_WARNING("formula1", "import_common_attr_fail: " + common_attr->name())
          << "import common_attr '" << common_attr->name()
          << "' fail, unsupported attr type: " << RecoUtil::GetAttrTypeName(common_attr->value_type);
      break;
  }
}

void CommonRecoFormulaOneEnricher::ImportItemAttrType(MutableRecoContextInterface *context,
                                                      const Formula1AttrConfig &item_config) {
  const auto &item_attr = item_config.accessor;
  switch (item_attr->value_type) {
    case AttrType::INT:
    case AttrType::FLOAT: {
      formula1_->SetItemDataType(item_config.as, ks::reco::formula1::VarType::DOUBLE);
    } break;
    case AttrType::STRING: {
      formula1_->SetItemDataType(item_config.as, ks::reco::formula1::VarType::STRING);
    } break;
    case AttrType::UNKNOWN:
      if (item_config.default_val_type == AttrType::FLOAT) {
        formula1_->SetItemDataType(item_config.as, ks::reco::formula1::VarType::DOUBLE);
      } else if (item_config.default_val_type == AttrType::STRING) {
        formula1_->SetItemDataType(item_config.as, ks::reco::formula1::VarType::STRING);
      }
      break;
    default:
      CL_LOG_WARNING("formula1", "import_item_attr_type_fail: " + item_attr->name())
          << "import item_attr '" << item_attr->name()
          << "' fail, unsupported attr type: " << RecoUtil::GetAttrTypeName(item_attr->value_type);
      break;
  }
}

void CommonRecoFormulaOneEnricher::ImportItemAttr(MutableRecoContextInterface *context,
                                                  const Formula1AttrConfig &item_config,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
  const auto &item_attr = item_config.accessor;
  const int item_num = std::distance(begin, end);
  switch (item_attr->value_type) {
    case AttrType::INT: {
      std::vector<double> values;
      values.reserve(item_num);
      std::for_each(begin, end, [&](const auto &item) {
        values.push_back(context->GetIntItemAttr(item, item_attr).value_or(item_config.default_double));
      });
      formula1_->SetItemData(item_config.as, std::move(values));
    } break;
    case AttrType::FLOAT: {
      std::vector<double> values;
      values.reserve(item_num);
      std::for_each(begin, end, [&](const auto &item) {
        values.push_back(
            IsFiniteOrDefault(context->GetDoubleItemAttr(item, item_attr), item_config.default_double));
      });
      formula1_->SetItemData(item_config.as, std::move(values));
    } break;
    case AttrType::STRING: {
      std::vector<std::string> values(item_num);
      int i = 0;
      for (auto it = begin; it != end && i < item_num; ++it, ++i) {
        if (auto string_val = context->GetStringItemAttr(*it, item_attr)) {
          values[i] = std::string(*string_val);
        } else {
          values[i] = item_config.default_string;
        }
      }
      formula1_->SetItemData(item_config.as, std::move(values));
    } break;
    case AttrType::UNKNOWN:
      if (item_config.default_val_type == AttrType::FLOAT) {
        std::vector<double> values(item_num, item_config.default_double);
        formula1_->SetItemData(item_config.as, std::move(values));
      } else if (item_config.default_val_type == AttrType::STRING) {
        std::vector<std::string> values(item_num, item_config.default_string);
        formula1_->SetItemData(item_config.as, std::move(values));
      }
      break;
    default:
      CL_LOG_WARNING("formula1", "import_item_attr_fail: " + item_attr->name())
          << "import item_attr '" << item_attr->name()
          << "' fail, unsupported attr type: " << RecoUtil::GetAttrTypeName(item_attr->value_type);
      break;
  }
}

void CommonRecoFormulaOneEnricher::ExportFormulaValue(MutableRecoContextInterface *context,
                                                      RecoResultConstIter begin, RecoResultConstIter end) {
  int item_num = std::distance(begin, end);
  for (const auto &attr_config : export_attrs_) {
    const auto &formula_name = attr_config.name;
    auto var_type = formula1_->GetVarType(formula_name);
    if (attr_config.to_common) {
      switch (var_type) {
        case reco::formula1::VarType::DOUBLE: {
          if (!formula1_->HasVar(formula_name)) {
            CL_LOG_WARNING("formula1", "formula_not_exist: " + formula_name)
                << "export formula value to common fail, formula not exist: " << formula_name;
          } else {
            auto first_value = formula1_->GetFirstValue(formula_name);
            if (first_value) {
              if (attr_config.to_int) {
                context->SetIntCommonAttr(attr_config.accessor, *first_value);
              } else {
                context->SetDoubleCommonAttr(attr_config.accessor, *first_value);
              }
            } else {
              CL_LOG(INFO) << "export formula value to common fail, value is empty: " << formula_name;
            }
          }
        } break;
        case reco::formula1::VarType::STRING: {
          if (!formula1_->HasVar(formula_name)) {
            CL_LOG_WARNING("formula1", "string_not_exist: " + formula_name)
                << "export string value to common fail, string not exist: " << formula_name;
          } else {
            auto first_value = formula1_->GetFirstStringValue(formula_name);
            if (first_value) {
              context->SetStringCommonAttr(attr_config.accessor, std::string(*first_value));
            } else {
              CL_LOG(INFO) << "export string value to common fail, value is empty: " << formula_name;
            }
          }
        } break;
        default:
          CL_LOG_WARNING("formula1", "formula_not_exist: " + formula_name)
              << "export formula value to common fail, formula not exist: " << formula_name;
      }
    } else {  // to item
      switch (var_type) {
        case reco::formula1::VarType::DOUBLE: {
          auto values = formula1_->GetValue(formula_name);
          if (!values) {
            CL_LOG_WARNING("formula1", "formula_not_exist: " + formula_name)
                << "export formula value fail, formula not exist: " << formula_name;
          } else {
            if (values->size() != item_num) {
              CL_LOG_WARNING("formula1", "formula_value_size_mismatch: " + formula_name)
                  << "export formula value fail, formula value size mismatch: " << values->size() << "vs"
                  << item_num;
              continue;
            }
            int i = 0;
            for (auto it = begin; it != end && i < values->size(); ++it, ++i) {
              if (attr_config.to_int) {
                context->SetIntItemAttr(*it, attr_config.accessor, values->at(i));
              } else {
                context->SetDoubleItemAttr(*it, attr_config.accessor, values->at(i));
              }
            }
          }
        } break;
        case reco::formula1::VarType::STRING: {
          auto values = formula1_->GetStringValue(formula_name);
          if (!values) {
            CL_LOG_WARNING("formula1", "string_not_exist: " + formula_name)
                << "export string value fail, string not exist: " << formula_name;
          } else {
            if (values->size() != item_num) {
              CL_LOG_WARNING("formula1", "string_value_size_mismatch: " + formula_name)
                  << "export string value fail, string value size mismatch: " << values->size() << "vs"
                  << item_num;
              continue;
            }
            int i = 0;
            for (auto it = begin; it != end && i < values->size(); ++it, ++i) {
              context->SetStringItemAttr(*it, attr_config.accessor, std::move(std::string(values->at(i))));
            }
          }
        } break;
        default:
          CL_LOG_WARNING("formula1", "formula_not_exist: " + formula_name)
              << "export formula value fail, formula not exist: " << formula_name;
      }
    }
  }
}

Formula1AttrConfig CommonRecoFormulaOneEnricher::TranslateFormulaConfig(
    const reco::formula1::Formula1ImportConfig &import_config, MutableRecoContextInterface *context,
    bool is_common) {
  Formula1AttrConfig f1_config;
  f1_config.name = import_config.name;
  f1_config.as = import_config.as;
  if (is_common) {
    f1_config.accessor = context->GetCommonAttrAccessor(import_config.name);
  } else {
    f1_config.accessor = context->GetItemAttrAccessor(import_config.name);
  }
  if (import_config.default_val_type == reco::formula1::VarType::DOUBLE) {
    f1_config.default_val_type = AttrType::FLOAT;
    f1_config.default_double = import_config.default_double;
  } else if (import_config.default_val_type == reco::formula1::VarType::STRING) {
    f1_config.default_val_type = AttrType::STRING;
    f1_config.default_string = import_config.default_string;
  }
  return f1_config;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoFormulaOneEnricher, CommonRecoFormulaOneEnricher)

}  // namespace platform
}  // namespace ks
