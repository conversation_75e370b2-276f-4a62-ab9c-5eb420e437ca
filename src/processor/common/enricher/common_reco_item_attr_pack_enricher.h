#pragma once

#include <string>
#include <utility>
#include <vector>

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoItemAttrPackEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemAttrPackEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  enum class Aggregator : int {
    UNKNOWN = 0,
    // 拼接
    CONCAT,
    // 拷贝 item attr 到 common attr
    COPY,
    // 取均值
    AVG,
    // 取和
    SUM,
    // 取最小值
    MIN,
    // 取最大值
    MAX,
    // 取方差
    DEV,
  };

  struct AttrPackMapping {
    std::string from_item_attr;
    std::string pack_if;
    std::string to_common_attr;
    int item_attr_limit = 0;
    bool dedup_to_common_attr = false;
    bool reset_to_common_attr = true;
    Aggregator aggregator = Aggregator::UNKNOWN;

    AttrType default_val_type = AttrType::UNKNOWN;
    int64 default_int_val = 0.0;
    double default_double_val = 0.0;
    std::string default_string_val;
    base::Json *dynamic_item_attr_limit = nullptr;
  };

  bool InitProcessor() override {
    auto *item_source = config()->Get("item_source");
    if (!item_source || !item_source->IsObject()) {
      LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                 << " init failed! Missing \"item_source\" config"
                 << " or it is not a dict";
      return false;
    }

    include_reco_results_ = item_source->GetBoolean("reco_results", false);

    auto *latest_browse_set_item = item_source->Get("latest_browse_set_item");
    if (latest_browse_set_item) {
      include_browse_set_ = true;
      if (!latest_browse_set_item->IntValue(&latest_browsed_num_)) {
        LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                   << " init failed! \"latest_browse_set_item\" should"
                   << " be an int! Value found: " << latest_browse_set_item->ToString();
        return false;
      }
    } else {
      include_browse_set_ = false;
    }

    auto *common_attr = item_source->Get("common_attr");
    if (common_attr) {
      if (!common_attr->IsArray()) {
        LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                   << " init failed! \"common_attr\" should be an array!"
                   << " Value found: " << common_attr->ToString();
        return false;
      }
      for (const auto *attr : common_attr->array()) {
        if (!attr->IsString()) {
          LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                     << " init failed! Item of \"common_attr\" should be string!"
                     << " Value found: " << attr->ToString();
          return false;
        }
        std::string attr_name = attr->StringValue();
        if (!attr_name.empty()) {
          source_common_attrs_.push_back(attr_name);
        }
      }
    }

    single_limit_ = item_source->GetInt("single_limit", 0);
    total_limit_cfg_ = item_source->Get("total_limit");

    auto mappings_config = config()->Get("mappings");
    if (!mappings_config || !mappings_config->IsArray()) {
      LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                 << " init failed! Missing \"mappings\" config"
                 << " or it is not an array.";
      return false;
    }

    attr_convert_mapping_.clear();
    for (const auto *mapping_json : mappings_config->array()) {
      if (!mapping_json->IsObject()) {
        LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                   << " init failed! Item of mappings should be a dict!"
                   << " Value found: " << mapping_json->ToString();
        return false;
      }

      AttrPackMapping mapping;
      mapping.from_item_attr = mapping_json->GetString("from_item_attr");
      if (!mapping_json->GetString("to_common_attr", &mapping.to_common_attr)) {
        LOG(ERROR) << "CommonRecoItemAttrPackEnricher"
                   << " init failed! Missing string config \"to_common_attr\""
                   << " in mapping!";
        return false;
      }
      std::string agg = mapping_json->GetString("aggregator", "concat");
      if (agg == "concat") {
        mapping.aggregator = Aggregator::CONCAT;
      } else if (agg == "copy") {
        mapping.aggregator = Aggregator::COPY;
      } else if (agg == "avg") {
        mapping.aggregator = Aggregator::AVG;
      } else if (agg == "sum") {
        mapping.aggregator = Aggregator::SUM;
      } else if (agg == "min") {
        mapping.aggregator = Aggregator::MIN;
      } else if (agg == "max") {
        mapping.aggregator = Aggregator::MAX;
      } else if (agg == "dev") {
        mapping.aggregator = Aggregator::DEV;
      } else {
        mapping.aggregator = Aggregator::UNKNOWN;
      }
      if (mapping.aggregator == Aggregator::UNKNOWN) {
        LOG(ERROR) << "CommonRecoItemAttrPackEnricher init failed!"
                   << " Invalid aggregator: " << agg << ", should be concat/copy/avg/sum/min/max";
        return false;
      }
      mapping.dynamic_item_attr_limit = mapping_json->Get("item_attr_limit");
      mapping.dedup_to_common_attr = mapping_json->GetBoolean("dedup_to_common_attr", false);
      mapping.reset_to_common_attr = mapping_json->GetBoolean("reset_to_common_attr", true);
      mapping.pack_if = mapping_json->GetString("pack_if");
      auto *default_val = mapping_json->Get("default_val");
      if (default_val) {
        if (default_val->IntValue(&mapping.default_int_val)) {
          mapping.default_val_type = AttrType::INT;
        } else if (default_val->NumberValue(&mapping.default_double_val)) {
          mapping.default_val_type = AttrType::FLOAT;
        } else if (default_val->StringValue(&mapping.default_string_val)) {
          mapping.default_val_type = AttrType::STRING;
        } else {
          LOG(ERROR) << "CommonRecoItemAttrPackEnricher init failed!"
                     << " Invalid default_val type, only support int/double/string value";
          return false;
        }
      }
      attr_convert_mapping_.push_back(std::move(mapping));
    }

    // 为兼容 a 站当前配置临时加一个兼容开关
    reverse_list_order_ = config()->GetBoolean("reverse_list_order", false);

    return true;
  }

  void HandleAttrMapping(MutableRecoContextInterface *context, const std::vector<uint64> &item_keys,
                         const AttrPackMapping &mapping);
  void AppendItemAttrs(MutableRecoContextInterface *context, const std::vector<uint64> &item_keys,
                       const AttrPackMapping &mapping);
  void AggregateItemAttrs(MutableRecoContextInterface *context, const std::vector<uint64> &item_keys,
                          const AttrPackMapping &mapping);
  void CopyItemAttr(MutableRecoContextInterface *context, const std::vector<uint64> &item_keys,
                    const AttrPackMapping &mapping);
  bool CheckDup(bool dedup_to_common_attr, folly::F14FastSet<uint64> *dedup_set, uint64 value);

 private:
  bool include_reco_results_ = false;
  bool include_browse_set_ = false;
  int64 latest_browsed_num_ = 0;
  std::vector<uint64> all_items_;
  std::vector<std::string> source_common_attrs_;
  int single_limit_ = 0;
  const base::Json *total_limit_cfg_ = nullptr;
  std::vector<AttrPackMapping> attr_convert_mapping_;

  bool reverse_list_order_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemAttrPackEnricher);
};

}  // namespace platform
}  // namespace ks
