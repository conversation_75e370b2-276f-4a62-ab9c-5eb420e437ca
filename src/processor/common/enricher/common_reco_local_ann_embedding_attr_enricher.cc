//
// Created by ya<PERSON><PERSON><PERSON> on 2020/10/10.
//

#include "dragon/src/processor/common/enricher/common_reco_local_ann_embedding_attr_enricher.h"

namespace ks {
namespace platform {

void CommonRecoLocalAnnEmbeddingAttrEnricher::Enrich(MutableRecoContextInterface *context,
                                                     RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  std::vector<uint64> nids;

  src_data_type_ = GetStringProcessorParameter(context, "src_data_type", "");
  if (src_data_type_.empty()) {
    CL_LOG_ERROR("local_ann_embedding", "empty_src_data_type")
        << "local ann kv request cancelled: empty src_data_type. ";
    return;
  }

  nids.reserve(std::distance(begin, end));
  std::for_each(begin, end, [&nids](const CommonRecoResult &item) { nids.push_back(item.GetId()); });

  kv_result_.clear();
  kv_result_.resize(nids.size(), nullptr);

  timer_.AppendCostMs("collect_items");

  SendRequest(context, nids, begin, end);
}

void CommonRecoLocalAnnEmbeddingAttrEnricher::SendRequest(MutableRecoContextInterface *context,
                                                          const std::vector<uint64> &nids,
                                                          RecoResultConstIter begin,
                                                          RecoResultConstIter end) {
  int dim;
  auto status =
      CommonRecoAnnRetrieve::Singleton()->GetLocalEmbeddings(nids, src_data_type_, &kv_result_, &dim);

  if (!status) {
    CL_LOG_ERROR_EVERY("local_ann_embedding", "get_kv_fail", 100) << "GetKVResult fail";
    return;
  }
  timer_.AppendCostMs("wait_for_response");

  if (save_to_common_attr_) {
    FillCommonAttr(context, dim, begin, end);
    timer_.AppendCostMs("fill_common_attr");
  } else {
    FillItemAttr(context, dim, begin, end);
    timer_.AppendCostMs("fill_item_attr");
  }
  CL_LOG(INFO) << timer_.display();
}

void CommonRecoLocalAnnEmbeddingAttrEnricher::FillCommonAttr(MutableRecoContextInterface *context, int dim,
                                                             RecoResultConstIter begin,
                                                             RecoResultConstIter end) const {
  auto result_size = kv_result_.size();
  if (result_size == 0) {
    CL_LOG(INFO) << "empty kv result";
    return;
  }

  auto request_size = std::distance(begin, end);
  if (result_size != request_size) {
    CL_LOG_ERROR_EVERY("local_ann_embedding", "size_mismatch", 100)
        << "request_size: " << request_size << ", result_size: " << result_size;
    return;
  }

  if (dim_ != dim) {
    CL_LOG_ERROR_EVERY("local_ann_embedding", "dim_mismatch", 100)
        << "configured dim: " << dim_ << ", result actual dim: " << dim;
    return;
  }

  std::vector<int64> item_keys;
  std::vector<std::string> data_types;

  std::vector<const float *> embedding_ptrs;
  std::vector<double> embeddings;

  int i = 0;
  for (auto iter = begin; iter != end; iter++, i++) {
    auto item_key = iter->item_key;
    auto item_id = iter->GetId();
    auto *head = kv_result_[i];
    if (head == nullptr) {
      CL_LOG_EVERY_N(INFO, 100) << "request item_id: " << item_id << ", item_key: " << item_key
                                << ", embedding empty";
      continue;
    }

    item_keys.push_back(iter->item_key);
    data_types.push_back(src_data_type_);

    if (save_as_ptr_) {
      embedding_ptrs.push_back(head);
    } else {
      for (int j = 0; j < dim_; j++) {
        auto *curr = head + j;
        if (curr == nullptr) {
          CL_LOG_EVERY_N(ERROR, 10000) << i << "th embedding's " << j << " element ptr is null";
          break;
        }
        embeddings.push_back(*curr);
      }
    }
  }

  if (item_keys.size() == 0 || embeddings.size() == 0 || (embeddings.size() % item_keys.size()) != 0) {
    CL_LOG_ERROR("local_ann_embedding", "size_mismatch")
        << "embeddings size: " << embeddings.size() << " mismatch item_keys size: " << item_keys.size();
    return;
  }
  context->SetIntListCommonAttr(item_list_output_attr_, std::move(item_keys));
  if (!data_type_list_output_attr_.empty()) {
    context->SetStringListCommonAttr(data_type_list_output_attr_, std::move(data_types));
  }

  if (save_as_ptr_) {
    context->SetExtraCommonAttr(embedding_list_output_attr_, std::move(embedding_ptrs));
  } else {
    context->SetDoubleListCommonAttr(embedding_list_output_attr_, std::move(embeddings));
  }
}

void CommonRecoLocalAnnEmbeddingAttrEnricher::FillItemAttr(MutableRecoContextInterface *context, int dim,
                                                           RecoResultConstIter begin,
                                                           RecoResultConstIter end) const {
  auto result_size = kv_result_.size();
  if (result_size == 0) {
    CL_LOG(INFO) << "empty kv result";
    return;
  }

  auto request_size = std::distance(begin, end);
  if (result_size != request_size) {
    CL_LOG_ERROR_EVERY("local_ann_embedding", "size_mismatch", 100)
        << "request_size: " << request_size << ", result_size: " << result_size;
    return;
  }

  if (dim_ != dim) {
    CL_LOG_ERROR_EVERY("local_ann_embedding", "dim_mismatch", 100)
        << "configured dim: " << dim_ << ", result actual dim: " << dim;
    return;
  }

  ItemAttr *output_embedding_accessor = context->GetItemAttrAccessor(embedding_item_attr_);
  ItemAttr *output_data_type_accessor =
      data_type_item_attr_.empty() ? nullptr : context->GetItemAttrAccessor(data_type_item_attr_);
  int i = 0;
  for (auto iter = begin; iter != end; iter++, i++) {
    auto item_id = iter->GetId();
    uint64 item_key = Util::GenKeysign(item_type_, item_id);
    if (output_data_type_accessor != nullptr) {
      iter->SetStringAttr(output_data_type_accessor, src_data_type_);
    }
    auto *head = kv_result_[i];
    if (head == nullptr) {
      CL_LOG_EVERY_N(INFO, 1000) << "request item_id: " << item_id << ", item_key: " << item_key
                                 << ", embedding empty";
      continue;
    }

    if (save_as_ptr_) {
      iter->SetPtrAttr(output_embedding_accessor, std::move(head));
    } else {
      std::vector<double> embeddings;
      for (int j = 0; j < dim_; j++) {
        auto *curr = head + j;
        if (curr == nullptr) {
          CL_LOG_EVERY_N(ERROR, 10000) << i << "th embedding's " << j << " element ptr is null";
          break;
        }
        embeddings.push_back(*curr);
      }
      if (embeddings.size() == dim_) {
        iter->SetDoubleListAttr(output_embedding_accessor, std::move(embeddings));
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLocalAnnEmbeddingAttrEnricher,
                 CommonRecoLocalAnnEmbeddingAttrEnricher);

}  // namespace platform
}  // namespace ks
