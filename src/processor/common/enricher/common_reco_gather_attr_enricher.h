#pragma once

#include <map>
#include <string>
#include <utility>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoGatherAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoGatherAttrEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    index_attr_ = config()->GetString("index_attr");
    if (index_attr_.empty()) {
      LOG(ERROR) << "CommonRecoGatherAttrEnricher init failed!"
                 << " Missing 'index_attr' config.";
      return false;
    }

    is_common_attr_ = config()->GetBoolean("is_common_attr", is_common_attr_);
    is_range_ = config()->GetBoolean("is_range", false);

    auto *list_values = config()->Get("list_values");
    if (list_values && list_values->IsArray()) {
      for (const auto *value : list_values->array()) {
        if (value->IsObject()) {
          const std::string &input_attr = value->GetString("from");
          const std::string &output_attr = value->GetString("to");

          if (input_attr.empty() || output_attr.empty()) {
            LOG(ERROR)
                << "CommonRecoGatherAttrEnricher init failed! the element of `list_values` should be an "
                   "object with keys of `from` and `to`: "
                << value->ToString();
            return false;
          }

          map_output_attrs_to_input_attrs_.emplace(std::move(output_attr), std::move(input_attr));
        }
      }
    }

    if (map_output_attrs_to_input_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoGatherAttrEnricher init failed! `list_values` is required!";
      return false;
    }

    return true;
  }

  void ProcessCommonAttrs(MutableRecoContextInterface *context);
  void ProcessItemAttrs(MutableRecoContextInterface *context, RecoResultConstIter begin,
                        RecoResultConstIter end);

 private:
  std::string index_attr_;
  std::map<std::string, std::string> map_output_attrs_to_input_attrs_;
  bool is_common_attr_ = true;
  bool is_range_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoGatherAttrEnricher);
};

}  // namespace platform
}  // namespace ks
