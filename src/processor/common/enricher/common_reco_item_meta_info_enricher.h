#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class CommonRecoItemMetaInfoEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoItemMetaInfoEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    save_item_key_to_attr_ = config()->GetString("save_item_key_to_attr");
    save_item_id_to_attr_ = config()->GetString("save_item_id_to_attr");
    save_item_type_to_attr_ = config()->GetString("save_item_type_to_attr");
    save_reason_to_attr_ = config()->GetString("save_reason_to_attr");
    save_score_to_attr_ = config()->GetString("save_score_to_attr");
    save_in_browse_set_to_attr_ = config()->GetString("save_in_browse_set_to_attr");
    save_item_seq_to_attr_ = config()->GetString("save_item_seq_to_attr");
    return true;
  }

  ItemAttr *GetAttrAccessor(MutableRecoContextInterface *context, const std::string &attr_name) {
    return attr_name.empty() ? nullptr : context->GetItemAttrAccessor(attr_name);
  }

 private:
  std::string save_item_key_to_attr_;
  std::string save_item_id_to_attr_;
  std::string save_item_type_to_attr_;
  std::string save_reason_to_attr_;
  std::string save_score_to_attr_;
  std::string save_in_browse_set_to_attr_;
  std::string save_item_seq_to_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemMetaInfoEnricher);
};

}  // namespace platform
}  // namespace ks
