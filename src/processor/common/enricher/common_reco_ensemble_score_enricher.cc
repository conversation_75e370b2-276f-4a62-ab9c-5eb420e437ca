#include "dragon/src/processor/common/enricher/common_reco_ensemble_score_enricher.h"

#include <algorithm>
#include <cmath>
#include "serving_base/util/math.h"
#include "serving_base/utility/timer.h"

namespace ks {
namespace platform {

void CommonRecoEnsembleScoreEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                             RecoResultConstIter end) {
  serving_base::Timer timer;
  item_list_.clear();
  std::copy(begin, end, std::back_inserter(item_list_));
  if (item_list_.empty()) {
    CL_LOG(INFO) << "calc ensemble score cancelled for empty item list";
    return;
  }

  timer.AppendCostMs("prepare_items");

  auto comparer = [this](const CommonRecoResult &a, const CommonRecoResult &b) { return a.score > b.score; };
  for (auto &channel : ensemble_channels_) {
    channel.weight = GetDoubleProcessorParameter(context, channel.weight_config, 0.0, true);
    channel.enabled = GetBoolProcessorParameter(context, channel.enabled_config, true);
    if (!channel.enabled || channel.weight == 0.0) {
      continue;
    }
    auto *attr_accessor = context->GetItemAttrAccessor(channel.name);
    if (attr_accessor->value_type == AttrType::UNKNOWN) {
      channel.seq_attr = nullptr;
      CL_LOG_WARNING_EVERY("ensemble_score", "attr_not_found:" + channel.name, 100)
          << "skipped calc ensemble score on channel " << channel.name << ": no such attr";
      continue;
    }
    channel.hyper_scala = GetDoubleProcessorParameter(context, channel.hyper_scala_config, 0.0, true);
    if (formula_version_ == 2) {  // XXX: 对于双曲函数，需要判定 scale 合法性
      if (channel.hyper_scala == 0.0) {
        channel.enabled = false;
        continue;
      }
      channel.hyper_division = 2.0 * (std::exp(-channel.hyper_scala) - std::exp(channel.hyper_scala));
    }
    if (channel.as_seq_value) {
      channel.seq_attr = attr_accessor;
      if (attr_accessor->value_type != AttrType::INT) {
        channel.seq_attr = nullptr;
        auto type = RecoUtil::GetAttrTypeName(attr_accessor->value_type);
        std::string msg = "non_seq_attr:" + channel.name + "(" + type + ")";
        CL_LOG_WARNING_EVERY("ensemble_score", msg, 100)
            << "skipped calc ensemble score on channel " << channel.name
            << ": not an int attr, value_type: " << type;
      }
      continue;
    }

    if (attr_accessor->value_type != AttrType::FLOAT && attr_accessor->value_type != AttrType::INT) {
      channel.seq_attr = nullptr;
      auto type = RecoUtil::GetAttrTypeName(attr_accessor->value_type);
      std::string msg = "non_numeric_attr:" + channel.name + "(" + type + ")";
      CL_LOG_WARNING_EVERY("ensemble_score", msg, 100)
          << "skipped calc ensemble score on channel " << channel.name
          << ": not a numeric attr, value_type: " << type;
      continue;
    }

    if (!channel.seq_attr) {
      channel.seq_attr = context->GetItemAttrAccessor("$$SEQ_" + channel.name);
    }
    for (auto &item : item_list_) {
      item.score = GetDoubleValue(attr_accessor, item);
    }
    if (stable_sort_) {
      std::stable_sort(item_list_.begin(), item_list_.end(), comparer);
    } else {
      std::sort(item_list_.begin(), item_list_.end(), comparer);
    }
    if (allow_tied_seq_) {
      int shift = 0;
      double current_val = 0.0;
      double last_val = 0.0;
      for (int i = 0; i < item_list_.size(); ++i) {
        current_val = item_list_[i].score;
        if (i > 0 && base::IsEqual(current_val, last_val, epsilon_)) {
          ++shift;
        } else if (!continuous_tied_seq_) {
          shift = 0;
        }
        item_list_[i].SetIntAttr(channel.seq_attr, start_seq_ + i - shift, false, false);
        last_val = current_val;
      }
    } else {
      for (int i = 0; i < item_list_.size(); ++i) {
        item_list_[i].SetIntAttr(channel.seq_attr, start_seq_ + i, false, false);
      }
    }
  }
  timer.AppendCostMs("calc_seq");

  double regulator = GetDoubleProcessorParameter(context, regulator_config_, 0.0, true);
  double smooth = GetDoubleProcessorParameter(context, smooth_config_, 0.0, true);
  double cliff_ratio = GetDoubleProcessorParameter(context, cliff_ratio_config_, -1.0, true);
  int cliff_height = GetIntProcessorParameter(context, cliff_height_config_, (int64)0);
  int cliff_point = start_seq_ - 1;
  if (cliff_ratio > 0) {
    cliff_point = start_seq_ + std::distance(begin, end) * cliff_ratio;
  }
  for (auto &channel : ensemble_channels_) {
    if (channel.save_score_to_accessor == nullptr && !channel.save_score_to.empty()) {
      channel.save_score_to_accessor = context->GetItemAttrAccessor(channel.save_score_to);
    }
  }
  auto *output_accessor = context->GetItemAttrAccessor(output_attr_);
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    double score = 0.0;
    for (const auto &channel : ensemble_channels_) {
      if (!channel.enabled || channel.weight == 0.0) {
        continue;
      }
      if (!channel.seq_attr) continue;
      auto seq = result.GetIntAttr(channel.seq_attr);
      if (!seq) continue;
      int rank = *seq;
      int list_size = item_list_.size();

      if (cliff_point >= start_seq_ && rank >= cliff_point) {
        rank += cliff_height;
        list_size += cliff_height;
      }
      double channel_score = 0.0;
      if (formula_version_ == 1) {
        channel_score =
            channel.weight *
            (1.0 - std::pow(std::min<double>(rank, item_list_.size()) / item_list_.size(), regulator));
        score += channel_score;
      } else if (formula_version_ == 2) {
        double hyper_val = std::exp(channel.hyper_scala * (2.0 * rank / list_size - 1.0)) -
                           std::exp(channel.hyper_scala * (1.0 - 2.0 * rank / list_size));
        // XXX: 确保队列 score 是正值，否则进行 log 等操作的时候，还需要额外处理
        channel_score = channel.weight * (hyper_val / channel.hyper_division + 0.5);
        score += channel_score;
      } else if (formula_version_ == 3) {
        // weight * (1.0 - (seq / (size + smooth))^coef)
        channel_score = channel.weight * (1.0 - std::pow(rank / (item_list_.size() + smooth), regulator));
        score += channel_score;
      } else {
        channel_score = channel.weight / (std::pow(rank, regulator) + smooth);
        score += channel_score;
      }
      if (channel.save_score_to_accessor) {
        result.SetDoubleAttr(channel.save_score_to_accessor, channel_score);
      }
    }
    result.SetDoubleAttr(output_accessor, score);
  });
  timer.AppendCostMs("calc_score");

  CL_LOG(INFO) << "calc ensemble score done for " << item_list_.size() << " items," << timer.display();
}

double CommonRecoEnsembleScoreEnricher::GetDoubleValue(ItemAttr *attr_accessor,
                                                       const CommonRecoResult &result) const {
  if (attr_accessor->value_type == AttrType::FLOAT) {
    auto double_val = result.GetDoubleAttr(attr_accessor);
    if (double_val) return *double_val;
  } else if (attr_accessor->value_type == AttrType::INT) {
    // 如果 double attr 不存在尝试读取 int attr
    auto int_val = result.GetIntAttr(attr_accessor);
    if (int_val) return *int_val;
  }
  // 缺省则返回 default value
  return default_value_;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoEnsembleScoreEnricher, CommonRecoEnsembleScoreEnricher)

}  // namespace platform
}  // namespace ks
