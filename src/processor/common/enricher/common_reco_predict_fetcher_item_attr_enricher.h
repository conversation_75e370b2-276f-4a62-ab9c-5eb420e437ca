#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "folly/container/F14Map.h"
#include "ks/reco_proto/action/sample_list.pb.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/reco_proto/proto/predict_service.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_pub/reco/predict/clients/predict_kess_fetcher.h"
#include "learning/kuiba/predict_base/common_predict_client.h"
#include "learning/kuiba/predict_base/predict_base.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"

namespace ks {
namespace reco {
class CommonRecoPredictClient;
}  // namespace reco
}  // namespace ks

namespace ks {
namespace platform {

// XXX(fangjianbing): PXTR_LIST 中的 pxtr 名称顺序必须与 ENRICH_PREDICT_RESULT 调用的顺序完全一致！！！
// 并且注意同步更新至 dragonfly 代码中的 __PXTR_CANDIDATES 变量列表
static const std::vector<std::string> PXTR_LIST = {
    "pctr",        "pltr",   "pwtr",   "plvtr",  "psvtr",     "pftr",   "pshowtr",     "plvtr2", "ptr",
    "pwatch_time", "pepstr", "pcmtr",  "pecstr", "plivingtr", "pcestr", "plttr",       "pwttr",  "pdtr",
    "pelivingtr",  "pcotr",  "pfostr", "pwtd",   "pclk_cmt",  "pswptr", "pswptr_after", "pcltr", "phtr"};

class CommonRecoPredictFetcherItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  CommonRecoPredictFetcherItemAttrEnricher() {}

  bool IsAsync() const override {
    return true;
  }

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    service_group_ = config()->GetString("service_group", "PRODUCTION");
    thread_num_ = config()->GetInt("thread_num", 10);
    timeout_ms_ = config()->GetInt("timeout_ms", 80);

    user_info_attr_ = config()->GetString("user_info_attr", "");
    if (user_info_attr_.empty()) {
      LOG(ERROR) << "CommonRecoPredictFetcherItemAttrEnricher"
                 << " init failed! Missing \"user_info_attr\"!";
      return false;
    }

    return_pxtr_label_ = config()->GetString("return_pxtr_label", "pxtr_label_attr");
    return_pxtr_value_ = config()->GetString("return_pxtr_value", "pxtr_value_attr");
    try_parse_user_info_ = config()->GetBoolean("try_parse_user_info", true);

    auto *pxtr = config()->Get("pxtr");
    if (pxtr) {
      if (!pxtr->IsArray()) {
        LOG(ERROR) << "CommonRecoPredictFetcherItemAttrEnricher init failed!"
                   << " Config \"pxtr\" should be an array!";
        return false;
      }
      include_pxtrs_.resize(PXTR_LIST.size(), false);
      std::string str;
      for (const auto *xtr : pxtr->array()) {
        if (xtr->StringValue(&str) && !str.empty()) {
          auto it = std::find(PXTR_LIST.begin(), PXTR_LIST.end(), str);
          if (it != PXTR_LIST.end()) {
            include_pxtrs_[it - PXTR_LIST.begin()] = true;
          }
        }
      }
    }

    item_id_attr_ = config()->GetString("item_id_attr", "");
    output_attr_prefix_ = config()->GetString("output_prefix", "");

    return true;
  }

 protected:
  void EnrichPredictResult(MutableRecoContextInterface *context, int request_item_num,
                           const folly::F14FastMap<uint64, CommonRecoResult> &item_map,
                           const std::string &request_info, const ks::reco::PredictResponse *resp);

  virtual void GetRemotePxtr(MutableRecoContextInterface *context, const ks::reco::UserInfo *user_info,
                             const std::string &kess_service, const std::string &request_info);

  std::string service_group_;
  int thread_num_;
  int timeout_ms_;

  std::string item_id_attr_;
  std::string user_info_attr_;
  std::string output_attr_prefix_;

  std::string return_pxtr_label_;
  std::string return_pxtr_value_;
  bool try_parse_user_info_;

  // 记录实际发送的 item id
  folly::F14FastMap<uint64, CommonRecoResult> item_id_to_result_map_;
  std::vector<uint64> item_ids_;
  std::vector<bool> include_pxtrs_;
  std::vector<ItemAttr *> output_attr_accessors_;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoPredictFetcherItemAttrEnricher);
};

class CommonRecoPredictFetcherV2ItemAttrEnricher : public CommonRecoPredictFetcherItemAttrEnricher {
 public:
  CommonRecoPredictFetcherV2ItemAttrEnricher();

 protected:
  // 如果能直接获取到 user_id、user_info_str 的话， user_info 可以为 nullptr
  void GetRemotePxtr(MutableRecoContextInterface *context, const ks::reco::UserInfo *user_info,
                     const std::string &kess_service, const std::string &request_info) override;

  std::shared_ptr<ks::reco::CommonRecoPredictClient> common_reco_predict_client_;
};

}  // namespace platform
}  // namespace ks
