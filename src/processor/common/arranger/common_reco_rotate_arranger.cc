#include "dragon/src/processor/common/arranger/common_reco_rotate_arranger.h"
#include <algorithm>

namespace ks {
namespace platform {

RecoResultIter CommonRecoRotateArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                 RecoResultIter end) {
  int head = GetIntProcessorParameter(context, "head");
  int size = std::distance(begin, end);
  // head 大于 0 时从头往后数, 小于 0 时从尾往前数
  auto mid = head >= 0 ? std::next(begin, std::min(head, size)) : std::prev(end, std::min(-head, size));
  // mid 位置的 item 将被旋转成为 range 里的第一位
  std::rotate(begin, mid, end);
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRotateArranger, CommonRecoRotateArranger)

}  // namespace platform
}  // namespace ks
