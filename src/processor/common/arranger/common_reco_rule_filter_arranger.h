#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "folly/container/F14Set.h"
#include "serving_base/jansson/jansson.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

class CommonRecoRuleFilterArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoRuleFilterArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    auto *filter_rule = config()->Get("rule");
    if (!filter_rule || !filter_rule->IsObject()) {
      LOG(ERROR) << "CommonRecoRuleFilterArranger"
                 << " init failed! Missing \"rules\" config or it is"
                 << " not an dict.";
      return false;
    }

    ignore_invalid_rule_ = config()->GetBoolean("ignore_invalid_rule", false);

    return root_filter_.InitFilterTree(filter_rule, &root_filter_, "remove");
  }

 private:
  RuleFilter root_filter_;
  bool ignore_invalid_rule_ = false;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoRuleFilterArranger);
};

}  // namespace platform
}  // namespace ks
