#include "dragon/src/processor/common/arranger/common_reco_rule_filter_arranger.h"
#include <memory>
#include <unordered_set>

namespace ks {
namespace platform {

RecoResultIter CommonRecoRuleFilterArranger::Arrange(MutableRecoContextInterface *context,
                                                     RecoResultIter begin, RecoResultIter end) {
  if (!LoadFilterTree(context, &root_filter_, "remove")) {
    return end;
  }

  if (root_filter_.is_illegal_rule) {
    CL_LOG_ERROR("rule_filter", "illegal_rule_filter: " + root_filter_.illegal_info);
    return end;
  }

  int pardon_num = GetIntProcessorParameter(context, "pardon_num", 0);

  // 记录当前已被赦免了多少个
  int current_pardon_count = 0;
  int type_mismatch_count = 0;
  auto remove_condition = [this, context, pardon_num, &current_pardon_count,
                           &type_mismatch_count](const CommonRecoResult &result) {
    bool should_remove =
        CheckItemMeetConditions(context, result, &root_filter_, "remove", &type_mismatch_count);
    if (should_remove && current_pardon_count < pardon_num) {
      // 如果是需要被赦免的 item
      should_remove = false;
      ++current_pardon_count;
    }
    return should_remove;
  };
  int cancel_num = GetIntProcessorParameter(context, "cancel_num", -1);
  if (cancel_num >= 0) {
    int remain_num = std::distance(begin, end) - std::count_if(begin, end, remove_condition);
    if (remain_num <= cancel_num) {
      CL_LOG(INFO) << "rule filter cancelled: will remain " << remain_num
                   << " items only, cancel_num: " << cancel_num;
      base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "rule_filter_cancel_count",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), GetName());
      return end;
    }
  }

  auto new_end = std::remove_if(begin, end, remove_condition);

  if (current_pardon_count > 0) {
    CL_LOG(INFO) << "rule filter has pardoned " << current_pardon_count
                 << " items, max pardon_num: " << pardon_num;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        current_pardon_count, kPerfNs, "rule_filter_pardon_count", GlobalHolder::GetServiceIdentifier(),
        context->GetRequestType(), GetName());
  }

  if (type_mismatch_count > 0) {
    CL_LOG_ERROR_COUNT(type_mismatch_count, "rule_filter", "value_type_mismatch: rule filter")
        << ", mismatched item num: " << type_mismatch_count;
  }

  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "rule_filter_count",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRuleFilterArranger, CommonRecoRuleFilterArranger)

}  // namespace platform
}  // namespace ks
