#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

/**
 * NOTE(fangjianbing): 该 processor 根据用户指定的 item_type pattern list 自动调整当前结果集
 * 中的 item 顺序, 以尽可能的满足其对应位置的 pattern
 * mix_pattern 示例: [1, 1, 0] 表示让结果集中的 item_type 顺序循环满足 1, 1, 0, 1, 1, 0, ... 的排列
 */
class CommonRecoIntermixArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoIntermixArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    mix_on_attr_ = config()->GetString("mix_on_attr");
    mix_on_reason_ = config()->GetBoolean("mix_on_reason", false);
    return true;
  }

  int64 GetSlotPattern(const std::vector<int64> &pattern, int slot) const {
    return pattern[slot % pattern.size()];
  }

  bool IsPatternMatch(ReadableRecoContextInterface *context, const CommonRecoResult &result,
                      int64 pattern) const {
    if (mix_on_attr_.empty() && mix_on_reason_) {
      return result.reason == pattern;
    } else if (mix_on_attr_.empty()) {
      // 兼容老配置项, 默认比较 item_type
      return (int64)Util::GetType(result.item_key) == pattern;
    } else {
      auto val = context->GetIntItemAttr(result.item_key, mix_on_attr_);
      if (!val) return false;
      return *val == pattern;
    }
  }

  void HandleIntermix(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end);

 private:
  std::string mix_on_attr_;
  std::vector<CommonRecoResult> mix_results_;
  std::vector<bool> succeed_flags_;
  bool mix_on_reason_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoIntermixArranger);
};

}  // namespace platform
}  // namespace ks
