#pragma once

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

/**
 * NOTE(fangjianbing): 该 processor 的核心功能是将 begin 和 end 之间的结果集通过顺序旋转(std::rotate)
 * 达到将尾部 item 提升(或强插)至头部位置的效果, 配合 Arranger 的公有配置项 range_start/range_end 可完成
 * 将任意连续的 N 个 items 强插至第 M 个位置的功能
 */
class CommonRecoRotateArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoRotateArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoRotateArranger);
};

}  // namespace platform
}  // namespace ks
