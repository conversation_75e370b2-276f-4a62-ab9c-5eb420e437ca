#include "dragon/src/processor/common/arranger/common_reco_diversity_rules_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoDiversityRulesArranger::Arrange(MutableRecoContextInterface *context,
                                                         RecoResultIter begin, RecoResultIter end) {
  auto prev_items =
      prev_items_from_attr_.empty() ? absl::nullopt : context->GetIntListCommonAttr(prev_items_from_attr_);
  const int static_offset = prev_items ? prev_items->size() : 0;

  // 读取 slot_num 配置
  slot_num_ = GetIntProcessorParameter(context, raw_slot_num_config_, (int64)0);
  if (slot_num_ <= 0) {
    CL_LOG(INFO) << "diversify cancelled: max_satisfied_pick should be > 0.";
    return end;
  }
  // 读取 perflog_enabled
  perflog_enabled_ = GetBoolProcessorParameter(context, raw_perflog_enabled_, false);
  // 读取规则配置
  bool rules_init_flag = InitRules(context);
  if (!rules_init_flag) {
    return end;
  }
  // 取规则中用到的 accseeor
  folly::F14FastMap<std::string, ItemAttr *> attr_accessor_map;
  for (auto &rule_validators : priority_rule_validators_) {
    for (auto &rule_validator : rule_validators) {
      const auto &attr_name = rule_validator.rule().attr_name;
      const auto &rule_id = rule_validator.rule().rule_id;
      ItemAttr *attr_accessor = context->GetItemAttrAccessor(attr_name);
      attr_accessor_map.insert(std::make_pair(attr_name, attr_accessor));
      // use rule_id as index to store the perflog data, and avoid rule_id exceed list range
      if (rule_id < perflog_rule_list_.size()) {
        perflog_rule_list_[rule_id] = std::make_pair(attr_name, 0);
      }
    }
  }
  // 加入跨屏信息
  int idx = 0;
  if (prev_items) {
    for (auto item_key : *prev_items) {
      auto item = context->NewCommonRecoResult(item_key, -1);
      GenDiversityTag(context, item, attr_accessor_map, idx, true);
      ranked_key_.push_back(idx);
      idx++;
    }
  }
  // 为候选项关联规则
  std::for_each(begin, end, [this, &idx, context, &attr_accessor_map](const CommonRecoResult &result) {
    GenDiversityTag(context, result, attr_accessor_map, idx, false);
    ranked_key_.push_back(idx);
    idx++;
  });
  // 更新跨屏规则状态
  UpdateSessionRules(static_offset);
  // 筛选前 slot_num 个候选项
  slot_num_ = std::min(slot_num_ + static_offset, (int)ranked_key_.size());
  for (int slot_id = static_offset; slot_id < slot_num_; slot_id++) {
    // 从 slot_id 位置开始，检测当前位置下，各候选项的规则命中情况，并按优先级加入到 priority_results_
    ValidateRules(slot_id);
    // 从 priority_results_ 选出当前位置的最优解
    int selected_idx = RankOneSlot();
    if (selected_idx < 0) {
      CL_LOG_ERROR("diversify", "not_enough_candidates")
          << "diversify cancelled: candidate_num = " << ranked_key_.size()
          << "max_satisfied_pick = " << slot_num_;
      return end;
    }
    // 交换视频
    if (selected_idx != slot_id) {
      std::swap(ranked_key_[slot_id], ranked_key_[selected_idx]);
    }
    // 更新规则状态
    UpdateRuleStats(slot_id, static_offset, false);
  }
  // 将 slot_num 之后的候选项按原序排序
  if (ranked_key_.size() > slot_num_) {
    std::sort(ranked_key_.begin() + slot_num_, ranked_key_.end());
  }
  // get perflog list data
  if (perflog_enabled_) {
    for (auto &iter : perflog_rule_list_) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(iter.second, "common", "diversity_rule_valid_cnt",
                                                        GlobalHolder::GetServiceIdentifier(),
                                                        context->GetRequestType(), iter.first);
    }
  }
  // 返回排序结果
  std::vector<CommonRecoResult> ranked_results;
  for (int ranked_id : ranked_key_) {
    // skip ranked_id < static_offset
    if (ranked_id < static_offset) continue;
    int origin_offset = ranked_id - static_offset;
    ranked_results.push_back(*std::next(begin, origin_offset));
  }
  auto last = std::copy(ranked_results.begin(), ranked_results.end(), begin);
  return last;
}

void CommonRecoDiversityRulesArranger::UpdateSessionRules(const int session_offset) {
  // 返回条件
  if (session_offset <= 0) return;
  for (int slot_id = 0; slot_id < session_offset; slot_id++) {
    UpdateRuleStats(slot_id, session_offset, true);
  }
  return;
}

CommonRecoDiversityRulesArranger::RatioRule::RatioRule(const base::Json &kv) : BaseRule(-1, true) {
  rule_id = kv.GetInt("rule_id", -1);
  std::string type = kv.GetString("window_type", "slide");
  window_type = (type == "top") ? WINDOW_TYPE_TOP : WINDOW_TYPE_SLIDE;
  attr_name = kv.GetString("attr_name", "");
  priority = kv.GetInt("priority", -1);
  window_size = kv.GetInt("window_size", 0);
  min_num = kv.GetInt("min_num", 0);
  max_num = kv.GetInt("max_num", INT_MAX);
  enabled = kv.GetBoolean("enabled", true);
  consider_prev_items = kv.GetBoolean("consider_prev_items", false);
}

CommonRecoDiversityRulesArranger::RuleValidator::RuleValidator(const RuleValidator &rule) {
  rule_.rule_id = rule.rule_.rule_id;
  rule_.enabled = rule.rule_.enabled;
  rule_.consider_prev_items = rule.rule_.consider_prev_items;
  rule_.priority = rule.rule_.priority;
  rule_.window_type = rule.rule_.window_type;
  rule_.window_size = rule.rule_.window_size;
  rule_.attr_name = rule.rule_.attr_name;
  rule_.max_num = rule.rule_.max_num;
  rule_.min_num = rule.rule_.min_num;
  tag_cnt_ = folly::F14FastMap<int64, int>(rule.tag_cnt_);
}

bool CommonRecoDiversityRulesArranger::RuleValidator::CheckConfig(int priority_num) {
  if (rule_.window_size <= 0) return false;
  if (rule_.max_num < 0 || rule_.max_num < rule_.min_num) return false;
  if (rule_.attr_name.empty()) return false;
  if (rule_.priority < 0 || rule_.priority >= priority_num) return false;
  return true;
}

CommonRecoDiversityRulesArranger::RatioStatus CommonRecoDiversityRulesArranger::RuleValidator::Validate(
    const folly::F14FastMap<int, std::vector<int64>> &diversity_tags, int slot_id) {
  auto rule_tag_list = diversity_tags.find(rule_.rule_id);
  if (rule_tag_list == diversity_tags.end() ||
      (rule_.window_type == WINDOW_TYPE_TOP && slot_id >= rule_.window_size)) {
    return RATIO_NORMAL;
  }
  const auto &tag_values = rule_tag_list->second;
  for (const auto tag_value : tag_values) {
    int value_cnt = tag_cnt_[tag_value];
    int cur_window_size = std::min(slot_id + 1, rule_.window_size);
    int count_when_not_selected = value_cnt + rule_.window_size - cur_window_size;
    if (count_when_not_selected < rule_.min_num) {
      return RATIO_UNDERFLOW;  // 命中强插规则
    }
    int count_when_selected = value_cnt + 1;
    if (count_when_selected > rule_.max_num) {
      return RATIO_OVERFLOW;  // 命中打散规则
    }
  }
  return RATIO_NORMAL;  // 未命中规则
}

void CommonRecoDiversityRulesArranger::RuleValidator::UpdateStats(
    const std::vector<int> &ranked_key,
    const folly::F14FastMap<int, folly::F14FastMap<int, std::vector<int64>>> &item_tags_map, int slot_id,
    bool session_flag, const int session_offset) {
  if (session_flag && rule_.window_type != WINDOW_TYPE_SLIDE) return;
  //  针对被选出的候选项，更新规则状态
  int selected_id = ranked_key[slot_id];
  auto tags_map = item_tags_map.find(selected_id);
  if (tags_map == item_tags_map.end()) return;
  auto tags_list = tags_map->second.find(rule_.rule_id);
  if (tags_list != tags_map->second.end()) {
    for (const auto tag_value : tags_list->second) {
      tag_cnt_[tag_value]++;
    }
  }
  if (slot_id + 1 < rule_.window_size || rule_.window_type != WINDOW_TYPE_SLIDE) return;
  //  针对已滑出窗口的候选项，更新规则状态
  int out_id = ranked_key[slot_id + 1 - rule_.window_size];
  //  跨屏视频没有的规则跳过计数统计
  if (slot_id + 1 - rule_.window_size < session_offset && !rule_.consider_prev_items) return;
  auto out_tags_map = item_tags_map.find(out_id);
  if (out_tags_map == item_tags_map.end()) return;
  auto out_tags_list = out_tags_map->second.find(rule_.rule_id);
  if (out_tags_list != out_tags_map->second.end()) {
    for (const auto tag_value : out_tags_list->second) {
      auto it = tag_cnt_.find(tag_value);
      if (it != tag_cnt_.end()) {
        it->second -= 1;
      }
    }
  }
}

bool CommonRecoDiversityRulesArranger::InitRules(ReadableRecoContextInterface *context) {
  int valid_rule_cnt = 0;  // 统计合法且生效的规则数量
  int all_rule_cnt = 0;    // 为规则赋值 rule_id，从 1 开始
  priority_rule_validators_.clear();
  priority_rule_validators_.resize(priority_num_);  // 规则有 priority_num_ 个优先级
  priority_results_.resize(2 * priority_num_ + 1);  // 候选项有 2 * priority_num_ + 1 个优先级
  for (auto &results : priority_results_) results.clear();
  rules_config_ = std::make_unique<base::Json>(base::StringToJson(raw_rules_config_->ToString()));
  for (auto *processed_rule_conf : rules_config_->array()) {
    SetDynamicRules(processed_rule_conf, ++all_rule_cnt, context);
    RuleValidator rule(*processed_rule_conf);
    if (rule.CheckConfig(priority_num_)) {
      if (rule.rule().enabled) {
        valid_rule_cnt++;
        priority_rule_validators_[rule.rule().priority].push_back(rule);
      }
    } else {
      CL_LOG_ERROR("diversify", "rule_check_failed")
          << " rule ignored: illegal rule! rule NO." << rule.rule().rule_id
          << " config error. attr_name = " << rule.rule().attr_name << ", priority = " << rule.rule().priority
          << ", max_num = " << rule.rule().max_num << ", min_num = " << rule.rule().min_num
          << ", window_size = " << rule.rule().window_size;
    }
  }

  if (valid_rule_cnt < 1) {
    return false;
  }
  perflog_rule_list_.clear();
  perflog_rule_list_.resize(all_rule_cnt + 1);
  item_tags_map_.clear();
  ranked_key_.clear();
  return true;
}

void CommonRecoDiversityRulesArranger::SetDynamicRules(base::Json *processed_rule_conf, int rule_id,
                                                       ReadableRecoContextInterface *context) {
  processed_rule_conf->set("rule_id", rule_id);
  processed_rule_conf->set("enabled",
                           GetBoolProcessorParameter(context, processed_rule_conf->Get("enabled"), true));
  processed_rule_conf->set(
      "consider_prev_items",
      GetBoolProcessorParameter(context, processed_rule_conf->Get("consider_prev_items"), false));
  processed_rule_conf->set("window_size", GetIntProcessorParameter(
                                              context, processed_rule_conf->Get("window_size"), (int64)0));
  processed_rule_conf->set(
      "max_num", GetIntProcessorParameter(context, processed_rule_conf->Get("max_num"), INT_MAX));
  processed_rule_conf->set(
      "min_num", GetIntProcessorParameter(context, processed_rule_conf->Get("min_num"), (int64)0));
  processed_rule_conf->set("priority",
                           GetIntProcessorParameter(context, processed_rule_conf->Get("priority"), -1));
}

void CommonRecoDiversityRulesArranger::GenDiversityTag(
    MutableRecoContextInterface *context, const CommonRecoResult &result,
    const folly::F14FastMap<std::string, ItemAttr *> &attr_accessor_map, int idx, bool session_flag) {
  folly::F14FastMap<int, std::vector<int64>> rule_tags_map;
  for (auto &rule_validators : priority_rule_validators_) {
    for (auto &rule_validator : rule_validators) {
      // session content skip attr not consider_prev_items
      const auto &rule_id = rule_validator.rule().rule_id;
      if (session_flag && !rule_validator.rule().consider_prev_items) {
        continue;
      }
      const auto &attr_name = rule_validator.rule().attr_name;
      auto attr_accessor_iter = attr_accessor_map.find(attr_name);
      if (attr_accessor_iter != attr_accessor_map.end()) {
        if (auto int_val = result.GetIntAttr(attr_accessor_iter->second)) {
          rule_tags_map.insert(std::make_pair(rule_id, std::vector<int64>(1, *int_val)));
        } else if (auto int_list_val = result.GetIntListAttr(attr_accessor_iter->second)) {
          rule_tags_map.insert(
              std::make_pair(rule_id, std::vector<int64>(int_list_val->begin(), int_list_val->end())));
        } else if (auto str_val = result.GetStringAttr(attr_accessor_iter->second)) {
          int64 str_tag = base::CityHash64(str_val->data(), str_val->size());
          rule_tags_map.insert(std::make_pair(rule_id, std::vector<int64>(1, str_tag)));
        } else if (auto string_list_val = result.GetStringListAttr(attr_accessor_iter->second)) {
          std::vector<int64> str_tags;
          for (auto str_view : *string_list_val) {
            int64 str_tag = base::CityHash64(str_view.data(), str_view.size());
            str_tags.emplace_back(str_tag);
          }
          rule_tags_map.insert(std::make_pair(rule_id, str_tags));
        }
      }
    }
  }
  item_tags_map_.insert(std::make_pair(idx, rule_tags_map));
}

void CommonRecoDiversityRulesArranger::ValidateRules(int slot_id) {
  for (auto &results : priority_results_) results.clear();
  for (int i = slot_id; i < ranked_key_.size(); i++) {
    int cur_item = ranked_key_[i];
    bool hit = false;
    for (auto rule_lists = priority_rule_validators_.rbegin(); rule_lists != priority_rule_validators_.rend();
         ++rule_lists) {
      for (auto &rule_validator : *rule_lists) {
        auto &rule = rule_validator.rule();
        RatioStatus status = rule_validator.Validate(item_tags_map_[cur_item], slot_id);
        int priority = priority_num_;
        if (status == RATIO_UNDERFLOW) {
          priority = priority_num_ + 1 + rule.priority;  // 命中强插规则
          if (rule.rule_id < perflog_rule_list_.size()) {
            perflog_rule_list_[rule.rule_id].second++;  // 记录强插规则 rule id
          }
        } else if (status == RATIO_OVERFLOW) {
          priority = priority_num_ - 1 - rule.priority;  // 命中打散规则
          if (rule.rule_id < perflog_rule_list_.size()) {
            perflog_rule_list_[rule.rule_id].second++;  // 记录打散规则 rule id
          }
        }
        if (priority != priority_num_) {
          priority_results_[priority].push_back(i);
          hit = true;
          break;
        }
      }
      if (hit) break;
    }
    if (!hit) {  // 无规则命中
      priority_results_[priority_num_].push_back(i);
    }
  }
}

int CommonRecoDiversityRulesArranger::RankOneSlot() {
  int selected_idx = -1;
  // 按优先级从高到低的顺序选取候选项
  for (auto candidates = priority_results_.rbegin(); candidates != priority_results_.rend(); ++candidates) {
    if (candidates->empty()) {
      continue;
    }
    int min_oindex = INT_MAX;
    // 最高优先级下有多个候选项时，按照进入规则引擎时的原顺序选取
    for (int j : *candidates) {
      if (ranked_key_[j] < min_oindex) {
        selected_idx = j;
        min_oindex = ranked_key_[j];
      }
    }
    break;
  }
  return selected_idx;
}

void CommonRecoDiversityRulesArranger::UpdateRuleStats(int slot_id, const int session_offset,
                                                       bool session_flag = false) {
  for (auto &rule_validators : priority_rule_validators_) {
    for (auto &rule_validator : rule_validators) {
      if (session_flag && !rule_validator.rule().consider_prev_items) continue;
      rule_validator.UpdateStats(ranked_key_, item_tags_map_, slot_id, session_flag, session_offset);
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoDiversityRulesArranger, CommonRecoDiversityRulesArranger);

}  // namespace platform
}  // namespace ks
