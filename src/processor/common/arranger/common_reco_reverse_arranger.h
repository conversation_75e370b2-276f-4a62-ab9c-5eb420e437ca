#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoItemReverseArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoItemReverseArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemReverseArranger);
};

}  // namespace platform
}  // namespace ks
