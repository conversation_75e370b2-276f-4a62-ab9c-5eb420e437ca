#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoReasonLimitArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoReasonLimitArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  struct TruncateConfig {
    const base::Json *ratio_cfg = nullptr;
    const base::Json *limit_cfg = nullptr;
    const base::Json *min_survival_cfg = nullptr;
    double ratio = -1.0;
    int64 limit = -1;
    int64 min_survival = 0;
  };

  bool InitProcessor() override {
    reason_config_.resize(2);
    reason_config_[0] = std::make_shared<std::unordered_map<int64, std::shared_ptr<TruncateConfig>>>();
    reason_config_[1] = std::make_shared<std::unordered_map<int64, std::shared_ptr<TruncateConfig>>>();

    if (const auto *default_reason_config = config()->Get("default_reason_config")) {
      default_reason_config_ = std::make_unique<TruncateConfig>();
      default_reason_config_->limit_cfg = default_reason_config->Get("limit");
      default_reason_config_->ratio_cfg = default_reason_config->Get("ratio");
      default_reason_config_->min_survival_cfg = default_reason_config->Get("min_survival");
    }

    if (const auto *reason_config = config()->Get("reason_config")) {
      if (reason_config->IsArray()) {
        for (const auto *attr_json : reason_config->array()) {
          if (!attr_json->IsObject()) {
            LOG(ERROR) << "CommonRecoReasonLimitArranger init failed! Item of reason_config must be a dict!"
                       << " Value found: " << attr_json->ToString();
            return false;
          }
          auto truncate_config = std::make_shared<TruncateConfig>();
          truncate_config->limit_cfg = attr_json->Get("limit");
          truncate_config->ratio_cfg = attr_json->Get("ratio");
          truncate_config->min_survival_cfg = attr_json->Get("min_survival");

          const auto *reason_json = attr_json->Get("reason");
          if (!reason_json) {
            LOG(ERROR) << "CommonRecoReasonLimitArranger init failed! Missing 'reason' field!"
                       << " Value found: " << attr_json->ToString();
            return false;
          }

          int64 reason = 0;
          auto current_reason_config = reason_config_[current_index_];
          if (reason_json->IntValue(&reason)) {
            (*current_reason_config)[reason] = truncate_config;
          } else if (reason_json->IsArray()) {
            for (const auto *j : reason_json->array()) {
              if (j->IntValue(&reason)) {
                (*current_reason_config)[reason] = truncate_config;
              }
            }
          } else if (RecoUtil::IsDynamicParameter(reason_json->StringValue())) {
            reason_config_dynamic_reason_.push_back({reason_json, truncate_config});
          } else {
            LOG(ERROR) << "CommonRecoReasonLimitArranger init failed! 'reason' field must be int/int_list!"
                       << " Value found: " << attr_json->ToString();
            return false;
          }
        }
      } else if (reason_config->IsString()) {
        std::string kconf_key = reason_config->StringValue();
        Json::Value null_json;
        ks::infra::OnContentChange<Json::Value> on_change = [&](const Json::Value &value) {
          if (!value.isArray()) return;
          auto new_config = std::make_shared<std::unordered_map<int64, std::shared_ptr<TruncateConfig>>>();
          for (const auto &val : value) {
            auto truncate_config = std::make_shared<TruncateConfig>();
            if (val["limit"].isIntegral()) {
              truncate_config->limit = val["limit"].asInt64();
            }
            if (val["ratio"].isDouble()) {
              truncate_config->ratio = val["ratio"].asDouble();
            }
            if (val["min_survival"].isIntegral()) {
              truncate_config->min_survival = val["min_survival"].asInt64();
            }
            if (val["reason"].isIntegral()) {
              (*new_config)[val["reason"].asInt64()] = truncate_config;
            } else if (val["reason"].isArray()) {
              for (const auto &v : val["reason"]) {
                if (v.isIntegral()) {
                  (*new_config)[v.asInt64()] = truncate_config;
                }
              }
            }
          }
          int next_index = (current_index_ + 1) % 2;
          reason_config_[next_index] = new_config;
          current_index_ = next_index;
        };
        ks::infra::KConf().SetWatch(kconf_key, null_json, on_change);
      } else {
        LOG(ERROR) << "CommonRecoReasonLimitArranger init failed!"
                   << " 'reason_config' should be a list or string!";
        return false;
      }
    }

    return true;
  }

 private:
  std::unique_ptr<TruncateConfig> default_reason_config_;
  std::vector<std::shared_ptr<std::unordered_map<int64, std::shared_ptr<TruncateConfig>>>> reason_config_;
  std::atomic_int current_index_{0};
  std::vector<std::pair<const base::Json *, std::shared_ptr<TruncateConfig>>> reason_config_dynamic_reason_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoReasonLimitArranger);
};

}  // namespace platform
}  // namespace ks
