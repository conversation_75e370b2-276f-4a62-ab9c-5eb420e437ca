#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoBrowseSetFilterArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoBrowseSetFilterArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    save_filtered_items_to_common_attr_ = config()->GetString("save_filtered_items_to_common_attr", "");
    check_id_in_attr_ = config()->GetString("check_id_in_attr", "");
    item_type_of_checked_id_ = config()->GetInt("item_type_of_checked_id", -1);
    need_save_filtered_items_ = !save_filtered_items_to_common_attr_.empty();
    return true;
  }

  bool IsInBrowseSet(MutableRecoContextInterface *context, ItemAttr *id_in_attr,
                     const CommonRecoResult &result) const;

 private:
  std::string save_filtered_items_to_common_attr_ = "";
  std::string check_id_in_attr_;
  int item_type_of_checked_id_ = -1;
  bool need_save_filtered_items_ = false;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBrowseSetFilterArranger);
};

}  // namespace platform
}  // namespace ks
