#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "folly/container/F14Map.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoDiversityRulesArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoDiversityRulesArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    raw_rules_config_ = config()->Get("rules");
    if (!raw_rules_config_ || !raw_rules_config_->IsArray()) {
      LOG(ERROR) << "CommonRecoDiversityRulesArranger init failed! Missing 'rules' or it is not an array!";
      return false;
    }

    raw_slot_num_config_ = config()->Get("max_satisfied_pick");
    if (!raw_slot_num_config_) {
      LOG(ERROR) << "CommonRecoDiversityRulesArranger init failed! Missing 'max_satisfied_pick'!";
      return false;
    }

    priority_num_ = config()->GetInt("top_priority", 9) + 1;
    if (priority_num_ < 1) {
      LOG(ERROR) << "CommonRecoDiversityRulesArranger init failed! 'top_priority' should be >= 0!";
      return false;
    }
    // 动态参数配置
    raw_perflog_enabled_ = config()->Get("perflog_enabled");
    prev_items_from_attr_ = config()->GetString("prev_items_from_attr", "");
    return true;
  }

  bool InitRules(ReadableRecoContextInterface *context);
  void SetDynamicRules(base::Json *rule_json, int rule_id, ReadableRecoContextInterface *context);
  void GenDiversityTag(MutableRecoContextInterface *context, const CommonRecoResult &result,
                       const folly::F14FastMap<std::string, ItemAttr *> &attr_accessor_map, int idx,
                       bool session_flag);
  void ValidateRules(int slot_id);
  int RankOneSlot();

  class RuleValidator;

  int slot_num_;
  int priority_num_;
  bool perflog_enabled_;
  base::Json *raw_rules_config_;
  std::unique_ptr<base::Json> rules_config_;
  base::Json *raw_slot_num_config_;
  base::Json *raw_perflog_enabled_;

  std::vector<std::vector<RuleValidator>> priority_rule_validators_;
  std::vector<std::vector<int>> priority_results_;
  std::vector<int> ranked_key_;
  folly::F14FastMap<int, folly::F14FastMap<int, std::vector<int64>>> item_tags_map_;

  enum WindowType { WINDOW_TYPE_SLIDE, WINDOW_TYPE_TOP };
  enum RatioStatus { RATIO_NORMAL, RATIO_UNDERFLOW, RATIO_OVERFLOW };

  struct BaseRule {
    BaseRule(int rule_id_, bool enabled_) : rule_id(rule_id_), enabled(enabled_) {}
    BaseRule() {}
    int rule_id;
    bool enabled;
    bool consider_prev_items;
  };

  struct RatioRule : BaseRule {
    RatioRule() {}
    explicit RatioRule(const base::Json &kv);
    int priority;
    WindowType window_type;
    int window_size;
    std::string attr_name;
    int min_num;
    int max_num;
  };

  // 跨屏打散 items 取的 common attr name，存的是 int list
  std::string prev_items_from_attr_;
  // 打散生效规则计数 (attr_name, cnt) pair
  std::vector<std::pair<std::string, int>> perflog_rule_list_;

  // 获取跨屏列表数目
  void UpdateSessionRules(const int session_offset);
  // 跨屏信息更新
  void UpdateRuleStats(int slot_id, const int session_offset, bool session_flag);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoDiversityRulesArranger);
};

class CommonRecoDiversityRulesArranger::RuleValidator {
 public:
  explicit RuleValidator(const base::Json &kv) : rule_(kv) {
    tag_cnt_.clear();
  }
  RuleValidator(const RuleValidator &rule);
  RatioStatus Validate(const folly::F14FastMap<int, std::vector<int64>> &diversity_tags, int slot_id);
  void UpdateStats(const std::vector<int> &ranked_key,
                   const folly::F14FastMap<int, folly::F14FastMap<int, std::vector<int64>>> &item_tags_map,
                   int slot_id, bool session_flag, const int session_offset);
  bool CheckConfig(int priority_num);
  RatioRule &rule() {
    return rule_;
  }

 private:
  RatioRule rule_;
  folly::F14FastMap<int64, int> tag_cnt_;
};

}  // namespace platform
}  // namespace ks
