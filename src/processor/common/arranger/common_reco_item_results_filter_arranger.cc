#include "dragon/src/processor/common/arranger/common_reco_item_results_filter_arranger.h"

#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoItemResultsFilterArranger::Arrange(MutableRecoContextInterface *context,
                                                            RecoResultIter begin, RecoResultIter end) {
  CommonRecoResultSet retained_items = GetRetainedItems(context);
  auto new_end = std::remove_if(begin, end, [&retained_items](const CommonRecoResult &result) {
    return retained_items.count(result) == 0;
  });
  return new_end;
}

CommonRecoResultSet CommonRecoItemResultsFilterArranger::GetRetainedItems(
    MutableRecoContextInterface *context) {
  CommonRecoResultSet retained_items;
  for (const auto &result_list_name : remove_if_not_in_) {
    const auto *result_list_ptr =
        context->GetPtrCommonAttr<const std::vector<CommonRecoResult>>(result_list_name);
    if (result_list_ptr) {
      retained_items.insert(result_list_ptr->begin(), result_list_ptr->end());
    }
  }
  return retained_items;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemResultsFilterArranger, CommonRecoItemResultsFilterArranger)

}  // namespace platform
}  // namespace ks
