#pragma once

#include <string>

#include "base/random/pseudo_random.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoShuffleArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoShuffleArranger() : random_(base::GetTimestamp()) {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 protected:
  void UniformShuffle(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end);

  void WeightedShuffle(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end);

 private:
  bool InitProcessor() override {
    weight_attr_name_ = config()->GetString("weight_attr", "");
    return true;
  }

  double GetItemWeight(ItemAttr *attr_accessor, const CommonRecoResult &result);

 private:
  std::string weight_attr_name_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoShuffleArranger);
};

}  // namespace platform
}  // namespace ks
