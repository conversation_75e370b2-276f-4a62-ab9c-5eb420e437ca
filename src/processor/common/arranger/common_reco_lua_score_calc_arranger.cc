#include "dragon/src/processor/common/arranger/common_reco_lua_score_calc_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoLuaScoreCalcArranger::Arrange(MutableRecoContextInterface *context,
                                                       RecoResultIter begin, RecoResultIter end) {
  std::string score_function = GetStringProcessorParameter(context, "score_function", "calculate");
  lua_getglobal(lua_state_, score_function.data());
  bool is_function = lua_isfunction(lua_state_, -1);
  lua_pop(lua_state_, 1);
  if (!is_function) {
    CL_LOG_ERROR_EVERY("lua_score_calc", "not_lua_function:" + score_function, 1000)
        << "lua score calc cancelled: '" << score_function << "' is not a function in lua_script!"
        << RecoUtil::GetRequestInfoForLog(context);
    return end;
  }

  std::for_each(begin, end, [this, context, &score_function](CommonRecoResult &result) {
    lua_newtable(lua_state_);
    for (const auto &pr : args_) {
      AttrValue value;
      GetArgumentValue(context, pr.second, result.item_key, &value);
      switch (value.value_type) {
        case AttrType::INT:
          lua_pushinteger(lua_state_, value.int_value);
          break;
        case AttrType::STRING:
          if (value.string_value) {
            lua_pushlstring(lua_state_, value.string_value->data(), value.string_value->size());
          } else {
            lua_pushstring(lua_state_, "");
          }
          break;
        default:
          lua_pushnumber(lua_state_, value.double_value);
          break;
      }
      lua_setfield(lua_state_, -2, pr.first.data());
    }
    lua_setglobal(lua_state_, "D");

    lua_getglobal(lua_state_, score_function.data());
    int err_code = lua_pcall(lua_state_, 0, 1, 0);
    if (err_code) {
      CL_LOG_ERROR_EVERY("lua_score_calc", "lua_pcall_error:" + base::IntToString(err_code), 10000)
          << "failed to execute lua_pcall, error code: " << err_code
          << RecoUtil::GetRequestInfoForLog(context);
      return;
    }

    if (lua_isnumber(lua_state_, -1)) {
      result.score = lua_tonumber(lua_state_, -1);
    } else {
      CL_LOG_ERROR_EVERY("lua_score_calc", "no_number_return", 10000)
          << "failed to calc score, function 'calculate' didn't return a number!"
          << RecoUtil::GetRequestInfoForLog(context);
    }
    lua_pop(lua_state_, 1);
  });

  return end;
}

CommonRecoLuaScoreCalcArranger::Argument CommonRecoLuaScoreCalcArranger::ParseArgument(
    const std::string &input) {
  Argument arg;
  // 兼容一下老格式中的 , 分隔符
  std::string delimiter = input.find(",") != std::string::npos ? "," : "|";
  std::vector<std::string> attr_names;
  base::SplitStringWithOptions(input, delimiter, true, true, &attr_names);
  static const std::string kItemAttrPrefix = "item/";
  static const std::string kCommonAttrPrefix = "user/";
  for (const auto &name : attr_names) {
    Attr attr;
    if (base::StartsWith(name, kItemAttrPrefix, true)) {
      attr.attr_name = name.substr(kItemAttrPrefix.size());
      attr.attr_source = AttrSource::ITEM;
    } else if (base::StartsWith(name, kCommonAttrPrefix, true)) {
      attr.attr_name = name.substr(kCommonAttrPrefix.size());
      attr.attr_source = AttrSource::COMMON;
    } else {
      attr.attr_name = name;
      attr.attr_source = AttrSource::UNKNOWN;
    }
    arg.attrs.push_back(std::move(attr));
  }
  if (!arg.attrs.empty() && absl::SimpleAtod(arg.attrs.back().attr_name, &arg.default_value)) {
    arg.attrs.pop_back();
  }
  return std::move(arg);
}

void CommonRecoLuaScoreCalcArranger::GetArgumentValue(ReadableRecoContextInterface *context,
                                                      const Argument &arg, uint64 item_key, AttrValue *val) {
  for (const auto &attr : arg.attrs) {
    if (attr.attr_source == AttrSource::ITEM) {
      if (GetValueFromItemAttr(context, attr.attr_name, item_key, val)) {
        return;
      }
    } else if (attr.attr_source == AttrSource::COMMON) {
      if (GetValueFromCommonAttr(context, attr.attr_name, item_key, val)) {
        return;
      }
    } else {
      // 如果未指定 item_attr 或 common_attr 则都进行尝试获取
      if (GetValueFromItemAttr(context, attr.attr_name, item_key, val)) {
        return;
      }
      if (GetValueFromCommonAttr(context, attr.attr_name, item_key, val)) {
        return;
      }
    }
  }
  val->value_type = AttrType::FLOAT;
  val->double_value = arg.default_value;
}

bool CommonRecoLuaScoreCalcArranger::GetValueFromItemAttr(ReadableRecoContextInterface *context,
                                                          const std::string &attr_name, uint64 item_key,
                                                          AttrValue *val) {
  if (context->HasItemAttr(attr_name)) {
    if (context->GetDoubleItemAttr(item_key, attr_name)) {
      val->value_type = AttrType::FLOAT;
      val->double_value = *context->GetDoubleItemAttr(item_key, attr_name);
      return true;
    }
    if (context->GetIntItemAttr(item_key, attr_name)) {
      val->value_type = AttrType::INT;
      val->int_value = *context->GetIntItemAttr(item_key, attr_name);
      return true;
    }
    if (context->GetStringItemAttr(item_key, attr_name)) {
      val->value_type = AttrType::STRING;
      val->string_value = context->GetStringItemAttr(item_key, attr_name);
      return true;
    }
  }
  return false;
}

bool CommonRecoLuaScoreCalcArranger::GetValueFromCommonAttr(ReadableRecoContextInterface *context,
                                                            const std::string &attr_name, uint64 item_key,
                                                            AttrValue *val) {
  if (context->HasCommonAttr(attr_name)) {
    if (context->GetDoubleCommonAttr(attr_name)) {
      val->value_type = AttrType::FLOAT;
      val->double_value = *context->GetDoubleCommonAttr(attr_name);
      return true;
    }
    if (context->GetIntCommonAttr(attr_name)) {
      val->value_type = AttrType::INT;
      val->int_value = *context->GetIntCommonAttr(attr_name);
      return true;
    }
    if (context->GetStringCommonAttr(attr_name)) {
      val->value_type = AttrType::STRING;
      val->string_value = context->GetStringCommonAttr(attr_name);
      return true;
    }
  }
  return false;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoLuaScoreCalcArranger, CommonRecoLuaScoreCalcArranger)

}  // namespace platform
}  // namespace ks
