#pragma once
#include <string>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoReasonTruncateArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoReasonTruncateArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  struct QueueTruncateConfig {
    double ratio;
    int32 limit;
  };

  bool InitProcessor() override {
    // reason config array
    auto *reason_config = config()->Get("reason_config");
    reason_survival_map_.clear();
    if (reason_config) {
      if (!reason_config->IsArray() || reason_config->array().empty()) {
        LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                   << " init failed! reason_config should be a array!";
        return false;
      }

      for (const auto *attr_json : reason_config->array()) {
        if (!attr_json->IsObject()) {
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Item of reason_config should be a dict!"
                     << " Value found: " << attr_json->ToString();
          return false;
        }

        int64 reason;
        if (!attr_json->GetInt("reason", &reason)) {
          std::string value_str = attr_json->Get("reason") ? attr_json->Get("reason")->ToString() : "null";
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Attr reason should be a int!"
                     << " Value found: " << value_str;
          return false;
        }

        reason_survival_map_[reason] = attr_json->Get("min_survival");
      }
    }

    // queues array
    auto *queues = config()->Get("queues");
    reason_queues_map_.clear();
    if (queues) {
      if (!queues->IsArray() || queues->array().empty()) {
        LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                   << " init failed! queues should be a array!";
        return false;
      }

      for (const auto *attr_json : queues->array()) {
        if (!attr_json->IsObject()) {
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Item of queues should be a dict!"
                     << " Value found: " << attr_json->ToString();
          return false;
        }
        int64 reason, limit;
        double ratio;
        if (!attr_json->GetInt("reason", &reason)) {
          std::string value_str = attr_json->Get("reason") ? attr_json->Get("reason")->ToString() : "null";
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Attr reason should be a int!"
                     << " Value found: " << value_str;
          return false;
        }
        if (!attr_json->GetInt("limit", &limit)) {
          std::string value_str = attr_json->Get("limit") ? attr_json->Get("limit")->ToString() : "null";
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Attr limit should be a int!"
                     << " Value found: " << value_str;
          return false;
        }
        if (!attr_json->GetNumber("ratio", &ratio)) {
          std::string value_str = attr_json->Get("ratio") ? attr_json->Get("ratio")->ToString() : "null";
          LOG(ERROR) << "CommonRecoReasonTruncateArranger"
                     << " init failed! Attr ratio should be an double!"
                     << " Value found: " << value_str;
          return false;
        }

        if (reason >= 0 && limit >= 0 && ratio >= 0) {
          reason_queues_map_[reason].limit = limit;
          reason_queues_map_[reason].ratio = ratio;
        } else {
          LOG(ERROR) << "CommonRecoReasonTruncateArranger init failed! "
                     << "'reason', 'limit' and 'ratio' should be all >= 0";
          return false;
        }
      }
    }
    return true;
  }

 private:
  std::unordered_map<int64, QueueTruncateConfig> reason_queues_map_;
  // key 是 reason，value 是这个 reason 的 min_survival
  std::unordered_map<int64, const base::Json *> reason_survival_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoReasonTruncateArranger);
};

}  // namespace platform
}  // namespace ks
