#pragma once

#include <utility>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoPartitionArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoPartitionArranger() = default;

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  std::vector<std::pair<ItemAttr *, absl::flat_hash_set<int64>>> attr_mapping_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoPartitionArranger);
};

}  // namespace platform
}  // namespace ks
