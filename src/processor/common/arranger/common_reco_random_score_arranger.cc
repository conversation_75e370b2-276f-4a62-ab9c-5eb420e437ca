#include "dragon/src/processor/common/arranger/common_reco_random_score_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoRandomScoreArranger::Arrange(MutableRecoContextInterface *context,
                                                      RecoResultIter begin, RecoResultIter end) {
  std::for_each(begin, end, [this, context](CommonRecoResult &result) {
    result.score = lower_bound_ + random_.GetDouble() * (upper_bound_ - lower_bound_);
  });
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRandomScoreArranger, CommonRecoRandomScoreArranger)

}  // namespace platform
}  // namespace ks
