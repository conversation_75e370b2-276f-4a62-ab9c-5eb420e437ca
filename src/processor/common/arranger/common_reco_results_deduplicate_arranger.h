#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "folly/container/F14Map.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

enum class ReasonPriorityMode {
  UNKNOWN = 0,
  // 先召回的优先级更高
  FIRST = 1,
  // reason 数值大的优先级更高
  GREATER = 2,
  // reason 数值小的优先级更高
  LESS = 3,
};

class CommonRecoResultsDeduplicateArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoResultsDeduplicateArranger() {
    reason_priority_mode_map_.insert(std::make_pair("first", ReasonPriorityMode::FIRST));
    reason_priority_mode_map_.insert(std::make_pair("greater", ReasonPriorityMode::GREATER));
    reason_priority_mode_map_.insert(std::make_pair("less", ReasonPriorityMode::LESS));
  }

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    save_dup_count_to_ = config()->GetString("save_dup_count_to");
    append_reason_to_ = config()->GetString("append_reason_to");
    append_order_to_ = config()->GetString("append_order_to");
    append_reason_order_to_ = config()->GetString("append_reason_order_to");
    on_item_attr_ = config()->GetString("on_item_attr");

    std::string reason_priority_mode = config()->GetString("reason_priority_mode", "first");
    auto it = reason_priority_mode_map_.find(reason_priority_mode);
    if (it == reason_priority_mode_map_.end()) {
      LOG(ERROR) << "CommonRecoResultsDeduplicateArranger init failed: reason_priority_mode must be "
                    "first/greater/less.";
      return false;
    }
    reason_priority_mode_ = it->second;

    std::vector<int64> priority_list;
    RecoUtil::ExtractIntListFromJsonConfig(config()->Get("reason_priority_list"), &priority_list);

    std::vector<int64> lower_priority_list;
    RecoUtil::ExtractIntListFromJsonConfig(config()->Get("lower_reason_priority_list"), &lower_priority_list);

    // 0 是留给没有配置的 reason 默认的优先级，所以优先级列表中不能有 0
    priority_map_.clear();
    int priority = priority_list.size();
    for (int64 &reason : priority_list) {
      priority_map_.insert(std::make_pair(reason, priority));
      priority--;
    }
    // 反向优先级
    priority = -1;
    for (int64 &reason : lower_priority_list) {
      priority_map_.insert(std::make_pair(reason, priority));
      priority--;
    }
    return true;
  }

  bool GetDedupKey(const CommonRecoResult &result, uint64 *dedup_key);
  int GetDedupReason(int old_reason, int new_reason);
  void PerfItemReasonRepetition(MutableRecoContextInterface *context, RecoResultIter begin,
                                RecoResultIter end);

 private:
  folly::F14FastMap<uint64, int> priority_map_;
  ReasonPriorityMode reason_priority_mode_ = ReasonPriorityMode::UNKNOWN;
  std::unordered_map<std::string, ReasonPriorityMode> reason_priority_mode_map_;
  std::string save_dup_count_to_;
  std::string append_reason_to_;
  std::string append_order_to_;
  std::string append_reason_order_to_;
  std::string on_item_attr_;
  ItemAttr *dedup_attr_accessor_ = nullptr;
  ItemAttr *append_reason_attr_accessor_ = nullptr;
  ItemAttr *append_order_attr_accessor_ = nullptr;
  ItemAttr *append_reason_order_attr_accessor_ = nullptr;
  ItemAttr *save_dup_count_attr_accessor_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoResultsDeduplicateArranger);
};

}  // namespace platform
}  // namespace ks
