#include "dragon/src/processor/common/arranger/common_reco_reason_truncate_arranger.h"
#include "dragon/src/core/common_reco_util.h"

#include <algorithm>
#include <unordered_map>

namespace ks {
namespace platform {

RecoResultIter CommonRecoReasonTruncateArranger::Arrange(MutableRecoContextInterface *context,
                                                         RecoResultIter begin, RecoResultIter end) {
  int num_items = std::distance(begin, end);

  int64 size_limit = GetIntProcessorParameter(context, "size_limit", -1);
  if (size_limit >= num_items || size_limit < 0) {
    return end;
  }
  // 全局的缩放比例
  double scale_ratio = (double)size_limit / num_items;
  // 统计每个 reason 的数目
  std::unordered_map<int, int> reason_counts;
  std::for_each(begin, end, [context, &reason_counts](const CommonRecoResult &result) {
    ++reason_counts[result.reason];
  });

  // min survial 是可选配置
  int64 default_min_survival = GetIntProcessorParameter(context, "min_survival", 1);

  // 设定每个 reason 的 remain size
  for (const auto &pair : reason_counts) {
    auto reason = pair.first;
    auto item_num = pair.second;

    // 默认会以全局配置缩放比例为准
    int64 remain_size = item_num * scale_ratio;

    if (!reason_queues_map_.empty()) {
      // 模式 1：如果 reason 做了单独配置，则覆盖全局的 truncate size
      const auto it = reason_queues_map_.find(reason);
      if (it != reason_queues_map_.end()) {
        remain_size = std::min(it->second.limit, (int32)(item_num * it->second.ratio));
      }
    } else {
      // 模式 2：如果配置了 min_survival 则会进行保量
      int64 min_survival = default_min_survival;
      const auto it = reason_survival_map_.find(reason);
      if (it != reason_survival_map_.end()) {
        // reason 的保量会覆盖全局默认保量
        min_survival = GetIntProcessorParameter(context, it->second, default_min_survival);
      }
      remain_size = std::max(remain_size, min_survival);
    }

    // 更新每个 reason 的数目为要保量留下的数目
    reason_counts[reason] = remain_size;
  }

  auto new_end = std::remove_if(begin, end, [&reason_counts](const CommonRecoResult &result) {
    if (reason_counts[result.reason] > 0) {
      reason_counts[result.reason]--;
      return false;
    } else {
      return true;
    }
  });
  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "reason_truncate_count",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoReasonTruncateArranger, CommonRecoReasonTruncateArranger)

}  // namespace platform
}  // namespace ks
