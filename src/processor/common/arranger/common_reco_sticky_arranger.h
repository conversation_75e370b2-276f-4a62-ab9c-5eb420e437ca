#pragma once

#include <string>
#include <unordered_set>
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoStickyArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoStickyArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 protected:
  bool InitProcessor() override {
    reset_reason_ = config()->GetInt("reset_reason", -1);
    position_from_attr_ = config()->GetString("position_from_attr");
    return true;
  }

  void SortByPosition(MutableRecoContextInterface *, RecoResultIter, RecoResultIter) const;
  int GetStickyPosition(const CommonRecoResult &result, int position,
                        const std::unordered_set<int> &sticky_reasons,
                        ItemAttr *position_attr_accessor) const;

 private:
  int reset_reason_ = -1;
  std::string position_from_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStickyArranger);
};

}  // namespace platform
}  // namespace ks
