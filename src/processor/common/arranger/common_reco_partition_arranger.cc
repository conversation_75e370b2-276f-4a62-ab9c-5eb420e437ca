#include "dragon/src/processor/common/arranger/common_reco_partition_arranger.h"

#include <algorithm>
#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoPartitionArranger::Arrange(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end) {
  attr_mapping_ = GenTargetItemAttrMapping(context, config()->Get("target"));
  if (attr_mapping_.empty()) {
    CL_LOG(WARNING) << "partition cancelled: no targets found";
    return end;
  }

  auto mid = std::stable_partition(begin, end, [this, context](const CommonRecoResult &result) -> bool {
    return IsTargetItem(context, attr_mapping_, result);
  });

  CL_LOG(INFO) << "partitioned " << std::distance(begin, mid) << " items to the front";

  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoPartitionArranger, CommonRecoPartitionArranger)

}  // namespace platform
}  // namespace ks
