#pragma once

#include <algorithm>
#include <string>
#include <vector>

#include "base/hash_function/city.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {
/**
 * NOTE(qingjun): 开发该 processor 请务必确认你已经看过文档:
 * https://docs.corp.kuaishou.com/d/home/<USER>
 * 请务必知道该 processor 必须需要业务方开发之后才能正常使用！！！！
 */
class CommonRecoMmrDiversityArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoMmrDiversityArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    return true;
  }

  // 计算最大相似度的函数，业务方可继承覆写自定义逻辑
  // 默认会依次让每个 item 都与已经选择的 item 进行相似度计算，然后取最大值
  virtual double CalcMaxSimScore(MutableRecoContextInterface *context, RecoResultIter begin,
                                 RecoResultIter end, const CommonRecoResult &item,
                                 const std::vector<CommonRecoResult> &final_results,
                                 folly::F14FastMap<uint64, double> *already_calc_sim);

  // 计算 itemA 和 itemB 之间的相似度函数，业务方必须继承覆写自定义逻辑
  // 相似度取值范围建议在 [0,1]，越大表示相似度越高
  virtual double CalcSimScore(MutableRecoContextInterface *context, const CommonRecoResult &itemA,
                              const CommonRecoResult &itemB) = 0;

  // 获取最基础的排序 score，业务方可继承覆写自定义逻辑
  // 默认使用 item 的 score 作为分数
  virtual double GetScore(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end,
                          const CommonRecoResult &item);

  // 初始化, 业务方可继承覆写自定义逻辑
  // 默认不做任何处理
  virtual void PrepareResultItems(MutableRecoContextInterface *context, RecoResultIter begin,
                                  RecoResultIter end);

  // 最终用来计算 MmrScore，业务方可继承覆写自定义逻辑
  // 默认公式为最基础的 Mmr 方程: lambda * score - (1 - lambda) * max_sim_score
  virtual double CalcMmrScore(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end,
                              const CommonRecoResult &item, double score, double max_sim_score);

  // 最终拿到 Mmr 候选后的 DebugInfo 处理，业务方可继承覆写自定义逻辑
  // 默认用来存储计算出来的 Mmr 分数
  virtual void DealBestItem(MutableRecoContextInterface *context, const CommonRecoResult &item, int index,
                            double best_score);

  DISALLOW_COPY_AND_ASSIGN(CommonRecoMmrDiversityArranger);
};

}  // namespace platform
}  // namespace ks
