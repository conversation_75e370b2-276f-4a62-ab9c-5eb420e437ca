#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "serving_base/retrieval/variant.h"

namespace ks {
namespace platform {

class CommonRecoVariantArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoVariantArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  struct VariantConfig {
    bool enabled = true;
    bool any_of = false;
  };

  bool InitProcessor() override {
    raw_variant_config_ = config()->Get("variant_config");
    if (!raw_variant_config_) {
      LOG(ERROR) << "CommonRecoVariantArranger init failed! Missing \"variant_config\"!";
      return false;
    }
    variant_config_ = std::make_unique<base::Json>(base::StringToJson(raw_variant_config_->ToString()));
    for (const auto &pr : variant_config_->objects()) {
      if (pr.second->IsObject()) {
        VariantConfig cfg;
        cfg.enabled = pr.second->GetBoolean("enabled", true);
        cfg.any_of = pr.second->GetBoolean("any_of", false);
        variant_attrs_[pr.first] = cfg;
      }
    }

    if (variant_attrs_.empty()) {
      LOG(ERROR) << "CommonRecoVariantArranger init failed!"
                 << " No variant attr found in \"variant_config\"";
      return false;
    }

    save_decay_score_to_attr_ = config()->GetString("save_decay_score_to_attr", "");
    init_decay_score_from_attr_ = config()->GetString("init_decay_score_from_attr", "");
    prev_items_from_attr_ = config()->GetString("prev_items_from_attr", "");
    default_init_decay_score_ = config()->GetNumber("default_init_decay_score", 1.0);

    return true;
  }

  bool SetIntVariantConfig(ReadableRecoContextInterface *context, const std::string &variant_attr_name,
                           const std::string &attr_name);

  bool SetDoubleVariantConfig(ReadableRecoContextInterface *context, const std::string &variant_attr_name,
                              const std::string &attr_name);

  bool InitializeVariantConfig(ReadableRecoContextInterface *context);

  void GenVariantTag(MutableRecoContextInterface *context, const CommonRecoResult &result, int item_index);

  double GetInitDecayScore(const CommonRecoResult &result, ItemAttr *attr_accessor);

 private:
  std::unique_ptr<base::Json> variant_config_;
  const base::Json *raw_variant_config_;
  std::string save_decay_score_to_attr_;
  std::string init_decay_score_from_attr_;
  std::string prev_items_from_attr_;
  double default_init_decay_score_ = 1.0;
  folly::F14FastMap<std::string, VariantConfig> variant_attrs_;

  base::Variant variant_;
  std::vector<base::Variant::Item> ordered_items_;
  std::vector<base::Variant::VariantItem> variant_items_;
  std::vector<CommonRecoResult> variant_results_;
  folly::F14FastMap<uint64, CommonRecoResult> results_map_;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoVariantArranger);
};

}  // namespace platform
}  // namespace ks
