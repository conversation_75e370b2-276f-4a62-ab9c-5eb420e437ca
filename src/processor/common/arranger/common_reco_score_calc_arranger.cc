#include "dragon/src/processor/common/arranger/common_reco_score_calc_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoScoreCalcArranger::Arrange(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end) {
  score_calc_handler_.ResetRecoContext(context);
  score_calc_handler_.CalcScoreForItems(begin, end);
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoScoreCalcArranger, CommonRecoScoreCalcArranger)

}  // namespace platform
}  // namespace ks
