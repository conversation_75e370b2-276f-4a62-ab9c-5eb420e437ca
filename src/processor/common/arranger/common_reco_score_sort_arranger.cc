#include "dragon/src/processor/common/arranger/common_reco_score_sort_arranger.h"

#include <algorithm>
#include <cstddef>
#include <iterator>

#include "absl/strings/str_cat.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoScoreSortArranger::Arrange(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end) {
  const int item_num = std::distance(begin, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(item_num, kPerfNs, "ranking_item_num",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  if (item_num <= 0) {
    CL_LOG_EVERY_N(INFO, 100) << "score sort cancelled: item_num=" << item_num;
    return end;
  }
  return Sort(context, begin, end);
}

const ItemAttr *CommonRecoScoreSortArranger::GetScoreAttr(MutableRecoContextInterface *context,
                                                          const std::string &score_from_attr) {
  auto *attr_accessor = context->GetItemAttrAccessor(score_from_attr);
  switch (attr_accessor->value_type) {
    case AttrType::INT:
    case AttrType::FLOAT:
    case AttrType::INT_LIST:
    case AttrType::FLOAT_LIST:
      return attr_accessor;
    case AttrType::UNKNOWN:
      CL_LOG_ERROR_EVERY("score_sort", "item_attr_not_found:" + score_from_attr, 100)
          << "score sort cancelled: item_attr " << score_from_attr << " doesn't exist,"
          << " processor: " << GetName() << RecoUtil::GetRequestInfoForLog(context);
      return nullptr;
    default: {
      auto type = RecoUtil::GetAttrTypeName(attr_accessor->value_type);
      std::string msg = "non_numeric_attr:" + score_from_attr + "(" + type + ")";
      CL_LOG_ERROR_EVERY("score_sort", msg, 100)
          << "score sort cancelled: non-numeric item_attr " << score_from_attr << ", attr type: " << type
          << ", processor: " << GetName() << RecoUtil::GetRequestInfoForLog(context);
      return nullptr;
    }
  }
}

RecoResultIter CommonRecoScoreSortArranger::Sort(MutableRecoContextInterface *context, RecoResultIter begin,
                                                 RecoResultIter end) {
  std::vector<const ItemAttr *> score_from_attr_accessor_list;
  score_from_attr_accessor_list.reserve(score_from_attr_list_.size());
  for (const auto &score_attr_name : score_from_attr_list_) {
    const auto *score_attr = GetScoreAttr(context, score_attr_name);
    if (score_attr == nullptr) {
      CL_LOG_WARNING("score_sort", absl::StrCat("invalid attr: ", score_attr_name))
          << " score sort attr: " << score_attr_name << " is invalid";
      continue;
    }
    score_from_attr_accessor_list.emplace_back(score_attr);
  }

  if (!score_from_attr_list_.empty() && score_from_attr_accessor_list.empty()) {
    return end;
  }

  // NOTE(zhaoyang09): sort_by_score 用来标记是否可以只用 score 来排序。
  bool sort_by_score = false;

  if (score_from_attr_list_.empty()) {
    sort_by_score = true;
  } else if (update_score_ && score_from_attr_accessor_list.size() == 1) {
    // NOTE(zhaoyang09): 对用于单 attr 排序且更新 score 场景提前预填 score 加速排序。
    sort_by_score = true;
    const auto &attr_accessor = score_from_attr_accessor_list[0];
    std::for_each(begin, end, [this, context, attr_accessor](CommonRecoResult &result) {
      result.score = GetScore(context, result, attr_accessor);
    });
  }

  auto comparer = [this, context, &score_from_attr_accessor_list, sort_by_score](const CommonRecoResult &a,
                                                                                 const CommonRecoResult &b) {
    if (sort_by_score) {
      return desc_ ? a.score > b.score : a.score < b.score;
    } else {
      for (const auto &attr : score_from_attr_accessor_list) {
        double a_score = GetScore(context, a, attr);
        double b_score = GetScore(context, b, attr);
        if (a_score != b_score) {
          return desc_ ? a_score > b_score : a_score < b_score;
        }
      }
      return false;
    }
  };
  if (stable_sort_) {
    std::stable_sort(begin, end, comparer);
  } else if (partial_sort_) {
    partial_num_ = GetIntProcessorParameter(context, config()->Get("partial_num"), (int64)0);
    int middle = std::min(static_cast<int>(std::distance(begin, end)), partial_num_);
    std::partial_sort(begin, begin + middle, end, comparer);
  } else {
    std::sort(begin, end, comparer);
  }

  auto *score_accessor = context->GetItemAttrAccessor(kScoreAttr);
  if (sort_by_score) {
    std::for_each(begin, end, [this, context, score_accessor](CommonRecoResult &result) {
      context->SetDoubleItemAttr(result, score_accessor, result.score);
    });
  } else {
    auto *first_score_attr = score_from_attr_accessor_list[0];
    std::for_each(begin, end, [this, context, score_accessor, first_score_attr](CommonRecoResult &result) {
      double score = GetScore(context, result, first_score_attr);
      context->SetDoubleItemAttr(result, score_accessor, score);
      if (update_score_) {
        result.score = score;
      }
    });
  }

  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoScoreSortArranger, CommonRecoScoreSortArranger)

}  // namespace platform
}  // namespace ks
