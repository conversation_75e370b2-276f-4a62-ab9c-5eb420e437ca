#include "dragon/src/processor/common/arranger/common_reco_variant_arranger.h"

#include <algorithm>
#include "dragon/src/core/common_reco_statics.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoVariantArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                  RecoResultIter end) {
  ordered_items_.clear();
  variant_items_.clear();
  results_map_.clear();

  absl::optional<absl::Span<const int64>> prev_items;
  if (!prev_items_from_attr_.empty()) {
    prev_items = context->GetIntListCommonAttr(prev_items_from_attr_);
  }

  if (prev_items) {
    for (auto item_key : *prev_items) {
      double init_decay_score = default_init_decay_score_;
      if (!init_decay_score_from_attr_.empty()) {
        if (auto p = context->GetDoubleItemAttr(item_key, init_decay_score_from_attr_)) {
          init_decay_score = *p;
        } else if (auto p = context->GetIntItemAttr(item_key, init_decay_score_from_attr_)) {
          init_decay_score = *p;
        }
      }
      if (init_decay_score <= 0) {
        CL_LOG_EVERY_N(WARNING, 10) << "get negative initial decay score: " << init_decay_score
                                    << ", item_key: " << item_key;
      }
      ordered_items_.emplace_back(item_key, init_decay_score);
    }
  }

  auto *init_attr_accessor = init_decay_score_from_attr_.empty()
                                 ? nullptr
                                 : context->GetItemAttrAccessor(init_decay_score_from_attr_);
  std::for_each(begin, end, [this, init_attr_accessor](const CommonRecoResult &result) {
    double init_decay_score = GetInitDecayScore(result, init_attr_accessor);
    if (init_decay_score <= 0) {
      CL_LOG_EVERY_N(WARNING, 1000) << "get negative initial decay score: " << init_decay_score
                                    << ", item_key: " << result.item_key << ", item_id: " << result.GetId()
                                    << ", item_type: " << result.GetType() << ", reason: " << result.reason;
    }
    ordered_items_.emplace_back(result.item_key, init_decay_score);
    results_map_.insert({result.item_key, result});
  });

  if (!InitializeVariantConfig(context)) {
    CL_LOG_ERROR("variant", "variant_cancelled")
        << "initialize variant config failed, please check the variant_config:\n"
        << raw_variant_config_->ToString();
    return end;
  }

  VLOG(100) << "using variant_config: "
            << (variant_config_ ? variant_config_->ToString() : "variant_config is empty.");

  variant_.Initialize(variant_config_.get());
  variant_.InitializeItem(ordered_items_);

  int idx = 0;

  if (prev_items) {
    for (const auto &item_key : *prev_items) {
      auto item = context->NewCommonRecoResult(item_key, -1);
      GenVariantTag(context, item, idx);
      ++idx;
    }
  }

  std::for_each(begin, end, [this, &idx, context](const CommonRecoResult &result) {
    GenVariantTag(context, result, idx);
    ++idx;
  });

  const bool allow_item_deletion = GetBoolProcessorParameter(context, "allow_item_deletion", true);

  const int static_offset = prev_items ? prev_items->size() : 0;
  int min_select = ordered_items_.size();
  int max_undecay_select = ordered_items_.size();
  if (allow_item_deletion) {
    // 以下是为了兼容老 leaf 中的打散行为, 不建议使用
    int num;
    num = GetIntProcessorParameter(context, "min_select", -1);
    if (num >= 0) {
      min_select = std::min<int>(ordered_items_.size(), num + static_offset);
    }
    num = GetIntProcessorParameter(context, "max_undecay_select", -1);
    if (num >= 0) {
      max_undecay_select = std::min<int>(ordered_items_.size(), num + static_offset);
    }
  }
  variant_.GetVarientOrder(min_select, max_undecay_select, static_offset, &variant_items_);

  variant_results_.clear();
  // 记录满足打散条件的 item 数目
  int satisfied_count = 0;
  auto *decay_score_accessor =
      save_decay_score_to_attr_.empty() ? nullptr : context->GetItemAttrAccessor(save_decay_score_to_attr_);
  std::for_each(variant_items_.begin() + static_offset, variant_items_.end(), [&](const auto &var) {
    auto it = results_map_.find(var.item->key);
    if (it == results_map_.end()) {
      CL_LOG_WARNING("variant", "duplicate_item_discard")
          << "found duplicate item_key " << var.item->key << ", it will be discarded by variant";
      return;
    }
    variant_results_.push_back(it->second);
    results_map_.erase(it);
    if (decay_score_accessor) {
      // 记录打散的 decay 分数到 item attr，可用于之后的过滤
      variant_results_.back().SetDoubleAttr(decay_score_accessor, var.score);
    }
    if (base::IsEqual(var.score, GetInitDecayScore(variant_results_.back(), init_attr_accessor))) {
      ++satisfied_count;
    }
  });

  int variant_count = variant_items_.size() - static_offset;
  if (variant_count > 0) {
    int64 satisfy_rate = ((double)satisfied_count / variant_count) * 100;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(satisfy_rate, kPerfNs, "variant_satisfy_rate",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
  }

  const int total_item_num = std::distance(begin, end);

  std::vector<CommonRecoResult> deleted_items;
  if (!allow_item_deletion && variant_results_.size() != total_item_num) {
    deleted_items.reserve(total_item_num - variant_results_.size());
    std::copy_if(
        begin, end, std::back_inserter(deleted_items),
        [this](const CommonRecoResult &result) -> bool { return results_map_.count(result.item_key); });
  }

  const int copy_num = std::min<int>(variant_results_.size(), total_item_num);
  auto last = std::copy(variant_results_.begin(), variant_results_.begin() + copy_num, begin);
  if (!allow_item_deletion && !deleted_items.empty()) {
    last = std::copy(deleted_items.begin(), deleted_items.end(), last);
    if (last != end) {
      CL_LOG_ERROR("variant", "variant_item_lost")
          << std::distance(last, end)
          << " items are deleted by variant due to duplicate item_key, total_item_num: " << total_item_num
          << RecoUtil::GetRequestInfoForLog(context);
    }
  }

  return last;
}

bool CommonRecoVariantArranger::InitializeVariantConfig(ReadableRecoContextInterface *context) {
  if (!variant_config_ || !raw_variant_config_) {
    CL_LOG_ERROR("variant", "load_variant_config_error") << "load variant config error";
    return false;
  }

  for (auto it = raw_variant_config_->object_begin(); it != raw_variant_config_->object_end(); ++it) {
    if (!it->second->IsObject()) continue;

    // 判断这个属性是否配置了 enabled
    bool enabled = GetBoolProcessorParameter(context, it->second->Get("enabled"), true);
    variant_attrs_[it->first].enabled = enabled;
    variant_config_->Get(it->first)->set("enabled", enabled);

    if (!SetIntVariantConfig(context, it->first, "decay_window_size")) {
      return false;
    }
    if (!SetIntVariantConfig(context, it->first, "decay_occurrent_times")) {
      return false;
    }
    if (!SetIntVariantConfig(context, it->first, "min_rank")) {
      return false;
    }
    if (!SetDoubleVariantConfig(context, it->first, "decay_rate")) {
      return false;
    }
  }
  return true;
}

bool CommonRecoVariantArranger::SetIntVariantConfig(ReadableRecoContextInterface *context,
                                                    const std::string &variant_attr_name,
                                                    const std::string &config_key) {
  std::string default_attr_name = "default_" + config_key;
  base::Json *raw_variant_attr = raw_variant_config_->Get(variant_attr_name);

  if (raw_variant_attr->Get(config_key) && raw_variant_attr->Get(config_key)->IsString()) {
    int64 default_val = -1;
    raw_variant_config_->GetInt(default_attr_name, &default_val);
    int64 attr_val = GetIntProcessorParameter(context, raw_variant_attr->Get(config_key), default_val);
    if (attr_val < 0) {
      CL_LOG_ERROR("variant", "invalid_variant_config:" + config_key)
          << "got invalid variant config value " << attr_val << " for config: " << config_key;
      return false;
    }
    variant_config_->Get(variant_attr_name)->set(config_key, attr_val);
  }
  return true;
}

bool CommonRecoVariantArranger::SetDoubleVariantConfig(ReadableRecoContextInterface *context,
                                                       const std::string &variant_attr_name,
                                                       const std::string &config_key) {
  std::string default_attr_name = "default_" + config_key;
  base::Json *raw_variant_attr = raw_variant_config_->Get(variant_attr_name);

  if (raw_variant_attr->Get(config_key) && raw_variant_attr->Get(config_key)->IsString()) {
    double default_val = -1;
    raw_variant_config_->GetNumber(default_attr_name, &default_val);
    double attr_val =
        GetDoubleProcessorParameter(context, raw_variant_attr->Get(config_key), default_val);
    if (attr_val < 0) {
      CL_LOG_ERROR("variant", "invalid_variant_config:" + config_key)
          << "got invalid variant config value " << attr_val << " for config: " << config_key;
      return false;
    }
    variant_config_->Get(variant_attr_name)->set(config_key, attr_val);
  }
  return true;
}

void CommonRecoVariantArranger::GenVariantTag(MutableRecoContextInterface *context,
                                              const CommonRecoResult &result, int item_index) {
  for (const auto &pr : variant_attrs_) {
    const auto &attr_config = pr.second;
    if (!attr_config.enabled) continue;
    const auto &attr_name = pr.first;
    if (attr_name == kReasonKey) {
      // XXX(fangjianbing): 当配置的 attr_name 为 "reason" 时使用召回原因作为
      // 打散 tag. 这里有一个默认契约是用户不会自定义一个名字为 "reason" 的
      // ItemAttr 且同时要用于打散
      variant_.AddTag(item_index, variant_.GenTag(attr_name, result.reason));
      continue;
    }

    auto *attr_accessor = context->GetItemAttrAccessor(attr_name);
    // NOTE(fangjianbing): 其它情况当做 ItemAttr 正常处理
    if (auto int_val = result.GetIntAttr(attr_accessor)) {
      variant_.AddTag(item_index, variant_.GenTag(attr_name, *int_val));
    } else if (auto str_val = result.GetStringAttr(attr_accessor)) {
      uint64 sign = base::CityHash64(str_val->data(), str_val->size());
      variant_.AddTag(item_index, variant_.GenTag(attr_name, sign));
    } else if (auto int_list_val = result.GetIntListAttr(attr_accessor)) {
      if (attr_config.any_of) {
        for (int64 int_val : *int_list_val) {
          variant_.AddTag(item_index, variant_.GenTag(attr_name, int_val));
        }
      } else {
        // 参照 Java Arrays.hashCode(int a[]) 的实现方案
        int64 sign = 1;
        for (int64 int_val : *int_list_val) {
          sign = 31 * sign + int_val;
        }
        variant_.AddTag(item_index, variant_.GenTag(attr_name, sign));
      }
    } else if (auto string_list_val = result.GetStringListAttr(attr_accessor)) {
      if (attr_config.any_of) {
        for (auto sv : *string_list_val) {
          uint64 sign = base::CityHash64(sv.data(), sv.size());
          variant_.AddTag(item_index, variant_.GenTag(attr_name, sign));
        }
      } else {
        // 参照 Java Arrays.hashCode(Object a[]) 的实现方案
        int64 sign = 1;
        for (auto sv : *string_list_val) {
          sign = 31 * sign + base::CityHash64(sv.data(), sv.size());
        }
        variant_.AddTag(item_index, variant_.GenTag(attr_name, sign));
      }
    }
  }
}

double CommonRecoVariantArranger::GetInitDecayScore(const CommonRecoResult &result, ItemAttr *attr_accessor) {
  if (attr_accessor) {
    auto double_val = result.GetDoubleAttr(attr_accessor);
    if (double_val) return *double_val;
    auto int_val = result.GetIntAttr(attr_accessor);
    if (int_val) return *int_val;
  }
  return default_init_decay_score_;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoVariantArranger, CommonRecoVariantArranger)

}  // namespace platform
}  // namespace ks
