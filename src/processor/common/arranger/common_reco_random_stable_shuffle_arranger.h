#pragma once

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/util/random.h"

namespace ks {
namespace platform {

// NOTE(zhaoyang09): 相关性能记录: 10000 个 item
//  stable_gather = False 耗时： 0.5ms
//  stable_gather = True 耗时： 1.2ms

class CommonRecoRandomStableShuffleArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoRandomStableShuffleArranger() = default;
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    shuffle_by_reason_ = config()->GetBoolean("shuffle_by_reason", false);
    shuffle_by_channel_ = config()->GetBoolean("shuffle_by_channel", false);
    stable_gather_ = config()->GetBoolean("stable_gather", false);
    if (!shuffle_by_reason_ && !shuffle_by_channel_) {
      LOG(ERROR)
          << "CommonRecoRandomStableShuffleArranger init failed, shuffle_by_reason and shuffle_by_channel "
             "both false";
      return false;
    }
    random_engine_ = local_random_engine();
    return true;
  }

  int64 GetFlag(const CommonRecoResult &it) const {
    if (shuffle_by_reason_) {
      return it.reason;
    } else if (shuffle_by_channel_) {
      return it.channel;
    }
    return 0;
  }

 private:
  bool shuffle_by_reason_ = false;
  bool shuffle_by_channel_ = false;
  bool stable_gather_ = false;
  std::default_random_engine random_engine_;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoRandomStableShuffleArranger);
};
}  // namespace platform
}  // namespace ks
