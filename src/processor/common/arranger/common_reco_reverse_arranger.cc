#include "dragon/src/processor/common/arranger/common_reco_reverse_arranger.h"

#include <utility>
namespace ks {
namespace platform {

RecoResultIter CommonRecoItemReverseArranger::Arrange(MutableRecoContextInterface *context,
                                                      RecoResultIter begin,
                                                      RecoResultIter end) {
  std::reverse(begin, end);
  return end;
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoItemReverse<PERSON>rranger, CommonRecoItemReverseArranger)

}  // namespace platform
}  // namespace ks
