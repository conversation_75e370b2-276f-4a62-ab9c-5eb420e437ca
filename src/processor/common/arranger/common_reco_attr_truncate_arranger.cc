#include "dragon/src/processor/common/arranger/common_reco_attr_truncate_arranger.h"

#include <algorithm>
#include <unordered_map>
#include <unordered_set>
#include <vector>

namespace ks {
namespace platform {

RecoResultIter CommonRecoAttrTruncateArranger::Arrange(MutableRecoContextInterface *context,
                                                       RecoResultIter begin, RecoResultIter end) {
  int size = std::distance(begin, end);

  int size_limit_ = GetIntProcessorParameter(context, limit_config_, -1);
  if (size_limit_ < 0) {
    CL_LOG_EVERY_N(INFO, 100) << "truncate cancelled, invalid value of config 'size_limit': " << size_limit_;
    return end;
  }

  if (size_limit_ >= size) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(0, kPerfNs, "truncate_filter_count",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
    return end;
  }
  std::unordered_map<uint64, std::vector<uint64>> group_items;
  std::for_each(begin, end, [this, context, &group_items](const CommonRecoResult &result) {
    uint64 item_key = result.item_key;
    uint64 group_id = 0;
    auto attr_str_value = context->GetStringItemAttr(item_key, attr_name_);
    if (attr_str_value) {
      group_id = base::CityHash64(attr_str_value->data(), attr_str_value->size());
    } else {
      auto attr_value = context->GetIntItemAttr(item_key, attr_name_);
      if (attr_value) {
        group_id = *attr_value;
      }
    }
    group_items[group_id].emplace_back(item_key);
  });
  double truncate_ratio = size_limit_ * 1.0 / size;
  if (!allow_overflow_) {
    int32 dynamic_limit = size_limit_, other_size = 0;
    for (const auto &pair : group_items) {
      if (config_map_.count(pair.first)) {
        auto &config = config_map_[pair.first];
        int limit_num = GetIntProcessorParameter(context, config.limit, -1);
        if (limit_num < 0) {
          limit_num = INT_MAX;
        }
        double queue_ratio = GetDoubleProcessorParameter(context, config.ratio, 1.0, true);
        if (queue_ratio < 0.0 || queue_ratio > 1.0) {
          queue_ratio = 1.0;
        }
        dynamic_limit -= std::min(limit_num, (int32)(pair.second.size() * queue_ratio));
      } else {
        other_size += pair.second.size();
      }
    }
    if (dynamic_limit <= 0) {
      truncate_ratio = 0.0;
    } else if (other_size > 0) {
      truncate_ratio = dynamic_limit * 1.0 / other_size;
    }
  }
  std::unordered_set<uint64> remain_items;
  for (const auto &pair : group_items) {
    const auto &items = pair.second;
    int32 truncate_size = (int32)(items.size() * truncate_ratio);
    int32 truncate_size_config = truncate_size;
    if (config_map_.count(pair.first)) {
      auto &config = config_map_[pair.first];
      int limit_num = GetIntProcessorParameter(context, config.limit, -1);
      if (limit_num < 0) {
        limit_num = INT_MAX;
      }
      double queue_ratio = GetDoubleProcessorParameter(context, config.ratio, 1.0, true);
      if (queue_ratio < 0.0 || queue_ratio > 1.0) {
        queue_ratio = 1.0;
      }
      truncate_size_config = std::min(limit_num, (int32)(items.size() * queue_ratio));
      if (config.mode == 1) {
        truncate_size = std::max(truncate_size_config, truncate_size);
      } else if (config.mode == 2) {
        truncate_size = std::min(truncate_size_config, truncate_size);
      } else {
        truncate_size = truncate_size_config;
      }
    }
    for (int i = 0; i < truncate_size && i < items.size(); ++i) {
      remain_items.insert(items[i]);
    }
  }
  auto new_end = std::remove_if(begin, end, [&remain_items](const CommonRecoResult &result) {
    if (remain_items.count(result.item_key)) {
      return false;
    }
    return true;
  });
  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "truncate_filter_count",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAttrTruncateArranger, CommonRecoAttrTruncateArranger)

}  // namespace platform
}  // namespace ks
