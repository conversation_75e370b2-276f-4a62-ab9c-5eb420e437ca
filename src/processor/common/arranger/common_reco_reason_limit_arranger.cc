#include "dragon/src/processor/common/arranger/common_reco_reason_limit_arranger.h"
#include "dragon/src/core/common_reco_util.h"

#include <algorithm>

namespace ks {
namespace platform {

RecoResultIter CommonRecoReasonLimitArranger::Arrange(MutableRecoContextInterface *context,
                                                      RecoResultIter begin, RecoResultIter end) {
  const int64 total_item_num = std::distance(begin, end);
  if (total_item_num <= 0) {
    return end;
  }

  int64 size_limit = GetIntProcessorParameter(context, "size_limit", -1);
  if (size_limit >= 0 && size_limit >= total_item_num) {
    return end;
  }
  // 全局的缩放比例
  double scale_ratio = (double)size_limit / total_item_num;
  // 统计每个 reason 的数目
  std::unordered_map<int64, int64> reason_counts;
  std::for_each(begin, end, [&](const CommonRecoResult &result) { ++reason_counts[result.reason]; });

  int64 reason_limit = -1;
  // 优先考虑 size_limit ，若配置则 reason_limit 失效
  if (size_limit < 0) {
    reason_limit = GetIntProcessorParameter(context, "reason_limit", -1);
  }

  // min survial 是可选配置
  int64 default_min_survival = GetIntProcessorParameter(context, "min_survival", 0);

  // reason_config 中，动态参数配置的 reason 解析，避免影响静态配置数据
  std::unordered_map<int64, std::shared_ptr<TruncateConfig>> dynamic_reason_config;
  for (const auto &raw_json_config : reason_config_dynamic_reason_) {
    int64 int_value = 0;
    std::vector<int64> int_list_value;
    if (TryGetIntProcessorParameter(context, raw_json_config.first, &int_value)) {
      dynamic_reason_config[int_value] = raw_json_config.second;
    } else if (TryGetIntListProcessorParameter(context, raw_json_config.first, &int_list_value)) {
      for (auto i : int_list_value) {
        dynamic_reason_config[i] = raw_json_config.second;
      }
    }
  }

  // 设定每个 reason 的 remain size
  for (auto &[reason, item_num] : reason_counts) {
    // 默认会以全局配置缩放比例为准
    int64 remain_size = item_num;
    if (scale_ratio >= 0) {
      remain_size *= scale_ratio;
    } else if (reason_limit >= 0) {
      remain_size = reason_limit;
    }

    const TruncateConfig *reason_cfg = nullptr;
    auto current_reason_config = reason_config_[current_index_];
    if (current_reason_config && !current_reason_config->empty()) {
      auto it = current_reason_config->find(reason);
      if (it != current_reason_config->end()) {
        reason_cfg = it->second.get();
      }
    }
    // reason 若存在动态参数配置，选用动态参数配置的信息
    if (!dynamic_reason_config.empty()) {
      auto it = dynamic_reason_config.find(reason);
      if (it != dynamic_reason_config.end()) {
        reason_cfg = it->second.get();
      }
    }
    if (!reason_cfg) {
      reason_cfg = default_reason_config_.get();
    }

    if (reason_cfg) {
      // 如果 reason 做了单独配置，则覆盖全局的 truncate size
      int64 limit = GetIntProcessorParameter(context, reason_cfg->limit_cfg, reason_cfg->limit);
      if (limit >= 0) {
        remain_size = limit;
      }
      double ratio = GetDoubleProcessorParameter(context, reason_cfg->ratio_cfg, reason_cfg->ratio);
      if (ratio >= 0) {
        remain_size = item_num * ratio;
        if (limit >= 0 && limit < remain_size) {
          remain_size = limit;
        }
      }
      // 如果配置了 min_survival 则会进行保量
      int64 min_survival = GetIntProcessorParameter(context, reason_cfg->min_survival_cfg,
                                                    std::max(default_min_survival, reason_cfg->min_survival));
      remain_size = std::max(remain_size, min_survival);
    } else {
      remain_size = std::max(remain_size, default_min_survival);
    }

    // 更新每个 reason 的数目为要保量留下的数目
    item_num = remain_size;
  }

  auto new_end = std::remove_if(begin, end, [&reason_counts](const CommonRecoResult &result) {
    return reason_counts[result.reason]-- <= 0;
  });

  int64 filtered_count = std::distance(new_end, end);
  if (filtered_count > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "reason_limit_count",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), GetName());
  }

  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoReasonLimitArranger, CommonRecoReasonLimitArranger)

}  // namespace platform
}  // namespace ks
