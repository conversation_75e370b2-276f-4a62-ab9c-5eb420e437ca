#include "dragon/src/processor/common/arranger/common_reco_browse_set_filter_arranger.h"
#include <algorithm>

namespace ks {
namespace platform {

RecoResultIter CommonRecoBrowseSetFilterArranger::Arrange(MutableRecoContextInterface *context,
                                                          RecoResultIter begin, RecoResultIter end) {
  if (need_save_filtered_items_) {
    context->ResetIntListCommonAttr(save_filtered_items_to_common_attr_);
  }

  ItemAttr *id_in_attr = nullptr;
  if (!check_id_in_attr_.empty()) {
    id_in_attr = context->GetItemAttrAccessor(check_id_in_attr_);
  }

  auto new_end = std::remove_if(begin, end, [this, context, id_in_attr](const CommonRecoResult &result) {
    if (IsInBrowseSet(context, id_in_attr, result)) {
      if (need_save_filtered_items_) {
        // 小业务经常需要从 browse set 过滤掉的 item 里进行重召回，这里通过
        // common attr 记录下来
        context->AppendIntListCommonAttr(save_filtered_items_to_common_attr_, result.item_key);
      }
      return true;
    }
    return false;
  });
  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "browse_set_filter",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  return new_end;
}

bool CommonRecoBrowseSetFilterArranger::IsInBrowseSet(MutableRecoContextInterface *context,
                                                      ItemAttr *id_in_attr,
                                                      const CommonRecoResult &result) const {
  if (!id_in_attr) {
    return context->InBrowseSet(result.item_key);
  }

  int item_type = item_type_of_checked_id_ < 0 ? result.GetType() : item_type_of_checked_id_;
  if (auto item_id = context->GetIntItemAttr(result, id_in_attr)) {
    uint64 key = Util::GenKeysign(item_type, *item_id);
    return context->InBrowseSet(key);
  } else if (auto item_id_list = context->GetIntListItemAttr(result, id_in_attr)) {
    return std::any_of(item_id_list->begin(), item_id_list->end(), [&](int64 item_id) {
      uint64 key = Util::GenKeysign(item_type, item_id);
      return context->InBrowseSet(key);
    });
  } else {
    CL_LOG_EVERY_N(WARNING, 1000) << "browse_set_filter cancelled for item_id: " << result.GetId()
                                  << ", item_type: " << result.GetType()
                                  << ", due to '" + check_id_in_attr_ + "' attr missing";
    return false;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoBrowseSetFilterArranger, CommonRecoBrowseSetFilterArranger)

}  // namespace platform
}  // namespace ks
