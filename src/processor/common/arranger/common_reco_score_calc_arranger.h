#pragma once

#include <algorithm>
#include <string>

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/module/common_reco_score_base.h"

namespace ks {
namespace platform {

class CommonRecoScoreCalcArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoScoreCalcArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    auto *formula_config = config()->Get("formula");
    if (!formula_config) {
      LOG(ERROR) << "CommonRecoScoreCalcArranger init failed! Missing config 'formula'!";
      return false;
    }

    auto *parameter_config = config()->Get("parameter");
    if (!parameter_config) {
      LOG(ERROR) << "CommonRecoScoreCalcArranger init failed! Missing config 'parameter'!";
      return false;
    }

    auto *dynamic_parameter_config = config()->Get("dynamic_parameter");
    if (!dynamic_parameter_config) {
      LOG(ERROR) << "CommonRecoScoreCalcArranger init failed! Missing config 'dynamic_parameter'!";
      return false;
    }

    std::string score_key = config()->GetString("score_key", "score");
    std::string save_score_to_attr = config()->GetString("save_score_to_attr", "");
    bool save_formula_score = config()->GetBoolean("save_formula_score", false);
    std::string save_formula_score_prefix = config()->GetString("save_formula_score_prefix", "");
    bool save_for_debug_request_only = config()->GetBoolean("save_for_debug_request_only", false);

    score_calc_handler_.InitContext(parameter_config, dynamic_parameter_config, formula_config, score_key,
                                    save_for_debug_request_only, save_score_to_attr, save_formula_score,
                                    save_formula_score_prefix);
    score_calc_handler_.EnableDebugInfo(config()->GetBoolean("enable_debug_info_printing", false));

    return true;
  }

 private:
  ScoreCalcHandler score_calc_handler_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoScoreCalcArranger);
};

}  // namespace platform
}  // namespace ks
