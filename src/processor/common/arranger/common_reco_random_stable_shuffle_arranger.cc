#include "dragon/src/processor/common/arranger/common_reco_random_stable_shuffle_arranger.h"

#include <algorithm>
#include <iterator>
#include <utility>
#include <vector>

namespace ks {
namespace platform {
RecoResultIter CommonRecoRandomStableShuffleArranger::Arrange(MutableRecoContextInterface *context,
                                                              RecoResultIter begin, RecoResultIter end) {
  size_t item_num = std::distance(begin, end);
  if (item_num <= 1) {
    return end;
  }

  auto begin_t = begin;
  auto end_t = end;

  std::vector<CommonRecoResult> stable_gather_results;
  if (stable_gather_) {
    stable_gather_results.assign(begin_t, end_t);
    std::stable_sort(stable_gather_results.begin(), stable_gather_results.end(),
                     [this](const CommonRecoResult &a, const CommonRecoResult &b) -> bool {
                       return GetFlag(a) > GetFlag(b);
                     });
    begin_t = stable_gather_results.begin();
    end_t = stable_gather_results.end();
  }

  std::vector<std::pair<int64, int64>> postion_list;
  postion_list.reserve(item_num);
  int64 last_flag = GetFlag(*begin_t);
  std::pair<int64, int64> pos_pair;
  pos_pair.first = 0;
  for (int64 i = 1; begin_t + i < end_t; i++) {
    int64 flag = GetFlag(*(begin_t + i));
    if (flag != last_flag) {
      pos_pair.second = i;
      postion_list.push_back(pos_pair);
      last_flag = flag;
      pos_pair.first = i;
    }
  }
  pos_pair.second = item_num;
  postion_list.push_back(pos_pair);
  if (postion_list.size() <= 1) {
    return end;
  }
  if (postion_list.size() > 1) {
    std::shuffle(postion_list.begin(), postion_list.end(), random_engine_);
  }
  std::vector<CommonRecoResult> final_results;
  final_results.resize(item_num, {0, 0, 0});
  int64 s = 0;
  for (size_t i = 0; i < postion_list.size(); i++) {
    std::copy(begin_t + postion_list[i].first, begin_t + postion_list[i].second, final_results.begin() + s);
    s += postion_list[i].second - postion_list[i].first;
  }
  std::copy(final_results.begin(), final_results.end(), begin);
  return end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRandomStableShuffleArranger,
                 CommonRecoRandomStableShuffleArranger)

}  // namespace platform
}  // namespace ks
