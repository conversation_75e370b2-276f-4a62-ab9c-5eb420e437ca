#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoScoreSortArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoScoreSortArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    stable_sort_ = config()->GetBoolean("stable_sort", false);
    partial_sort_ = config()->GetBoolean("partial_sort_", false);
    desc_ = config()->GetBoolean("desc", true);
    update_score_ = config()->GetBoolean("update_score", true);
    auto *score_from_attr = config()->Get("score_from_attr");
    if (score_from_attr) {
      if (score_from_attr->IsString()) {
        std::string v = score_from_attr->StringValue();
        if (!v.empty()) {
          score_from_attr_list_.emplace_back(std::move(v));
        }
      } else if (score_from_attr->IsArray()) {
        if (!RecoUtil::ExtractStringListFromJsonConfig(score_from_attr, &score_from_attr_list_) ||
            score_from_attr_list_.empty()) {
          LOG(ERROR) << "CommonRecoScoreSortArranger Failed to parse config! error message: "
                        "score_from_attr_list is empty ";
          return false;
        }
      }
      if (score_from_attr_list_.empty()) {
        LOG(ERROR) << "CommonRecoScoreSortArranger Failed to parse config! error message: "
                      "score_from_attr is empty ";
        return false;
      }
    }

    return true;
  }

  // NOTE(zhaoyang09)： 把使用单 attr 排序且更新 score 单独拆出函数，以降低性能损耗。
  // 性能损耗：
  // | 10000 item             | 耗时     |
  // | score                  | 0.5ms   |
  // | 单 attr 且更新 score     | 约 1ms  |
  // | 不更新 score 或 多 attr  | >= 5ms  |
  // 注： 多 attr 排序中，每增加 10000 个 item*attr 就会增加约 5ms 耗时。
  RecoResultIter Sort(MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end);

  double GetScore(MutableRecoContextInterface *context, const CommonRecoResult &result,
                  const ItemAttr *attr_accessor) {
    double score = 0.0;
    switch (attr_accessor->value_type) {
      case AttrType::INT:
        if (auto int_val = context->GetIntItemAttr(result, attr_accessor)) {
          score = *int_val;
        }
        break;
      case AttrType::FLOAT:
        if (auto double_val = context->GetDoubleItemAttr(result, attr_accessor)) {
          score = *double_val;
        }
        break;
      case AttrType::INT_LIST:
        if (auto int_list = context->GetIntListItemAttr(result, attr_accessor)) {
          auto max_val = std::max_element(int_list->begin(), int_list->end());
          if (max_val != int_list->end()) {
            score = *max_val;
          }
        }
        break;
      case AttrType::FLOAT_LIST:
        if (auto double_list = context->GetDoubleListItemAttr(result, attr_accessor)) {
          auto max_val = std::max_element(double_list->begin(), double_list->end());
          if (max_val != double_list->end()) {
            score = *max_val;
          }
        }
        break;
      default:
        break;
    }
    return score;
  }

  const ItemAttr *GetScoreAttr(MutableRecoContextInterface *context, const std::string &score_from_attr);

 private:
  bool stable_sort_ = false;
  bool partial_sort_ = false;
  bool desc_ = true;
  bool update_score_ = true;
  int partial_num_ = 0;
  std::vector<std::string> score_from_attr_list_ = {};

  DISALLOW_COPY_AND_ASSIGN(CommonRecoScoreSortArranger);
};

}  // namespace platform
}  // namespace ks
