#include "dragon/src/processor/common/arranger/common_reco_common_attr_filter_arranger.h"

#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoCommonAttrFilterArranger::Arrange(MutableRecoContextInterface *context,
                                                           RecoResultIter begin, RecoResultIter end) {
  thread_local folly::F14FastSet<int64> target_items;
  target_items.clear();

  int pardon_num = GetIntProcessorParameter(context, "pardon_num", 0);

  for (const auto &attr : common_attrs_) {
    auto p = context->GetIntListCommonAttr(attr);
    if (p) target_items.insert(p->begin(), p->end());
  }

  int current_pardon_count = 0;
  auto *attr_accessor = on_item_attr_.empty() ? nullptr : context->GetItemAttrAccessor(on_item_attr_);

  auto remove_condition = [this, attr_accessor, pardon_num,
                           &current_pardon_count](const CommonRecoResult &result) {
    int64 key = result.item_key;
    if (attr_accessor) {
      auto val = result.GetIntAttr(attr_accessor);
      if (!val) {
        return !exclude_;
      }
      key = *val;
    }
    bool found = target_items.find(key) != target_items.end();
    bool should_remove = exclude_ ? found : !found;
    if (should_remove && current_pardon_count < pardon_num) {
      // 如果是需要被赦免的 item
      should_remove = false;
      ++current_pardon_count;
    }
    return should_remove;
  };

  int cancel_num = GetIntProcessorParameter(context, "cancel_num", -1);
  if (cancel_num >= 0) {
    int remain_num = std::distance(begin, end) - std::count_if(begin, end, remove_condition);
    if (remain_num <= cancel_num) {
      CL_LOG(INFO) << "attr filter cancelled: will remain " << remain_num
                   << " items only, cancel_num: " << cancel_num;
      base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "attr_filter_cancel_count",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), GetName());
      return end;
    }
  }

  current_pardon_count = 0;
  auto new_end = std::remove_if(begin, end, remove_condition);

  if (current_pardon_count > 0) {
    CL_LOG(INFO) << "current_pardon_count: " << current_pardon_count << ", max pardon_num: " << pardon_num;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        current_pardon_count, kPerfNs, "attr_filter_pardon_count", GlobalHolder::GetServiceIdentifier(),
        context->GetRequestType(), GetName());
  }

  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "common_attr_filter",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName(), filter_info_);
  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoCommonAttrFilterArranger, CommonRecoCommonAttrFilterArranger)

}  // namespace platform
}  // namespace ks
