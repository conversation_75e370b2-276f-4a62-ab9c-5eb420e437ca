#include "dragon/src/processor/common/arranger/common_reco_truncate_arranger.h"

#include <algorithm>
#include <unordered_map>

namespace ks {
namespace platform {

RecoResultIter CommonRecoTruncateArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                   RecoResultIter end) {
  int size_limit = GetIntProcessorParameter(context, limit_config_, -1);
  if (size_limit < 0) {
    CL_LOG_EVERY_N(INFO, 100) << "truncate cancelled, invalid value of config 'size_limit': " << size_limit;
    return end;
  }

  int total_size = std::distance(begin, end);
  auto new_end = std::next(begin, std::min(size_limit, total_size));

  if (backfill_to_) {
    new_end = HandleBackfillItems(context, begin, new_end, end);
  }

  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "truncate_filter_count",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName(), limit_info_);
  return new_end;
}

RecoResultIter CommonRecoTruncateArranger::HandleBackfillItems(ReadableRecoContextInterface *context,
                                                               RecoResultIter begin, RecoResultIter mid,
                                                               RecoResultIter end) {
  std::unordered_map<ItemAttr *, int> fill_target;
  fill_target.reserve(backfill_to_->objects().size());
  for (const auto &pr : backfill_to_->objects()) {
    auto *attr = context->GetItemAttrAccessor(pr.first);
    int num = GetIntProcessorParameter(context, pr.second, (int64)0);
    auto it = fill_target.find(attr);
    if (it == fill_target.end()) {
      if (num > 0) {
        fill_target[attr] = num;
      }
    } else {
      if (num > it->second) {
        it->second = num;
      }
    }
  }

  // 先统计前 size_limit 个 item 中已存在多少个
  std::for_each(begin, mid, [&fill_target](const CommonRecoResult &result) {
    auto it = fill_target.begin();
    while (it != fill_target.end()) {
      if (it->second <= 0) {
        it = fill_target.erase(it);
        continue;
      }
      auto val = result.GetIntAttr(it->first);
      if (val && *val) {
        --(it->second);
      }
      ++it;
    }
  });

  // 再寻找 size_limit 之后的 item 中哪些需要被补充
  auto new_end = std::remove_if(mid, end, [&fill_target](const CommonRecoResult &result) {
    bool should_remove = true;
    auto it = fill_target.begin();
    while (it != fill_target.end()) {
      if (it->second <= 0) {
        it = fill_target.erase(it);
        continue;
      }
      auto val = result.GetIntAttr(it->first);
      if (val && *val) {
        should_remove = false;
        --(it->second);
      }
      ++it;
    }
    return should_remove;
  });

  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoTruncateArranger, CommonRecoTruncateArranger)

}  // namespace platform
}  // namespace ks
