#pragma once

#include <string>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {

class CommonRecoAttrFilterArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoAttrFilterArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    std::string remove_if = config()->GetString("remove_if", "<=");
    compare_filter_.ParseCompareOperator(remove_if);
    if (compare_filter_.oper == CompareOperator::UNKNOWN) {
      LOG(ERROR) << "CommonRecoAttrFilterArranger init failed! Unknown compare operator, pleace check "
                 << "\"remove_if\" config!";
      return false;
    }

    filter_info_ += remove_if;

    auto *compare_to = config()->Get("compare_to");
    if (compare_to) {
      if (compare_to->IsInteger()) {
        compare_filter_.value_type = CompareValueType::INT;
        compare_filter_.compare_to_int = compare_to->IntValue((int64)0);
        filter_info_ += std::to_string(compare_filter_.compare_to_int);
      } else if (compare_to->IsDouble()) {
        compare_filter_.value_type = CompareValueType::DOUBLE;
        compare_filter_.compare_to_double = compare_to->FloatValue((double)0.0);
        filter_info_ += std::to_string(compare_filter_.compare_to_double);
      } else if (compare_to->IsString()) {
        if (auto op = RecoUtil::ExtractCommonAttrFromExpr(compare_to)) {
          compare_filter_.compare_to = compare_to;
          filter_info_.append(op->data(), op->size());
        } else {
          std::string str = compare_to->StringValue();
          compare_filter_.value_type = CompareValueType::STRING;
          compare_filter_.compare_to_int = base::CityHash64(str.data(), str.size());
          if (compare_filter_.oper != CompareOperator::EQ && compare_filter_.oper != CompareOperator::NE) {
            LOG(ERROR) << "CommonRecoAttrFilterArranger init failed! \"remove_if\" could only be"
                       << " \"==\" or \"!=\" if \"compare_to\" is string";
            return false;
          }
          filter_info_ += str;
        }
      } else {
        LOG(ERROR) << "CommonRecoAttrFilterArranger init failed! Unsupported value type of 'compare_to': "
                   << compare_to->ToString();
        return false;
      }
    }

    remove_if_attr_missing_ = config()->GetBoolean("remove_if_attr_missing", false);
    if (!remove_if_attr_missing_) {
      // 兼容一下老的配置名
      remove_if_attr_missing_ = config()->GetBoolean("eject_if_attr_missing", false);
    }

    return true;
  }

  bool CheckItemShouldRemove(ItemAttr *attr_accessor, const CommonRecoResult &result,
                             int *incomparable_count);

  bool InitCompareFilter(ReadableRecoContextInterface *context, ItemAttr *attr);

 private:
  enum class CompareOperator : int { UNKNOWN = 0, LE, GE, LT, GT, EQ, NE };

  enum class CompareValueType : int { DOUBLE = 0, INT, STRING };

  struct CompareFilter {
    CompareOperator oper = CompareOperator::LE;
    CompareValueType value_type = CompareValueType::DOUBLE;
    int64 compare_to_int = 0;
    double compare_to_double = 0.0;
    const base::Json *compare_to = nullptr;

    void ParseCompareOperator(const std::string &oper) {
      if (oper == "<=" || oper == "LE") {
        this->oper = CompareOperator::LE;
      } else if (oper == ">=" || oper == "GE") {
        this->oper = CompareOperator::GE;
      } else if (oper == "<" || oper == "LT") {
        this->oper = CompareOperator::LT;
      } else if (oper == ">" || oper == "GT") {
        this->oper = CompareOperator::GT;
      } else if (oper == "=" || oper == "==" || oper == "EQ") {
        this->oper = CompareOperator::EQ;
      } else if (oper == "!=" || oper == "<>" || oper == "NE") {
        this->oper = CompareOperator::NE;
      } else {
        this->oper = CompareOperator::UNKNOWN;
      }
    }

    bool CheckAsInt(int64 val) {
      int64 compare_val = this->compare_to_int;
      switch (this->oper) {
        case CompareOperator::EQ:
          return val == compare_val;
        case CompareOperator::NE:
          return val != compare_val;
        case CompareOperator::LE:
          return val <= compare_val;
        case CompareOperator::GE:
          return val >= compare_val;
        case CompareOperator::LT:
          return val < compare_val;
        case CompareOperator::GT:
          return val > compare_val;
        default:
          NOT_REACHED();
          return false;
      }
    }

    bool CheckAsDouble(double val) {
      double compare_val = this->value_type == CompareValueType::DOUBLE ? this->compare_to_double
                                                                        : (double)this->compare_to_int;
      switch (this->oper) {
        case CompareOperator::LE:
          return base::IsLessEqual(val, compare_val);
        case CompareOperator::GE:
          return base::IsGreaterEqual(val, compare_val);
        case CompareOperator::LT:
          return base::IsLess(val, compare_val);
        case CompareOperator::GT:
          return base::IsGreater(val, compare_val);
        case CompareOperator::EQ:
          return base::IsEqual(val, compare_val);
        case CompareOperator::NE:
          return !base::IsEqual(val, compare_val);
        default:
          NOT_REACHED();
          return false;
      }
    }
  };

 private:
  bool remove_if_attr_missing_ = false;
  CompareFilter compare_filter_;
  std::string filter_info_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoAttrFilterArranger);
};

}  // namespace platform
}  // namespace ks
