#pragma once

#include "base/random/pseudo_random.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoRandomScoreArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoRandomScoreArranger() : random_(base::GetTimestamp()) {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    lower_bound_ = config()->GetInt("lower_bound", 0);
    upper_bound_ = config()->GetInt("upper_bound", 100);
    if (lower_bound_ > upper_bound_) {
      LOG(ERROR) << "CommonRecoRandomScoreArranger"
                 << " init failed! lower_bound " << lower_bound_ << " cannot be greater than upper_bound "
                 << upper_bound_;
      return false;
    }
    return true;
  }

 private:
  int lower_bound_;
  int upper_bound_;
  base::PseudoRandom random_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRandomScoreArranger);
};

}  // namespace platform
}  // namespace ks
