#pragma once

#include <third_party/lua-5.4.4/src/lua.hpp>

#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class CommonRecoLuaScoreCalcArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoLuaScoreCalcArranger() {}
  ~CommonRecoLuaScoreCalcArranger() {
    lua_close(lua_state_);
  }

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  enum class AttrSource : int { UNKNOWN = 0, COMMON, ITEM };
  struct Attr {
    std::string attr_name;
    AttrSource attr_source = AttrSource::UNKNOWN;
  };
  struct AttrValue {
    AttrType value_type = AttrType::UNKNOWN;
    lua_Integer int_value = 0;
    lua_Number double_value = 0.0;
    absl::optional<absl::string_view> string_value;
  };
  struct Argument {
    std::vector<Attr> attrs;
    lua_Number default_value = 0.0;
  };

 private:
  bool InitProcessor() override {
    auto *args_config = config()->Get("global_D");
    if (!args_config || !args_config->IsObject()) {
      LOG(ERROR) << "CommonRecoLuaScoreCalcArranger init failed! Missing config 'global_D' or not an object!";
      return false;
    }

    args_.clear();
    for (auto &pr : args_config->objects()) {
      args_[pr.first] = ParseArgument(pr.second->StringValue());
    }

    std::string lua_script = config()->GetString("lua_script");
    if (lua_script.empty()) {
      LOG(ERROR) << "CommonRecoLuaScoreCalcArranger init failed! 'lua_script' cannot be empty!";
      return false;
    }

    lua_state_ = luaL_newstate();
    if (!lua_state_) {
      LOG(ERROR) << "CommonRecoLuaScoreCalcArranger init failed! Failed to new lua_state!";
      return false;
    }

    luaL_openlibs(lua_state_);
    int err = luaL_loadstring(lua_state_, lua_script.data());
    if (err) {
      LOG(ERROR) << "CommonRecoLuaScoreCalcArranger init failed! 'lua_script' load error: " << err;
      return false;
    }
    lua_pcall(lua_state_, 0, 0, 0);

    return true;
  }

  Argument ParseArgument(const std::string &input);

  void GetArgumentValue(ReadableRecoContextInterface *context, const Argument &arg, uint64 item_key,
                        AttrValue *val);

  bool GetValueFromItemAttr(ReadableRecoContextInterface *context, const std::string &attr_name,
                            uint64 item_key, AttrValue *val);

  bool GetValueFromCommonAttr(ReadableRecoContextInterface *context, const std::string &attr_name,
                              uint64 item_key, AttrValue *val);

 private:
  lua_State *lua_state_ = nullptr;
  folly::F14FastMap<std::string, Argument> args_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLuaScoreCalcArranger);
};

}  // namespace platform
}  // namespace ks
