#include "dragon/src/processor/common/arranger/common_reco_mmr_diversity_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoMmrDiversityArranger::Arrange(MutableRecoContextInterface *context,
                                                       RecoResultIter begin, RecoResultIter end) {
  // step1: preCheck
  int64 start_ts = base::GetTimestamp();
  const int total_size = std::distance(begin, end);
  if (total_size <= 0) {
    return end;
  }

  int64 limit = GetIntProcessorParameter(context, "limit", -1);
  if (limit <= 0) {
    CL_LOG(INFO) << "Mmr diversify cancelled, invalid limit: " << limit;
    return end;
  }

  if (limit >= total_size) {
    limit = total_size;
  }

  folly::F14FastMap<uint64, double> already_calc_sim;

  // Step2: prepare result item
  // 该函数业务方可修改，用作预处理 item, 例如填充部分 itemAttr
  PrepareResultItems(context, begin, end);

  // Step3: get final result
  std::vector<CommonRecoResult> final_results;
  // 新定义一个 selected_index 防止有重复 item->key 出现
  std::vector<bool> selected_index(total_size, false);
  final_results.reserve(limit);
  int result_count = 0;
  while (result_count < limit) {
    int best_index = -1;
    double best_score = -1.0f;
    bool is_first = false;
    for (int index = 0; index < total_size; ++index) {
      auto item = (begin + index);
      if (selected_index[index]) {
        continue;
      }
      double score = GetScore(context, begin, end, *item);
      double max_sim_score = CalcMaxSimScore(context, begin, end, *item, final_results, &already_calc_sim);
      double mmr_score = CalcMmrScore(context, begin, end, *item, score, max_sim_score);
      if (!is_first || best_score < mmr_score) {
        best_index = index;
        best_score = mmr_score;
        is_first = true;
      }
    }
    auto best_item = (begin + best_index);
    if (best_index > -1) {
      DealBestItem(context, *best_item, best_index, best_score);
      final_results.push_back(*best_item);
      ++result_count;
      selected_index[best_index] = true;
    } else {
      CL_LOG_ERROR("mmr_diversify", "no_best_item")
          << "no best item! Please check CalcMmrScore function logic! Skip this processor!";
      return end;
    }
  }

  if (result_count == 0) {
    CL_LOG_ERROR("mmr_diversify", "no_result_count")
        << "No result count! Please check your CalcMmrScore function logic! Skip this processor!";
    return end;
  }

  for (int index = 0; index < total_size; ++index) {
    if (selected_index[index]) {
      continue;
    }
    auto item = (begin + index);
    final_results.push_back(*item);
  }

  CL_LOG(INFO) << "mmr diversify finished, selected " << result_count << " items out of " << total_size;

  std::copy(final_results.begin(), final_results.end(), begin);
  return end;
}

// 最终拿到 Mmr 候选后的 DebugInfo 处理，业务方可继承覆写自定义逻辑
// 默认用来存储计算出来的 Mmr 分数
void CommonRecoMmrDiversityArranger::DealBestItem(MutableRecoContextInterface *context,
                                                  const CommonRecoResult &item, int index,
                                                  double best_score) {
  std::string mmr_score_name = GetStringProcessorParameter(context, "mmr_score_name", "");
  if (!mmr_score_name.empty()) {
    context->SetDoubleItemAttr(item.item_key, mmr_score_name, best_score);
  }
}

// 计算最大相似度的函数，业务方可继承覆写自定义逻辑
// 默认会依次让每个 item 都与已经选择的 item 进行相似度计算，然后取最大值
double CommonRecoMmrDiversityArranger::CalcMaxSimScore(MutableRecoContextInterface *context,
                                                       RecoResultIter begin, RecoResultIter end,
                                                       const CommonRecoResult &item,
                                                       const std::vector<CommonRecoResult> &final_results,
                                                       folly::F14FastMap<uint64, double> *already_calc_sim) {
  double score = 0.0;
  char buffer[128];
  memcpy(buffer, &item.item_key, sizeof(uint64));
  // 加了一层缓存来减少计算延迟
  for (const auto &fr : final_results) {
    memcpy(buffer + 64, &fr.item_key, sizeof(uint64));
    uint64 hash_value = base::CityHash64(buffer, sizeof(buffer));
    auto iter = already_calc_sim->find(hash_value);
    if (iter != already_calc_sim->end()) {
      score = std::max(score, iter->second);
      continue;
    }
    double tmp_score = CalcSimScore(context, item, fr);
    score = std::max(score, tmp_score);
    already_calc_sim->emplace(hash_value, tmp_score);
  }
  return score;
}

// 获取最基础的排序 score，业务方可继承覆写自定义逻辑
// 默认直接使用 item 的 score
double CommonRecoMmrDiversityArranger::GetScore(MutableRecoContextInterface *context, RecoResultIter begin,
                                                RecoResultIter end, const CommonRecoResult &item) {
  return item.score;
}

// 初始化，业务方可继承覆写自定义逻辑
// 默认不做任何处理
void CommonRecoMmrDiversityArranger::PrepareResultItems(MutableRecoContextInterface *context,
                                                        RecoResultIter begin, RecoResultIter end) {
  return;
}

// 最终用来计算 MmrScore，业务方可继承实现
// 默认公式为最基础的 Mmr 方程: lambda * score - (1 - lambda) * max_sim_score
double CommonRecoMmrDiversityArranger::CalcMmrScore(MutableRecoContextInterface *context,
                                                    RecoResultIter begin, RecoResultIter end,
                                                    const CommonRecoResult &item, double score,
                                                    double max_sim_score) {
  // 1.0 默认值表示 mmr 函数不生效
  double lambda = GetDoubleProcessorParameter(context, "mmr_lambda", 1.0);
  return lambda * score - (1 - lambda) * max_sim_score;
}

typedef base::JsonFactoryClass JsonFactoryClass;
}  // namespace platform
}  // namespace ks
