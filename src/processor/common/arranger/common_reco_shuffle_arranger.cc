#include "dragon/src/processor/common/arranger/common_reco_shuffle_arranger.h"

#include <algorithm>
#include <numeric>
#include <utility>

namespace ks {
namespace platform {

RecoResultIter CommonRecoShuffleArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                  RecoResultIter end) {
  // 若未指定 item attr 则按照等概率均匀 shuffle
  if (weight_attr_name_.empty()) {
    UniformShuffle(context, begin, end);
  } else {
    WeightedShuffle(context, begin, end);
  }
  return end;
}

void CommonRecoShuffleArranger::UniformShuffle(MutableRecoContextInterface *context, RecoResultIter begin,
                                               RecoResultIter end) {
  for (auto i = (end - begin) - 1; i > 0; --i) {
    std::swap(*std::next(begin, i), *std::next(begin, random_.GetInt(0, i)));
  }
}

void CommonRecoShuffleArranger::WeightedShuffle(MutableRecoContextInterface *context, RecoResultIter begin,
                                                RecoResultIter end) {
  auto *attr_accessor = context->GetItemAttrAccessor(weight_attr_name_);
  // 从 [begin, end) 中根据权重概率来获取 postion
  auto find_postion_iter = [this, attr_accessor](RecoResultIter begin, RecoResultIter end, double sum) {
    double target = sum * random_.GetDouble();
    double base = 0.0;
    auto it = std::find_if(begin, end,
                           [this, attr_accessor, target, &base](const CommonRecoResult &result) -> bool {
                             base += GetItemWeight(attr_accessor, result);
                             return target < base;
                           });
    return it;
  };

  double sum =
      std::accumulate(begin, end, 0.0, [this, attr_accessor](double total, const CommonRecoResult &result) {
        total += GetItemWeight(attr_accessor, result);
        return total;
      });

  for (auto it = begin; it < end; ++it) {
    auto pos = find_postion_iter(it, end, sum);
    sum -= GetItemWeight(attr_accessor, *pos);
    std::swap(*it, *pos);
  }
}

double CommonRecoShuffleArranger::GetItemWeight(ItemAttr *attr_accessor, const CommonRecoResult &result) {
  double weight = 0.0;
  // 若对应的 attr 不存在，则不进行 shuffle
  if (auto double_val = result.GetDoubleAttr(attr_accessor)) {
    weight = *double_val;
  } else if (auto int_val = result.GetIntAttr(attr_accessor)) {
    weight = static_cast<double>(*int_val);
  }
  return std::max(0.0, weight);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoShuffleArranger, CommonRecoShuffleArranger)

}  // namespace platform
}  // namespace ks
