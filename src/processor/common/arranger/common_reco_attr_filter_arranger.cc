#include "dragon/src/processor/common/arranger/common_reco_attr_filter_arranger.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoAttrFilterArranger::Arrange(MutableRecoContextInterface *context,
                                                     RecoResultIter begin, RecoResultIter end) {
  std::string attr_name = GetStringProcessorParameter(context, "attr_name");
  if (attr_name.empty()) {
    CL_LOG_ERROR("attr_filter", "empty_attr_name") << "attr filter cancelled: empty attr_name";
    return end;
  }

  auto *attr_accessor = context->GetItemAttrAccessor(attr_name);
  if (!InitCompareFilter(context, attr_accessor)) {
    return end;
  }

  int pardon_num = GetIntProcessorParameter(context, "pardon_num", 0);

  // 记录当前已被赦免了多少个
  int current_pardon_count = 0;
  int type_mismatch_count = 0;
  auto remove_condition = [this, attr_accessor, pardon_num, &current_pardon_count,
                           &type_mismatch_count](const CommonRecoResult &result) {
    bool should_remove = CheckItemShouldRemove(attr_accessor, result, &type_mismatch_count);
    if (should_remove && current_pardon_count < pardon_num) {
      // 如果是需要被赦免的 item
      should_remove = false;
      ++current_pardon_count;
    }
    return should_remove;
  };

  int cancel_num = GetIntProcessorParameter(context, "cancel_num", -1);
  if (cancel_num >= 0) {
    int remain_num = std::distance(begin, end) - std::count_if(begin, end, remove_condition);
    if (remain_num <= cancel_num) {
      CL_LOG(INFO) << "attr filter for " << attr_name << " cancelled: will remain " << remain_num
                   << " items only, cancel_num: " << cancel_num;
      base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "attr_filter_cancel_count",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), GetName());
      return end;
    }
  }

  current_pardon_count = 0;
  type_mismatch_count = 0;
  auto new_end = std::remove_if(begin, end, remove_condition);

  if (current_pardon_count > 0) {
    CL_LOG(INFO) << "attr filter for " << attr_name << " has pardoned " << current_pardon_count
                 << " items, max pardon_num: " << pardon_num;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        current_pardon_count, kPerfNs, "attr_filter_pardon_count", GlobalHolder::GetServiceIdentifier(),
        context->GetRequestType(), GetName());
  }

  if (type_mismatch_count > 0) {
    CL_LOG_ERROR_COUNT(type_mismatch_count, "attr_filter", "value_type_mismatch:" + attr_name)
        << "mismatched compare value type with item_attr: " << attr_name
        << ", mismatched item num: " << type_mismatch_count;
  }

  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      filtered_count, kPerfNs, "attr_filter_count", GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), GetName(), attr_name + filter_info_);
  return new_end;
}

bool CommonRecoAttrFilterArranger::InitCompareFilter(ReadableRecoContextInterface *context, ItemAttr *attr) {
  if (!compare_filter_.compare_to) {
    return true;
  }
  switch (attr->value_type) {
    case AttrType::INT:
    case AttrType::FLOAT: {
      if (TryGetIntProcessorParameter(context, compare_filter_.compare_to, &compare_filter_.compare_to_int)) {
        compare_filter_.value_type = CompareValueType::INT;
      } else if (TryGetDoubleProcessorParameter(context, compare_filter_.compare_to,
                                                &compare_filter_.compare_to_double)) {
        compare_filter_.value_type = CompareValueType::DOUBLE;
      } else {
        CL_LOG_ERROR("attr_filter", "unknown_attr_type")
            << "attr filter cancelled, unknown attr type for " << compare_filter_.compare_to->ToString();
        return false;
      }
      return true;
    }
    case AttrType::STRING: {
      if (compare_filter_.oper != CompareOperator::EQ && compare_filter_.oper != CompareOperator::NE) {
        CL_LOG_ERROR("attr_filter", "invalid_string_compare:" + attr->name())
            << "attr filter cancelled: string common_attr " << attr->name()
            << " could be compared for '==' and '!=' operator only!";
        return false;
      }
      compare_filter_.value_type = CompareValueType::STRING;
      auto string_value = GetStringProcessorParameter(context, compare_filter_.compare_to);
      compare_filter_.compare_to_int = base::CityHash64(string_value.data(), string_value.size());
      return true;
    }
    default:
      CL_LOG_ERROR("attr_filter", "unknown_attr_type")
          << "attr filter cancelled, unknown attr type for " << attr->name();
      return false;
  }
}

bool CommonRecoAttrFilterArranger::CheckItemShouldRemove(ItemAttr *attr_accessor,
                                                         const CommonRecoResult &result,
                                                         int *incomparable_count) {
  if (auto int_val = result.GetIntAttr(attr_accessor)) {
    if (compare_filter_.value_type == CompareValueType::INT) {
      return compare_filter_.CheckAsInt(*int_val);
    } else if (compare_filter_.value_type == CompareValueType::DOUBLE) {
      return compare_filter_.CheckAsDouble(*int_val);
    } else {
      ++(*incomparable_count);
      return false;
    }
  } else if (auto double_val = result.GetDoubleAttr(attr_accessor)) {
    if (compare_filter_.value_type == CompareValueType::INT ||
        compare_filter_.value_type == CompareValueType::DOUBLE) {
      return compare_filter_.CheckAsDouble(*double_val);
    } else {
      ++(*incomparable_count);
      return false;
    }
  } else if (auto str_val = result.GetStringAttr(attr_accessor)) {
    if (compare_filter_.value_type == CompareValueType::STRING) {
      return compare_filter_.CheckAsInt(base::CityHash64(str_val->data(), str_val->size()));
    } else {
      ++(*incomparable_count);
      return false;
    }
  } else {
    return !result.HasAttr(attr_accessor) && remove_if_attr_missing_;
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoAttrFilterArranger, CommonRecoAttrFilterArranger)

}  // namespace platform
}  // namespace ks
