#include <memory>
#include "gtest/gtest.h"

#include "dragon/src/processor/common/arranger/common_reco_truncate_arranger.h"
#include "dragon/src/module/common_reco_pipeline_executor.h"

class CommonRecoTruncateArrangerTest : public ::testing::Test {
 protected:
  void SetUp() override {
    exec_ = std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(R"json(
    {
      "base_pipeline": {
        "processor": {
          "truncate": {
            "size_limit": 10,
            "type_name": "CommonRecoTruncateArranger"
          }
        },
        "type_name": "CommonRecoPipeline"
      },
      "pipeline_map": {
        "default": {
          "__PARENT": "base_pipeline",
          "pipeline": [
            "truncate"
          ]
        }
      }
    }
    )json");
  }

  std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor> exec_;
};

TEST_F(CommonRecoTruncateArrangerTest, TruncateLongItemList) {
  for (int i = 0; i < 20; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("default");
  auto item_key_list = exec_->GetItemKeyList();
  EXPECT_EQ(item_key_list.size(), 10);
  for (int i = 0; i < item_key_list.size(); i++) {
    EXPECT_EQ(item_key_list[i], i);
  }
}

TEST_F(CommonRecoTruncateArrangerTest, TruncateShortItemList) {
  for (int i = 0; i < 2; i++) {
    exec_->AddItem(i);
  }
  exec_->Run("default");
  auto item_key_list = exec_->GetItemKeyList();
  EXPECT_EQ(item_key_list.size(), 2);
  for (int i = 0; i < item_key_list.size(); i++) {
    EXPECT_EQ(item_key_list[i], i);
  }
}
