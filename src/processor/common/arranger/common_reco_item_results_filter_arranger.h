#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/hasher_util.h"

namespace ks {
namespace platform {

typedef folly::F14FastSet<CommonRecoResult, CommonRecoResultHasher> CommonRecoResultSet;

class CommonRecoItemResultsFilterArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoItemResultsFilterArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    if (!RecoUtil::ExtractStringListFromJsonConfig(config()->Get("remove_if_not_in"), &remove_if_not_in_)) {
      LOG(ERROR) << "CommonRecoPipelineEnricher init failed! merge_common_attrs parse error.";
      return false;
    }
    return true;
  }

  CommonRecoResultSet GetRetainedItems(MutableRecoContextInterface *context);

 private:
  std::vector<std::string> remove_if_not_in_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoItemResultsFilterArranger);
};

}  // namespace platform
}  // namespace ks
