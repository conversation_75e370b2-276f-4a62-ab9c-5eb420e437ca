#include "dragon/src/processor/common/arranger/common_reco_intermix_arranger.h"

#include <algorithm>
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoIntermixArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                   RecoResultIter end) {
  HandleIntermix(context, begin, end);
  return end;
}

void CommonRecoIntermixArranger::HandleIntermix(MutableRecoContextInterface *context, RecoResultIter begin,
                                                RecoResultIter end) {
  const int total_size = std::distance(begin, end);
  if (total_size <= 0) {
    CL_LOG(INFO) << "intermix cancelled: empty item list!";
    return;
  }

  auto mix_pattern = GetIntListProcessorParameter(context, "mix_pattern");
  if (mix_pattern.empty()) {
    CL_LOG(INFO) << "intermix cancelled: empty mix pattern!";
    return;
  }

  // 最多让前 num_limit_ 个 item 满足 mix_pattern_ 条件即可
  int64 num_limit = GetIntProcessorParameter(context, "num_limit", -1);

  mix_results_.assign(begin, end);
  succeed_flags_.resize(total_size);
  std::fill(succeed_flags_.begin(), succeed_flags_.end(), false);

  // last_pattern_iter_map 记录每个 item_type 上一次被找到的位置, 下次寻找时从上次的位置继续即可, 不用从头开始
  folly::F14FastMap<int64, int> last_pattern_iter_map;
  int dissatisfied_num = 0;
  int continuous_satisfied_num = 0;
  int retry_count = 0;
  int slot = 0;
  auto current = mix_results_.begin();
  while (current != mix_results_.end()) {
    if (retry_count >= mix_pattern.size() ||
        (num_limit >= 0 && std::distance(mix_results_.begin(), current) >= num_limit)) {
      // 如果试过了所有的 pattern 都找不到, 或已经达到了 num_limit 限制, 则提前结束
      break;
    }

    int64 desired_pattern = mix_pattern[slot];
    auto start = std::next(begin, last_pattern_iter_map[desired_pattern]);
    auto target =
        std::find_if(start, end, [this, context, desired_pattern](const CommonRecoResult &result) -> bool {
          return IsPatternMatch(context, result, desired_pattern);
        });
    if (target == end) {
      // 如果当前 slot 上的 pattern 无法被满足, 尝试下一个 slot 的 pattern
      last_pattern_iter_map[desired_pattern] = total_size;
      ++dissatisfied_num;
      ++retry_count;
    } else {
      last_pattern_iter_map[desired_pattern] = std::distance(begin, target) + 1;
      // 找到了满足当前 slot pattern 的 item: target, 将其拷贝至 current
      *current = *target;
      ++current;
      succeed_flags_[std::distance(begin, target)] = true;
      retry_count = 0;
      if (dissatisfied_num == 0) {
        ++continuous_satisfied_num;
      }
    }

    slot = (slot + 1) % mix_pattern.size();
  }

  if (current != mix_results_.end()) {
    for (int i = 0; i < succeed_flags_.size(); ++i) {
      if (!succeed_flags_[i]) {
        *current = *std::next(begin, i);
        ++current;
      }
    }
  }

  if (current != mix_results_.end()) {
    CL_LOG_ERROR("intermix", "results_size_mismatch")
        << "[SHOULD NOT HAPPEN] mix_results size mismatch: current_pos="
        << std::distance(mix_results_.begin(), current) << ", vs total_size=" << total_size;
  }
  std::copy(mix_results_.begin(), mix_results_.end(), begin);

  base::perfutil::PerfUtilWrapper::IntervalLogStash(
      continuous_satisfied_num, kPerfNs, "intermix.satisfied_num", GlobalHolder::GetServiceIdentifier(),
      context->GetRequestType(), GetName());
  CL_LOG(INFO) << "intermix done, total num: " << total_size
               << ", first continuous satisfied num: " << continuous_satisfied_num
               << ", total dissatisfied num: " << dissatisfied_num;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoIntermixArranger, CommonRecoIntermixArranger)

}  // namespace platform
}  // namespace ks
