#pragma once

#include <string>
#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoTruncateArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoTruncateArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    backfill_to_ = config()->Get("backfill_to");
    if (backfill_to_ && !backfill_to_->IsObject()) {
      LOG(ERROR) << "CommonRecoTruncateArranger init failed! Config 'backfill_to' should be an object!";
      return false;
    }
    limit_config_ = config()->Get("size_limit");
    if (limit_config_) {
      if (!limit_config_->StringValue(&limit_info_)) {
        limit_info_ = std::to_string(limit_config_->IntValue(0L));
      }
    }
    return true;
  }

  RecoResultIter HandleBackfillItems(ReadableRecoContextInterface *context, RecoResultIter begin,
                                     RecoResultIter mid, RecoResultIter end);

 private:
  const base::Json *backfill_to_ = nullptr;
  const base::Json *limit_config_ = nullptr;
  std::string limit_info_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoTruncateArranger);
};

}  // namespace platform
}  // namespace ks
