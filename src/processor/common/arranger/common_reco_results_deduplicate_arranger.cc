#include "dragon/src/processor/common/arranger/common_reco_results_deduplicate_arranger.h"

#include "base/hash_function/city.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoResultsDeduplicateArranger::Arrange(MutableRecoContextInterface *context,
                                                             RecoResultIter begin, RecoResultIter end) {
  PerfItemReasonRepetition(context, begin, end);

  if (!dedup_attr_accessor_ && !on_item_attr_.empty()) {
    dedup_attr_accessor_ = context->GetItemAttrAccessor(on_item_attr_);
  }
  if (!append_reason_attr_accessor_ && !append_reason_to_.empty()) {
    append_reason_attr_accessor_ = context->GetItemAttrAccessor(append_reason_to_);
  }
  if (!append_order_attr_accessor_ && !append_order_to_.empty()) {
    append_order_attr_accessor_ = context->GetItemAttrAccessor(append_order_to_);
  }
  if (!append_reason_order_attr_accessor_ && !append_reason_order_to_.empty()) {
    append_reason_order_attr_accessor_ = context->GetItemAttrAccessor(append_reason_order_to_);
  }
  if (!save_dup_count_attr_accessor_ && !save_dup_count_to_.empty()) {
    save_dup_count_attr_accessor_ = context->GetItemAttrAccessor(save_dup_count_to_);
  }
  int append_reason_for_top_distinct_items =
      GetIntProcessorParameter(context, "append_reason_for_top_distinct_items", -1);

  thread_local folly::F14FastMap<uint64, int> dedup_map;
  thread_local folly::F14FastMap<uint64, int> reason_map;
  thread_local folly::F14FastMap<int, int> reason_order_map;
  dedup_map.clear();
  reason_map.clear();
  reason_order_map.clear();
  int distinct_item_num = 0;
  int item_order_index = 0;
  auto new_end = std::remove_if(begin, end, [&](const CommonRecoResult &result) {
    ++item_order_index;
    if (append_reason_attr_accessor_ && (append_reason_for_top_distinct_items < 0 ||
                                         distinct_item_num < append_reason_for_top_distinct_items)) {
      result.AppendIntListAttr(append_reason_attr_accessor_, result.reason);
      if (append_order_attr_accessor_) {
        result.AppendIntListAttr(append_order_attr_accessor_, item_order_index);
      }
      if (append_reason_order_attr_accessor_) {
        auto reason_order_map_iter = reason_order_map.find(result.reason);
        if (reason_order_map_iter != reason_order_map.end()) {
          reason_order_map_iter->second += 1;
        } else {
          reason_order_map[result.reason] = 1;
        }
        LOG_EVERY_N(INFO, 5000) << "append_reason_order"
            << " reason_order_map[result.reason] " << reason_order_map[result.reason]
            << " result.reason " << result.reason;
        context->AppendIntListItemAttr(result, append_reason_order_attr_accessor_,
            reason_order_map[result.reason]);
      }
    }

    uint64 dedup_key;
    if (!GetDedupKey(result, &dedup_key)) {
      distinct_item_num++;
      return false;
    }
    auto reason_map_iter = reason_map.find(dedup_key);
    if (reason_map_iter == reason_map.end()) {
      reason_map[dedup_key] = result.reason;
    } else {
      int old_reason = reason_map_iter->second;
      reason_map_iter->second = GetDedupReason(old_reason, result.reason);
    }
    bool is_dup = ++dedup_map[dedup_key] > 1;
    if (!is_dup) {
      distinct_item_num++;
    }
    return is_dup;
  });

  std::for_each(begin, new_end, [this](CommonRecoResult &result) {
    uint64 dedup_key;
    if (GetDedupKey(result, &dedup_key)) {
      result.reason = reason_map[dedup_key];
      if (save_dup_count_attr_accessor_) {
        auto val = result.GetIntAttr(save_dup_count_attr_accessor_);
        int64 base_count = val ? *val : 0;
        result.SetIntAttr(save_dup_count_attr_accessor_, dedup_map[dedup_key] + base_count);
      }
    }
  });

  int64 survival_count = std::distance(begin, new_end);
  int64 filtered_count = std::distance(new_end, end);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(survival_count, kPerfNs, "deduplicate_remain",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  base::perfutil::PerfUtilWrapper::IntervalLogStash(filtered_count, kPerfNs, "deduplicate_filter",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), GetName());
  return new_end;
}

int CommonRecoResultsDeduplicateArranger::GetDedupReason(int old_reason, int new_reason) {
  if (!priority_map_.empty()) {
    auto old_iter = priority_map_.find(old_reason);
    auto new_iter = priority_map_.find(new_reason);
    if (old_iter != priority_map_.end() && new_iter == priority_map_.end()) {
      if (old_iter->second > 0) {
        return old_reason;
      }
      return new_reason;
    }
    if (old_iter == priority_map_.end() && new_iter != priority_map_.end()) {
      if (new_iter->second > 0) {
        return new_reason;
      }
      return old_reason;
    }
    if (old_iter != priority_map_.end() && new_iter != priority_map_.end()) {
      if (old_iter->second >= new_iter->second) {
        return old_reason;
      } else {
        return new_reason;
      }
    }
  }
  if (reason_priority_mode_ == ReasonPriorityMode::GREATER) {
    return old_reason >= new_reason ? old_reason : new_reason;
  } else if (reason_priority_mode_ == ReasonPriorityMode::LESS) {
    return old_reason <= new_reason ? old_reason : new_reason;
  } else {
    return old_reason;
  }
}

bool CommonRecoResultsDeduplicateArranger::GetDedupKey(const CommonRecoResult &result, uint64 *dedup_key) {
  if (!dedup_attr_accessor_) {
    *dedup_key = result.item_key;
    return true;
  }

  if (auto p = result.GetIntAttr(dedup_attr_accessor_)) {
    *dedup_key = *p;
    return true;
  } else if (auto p = result.GetStringAttr(dedup_attr_accessor_)) {
    *dedup_key = base::CityHash64(p->data(), p->size());
    return true;
  }

  return false;
}

void CommonRecoResultsDeduplicateArranger::PerfItemReasonRepetition(MutableRecoContextInterface *context,
                                                                    RecoResultIter begin,
                                                                    RecoResultIter end) {
  folly::F14FastMap<uint64, std::pair<int, bool>> item_repetition_map;
  folly::F14FastMap<int, std::pair<int, int>> reason_repetition_count_map;

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto p = item_repetition_map.insert({result.item_key, {result.reason, false}});
    if (!p.second && p.first->second.first != result.reason) {
      p.first->second.second = true;
    }
    reason_repetition_count_map[result.reason].first++;
  });

  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    if (item_repetition_map[result.item_key].second) {
      reason_repetition_count_map[result.reason].second++;
    }
  });

  for (const auto &it : reason_repetition_count_map) {
    if (it.second.first != 0) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          it.second.second * 1000 / it.second.first, kPerfNs, "reason_repetition",
          GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
          base::IntToString(it.first));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoResultsDeduplicateArranger, CommonRecoResultsDeduplicateArranger)

}  // namespace platform
}  // namespace ks
