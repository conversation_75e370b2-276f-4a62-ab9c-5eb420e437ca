#include "dragon/src/processor/common/arranger/common_reco_sticky_arranger.h"

#include <algorithm>
#include <map>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "dragon/src/util/logging_util.h"

namespace ks {
namespace platform {

RecoResultIter CommonRecoStickyArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                 RecoResultIter end) {
  SortByPosition(context, begin, end);
  return end;
}

int CommonRecoStickyArranger::GetStickyPosition(const CommonRecoResult &result, int position,
                                                const std::unordered_set<int> &sticky_reasons,
                                                ItemAttr *position_attr_accessor) const {
  if (position >= 0 && sticky_reasons.find(result.reason) != sticky_reasons.end()) {
    return position;
  }

  if (position_attr_accessor) {
    auto p = result.GetIntAttr(position_attr_accessor);
    if (p && *p >= 0) {
      return *p;
    }
  }

  return -1;
}

// NOTE(hechao): 通过指定位置的方式实现置顶，可以放到任何一个位置
void CommonRecoStickyArranger::SortByPosition(MutableRecoContextInterface *context, RecoResultIter begin,
                                              RecoResultIter end) const {
  std::unordered_map<uint64, int> sticky_positions;
  // 为保证插入顺序，额外用一个 vector 保存 sticky_positions 中 key 的先后顺序
  std::vector<uint64> sticky_item_keys;

  // 最高优保证 request 中的强插数据生效
  for (const auto &pr : context->GetForceInsertMap()) {
    if (pr.second >= 0) {
      auto status = sticky_positions.insert(std::make_pair(pr.first, pr.second));
      if (status.second) {
        sticky_item_keys.push_back(pr.first);
      }
    }
  }

  std::unordered_set<int> sticky_reasons;
  int reason = GetIntProcessorParameter(context, "reason", -1);
  if (reason >= 0) {
    sticky_reasons.insert(reason);
  } else {
    std::vector<int64> reasons = GetIntListProcessorParameter(context, "reason");
    for (auto reason : reasons) {
      if (reason >= 0) {
        sticky_reasons.insert(reason);
      }
    }
  }
  // 补充 processor 配置的强插数据
  int position = GetIntProcessorParameter(context, "position", -1);
  auto *position_attr_accessor =
      position_from_attr_.empty() ? nullptr : context->GetItemAttrAccessor(position_from_attr_);
  std::for_each(begin, end,
                [this, position, position_attr_accessor, &sticky_reasons, &sticky_positions,
                 &sticky_item_keys](const CommonRecoResult &result) {
                  int pos = GetStickyPosition(result, position, sticky_reasons, position_attr_accessor);
                  if (pos >= 0) {
                    auto status = sticky_positions.insert(std::make_pair(result.item_key, pos));
                    if (status.second) {
                      sticky_item_keys.push_back(result.item_key);
                    }
                  }
                });

  if (sticky_positions.empty()) {
    CL_LOG(INFO) << "force_insert cancelled: empty sticky_positions.";
    return;
  }

  std::vector<std::pair<uint64, int>> orders;
  orders.reserve(sticky_positions.size());
  for (const auto &item_key : sticky_item_keys) {
    auto it = sticky_positions.find(item_key);
    if (it != sticky_positions.end()) {
      orders.emplace_back(item_key, it->second);
    }
  }

  std::stable_sort(orders.begin(), orders.end(),
                   [](const auto &a, const auto &b) -> bool { return a.second < b.second; });
  int sticky_limit = GetIntProcessorParameter(context, "limit");
  if (sticky_limit > 0 && sticky_limit < orders.size()) {
    orders.resize(sticky_limit);
  }

  // position 可能有重复值, 这里进行唯一化
  for (int i = 1, last_occupied_pos = orders[0].second; i < orders.size(); ++i) {
    if (orders[i].second <= last_occupied_pos) {
      orders[i].second = ++last_occupied_pos;
    } else {
      last_occupied_pos = orders[i].second;
    }
  }

  const int total_size = std::distance(begin, end);

  sticky_positions.clear();
  for (const auto &order : orders) {
    if (order.second < total_size) {
      sticky_positions.insert(order);
    } else {
      CL_LOG(WARNING) << "ignored force insert item for position=" << order.second
                      << " >= total_size=" << total_size;
    }
  }

  std::vector<CommonRecoResult> plain_results;
  plain_results.reserve(total_size);
  std::map<int, CommonRecoResult> sticky_results;
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto it = sticky_positions.find(result.item_key);
    if (it == sticky_positions.end()) {
      plain_results.push_back(result);
    } else {
      auto pr = sticky_results.insert({it->second, result});
      if (reset_reason_ >= 0 && pr.second) {
        pr.first->second.reason = reset_reason_;
      }
      sticky_positions.erase(it);
    }
  });

  if (total_size != plain_results.size() + sticky_results.size()) {
    CL_LOG_ERROR("force_insert", "results_size_mismatch")
        << "[SHOULD NOT HAPPEN] force_insert cancelled: final_results size mismatch! original_results="
        << total_size << " vs final_results=" << plain_results.size() + sticky_results.size();
    return;
  }

  // 按序归并强插和非强插两个队列结果
  auto plain_iter = plain_results.cbegin();
  auto sticky_iter = sticky_results.cbegin();
  for (auto it = begin; it != end; ++it) {
    int pos = std::distance(begin, it);
    if (sticky_iter != sticky_results.cend() && sticky_iter->first == pos) {
      *it = (sticky_iter++)->second;
      CL_LOG_EVERY_N(INFO, 103) << "force inserted item_id=" << it->GetId() << " to position=" << pos;
    } else {
      *it = *(plain_iter++);
    }
  }

  CL_LOG(INFO) << "force inserted " << sticky_results.size() << " items";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStickyArranger, CommonRecoStickyArranger)

}  // namespace platform
}  // namespace ks
