#pragma once

#include <string>
#include <vector>

#include "folly/String.h"
#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {

class CommonRecoCommonAttrFilterArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoCommonAttrFilterArranger() {}
  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override {
    if (!RecoUtil::ExtractStringListFromJsonConfig(config()->Get("common_attr"), &common_attrs_)) {
      LOG(ERROR) << "CommonRecoCommonAttrFilterArranger init failed! 'common_attr' should be a string array.";
      return false;
    }
    exclude_ = config()->GetBoolean("exclude", true);
    on_item_attr_ = config()->GetString("on_item_attr");
    filter_info_ =  on_item_attr_ + (exclude_ ? " in " : " not in ")
                    + "[" + folly::join(",", common_attrs_) + "]";
    return true;
  }

 private:
  std::vector<std::string> common_attrs_;
  bool exclude_ = true;
  std::string on_item_attr_;
  std::string filter_info_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoCommonAttrFilterArranger);
};

}  // namespace platform
}  // namespace ks
