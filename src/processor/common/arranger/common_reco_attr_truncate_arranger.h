#pragma once
#include <string>
#include <unordered_map>

#include "dragon/src/processor/base/common_reco_base_arranger.h"

namespace ks {
namespace platform {

class CommonRecoAttrTruncateArranger : public CommonRecoBaseArranger {
 public:
  CommonRecoAttrTruncateArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  struct TruncateConfig {
    const base::Json *ratio = nullptr;
    const base::Json *limit = nullptr;
    int32 mode = 0;
  };

  bool InitProcessor() override {
    limit_config_ = config()->Get("size_limit");
    if (!config()->GetString("attr_name", &attr_name_)) {
      LOG(ERROR) << "CommonRecoAttrTruncateArranger"
                 << " init failed! Attr attr_name should be a string!";
      return false;
    }
    config()->GetBoolean("allow_overflow", &allow_overflow_);
    config_map_.clear();
    auto *queues = config()->Get("queues");
    if (queues && queues->IsArray() && queues->array().size() > 0) {
      for (const auto *attr_json : queues->array()) {
        if (!attr_json->IsObject()) {
          LOG(WARNING) << "CommonRecoAttrTruncateArranger queue ignored!"
                       << " Item of queues should be a dict! Value found: " << attr_json->ToString();
          continue;
        }
        std::string mode_string = "default";
        int64 mode = 0;
        if (attr_json->Get("mode")) {
          if (!attr_json->GetInt("mode", &mode)) {
            if (attr_json->GetString("mode", &mode_string)) {
              if (mode_string == "max") {
                mode = 1;
              } else if (mode_string == "min") {
                mode = 2;
              } else if (mode_string == "default") {
                mode = 0;
              } else {
                LOG(WARNING) << "CommonRecoAttrTruncateArranger config error!"
                             << " Attr mode should be min, max or default!"
                             << " Value found: " << mode_string;
              }
            } else {
              std::string value_str = attr_json->Get("mode")->ToString();
              LOG(WARNING) << "CommonRecoAttrTruncateArranger config error!"
                           << " init failed! Attr mode should be int or string!"
                           << " Value found: " << value_str;
            }
          }
        }
        uint64 attr_value;
        int64 attr_int_value;
        std::string attr_str_value;
        if (attr_json->GetInt("attr_value", &attr_int_value)) {
          attr_value = attr_int_value;
        } else if (attr_json->GetString("attr_value", &attr_str_value)) {
          attr_value = base::CityHash64(attr_str_value.data(), attr_str_value.size());
        } else {
          continue;
        }
        const base::Json *limit = attr_json->Get("limit");
        const base::Json *ratio = attr_json->Get("ratio");

        config_map_[attr_value].limit = limit;
        config_map_[attr_value].ratio = ratio;
        config_map_[attr_value].mode = mode;
      }
    }
    return true;
  }

 private:
  std::string attr_name_;
  bool allow_overflow_ = true;
  std::unordered_map<uint64, TruncateConfig> config_map_;
  const base::Json *limit_config_ = nullptr;
  DISALLOW_COPY_AND_ASSIGN(CommonRecoAttrTruncateArranger);
};

}  // namespace platform
}  // namespace ks
