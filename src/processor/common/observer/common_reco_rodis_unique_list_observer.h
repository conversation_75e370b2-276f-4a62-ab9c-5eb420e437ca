#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_observer.h"
#include "ks/ds/proto/userprofile/rodis.kess.grpc.pb.h"

namespace ks {
namespace platform {

class CommonRecoRodisUniqueListObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoRodisUniqueListObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

  bool IsAsync() const {
    return true;
  }

  const std::string &GetDownstreamProcessor() const final {
    static const std::string fixed_str = "__none";
    return fixed_str;
  }

 private:
  bool InitProcessor() override {
    kess_name_ = config()->GetString("kess_name");
    if (kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! kess_name is required.";
      return false;
    }
    domain_ = config()->GetString("domain");
    if (domain_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! domain is required.";
      return false;
    }
    payload_id_ = config()->GetInt("payload_id", payload_id_);
    if (payload_id_ < 0) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! payload_id is required.";
      return false;
    }
    timeout_ms_ = config()->GetInt("timeout_ms", timeout_ms_);
    key_attr_ = config()->GetString("key_attr");
    if (key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! key_attr is required.";
      return false;
    }
    unique_key_attr_ = config()->GetString("unique_key_attr");
    if (unique_key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! unique_key_attr is required.";
      return false;
    }
    sort_key_attr_ = config()->GetString("sort_key_attr");
    if (sort_key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! sort_key_attr is required.";
      return false;
    }
    data_attr_ = config()->GetString("data_attr");
    if (data_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisUniqueListObserver init failed! data_attr is required.";
      return false;
    }
    return true;
  }

 private:
  std::string kess_name_;
  std::string domain_;
  int payload_id_ = -1;
  int timeout_ms_ = 10;
  std::string key_attr_;
  std::string unique_key_attr_;
  std::string sort_key_attr_;
  std::string data_attr_;
  ::kuaishou::ds::BatchUpdateRequest request_;
  ::kuaishou::ds::BatchUpdateResponse response_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRodisUniqueListObserver);
};

}  // namespace platform
}  // namespace ks
