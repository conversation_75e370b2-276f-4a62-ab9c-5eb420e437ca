#include "dragon/src/processor/common/observer/common_reco_rodis_attr_observer.h"

#include <utility>

#include "absl/strings/str_format.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

void CommonRecoRodisAttrObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                          RecoResultConstIter end) {
  if (is_common_) {
    HandleCommonAttr(context);
  } else {
    batch_request_.clear_request();
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      HandleItemAttr(context, result, context->GetItemAttr(key_attr_), context->GetItemAttr(value_attr_));
    });
    if (batch_request_.request_size() == 0) {
      return;
    }
    auto pr = [&]() {
      KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_name_, "PRODUCTION", "s0", timeout_ms_, batch_request_,
                                             &batch_response_, ::kuaishou::ds::kess::RodisService,
                                             AsyncBatchUpdate);
    }();

    if (!pr.first) {
      CL_LOG(WARNING) << "Request rodis service failed: " << kess_name_;
      return;
    }

    RegisterAsyncCallback(context, std::move(pr.second), [](::kuaishou::ds::BatchUpdateResponse *response) {
      VLOG(1) << "response: " << response->DebugString();
      VLOG(1) << "Get response: err_code=" << response->err_code();
    });
  }
}
void CommonRecoRodisAttrObserver::HandleCommonAttr(ReadableRecoContextInterface *context) {
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }
  auto value_val = context->GetStringCommonAttr(value_attr_);
  if (!value_val) {
    CL_LOG(WARNING) << "unsupported value_attr(" << (int)context->GetCommonAttrType(value_attr_)
                    << "): " << value_attr_;
    return;
  }
  request_.clear_item();
  auto *item = request_.add_item();
  item->set_ts(context->GetRequestTime());
  item->set_data(value_val->data(), value_val->size());
  request_.set_key(key);

  VLOG(1) << "request: " << request_.DebugString();

  auto pr = [&]() {
    KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_name_, "PRODUCTION", "s0", timeout_ms_, request_, &response_,
                                           ::kuaishou::ds::kess::RodisService, AsyncTimeListAppend);
  }();

  if (!pr.first) {
    CL_LOG(WARNING) << "Request rodis service failed: " << kess_name_;
    return;
  }

  RegisterAsyncCallback(context, std::move(pr.second), [](::kuaishou::ds::TimeListAppendResponse *response) {
    VLOG(1) << "response: " << response->DebugString();
    VLOG(1) << "Get response: err_code=" << response->err_code();
  });
}
void CommonRecoRodisAttrObserver::HandleItemAttr(ReadableRecoContextInterface *context,
                                                 const CommonRecoResult &result, ItemAttr *key_attr_accessor,
                                                 ItemAttr *value_attr_accessor) {
  std::string key;
  if (auto str_val = context->GetStringItemAttr(result, key_attr_accessor)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntItemAttr(result, key_attr_accessor)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetItemAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }
  auto value_val = context->GetStringItemAttr(result, value_attr_accessor);
  if (!value_val) {
    CL_LOG(WARNING) << "unsupported value_attr(" << (int)context->GetItemAttrType(value_attr_)
                    << "): " << value_attr_;
    return;
  }
  auto *request = batch_request_.add_request();
  request->set_domain(domain_);
  request->set_key(key);
  auto *update = request->add_update();
  update->set_payload_id(payload_id_);
  auto *time_list = update->mutable_time_list();
  auto *item = time_list->add_item();
  item->set_ts(context->GetRequestTime());
  item->set_data(value_val->data(), value_val->size());
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisAttrObserver, CommonRecoRodisAttrObserver)

}  // namespace platform
}  // namespace ks
