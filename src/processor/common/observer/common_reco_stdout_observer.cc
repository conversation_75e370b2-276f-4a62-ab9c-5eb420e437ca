#include "dragon/src/processor/common/observer/common_reco_stdout_observer.h"

#include <utility>

#include "ks/base/abtest/metrics/abtest_metric.h"
#include "serving_base/util/hash.h"

namespace ks {
namespace platform {

std::once_flag CommonRecoStdoutObserver::create_file_writer_;
SafeFileWriter CommonRecoStdoutObserver::file_writer_;

void CommonRecoStdoutObserver::DoWrite(absl::string_view msg) {
  if (!output_file_.empty() && file_writer_.Good()) {
    file_writer_.Write(msg.data(), msg.size());
    file_writer_.Write("\n");
  } else {
    std::cout << msg << std::endl;
  }
}

void CommonRecoStdoutObserver::WriteToStdout(absl::string_view msg, bool base64_encode) {
  if (base64_encode) {
    std::string base64_msg;
    if (base::Base64Encode(msg.data(), msg.size(), &base64_msg)) {
      DoWrite(base64_msg);
    } else {
      CL_LOG_ERROR("stdout", "base64_fail") << "Failed to base64 encode message: " << msg;
    }
  } else {
    DoWrite(msg);
  }
}

void CommonRecoStdoutObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  const ::google::protobuf::Message *msg_value;

  if (auto str_value = context->GetStringCommonAttr(common_attr_)) {
    WriteToStdout(*str_value, base64_encode_);
  } else if (auto str_list_value = context->GetStringListCommonAttr(common_attr_)) {
    for (auto msg : *str_list_value) {
      WriteToStdout(msg, base64_encode_);
    }
  } else if ((msg_value = context->GetPtrCommonAttr<::google::protobuf::Message>(common_attr_))) {
    std::string encoded_msg;
    if (msg_value->SerializeToString(&encoded_msg)) {
      WriteToStdout(encoded_msg, true);
    } else {
      CL_LOG_ERROR("stdout", "serialize_fail")
          << "Failed to serialize protobuf[" << common_attr_ << "]: " << msg_value->DebugString();
    }
  } else {
    CL_LOG(WARNING) << "Failed to write message to stdout as unsupported type or missing attr: "
                    << common_attr_ << "(" << int(context->GetCommonAttrType(common_attr_)) << ")";
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoStdoutObserver, CommonRecoStdoutObserver)

}  // namespace platform
}  // namespace ks
