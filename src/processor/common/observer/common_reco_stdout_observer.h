#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>
#include <mutex>

#include "base/encoding/base64.h"

#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks {
namespace platform {

class SafeFileWriter {
 public:
  SafeFileWriter() {}
  ~SafeFileWriter() {
    if (ofile_.is_open()) {
      ofile_.flush();
      ofile_.close();
    }
  }

  bool Open(const std::string& file_path) {
    ofile_.open(file_path, std::ios::out);
    if (ofile_.is_open()) {
      LOG(INFO) << "succeed to open file : " << file_path;
    } else {
      LOG(INFO) << "failed to open file : " << file_path;
    }
    return ofile_.is_open();
  }

  inline void Write(const char* data, size_t data_size) {
    thread::AutoLock lock(&mutex_);
    ofile_.write(data, data_size);
  }

  inline void Write(const std::string& data) {
    return Write(data.data(), data.size());
  }

  inline bool Good() const {
    return ofile_.is_open() && ofile_.good();
  }

 private:
  std::fstream ofile_;
  mutable thread::Mutex mutex_;
};

class CommonRecoStdoutObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoStdoutObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    common_attr_ = config()->GetString("common_attr");
    if (common_attr_.empty()) {
      LOG(ERROR) << "CommonRecoStdoutObserver init failed! common_attr is required.";
      return false;
    }

    base64_encode_ = config()->GetBoolean("base64_encode", false);
    output_file_ = config()->GetString("output_file", "");

    if (!output_file_.empty()) {
      std::call_once(create_file_writer_, [this]() { file_writer_.Open(output_file_); });
    }

    return true;
  }

  void WriteToStdout(absl::string_view msg, bool base64_encode);
  void DoWrite(absl::string_view msg);

 private:
  std::string common_attr_;
  bool base64_encode_ = false;

  std::string output_file_;
  static SafeFileWriter file_writer_;
  static std::once_flag create_file_writer_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoStdoutObserver);
};

}  // namespace platform
}  // namespace ks
