#pragma once

#include <string>

#include "dragon/src/processor/base/common_reco_base_observer.h"
#include "ks/ds/proto/userprofile/rodis.kess.grpc.pb.h"

namespace ks {
namespace platform {

class CommonRecoRodisAttrObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoRodisAttrObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

  bool IsAsync() const {
    return true;
  }

  const std::string &GetDownstreamProcessor() const final {
    static const std::string fixed_str = "__none";
    return fixed_str;
  }

 private:
  bool InitProcessor() override {
    kess_name_ = config()->GetString("kess_name");
    if (kess_name_.empty()) {
      LOG(ERROR) << "CommonRecoRodisAttrObserver init failed! kess_name is required.";
      return false;
    }

    domain_ = config()->GetString("domain");
    if (domain_.empty()) {
      LOG(ERROR) << "CommonRecoRodisAttrObserver init failed! domain is required.";
      return false;
    }

    payload_id_ = config()->GetInt("payload_id", payload_id_);
    if (payload_id_ < 0) {
      LOG(ERROR) << "CommonRecoRodisAttrObserver init failed! payload_id is required.";
      return false;
    }

    timeout_ms_ = config()->GetInt("timeout_ms", timeout_ms_);

    key_attr_ = config()->GetString("key_attr");
    if (key_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisAttrObserver init failed! key_attr is required.";
      return false;
    }

    value_attr_ = config()->GetString("value_attr");
    if (value_attr_.empty()) {
      LOG(ERROR) << "CommonRecoRodisAttrObserver init failed! value_attr is required.";
      return false;
    }

    is_common_ = config()->GetBoolean("is_common", true);

    request_.set_domain(domain_);
    request_.set_payload_id(payload_id_);

    return true;
  }

 private:
  void HandleCommonAttr(ReadableRecoContextInterface *context);
  void HandleItemAttr(ReadableRecoContextInterface *context, const CommonRecoResult &result,
                      ItemAttr *key_attr_accessor, ItemAttr *value_attr_accessor);
  std::string kess_name_;
  std::string domain_;
  int payload_id_ = -1;
  int timeout_ms_ = 10;
  std::string key_attr_, value_attr_;
  bool is_common_ = true;
  ::kuaishou::ds::TimeListAppendRequest request_;
  ::kuaishou::ds::TimeListAppendResponse response_;
  ::kuaishou::ds::BatchUpdateRequest batch_request_;
  ::kuaishou::ds::BatchUpdateResponse batch_response_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoRodisAttrObserver);
};

}  // namespace platform
}  // namespace ks
