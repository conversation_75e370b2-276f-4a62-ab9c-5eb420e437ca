#include "dragon/src/processor/common/observer/common_reco_rodis_unique_list_observer.h"

#include <utility>

#include "absl/strings/str_format.h"
#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

void CommonRecoRodisUniqueListObserver::Observe(ReadableRecoContextInterface *context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
  std::string key;
  if (auto str_val = context->GetStringCommonAttr(key_attr_)) {
    key = std::string(*str_val);
  } else if (auto int_val = context->GetIntCommonAttr(key_attr_)) {
    key = absl::StrFormat("%d", *int_val);
  } else {
    CL_LOG(WARNING) << "unsupported key_attr(" << (int)context->GetCommonAttrType(key_attr_)
                    << "): " << key_attr_;
    return;
  }
  auto unique_key_list = context->GetStringListCommonAttr(unique_key_attr_);
  if (!unique_key_list) {
    CL_LOG(WARNING) << "unsupported unique_key_attr(" << (int)context->GetCommonAttrType(unique_key_attr_)
                    << "): " << unique_key_attr_;
    return;
  }
  auto sort_key_list = context->GetStringListCommonAttr(sort_key_attr_);
  if (!sort_key_list) {
    CL_LOG(WARNING) << "unsupported sort_key_attr(" << (int)context->GetCommonAttrType(sort_key_attr_)
                    << "): " << sort_key_attr_;
    return;
  }
  auto data_attr_list = context->GetStringListCommonAttr(data_attr_);
  if (!data_attr_list) {
    CL_LOG(WARNING) << "unsupported data_attr(" << (int)context->GetCommonAttrType(data_attr_)
                    << "): " << data_attr_;
    return;
  }
  if (unique_key_list->size() != sort_key_list->size() || sort_key_list->size() != data_attr_list->size()) {
    CL_LOG(WARNING) << "input item size invalid"
                    << " ,unique_key_attr size is" << unique_key_list->size()
                    << " ,sort_key_attr size is" << sort_key_list->size()
                    << " ,data_attr size is" << data_attr_list->size();
    return;
  }

  request_.clear_request();
  auto *update_req = request_.add_request();
  update_req->set_domain(domain_);
  update_req->set_key(key);
  auto *payload_req = update_req->add_update();
  payload_req->set_payload_id(payload_id_);
  payload_req->set_op(::kuaishou::ds::PayloadUpdateOperation::kUniqueListAppend);
  for (auto i = 0; i < unique_key_list->size(); ++i) {
    auto *unique_item = payload_req->mutable_unique_list()->add_item();
    unique_item->set_unique_key(std::string((*unique_key_list)[i]));
    unique_item->set_sort_key(std::string((*sort_key_list)[i]));
    unique_item->set_data(std::string((*data_attr_list)[i]));
    unique_item->set_strategy(::kuaishou::ds::UniqueListItem::kUpdateDataAndSortKey);
  }
  VLOG(1) << "request: " << request_.DebugString();
  auto pr = [&]() {
    KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN(kess_name_, "PRODUCTION", "s0", timeout_ms_, request_, &response_,
                                           ::kuaishou::ds::kess::RodisService, AsyncBatchUpdate);
  }();
  if (!pr.first) {
    CL_LOG(WARNING) << "request rodis service failed: " << kess_name_;
    return;
  }
  RegisterAsyncCallback(context, std::move(pr.second), [](::kuaishou::ds::BatchUpdateResponse *response) {
    VLOG(1) << "response: " << response->DebugString();
    VLOG(1) << "get response: err_code=" << response->err_code();
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoRodisUniqueListObserver, CommonRecoRodisUniqueListObserver)

}  // namespace platform
}  // namespace ks
