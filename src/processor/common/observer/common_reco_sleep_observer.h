#pragma once

#include "dragon/src/processor/base/common_reco_base_observer.h"

namespace ks {
namespace platform {

class CommonRecoSleepObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoSleepObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoSleepObserver);
};

}  // namespace platform
}  // namespace ks
