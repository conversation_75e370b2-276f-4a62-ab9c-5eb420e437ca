#pragma once
#include <algorithm>
#include <memory>
#include <string>
#include <unordered_set>
#include <vector>
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/module/async_task_thread_pool.h"
#include "dragon/src/module/traceback_util.h"
#include "dragon/src/processor/base/common_reco_base_observer.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "redis_proxy_client/redis_pipeline.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/util/math.h"

DECLARE_bool(lazy_init_redis_client);
DECLARE_int64(sub_flow_task_queue_max_size);
DECLARE_int64(redis_async_pool_task_queue_max_size);
DECLARE_int64(redis_async_pool_thread_num);
DECLARE_int64(redis_async_pool_min_thred_num);
DECLARE_int64(redis_async_pool_max_thred_num);
DECLARE_int64(redis_async_pool_min_thread_num);
DECLARE_int64(redis_async_pool_max_thread_num);

namespace ks {
namespace platform {
class CommonRecoWriteToRedisObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoWriteToRedisObserver() {}
  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

  bool IsAsync() const override {
    return true;
  }

 private:
  bool InitProcessor() override {
    kcc_cluster_ = config()->GetString("kcc_cluster", "");
    list_separator_ = config()->GetString("list_separator", ",");
    timeout_ms_ = config()->GetInt("timeout_ms", 10);
    expire_second_ = config()->Get("expire_second");
    key_ = config()->Get("key");
    key_from_item_attr_ = config()->Get("key_from_item_attr");
    value_ = config()->Get("value");
    value_from_item_attr_ = config()->Get("value_from_item_attr");
    key_prefix_ = config()->Get("key_prefix");

    if (!FLAGS_lazy_init_redis_client) {
      redis_client_ = GetRedisClient();
      if (!redis_client_) {
        LOG(ERROR) << "CommonRecoWriteToRedisObserver init failed! Redis client is null, cluster name: "
                   << kcc_cluster_;
        return false;
      }
    }

    std::call_once(oc_, [&]() {
      // NOTE(zhaoyang09): 以下操作为兼容 redis_async_pool_xxx_thred_num 错误拼写
      int min_thread_num = FLAGS_redis_async_pool_min_thred_num;
      if (FLAGS_redis_async_pool_min_thread_num > 0) {
        min_thread_num = FLAGS_redis_async_pool_min_thread_num;
      }
      int max_thread_num = FLAGS_redis_async_pool_max_thred_num;
      if (FLAGS_redis_async_pool_max_thread_num > 0) {
        max_thread_num = FLAGS_redis_async_pool_max_thread_num;
      }
      // NOTE END
      int queue_capacity = FLAGS_sub_flow_task_queue_max_size;
      if (FLAGS_redis_async_pool_thread_num > 0) {
        min_thread_num = FLAGS_redis_async_pool_thread_num;
        max_thread_num = FLAGS_redis_async_pool_thread_num;
      }
      if (FLAGS_redis_async_pool_task_queue_max_size > 0) {
        queue_capacity = FLAGS_redis_async_pool_task_queue_max_size;
      }
      local_async_pool_ = std::make_unique<AsyncTaskThreadPool<void *>>("WriteRedisTaskPool", min_thread_num,
                                                                        max_thread_num, queue_capacity);
      local_async_pool_->WaitForInitDone();
    });
    return true;
  }

  ks::infra::RedisPipelineClient *GetRedisClient() {
    auto *redis_client =
        ks::infra::RedisProxyClient::GetRedisPipelineClientByKccFromKconf(kcc_cluster_, timeout_ms_);
    if (!redis_client) {
      redis_client = ks::infra::RedisProxyClient::GetRedisPipelineClientByKcc(kcc_cluster_, timeout_ms_);
    }
    return redis_client;
  }

 private:
  bool WriteRedisForCommonAttrs(ReadableRecoContextInterface *context,
                                ks::infra::RedisPipeline *redis_pipeline, const std::string &key_prefix);
  bool WriteRedisForItemAttrs(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                              RecoResultConstIter end, ks::infra::RedisPipeline *redis_pipeline,
                              const std::string &key_prefix);
  bool SaveWithCommonAttr(ReadableRecoContextInterface *context, const std::vector<std::string> &keys,
                          ks::infra::RedisPipeline *redis_pipeline, const std::string &key_prefix);
  std::vector<std::string> GetCommonKey(ReadableRecoContextInterface *context);
  void SaveValueWithRedis(ks::infra::RedisPipeline *redis_pipeline, const std::string &key,
                          const std::string &value, int expire_second, const std::string &key_prefix);
  std::string kcc_cluster_ = "";
  std::string list_separator_ = ",";
  int timeout_ms_ = 10;
  ks::infra::RedisPipelineClient *redis_client_ = nullptr;
  static std::unique_ptr<AsyncTaskThreadPool<void *>> local_async_pool_;
  static std::once_flag oc_;

  const base::Json *expire_second_ = nullptr;
  const base::Json *key_ = nullptr;
  const base::Json *key_from_item_attr_ = nullptr;
  const base::Json *value_ = nullptr;
  const base::Json *value_from_item_attr_ = nullptr;
  const base::Json *key_prefix_ = nullptr;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoWriteToRedisObserver);
};
}  // namespace platform
}  // namespace ks
