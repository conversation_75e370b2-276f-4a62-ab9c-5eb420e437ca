#pragma once

#include "dragon/src/processor/base/common_reco_base_observer.h"

#include <string>

namespace ks {
namespace platform {

class CommonRecoReasonCountPerflogObserver : public CommonRecoBaseObserver {
 public:
  CommonRecoReasonCountPerflogObserver() {}

  void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
               RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    log_info_from_attr_ = config()->GetString("log_info_from_attr");
    return true;
  }

 private:
  std::string log_info_from_attr_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoReasonCountPerflogObserver);
};

}  // namespace platform
}  // namespace ks
