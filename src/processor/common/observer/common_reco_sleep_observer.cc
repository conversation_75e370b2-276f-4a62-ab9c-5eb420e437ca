#include "dragon/src/processor/common/observer/common_reco_sleep_observer.h"
#include "base/common/sleep.h"

namespace ks {
namespace platform {

void CommonRecoSleepObserver::Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                      RecoResultConstIter end) {
  int sleep_ms = GetIntProcessorParameter(context, "sleep_ms", 0);
  if (sleep_ms > 0) {
    CL_LOG(INFO) << "sleep for " << sleep_ms << " ms...";
    base::SleepForMilliseconds(sleep_ms);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoSleepObserver, CommonRecoSleepObserver)

}  // namespace platform
}  // namespace ks
