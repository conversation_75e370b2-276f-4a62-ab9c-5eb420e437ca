#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {

class AdRewriteTopkIndexEnricher : public CommonRecoBaseEnricher {
 public:
  AdRewriteTopkIndexEnricher() {}
  ~AdRewriteTopkIndexEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  std::pair<std::vector<int64>, std::vector<double>> RewriteIndex(
    const std::vector<int64>& topk_indices,
    const std::vector<double>& topk_values,
    const int64 max_len,
    const int64 limit_num);

 private:
  std::string topk_indices_attr_;
  std::string topk_values_attr_;
  std::string origin_item_id_list_attr_;
  std::string output_topk_indices_attr_;
  std::string output_topk_values_attr_;
  std::string output_topk_values_int_attr_;

  int limit_num_ = 100;
  int64 max_len_ = 10000;
  bool output_int_value_ = false;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(AdRewriteTopkIndexEnricher);
};

}  // namespace platform
}  // namespace ks
