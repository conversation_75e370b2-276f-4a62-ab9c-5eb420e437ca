#include "dragon/src/processor/ext/ad/gsu/enricher/ad_rewrite_topk_index_enricher.h"

#include <algorithm>
#include <iostream>
#include <unordered_map>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"

namespace ks {
namespace platform {

std::pair<std::vector<int64>, std::vector<double>> AdRewriteTopkIndexEnricher::RewriteIndex(
  const std::vector<int64>& topk_indices,
  const std::vector<double>& topk_values,
  const int64 max_len,
  const int64 limit_num) {

  std::vector<int64> indices;
  std::vector<double> values;
  std::unordered_set<int64> seen;
  
  // max_len 是 topk 之前的原始序列的长度， max_len - 1 是可能出现的最大的 index 
  int64 limit = std::min(max_len, limit_num);

  // Step 1: 合成并排序 items
  struct Item {
    int64 idx;
    double val;
  };
  std::vector<Item> items;
  for (size_t i = 0; i < topk_indices.size(); ++i) {
    if (topk_values[i] > 0) {
      items.push_back({topk_indices[i], topk_values[i]});
    }
  }

  std::sort(items.begin(), items.end(), [](const Item& a, const Item& b) {
    if (a.val == b.val) return a.idx < b.idx;
    return a.val > b.val;
  });

  // Step 2: 插入前 limit 个不重复 index
  for (const auto& item : items) {
    int64 idx = item.idx;
    if (seen.count(idx) == 0) {
      indices.push_back(idx);
      values.push_back(item.val);
      seen.insert(idx);
      if (static_cast<int64>(indices.size()) >= limit) {
        // int64 cnt = static_cast<int64>(values.size());
        return std::make_pair(std::move(indices), std::move(values));
      }
    }
  }

  // Step 3: 如果 indices 不足 limit，则补全
  for (int i = 0; i < max_len; ++i) {
    if (seen.count(i) == 0) {
      indices.push_back(i);
      values.push_back(0.0);
      if (static_cast<int64>(indices.size()) >= limit) {
        break;
      }
    }
  }

  // int64 cnt = static_cast<int64>(values.size());
  return std::make_pair(std::move(indices), std::move(values));
}


bool AdRewriteTopkIndexEnricher::InitProcessor() {
  topk_indices_attr_ = config()->GetString("topk_indices", "");
  topk_values_attr_ = config()->GetString("topk_values", "");
  output_topk_indices_attr_ = config()->GetString("output_topk_indices", "");
  output_topk_values_attr_ = config()->GetString("output_topk_values", "");
  if (topk_indices_attr_.empty()) {
    CL_LOG(ERROR) << "miss topk_indices_attr";
    return false;
  }
  if (topk_values_attr_.empty()) {
    CL_LOG(ERROR) << "miss topk_values_attr";
    return false;
  }
  if (output_topk_indices_attr_.empty()) {
    CL_LOG(ERROR) << "miss output_topk_indices_attr";
    return false;
  }
  if (output_topk_values_attr_.empty()) {
    CL_LOG(ERROR) << "miss output_topk_values_attr";
    return false;
  }

  limit_num_ = config()->GetInt("limit_num", 100);
  max_len_ = config()->GetInt("max_len", 10000);
  if (limit_num_ < 1 || max_len_ < 1) {
    CL_LOG(ERROR) << "wrong limit_num or max_len";
    return false;
  }

  output_int_value_ = config()->GetBoolean("output_int_value", false);
  output_topk_values_int_attr_ = config()->GetString("output_topk_values_int", "");
  if (output_int_value_ && output_topk_values_int_attr_.empty()) {
    CL_LOG(ERROR) << "miss output_topk_values_int";
    return false;
  }

  return true;
}

void AdRewriteTopkIndexEnricher::Enrich(MutableRecoContextInterface *context,
    RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  for (auto it = begin; it != end; ++it) {
    const auto& topk_indices_list = context->GetIntListItemAttr(it->item_key, topk_indices_attr_);
    const auto& topk_values_list = context->GetDoubleListItemAttr(it->item_key, topk_values_attr_);
    
    if (!topk_indices_list || topk_indices_list->size() == 0) {
      FB_LOG_EVERY_MS(WARNING, 1000) << "topk_indices is empty!";
      continue;
    }
    if (!topk_values_list || topk_values_list->size() == 0) {
      FB_LOG_EVERY_MS(WARNING, 1000) << "topk_values is empty!";
      continue;
    }

    int64 length = topk_indices_list->size();
    if (topk_values_list->size() != length) {
      FB_LOG_EVERY_MS(WARNING, 1000) << "topk indices length not match: " << length
                    << ", topk values length: " << topk_values_list->size();
      continue;
    }

    // 将absl::Span转换为vector
    std::vector<int64> topk_indices_vec(topk_indices_list->begin(), topk_indices_list->end());
    std::vector<double> topk_values_vec(topk_values_list->begin(), topk_values_list->end());
    // topk_indices_vec.assign(topk_indices_list->begin(), topk_indices_list->end());
    // topk_values_vec.assign(topk_values_list->begin(), topk_values_list->end());

    // topk_indices_vec = std::vector<int64>(*topk_indices_list)

    auto pair_result = AdRewriteTopkIndexEnricher::RewriteIndex(topk_indices_vec, topk_values_vec, max_len_, limit_num_);
    auto& indices = pair_result.first;
    auto& values = pair_result.second;

    context->SetIntListItemAttr(it->item_key, output_topk_indices_attr_, std::move(indices));
    context->SetDoubleListItemAttr(it->item_key, output_topk_values_attr_, std::move(values));
    
    if (output_int_value_) {
      // 后处理：乘100、转int、clip到[0, 100]
      std::vector<int64> int_scores;
      int_scores.reserve(values.size());
      int_scores.clear();
      for (double v : values) {
        int64 score = std::min(100, std::max(0, static_cast<int64>(v * 100.0)));  // clip 到 [0, 100]
        int_scores.push_back(score);
      }

      context->SetIntListItemAttr(it->item_key, output_topk_values_int_attr_, std::vector<int64>(int_scores));
    }
  }
  timer_.AppendCostMs("fill_attr");
}
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdRewriteTopkIndexEnricher, AdRewriteTopkIndexEnricher);

}  // namespace platform
}  // namespace ks
