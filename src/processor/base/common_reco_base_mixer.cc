#include "dragon/src/processor/base/common_reco_base_mixer.h"

#include <memory>

namespace ks {
namespace platform {

bool CommonRecoBaseMixerEmbedPipeline::InitEmbeddedPipeline() {
  base::Json *pipeline_manager_config = GlobalHolder::GetDynamicJsonConfig()->Get("pipeline_manager_config");
  if (!pipeline_manager_config) {
    LOG(ERROR) << "pipeline_manager_config is null, you should set user_static_flags: "
               << "use_fake_json_config_first=true when using SampleServer, "
               << "because GlobalHolder::GetDynamicJsonConfig()="
               << base::JsonToString(GlobalHolder::GetDynamicJsonConfig()->get(), 2);
  }

  auto Initialize = [this](const base::Json *config,
                           const std::string &pipeline_name) -> std::unique_ptr<CommonRecoPipeline> {
    if (!config) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init error: no config.";
      return nullptr;
    }

    const auto *pipeline_map = config->Get("pipeline_map");
    if (!pipeline_map) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init error: no pipeline_map in config.";
      return nullptr;
    }

    const auto *pipeline_config = pipeline_map->Get(pipeline_name);
    if (!pipeline_config) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init error: pipeline " << pipeline_name
                 << " not exist in pipeline map.";
      return nullptr;
    }

    const auto *base_pipeline = config->Get("base_pipeline");
    if (!base_pipeline) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init error: get null base_pipeline config";
      return nullptr;
    }

    const auto *processor_config = base_pipeline->Get("processor");
    if (!processor_config) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init error: get null processor config in base_pipeline";
      return nullptr;
    }

    const auto *combo_processor_config = base_pipeline->Get("combo_processor");

    auto pipeline = std::make_unique<CommonRecoPipeline>(pipeline_name);
    if (!pipeline->Initialize(pipeline_config, processor_config, combo_processor_config)) {
      LOG(ERROR) << "SinglePipelineExecutor init error: new pipeline " << pipeline_name << " failed";
      return nullptr;
    }
    // processor_num_ = pipeline_->GetProcessorRun().size();
    // context_.SetupAttrTypesFromConfig(GlobalHolder::GetDynamicJsonConfig().get());
    return pipeline;
  };

  auto *embedded_pipelines = config()->Get("embedded_pipelines");
  if (!embedded_pipelines || !embedded_pipelines->IsObject()) {
    return true;
  }

  for (const auto &kv : embedded_pipelines->objects()) {
    std::string alias = kv.first;
    std::string pipeline_name = kv.second->StringValue();
    if (pipeline_name.empty()) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init failed! embedded_pipelines[" << alias
                 << "] is not a string or empty";
      return false;
    }

    auto pipeline = Initialize(pipeline_manager_config, pipeline_name);

    if (!pipeline) {
      LOG(ERROR) << "CommonRecoBaseMixerEmbedPipeline init failed! Pipeline " << pipeline_name
                 << " initialize failed.";
      return false;
    }

    embedded_pipeline_map_.emplace(alias, std::move(pipeline));
  }

  return true;
}

bool CommonRecoBaseMixerEmbedPipeline::RunEmbeddedPipeline(AddibleRecoContextInterface *context,
                                                           const std::string &pipeline_name) {
  auto iter = embedded_pipeline_map_.find(pipeline_name);
  if (iter == embedded_pipeline_map_.end()) {
    LOG(WARNING) << "Cannot find embedded pipeline " << pipeline_name << " in processor " << GetName();
    return false;
  }
  auto *pipeline = iter->second.get();
  auto *real_context = static_cast<CommonRecoContext *>(context);
  real_context->SetMainTable("");
  pipeline->Execute(real_context, real_context->GetRecoResults());
  switch (context->GetExecutionStatus()) {
    case ExecutionStatus::SUCCESS: {
      context->SetExecutionStatus(ExecutionStatus::UNKNOWN);
      break;
    }
    case ExecutionStatus::UNKNOWN: {
      break;
    }
    case ExecutionStatus::FAILED:
    case ExecutionStatus::TERMINATED:
    case ExecutionStatus::CANCELLED:
    case ExecutionStatus::PERMISSION_DENIED: {
      // NOTE(weiyilong): 算子实现应该在本函数返回 false 时终止执行
      return false;
    }
  }
  real_context->SetRunningProcessor(this);
  return true;
}

}  // namespace platform
}  // namespace ks
