
#pragma once

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"
#include "third_party/croaring/roaring.hh"

namespace ks {
namespace platform {

/**
 * CommonRecoLeaf Arranger 的基类
 * Leaf 开发者应继承该类进行新 Arranger 的实现，针对场景：item
 * 打分、排序、过滤、截断、打散...
 */
class CommonRecoBaseArranger : public CommonRecoBaseProcessor {
 public:
  // NOTE(fangjianbing): 为防止结果状态异常，禁止异步的 Arranger 实现
  bool IsAsync() const final {
    return false;
  }
  const std::string &GetDownstreamProcessor() const final {
    static const std::string empty_str = "";
    return empty_str;
  }

  // 给 Leaf 开发人员实现具体 Arrange 逻辑的接口，返回指向新末尾的 Iterator
  virtual RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                 RecoResultIter end) = 0;

  void Run(AddibleRecoContextInterface *context, std::vector<CommonRecoResult> *results) final {
    std::string item_list_from_attr = GetItemListCommonAttr();
    thread_local std::vector<CommonRecoResult> alternative_items;
    alternative_items.clear();
    if (!item_list_from_attr.empty()) {
      auto p_int_list = context->GetIntListCommonAttr(item_list_from_attr);
      if (!p_int_list) {
        CL_LOG(INFO) << "int_list item_list_from_attr=" << item_list_from_attr
                     << " not found, cancelled run processor: " << GetName();
        return;
      }
      std::transform(p_int_list->begin(), p_int_list->end(), std::back_inserter(alternative_items),
                     [context](uint64 item_key) { return context->NewCommonRecoResult(item_key, -1); });
      results = &alternative_items;
    }

    if (results->empty()) {
      CL_LOG(INFO) << "empty results, cancelled run processor(Arranger): " << GetName();
      return;
    }

    int size = results->size();
    auto begin = std::begin(*results);
    auto end = std::end(*results);
    int range_start = GetRangeStart(context);
    int range_end = GetRangeEnd(context);
    auto first = range_start >= 0 ? std::next(begin, std::min(range_start, size))
                                  : std::prev(end, std::min(-range_start, size));
    auto last = range_end > 0 ? std::next(begin, std::min(range_end, size))
                              : std::prev(end, std::min(-range_end, size));
    if (last - first <= 0) {
      CL_LOG(INFO) << "empty item range, cancelled run processor(Arranger): " << GetName();
      return;
    }

    auto *input_results = results;
    auto input_first = first;
    auto input_last = last;

    thread_local std::vector<CommonRecoResult> target_results;
    thread_local std::vector<int> target_results_indices;
    target_results.clear();
    target_results_indices.clear();

    bool use_target_items = GetTargetItems(context, first, last, &target_results, &target_results_indices);
    bool use_select_items = GetSelectItems(context, first, last, &target_results, &target_results_indices);
    if (use_target_items || use_select_items) {
      input_first = std::begin(target_results);
      input_last = std::end(target_results);
    }

    if (input_last - input_first <= 0) {
      CL_LOG(INFO) << "empty target item range, cancelled run processor(Arranger): " << GetName();
      return;
    }

    if (FLAGS_record_filter_reason && !FLAGS_all_processor_record_filter_reason &&
        !config()->GetString("filter_reason", "").empty()) {
      roaring::Roaring *results_before = static_cast<CommonRecoContext *>(context)->GetResultBitMap();
      roaring::Roaring results_current;
      for (const CommonRecoResult &result : *results) {
        results_current.add(result.GetAttrIndex());
      }
      results_before->swap(results_current);
    }
    auto new_end = Arrange(context, input_first, input_last);
    const int removed_item_num = std::distance(new_end, input_last);

    if (use_target_items || use_select_items) {
      // 对 target_item 中要保留下的 item 按顺序复制回结果集
      size_t index = 0;
      for (auto from = input_first; from != new_end; ++from, ++index) {
        auto to = std::next(first, target_results_indices[index]);
        *to = std::move(*from);
      }
      if (removed_item_num > 0) {
        // 对 target_item 中无需保留的 item 进行删除处理 (标记删除边界)
        new_end = std::next(first, target_results_indices[index]);
        for (auto it = new_end; it != last; ++it) {
          if (index >= target_results_indices.size() ||
              std::distance(first, it) != target_results_indices[index]) {
            *new_end++ = std::move(*it);
          } else {
            ++index;
          }
        }
      }
    }

    if (removed_item_num > 0) {
      results->erase(new_end, last);

      if (FLAGS_record_filter_reason &&
          (FLAGS_all_processor_record_filter_reason || !config()->GetString("filter_reason", "").empty())) {
        roaring::Roaring *results_before = static_cast<CommonRecoContext *>(context)->GetResultBitMap();
        roaring::Roaring results_after;
        for (const CommonRecoResult &result : *results) {
          results_after.add(result.GetAttrIndex());
        }
        *results_before -= results_after;

        thread_local std::vector<uint32> removed_results;
        removed_results.resize(results_before->cardinality());
        results_before->toUint32Array(removed_results.data());
        static_cast<CommonRecoContext *>(context)->ReserveFilterResults();
        for (auto attr_index : removed_results) {
          static_cast<CommonRecoContext *>(context)->AddFilterResult(attr_index, GetFilterReason());
        }
        results_before->swap(results_after);
      }

      base::perfutil::PerfUtilWrapper::IntervalLogStash(removed_item_num, kPerfNs, "item_remove_num",
                                                        GlobalHolder::GetServiceIdentifier(),
                                                        context->GetRequestType(), GetName());
    }

    if (!item_list_from_attr.empty()) {
      std::vector<int64> val(results->size());
      std::transform(results->begin(), results->end(), val.begin(),
                     [](const CommonRecoResult &result) { return result.item_key; });
      context->SetIntListCommonAttr(item_list_from_attr, std::move(val), false, false);
    }
  }

  void OnEnter(ReadableRecoContextInterface *context) override {
    VLOG(100) << "Enter processor: " << GetName();
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) override {}

  void OnExit(ReadableRecoContextInterface *context) override {}

  ProcessorType GetType() const final {
    return ProcessorType::ARRANGER;
  }

 protected:
  CommonRecoBaseArranger() {}

  // 从指定的 CommonAttr 中抽取 item_list 进行处理
  virtual std::string GetItemListCommonAttr() const {
    return config()->GetString("item_list_from_attr");
  }
  // range start 为负数时表示倒数第几个
  virtual int GetRangeStart(ReadableRecoContextInterface *context) const {
    return GetIntProcessorParameter(context, "range_start", 0);
  }
  // range end 为 0 表示取末尾，负数表示倒数第几个
  virtual int GetRangeEnd(ReadableRecoContextInterface *context) const {
    return GetIntProcessorParameter(context, "range_end", 0);
  }

  virtual std::string GetFilterReason() const {
    return config()->GetString("filter_reason", GetName());
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseArranger);
};

}  // namespace platform
}  // namespace ks
