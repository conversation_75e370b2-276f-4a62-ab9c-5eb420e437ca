#pragma once

#include <algorithm>
#include <string>
#include <tuple>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_processor.h"

namespace ks {
namespace platform {

/**
 * CommonRecoLeaf Observer 的基类
 * Leaf 开发者应继承该类进行新 Observer 的实现，
 * 针对场景：debug info logging, 发送 leaf show 等只读场景
 */
class CommonRecoBaseObserver : public CommonRecoBaseProcessor {
 public:
  void Run(AddibleRecoContextInterface *context, std::vector<CommonRecoResult> *results) final {
    std::string item_list_from_attr = GetItemListCommonAttr();
    thread_local std::vector<CommonRecoResult> alternative_items;
    alternative_items.clear();
    if (!item_list_from_attr.empty()) {
      if (auto p_int_list = context->GetIntListCommonAttr(item_list_from_attr)) {
        std::transform(p_int_list->begin(), p_int_list->end(), std::back_inserter(alternative_items),
                       [context](uint64 item_key) { return context->NewCommonRecoResult(item_key, -1); });
      } else if (auto p_int = context->GetIntCommonAttr(item_list_from_attr)) {
        alternative_items.emplace_back(context->NewCommonRecoResult(*p_int, -1));
      } else {
        CL_LOG(INFO) << "int/int_list item_list_from_attr=" << item_list_from_attr
                     << " not found, cancelled run processor: " << GetName();
        return;
      }
      results = &alternative_items;
    }

    int size = results->size();
    auto begin = std::cbegin(*results);
    auto end = std::cend(*results);
    int range_start = GetRangeStart(context);
    int range_end = GetRangeEnd(context);
    auto first = range_start >= 0 ? std::next(begin, std::min(range_start, size))
                                  : std::prev(end, std::min(-range_start, size));
    auto last = range_end > 0 ? std::next(begin, std::min(range_end, size))
                              : std::prev(end, std::min(-range_end, size));
    if (last - first < 0) {
      last = first;
    }

    std::vector<CommonRecoResult> target_items;
    bool use_target_items = GetTargetItems(context, first, last, &target_items);
    bool use_select_items = GetSelectItems(context, first, last, &target_items);
    if (use_target_items || use_select_items) {
      first = std::cbegin(target_items);
      last = std::cend(target_items);
    }

    Observe(static_cast<ReadableRecoContextInterface *>(context), first, last);
  }

  // 给 Leaf 开发人员实现具体 Observe 逻辑的接口
  virtual void Observe(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                       RecoResultConstIter end) = 0;

  void OnEnter(ReadableRecoContextInterface *context) override {
    VLOG(100) << "Enter processor: " << GetName();
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) override {}

  void OnExit(ReadableRecoContextInterface *context) override {}

  ProcessorType GetType() const final {
    return ProcessorType::OBSERVER;
  }

  // 不同版本的 callback 注册接口
  template <typename Response, typename Callback>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context,
                             ks::kess::rpc::grpc::Future<Response *> future, Callback &&callback,
                             const std::string &info = "") {
    RegisterAsyncCallback(
        context, std::move(future), std::forward<Callback>(callback), []() {}, info);
  }

  template <typename Response, typename Callback,
            std::enable_if_t<folly::is_invocable_r<void, Callback, Response *>::value, int> = 0>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context,
                             ks::kess::rpc::grpc::Future<Response *> future, Callback &&callback,
                             std::function<void()> finally, const std::string &info = "") {
    if (context->GetDetachedBatchWaiter() != nullptr) {
      RegisterDetachedAsyncCallback<Response *, std::tuple<::grpc::Status, Response *>>(
          context, std::move(future), std::forward<Callback>(callback), std::move(finally),
          /*status_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> bool { return std::get<0>(value).ok(); },
          /*payload_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> Response *const & {
            return std::get<1>(value);
          },
          /*err_msg_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> std::string {
            const ::grpc::Status &status = std::get<0>(value);
            return absl::StrCat("grpc_err_", std::to_string(status.error_code()), "-",
                                status.error_message());
          },
          info);
    } else {
      // NOTE(zhaoyang09): 暂不清楚为什么该重载必须显示声明父类作用域。
      CommonRecoBaseProcessor::RegisterAsyncCallback<Response *, std::tuple<::grpc::Status, Response *>>(
          context, std::move(future), std::forward<Callback>(callback), std::move(finally),
          /*status_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> bool { return std::get<0>(value).ok(); },
          /*payload_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> Response *const & {
            return std::get<1>(value);
          },
          /*err_msg_getter=*/
          [](const std::tuple<::grpc::Status, Response *> &value) -> std::string {
            const ::grpc::Status &status = std::get<0>(value);
            return absl::StrCat("grpc_err_", std::to_string(status.error_code()), "-",
                                status.error_message());
          },
          info);
    }
  }

  template <typename T, typename Callback,
            std::enable_if_t<folly::is_invocable_r<void, Callback, const T &>::value, int> = 0>
  void RegisterDetachedAsyncCallback(ReadableRecoContextInterface *context, ks::kess::rpc::Future<T> future,
                                     Callback &&callback, const std::string &info = "") {
    if (context->GetDetachedBatchWaiter() != nullptr) {
      RegisterDetachedAsyncCallback<T, T>(
          context, std::move(future), std::forward<Callback>(callback), []() {},
          /*status_getter=*/
          [](const T &value) -> bool { return true; },
          /*payload_getter=*/
          [](const T &value) -> const T & { return value; },
          /*err_msg_getter=*/
          [](const T &value) -> std::string { return ""; }, info);
    } else {
      CommonRecoBaseProcessor::RegisterAsyncCallback<T, T>(
          context, std::move(future), std::forward<Callback>(callback), []() {},
          /*status_getter=*/
          [](const T &value) -> bool { return true; },
          /*payload_getter=*/
          [](const T &value) -> const T & { return value; },
          /*err_msg_getter=*/
          [](const T &value) -> std::string { return ""; }, info);
    }
  }

  template <typename Payload, typename FutureValue>
  void RegisterDetachedAsyncCallback(ReadableRecoContextInterface *context,
                                     ks::kess::rpc::Future<FutureValue> future,
                                     std::function<void(const Payload &)> callback,
                                     std::function<void()> finally,
                                     std::function<bool(const FutureValue &)> status_getter,
                                     std::function<const Payload &(const FutureValue &)> payload_getter,
                                     std::function<std::string(const FutureValue &)> err_msg_getter,
                                     const std::string &info = "") {
    const std::string &name = GetName();
    const std::string &downstream_name = GetDownstreamProcessor();
    const std::string &degrade_key = GetDegradeKey(context);
    DegraderManager::Singleton()->BeginCall(degrade_key);
    std::string status_attr = GetAsyncStatusAttr();

    auto *running_processor = this;
    context->GetAsyncUpstreamProcessors(downstream_name, true)->push_back(name);
    context->GetDetachedBatchWaiter()->Add(
        std::move(future),
        [callback = std::move(callback), finally = std::move(finally),
         status_getter = std::move(status_getter), payload_getter = std::move(payload_getter),
         err_msg_getter = std::move(err_msg_getter), status_attr = std::move(status_attr), name, context,
         running_processor, downstream_name, degrade_key, info](const FutureValue &value) mutable {
          bool success = status_getter(value);
          DegraderManager::Singleton()->EndCall(degrade_key, success);

          auto *mutable_context = static_cast<MutableRecoContextInterface *>(context);
          mutable_context->SetRunningProcessor(running_processor);
          if (!status_attr.empty()) {
            mutable_context->SetIntCommonAttr(status_attr, success ? 0 : 1, false, false);
          }

          auto biz_perf_extra_a = context->GetStringCommonAttr(kPerfLogExtraA);
          auto biz_perf_extra_b = context->GetStringCommonAttr(kPerfLogExtraB);
          auto biz_perf_extra_c = context->GetStringCommonAttr(kPerfLogExtraC);
          auto perf_extra4 = std::string(biz_perf_extra_a.value_or(downstream_name));
          auto perf_extra5 = std::string(biz_perf_extra_b.value_or(""));
          auto perf_extra6 = std::string(biz_perf_extra_c.value_or(GlobalHolder::GetJsonConfigVersion()));

          auto *common_context = static_cast<CommonRecoContext *>(context);
          std::vector<CommonRecoResult> *results = common_context->GetRecoResults();

          if (success) {
            int64 start_ts = base::GetTimestamp();
            callback(payload_getter(value));
            int64 duration = base::GetTimestamp() - start_ts;
            CL_PERF_INTERVAL(
                duration, kPerfNs, "callback_execute_time", GlobalHolder::GetServiceIdentifier(),
                context->GetRequestType(), name, perf_extra4, perf_extra5, perf_extra6);
            running_processor->UpdateCacheAfterRun(context);
          } else {
            base::perfutil::PerfUtilWrapper::CountLogStash(
                kPerfNs, "async_wait_fail", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                name, perf_extra4, perf_extra5);

            CL_LOG_WARNING(err_msg_getter(value), info) << err_msg_getter(value) << ", extra_info: " << info
                                                        << RecoUtil::GetRequestInfoForLog(context);
          }
          finally();
          running_processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), false, false);
        },
        GetName());
  }

  template <typename Response, typename Callback>
  void RegisterLocalAsyncCallback(ReadableRecoContextInterface *context, std::future<Response *> &&future,
                                  Callback &&callback, const std::string &info = "", int64 timeout_ms = 0) {
    RegisterLocalAsyncCallback(
        context, std::move(future), std::forward<Callback>(callback), []() {}, info, timeout_ms);
  }

  template <typename Response, typename Callback>
  void RegisterLocalAsyncCallback(ReadableRecoContextInterface *context, std::future<Response *> &&future,
                                  Callback &&callback, std::function<void()> finally,
                                  const std::string &info = "", int64 timeout_ms = 0,
                                  std::function<void()> timeout_cb = nullptr) {
    if (context->GetLocalAsyncTimeoutWaiter() != nullptr) {
      RegisterDetachedLocalAsyncCallback(context, std::move(future), std::forward<Callback>(callback), info);
    } else {
      CommonRecoBaseProcessor::RegisterLocalAsyncCallback(context, std::move(future),
                                                          std::forward<Callback>(callback), finally, info,
                                                          timeout_ms, timeout_cb);
    }
  }

  template <typename Response, typename Callback>
  void RegisterDetachedLocalAsyncCallback(ReadableRecoContextInterface *context,
                                          std::future<Response *> &&future, Callback &&callback,
                                          const std::string &info = "") {
    const std::string &name = GetName();
    // 参照 batch_waiter_wrapper.h 里的实现
    std::function<void(Response *)> cb = std::forward<Callback>(callback);
    const std::string &downstream_name = GetDownstreamProcessor();
    const std::string &degrade_key = GetDegradeKey(context);
    DegraderManager::Singleton()->BeginCall(degrade_key);

    context->GetLocalAsyncTimeoutWaiter()->Add(
        context, std::move(future),
        [cb = std::move(cb), name, downstream_name, degrade_key, context, info](Response *response) mutable {
          bool success = nullptr != response;
          DegraderManager::Singleton()->EndCall(degrade_key, success);

          if (success) {
            int64 start_ts = base::GetTimestamp();
            cb(response);
            int64 duration = base::GetTimestamp() - start_ts;
            CL_PERF_INTERVAL(
                duration, kPerfNs, "callback_execute_time", GlobalHolder::GetServiceIdentifier(),
                context->GetRequestType(), name, downstream_name, "", GlobalHolder::GetJsonConfigVersion());
          } else {
            CL_LOG_WARNING("local_async_fail", info)
                << "local_async process error, processor: " << name << ", extra_info: " << info
                << RecoUtil::GetRequestInfoForLog(context);
          }
        },
        GetName(), 0, 0, nullptr);
  }

 protected:
  CommonRecoBaseObserver() {}

  // 从指定的 CommonAttr 中抽取 item_list 进行处理
  virtual std::string GetItemListCommonAttr() const {
    return config()->GetString("item_list_from_attr");
  }
  // range start 为负数时表示倒数第几个
  virtual int GetRangeStart(ReadableRecoContextInterface *context) {
    return GetIntProcessorParameter(context, "range_start", 0);
  }
  // range end 为 0 表示取末尾，负数表示倒数第几个
  virtual int GetRangeEnd(ReadableRecoContextInterface *context) {
    return GetIntProcessorParameter(context, "range_end", 0);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseObserver);
};

}  // namespace platform
}  // namespace ks
