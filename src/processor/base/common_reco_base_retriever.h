#pragma once

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include "absl/strings/str_join.h"
#include "base/strings/string_number_conversions.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"
#include "folly/container/F14Map.h"

DECLARE_bool(enable_processor_retrieve_item_cache);

namespace ks {
namespace platform {

/**
 * CommonRecoLeaf Retriever 的基类
 * Leaf 开发者应继承该类进行新 Retriever 的实现，针对场景：item 召回
 */
class CommonRecoBaseRetriever : public CommonRecoBaseProcessor {
 public:
  void Run(AddibleRecoContextInterface *context, std::vector<CommonRecoResult> *results) final {
    int threshold = GetShortCircuitThreshold(context);
    if (threshold > 0 && results->size() >= threshold) {
      CL_LOG(INFO) << "Retriever [" << GetName() << "] is short-circuited, threshold: " << threshold
                   << ", current results size: " << results->size();
      return;
    }
    if (IsCacheEnabled(context)) {
      auto res = GetUnCachedItems(context, std::cbegin(*results), std::cend(*results), results);
      if (res == CacheStatus::FULLY) {
        VLOG(10) << "skip Run() because of hit cache: " << GetName();
        return;
      }
      VLOG(10) << "not hit cache, run retrieve: " << GetName();
    }
    Retrieve(context);
  }

  // 给 Leaf 开发人员实现具体 Retrieve 逻辑的接口
  virtual void Retrieve(AddibleRecoContextInterface *context) = 0;

  void OnEnter(ReadableRecoContextInterface *context) override {
    VLOG(100) << "Enter processor: " << GetName();
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) override {}

  void OnExit(ReadableRecoContextInterface *context) override {}

  ProcessorType GetType() const final {
    return ProcessorType::RETRIEVER;
  }

 protected:
  CommonRecoBaseRetriever() {}

  // Retriever 短路设置, 当结果集大小超过指定数目时, 取消执行当前 Retriever 逻辑
  virtual int GetShortCircuitThreshold(ReadableRecoContextInterface *context) {
    return GetIntProcessorParameter(context, "short_circuit_threshold", 0);
  }

  virtual std::string SaveResultToCommonAttr() {
    return config()->GetString("save_result_to_common_attr", "");
  }

  virtual bool SaveResultToCommonAttrById() {
    return config()->GetBoolean("save_result_to_common_attr_by_id", true);
  }

  virtual int GetResetItemType(ReadableRecoContextInterface *context) {
    return GetIntProcessorParameter(context, config()->Get("reset_item_type"), -1);
  }

  virtual bool OverrideReason() {
    return config()->GetBoolean("override_reason", false);
  }

  virtual std::string AddReasonToAttr() {
    return config()->GetString("add_reason_to_attr", "");
  }

  virtual int GetItemChannel() {
    return config()->GetInt("item_channel", 0);
  }

  // 实现一个追加召回结果的辅助函数
  void AddToRecoResults(AddibleRecoContextInterface *context,
                        const std::vector<CommonRecoRetrieveResult> &candidates,
                        std::vector<CommonRecoResult> *results = nullptr) {
    if (IsCacheEnabled(context) && need_update_cache_) {
      auto start_ts = base::GetTimestamp();
      do {
        const auto &processor_name = GetName();
        if (!GetRetrieveCacheKeyAttrName().empty()) {
          auto cache_key = GetRetrieveCacheKeyValue(context);
          if (cache_key.empty()) break;
          VLOG(10) << "Write retrieve items to cache, processor: " << processor_name << " key: " << cache_key
                   << " candidates: "
                   << absl::StrJoin(candidates, " ",
                                    [](std::string *out, const CommonRecoRetrieveResult &candidate) {
                                      out->append(base::Uint64ToString(candidate.item_key));
                                    });
          LocalRetrieveCache::ValueType cached_val = candidates;
          LocalRetrieveCache::GetInstance()->Set(processor_name, cache_key, std::move(cached_val));
        }
      } while (0);
      int64 duration = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(duration, kPerfNs, "processor_output_cache.write",
                       GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                       type_name(), "", GlobalHolder::GetJsonConfigVersion());
    }

    std::string common_attr = SaveResultToCommonAttr();
    int channel = GetItemChannel();
    if (candidates.empty()) {
      if (common_attr.empty()) {
        base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "empty_retrieve",
                                                       GlobalHolder::GetServiceIdentifier(),
                                                       context->GetRequestType(), GetName());
      }
      return;
    }

    int reset_item_type = GetResetItemType(context);
    if (results) {
      results->reserve(results->size() + candidates.size());
    }

    if (!common_attr.empty()) {
      auto *accessor = context->GetCommonAttrAccessor(common_attr);
      bool save_id = SaveResultToCommonAttrById();
      for (const auto &candidate : candidates) {
        int64 item_key = save_id ? Util::GetId(candidate.item_key)
                                 : RecoUtil::ResetItemType(reset_item_type, candidate.item_key);
        accessor->AppendIntListValue(item_key);
        if (results) {
          results->emplace_back(context->NewCommonRecoResult(item_key, candidate.reason, candidate.score,
                                                             channel, GetTableName()));
        }
      }
      CL_LOG(INFO) << "saved " << candidates.size() << " retrieve result ids to common attr: " << common_attr;
      return;
    }

    // 统计各 reason 数目
    folly::F14FastMap<int, int> reason_count;
    std::string add_reason_attr = AddReasonToAttr();
    auto *multi_reason_attr =
        add_reason_attr.empty() ? nullptr : context->GetItemAttrAccessor(add_reason_attr);
    // item 固定的 reason list 属性
    auto *reason_list_attr =
        FLAGS_record_reason_list ? context->GetItemAttrAccessor(kReasonListAttr) : nullptr;

    bool override_reason = OverrideReason();
    for (const auto &candidate : candidates) {
      int64 item_key = RecoUtil::ResetItemType(reset_item_type, candidate.item_key);
      auto &result =
          context->AddCommonRecoResult(item_key, candidate.reason, candidate.score, channel, override_reason);
      // 指定记录同一个 item_key 的所有 reason，业务侧单独配置 reason list 对应的属性
      if (multi_reason_attr) {
        result.AppendIntListAttr(multi_reason_attr, candidate.reason);
      }
      // 向 item 固定的 _REASON_LIST_ 属性写入 reason
      if (FLAGS_record_reason_list && reason_list_attr) {
        auto reason_list = result.GetIntListAttr(reason_list_attr);
        if (reason_list) {
          folly::F14FastSet<int> dedup_reason_list_set(reason_list->begin(), reason_list->end());
          if (dedup_reason_list_set.insert(candidate.reason).second) {
            result.AppendIntListAttr(reason_list_attr, candidate.reason);
          }
        } else {
          result.AppendIntListAttr(reason_list_attr, candidate.reason);
        }
      }

      ++reason_count[candidate.reason];
      if (results) {
        results->push_back(result);
      }
    }
    for (const auto &pr : reason_count) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          pr.second, kPerfNs, "retrieve_item_num", GlobalHolder::GetServiceIdentifier(),
          context->GetRequestType(), base::IntToString(pr.first), GetName());
    }
  }

 private:
  bool IsCacheEnabled(ReadableRecoContextInterface *context) const override {
    return FLAGS_enable_processor_retrieve_item_cache && output_cache_config_ &&
           GetBoolProcessorParameter(context, output_cache_config_->enable, false);
  }

  CacheStatus CacheRead(MutableRecoContextInterface *context, RecoResultConstIter first,
                        RecoResultConstIter last, std::vector<CommonRecoResult> *target_items) override {
    const auto &processor_name = GetName();
    if (!GetRetrieveCacheKeyAttrName().empty()) {
      auto cache_key = GetRetrieveCacheKeyValue(context);
      const LocalRetrieveCache::ValueType *res = nullptr;
      if (!cache_key.empty()) {
        res = LocalRetrieveCache::GetInstance()->Get(processor_name, cache_key);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(res ? 1000 : 0, kPerfNs, "processor_cache_rate",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName());
      }
      if (!res) return CacheStatus::NONE;
      VLOG(10) << "Read retrieve items from cache, processor: " << processor_name << " key: " << cache_key
               << " candidates: "
               << absl::StrJoin(*res, " ", [](std::string *out, const CommonRecoRetrieveResult &candidate) {
                    out->append(base::Uint64ToString(candidate.item_key));
                  });
      need_update_cache_ = false;
      AddToRecoResults(static_cast<AddibleRecoContextInterface *>(context), *res);
      return CacheStatus::FULLY;
    }
    return CacheStatus::NONE;
  }

  void CacheWrite(MutableRecoContextInterface *context) const final {
    // NOTE(caohongjin): Retriever 在 AddToRecoResults 中进行 CacheWrite
  }

  void UpdateCacheAfterRun(ReadableRecoContextInterface *context) const final {
    // NOTE(caohongjin): Retriever 在 AddToRecoResults 中进行 CacheWrite
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseRetriever);
};

}  // namespace platform
}  // namespace ks
