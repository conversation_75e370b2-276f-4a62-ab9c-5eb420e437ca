#pragma once

#include <algorithm>
#include <memory>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_set.h"
#include "absl/strings/str_cat.h"
#include "absl/strings/substitute.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/core/common_reco_pipeline.h"
#include "dragon/src/core/degrader_manager.h"
#include "dragon/src/util/local_cache.h"
#include "folly/String.h"
#include "folly/functional/Invoke.h"
#include "serving_base/util/math.h"

DECLARE_string(degrade_config_kconf_key);

#define OUTPUT_LIST_ATTR_TO_STR(TYPE)                                                          \
  {                                                                                            \
    absl::StrAppend(&output_str, "\n", accessor->name(), " (", TYPE, "[", p->size(), "]): ["); \
    if (!p->empty()) {                                                                         \
      absl::StrAppend(&output_str, p->front());                                                \
      std::for_each(std::next(p->begin()), p->end(),                                           \
                    [&](const auto &v) { absl::StrAppend(&output_str, ", ", v); });            \
    }                                                                                          \
    absl::StrAppend(&output_str, "]");                                                         \
  }

namespace ks {
namespace platform {

enum class DebugLogOutputType : int {
  NONE = 0,
  LOG,
  CL_LOG,
  STDOUT,
  STDERR,
};

enum class CompareOperator : int {
  UNKNOWN = 0,
  LE,
  GE,
  LT,
  GT,
  EQ,
  NE,
  IN,
  NOT_IN,
  CONTAIN,
  NOT_CONTAIN,
  INTERSECT,
  NOT_INTERSECT,
  IS_NULL,
  NOT_NULL,
};

enum class CompareValueType : int { UNKNOWN = 0, INT, DOUBLE, STRING, INT_LIST, STRING_LIST, RANGE };

struct CompareValueRange {
  const base::Json *lower_bound_json = nullptr;
  const base::Json *upper_bound_json = nullptr;
  bool has_lower_bound = false;
  bool has_upper_bound = false;
  double lower_bound = 0.0;
  double upper_bound = 0.0;
};

enum class JoinOperator : int { NONE = 0, OR, AND };

struct RuleFilter {
  std::vector<RuleFilter> children;
  const base::Json *enable_config = nullptr;
  bool enable = true;
  JoinOperator join = JoinOperator::NONE;
  CompareOperator oper = CompareOperator::UNKNOWN;
  CompareValueType value_type = CompareValueType::UNKNOWN;
  int64 compare_to_int = 0;
  double compare_to_double = 0.0;
  folly::F14FastSet<int64> compare_to_list;
  CompareValueRange compare_value_range;
  bool match_if_attr_missing = false;
  ItemAttr *attr_accessor = nullptr;
  std::string compare_to_attr;
  const base::Json *compare_to_attr_json = nullptr;
  std::string compare_to_item_attr;
  ItemAttr *compare_to_item_attr_accessor = nullptr;
  std::string attr_name;
  // 当前 rule 是否被忽略，rule 被忽略后 processor 整体可正常过滤。
  bool is_ignored_rule = false;
  // 当前 rule 是否不合法，rule 被忽略后 processor 直接报错，不做过滤。
  mutable bool is_illegal_rule = false;
  mutable std::string illegal_info = "";
  bool check_reason = false;
  const base::Json *limit = nullptr;

  void ParseCompareOperator(const std::string &remove_if) {
    if (remove_if == "<=" || remove_if == "LE") {
      oper = CompareOperator::LE;
    } else if (remove_if == ">=" || remove_if == "GE") {
      oper = CompareOperator::GE;
    } else if (remove_if == "<" || remove_if == "LT") {
      oper = CompareOperator::LT;
    } else if (remove_if == ">" || remove_if == "GT") {
      oper = CompareOperator::GT;
    } else if (remove_if == "=" || remove_if == "==" || remove_if == "EQ") {
      oper = CompareOperator::EQ;
    } else if (remove_if == "!=" || remove_if == "<>" || remove_if == "NE") {
      oper = CompareOperator::NE;
    } else if (remove_if == "in" || remove_if == "IN") {
      oper = CompareOperator::IN;
    } else if (remove_if == "not in" || remove_if == "NOT_IN") {
      oper = CompareOperator::NOT_IN;
    } else if (remove_if == "contain" || remove_if == "CONTAIN") {
      oper = CompareOperator::CONTAIN;
    } else if (remove_if == "not contain" || remove_if == "NOT_CONTAIN") {
      oper = CompareOperator::NOT_CONTAIN;
    } else if (remove_if == "intersect" || remove_if == "INTERSECT") {
      oper = CompareOperator::INTERSECT;
    } else if (remove_if == "not intersect" || remove_if == "NOT_INTERSECT") {
      oper = CompareOperator::NOT_INTERSECT;
    } else if (remove_if == "is null" || remove_if == "IS_NULL") {
      oper = CompareOperator::IS_NULL;
    } else if (remove_if == "not null" || remove_if == "NOT_NULL") {
      oper = CompareOperator::NOT_NULL;
    } else {
      oper = CompareOperator::UNKNOWN;
    }
  }

  std::string ExtractOperatorString() const {
    switch (oper) {
      case CompareOperator::LE:
        return "<=";
      case CompareOperator::GE:
        return ">=";
      case CompareOperator::LT:
        return "<";
      case CompareOperator::GT:
        return ">";
      case CompareOperator::EQ:
        return "==";
      case CompareOperator::NE:
        return "!=";
      case CompareOperator::IN:
        return "in";
      case CompareOperator::NOT_IN:
        return "not in";
      case CompareOperator::CONTAIN:
        return "contain";
      case CompareOperator::NOT_CONTAIN:
        return "not contain";
      case CompareOperator::INTERSECT:
        return "intersect";
      case CompareOperator::NOT_INTERSECT:
        return "not intersect";
      default:
        return "unknown";
    }
  }

  bool InitFilter(const base::Json *filter_config, const std::string &remove_or_select) {
    match_if_attr_missing = filter_config->GetBoolean(remove_or_select + "_if_attr_missing", false);

    std::string remove_if = filter_config->GetString(remove_or_select + "_if");
    ParseCompareOperator(remove_if);
    std::string error_info;
    if (remove_or_select == "remove") {
      error_info = "CommonRecoRuleFilterArranger";
    } else {
      error_info = "select_item";
    }
    if (oper == CompareOperator::UNKNOWN && !match_if_attr_missing) {
      LOG(ERROR) << error_info << " init failed! Unknown compare operator, please check \""
                 << remove_or_select << "_if\" config!" << filter_config->ToString();
      return false;
    }

    auto *compare_to = filter_config->Get("compare_to");
    auto *compare_to_item = filter_config->Get("compare_to_item_attr");
    if (compare_to && compare_to_item) {
      LOG(ERROR) << error_info << " init failed!: \"compare_to\" and "
                 << "\"compare_to_item_attr\" should not in same filter" << filter_config->ToString();
      return false;
    }

    attr_name = filter_config->GetString("attr_name");
    check_reason = filter_config->GetBoolean("check_reason", false);
    if (attr_name.empty() && !check_reason) {
      CL_LOG(ERROR) << error_info << "  init failed: empty attr_name" << filter_config->ToString()
                    << " nor check_reason";
      return false;
    }

    if (compare_to) {
      if (compare_to->IsInteger()) {
        value_type = CompareValueType::INT;
        compare_to_int = compare_to->IntValue((int64)0);
      } else if (compare_to->IsDouble()) {
        value_type = CompareValueType::DOUBLE;
        compare_to_double = compare_to->FloatValue((double)0.0);
      } else if (compare_to->IsString()) {
        if (auto op = RecoUtil::ExtractCommonAttrFromExpr(compare_to)) {
          std::string attr_name(*op);
          compare_to_attr = attr_name;
          compare_to_attr_json = compare_to;
        } else {
          std::string str = compare_to->StringValue();
          value_type = CompareValueType::STRING;
          compare_to_int = base::CityHash64(str.data(), str.size());
          if (oper != CompareOperator::EQ && oper != CompareOperator::NE) {
            LOG(ERROR) << error_info << " init failed! \"remove_if\" could only be"
                       << " \"==\" or \"!=\" if \"compare_to\" is string" << filter_config->ToString();
            return false;
          }
        }
      } else if (compare_to->IsArray()) {
        for (auto *json : compare_to->array()) {
          if (json->IsInteger() &&
              (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::UNKNOWN)) {
            value_type = CompareValueType::INT_LIST;
            compare_to_list.insert(json->IntValue((int64)0));
          } else if (json->IsString() && (value_type == CompareValueType::STRING_LIST ||
                                          value_type == CompareValueType::UNKNOWN)) {
            value_type = CompareValueType::STRING_LIST;
            std::string str = json->StringValue();
            compare_to_list.insert(base::CityHash64(str.data(), str.size()));
          } else {
            LOG(ERROR) << error_info
                       << " init failed! element in \"compare_to\" could only "
                          "be int_list or string_list if \"compare_to\" is list"
                       << filter_config->ToString();
            return false;
          }
        }
      } else if (compare_to->IsObject()) {
        base::Json *range = compare_to->Get("range");
        if (range && range->IsObject()) {
          base::Json *lb = range->Get("lower_bound");
          if (lb) {
            value_type = CompareValueType::RANGE;
            compare_value_range.lower_bound_json = lb;
          }
          base::Json *ub = range->Get("upper_bound");
          if (ub) {
            value_type = CompareValueType::RANGE;
            compare_value_range.upper_bound_json = ub;
          }
        }
      } else {
        LOG(ERROR) << "unsupported compare_to config" << compare_to->ToString();
        return false;
      }
    }

    if (compare_to_item) {
      if (compare_to_item->IsString()) {
        compare_to_item_attr = compare_to_item->StringValue();
      } else {
        LOG(ERROR) << "unsupported compare_to_item_attr config:" << compare_to_item->ToString()
                   << ", compare_to_item_attr must be string";
        return false;
      }
    }
    return true;
  }

  bool InitFilterTree(const base::Json *filter_config, RuleFilter *filter,
                      const std::string &remove_or_select, int level = 1) {
    if (!filter) {
      return false;
    }
    std::string join = filter_config->GetString("join");
    filter->enable_config = filter_config->Get("enable");
    if (join == "and") {
      filter->join = JoinOperator::AND;
    } else if (join == "or") {
      filter->join = JoinOperator::OR;
    } else {
      filter->join = JoinOperator::NONE;
      return filter->InitFilter(filter_config, remove_or_select);
    }

    auto *filter_children = filter_config->Get("filters");
    if (!filter_children) {
      LOG(ERROR) << (remove_or_select == "select" ? "select_item" : "CommonRecoRuleFilterArranger")
                 << " init failed! missing "
                 << "\"filters\" config!" << filter_config->ToString();
      return false;
    }
    for (const base::Json *filter_config : filter_children->array()) {
      RuleFilter filter_child;
      if (!InitFilterTree(filter_config, &filter_child, remove_or_select, level + 1)) {
        return false;
      }
      filter->children.emplace_back(std::move(filter_child));
    }
    return true;
  }

  bool LoadFilterForItemCompare(MutableRecoContextInterface *context, const CommonRecoResult &result,
                                const std::string &remove_or_select, bool ignore_invalid_rule = false) {
    if (!compare_to_item_attr_accessor) {
      return true;
    }
    std::string error_info;
    if (remove_or_select == "remove") {
      error_info = "CommonRecoRuleFilterArranger";
    } else {
      error_info = "select_item";
    }
    switch (compare_to_item_attr_accessor->value_type) {
      case AttrType::INT: {
        if (auto p = context->GetIntItemAttr(result, compare_to_item_attr_accessor)) {
          value_type = CompareValueType::INT;
          compare_to_int = *p;
          return true;
        }
        break;
      }
      case AttrType::FLOAT: {
        if (auto p = context->GetDoubleItemAttr(result, compare_to_item_attr_accessor)) {
          value_type = CompareValueType::DOUBLE;
          compare_to_double = *p;
          return true;
        }
        break;
      }
      case AttrType::STRING: {
        if (oper != CompareOperator::EQ && oper != CompareOperator::NE && oper != CompareOperator::CONTAIN &&
            oper != CompareOperator::NOT_CONTAIN) {
          CL_LOG_ERROR_EVERY(error_info, "invalid_item_string_compare:" + compare_to_item_attr, 1000)
              << error_info << " cancelled: string item_attr " << compare_to_item_attr
              << " could be compared for '==/!=/contain/not contain' operator only!";
          return false;
        }
        if (auto p = context->GetStringItemAttr(result, compare_to_item_attr_accessor)) {
          compare_to_int = compare_to_int = base::CityHash64(p->data(), p->size());
          value_type = CompareValueType::STRING;
          return true;
        }
        break;
      }
      case AttrType::INT_LIST: {
        if (oper != CompareOperator::IN && oper != CompareOperator::NOT_IN &&
            oper != CompareOperator::CONTAIN && oper != CompareOperator::NOT_CONTAIN &&
            oper != CompareOperator::INTERSECT && oper != CompareOperator::NOT_INTERSECT) {
          CL_LOG_ERROR_EVERY(error_info, "invalid_item_int_list_compare:" + compare_to_item_attr, 1000)
              << error_info << " cancelled: int_list item_attr " << compare_to_item_attr
              << " could be compared for 'in/not in/contain/not contain/intersect/not intersect'"
              << " operator only!";
          return false;
        }
        compare_to_list.clear();
        if (auto int_list = context->GetIntListItemAttr(result, compare_to_item_attr_accessor)) {
          for (auto int_val : int_list.value()) {
            compare_to_list.insert(int_val);
          }
          value_type = CompareValueType::INT_LIST;
          return true;
        }
        break;
      }
      case AttrType::STRING_LIST: {
        if (oper != CompareOperator::IN && oper != CompareOperator::NOT_IN &&
            oper != CompareOperator::CONTAIN && oper != CompareOperator::NOT_CONTAIN &&
            oper != CompareOperator::INTERSECT && oper != CompareOperator::NOT_INTERSECT) {
          CL_LOG_ERROR_EVERY(error_info, "invalid_item_string_list_compare:" + compare_to_item_attr, 1000)
              << error_info << " cancelled: int_list item_attr " << compare_to_item_attr
              << " could be compared for 'in/not in/contain/not contain/intersect/not intersect'"
              << " operator only!";
          return false;
        }
        compare_to_list.clear();
        if (auto str_list = context->GetStringListItemAttr(result, compare_to_item_attr_accessor)) {
          for (auto str_v : str_list.value()) {
            compare_to_list.insert(base::CityHash64(str_v.data(), str_v.size()));
          }
          value_type = CompareValueType::STRING_LIST;
          return true;
        }
        break;
      }
      default: {
        if (ignore_invalid_rule) {
          is_ignored_rule = true;
          return true;
        }
        CL_LOG_ERROR_EVERY(error_info, "unsupported_compare_to_item_attr_type:" + compare_to_item_attr, 1000)
            << error_info << " cancelled, compare_to_item type error: " << compare_to_item_attr
            << ". type could be 'int/double/string/int list/string list' only!";
        return false;
      }
    }

    if (ignore_invalid_rule) {
      is_ignored_rule = true;
      return true;
    }
    CL_LOG_ERROR_EVERY("rule_filter", "missing_item_attr:" + std::string(compare_to_item_attr), 1000)
        << "rule filter cancelled: missing item_attr " << compare_to_item_attr;
    return false;
  }

  bool CheckAsInt(int64 val) const {
    int64 compare_val = compare_to_int;
    switch (oper) {
      case CompareOperator::EQ:
        return val == compare_val;
      case CompareOperator::NE:
        return val != compare_val;
      case CompareOperator::LE:
        return val <= compare_val;
      case CompareOperator::GE:
        return val >= compare_val;
      case CompareOperator::LT:
        return val < compare_val;
      case CompareOperator::GT:
        return val > compare_val;
      case CompareOperator::IN:
        if (value_type != CompareValueType::RANGE) {
          return compare_to_list.find(val) != compare_to_list.end();
        } else {
          return (!(compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) &&
                  !(compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
        }
      case CompareOperator::NOT_IN:
        if (value_type != CompareValueType::RANGE) {
          return compare_to_list.find(val) == compare_to_list.end();
        } else {
          return ((compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) ||
                  (compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
        }
      default:
        is_illegal_rule = true;
        illegal_info = "rule cancelled: attr " + attr_name +
                       " doesn't allow the comparing operator: " + ExtractOperatorString();
        return false;
    }
  }

  bool CheckAsDouble(double val) const {
    double compare_val = value_type == CompareValueType::DOUBLE ? compare_to_double : (double)compare_to_int;
    switch (oper) {
      case CompareOperator::LE:
        return base::IsLessEqual(val, compare_val);
      case CompareOperator::GE:
        return base::IsGreaterEqual(val, compare_val);
      case CompareOperator::LT:
        return base::IsLess(val, compare_val);
      case CompareOperator::GT:
        return base::IsGreater(val, compare_val);
      case CompareOperator::EQ:
        return base::IsEqual(val, compare_val);
      case CompareOperator::NE:
        return !base::IsEqual(val, compare_val);
      case CompareOperator::IN:
        if (value_type == CompareValueType::RANGE) {
          return (
              !(compare_value_range.has_lower_bound && base::IsLess(val, compare_value_range.lower_bound)) &&
              !(compare_value_range.has_upper_bound &&
                base::IsGreaterEqual(val, compare_value_range.upper_bound)));
        }
      case CompareOperator::NOT_IN:
        if (value_type == CompareValueType::RANGE) {
          return (
              (compare_value_range.has_lower_bound && base::IsLess(val, compare_value_range.lower_bound)) ||
              (compare_value_range.has_upper_bound &&
               base::IsGreaterEqual(val, compare_value_range.upper_bound)));
        }
      default:
        is_illegal_rule = true;
        illegal_info = "rule cancelled: attr " + attr_name +
                       " doesn't allow the comparing operator: " + ExtractOperatorString();
        return false;
    }
  }

  bool CheckAsIntList(const absl::Span<const int64> list_val) const {
    switch (oper) {
      case CompareOperator::CONTAIN: {
        if (value_type == CompareValueType::INT || value_type == CompareValueType::STRING) {
          return std::find(list_val.begin(), list_val.end(), compare_to_int) != list_val.end();
        } else if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::all_of(compare_to_list.begin(), compare_to_list.end(), [&](const int64 val) {
            return std::find(list_val.begin(), list_val.end(), val) != list_val.end();
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::NOT_CONTAIN: {
        if (value_type == CompareValueType::INT || value_type == CompareValueType::STRING) {
          return std::find(list_val.begin(), list_val.end(), compare_to_int) == list_val.end();
        } else if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::any_of(compare_to_list.begin(), compare_to_list.end(), [&](const int64 val) {
            return std::find(list_val.begin(), list_val.end(), val) == list_val.end();
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::INTERSECT: {
        if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return compare_to_list.find(val) != compare_to_list.end();
          });
        } else if (value_type == CompareValueType::RANGE) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return (!(compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) &&
                    !(compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::NOT_INTERSECT: {
        if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::none_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return compare_to_list.find(val) != compare_to_list.end();
          });
        } else if (value_type == CompareValueType::RANGE) {
          return std::none_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return (!(compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) &&
                    !(compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::IN: {
        if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::all_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return compare_to_list.find(val) != compare_to_list.end();
          });
        } else if (value_type == CompareValueType::RANGE) {
          return std::all_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return (!(compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) &&
                    !(compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::NOT_IN: {
        if (value_type == CompareValueType::INT_LIST || value_type == CompareValueType::STRING_LIST) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return compare_to_list.find(val) == compare_to_list.end();
          });
        } else if (value_type == CompareValueType::RANGE) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return ((compare_value_range.has_lower_bound && val < compare_value_range.lower_bound) ||
                    (compare_value_range.has_upper_bound && val >= compare_value_range.upper_bound));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      default:
        is_illegal_rule = true;
        illegal_info = "rule cancelled: attr " + attr_name +
                       " doesn't allow the comparing operator: " + ExtractOperatorString();
        return false;
    }
  }

  bool CheckAsDoubleList(const absl::Span<const double> list_val) const {
    switch (oper) {
      case CompareOperator::IN: {
        if (value_type == CompareValueType::RANGE) {
          return std::all_of(list_val.begin(), list_val.end(), [this](const double val) {
            return (!(compare_value_range.has_lower_bound &&
                      base::IsLess(val, compare_value_range.lower_bound)) &&
                    !(compare_value_range.has_upper_bound &&
                      base::IsGreaterEqual(val, compare_value_range.upper_bound)));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::NOT_IN: {
        if (value_type == CompareValueType::RANGE) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const int64 val) {
            return (
                (compare_value_range.has_lower_bound && base::IsLess(val, compare_value_range.lower_bound)) ||
                (compare_value_range.has_upper_bound &&
                 base::IsGreaterEqual(val, compare_value_range.upper_bound)));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::INTERSECT: {
        if (value_type == CompareValueType::RANGE) {
          return std::any_of(list_val.begin(), list_val.end(), [this](const double val) {
            return (!(compare_value_range.has_lower_bound &&
                      base::IsLess(val, compare_value_range.lower_bound)) &&
                    !(compare_value_range.has_upper_bound &&
                      base::IsGreaterEqual(val, compare_value_range.upper_bound)));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      case CompareOperator::NOT_INTERSECT: {
        if (value_type == CompareValueType::RANGE) {
          return std::none_of(list_val.begin(), list_val.end(), [this](const double val) {
            return (!(compare_value_range.has_lower_bound &&
                      base::IsLess(val, compare_value_range.lower_bound)) &&
                    !(compare_value_range.has_upper_bound &&
                      base::IsGreaterEqual(val, compare_value_range.upper_bound)));
          });
        } else {
          is_illegal_rule = true;
          illegal_info = "rule cancelled: attr " + attr_name +
                         " doesn't allow the comparing operator: " + ExtractOperatorString();
          return false;
        }
      }
      default:
        is_illegal_rule = true;
        illegal_info = "rule cancelled: attr " + attr_name +
                       " doesn't allow the comparing operator: " + ExtractOperatorString();
        return false;
    }
  }
};
/**
 * NOTE(fangjianbing):
 * CommonRecoLeaf Processor 基类
 * 注意：原则上禁止 Leaf 开发者直接继承该类进行新 Processor 的实现
 *
 * CommonRecoBaseProcessor 中的 GetXxxProcessorParameter 方法提供了两种配置获取方式：
 * 1. 从 dynamic_json_config.json 中获取静态的配置值，例如 {"size": 1}，将始终返回 1 作为 "size" 的参数值
 * 2. 从 Context 的 CommonAttr 中获取动态的参数值，value 需为 string 且满足 "{{}}" 格式，
 *    例如 {"size": "{{SizeAttr}}"}，将返回名为 "SizeAttr" 的 CommonAttr 值作为 "size" 的参数值
 */
class CommonRecoBaseProcessor : public BaseRecoProcessor {
 public:
  bool Skip(ReadableRecoContextInterface *context, RecoResultConstIter begin,
            RecoResultConstIter end) const final {
    return GetBoolProcessorParameter(context, "skip", false) ||
           ProcessorCircuitBreaker::ShouldBreak(context->GetRequestType(), GetName());
  }

  virtual CacheStatus CacheRead(MutableRecoContextInterface *context, RecoResultConstIter first,
                                RecoResultConstIter last, std::vector<CommonRecoResult> *target_items) {
    CL_LOG(ERROR) << __func__ << "() is NOT implemented";
    return CacheStatus::NONE;
  }

  CacheStatus GetUnCachedItems(ReadableRecoContextInterface *context, RecoResultConstIter first,
                               RecoResultConstIter last, std::vector<CommonRecoResult> *target_items) {
    int64 start_ts = base::GetTimestamp();
    need_update_cache_ = true;
    miss_cache_item_keys_.clear();
    auto *mutable_context = static_cast<MutableRecoContextInterface *>(context);
    auto res = CacheRead(mutable_context, first, last, target_items);
    if (res == CacheStatus::FULLY) {
      need_update_cache_ = false;
    }
    int64 duration = base::GetTimestamp() - start_ts;
    CL_PERF_INTERVAL(duration, kPerfNs, "processor_output_cache.read", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), GetName(), type_name(), "",
                     GlobalHolder::GetJsonConfigVersion());
    return res;
  }

  virtual void CacheWrite(MutableRecoContextInterface *context) const {}

  virtual void UpdateCacheAfterRun(ReadableRecoContextInterface *context) const {}

  void InternalClear(ReadableRecoContextInterface *context) final {
    if (output_cache_config_) {
      output_cache_config_->ClearCacheKeyValue();
    }
  }

  int GetAsyncWaitPriority() const final {
    return async_wait_priority_;
  }

  bool RecordStepInfoDetails(ReadableRecoContextInterface *context, bool default_traceback) const final {
    return config()->GetBoolean("traceback", default_traceback);
  }

  bool RecordStepInfoFullDetails() const final {
    return need_full_traceback_;
  }

  bool PerfOutputAttrs() const {
    return need_perf_output_;
  }

  bool DebugLogEnabled() const {
    return debug_log_output_type_ != DebugLogOutputType::NONE;
  }

  DebugLogOutputType DebugLogOutput() const {
    return debug_log_output_type_;
  }

  int DebugLogItemNum() const {
    return config()->GetInt("debug_log_item_num", 10);
  }

  std::string GetDegradeKey(ReadableRecoContextInterface *context) const override {
    return DegraderManager::GenDegraderKey(
        context->GetRequestType(), GetName(),
        GetDynamicStringParam(context, config()->Get("degrade_strategy")),
        GetDynamicStringParam(context, config()->Get("degrade_strategy_prefix")));
  }

  std::string GetDegradeLimitAttr() const override {
    return config()->GetString("degrade_limit", "");
  }

  void SetDegradeLimit() override {
    degrade_limit_ = true;
  }

  void RegistDynamicDegrader(ReadableRecoContextInterface *context) {
    std::string dynamic_prefix = GetDynamicStringParam(context, config()->Get("degrade_strategy_prefix"));
    if (dynamic_prefix.empty()) {
      return;
    }

    std::string strategy_name = GetDynamicStringParam(context, config()->Get("degrade_strategy"));
    std::string full_strategy_name = dynamic_prefix + strategy_name;
    if (DegraderManager::Singleton()->Contain(full_strategy_name)) {
      return;
    }
    auto *degrader = DegraderManager::Singleton()->Get(strategy_name);
    if (!degrader) {
      return;
    }

    CL_LOG(INFO) << "Create dynamic degrader " << full_strategy_name;

    DegraderManager::Singleton()->Insert(
        full_strategy_name, DegraderManager::Singleton()->CreateDegraderFromGlobalConfig(strategy_name));
  }

  virtual bool IsCacheEnabled(ReadableRecoContextInterface *context) const {
    return false;
  }

  const std::string &GetItemCacheKeyAttrName() const {
    static const std::string default_str = "";
    return output_cache_config_ ? output_cache_config_->item_cache_key : default_str;
  }

  const std::string &GetCommonCacheKeyAttrName() const {
    static const std::string default_str = "";
    return output_cache_config_ ? output_cache_config_->common_cache_key : default_str;
  }

  const std::string &GetRetrieveCacheKeyAttrName() const {
    static const std::string default_str = "";
    return output_cache_config_ ? output_cache_config_->retrieve_cache_key : default_str;
  }

  std::string GetCacheKeyValueByName(ReadableRecoContextInterface *context,
                                     const std::string &cache_key_name) const {
    // 对 cache_key 的要求，必须要满足条件: cache_key 为非 list 类型或者 list size 为 1
    std::string default_val = "";
    if (cache_key_name.empty()) return default_val;

    auto *attr = context->GetCommonAttr(cache_key_name);
    if (!attr) {
      return default_val;
    }

    switch (attr->value_type) {
      case AttrType::INT: {
        if (auto int_val = context->GetIntCommonAttr(attr)) {
          return base::IntToString(*int_val);
        }
        break;
      }
      case AttrType::STRING: {
        if (auto str_val = context->GetStringCommonAttr(attr)) {
          return std::string(str_val->data(), str_val->size());
        }
        break;
      }
      case AttrType::INT_LIST: {
        if (auto list_val = context->GetIntListCommonAttr(attr)) {
          if (list_val->size() == 1) {
            return base::IntToString(list_val->at(0));
          }
          CL_LOG(ERROR) << "common_cache_key " << attr << " size error: " << list_val->size();
        }
        break;
      }
      case AttrType::STRING_LIST: {
        if (auto list_val = context->GetStringListCommonAttr(attr)) {
          if (list_val->size() == 1) {
            auto target = list_val->at(0);
            return std::string(target.data(), target.size());
          }
          CL_LOG(ERROR) << "common_cache_key " << attr << " size error: " << list_val->size();
        }
        break;
      }
      case AttrType::UNKNOWN:
        CL_LOG(INFO) << "cache_key unknown type: " << cache_key_name;
        break;
      default:
        CL_LOG(ERROR) << "cache_key type error: " << cache_key_name;
        break;
    }
    return default_val;
  }

  std::string GetCommonCacheKeyValue(ReadableRecoContextInterface *context) const {
    if (!output_cache_config_->common_cache_key_value.empty()) {
      return output_cache_config_->common_cache_key_value;
    }
    return output_cache_config_->common_cache_key_value =
               GetCacheKeyValueByName(context, GetCommonCacheKeyAttrName());
  }

  std::string GetRetrieveCacheKeyValue(ReadableRecoContextInterface *context) const {
    if (!output_cache_config_->retrieve_cache_key_value.empty()) {
      return output_cache_config_->retrieve_cache_key_value;
    }
    return output_cache_config_->retrieve_cache_key_value =
               GetCacheKeyValueByName(context, GetRetrieveCacheKeyAttrName());
  }

  bool IsItemCacheKeyUseItemKey() const {
    return output_cache_config_ ? output_cache_config_->item_cache_key_use_item_key : false;
  }

  bool IsItemCacheKeyUseUidAndDid() const {
    return output_cache_config_ ? output_cache_config_->item_cache_key_use_uid_and_did : false;
  }

  void InspectAttrs(ReadableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end,
                    bool is_input, bool is_async_delayed) const {
    std::vector<CommonAttr *> common_attr_accessors;
    std::vector<ItemAttr *> item_attr_accessors;
    if (PerfOutputAttrs() || DebugLogEnabled()) {
      if (is_input) {
        if (meta_data_.input_common_attrs) {
          for (const auto &attr_name : *(meta_data_.input_common_attrs)) {
            common_attr_accessors.push_back(context->GetCommonAttrAccessor(attr_name));
          }
        }
        if (meta_data_.input_item_attrs) {
          for (const auto &attr_name : *(meta_data_.input_item_attrs)) {
            item_attr_accessors.push_back(context->GetItemAttrAccessor(attr_name));
          }
        }
      } else {
        if (meta_data_.output_common_attrs) {
          for (auto attr_name : *(meta_data_.output_common_attrs)) {
            common_attr_accessors.push_back(context->GetCommonAttrAccessor(attr_name));
          }
        }
        if (meta_data_.output_item_attrs) {
          for (auto attr_name : *(meta_data_.output_item_attrs)) {
            item_attr_accessors.push_back(context->GetItemAttrAccessor(attr_name));
          }
        }
      }
    }

    if (!is_input && PerfOutputAttrs()) {
      std::string check_point = absl::StrCat("[OUTPUT] ", GetName());
      PerflogCommonAttrValue(context, common_attr_accessors, check_point);
      PerflogItemAttrValue(context, begin, end, item_attr_accessors, check_point);
    }

    if (DebugLogEnabled()) {
      if (debug_log_output_type_ != DebugLogOutputType::CL_LOG || LoggingUtil::IsLoggingEnabled()) {
        std::string output_str;
        absl::SubstituteAndAppend(&output_str, "\n\n----------- [$0] $1 <$2> -----------",
                                  (is_input ? "INPUT" : "OUTPUT"), GetName(), type_name());
        absl::SubstituteAndAppend(
            &output_str, "\nrequest_id: $0, user_id: $1, device_id: $2, request_num: $3, request_type: $4",
            context->GetRequestId(), context->GetUserId(), context->GetDeviceId(), context->GetRequestNum(),
            context->GetRequestType());
        if (!GetTableName().empty()) {
          absl::StrAppend(&output_str, ", item_table: ", GetTableName());
        }
        if (is_async_delayed) {
          absl::StrAppend(
              &output_str,
              "\n(async output will be delay printed, please try find them in the later log...)\n\n");
        } else {
          const std::string &common_attr_str = GetCommonAttrDebugInfo(context, common_attr_accessors);
          const std::string &item_attr_str =
              GetItemAttrDebugInfo(context, begin, end, item_attr_accessors, DebugLogItemNum());
          absl::StrAppend(&output_str, common_attr_str, item_attr_str, "\n\n");
        }

        if (debug_log_output_type_ == DebugLogOutputType::STDOUT) {
          std::cout << output_str;
        } else if (debug_log_output_type_ == DebugLogOutputType::STDERR) {
          std::cerr << output_str;
        } else {
          LOG(INFO) << output_str;
        }
      }
    }
  }

 protected:
  CommonRecoBaseProcessor() {}

  std::vector<std::pair<ItemAttr *, absl::flat_hash_set<int64>>> GenTargetItemAttrMapping(
      ReadableRecoContextInterface *context, const base::Json *target_item_config) {
    std::vector<std::pair<ItemAttr *, absl::flat_hash_set<int64>>> res;
    if (target_item_config && target_item_config->IsObject()) {
      for (const auto &pr : target_item_config->objects()) {
        auto attr_accessor = context->GetItemAttrAccessor(pr.first);
        auto target_val = pr.second;
        absl::flat_hash_set<int64> target_value;
        if (attr_accessor->value_type == AttrType::INT) {
          auto append_int_target_value = [&](const base::Json *json_value) {
            int64 int_val;
            if (TryGetIntProcessorParameter(context, json_value, &int_val)) {
              target_value.insert(int_val);
            } else {
              std::vector<int64> int_val_list;
              if (TryGetDynamicIntListParam(context, json_value, &int_val_list)) {
                for (const auto value : int_val_list) {
                  target_value.insert(value);
                }
              }
            }
          };
          if (target_val->IsArray()) {
            for (auto *json_val : target_val->array()) {
              append_int_target_value(json_val);
            }
          } else {
            append_int_target_value(target_val);
          }
        } else if (attr_accessor->value_type == AttrType::STRING) {
          auto append_string_target_value = [&](const base::Json *json_value) {
            std::string string_val;
            if (TryGetStringProcessorParameter(context, json_value, &string_val)) {
              target_value.insert(base::CityHash64(string_val.data(), string_val.size()));
            } else {
              std::vector<absl::string_view> string_val_list;
              if (TryGetDynamicStringListParam(context, json_value, &string_val_list)) {
                for (const auto &value : string_val_list) {
                  target_value.insert(base::CityHash64(value.data(), value.size()));
                }
              }
            }
          };
          if (target_val->IsArray()) {
            for (auto *json_val : target_val->array()) {
              append_string_target_value(json_val);
            }
          } else {
            append_string_target_value(target_val);
          }
        }
        res.push_back({attr_accessor, target_value});
      }
    }
    return res;
  }

  bool IsTargetItem(ReadableRecoContextInterface *context,
                    const std::vector<std::pair<ItemAttr *, absl::flat_hash_set<int64>>> &target_values,
                    const CommonRecoResult &result, bool check_multi_table = false) {
    return std::all_of(target_values.begin(), target_values.end(),
                       [this, &result, context, check_multi_table](auto &pr) {
                         auto &attr_accessor = pr.first;
                         auto &target_val = pr.second;
                         if (attr_accessor->value_type == AttrType::INT) {
                           auto p = result.GetIntAttr(attr_accessor, check_multi_table);
                           return (p && target_val.count(*p));
                         } else if (attr_accessor->value_type == AttrType::STRING) {
                           auto p = result.GetStringAttr(attr_accessor, check_multi_table);
                           return (p && target_val.count(base::CityHash64(p->data(), p->size())));
                         }
                         return false;
                       });
  }

  // NOTE(fangjianbing): 各个 GetXxxProcessorParameter 方法用于封装 int/double/bool/string 类型的 config
  // 抽取逻辑, 既支持原始的 base::Json 直接取值, 也支持通过 "{{ATTR}}" 格式从名为 ATTR 的 CommonAttr 中取值
  int64 GetIntProcessorParameter(const ReadableRecoContextInterface *context, const std::string &key,
                                 int64 default_val = 0) const {
    auto *json = config()->Get(key);
    return GetDynamicIntParam(context, json, default_val);
  }

  int64 GetIntProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                 int64 default_val = 0) const {
    return GetDynamicIntParam(context, json, default_val);
  }

  bool TryGetIntProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                   int64 *default_val) const {
    return TryGetDynamicIntParam(context, json, default_val);
  }

  double GetDoubleProcessorParameter(const ReadableRecoContextInterface *context, const std::string &key,
                                     double default_val = 0.0, bool try_int_attr = false) const {
    auto *json = config()->Get(key);
    return GetDynamicDoubleParam(context, json, default_val, try_int_attr);
  }

  double GetDoubleProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                     double default_val = 0.0, bool try_int_attr = false) const {
    return GetDynamicDoubleParam(context, json, default_val, try_int_attr);
  }

  bool TryGetDoubleProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                      double *default_val, bool try_int_attr = false) const {
    return TryGetDynamicDoubleParam(context, json, default_val, try_int_attr);
  }

  bool GetBoolProcessorParameter(const ReadableRecoContextInterface *context, const std::string &key,
                                 bool default_val = false) const {
    auto *json = config()->Get(key);
    return GetDynamicBoolParam(context, json, default_val);
  }

  bool GetBoolProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                 bool default_val = false) const {
    return GetDynamicBoolParam(context, json, default_val);
  }

  std::string GetStringProcessorParameter(const ReadableRecoContextInterface *context, const std::string &key,
                                          const std::string &default_val = "") const {
    auto *json = config()->Get(key);
    return GetDynamicStringParam(context, json, default_val);
  }

  std::string GetStringProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                          const std::string &default_val = "") const {
    return GetDynamicStringParam(context, json, default_val);
  }

  bool TryGetStringProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                      std::string *default_val) const {
    return TryGetDynamicStringParam(context, json, default_val);
  }

  std::vector<int64> GetIntListProcessorParameter(const ReadableRecoContextInterface *context,
                                                  const std::string &key) const {
    auto *json = config()->Get(key);
    return GetDynamicIntListParam(context, json);
  }

  std::vector<int64> GetIntListProcessorParameter(const ReadableRecoContextInterface *context,
                                                  const base::Json *json) const {
    return GetDynamicIntListParam(context, json);
  }

  bool TryGetIntListProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                       std::vector<int64> *int_list_val) const {
    return TryGetDynamicIntListParam(context, json, int_list_val);
  }

  std::vector<double> GetDoubleListProcessorParameter(const ReadableRecoContextInterface *context,
                                                      const std::string &key) const {
    auto *json = config()->Get(key);
    return GetDynamicDoubleListParam(context, json);
  }

  std::vector<double> GetDoubleListProcessorParameter(const ReadableRecoContextInterface *context,
                                                      const base::Json *json) const {
    return GetDynamicDoubleListParam(context, json);
  }

  bool TryGetDoubleListProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                          std::vector<double> *double_list_val) const {
    return TryGetDynamicDoubleListParam(context, json, double_list_val);
  }

  std::vector<absl::string_view> GetStringListProcessorParameter(const ReadableRecoContextInterface *context,
                                                                 const std::string &key) const {
    auto *json = config()->Get(key);
    return GetDynamicStringListParam(context, json);
  }

  std::vector<absl::string_view> GetStringListProcessorParameter(const ReadableRecoContextInterface *context,
                                                                 const base::Json *json) const {
    return GetDynamicStringListParam(context, json);
  }

  bool TryGetStringListProcessorParameter(const ReadableRecoContextInterface *context, const base::Json *json,
                                          std::vector<absl::string_view> *string_list_val) const {
    return TryGetDynamicStringListParam(context, json, string_list_val);
  }

  RuleFilter *GetSelectItemRoot() {
    return select_rule_root_.get();
  }

  void InitConfigData() final {
    meta_data_.processor_name = name();
    meta_data_.processor_type_name = type_name();

    if (const auto *meta_data_config = config()->Get(kMetaData)) {
      auto extract_helper = [this](const base::Json *config, StringViewSet *target) {
        std::set<std::string> attrs;
        RecoUtil::ExtractStringSetFromJsonConfig(config, &attrs);
        std::for_each(attrs.begin(), attrs.end(), [this, target](const auto &attr) {
          meta_data_.attr_names.emplace_back(std::make_unique<std::string>(attr));
          target->insert(*(meta_data_.attr_names.back()));
        });
      };
      if (meta_data_config->Get(kModifyItemTables)) {
        meta_data_.modify_item_tables = std::make_unique<StringViewSet>();
        extract_helper(meta_data_config->Get(kModifyItemTables), meta_data_.modify_item_tables.get());
      }
      if (meta_data_config->Get(kInputItemAttrs)) {
        meta_data_.input_item_attrs = std::make_unique<StringViewSet>();
        extract_helper(meta_data_config->Get(kInputItemAttrs), meta_data_.input_item_attrs.get());
      }
      if (meta_data_config->Get(kInputCommonAttrs)) {
        meta_data_.input_common_attrs = std::make_unique<StringViewSet>();
        extract_helper(meta_data_config->Get(kInputCommonAttrs), meta_data_.input_common_attrs.get());
      }
      if (meta_data_config->Get(kOutputCommonAttrs)) {
        meta_data_.output_common_attrs = std::make_unique<StringViewSet>();
        extract_helper(meta_data_config->Get(kOutputCommonAttrs), meta_data_.output_common_attrs.get());
      }
      if (meta_data_config->Get(kOutputItemAttrs)) {
        meta_data_.output_item_attrs = std::make_unique<StringViewSet>();
        extract_helper(meta_data_config->Get(kOutputItemAttrs), meta_data_.output_item_attrs.get());
      }
      if (meta_data_config->Get(kDownstreamItemAttrs)) {
        meta_data_.downstream_item_attrs = std::make_unique<std::set<std::string>>();
        RecoUtil::ExtractStringSetFromJsonConfig(meta_data_config->Get(kDownstreamItemAttrs),
                                                 meta_data_.downstream_item_attrs.get());
      }
      if (meta_data_config->Get(kDownstreamNewResult)) {
        meta_data_.downstream_new_result = meta_data_config->GetBoolean(kDownstreamNewResult, false);
      }
    }

    if (const auto *span_tags_config = config()->Get("span_tags")) {
      meta_data_.span_tags = std::make_unique<std::unordered_map<std::string, std::string>>();
      RecoUtil::ExtractStringMapFromJsonConfig(span_tags_config, meta_data_.span_tags.get());
    }
    if (const auto *eval_config = config()->Get(kEvalCommonAttrs)) {
      meta_data_.eval_common_attrs = std::make_unique<std::vector<std::string>>();
      RecoUtil::ExtractStringListFromJsonConfig(eval_config, meta_data_.eval_common_attrs.get());
    }

    json_t *json_value = base::StringToJson(config()->ToString());
    json_object_del(json_value, kMetaData);
    json_object_del(json_value, kOutputItemAttrs);
    json_object_del(json_value, kDownstreamItemAttrs);
    json_object_del(json_value, kDownstreamNewResult);
    meta_data_.json_config_string = std::make_unique<base::Json>(json_value)->ToString();

    async_wait_priority_ = config()->GetInt("wait_priority", 0);
    need_perf_output_ = config()->GetBoolean("perf_output", false);
    need_full_traceback_ = config()->GetBoolean("full_traceback", false);
    debug_log_output_type_ = InitDebugLogOutput(config());

    auto *select_rule = config()->Get("select_item");
    if (select_rule && select_rule->IsObject()) {
      select_rule_root_ = std::make_unique<RuleFilter>();
      select_rule_root_->InitFilterTree(select_rule, select_rule_root_.get(), "select");
      select_rule_root_->limit = select_rule->Get("limit");
    }

    auto *cache_outputs = config()->Get("cache_outputs");
    if (cache_outputs && cache_outputs->IsObject()) {
      output_cache_config_ = std::make_unique<CacheConfig>();
      output_cache_config_->InitCacheConfig(cache_outputs);
    }

    SetupTargetReasonSet();
    SetupTargetChannelSet();
  }

  // 不同版本的 callback 注册接口
  template <typename Response, typename Callback>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context,
                             ks::kess::rpc::grpc::Future<Response *> future, Callback &&callback,
                             const std::string &info = "") {
    RegisterAsyncCallback(
        context, std::move(future), std::forward<Callback>(callback), []() {}, info);
  }

  template <typename Response, typename Callback,
            std::enable_if_t<folly::is_invocable_r<void, Callback, Response *>::value, int> = 0>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context,
                             ks::kess::rpc::grpc::Future<Response *> future, Callback &&callback,
                             std::function<void()> finally, const std::string &info = "") {
    RegisterAsyncCallback<Response *, std::tuple<::grpc::Status, Response *>>(
        context, std::move(future), std::forward<Callback>(callback), std::move(finally),
        /*status_getter=*/
        [](const std::tuple<::grpc::Status, Response *> &value) -> bool { return std::get<0>(value).ok(); },
        /*payload_getter=*/
        [](const std::tuple<::grpc::Status, Response *> &value) -> Response *const & {
          return std::get<1>(value);
        },
        /*err_msg_getter=*/
        [](const std::tuple<::grpc::Status, Response *> &value) -> std::string {
          const ::grpc::Status &status = std::get<0>(value);
          return absl::StrCat("grpc_err_", std::to_string(status.error_code()), "-", status.error_message());
        },
        info);
  }

  template <typename T, typename Callback,
            std::enable_if_t<folly::is_invocable_r<void, Callback, const T &>::value, int> = 0>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context, ks::kess::rpc::Future<T> future,
                             Callback &&callback, const std::string &info = "") {
    RegisterAsyncCallback<T, T>(
        context, std::move(future), std::forward<Callback>(callback), []() {},
        /*status_getter=*/
        [](const T &value) -> bool { return true; },
        /*payload_getter=*/
        [](const T &value) -> const T & { return value; },
        /*err_msg_getter=*/
        [](const T &value) -> std::string { return ""; }, info);
  }

  template <typename Payload, typename FutureValue>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context, ks::kess::rpc::Future<FutureValue> future,
                             std::function<void(const Payload &)> callback, std::function<void()> finally,
                             std::function<bool(const FutureValue &)> status_getter,
                             std::function<const Payload &(const FutureValue &)> payload_getter,
                             std::function<std::string(const FutureValue &)> err_msg_getter,
                             const std::string &info = "") {
    const std::string &name = GetName();
    const std::string &downstream_name = GetDownstreamProcessor();
    const std::string &degrade_key = GetDegradeKey(context);
    DegraderManager::Singleton()->BeginCall(degrade_key);
    std::string status_attr = GetAsyncStatusAttr();

    auto *running_processor = this;
    context->GetAsyncUpstreamProcessors(downstream_name, true)->push_back(name);
    context->GetBatchWaiter(downstream_name, true)
        ->Add(
            std::move(future),
            [callback = std::move(callback), finally = std::move(finally),
             status_getter = std::move(status_getter), payload_getter = std::move(payload_getter),
             err_msg_getter = std::move(err_msg_getter), status_attr = std::move(status_attr), name, context,
             running_processor, downstream_name, degrade_key, info](const FutureValue &value) mutable {
              bool success = status_getter(value);
              DegraderManager::Singleton()->EndCall(degrade_key, success);

              auto *mutable_context = static_cast<MutableRecoContextInterface *>(context);
              mutable_context->SetRunningProcessor(running_processor);
              if (!status_attr.empty()) {
                mutable_context->SetIntCommonAttr(status_attr, success ? 0 : 1, false, false);
              }

              auto biz_perf_extra_a = context->GetStringCommonAttr(kPerfLogExtraA);
              auto biz_perf_extra_b = context->GetStringCommonAttr(kPerfLogExtraB);
              auto biz_perf_extra_c = context->GetStringCommonAttr(kPerfLogExtraC);
              auto perf_extra4 = std::string(biz_perf_extra_a.value_or(downstream_name));
              auto perf_extra5 = std::string(biz_perf_extra_b.value_or(""));
              auto perf_extra6 = std::string(biz_perf_extra_c.value_or(GlobalHolder::GetJsonConfigVersion()));

              auto *common_context = static_cast<CommonRecoContext *>(context);
              std::vector<CommonRecoResult> *results = common_context->GetRecoResults();

              if (success) {
                int64 start_ts = base::GetTimestamp();
                callback(payload_getter(value));
                int64 duration = base::GetTimestamp() - start_ts;
                CL_PERF_INTERVAL(
                    duration, kPerfNs, "callback_execute_time", GlobalHolder::GetServiceIdentifier(),
                    context->GetRequestType(), name, perf_extra4, perf_extra5, perf_extra6);
                running_processor->UpdateCacheAfterRun(context);
              } else {
                base::perfutil::PerfUtilWrapper::CountLogStash(
                    kPerfNs, "async_wait_fail", GlobalHolder::GetServiceIdentifier(),
                    context->GetRequestType(), name, perf_extra4, perf_extra5);

                CL_LOG_WARNING(err_msg_getter(value), info)
                    << err_msg_getter(value) << ", extra_info: " << info
                    << RecoUtil::GetRequestInfoForLog(context);
              }
              finally();
              running_processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), false,
                                              false);
            },
            GetName());
  }

  template <typename T, typename Callback,
            std::enable_if_t<folly::is_invocable_r<void, Callback, const T &>::value, int> = 0>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context, CommonRecoFutureActionWrapper<T> future,
                             Callback &&callback, const std::string &info = "") {
    RegisterAsyncCallback<T, T>(
        context, std::move(future), std::forward<Callback>(callback), []() {},
        /*status_getter=*/
        [](const T &value) -> bool { return true; },
        /*payload_getter=*/
        [](const T &value) -> const T & { return value; },
        /*err_msg_getter=*/
        [](const T &value) -> std::string { return ""; }, info);
  }

  template <typename Payload, typename FutureValue>
  void RegisterAsyncCallback(ReadableRecoContextInterface *context,
                             CommonRecoFutureActionWrapper<FutureValue> future,
                             std::function<void(const Payload &)> callback, std::function<void()> finally,
                             std::function<bool(const FutureValue &)> status_getter,
                             std::function<const Payload &(const FutureValue &)> payload_getter,
                             std::function<std::string(const FutureValue &)> err_msg_getter,
                             const std::string &info = "") {
    const std::string &name = GetName();
    const std::string &downstream_name = GetDownstreamProcessor();
    const std::string &degrade_key = GetDegradeKey(context);
    DegraderManager::Singleton()->BeginCall(degrade_key);
    std::string status_attr = GetAsyncStatusAttr();

    auto *running_processor = this;
    context->GetAsyncUpstreamProcessors(downstream_name, true)->push_back(name);
    context->GetBatchWaiter(downstream_name, true)
        ->Add(
            std::move(future),
            [callback = std::move(callback), finally = std::move(finally),
             status_getter = std::move(status_getter), payload_getter = std::move(payload_getter),
             err_msg_getter = std::move(err_msg_getter), status_attr = std::move(status_attr), name, context,
             running_processor, downstream_name, degrade_key, info](const FutureValue &value) mutable {
              bool success = status_getter(value);
              DegraderManager::Singleton()->EndCall(degrade_key, success);

              auto *mutable_context = static_cast<MutableRecoContextInterface *>(context);
              mutable_context->SetRunningProcessor(running_processor);
              if (!status_attr.empty()) {
                mutable_context->SetIntCommonAttr(status_attr, success ? 0 : 1, false, false);
              }

              auto biz_perf_extra_a = context->GetStringCommonAttr(kPerfLogExtraA);
              auto biz_perf_extra_b = context->GetStringCommonAttr(kPerfLogExtraB);
              auto biz_perf_extra_c = context->GetStringCommonAttr(kPerfLogExtraC);
              auto perf_extra4 = std::string(biz_perf_extra_a.value_or(downstream_name));
              auto perf_extra5 = std::string(biz_perf_extra_b.value_or(""));
              auto perf_extra6 = std::string(biz_perf_extra_c.value_or(GlobalHolder::GetJsonConfigVersion()));

              auto *common_context = static_cast<CommonRecoContext *>(context);
              std::vector<CommonRecoResult> *results = common_context->GetRecoResults();

              if (success) {
                int64 start_ts = base::GetTimestamp();
                callback(payload_getter(value));
                int64 duration = base::GetTimestamp() - start_ts;
                CL_PERF_INTERVAL(
                    duration, kPerfNs, "callback_execute_time", GlobalHolder::GetServiceIdentifier(),
                    context->GetRequestType(), name, perf_extra4, perf_extra5, perf_extra6);
              } else {
                base::perfutil::PerfUtilWrapper::CountLogStash(
                    kPerfNs, "async_wait_fail", GlobalHolder::GetServiceIdentifier(),
                    context->GetRequestType(), name, perf_extra4, perf_extra5);

                CL_LOG_WARNING(err_msg_getter(value), info)
                    << err_msg_getter(value) << ", extra_info: " << info
                    << RecoUtil::GetRequestInfoForLog(context);
              }
              finally();
              running_processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), false,
                                              false);
            },
            GetName());
  }

  template <typename Response, typename Callback>
  void RegisterLocalAsyncCallback(ReadableRecoContextInterface *context, std::future<Response *> &&future,
                                  Callback &&callback, const std::string &info = "", int64 timeout_ms = 0) {
    RegisterLocalAsyncCallback(
        context, std::move(future), std::forward<Callback>(callback), []() {}, info, timeout_ms);
  }

  template <typename Response, typename Callback>
  void RegisterLocalAsyncCallback(ReadableRecoContextInterface *context, std::future<Response *> &&future,
                                  Callback &&callback, std::function<void()> finally,
                                  const std::string &info = "", int64 timeout_ms = 0,
                                  std::function<void()> timeout_cb = nullptr) {
    const std::string &name = GetName();
    int priority = GetAsyncWaitPriority();
    // 参照 batch_waiter_wrapper.h 里的实现
    std::function<void(Response *)> cb = std::forward<Callback>(callback);

    const std::string &downstream_name = GetDownstreamProcessor();
    const std::string &degrade_key = GetDegradeKey(context);
    DegraderManager::Singleton()->BeginCall(degrade_key);

    std::string status_attr = GetAsyncStatusAttr();

    auto *running_processor = this;
    context->GetAsyncUpstreamProcessors(downstream_name, true)->push_back(name);
    context->GetLocalAsyncWaiter(downstream_name, true)
        ->Add(
            context, std::move(future),
            [cb = std::move(cb), finally = std::move(finally), status_attr = std::move(status_attr), name,
             info, context, running_processor, downstream_name, degrade_key](Response *response) mutable {
              bool success = nullptr != response;
              DegraderManager::Singleton()->EndCall(degrade_key, success);

              auto *mutable_context = static_cast<MutableRecoContextInterface *>(context);
              mutable_context->SetRunningProcessor(running_processor);
              if (!status_attr.empty()) {
                mutable_context->SetIntCommonAttr(status_attr, success ? 0 : 1, false, false);
              }

              auto *common_context = static_cast<CommonRecoContext *>(context);
              std::vector<CommonRecoResult> *results = common_context->GetRecoResults();

              if (success) {
                int64 start_ts = base::GetTimestamp();
                cb(response);
                int64 duration = base::GetTimestamp() - start_ts;
                CL_PERF_INTERVAL(
                    duration, kPerfNs, "callback_execute_time", GlobalHolder::GetServiceIdentifier(),
                    context->GetRequestType(), name, downstream_name, "",
                    GlobalHolder::GetJsonConfigVersion());
                running_processor->UpdateCacheAfterRun(context);
              } else {
                CL_LOG_WARNING("local_async_fail", info)
                    << "local_async process error, processor: " << name << ", extra_info: " << info
                    << RecoUtil::GetRequestInfoForLog(context);
              }
              finally();
              running_processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), false,
                                              false);
            },
            GetName(), priority, timeout_ms, std::move(timeout_cb));
  }

  virtual bool GetTargetItems(ReadableRecoContextInterface *context, RecoResultConstIter first,
                              RecoResultConstIter last, std::vector<CommonRecoResult> *target_items,
                              std::vector<int> *target_items_indices = nullptr,
                              bool check_multi_table = false) {
    if (!target_items) {
      return false;
    }

    std::function<bool(const CommonRecoResult &)> pred;
    const auto *target_item_config = GetTargetItemConfig();
    if (!target_channel_set_.empty()) {
      VLOG(100) << "processor: " << GetName()
                << ", dealing with target_channel: " << folly::join(",", target_channel_set_);
      pred = [this](const CommonRecoResult &result) -> bool {
        return target_channel_set_.count(result.channel);
      };
    } else if (!target_reason_set_.empty()) {
      VLOG(100) << "processor: " << GetName()
                << ", dealing with target_reason: " << folly::join(",", target_reason_set_);
      pred = [this](const CommonRecoResult &result) -> bool {
        return target_reason_set_.count(result.reason);
      };
    } else if (target_item_config && target_item_config->IsObject() &&
               !target_item_config->objects().empty()) {
      auto mapping = GenTargetItemAttrMapping(context, target_item_config);
      pred = [this, mapping = std::move(mapping), context,
              check_multi_table](const CommonRecoResult &result) -> bool {
        return IsTargetItem(context, mapping, result, check_multi_table);
      };
      VLOG(100) << "processor: " << GetName()
                << ", dealing with target_item: " << target_item_config->ToString();
    } else if (GetTargetItemType() >= 0) {
      int target_item_type = GetTargetItemType();
      pred = [target_item_type](const CommonRecoResult &result) -> bool {
        return Util::GetType(result.item_key) == target_item_type;
      };
      VLOG(100) << "processor: " << GetName() << ", dealing with target_item_type: " << target_item_type;
    } else {
      return false;
    }

    int index = 0;
    for (auto it = first; it != last; ++it, ++index) {
      if (pred(*it)) {
        target_items->push_back(*it);
        if (target_items_indices) {
          target_items_indices->push_back(index);
        }
      }
    }

    return true;
  }

  bool LoadFilterRule(ReadableRecoContextInterface *context, RuleFilter *rule,
                      const std::string &remove_or_select, bool ignore_invalid_rule = false) {
    std::string error_info;
    if (remove_or_select == "remove") {
      error_info = "attr filter";
    } else {
      error_info = "select_item";
    }

    if (rule->join == JoinOperator::NONE) {
      rule->attr_accessor = context->GetItemAttrAccessor(rule->attr_name);
    }

    if (!(rule->compare_to_item_attr.empty())) {
      rule->compare_to_item_attr_accessor = context->GetItemAttrAccessor(rule->compare_to_item_attr);
      return true;
    }

    auto &value_range = rule->compare_value_range;
    if (value_range.lower_bound_json || value_range.upper_bound_json) {
      if (value_range.lower_bound_json) {
        value_range.has_lower_bound = TryGetDoubleProcessorParameter(context, value_range.lower_bound_json,
                                                                     &(value_range.lower_bound), true);
      }
      if (value_range.upper_bound_json) {
        value_range.has_upper_bound = TryGetDoubleProcessorParameter(context, value_range.upper_bound_json,
                                                                     &(value_range.upper_bound), true);
      }
      return true;
    }

    if (rule->compare_to_attr.empty()) return true;

    int64 int_val = 0;
    if (TryGetIntProcessorParameter(context, rule->compare_to_attr_json, &int_val)) {
      rule->value_type = CompareValueType::INT;
      rule->compare_to_int = int_val;
      return true;
    }

    double double_val = 0.0;
    if (TryGetDoubleProcessorParameter(context, rule->compare_to_attr_json, &double_val)) {
      rule->value_type = CompareValueType::DOUBLE;
      rule->compare_to_double = double_val;
      return true;
    }

    std::string str = "";
    if (TryGetStringProcessorParameter(context, rule->compare_to_attr_json, &str)) {
      if (rule->oper != CompareOperator::EQ && rule->oper != CompareOperator::NE &&
          rule->oper != CompareOperator::CONTAIN && rule->oper != CompareOperator::NOT_CONTAIN) {
        CL_LOG_ERROR("rule_filter", "invalid_string_compare:" + rule->compare_to_attr)
            << "attr filter cancelled: string common_attr " << rule->compare_to_attr
            << " could be compared for '==/!=/contain/not contain' operator only!";
        return false;
      }
      rule->value_type = CompareValueType::STRING;
      rule->compare_to_int = base::CityHash64(str.data(), str.size());
      return true;
    }
    rule->compare_to_list.clear();
    std::vector<int64> int_list;
    if (TryGetIntListProcessorParameter(context, rule->compare_to_attr_json, &int_list)) {
      if (rule->oper != CompareOperator::IN && rule->oper != CompareOperator::NOT_IN &&
          rule->oper != CompareOperator::CONTAIN && rule->oper != CompareOperator::NOT_CONTAIN &&
          rule->oper != CompareOperator::INTERSECT && rule->oper != CompareOperator::NOT_INTERSECT) {
        CL_LOG_ERROR("rule_filter", "invalid_int_list_compare:" + rule->compare_to_attr)
            << "attr filter cancelled: int_list common_attr " << rule->compare_to_attr
            << " could be compared for 'in/not in/contain/not contain/intersect/not intersect' operator "
               "only!";
        return false;
      }
      rule->value_type = CompareValueType::INT_LIST;
      for (auto int_v : int_list) {
        rule->compare_to_list.insert(int_v);
      }
      return true;
    }

    std::vector<absl::string_view> str_list;
    if (TryGetStringListProcessorParameter(context, rule->compare_to_attr_json, &str_list)) {
      if (rule->oper != CompareOperator::IN && rule->oper != CompareOperator::NOT_IN &&
          rule->oper != CompareOperator::CONTAIN && rule->oper != CompareOperator::NOT_CONTAIN &&
          rule->oper != CompareOperator::INTERSECT && rule->oper != CompareOperator::NOT_INTERSECT) {
        CL_LOG_ERROR("rule_filter", "invalid_string_list_compare:" + rule->compare_to_attr)
            << "attr filter cancelled: string_list common_attr " << rule->compare_to_attr
            << " could be compared for 'in/not in/contain/not contain/intersect/not intersect' operator "
               "only!";
        return false;
      }
      rule->value_type = CompareValueType::STRING_LIST;
      for (auto str_v : str_list) {
        rule->compare_to_list.insert(base::CityHash64(str_v.data(), str_v.size()));
      }
      return true;
    }

    if (ignore_invalid_rule) {
      rule->is_ignored_rule = true;
      return true;
    }
    CL_LOG_ERROR("rule_filter", "missing_common_attr:" + rule->compare_to_attr)
        << "rule filter cancelled: missing common_attr " << rule->compare_to_attr;
    return false;
  }

  bool LoadFilterTree(MutableRecoContextInterface *context, RuleFilter *rule,
                      const std::string &remove_or_select, bool ignore_invalid_rule = false) {
    if (!rule || !LoadFilterRule(context, rule, remove_or_select, ignore_invalid_rule)) {
      return false;
    }

    rule->enable = GetBoolProcessorParameter(context, rule->enable_config, true);
    if (!rule->enable) {
      return true;
    }

    for (RuleFilter &child : rule->children) {
      if (!LoadFilterTree(context, &child, remove_or_select, ignore_invalid_rule)) {
        return false;
      }
    }
    return true;
  }

  bool GetSelectItems(MutableRecoContextInterface *context, RecoResultConstIter first,
                      RecoResultConstIter last, std::vector<CommonRecoResult> *target_items,
                      std::vector<int> *target_items_indices = nullptr, bool check_multi_table = false) {
    auto *select_root = GetSelectItemRoot();
    if (!target_items || !select_root) {
      return false;
    }
    int limit = GetIntProcessorParameter(context, select_root->limit, -1);

    if (LoadFilterTree(context, select_root, "select")) {
      int index = 0;
      int type_mismatch_count = 0;
      for (auto it = first; it != last; ++it, ++index) {
        if ((limit != 0) && CheckItemMeetConditions(context, *it, select_root, "select", &type_mismatch_count,
                                                    check_multi_table)) {
          target_items->emplace_back(*it);
          if (target_items_indices) {
            target_items_indices->emplace_back(index);
          }
          limit--;
        }
      }
      if (type_mismatch_count > 0) {
        CL_LOG_ERROR_COUNT(type_mismatch_count, "select_item", "value_type_mismatch: select_item")
            << ", mismatched item num: " << type_mismatch_count;
      }
      return true;
    }
    return false;
  }

  // 只针对满足属性值条件的 item 进行处理
  virtual const base::Json *GetTargetItemConfig() const {
    return config()->Get("target_item");
  }
  // 只针对某个 item_type 进行处理
  virtual int GetTargetItemType() const {
    return config()->GetInt("target_item_type", -1);
  }
  // 只针对满足 reason 条件的 item 进行处理
  virtual void SetupTargetReasonSet() {
    target_reason_set_.clear();
    RecoUtil::ExtractIntSetFromJsonConfig(config()->Get("target_reason"), &target_reason_set_, true, true);
  }

  // 只针对满足 channel 条件的 item 进行处理
  virtual void SetupTargetChannelSet() {
    target_channel_set_.clear();
    RecoUtil::ExtractIntSetFromJsonConfig(config()->Get("target_channel"), &target_channel_set_, true, true);
  }

 protected:
  bool CheckItemMeetConditions(MutableRecoContextInterface *context, const CommonRecoResult &result,
                               RuleFilter *rule, const std::string &remove_or_select,
                               int *type_mismatch_count, bool check_multi_table = false) {
    // filter 不合法或开关不开，不做任何过滤
    if (!rule || !rule->enable || rule->is_illegal_rule) {
      if (rule->is_illegal_rule) {
        CL_LOG_ERROR(remove_or_select == "remove" ? "rule_filter" : "select_item",
                     "illegal_rule: " + rule->illegal_info);
      }
      return false;
    }

    if (rule->join == JoinOperator::NONE) {
      bool filter_condition =
          CheckCondition(context, result, rule, remove_or_select, type_mismatch_count, check_multi_table);
      return filter_condition;
    }

    if (rule->join == JoinOperator::AND) {
      int skipped_rule_count = 0;
      for (RuleFilter &child : rule->children) {
        if (child.is_ignored_rule || !child.enable) {
          skipped_rule_count += 1;
          continue;
        }
        if (!CheckItemMeetConditions(context, result, &child, remove_or_select, type_mismatch_count,
                                     check_multi_table)) {
          return false;
        }
      }
      return (skipped_rule_count < rule->children.size());
    } else if (rule->join == JoinOperator::OR) {
      for (RuleFilter &child : rule->children) {
        if (child.is_ignored_rule || !child.enable) {
          continue;
        }
        if (CheckItemMeetConditions(context, result, &child, remove_or_select, type_mismatch_count,
                                    check_multi_table)) {
          return true;
        }
      }
      return false;
    } else {
      return false;
    }
  }

  bool CheckCondition(MutableRecoContextInterface *context, const CommonRecoResult &result, RuleFilter *rule,
                      const std::string &remove_or_select, int *type_mismatch_count,
                      bool check_multi_table = false) {
    if (!rule) {
      return false;
    }
    if (rule->compare_to_item_attr_accessor) {
      rule->LoadFilterForItemCompare(context, result, remove_or_select);
    }

    if (rule->oper == CompareOperator::NOT_NULL) {
      return result.HasAttr(rule->attr_accessor, check_multi_table);
    }
    if (rule->oper == CompareOperator::IS_NULL) {
      return !result.HasAttr(rule->attr_accessor, check_multi_table);
    }
    if (rule->check_reason) {
      return rule->CheckAsInt(result.reason);
    }
    if (auto int_val = result.GetIntAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::INT || rule->value_type == CompareValueType::INT_LIST ||
          rule->value_type == CompareValueType::RANGE) {
        return rule->CheckAsInt(*int_val);
      } else if (rule->value_type == CompareValueType::DOUBLE) {
        return rule->CheckAsDouble(*int_val);
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else if (auto double_val = result.GetDoubleAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::INT || rule->value_type == CompareValueType::DOUBLE ||
          rule->value_type == CompareValueType::RANGE) {
        return rule->CheckAsDouble(*double_val);
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else if (auto str_val = result.GetStringAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::STRING || rule->value_type == CompareValueType::STRING_LIST) {
        return rule->CheckAsInt(base::CityHash64(str_val->data(), str_val->size()));
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else if (auto int_list = result.GetIntListAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::INT || rule->value_type == CompareValueType::INT_LIST ||
          rule->value_type == CompareValueType::RANGE) {
        return rule->CheckAsIntList(*int_list);
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else if (auto str_list = result.GetStringListAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::STRING || rule->value_type == CompareValueType::STRING_LIST) {
        std::vector<int64> int_list_val;
        for (auto sv : *str_list) {
          int_list_val.push_back(base::CityHash64(sv.data(), sv.size()));
        }
        return rule->CheckAsIntList(int_list_val);
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else if (auto double_list = result.GetDoubleListAttr(rule->attr_accessor, check_multi_table)) {
      if (rule->value_type == CompareValueType::RANGE) {
        return rule->CheckAsDoubleList(*double_list);
      } else {
        if (!rule->match_if_attr_missing) {
          ++(*type_mismatch_count);
        }
        return false;
      }
    } else {
      return rule->match_if_attr_missing && !result.HasAttr(rule->attr_accessor, check_multi_table);
    }
  }

 private:
  int64 GetDynamicIntParam(const ReadableRecoContextInterface *context, const base::Json *json,
                           int64 default_val) const {
    TryGetDynamicIntParam(context, json, &default_val);
    return default_val;
  }

  bool TryGetDynamicIntParam(const ReadableRecoContextInterface *context, const base::Json *json,
                             int64 *int_val) const {
    if (!json) {
      return false;
    }
    if (json->IsInteger()) {
      return json->IntValue(int_val);
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          return context->EvalIntParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name, int_val);
        } else {
          auto val = context->GetIntCommonAttr(*attr_name);
          if (val) {
            *int_val = *val;
            return true;
          }
        }
      }
    }
    return false;
  }

  bool GetDynamicBoolParam(const ReadableRecoContextInterface *context, const base::Json *json,
                           bool default_val) const {
    if (!json) {
      return default_val;
    }
    if (json->IsBoolean()) {
      return json->BooleanValue(default_val);
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          int64 int_val = 0;
          bool expr_is_valid =
              context->EvalIntParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name, &int_val);
          if (expr_is_valid) {
            return int_val;
          } else {
            return default_val;
          }
        } else {
          auto val = context->GetIntCommonAttr(*attr_name);
          if (val) {
            return *val;
          }
        }
      }
    }
    return default_val;
  }

  double GetDynamicDoubleParam(const ReadableRecoContextInterface *context, const base::Json *json,
                               double default_val, bool try_int_attr = false) const {
    TryGetDynamicDoubleParam(context, json, &default_val, try_int_attr);
    return default_val;
  }

  bool TryGetDynamicDoubleParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                double *double_val, bool try_int_attr = false) const {
    if (!json) {
      return false;
    }
    if (json->IsDouble() || json->IsInteger()) {
      return json->NumberValue(double_val);
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          return context->EvalDoubleParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name,
                                                     double_val);
        } else {
          auto val = context->GetDoubleCommonAttr(*attr_name);
          if (val) {
            *double_val = *val;
            return true;
          }
          if (try_int_attr) {
            auto val = context->GetIntCommonAttr(*attr_name);
            if (val) {
              *double_val = *val;
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  std::string GetDynamicStringParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                    const std::string &default_val = "") const {
    std::string result = default_val;
    TryGetDynamicStringParam(context, json, &result);
    return result;
  }

  bool TryGetDynamicStringParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                std::string *string_val) const {
    if (!json) {
      return false;
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          return context->EvalStringParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name,
                                                     string_val);
        } else {
          auto val = context->GetStringCommonAttr(*attr_name);
          if (val) {
            *string_val = std::string(*val);
            return true;
          }
        }
      } else {
        *string_val = json_string_value(json->get());
        return true;
      }
    }
    return false;
  }

  std::vector<int64> GetDynamicIntListParam(const ReadableRecoContextInterface *context,
                                            const base::Json *json) const {
    std::vector<int64> result;
    TryGetDynamicIntListParam(context, json, &result);
    return result;
  }

  bool TryGetDynamicIntListParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                 std::vector<int64> *int_list_val) const {
    if (!json) {
      return false;
    }
    if (json->IsArray()) {
      int_list_val->reserve(json->size());
      for (auto val : json->array()) {
        if (val->IsInteger()) {
          int_list_val->push_back(val->IntValue((int64)0));
        }
      }
      return true;
    }

    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          return context->EvalIntListParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name,
                                                      int_list_val);
        } else {
          auto val = context->GetIntListCommonAttr(*attr_name);
          if (val) {
            int_list_val->reserve(val->size());
            std::copy(val->begin(), val->end(), std::back_inserter(*int_list_val));
            return true;
          }
        }
      }
    }
    return false;
  }

  std::vector<double> GetDynamicDoubleListParam(const ReadableRecoContextInterface *context,
                                                const base::Json *json) const {
    std::vector<double> result;
    TryGetDynamicDoubleListParam(context, json, &result);
    return result;
  }

  bool TryGetDynamicDoubleListParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                    std::vector<double> *double_list_val) const {
    if (!json) {
      return false;
    }
    if (json->IsArray()) {
      double_list_val->reserve(json->size());
      double d;
      for (auto val : json->array()) {
        if (val->NumberValue(&d)) {
          double_list_val->push_back(d);
        }
      }
      return true;
    }
    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          return context->EvalDoubleListParamFromLuaExpr(meta_data_.eval_common_attrs.get(), *attr_name,
                                                         double_list_val);
        } else {
          auto val = context->GetDoubleListCommonAttr(*attr_name);
          if (val) {
            double_list_val->reserve(val->size());
            std::copy(val->begin(), val->end(), std::back_inserter(*double_list_val));
            return true;
          }
        }
      }
    }
    return false;
  }

  std::vector<absl::string_view> GetDynamicStringListParam(const ReadableRecoContextInterface *context,
                                                           const base::Json *json) const {
    std::vector<absl::string_view> result;
    TryGetDynamicStringListParam(context, json, &result);
    return result;
  }

  bool TryGetDynamicStringListParam(const ReadableRecoContextInterface *context, const base::Json *json,
                                    std::vector<absl::string_view> *string_list_val) const {
    if (!json) {
      return false;
    }
    if (json->IsArray()) {
      string_list_val->reserve(json->size());
      for (auto val : json->array()) {
        if (val->IsString()) {
          string_list_val->emplace_back(json_string_value(val->get()));
        }
      }
      return true;
    }

    if (json->IsString()) {
      if (auto attr_name = RecoUtil::ExtractCommonAttrFromExpr(json)) {
        if (RecoUtil::IsLuaExpr(*attr_name)) {
          CL_LOG_ERROR("dynamic_param", "unsupported lua return: string_list")
              << "TryGetDynamicStringList failed, return string_list is unsupported in lua expr";
          return false;
        } else {
          auto val = context->GetStringListCommonAttr(*attr_name);
          if (val) {
            string_list_val->reserve(val->size());
            std::copy(val->begin(), val->end(), std::back_inserter(*string_list_val));
            return true;
          }
        }
      }
    }
    return false;
  }

  DebugLogOutputType InitDebugLogOutput(const base::Json *config) {
    std::string output = config->GetString("debug_log_to");
    if (output.empty() && config->GetBoolean("debug_log", false)) {
      return DebugLogOutputType::CL_LOG;
    }
    if (output == "stdout") {
      return DebugLogOutputType::STDOUT;
    }
    if (output == "stderr") {
      return DebugLogOutputType::STDERR;
    }
    if (output == "log" || output == "glog") {
      return DebugLogOutputType::CL_LOG;
    }
    if (output == "log!" || output == "glog!") {
      return DebugLogOutputType::LOG;
    }
    return DebugLogOutputType::NONE;
  }

  void PerflogCommonAttrValue(ReadableRecoContextInterface *context,
                              const std::vector<CommonAttr *> &common_attr_accessors,
                              const std::string &check_point) const {
    for (const auto *accessor : common_attr_accessors) {
      double val = 0.0;
      bool has_value = false;
      if (auto double_val = accessor->GetDoubleValue()) {
        val = *double_val;
        has_value = true;
      } else if (auto int_val = accessor->GetIntValue()) {
        val = *int_val;
        has_value = true;
      } else if (auto int_list = accessor->GetIntListValue()) {
        if (!int_list->empty()) {
          val = std::accumulate(int_list->begin(), int_list->end(), 0.0) / int_list->size();
          has_value = true;
        }
      } else if (auto double_list = accessor->GetDoubleListValue()) {
        if (!double_list->empty()) {
          val = std::accumulate(double_list->begin(), double_list->end(), 0.0) / double_list->size();
          has_value = true;
        }
      }
      // perflog 不支持 double 类型值, 需放大倍数后保留小数部分信息, 这里固定保留 6 位小数
      if (has_value) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000000L * val, kPerfNs, "common_attr_avg_val", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), check_point, accessor->name());
      }
    }
  }

  void PerflogItemAttrValue(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                            RecoResultConstIter end, const std::vector<ItemAttr *> &item_attr_accessors,
                            const std::string &check_point) const {
    for (const auto *accessor : item_attr_accessors) {
      double sum = 0.0;
      int count = 0;
      int total_count = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        if (auto double_val = result.GetDoubleAttr(accessor)) {
          double val = *double_val;
          if (!std::isnan(val) && !std::isinf(val)) {
            sum += val;
            ++count;
          }
        } else if (auto int_val = result.GetIntAttr(accessor)) {
          sum += *int_val;
          ++count;
        } else if (auto double_list = result.GetDoubleListAttr(accessor)) {
          if (!double_list->empty()) {
            sum += std::accumulate(double_list->begin(), double_list->end(), 0.0) / double_list->size();
            ++count;
          }
        } else if (auto int_list = result.GetIntListAttr(accessor)) {
          if (!int_list->empty()) {
            sum += std::accumulate(int_list->begin(), int_list->end(), 0.0) / int_list->size();
            ++count;
          }
        }
        ++total_count;
      });
      // perflog 不支持 double 类型值, 需放大倍数后保留小数部分信息, 这里固定保留 6 位小数
      if (count > 0) {
        int64 avg = 1000000L * (sum / count);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            avg, kPerfNs, "item_attr_avg_val", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), check_point, accessor->name());
      }
      total_count = std::max(total_count, 1);
      int64 ratio = 1000000L * (static_cast<double>(count) / total_count);
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          ratio, kPerfNs, "item_attr_hit", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
          check_point, accessor->name());
    }
  }

  std::string GetCommonAttrDebugInfo(ReadableRecoContextInterface *context,
                                     const std::vector<CommonAttr *> &common_attr_accessors) const {
    std::string output_str = "\n\n[common_attr]";
    for (const auto *accessor : common_attr_accessors) {
      absl::StrAppend(&output_str, "\n", accessor->GetDebugString());
    }
    return output_str;
  }

  std::string GetItemAttrDebugInfo(ReadableRecoContextInterface *context, RecoResultConstIter begin,
                                   RecoResultConstIter end,
                                   const std::vector<ItemAttr *> &item_attr_accessors, int item_num) const {
    std::string output_str = "\n\n[item_attr]";
    auto last = end;
    int length = std::distance(begin, end);
    absl::StrAppend(&output_str, " total item num: ", length);
    if (item_num >= 0 && length > item_num) {
      last = std::next(begin, item_num);
      absl::StrAppend(&output_str, ", showing top ", item_num, " items");
    }
    int index = 0;
    for (auto it = begin; it != last; ++it, ++index) {
      absl::StrAppend(&output_str, "\n[", index, "] item_key: ", it->item_key);
      if (it->GetType() != 0) {
        absl::StrAppend(&output_str, ", item_id: ", it->GetId(), ", item_type: ", it->GetType());
      }
      absl::StrAppend(&output_str, ", reason: ", it->reason, ", score: ", it->score);
      if (it->channel != 0) {
        absl::StrAppend(&output_str, ", channel: ", it->channel);
      }
      if (!item_attr_accessors.empty()) {
        for (const auto *accessor : item_attr_accessors) {
          absl::StrAppend(&output_str, "\n", accessor->GetDebugString(*it));
        }
        absl::StrAppend(&output_str, "\n");
      }
    }
    return output_str;
  }

 protected:
  folly::F14FastSet<int64> target_reason_set_;
  folly::F14FastSet<int64> target_channel_set_;
  std::unique_ptr<CacheConfig> output_cache_config_{nullptr};
  bool degrade_limit_ = false;

 private:
  int async_wait_priority_ = 0;
  bool need_perf_output_ = false;
  bool need_full_traceback_ = false;
  DebugLogOutputType debug_log_output_type_ = DebugLogOutputType::NONE;
  std::unique_ptr<RuleFilter> select_rule_root_{nullptr};

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseProcessor);
};

}  // namespace platform
}  // namespace ks
