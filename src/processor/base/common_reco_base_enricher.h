#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"

DECLARE_bool(enable_processor_output_attr_cache);

namespace ks {
namespace platform {

/**
 * CommonRecoLeaf Enricher 的基类
 * Leaf 开发者应继承该类进行新 Enricher 的实现，针对场景：Attr 等 Context
 * 内容更新
 */
class CommonRecoBaseEnricher : public CommonRecoBaseProcessor {
 public:
  // 给 Leaf 开发人员实现具体 Enrich 逻辑的接口
  virtual void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                      RecoResultConstIter end) = 0;

  void Run(AddibleRecoContextInterface *context, std::vector<CommonRecoResult> *results) final {
    std::string item_list_from_attr = GetItemListCommonAttr();
    thread_local std::vector<CommonRecoResult> alternative_items;
    alternative_items.clear();
    if (!item_list_from_attr.empty()) {
      if (auto p_int_list = context->GetIntListCommonAttr(item_list_from_attr)) {
        std::transform(p_int_list->begin(), p_int_list->end(), std::back_inserter(alternative_items),
                       [context](uint64 item_key) { return context->NewCommonRecoResult(item_key, -1); });
      } else if (auto p_int = context->GetIntCommonAttr(item_list_from_attr)) {
        alternative_items.emplace_back(context->NewCommonRecoResult(*p_int, -1));
      } else {
        CL_LOG(INFO) << "int/int_list item_list_from_attr=" << item_list_from_attr
                     << " not found, cancelled run processor: " << GetName();
        return;
      }
      results = &alternative_items;
    }

    int size = results->size();
    auto begin = std::cbegin(*results);
    auto end = std::cend(*results);
    int range_start = GetRangeStart(context);
    int range_end = GetRangeEnd(context);
    auto first = range_start >= 0 ? std::next(begin, std::min(range_start, size))
                                  : std::prev(end, std::min(-range_start, size));
    auto last = range_end > 0 ? std::next(begin, std::min(range_end, size))
                              : std::prev(end, std::min(-range_end, size));
    if (last - first < 0) {
      last = first;
    }

    std::vector<CommonRecoResult> target_items;
    bool use_item_from_tables = ItemFromTables() != nullptr;
    bool use_target_items =
        GetTargetItems(context, first, last, &target_items, nullptr, use_item_from_tables);
    bool use_select_items =
        GetSelectItems(context, first, last, &target_items, nullptr, use_item_from_tables);

    if (use_target_items || use_select_items) {
      first = std::cbegin(target_items);
      last = std::cend(target_items);
    }

    if (use_item_from_tables) {
      folly::F14FastMap<std::string, int> table_item_cnt;
      for (auto it = first; it != last; ++it) {
        const AttrTable *table = it->GetTable();
        table_item_cnt[table->name()]++;
      }
      for (const auto &iter : table_item_cnt) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(iter.second, kPerfNs, "item_from_tables_item_cnt",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName(), iter.first);
      }
    }

    if (IsCacheEnabled(context)) {
      auto res = GetUnCachedItems(context, first, last, &target_items);
      if (res == CacheStatus::FULLY) {
        VLOG(2) << "skip Run() because of hit cache: " << GetName();
        return;
      } else if (res == CacheStatus::PARTIALLY) {
        first = std::cbegin(target_items);
        last = std::cend(target_items);
      }
    }

    if (degrade_limit_) {
      auto limit_num = context->GetIntCommonAttr(GetDegradeLimitAttr());
      if (limit_num && std::distance(first, last) > *limit_num) {
        last = std::next(first, *limit_num);
      }
      degrade_limit_ = false;
    }

    if (IsAsync()) {
      // 对异步 Processor 处理 partition 逻辑
      int partition_size = GetPartitionSize(context);
      int total_num = std::distance(first, last);
      int partition_num = partition_size > 0 ? (total_num / partition_size) : 0;

      auto partition_begin = first;
      // for loop 只处理 total_num / partition_size 的整除部分
      for (int i = 0; i < partition_num; ++i) {
        auto partition_end = std::next(partition_begin, partition_size);
        Enrich(context, partition_begin, partition_end);
        partition_begin = partition_end;
      }
      // if block 处理 total_num / partition_size 的余数部分
      // 额外判断 partition_num == 0 以保证 Enrich 方法至少被执行一次
      if (partition_num == 0 || std::distance(partition_begin, last) > 0) {
        Enrich(context, partition_begin, last);
      }
      // 异步算子 UpdateCacheAfterRun 在 RegisterLocalAsyncCallback 和 RegisterAsyncCallback 里调用
    } else {
      Enrich(context, first, last);
      UpdateCacheAfterRun(context);
    }
  }

  bool IsCacheEnabled(ReadableRecoContextInterface *context) const override {
    return FLAGS_enable_processor_output_attr_cache && output_cache_config_ &&
           GetBoolProcessorParameter(context, output_cache_config_->enable, false);
  }

  absl::optional<CacheKeyType> GetItemCacheKeyValueImpl(ReadableRecoContextInterface *context,
                                                        uint64_t item_key) const {
    static const uint64_t kDefaultUid{0};
    static const std::string kDefaultDid{""};
    static const uint64_t kDefaultItemKey{0};
    static const uint64_t kDefaultItemIntAttr{0};
    static const std::string kDefaultItemStringAttr{""};

    const auto &processor_name = GetName();
    uint64_t uid = kDefaultUid;
    absl::string_view did = kDefaultDid;
    if (IsItemCacheKeyUseUidAndDid()) {
      uid = context->GetUserId();
      did = context->GetDeviceId();
    }

    if (IsItemCacheKeyUseItemKey()) {
      return {{uid, did, processor_name, item_key, kDefaultItemIntAttr, kDefaultItemStringAttr}};
    }

    const auto &cache_key_name = GetItemCacheKeyAttrName();
    if (cache_key_name.empty()) return absl::nullopt;

    auto *attr = context->GetItemAttr(cache_key_name);
    if (!attr) {
      CL_LOG_EVERY_N(ERROR, 1000) << "not found cache_key in item attr: " << cache_key_name;
      return absl::nullopt;
    }

    switch (attr->value_type) {
      case AttrType::INT: {
        if (auto int_val = context->GetIntItemAttr(item_key, attr)) {
          return {{uid, did, processor_name, kDefaultItemKey, *int_val, kDefaultItemStringAttr}};
        }
        break;
      }
      case AttrType::STRING: {
        if (auto str_val = context->GetStringItemAttr(item_key, attr)) {
          return {{uid, did, processor_name, kDefaultItemKey, kDefaultItemIntAttr, *str_val}};
        }
        break;
      }
      case AttrType::INT_LIST: {
        if (auto list_val = context->GetIntListItemAttr(item_key, attr)) {
          if (list_val->size() == 1) {
            return {{uid, did, processor_name, kDefaultItemKey, list_val->at(0), kDefaultItemStringAttr}};
          }
          CL_LOG(ERROR) << "item_cache_key " << attr << " size error: " << list_val->size();
        }
        break;
      }
      case AttrType::STRING_LIST: {
        if (auto list_val = context->GetStringListItemAttr(item_key, attr)) {
          if (list_val->size() == 1) {
            return {{uid, did, processor_name, kDefaultItemKey, kDefaultItemIntAttr, list_val->at(0)}};
          }
          CL_LOG(ERROR) << "item_cache_key " << attr << " size error: " << list_val->size();
        }
        break;
      }
      case AttrType::UNKNOWN:
        CL_LOG(INFO) << "item_cache_key unknown type: " << cache_key_name;
        break;
      default:
        CL_LOG(ERROR) << "item_cache_key type error: " << cache_key_name;
        break;
    }
    return absl::nullopt;
  }

  absl::optional<CacheKeyType> GetItemCacheKeyValue(ReadableRecoContextInterface *context,
                                                    uint64_t item_key) const {
    auto &dict = output_cache_config_->item_cache_key_map;
    auto it = dict.find(item_key);
    if (it != dict.end()) {
      return it->second;
    }
    auto cache_key = GetItemCacheKeyValueImpl(context, item_key);
    dict.emplace(item_key, cache_key);  // save cache_key even it is a nullopt
    return cache_key;
  }

  CacheStatus CacheRead(MutableRecoContextInterface *context, RecoResultConstIter first,
                        RecoResultConstIter last, std::vector<CommonRecoResult> *target_items) override {
    const auto &processor_name = GetName();
    // common cache
    if (!GetCommonCacheKeyAttrName().empty()) {
      auto cache_key = GetCommonCacheKeyValue(context);
      const LocalAttrCache::ValueType *res = nullptr;
      if (!cache_key.empty()) {
        res = LocalAttrCache::GetInstance()->Get(processor_name, cache_key);
        base::perfutil::PerfUtilWrapper::IntervalLogStash(res ? 1000 : 0, kPerfNs, "processor_cache_rate",
                                                          GlobalHolder::GetServiceIdentifier(),
                                                          context->GetRequestType(), GetName());
      }
      if (!res) return CacheStatus::NONE;

      for (const auto &kv : *res) {
        SetCachedCommonAttr(context, kv.first, kv.second);
      }
      return CacheStatus::FULLY;
    } else {  // item cache
      std::vector<CommonRecoResult> miss_items;
      const size_t total_size = std::distance(first, last);
      for (auto it = first; it != last; ++it) {
        auto cache_key = GetItemCacheKeyValue(context, it->item_key);
        const LocalAttrCache::ValueType *res = nullptr;
        if (cache_key) {
          res = LocalAttrCache::GetInstance()->Get(*cache_key);
        }
        if (!res) {
          miss_items.emplace_back(*it);
        } else {
          for (const auto &kv : *res) {
            SetCachedItemAttr(context, it, kv.first, kv.second);
          }
        }
      }

      VLOG(10) << "user_id: " << context->GetUserId() << " device_id: " << context->GetDeviceId()
               << " processor: " << GetName() << " item total_size: " << total_size
               << " miss_items: " << miss_items.size();

      if (total_size > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(
            1000 - 1000 * miss_items.size() / total_size, kPerfNs, "processor_cache_rate",
            GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName());
      }
      if (miss_items.empty()) return CacheStatus::FULLY;

      miss_cache_item_keys_.reserve(miss_items.size());
      for (const auto &miss_it : miss_items) {
        miss_cache_item_keys_.emplace_back(miss_it.item_key);
      }

      if (total_size > miss_items.size()) {
        target_items->swap(miss_items);
        return CacheStatus::PARTIALLY;
      }
    }
    return CacheStatus::NONE;
  }

  void CacheWrite(MutableRecoContextInterface *context) const override {
    const auto &processor_name = GetName();
    // common cache
    if (!GetCommonCacheKeyAttrName().empty()) {
      const auto &cache_key = GetCommonCacheKeyValue(context);
      if (cache_key.empty()) return;
      bool has_val = false;
      LocalAttrCache::ValueType cached_val;
      for (const auto &name : *meta_data_.output_common_attrs) {
        auto *attr = context->GetCommonAttr(name);
        if (!attr) continue;

        if (attr->value_type == AttrType::INT) {
          if (auto int_val = context->GetIntCommonAttr(attr)) {
            cached_val.try_emplace(name.data(), *int_val);
            has_val = true;
          }
        } else if (attr->value_type == AttrType::FLOAT) {
          if (auto double_val = context->GetDoubleCommonAttr(attr)) {
            cached_val.try_emplace(name.data(), *double_val);
            has_val = true;
          }
        } else if (attr->value_type == AttrType::STRING) {
          if (auto string_val = context->GetStringCommonAttr(attr)) {
            cached_val.try_emplace(name.data(), static_cast<std::string>(*string_val));
            has_val = true;
          }
        } else if (attr->value_type == AttrType::INT_LIST) {
          if (auto int_list_val = context->GetIntListCommonAttr(attr)) {
            std::vector<int64_t> val;
            val.reserve(int_list_val->size());
            std::copy(int_list_val->begin(), int_list_val->end(), std::back_inserter(val));
            cached_val.try_emplace(name.data(), std::move(val));
            has_val = true;
          }
        } else if (attr->value_type == AttrType::FLOAT_LIST) {
          if (auto double_list_val = context->GetDoubleListCommonAttr(attr)) {
            std::vector<double> val;
            val.reserve(double_list_val->size());
            std::copy(double_list_val->begin(), double_list_val->end(), std::back_inserter(val));
            cached_val.try_emplace(name.data(), std::move(val));
            has_val = true;
          }
        } else if (attr->value_type == AttrType::STRING_LIST) {
          if (auto string_list_val = context->GetStringListCommonAttr(attr)) {
            std::vector<std::string> val;
            val.reserve(string_list_val->size());
            for (auto string_view : *string_list_val) {
              val.emplace_back(static_cast<std::string>(string_view));
            }
            cached_val.try_emplace(name.data(), std::move(val));
            has_val = true;
          }
        } else if (attr->value_type == AttrType::EXTRA) {
          if (auto extra_val = context->GetPtrCommonAttr<std::vector<std::vector<int64_t>>>(name)) {
            cached_val.try_emplace(name.data(), std::move(*extra_val));
            has_val = true;
          } else if (auto extra_val = context->GetPtrCommonAttr<std::vector<std::vector<double>>>(name)) {
            cached_val.try_emplace(name.data(), std::move(*extra_val));
            has_val = true;
          } else if (auto extra_val = context->GetPtrCommonAttr<google::protobuf::Message>(name)) {
            const auto &full_name = interop::GetProtoMessageFullName(extra_val);
            std::shared_ptr<google::protobuf::Message> ptr(interop::CreateProtoMessageByFullName(full_name));
            if (!ptr) {
              base::perfutil::PerfUtilWrapper::CountLogStash(
                  1, kPerfNs, "error", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                  name.data(), "unsupported_cache_proto_type:" + full_name);
              return;
            }
            ptr->CopyFrom(*extra_val);
            cached_val.try_emplace(name.data(), ptr);
            has_val = true;
          } else {
            base::perfutil::PerfUtilWrapper::CountLogStash(
                1, kPerfNs, "error", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                name.data(), "unsupported_cache_type");
            return;
          }
        }
      }
      if (output_cache_config_->allow_write_null_value) {
        // 默认情况下，无结果 cache 也被写入，避免缓存穿透问题
        LocalAttrCache::GetInstance()->Set(processor_name, cache_key, std::move(cached_val));
      } else {
        if (has_val) {
          LocalAttrCache::GetInstance()->Set(processor_name, cache_key, std::move(cached_val));
        }
      }
    } else {  // item cache
      if (miss_cache_item_keys_.empty()) return;
      for (uint64_t item_key : miss_cache_item_keys_) {
        auto cache_key = GetItemCacheKeyValue(context, item_key);
        if (!cache_key) continue;

        bool has_val = false;
        LocalAttrCache::ValueType cached_val;
        for (const auto &name : *meta_data_.output_item_attrs) {
          auto attr = context->GetItemAttr(name);
          if (!attr) continue;

          if (attr->value_type == AttrType::INT) {
            if (auto int_val = context->GetIntItemAttr(item_key, name)) {
              cached_val.try_emplace(name.data(), *int_val);
              has_val = true;
            }
          } else if (attr->value_type == AttrType::FLOAT) {
            if (auto double_val = context->GetDoubleItemAttr(item_key, name)) {
              cached_val.try_emplace(name.data(), *double_val);
              has_val = true;
            }
          } else if (attr->value_type == AttrType::STRING) {
            if (auto string_val = context->GetStringItemAttr(item_key, name)) {
              cached_val.try_emplace(name.data(), static_cast<std::string>(*string_val));
              has_val = true;
            }
          } else if (attr->value_type == AttrType::INT_LIST) {
            if (auto int_list_val = context->GetIntListItemAttr(item_key, name)) {
              std::vector<int64_t> val;
              val.reserve(int_list_val->size());
              std::copy(int_list_val->begin(), int_list_val->end(), std::back_inserter(val));
              cached_val.try_emplace(name.data(), std::move(val));
              has_val = true;
            }
          } else if (attr->value_type == AttrType::FLOAT_LIST) {
            if (auto double_list_val = context->GetDoubleListItemAttr(item_key, name)) {
              std::vector<double> val;
              val.reserve(double_list_val->size());
              std::copy(double_list_val->begin(), double_list_val->end(), std::back_inserter(val));
              cached_val.try_emplace(name.data(), std::move(val));
              has_val = true;
            }
          } else if (attr->value_type == AttrType::STRING_LIST) {
            if (auto string_list_val = context->GetStringListItemAttr(item_key, name)) {
              std::vector<std::string> val;
              val.reserve(string_list_val->size());
              for (auto string_view : *string_list_val) {
                val.emplace_back(static_cast<std::string>(string_view));
              }
              cached_val.try_emplace(name.data(), std::move(val));
              has_val = true;
            }
          } else if (attr->value_type == AttrType::EXTRA) {
            if (auto extra_val = context->GetPtrItemAttr<std::vector<std::vector<int64_t>>>(item_key, name)) {
              auto val = *extra_val;
              cached_val.try_emplace(name.data(), std::move(val));
              has_val = true;
            } else if (auto extra_val =
                           context->GetPtrItemAttr<std::vector<std::vector<double>>>(item_key, name)) {
              auto val = *extra_val;
              cached_val.try_emplace(name.data(), std::move(val));
              has_val = true;
            } else if (auto extra_val = context->GetPtrItemAttr<google::protobuf::Message>(item_key, name)) {
              const auto &full_name = interop::GetProtoMessageFullName(extra_val);
              std::shared_ptr<google::protobuf::Message> ptr(
                  interop::CreateProtoMessageByFullName(full_name));
              if (!ptr) {
                base::perfutil::PerfUtilWrapper::CountLogStash(
                    1, kPerfNs, "error", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                    name.data(), "unsupported_cache_proto_type:" + full_name);
                return;
              }
              ptr->CopyFrom(*extra_val);
              cached_val.try_emplace(name.data(), ptr);
              has_val = true;
            } else {
              base::perfutil::PerfUtilWrapper::CountLogStash(
                  1, kPerfNs, "error", GlobalHolder::GetServiceIdentifier(), context->GetRequestType(),
                  name.data(), "unsupported_cache_type");
              return;
            }
          }
        }

        if (output_cache_config_->allow_write_null_value) {
          LocalAttrCache::GetInstance()->Set(*cache_key, std::move(cached_val));
        } else {
          if (has_val) {
            LocalAttrCache::GetInstance()->Set(*cache_key, std::move(cached_val));
          }
        }
      }
    }
  }

  void SetCachedItemAttr(MutableRecoContextInterface *context, RecoResultConstIter it,
                         absl::string_view attr_name, const LocalAttrCache::Field &res) const {
    if (absl::holds_alternative<int64_t>(res)) {
      context->SetIntItemAttr(it->item_key, attr_name, absl::get<int64_t>(res));
    } else if (absl::holds_alternative<double>(res)) {
      context->SetDoubleItemAttr(it->item_key, attr_name, absl::get<double>(res));
    } else if (absl::holds_alternative<std::string>(res)) {
      context->SetStringItemAttr(it->item_key, attr_name, absl::get<std::string>(res));
    } else if (absl::holds_alternative<std::vector<int64_t>>(res)) {
      auto val = absl::get<std::vector<int64_t>>(res);
      context->SetIntListItemAttr(it->item_key, attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<double>>(res)) {
      auto val = absl::get<std::vector<double>>(res);
      context->SetDoubleListItemAttr(it->item_key, attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::string>>(res)) {
      auto val = absl::get<std::vector<std::string>>(res);
      context->SetStringListItemAttr(it->item_key, attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::vector<int64_t>>>(res)) {
      auto val = std::make_shared<std::vector<std::vector<int64_t>>>(
          absl::get<std::vector<std::vector<int64_t>>>(res));
      context->SetPtrItemAttr(it->item_key, attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::vector<double>>>(res)) {
      auto val = std::make_shared<std::vector<std::vector<double>>>(
          absl::get<std::vector<std::vector<double>>>(res));
      context->SetPtrItemAttr(it->item_key, attr_name, std::move(val));
    } else if (absl::holds_alternative<std::shared_ptr<google::protobuf::Message>>(res)) {
      auto val = absl::get<std::shared_ptr<google::protobuf::Message>>(res);
      context->SetPtrItemAttr(it->item_key, attr_name, val);
    }
  }

  void SetCachedCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                           const LocalAttrCache::Field &res) const {
    if (absl::holds_alternative<int64_t>(res)) {
      context->SetIntCommonAttr(attr_name, absl::get<int64_t>(res));
    } else if (absl::holds_alternative<double>(res)) {
      context->SetDoubleCommonAttr(attr_name, absl::get<double>(res));
    } else if (absl::holds_alternative<std::string>(res)) {
      context->SetStringCommonAttr(attr_name, absl::get<std::string>(res));
    } else if (absl::holds_alternative<std::vector<int64_t>>(res)) {
      auto val = absl::get<std::vector<int64_t>>(res);
      context->SetIntListCommonAttr(attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<double>>(res)) {
      auto val = absl::get<std::vector<double>>(res);
      context->SetDoubleListCommonAttr(attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::string>>(res)) {
      auto val = absl::get<std::vector<std::string>>(res);
      context->SetStringListCommonAttr(attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::vector<int64_t>>>(res)) {
      auto val = std::make_shared<std::vector<std::vector<int64_t>>>(
          absl::get<std::vector<std::vector<int64_t>>>(res));
      context->SetPtrCommonAttr(attr_name, std::move(val));
    } else if (absl::holds_alternative<std::vector<std::vector<double>>>(res)) {
      auto val = std::make_shared<std::vector<std::vector<double>>>(
          absl::get<std::vector<std::vector<double>>>(res));
      context->SetPtrCommonAttr(attr_name, std::move(val));
    } else if (absl::holds_alternative<std::shared_ptr<google::protobuf::Message>>(res)) {
      auto val = absl::get<std::shared_ptr<google::protobuf::Message>>(res);
      context->SetPtrCommonAttr(attr_name, val);
    }
  }

  void UpdateCacheAfterRun(ReadableRecoContextInterface *context) const override {
    if (IsCacheEnabled(context) && need_update_cache_) {
      auto start_ts = base::GetTimestamp();
      CacheWrite(static_cast<MutableRecoContextInterface *>(context));
      int64 duration = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(duration, kPerfNs, "processor_output_cache.write",
                       GlobalHolder::GetServiceIdentifier(), context->GetRequestType(), GetName(),
                       type_name(), "", GlobalHolder::GetJsonConfigVersion());
    }
  }

  void OnEnter(ReadableRecoContextInterface *context) override {
    VLOG(100) << "Enter processor: " << GetName();
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) override {}

  void OnExit(ReadableRecoContextInterface *context) override {}

  ProcessorType GetType() const final {
    return ProcessorType::ENRICHER;
  }

 protected:
  CommonRecoBaseEnricher() {}

  // 从指定的 CommonAttr 中抽取 item_list 进行处理
  virtual std::string GetItemListCommonAttr() const {
    return config()->GetString("item_list_from_attr");
  }
  // range start 为负数时表示倒数第几个
  virtual int GetRangeStart(ReadableRecoContextInterface *context) const {
    return GetIntProcessorParameter(context, "range_start", 0);
  }
  // range end 为 0 表示取末尾，负数表示倒数第几个
  virtual int GetRangeEnd(ReadableRecoContextInterface *context) const {
    return GetIntProcessorParameter(context, "range_end", 0);
  }
  // 对结果集进行分块处理时的分块大小，0 为不分块
  virtual int GetPartitionSize(ReadableRecoContextInterface *context) const {
    return GetIntProcessorParameter(context, "partition_size", 0);
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseEnricher);
};

}  // namespace platform
}  // namespace ks
