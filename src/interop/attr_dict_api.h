#pragma once

#include <memory>
#include <string>
#include <vector>

#include "dragon/src/core/common_reco_context_interface.h"
#include "learning/kuiba/sample_reader/attr_dict_api.h"

namespace ks {
namespace platform {
namespace interop {
namespace attr_dict_api {

class CommonRecoContextCommonAttrWrapper : public kuiba::AttrDictApi {
 public:
  explicit CommonRecoContextCommonAttrWrapper(ReadableRecoContextInterface *context) : context_(context) {}

  const kuiba::AnyAttr *Lookup(const std::string &name) const override;

  std::string ToString() const override {
    return "CommonRecoContextCommonAttrWrapper";
  }

 private:
  ReadableRecoContextInterface *const context_;
  mutable std::vector<std::unique_ptr<const kuiba::AnyAttr>> attrs_;
};

class CommonRecoContextItemAttrWrapper : public kuiba::AttrDict<PERSON>pi {
 public:
  CommonRecoContextItemAttrWrapper(ReadableRecoContextInterface *context, uint64 item_key)
      : context_(context), item_key_(item_key) {}

  const kuiba::AnyAttr *Lookup(const std::string &name) const override;

  std::string ToString() const override {
    return "CommonRecoContextItemAttrWrapper of " + std::to_string(item_key_);
  }

 private:
  ReadableRecoContextInterface *const context_;
  const uint64 item_key_;
  mutable std::vector<std::unique_ptr<const kuiba::AnyAttr>> attrs_;
};

class CommonRecoContextWrapper : public kuiba::AttrDictApi {
 public:
  CommonRecoContextWrapper(ReadableRecoContextInterface *context, uint64 item_key,
                           bool use_common_attr_first = false)
      : item_key_(item_key)
      , common_attr_visitor_(context)
      , item_attr_visitor_(context, item_key)
      , use_common_attr_first_(use_common_attr_first) {}

  const kuiba::AnyAttr *Lookup(const std::string &name) const override;

  std::string ToString() const override {
    return "CommonRecoContextWrapper of " + std::to_string(item_key_);
  }

 private:
  const uint64 item_key_;
  const CommonRecoContextCommonAttrWrapper common_attr_visitor_;
  const CommonRecoContextItemAttrWrapper item_attr_visitor_;
  const bool use_common_attr_first_;
};
}  // namespace attr_dict_api

static inline std::shared_ptr<kuiba::AttrDictApi> WrapCommonAttrAsAttrDictApi(
    ReadableRecoContextInterface *context) {
  return std::make_shared<attr_dict_api::CommonRecoContextCommonAttrWrapper>(context);
}

static inline std::shared_ptr<kuiba::AttrDictApi> WrapItemAttrAsAttrDictApi(
    ReadableRecoContextInterface *context, uint64 item_key, bool include_common_attr = true,
    bool common_attr_first = false) {
  if (include_common_attr) {
    return std::make_shared<attr_dict_api::CommonRecoContextWrapper>(context, item_key, common_attr_first);
  } else {
    return std::make_shared<attr_dict_api::CommonRecoContextItemAttrWrapper>(context, item_key);
  }
}
}  // namespace interop
}  // namespace platform
}  // namespace ks
