#include "dragon/src/interop/general_sample_attr.h"

#include <gtest/gtest.h>
#include <absl/types/any.h>
#include <absl/types/optional.h>
#include <absl/strings/string_view.h>

#include "learning/kuiba/proto/common_sample_log.pb.h"
#include "ks/reco_proto/sample_log/reco_label_set.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/ad/ad_base.pb.h"

namespace ks {
namespace platform {
namespace interop {

TEST(ProtobufDescriptor, SameDescriptor) {
  kuiba::SampleAttr a;
  kuiba::SampleAttr b;
  EXPECT_EQ(a.GetDescriptor(), b.GetDescriptor());
}

TEST(SampleAttrDescriptor, SameDescriptor) {
  EXPECT_EQ(SampleAttrLikeDescriptor::GetByDescriptor(kuiba::SampleAttr::descriptor()),
            SampleAttrLikeDescriptor::GetByDescriptor(kuiba::SampleAttr::descriptor()));
}

TEST(GetSampleAttrDescriptor, KuibaSampleAttr) {
  const google::protobuf::Descriptor *pb_descriptor = kuiba::SampleAttr::descriptor();

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(pb_descriptor);

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_EQ(sample_attr_descriptor->descriptor_, pb_descriptor);
  EXPECT_EQ(sample_attr_descriptor->name_value_field_, nullptr);
  EXPECT_EQ(sample_attr_descriptor->name_field_, pb_descriptor->FindFieldByName("name"));
  EXPECT_EQ(sample_attr_descriptor->type_field_, pb_descriptor->FindFieldByName("type"));
  EXPECT_EQ(sample_attr_descriptor->bool_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->bool_enum_value_.has_value());
  EXPECT_EQ(sample_attr_descriptor->int_value_field_, pb_descriptor->FindFieldByName("int_value"));
  EXPECT_TRUE(sample_attr_descriptor->int_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->int_enum_value_, 1);
  EXPECT_EQ(sample_attr_descriptor->float_value_field_, pb_descriptor->FindFieldByName("float_value"));
  EXPECT_TRUE(sample_attr_descriptor->float_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->float_enum_value_, 2);
  EXPECT_EQ(sample_attr_descriptor->string_value_field_, pb_descriptor->FindFieldByName("string_value"));
  EXPECT_TRUE(sample_attr_descriptor->string_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->string_enum_value_, 3);
  EXPECT_EQ(sample_attr_descriptor->int_list_value_field_, pb_descriptor->FindFieldByName("int_list_value"));
  EXPECT_TRUE(sample_attr_descriptor->int_list_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->int_list_enum_value_, 4);
  EXPECT_EQ(sample_attr_descriptor->float_list_value_field_,
            pb_descriptor->FindFieldByName("float_list_value"));
  EXPECT_TRUE(sample_attr_descriptor->float_list_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->float_list_enum_value_, 5);
  EXPECT_EQ(sample_attr_descriptor->string_list_value_field_,
            pb_descriptor->FindFieldByName("string_list_value"));
  EXPECT_TRUE(sample_attr_descriptor->string_list_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->string_list_enum_value_, 6);
}

TEST(GetSampleAttrDescriptor, MixLogLabelAttr) {
  const google::protobuf::Descriptor *pb_descriptor = ks::reco::arch::log::LabelAttr::descriptor();

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(pb_descriptor);

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_EQ(sample_attr_descriptor->descriptor_, pb_descriptor);
  EXPECT_EQ(sample_attr_descriptor->name_value_field_, pb_descriptor->FindFieldByName("name_value"));
  EXPECT_EQ(sample_attr_descriptor->name_field_, nullptr);
  EXPECT_EQ(sample_attr_descriptor->type_field_, pb_descriptor->FindFieldByName("type"));
  EXPECT_EQ(sample_attr_descriptor->bool_value_field_, pb_descriptor->FindFieldByName("bool_value"));
  EXPECT_TRUE(sample_attr_descriptor->bool_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->bool_enum_value_, 1);
  EXPECT_EQ(sample_attr_descriptor->int_value_field_, pb_descriptor->FindFieldByName("int_value"));
  EXPECT_TRUE(sample_attr_descriptor->int_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->int_enum_value_, 2);
  EXPECT_EQ(sample_attr_descriptor->float_value_field_, pb_descriptor->FindFieldByName("float_value"));
  EXPECT_TRUE(sample_attr_descriptor->float_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->float_enum_value_, 3);
  EXPECT_EQ(sample_attr_descriptor->string_value_field_, pb_descriptor->FindFieldByName("string_value"));
  EXPECT_TRUE(sample_attr_descriptor->string_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->string_enum_value_, 4);
  EXPECT_EQ(sample_attr_descriptor->int_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->int_list_enum_value_.has_value());
  EXPECT_EQ(sample_attr_descriptor->float_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->float_list_enum_value_.has_value());
  EXPECT_EQ(sample_attr_descriptor->string_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->string_list_enum_value_.has_value());
}

TEST(GetSampleAttrDescriptor, AdLabelAttr) {
  const google::protobuf::Descriptor *pb_descriptor = mix::kuaishou::ad::LabelAttr::descriptor();

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(pb_descriptor);

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_EQ(sample_attr_descriptor->descriptor_, pb_descriptor);
  EXPECT_EQ(sample_attr_descriptor->name_value_field_, pb_descriptor->FindFieldByName("name_value"));
  EXPECT_EQ(sample_attr_descriptor->name_field_, nullptr);
  EXPECT_EQ(sample_attr_descriptor->type_field_, pb_descriptor->FindFieldByName("type"));
  EXPECT_EQ(sample_attr_descriptor->bool_value_field_, pb_descriptor->FindFieldByName("bool_value"));
  EXPECT_TRUE(sample_attr_descriptor->bool_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->bool_enum_value_, 3);
  EXPECT_EQ(sample_attr_descriptor->int_value_field_, pb_descriptor->FindFieldByName("int_value"));
  EXPECT_TRUE(sample_attr_descriptor->int_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->int_enum_value_, 1);
  EXPECT_EQ(sample_attr_descriptor->float_value_field_, pb_descriptor->FindFieldByName("float_value"));
  EXPECT_TRUE(sample_attr_descriptor->float_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->float_enum_value_, 2);
  EXPECT_EQ(sample_attr_descriptor->string_value_field_, pb_descriptor->FindFieldByName("string_value"));
  EXPECT_TRUE(sample_attr_descriptor->string_enum_value_.has_value());
  EXPECT_EQ(*sample_attr_descriptor->string_enum_value_, 4);
  EXPECT_EQ(sample_attr_descriptor->int_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->int_list_enum_value_.has_value());
  EXPECT_EQ(sample_attr_descriptor->float_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->float_list_enum_value_.has_value());
  EXPECT_EQ(sample_attr_descriptor->string_list_value_field_, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->string_list_enum_value_.has_value());
}

TEST(MatchSampleAttr, KuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_name("test_attr");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "test_attr", -1));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "test_attr", 2));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", -1));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "", -1));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", 2));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "", 2));
}

TEST(MatchSampleAttr, MixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_name_value(2);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "test_attr", -1));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "test_attr", 2));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", -1));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "", -1));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", 2));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "", 2));
}

TEST(MatchSampleAttr, AdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_name_value(2);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "test_attr", -1));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "test_attr", 2));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", -1));
  EXPECT_FALSE(sample_attr_descriptor->Match(sample_attr, "", -1));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "invalid_attr", 2));
  EXPECT_TRUE(sample_attr_descriptor->Match(sample_attr, "", 2));
}

class AnyHandle {
 public:
  AnyHandle(absl::any &a, std::string &attr_name) : a_(a), attr_name_(attr_name) {}

  inline void SetIntAttr(absl::string_view attr_name, int64 value, bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = value;
  }

  inline void SetDoubleAttr(absl::string_view attr_name, double value, bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = value;
  }

  inline void SetStringAttr(absl::string_view attr_name, std::string value, bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = value;
  }

  inline void SetIntListAttr(absl::string_view attr_name, std::vector<int64> &&value,
                             bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = std::move(value);
  }

  inline void SetDoubleListAttr(absl::string_view attr_name, std::vector<double> &&value,
                                bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = std::move(value);
  }

  inline void SetStringListAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                bool no_overwrite = false) {
    attr_name_ = std::string(attr_name);
    a_ = std::move(value);
  }

  inline void AppendIntListAttr(absl::string_view attr_name, int64 value) {
    attr_name_ = std::string(attr_name);
    if (a_.has_value()) {
      absl::any_cast<std::vector<int64> &>(a_).push_back(value);
    } else {
      a_ = std::vector<int64>{value};
    }
  }

  inline void AppendDoubleListAttr(absl::string_view attr_name, double value) {
    attr_name_ = std::string(attr_name);
    if (a_.has_value()) {
      absl::any_cast<std::vector<double> &>(a_).push_back(value);
    } else {
      a_ = std::vector<double>{value};
    }
  }

  inline void AppendStringListAttr(absl::string_view attr_name, std::string value) {
    attr_name_ = std::string(attr_name);
    if (a_.has_value()) {
      absl::any_cast<std::vector<std::string> &>(a_).push_back(value);
    } else {
      a_ = std::vector<std::string>{value};
    }
  }

 private:
  absl::any &a_;
  std::string &attr_name_;
};

TEST(SaveSampleAttr, IntKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::INT_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 1);
}

TEST(SaveSampleAttr, FloatKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
  sample_attr.set_float_value(0.5);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_NEAR(absl::any_cast<double>(value), 0.5, 0.001);
}

TEST(SaveSampleAttr, StringKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::STRING_ATTR);
  sample_attr.set_string_value("value");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::string &>(value), "value");
}

TEST(SaveSampleAttr, IntListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::INT_LIST_ATTR);
  sample_attr.add_int_list_value(1);
  sample_attr.add_int_list_value(2);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 2}));
}

TEST(SaveSampleAttr, FloatListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
  sample_attr.add_float_list_value(0.1);
  sample_attr.add_float_list_value(0.2);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  std::vector<double> &double_list_value = absl::any_cast<std::vector<double> &>(value);
  ASSERT_EQ(double_list_value.size(), 2);
  EXPECT_NEAR(double_list_value[0], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[1], 0.2, 0.001);
}

TEST(SaveSampleAttr, StringListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::STRING_LIST_ATTR);
  sample_attr.add_string_list_value("string");
  sample_attr.add_string_list_value("value");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<std::string> &>(value), std::vector<std::string>({"string", "value"}));
}

TEST(SaveSampleAttr, TrueMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::BOOL_ATTR);
  sample_attr.set_bool_value(true);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 1);
}

TEST(SaveSampleAttr, FalseMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::BOOL_ATTR);
  sample_attr.set_bool_value(false);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 0);
}

TEST(SaveSampleAttr, IntMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::INT_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 1);
}

TEST(SaveSampleAttr, FloatMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::FLOAT_ATTR);
  sample_attr.set_float_value(0.5);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_NEAR(absl::any_cast<double>(value), 0.5, 0.001);
}

TEST(SaveSampleAttr, StringMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::STRING_ATTR);
  sample_attr.set_string_value("value");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::string &>(value), "value");
}

TEST(SaveSampleAttr, TrueAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::BOOL_ATTR);
  sample_attr.set_bool_value(true);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 1);
}

TEST(SaveSampleAttr, FalseAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::BOOL_ATTR);
  sample_attr.set_bool_value(false);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 0);
}

TEST(SaveSampleAttr, IntAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::UINT64_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<int64>(value), 1);
}

TEST(SaveSampleAttr, FloatAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::FLOAT_ATTR);
  sample_attr.set_float_value(0.5);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_NEAR(absl::any_cast<double>(value), 0.5, 0.001);
}

TEST(SaveSampleAttr, StringAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::STRING_ATTR);
  sample_attr.set_string_value("value");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                       "attr_name", sample_attr, false),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::string &>(value), "value");
}

TEST(AppendSampleAttr, IntKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::INT_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_int_value(2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 2}));
}

TEST(AppendSampleAttr, FloatKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::FLOAT_ATTR);
  sample_attr.set_float_value(0.1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_float_value(0.2);

  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  std::vector<double> &double_list_value = absl::any_cast<std::vector<double> &>(value);
  ASSERT_EQ(double_list_value.size(), 2);
  EXPECT_NEAR(double_list_value[0], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[1], 0.2, 0.001);
}

TEST(AppendSampleAttr, StringKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::STRING_ATTR);
  sample_attr.set_string_value("string");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_string_value("value");
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<std::string> &>(value), std::vector<std::string>({"string", "value"}));
}

TEST(AppendSampleAttr, IntListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::INT_LIST_ATTR);
  sample_attr.add_int_list_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.add_int_list_value(2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 1, 2}));
}

TEST(AppendSampleAttr, FloatListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
  sample_attr.add_float_list_value(0.1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.add_float_list_value(0.2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  std::vector<double> &double_list_value = absl::any_cast<std::vector<double> &>(value);
  ASSERT_EQ(double_list_value.size(), 3);
  EXPECT_NEAR(double_list_value[0], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[1], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[2], 0.2, 0.001);
}

TEST(AppendSampleAttr, StringListKuibaSampleAttr) {
  kuiba::SampleAttr sample_attr;
  sample_attr.set_type(kuiba::CommonSampleEnum::STRING_LIST_ATTR);
  sample_attr.add_string_list_value("string");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.add_string_list_value("value");
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            2);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<std::string> &>(value),
            std::vector<std::string>({"string", "string", "value"}));
}

TEST(AppendSampleAttr, BoolMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::BOOL_ATTR);
  sample_attr.set_bool_value(true);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_bool_value(false);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 0}));
}

TEST(AppendSampleAttr, IntMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::INT_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_int_value(2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 2}));
}

TEST(AppendSampleAttr, FloatMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::FLOAT_ATTR);
  sample_attr.set_float_value(0.1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_float_value(0.2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  std::vector<double> &double_list_value = absl::any_cast<std::vector<double> &>(value);
  ASSERT_EQ(double_list_value.size(), 2);
  EXPECT_NEAR(double_list_value[0], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[1], 0.2, 0.001);
}

TEST(AppendSampleAttr, StringMixLogLabelAttr) {
  ks::reco::arch::log::LabelAttr sample_attr;
  sample_attr.set_type(ks::reco::arch::log::LabelAttr::STRING_ATTR);
  sample_attr.set_string_value("string");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_string_value("value");
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);
  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<std::string> &>(value), std::vector<std::string>({"string", "value"}));
}

TEST(AppendSampleAttr, BoolAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::BOOL_ATTR);
  sample_attr.set_bool_value(true);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_bool_value(false);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 0}));
}

TEST(AppendSampleAttr, IntAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::UINT64_ATTR);
  sample_attr.set_int_value(1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_int_value(2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<int64> &>(value), std::vector<int64>({1, 2}));
}

TEST(AppendSampleAttr, FloatAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::FLOAT_ATTR);
  sample_attr.set_float_value(0.1);

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_float_value(0.2);
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  EXPECT_EQ(attr_name, "attr_name");
  std::vector<double> &double_list_value = absl::any_cast<std::vector<double> &>(value);
  ASSERT_EQ(double_list_value.size(), 2);
  EXPECT_NEAR(double_list_value[0], 0.1, 0.001);
  EXPECT_NEAR(double_list_value[1], 0.2, 0.001);
}

TEST(AppendSampleAttr, StringAdLabelAttr) {
  mix::kuaishou::ad::LabelAttr sample_attr;
  sample_attr.set_type(mix::kuaishou::ad::LabelCommonTypeEnum::STRING_ATTR);
  sample_attr.set_string_value("string");

  const SampleAttrLikeDescriptor *sample_attr_descriptor =
      SampleAttrLikeDescriptor::GetByDescriptor(sample_attr.GetDescriptor());

  ASSERT_NE(sample_attr_descriptor, nullptr);

  absl::any value;
  std::string attr_name;
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);

  sample_attr.set_string_value("value");
  EXPECT_EQ(sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(AnyHandle(value, attr_name),
                                                                         "attr_name", sample_attr),
            1);
  EXPECT_EQ(attr_name, "attr_name");
  EXPECT_EQ(absl::any_cast<std::vector<std::string> &>(value), std::vector<std::string>({"string", "value"}));
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
