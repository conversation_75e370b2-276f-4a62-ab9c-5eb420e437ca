#include "dragon/src/interop/kuiba_sample_attr.h"

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {
namespace interop {
namespace {

class SampleAttrLoader : public CommonRecoContextOtherAttrLoader<SampleAttrLoader, ::kuiba::SampleAttr> {
 public:
  using CommonRecoContextOtherAttrLoader<SampleAttrLoader,
                                         ::kuiba::SampleAttr>::CommonRecoContextOtherAttrLoader;
  inline void FromInt(int64 val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::INT_ATTR);
    ptr()->set_int_value(val);
  }

  inline void FromDouble(double val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::FLOAT_ATTR);
    ptr()->set_float_value(val);
  }

  inline void FromString(absl::string_view val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::STRING_ATTR);
    ptr()->set_string_value(val.data(), val.size());
  }

  inline void FromIntList(absl::Span<const int64> val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::INT_LIST_ATTR);
    for (auto v : val) ptr()->add_int_list_value(v);
  }

  inline void FromFloatList(const std::vector<float> &val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
    for (auto v : val) ptr()->add_float_list_value(v);
  }

  inline void FromDoubleList(absl::Span<const double> val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::FLOAT_LIST_ATTR);
    for (auto v : val) ptr()->add_float_list_value(v);
  }

  inline void FromStringList(const std::vector<absl::string_view> &val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::STRING_LIST_ATTR);
    for (const auto &v : val) ptr()->add_string_list_value(v.data(), v.size());
  }

  inline void FromProtobufMessage(const ::google::protobuf::Message *val) {
    ptr()->set_type(::kuiba::CommonSampleEnum::STRING_ATTR);
    val->SerializeToString(ptr()->mutable_string_value());
  }
};

}  // namespace

template <typename SetterHandle>
size_t AppendSampleAttrWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                        const kuiba::SampleAttr &attr) {
  size_t list_size = 1;
  switch (attr.type()) {
    case kuiba::CommonSampleEnum::INT_ATTR: {
      handle.AppendIntListAttr(attr_name, attr.int_value());
      break;
    }
    case kuiba::CommonSampleEnum::FLOAT_ATTR: {
      handle.AppendDoubleListAttr(attr_name, attr.float_value());
      break;
    }
    case kuiba::CommonSampleEnum::STRING_ATTR: {
      handle.AppendStringListAttr(attr_name, attr.string_value());
      break;
    }
    case kuiba::CommonSampleEnum::INT_LIST_ATTR: {
      list_size = attr.int_list_value_size();
      for (auto int_attr : attr.int_list_value()) {
        handle.AppendIntListAttr(attr_name, int_attr);
      }
      break;
    }
    case kuiba::CommonSampleEnum::FLOAT_LIST_ATTR: {
      list_size = attr.float_list_value_size();
      for (auto double_attr : attr.float_list_value()) {
        handle.AppendDoubleListAttr(attr_name, double_attr);
      }
      break;
    }
    case kuiba::CommonSampleEnum::STRING_LIST_ATTR: {
      list_size = attr.string_list_value_size();
      for (auto &string_attr : attr.string_list_value()) {
        handle.AppendStringListAttr(attr_name, string_attr);
      }
      break;
    }
    default:
      VLOG(100) << "Unknown kuiba AttrType: " << kuiba::CommonSampleEnum_AttrType_Name(attr.type())
                << ", attr_name: " << attr_name;
      return 0;
  }
  return list_size;
}

template <typename SetterHandle>
size_t SaveSampleAttrWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                      const kuiba::SampleAttr &attr, bool no_overwrite) {
  size_t list_size = 1;
  switch (attr.type()) {
    case kuiba::CommonSampleEnum::INT_ATTR:
      handle.SetIntAttr(attr_name, attr.int_value(), no_overwrite);
      break;
    case kuiba::CommonSampleEnum::FLOAT_ATTR:
      handle.SetDoubleAttr(attr_name, attr.float_value(), no_overwrite);
      break;
    case kuiba::CommonSampleEnum::STRING_ATTR:
      handle.SetStringAttr(attr_name, attr.string_value(), no_overwrite);
      break;
    case kuiba::CommonSampleEnum::INT_LIST_ATTR: {
      list_size = attr.int_list_value_size();
      std::vector<int64> int_list(attr.int_list_value().begin(), attr.int_list_value().end());
      handle.SetIntListAttr(attr_name, std::move(int_list), no_overwrite);
      break;
    }
    case kuiba::CommonSampleEnum::FLOAT_LIST_ATTR: {
      list_size = attr.float_list_value_size();
      std::vector<double> double_list(attr.float_list_value().begin(), attr.float_list_value().end());
      handle.SetDoubleListAttr(attr_name, std::move(double_list), no_overwrite);
      break;
    }
    case kuiba::CommonSampleEnum::STRING_LIST_ATTR: {
      list_size = attr.string_list_value_size();
      std::vector<std::string> string_list(attr.string_list_value().begin(), attr.string_list_value().end());
      handle.SetStringListAttr(attr_name, std::move(string_list), no_overwrite);
      break;
    }
    default:
      VLOG(100) << "Unknown kuiba AttrType: " << kuiba::CommonSampleEnum_AttrType_Name(attr.type())
                << ", attr_name: " << attr_name;
      return 0;
  }
  return list_size;
}

::kuiba::SampleAttr *BuildSampleAttrFromCommonAttr(
    ReadableRecoContextInterface *context, absl::string_view attr_name,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename,
    bool readonly) {
  if (!context->HasCommonAttr(attr_name) || !attrs) {
    return nullptr;
  }

  ::kuiba::SampleAttr *attr = attrs->Add();

  SampleAttrLoader(attr).LoadFromCommonAttr(context, attr_name);

  if (rename.empty()) {
    attr->set_name(attr_name.data(), attr_name.size());
  } else {
    attr->set_name(rename.data(), rename.size());
  }

  return attr;
}

bool LoadSampleAttrFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                  ::kuiba::SampleAttr *attr, absl::string_view rename, bool readonly) {
  if (!context->HasCommonAttr(attr_name) || !attr) {
    return false;
  }

  SampleAttrLoader(attr).LoadFromCommonAttr(context, attr_name);

  if (rename.empty()) {
    attr->set_name(attr_name.data(), attr_name.size());
  } else {
    attr->set_name(rename.data(), rename.size());
  }
  attr->set_readonly(readonly);

  return true;
}

::kuiba::SampleAttr *BuildSampleAttrFromItemAttr(
    ReadableRecoContextInterface *context, uint64 item_key, absl::string_view attr_name,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename) {
  if (!context->HasItemAttr(item_key, attr_name) || !attrs) {
    return nullptr;
  }

  ::kuiba::SampleAttr *attr = attrs->Add();

  SampleAttrLoader(attr).LoadFromItemAttr(context, item_key, attr_name);

  if (rename.empty()) {
    attr->set_name(attr_name.data(), attr_name.size());
  } else {
    attr->set_name(rename.data(), rename.size());
  }

  return attr;
}

::kuiba::SampleAttr *BuildSampleAttrFromItemAttr(
    const CommonRecoResult &result, ItemAttr *attr_accessor,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename) {
  if (!attr_accessor || !attrs || !result.HasAttr(attr_accessor)) {
    return nullptr;
  }

  ::kuiba::SampleAttr *attr = attrs->Add();

  SampleAttrLoader(attr).LoadFromItemAttr(result, attr_accessor);

  if (rename.empty()) {
    attr->set_name(attr_accessor->name());
  } else {
    attr->set_name(rename.data(), rename.size());
  }

  return attr;
}

bool LoadSampleAttrFromItemAttr(ReadableRecoContextInterface *context, uint64 item_key,
                                absl::string_view attr_name, ::kuiba::SampleAttr *attr,
                                absl::string_view rename) {
  if (!context->HasItemAttr(item_key, attr_name) || !attr) {
    return false;
  }

  SampleAttrLoader(attr).LoadFromItemAttr(context, item_key, attr_name);

  if (rename.empty()) {
    attr->set_name(attr_name.data(), attr_name.size());
  } else {
    attr->set_name(rename.data(), rename.size());
  }

  return true;
}

bool LoadSampleAttrFromItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                ::kuiba::SampleAttr *attr, absl::string_view rename) {
  if (!attr_accessor || !attr || !result.HasAttr(attr_accessor)) {
    return false;
  }

  SampleAttrLoader(attr).LoadFromItemAttr(result, attr_accessor);

  if (rename.empty()) {
    attr->set_name(attr_accessor->name());
  } else {
    attr->set_name(rename.data(), rename.size());
  }

  return true;
}

size_t SaveSampleAttrToCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                                  const kuiba::SampleAttr &attr, bool no_overwrite) {
  return SaveSampleAttrWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name, attr,
                                        no_overwrite);
}

size_t SaveSampleAttrToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                absl::string_view attr_name, const kuiba::SampleAttr &attr,
                                bool no_overwrite) {
  return SaveSampleAttrWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key), attr_name,
                                        attr, no_overwrite);
}

size_t SaveSampleAttrToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                const kuiba::SampleAttr &attr, bool no_overwrite) {
  return SaveSampleAttrWithSetterHandle(CommonRecoContextItemAttrHpSetterHandle(result, attr_accessor),
                                        attr_accessor->name(), attr, no_overwrite);
}

size_t AppendSampleAttrToCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                                    const kuiba::SampleAttr &attr) {
  return AppendSampleAttrWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name, attr);
}

size_t AppendSampleAttrToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                  absl::string_view attr_name, const kuiba::SampleAttr &attr) {
  return AppendSampleAttrWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key), attr_name,
                                          attr);
}

size_t AppendSampleAttrToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                  const kuiba::SampleAttr &attr) {
  return AppendSampleAttrWithSetterHandle(CommonRecoContextItemAttrHpSetterHandle(result, attr_accessor),
                                          attr_accessor->name(), attr);
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
