#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/interop/protobuf.h"
#include "dragon/src/interop/util.h"
#include "dragon/src/module/photo_store_manager.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/attr_kv_photo_store_item.h"
#include "teams/reco-arch/colossusdb/common/field_replace_conf.h"

namespace ks {
namespace platform {

DECLARE_bool(photo_store_fetch_required_attr_only);

bool FetchAttrNameAndTypes(const std::string &kconf_key, std::unordered_map<std::string, int> *attr_types);

std::unordered_set<int32> GetRequestDataSetTags(const MutableRecoContextInterface *context,
                                                const std::string &request_data_set_tags_attr);

std::shared_ptr<colossusdb::FieldReplaceKconfHolder> GetFieldReplacerFromPhotoStoreConfig(
    const std::string &kconf_key);

class FlatIndexAttrMetaDataHolder {
 private:
  ks::reco::protoutil::AttrType ConvertAttrType(const std::string &attr_type) {
    if (attr_type == "int") {
      return ks::reco::protoutil::AttrType::kInt;
    } else if (attr_type == "float") {
      return ks::reco::protoutil::AttrType::kFloat;
    } else if (attr_type == "string") {
      return ks::reco::protoutil::AttrType::kString;
    } else if (attr_type == "int_list") {
      return ks::reco::protoutil::AttrType::kIntList;
    } else if (attr_type == "float_list") {
      return ks::reco::protoutil::AttrType::kFloatList;
    } else if (attr_type == "string_list") {
      return ks::reco::protoutil::AttrType::kStringList;
    } else {
      return ks::reco::protoutil::AttrType::kInvalid;
    }
  }

 public:
  SINGLETON(FlatIndexAttrMetaDataHolder);

  bool RegistProtobufAttr(const std::string &kconf_key, base::Json *item_attrs_config) {
    base::AutoLock auto_lock(lock_);
    bool using_protobuf_attr = false;

    if (!item_attrs_config->IsArray()) {
      LOG(INFO) << kconf_key << "config is not object array";
      return false;
    }

    auto &meta_map = *(attr_meta_data_map_);
    for (auto pr : item_attrs_config->array()) {
      const auto &attr_name = pr->GetString("name");
      const auto &class_name = pr->GetString("class_name");
      if (!class_name.empty() && meta_map.count(attr_name) > 0) {
        auto meta_tmp = meta_map[attr_name];
        meta_tmp.class_name = class_name;
        auto descriptor =
            ::google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(class_name);
        if (descriptor) {
          const google::protobuf::Message *prototype =
              google::protobuf::MessageFactory::generated_factory()->GetPrototype(descriptor);
          if (prototype) {
            meta_tmp.prototype = prototype;
            using_protobuf_attr = true;
          } else {
            LOG(ERROR) << "prototype for " << class_name << " not found";
          }
          LOG(INFO) << "register protobuf attr=" << attr_name << ", class_name=" << class_name;
        } else {
          LOG(ERROR) << "descriptor for " << class_name << " not found";
        }
        meta_map[attr_name] = meta_tmp;
      }
    }
    if (using_protobuf_attr) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(1, kPerfNs, "forward_index.protobuf_attr",
                                                        GlobalHolder::GetServiceIdentifier());
    }
    return true;
  }

  bool RegistAttrMetaData(const std::string &kconf_key, base::Json *item_attrs_config) {
    std::vector<std::string> attrs;
    std::unordered_map<std::string, ks::reco::protoutil::AttrType> attr_map;
    ExtractAttrConfig(item_attrs_config, &attrs, &attr_map);
    ExtractAbtestConfig(kconf_key, &attrs, &attr_map);

    base::AutoLock auto_lock(lock_);

    if (kconf_attr_types_.find(kconf_key) == kconf_attr_types_.end()) {
      kconf_attr_types_.insert({kconf_key, std::make_shared<std::unordered_map<std::string, int>>()});
    }
    auto attr_types = kconf_attr_types_[kconf_key];
    if (!FetchAttrNameAndTypes(kconf_key, attr_types.get())) {
      LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!";
      return false;
    }

    for (const auto &attr_name : attrs) {
      auto iter = attr_types->find(attr_name);
      if (iter == attr_types->end()) {
        LOG(ERROR) << "Can't find attr '" << attr_name << "' type.";
        base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "process_exception",
                                                       GlobalHolder::GetServiceIdentifier(), "",
                                                       "distributed_index_attr_init_failed:" + attr_name);
        continue;
      }

      if (iter->second <= static_cast<int>(ks::reco::protoutil::AttrType::kInvalid) ||
          iter->second > static_cast<int>(ks::reco::protoutil::AttrType::kStringList)) {
        LOG(ERROR) << "AttrType invalid! unknown value:" << iter->second;
        continue;
      }
      attr_map.insert(std::make_pair(attr_name, static_cast<ks::reco::protoutil::AttrType>(iter->second)));
    }

    for (const auto &pr : attr_map) {
      const auto &attr_name = pr.first;
      const auto &attr_type = pr.second;
      auto it = attr_map_.find(attr_name);
      if (it != attr_map_.end() && it->second != attr_type) {
        LOG(ERROR) << "AttrType inconsistent in " << kconf_key << "! Attr name = " << attr_name;
        return false;
      } else {
        attr_map_.insert(pr);
      }
    }

    return true;
  }

  bool RegistAllAttrMetaData() {
    bool init_success = true;
    // 请求远端索引服务，获取 attrs 对应类型。全局处理一次
    std::call_once(attr_meta_data_init_flag, [&]() {
      std::shared_ptr<base::Json> config_holder = GlobalHolder::GetDynamicJsonConfig();
      const base::Json *global_config = config_holder.get();
      if (!global_config) {
        LOG(ERROR) << "Empty global config";
        init_success = false;
        return;
      }

      auto *pipeline_manager_config = global_config->Get("pipeline_manager_config");
      if (!pipeline_manager_config) {
        LOG(ERROR) << "Empty pipeline_manager_config!";
        init_success = false;
        return;
      }

      auto *flat_index_attrs_with_kconf_key = pipeline_manager_config->Get("flat_index_attrs_with_kconf_key");
      if (flat_index_attrs_with_kconf_key && flat_index_attrs_with_kconf_key->IsObject()) {
        // 先生成 attr_meta_data_map_，再注册 RegistProtobufAttr
        for (const auto &pair : flat_index_attrs_with_kconf_key->objects()) {
          auto success = RegistAttrMetaData(pair.first, pair.second);
          if (!success) {
            LOG(ERROR) << "Init for config " << pair.first << " error.";
          }
          init_success &= success;
        }
        attr_meta_data_map_ = ks::reco::protoutil::GenAttrMetaData(attr_map_);
        for (const auto &pair : flat_index_attrs_with_kconf_key->objects()) {
          RegistProtobufAttr(pair.first, pair.second);
        }
      } else {
        LOG(ERROR) << "Empty flat_index_item_attrs nor flat_index_item_attrs_with_key!";
        init_success = false;
        return;
      }
    });
    return init_success;
  }

  std::shared_ptr<folly::F14FastMap<std::string, ks::reco::protoutil::AttrMetaData>> GetAttrMetaData() {
    return attr_meta_data_map_;
  }

  std::shared_ptr<std::unordered_map<std::string, int>> GetAttrTypesByKconfKey(const std::string &kconf_key) {
    base::AutoLock auto_lock(lock_);

    auto it = kconf_attr_types_.find(kconf_key);
    if (it != kconf_attr_types_.end()) {
      return it->second;
    } else {
      kconf_attr_types_.insert({kconf_key, std::make_shared<std::unordered_map<std::string, int>>()});
      if (!FetchAttrNameAndTypes(kconf_key, kconf_attr_types_[kconf_key].get())) {
        LOG(ERROR) << "CommonRecoDistributedIndexItemAttrEnricher init failed!";
        return nullptr;
      }
      return kconf_attr_types_[kconf_key];
    }
  }

 private:
  void ExtractAttrConfig(base::Json *item_attrs_config, std::vector<std::string> *attrs,
                         std::unordered_map<std::string, ks::reco::protoutil::AttrType> *attr_map) {
    // 配置有 3 种格式
    // 1. [ "author" ]
    // 2. [ {"name" : "author", "type": "int", "class_name": "xxx", "as": "xxx"}]
    // 3. { "author": "int"}
    if (item_attrs_config->IsArray()) {
      for (auto pr : item_attrs_config->array()) {
        if (pr->IsString()) {
          std::string val = pr->StringValue();
          if (!val.empty()) {
            attrs->push_back(std::move(val));
          }
        } else if (pr->IsObject()) {
          const auto &attr_name = pr->GetString("name");
          const auto &attr_type = pr->GetString("type");
          if (!attr_name.empty()) {
            const auto &type = ConvertAttrType(attr_type);
            if (attr_type == "auto") {
              attrs->push_back(attr_name);
            } else if (type != ks::reco::protoutil::AttrType::kInvalid) {
              attr_map->insert(std::make_pair(attr_name, type));
            } else {
              LOG(ERROR) << "unrecognized attr type " << attr_type;
            }
          }
        }
      }
    } else {
      // 自定义类型的 attr 直接填入 attr_map，无自定义类型的从远端索引服务获取
      for (auto pr : item_attrs_config->objects()) {
        const auto &attr_name = pr.first;
        const auto &attr_type = pr.second->StringValue();
        const auto &type = ConvertAttrType(attr_type);
        if (attr_type == "auto") {
          attrs->push_back(attr_name);
        } else if (type != ks::reco::protoutil::AttrType::kInvalid) {
          attr_map->insert(std::make_pair(attr_name, type));
        } else {
          LOG(ERROR) << "unrecognized attr type " << attr_type;
        }
      }
    }
  }
  void ExtractAbtestConfig(const std::string &kconf_key, std::vector<std::string> *attrs,
                           std::unordered_map<std::string, ks::reco::protoutil::AttrType> *attr_map) {
    auto field_replacer = GetFieldReplacerFromPhotoStoreConfig(kconf_key);
    if (!field_replacer) {
      return;
    }

    auto func = [attrs, attr_map](const folly::F14FastMap<std::string, std::string> &field_map) -> bool {
      for (auto &pair : field_map) {
        auto it = attr_map->find(pair.second);
        if (it != attr_map->end()) {
          attr_map->insert({pair.second, it->second});
        } else {
          attrs->push_back(pair.second);
        }
      }
      return true;
    };

    field_replacer->UseFieldMap(func);

    return;
  }

 private:
  static base::Lock lock_;
  std::once_flag attr_meta_data_init_flag;
  std::unordered_map<std::string, ks::reco::protoutil::AttrType> attr_map_;
  std::shared_ptr<folly::F14FastMap<std::string, ks::reco::protoutil::AttrMetaData>> attr_meta_data_map_;
  std::unordered_map<std::string, std::shared_ptr<std::unordered_map<std::string, int>>> kconf_attr_types_;
};

}  // namespace platform
}  // namespace ks
