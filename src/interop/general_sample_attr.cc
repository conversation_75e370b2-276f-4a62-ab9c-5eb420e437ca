#include "dragon/src/interop/general_sample_attr.h"

#include <folly/concurrency/ConcurrentHashMap.h>

using google::protobuf::FieldDescriptor;

namespace ks {
namespace platform {
namespace interop {
bool SampleAttrLikeDescriptor::Init(const google::protobuf::Descriptor *descriptor) {
  descriptor_ = descriptor;
  if (!descriptor_) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": null descriptor!";
    return false;
  }

  /* field for name_value or name */
  name_value_field_ = descriptor->FindFieldByName("name_value");
  if (name_value_field_ &&
      (name_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_INT64 || name_value_field_->is_repeated())) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": name_value field is invalid!";
    return false;
  }

  name_field_ = descriptor->FindFieldByName("name");
  if (name_field_ &&
      (name_field_->cpp_type() != FieldDescriptor::CPPTYPE_STRING || name_field_->is_repeated())) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": name field is invalid!";
    return false;
  }

  if (!name_value_field_ && !name_field_) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": name_value field or name field is required!";
    return false;
  }

  /* field for type */
  type_field_ = descriptor->FindFieldByName("type");
  if (!type_field_ || type_field_->cpp_type() != FieldDescriptor::CPPTYPE_ENUM ||
      type_field_->is_repeated()) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": type field is invalid!";
    return false;
  }

  const google::protobuf::EnumDescriptor *type_enum_descriptor = type_field_->enum_type();
  if (!type_enum_descriptor) {
    LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
               << ": type is invalid enum!";
    return false;
  }

  /* field for values */
  const google::protobuf::EnumValueDescriptor *bool_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("BOOL_ATTR");
  if (bool_enum_value_descriptor) {
    bool_enum_value_ = bool_enum_value_descriptor->number();
    bool_value_field_ = descriptor->FindFieldByName("bool_value");
    if (!bool_value_field_ || bool_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_BOOL ||
        bool_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": bool_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *int_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("INT_ATTR")
          ? type_enum_descriptor->FindValueByName("INT_ATTR")
          : type_enum_descriptor->FindValueByName(
                "UINT64_ATTR");  // XXX(huiyiqun): A feature from
                                 // mix.kuaishou.ad.LabelCommonTypeEnum.AttrType.UINT64_ATTR
  if (int_enum_value_descriptor) {
    int_enum_value_ = int_enum_value_descriptor->number();
    int_value_field_ = descriptor->FindFieldByName("int_value");
    if (!int_value_field_ || int_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_INT64 ||
        int_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": int_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *float_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("FLOAT_ATTR");
  if (float_enum_value_descriptor) {
    float_enum_value_ = float_enum_value_descriptor->number();
    float_value_field_ = descriptor->FindFieldByName("float_value");
    if (!float_value_field_ || float_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_FLOAT ||
        float_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": float_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *string_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("STRING_ATTR");
  if (string_enum_value_descriptor) {
    string_enum_value_ = string_enum_value_descriptor->number();
    string_value_field_ = descriptor->FindFieldByName("string_value");
    if (!string_value_field_ || string_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_STRING ||
        string_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": string_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *int_list_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("INT_LIST_ATTR");
  if (int_list_enum_value_descriptor) {
    int_list_enum_value_ = int_list_enum_value_descriptor->number();
    int_list_value_field_ = descriptor->FindFieldByName("int_list_value");
    if (!int_list_value_field_ || int_list_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_INT64 ||
        !int_list_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": int_list_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *float_list_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("FLOAT_LIST_ATTR");
  if (float_list_enum_value_descriptor) {
    float_list_enum_value_ = float_list_enum_value_descriptor->number();
    float_list_value_field_ = descriptor->FindFieldByName("float_list_value");
    if (!float_list_value_field_ || float_list_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_FLOAT ||
        !float_list_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": float_list_value field is invalid!";
      return false;
    }
  }

  const google::protobuf::EnumValueDescriptor *string_list_enum_value_descriptor =
      type_enum_descriptor->FindValueByName("STRING_LIST_ATTR");
  if (string_list_enum_value_descriptor) {
    string_list_enum_value_ = string_list_enum_value_descriptor->number();
    string_list_value_field_ = descriptor->FindFieldByName("string_list_value");
    if (!string_list_value_field_ ||
        string_list_value_field_->cpp_type() != FieldDescriptor::CPPTYPE_STRING ||
        !string_list_value_field_->is_repeated()) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": float_list_value field is invalid!";
      return false;
    }
  }

  if (!bool_value_field_ && !int_value_field_ && !float_value_field_ && !string_value_field_ &&
      !int_list_value_field_ && !float_list_value_field_ && !string_list_value_field_) {
      LOG(ERROR) << "SampleAttrLikeDescriptor Init failed for " << descriptor->full_name()
                 << ": at least one value field is invalid!";
    return false;
  }

  return true;
}

bool SampleAttrLikeDescriptor::Match(const google::protobuf::Message &msg, const std::string &name,
                                     int64 name_value) const {
  auto *reflection = msg.GetReflection();
  auto *descriptor = msg.GetDescriptor();
  if (descriptor != descriptor_) {
    return false;
  }

  if (name_field_ && !name.empty()) {
    std::string scratch;
    return reflection->GetStringReference(msg, name_field_, &scratch) == name;
  } else if (name_value_field_ && name_value >= 0) {
    return reflection->GetInt64(msg, name_value_field_) == name_value;
  }

  return false;
}

const SampleAttrLikeDescriptor *SampleAttrLikeDescriptor::GetByDescriptor(
    const google::protobuf::Descriptor *descriptor) {
  static folly::ConcurrentHashMap<const google::protobuf::Descriptor *, const SampleAttrLikeDescriptor *>
      cache;

  auto it = cache.find(descriptor);
  if (it != cache.end()) {
    return it->second;
  }

  SampleAttrLikeDescriptor *sample_attr_descriptor = new SampleAttrLikeDescriptor;
  if (!sample_attr_descriptor->Init(descriptor)) {
    delete sample_attr_descriptor;
    sample_attr_descriptor = nullptr;
  }

  bool inserted;
  std::tie(it, inserted) = cache.emplace(descriptor, sample_attr_descriptor);

  if (!inserted) {
    delete sample_attr_descriptor;
  }
  return it->second;
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
