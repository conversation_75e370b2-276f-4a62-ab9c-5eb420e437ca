#include "dragon/src/interop/protobuf.h"

#include <algorithm>
#include <unordered_set>
#include <utility>
#include <vector>

#include "dragon/src/interop/util.h"

using google::protobuf::FieldDescriptor;
using google::protobuf::Message;

namespace ks {
namespace platform {
namespace interop {

#define FILL_ITEM_LIST_ATTR_FROM_PB(FUNC, ATTR_TYPE, PB_TYPE)             \
  {                                                                       \
    std::vector<ATTR_TYPE> list;                                          \
    list.reserve(list_size);                                              \
    for (int i = 0; i < list_size; i++) {                                 \
      list.emplace_back(reflection->GetRepeated##PB_TYPE(msg, field, i)); \
    }                                                                     \
    handle.FUNC(attr_name, std::move(list));                              \
    break;                                                                \
  }

#define APPEND_LIST_ATTR_FROM_PB(FUNC, PB_TYPE, DEFAULT_VAL)                     \
  {                                                                              \
    if (field->is_repeated()) {                                                  \
      for (int i = 0; i < list_size; i++) {                                      \
        handle.FUNC(attr_name, reflection->GetRepeated##PB_TYPE(msg, field, i)); \
      }                                                                          \
      for (int i = 0; i < repeat_limit - list_size && repeat_align; i++) {       \
        handle.FUNC(attr_name, DEFAULT_VAL);                                     \
      }                                                                          \
    } else if (!skip_unset_field || reflection->HasField(msg, field)) {          \
      handle.FUNC(attr_name, reflection->Get##PB_TYPE(msg, field));              \
    }                                                                            \
    break;                                                                       \
  }

namespace {
// follow `msg_path` until the last part and append pointers to the leaf messages to `leaf_msgs`.
// if there are repeated messages in the path, `has_repeated_in_path` will be set to true, or left unchanged.
// if there are optional message that not set in the path, `has_empty_field_in_path` will be set to true, or
// left unchanged. from is an inner parameter, NEVER use it.
bool FollowMessagePath(const Message &msg, const std::vector<int> &msg_path, bool *has_repeated_in_path,
                       bool *has_empty_field_in_path, std::vector<const Message *> *leaf_msgs, int from = 0,
                       const std::vector<int> *repeat_limit = nullptr) {
  if (msg_path.size() < 1 || from > msg_path.size() - 1) return false;

  CHECK(has_repeated_in_path);
  CHECK(has_empty_field_in_path);
  CHECK(leaf_msgs);

  auto *reflection = msg.GetReflection();
  auto *descriptor = msg.GetDescriptor();

  if (from == msg_path.size() - 1) {
    // already leaf message
    leaf_msgs->push_back(&msg);
    return true;
  }

  int field_index = msg_path[from];
  auto *msg_field = descriptor->field(field_index);
  if (!msg_field) {
    CL_LOG(ERROR) << "pb field [" << from << "] " << field_index << " is not a valid Message";
    return false;
  }
  if (msg_field->type() != FieldDescriptor::TYPE_MESSAGE) {
    CL_LOG(ERROR) << "pb field [" << from << "] " << field_index << " is not Message type"
                  << msg_field->type_name() << " " << msg_field->cpp_type_name() << "  " << msg_field->type();
    return false;
  }

  if (msg_field->is_repeated()) {
    *has_repeated_in_path = true;
    int list_size = reflection->FieldSize(msg, msg_field);

    if (repeat_limit && repeat_limit->size() > from) {
      list_size = (*repeat_limit)[from] >= 0 ? std::min(list_size, (*repeat_limit)[from]) : list_size;
    }

    for (int i = 0; i < list_size; i++) {
      if (!FollowMessagePath(reflection->GetRepeatedMessage(msg, msg_field, i), msg_path,
                             has_repeated_in_path, has_empty_field_in_path, leaf_msgs, from + 1,
                             repeat_limit)) {
        return false;
      }
    }
  } else {
    if (!reflection->HasField(msg, msg_field)) {
      *has_empty_field_in_path = true;
    }
    if (!FollowMessagePath(reflection->GetMessage(msg, msg_field), msg_path, has_repeated_in_path,
                           has_empty_field_in_path, leaf_msgs, from + 1, repeat_limit)) {
      return false;
    }
  }

  return true;
}

// follow message path and return mutable leaf message
Message *FollowMessagePath(Message *msg, const std::vector<int> &msg_path, int from = 0) {
  if (msg_path.size() < 1 || from > msg_path.size() - 1) return nullptr;

  auto *reflection = msg->GetReflection();
  auto *descriptor = msg->GetDescriptor();

  if (from == msg_path.size() - 1) {
    // already leaf message
    return msg;
  }

  int field_index = msg_path[from];
  auto *msg_field = descriptor->field(field_index);
  if (!msg_field) {
    CL_LOG(ERROR) << "pb field [" << from << "] " << field_index << " is not a valid Message";
    return nullptr;
  }
  if (msg_field->type() != FieldDescriptor::TYPE_MESSAGE) {
    CL_LOG(ERROR) << "pb field [" << from << "] " << field_index << " is not Message type"
                  << msg_field->type_name() << " " << msg_field->cpp_type_name() << "  " << msg_field->type();
    return nullptr;
  }

  if (msg_field->is_repeated()) {
    return FollowMessagePath(reflection->MutableRepeatedMessage(msg, msg_field, 0), msg_path, from + 1);
  } else {
    return FollowMessagePath(reflection->MutableMessage(msg, msg_field), msg_path, from + 1);
  }
}

void VisitMessageTree(const Message &msg,
                      std::function<void(const Message &, int, const std::string &, bool)> callback,
                      const std::string &prefix = "", int max_depth = -1, int depth = 0,
                      bool has_repeated_in_path = false) {
  if (max_depth >= 0 && depth > max_depth) {
    return;
  }

  auto *reflection = msg.GetReflection();
  auto *descriptor = msg.GetDescriptor();

  for (int i = 0; i < descriptor->field_count(); i++) {
    auto *field = CHECK_NOTNULL(descriptor->field(i));

    const std::string fullpath = prefix + (depth == 0 ? "" : ".") + field->name();
    if (field->type() == FieldDescriptor::TYPE_MESSAGE) {
      if (field->is_repeated()) {
        int list_size = reflection->FieldSize(msg, field);
        for (int i = 0; i < list_size; i++) {
          VisitMessageTree(reflection->GetRepeatedMessage(msg, field, i), callback, fullpath, max_depth,
                           depth + 1, true);
        }
      } else {
        VisitMessageTree(reflection->GetMessage(msg, field), callback, fullpath, max_depth, depth + 1,
                         has_repeated_in_path);
      }
    } else {
      callback(msg, field->index(), fullpath, has_repeated_in_path);
    }
  }
}

template <typename SetterHandle>
size_t SaveProtobufMessageWithSetterHandle(SetterHandle handle, const std::string &attr_name,
                                           const Message &msg, int field_index, bool skip_unset_field = false,
                                           int repeat_limit = -1, bool repeat_align = false,
                                           const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                           const std::string &sample_attr_name = "",
                                           int64 sample_attr_name_value = -1) {
  auto *reflection = msg.GetReflection();
  auto *descriptor = msg.GetDescriptor();
  auto *field = descriptor->field(field_index);
  if (!field) {
    CL_LOG(ERROR) << field_index << " is not a valid pb msg field index for attr: " << attr_name;
    return 0;
  }

  size_t list_size = 1;
  if (field->is_repeated()) {
    if (repeat_limit >= 0) {
      list_size = std::min(reflection->FieldSize(msg, field), repeat_limit);
    } else {
      list_size = reflection->FieldSize(msg, field);
    }
    switch (field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetDoubleListAttr, double, Double);
      case FieldDescriptor::CPPTYPE_FLOAT:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetDoubleListAttr, double, Float);
      case FieldDescriptor::CPPTYPE_INT64:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, Int64);
      case FieldDescriptor::CPPTYPE_UINT64:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, UInt64);
      case FieldDescriptor::CPPTYPE_INT32:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, Int32);
      case FieldDescriptor::CPPTYPE_UINT32:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, UInt32);
      case FieldDescriptor::CPPTYPE_BOOL:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, Bool);
      case FieldDescriptor::CPPTYPE_ENUM:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetIntListAttr, int64, EnumValue);
      case FieldDescriptor::CPPTYPE_STRING:
        FILL_ITEM_LIST_ATTR_FROM_PB(SetStringListAttr, std::string, String);
      case FieldDescriptor::CPPTYPE_MESSAGE:
        if (sample_attr_descriptor) {
          auto field_size = reflection->FieldSize(msg, field);
          for (int i = field_size - 1; i >= 0; i--) {
            const google::protobuf::Message &sub_msg = reflection->GetRepeatedMessage(msg, field, i);
            if (sample_attr_descriptor->Match(sub_msg, sample_attr_name, sample_attr_name_value)) {
              sample_attr_descriptor->SaveSampleAttrLikeWithSetterHandle(handle, attr_name, sub_msg, false);
              break;
            }
          }
        } else {
          CL_LOG_EVERY_N(ERROR, 1000)
              << "Failed to set list attr [" << attr_name << "] with field [" << field_index
              << "] of unsupported type: repeated " << FieldDescriptor::TypeName(field->type());
        }
        break;
      default:
        CL_LOG_EVERY_N(ERROR, 1000) << "Failed to set list attr [" << attr_name << "] with field ["
                                    << field_index << "] of unsupported type: repeated "
                                    << FieldDescriptor::TypeName(field->type());
        return 0;
    }
  } else {
    if (skip_unset_field && !reflection->HasField(msg, field)) {
      return 0;
    }
    switch (field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE:
        handle.SetDoubleAttr(attr_name, reflection->GetDouble(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_FLOAT:
        handle.SetDoubleAttr(attr_name, reflection->GetFloat(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_INT64:
        handle.SetIntAttr(attr_name, reflection->GetInt64(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_UINT64:
        handle.SetIntAttr(attr_name, reflection->GetUInt64(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_INT32:
        handle.SetIntAttr(attr_name, reflection->GetInt32(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_UINT32:
        handle.SetIntAttr(attr_name, reflection->GetUInt32(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_BOOL:
        handle.SetIntAttr(attr_name, reflection->GetBool(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_ENUM:
        handle.SetIntAttr(attr_name, reflection->GetEnumValue(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_STRING:
        handle.SetStringAttr(attr_name, reflection->GetString(msg, field));
        break;
      case FieldDescriptor::CPPTYPE_MESSAGE:
        handle.SetPtrAttr(attr_name, &reflection->GetMessage(msg, field));
        break;
      default:
        CL_LOG_EVERY_N(ERROR, 1000) << "Failed to set singular attr [" << attr_name << "] with field ["
                                    << field_index
                                    << "] of unsupported type: " << FieldDescriptor::TypeName(field->type());
        return 0;
    }
  }
  return list_size;
}


template <typename GetterHandle>
bool SaveToProtobufMessageFromGetterHandle(GetterHandle handle, const std::string &attr_name, Message *msg,
                                           int field_index, bool append, bool allocated) {
  auto *reflection = msg->GetReflection();
  auto *descriptor = msg->GetDescriptor();
  auto *field = descriptor->field(field_index);
  if (!field) {
    CL_LOG(ERROR) << "invalid pb msg field index " << field_index << " for attr: " << attr_name;
    return false;
  }

  if (field->is_repeated()) {
    if (!append) {
      reflection->ClearField(msg, field);
    }
    switch (field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE:
        if (append) {
          if (auto double_val = handle.GetDoubleAttr(attr_name)) {
            reflection->AddDouble(msg, field, *double_val);
            return true;
          }
        }
        if (auto double_list_val = handle.GetDoubleListAttr(attr_name)) {
          for (double double_val : *double_list_val) {
            reflection->AddDouble(msg, field, double_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_FLOAT:
        if (append) {
          if (auto double_val = handle.GetDoubleAttr(attr_name)) {
            reflection->AddFloat(msg, field, *double_val);
            return true;
          }
        }
        if (auto double_list_val = handle.GetDoubleListAttr(attr_name)) {
          for (double double_val : *double_list_val) {
            reflection->AddFloat(msg, field, double_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_INT64:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddInt64(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddInt64(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_UINT64:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddUInt64(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddUInt64(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_INT32:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddInt32(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddInt32(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_UINT32:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddUInt32(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddUInt32(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_BOOL:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddBool(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddBool(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_ENUM:
        if (append) {
          if (auto int_val = handle.GetIntAttr(attr_name)) {
            reflection->AddEnumValue(msg, field, *int_val);
            return true;
          }
        }
        if (auto int_list_val = handle.GetIntListAttr(attr_name)) {
          for (int64 int_val : *int_list_val) {
            reflection->AddEnumValue(msg, field, int_val);
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_STRING:
        if (append) {
          if (auto str_val = handle.GetStringAttr(attr_name)) {
            reflection->AddString(msg, field, std::string(*str_val));
            return true;
          }
        }
        if (auto str_list_val = handle.GetStringListAttr(attr_name)) {
          for (auto str_val : *str_list_val) {
            reflection->AddString(msg, field, std::string(str_val));
          }
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_MESSAGE:
        if (append) {
          if (auto ptr_val = const_cast<::google::protobuf::Message *>(
                  handle.template GetPtrAttr<::google::protobuf::Message>(attr_name))) {
            if (allocated) {
              reflection->AddAllocatedMessage(msg, field, ptr_val);
            } else {
              reflection->AddMessage(msg, field)->CopyFrom(*ptr_val);
            }
            return true;
          }
        }
        break;
      default:
        CL_LOG_EVERY_N(ERROR, 1000) << "Failed to set list attr [" << attr_name << "] with field ["
                                    << field_index << "] of unsupported type: repeated "
                                    << FieldDescriptor::TypeName(field->type());
        return false;
    }
  } else {
    switch (field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE:
        if (auto double_val = handle.GetDoubleAttr(attr_name)) {
          reflection->SetDouble(msg, field, *double_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_FLOAT:
        if (auto double_val = handle.GetDoubleAttr(attr_name)) {
          reflection->SetFloat(msg, field, *double_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_INT64:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetInt64(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_UINT64:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetUInt64(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_INT32:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetInt32(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_UINT32:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetUInt32(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_BOOL:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetBool(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_ENUM:
        if (auto int_val = handle.GetIntAttr(attr_name)) {
          reflection->SetEnumValue(msg, field, *int_val);
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_STRING:
        if (auto str_val = handle.GetStringAttr(attr_name)) {
          reflection->SetString(msg, field, std::string(*str_val));
          return true;
        }
        break;
      case FieldDescriptor::CPPTYPE_MESSAGE:
        if (auto ptr_val = const_cast<::google::protobuf::Message *>(
                handle.template GetPtrAttr<::google::protobuf::Message>(attr_name))) {
          if (allocated) {
            reflection->SetAllocatedMessage(msg, ptr_val, field);
          } else {
            reflection->MutableMessage(msg, field)->CopyFrom(*ptr_val);
          }
          return true;
        }
        break;
      default:
        CL_LOG_EVERY_N(ERROR, 1000) << "Failed to set singular attr [" << attr_name << "] with field ["
                                    << field_index
                                    << "] of unsupported type: " << FieldDescriptor::TypeName(field->type());
        return false;
    }
  }

  CL_LOG_EVERY_N(WARNING, 1000) << "Failed to set attr [" << attr_name << "] with field [" << field_index
                                << "], field type: " << (field->is_repeated() ? "repeated " : "")
                                << FieldDescriptor::TypeName(field->type());
  return false;
}

// similar to SaveProtobufMessageWithSetterHandle except that always append result as list and do NOT reset
// before append
template <typename SetterHandle>
size_t AppendProtobufMessageWithSetterHandle(SetterHandle handle, const std::string &attr_name,
                                             const Message &msg, int field_index,
                                             bool skip_unset_field = false, int repeat_limit = -1,
                                             bool repeat_align = false,
                                             const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                             const std::string &sample_attr_name = "",
                                             int64 sample_attr_name_value = -1) {
  auto *reflection = msg.GetReflection();
  auto *descriptor = msg.GetDescriptor();
  auto *field = descriptor->field(field_index);
  if (!field) {
    CL_LOG(ERROR) << field_index << " is not a valid pb msg field index for attr: " << attr_name;
    return 0;
  }

  size_t list_size = 1;
  if (field->is_repeated()) {
    if (repeat_limit >= 0) {
      list_size = std::min(reflection->FieldSize(msg, field), repeat_limit);
    } else {
      list_size = reflection->FieldSize(msg, field);
    }
  }

  switch (field->cpp_type()) {
    case FieldDescriptor::CPPTYPE_DOUBLE:
      APPEND_LIST_ATTR_FROM_PB(AppendDoubleListAttr, Double, 0.0);
    case FieldDescriptor::CPPTYPE_FLOAT:
      APPEND_LIST_ATTR_FROM_PB(AppendDoubleListAttr, Float, 0.0);
    case FieldDescriptor::CPPTYPE_INT64:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, Int64, 0);
    case FieldDescriptor::CPPTYPE_UINT64:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, UInt64, 0);
    case FieldDescriptor::CPPTYPE_INT32:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, Int32, 0);
    case FieldDescriptor::CPPTYPE_UINT32:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, UInt32, 0);
    case FieldDescriptor::CPPTYPE_BOOL:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, Bool, 0);
    case FieldDescriptor::CPPTYPE_ENUM:
      APPEND_LIST_ATTR_FROM_PB(AppendIntListAttr, EnumValue, 0);
    case FieldDescriptor::CPPTYPE_STRING:
      APPEND_LIST_ATTR_FROM_PB(AppendStringListAttr, String, "");
    case FieldDescriptor::CPPTYPE_MESSAGE:
      if (sample_attr_descriptor) {
        if (field->is_repeated()) {
          auto field_size = reflection->FieldSize(msg, field);
          for (int i = field_size - 1; i >= 0; i--) {
            const google::protobuf::Message &sub_msg = reflection->GetRepeatedMessage(msg, field, i);
            if (sample_attr_descriptor->Match(sub_msg, sample_attr_name, sample_attr_name_value)) {
              sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(handle, attr_name, sub_msg);
              break;
            }
          }
        } else {
          const google::protobuf::Message &sub_msg = reflection->GetMessage(msg, field);
          if (sample_attr_descriptor->Match(sub_msg, sample_attr_name, sample_attr_name_value)) {
            sample_attr_descriptor->AppendSampleAttrLikeWithSetterHandle(handle, attr_name, sub_msg);
          }
        }
      } else {
        CL_LOG_EVERY_N(ERROR, 1000) << "Failed to set protobuf message list attr [" << attr_name
                                    << "] with field [" << field_index
                                    << "] of unsupported type: " << field->message_type()->full_name();
      }
      break;
    default:
      CL_LOG_EVERY_N(ERROR, 1000) << "Failed to append list attr [" << attr_name << "] with field ["
                                  << field_index
                                  << "] of unsupported type: " << (field->is_repeated() ? "repeated " : "")
                                  << FieldDescriptor::TypeName(field->type());
      return 0;
  }
  return list_size;
}

// resetter for AppendProtobufMessageWithSetterHandle
template <typename SetterHandle>
bool ResetListWithSetterHandleAccordingToProtobufFieldType(SetterHandle handle, const std::string &attr_name,
                                                           const Message &msg, int field_index,
                                                           int capacity = 0) {
  auto *descriptor = msg.GetDescriptor();
  auto *field = descriptor->field(field_index);
  if (!field) {
    CL_LOG(ERROR) << field_index << " is not a valid pb msg field index for attr: " << attr_name;
    return false;
  }

  switch (field->cpp_type()) {
    case FieldDescriptor::CPPTYPE_DOUBLE:
    case FieldDescriptor::CPPTYPE_FLOAT:
      handle.ResetDoubleListAttr(attr_name, capacity);
      break;
    case FieldDescriptor::CPPTYPE_INT64:
    case FieldDescriptor::CPPTYPE_UINT64:
    case FieldDescriptor::CPPTYPE_INT32:
    case FieldDescriptor::CPPTYPE_UINT32:
    case FieldDescriptor::CPPTYPE_BOOL:
    case FieldDescriptor::CPPTYPE_ENUM:
      handle.ResetIntListAttr(attr_name, capacity);
      break;
    case FieldDescriptor::CPPTYPE_STRING:
      handle.ResetStringListAttr(attr_name, capacity);
      break;
    case FieldDescriptor::CPPTYPE_MESSAGE:
      break;
    default:
      CL_LOG_EVERY_N(ERROR, 1000) << "Failed to reset list attr [" << attr_name << "] with field ["
                                  << field_index
                                  << "] of unsupported type: " << (field->is_repeated() ? "repeated " : "")
                                  << FieldDescriptor::TypeName(field->type());
      return false;
  }
  return true;
}

template <typename SetterHandle>
std::unordered_set<std::string> SaveWholeProtobufMessageWithSetterHandle(SetterHandle handle,
                                                                         const std::string &prefix,
                                                                         const Message &msg, int max_depth) {
  std::unordered_set<std::string> visited_path;
  VisitMessageTree(
      msg,
      [&](const Message &msg, int field, const std::string &fullpath, bool has_repeated_in_path) {
        if (has_repeated_in_path) {
          if (visited_path.insert(fullpath).second) {
            ResetListWithSetterHandleAccordingToProtobufFieldType(handle, fullpath, msg, field);
          }
          AppendProtobufMessageWithSetterHandle(handle, fullpath, msg, field);
        } else {
          visited_path.insert(fullpath);
          SaveProtobufMessageWithSetterHandle(handle, fullpath, msg, field);
        }
      },
      prefix, max_depth);
  return visited_path;
}

template <typename SetterHandle>
size_t SaveProtobufMessageWithSetterHandle(
    SetterHandle handle, const std::string &attr_name, const Message &msg, const std::vector<int> &msg_path,
    bool skip_unset_field = false, const std::vector<int> *repeat_limit = nullptr, bool repeat_align = false,
    const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
    const std::string &sample_attr_name = "", int64 sample_attr_name_value = -1) {
  std::vector<const Message *> leaf_msgs;
  bool has_repeated_in_path = false;
  bool has_empty_field_in_path = false;
  if (!FollowMessagePath(msg, msg_path, &has_repeated_in_path, &has_empty_field_in_path, &leaf_msgs, 0,
                         repeat_limit) ||
      leaf_msgs.empty()) {
    return 0;
  }

  int limit = -1;
  if (repeat_limit && repeat_limit->size() == msg_path.size()) {
    limit = repeat_limit->back();
  }

  int field_index = msg_path.back();
  if (has_repeated_in_path) {
    size_t num_items = 0;
    ResetListWithSetterHandleAccordingToProtobufFieldType(handle, attr_name, *leaf_msgs.front(), field_index,
                                                          leaf_msgs.size());
    for (const Message *msg : leaf_msgs) {
      num_items += AppendProtobufMessageWithSetterHandle(
          handle, attr_name, *msg, field_index, skip_unset_field, limit, repeat_align, sample_attr_descriptor,
          sample_attr_name, sample_attr_name_value);
    }
    return num_items;
  } else {
    if (skip_unset_field && has_empty_field_in_path) {
      return 0;
    }
    return SaveProtobufMessageWithSetterHandle(handle, attr_name, *leaf_msgs.front(), field_index,
                                               skip_unset_field, limit, repeat_align, sample_attr_descriptor,
                                               sample_attr_name, sample_attr_name_value);
  }
}
}  // namespace

size_t AppendProtobufMessageToListCommonAttr(MutableRecoContextInterface *context,
                                             const std::string &attr_name,
                                             const google::protobuf::Message &msg,
                                             const std::vector<int> &msg_path, bool skip_unset_field) {
  std::vector<const Message *> leaf_msgs;
  bool has_repeated_in_path = false;
  bool has_empty_field_in_path = false;  // be of no use.
  if (!FollowMessagePath(msg, msg_path, &has_repeated_in_path, &has_empty_field_in_path, &leaf_msgs, 0) ||
      leaf_msgs.empty() || has_repeated_in_path) {
    return 0;
  }
  int field_index = msg_path.back();
  return AppendProtobufMessageWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name,
                                               *(leaf_msgs[0]), field_index);
}

size_t SaveProtobufMessageToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                       const Message &msg, const std::vector<int> &msg_path,
                                       bool skip_unset_field, const std::vector<int> *repeat_limit,
                                       bool repeat_align,
                                       const SampleAttrLikeDescriptor *sample_attr_descriptor,
                                       const std::string &sample_attr_name, int64 sample_attr_name_value) {
  return SaveProtobufMessageWithSetterHandle(
      CommonRecoContextCommonAttrSetterHandle(context), attr_name, msg, msg_path, skip_unset_field,
      repeat_limit, repeat_align, sample_attr_descriptor, sample_attr_name, sample_attr_name_value);
}

size_t SaveProtobufMessageToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                     const std::string &attr_name, const Message &msg,
                                     const std::vector<int> &msg_path, bool skip_unset_field,
                                     const std::vector<int> *repeat_limit, bool repeat_align,
                                     const SampleAttrLikeDescriptor *sample_attr_descriptor,
                                     const std::string &sample_attr_name, int64 sample_attr_name_value) {
  return SaveProtobufMessageWithSetterHandle(
      CommonRecoContextItemAttrSetterHandle(context, item_key), attr_name, msg, msg_path, skip_unset_field,
      repeat_limit, repeat_align, sample_attr_descriptor, sample_attr_name, sample_attr_name_value);
}

size_t SaveProtobufMessageToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                     const Message &msg, const std::vector<int> &msg_path,
                                     bool skip_unset_field, const std::vector<int> *repeat_limit,
                                     bool repeat_align,
                                     const SampleAttrLikeDescriptor *sample_attr_descriptor,
                                     const std::string &sample_attr_name, int64 sample_attr_name_value) {
  return SaveProtobufMessageWithSetterHandle(CommonRecoContextItemAttrHpSetterHandle(result, attr_accessor),
                                             attr_accessor->name(), msg, msg_path, skip_unset_field,
                                             repeat_limit, repeat_align, sample_attr_descriptor,
                                             sample_attr_name, sample_attr_name_value);
}

const char *GetAttrValueVariantTypeName(const AttrValueVariant &variant) {
  switch (variant.index()) {
    case 0:
      return "none(absl::nullopt_t)";
    case 1:
      return "int64";
    case 2:
      return "double";
    case 3:
      return "string";
    case 4:
      return "int64_list";
    case 5:
      return "double_list";
    case 6:
      return "string_list";
    case 7:
      return "pb_message";
    default:
      break;
  }
  return "unknown";
}

class AttrValueVariantSetterHandle {
 public:
  explicit AttrValueVariantSetterHandle(AttrValueVariant *variant) : variant_(variant) {}

  inline void SetIntAttr(absl::string_view attr_name, int64 val, bool no_overwrite = false) {
    *variant_ = AttrValueVariant(val);
  }
  inline void SetDoubleAttr(absl::string_view attr_name, double val, bool no_overwrite = false) {
    *variant_ = AttrValueVariant(val);
  }
  inline void SetStringAttr(absl::string_view attr_name, std::string val, bool no_overwrite = false) {
    *variant_ = AttrValueVariant(std::move(val));
  }

  inline void ResetIntListAttr(absl::string_view attr_name, int capacity = 0) {
    std::vector<int64> list;
    list.reserve(capacity);
    *variant_ = AttrValueVariant(std::move(list));
  }
  inline void ResetDoubleListAttr(absl::string_view attr_name, int capacity = 0) {
    std::vector<double> list;
    list.reserve(capacity);
    *variant_ = AttrValueVariant(std::move(list));
  }
  inline void ResetStringListAttr(absl::string_view attr_name, int capacity = 0) {
    std::vector<std::string> list;
    list.reserve(capacity);
    *variant_ = AttrValueVariant(std::move(list));
  }

  inline void AppendIntListAttr(absl::string_view attr_name, int64 value) {
    if (!absl::holds_alternative<std::vector<int64>>(*variant_)) {
      *variant_ = AttrValueVariant(std::vector<int64>());
    }
    auto &list = absl::get<std::vector<int64>>(*variant_);
    list.push_back(value);
  }
  inline void AppendDoubleListAttr(absl::string_view attr_name, double value) {
    if (!absl::holds_alternative<std::vector<double>>(*variant_)) {
      *variant_ = AttrValueVariant(std::vector<double>());
    }
    auto &list = absl::get<std::vector<double>>(*variant_);
    list.push_back(value);
  }
  inline void AppendStringListAttr(absl::string_view attr_name, std::string value) {
    if (!absl::holds_alternative<std::vector<std::string>>(*variant_)) {
      *variant_ = AttrValueVariant(std::vector<std::string>());
    }
    auto &list = absl::get<std::vector<std::string>>(*variant_);
    list.push_back(std::move(value));
  }

  inline void SetIntListAttr(absl::string_view attr_name, std::vector<int64> &&value,
                             bool no_overwrite = false) {
    *variant_ = AttrValueVariant(std::move(value));
  }
  inline void SetDoubleListAttr(absl::string_view attr_name, std::vector<double> &&value,
                                bool no_overwrite = false) {
    *variant_ = AttrValueVariant(std::move(value));
  }
  inline void SetStringListAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                bool no_overwrite = false) {
    *variant_ = AttrValueVariant(std::move(value));
  }

  template <typename Ptr>
  inline void SetPtrAttr(absl::string_view attr_name, Ptr &&value, bool no_overwrite = false) {
    *variant_ = AttrValueVariant(static_cast<const google::protobuf::Message *>(value));
  }

 private:
  AttrValueVariant *variant_;
};

void SaveProtobufMessageToAttrValueVariant(AttrValueVariant *variant, const google::protobuf::Message &msg,
                                           const std::vector<int> &msg_path, bool skip_unset_field,
                                           const std::vector<int> *repeat_limit, bool repeat_align,
                                           const SampleAttrLikeDescriptor *sample_attr_descriptor,
                                           const std::string &sample_attr_name,
                                           int64 sample_attr_name_value) {
  SaveProtobufMessageWithSetterHandle(AttrValueVariantSetterHandle(variant), "", msg, msg_path,
                                      skip_unset_field, repeat_limit, repeat_align, sample_attr_descriptor,
                                      sample_attr_name, sample_attr_name_value);
}

bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     google::protobuf::Message *msg, const std::vector<int> &msg_path,
                                     bool append, bool allocated) {
  google::protobuf::Message *leaf_msg = FollowMessagePath(msg, msg_path);
  if (leaf_msg) {
    return SaveCommonAttrToProtobufMessage(context, attr_name, leaf_msg, msg_path.back(), append, allocated);
  } else {
    return false;
  }
}

bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     const std::vector<int> &source_path, google::protobuf::Message *dest_msg,
                                     const std::vector<int> &dest_path, bool append) {
  auto *source_msg = const_cast<::google::protobuf::Message *>(
    CommonRecoContextCommonAttrGetterHandle(context)
    .GetPtrAttr<::google::protobuf::Message>(attr_name));
  if (!source_msg || !dest_msg) {
    CL_LOG_WARNING("SaveCommonAttrToProtobufMessage", "cp_pb.save_common_execute_error")
      << "Execute copy common field to protobuf field error."
      << "source_msg or dest_msg is nullptr. common_attr name: " << attr_name;
    return false;
  }
  google::protobuf::Message *source_leaf_msg = FollowMessagePath(source_msg, source_path);
  google::protobuf::Message *dest_leaf_msg = FollowMessagePath(dest_msg, dest_path);
  if (!source_leaf_msg || !dest_leaf_msg) {
    CL_LOG_WARNING("SaveCommonAttrToProtobufMessage", "cp_pb.save_common_execute_error")
      << "Execute copy common field to protobuf field error."
      << "source_leaf_msg or dest_leaf_msg is nullptr. common_attr name: " << attr_name;
    return false;
  }
  std::string err_str;
  bool execute_res = SaveProtobuf2ProtobufExecutor(source_leaf_msg,
    dest_leaf_msg, source_path, dest_path, append, &err_str);
  if (!execute_res) {
    CL_LOG_WARNING("SaveCommonAttrToProtobufMessage", "cp_pb.save_common_execute_error")
      << "Do Execute copy common field to protobuf field error."
      << "common_attr name: " << attr_name
      << "error str: " << err_str;
    return false;
  }
  return true;
}

bool SaveItemAttrToProtobufMessage(ReadableRecoContextInterface *context, uint64 item_key,
                                   const std::string &attr_name, google::protobuf::Message *msg,
                                   const std::vector<int> &msg_path, bool append, bool allocated) {
  google::protobuf::Message *leaf_msg = FollowMessagePath(msg, msg_path);
  if (leaf_msg) {
    return SaveItemAttrToProtobufMessage(context, item_key, attr_name, leaf_msg, msg_path.back(), append,
                                         allocated);
  } else {
    return false;
  }
}

bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                   google::protobuf::Message *msg, const std::vector<int> &msg_path,
                                   bool append, bool allocated) {
  google::protobuf::Message *leaf_msg = FollowMessagePath(msg, msg_path);
  if (leaf_msg) {
    return SaveItemAttrToProtobufMessage(result, attr_accessor, leaf_msg, msg_path.back(), append, allocated);
  } else {
    return false;
  }
}

bool ProcessIntSourceField(std::vector<int64> *source_values,
  google::protobuf::Message *source_leaf_msg,
  const google::protobuf::Reflection *source_reflection,
  const google::protobuf::Descriptor *source_descriptor,
  const google::protobuf::FieldDescriptor *source_field) {
  if (source_field->is_repeated()) {
    size_t list_size = source_reflection->FieldSize(*source_leaf_msg, source_field);
    switch (source_field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedDouble(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_FLOAT: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedFloat(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_INT64: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedInt64(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT64: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedUInt64(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_INT32: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedInt32(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT32: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedUInt32(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_BOOL: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedBool(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_ENUM: {
        for (int i = 0; i < list_size; i++) {
          int64_t int_val = source_reflection->GetRepeatedEnumValue(*source_leaf_msg, source_field, i);
          source_values->emplace_back(int_val);
        }
        break;
      }
      default:
        return false;
    }
  } else {
    switch (source_field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE: {
        source_values->emplace_back(source_reflection->GetDouble(*source_leaf_msg, source_field));
        break;
      }
      case FieldDescriptor::CPPTYPE_FLOAT: {
        source_values->emplace_back(source_reflection->GetFloat(*source_leaf_msg, source_field));
        break;
      }
      case FieldDescriptor::CPPTYPE_INT64: {
        int64_t int_val = source_reflection->GetInt64(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT64: {
        int64_t int_val = source_reflection->GetUInt64(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      case FieldDescriptor::CPPTYPE_INT32: {
        int64_t int_val = source_reflection->GetInt32(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT32: {
        int64_t int_val = source_reflection->GetUInt32(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      case FieldDescriptor::CPPTYPE_BOOL: {
        int64_t int_val = source_reflection->GetBool(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      case FieldDescriptor::CPPTYPE_ENUM: {
        int64_t int_val = source_reflection->GetEnumValue(*source_leaf_msg, source_field);
        source_values->emplace_back(int_val);
        break;
      }
      default:
        return false;
    }
  }
  return true;
}

bool ProcessDoubleSourceField(std::vector<double> *source_values,
  google::protobuf::Message *source_leaf_msg,
  const google::protobuf::Reflection *source_reflection,
  const google::protobuf::Descriptor *source_descriptor,
  const google::protobuf::FieldDescriptor *source_field) {
  if (source_field->is_repeated()) {
    size_t list_size = source_reflection->FieldSize(*source_leaf_msg, source_field);
    switch (source_field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_DOUBLE: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedDouble(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_FLOAT: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedFloat(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_INT64: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedInt64(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT64: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedUInt64(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_INT32: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedInt32(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_UINT32: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedUInt32(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_BOOL: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedBool(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      case FieldDescriptor::CPPTYPE_ENUM: {
        for (int i = 0; i < list_size; i++) {
          double double_val = source_reflection->GetRepeatedEnumValue(*source_leaf_msg, source_field, i);
          source_values->emplace_back(double_val);
        }
        break;
      }
      default:
        return false;
    }
    return true;
  }
  switch (source_field->cpp_type()) {
    case FieldDescriptor::CPPTYPE_DOUBLE: {
      source_values->emplace_back(source_reflection->GetDouble(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_FLOAT: {
      source_values->emplace_back(source_reflection->GetFloat(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_INT64: {
      source_values->emplace_back(source_reflection->GetInt64(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_UINT64: {
      source_values->emplace_back(source_reflection->GetUInt64(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_INT32: {
      source_values->emplace_back(source_reflection->GetInt32(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_UINT32: {
      source_values->emplace_back(source_reflection->GetUInt32(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_BOOL: {
      source_values->emplace_back(source_reflection->GetBool(*source_leaf_msg, source_field));
      break;
    }
    case FieldDescriptor::CPPTYPE_ENUM: {
      source_values->emplace_back(source_reflection->GetEnumValue(*source_leaf_msg, source_field));
      break;
    }
    default:
      return false;
  }
  return true;
}

bool ProcessStringSourceField(std::vector<std::string> *source_values,
  google::protobuf::Message *source_leaf_msg,
  const google::protobuf::Reflection *source_reflection,
  const google::protobuf::Descriptor *source_descriptor,
  const google::protobuf::FieldDescriptor *source_field) {
  if (source_field->is_repeated()) {
    size_t list_size = source_reflection->FieldSize(*source_leaf_msg, source_field);
    switch (source_field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_STRING: {
        for (int i = 0; i < list_size; i++) {
          std::string str_val = source_reflection->GetRepeatedString(*source_leaf_msg, source_field, i);
          source_values->emplace_back(str_val);
        }
        break;
      }
      default:
        return false;
    }
  } else {
    switch (source_field->cpp_type()) {
      case FieldDescriptor::CPPTYPE_STRING: {
        std::string str_val = source_reflection->GetString(*source_leaf_msg, source_field);
        source_values->emplace_back(str_val);
        break;
      }
      default:
        return false;
    }
  }
  return true;
}

bool SaveProtobuf2ProtobufExecutor(google::protobuf::Message *source_leaf_msg,
  google::protobuf::Message *dest_leaf_msg, const std::vector<int> &source_path,
  const std::vector<int> &dest_path, bool append, std::string* err_str) {
  auto *source_reflection = source_leaf_msg->GetReflection();
  auto *source_descriptor = source_leaf_msg->GetDescriptor();
  auto *source_field = source_descriptor->field(source_path.back());
  auto *dest_reflection = dest_leaf_msg->GetReflection();
  auto *dest_descriptor = dest_leaf_msg->GetDescriptor();
  auto *dest_field = dest_descriptor->field(dest_path.back());
  if (!source_reflection || !dest_reflection) {
    CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.reflection_nullptr")
      << "Execute SaveProtobuf2ProtobufExecutor get source and dest reflection error."
      << "one of source_reflection or dest_reflection is nullptr.";
    *err_str = "one of source_reflection or dest_reflection is nullptr";
    return false;
  }
  if (!source_descriptor || !dest_descriptor) {
    CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.descriptor_nullptr")
      << "Execute SaveProtobuf2ProtobufExecutor get source and dest descriptor error."
      << "one of source_descriptor or dest_descriptor is nullptr.";
    *err_str = "one of source_descriptor or dest_descriptor is nullptr";
    return false;
  }
  if (!source_field || !dest_field) {
    CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.field_nullptr")
      << "Execute SaveProtobuf2ProtobufExecutor get source and dest field error."
      << "one of source_field or dest_field is nullptr.";
    *err_str = "one of source_field or dest_field is nullptr";
    return false;
  }
  if (source_field->is_repeated() && !dest_field->is_repeated()) {
    CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.field_type_error")
      << "Execute SaveProtobuf2ProtobufExecutor assign repeated source field to dest field error."
      << "source_field is repeated and dest_field is single.";
    *err_str = "source_field is repeated and dest_field is single.";
    return false;
  }

  switch (dest_field->cpp_type()) {
    case FieldDescriptor::CPPTYPE_DOUBLE: {
      std::vector<double> source_values;
      bool process_res = ProcessDoubleSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << "source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process double field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddDouble(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetDouble(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_FLOAT: {
      std::vector<double> source_values;
      bool process_res = ProcessDoubleSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process float field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddFloat(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetFloat(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_INT64: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process int64 field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddInt64(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetInt64(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_UINT64: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process uint64 field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddUInt64(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetUInt64(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_INT32: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process int32 field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddInt32(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetInt32(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_UINT32: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process uint32 field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddUInt32(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetUInt32(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_BOOL: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process bool field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddBool(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetBool(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_ENUM: {
      std::vector<int64_t> source_values;
      bool process_res = ProcessIntSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process enum field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto double_val : source_values) {
          dest_reflection->AddEnumValue(dest_leaf_msg, dest_field, double_val);
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetEnumValue(dest_leaf_msg, dest_field, source_values.at(0));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_STRING: {
      std::vector<std::string> source_values;
      bool process_res = ProcessStringSourceField(&source_values, source_leaf_msg,
        source_reflection, source_descriptor, source_field);
      if (!process_res) {
        CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
          << "Execute GetSourceFieldInfo error."
          << ", source_field name: " << source_field->name()
          << ", source_field type: " << source_field->cpp_type_name()
          << ", dest_field name: " << dest_field->name()
          << ", dest_field type: " << dest_field->cpp_type_name();
        *err_str = "process string field is error.";
        return false;
      }
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
        for (auto str_val : source_values) {
          dest_reflection->AddString(dest_leaf_msg, dest_field, std::string(str_val));
        }
      } else {
        if (source_values.size() > 0) {
          dest_reflection->SetString(dest_leaf_msg, dest_field, std::string(source_values.at(0)));
        }
      }
      break;
    }
    case FieldDescriptor::CPPTYPE_MESSAGE: {
      if (dest_field->is_repeated()) {
        if (!append) dest_reflection->ClearField(dest_leaf_msg, dest_field);
      }
      if (source_field->is_repeated()) {
        size_t list_size = source_reflection->FieldSize(*source_leaf_msg, source_field);
        for (int i = 0; i < list_size; i++) {
          auto* msg_val = const_cast<::google::protobuf::Message *>(
              &(source_reflection->GetRepeatedMessage(*source_leaf_msg, source_field, i)));
          if (msg_val == nullptr) {
            CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
              << "Execute Current message value nullptr."
              << ", source_field name: " << source_field->name()
              << ", source_field type: " << source_field->cpp_type_name()
              << ", dest_field name: " << dest_field->name()
              << ", dest_field type: " << dest_field->cpp_type_name();
            return false;
          }
          if (dest_field->is_repeated()) {
            dest_reflection->AddMessage(dest_leaf_msg, dest_field)->CopyFrom(*msg_val);
          } else {
            CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.do_execute_error")
              << "Execute GetSourceFieldInfo error."
              << ", source_field name: " << source_field->name()
              << ", source_field type: " << source_field->cpp_type_name()
              << ", dest_field name: " << dest_field->name()
              << ", dest_field type: " << dest_field->cpp_type_name();
            *err_str = "Unsupport copy repeated source message field to single dest message field.";
            return false;
          }
        }
      } else {
        auto* msg_val = const_cast<::google::protobuf::Message *>(
          &(source_reflection->GetMessage(*source_leaf_msg, source_field)));
        if (msg_val == nullptr) {
          CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.execute_copy_error")
              << "Execute Current message value nullptr."
              << ", source_field name: " << source_field->name()
              << ", source_field type: " << source_field->cpp_type_name()
              << ", dest_field name: " << dest_field->name()
              << ", dest_field type: " << dest_field->cpp_type_name();
          return false;
        }
        if (dest_field->is_repeated()) {
          dest_reflection->AddMessage(dest_leaf_msg, dest_field)->CopyFrom(*msg_val);
        } else {
          dest_reflection->MutableMessage(dest_leaf_msg, dest_field)->CopyFrom(*msg_val);
        }
      }
      break;
    }
    default:
      CL_LOG_WARNING("SaveProtobuf2ProtobufExecutor", "cp_pb.do_execute_error")
        << "Unsupport dest field type: "
        << ", dest_field name: " << dest_field->name()
        << ", dest_field type: " << dest_field->cpp_type_name();
      *err_str = "Unsupport dest_field type.";
      return false;
  }
  return true;
}

bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                   const std::string& attr_name, google::protobuf::Message *dest_msg,
                                   const std::vector<int> &dest_path, const std::vector<int> &source_path,
                                   bool append) {
  auto* source_msg = const_cast<::google::protobuf::Message*>(
    CommonRecoContextItemAttrHpGetterHandle(result, attr_accessor)
    .GetPtrAttr<::google::protobuf::Message>(attr_name));
  if (!source_msg || !dest_msg) {
    CL_LOG_WARNING("SaveItemAttrToProtobufMessage", "cp_pb.save_item_execute_error")
      << "Execute copy item field to protobuf field error."
      << "source_msg or dest_msg is nullptr. item_attr name: " << attr_name;
    return false;
  }
  google::protobuf::Message *dest_leaf_msg = FollowMessagePath(dest_msg, dest_path);
  google::protobuf::Message *source_leaf_msg = FollowMessagePath(source_msg, source_path);
  if (!source_leaf_msg || !dest_leaf_msg) {
    CL_LOG_WARNING("SaveItemAttrToProtobufMessage", "cp_pb.save_item_execute_error")
      << "Execute copy item field to protobuf field error."
      << "source_leaf_msg or dest_leaf_msg is nullptr. item_attr name: " << attr_name;
    return false;
  }
  std::string err_str;
  bool execute_res = SaveProtobuf2ProtobufExecutor(source_leaf_msg,
    dest_leaf_msg, source_path, dest_path, append, &err_str);
  if (!execute_res) {
    CL_LOG_WARNING("SaveItemAttrToProtobufMessage", "cp_pb.save_item_execute_error")
      << "Do Execute copy item field to protobuf field error."
      << "item_attr name: " << attr_name
      << "error str: " << err_str;
    return false;
  }
  return true;
}

size_t SaveProtobufMessageToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                       const Message &msg, int field_index, bool skip_unset_field) {
  return SaveProtobufMessageWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name, msg,
                                             field_index, skip_unset_field);
}

size_t SaveProtobufMessageToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                     const std::string &attr_name, const Message &msg, int field_index,
                                     bool skip_unset_field) {
  return SaveProtobufMessageWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key),
                                             attr_name, msg, field_index, skip_unset_field);
}

size_t SaveProtobufMessageToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                     const Message &msg, int field_index, bool skip_unset_field) {
  return SaveProtobufMessageWithSetterHandle(CommonRecoContextItemAttrHpSetterHandle(result, attr_accessor),
                                             attr_accessor->name(), msg, field_index, skip_unset_field);
}

bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     google::protobuf::Message *msg, int field_index, bool append,
                                     bool allocated) {
  return SaveToProtobufMessageFromGetterHandle(CommonRecoContextCommonAttrGetterHandle(context), attr_name,
                                               msg, field_index, append, allocated);
}

bool SaveItemAttrToProtobufMessage(ReadableRecoContextInterface *context, uint64 item_key,
                                   const std::string &attr_name, google::protobuf::Message *msg,
                                   int field_index, bool append, bool allocated) {
  return SaveToProtobufMessageFromGetterHandle(CommonRecoContextItemAttrGetterHandle(context, item_key),
                                               attr_name, msg, field_index, append, allocated);
}

bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                   google::protobuf::Message *msg, int field_index, bool append,
                                   bool allocated) {
  return SaveToProtobufMessageFromGetterHandle(CommonRecoContextItemAttrHpGetterHandle(result, attr_accessor),
                                               attr_accessor->name(), msg, field_index, append, allocated);
}

std::unordered_set<std::string> SaveWholeProtobufMessageToCommonAttr(MutableRecoContextInterface *context,
                                                                     const std::string &prefix,
                                                                     const google::protobuf::Message &msg,
                                                                     int max_depth) {
  return SaveWholeProtobufMessageWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), prefix,
                                                  msg, max_depth);
}

std::unordered_set<std::string> SaveWholeProtobufMessageToItemAttr(MutableRecoContextInterface *context,
                                                                   uint64 item_key, const std::string &prefix,
                                                                   const google::protobuf::Message &msg,
                                                                   int max_depth) {
  return SaveWholeProtobufMessageWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key),
                                                  prefix, msg, max_depth);
}

bool ReleaseProtobufMessage(google::protobuf::Message *msg, const std::string &path,
                            const std::vector<int> &msg_path) {
  google::protobuf::Message *leaf_msg = FollowMessagePath(msg, msg_path);
  if (leaf_msg) {
    return ReleaseProtobufMessage(leaf_msg, path, msg_path.back());
  } else {
    return false;
  }
}

bool ReleaseProtobufMessage(google::protobuf::Message *msg, const std::string &path, int field_index) {
  auto *reflection = msg->GetReflection();
  auto *descriptor = msg->GetDescriptor();
  auto *field = descriptor->field(field_index);
  if (!field) {
    CL_LOG(ERROR) << field_index << " is not a valid pb msg field index for path: " << path;
    return false;
  }

  if (field->cpp_type() == FieldDescriptor::CPPTYPE_MESSAGE) {
    if (field->is_repeated()) {
      while (reflection->FieldSize(*msg, field) > 0) {
        reflection->ReleaseLast(msg, field);
      }
    } else {
      reflection->ReleaseMessage(msg, field);
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 1000) << "Failed to release path [" << path << "] with field [" << field_index
                                << "] of unsupported type: " << FieldDescriptor::TypeName(field->type());
  }
  return false;
}

bool ConvertMsgPathToFieldIndexPath(const google::protobuf::Descriptor *descriptor,
                                    absl::string_view msg_path, std::vector<int> *field_path,
                                    const SampleAttrLikeDescriptor **sample_attr_descriptor) {
  if (!descriptor || !field_path) {
    return false;
  }

  for (auto field_name : absl::StrSplit(msg_path, '.', absl::SkipEmpty())) {
    if (!descriptor) {
      LOG(ERROR) << "[" << msg_path << "] prior node is not message, field_name: " << field_name;
      return false;
    }

    std::string str_name = std::string(field_name);
    const FieldDescriptor *field_descriptor = descriptor->FindFieldByName(str_name);
    if (!field_descriptor) {
      LOG(ERROR) << "[" << msg_path << "] field_name: " << field_name << " not found";
      return false;
    }

    field_path->emplace_back(field_descriptor->index());

    descriptor = field_descriptor->message_type();
  }

  if (sample_attr_descriptor) {
    if (!descriptor) {
      LOG(ERROR) << "[" << msg_path << "] last node is not message";
      return false;
    }

    *sample_attr_descriptor = SampleAttrLikeDescriptor::GetByDescriptor(descriptor);
    if (!*sample_attr_descriptor) {
      LOG(ERROR) << "[" << msg_path << "] descriptor: " << descriptor->full_name()
                 << " is not valid SampleAttr-like attr";
      return false;
    }
  }

  return true;
}

const std::string &GetProtoMessageFullName(const google::protobuf::Message *msg) {
  const google::protobuf::Descriptor *descriptor = msg->GetDescriptor();
  return descriptor->full_name();
}

google::protobuf::Message *CreateProtoMessageByFullName(const std::string &full_name) {
  const google::protobuf::Descriptor *descriptor =
      google::protobuf::DescriptorPool::generated_pool()->FindMessageTypeByName(full_name);
  if (descriptor) {
    const google::protobuf::Message *prototype =
        google::protobuf::MessageFactory::generated_factory()->GetPrototype(descriptor);
    if (prototype) {
      return prototype->New();
    }
  }
  return nullptr;
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
