#include "dragon/src/interop/flat_index_holder.h"

#include "serving_base/server_base/kess_client.h"

namespace ks {
namespace platform {

DEFINE_bool(photo_store_fetch_required_attr_only, false,
            "use adaptive attr list instead all attrs calling index");
DEFINE_int32(attrkv_index_init_request_timeout_ms, 5000, "init request timeout in ms");

base::Lock FlatIndexAttrMetaDataHolder::lock_;

bool FetchAttrNameAndTypes(const std::string &photo_store_kconf,
                           std::unordered_map<std::string, int> *attr_types) {
  if (photo_store_kconf.empty()) {
    return false;
  }

  std::shared_ptr<::Json::Value> default_value = std::make_shared<::Json::Value>();
  auto config = ks::infra::KConf().Get(photo_store_kconf, default_value);
  if (!config || !config->Get()) {
    LOG(ERROR) << "Failed read kconf content. path:" << photo_store_kconf;
    return false;
  }
  if (!config->Get()->isObject()) {
    LOG(ERROR) << "Invalid photo store kconf content. path:" << photo_store_kconf;
    return false;
  }

  std::string ps_server_config =
      config->Get()->get(ks::photo_store::PhotoStoreKconfParser::kPsClientConfig, "").asString();

  if (!ps_server_config.empty()) {
    auto ps_client = colossusdb::ps::PsKconfClientManager::Singleton().Get(ps_server_config)->GetClient();
    auto response = ps_client->GetMeta();
    VLOG(1) << "GetAttrList response:" << response.ShortDebugString();
    for (auto &pair : response.attr_name_type()) {
      attr_types->insert(pair);
    }
  } else {
    std::string kess_name =
        config->Get()->get(ks::photo_store::PhotoStoreKconfParser::kItemDocGrpcServiceName, "").asString();
    if (kess_name.empty()) {
      LOG(ERROR)
          << "FlatIndexAttrMetaData init failed! Can not get distributed index server kess name from kconf ["
          << photo_store_kconf << "]. Check if photo store using other method to get kess name";
      return false;
    }

    auto client = base::KessGrpcClient::Singleton()->GetClient2(kess_name, "PRODUCTION");
    ks::kess::rpc::grpc::Options options;
    options.SetTimeout(std::chrono::milliseconds(FLAGS_attrkv_index_init_request_timeout_ms));
    ks::reco::GetAttrListRequest request;
    ks::reco::GetAttrListResponse response;
    auto status =
        client->All()->SelectOne()->Stub<ks::reco::kess::DistributedPhotoInfoService::Stub>()->GetAttrList(
            options, request, &response);
    if (!status.ok()) {
      LOG(ERROR) << "Failed get attr name and types, status::" << status.error_message()
                 << " kess_name: " << kess_name;
      return false;
    }

    VLOG(1) << "GetAttrList response:" << response.ShortDebugString();
    for (auto &pair : response.attrs()) {
      attr_types->insert(pair);
    }
  }

  return true;
}

std::unordered_set<int32> GetRequestDataSetTags(const MutableRecoContextInterface *context,
                                                const std::string &request_data_set_tags_attr) {
  if (request_data_set_tags_attr.size() == 0) {
    return photo_store::DefaultDataSetTags();
  }
  auto request_data_set_tags_str = context->GetStringCommonAttr(request_data_set_tags_attr);
  if (!request_data_set_tags_str.has_value()) {
    return photo_store::DefaultDataSetTags();
  }
  std::unordered_set<int32> request_data_set_tags;
  std::vector<absl::string_view> tag_str_vec =
      absl::StrSplit(request_data_set_tags_str.value(), ",", absl::SkipWhitespace());
  for (auto tag_str : tag_str_vec) {
    int32 tagId;
    if (absl::SimpleAtoi(tag_str, &tagId)) {
      request_data_set_tags.insert(tagId);
    }
  }
  return request_data_set_tags;
}

std::shared_ptr<colossusdb::FieldReplaceKconfHolder> GetFieldReplacerFromPhotoStoreConfig(
    const std::string &kconf_key) {
  std::shared_ptr<::Json::Value> default_value = std::make_shared<::Json::Value>();
  auto kconf_config = ks::infra::KConf().Get(kconf_key, default_value);
  if (!kconf_config || !kconf_config->Get()) {
    LOG(WARNING) << "Invalid photo store config.";
    return nullptr;
  }

  std::shared_ptr<::Json::Value> json_config = kconf_config->Get();
  if (!json_config->isObject()) {
    LOG(WARNING) << "Invalid photo store config.";
    return nullptr;
  }

  ::Json::Value *json_config_value = json_config.get();
  if (!json_config_value) {
    LOG(WARNING) << "Invalid photo store config.";
    return nullptr;
  }
  const ::Json::Value &value =
      json_config_value->get(ks::photo_store::PhotoStoreKconfParser::kAbtestConfigPath, ::Json::Value());

  if (!value) {
    return nullptr;
  }
  if (!value.isString()) {
    LOG(WARNING) << "Invalid " << ks::photo_store::PhotoStoreKconfParser::kAbtestConfigPath << " config.";
    return nullptr;
  }

  VLOG(1) << "Init abconfig done, photo store kconf=" << kconf_key << ", ab kconf = " << value.asString();

  // 字段相关配置在服务初始化生效，因此 watch 参数取 false
  return std::make_shared<colossusdb::FieldReplaceKconfHolder>(value.asString(), false);
}

}  // namespace platform
}  // namespace ks
