#include "dragon/src/interop/shm_custom_kv.h"

#include <utility>
#include <vector>

#include "dragon/src/interop/util.h"
#include "serving_base/util/math.h"

namespace ks {
namespace platform {
namespace interop {
namespace {
template <typename SetterHandle>
size_t SaveCustomKVValueWithSetterHandle(SetterHandle handle, const base::ShmCustomKVReader::Value &attr,
                                         const std::string &attr_name, bool no_overwrite) {
  CommonRecoContextAttrFormatter fmt;
  size_t list_size = 1;
  int64 int_val;
  float float_val;
  std::string str_val;
  if (attr.GetIntValue(&int_val)) {
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_int_val = handle.GetIntAttr(attr_name);
      if (!prev_int_val || *prev_int_val != int_val) {
        LOG(INFO) << "unmatched int value for " << handle.Info() << " of " << attr_name << ": " << int_val
                  << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetIntAttr(attr_name, int_val, no_overwrite);
  } else if (attr.GetFloatValue(&float_val)) {
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_double_val = handle.GetDoubleAttr(attr_name);
      if (!prev_double_val || !base::IsEqual(*prev_double_val, float_val)) {
        LOG(INFO) << "unmatched double value for " << handle.Info() << " of " << attr_name << ": "
                  << float_val << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetDoubleAttr(attr_name, float_val, no_overwrite);
  } else if (attr.GetStringValue(&str_val)) {
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_str_val = handle.GetStringAttr(attr_name);
      if (!prev_str_val || *prev_str_val != str_val) {
        LOG(INFO) << "unmatched string value for " << handle.Info() << " of " << attr_name << ": " << str_val
                  << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetStringAttr(attr_name, str_val, no_overwrite);
  } else if ((list_size = attr.GetIntListSize())) {
    std::vector<int64> int_list(list_size);
    for (int i = 0; i < list_size; i++) {
      CHECK(attr.GetIntListValue(i, &int_list[i]));
    }
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_int_list = handle.GetIntListAttr(attr_name);
      if (!prev_int_list || prev_int_list->size() != int_list.size() ||
          std::equal(int_list.begin(), int_list.end(), prev_int_list->begin())) {
        LOG(INFO) << "unmatched string value for " << handle.Info() << " of " << attr_name << ": "
                  << fmt.ListToString(int_list) << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetIntListAttr(attr_name, std::move(int_list), no_overwrite);
  } else if ((list_size = attr.GetFloatListSize())) {
    std::vector<double> double_list(list_size);
    for (int i = 0; i < list_size; i++) {
      CHECK(attr.GetFloatListValue(i, &float_val));
      double_list[i] = float_val;
    }
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_double_list = handle.GetDoubleListAttr(attr_name);
      if (!prev_double_list || prev_double_list->size() != double_list.size() ||
          std::equal(double_list.begin(), double_list.end(), prev_double_list->begin())) {
        LOG(INFO) << "unmatched string value for " << handle.Info() << " of " << attr_name << ": "
                  << fmt.ListToString(double_list) << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetDoubleListAttr(attr_name, std::move(double_list), no_overwrite);
  } else if ((list_size = attr.GetStringListSize())) {
    std::vector<std::string> str_list(list_size);
    for (int i = 0; i < list_size; i++) {
      CHECK(attr.GetStringListValue(i, &str_list[i]));
    }
    if (VLOG_IS_ON(10) && no_overwrite) {
      auto prev_str_list = handle.GetStringListAttr(attr_name);
      if (!prev_str_list || prev_str_list->size() != str_list.size() ||
          !std::equal(str_list.begin(), str_list.end(), prev_str_list->begin())) {
        LOG(INFO) << "unmatched string value for " << handle.Info() << " of " << attr_name << ": "
                  << fmt.ListToString(str_list) << " vs " << fmt.AttrToString(handle, attr_name);
      }
    }
    handle.SetStringListAttr(attr_name, std::move(str_list), no_overwrite);
  } else {
    CL_LOG_EVERY_N(WARNING, 1000) << "failed to get value from CustomKVValue " << attr_name << " of "
                                  << attr.type;
    if (VLOG_IS_ON(10) && no_overwrite) {
      if (handle.HasAttr(attr_name)) {
        LOG(INFO) << "unmatched string value for " << handle.Info() << " of " << attr_name << ": NULL vs "
                  << fmt.AttrToString(handle, attr_name);
      }
    }
    return 0;
  }

  return list_size;
}
}  // namespace

size_t SaveCustomKVValueToCommonAttr(MutableRecoContextInterface *context,
                                     const base::ShmCustomKVReader::Value &attr, const std::string &attr_name,
                                     bool no_overwrite) {
  return SaveCustomKVValueWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr, attr_name,
                                           no_overwrite);
}

size_t SaveCustomKVValueToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                   const base::ShmCustomKVReader::Value &attr, const std::string &attr_name,
                                   bool no_overwrite) {
  return SaveCustomKVValueWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key), attr,
                                           attr_name, no_overwrite);
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
