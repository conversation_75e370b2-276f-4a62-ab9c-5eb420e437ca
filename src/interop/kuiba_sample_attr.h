#pragma once

#include "dragon/src/core/common_reco_context_interface.h"
#include "learning/kuiba/proto/common_sample_log.pb.h"

namespace ks {
namespace platform {
namespace interop {

::kuiba::SampleAttr *BuildSampleAttrFromCommonAttr(
    ReadableRecoContextInterface *context, absl::string_view attr_name,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename = "",
    bool readonly = false);

bool LoadSampleAttrFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                  ::kuiba::SampleAttr *attr, absl::string_view rename = "",
                                  bool readonly = false);

::kuiba::SampleAttr *BuildSampleAttrFromItemAttr(
    ReadableRecoContextInterface *context, uint64 item_key, absl::string_view attr_name,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename = "");
::kuiba::SampleAttr *BuildSampleAttrFromItemAttr(
    const CommonRecoResult &result, ItemAttr *attr_accessor,
    ::google::protobuf::RepeatedPtrField<::kuiba::SampleAttr> *attrs, absl::string_view rename = "");

bool LoadSampleAttrFromItemAttr(ReadableRecoContextInterface *context, uint64 item_key,
                                absl::string_view attr_name, ::kuiba::SampleAttr *attr,
                                absl::string_view rename = "");
bool LoadSampleAttrFromItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                ::kuiba::SampleAttr *attr, absl::string_view rename = "");

size_t SaveSampleAttrToCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                                  const kuiba::SampleAttr &attr, bool no_overwrite = false);
size_t SaveSampleAttrToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                absl::string_view attr_name, const kuiba::SampleAttr &attr,
                                bool no_overwrite = false);
size_t SaveSampleAttrToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                const kuiba::SampleAttr &attr, bool no_overwrite = false);

size_t AppendSampleAttrToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                  absl::string_view attr_name, const kuiba::SampleAttr &attr);

size_t AppendSampleAttrToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                  const kuiba::SampleAttr &attr);

inline static size_t SaveSampleAttrToCommonAttr(MutableRecoContextInterface *context,
                                                const kuiba::SampleAttr &attr, bool no_overwrite = false) {
  return SaveSampleAttrToCommonAttr(context, attr.name(), attr, no_overwrite);
}

inline static size_t SaveSampleAttrToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                              const kuiba::SampleAttr &attr, bool no_overwrite = false) {
  return SaveSampleAttrToItemAttr(context, item_key, attr.name(), attr, no_overwrite);
}

template <typename SetterHandle>
size_t SaveSampleAttrWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                      const kuiba::SampleAttr &attr, bool no_overwrite);

template <typename SetterHandle>
size_t AppendSampleAttrWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                        const kuiba::SampleAttr &attr);

}  // namespace interop
}  // namespace platform
}  // namespace ks
