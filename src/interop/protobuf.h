#pragma once

#include <google/protobuf/descriptor.h>
#include <google/protobuf/message.h>
#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/core/common_reco_context_interface.h"

#include "dragon/src/interop/general_sample_attr.h"

namespace ks {
namespace platform {
namespace interop {

bool ConvertMsgPathToFieldIndexPath(const google::protobuf::Descriptor *descriptor,
                                    absl::string_view msg_path, std::vector<int> *field_path,
                                    const SampleAttrLikeDescriptor **sample_attr_descriptor = nullptr);

// for flatten message
size_t SaveProtobufMessageToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                       const google::protobuf::Message &msg, int field_index,
                                       bool skip_unset_field = false);
size_t SaveProtobufMessageToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                     const std::string &attr_name, const google::protobuf::Message &msg,
                                     int field_index, bool skip_unset_field = false);
size_t SaveProtobufMessageToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                     const google::protobuf::Message &msg, int field_index,
                                     bool skip_unset_field = false);

bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     google::protobuf::Message *msg, int field_index, bool append = false,
                                     bool allocated = false);
bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     const std::vector<int> &source_path, google::protobuf::Message *dest_msg,
                                     const std::vector<int> &dest_path, bool append = false);
bool SaveItemAttrToProtobufMessage(ReadableRecoContextInterface *context, uint64 item_key,
                                   const std::string &attr_name, google::protobuf::Message *msg,
                                   int field_index, bool append = false, bool allocated = false);
bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                   google::protobuf::Message *msg, int field_index, bool append = false,
                                   bool allocated = false);

// for nested message
size_t SaveProtobufMessageToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                       const google::protobuf::Message &msg, const std::vector<int> &msg_path,
                                       bool skip_unset_field = false,
                                       const std::vector<int> *repeat_limit = nullptr,
                                       bool repeat_align = false,
                                       const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                       const std::string &sample_attr_name = "",
                                       int64 sample_attr_name_value = -1);
size_t SaveProtobufMessageToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                     const std::string &attr_name, const google::protobuf::Message &msg,
                                     const std::vector<int> &msg_path, bool skip_unset_field = false,
                                     const std::vector<int> *repeat_limit = nullptr,
                                     bool repeat_align = false,
                                     const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                     const std::string &sample_attr_name = "",
                                     int64 sample_attr_name_value = -1);
size_t SaveProtobufMessageToItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                     const google::protobuf::Message &msg, const std::vector<int> &msg_path,
                                     bool skip_unset_field = false,
                                     const std::vector<int> *repeat_limit = nullptr,
                                     bool repeat_align = false,
                                     const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                     const std::string &sample_attr_name = "",
                                     int64 sample_attr_name_value = -1);
size_t AppendProtobufMessageToListCommonAttr(MutableRecoContextInterface *context,
                                             const std::string &attr_name,
                                             const google::protobuf::Message &msg,
                                             const std::vector<int> &msg_path, bool skip_unset_field = false);

using AttrValueVariant =
    absl::variant<absl::nullopt_t, int64, double, std::string, std::vector<int64>, std::vector<double>,
                  std::vector<std::string>, const ::google::protobuf::Message *>;

const char *GetAttrValueVariantTypeName(const AttrValueVariant &variant);

void SaveProtobufMessageToAttrValueVariant(AttrValueVariant *variant, const google::protobuf::Message &msg,
                                           const std::vector<int> &msg_path, bool skip_unset_field = false,
                                           const std::vector<int> *repeat_limit = nullptr,
                                           bool repeat_align = false,
                                           const SampleAttrLikeDescriptor *sample_attr_descriptor = nullptr,
                                           const std::string &sample_attr_name = "",
                                           int64 sample_attr_name_value = -1);

bool SaveCommonAttrToProtobufMessage(ReadableRecoContextInterface *context, const std::string &attr_name,
                                     google::protobuf::Message *msg, const std::vector<int> &msg_path,
                                     bool append = false, bool allocated = false);
bool SaveItemAttrToProtobufMessage(ReadableRecoContextInterface *context, uint64 item_key,
                                   const std::string &attr_name, google::protobuf::Message *msg,
                                   const std::vector<int> &msg_path, bool append = false,
                                   bool allocated = false);
bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result, ItemAttr *attr_accessor,
                                   google::protobuf::Message *msg, const std::vector<int> &msg_path,
                                   bool append = false, bool allocated = false);

bool ProcessIntSourceField(std::vector<int64> *source_values, google::protobuf::Message *source_leaf_msg,
  google::protobuf::Reflection *source_reflection, google::protobuf::Descriptor *source_descriptor,
  google::protobuf::FieldDescriptor *source_field);

bool ProcessDoubleSourceField(std::vector<double> *source_values, google::protobuf::Message *source_leaf_msg,
  google::protobuf::Reflection *source_reflection, google::protobuf::Descriptor *source_descriptor,
  google::protobuf::FieldDescriptor *source_field);

bool ProcessStringSourceField(std::vector<std::string> *source_values,
  google::protobuf::Message *source_leaf_msg,
  google::protobuf::Reflection *source_reflection,
  google::protobuf::Descriptor *source_descriptor,
  google::protobuf::FieldDescriptor *source_field);

bool ProcessMessageSourceField(std::vector<::google::protobuf::Message*> *source_values,
  google::protobuf::Message *source_leaf_msg,
  google::protobuf::Reflection *source_reflection,
  google::protobuf::Descriptor *source_descriptor,
  google::protobuf::FieldDescriptor *source_field);

bool SaveProtobuf2ProtobufExecutor(google::protobuf::Message *source_leaf_msg,
  google::protobuf::Message *dest_leaf_msg,
  const std::vector<int> &source_path,
  const std::vector<int> &dest_path,
  bool append, std::string* err_str);

bool SaveItemAttrToProtobufMessage(const CommonRecoResult &result,
  ItemAttr *attr_accessor, const std::string& attr_name,
  google::protobuf::Message *dest_msg, const std::vector<int> &source_path,
  const std::vector<int> &dest_path, bool append = false);

// for whole message
std::unordered_set<std::string> SaveWholeProtobufMessageToCommonAttr(MutableRecoContextInterface *context,
                                                                     const std::string &prefix,
                                                                     const google::protobuf::Message &msg,
                                                                     int max_depth = -1);
std::unordered_set<std::string> SaveWholeProtobufMessageToItemAttr(MutableRecoContextInterface *context,
                                                                   uint64 item_key, const std::string &prefix,
                                                                   const google::protobuf::Message &msg,
                                                                   int max_depth = -1);
bool ReleaseProtobufMessage(google::protobuf::Message *msg, const std::string &path,
                            const std::vector<int> &msg_path);

bool ReleaseProtobufMessage(google::protobuf::Message *msg, const std::string &path, int field_index);

const std::string &GetProtoMessageFullName(const google::protobuf::Message *msg);

google::protobuf::Message *CreateProtoMessageByFullName(const std::string &full_name);
}  // namespace interop
}  // namespace platform
}  // namespace ks
