#pragma once

#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/core/common_reco_context_interface.h"
#include "json/json.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {
namespace interop {

size_t SaveJsonValueToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                 const Json::Value &json_value, bool append = false);

size_t SaveJsonValueToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                 const Json::Value &json_value, const std::vector<std::string> &json_path,
                                 bool append = false, const base::Json *default_value = nullptr);

size_t SaveJsonValueToItemAttr(MutableRecoContextInterface *context, uint64_t item_key,
                               const std::string &attr_name, const Json::Value &json_value);

size_t SaveJsonValueToItemAttr(MutableRecoContextInterface *context, uint64_t item_key,
                               const std::string &attr_name, const Json::Value &json_value,
                               const std::vector<std::string> &json_path);

size_t SaveJsonValueToItemAttr(CommonRecoResult result, ItemAttr *item_attr, const Json::Value &json_value);

size_t SaveJsonValueToItemAttr(CommonRecoResult result, ItemAttr *item_attr, const Json::Value &json_value,
                               const std::vector<std::string> &json_path,
                               const base::Json *default_value = nullptr);

Json::Value ExtractTargetField(const Json::Value &json_value, const std::vector<std::string> &json_path,
                               size_t index = 0, const base::Json *default_value = nullptr);

}  // namespace interop
}  // namespace platform
}  // namespace ks
