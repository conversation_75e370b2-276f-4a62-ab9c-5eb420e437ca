#include "dragon/src/interop/protobuf.h"

#include <gtest/gtest.h>

#include <vector>

#include "ks/ksib_reco/ksib_proto/proto/ksib/reco/user/user_info_service.pb.h"

namespace ks {
namespace platform {
namespace interop {

TEST(ConvertMsgPathToFieldIndexPath, KsibReaderInfo) {
  std::vector<int> field_path;
  const SampleAttrLikeDescriptor *sample_attr_descriptor;
  EXPECT_TRUE(ConvertMsgPathToFieldIndexPath(ksib::reco::ReaderInfo::descriptor(), "common_attrs",
                                             &field_path, &sample_attr_descriptor));
  EXPECT_EQ(field_path,
            std::vector<int>{ksib::reco::ReaderInfo::descriptor()->FindFieldByName("common_attrs")->index()});
  EXPECT_EQ(sample_attr_descriptor,
            SampleAttrLikeDescriptor::GetByDescriptor(::kuiba::SampleAttr::descriptor()));
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
