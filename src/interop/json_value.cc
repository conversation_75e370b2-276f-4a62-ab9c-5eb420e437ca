#include "dragon/src/interop/json_value.h"

#include <utility>
#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {
namespace interop {

namespace {

template <typename SetterHandle>
size_t SaveJsonValueWithSetterHandle(SetterHandle handle, const std::string &attr_name,
                                     const Json::Value &json_value, bool append = false) {
  int count = 1;
  if (json_value.isIntegral()) {
    if (!append) {
      handle.SetIntAttr(attr_name, json_value.asInt64());
    } else {
      handle.AppendIntListAttr(attr_name, json_value.asInt64());
    }
  } else if (json_value.isDouble()) {
    if (!append) {
      handle.SetDoubleAttr(attr_name, json_value.asDouble());
    } else {
      handle.AppendDoubleListAttr(attr_name, json_value.asDouble());
    }
  } else if (json_value.isString()) {
    if (!append) {
      handle.SetStringAttr(attr_name, json_value.asString());
    } else {
      handle.AppendStringListAttr(attr_name, json_value.asString());
    }
  } else if (json_value.isBool()) {
    if (!append) {
      handle.SetIntAttr(attr_name, json_value.asBool());
    } else {
      handle.AppendIntListAttr(attr_name, json_value.asBool());
    }
  } else if (json_value.isArray()) {
    if (json_value.empty()) {
      VLOG(100) << "ignored save json value to attr " << attr_name << ": empty array";
      return 0;
    }
    count = json_value.size();
    if (json_value[0].isIntegral()) {
      std::vector<int64> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        if (!append) {
          values.emplace_back(val.asInt64());
        } else {
          handle.AppendIntListAttr(attr_name, val.asInt64());
        }
      }
      if (!append) {
        handle.SetIntListAttr(attr_name, std::move(values));
      }
    } else if (json_value[0].isBool()) {
      std::vector<int64> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        if (!append) {
          values.emplace_back(val.asBool());
        } else {
          handle.AppendIntListAttr(attr_name, val.asBool());
        }
      }
      if (!append) {
        handle.SetIntListAttr(attr_name, std::move(values));
      }
    } else if (json_value[0].isDouble()) {
      std::vector<double> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        if (!append) {
          values.emplace_back(val.asDouble());
        } else {
          handle.AppendDoubleListAttr(attr_name, val.asDouble());
        }
      }
      if (!append) {
        handle.SetDoubleListAttr(attr_name, std::move(values));
      }
    } else if (json_value[0].isString()) {
      std::vector<std::string> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        if (!append) {
          values.emplace_back(val.asString());
        } else {
          handle.AppendStringListAttr(attr_name, val.asString());
        }
      }
      if (!append) {
        handle.SetStringListAttr(attr_name, std::move(values));
      }
    } else {
      CL_LOG(ERROR) << "cannot save json value to attr " << attr_name << " with unsupported value type,"
                    << " should be: int|double|string|int_list|double_list|string_list";
      return 0;
    }
  } else {
    CL_LOG(ERROR) << "cannot save json value to attr " << attr_name << " with unsupported value type,"
                  << " should be: int|double|string|int_list|double_list|string_list";
    return 0;
  }
  return count;
}

}  // namespace

size_t SaveJsonValueToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                 const Json::Value &json_value, bool append) {
  return SaveJsonValueWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name,
                                       json_value, append);
}

size_t SaveJsonValueToCommonAttr(MutableRecoContextInterface *context, const std::string &attr_name,
                                 const Json::Value &json_value, const std::vector<std::string> &json_path,
                                 bool append, const base::Json *default_value) {
  Json::Value value = ExtractTargetField(json_value, json_path, 0, default_value);
  if (value.isNull()) {
    return 0;
  }
  return SaveJsonValueToCommonAttr(context, attr_name, value, append);
}

size_t SaveJsonValueToItemAttr(MutableRecoContextInterface *context, uint64_t item_key,
                               const std::string &attr_name, const Json::Value &json_value) {
  return SaveJsonValueWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key), attr_name,
                                       json_value);
}

size_t SaveJsonValueToItemAttr(MutableRecoContextInterface *context, uint64_t item_key,
                               const std::string &attr_name, const Json::Value &json_value,
                               const std::vector<std::string> &json_path, const base::Json *default_value) {
  Json::Value value = ExtractTargetField(json_value, json_path, 0, default_value);
  if (value.isNull()) {
    return 0;
  }
  return SaveJsonValueToItemAttr(context, item_key, attr_name, value);
}

size_t SaveJsonValueToItemAttr(CommonRecoResult result, ItemAttr *item_attr, const Json::Value &json_value) {
  int count = 1;
  if (json_value.isIntegral()) {
    result.SetIntAttr(item_attr, json_value.asInt64());
  } else if (json_value.isDouble()) {
    result.SetDoubleAttr(item_attr, json_value.asDouble());
  } else if (json_value.isString()) {
    result.SetStringAttr(item_attr, json_value.asString());
  } else if (json_value.isBool()) {
    result.SetIntAttr(item_attr, json_value.asBool());
  } else if (json_value.isArray()) {
    if (json_value.empty()) {
      VLOG(100) << "ignored save json value to attr " << item_attr->name() << ": empty array";
      return 0;
    }
    count = json_value.size();
    if (json_value[0].isIntegral()) {
      std::vector<int64> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        values.emplace_back(val.asInt64());
      }
      result.SetIntListAttr(item_attr, std::move(values));
    } else if (json_value[0].isBool()) {
      std::vector<int64> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        values.emplace_back(val.asBool());
      }
      result.SetIntListAttr(item_attr, std::move(values));
    } else if (json_value[0].isDouble()) {
      std::vector<double> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        values.emplace_back(val.asDouble());
      }
      result.SetDoubleListAttr(item_attr, std::move(values));
    } else if (json_value[0].isString()) {
      std::vector<std::string> values;
      values.reserve(json_value.size());
      for (const auto &val : json_value) {
        values.emplace_back(val.asString());
      }
      result.SetStringListAttr(item_attr, std::move(values));
    } else {
      CL_LOG(ERROR) << "cannot save json value to attr " << item_attr->name()
                    << " with unsupported value type,"
                    << " should be: int|double|string|int list|double list|string list";
      return 0;
    }
  } else {
    CL_LOG(ERROR) << "cannot save json value to attr " << item_attr->name() << " with unsupported value type,"
                  << " should be: int|double|string|int list|double list|string list";
    return 0;
  }
  return count;
}

size_t SaveJsonValueToItemAttr(CommonRecoResult result, ItemAttr *item_attr, const Json::Value &json_value,
                               const std::vector<std::string> &json_path, const base::Json *default_value) {
  Json::Value value = ExtractTargetField(json_value, json_path, 0, default_value);
  if (value.isNull()) {
    return 0;
  }
  return SaveJsonValueToItemAttr(result, item_attr, value);
}

Json::Value ExtractTargetField(const Json::Value &json_value, const std::vector<std::string> &json_path,
                               size_t index, const base::Json *default_value) {
  if (index >= json_path.size() || !json_value.isObject() && !json_value.isArray()) {
    return json_value;
  }

  const std::string &field = json_path[index];

  if (field == "[]") {
    if (!json_value.isArray()) {
      return Json::Value(Json::nullValue);
    }
    Json::Value result(Json::arrayValue);
    for (const auto &item : json_value) {
      Json::Value subResult = ExtractTargetField(item, json_path, index + 1, default_value);
      if (subResult.isObject() || subResult.isArray()) {
        for (const auto &sr : subResult) {
          result.append(sr);
        }
      } else if (!subResult.isNull()) {
        result.append(subResult);
      }
    }
    return result;
  } else {
    if (!json_value.isObject()) {
      return Json::Value(Json::nullValue);
    }
    if (!json_value.isMember(field)) {
      if (default_value) {
        if (default_value->IsInteger()) {
          return Json::Value(static_cast<Json::Int64>(default_value->IntValue(0L)));
        } else if (default_value->IsDouble()) {
          return Json::Value(default_value->FloatValue(0.0));
        } else if (default_value->IsString()) {
          return Json::Value(default_value->StringValue());
        } else if (default_value->IsBoolean()) {
          return Json::Value(default_value->BooleanValue(false));
        } else {
          return Json::Value(Json::nullValue);
        }
      } else {
        return Json::Value(Json::nullValue);
      }
    }
    return ExtractTargetField(json_value[field], json_path, index + 1, default_value);
  }
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
