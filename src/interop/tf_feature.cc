#include "dragon/src/interop/tf_feature.h"

#include <utility>
#include <vector>

#include "dragon/src/interop/util.h"

namespace ks {
namespace platform {
namespace interop {
namespace {

class TfFeatureLoader
    : public CommonRecoContextOtherAttrLoader<TfFeatureLoader, ::ks::reco::feature_pipe::Feature> {
 public:
  using CommonRecoContextOtherAttrLoader<TfFeatureLoader,
                                         ::ks::reco::feature_pipe::Feature>::CommonRecoContextOtherAttrLoader;
  inline void FromInt(int64 val) {
    ptr()->set_is_list(false);
    ptr()->mutable_int64_list()->add_value(val);
  }

  inline void FromDouble(double val) {
    ptr()->set_is_list(false);
    ptr()->mutable_float_list()->add_value(val);
  }

  inline void FromString(absl::string_view val) {
    ptr()->set_is_list(false);
    ptr()->mutable_bytes_list()->add_value(val.data(), val.size());
  }

  inline void FromIntList(absl::Span<const int64> val) {
    ptr()->set_is_list(true);
    for (auto v : val) {
      ptr()->mutable_int64_list()->add_value(v);
    }
  }

  inline void FromDoubleList(absl::Span<const double> val) {
    ptr()->set_is_list(true);
    for (auto v : val) {
      ptr()->mutable_float_list()->add_value(v);
    }
  }

  inline void FromStringList(const std::vector<absl::string_view> &val) {
    ptr()->set_is_list(true);
    for (auto v : val) {
      ptr()->mutable_bytes_list()->add_value(v.data(), v.size());
    }
  }
};

template <typename SetterHandle>
size_t SaveTfFeatureWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                     const ::ks::reco::feature_pipe::Feature &feature) {
  size_t list_size = 0;
  if (feature.is_list()) {
    if (feature.has_float_list()) {
      list_size = feature.float_list().value_size();
      std::vector<double> double_list;
      double_list.reserve(list_size);
      for (float v : feature.float_list().value()) {
        double_list.emplace_back((double)v);
      }
      handle.SetDoubleListAttr(attr_name, std::move(double_list));
    } else if (feature.has_int64_list()) {
      list_size = feature.int64_list().value_size();
      std::vector<int64> int_list;
      int_list.reserve(list_size);
      for (int64 v : feature.int64_list().value()) {
        int_list.emplace_back(v);
      }
      handle.SetIntListAttr(attr_name, std::move(int_list));
    } else if (feature.has_bytes_list()) {
      list_size = feature.bytes_list().value_size();
      std::vector<std::string> str_list;
      str_list.reserve(list_size);
      for (const std::string &v : feature.bytes_list().value()) {
        str_list.emplace_back(v);
      }
      handle.SetStringListAttr(attr_name, std::move(str_list));
    } else {
      CL_LOG_EVERY_N(WARNING, 1000) << "empty attr detected: " << attr_name;
    }
  } else {
    if (feature.has_float_list() && feature.float_list().value_size() > 0) {
      list_size = 1;
      handle.SetDoubleAttr(attr_name, feature.float_list().value(0));
    } else if (feature.has_int64_list() && feature.int64_list().value_size() > 0) {
      list_size = 1;
      handle.SetIntAttr(attr_name, feature.int64_list().value(0));
    } else if (feature.has_bytes_list() && feature.bytes_list().value_size() > 0) {
      list_size = 1;
      handle.SetStringAttr(attr_name, feature.bytes_list().value(0));
    } else {
      CL_LOG_EVERY_N(WARNING, 1000) << "empty attr detected: " << attr_name;
    }
  }
  return list_size;
}
}  // namespace

bool LoadTfFeatureFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                 ::ks::reco::feature_pipe::Feature *feature) {
  if (!context->HasCommonAttr(attr_name) || !feature) {
    return false;
  }
  TfFeatureLoader(feature).LoadFromCommonAttr(context, attr_name);
  return true;
}

bool LoadTfFeatureListFromItemAttr(ReadableRecoContextInterface *context,
                                   const std::vector<uint64> &item_keys, absl::string_view attr_name,
                                   ::ks::reco::feature_pipe::FeatureList *feature_list) {
  if (!context->HasItemAttr(attr_name) || !feature_list) {
    return false;
  }
  for (auto const &item_key : item_keys) {
    TfFeatureLoader(feature_list->add_feature()).LoadFromItemAttr(context, item_key, attr_name);
  }
  return true;
}

size_t SaveTfFeatureToCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                                 const ::ks::reco::feature_pipe::Feature &feature) {
  return SaveTfFeatureWithSetterHandle(CommonRecoContextCommonAttrSetterHandle(context), attr_name, feature);
}

size_t SaveTfFeatureToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                               absl::string_view attr_name,
                               const ::ks::reco::feature_pipe::Feature &feature) {
  return SaveTfFeatureWithSetterHandle(CommonRecoContextItemAttrSetterHandle(context, item_key), attr_name,
                                       feature);
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
