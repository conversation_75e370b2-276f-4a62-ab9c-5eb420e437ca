#pragma once

#include <string>
#include <vector>

#include "dragon/src/core/common_reco_context_interface.h"
#include "ks/reco_proto/feature_pipe/tf_example.pb.h"

namespace ks {
namespace platform {
namespace interop {
bool LoadTfFeatureFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                 ::ks::reco::feature_pipe::Feature *feature);

inline bool LoadTfFeatureFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                        ::ks::reco::feature_pipe::Features *features) {
  return LoadTfFeatureFromCommonAttr(context, attr_name,
                                     &(*features->mutable_feature())[std::string(attr_name)]);
}

bool LoadTfFeatureListFromItemAttr(ReadableRecoContextInterface *context,
                                   const std::vector<uint64> &item_keys, absl::string_view attr_name,
                                   ::ks::reco::feature_pipe::FeatureList *feature_list);

inline bool LoadTfFeatureListFromItemAttr(ReadableRecoContextInterface *context,
                                          const std::vector<uint64> &item_keys, absl::string_view attr_name,
                                          ::ks::reco::feature_pipe::FeatureLists *feature_lists) {
  return LoadTfFeatureListFromItemAttr(context, item_keys, attr_name,
                                       &(*feature_lists->mutable_feature_list())[std::string(attr_name)]);
}

size_t SaveTfFeatureToCommonAttr(MutableRecoContextInterface *context, absl::string_view attr_name,
                                 const ::ks::reco::feature_pipe::Feature &feature);
size_t SaveTfFeatureToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                               absl::string_view attr_name, const ::ks::reco::feature_pipe::Feature &feature);

}  // namespace interop
}  // namespace platform
}  // namespace ks
