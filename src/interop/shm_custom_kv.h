#pragma once

#include <string>

#include "dragon/src/core/common_reco_context_interface.h"
#include "serving_base/util/shm_custom_kv.h"

namespace ks {
namespace platform {
namespace interop {
size_t SaveCustomKVValueToCommonAttr(MutableRecoContextInterface *context,
                                     const base::ShmCustomKVReader::Value &attr, const std::string &attr_name,
                                     bool no_overwrite = false);
size_t SaveCustomKVValueToItemAttr(MutableRecoContextInterface *context, uint64 item_key,
                                   const base::ShmCustomKVReader::Value &attr, const std::string &attr_name,
                                   bool no_overwrite = false);
}  // namespace interop
}  // namespace platform
}  // namespace ks
