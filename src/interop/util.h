#pragma once

#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "third_party/abseil/absl/strings/str_join.h"
#include "third_party/abseil/absl/strings/str_split.h"
#include "third_party/abseil/absl/strings/string_view.h"
#include "third_party/abseil/absl/types/optional.h"
#include "third_party/abseil/absl/types/span.h"

namespace ks {
namespace platform {
namespace interop {

class CommonRecoContextCommonAttrGetterHandle {
 public:
  explicit CommonRecoContextCommonAttrGetterHandle(const ReadableRecoContextInterface *context)
      : context_(context) {}

  std::string Info() const {
    return "common_attr";
  }
  inline auto GetAttrType(absl::string_view attr_name) const {
    return context_->GetCommonAttrType(attr_name);
  }
  inline bool HasAttr(absl::string_view attr_name) const {
    return context_->HasCommonAttr(attr_name);
  }
  inline auto GetIntAttr(absl::string_view attr_name) const {
    return context_->GetIntCommonAttr(attr_name);
  }
  inline auto GetDoubleAttr(absl::string_view attr_name) const {
    return context_->GetDoubleCommonAttr(attr_name);
  }
  inline auto GetStringAttr(absl::string_view attr_name) const {
    return context_->GetStringCommonAttr(attr_name);
  }
  inline auto GetIntListAttr(absl::string_view attr_name) const {
    return context_->GetIntListCommonAttr(attr_name);
  }
  inline auto GetFloatListAttr(absl::string_view attr_name) const {
    return context_->GetPtrCommonAttr<const std::vector<float>>(attr_name);
  }
  inline auto GetDoubleListAttr(absl::string_view attr_name) const {
    return context_->GetDoubleListCommonAttr(attr_name);
  }
  inline auto GetStringListAttr(absl::string_view attr_name) const {
    return context_->GetStringListCommonAttr(attr_name);
  }
  template <typename T>
  inline auto GetPtrAttr(absl::string_view attr_name) const {
    return context_->GetPtrCommonAttr<T>(attr_name);
  }

 private:
  const ReadableRecoContextInterface *const context_;
};

class CommonRecoContextItemAttrGetterHandle {
 public:
  CommonRecoContextItemAttrGetterHandle(const ReadableRecoContextInterface *context, uint64 item_key)
      : context_(context), item_key_(item_key) {}

  std::string Info() const {
    return "item_attr(" + std::to_string(item_key_) + ")";
  }
  inline auto GetAttrType(absl::string_view attr_name) const {
    return context_->GetItemAttrType(attr_name);
  }
  inline bool HasAttr(absl::string_view attr_name) const {
    return context_->HasItemAttr(item_key_, attr_name);
  }
  inline auto GetIntAttr(absl::string_view attr_name) const {
    return context_->GetIntItemAttr(item_key_, attr_name);
  }
  inline auto GetDoubleAttr(absl::string_view attr_name) const {
    return context_->GetDoubleItemAttr(item_key_, attr_name);
  }
  inline auto GetStringAttr(absl::string_view attr_name) const {
    return context_->GetStringItemAttr(item_key_, attr_name);
  }
  inline auto GetIntListAttr(absl::string_view attr_name) const {
    return context_->GetIntListItemAttr(item_key_, attr_name);
  }
  inline auto GetFloatListAttr(absl::string_view attr_name) const {
    return context_->GetPtrItemAttr<const std::vector<float>>(item_key_, attr_name);
  }
  inline auto GetDoubleListAttr(absl::string_view attr_name) const {
    return context_->GetDoubleListItemAttr(item_key_, attr_name);
  }
  inline auto GetStringListAttr(absl::string_view attr_name) const {
    return context_->GetStringListItemAttr(item_key_, attr_name);
  }
  template <typename T>
  inline auto GetPtrAttr(absl::string_view attr_name) const {
    return context_->GetPtrItemAttr<T>(item_key_, attr_name);
  }

 private:
  const ReadableRecoContextInterface *const context_;
  const uint64_t item_key_;
};

/**
 * "Hp" stands for "High Performance"
 */
class CommonRecoContextItemAttrHpGetterHandle {
 public:
  CommonRecoContextItemAttrHpGetterHandle(const CommonRecoResult &result, ItemAttr *attr_accessor)
      : result_(result), attr_accessor_(CHECK_NOTNULL(attr_accessor)) {}

  std::string Info() const {
    return "item_attr(" + std::to_string(result_.item_key) + ")";
  }
  inline auto GetAttrType(absl::string_view attr_name) const {
    return attr_accessor_->value_type;
  }
  inline bool HasAttr(absl::string_view attr_name) const {
    return result_.HasAttr(attr_accessor_);
  }
  inline auto GetIntAttr(absl::string_view attr_name) const {
    return result_.GetIntAttr(attr_accessor_);
  }
  inline auto GetDoubleAttr(absl::string_view attr_name) const {
    return result_.GetDoubleAttr(attr_accessor_);
  }
  inline auto GetStringAttr(absl::string_view attr_name) const {
    return result_.GetStringAttr(attr_accessor_);
  }
  inline auto GetIntListAttr(absl::string_view attr_name) const {
    return result_.GetIntListAttr(attr_accessor_);
  }
  inline auto GetFloatListAttr(absl::string_view attr_name) const {
    return result_.GetPtrAttr<const std::vector<float>>(attr_accessor_);
  }
  inline auto GetDoubleListAttr(absl::string_view attr_name) const {
    return result_.GetDoubleListAttr(attr_accessor_);
  }
  inline auto GetStringListAttr(absl::string_view attr_name) const {
    return result_.GetStringListAttr(attr_accessor_);
  }
  template <typename T>
  inline auto GetPtrAttr(absl::string_view attr_name) const {
    return result_.GetPtrAttr<T>(attr_accessor_);
  }

 private:
  ItemAttr *attr_accessor_ = nullptr;
  const CommonRecoResult result_;
};

class CommonRecoContextCommonAttrSetterHandle : public CommonRecoContextCommonAttrGetterHandle {
 public:
  explicit CommonRecoContextCommonAttrSetterHandle(MutableRecoContextInterface *context)
      : CommonRecoContextCommonAttrGetterHandle(context), context_(context) {}

  inline void SetIntAttr(absl::string_view attr_name, int64 value, bool no_overwrite = false) {
    context_->SetIntCommonAttr(attr_name, value, no_overwrite);
  }
  inline void SetDoubleAttr(absl::string_view attr_name, double value, bool no_overwrite = false) {
    context_->SetDoubleCommonAttr(attr_name, value, no_overwrite);
  }
  inline void SetStringAttr(absl::string_view attr_name, std::string value, bool no_overwrite = false) {
    context_->SetStringCommonAttr(attr_name, std::move(value), no_overwrite);
  }
  inline void ResetIntListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetIntListCommonAttr(attr_name, capacity);
  }
  inline void ResetDoubleListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetDoubleListCommonAttr(attr_name, capacity);
  }
  inline void ResetStringListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetStringListCommonAttr(attr_name, capacity);
  }
  inline void AppendIntListAttr(absl::string_view attr_name, int64 value) {
    context_->AppendIntListCommonAttr(attr_name, value);
  }
  inline void AppendDoubleListAttr(absl::string_view attr_name, double value) {
    context_->AppendDoubleListCommonAttr(attr_name, value);
  }
  inline void AppendStringListAttr(absl::string_view attr_name, std::string value) {
    context_->AppendStringListCommonAttr(attr_name, std::move(value));
  }
  inline void SetIntListAttr(absl::string_view attr_name, std::vector<int64> &&value,
                             bool no_overwrite = false) {
    context_->SetIntListCommonAttr(attr_name, std::move(value), no_overwrite);
  }
  inline void SetDoubleListAttr(absl::string_view attr_name, std::vector<double> &&value,
                                bool no_overwrite = false) {
    context_->SetDoubleListCommonAttr(attr_name, std::move(value), no_overwrite);
  }
  inline void SetStringListAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                bool no_overwrite = false) {
    context_->SetStringListCommonAttr(attr_name, std::move(value), no_overwrite);
  }
  template <typename Ptr>
  inline void SetPtrAttr(absl::string_view attr_name, Ptr &&value) {
    context_->SetPtrCommonAttr(attr_name, std::forward<Ptr>(value));
  }

 private:
  MutableRecoContextInterface *const context_;
};

class CommonRecoContextItemAttrSetterHandle : public CommonRecoContextItemAttrGetterHandle {
 public:
  CommonRecoContextItemAttrSetterHandle(MutableRecoContextInterface *context, uint64 item_key)
      : CommonRecoContextItemAttrGetterHandle(context, item_key), context_(context), item_key_(item_key) {}

  inline void SetIntAttr(absl::string_view attr_name, int64 value, bool no_overwrite = false) {
    context_->SetIntItemAttr(item_key_, attr_name, value, no_overwrite);
  }
  inline void SetDoubleAttr(absl::string_view attr_name, double value, bool no_overwrite = false) {
    context_->SetDoubleItemAttr(item_key_, attr_name, value, no_overwrite);
  }
  inline void SetStringAttr(absl::string_view attr_name, std::string value, bool no_overwrite = false) {
    context_->SetStringItemAttr(item_key_, attr_name, std::move(value), no_overwrite);
  }
  inline void ResetIntListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetIntListItemAttr(item_key_, attr_name, capacity);
  }
  inline void ResetDoubleListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetDoubleListItemAttr(item_key_, attr_name, capacity);
  }
  inline void ResetStringListAttr(absl::string_view attr_name, int capacity = 0) {
    context_->ResetStringListItemAttr(item_key_, attr_name, capacity);
  }
  inline void AppendIntListAttr(absl::string_view attr_name, int64 value) {
    context_->AppendIntListItemAttr(item_key_, attr_name, value);
  }
  inline void AppendDoubleListAttr(absl::string_view attr_name, double value) {
    context_->AppendDoubleListItemAttr(item_key_, attr_name, value);
  }
  inline void AppendStringListAttr(absl::string_view attr_name, std::string value) {
    context_->AppendStringListItemAttr(item_key_, attr_name, std::move(value));
  }
  inline void SetIntListAttr(absl::string_view attr_name, std::vector<int64> &&value,
                             bool no_overwrite = false) {
    context_->SetIntListItemAttr(item_key_, attr_name, std::move(value), no_overwrite);
  }
  inline void SetDoubleListAttr(absl::string_view attr_name, std::vector<double> &&value,
                                bool no_overwrite = false) {
    context_->SetDoubleListItemAttr(item_key_, attr_name, std::move(value), no_overwrite);
  }
  inline void SetStringListAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                bool no_overwrite = false) {
    context_->SetStringListItemAttr(item_key_, attr_name, std::move(value), no_overwrite);
  }
  template <typename Ptr>
  inline void SetPtrAttr(absl::string_view attr_name, Ptr &&value) {
    context_->SetPtrItemAttr(item_key_, attr_name, std::forward<Ptr>(value));
  }

 private:
  MutableRecoContextInterface *const context_;
  const uint64 item_key_;
};

/**
 * "Hp" stands for "High Performance"
 */
class CommonRecoContextItemAttrHpSetterHandle : public CommonRecoContextItemAttrHpGetterHandle {
 public:
  CommonRecoContextItemAttrHpSetterHandle(const CommonRecoResult &result, ItemAttr *attr_accessor)
      : CommonRecoContextItemAttrHpGetterHandle(result, attr_accessor)
      , result_(result)
      , attr_accessor_(attr_accessor) {}

  inline void SetIntAttr(absl::string_view attr_name, int64 value, bool no_overwrite = false) {
    result_.SetIntAttr(attr_accessor_, value, no_overwrite);
  }
  inline void SetDoubleAttr(absl::string_view attr_name, double value, bool no_overwrite = false) {
    result_.SetDoubleAttr(attr_accessor_, value, no_overwrite);
  }
  inline void SetStringAttr(absl::string_view attr_name, std::string value, bool no_overwrite = false) {
    result_.SetStringAttr(attr_accessor_, std::move(value), no_overwrite);
  }
  inline void ResetIntListAttr(absl::string_view attr_name, int capacity = 0) {
    result_.ResetIntListAttr(attr_accessor_, capacity);
  }
  inline void ResetDoubleListAttr(absl::string_view attr_name, int capacity = 0) {
    result_.ResetDoubleListAttr(attr_accessor_, capacity);
  }
  inline void ResetStringListAttr(absl::string_view attr_name, int capacity = 0) {
    result_.ResetStringListAttr(attr_accessor_, capacity);
  }
  inline void AppendIntListAttr(absl::string_view attr_name, int64 value) {
    result_.AppendIntListAttr(attr_accessor_, value);
  }
  inline void AppendDoubleListAttr(absl::string_view attr_name, double value) {
    result_.AppendDoubleListAttr(attr_accessor_, value);
  }
  inline void AppendStringListAttr(absl::string_view attr_name, std::string value) {
    result_.AppendStringListAttr(attr_accessor_, std::move(value));
  }
  inline void SetIntListAttr(absl::string_view attr_name, std::vector<int64> &&value,
                             bool no_overwrite = false) {
    result_.SetIntListAttr(attr_accessor_, std::move(value), no_overwrite);
  }
  inline void SetDoubleListAttr(absl::string_view attr_name, std::vector<double> &&value,
                                bool no_overwrite = false) {
    result_.SetDoubleListAttr(attr_accessor_, std::move(value), no_overwrite);
  }
  inline void SetStringListAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                bool no_overwrite = false) {
    result_.SetStringListAttr(attr_accessor_, std::move(value), no_overwrite);
  }
  template <typename Ptr>
  inline void SetPtrAttr(absl::string_view attr_name, Ptr &&value) {
    result_.SetPtrAttr(attr_accessor_, std::forward<Ptr>(value));
  }

 private:
  ItemAttr *attr_accessor_ = nullptr;
  const CommonRecoResult result_;
};

/**
 * NOTE(huiyiqun): 一个工具模板类，用于抹平 CommonAttr 与 ItemAttr 的 API 之间的细微差异。
 * 可以用这个类快速方便地实现对于不同类型的 CommonAttr 与 ItemAttr 执行特定操作，使用方法是：
 * 1. 继承这个类，并且将类名放到模板变量里。
 * 2. override 需要的类型分支函数。
 * 3. 调用 EvaluateWithCommonAttr 和 EvaluateWithItemAttr。
 * 一个简单的例子见 CommonRecoContextAttrPrinter
 */
template <typename Derived>
class CommonRecoContextBranchEvaluator {
 protected:
  Derived &EvaluateWithCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name) {
    return EvaluateWithGetterHandle(CommonRecoContextCommonAttrGetterHandle(context), attr_name);
  }

  Derived &EvaluateWithItemAttr(ReadableRecoContextInterface *context, uint64 item_key,
                                absl::string_view attr_name) {
    return EvaluateWithGetterHandle(CommonRecoContextItemAttrGetterHandle(context, item_key), attr_name);
  }

  Derived &EvaluateWithItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor) {
    return EvaluateWithGetterHandle(CommonRecoContextItemAttrHpGetterHandle(result, attr_accessor),
                                    attr_accessor->name());
  }

  template <typename GetterHandle>
  Derived &EvaluateWithGetterHandle(GetterHandle handle, absl::string_view attr_name) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    auto attr_type = handle.GetAttrType(attr_name);
    bool success = false;
    switch (attr_type) {
      case AttrType::INT:
        if (auto val = handle.GetIntAttr(attr_name)) {
          derived_this->WithInt(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::FLOAT:
        if (auto val = handle.GetDoubleAttr(attr_name)) {
          derived_this->WithDouble(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::STRING:
        if (auto val = handle.GetStringAttr(attr_name)) {
          derived_this->WithString(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::INT_LIST:
        if (auto val = handle.GetIntListAttr(attr_name)) {
          derived_this->WithIntList(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::FLOAT_LIST:
        if (auto val = handle.GetDoubleListAttr(attr_name)) {
          derived_this->WithDoubleList(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::STRING_LIST:
        if (auto val = handle.GetStringListAttr(attr_name)) {
          derived_this->WithStringList(attr_name, *val);
          success = true;
        }
        break;
      case AttrType::EXTRA:
        if (auto float_list_val = handle.GetFloatListAttr(attr_name)) {
          derived_this->WithFloatList(attr_name, *float_list_val);
          success = true;
        } else if (auto msg_val = handle.template GetPtrAttr<::google::protobuf::Message>(attr_name)) {
          derived_this->WithProtobufMessage(attr_name, msg_val);
          success = true;
        }
        break;
      default:
        break;
    }
    if (!success) {
      derived_this->WithNoSuchAttr(attr_name);
    }
    return *derived_this;
  }
  // attr_name 是 WithXXX 的上文信息，不一定参与运算
  inline void WithInt(absl::string_view attr_name, int64 val) {}

  inline void WithDouble(absl::string_view attr_name, double val) {}

  inline void WithString(absl::string_view attr_name, absl::string_view val) {}

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {}

  inline void WithFloatList(absl::string_view attr_name, const std::vector<float> &val) {}

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {}

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {}

  inline void WithProtobufMessage(absl::string_view attr_name, const ::google::protobuf::Message *val) {}

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    CL_LOG_EVERY_N(ERROR, 100) << "attr does not exist: " << attr_name;
  }
};

// 用于 context 内部 的 Attr 拷贝
// 目前实现了 CopyFromItemAttr，拷贝来源是 ItemAttr; 目标是 SetterHandle 指向的 Attr

template <typename SetterHandle>
class CommonRecoContextInnerCopier
    : public CommonRecoContextBranchEvaluator<CommonRecoContextInnerCopier<SetterHandle>> {
 public:
  explicit CommonRecoContextInnerCopier(const SetterHandle &set_handle, absl::string_view new_attr_name)
      : handle_(set_handle), new_attr_name_(new_attr_name.data(), new_attr_name.size()) {}

  bool CopyFromItemAttr(ReadableRecoContextInterface *context, uint64 item_key, absl::string_view attr_name) {
    return this->EvaluateWithItemAttr(context, item_key, attr_name).IsSuccess();
  }

  inline bool IsSuccess() const {
    return is_success_;
  }

  inline void WithInt(absl::string_view attr_name, int64 val) {
    handle_.SetIntAttr(new_attr_name_, val);
  }

  inline void WithDouble(absl::string_view attr_name, double val) {
    handle_.SetDoubleAttr(new_attr_name_, val);
  }

  inline void WithString(absl::string_view attr_name, absl::string_view val) {
    handle_.SetStringAttr(new_attr_name_, std::string(val.data(), val.size()));
  }

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {
    handle_.SetIntListAttr(new_attr_name_, {val.begin(), val.end()});
  }

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {
    handle_.SetDoubleListAttr(new_attr_name_, {val.begin(), val.end()});
  }

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {
    std::vector<std::string> vec;
    vec.reserve(val.size());
    for (auto v : val) {
      vec.emplace_back(v.data(), v.size());
    }
    handle_.SetStringListAttr(new_attr_name_, std::move(vec));
  }

  inline void WithProtobufMessage(absl::string_view attr_name, const ::google::protobuf::Message *val) {
    handle_.SetPtrAttr(new_attr_name_, val);
  }

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    is_success_ = false;
  }

 private:
  SetterHandle handle_;
  std::string new_attr_name_;
  bool is_success_ = true;
};

class CommonRecoContextAttrPrinter : public CommonRecoContextBranchEvaluator<CommonRecoContextAttrPrinter> {
 public:
  // public API
  void PrintCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name) {
    EvaluateWithCommonAttr(context, attr_name);
  }

  void PrintItemAttr(ReadableRecoContextInterface *context, uint64 item_key, absl::string_view attr_name) {
    EvaluateWithItemAttr(context, item_key, attr_name);
  }

  // implementations for CommonRecoContextAttrPrinter
  inline void WithInt(absl::string_view attr_name, int64 val) {
    CL_LOG(INFO) << "[int] " << attr_name << ": " << val;
  }

  inline void WithDouble(absl::string_view attr_name, double val) {
    CL_LOG(INFO) << "[double] " << attr_name << ": " << val;
  }

  inline void WithString(absl::string_view attr_name, absl::string_view val) {
    CL_LOG(INFO) << "[string] " << attr_name << ": " << val;
  }

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {
    CL_LOG(INFO) << "[int list] " << attr_name << ": {";
    for (int64 v : val) {
      CL_LOG(INFO) << v;
    }
    CL_LOG(INFO) << "} ";
  }

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {
    CL_LOG(INFO) << "[double list] " << attr_name << ": {";
    for (double v : val) {
      CL_LOG(INFO) << v;
    }
    CL_LOG(INFO) << "} ";
  }

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {
    CL_LOG(INFO) << "[string list] " << attr_name << ": {";
    for (auto v : val) {
      CL_LOG(INFO) << v;
    }
    CL_LOG(INFO) << "} ";
  }

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    CL_LOG(INFO) << "[null] " << attr_name;
  }
};

class CommonRecoContextAttrFormatter
    : public CommonRecoContextBranchEvaluator<CommonRecoContextAttrFormatter> {
 public:
  // public API
  std::string CommonAttrToString(ReadableRecoContextInterface *context, absl::string_view attr_name) {
    return EvaluateWithCommonAttr(context, attr_name).Result();
  }

  std::string ItemAttrToString(ReadableRecoContextInterface *context, uint64 item_key,
                               absl::string_view attr_name) {
    return EvaluateWithItemAttr(context, item_key, attr_name).Result();
  }

  template <typename GetterHandle>
  std::string AttrToString(GetterHandle handle, absl::string_view attr_name) {
    return EvaluateWithGetterHandle(handle, attr_name).Result();
  }

  inline std::string ToString(const std::string &value) {
    return value;
  }

  template <typename T>
  inline std::string ToString(const T &value) {
    return std::to_string(value);
  }

  template <typename T>
  inline std::string ListToString(std::vector<T> values) {
    return absl::StrJoin(values, " ");
  }

  // implementations for CommonRecoContextAttrFormatter
  inline void WithInt(absl::string_view attr_name, int64 val) {
    result_ = "(int)" + ToString(val);
  }

  inline void WithDouble(absl::string_view attr_name, double val) {
    result_ = "(double)" + ToString(val);
  }

  inline void WithString(absl::string_view attr_name, absl::string_view val) {
    result_ = "(string)" + std::string(val.data(), val.size());
  }

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {
    result_ = "(int list)" + absl::StrJoin(val.begin(), val.end(), " ");
  }

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {
    result_ = "(double list)" + absl::StrJoin(val.begin(), val.end(), " ");
  }

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {
    result_ = "(string list)" + absl::StrJoin(val.begin(), val.end(), " ");
  }

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    result_ = "(null)";
  }

 private:
  std::string Result() const {
    return result_;
  }

  std::string result_;
};

template <typename Derived, typename OtherAttr>
class CommonRecoContextOtherAttrBuilder : public CommonRecoContextBranchEvaluator<Derived> {
 public:
  OtherAttr *BuildFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name) {
    return this->EvaluateWithCommonAttr(context, attr_name).Returns();
  }

  OtherAttr *BuildFromItemAttr(ReadableRecoContextInterface *context, uint64 item_key,
                               absl::string_view attr_name) {
    return this->EvaluateWithItemAttr(context, item_key, attr_name).Returns();
  }

  OtherAttr *BuildFromItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor) {
    return this->EvaluateWithItemAttr(result, attr_accessor).Returns();
  }

  // implementations for CommonRecoContextBranchEvaluator
  inline void WithInt(absl::string_view attr_name, int64 val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromInt(val);
  }

  inline void WithDouble(absl::string_view attr_name, double val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromDouble(val);
  }

  inline void WithString(absl::string_view attr_name, absl::string_view val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromString(val);
  }

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromIntList(val);
  }

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromDoubleList(val);
  }

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromStringList(val);
  }

  inline void WithProtobufMessage(absl::string_view attr_name, const ::google::protobuf::Message *val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->FromProtobufMessage(val);
  }

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    ptr_ = derived_this->DefaultValue(attr_name);
  }

 private:
  OtherAttr *ptr_;
  inline OtherAttr *Returns() const {
    return ptr_;
  }

  // subclasses should implement following methods
  inline OtherAttr *FromInt(int64 val) {
    return nullptr;
  }
  inline OtherAttr *FromDouble(double val) {
    return nullptr;
  }
  inline OtherAttr *FromString(absl::string_view val) {
    return nullptr;
  }
  inline OtherAttr *FromIntList(absl::Span<const int64> val) {
    return nullptr;
  }
  inline OtherAttr *FromDoubleList(absl::Span<const double> val) {
    return nullptr;
  }
  inline OtherAttr *FromStringList(const std::vector<absl::string_view> &val) {
    return nullptr;
  }
  inline OtherAttr *FromProtobufMessage(const ::google::protobuf::Message *val) {
    return nullptr;
  }
  inline OtherAttr *DefaultValue(absl::string_view attr_name) {
    return nullptr;
  }
};

template <typename Derived, typename OtherAttr>
class CommonRecoContextOtherAttrLoader : public CommonRecoContextBranchEvaluator<Derived> {
 public:
  explicit CommonRecoContextOtherAttrLoader(OtherAttr *ptr) : ptr_(CHECK_NOTNULL(ptr)) {}

  void LoadFromCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->EvaluateWithCommonAttr(context, attr_name);
  }

  void LoadFromItemAttr(ReadableRecoContextInterface *context, uint64 item_key, absl::string_view attr_name) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->EvaluateWithItemAttr(context, item_key, attr_name);
  }

  void LoadFromItemAttr(const CommonRecoResult &result, ItemAttr *attr_accessor) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->EvaluateWithItemAttr(result, attr_accessor);
  }

  // implementations for CommonRecoContextBranchEvaluator
  inline void WithInt(absl::string_view attr_name, int64 val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromInt(val);
  }

  inline void WithDouble(absl::string_view attr_name, double val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromDouble(val);
  }

  inline void WithString(absl::string_view attr_name, absl::string_view val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromString(val);
  }

  inline void WithIntList(absl::string_view attr_name, absl::Span<const int64> val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromIntList(val);
  }

  inline void WithFloatList(absl::string_view attr_name, const std::vector<float> &val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromFloatList(val);
  }

  inline void WithDoubleList(absl::string_view attr_name, absl::Span<const double> val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromDoubleList(val);
  }

  inline void WithStringList(absl::string_view attr_name, const std::vector<absl::string_view> &val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromStringList(val);
  }

  inline void WithProtobufMessage(absl::string_view attr_name, const ::google::protobuf::Message *val) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->FromProtobufMessage(val);
  }

  inline void WithNoSuchAttr(absl::string_view attr_name) {
    auto *derived_this = reinterpret_cast<Derived *>(this);
    derived_this->DefaultValue(attr_name);
  }

 protected:
  inline OtherAttr *ptr() const {
    return ptr_;
  }

 private:
  OtherAttr *const ptr_;

  // subclasses should implement following methods
  inline void FromInt(int64 val) {}
  inline void FromDouble(double val) {}
  inline void FromString(absl::string_view val) {}
  inline void FromIntList(absl::Span<const int64> val) {}
  inline void FromFloatList(const std::vector<float> &val) {}
  inline void FromDoubleList(absl::Span<const double> val) {}
  inline void FromStringList(const std::vector<absl::string_view> &val) {}
  inline void FromProtobufMessage(const ::google::protobuf::Message *val) {}
  inline void DefaultValue(absl::string_view attr_name) {}
};

inline static bool ItemAttrInnerCopy(MutableRecoContextInterface *context, uint64 from_item_key,
                                     absl::string_view from_attr_name, uint64 to_item_key,
                                     absl::string_view to_attr_name) {
  return CommonRecoContextInnerCopier<CommonRecoContextItemAttrSetterHandle>(
             CommonRecoContextItemAttrSetterHandle(context, to_item_key), to_attr_name)
      .CopyFromItemAttr(context, from_item_key, from_attr_name);
}

inline static bool CopyItemAttrToCommonAttr(MutableRecoContextInterface *context, uint64 from_item_key,
                                            absl::string_view from_attr_name,
                                            absl::string_view to_attr_name) {
  return CommonRecoContextInnerCopier<CommonRecoContextCommonAttrSetterHandle>(
             CommonRecoContextCommonAttrSetterHandle(context), to_attr_name)
      .CopyFromItemAttr(context, from_item_key, from_attr_name);
}

}  // namespace interop
}  // namespace platform
}  // namespace ks
