Interoperability
================

Utils to interoperate with other systems, typically type converters.

Immutable
=========

1. wrappers to implement kuiba::AttrDictApi (or generator for kuiba::AnyAttr). (for ks/reco_pub/kuiba/parameter/parameter_config.h)
2. generator for kuiba::SampleAttr. (for grpc response, DelegateEnricher)

Mutable
=======

Utils to inserting sepecific classes directly, such as Protobuf or attrs from indexes.

1. ~~kuiba::AnyAttr~~
2. ~~adsindexing::CustomerAttrValue (ks/realtime_reco/index/index.h, ks/common_reco/index/index_manager.h)~~
3. ks::reco::PredictItemKV (for ks/realtime_reco/kuiba/predict_item_index.h and photo_store)
4. kuiba::SampleAttr (for grpc request, ks/action/sample_list_kess_client.h, DelegateEnricher)
4. google::protobuf::Message (for PhotoInfo, UserProfile)
