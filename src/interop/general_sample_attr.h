#pragma once

#include <absl/types/optional.h>
#include <absl/strings/string_view.h>

#include <google/protobuf/descriptor.h>
#include <google/protobuf/message.h>

#include <gtest/gtest_prod.h>

#include <string>
#include <utility>
#include <vector>
#include <unordered_map>

#include "base/common/basic_types.h"

namespace ks {
namespace platform {
namespace interop {

class SampleAttrLikeDescriptor {
 public:
  bool Match(const google::protobuf::Message &msg, const std::string &name, int64 name_value) const;

  static const SampleAttrLikeDescriptor *GetByDescriptor(const google::protobuf::Descriptor *descriptor);

  template <typename SetterHandle>
  size_t SaveSampleAttrLikeWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                            const google::protobuf::Message &msg, bool no_overwrite) const {
    auto *reflection = msg.GetReflection();
    auto *descriptor = msg.GetDescriptor();
    if (descriptor != descriptor_) {
      return 0;
    }

    int list_size = 1;
    int type_value = reflection->GetEnumValue(msg, type_field_);
    if (bool_enum_value_ && type_value == *bool_enum_value_) {
      handle.SetIntAttr(attr_name, reflection->GetBool(msg, bool_value_field_), no_overwrite);
    } else if (int_enum_value_ && type_value == *int_enum_value_) {
      handle.SetIntAttr(attr_name, reflection->GetInt64(msg, int_value_field_), no_overwrite);
    } else if (float_enum_value_ && type_value == *float_enum_value_) {
      handle.SetDoubleAttr(attr_name, reflection->GetFloat(msg, float_value_field_), no_overwrite);
    } else if (string_enum_value_ && type_value == *string_enum_value_) {
      handle.SetStringAttr(attr_name, reflection->GetString(msg, string_value_field_), no_overwrite);
    } else if (int_list_enum_value_ && type_value == *int_list_enum_value_) {
      list_size = reflection->FieldSize(msg, int_list_value_field_);
      std::vector<int64> int_list;
      int_list.reserve(list_size);
      for (size_t i = 0; i < list_size; i++) {
        int_list.push_back(reflection->GetRepeatedInt64(msg, int_list_value_field_, i));
      }
      handle.SetIntListAttr(attr_name, std::move(int_list), no_overwrite);
    } else if (float_list_enum_value_ && type_value == *float_list_enum_value_) {
      list_size = reflection->FieldSize(msg, float_list_value_field_);
      std::vector<double> float_list;
      float_list.reserve(list_size);
      for (size_t i = 0; i < list_size; i++) {
        float_list.push_back(reflection->GetRepeatedFloat(msg, float_list_value_field_, i));
      }
      handle.SetDoubleListAttr(attr_name, std::move(float_list), no_overwrite);
    } else if (string_list_enum_value_ && type_value == *string_list_enum_value_) {
      list_size = reflection->FieldSize(msg, string_list_value_field_);
      std::vector<std::string> string_list;
      string_list.reserve(list_size);
      for (size_t i = 0; i < list_size; i++) {
        string_list.push_back(reflection->GetRepeatedString(msg, string_list_value_field_, i));
      }
      handle.SetStringListAttr(attr_name, std::move(string_list), no_overwrite);
    } else {
      list_size = 0;
    }
    return list_size;
  }

  template <typename SetterHandle>
  size_t AppendSampleAttrLikeWithSetterHandle(SetterHandle handle, absl::string_view attr_name,
                                              const google::protobuf::Message &msg) const {
    auto *reflection = msg.GetReflection();
    auto *descriptor = msg.GetDescriptor();
    if (descriptor != descriptor_) {
      return 0;
    }

    int list_size = 1;
    int type_value = reflection->GetEnumValue(msg, type_field_);
    if (bool_enum_value_ && type_value == *bool_enum_value_) {
      handle.AppendIntListAttr(attr_name, reflection->GetBool(msg, bool_value_field_));
    } else if (int_enum_value_ && type_value == *int_enum_value_) {
      handle.AppendIntListAttr(attr_name, reflection->GetInt64(msg, int_value_field_));
    } else if (float_enum_value_ && type_value == *float_enum_value_) {
      handle.AppendDoubleListAttr(attr_name, reflection->GetFloat(msg, float_value_field_));
    } else if (string_enum_value_ && type_value == *string_enum_value_) {
      handle.AppendStringListAttr(attr_name, reflection->GetString(msg, string_value_field_));
    } else if (int_list_enum_value_ && type_value == *int_list_enum_value_) {
      list_size = reflection->FieldSize(msg, int_list_value_field_);
      for (size_t i = 0; i < list_size; i++) {
        handle.AppendIntListAttr(attr_name, reflection->GetRepeatedInt64(msg, int_list_value_field_, i));
      }
    } else if (float_list_enum_value_ && type_value == *float_list_enum_value_) {
      list_size = reflection->FieldSize(msg, float_list_value_field_);
      for (size_t i = 0; i < list_size; i++) {
        handle.AppendDoubleListAttr(attr_name, reflection->GetRepeatedFloat(msg, float_list_value_field_, i));
      }
    } else if (string_list_enum_value_ && type_value == *string_list_enum_value_) {
      list_size = reflection->FieldSize(msg, string_list_value_field_);
      for (size_t i = 0; i < list_size; i++) {
        handle.AppendStringListAttr(attr_name,
                                    reflection->GetRepeatedString(msg, string_list_value_field_, i));
      }
    } else {
      list_size = 0;
    }
    return list_size;
  }

 private:
  bool Init(const google::protobuf::Descriptor *descriptor);

  SampleAttrLikeDescriptor() {}
  ~SampleAttrLikeDescriptor() {}

 private:
  const google::protobuf::Descriptor *descriptor_ = nullptr;
  const google::protobuf::FieldDescriptor *name_value_field_ = nullptr;
  const google::protobuf::FieldDescriptor *name_field_ = nullptr;
  const google::protobuf::FieldDescriptor *type_field_ = nullptr;
  absl::optional<int> bool_enum_value_;
  const google::protobuf::FieldDescriptor *bool_value_field_ = nullptr;
  absl::optional<int> int_enum_value_;
  const google::protobuf::FieldDescriptor *int_value_field_ = nullptr;
  absl::optional<int> float_enum_value_;
  const google::protobuf::FieldDescriptor *float_value_field_ = nullptr;
  absl::optional<int> string_enum_value_;
  const google::protobuf::FieldDescriptor *string_value_field_ = nullptr;
  absl::optional<int> int_list_enum_value_;
  const google::protobuf::FieldDescriptor *int_list_value_field_ = nullptr;
  absl::optional<int> float_list_enum_value_;
  const google::protobuf::FieldDescriptor *float_list_value_field_ = nullptr;
  absl::optional<int> string_list_enum_value_;
  const google::protobuf::FieldDescriptor *string_list_value_field_ = nullptr;

  FRIEND_TEST(GetSampleAttrDescriptor, KuibaSampleAttr);
  FRIEND_TEST(GetSampleAttrDescriptor, MixLogLabelAttr);
  FRIEND_TEST(GetSampleAttrDescriptor, AdLabelAttr);
};

}  // namespace interop
}  // namespace platform
}  // namespace ks
