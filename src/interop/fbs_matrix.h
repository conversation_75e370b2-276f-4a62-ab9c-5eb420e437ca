#pragma once

#include <utility>
#include <string>
#include <vector>
#include <memory>

#include "ks/reco_proto/common_reco/leaf/fbs/flatten_common_reco_generated.h"

namespace ks {
namespace platform {
namespace interop {

namespace fbs {

template <typename M> class BaseMatrixReader {
 public:
  explicit BaseMatrixReader(const M* matrix) : m(matrix), dim(0), rown(0), coln(0), size(0) {
    if (m) {
      size = m->value() ? m->value()->size() : 0;
      rown = m->rown();
      coln = m->coln();
      if (size > 0 && rown > 0 && coln > 0 && (size % (rown * coln) == 0)) {
        dim = size / (rown * coln);
      }
    }
  }

  inline bool Valid() const {
    return dim > 0;
  }

  inline int Dim() const {
    return dim;
  }

  inline int RowNum() const {
    return rown;
  }

  inline int ColNum() const {
    return coln;
  }

  inline int Size() const {
    return size;
  }

 protected:
  const M* m;
  int dim = 0;
  int rown = 0;
  int coln = 0;
  int size = 0;
};

template <typename T, typename M> class MatrixReader : public BaseMatrixReader<M> {
 public:
  explicit MatrixReader(const M* matrix) : BaseMatrixReader<M>(matrix) {}

  // 需自行确保调用安全性
  inline const T* GetRow(int row_idx) const {
    return BaseMatrixReader<M>::m->value()->data() +
           row_idx * BaseMatrixReader<M>::coln * BaseMatrixReader<M>::dim;
  }

  // 需自行确保调用安全性
  inline const T* Get(int row_idx, int col_idx) const {
    return BaseMatrixReader<M>::m->value()->data() +
           (row_idx * BaseMatrixReader<M>::coln + col_idx) * BaseMatrixReader<M>::dim;
  }

  inline const T* SafeGetRow(int row_idx) const {
    if (BaseMatrixReader<M>::Valid() && row_idx < BaseMatrixReader<M>::rown) {
      return GetRow(row_idx);
    }
    return nullptr;
  }

  inline const T* SafeGet(int row_idx, int col_idx) const {
    if (BaseMatrixReader<M>::Valid() && row_idx < BaseMatrixReader<M>::rown &&
        col_idx < BaseMatrixReader<M>::coln) {
      return Get(row_idx, col_idx);
    }
    return nullptr;
  }
};

template <typename M> class MatrixReader<std::string, M> : public BaseMatrixReader<M> {
 public:
  explicit MatrixReader(const M* matrix) : BaseMatrixReader<M>(matrix) {}

  // 需自行确保调用安全性
  inline std::string* GetRow(int row_idx) const {
    return nullptr;
  }

  // 需自行确保调用安全性
  inline const std::string Get(int row_idx, int col_idx) const {
    return BaseMatrixReader<M>::m->value()->Get(row_idx * BaseMatrixReader<M>::coln + col_idx)->str();
  }

  inline const std::string* SafeGetRow(int row_idx) const {
    return nullptr;
  }

  inline std::string SafeGet(int row_idx, int col_idx) const {
    if (BaseMatrixReader<M>::Valid() && row_idx < BaseMatrixReader<M>::rown &&
        col_idx < BaseMatrixReader<M>::coln) {
      return Get(row_idx, col_idx);
    }
    return "";
  }
};

template <typename M> class BaseVMatrixReader {
 public:
  explicit BaseVMatrixReader(const M* matrix) : m(matrix), dim(0), expand(0), rown(0) {}

  inline bool Valid() const {
    return valid;
  }
  inline int Dim() const {
    return dim;
  }
  inline int Expand() const {
    return expand;
  }
  inline int RowNum() const {
    return rown;
  }
  inline int ColOffset(int row_idx) const {
    return valid && row_idx < rown ? m->col_offset()->Get(row_idx) : -1;
  }
  inline int ColSize(int row_idx) const {
    return valid && row_idx < rown ? (ColOffset(row_idx + 1) - ColOffset(row_idx)) / dim : 0;
  }

 protected:
  const M* m;
  int dim;
  int expand;
  int rown;
  bool valid;
};

template <typename T, typename M> class VMatrixReader : public BaseVMatrixReader<M> {
 public:
  explicit VMatrixReader(const M* matrix) : BaseVMatrixReader<M>(matrix) {
    auto m = BaseVMatrixReader<M>::m;
    if (m) {
      auto& dim = BaseVMatrixReader<M>::dim;
      auto& expand = BaseVMatrixReader<M>::expand;
      auto& rown = BaseVMatrixReader<M>::rown;
      dim = m->dim();
      expand = m->expand();
      rown = m->rown();
      auto col_offset = m->col_offset();
      auto value = m->value();
      BaseVMatrixReader<M>::valid = dim > 0 && rown > 0 && col_offset && value &&
                                    (rown + 1 == col_offset->size()) &&
                                    (value->size() == col_offset->Get(rown)) && (value->size() % dim) == 0;
    }
  }

  inline const T* GetRow(int row_idx) const {
    return BaseVMatrixReader<M>::valid && row_idx < BaseVMatrixReader<M>::rown
               ? BaseVMatrixReader<M>::m->value()->data() + BaseVMatrixReader<M>::ColOffset(row_idx)
               : nullptr;
  }

  inline const T* Get(int row_idx, int col_idx) {
    return BaseVMatrixReader<M>::valid && row_idx < BaseVMatrixReader<M>::rown &&
                   col_idx < BaseVMatrixReader<M>::ColSize(row_idx)
               ? BaseVMatrixReader<M>::m->value()->data() + BaseVMatrixReader<M>::ColOffset(row_idx) +
                     BaseVMatrixReader<M>::dim * col_idx
               : nullptr;
  }
  inline const T* Get(int idx) const {
    return BaseVMatrixReader<M>::valid && idx < BaseVMatrixReader<M>::m->value()->size()
               ? BaseVMatrixReader<M>::m->value()->data() + BaseVMatrixReader<M>::dim * idx
               : nullptr;
  }
};

template <typename M> class VMatrixReader<std::string, M> : public BaseVMatrixReader<M> {
 public:
  explicit VMatrixReader(const M* matrix) : BaseVMatrixReader<M>(matrix) {
    auto m = BaseVMatrixReader<M>::m;
    if (m) {
      auto& rown = BaseVMatrixReader<M>::rown;
      rown = m->rown();
      auto col_offset = m->col_offset();
      auto value = m->value();
      BaseVMatrixReader<M>::valid = rown > 0 && col_offset && value && (rown + 1 == col_offset->size()) &&
                                    (value->size() == col_offset->Get(rown)) == 0;
    }
  }

  inline const std::string* GetRow(int row_idx) const {
    return nullptr;
  }
  inline std::string Get(int row_idx, int col_idx) {
    return BaseVMatrixReader<M>::valid && row_idx < BaseVMatrixReader<M>::rown &&
                   col_idx < BaseVMatrixReader<M>::ColSize(row_idx)
               ? BaseVMatrixReader<M>::m->value()
                     ->Get(BaseVMatrixReader<M>::ColOffset(row_idx) + col_idx)
                     ->str()
               : nullptr;
  }
  inline std::string Get(int idx) const {
    return BaseVMatrixReader<M>::valid && idx < BaseVMatrixReader<M>::m->value()->size()
               ? BaseVMatrixReader<M>::m->value()->Get(idx)->str()
               : nullptr;
  }
};

template <typename T, typename Allocator = std::allocator<T>>
class BaseMatrixWrapper {
 public:
  explicit BaseMatrixWrapper(const std::string &nm, int r, int c, std::vector<T, Allocator> &&v)
      : name(nm), dim(0), rown(r), coln(c), value(std::move(v)) {
    if (rown > 0 && coln > 0 && value.size() > 0 && (value.size() % (rown * coln) == 0)) {
      dim = value.size() / (rown * coln);
    }
  }

  inline bool Valid() const {
    return dim > 0;
  }
  inline int RowNum() const {
    return rown;
  }
  inline int ColNum() const {
    return coln;
  }
  inline int Dim() const {
    return dim;
  }
  inline const std::vector<T, Allocator>& Value() const {
    return value;
  }

 protected:
  std::string name;
  int dim;
  int rown;
  int coln;
  std::vector<T, Allocator> value;
};

template <typename T> class BaseVMatrixWrapper {
 public:
  explicit BaseVMatrixWrapper(const std::string& nm, int r, std::vector<int32>&& offset, std::vector<T>&& v,
                              int d = 1, int e = 1)
      : name(nm), valid(false), dim(d), expand(e), rown(r), col_offset(offset), value(v) {
    if (dim > 0 && expand > 0 && rown > 0 && col_offset.size() > 0 && value.size() > 0 &&
        (col_offset.size() == rown + 1) && (col_offset[rown] == value.size())) {
      valid = true;
    }
  }

  inline bool Valid() const {
    return valid;
  }
  inline int RowNum() const {
    return rown;
  }
  inline int Dim() const {
    return dim;
  }
  inline int Expand() const {
    return expand;
  }
  inline const std::vector<int32>& ColOffset() const {
    return col_offset;
  }
  inline const std::vector<T>& Value() const {
    return value;
  }

 protected:
  std::string name;
  bool valid;
  int dim;
  int expand;
  int rown;
  std::vector<int32> col_offset;
  std::vector<T> value;
};
}  // namespace fbs

}  // namespace interop
}  // namespace platform
}  // namespace ks
