#include "dragon/src/interop/attr_dict_api.h"

#include <algorithm>
#include <functional>
#include <utility>
#include "dragon/src/interop/util.h"
#include "learning/kuiba/sample_reader/sample_attr_kv.h"

namespace ks {
namespace platform {
namespace interop {
namespace attr_dict_api {

namespace {
class AnyAttrBuilder : public CommonRecoContextOtherAttrBuilder<AnyAttrBuilder, ::kuiba::AnyAttr> {
 public:
  inline ::kuiba::AnyAttr *FromInt(int64 val) {
    return new ::kuiba::IntAttr(val);
  }

  inline ::kuiba::AnyAttr *FromDouble(double val) {
    return new ::kuiba::FloatAttr(val);
  }

  inline ::kuiba::AnyAttr *FromString(absl::string_view val) {
    return new ::kuiba::StringAttr(val.data(), val.size());
  }

  inline ::kuiba::AnyAttr *FromIntList(absl::Span<const int64> val) {
    std::vector<int64> int_list(val.size());
    std::transform(val.begin(), val.end(), int_list.begin(), [](int64 d) -> int64 { return d; });
    return new ::kuiba::StdVectorAttr<int64>(std::move(int_list));
  }

  inline ::kuiba::AnyAttr *FromDoubleList(absl::Span<const double> val) {
    std::vector<float> float_list(val.size());
    std::transform(val.begin(), val.end(), float_list.begin(), [](double d) -> float { return d; });
    return new ::kuiba::StdVectorAttr<float>(std::move(float_list));
  }

  inline ::kuiba::AnyAttr *FromStringList(const std::vector<absl::string_view> &val) {
    std::vector<base::Slice> slice_list(val.size());
    std::transform(val.begin(), val.end(), slice_list.begin(),
                   [](absl::string_view sv) { return base::Slice(sv.data(), sv.size()); });
    return new ::kuiba::StdVectorAttr<base::Slice>(slice_list);
  }
};
}  // namespace

const kuiba::AnyAttr *CommonRecoContextCommonAttrWrapper::Lookup(const std::string &name) const {
  const kuiba::AnyAttr *attr = AnyAttrBuilder().BuildFromCommonAttr(context_, name);

  if (attr) {
    attrs_.emplace_back(attr);
  }

  return attr;
}

const kuiba::AnyAttr *CommonRecoContextItemAttrWrapper::Lookup(const std::string &name) const {
  const kuiba::AnyAttr *attr = AnyAttrBuilder().BuildFromItemAttr(context_, item_key_, name);

  if (attr) {
    attrs_.emplace_back(attr);
  }

  return attr;
}

const kuiba::AnyAttr *CommonRecoContextWrapper::Lookup(const std::string &name) const {
  if (use_common_attr_first_) {
    auto *attr = common_attr_visitor_.Lookup(name);
    if (!attr) {
      attr = item_attr_visitor_.Lookup(name);
    }
    return attr;
  } else {
    auto *attr = item_attr_visitor_.Lookup(name);
    if (!attr) {
      attr = common_attr_visitor_.Lookup(name);
    }
    return attr;
  }
}

}  // namespace attr_dict_api
}  // namespace interop
}  // namespace platform
}  // namespace ks
