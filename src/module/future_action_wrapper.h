#pragma once

#include <functional>
#include <utility>

namespace ks {
namespace platform {

// NOTE(zhaoyang09): CommonRecoFutureActionWrapper 使用场景为无法简单套用 std::future &&
// ks::kess::rpc::Future 又不需要使用 local thread pool 线程执行的特殊场景。

template <typename T>
class CommonRecoFutureActionWrapper {
 public:
  CommonRecoFutureActionWrapper() = delete;
  explicit CommonRecoFutureActionWrapper(std::function<T()> action) {
    action_ = std::move(action);
  }
  CommonRecoFutureActionWrapper(CommonRecoFutureActionWrapper &&c) {
    action_ = std::move(c.action_);
  }

  CommonRecoFutureActionWrapper(const CommonRecoFutureActionWrapper &c) {
    action_ = c.action_;
  }

  T Get() {
    return action_();
  }

 private:
  std::function<T()> action_;
};
}  // namespace platform
}  // namespace ks
