#pragma once

#include <memory>
#include <queue>
#include <string>

#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dynamic_kafka_client/dynamic_kafka_client.h"
#include "dynamic_kafka_client/mock_consumer/consumer_mock.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

class KafkaConsumerMocker : public ks::infra::kfk::IConsumerMock {
 public:
  ks::infra::kfk::Message *MockConsume(const std::string &topic_name) override {
    // 此处实现非线程安全
    auto it = msg_queues_.find(topic_name);
    if (it != msg_queues_.end()) {
      if ((it->second).empty()) {
        return nullptr;
      }
      auto *msg = (it->second).front();
      (it->second).pop();
      return msg;
    }
    return nullptr;
  }

  void AddMockMessage(const std::string &topic_name, const std::string &value) {
    MockMessage mock_msg;
    mock_msg.cluster_id_ = "mock_cluster";
    mock_msg.topic_name_ = topic_name;
    mock_msg.partition_ = 1;
    mock_msg.key_ = "mock-key";
    mock_msg.value_ = value;
    mock_msg.offset_ = -1;
    auto *msg = GetMockMessage(mock_msg);
    msg_queues_[mock_msg.topic_name_].push(msg);
  }

 private:
  folly::F14FastMap<std::string, std::queue<ks::infra::kfk::Message *>> msg_queues_;
};

class CommonRecoLeafKafkaMocker {
 public:
  void MockKafkaMessage(const std::string &topic_name, const std::string &value) {
    auto consumer_mock = std::make_shared<ks::platform::KafkaConsumerMocker>();
    consumer_mock->AddMockMessage(topic_name, value);
    ks::infra::kfk::DynamicKafkaClient::TurnOnMockConsumer();
    ks::infra::kfk::DynamicKafkaClient::SetIConsumerMock(consumer_mock);
  }
};

}  // namespace platform
}  // namespace ks
