#pragma once

#include <kenv/context.h>
#include <atomic>
#include <future>
#include <memory>
#include <string>
#include <thread>
#include <utility>

#include "base/common/basic_types.h"
#include "base/common/sleep.h"
#include "base/time/timestamp.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/ktrace_util.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/perf_report_util.h"
#include "folly/GLog.h"
#include "folly/executors/CPUThreadPoolExecutor.h"
#include "folly/executors/task_queue/LifoSemMPMCQueue.h"
#include "folly/executors/thread_factory/InitThreadFactory.h"
#include "folly/executors/thread_factory/NamedThreadFactory.h"
#include "folly/futures/Future.h"
#include "ks/base/perfutil/perfutil_wrapper.h"
#include "ktrace/core/span.h"
#include "ktrace/core/span_context.h"
#include "ktrace/core/tracer_manager.h"

namespace ks {
namespace platform {

DECLARE_double(sub_flow_task_queue_guard_line);

template <typename T>
class AsyncTaskThreadPool {
 public:
  AsyncTaskThreadPool(const std::string &name, int thread_num, int queue_capacity,
                      std::function<void(int)> after_start = nullptr)
      : AsyncTaskThreadPool(name, thread_num, thread_num, queue_capacity, std::move(after_start)) {}

  AsyncTaskThreadPool(const std::string &name, int min_thread_num, int max_thread_num, int queue_capacity,
                      std::function<void(int)> after_start = nullptr)
      : name_(name)
      , min_thread_num_(min_thread_num)
      , max_thread_num_(max_thread_num)
      , queue_capacity_(queue_capacity)
      , after_start_(std::move(after_start)) {
    VLOG(10) << "Creating AsyncTaskThreadPool...";
    thread_factory_ =
        std::make_shared<folly::InitThreadFactory>(std::make_shared<folly::NamedThreadFactory>(name), [this] {
          int thread_index = thread_index_++;
          if (after_start_) {
            after_start_(thread_index);
          }
          running_count_++;
        });
    executor_ = std::make_unique<folly::CPUThreadPoolExecutor>(
        std::make_pair(min_thread_num_, max_thread_num_),
        std::make_unique<folly::LifoSemMPMCQueue<folly::CPUThreadPoolExecutor::CPUTask,
                                                 folly::QueueBehaviorIfFull::BLOCK>>(queue_capacity),
        thread_factory_);
  }

  AsyncTaskThreadPool() = delete;

  ~AsyncTaskThreadPool() {
    Stop();
  }

  void Stop() {
    stop_ = true;
    executor_->stop();
  }

  size_t GetQueueSize() const {
    return executor_->getTaskQueueSize();
  }

  bool IsNearlyFull() {
    if (FLAGS_sub_flow_task_queue_guard_line < 1) {
      return GetQueueSize() > FLAGS_sub_flow_task_queue_guard_line * queue_capacity_;
    } else {
      return false;
    }
  }

  size_t GetThreadNum() const {
    return executor_->numThreads();
  }

  size_t GetActiveThreadNum() const {
    return executor_->numActiveThreads();
  }

  void SetKtraceSpanName(const std::string &span_name) {
    ktrace_span_name_ = span_name;
  }

  std::future<T> Async(const std::string &request_type, const std::string &processor_name,
                       std::function<T()> func, const std::string &downstream_name = "") {
    if (IsNearlyFull()) {
      std::shared_ptr<std::promise<T>> task_promise = std::make_shared<std::promise<T>>();
      std::future<T> ret_future = task_promise->get_future();
      task_promise->set_value(T());
      CL_LOG_ERROR("[ATTENTION] async task discard", "task queue full: " + name_)
          << "[ATTENTION] " << name_ << " async task queue is full, task has been discarded";
      return ret_future;
    } else {
      return AsyncWithBlock(request_type, processor_name, std::move(func), downstream_name);
    }
  }

  std::future<T> AsyncWithBlock(const std::string &request_type, const std::string &processor_name,
                                std::function<T()> func, const std::string &downstream_name = "") {
    std::shared_ptr<std::promise<T>> task_promise = std::make_shared<std::promise<T>>();
    std::future<T> ret_future = task_promise->get_future();
    auto snapshot = ks::infra::ktrace::TracerManager::CreateSnapshot();
    auto kenv_ctx = ks::infra::kenv::Context::Current()->Copy();
    int64 put_in_pool_ts = base::GetTimestamp();
    auto task = [snapshot = std::move(snapshot), kenv_ctx = std::move(kenv_ctx), func = std::move(func),
                 processor_name, downstream_name, span_name = ktrace_span_name_,
                 task_promise = std::move(task_promise), put_in_pool_ts, request_type]() {
      ks::infra::ktrace::TracerManager::Attach(snapshot);
      bool is_sampled = !span_name.empty() && ks::platform::KtraceUtil::IsKtraceEnabledForPipeline() &&
                        ks::infra::ktrace::TracerManager::IsSampled();
      std::shared_ptr<ks::infra::ktrace::Span> local_span;
      if (is_sampled) {
        local_span = ks::infra::ktrace::TracerManager::StartSpan(span_name);
        auto kspan = std::static_pointer_cast<ks::infra::ktrace::KSpan>(local_span);
        if (kspan) {
          kspan->SetComponentType("LOCAL");
          kspan->SetComponentName(processor_name);
        }
      }
      T fut = kenv_ctx->Run(std::move(func));
      if (local_span) {
        local_span->Finish();
      }
      if (!processor_name.empty()) {
        CL_PERF_INTERVAL(base::GetTimestamp() - put_in_pool_ts, kPerfNs, "processor_async_ready",
                         GlobalHolder::GetServiceIdentifier(), request_type, processor_name, downstream_name,
                         "", GlobalHolder::GetJsonConfigVersion());
      }
      task_promise->set_value(fut);
    };
    folly::via(executor_.get()).then(task);
    return ret_future;
  }

  void WaitForInitDone() {
    while (running_count_ < min_thread_num_) {
      FB_LOG_EVERY_MS(INFO, 1000) << "waiting for async task thread pool init (" << name_ << ")... "
                                  << running_count_ << " / " << min_thread_num_;
    }
  }

 private:
  std::string name_;
  std::unique_ptr<folly::CPUThreadPoolExecutor> executor_;
  std::shared_ptr<folly::InitThreadFactory> thread_factory_;
  std::atomic_bool stop_{false};
  std::atomic_int thread_index_{0};
  std::atomic_int running_count_{0};
  int min_thread_num_ = 0;
  int max_thread_num_ = 0;
  int queue_capacity_ = 0;
  std::function<void(int)> after_start_ = nullptr;
  std::string ktrace_span_name_;
  DISALLOW_COPY_AND_ASSIGN(AsyncTaskThreadPool);
};  // namespace platform
}  // namespace platform
}  // namespace ks
