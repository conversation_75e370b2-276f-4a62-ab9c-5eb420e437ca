
#include "dragon/src/module/async_task_thread_pool.h"

#include <atomic>
#include <memory>
#include <vector>

#include "gtest/gtest.h"

static const int kMaxNumList = 500;
static const int ThreadNum = 3;

class SubFlowThreadPoolTest : public ::testing::Test {
 protected:
  void SetUp() override {
    thread_pool_ = std::make_unique<ks::platform::AsyncTaskThreadPool<int>>(
        "SubFlowTaskPool", ThreadNum, ThreadNum, 10000, [this](int index) { count_num_++; });
  }
  std::unique_ptr<ks::platform::AsyncTaskThreadPool<int>> thread_pool_ = nullptr;
  std::atomic_int count_num_{0};
};

TEST_F(SubFlowThreadPoolTest, GetThreadPool) {
  std::vector<int> test_list;
  for (int i = 0; i < kMaxNumList; ++i) {
    test_list.push_back(i);
  }

  std::vector<std::future<int>> results;
  for (int i = 0; i < kMaxNumList; ++i) {
    results.push_back(thread_pool_->Async("test", "test", [&, i] { return test_list[i]; }));
  }
  thread_pool_->WaitForInitDone();

  EXPECT_EQ(count_num_, ThreadNum);

  for (int i = 0; i < kMaxNumList; ++i) {
    EXPECT_EQ(results[i].get(), test_list[i]);
  }
}
