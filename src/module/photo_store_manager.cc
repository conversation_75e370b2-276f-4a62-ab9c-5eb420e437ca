#include "dragon/src/module/photo_store_manager.h"

// 分布式索引
DEFINE_bool(use_distributed_index, false, "distributed index flag");
DEFINE_int32(retrieval_max_days, 15, "retrieval source timestamp flag");
DEFINE_int32(retrieval_old_showed_photo_forward_index_max_day, 1,
             "retrieval old showed photo forward max day by follow list retrieval");
DEFINE_string(photo_info_backup_redis_cluster, "recoRelationPhotoInfoBackup", "photo info backup redis");
// 使用粗排 + 精排 两段式 排序架构
DEFINE_bool(use_two_stage_ranking_framework, false, "using two stage ranking framework");
