#include "dragon/src/module/matx/matx_item_attr_getter.h"

namespace ks {
namespace platform {

void *MatxItemAttrGetter_F__placement_new__(void *buf) {
  return new (buf) MatxItemAttrGetter;
}

void MatxItemAttrGetter_F__placement_del__(matxscript::runtime::ILightUserData *ptr) {
  static_cast<MatxItemAttrGetter *>(ptr)->MatxItemAttrGetter::~MatxItemAttrGetter();
}

}  // namespace platform
}  // namespace ks
