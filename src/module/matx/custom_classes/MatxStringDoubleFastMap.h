#pragma once

#include "dragon/src/module/matx/matx_type_registry.h"
#include "folly/container/F14Map.h"
#include "matxscript/runtime/codegen_all_includes.h"

using namespace ::matxscript::runtime;

namespace ks {
namespace platform {
struct MatxStringDoubleFastMap : public matxscript::runtime::StringDoubleFastMap {
  // PtrWrapper
  MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE((folly::F14FastMap<std::string, double>))

  // virtual methods
  bool empty() override {
    return GetPtr()->empty();
  }

  int64_t size() override {
    return GetPtr()->size();
  }

  int64_t count(const string_view &key) override {
    return GetPtr()->count(key.data());
  }

  double at(const string_view &key1) override {
    return GetPtr()->at(key1.data());
  }

  bool insert(const string_view &key2, double value) override {
    return GetPtr()->insert({key2.data(), value}).second;
  }

  bool insert_or_assign(const string_view &key3, double value1) override {
    return GetPtr()->insert_or_assign(key3.data(), value1).second;
  }

  int64_t erase(const string_view &key4) override {
    return GetPtr()->erase(key4.data());
  }

  bool clear() override {
    GetPtr()->clear();
    return true;
  }
};

}  // namespace platform
}  // namespace ks
