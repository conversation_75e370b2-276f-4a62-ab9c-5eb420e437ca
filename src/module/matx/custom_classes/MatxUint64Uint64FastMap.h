#pragma once

#include "dragon/src/module/matx/matx_type_registry.h"
#include "folly/container/F14Map.h"
#include "matxscript/runtime/codegen_all_includes.h"

using namespace ::matxscript::runtime;

namespace ks {
namespace platform {
struct MatxUint64Uint64FastMap : public matxscript::runtime::Uint64Uint64FastMap {
  // PtrWrapper
  MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE((folly::F14FastMap<uint64_t, uint64_t>))

  // virtual methods
  bool empty() override {
    return GetPtr()->empty();
  }

  int64_t size() override {
    return GetPtr()->size();
  }

  int64_t count(uint64_t key) override {
    return GetPtr()->count(key);
  }

  uint64_t at(uint64_t key1) override {
    return GetPtr()->at(key1);
  }

  bool insert(uint64_t key2, uint64_t value) override {
    return GetPtr()->insert({key2, value}).second;
  }

  bool insert_or_assign(uint64_t key3, uint64_t value1) override {
    return GetPtr()->insert_or_assign(key3, value1).second;
  }

  int64_t erase(uint64_t key4) override {
    return GetPtr()->erase(key4);
  }

  bool clear() override {
    GetPtr()->clear();
    return true;
  }
};

}  // namespace platform
}  // namespace ks
