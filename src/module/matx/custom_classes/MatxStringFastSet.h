#pragma once

#include "dragon/src/module/matx/matx_type_registry.h"
#include "folly/container/F14Set.h"
#include "matxscript/runtime/codegen_all_includes.h"

using namespace ::matxscript::runtime;

namespace ks {
namespace platform {
struct MatxStringFastSet : public matxscript::runtime::StringFastSet {
  // PtrWrapper
  MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE((folly::F14FastSet<std::string>))

  // virtual methods
  bool empty() override {
    return GetPtr()->empty();
  }

  int64_t size() override {
    return GetPtr()->size();
  }

  int64_t count(const string_view &value) override {
    return GetPtr()->count(value.data());
  }

  bool insert(const string_view &value1) override {
    return GetPtr()->insert(value1.data()).second;
  }

  int64_t erase(const string_view &value2) override {
    return GetPtr()->erase(value2.data());
  }

  bool clear() override {
    GetPtr()->clear();
    return true;
  }
};

}  // namespace platform
}  // namespace ks
