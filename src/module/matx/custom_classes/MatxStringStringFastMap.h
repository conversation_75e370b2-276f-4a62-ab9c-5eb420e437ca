#pragma once

#include "dragon/src/module/matx/matx_type_registry.h"
#include "folly/container/F14Map.h"
#include "matxscript/runtime/codegen_all_includes.h"

using namespace ::matxscript::runtime;

namespace ks {
namespace platform {
struct MatxStringStringFastMap : public matxscript::runtime::StringStringFastMap {
  // PtrWrapper
  MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE((folly::F14FastMap<std::string, std::string>))

  // virtual methods
  bool empty() override {
    return GetPtr()->empty();
  }

  int64_t size() override {
    return GetPtr()->size();
  }

  int64_t count(const string_view &key) override {
    return GetPtr()->count(key.data());
  }

  string_view at(const string_view &key1) override {
    return GetPtr()->at(key1.data());
  }

  bool insert(const string_view &key2, const string_view &value) override {
    return GetPtr()->insert({key2.data(), value.data()}).second;
  }

  bool insert_or_assign(const string_view &key3, const string_view &value1) override {
    return GetPtr()->insert_or_assign(key3.data(), value1.data()).second;
  }

  int64_t erase(const string_view &key4) override {
    return GetPtr()->erase(key4.data());
  }

  bool clear() override {
    GetPtr()->clear();
    return true;
  }
};

}  // namespace platform
}  // namespace ks
