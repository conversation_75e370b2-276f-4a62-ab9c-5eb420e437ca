#pragma once

#include "dragon/src/module/matx/matx_type_registry.h"
#include "folly/container/F14Set.h"
#include "matxscript/runtime/codegen_all_includes.h"

using namespace ::matxscript::runtime;

namespace ks {
namespace platform {
struct MatxUint64FastSet : public matxscript::runtime::Uint64FastSet {
  // PtrWrapper
  MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE((folly::F14FastSet<uint64_t>))

  // virtual methods
  bool empty() override {
    return GetPtr()->empty();
  }

  int64_t size() override {
    return GetPtr()->size();
  }

  int64_t count(uint64_t value) override {
    return GetPtr()->count(value);
  }

  bool insert(uint64_t value1) override {
    return GetPtr()->insert(value1).second;
  }

  int64_t erase(uint64_t value2) override {
    return GetPtr()->erase(value2);
  }

  bool clear() override {
    GetPtr()->clear();
    return true;
  }
};

}  // namespace platform
}  // namespace ks
