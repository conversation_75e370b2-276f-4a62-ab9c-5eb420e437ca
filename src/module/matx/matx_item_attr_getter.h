#pragma once

#include <string>
#include <utility>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/matx/matx_type_registry.h"
#include "matxscript/runtime/codegen_all_includes.h"
#include "matxscript/runtime/logging.h"

namespace ks {
namespace platform {

struct MatxItemAttrGetter : public matxscript::runtime::ItemAttrGetter {
  RecoResultConstIter begin_;
  int64 result_size_;
  const ItemAttr *accessor_ = nullptr;

  int64_t GetInt(int64_t item_index, int64_t default_value) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto int_optional = (*(begin_ + item_index)).GetIntAttr(accessor_);
      return int_optional.value_or(default_value);
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              default_value);
    }
  }

  double GetDouble(int64_t item_index, double default_value) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto double_optional = (*(begin_ + item_index)).GetDoubleAttr(accessor_);
      return double_optional.value_or(default_value);
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              default_value);
    }
  }

  matxscript::runtime::string_view GetString(int64_t item_index,
                                             const matxscript::runtime::string_view &default_value) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto string_optional = (*(begin_ + item_index)).GetStringAttr(accessor_);
      return string_optional
                 ? matxscript::runtime::string_view(string_optional->data(), string_optional->size())
                 : default_value;
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              default_value);
    }
  }

  matxscript::runtime::FTList<int64_t> GetIntList(int64_t item_index) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto int_list_optional = (*(begin_ + item_index)).GetIntListAttr(accessor_);
      return int_list_optional
                 ? std::move(matxscript::runtime::FTList<int64_t>(
                       const_cast<int64_t *>(int_list_optional->begin()), int_list_optional->size()))
                 : std::move(matxscript::runtime::FTList<int64_t>());
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              matxscript::runtime::FTList<int64_t>());
    }
  }

  matxscript::runtime::FTList<double> GetDoubleList(int64_t item_index) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto double_list_optional = (*(begin_ + item_index)).GetDoubleListAttr(accessor_);
      return double_list_optional
                 ? std::move(matxscript::runtime::FTList<double>(
                       const_cast<double *>(double_list_optional->begin()), double_list_optional->size()))
                 : std::move(matxscript::runtime::FTList<double>());
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              matxscript::runtime::FTList<double>());
    }
  }

  matxscript::runtime::FTList<matxscript::runtime::string_view> GetStringList(int64_t item_index) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto string_list_optional = (*(begin_ + item_index)).GetStringListAttr(accessor_);
      if (string_list_optional) {
        matxscript::runtime::FTList<matxscript::runtime::string_view> result;
        for (const auto &string : *string_list_optional) {
          result.emplace_back(matxscript::runtime::string_view(string.data(), string.size()));
        }
        return std::move(result);
      }
      return std::move(matxscript::runtime::FTList<matxscript::runtime::string_view>());
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              matxscript::runtime::FTList<matxscript::runtime::string_view>());
    }
  }

  int64_t HasValue(int64_t item_index) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      return (*(begin_ + item_index)).HasAttr(accessor_);
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              0);
    }
  }

  bool GetPtr(int64_t item_index, const matxscript::runtime::PtrWrapper_SharedView &ptr_wrapper) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      auto any = (*(begin_ + item_index)).GetExtraAttr(accessor_);
      return ptr_wrapper->SetPtrWithAny(any);
    }
    return false;
  }
};

void *MatxItemAttrGetter_F__placement_new__(void *buf);
void MatxItemAttrGetter_F__placement_del__(matxscript::runtime::ILightUserData *ptr);
}  // namespace platform
}  // namespace ks
