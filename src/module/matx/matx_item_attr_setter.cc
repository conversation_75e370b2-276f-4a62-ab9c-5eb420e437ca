#include "dragon/src/module/matx/matx_item_attr_setter.h"

namespace ks {
namespace platform {

void *MatxItemAttrSetter_F__placement_new__(void *buf) {
  return new (buf) MatxItemAttrSetter;
}

void MatxItemAttrSetter_F__placement_del__(matxscript::runtime::ILightUserData *ptr) {
  static_cast<MatxItemAttrSetter *>(ptr)->MatxItemAttrSetter::~MatxItemAttrSetter();
}

}  // namespace platform
}  // namespace ks
