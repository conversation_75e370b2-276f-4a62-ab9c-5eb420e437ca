#pragma once

#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/matx/matx_type_registry.h"
#include "matxscript/runtime/codegen_all_includes.h"
#include "matxscript/runtime/logging.h"

namespace ks {
namespace platform {

struct MatxItemAttrSetter : public matxscript::runtime::ItemAttrSetter {
  RecoResultConstIter begin_;
  int64 result_size_;
  ItemAttr *accessor_ = nullptr;

  int64_t SetInt(int64_t item_index, int64_t value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).SetIntAttr(accessor_, value)
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t SetDouble(int64_t item_index, double value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).SetDoubleAttr(accessor_, value)
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t SetString(int64_t item_index, const matxscript::runtime::string_view &value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).SetStringAttr(accessor_, std::string(value.data(), value.size()))
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t SetIntList(int64_t item_index, const matxscript::runtime::FTList<int64_t> &value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).SetIntListAttr(accessor_, std::move(value.MutableImpl()))
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t SetDoubleList(int64_t item_index, const matxscript::runtime::FTList<double> &value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).SetDoubleListAttr(accessor_, std::move(value.MutableImpl()))
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64 SetStringList(int64_t item_index,
                      const matxscript::runtime::FTList<matxscript::runtime::String> &value) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      std::vector<std::string> string_list;
      string_list.reserve(value.size());
      for (const auto &source : value) {
        string_list.emplace_back(source.data(), source.size());
      }
      return (*(begin_ + item_index)).SetStringListAttr(accessor_, std::move(string_list));
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Check failed: item_index out of range",
              0);
    }
  }

  int64_t AppendIntList(int64_t item_index, int64_t value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).AppendIntListAttr(accessor_, value)
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t AppendDoubleList(int64_t item_index, double value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).AppendDoubleListAttr(accessor_, value)
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t AppendStringList(int64_t item_index, const matxscript::runtime::string_view &value) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index))
                     .AppendStringListAttr(accessor_, std::string(value.data(), value.size()))
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t SetPtr(int64_t item_index, const matxscript::runtime::PtrWrapper_SharedView &value) override {
    if (likely(item_index >= 0 && item_index < result_size_)) {
      value->SetItemPtr(&(*(begin_ + item_index)), accessor_);
      return 1;
    }
    return 0;
  }
};

void *MatxItemAttrSetter_F__placement_new__(void *buf);
void MatxItemAttrSetter_F__placement_del__(matxscript::runtime::ILightUserData *ptr);
}  // namespace platform
}  // namespace ks
