#include "dragon/src/module/matx/matx_dragon_context.h"

namespace ks {
namespace platform {

void MatxDragonContext_F__deleter__(matxscript::runtime::ILightUserData *ptr) {
  delete static_cast<MatxDragonContext *>(ptr);
}

void *MatxDragonContext_F__placement_new__(void *buf) {
  return new (buf) MatxDragonContext;
}

void MatxDragonContext_F__placement_del__(matxscript::runtime::ILightUserData *ptr) {
  static_cast<MatxDragonContext *>(ptr)->MatxDragonContext::~MatxDragonContext();
}

matxscript::runtime::RTValue MatxDragonContext__F___init__(
    const matxscript::runtime::DragonflyContext_SharedView &self, void *handle_2_71828182846) {
  return (self->__init__(handle_2_71828182846));  // NOLINT
}

matxscript::runtime::DragonflyContext_SharedView MatxDragonContext__F___init___wrapper(
    void *handle_2_71828182846) {
  static auto buffer_size = matxscript::runtime::UserDataRef::GetInternalBufferSize();
  if (buffer_size < sizeof(MatxDragonContext)) {
    auto self = new MatxDragonContext;
    self->function_table_2_71828182846_ = &MatxDragonContext::function_table_s_2_71828182846_;
    DragonflyContext__F___init__(self, handle_2_71828182846);
    matxscript::runtime::UserDataRef self_ref(MatxDragonContext::tag_s_2_71828182846_,
                                              MatxDragonContext::var_num_s_2_71828182846_, self,
                                              MatxDragonContext_F__deleter__, nullptr);
    self->self_node_ptr_2_71828182846 = (matxscript::runtime::Object *)(self_ref.get());
    return self_ref;
  } else {
    matxscript::runtime::UserDataRef self(MatxDragonContext::tag_s_2_71828182846_,
                                          MatxDragonContext::var_num_s_2_71828182846_,
                                          sizeof(MatxDragonContext), MatxDragonContext_F__placement_new__,
                                          MatxDragonContext_F__placement_del__, nullptr);
    matxscript::runtime::DragonflyContext_SharedView self_view(
        reinterpret_cast<MatxDragonContext *>(self.ud_ptr_nocheck()));
    self_view->function_table_2_71828182846_ = &MatxDragonContext::function_table_s_2_71828182846_;
    DragonflyContext__F___init__(self_view, handle_2_71828182846);
    self_view->self_node_ptr_2_71828182846 = (matxscript::runtime::Object *)(self.get());
    return self;
  }
}

}  // namespace platform
}  // namespace ks
