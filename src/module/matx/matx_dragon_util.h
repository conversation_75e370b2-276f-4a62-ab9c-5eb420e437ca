#pragma once

#include <limits>
#include <string>
#include <utility>
#include <vector>

#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/random.h"
#include "ks/util/location.h"
#include "matxscript/runtime/codegen_all_includes.h"

namespace ks {
namespace platform {

struct MatxDragonUtil : public matxscript::runtime::DragonflyUtil {
  int64_t GetTimestamp() override {
    return base::GetTimestamp();
  }

  double GetDistance(double lat_a, double lon_a, double lat_b, double lon_b) override {
    return ks::location_util::GetDistance(lat_a, lon_a, lat_b, lon_b);
  }

  matxscript::runtime::RTValue Sleep(int64_t ms) override {
    base::SleepForMilliseconds(ms);
    return matxscript::runtime::None;
  }

  double Random() override {
    return ks::platform::uniform_real<float>();
  }

  int64_t CityHash64(const matxscript::runtime::string_view &str) override {
    return base::CityHash64(str.data(), str.length());
  }

  matxscript::runtime::String IntToString(int64_t data) override {
    char buffer[21];
    char *end = absl::numbers_internal::FastIntToBuffer(data, buffer);
    return matxscript::runtime::String(std::move(buffer), end - buffer);
  }

  matxscript::runtime::String DoubleToString(double data, int64_t precision) override {
    char buffer[64];
    int len = 0;
    if (precision >= 0) {
      len = absl::SNPrintF(buffer, sizeof(buffer), "%.*f", static_cast<int>(precision), data);
    } else {
      len = absl::SNPrintF(buffer, sizeof(buffer), "%.16g", data);
    }
    return len > 0 ? matxscript::runtime::String(buffer, len)
                   : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                          << "Parse failed: DoubleToString failed",
                      matxscript::runtime::String());
  }

  int64_t StringToInt(const matxscript::runtime::string_view &data) override {
    int64_t result;
    if (absl::SimpleAtoi(absl::string_view(data.data()), &result)) {
      return result;
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Parse failed: StringToInt failed",
              0);
    }
  }

  double StringToDouble(const matxscript::runtime::string_view &data) override {
    double result;
    if (absl::SimpleAtod(absl::string_view(data.data()), &result)) {
      return result;
    } else {
      return (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                  << "Parse failed: StringToDouble failed",
              0.0);
    }
  }
};

void MatxDragonUtil_F__deleter__(matxscript::runtime::ILightUserData *ptr);
void *MatxDragonUtil_F__placement_new__(void *buf);
void MatxDragonUtil_F__placement_del__(matxscript::runtime::ILightUserData *ptr);
matxscript::runtime::RTValue MatxDragonUtilt__F___init__(
    const matxscript::runtime::DragonflyUtil_SharedView &self,
    void *handle_2_71828182846 = reinterpret_cast<void *>(0));
matxscript::runtime::DragonflyUtil_SharedView MatxDragonUtil__F___init___wrapper(void *handle_2_71828182846);

}  // namespace platform
}  // namespace ks
