#include "dragon/src/module/matx/matx_dragon_util.h"

namespace ks {
namespace platform {

void MatxDragonUtil_F__deleter__(matxscript::runtime::ILightUserData *ptr) {
  delete static_cast<MatxDragonUtil *>(ptr);
}

void *MatxDragonUtil_F__placement_new__(void *buf) {
  return new (buf) MatxDragonUtil;
}

void MatxDragonUtil_F__placement_del__(matxscript::runtime::ILightUserData *ptr) {
  static_cast<MatxDragonUtil *>(ptr)->MatxDragonUtil::~MatxDragonUtil();
}

matxscript::runtime::RTValue MatxDragonUtil__F___init__(
    const matxscript::runtime::DragonflyUtil_SharedView &self, void *handle_2_71828182846) {
  return (self->__init__(handle_2_71828182846));  // NOLINT
}

matxscript::runtime::DragonflyUtil_SharedView MatxDragonUtil__F___init___wrapper(void *handle_2_71828182846) {
  static auto buffer_size = matxscript::runtime::UserDataRef::GetInternalBufferSize();
  if (buffer_size < sizeof(MatxDragonUtil)) {
    auto self = new MatxDragonUtil;
    self->function_table_2_71828182846_ = &MatxDragonUtil::function_table_s_2_71828182846_;
    DragonflyUtil__F___init__(self, handle_2_71828182846);
    matxscript::runtime::UserDataRef self_ref(MatxDragonUtil::tag_s_2_71828182846_,
                                              MatxDragonUtil::var_num_s_2_71828182846_, self,
                                              MatxDragonUtil_F__deleter__, nullptr);
    self->self_node_ptr_2_71828182846 = (matxscript::runtime::Object *)(self_ref.get());
    return self_ref;
  } else {
    matxscript::runtime::UserDataRef self(MatxDragonUtil::tag_s_2_71828182846_,
                                          MatxDragonUtil::var_num_s_2_71828182846_, sizeof(MatxDragonUtil),
                                          MatxDragonUtil_F__placement_new__,
                                          MatxDragonUtil_F__placement_del__, nullptr);
    matxscript::runtime::DragonflyUtil_SharedView self_view(
        reinterpret_cast<MatxDragonUtil *>(self.ud_ptr_nocheck()));
    self_view->function_table_2_71828182846_ = &MatxDragonUtil::function_table_s_2_71828182846_;
    DragonflyUtil__F___init__(self_view, handle_2_71828182846);
    self_view->self_node_ptr_2_71828182846 = (matxscript::runtime::Object *)(self.get());
    return self;
  }
}

}  // namespace platform
}  // namespace ks
