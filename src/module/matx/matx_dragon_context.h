#pragma once

#include <memory>
#include <sstream>
#include <string>
#include <typeinfo>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/matx/matx_dragon_util.h"
#include "dragon/src/module/matx/matx_item_attr_getter.h"
#include "dragon/src/module/matx/matx_item_attr_setter.h"

namespace ks {
namespace platform {
struct MatxDragonContext : public matxscript::runtime::DragonflyContext {
  RecoResultConstIter begin_;
  int64 result_size_;
  absl::flat_hash_map<std::string, const CommonAttr *> import_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, CommonAttr *> export_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, const ItemAttr *> import_item_attr_accessor_map_;
  absl::flat_hash_map<std::string, ItemAttr *> export_item_attr_accessor_map_;

  matxscript::runtime::RTValue __init__(void *handle_2_71828182846) override {  // NOLINT
    this->session_handle_2_71828182846_ = handle_2_71828182846;
    this->util = MatxDragonUtil__F___init___wrapper(this->session_handle_2_71828182846_);
    return (matxscript::runtime::None);
  }

  int64_t GetInt(const matxscript::runtime::string_view &attr_name, int64_t default_value) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != import_common_attr_accessor_map_.end()
               ? iter->second->GetIntValue().value_or(default_value)
               : default_value;
  }

  double GetDouble(const matxscript::runtime::string_view &attr_name, double default_value) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != import_common_attr_accessor_map_.end()
               ? iter->second->GetDoubleValue().value_or(default_value)
               : default_value;
  }

  matxscript::runtime::string_view GetString(const matxscript::runtime::string_view &attr_name,
                                             const matxscript::runtime::string_view &default_value) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_common_attr_accessor_map_.end()) {
      auto string_optional = iter->second->GetStringValue();
      return string_optional ? std::move(matxscript::runtime::string_view(string_optional->data(),
                                                                          string_optional->size()))
                             : default_value;
    }
    return default_value;
  }

  matxscript::runtime::FTList<int64_t> GetIntList(
      const matxscript::runtime::string_view &attr_name) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_common_attr_accessor_map_.end()) {
      auto int_list_optional = iter->second->GetIntListValue();
      return int_list_optional
                 ? std::move(matxscript::runtime::FTList<int64_t>(
                       const_cast<int64_t *>(int_list_optional->begin()), int_list_optional->size()))
                 : std::move(matxscript::runtime::FTList<int64_t>());
    }
    return std::move(matxscript::runtime::FTList<int64_t>());
  }

  matxscript::runtime::FTList<double> GetDoubleList(
      const matxscript::runtime::string_view &attr_name) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_common_attr_accessor_map_.end()) {
      auto double_list_optional = iter->second->GetDoubleListValue();
      return double_list_optional
                 ? std::move(matxscript::runtime::FTList<double>(
                       const_cast<double *>(double_list_optional->begin()), double_list_optional->size()))
                 : std::move(matxscript::runtime::FTList<double>());
    }
    return std::move(matxscript::runtime::FTList<double>());
  }

  matxscript::runtime::FTList<matxscript::runtime::string_view> GetStringList(
      const matxscript::runtime::string_view &attr_name) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_common_attr_accessor_map_.end()) {
      auto string_list_optional = iter->second->GetStringListValue();
      if (string_list_optional) {
        matxscript::runtime::FTList<matxscript::runtime::string_view> result;
        for (const auto &string : *string_list_optional) {
          result.emplace_back(matxscript::runtime::string_view(string.data(), string.size()));
        }
        return std::move(result);
      }
      return std::move(matxscript::runtime::FTList<matxscript::runtime::string_view>());
    }
    return std::move(matxscript::runtime::FTList<matxscript::runtime::string_view>());
  }

  int64_t SetInt(const matxscript::runtime::string_view &attr_name, int64_t value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end() ? iter->second->SetIntValue(value) : 0;
  }

  int64_t SetDouble(const matxscript::runtime::string_view &attr_name, double value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end() ? iter->second->SetDoubleValue(value) : 0;
  }

  int64_t SetString(const matxscript::runtime::string_view &attr_name,
                    const matxscript::runtime::string_view &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end()
               ? iter->second->SetStringValue(std::string(value.data(), value.size()))
               : 0;
  }

  int64_t SetIntList(const matxscript::runtime::string_view &attr_name,
                     const matxscript::runtime::FTList<int64_t> &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end()
               ? iter->second->SetIntListValue(std::move(value.MutableImpl()))
               : 0;
  }

  int64_t SetDoubleList(const matxscript::runtime::string_view &attr_name,
                        const matxscript::runtime::FTList<double> &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end()
               ? iter->second->SetDoubleListValue(std::move(value.MutableImpl()))
               : 0;
  }

  int64_t SetStringList(const matxscript::runtime::string_view &attr_name,
                        const matxscript::runtime::FTList<matxscript::runtime::String> &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != export_common_attr_accessor_map_.end()) {
      std::vector<std::string> string_list;
      string_list.reserve(value.size());
      for (const auto &source : value) {
        string_list.emplace_back(source.data(), source.size());
      }
      return iter->second->SetStringListValue(std::move(string_list));
    }
    return 0;
  }

  int64_t GetItemNum() override {
    return result_size_;
  }

  matxscript::runtime::ItemAttrGetter_SharedView ItemAttrGetter(
      const matxscript::runtime::string_view &attr_name) override {
    auto iter = import_item_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_item_attr_accessor_map_.end()) {
      matxscript::runtime::ItemAttrGetter_SharedView matx_item_attr_getter_shared_view =
          matxscript::runtime::UserDataRef(MatxItemAttrGetter::tag_s_2_71828182846_,
                                           MatxItemAttrGetter::var_num_s_2_71828182846_,
                                           sizeof(MatxItemAttrGetter), MatxItemAttrGetter_F__placement_new__,
                                           MatxItemAttrGetter_F__placement_del__, nullptr);
      static_cast<MatxItemAttrGetter *>(matx_item_attr_getter_shared_view.ptr)->begin_ = begin_;
      static_cast<MatxItemAttrGetter *>(matx_item_attr_getter_shared_view.ptr)->result_size_ = result_size_;
      static_cast<MatxItemAttrGetter *>(matx_item_attr_getter_shared_view.ptr)->accessor_ = iter->second;
      return std::move(matx_item_attr_getter_shared_view);
    }
    return std::move(matxscript::runtime::ItemAttrSetter_SharedView());
  }

  matxscript::runtime::ItemAttrSetter_SharedView ItemAttrSetter(
      const matxscript::runtime::string_view &attr_name) override {
    auto iter = export_item_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != export_item_attr_accessor_map_.end()) {
      matxscript::runtime::ItemAttrSetter_SharedView matx_item_attr_setter_shared_view =
          matxscript::runtime::UserDataRef(MatxItemAttrSetter::tag_s_2_71828182846_,
                                           MatxItemAttrSetter::var_num_s_2_71828182846_,
                                           sizeof(MatxItemAttrSetter), MatxItemAttrSetter_F__placement_new__,
                                           MatxItemAttrSetter_F__placement_del__, nullptr);
      static_cast<MatxItemAttrSetter *>(matx_item_attr_setter_shared_view.ptr)->begin_ = begin_;
      static_cast<MatxItemAttrSetter *>(matx_item_attr_setter_shared_view.ptr)->result_size_ = result_size_;
      static_cast<MatxItemAttrSetter *>(matx_item_attr_setter_shared_view.ptr)->accessor_ = iter->second;
      return std::move(matx_item_attr_setter_shared_view);
    }
    return std::move(matxscript::runtime::ItemAttrSetter_SharedView());
  }

  int64_t GetKey(int64_t item_index) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).item_key
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t GetId(int64_t item_index) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).GetId()
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t GetType(int64_t item_index) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).GetType()
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  int64_t GetReason(int64_t item_index) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).reason
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0);
  }

  double GetScore(int64_t item_index) override {
    return likely(item_index >= 0 && item_index < result_size_)
               ? (*(begin_ + item_index)).score
               : (matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
                      << "Check failed: item_index out of range",
                  0.0);
  }

  int64_t HasValue(const matxscript::runtime::string_view &attr_name) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != import_common_attr_accessor_map_.end() ? iter->second->HasValue() : 0;
  }

  int64_t AppendIntList(const matxscript::runtime::string_view &attr_name, int64_t value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end() ? iter->second->AppendIntListValue(value) : 0;
  }

  int64_t AppendDoubleList(const matxscript::runtime::string_view &attr_name, double value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end() ? iter->second->AppendDoubleListValue(value) : 0;
  }

  int64_t AppendStringList(const matxscript::runtime::string_view &attr_name,
                           const matxscript::runtime::string_view &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    return iter != export_common_attr_accessor_map_.end()
               ? iter->second->AppendStringListValue(std::string(value.data(), value.size()))
               : 0;
  }

  bool GetPtr(const matxscript::runtime::string_view &attr_name,
              const matxscript::runtime::PtrWrapper_SharedView &ptr_wrapper) override {
    auto iter = import_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != import_common_attr_accessor_map_.end()) {
      auto any = iter->second->GetExtraValue();
      return ptr_wrapper->SetPtrWithAny(any);
    }
    return false;
  }

  matxscript::runtime::PtrWrapper_SharedView PtrWrapper(
      const matxscript::runtime::string_view &class_name) override {
    auto &type_map = matxscript::runtime::MatxTypeRegistry::GetTypeMap();
    auto type_map_iter = type_map.find(class_name);
    if (type_map_iter != type_map.end()) {
      auto &matx_type_info = type_map_iter->second;
      return std::move(matx_type_info->CreatePtrWrapper());
    } else {
      matxscript::runtime::LogMessageFatal(__FILE__, __LINE__).stream()
          << "PtrWrapper failed: " << class_name << " is not registered.";
    }
    return std::move(matxscript::runtime::PtrWrapper_SharedView(true));
  }

  int64_t SetPtr(const matxscript::runtime::string_view &attr_name,
                 const matxscript::runtime::PtrWrapper_SharedView &value) override {
    auto iter = export_common_attr_accessor_map_.find(absl::string_view(attr_name.data(), attr_name.size()));
    if (iter != export_common_attr_accessor_map_.end()) {
      value->SetCommonPtr(iter->second);
      return 1;
    }
    return 0;
  }
};

void MatxDragonContext_F__deleter__(matxscript::runtime::ILightUserData *ptr);
void *MatxDragonContext_F__placement_new__(void *buf);
void MatxDragonContext_F__placement_del__(matxscript::runtime::ILightUserData *ptr);
matxscript::runtime::RTValue MatxDragonContext__F___init__(
    const matxscript::runtime::DragonflyContext_SharedView &self,
    void *handle_2_71828182846 = reinterpret_cast<void *>(0));
matxscript::runtime::DragonflyContext_SharedView MatxDragonContext__F___init___wrapper(
    void *handle_2_71828182846);

}  // namespace platform
}  // namespace ks
