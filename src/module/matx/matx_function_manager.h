#pragma once

#include <dlfcn.h>
#include <sys/file.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <cstddef>
#include <cstdlib>
#include <filesystem>
#include <fstream>
#include <sstream>
#include <string>

#include "base/thread/internal/synchronization/lock.h"
#include "dragon/src/util/logging_util.h"

#include "third_party/folly/concurrency/ConcurrentHashMap.h"
#include "third_party/folly/container/F14Map.h"

#include "matxscript/runtime/codegen_all_includes.h"
#include "matxscript/runtime/func_registry_names_io.h"
#include "matxscript/runtime/function_name_rules.h"

#define PYUDF_LOCK_FILE_NAME "pyudf.lock"
#define URL_PREFIX "http://bs3-hb1.internal/reco-dragon-matx-so/"
#define MATX_MOUNT_PATH "/data/dragon_matx/"
#define DOWNLOAD_MAX_RETRY 3

namespace ks {
namespace platform {
extern "C" void *__matxscript_module_ctx;  // NOLINT

class MatxFunctionManager {
 public:
  static MatxFunctionManager *GetInstance() {
    static MatxFunctionManager instance;
    return &instance;
  }

  ~MatxFunctionManager() {}

  bool StaticInit();

  bool DynamicInit(const std::string &function_set, const std::string &so_name,
                   const std::string &remote_so_name) {
    base::AutoLock lock(lock_);
    // 是否 set 过 dso 目录
    if (!SetDsoDir()) {
      return false;
    }
    // 创建 dso 目录
    if (!MakeDirectory(dso_dir_)) {
      return false;
    }

    // 进程文件锁
    int lock_fd = open(std::string(dso_dir_ + PYUDF_LOCK_FILE_NAME).c_str(), O_CREAT | O_RDWR, 0666);
    if (lock_fd == -1) {
      LOG(ERROR) << "MatxFunctionManager init error! open lock: " << dso_dir_ << PYUDF_LOCK_FILE_NAME
                 << " failed!";
      return false;
    }
    if (flock(lock_fd, LOCK_EX) == -1) {
      LOG(ERROR) << "MatxFunctionManager init error! get lock failed!";
      return false;
    }

    // 该 function set 初始化过则跳过
    if (function_map_.find(function_set) != function_map_.end()) {
      ReleaseLock(lock_fd);
      return true;
    }

    std::string so_path = dso_dir_ + so_name;
    // 使用远程 so
    if (!remote_so_name.empty()) {
      if (!mount_checked_) {
        has_mount_ = MountMatx();
        mount_checked_ = true;
      }
      if (!CheckSoAndLink(remote_so_name, so_path)) {
        ReleaseLock(lock_fd);
        return false;
      }
    }

    std::ifstream so_file(so_path);
    if (!so_file) {
      LOG(ERROR) << "MatxFunctionManager init error! so file not exists!, path:" << so_path;
      ReleaseLock(lock_fd);
      return false;
    }
    void *so_handle = dlopen(so_path.data(), RTLD_LAZY);
    if (!so_handle) {
      LOG(ERROR) << "MatxFunctionManager init error! Loading so failed, path:" << so_path
                 << ", error: " << dlerror();
      ReleaseLock(lock_fd);
      return false;
    }
    const char *func_registry_name = "__matxscript_func_registry__";
    void *func_registry_addr = dlsym(so_handle, func_registry_name);
    if (!func_registry_addr) {
      LOG(ERROR) << "MatxFunctionManager init error! Getting function registry address failed!";
      dlclose(so_handle);
      ReleaseLock(lock_fd);
      return false;
    }
    MATXScriptFuncRegistry *func_registry = reinterpret_cast<MATXScriptFuncRegistry *>(func_registry_addr);
    AddFunction(func_registry, function_set);
    ReleaseLock(lock_fd);
    return true;
  }

  MATXScriptBackendPackedCFunc GetFunction(const std::string &function_set,
                                           const std::string &function_name) {
    auto function_set_it = function_map_.find(function_set);
    if (function_set_it == function_map_.end()) {
      LOG(ERROR) << "MatxFunctionManager can not get function set! function name: " << function_set;
      return nullptr;
    }
    auto function_it = function_set_it->second.find(function_name);
    if (function_it == function_set_it->second.end()) {
      LOG(ERROR) << "MatxFunctionManager can not get function ptr! function name: " << function_name
                 << ", in function set: " << function_set;
      return nullptr;
    }
    return function_it->second;
  }

 private:
  void AddFunction(MATXScriptFuncRegistry *func_reg, const std::string &function_set_name) {
    folly::F14FastMap<std::string, MATXScriptBackendPackedCFunc> function_table;
    matxscript::runtime::string_view class_name =
        matxscript::runtime::string_view(function_set_name.data(), function_set_name.size());
    auto function_names = matxscript::runtime::ReadFuncRegistryNames(func_reg->names);
    for (size_t i = 0; i < function_names.size(); ++i) {
      matxscript::runtime::string_view name_unbound = function_names[i];
      if (matxscript::runtime::FunctionNameRules::is_class_method(class_name, name_unbound)) {
        auto name_bound =
            matxscript::runtime::FunctionNameRules::remove_class_prefix(class_name, name_unbound);
        function_table.emplace(std::string(name_bound.data(), name_bound.size()), func_reg->funcs[i]);
      } else {
        function_table.emplace(std::string(name_unbound.data(), name_unbound.size()), func_reg->funcs[i]);
      }
    }
    function_map_.insert(function_set_name, function_table);
  }

  bool DownloadWithRetry(const std::string &object_key, const std::string &local_path) {
    std::string url = URL_PREFIX + object_key;
    std::string dir_path;
    std::string file_name;
    GetDir(local_path, &dir_path, &file_name);
    if (!MakeDirectory(dir_path)) {
      return false;
    }
    for (auto i = 0; i < DOWNLOAD_MAX_RETRY; ++i) {
      LOG(INFO) << "MatxFunctionManager download " + std::to_string(i) + " " + url + " to " + local_path;
      std::string cmd = "wget --header=\"service: krp_common_leaf\" -N -O " + local_path + " " + url;
      int result = std::system(cmd.c_str());
      if (result == 0) {
        return true;
      }
    }
    LOG(ERROR) << "MatxFunctionManager download " + url + " to " + local_path + " failed!";
    return false;
  }

  void GetDir(const std::string &file_path, std::string *dir_path, std::string *file_name) {
    std::size_t pos = file_path.rfind('/');
    if (pos != std::string::npos) {  // 获取文件名
      *file_name = file_path.substr(pos + 1);
    }
    if (pos != 0) {  // 获取文件夹路径
      *dir_path = file_path.substr(0, pos);
    } else {
      *dir_path = "/";
    }
  }

  bool CreateLink(const std::string &from, const std::string &to) {
    LOG(INFO) << "MatxFunctionManager CreateLink: " << from << " " << to;
    unlink(to.c_str());
    if (std::ifstream(from).good() && symlink(from.c_str(), to.c_str()) == 0) {
      return true;
    }
    LOG(ERROR) << "MatxFunctionManager create link " + from + " to " + to + " error.";
    return false;
  }

  bool MountMatx() {
    struct stat info;
    if (stat(MATX_MOUNT_PATH, &info) != 0 || !S_ISDIR(info.st_mode)) {
      LOG(INFO) << "MatxFunctionManager no mount " MATX_MOUNT_PATH;
      return false;
    }
    if (access(MATX_MOUNT_PATH, R_OK | W_OK) == -1) {
      LOG(INFO) << "MatxFunctionManager does not have read/write permission to " MATX_MOUNT_PATH;
      return false;
    }
    return true;
  }

  bool CheckSoAndLink(const std::string &object_key, const std::string &link_path) {
    LOG(INFO) << "MatxFunctionManager CheckSoAndLink: " << object_key << " " << link_path;
    std::string download_object_path;
    if (has_mount_) {
      download_object_path = MATX_MOUNT_PATH + object_key;
    } else {
      download_object_path = dso_dir_ + object_key;
    }
    if (!std::ifstream(download_object_path).good()) {
      LOG(INFO) << "MatxFunctionManager no file, begin download " << object_key << " " << link_path;
      if (!DownloadWithRetry(object_key, download_object_path)) {
        return false;
      }
    }
    std::string so_path;

    // 是压缩文件时
    if (EndsWith(download_object_path, ".tar.gz")) {
      std::filesystem::path path_obj(download_object_path);
      std::string parent_dir = path_obj.parent_path().string();
      std::string filename = path_obj.stem().stem().string();
      so_path = (std::filesystem::path(parent_dir) / (filename + ".so")).string();
      std::string tar_cmd =
          "tar -xzf \"" + download_object_path + "\" -C \"" + parent_dir + "\" --strip-components=0";
      if (std::system(tar_cmd.c_str()) != 0) {
        LOG(ERROR) << "MatxFunctionManager tar " << download_object_path << " failed.";
        return false;
      }
      if (!std::ifstream(so_path).good()) {
        LOG(ERROR) << "MatxFunctionManager " << so_path << " not found.";
        return false;
      }
    } else {
      so_path = download_object_path;
    }

    return CreateLink(so_path, link_path);
  }

  bool MakeDirectory(const std::string &path) {
    if (std::ifstream(path).good()) {
      return true;
    }
    std::string command = "mkdir -p " + path;
    int result = std::system(command.c_str());
    if (result == 0) {
      LOG(INFO) << "MatxFunctionManager mkdir " << path;
      return true;
    } else {
      LOG(ERROR) << "MatxFunctionManager mkdir " << path << " failed.";
      return false;
    }
  }

  bool SetDsoDir() {
    if (dso_dir_.empty()) {
      // 适配 pywrap 运行方式，如 playground
      char *dso_path = getenv("DRAGON_MATX_DSO_PATH");
      if (dso_path != nullptr) {
        dso_dir_ = dso_path;
        return true;
      }

      char cwd[1024];
      if (!getcwd(cwd, sizeof(cwd))) {
        LOG(ERROR) << "MatxFunctionManager failed to get current working directory: " << strerror(errno);
        return false;
      }
      std::string bin_dir = cwd;
      dso_dir_ = bin_dir.substr(0, bin_dir.rfind("/")) + "/dso/";
    }
    return true;
  }

  void ReleaseLock(int lock_fd) {
    // 释放进程文件锁
    flock(lock_fd, LOCK_UN);
    close(lock_fd);
  }

  bool EndsWith(const std::string &str, const std::string &suffix) {
    if (str.length() < suffix.length()) return false;
    return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
  }

  bool mount_checked_ = false;
  bool has_mount_ = false;
  std::string dso_dir_;
  bool static_init_success_ = false;
  base::Lock lock_;
  folly::ConcurrentHashMap<std::string, bool> remote_so_downloaded_;
  folly::ConcurrentHashMap<std::string, folly::F14FastMap<std::string, MATXScriptBackendPackedCFunc>>
      function_map_;
};

}  // namespace platform
}  // namespace ks
