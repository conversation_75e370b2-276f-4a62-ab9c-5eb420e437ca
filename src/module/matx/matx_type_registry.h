#pragma once

#include <boost/any.hpp>
#include <memory>
#include <typeinfo>
#include <utility>
#include "dragon/src/core/common_reco_util.h"
#include "matxscript/runtime/dragon/ptr_wrapper.h"
#include "matxscript/runtime/dragon/type_registry.h"

namespace ks {
namespace platform {

struct DragonMatxTypeInfo : public matxscript::runtime::MatxTypeInfo {
  typedef matxscript::runtime::PtrWrapper_SharedView (*AnyToPtrWrapper)(
      const boost::any *, const matxscript::runtime::MatxTypeInfo *);
  typedef bool (*SetPtrWithAny)(const boost::any *, const matxscript::runtime::PtrWrapper_SharedView &);
  typedef void (*CommonPtrSetter)(const matxscript::runtime::PtrWrapper_SharedView &, AttrValue *);
  typedef void (*ItemPtrSetter)(const matxscript::runtime::PtrWrapper_SharedView &, const CommonRecoResult &,
                                AttrValue *);

  AnyToPtrWrapper any_to_ptr_wrapper_;
  SetPtrWithAny set_ptr_with_any_;
  CommonPtrSetter common_ptr_setter_;
  ItemPtrSetter item_ptr_setter_;

  DragonMatxTypeInfo(const matxscript::runtime::String &name, const std::type_info &type, uint32_t tag,
                     uint32_t var_num, size_t buf_size, matxscript::runtime::FUserDataPlacementNew creator,
                     matxscript::runtime::FUserDataDeleter deleter, FunctionTable *function_table,
                     AnyToPtrWrapper any_to_ptr_wrapper, SetPtrWithAny set_ptr_with_any,
                     CommonPtrSetter common_ptr_setter, ItemPtrSetter item_ptr_setter)
      : matxscript::runtime::MatxTypeInfo(name, type, tag, var_num, buf_size, creator, deleter,
                                          function_table)
      , any_to_ptr_wrapper_(any_to_ptr_wrapper)
      , set_ptr_with_any_(set_ptr_with_any)
      , common_ptr_setter_(common_ptr_setter)
      , item_ptr_setter_(item_ptr_setter) {}
};

#define REMOVE_PARENS(...) __VA_ARGS__

// 辅助宏：将类型名转换为无括号形式，并用括号保护模板参数
#define PROTECT_TYPE(type) REMOVE_PARENS type

#define MATX_CUSTOM_CLASS_OVERRIDE_FUNCTION_DEFINE(type)                                                    \
  inline const std::type_info *GetTypeInfo() override {                                                     \
    return &typeid(std::shared_ptr<const PROTECT_TYPE(type)>);                                              \
  }                                                                                                         \
  inline PROTECT_TYPE(type) * GetPtr() noexcept {                                                           \
    return static_cast<PROTECT_TYPE(type) *>(contain_ptr_.get());                                           \
  }                                                                                                         \
  bool New() override {                                                                                     \
    contain_ptr_ = std::make_shared<PROTECT_TYPE(type)>();                                                  \
    return true;                                                                                            \
  }                                                                                                         \
  bool SetPtrWithAny(const void *any) override {                                                            \
    try {                                                                                                   \
      const std::shared_ptr<const PROTECT_TYPE(type)> *ptr =                                                \
          boost::any_cast<std::shared_ptr<const PROTECT_TYPE(type)>>(static_cast<const boost::any *>(any)); \
      if (ptr) {                                                                                            \
        contain_ptr_ =                                                                                      \
            std::shared_ptr<void>(*ptr, const_cast<void *>(static_cast<const void *>(ptr->get())));         \
      }                                                                                                     \
    } catch (const boost::bad_any_cast &e) {                                                                \
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();                                        \
      return false;                                                                                         \
    }                                                                                                       \
    return contain_ptr_ ? true : false;                                                                     \
  }                                                                                                         \
  void SetCommonPtr(void *accessor) override {                                                              \
    if (contain_ptr_) {                                                                                     \
      auto *raw_ptr = static_cast<PROTECT_TYPE(type) *>(contain_ptr_.get());                                \
      static_cast<AttrValue *>(accessor)->SetPtrValue<std::shared_ptr<PROTECT_TYPE(type)>>(                 \
          std::shared_ptr<PROTECT_TYPE(type)>(std::move(contain_ptr_), raw_ptr));                           \
    }                                                                                                       \
  }                                                                                                         \
  void SetItemPtr(const void *result, void *accessor) override {                                            \
    if (contain_ptr_) {                                                                                     \
      auto *raw_ptr = static_cast<PROTECT_TYPE(type) *>(contain_ptr_.get());                                \
      static_cast<const CommonRecoResult *>(result)->SetPtrAttr(                                            \
          static_cast<AttrValue *>(accessor),                                                               \
          std::shared_ptr<PROTECT_TYPE(type)>(std::move(contain_ptr_), raw_ptr));                           \
    }                                                                                                       \
  }

#define MATX_REGISTER_PTR_TYPE_BUILTIN(custom_class, prefix, type)                                           \
  static_assert(std::is_base_of<matxscript::runtime::PtrWrapper, custom_class>::value,                       \
                #custom_class " must be derived from matxscript::runtime::PtrWrapper");                      \
                                                                                                             \
  inline void *prefix##custom_class##_F__placement_new__(void *buf) {                                        \
    return new (buf) prefix##custom_class;                                                                   \
  }                                                                                                          \
                                                                                                             \
  inline void prefix##custom_class##_F__placement_del__(matxscript::runtime::ILightUserData *ptr) {          \
    static_cast<prefix##custom_class *>(ptr)->prefix##custom_class::~prefix##custom_class();                 \
  }                                                                                                          \
                                                                                                             \
  matxscript::runtime::PtrWrapper_SharedView prefix##custom_class##_F__AnyToPtrWrapper__(                    \
      const boost::any *any, const matxscript::runtime::MatxTypeInfo *matx_type_info) {                      \
    matxscript::runtime::UserDataRef ref(matx_type_info->tag_, matx_type_info->var_num_,                     \
                                         matx_type_info->buf_size_, matx_type_info->creator_,                \
                                         matx_type_info->deleter_, nullptr);                                 \
    void *ud_ref = ref.ud_ptr_nocheck();                                                                     \
    reinterpret_cast<matxscript::runtime::IUserDataRoot *>(ud_ref)->function_table_2_71828182846_ =          \
        matx_type_info->function_table_;                                                                     \
    try {                                                                                                    \
      const std::shared_ptr<const PROTECT_TYPE(type)> *ptr =                                                 \
          boost::any_cast<std::shared_ptr<const PROTECT_TYPE(type)>>(any);                                   \
      static_cast<matxscript::runtime::PtrWrapper *>(ud_ref)->contain_ptr_ =                                 \
          std::const_pointer_cast<void>(std::static_pointer_cast<const void>(*ptr));                         \
    } catch (const boost::bad_any_cast &e) {                                                                 \
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();                                         \
    }                                                                                                        \
    return std::move(matxscript::runtime::PtrWrapper_SharedView(                                             \
        static_cast<matxscript::runtime::PtrWrapper *>(ud_ref), ref));                                       \
  }                                                                                                          \
                                                                                                             \
  bool prefix##custom_class##_F__SetPtrWithAny__(                                                            \
      const boost::any *any, const matxscript::runtime::PtrWrapper_SharedView &ptr_wrapper) {                \
    try {                                                                                                    \
      const std::shared_ptr<const PROTECT_TYPE(type)> *ptr =                                                 \
          boost::any_cast<std::shared_ptr<const PROTECT_TYPE(type)>>(any);                                   \
      ptr_wrapper->contain_ptr_ =                                                                            \
          std::shared_ptr<void>(*ptr, const_cast<void *>(static_cast<const void *>(ptr->get())));            \
    } catch (const boost::bad_any_cast &e) {                                                                 \
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();                                         \
      return false;                                                                                          \
    }                                                                                                        \
    return true;                                                                                             \
  }                                                                                                          \
                                                                                                             \
  inline void prefix##custom_class##_F__SetPtrCommonValue__(                                                 \
      const matxscript::runtime::PtrWrapper_SharedView &ptr_wrapper, AttrValue *accessor) {                  \
    if (ptr_wrapper->contain_ptr_) {                                                                         \
      auto *raw_ptr = static_cast<PROTECT_TYPE(type) *>(ptr_wrapper->contain_ptr_.get());                    \
      accessor->SetPtrValue<std::shared_ptr<PROTECT_TYPE(type)>>(                                            \
          std::shared_ptr<PROTECT_TYPE(type)>(std::move(ptr_wrapper->contain_ptr_), raw_ptr));               \
    }                                                                                                        \
  }                                                                                                          \
                                                                                                             \
  inline void prefix##custom_class##_F__SetPtrItemValue__(                                                   \
      const matxscript::runtime::PtrWrapper_SharedView &ptr_wrapper, const CommonRecoResult &result,         \
      AttrValue *accessor) {                                                                                 \
    if (ptr_wrapper->contain_ptr_) {                                                                         \
      auto *raw_ptr = static_cast<PROTECT_TYPE(type) *>(ptr_wrapper->contain_ptr_.get());                    \
      result.SetPtrAttr(static_cast<AttrValue *>(accessor),                                                  \
                        std::shared_ptr<PROTECT_TYPE(type)>(std::move(ptr_wrapper->contain_ptr_), raw_ptr)); \
    }                                                                                                        \
  }                                                                                                          \
                                                                                                             \
  static MATXSCRIPT_ATTRIBUTE_UNUSED auto __dragon_##prefix##custom_class##_matx_type_info_ =                \
      new DragonMatxTypeInfo(                                                                                \
          #custom_class, typeid(std::shared_ptr<const PROTECT_TYPE(type)>),                                  \
          custom_class::tag_s_2_71828182846_, custom_class::var_num_s_2_71828182846_,                        \
          sizeof(prefix##custom_class), prefix##custom_class##_F__placement_new__,                           \
          prefix##custom_class##_F__placement_del__,                                                         \
          &matxscript::runtime::MatxTypeInfo::empty_function_table,                                          \
          prefix##custom_class##_F__AnyToPtrWrapper__, prefix##custom_class##_F__SetPtrWithAny__,            \
          prefix##custom_class##_F__SetPtrCommonValue__, prefix##custom_class##_F__SetPtrItemValue__);       \
  static MATXSCRIPT_ATTRIBUTE_UNUSED auto &__dragon_##MATX_TYPE_REGISTRY##_##prefix##custom_class =          \
      ::matxscript::runtime::MatxTypeRegistry::Register(__dragon_##prefix##custom_class##_matx_type_info_);

}  // namespace platform
}  // namespace ks
