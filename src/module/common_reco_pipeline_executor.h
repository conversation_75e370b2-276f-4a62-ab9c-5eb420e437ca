#pragma once

#include <algorithm>
#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "framework/perf_adapter.h"
#include "kess/common/perf.h"

#include "absl/strings/substitute.h"
#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/core/common_reco_pipeline.h"
#include "dragon/src/core/pipeline_manager.h"
#include "dragon/src/core/sub_flow_thread_pool_manager.h"
#include "dragon/src/util/common_util.h"
#include "kess/rpc/perf.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {

typedef PipelineManager<CommonRecoPipeline> CommonRecoPipelineManager;

/**
 * 对于 Pipeline，Context，Container 三者的封装，便于快速
 * - 构造 Pipeline
 * - 调用 Pipeline
 * - 操作 Context 和 Container
 * - 访问 Context 和 Container
 *
 * 其中操作和访问 Context 和 Container 的 API 主要用于 debug
 * 和单元测试，故易用性比性能更重要。
 */
class CommonRecoLeafPipelineExecutor {
 public:
  static void SetServiceIdentifier(const std::string &identifier) {
    ks::platform::GlobalHolder::SetServiceIdentifier(identifier);
    ks::kess::rpc::Perf::SetCaller({"dragonfly_py_runner", identifier, "", serving_base::GetHostName(), "0"});
  }

  class Item {
   public:
    Item(CommonRecoLeafPipelineExecutor *exec, CommonRecoResult result) : exec_(exec), result_(result) {}

    Item(Item &&item) : exec_(item.exec_), result_(item.result_) {}

    Item &SetInt(const std::string &name, int64 val) {
      exec_->context_.SetIntItemAttr(GetItemKey(), name, val);
      return *this;
    }

    Item &SetDouble(const std::string &name, double val) {
      exec_->context_.SetDoubleItemAttr(GetItemKey(), name, val);
      return *this;
    }

    Item &SetString(const std::string &name, std::string val) {
      exec_->context_.SetStringItemAttr(GetItemKey(), name, std::move(val));
      return *this;
    }

    Item &SetIntList(const std::string &name, std::vector<int64> val_list) {
      exec_->context_.SetIntListItemAttr(GetItemKey(), name, std::move(val_list));
      return *this;
    }

    Item &SetDoubleList(const std::string &name, std::vector<double> val_list) {
      exec_->context_.SetDoubleListItemAttr(GetItemKey(), name, std::move(val_list));
      return *this;
    }

    Item &SetStringList(const std::string &name, std::vector<std::string> val_list) {
      exec_->context_.SetStringListItemAttr(GetItemKey(), name, std::move(val_list));
      return *this;
    }

    // proxies
    auto HasAttr(const std::string &name) const {
      auto p = exec_->context_.HasItemAttr(GetItemKey(), name);
      return p;
    }

    auto GetInt(const std::string &name) const {
      auto p = exec_->context_.GetIntItemAttr(GetItemKey(), name);
      return p;
    }

    auto GetDouble(const std::string &name) const {
      return exec_->context_.GetDoubleItemAttr(GetItemKey(), name);
    }

    auto GetString(const std::string &name) const {
      return exec_->context_.GetStringItemAttr(GetItemKey(), name);
    }

    auto GetIntList(const std::string &name) const {
      return exec_->context_.GetIntListItemAttr(GetItemKey(), name);
    }

    auto GetDoubleList(const std::string &name) const {
      return exec_->context_.GetDoubleListItemAttr(GetItemKey(), name);
    }

    auto GetStringList(const std::string &name) const {
      return exec_->context_.GetStringListItemAttr(GetItemKey(), name);
    }

    template <typename T>
    const T *GetPtrAttr(const std::string &name) const {
      return exec_->context_.GetPtrItemAttr<T>(GetItemKey(), name);
    }

    template <typename T>
    std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *> GetProtoMessagePtrAttr(
        const std::string &name) const {
      return exec_->context_.GetProtoMessagePtrItemAttr<T>(GetItemKey(), name);
    }

    // setter/getter with item_attr
    Item &SetInt(ItemAttr *item_attr, int64 val) {
      result_.SetIntAttr(item_attr, val);
      return *this;
    }

    Item &SetDouble(ItemAttr *item_attr, double val) {
      result_.SetDoubleAttr(item_attr, val);
      return *this;
    }

    Item &SetString(ItemAttr *item_attr, std::string val) {
      result_.SetStringAttr(item_attr, std::move(val));
      return *this;
    }

    Item &SetIntList(ItemAttr *item_attr, std::vector<int64> val_list) {
      result_.SetIntListAttr(item_attr, std::move(val_list));
      return *this;
    }

    Item &SetDoubleList(ItemAttr *item_attr, std::vector<double> val_list) {
      result_.SetDoubleListAttr(item_attr, std::move(val_list));
      return *this;
    }

    Item &SetStringList(ItemAttr *item_attr, std::vector<std::string> val_list) {
      result_.SetStringListAttr(item_attr, std::move(val_list));
      return *this;
    }

    template <typename T>
    Item &SetPtrAttr(ItemAttr *item_attr, T &&ptr) {
      result_.SetPtrAttr(item_attr, std::forward<T>(ptr));
      return *this;
    }

    auto HasAttr(const ItemAttr *item_attr) const {
      return result_.HasAttr(item_attr);
    }

    auto GetInt(const ItemAttr *item_attr) const {
      return result_.GetIntAttr(item_attr);
    }

    auto GetDouble(const ItemAttr *item_attr) const {
      return result_.GetDoubleAttr(item_attr);
    }

    auto GetString(const ItemAttr *item_attr) const {
      return result_.GetStringAttr(item_attr);
    }

    auto GetIntList(const ItemAttr *item_attr) const {
      return result_.GetIntListAttr(item_attr);
    }

    auto GetDoubleList(const ItemAttr *item_attr) const {
      return result_.GetDoubleListAttr(item_attr);
    }

    auto GetStringList(const ItemAttr *item_attr) const {
      return result_.GetStringListAttr(item_attr);
    }

    template <typename T>
    const T *GetPtrAttr(const ItemAttr *item_attr) const {
      return result_.GetPtrAttr<T>(item_attr);
    }

    template <typename T>
    T *GetMutablePtrAttr(ItemAttr *item_attr) {
      return result_.GetMutablePtrAttr<T>(item_attr);
    }

    template <typename T>
    std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *> GetProtoMessagePtrAttr(
        const ItemAttr *item_attr) const {
      return result_.GetProtoMessagePtrAttr<T>(item_attr);
    }

    std::string GetAttrDebugString(const std::string &name) const {
      if (auto *attr = exec_->context_.GetItemAttr(name)) {
        return attr->GetDebugString(result_);
      } else {
        return "";
      }
    }

    double GetScore() const {
      return result_.score;
    }

    uint64 GetItemKey() const {
      return result_.item_key;
    }

    uint64 GetItemId() const {
      return result_.GetId();
    }

    int GetItemType() const {
      return result_.GetType();
    }

    int GetReason() const {
      return result_.reason;
    }

    std::string GetItemInfo() {
      if (GetItemType() != 0) {
        return absl::Substitute("key=$0 id=$1 type=$2 reason=$3 score=$4", GetItemKey(), GetItemId(),
                                GetItemType(), GetReason(), GetScore());
      } else {
        return absl::Substitute("key=$0 reason=$1 score=$2", GetItemKey(), GetReason(), GetScore());
      }
    }

   private:
    CommonRecoLeafPipelineExecutor *exec_;
    CommonRecoResult result_;

    DISALLOW_COPY_AND_ASSIGN(Item);
  };

  explicit CommonRecoLeafPipelineExecutor(const base::Json *config, bool check_valid = true,
                                          const std::unordered_set<std::string> *target_pipelines = nullptr)
      : check_valid_(check_valid) {
    // XXX(huiyiqun): 我们不能保证 CommonLeaf 的 Processor 可以并行构建，在 CommonLeaf 里 pipeline 是
    // 串行构建的，在 Executor 的应用场景下，经常会出现并行初始化的问题，为了避免出问题，这里我们加一个
    // mutex，避免并行初始化出问题。
    static std::mutex pipeline_manager_mutex;
    std::lock_guard<std::mutex> lock(pipeline_manager_mutex);

    // XXX(huiyiqun): kess client 的 perflog 打点依赖于下面的代码，在服务场景下，只要创建 application 就会
    // 自动调用，在 runner 场景下，需要手动调用。
    ks::kess::common::Perf::Init(ks::framework::PerfAdapter::GetInstance());

    std::shared_ptr<base::Json> fake_config = std::make_shared<base::Json>(base::StringToJson("{}"));
    fake_config->set("pipeline_manager_config", base::Json(base::StringToJson(config->ToString())));
    GlobalHolder::SetupFakeJsonConfig(fake_config);
    GlobalHolder::RegisterDynamicProto();
    pipeline_manager_ = std::make_unique<CommonRecoPipelineManager>();
    if (!pipeline_manager_->Initialize(config, target_pipelines)) {
      if (check_valid_) {
        LOG(FATAL) << "PipelineManager init failed!";
      } else {
        LOG(ERROR) << "CHECK FAIL! PipelineManager init failed!";
      }
      pipeline_manager_.reset();
    }

    // 初始化全局线程池
    ks::platform::SubFlowThreadPoolManager::GetInstance()->Initialize();

    context_.SetRequest(&request_);
    request_.set_request_type("default");
    context_.SetupAttrTypesFromConfig(GlobalHolder::GetDynamicJsonConfig().get());
  }

  explicit CommonRecoLeafPipelineExecutor(const std::string &json, bool check_valid = true,
                                          const std::unordered_set<std::string> *target_pipelines = nullptr)
      : CommonRecoLeafPipelineExecutor(std::make_unique<base::Json>(base::StringToJson(json)).get(),
                                       check_valid, target_pipelines) {}

  ~CommonRecoLeafPipelineExecutor() {
    LOG_EVERY_N(INFO, 1000) << "destruct CommonRecoLeafPipelineExecutor...";
    if (pipeline_manager_) {
      for (auto *pipeline : *pipeline_manager_) {
        for (auto *processor : pipeline->GetProcessors()) {
          processor->Purge()();
        }
      }
    }
  }

  void SetMainTable(const std::string &table_name) {
    context_.SetMainTable(table_name);
  }

  Item AddItem(uint64 item_key, int reason = 0, double score = 0.0) {
    return Item(this, context_.AddCommonRecoResult(item_key, reason, score));
  }

  Item AddItemByType(int item_type, uint64 item_id, int reason = 0, double score = 0.0) {
    auto item_key = ks::platform::Util::GenKeysign(item_type, item_id);
    return AddItem(item_key, reason, score);
  }

  Item NewItem(uint64 item_key, int reason = 0, double score = 0.0) {
    return Item(this, context_.NewCommonRecoResult(item_key, reason, score));
  }

  Item NewItemByType(int item_type, uint64 item_id, int reason = 0, double score = 0.0) {
    auto item_key = ks::platform::Util::GenKeysign(item_type, item_id);
    return NewItem(item_key, reason, score);
  }

  ItemAttr *GetItemAttrAccessor(std::string attr_name) {
    return context_.GetItemAttrAccessor(attr_name);
  }

  CommonRecoLeafPipelineExecutor &SetBrowseSet(std::vector<int64> item_list) {
    browse_set_ = item_list;
    for (auto item_key : browse_set_) {
      context_.AddItemToBrowseSet(item_key);
    }
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetInt(const std::string &name, int64 val) {
    context_.SetIntCommonAttr(name, val);
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetDouble(const std::string &name, double val) {
    context_.SetDoubleCommonAttr(name, val);
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetString(const std::string &name, std::string val) {
    context_.SetStringCommonAttr(name, std::move(val));
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetIntList(const std::string &name, std::vector<int64> val_list) {
    context_.SetIntListCommonAttr(name, std::move(val_list));
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetDoubleList(const std::string &name, std::vector<double> val_list) {
    context_.SetDoubleListCommonAttr(name, std::move(val_list));
    return *this;
  }

  CommonRecoLeafPipelineExecutor &SetStringList(const std::string &name, std::vector<std::string> val_list) {
    context_.SetStringListCommonAttr(name, std::move(val_list));
    return *this;
  }

  template <typename T>
  void SetCommonAttr(const std::string &attr_name, T &&value) {
    return SetAnyAttr<T>(attr_name, std::forward<T>(value));
  }

  template <typename T>
  void SetAnyAttr(const std::string &attr_name, T &&value) {
    return context_.SetExtraCommonAttr(attr_name, std::forward<T>(value));
  }

  template <typename Ptr>
  void SetPtrCommonAttr(const std::string &key, Ptr &&ptr) {
    return SetPtrAttr<Ptr>(key, std::forward<Ptr>(ptr));
  }

  template <typename Ptr>
  void SetPtrAttr(const std::string &key, Ptr &&ptr) {
    return context_.SetPtrCommonAttr(key, std::forward<Ptr>(ptr));
  }

  bool HasAttr(const std::string &name) const {
    return context_.HasCommonAttr(name);
  }

  absl::optional<int64> GetInt(const std::string &name) const {
    return context_.GetIntCommonAttr(name);
  }

  absl::optional<double> GetDouble(const std::string &name) const {
    return context_.GetDoubleCommonAttr(name);
  }

  absl::optional<absl::string_view> GetString(const std::string &name) const {
    return context_.GetStringCommonAttr(name);
  }

  absl::optional<absl::Span<const int64>> GetIntList(const std::string &name) const {
    return context_.GetIntListCommonAttr(name);
  }

  absl::optional<absl::Span<const double>> GetDoubleList(const std::string &name) const {
    return context_.GetDoubleListCommonAttr(name);
  }

  absl::optional<std::vector<absl::string_view>> GetStringList(const std::string &name) const {
    return context_.GetStringListCommonAttr(name);
  }

  template <typename T>
  const T *GetAnyCommonAttr(const std::string &name) const {
    return GetAnyAttr<T>(name);
  }

  template <typename T>
  const T *GetAnyAttr(const std::string &name) const {
    return context_.GetAnyCommonAttr<T>(name);
  }

  template <typename T>
  const T *GetPtrCommonAttr(const std::string &name) const {
    return GetPtrAttr<T>(name);
  }

  template <typename T>
  const T *GetPtrAttr(const std::string &name) const {
    return context_.GetPtrCommonAttr<T>(name);
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *> GetProtoMessagePtrAttr(
      const std::string &name) const {
    auto *ptr = GetPtrAttr<google::protobuf::Message>(name);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrAttr<T>(name);
    }
  }

  std::string GetAttrDebugString(const std::string &name) const {
    if (auto *attr = context_.GetCommonAttr(name)) {
      return attr->GetDebugString();
    } else {
      return "";
    }
  }

  std::vector<uint64> GetItemKeyList() {
    std::vector<uint64> res;
    const auto &results = context_.GetCommonRecoResults();
    res.resize(results.size());
    std::transform(results.cbegin(), results.cend(), res.begin(),
                   [](const CommonRecoResult &result) { return result.item_key; });
    return res;
  }

  std::vector<Item> GetItemList() {
    std::vector<Item> ret;
    const auto &results = context_.GetCommonRecoResults();
    ret.reserve(results.size());
    for (const auto &result : results) {
      ret.emplace_back(this, result);
    }
    return ret;
  }

  size_t GetItemNum() {
    return context_.GetCommonRecoResults().size();
  }

  std::vector<uint64> GetBrowseSet() const {
    std::vector<uint64> ret(browse_set_.cbegin(), browse_set_.cend());
    return ret;
  }

  uint64 GetUserId() const {
    return context_.GetUserId();
  }

  void SetUserId(uint64 user_id) {
    context_.PurposelyResetUserId(user_id);
  }

  const std::string &GetDeviceId() const {
    return context_.GetDeviceId();
  }

  void SetDeviceId(const std::string &device_id) {
    context_.PurposelyResetDeviceId(device_id);
  }

  int64 GetRequestTime() const {
    return context_.GetRequestTime();
  }

  void SetRequestTime(int64 time_ms) {
    context_.PurposelyResetRequestTime(time_ms);
  }

  int64 GetRequestNum() const {
    return context_.GetRequestNum();
  }

  void SetRequestNum(int64 req_num) {
    context_.PurposelyResetRequestNum(req_num);
  }

  const std::string &GetRequestType() const {
    return context_.GetRequestType();
  }

  void SetRequestType(const std::string &req_type) {
    context_.PurposelyResetRequestType(req_type);
  }

  void Run(const std::string &pipeline_name) {
    if (!pipeline_manager_) {
      if (check_valid_) {
        LOG(FATAL) << "cannot run pipeline executor: null pipeline_manager";
      } else {
        LOG(ERROR) << "CHECK FAIL! cannot run pipeline executor: null pipeline_manager";
        Reset();
      }
      return;
    }

    auto *pipeline = pipeline_manager_->GetPipeline(pipeline_name);
    if (!pipeline) {
      if (check_valid_) {
        LOG(FATAL) << "Pipeline not found: " << pipeline_name;
      } else {
        LOG(ERROR) << "CHECK FAIL! Pipeline not found: " << pipeline_name;
        Reset();
      }
      return;
    }

    pipeline->Execute(&context_, context_.GetRecoResults());
  }

  void Reset() {
    context_.ClearContext();
    // reset request after clear
    context_.SetRequest(&request_);
  }

  void RunAsync(const std::string &pipeline_name) {
    CHECK(!async_future_.valid());

    async_future_ = std::async([=]() { Run(pipeline_name); });
  }

  bool Wait(int timeout_ms = -1) {
    CHECK(async_future_.valid());

    if (timeout_ms >= 0) {
      std::future_status s = async_future_.wait_for(std::chrono::milliseconds(timeout_ms));
      if (s == std::future_status::timeout) {
        return false;
      }
    } else {
      async_future_.wait();
    }

    async_future_.get();
    CHECK(!async_future_.valid());

    return true;
  }

  void OnExit(const std::vector<std::string> &pipelines) {
    std::for_each(pipelines.rbegin(), pipelines.rend(), [&](auto &pipeline_name) {
      CommonRecoPipeline *pipeline = pipeline_manager_->GetPipeline(pipeline_name);
      if (pipeline) {
        pipeline->PipelineExit(&context_);
      }
    });
  }

 private:
  friend Item;

  bool check_valid_ = true;
  std::unique_ptr<CommonRecoPipelineManager> pipeline_manager_;
  CommonRecoContext context_;
  CommonRecoRequest request_;
  std::vector<int64> browse_set_;
  std::future<void> async_future_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoLeafPipelineExecutor);
};
}  // namespace platform
}  // namespace ks
