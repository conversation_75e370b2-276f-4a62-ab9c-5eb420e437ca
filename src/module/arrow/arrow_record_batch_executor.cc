#include "dragon/src/module/arrow/arrow_record_batch_executor.h"

#include <unordered_set>
#include <utility>
#include <vector>

#include "arrow/builder.h"
#include "arrow/record_batch.h"
#include "arrow/table.h"
#include "base/common/logging.h"
#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/processor/ext/arrow/utils/array_util.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/perf_report_util.h"
#include "serving_base/jansson/json.h"

DEFINE_bool(arrow_record_batch_executor_import_validate_full, true, "");
DEFINE_bool(arrow_record_batch_executor_export_check_schema, true, "");
DEFINE_bool(arrow_record_batch_executor_export_check_column_type, false, "");
DEFINE_bool(arrow_record_batch_executor_throw_inconsistent_type, false, "");

namespace ks {
namespace platform {

constexpr char kPipelineManagerConfig[] = "pipeline_manager_config";
constexpr char kJnaConfigKey[] = "jna_config";
constexpr char kJnaConfigInputKey[] = "inputs";
constexpr char kJnaConfigOutputKey[] = "outputs";
constexpr char kJnaConfigPipelineKey[] = "pipeline";
constexpr char kJnaConfigRecordBatchKey[] = "arrow_record_batch_attr";
constexpr char kJnaConfigRecordBatchContextKey[] = "arrow_record_batch_context_key";

class ArrowRecordBatchExecutor::Impl {
#define ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(ARROW_DATA_TYPE_INS, ARROW_BUILDER_TYPE, BUILDER) \
  status = arrow::MakeBuilder(pool_, (ARROW_DATA_TYPE_INS), &tmp);                                       \
  CHECK(status.ok()) << "make builder error: " << status;                                                \
  (BUILDER).reset(dynamic_cast<ARROW_BUILDER_TYPE *>(tmp.release()));                                    \
  CHECK(BUILDER) << "dynamic_cast error: " << #ARROW_BUILDER_TYPE;

 public:
  explicit Impl(const std::string &json_content) {
    base::Json json(base::StringToJson(json_content));
    LOG(INFO) << "json_content: " << json.ToString(2);
    const auto *pipeline_manager_config = json.Get(kPipelineManagerConfig);
    CHECK(pipeline_manager_config) << "please set `" << kPipelineManagerConfig << "` in json";
    executor_ = std::make_unique<CommonRecoLeafPipelineExecutor>(pipeline_manager_config);
    CHECK(executor_) << "nullptr";
    CHECK(ParseRecordBatchAttr(json.Get(kJnaConfigKey), kJnaConfigInputKey, kJnaConfigRecordBatchKey,
                               &record_batch_attr_in_))
        << "please check input attr, json path `" << kJnaConfigKey << "." << kJnaConfigInputKey << "."
        << kJnaConfigRecordBatchKey << "`";
    CHECK(ParseRecordBatchAttr(json.Get(kJnaConfigKey), kJnaConfigOutputKey, kJnaConfigRecordBatchKey,
                               &record_batch_attr_out_))
        << "please check output attr, json path `" << kJnaConfigKey << "." << kJnaConfigOutputKey << "."
        << kJnaConfigRecordBatchKey << "`";
    if (!ParseRecordBatchAttr(json.Get(kJnaConfigKey), kJnaConfigInputKey, kJnaConfigRecordBatchContextKey,
                              &record_batch_context_key_)) {
      CL_LOG_EVERY_N(WARNING, 100) << "json path `" << kJnaConfigKey << "." << kJnaConfigInputKey << "."
                                   << kJnaConfigRecordBatchContextKey
                                   << "` is not set, batch mode will treat input RecordBatch as one context";
      record_batch_context_key_ = "";
    }

    CHECK(RecoUtil::ExtractStringListFromJsonConfig(json.Get(kJnaConfigKey)->Get(kJnaConfigPipelineKey),
                                                    &pipeline_, true, true))
        << "please check json path `" << kJnaConfigKey << "." << kJnaConfigPipelineKey << "`";
    CHECK(!pipeline_.empty()) << "pipeline is empty";
    std::unique_ptr<arrow::ArrayBuilder> tmp;
    arrow::Status status;
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::int64(), arrow::Int64Builder, int64_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::float32(), arrow::FloatBuilder, double_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::binary(), arrow::BinaryBuilder, binary_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::utf8(), arrow::StringBuilder, string_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::list(arrow::int64()), arrow::ListBuilder,
                                                   int64_list_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::list(arrow::float32()), arrow::ListBuilder,
                                                   double_list_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::list(arrow::binary()), arrow::ListBuilder,
                                                   binary_list_builder_);
    ARROW_RECORD_BATCH_EXECUTOR_INIT_ARROW_BUILDER(arrow::list(arrow::utf8()), arrow::ListBuilder,
                                                   string_list_builder_);
  }
  void SetInt(const std::string &name, int64_t val) {
    executor_->SetInt(name, val);
  }
  absl::optional<int64_t> GetInt(const std::string &name) const {
    return executor_->GetInt(name);
  }
  void SetString(const std::string &name, const char *val) {
    executor_->SetString(name, val);
  }
  const char *GetString(const std::string &name) {
    absl::optional<absl::string_view> value = executor_->GetString(name);
    if (!value) return nullptr;
    // NOTE: make sure this string is c string
    return value->data();
  }

  template <typename Ptr>
  void SetPtrCommonAttr(const std::string &key, Ptr &&ptr) {
    executor_->SetPtrCommonAttr(key, std::forward<Ptr>(ptr));
  }
  template <typename T>
  const T *GetPtrCommonAttr(const std::string &name) const {
    return executor_->GetPtrCommonAttr<T>(name);
  }

  // shared ptr version of GetPtrCommonAttr, caller takes shared ownership of the pointer
  template <typename T>
  const std::shared_ptr<const T> *GetSharedPtrCommonAttr(const std::string &name) const {
    return executor_->GetAnyCommonAttr<std::shared_ptr<const T>>(name);
  }
  void Run(const std::string &pipeline_name) {
    executor_->Run(pipeline_name);
  }
  // reset all state
  void Reset() {
    ResetExecutor();
    ResetBatchCache();
  }

  bool RunBatch(const RunBatchRequest *request, RunBatchResponse *response) {
    ResetBatchCache();
    if (!request) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:request_null", 100) << "request is nullptr";
      return false;
    }
    const RunBatchArg *arg_in = request->record_batch;
    if (!CheckArgument(arg_in, record_batch_attr_in_, "request")) {
      return false;
    }
    if (!response) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:response_null", 100) << "response is nullptr";
      return false;
    }
    const RunBatchArg *arg_out = response->record_batch;
    if (!CheckArgument(arg_out, record_batch_attr_out_, "response")) {
      return false;
    }
    std::string msg;
    ArrowUtils::ImportRecordBatchCallbackT import_callback =
        [this](std::shared_ptr<arrow::RecordBatch> record_batch) {
          record_batch_in_ = std::move(record_batch);
          return true;
        };
    if (!ArrowUtils::ImportRecordBatch(arg_in->c_schema, arg_in->c_array,
                                       FLAGS_arrow_record_batch_executor_import_validate_full, &msg,
                                       import_callback)) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:import_error", 100)
          << "request error, message: " << msg << ", c_schema = " << arg_in->c_schema
          << ", c_array = " << arg_in->c_array;
      return false;
    }
    CHECK(record_batch_in_) << "nullptr";
    if (record_batch_in_->num_rows() <= 0) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:record_batch_empty", 100)
          << "record_batch is empty, num_rows = " << record_batch_in_->num_rows();
      return false;
    }

    if (record_batch_context_key_.empty()) {
      // treat as one whole batch
      RunOneContext(0, record_batch_in_->num_rows());
      return BuildResponse(response);
    }
    std::shared_ptr<arrow::Array> context_array =
        record_batch_in_->GetColumnByName(record_batch_context_key_);
    if (!context_array) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:get_column_error", 100)
          << "get column error, record_batch_context_key_ = " << record_batch_context_key_;
      return false;
    }
    if (context_array->null_count() > 0) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_batch:context_key_null_count", 100)
          << "context_key has null values, record_batch_context_key_ = " << record_batch_context_key_
          << ", null_count = " << context_array->null_count();
      return false;
    }
    for (int i = 0, j = 0; i < context_array->length(); i = j) {
      while (j < context_array->length() && context_array->RangeEquals(i, i + 1, j, *context_array)) {
        ++j;
      }
      RunOneContext(i, j);
    }
    return BuildResponse(response);
  }

 private:
  bool ParseRecordBatchAttr(const base::Json *json, const std::string &key, const std::string &second_key,
                            std::string *attr_name) {
    if (!json) return false;
    const auto *sub_json = json->Get(key);
    if (!sub_json) return false;
    return sub_json->GetString(second_key, attr_name);
  }

  bool CheckArgument(const RunBatchArg *arg, const std::string &expected, const std::string &prefix) {
    if (!arg) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "check_argument:" + prefix + "_null", 100)
          << prefix << "->record_batch is nullptr";
      return false;
    }
    // 如果设置了, 则需要和初始化传入的一致; 未设置则忽略
    if (arg->name && arg->name != expected) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "check_argument:" + prefix + "_arg_name_error", 100)
          << prefix + "->record_batch->name error: `" << arg->name << "`, expected: `" << expected << "`";
      return false;
    }
    return true;
  }

  void ResetExecutor() {
    executor_->Reset();
  }
  // reset batch cache
  void ResetBatchCache() {
    record_batch_in_.reset();
    rb_outs_.clear();
    c_schema_out_.reset();
    c_array_out_.reset();
    int64_builder_->Reset();
    double_builder_->Reset();
    binary_builder_->Reset();
    string_builder_->Reset();
    int64_list_builder_->Reset();
    double_list_builder_->Reset();
    binary_list_builder_->Reset();
    string_list_builder_->Reset();
  }

  void RunOneContext(int i, int j) {
    PerfReportUtil::Reset();
    ResetExecutor();
    auto rb_in = record_batch_in_->Slice(i, j - i);
    CHECK(rb_in) << i << " to " << j << "nullptr";
    SetPtrCommonAttr(record_batch_attr_in_, rb_in);
    for (const auto &pipe : pipeline_) {
      Run(pipe);
    }
    executor_->GetAnyCommonAttr<std::shared_ptr<const arrow::RecordBatch>>(record_batch_attr_out_);
    const std::shared_ptr<const arrow::RecordBatch> *rb_out =
        GetSharedPtrCommonAttr<arrow::RecordBatch>(record_batch_attr_out_);
    if (!rb_out || !(*rb_out)) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "run_one_context:record_batch_output_null", 100)
          << "record_batch_output is nullptr, record_batch_attr_out_ = " << record_batch_attr_out_;
      return;
    }
    rb_outs_.push_back(*rb_out);
  }

  bool BuildResponse(RunBatchResponse *response) {
    // NOTE: do not use thread_local, arrow memory might be managed by java, which will trigger oom
    std::vector<std::shared_ptr<arrow::Field>> fields;
    std::unordered_set<std::string> field_names;
    std::vector<std::shared_ptr<arrow::Array>> columns;
    std::vector<std::shared_ptr<arrow::RecordBatch>> rbs;
    if (rb_outs_.empty()) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "build_response:record_batches_empty", 100)
          << "rb_outs_ is empty";
      return false;
    }

    // combine all fields
    bool has_inconsistent_field_type = false;
    for (std::shared_ptr<const arrow::RecordBatch> &rb_out : rb_outs_) {
      CHECK(rb_out) << "nullptr";
      CHECK(rb_out->schema()) << "nullptr";
      for (const auto &field : rb_out->schema()->fields()) {
        if (field_names.find(field->name()) == field_names.end()) {
          field_names.insert(field->name());
          fields.push_back(field);
        }
      }
    }

    auto schema = arrow::schema(fields);
    CHECK(schema) << "nullptr";
    for (std::shared_ptr<const arrow::RecordBatch> &rb_out : rb_outs_) {
      columns.clear();
      for (int i = 0; i < fields.size(); ++i) {
        const auto &field = fields[i];
        const auto &col = rb_out->GetColumnByName(field->name());
        if ((col && !FLAGS_arrow_record_batch_executor_export_check_column_type) ||
            (col && field->type() && col->type() && field->type()->id() == col->type()->id())) {
          columns.push_back(col);
        } else {
          auto null_col = CreateNullArray(field->type().get(), rb_out->num_rows());
          CHECK(null_col) << "nullptr";
          columns.push_back(std::move(null_col));

          if (col && FLAGS_arrow_record_batch_executor_export_check_column_type) {
            CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor",
                               "build_response:column_type_inconsistent," + field->name(), 100)
                  << "field has different type, " << field->name() << ", "
                  << field->type()->name() << " vs " << col->type()->name()
                  << ", creat null with type " << field->type()->name();
            has_inconsistent_field_type = true;
          }
        }
      }
      CHECK_EQ(fields.size(), columns.size());
      rbs.push_back(arrow::RecordBatch::Make(schema, rb_out->num_rows(), columns));
    }
    if (has_inconsistent_field_type &&
        FLAGS_arrow_record_batch_executor_throw_inconsistent_type) {
      LOG(FATAL) << "arrow record_batches have different types";
    }
    auto table = arrow::Table::FromRecordBatches(schema, rbs);
    if (!table.ok()) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "build_response:create_table_error", 100)
          << "create_table error, status = " << table.status();
      return false;
    }
    auto record_batch_out = (*table)->CombineChunksToBatch(ArrowUtils::GetPool());
    if (!record_batch_out.ok()) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "build_response:to_batch_error", 100)
          << "table_combine_chunks_to_batch error, status = " << record_batch_out.status();
      return false;
    }
    CHECK(*record_batch_out) << "nullptr";
    std::string message;
    ArrowUtils::ExportRecordBatchCallbackT export_callback = [this](std::shared_ptr<char> c_schema,
                                                                    std::shared_ptr<char> c_array) {
      c_schema_out_ = std::move(c_schema);
      c_array_out_ = std::move(c_array);
      return true;
    };
    if (!ArrowUtils::ExportRecordBatch(record_batch_attr_out_, (*record_batch_out).get(),
                                       FLAGS_arrow_record_batch_executor_export_check_schema, &message,
                                       export_callback)) {
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "build_response:export_error", 100)
          << "export_error, message:" << message;
      return false;
    }
    CHECK(response && response->record_batch) << "nullptr";
    response->record_batch->c_schema =
        c_schema_out_ ? reinterpret_cast<ArrowSchema *>(c_schema_out_.get()) : nullptr;
    response->record_batch->c_array =
        c_array_out_ ? reinterpret_cast<ArrowArray *>(c_array_out_.get()) : nullptr;
    CHECK(response->record_batch->c_schema) << "nullptr";
    CHECK(response->record_batch->c_array) << "nullptr";
    CHECK(!ArrowSchemaIsReleased(response->record_batch->c_schema));
    CHECK(!ArrowArrayIsReleased(response->record_batch->c_array));
    return true;
  }

#define ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(LENGTH, BUILDER)                       \
  do {                                                                                      \
    BUILDER->Reset();                                                                       \
    CHECK(BUILDER->AppendNulls(LENGTH).ok());                                               \
    auto status = BUILDER->Finish(&array);                                                  \
    if (!status.ok()) {                                                                     \
      CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "create_null_array:finish_error", 100) \
          << #BUILDER << " finish error: " << status;                                       \
      BUILDER->Reset();                                                                     \
      break;                                                                                \
    }                                                                                       \
  } while (false)

  std::shared_ptr<arrow::Array> CreateNullArray(arrow::DataType *data_type, int num_rows) {
    CHECK(data_type) << "nullptr";
    std::shared_ptr<arrow::Array> array;
    switch (data_type->id()) {
      case arrow::Int64Type::type_id: {
        ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, int64_builder_);
      } break;
      case arrow::FloatType::type_id:
      case arrow::DoubleType::type_id: {
        ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, double_builder_);
      } break;
      case arrow::BinaryType::type_id: {
        ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, binary_builder_);
      } break;
      case arrow::StringType::type_id: {
        ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, string_builder_);
      } break;
      case arrow::ListType::type_id: {
        arrow::ListType *list_type = dynamic_cast<arrow::ListType *>(data_type);
        CHECK(list_type) << "nullptr: downcast DataType -> ListType";
        auto value_type = list_type->value_type();
        switch (value_type->id()) {
          case arrow::Int64Type::type_id: {
            ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, int64_list_builder_);
          } break;
          case arrow::FloatType::type_id:
          case arrow::DoubleType::type_id: {
            ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, double_list_builder_);
          } break;
          case arrow::BinaryType::type_id: {
            ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, binary_list_builder_);
          } break;
          case arrow::StringType::type_id: {
            ARROW_RECORD_BATCH_EXECUTOR_BUILD_NULL_ARRAY(num_rows, string_list_builder_);
          } break;
          default:
            CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "create_null_array:type_error", 100)
                << "type_error, type_name:list[" << value_type->name() << "]";
        }
      } break;
      default:
        CL_LOG_ERROR_EVERY("ArrowRecordBatchExecutor", "create_null_array:type_error", 100)
            << "type_error, type_name:" << data_type->name();
    }
    return array;
  }

 private:
  std::unique_ptr<CommonRecoLeafPipelineExecutor> executor_;
  std::string record_batch_attr_in_;
  std::string record_batch_attr_out_;
  std::string record_batch_context_key_;
  std::vector<std::string> pipeline_;
  // members used for RunBatch
  std::shared_ptr<arrow::RecordBatch> record_batch_in_;
  // share ownership from dragon context
  std::vector<std::shared_ptr<const arrow::RecordBatch>> rb_outs_;
  std::shared_ptr<char> c_schema_out_;
  std::shared_ptr<char> c_array_out_;
  arrow::MemoryPool *pool_ = arrow::default_memory_pool();
  std::shared_ptr<arrow::Int64Builder> int64_builder_;
  std::shared_ptr<arrow::FloatBuilder> double_builder_;
  std::shared_ptr<arrow::BinaryBuilder> binary_builder_;
  std::shared_ptr<arrow::StringBuilder> string_builder_;
  std::shared_ptr<arrow::ListBuilder> int64_list_builder_;
  std::shared_ptr<arrow::ListBuilder> double_list_builder_;
  std::shared_ptr<arrow::ListBuilder> binary_list_builder_;
  std::shared_ptr<arrow::ListBuilder> string_list_builder_;
};

ArrowRecordBatchExecutor::ArrowRecordBatchExecutor(const std::string &json_content)
    : impl_(new Impl(json_content)) {}

ArrowRecordBatchExecutor::~ArrowRecordBatchExecutor() = default;

ArrowRecordBatchExecutor *ArrowRecordBatchExecutor::SetInt(const std::string &name, int64_t val) {
  impl_->SetInt(name, val);
  return this;
}

absl::optional<int64_t> ArrowRecordBatchExecutor::GetInt(const std::string &name) const {
  return impl_->GetInt(name);
}

ArrowRecordBatchExecutor *ArrowRecordBatchExecutor::SetString(const std::string &name, const char *val) {
  impl_->SetString(name, val);
  return this;
}
const char *ArrowRecordBatchExecutor::GetString(const std::string &name) const {
  return impl_->GetString(name);
}

template <typename Ptr>
void ArrowRecordBatchExecutor::SetPtrCommonAttr(const std::string &key, Ptr &&ptr) {
  impl_->SetPtrCommonAttr(key, std::forward<Ptr>(ptr));
}

// explict instantiate for jna
template void ArrowRecordBatchExecutor::SetPtrCommonAttr<char *>(const std::string &key, char *&&ptr);

template <typename T>
const T *ArrowRecordBatchExecutor::GetPtrCommonAttr(const std::string &name) const {
  return impl_->GetPtrCommonAttr<T>(name);
}

// explict instantiate for jna
template const char *ArrowRecordBatchExecutor::GetPtrCommonAttr<char>(const std::string &name) const;

void ArrowRecordBatchExecutor::Run(const std::string &pipeline_name) {
  impl_->Run(pipeline_name);
}
bool ArrowRecordBatchExecutor::RunBatch(const RunBatchRequest *request, RunBatchResponse *response) {
  return impl_->RunBatch(request, response);
}
void ArrowRecordBatchExecutor::Reset() {
  impl_->Reset();
}
}  // namespace platform
}  // namespace ks
