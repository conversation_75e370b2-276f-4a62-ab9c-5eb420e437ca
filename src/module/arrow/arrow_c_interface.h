#pragma once
#include <cstdint>
#include <vector>

#include "arrow/c/bridge.h"

#define DRAGON_ARROW_EXPORT __attribute__((visibility("default")))

extern "C" {

struct DRAGON_ARROW_EXPORT OptionalInt64 {
  bool has_value;
  int64_t value;
};

struct DRAGON_ARROW_EXPORT RunBatchArg {
  const char *name = nullptr;
  ArrowSchema *c_schema = nullptr;
  ArrowArray *c_array = nullptr;
};

struct DRAGON_ARROW_EXPORT RunBatchRequest {
  // record_batch to drive batch run, memory managed by user
  RunBatchArg *record_batch = nullptr;
};

struct DRAGON_ARROW_EXPORT RunBatchResponse {
  // record_batch returned, memory managed by executor
  RunBatchArg *record_batch = nullptr;
};
}
