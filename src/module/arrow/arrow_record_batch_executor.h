#pragma once

#include <memory>
#include <string>

#include "absl/types/optional.h"
#include "base/common/gflags.h"
#include "dragon/src/module/arrow/arrow_c_interface.h"

DECLARE_bool(arrow_record_batch_executor_import_validate_full);
DECLARE_bool(arrow_record_batch_executor_export_check_schema);
DECLARE_bool(arrow_record_batch_executor_export_check_column_type);
DECLARE_bool(arrow_record_batch_executor_throw_inconsistent_type);

namespace ks {
namespace platform {

class ArrowRecordBatchExecutor {
 public:
  class Impl;

 public:
  explicit ArrowRecordBatchExecutor(const std::string &json_content);
  ~ArrowRecordBatchExecutor();
  ArrowRecordBatchExecutor *SetInt(const std::string &name, int64_t val);
  absl::optional<int64_t> GetInt(const std::string &name) const;
  ArrowRecordBatchExecutor *SetString(const std::string &name, const char *val);
  const char *GetString(const std::string &name) const;
  template <typename Ptr>
  void SetPtrCommonAttr(const std::string &key, Ptr &&ptr);
  template <typename T>
  const T *GetPtrCommonAttr(const std::string &name) const;
  void Run(const std::string &pipeline_name);
  bool RunBatch(const RunBatchRequest *request, RunBatchResponse *response);
  // reset executor state
  void Reset();

 private:
  std::unique_ptr<Impl> impl_;
};
}  // namespace platform
}  // namespace ks
