#pragma once

#include <functional>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_context.h"
#include "third_party/abseil/absl/container/flat_hash_map.h"

#define REGISTER_LIGHT_FUNCTION(FUNC)                   \
  do {                                                  \
    function_map_.emplace(#FUNC, FUNC);                 \
  } while (0)

#define GET_COMMON_ATTR(FUNC)                                                                         \
  do {                                                                                                \
    auto iter = import_common_attr_accessor_map_.find(attr_name);                                     \
    return iter != import_common_attr_accessor_map_.end() ? iter->second->FUNC() : absl::nullopt;     \
  } while (0)

#define GET_ITEM_ATTR(FUNC)                                                 \
  do {                                                                      \
    auto iter = import_item_attr_accessor_map_.find(attr_name);             \
    if (iter != import_item_attr_accessor_map_.end()) {                     \
      return [accessor = iter->second](const CommonRecoResult &result) {    \
        return result.FUNC(accessor);                                       \
      };                                                                    \
    } else {                                                                \
      return [](const CommonRecoResult &) {                                 \
        return absl::nullopt;                                               \
      };                                                                    \
    }                                                                       \
  } while (0)

#define SET_COMMON_ATTR(FUNC, VALUE)                                                                  \
  do {                                                                                                \
    auto iter = export_common_attr_accessor_map_.find(attr_name);                                     \
    return iter != export_common_attr_accessor_map_.end() ? iter->second->FUNC(VALUE) : false;        \
  } while (0)

#define SET_ITEM_ATTR(FUNC, TYPE, VALUE)                                                  \
  do {                                                                                    \
    auto iter = export_item_attr_accessor_map_.find(attr_name);                           \
    if (iter != export_item_attr_accessor_map_.end()) {                                   \
      return [accessor = iter->second](const CommonRecoResult &result, TYPE value) {      \
        result.FUNC(accessor, VALUE);                                                     \
      };                                                                                  \
    } else {                                                                              \
      return [](const CommonRecoResult &, TYPE) {                                         \
      };                                                                                  \
    }                                                                                     \
  } while (0)

namespace ks {
namespace platform {

class CommonRecoLightFunctionContext {
 public:
  CommonRecoLightFunctionContext() = default;

  void SetImportCommonAttrAccessorMap(
      const absl::flat_hash_map<std::string, const CommonAttr *> &import_common_attr_accessor_map) {
    import_common_attr_accessor_map_ = import_common_attr_accessor_map;
  }

  void SetExportCommonAttrAccessorMap(
      const absl::flat_hash_map<std::string, CommonAttr *> &export_common_attr_accessor_map) {
    export_common_attr_accessor_map_ = export_common_attr_accessor_map;
  }

  void SetImportItemAttrAccessorMap(
      const absl::flat_hash_map<std::string, const ItemAttr *> &import_item_attr_accessor_map) {
    import_item_attr_accessor_map_ = import_item_attr_accessor_map;
  }

  void SetExportItemAttrAccessorMap(
      const absl::flat_hash_map<std::string, ItemAttr *> &export_item_attr_accessor_map) {
    export_item_attr_accessor_map_ = export_item_attr_accessor_map;
  }

  template <typename T>
  const T *GetPtrCommonAttr(absl::string_view attr_name) const {
    auto iter = import_common_attr_accessor_map_.find(attr_name);
    return iter != import_common_attr_accessor_map_.end() ? iter->second->GetPtrValue<T>() : nullptr;
  }

  absl::optional<int64> GetIntCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetIntValue);
  }

  absl::optional<absl::Span<const int64>> GetIntListCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetIntListValue);
  }

  absl::optional<double> GetDoubleCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetDoubleValue);
  }

  absl::optional<absl::Span<const double>> GetDoubleListCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetDoubleListValue);
  }

  absl::optional<absl::string_view> GetStringCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetStringValue);
  }

  absl::optional<std::vector<absl::string_view>> GetStringListCommonAttr(absl::string_view attr_name) const {
    GET_COMMON_ATTR(GetStringListValue);
  }

  std::function<absl::optional<int64>(const CommonRecoResult &)> GetIntItemAttr(
      absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetIntAttr);
  }

  std::function<absl::optional<absl::Span<const int64>>(const CommonRecoResult &)> GetIntListItemAttr(
      absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetIntListAttr);
  }

  std::function<absl::optional<double>(const CommonRecoResult &)> GetDoubleItemAttr(
      absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetDoubleAttr);
  }

  std::function<absl::optional<absl::Span<const double>>(const CommonRecoResult &)> GetDoubleListItemAttr(
      absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetDoubleListAttr);
  }

  std::function<absl::optional<absl::string_view>(const CommonRecoResult &)> GetStringItemAttr(
      absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetStringAttr);
  }

  std::function<absl::optional<std::vector<absl::string_view>>(const CommonRecoResult &)>
      GetStringListItemAttr(absl::string_view attr_name) const {
    GET_ITEM_ATTR(GetStringListAttr);
  }

  bool SetIntCommonAttr(absl::string_view attr_name, const int64 value) const {
    SET_COMMON_ATTR(SetIntValue, value);
  }

  bool SetIntListCommonAttr(absl::string_view attr_name, std::vector<int64> &&value) const {
    SET_COMMON_ATTR(SetIntListValue, std::move(value));
  }

  bool SetDoubleCommonAttr(absl::string_view attr_name, const double value) const {
    SET_COMMON_ATTR(SetDoubleValue, value);
  }

  bool SetDoubleListCommonAttr(absl::string_view attr_name, std::vector<double> &&value) const {
    SET_COMMON_ATTR(SetDoubleListValue, std::move(value));
  }

  bool SetStringCommonAttr(absl::string_view attr_name, const std::string &value) const {
    SET_COMMON_ATTR(SetStringValue, value);
  }

  bool SetStringListCommonAttr(absl::string_view attr_name, std::vector<std::string> &&value) const {
    SET_COMMON_ATTR(SetStringListValue, std::move(value));
  }

  template <typename Ptr>
  void SetPtrCommonAttr(absl::string_view attr_name, Ptr &&ptr) const {
    auto iter = export_common_attr_accessor_map_.find(attr_name);
    if (iter != export_common_attr_accessor_map_.end()) {
      iter->second->SetPtrValue<Ptr>(std::forward<Ptr>(ptr));
    }
  }

  bool AppendIntListCommonAttr(absl::string_view attr_name, int64 value) const {
    SET_COMMON_ATTR(AppendIntListValue, value);
  }

  std::function<void(const CommonRecoResult &, const int64)>
      SetIntItemAttr(absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetIntAttr, const int64, value);
  }

  std::function<void(const CommonRecoResult &, std::vector<int64> &)>
      SetIntListItemAttr(absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetIntListAttr, std::vector<int64> &, std::move(value));
  }

  std::function<void(const CommonRecoResult &, const double)> SetDoubleItemAttr(
      absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetDoubleAttr, const double, value);
  }

  std::function<void(const CommonRecoResult &, std::vector<double> &)>
      SetDoubleListItemAttr(absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetDoubleListAttr, std::vector<double> &, std::move(value));
  }

  std::function<void(const CommonRecoResult &, const std::string &)> SetStringItemAttr(
      absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetStringAttr, const std::string &, value);
  }

  std::function<void(const CommonRecoResult &, std::vector<std::string> &)>
      SetStringListItemAttr(absl::string_view attr_name) const {
    SET_ITEM_ATTR(SetStringListAttr, std::vector<std::string> &, std::move(value));
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *>
  GetProtoMessagePtrCommonAttr(absl::string_view attr_name) const {
    auto *ptr = GetPtrCommonAttr<google::protobuf::Message>(attr_name);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrCommonAttr<T>(attr_name);
    }
  }

  template <typename T>
  const std::function<const T *(const CommonRecoResult &)> GetPtrItemAttr(absl::string_view attr_name) const {
    auto iter = import_item_attr_accessor_map_.find(attr_name);
    if (iter != import_item_attr_accessor_map_.end()) {
      return [accessor = iter->second](const CommonRecoResult &result) {
        return result.GetPtrAttr<T>(accessor);
      };
    } else {
      return [](const CommonRecoResult &) { return nullptr; };
    }
  }

  template <typename T>
  std::function<std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *>(
      const CommonRecoResult &)>
  GetProtoMessagePtrItemAttr(absl::string_view attr_name) const {
    auto iter = import_item_attr_accessor_map_.find(attr_name);
    if (iter != import_item_attr_accessor_map_.end()) {
      return [accessor = iter->second](const CommonRecoResult &result) {
        auto ptr = result.GetPtrAttr<google::protobuf::Message>(accessor);
        if (ptr) {
          return google::protobuf::down_cast<const T *>(ptr);
        } else {
          return result.GetPtrAttr<T>(accessor);
        }
      };
    } else {
      return [](const CommonRecoResult &) { return nullptr; };
    }
  }

 private:
  absl::flat_hash_map<std::string, const CommonAttr *> import_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, CommonAttr *> export_common_attr_accessor_map_;
  absl::flat_hash_map<std::string, const ItemAttr *> import_item_attr_accessor_map_;
  absl::flat_hash_map<std::string, ItemAttr *> export_item_attr_accessor_map_;
};


class CommonRecoBaseLightFunctionSet {
 public:
  typedef std::function<
      bool(const CommonRecoLightFunctionContext &, RecoResultConstIter, RecoResultConstIter)>
      CommonRecoLightFunction;

  CommonRecoBaseLightFunctionSet() = default;
  virtual ~CommonRecoBaseLightFunctionSet() = default;

  bool Run(
      absl::string_view function_name,
      const CommonRecoLightFunctionContext &context,
      RecoResultConstIter begin,
      RecoResultConstIter end) {
    auto iter = function_map_.find(function_name);
    if (iter != function_map_.end()) {
      return (iter->second)(context, begin, end);
    }

    LOG_EVERY_N(WARNING, 100) << "light function not registered: " << function_name;
    return false;
  }

 protected:
  absl::flat_hash_map<std::string, CommonRecoLightFunction> function_map_;

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
