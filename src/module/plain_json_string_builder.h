#pragma once

#include <string>
#include <type_traits>
#include <vector>

#include "third_party/abseil/absl/strings/string_view.h"
#include "third_party/abseil/absl/types/span.h"

namespace ks {
namespace platform {

/**
 * 一个轻量高效的单层扁平结构 json string 构建工具
 */
class PlainJsonStringBuilder {
 public:
  const std::string &json_string() {
    if (!done_) {
      if (payload_.empty()) {
        payload_.push_back('{');
      }
      payload_.push_back('}');
      done_ = true;
    }
    return payload_;
  }

  size_t size() const {
    return payload_.size();
  }

  void clear() {
    payload_.clear();
    done_ = false;
  }

  bool shrink_to(size_t s) {
    if (s >= payload_.size()) return false;
    payload_.resize(s);
    done_ = false;
    return true;
  }

  void append(const std::string &str) {
    if (str.empty()) return;
    payload_.append(str);
    done_ = false;
  }

  void push_back(char c) {
    payload_.push_back(c);
    done_ = false;
  }

  template <typename T, typename std::enable_if<std::is_arithmetic<T>::value, bool>::type = true>
  void AddField(const std::string &name, T value) {
    AppendFieldName(name);
    payload_.append(std::to_string(value));
  }

  void AddField(const std::string &name, absl::string_view value) {
    AppendFieldName(name);
    AppendEscapedString(value);
  }

  template <typename T, typename std::enable_if<std::is_arithmetic<T>::value, bool>::type = true>
  void AddField(const std::string &name, absl::Span<const T> value_list) {
    AppendFieldName(name);
    payload_.push_back('[');
    bool first = true;
    for (const auto value : value_list) {
      if (!first) {
        payload_.push_back(',');
      }
      payload_.append(std::to_string(value));
      first = false;
    }
    payload_.push_back(']');
  }

  void AddField(const std::string &name, const std::vector<absl::string_view> &value_list) {
    AppendFieldName(name);
    payload_.push_back('[');
    bool first = true;
    for (auto value : value_list) {
      if (!first) {
        payload_.push_back(',');
      }
      AppendEscapedString(value);
      first = false;
    }
    payload_.push_back(']');
  }

  void AddNullField(const std::string &name) {
    AppendFieldName(name);
    payload_.append("null");
  }

  void AppendFieldName(const std::string &name) {
    if (done_) {
      // pop the last character '}'
      payload_.pop_back();
      done_ = false;
    }
    if (payload_.empty()) {
      payload_.push_back('{');
    } else if (payload_.back() != '{') {
      payload_.push_back(',');
    }
    AppendEscapedString(name);
    payload_.push_back(':');
  }

  void AppendEscapedString(absl::string_view str) {
    if (done_) return;
    payload_.push_back('"');
    for (const char c : str) {
      switch (c) {
        case '"':
        case '\\':
          payload_.push_back('\\');
          payload_.push_back(c);
          break;
        // backspace (0x08)
        case '\b':
          payload_.push_back('\\');
          payload_.push_back('b');
          break;
        // formfeed (0x0c)
        case '\f':
          payload_.push_back('\\');
          payload_.push_back('f');
          break;
        // newline (0x0a)
        case '\n':
          payload_.push_back('\\');
          payload_.push_back('n');
          break;
        // carriage return (0x0d)
        case '\r':
          payload_.push_back('\\');
          payload_.push_back('r');
          break;
        // horizontal tab (0x09)
        case '\t':
          payload_.push_back('\\');
          payload_.push_back('t');
          break;
        default:
          if (c >= 0x00 && c <= 0x1f) {
            // show character c as \uxxxx
            payload_.push_back('\\');
            char buffer[8];
            int n = snprintf(buffer, sizeof(buffer), "u%04x", static_cast<int>(c));
            for (int i = 0; i < n; ++i) {
              payload_.push_back(buffer[i]);
            }
          } else {
            // all other characters are added as-is
            payload_.push_back(c);
          }
          break;
      }
    }
    payload_.push_back('"');
  }

 private:
  std::string payload_;
  bool done_ = false;
};

}  // namespace platform
}  // namespace ks
