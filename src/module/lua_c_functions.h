#pragma once

#include <boost/random.hpp>
#include <lua-5.4.4/src/lua.hpp>
#include <string>
#include <vector>

#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "dragon/src/util/random.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/util/location.h"
#include "location/geohash.h"
#include "third_party/xxhash/xxhash.h"

#define REGISTER_LUA_C_FUNCTION(FUNC_NAME) \
  {                                        \
    lua_pushstring(L, #FUNC_NAME);         \
    lua_pushcfunction(L, FUNC_NAME);       \
    lua_settable(L, -3);                   \
  }

namespace ks {
namespace platform {
namespace lua {

extern "C" {
static int beta_dist(lua_State *L) {
  double alpha = luaL_checknumber(L, 1);
  double beta = luaL_checknumber(L, 2);
  if (alpha <= 0 || beta <= 0) {
    return luaL_error(L, "alpha and beta of math.beta_dist() must be greater than 0");
  }
  boost::random::beta_distribution<> distribution(alpha, beta);
  thread_local boost::random::mt19937 rng(base::GetTimestamp());
  double result = distribution(rng);
  lua_pushnumber(L, result);
  return 1;
}

static int GetTimestamp(lua_State *L) {
  lua_pushinteger(L, base::GetTimestamp());
  return 1;
}

static int GetId(lua_State *L) {
  int64 item_key = luaL_checkinteger(L, 1);
  lua_pushinteger(L, Util::GetId(item_key));
  return 1;
}

static int GetType(lua_State *L) {
  int64 item_key = luaL_checkinteger(L, 1);
  lua_pushinteger(L, Util::GetType(item_key));
  return 1;
}

static int GenKeysign(lua_State *L) {
  int type = luaL_checkinteger(L, 1);
  uint64 id = luaL_checkinteger(L, 2);
  lua_pushinteger(L, Util::GenKeysign(type, id));
  return 1;
}

static int CityHash64(lua_State *L) {
  size_t len = 0;
  const char *data = luaL_checklstring(L, 1, &len);
  lua_pushinteger(L, base::CityHash64(data, len));
  return 1;
}

static int Random(lua_State *L) {
  lua_pushnumber(L, ks::platform::uniform_real<float>());
  return 1;
}

static int GetDistance(lua_State *L) {
  double lat_a = luaL_checknumber(L, 1);
  double lon_a = luaL_checknumber(L, 2);
  double lat_b = luaL_checknumber(L, 3);
  double lon_b = luaL_checknumber(L, 4);
  lua_pushnumber(L, ks::location_util::GetDistance(lat_a, lon_a, lat_b, lon_b));
  return 1;
}

static int Sleep(lua_State *L) {
  double ms = luaL_checkinteger(L, 1);
  base::SleepForMilliseconds(ms);
  return 0;
}

static int TimeStringToTimestamp(lua_State *L) {
  const char *data = luaL_checkstring(L, 1);
  base::Time t;
  if (base::Time::FromStringInMilliseconds(data, &t)) {
    // in format of "2011-01-02 15:38:59.300"
    base::TimeDelta d = t - base::Time::UnixEpoch();
    lua_pushinteger(L, d.InMilliseconds());
    return 1;
  }
  if (base::Time::FromStringInSeconds(data, &t)) {
    // in format of "2011-01-02 15:38:59"
    base::TimeDelta d = t - base::Time::UnixEpoch();
    lua_pushinteger(L, d.InMilliseconds());
    return 1;
  }
  return luaL_error(L, "failed to parse time string: %s", data);
}

static int discretize(lua_State *L) {
  double data = luaL_checknumber(L, 1);
  luaL_checktype(L, 2, LUA_TTABLE);
  int len = lua_rawlen(L, 2);
  std::vector<double> boundaries;
  boundaries.reserve(len);
  for (int i = 1; i <= len; ++i) {
    lua_rawgeti(L, 2, i);
    double d = luaL_checknumber(L, -1);
    lua_pop(L, 1);
    if (!boundaries.empty() && d <= boundaries.back()) {
      return luaL_error(
          L, "argument boundaries for util.discretize() must be in ascending order: [... %f, %f ...]",
          boundaries.back(), d);
    }
    boundaries.push_back(d);
  }

  auto bound = std::upper_bound(boundaries.begin(), boundaries.end(), data);
  int n = bound - boundaries.begin();
  lua_pushinteger(L, n);
  return 1;
}

enum XXHashMode { kXXHash32 = 0, kXXHash64 };

static int XXHashImpl(lua_State *L, XXHashMode mode) {
  size_t len = 0;
  const char *data = nullptr;
  uint64 uint64_data = 0;
  int type = lua_type(L, 1);
  switch (type) {
    case LUA_TSTRING:
      data = lua_tolstring(L, 1, &len);
      break;
    case LUA_TNUMBER:
      uint64_data = lua_tointegerx(L, 1, nullptr);
      data = reinterpret_cast<const char *>(&uint64_data);
      len = sizeof(uint64);
      break;
    default:
      return luaL_error(L, "first argument is not string or int");
  }
  int64 seed = (lua_gettop(L) >= 2) ? luaL_checkinteger(L, 2) : 0;
  if (mode == XXHashMode::kXXHash32) {
    lua_pushinteger(L, ks::XXH32(data, len, seed));
  } else {
    lua_pushinteger(L, ks::XXH64(data, len, seed));
  }
  return 1;
}

static int XXHash32(lua_State *L) {
  return XXHashImpl(L, XXHashMode::kXXHash32);
}

static int XXHash64(lua_State *L) {
  return XXHashImpl(L, XXHashMode::kXXHash64);
}

static int GeoHashEncode(lua_State *L) {
  static const int kGeoHashEncodeDefaultLength = 8;
  double latitude = luaL_checknumber(L, 1);
  double longitude = luaL_checknumber(L, 2);
  int64 length = (lua_gettop(L) >= 3) ? luaL_checkinteger(L, 3) : kGeoHashEncodeDefaultLength;
  std::string result = GeoHash::Encode(longitude, latitude, length);
  lua_pushlstring(L, result.data(), result.size());
  return 1;
}

static int GeoHashDecode(lua_State *L) {
  size_t len = 0;
  const char *data = luaL_checklstring(L, 1, &len);
  std::string hash_code(data, len);
  auto result = GeoHash::Decode(hash_code);
  double longitude = result.first;
  double latitude = result.second;
  lua_pushnumber(L, latitude);
  lua_pushnumber(L, longitude);
  return 2;
}
}

static void RegisterCFunctions(lua_State *L) {
  // math
  lua_getglobal(L, "math");
  // math.beta_dist()
  REGISTER_LUA_C_FUNCTION(beta_dist);
  // end of math
  lua_pop(L, 1);

  // util
  lua_newtable(L);
  // util.GetTimestamp()
  REGISTER_LUA_C_FUNCTION(GetTimestamp);
  // util.GetId()
  REGISTER_LUA_C_FUNCTION(GetId);
  // util.GetType()
  REGISTER_LUA_C_FUNCTION(GetType);
  // util.GenKeysign()
  REGISTER_LUA_C_FUNCTION(GenKeysign);
  // util.CityHash64()
  REGISTER_LUA_C_FUNCTION(CityHash64);
  // util.Random()
  REGISTER_LUA_C_FUNCTION(Random);
  // util.GetDistance()
  REGISTER_LUA_C_FUNCTION(GetDistance);
  // util.Sleep()
  REGISTER_LUA_C_FUNCTION(Sleep);
  // util.TimeStringToTimestamp()
  REGISTER_LUA_C_FUNCTION(TimeStringToTimestamp);
  // util.discretize()
  REGISTER_LUA_C_FUNCTION(discretize);
  // util.XXHash32()
  REGISTER_LUA_C_FUNCTION(XXHash32);
  // util.XXHash64()
  REGISTER_LUA_C_FUNCTION(XXHash64);
  // util.GeoHashEncode()
  REGISTER_LUA_C_FUNCTION(GeoHashEncode);
  // util.GeoHashDecode()
  REGISTER_LUA_C_FUNCTION(GeoHashDecode);
  // end of util
  lua_setglobal(L, "util");
}

}  // namespace lua
}  // namespace platform
}  // namespace ks
