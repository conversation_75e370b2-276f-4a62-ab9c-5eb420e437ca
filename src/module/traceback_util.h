#pragma once

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include "base/strings/string_split.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/util/logging_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "third_party/abseil/absl/strings/string_view.h"
#include "third_party/abseil/absl/types/optional.h"
#include "third_party/abseil/absl/types/span.h"
#include "third_party/zstd/lib/zstd.h"

DECLARE_int64(kafka_message_size_limit);

/**
 * 转为 PackedItemAttrValue 类型，做 TracebackLog 的 工具类
 */
namespace ks {
namespace platform {
namespace traceback_util {

/**
 * zstd 压缩算法，来源 facebook，代码基于 "serving_base/bt_queue/compress.pb.h" 二次开发
 */

// 参数:
// param1: input 表示需要压缩的数据
// param2: length 表示需要压缩的数据长度
// param3: ouput 表示压缩后的结果
// param4: flag 表示压缩等级 [-5 ~ 22] 从 -5 到 22 压缩后的消息变小，压缩耗时变大
// 返回值: 压缩后的 size， 返回 0 表示失败
static size_t zstd_util_compress(const char *input, size_t length, std::string *output, int flag) {
  int compress_length = ZSTD_compressBound(length);
  output->resize(compress_length);
  char *out = const_cast<char *>(output->data());
  size_t dst_size = ZSTD_compress(out, compress_length, input, length, flag);
  if (ZSTD_isError(dst_size) || dst_size > compress_length) return 0;
  output->resize(dst_size);
  return dst_size;
}

// 参数:
// param1: compressed 表示压缩的数据
// param2: length 表示压缩的数据长度
// param3: decompressed 表示解压后的结果
// 返回值: true 表示成功，false 表示失败
static bool zstd_util_decompress(const char *compressed, int length, std::string *decompressed) {
  int decompress_length = ZSTD_getDecompressedSize(compressed, length);
  decompressed->resize(decompress_length);
  char *out = const_cast<char *>(decompressed->data());
  size_t dst_size = ZSTD_decompress(out, decompress_length, compressed, length);
  if (ZSTD_isError(dst_size) || dst_size > decompress_length) return false;
  decompressed->resize(dst_size);
  return true;
}

static void BuildIntPackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                        absl::optional<int64> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_INT64);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    int64 value = *val;
    payload->append(reinterpret_cast<const char *>(&value), sizeof(int64));
    attr_value->add_value_length(1);
  } else {
    attr_value->add_value_length(-1);
  }
}

static void BuildDoublePackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                           absl::optional<double> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_FLOAT64);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    double value = *val;
    payload->append(reinterpret_cast<const char *>(&value), sizeof(double));
    attr_value->add_value_length(1);
  } else {
    attr_value->add_value_length(-1);
  }
}

static void BuildStringPackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                           absl::optional<absl::string_view> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_STRING);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    payload->append(val->data(), val->size());
    attr_value->add_value_length(val->size());
  } else {
    attr_value->add_value_length(-1);
  }
}

static void BuildIntListPackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                            absl::optional<absl::Span<const int64>> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_INT64_LIST);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    payload->append(reinterpret_cast<const char *>(val->data()), sizeof(int64) * val->size());
    attr_value->add_value_length(val->size());
  } else {
    attr_value->add_value_length(-1);
  }
}

static void BuildDoubleListPackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                               absl::optional<absl::Span<const double>> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_FLOAT64_LIST);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    payload->append(reinterpret_cast<const char *>(val->data()), sizeof(double) * val->size());
    attr_value->add_value_length(val->size());
  } else {
    attr_value->add_value_length(-1);
  }
}

static void BuildStringListPackedTracebackData(ks::platform::PackedItemAttrValue *attr_value,
                                               absl::optional<std::vector<absl::string_view>> val) {
  attr_value->set_value_type(PackedItemAttrValue_ValueType_STRING_LIST);
  attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_NO_COMPRESS);
  if (val) {
    std::string *payload = attr_value->mutable_value();
    attr_value->add_value_length(val->size());
    for (auto str : *val) {
      payload->append(str.data(), str.size());
      attr_value->add_value_length(str.size());
    }
  } else {
    attr_value->add_value_length(-1);
  }
}

static bool CompressPackedTracebackData(
    ks::platform::PackedItemAttrValue *attr_value,
    PackedItemAttrValue::CompressMode compress_mode = PackedItemAttrValue_CompressMode_NO_COMPRESS) {
  if (compress_mode == PackedItemAttrValue_CompressMode_NO_COMPRESS) {
    return true;
  }
  if (compress_mode == PackedItemAttrValue_CompressMode_ZSTD) {
    std::string compress_val;
    std::string *payload = attr_value->mutable_value();
    size_t compress_size = zstd_util_compress(payload->data(), payload->size(), &compress_val, -5);
    if (compress_size > 0) {
      attr_value->set_compress_mode(PackedItemAttrValue_CompressMode_ZSTD);
      payload->swap(compress_val);
      return true;
    } else {
      return false;
    }
  }
  return false;
}

static std::vector<int> ConvertDataVersionStringToVector(const std::string &version) {
  std::vector<int> version_number;
  if (version.size() < 1) return version_number;
  std::vector<std::string> version_string;
  // 首位如果是数字，那么不进行裁剪操作；如果非数字，去掉第一位。默认格式为 Vxx.xx.xx
  base::SplitString(((version[0] >= '0' && version[0] <= '9') ? version : version.substr(1)), ".",
                    &version_string);
  for (auto &str : version_string) {
    version_number.push_back(atoi(str.c_str()));
  }
  return version_number;
}

// traceback version 固定格式为: Vxx.xx.xx
// version1 > version2 return 1
// version1 == version2 return 0
// version1 < version2 return -1
static int CompareTracebackDataVersion(const std::string &version1, const std::string &version2) {
  if (version1 == version2) return 0;
  std::vector<int> version_number_1 = ConvertDataVersionStringToVector(version1);
  std::vector<int> version_number_2 = ConvertDataVersionStringToVector(version2);
  if (version_number_1.size() != 3 || version_number_1.size() != version_number_2.size()) {
    // 未填写 version，默认为 false；
    // subflow 的情况下默认不填写 version。
    return -1;
  }
  for (int i = 0; i < std::min(version_number_1.size(), version_number_2.size()); i++) {
    if (version_number_1[i] > version_number_2[i]) {
      return 1;
    } else if (version_number_1[i] < version_number_2[i]) {
      return -1;
    }
  }
  return 0;
}

static void RecordTracebackSequence(CommonRecoStepInfo *info, const std::vector<int> &sequence_vec) {
  for (int i = 0; i < sequence_vec.size(); i++) {
    if (i < info->sequence_size()) {
      info->set_sequence(i, sequence_vec[i]);
    } else {
      info->add_sequence(sequence_vec[i]);
    }
  }
}

static std::vector<int> SliceTracebackData(const ks::platform::CommonRecoTraceback &traceback_info,
                                           int64 msg_origin_size) {
  std::vector<int> patch_end;
  if (traceback_info.step_info_size() > 0) {
    int64 patch_size = msg_origin_size;
    for (int i = 0; i < traceback_info.step_info_size(); i++) {
      int curr_size = traceback_info.step_info(i).ByteSize();
      patch_size += curr_size;
      if (patch_size >= FLAGS_kafka_message_size_limit) {
        patch_end.push_back(i);
        patch_size = curr_size;
      }
    }
    patch_end.push_back(traceback_info.step_info_size());
  }
  return patch_end;
}

static void InitTraceback(ReadableRecoContextInterface *context,
                          MutableRecoContextInterface *execute_context) {
  // traceback Init
  // subflow traceback init
  std::vector<int> sequence_vec = context->GetTracebackSequence();
  sequence_vec.push_back(0);
  execute_context->SetTracebackSequence(std::move(sequence_vec));
  // set context
  execute_context->SetNeedStepInfo(context->GetNeedStepInfo());
  execute_context->SetShouldCompressItem(context->GetShouldCompressItem());
  execute_context->SetTracebackDataVersion(context->GetTracebackDataVersion());
  execute_context->SetShouldDropStudioField(context->GetShouldDropStudioField());
  execute_context->SetTracebackCompressMode(context->GetTracebackCompressMode());
  auto return_item_attrs = context->GetTracebackItemAttrs();
  if (return_item_attrs) {
    execute_context->SetTracebackItemAttrs(*return_item_attrs);
  }
  auto return_common_attr = context->GetTracebackCommonAttrs();
  if (return_common_attr) {
    execute_context->SetTracebackCommonAttrs(*return_common_attr);
  }
  execute_context->SetTracebackProcessors(context->GetTracebackProcessors());
  execute_context->SetTracebackProcessorsBlacklist(context->GetTracebackProcessorsBlacklist());
  execute_context->SetTracebackCollectMode(context->GetTracebackCollectMode());
  execute_context->SetTracebackWhiteListUserReportAllProcessors(
      context->GetTracebackWhiteListUserReportAllProcessors());
  CL_LOG_EVERY_N(INFO, 1000) << "subflow traceback_data_version: "
                             << execute_context->GetTracebackDataVersion()
                             << ", need_step_info_: " << execute_context->GetNeedStepInfo();
}

}  // namespace traceback_util
}  // namespace platform
}  // namespace ks
