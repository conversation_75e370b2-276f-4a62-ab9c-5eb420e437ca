#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "folly/container/F14Map.h"
#include "redis_proxy_client/redis_mock.h"

namespace ks {
namespace platform {

class RecoRedisMock : public ::ks::infra::redis::IRedisMock {
 public:
  bool MockRedisRequest(const std::string &cluster_name, const std::vector<std::string> &requests,
                        redisReply **response, ks::infra::RedisErrorCode *error_code) override {
    auto it = redis_responses_.find(cluster_name);

    if (it != redis_responses_.end()) {
      const std::vector<std::string> mock_response = it->second;
      // requests: [mget, key1, key2], mock_response: [val1, val2]
      if (requests.size() != mock_response.size() + 1) {
        return true;
      }
      auto reply = reinterpret_cast<redisReply *>(calloc(1, sizeof(redisReply)));
      reply->type = REDIS_REPLY_ARRAY;
      reply->elements = requests.size() - 1;
      reply->element = reinterpret_cast<redisReply **>(calloc(requests.size(), sizeof(redisReply *)));
      for (int i = 0; i < reply->elements; i++) {
        auto sub_reply = reinterpret_cast<redisReply *>(calloc(1, sizeof(redisReply)));
        sub_reply->type = REDIS_REPLY_STRING;
        int length = mock_response[i].length();
        sub_reply->str = reinterpret_cast<char *>(malloc((length + 1) * sizeof(char)));
        sub_reply->len = length;
        snprintf(sub_reply->str, length + 1, "%s", mock_response[i].c_str());
        reply->element[i] = sub_reply;
      }
      *response = reply;
    }
    return true;
  }

  void AddRedisMockResponse(const std::string &cluster_name, const std::vector<std::string> &response) {
    redis_responses_[cluster_name] = response;
  }

 private:
  folly::F14FastMap<std::string, std::vector<std::string>> redis_responses_;
};

class CommonRecoLeafRedisMocker {
 public:
  void MockRedisResponse(const std::vector<std::string> &cluster_names,
                         const std::vector<std::vector<std::string>> &responses) {
    if (cluster_names.size() != responses.size()) {
      CL_LOG(INFO) << "redis mock cancelled, length of \"cluster_names\" and \"responses\" not equal";
      return;
    }
    ks::infra::redis::TurnOnMockReidsClientClusterName();
    auto mock_redis = std::make_unique<RecoRedisMock>();

    for (int i = 0; i < cluster_names.size(); i++) {
      mock_redis->AddRedisMockResponse(cluster_names[i], responses[i]);
    }
    ks::infra::redis::SetRedisMock(std::move(mock_redis));
  }
};

}  // namespace platform
}  // namespace ks
