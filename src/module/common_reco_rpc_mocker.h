#pragma once

#include <kess/rpc/mock_rpc.h>
#include <memory>
#include <string>
#include <utility>

#include "dragon/src/module/common_reco_pipeline_executor.h"

namespace ks {
namespace platform {

class RecoMockRpc : public ks::kess::rpc::IRpcMock {
 public:
  bool MockRpc(const std::string &service_name, const std::string &method_name, const std::string &shard,
               const ::google::protobuf::Message *request, ::grpc::Status *status,
               ::google::protobuf::Message *response) {
    if (service_name == service_name_ && (method_name_.empty() || method_name_ == method_name) &&
        (shard_.empty() || shard_ == shard)) {
      auto st = google::protobuf::util::JsonStringToMessage(response_json_str_, response);
      if (st.ok()) {
        return true;
      }
      CL_LOG(ERROR) << "MockRpc failed, error: " << st.ToString() << " json_str: " << response_json_str_;
    } else {
      CL_LOG(ERROR) << "invalid MockRpc parameters, service_name: " << service_name << " vs " << service_name_
                    << " method: " << method_name << " vs " << method_name_ << " shard: " << shard << " vs "
                    << shard_;
    }
    return true;
  }

  void SetServiceName(const std::string &service_name) {
    service_name_ = service_name;
  }
  void SetMethodName(const std::string &method_name) {
    method_name_ = method_name;
  }
  void SetShard(const std::string &shard) {
    shard_ = shard;
  }
  void SetResponse(const std::string &response_json_str) {
    response_json_str_ = response_json_str;
  }

 private:
  std::string service_name_;
  std::string method_name_;
  std::string shard_;
  std::string response_json_str_;
};

/**
 * rpc mocker
 */
class CommonRecoLeafRpcMocker {
 public:
  void MockRpcResponse(const std::string &service_name, const std::string &method_name,
                       const std::string &shard, const std::string &response_json_str) {
    if (service_name.empty() || response_json_str.empty()) {
      CL_LOG(INFO) << "rpc mock cancelled, missing \"service_name\" or \"response_json_str\"";
      return;
    }
    std::unique_ptr<ks::kess::rpc::IRpcMock> mock_rpc(new RecoMockRpc());
    static_cast<RecoMockRpc *>(mock_rpc.get())->SetServiceName(service_name);
    static_cast<RecoMockRpc *>(mock_rpc.get())->SetResponse(response_json_str);

    if (!method_name.empty()) static_cast<RecoMockRpc *>(mock_rpc.get())->SetMethodName(method_name);

    if (!shard.empty()) static_cast<RecoMockRpc *>(mock_rpc.get())->SetShard(shard);

    ks::kess::rpc::RpcMockMgr::Instance()->SetMockCallback(std::move(mock_rpc));
  }
};

}  // namespace platform
}  // namespace ks
