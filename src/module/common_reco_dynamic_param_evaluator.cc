#include "dragon/src/module/common_reco_dynamic_param_evaluator.h"

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "base/common/basic_types.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/logging_util.h"
#include "ks/reco_pub/reco/util/hyper_config_key.h"

DEFINE_bool(use_micro_world, true, "use mille world or not");

namespace ks {
namespace platform {

static const int32 kLogInterval = 10000;

bool CommonRecoDynamicParamEvaluator::ProcessDynamicParams(
    const RequestContext &request_context, base::ScoreDynamicParameterHandler *dynamic_parameter_holder,
    base::DynamicParameter *dynamic_parameter, const base::Json *parameters,
    const base::ScoreCalculator *score_calculator) {
  // 参数检查，运行时，尽量不要 CHECK
  if (!parameters || !dynamic_parameter_holder) {
    CL_LOG_ERROR_EVERY("dynamic_parameter", "null_parameters", kLogInterval)
        << "Required Parameters or DynamicParameterHandler is nullptr"
        << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
    return false;
  }

  // 解析动态参数
  if (!dynamic_parameter_holder->Init(score_calculator, parameters)) {
    CL_LOG_ERROR_EVERY("dynamic_parameter", "dynamic_parameter_fail", kLogInterval)
        << "ParseDynamicParams failed!" << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
    return false;
  }

  // 请求级别参数求值
  if (!EvaluateAndFillRequestLevelParam(request_context, dynamic_parameter_holder, dynamic_parameter)) {
    CL_LOG_ERROR_EVERY("dynamic_parameter", "process_params_fail", kLogInterval)
        << "Processing Params Failed!" << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
    return false;
  }

  return true;
}

bool CommonRecoDynamicParamEvaluator::EvaluateAndFillRequestLevelParam(
    const RequestContext &request_context, base::ScoreDynamicParameterHandler *dynamic_parameter_holder,
    base::DynamicParameter *dynamic_parameter) {
  for (const auto *dynamic_param : *dynamic_parameter_holder->GetRequestLevelDynamicParams()) {
    if (dynamic_param == nullptr) {
      continue;
    }
    double value;
    if (CommonRecoDynamicParamEvaluator::EvaluateOneRequestLevelParam(request_context, dynamic_param,
                                                                      &value)) {
      dynamic_parameter->Set(dynamic_param->key_sign(), value);
    } else {
      CL_LOG_ERROR("dynamic_parameter", "load_param_failed")
          << "load param failed! key = " << dynamic_param->key() << ", set value 0."
          << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
      value = 0;
      dynamic_parameter->Set(dynamic_param->key_sign(), value);
    }
  }
  return true;
}

bool CommonRecoDynamicParamEvaluator::EvaluateItemLevelParam(
    const uint64 item_key, const MutableRecoContextInterface *context,
    base::ScoreDynamicParameterHandler *dynamic_parameter_holder, base::DynamicParameter *dynamic_parameter) {
  int error_count = 0;
  for (const auto *dynamic_param : *dynamic_parameter_holder->GetItemLevelDynamicParams()) {
    if (dynamic_param == nullptr) {
      continue;
    }
    double value;
    if (CommonRecoDynamicParamEvaluator::EvaluateOneItemLevelParam(item_key, dynamic_param, context,
                                                                   &value)) {
      dynamic_parameter->Set(dynamic_param->key_sign(), value);
    } else {
      value = 0;
      dynamic_parameter->Set(dynamic_param->key_sign(), value);
      LOG_IF_EVERY_N(ERROR, error_count == 0, kLogInterval)
          << "Evaluate failed! param : " << dynamic_param->key();
      VLOG(1) << "FAIL! item_key: " << item_key << ", param: " << dynamic_param->key();
      error_count++;
    }
  }
  return (error_count == 0);
}

bool CommonRecoDynamicParamEvaluator::EvaluateOneRequestLevelParam(
    const RequestContext &request_context, const base::internal::ScoreDynamicParam *one_param,
    double *value) {
  for (const auto &source : one_param->sources()) {
    VLOG(100) << "attr_name: " << one_param->key() << ", type: " << source.source_type();
    if (source.source_type() == base::internal::ScoreDynamicParam::USER_SOURCE) {
      if (CalculateUserSource(request_context, source, value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << one_param->key_sign()
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else if (source.source_type() == base::internal::ScoreDynamicParam::AB_TEST_SOURCE) {
      if (CalculateAbTestSource(request_context, source, value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << one_param->key_sign()
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else if (source.source_type() == base::internal::ScoreDynamicParam::KCONF_SOURCE) {
      if (CalculateKconfSource(request_context, source, value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << one_param->key_sign()
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else if (source.source_type() == base::internal::ScoreDynamicParam::DEFAULT_VAL_SOURCE) {
      if (CalculateDefaultSource(source, value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << one_param->key_sign()
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else {
      CL_LOG_ERROR_EVERY("dynamic_parameter", "wrong_source_type", kLogInterval)
          << "Wrong Source Type Found! key: " << one_param->key() << ", source_type: "
          << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
          << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
      return false;
    }
  }
  return false;
}

bool CommonRecoDynamicParamEvaluator::CalculateDefaultSource(
    const base::internal::ScoreDynamicParam::Source &one_source, double *value) {
  CHECK(one_source.source_type() == base::internal::ScoreDynamicParam::DEFAULT_VAL_SOURCE);
  *value = one_source.value();
  return true;
}
bool CommonRecoDynamicParamEvaluator::DebugOneItemLevelParam(
    const uint64 item_key, const base::internal::ScoreDynamicParam *one_param,
    const MutableRecoContextInterface *context, double *value, std::string *str_value) {
  return EvaluateOneItemLevelParam(item_key, one_param, context, value, str_value);
}

bool CommonRecoDynamicParamEvaluator::EvaluateOneItemLevelParam(
    const uint64 item_key, const base::internal::ScoreDynamicParam *one_param,
    const MutableRecoContextInterface *context, double *value, std::string *str_value) {
  for (const auto &source : one_param->sources()) {
    if (source.source_type() == base::internal::ScoreDynamicParam::ITEM_SOURCE ||
        source.source_type() == base::internal::ScoreDynamicParam::PRED_SOURCE) {
      if (CalculateItemSource(source, context, item_key, value, str_value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << item_key
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else if (source.source_type() == base::internal::ScoreDynamicParam::DEFAULT_VAL_SOURCE) {
      if (CalculateDefaultSource(source, value)) {
        VLOG(100) << "key: " << one_param->key() << ", key_sign: " << one_param->key_sign()
                  << ", choice: " << base::internal::ScoreDynamicParam::ChoiceSpec_Name(source.choice_spec())
                  << ", source_type: "
                  << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
                  << ", attr_name: " << source.attr_name() << ", value: " << *value;
        return true;
      }
    } else {
      CL_LOG_ERROR_EVERY("dynamic_parameter", "wrong_source_type", kLogInterval)
          << "Wrong Source Type Found! key: " << one_param->key() << ", source_type: "
          << base::internal::ScoreDynamicParam::ParamSourceType_Name(source.source_type())
          << RecoUtil::GetRequestInfoForLog(context);
      return false;
    }
  }

  CL_LOG_ERROR_EVERY("dynamic_parameter", "all_sources_failed", kLogInterval)
      << "All sources failed for " << one_param->key() << RecoUtil::GetRequestInfoForLog(context);
  return false;
}

bool CommonRecoDynamicParamEvaluator::CalculateUserSource(
    const RequestContext &request_context, const base::internal::ScoreDynamicParam::Source &one_source,
    double *value) {
  const auto context = request_context.reco_context_;
  if (auto val = context->GetDoubleCommonAttr(one_source.attr_name())) {
    *value = *val;
    return true;
  } else if (auto val = context->GetIntCommonAttr(one_source.attr_name())) {
    *value = *val;
    return true;
  }
  CL_LOG_EVERY_N(WARNING, 100) << "common attr [" << one_source.attr_name()
                               << "] is not a number type(int/double)"
                               << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
  return false;
}

// 获得超参值
bool CommonRecoDynamicParamEvaluator::CalculateKconfSource(
    const RequestContext &request_context, const base::internal::ScoreDynamicParam::Source &one_source,
    double *value) {
  if (value) {
    const std::string &config_key = one_source.attr_name();
    VLOG(100) << "attr_name:" << config_key;
    if (auto int32_config = ks::reco::ConfigKey<int32_t>::GetKconfValue(config_key)) {
      *value = int32_config->Get();
      VLOG(100) << "hyper_config_key:" << config_key << "value:" << *value;
      return true;
    } else if (auto int64_config = ks::reco::ConfigKey<int64_t>::GetKconfValue(config_key)) {
      *value = int64_config->Get();
      VLOG(100) << "hyper_config_key:" << config_key << "value:" << *value;
      return true;
    } else if (auto double_config = ks::reco::ConfigKey<double>::GetKconfValue(config_key)) {
      *value = double_config->Get();
      VLOG(100) << "hyper_config_key:" << config_key << "value:" << *value;
      return true;
    } else if (auto bool_config = ks::reco::ConfigKey<bool>::GetKconfValue(config_key)) {
      *value = bool_config->Get();
      VLOG(100) << "hyper_config_key:" << config_key << "value:" << *value;
      return true;
    } else {
      CL_LOG_ERROR("dynamic_parameter", "unsupported_type")
          << "not support type or not define config_key in hyper_config_key.h"
          << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
    }
  }
  return false;
}

bool CommonRecoDynamicParamEvaluator::CalculateAbTestSource(
    const RequestContext &request_context, const base::internal::ScoreDynamicParam::Source &one_source,
    double *value) {
  const auto *context = request_context.reco_context_;
  const ks::SessionContext *session_context =
      request_context.session_context_
          ? request_context.session_context_
          : GetThreadSessionContext(context->GetUserId(), context->GetDeviceId());
  ks::SessionContext *new_micro_session_context = nullptr;
  if (FLAGS_use_micro_world && FLAGS_abtestbiz_seq_num >= 0 && !(request_context.micro_session_context_)) {
    new_micro_session_context = ks::SessionContextFactory::Create(
        static_cast<ks::AbtestBiz>(FLAGS_abtestbiz_seq_num), context->GetUserId(), context->GetDeviceId());
    if (!new_micro_session_context) {
      VLOG(50) << "ab-test micro session context create error";
      return false;
    }
  }
  const ks::SessionContext *micro_session_context = request_context.micro_session_context_
                                                        ? request_context.micro_session_context_
                                                        : new_micro_session_context;
  if (session_context == nullptr && micro_session_context == nullptr) {
    CL_LOG_ERROR_EVERY("dynamic_parameter", "null_session_context", kLogInterval)
        << "session_context not found!" << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
    return false;
  }
  const Json *config = nullptr;
  if (session_context) {
    config = session_context->GetConfigContainerNullable(one_source.attr_name());
    if (!config && !micro_session_context) {
      VLOG(50) << "ab-test param not found, name : " << one_source.attr_name()
               << ", debug-str: " << session_context->TryGetDebugString(one_source.attr_name());
      return false;
    }
  }
  if (!config && micro_session_context) {
    if ((config = micro_session_context->GetConfigContainerNullable(one_source.attr_name())) == nullptr) {
      VLOG(50) << "ab-test param not found, name : " << one_source.attr_name()
               << ", debug-str: " << micro_session_context->TryGetDebugString(one_source.attr_name());
      FreeSessionContextMem(new_micro_session_context);
      return false;
    }
  }
  if (config->IsBoolean()) {
    bool result;
    CHECK(config->BooleanValue(&result));
    *value = result ? 1.0 : 0.0;
    FreeSessionContextMem(new_micro_session_context);
    return true;
  } else if (config->IsInteger()) {
    int64 result;
    CHECK(config->IntValue(&result));
    *value = result;
    FreeSessionContextMem(new_micro_session_context);
    return true;
  } else if (config->IsDouble()) {
    CHECK(config->FloatValue(value));
    FreeSessionContextMem(new_micro_session_context);
    return true;
  }
  CL_LOG_ERROR_EVERY("dynamic_parameter", "abtest_not_number", kLogInterval)
      << "ab-test param: " << one_source.attr_name() << " is no Number or Boolean"
      << RecoUtil::GetRequestInfoForLog(request_context.reco_context_);
  FreeSessionContextMem(new_micro_session_context);
  return false;
}

bool CommonRecoDynamicParamEvaluator::CalculateItemSource(
    const base::internal::ScoreDynamicParam::Source &one_source, const MutableRecoContextInterface *context,
    uint64 item_key, double *value, std::string *str_value) {
  if (value) {
    // 首先从 context 中获得索引 value 的逻辑
    auto double_value = context->GetDoubleItemAttr(item_key, one_source.attr_name());
    if (double_value) {
      *value = *double_value;
      VLOG(100) << "attr_name: " << one_source.attr_name() << ", item_key: " << item_key
                << ", value: " << *value;
      return true;
    }

    auto int_value = context->GetIntItemAttr(item_key, one_source.attr_name());
    if (int_value) {
      *value = *int_value;
      VLOG(100) << "attr_name: " << one_source.attr_name() << ", item_key: " << item_key
                << ", value: " << *value;
      return true;
    }
  }

  if (str_value) {
    auto string_value = context->GetStringItemAttr(item_key, one_source.attr_name());
    if (string_value) {
      *str_value = std::string(*string_value);
      VLOG(100) << "attr_name: " << one_source.attr_name() << ", item_key: " << item_key
                << ", value: " << *str_value;
      return true;
    }
  }

  VLOG(100) << "cannot get item attr, item_key: " << item_key << ", attr_name: " << one_source.attr_name()
            << RecoUtil::GetRequestInfoForLog(context);
  return false;
}
}  // namespace platform
}  // namespace ks
