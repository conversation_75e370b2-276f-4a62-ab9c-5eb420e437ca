#pragma once
#include <memory>

#include "dragon/src/core/common_reco_util.h"
#include "ks/util/json.h"
#include "serving_base/server_base/server_status.h"
#include "serving_base/util/dynamic_config.h"

namespace ks {
namespace platform {
class JsonConfigHandler : public serving_base::WebRequestHandler {
 public:
  JsonConfigHandler() {}
  ~JsonConfigHandler() {}
  void ProcessRequest(net::HTTPSession *session) {
    std::shared_ptr<ks::Json> config = GlobalHolder::GetDynamicJsonConfig();
    if (config) {
      session->response.data = config->ToString();
    } else {
      session->response.data = "{\"error\": \"null json config\"}";
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(JsonConfigHandler);
};

}  // namespace platform
}  // namespace ks
