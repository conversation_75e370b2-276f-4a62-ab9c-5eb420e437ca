#pragma once
// NOTE(huiyiqun): this file should be included by main.cc only!!!

#include "algo-engine/kaiworks/kaiworks-stream-proto/src/main/proto/kuaishou/kaiworks/stream/proto/row.pb.h"
#include "ks/common_reco/common_kv/proto/common_kv.pb.h"
#include "ks/ds/relation/relation_lib/proto/relation_api.pb.h"
#include "ks/ds/relation/relation_lib/proto/user_relation_photo_upload_service.pb.h"
#include "ks/ksib_reco/ksib_proto/proto/ksib/reco/photo/photo_profile.pb.h"
#include "ks/reco/fm/fm_action_server/reco_fm.pb.h"
#include "ks/reco/plateco-dsalog/ics/shop_customer_service/kess/dialog_nlg_service/model_serving.pb.h"
#include "ks/reco_proto/action/user_action_log.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/log/client_log/client_log.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/newsmodel/mix_rank_log_hive_schema.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/reco/explore_pic_joint_reco_log.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/reco/eyeshot_joint_reco_log.pb.h"
#include "ks/reco_proto/ad/ad_proto/kuaishou/reco/eyeshot_joint_reco_tiny_log.pb.h"
#include "ks/reco_proto/big_river/big_river_data.pb.h"
#include "ks/reco_proto/big_river/big_river_live_label_conf.pb.h"
#include "ks/reco_proto/live/gift.pb.h"
#include "ks/reco_proto/live/relationfollowing_service.pb.h"
#include "ks/reco_proto/search/user_query_click.pb.h"
#include "ks/reco_proto/merchantreco/kwaishop_marketing_coupon_query_service.pb.h"
#include "ks/reco_proto/merchantreco/kwaishop_product_shelf_index_reco_search_rpc_service.pb.h"
#include "ks/reco_proto/merchantreco/merchant_delivery_address_list.pb.h"
#include "ks/reco_proto/merchantreco/merchant_coupon_info.pb.h"
#include "ks/reco_proto/merchantreco/merchant_reco_incentive.pb.h"
#include "ks/reco_proto/merchantreco/merchant_reco_pageshow.pb.h"
#include "ks/reco_proto/merchantreco/merchant_reco_mocius.pb.h"
#include "ks/reco_proto/merchantreco/merchant_reco_swtn.pb.h"
#include "ks/reco_proto/merchantreco/merchant_reco_white_box.pb.h"
#include "ks/reco_proto/merchantreco/merchant_uni_ug_sample_server.pb.h"
#include "ks/reco_proto/merchantreco/merchant_user_action_list.pb.h"
#include "ks/reco_proto/nearby/poi_search.pb.h"
#include "ks/reco_proto/nearby/klink_user_status.pb.h"
#include "ks/reco_proto/nearby/frigate_fetch_location_sdk.pb.h"
#include "ks/reco_proto/proto/comment_rank_info.pb.h"
#include "ks/reco_proto/proto/entity_tag_service.pb.h"
#include "ks/reco_proto/proto/fans_group_open_service.pb.h"
#include "ks/reco_proto/proto/fishpond.pb.h"
#include "ks/reco_proto/proto/follow_live.pb.h"
#include "ks/reco_proto/proto/inner_fanstop_redis_value.pb.h"
#include "ks/reco_proto/proto/interest_model.pb.h"
#include "ks/reco_proto/proto/invite_pk_income.pb.h"
#include "ks/reco_proto/proto/live_pk_reco_invite_filter_service.pb.h"
#include "ks/reco_proto/proto/merchant_profile.pb.h"
#include "ks/reco_proto/proto/merchant_user_pk.pb.h"
#include "ks/reco_proto/proto/merchant/reco_merchant_bh_huopan_retr.pb.h"
#include "ks/reco_proto/proto/mmulac_service.pb.h"
#include "ks/reco_proto/proto/nearby_hotspot.pb.h"
#include "ks/reco_proto/proto/platform_coupon_req.pb.h"
#include "ks/reco_proto/proto/puji/user_profile.pb.h"
#include "ks/reco_proto/proto/realtime_reco.pb.h"
#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_proto/proto/reco_browse_set_adapt_java_service.pb.h"
#include "ks/reco_proto/proto/reco_distribute_ner.pb.h"
#include "ks/reco_proto/proto/reco_distribute_search_request.pb.h"
#include "ks/reco_proto/proto/reco_galaxy_retr_service.pb.h"
#include "ks/reco_proto/proto/reco_hotword_info.pb.h"
#include "ks/reco_proto/proto/reco_interest_explore.pb.h"
#include "ks/reco_proto/proto/reco_live_author_support.pb.h"
#include "ks/reco_proto/proto/reco_live_slide_live_stream_info.pb.h"
#include "ks/reco_proto/proto/reco_live_stream_pk.pb.h"
#include "ks/reco_proto/proto/reco_local_life.pb.h"
#include "ks/reco_proto/proto/reco_log_info.pb.h"
#include "ks/reco_proto/proto/reco_nr/reco_nr_session.pb.h"
#include "ks/reco_proto/proto/reco_nr/user_profile_query_service.pb.h"
#include "ks/reco_proto/proto/reco_nr_fr_pxtr.pb.h"
#include "ks/reco_proto/proto/reco_nr_poi_service.pb.h"
#include "ks/reco_proto/proto/reco_rank_ncee_score.pb.h"
#include "ks/reco_proto/proto/reco_rl.pb.h"
#include "ks/reco_proto/proto/reco_rpc_percentile_service.pb.h"
#include "ks/reco_proto/proto/reco_share_info.pb.h"
#include "ks/reco_proto/proto/reco_tag_photo.pb.h"
#include "ks/reco_proto/proto/reco_tnu_rank_xtr_log.pb.h"
#include "ks/reco_proto/proto/reco_with_ad_log.pb.h"
#include "ks/reco_proto/proto/relation_detail_service.pb.h"
#include "ks/reco_proto/proto/session_based_rl.pb.h"
#include "ks/reco_proto/proto/slide_high_value_data/author_chase_value_info.pb.h"
#include "ks/reco_proto/proto/slide_high_value_data/high_value_info.pb.h"
#include "ks/reco_proto/proto/user_interest_server.pb.h"
#include "ks/reco_proto/proto/user_live_paid_tag_feature_90d.pb.h"
#include "ks/reco_proto/proto/user_shop_consume.pb.h"
#include "ks/reco_proto/proto/zt_music.pb.h"
#include "ks/reco_proto/rodis_v2_proto/rodis_v2_service.pb.h"
#include "ks/reco_proto/rta_proto/alliance.pb.h"
#include "ks/reco_proto/rta_proto/gt_feature.pb.h"
#include "ks/reco_proto/rta_proto/hive2redis_data.pb.h"
#include "ks/reco_proto/rta_proto/rta_reco_leaf_context.pb.h"
#include "ks/reco_proto/rtb_proto/matrix_install_info.pb.h"
#include "ks/reco_proto/rtb_proto/new_rtb_creative.pb.h"
#include "ks/reco_proto/rtb_proto/rtb_attr_save.pb.h"
#include "ks/reco_proto/rtb_proto/rtb_reflux_device_profile.pb.h"
#include "ks/reco_proto/rtb_proto/use_profile_judge_service.pb.h"
#include "ks/reco_proto/sample_log/mix_sample_log.pb.h"
#include "ks/reco_proto/tdm_retr/tdm_tree_server.pb.h"
#include "ks/reco_proto/ug_feature/feature_model.pb.h"
#include "ks/reco_proto/ug_feature/feature_service.pb.h"
#include "teams/reco-arch/colossusdb/colossusdb-proto/label_list.pb.h"
#include "teams/reco-arch/kgnn/src/proto/gnn_kv_service.pb.h"
#include "ks/reco_proto/proto/feature.pb.h"
#include "ks/reco_proto/proto/eyeshot/rl/eyeshot_pic_rl.pb.h"

// NOTE(fangjianbing): 头文件 include 顺序按路径字母序排列, 不要盲目加在最后一行!

namespace ks {
namespace platform {
namespace protobuf_reg {
namespace {
::colossusdb::proto::LabelList label_list_reg;
::kuaishou::promotion::matrix::feature::BaseResponse base_resp_reg;
::kuaishou::promotion::matrix::feature::FeatureParam ug_feature_param_reg;
::kuaishou::promotion::matrix::feature::FeatureValueResponseV1 ug_feature_value_resp_v1_reg;
::kuaishou::promotion::matrix::feature::FeatureValueType ug_feature_value_type_reg;
::kuaishou::promotion::matrix::feature::GetUgFeatureRequest ug_feature_req_reg;
::kuaishou::promotion::matrix::feature::GetUgFeatureResponseV1 ug_feature_resp_v1_reg;
::kuaishou::promotion::matrix::feature::ResponseCode resp_code_reg;
::com::kuaishou::promotion::matrix::protobuf::AllianceProfileFeature alliance_profile_feature_reg;
::com::kuaishou::promotion::matrix::protobuf::GtUserFeatures gt_user_features_reg;
::com::kuaishou::promotion::matrix::protobuf::FeatureValue ug_feature_value_reg;
::gnn::BatchInsertRequest batch_insert_request;
::ks::reco::UserInfo user_info_reg;
::ks::reco::RecoLog reco_log_reg;
::ks::reco::PhotoInfo photo_info_reg;
::ks::reco::RecoWithAdLog reco_with_ad_log_reg;
::ks::reco::UserInterestRequest user_interest_req_reg;
::ks::reco::UserInterestResponse user_interest_resp_reg;
::ks::reco::ClientRequestInfo client_reques_info;
::ks::reco::GetPlatformCouponReq get_platform_coupon_reg;
::ks::reco::GetPlatformCouponResp get_platform_coupon_resp;
::ks::reco::HotWordInfo hotword_info_reg;
::ks::reco::LocalLifeActionItem local_life_action_item_reg;
::ks::reco::LocalLifePoiProductInfos local_life_poi_product_infos_reg;
::ks::reco::LocalLifeGoodsRankingResult local_life_goods_ranking_result_reg;
::ks::reco::LocalLifeGoodsRankingInfo local_life_goods_ranking_info_reg;
::ks::reco::LocalLifeUserCouponInfo local_life_user_coupon_info_reg;
::ks::reco::RecoPhotoRewardTask reco_photo_reward_task_reg;
::ks::reco::RecoPhotoRewardTaskDetail reco_photo_reward_task_detail_reg;
::ks::reco::LocalLifeCouponInfo local_life_coupon_info_reg;
::ks::reco::RedisValueList redis_value_list_reg;
::ks::reco::UserEmbAndCnt user_emb_and_cnt_reg;
::ks::action::UserActionLog user_action_log_reg;
::ks::reco::AdsKsSocUserDigAuthorInfoApiNd user_revenue_author;
::ks::reco::arch::log::MixLogSampleInfo mix_sample_log_reg;
::ks::reco::highvalue::HighValueAuthor high_value_author_info;
::ks::reco::highvalue::AuthorChaseValue author_chase_value_info;
::ks::reco::share::ShareInfos share_infos_reg;
::kuaishou::ds::relation::BatchRelationInfoRequest relation_api_req_reg;
::kuaishou::ds::relation::BatchRelationInfoResponse relation_api_resp_reg;
::kuaishou::ds::relation::UserRelationPhotoUploadRequest user_relation_photo_upload_req_reg;
::kuaishou::ds::relation::UserRelationPhotoUploadResponse user_relation_photo_upload_resp_reg;
::kuaishou::comment::CommentRankInfoRequest comment_rank_info_req_reg;
::kuaishou::comment::CommentRankInfoResponse comment_rank_info_resp_reg;
::kuaishou::reco::PoiNearbyRecoReq poi_nearby_reco_req;
::kuaishou::reco::PoiNearbyRecoResp poi_nearby_reco_resp;
::kuaishou::reco::DistributeSearchItemQueryRequest search_item_query_req_reg;
::kuaishou::reco::DistributeSearchItemQueryResponse search_item_query_resp_reg;
::kuaishou::reco::GetTagTopPhotoIdsRequest get_tag_top_photo_ids_req;
::kuaishou::reco::GetTopPhotoIdsResponse get_top_photo_ids_resp;
::kuaishou::reco::KOLRecognizeGoodsEntityRequest search_goods_entity_req_reg;
::kuaishou::reco::KOLRecognizeGoodsEntityResponse search_goods_entity_resp_reg;
::kuaishou::reco::KOLRecognizeGoodsBrandRequest search_goods_brand_req_reg;
::kuaishou::reco::KOLRecognizeGoodsBrandResponse search_goods_brand_resp_reg;
::kuaishou::reco::NrUserRankCache nr_user_rank_cache_reg;
::kuaishou::reco::QueryItemIdListRequest reco_homepage_index_req_reg;
::kuaishou::reco::QueryItemListRequest reco_homepage_table_req_reg;
::kuaishou::reco::QueryItemIdListResponse reco_homepage_index_resp_reg;
::kuaishou::reco::QueryItemListResponse reco_homepage_table_resp_reg;
::kuaishou::reco::RocketSyncPlanBatch  rocket_sync_plan_batch_reg;
::kuaishou::reco::TnuRankXtrLog tnu_rank_xtr_log_reg;
::kuaishou::reco::XtrFloatFeatures xtr_float_features_reg;
::kuaishou::reco::reco_nr::RecoNewNrSessionBasedJointLogV1 reco_nr_session_reg;
::kuaishou::reco::reco_nr::RecoNewNrSessionBasedJointLogV2 reco_nr_session_v2_reg;
::kuaishou::reco::reco_nr::UserProfileQueryResponse user_profile_query_resp_reg;
::kuaishou::reco::rodis_v2::CounterMapBatchGetRequest counter_map_batch_get_req_reg;
::kuaishou::reco::rodis_v2::CounterMapBatchGetResponse counter_map_batch_get_resp_reg;
::kuaishou::reco::interest::InterestsRequest interest_model_req_reg;
::kuaishou::reco::interest::InterestsResponse interest_model_resp_reg;
::kuaishou::reco::FmActionRequest fmaction_req_reg;
::kuaishou::reco::FmActionResponse fmaction_resp_reg;
::kuaishou::reco::RecoNceeOriginalScoreMsg reco_ncee_origin_score_msg_reg;
::kuaishou::reco::RecoGalaxyRetrRequest galaxy_req_reg;
::kuaishou::reco::RecoGalaxyRetrResponse galaxy_resp_reg;
::kuaishou::kwaishop::apollo::pinocchio::center::PKUserInfo pk_user_info;
::kuaishou::kwaishop::apollo::pinocchio::center::MerchantUserPKRequest merchant_user_pk_req_reg;
::kuaishou::kwaishop::apollo::pinocchio::center::MerchantUserPKResponse merchant_user_pk_resp_reg;
::mmu::serving::PredictRequest predict_request_reg;
::mmu::serving::PredictResult predict_result_reg;
::mmu::serving::BatchPredictRequest batch_predict_request_reg;
::mmu::serving::BatchPredictResult batch_predict_result_reg;
::kuaishou::livestream::LivePkRecoInviteAuthorInfo live_pk_reco_invite_author_info;
::kuaishou::livestream::LivePkRecoInviteFilterRequest live_pk_reco_invite_filter_request;
::kuaishou::livestream::LivePkRecoInviteFilterResponse live_pk_reco_invite_filter_response;
::kuaishou::livestream::LiveStreamIncomeInfo live_pk_reco_invite_income_info;
::kuaishou::livestream::LiveStreamIncomeParam live_pk_reco_invite_income_param;
::kuaishou::livestream::LiveStreamIncomeRequest live_pk_reco_invite_income_request;
::kuaishou::livestream::LiveStreamIncomeResponse live_pk_reco_invite_income_response;
::kuaishou::livestream::LiveStreamNewStatsGetRequest live_pk_reco_invite_stats_request;
::kuaishou::livestream::LiveStreamNewStatsGetResponse live_pk_reco_invite_stats_response;
::kuaishou::livestream::LiveStreamNewStatsProtoModel live_pk_reco_invite_stats_proto;
::mix::kuaishou::reco::ExplorePicJointRecoLogProto explore_pic_joint_reco_log_proto_reg;
::mix::kuaishou::reco::EyeshotJointRecoTinyLogProto eyeshot_joint_reco_tiny_log_proto_reg;
::mix::kuaishou::reco::EyeshotJointRecoLogProto eyeshot_joint_reco_log_proto_reg;
::kuaishou::reco::eyeshot::EyeshotPicRLSession eyeshot_pic_rl_session_reg;
::kuaishou::reco::eyeshot::EyeshotPicRLRequest eyeshot_pic_rl_request_reg;
::mix::kuaishou::client::log::BatchReportEvent batch_report_event_reg;
::kuaishou::relation::GetFollowReasonsRequest get_follow_reason_req;
::kuaishou::relation::FollowFromPageResponse follow_from_page_resp;
::kuaishou::eco::FansGroupSingleVisitorRequest fans_group_single_visitor_req;
::kuaishou::eco::FollowListResponse follow_list_resp;
::ks::fishpond::ReadRequest read_request_reg;
::ks::fishpond::ReadResponse read_response_reg;
::ks::fishpond::UnitFeature unit_feature;
::kuaishou::usershop::AdsKsSocUserShopConsumeApiNd user_shop_consume;
::kuaishou::reco::RecoRankPercentileRequest reco_rank_percentile_req;
::kuaishou::publive::AdsKsSocUserLivePaidTagFeature90d user_live_paid_tag_feature_90d;
::kuaishou::reco::merchant::MerchantGoodClickItem merchant_good_click_item;
::kuaishou::reco::merchant::MerchantGoodOrderItem merchant_good_order_item;
::kuaishou::nearby::hotspot::NearbyHotspotRelatedPhotosRequest Nnarby_hotspot_related_photos_request;
::kuaishou::nearby::hotspot::NearbyHotspotRelatedPhotosResponse nearby_hotspot_related_photos_response;
::kuaishou::nearby::userstatus::QueryInstance query_instance;
::kuaishou::nearby::userstatus::QueryRequest query_request;
::kuaishou::nearby::userstatus::QueryResponse query_response;
::kuaishou::nearby::userstatus::UserStatus user_status;
::kuaishou::reco::nearby::poi::PoiRelateHotPhotosRequest  poi_relate_hot_photos_req;
::kuaishou::reco::nearby::poi::RecallHotPidsInfoResult  recall_hot_pids_info_result;
::kuaishou::reco::nearby::poi::HotPidInfo  hot_pid_info;
::kuaishou::frigate::fetch::location::sdk::UserInfo user_info;
::kuaishou::frigate::fetch::location::sdk::GetLocationStrategyRequest get_location_strategy_request;
::kuaishou::frigate::fetch::location::sdk::LocationStrategy location_strategy;
::kuaishou::frigate::fetch::location::sdk::GetLocationStrategyResponse get_location_strategy_response;
::kuaishou::reco::nearby::poi::Resp  resp;
::kuaishou::reco::merchant::BatchGetPreSkyFallRequest batch_get_pre_sky_fall_request;
::kuaishou::reco::merchant::BatchGetPreSkyFallResponse batch_get_pre_sky_fall_response;
::kuaishou::reco::merchant::PageshowPackage pageshow_package;
::kuaishou::reco::merchant::PageshowItem pageshow_item;
::kuaishou::reco::mocius::CalibratorInfo calibrator_info;
::kuaishou::reco::mocius::CalibratorInfoWrapper calibrator_info_wrapper;
::kuaishou::reco::mocius::CalibratorRecord calibrator_record;
::kuaishou::reco::mocius::CalibratorAggRecord calibrator_agg_record;
::kuaishou::reco::mocius::CalibratorSubRecord calibrator_sub_record;
::kuaishou::reco::mocius::CalibratorAidAggRecord calibrator_aid_agg_record;
::kuaishou::reco::merchant::CurrentState current_state;
::kuaishou::reco::merchant::MerchantRecoSwtnUasInfo merchant_reco_swtn_uas_info;
::kuaishou::reco::merchant::MerchantRecoWhiteBoxMsg merchant_reco_white_box_msg;
::kuaishou::reco::merchant::MerchantUniUgSampleServerRequest merchant_ug_sample_server_request;
::kuaishou::reco::merchant::QueryExistInCrowdsRequest query_exist_crowd_req;
::kuaishou::reco::merchant::QueryExistInCrowdsResponse query_exist_crowd_resp;
::kuaishou::reco::merchant::QueryBuyerTagsRequest query_buyer_tags_request;
::kuaishou::reco::merchant::QueryBuyerTagsResponse query_buyer_tags_response;
::kuaishou::reco::merchant::TagsInfo tags_info;
::kuaishou::reco::merchant::CouponItem coupon_item;
::kuaishou::reco::merchant::TaskCouponItem task_coupon_item;
::kuaishou::reco::merchant::TaskCouponResult task_coupon_result;
::kuaishou::reco::merchant::TaskRequestInfo task_request_info;
::kuaishou::reco::merchant::CouponBaseInfo coupon_base_info;
::kuaishou::reco::merchant::CateCouponInfo cate_coupon_info;
::kuaishou::reco::merchant::CouponPackReqest coupon_pack_reqest;
::kuaishou::reco::merchant::CouponPackItem coupon_pack_item;
::kuaishou::reco::merchant::CouponPackReturn coupon_pack_return;
::kuaishou::reco::merchant::SelectionUserBaseResponseInfo selection_user_base_resp_info;
::kuaishou::reco::interest_explore::LongTermInterest long_term_interest_proto_reg;
::kuaishou::reco::interest_explore::LongTermInterestItem long_term_interest_item_reg;
::kuaishou::reco::RecoLiveLeafPostCache reco_live_leaf_post_cache;
::kuaishou::reco::RecoLiveLeafPostCacheCollection reco_live_leaf_post_cache_collection;
::kuaishou::tdm::SimpleUserInfo simple_user_info_reg;
::kuaishou::userprofile::judge::GetGroupingRequest get_grouping_req;
::kuaishou::userprofile::judge::GetGroupingResponse get_grouping_resp;
::kuaishou::userprofile::judge::MultiGetGroupingRequest multi_get_grouping_req;
::kuaishou::userprofile::judge::MultiGetGroupingResponse multi_get_grouping_resp;
::ks::platform::KmeansClusterResult kmeans_cluster_result_reg;
::ks::platform::CommonKvRequest common_kv_result_reg;
::ks::platform::CommonKvResponse common_kv_response_reg;
::ks::entity_tag::EntityTagValueRequest entity_tag_value_request_reg;
::ks::entity_tag::EntityTagValueResponse entity_tag_value_response_reg;
::kuaishou::livestream::LivestreamGiftRequest live_stream_gift_req;
::kuaishou::livestream::LivestreamGiftResponse live_stream_gift_resp;
::ks::search::UserQueryClickMessage search_user_click_message;
::kuaishou::rta::KsHiveTableRecoNewRtaRefluxDidPxtrDiRtaDidPxtr rta_did_pxtr;
::kuaishou::rta::reco::RTARecoLeafContext rta_reco_leaf_context;
::kuaishou::rtb::DeviceBidProfile device_bid_profile;
::kuaishou::rtb::RtbLeafAttr rtb_leaf_attr;
::kuaishou::rtb::RtbLeafItemAttr rtb_leaf_item_attr;
::kuaishou::rtb::RtbRefluxDeviceProfile rtb_reflux_device_profile;
::kuaishou::rtb::matrix::BatchQueryDeviceInstallInfoRequestV1 batch_query_device_install_info_req_v1;
::kuaishou::rtb::matrix::BatchQueryDeviceInstallInfoResponseV1 batch_query_device_install_info_resp_v1;
::kuaishou::rtb::matrix::ChannelInstallStatus channel_install_status;
::kuaishou::rtb::matrix::DeviceInstallInfoResult device_install_info_result;
::kuaishou::rtb::matrix::QueryDeviceInstallInfoRequest install_info_req;
::kuaishou::rtb::matrix::QueryDeviceInstallInfoRequestV1 install_info_req_v1;
::kuaishou::rtb::matrix::QueryDeviceInstallInfoResponse install_info_resp;
::kuaishou::rtb::matrix::BatchQueryDeviceInstallInfoRequest batch_install_info_req;
::kuaishou::rtb::matrix::BatchQueryDeviceInstallInfoResponse batch_install_info_resp;
::kuaishou::rtb2::Creative rtb_new_creative_resp;
::kuaishou::rtb2::RemoteForwardMessage rtb_new_remote_forward_msg_resp;
::kuaishou::zt::music::GetUserFavoriteMusicsByCursorRequest user_favorite_music_req;
::kuaishou::zt::music::GetUserFavoriteMusicsByCursorResponse user_favorite_music_resp;
::kuaishou::zt::music::GetUserFavoriteMusicsWithoutEncryptByCursorResponse user_favorite_music_without_encrypt_resp;
::kuaishou::zt::music::ZtMusicIdWithFavoriteTime music_id_with_favorite_time;
::kuaishou::zt::music::MusicFavoriteTimeAndValidDTO music_id_with_favorite_valid_time;
::ks::reco::FullLinkSample full_link_sample;
::ks::reco::FullLinkRequest full_link_request;
::ks::reco::FullLinkSession full_link_session;
::ks::mmulac::PredictRequest mmulac_predict_req;
::ks::mmulac::PredictResult mmulac_predict_resp;
::ks::mmulac::BatchPredictRequest mmulac_batchpredict_req;
::ks::mmulac::BatchPredictResult mmulac_batchpredict_resp;
::ks::reco::live_pk::RecoLiveStreamPKDuration reco_live_stream_pk_duration_reg;
::ks::reco::RLSample rl_sample;
::ks::reco::RLRequest rl_request;
::ks::reco::RLSession rl_session;
::ks::reco::PhotoPxtrAttrs photo_pxtr_attrs;
::ks::reco::SocialSampleData social_sample_data_reg;
::ks::reco::SocialSampleUserInfo social_sample_user_info_reg;
::ks::reco::SocialSampleItemInfo social_sample_item_info_reg;
::kuaishou::reco::big_river::StoreData store_data_reg;
::kuaishou::reco::big_river::ReferenceUnit reference_unit_reg;
::kuaishou::reco::big_river::ListOfBytes list_of_bytes_reg;
::kuaishou::reco::big_river::RecoLiveLabelConfs reco_live_label_confs_reg;
::kuaishou::kaiworks::stream::BatchRow batch_row_reg;
::puji::profile::PujiUserProfile puji_user_profile;
::puji::profile::PujiActionInfo puji_action_info;
::ksib::reco::PhotoProfile photo_profile_reg;
::kuaishou::reco::merchant::ItemEmbeddingGuessLikeRequest itemEmbeddingGuessLikeRequest;
::kuaishou::reco::merchant::ItemEmbeddingGuessLikeResponse itemEmbeddingGuessLikeResponse;
::ks::reco::QueryBrowseSetRequest query_browse_set_req;
::ks::reco::QueryBrowseSetResponse query_browse_set_resp;
::mix::kuaishou::newsmodel::MixRankShowLogHiveSchema mix_rank_show_log_hive_schema;
::ks::merchant::profile::MerchantUserProfileRequest merchant_user_profile_req;
::kuaishou::relation::GetPersonListRequest follow_list_req;
::kuaishou::reco::merchant::QueryAvailableCouponVouchersRequest query_availble_coupon_voucher_request;
::kuaishou::reco::merchant::QueryAvailableCouponVouchersResponse query_availble_coupon_voucher_response;
::kuaishou::reco::merchant::address::AddressListRequest address_list_request;
::kuaishou::reco::merchant::address::AddressListResponse address_list_response;
::kuaishou::reco::merchant::address::DivisionRequest address_division_request;
::kuaishou::reco::merchant::address::DivisionResponse address_division_response;
::kuaishou::kwaishop::product::shelf::center::SearchShelfIndexForRecoRequest search_shelf_index_for_reco_request;
::kuaishou::kwaishop::product::shelf::center::SearchShelfIndexForRecoResponse search_shelf_index_for_reco_response;
::kuaishou::kwaishop::product::shelf::center::CountShelfIndexForRecoResponse count_shelf_index_for_reco_response;
}  // namespace
}  // namespace protobuf_reg
}  // namespace platform
}  // namespace ks
