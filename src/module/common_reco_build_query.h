#pragma once

#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/strings/string_number_conversions.h"
#include "base/strings/string_printf.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "folly/container/F14Map.h"
#include "ks/common_reco/index/flatten_query.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "serving_base/jansson/json.h"
#include "serving_base/util/duplicate_hash.h"
#include "teams/reco-arch/colossusdb/common/field_replace_conf.h"

namespace ks {
namespace platform {

/************************************************************
 *  索引生成器, 读取配置生成特定数目的索引
 ************************************************************
  "query_json": {
    "querys": [
      {
        "query": "channel:{{video_list}} AND cluster:{{1-6}}",
        "random_search": 0
      },
      {
        "query": "similar:{{click_music_channel_list}}",
        "random_search": 0
      },
    ],
    "default_search_num": 10,
    "default_random_search": 1
  }
************************************************************/

/**
 * 实现单个 query 的解析，存储目前只支持 common_attr
 */
class IndexQueryTemplate {
 public:
  IndexQueryTemplate() {}
  ~IndexQueryTemplate() {}
  IndexQueryTemplate(const base::Json *json, const ReadableRecoContextInterface *context,
                     int default_search_num, bool default_random_search, int default_expire_second,
                     const colossusdb::FieldReplaceKconfHolder *field_replace_kconf_holder = nullptr);

  IndexQueryTemplate(const IndexQueryTemplate &query);

 public:
  void GetBraceContent(const std::string &raw_string, const ReadableRecoContextInterface *context);
  void OutputQueryString(std::vector<std::string> *query_vecs) const;

 private:
  void InitNearbyQuery(const base::Json *json, const ReadableRecoContextInterface *context);
  bool AddString(absl::string_view str, std::vector<std::string> *strings);
  bool GetIdString(const std::vector<int> &ids, std::vector<std::string> *strings);
  // 尝试从 common_attr 里取值，返回结果表示是否在其中找到该 attr_name
  bool TryGetAttrString(const ReadableRecoContextInterface *context, const std::string &attr_name,
                        std::vector<std::string> *strings);
  //以 row 和 col 起始的所有 query: row 是{{}}的编号，col 是{{}}里面的元素
  //(真实值) 编号
  void Output(int row, int col, std::unordered_set<std::string> *query_strings,
              std::vector<std::string> *attrs, int size, std::vector<std::string> *query_vecs) const;
  void GenNearbySuffix(std::vector<std::string> *query_vecs) const;

 public:
  std::string raw_string_;                // query 字符串
  std::vector<std::string> attr_string_;  // {{ }}中间的字符串
  // 一维按照 {{ }}的 order, 二维为每个 {{ }} 中对应的 value query attr
  std::vector<std::vector<std::string>> attrs_;
  bool random_search_ = true;  // 索引查找方式
  int search_num_ = 0;         // 索引查找数目
  int expire_second_ = 200;    // 索引请求超时时间
  int max_attr_num_ = 10000;   // query max_attr_num

  std::vector<double> unit_values_;
  double lat_value_ = 0;
  double lon_value_ = 0;
  int nearby_terms_ = 0;  // 选定一个还是四个格子或者九宫格；
};

/*实现querys的解析，存储，输出接口为CommonFlattenRequest 格式*/
class IndexQueryGenerator {
 public:
  IndexQueryGenerator() {}
  std::vector<std::pair<bool, int>> BuildQuery(
      const base::Json *json, const ReadableRecoContextInterface *context, QueryBuilderBase *request_builder,
      folly::F14FastMap<int, int> *query_number_to_reason_index, uint64 additional_total_number = 0,
      const colossusdb::FieldReplaceKconfHolder *field_replace_kconf_holder = nullptr);

  ~IndexQueryGenerator() {}

 private:
  // 获取目标 query, 放入 request_builder 中
  std::vector<std::pair<bool, int>> GenerateQuery(const std::vector<IndexQueryTemplate> &query_templates,
                                                  QueryBuilderBase *request_builder,
                                                  folly::F14FastMap<int, int> *query_number_to_reason_index);
  DISALLOW_COPY_AND_ASSIGN(IndexQueryGenerator);
};

}  // namespace platform
}  // namespace ks
