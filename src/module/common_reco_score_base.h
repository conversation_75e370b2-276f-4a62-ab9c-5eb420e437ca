#pragma once

#include <algorithm>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/time/timestamp.h"
#include "ks/base/abtest/session_context.h"
#include "ks/base/abtest/session_context_factory.h"
#include "dragon/src/core/common_reco_shared_gflags.h"
#include "dragon/src/module/common_reco_dynamic_param_evaluator.h"
#include "dragon/src/core/common_reco_util.h"
#include "serving_base/retrieval/score_calculator.h"
#include "serving_base/util/dynamic_config.h"

namespace ks {
namespace platform {

// 打分 Context
struct FormulaEvalContext {
  // NOTE(fangjianbing): 写死这些 key name，
  // 用户几乎不会有改动的需求，给配置文件减负
  const std::string parameter_key = "parameter";
  const std::string formula_key = "formula";
  const std::string dynamic_parameter_key = "dynamic_parameter";

  std::string score_key;
  base::Json *parameter = nullptr;
  base::Json *formula = nullptr;
  // 中台使用的通用动态配置，区别于原来在程序中写死的动态配置,
  // 如果没有配置，则不走这段逻辑
  base::Json *dynamic_parameter = nullptr;

  MutableRecoContextInterface *reco_context = nullptr;
  bool save_for_debug_request_only = true;
  std::string save_score_to_attr = "";
  bool save_formula_score = false;
  std::string save_formula_score_prefix = "";
};

//  score calc 实现
class ScoreCalcHandler {
 public:
  ScoreCalcHandler() {}
  ~ScoreCalcHandler() {
    delete score_calculator_;
  }

  void InitContext(base::Json *parameter_config, base::Json *dynamic_parameter_config,
                   base::Json *formula_config, const std::string &score_key, bool save_for_debug_request_only,
                   const std::string &save_score_to_attr, bool save_formula_score,
                   const std::string &save_formula_score_prefix) {
    context_.parameter = parameter_config;
    context_.dynamic_parameter = dynamic_parameter_config;
    context_.formula = formula_config;
    context_.score_key = score_key;
    context_.save_for_debug_request_only = save_for_debug_request_only;
    context_.save_score_to_attr = save_score_to_attr;
    context_.save_formula_score = save_formula_score;
    context_.save_formula_score_prefix = save_formula_score_prefix;
  }

  void ResetRecoContext(MutableRecoContextInterface *context) {
    context_.reco_context = context;
  }

  // Item Level 级别的计算
  template <typename RecoResultIter>
  void CalcScoreForItems(RecoResultIter begin, RecoResultIter end) {
    const std::string &score_key = context_.score_key;
    const std::string &formula_key = context_.formula_key;
    const std::string &parameter_key = context_.parameter_key;
    const std::string &dynamic_parameter_key = context_.dynamic_parameter_key;
    auto *parameter = context_.parameter;
    auto *formula = context_.formula;
    auto *dynamic_parameter = context_.dynamic_parameter;

    if (!parameter) {
      CL_LOG_ERROR("score_calculator", "missing_parameter")
          << "parameter not found: " << parameter_key
          << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      return;
    }

    if (!formula) {
      CL_LOG_ERROR("score_calculator", "missing_formula")
          << "formula not found: " << formula_key << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      return;
    }

    auto *score_calculator = new base::ScoreCalculator(score_key, parameter, formula);
    if (!score_calculator->valid()) {
      CL_LOG_ERROR("score_calculator", "invalid_formula")
          << "score_calculator error"
          << ", formula: " << formula_key << ", parameter: " << parameter_key << ", score_key: " << score_key
          << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      delete score_calculator;
      return;
    }

    VLOG(100) << "dynamic_parameter is: " << dynamic_parameter;
    if (dynamic_parameter) {
      if (!ProcessDynamicParams(score_calculator, dynamic_parameter)) {
        CL_LOG_ERROR("score_calculator", "invalid_dynamic_parameter")
            << "score_calculator error, set request dynamic params"
            << ", formula: " << formula_key << ", parameter: " << parameter_key
            << ", dynamic_parameter: " << dynamic_parameter_key << ", item_key:" << score_key
            << RecoUtil::GetRequestInfoForLog(context_.reco_context);
        delete score_calculator;
        return;
      }
    } else {
      CL_LOG_ERROR("score_calculator", "null_dynamic_parameter")
          << "dynamic_parameter is null." << RecoUtil::GetRequestInfoForLog(context_.reco_context);
    }

    delete score_calculator_;
    score_calculator_ = score_calculator;

    bool could_save_scores = !context_.save_for_debug_request_only || context_.reco_context->IsDebugRequest();

    std::unordered_map<std::string, double> intermediate_scores;
    if (could_save_scores && context_.save_formula_score && formula) {
      for (auto it = formula->object_begin(); it != formula->object_end(); ++it) {
        intermediate_scores.insert(std::make_pair(it->first, 0.0));
      }
    }

    bool need_save_score = !context_.save_score_to_attr.empty();
    for (auto it = begin; it != end; ++it) {
      uint64 item_key = it->item_key;
      double score = -1000000.0;  // 避免引入负数打分
      if (context_.save_formula_score) {
        GetScoreWithRankingResult(item_key, &score, &intermediate_scores);
        for (auto &pr : intermediate_scores) {
          context_.reco_context->SetDoubleItemAttr(item_key, context_.save_formula_score_prefix + pr.first,
                                                   pr.second);
        }
      } else {
        GetScoreWithRankingResult(item_key, &score);
      }
      it->score = score;

      if (could_save_scores && need_save_score) {
        context_.reco_context->SetDoubleItemAttr(item_key, context_.save_score_to_attr, score);
      }
    }
  }

  /**
   * NOTE(wanggang):
   * 为中台架构提供的通用动态参数获取功能，填充请求级别动态参数到dynamic_parameter_中，
   *   为了不影响原有逻辑，增加一个函数，在创建 score Calculator 之后
   */
  /**
   * 大致逻辑：
   * 1. 生成 item 级别的动态参数
   * 2. 调用 score_calculator_ 计算得分
   */
  bool GetScoreWithRankingResult(uint64 item_key, double *score,
                                 std::unordered_map<std::string, double> *intermediate_scores = nullptr) {
    if (!score_calculator_) {
      VLOG(100) << "score_calculator is nullptr!";
      return false;
    }

    // set item level param, return false if Failed
    if (!CommonRecoDynamicParamEvaluator::EvaluateItemLevelParam(
            item_key, context_.reco_context, &dynamic_parameter_holder_, &dynamic_parameter_)) {
      VLOG(100) << "EvaluateItemLevelParam failed!";
      return false;
    }

    std::string info;
    if (!score_calculator_->GetValueWithIntermediateScores(dynamic_parameter_, score, intermediate_scores,
                                                           enable_debug_info_printing_ ? &info : nullptr)) {
      if (!enable_debug_info_printing_) {
        score_calculator_->GetValue(dynamic_parameter_, score, &info);
      }
      CL_LOG_ERROR_EVERY("score_calculator", "missing_intermediate_scores", 100)
          << "item_key: " << item_key << ", score_calculator error: " << info
          << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      return false;
    }

    if (enable_debug_info_printing_) {
      CL_LOG(INFO) << "item_key:" << item_key << ", score_calculator info: " << info
                   << RecoUtil::GetRequestInfoForLog(context_.reco_context);
    }

    VLOG(70) << "item_key: " << item_key << ", score: " << *score;
    return true;
  }

  /**
   * 大致逻辑：
   * 1. 遍历 ScoreCalculator 二叉树，拿到所有的类型为
   * OperatorType::kParameterDynamic 参数，解析参数
   * 2. 将参数分成两类存储，request 级别 (user,ab) 和 item 级别 (item, pred),
   *   2.1 request 级别（每次请求计算一次），直接求值，并填入
   * dynamic_parameter_
   *   2.2 item 级别（每个 item 计算一次），不求值，仅放入参数 dict
   */
  bool ProcessDynamicParams(const base::ScoreCalculator *score_calculator, const Json *parameters) {
    // 参数检查，运行时，尽量不要 CHECK
    if (!score_calculator || !parameters) {
      CL_LOG_ERROR_EVERY("score_calculator", "null_param", kLogInterval)
          << "Required Param is nullptr, score_calculator: " << score_calculator
          << ", parameters: " << parameters << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      return false;
    }

    if (!SetEnvironmentAttr()) {
      CL_LOG(WARNING) << "Set Environment Attr failed!"
                      << RecoUtil::GetRequestInfoForLog(context_.reco_context);
      return false;
    }

    const auto *reco_context = context_.reco_context;
    const ks::SessionContext *session_context =
        GetThreadSessionContext(reco_context->GetUserId(), reco_context->GetDeviceId());
    // 增加千分世界
    const ks::SessionContext *micro_session_context = nullptr;
    if (FLAGS_use_micro_world && (FLAGS_abtestbiz_seq_num >= 0)) {
      micro_session_context =
          ks::SessionContextFactory::Create(static_cast<ks::AbtestBiz>(FLAGS_abtestbiz_seq_num),
                                            reco_context->GetUserId(), reco_context->GetDeviceId());
    }

    CommonRecoDynamicParamEvaluator::RequestContext request_context(context_.reco_context, session_context,
                                                                    micro_session_context);
    bool ret = true;
    ret = CommonRecoDynamicParamEvaluator::ProcessDynamicParams(
        request_context, &dynamic_parameter_holder_, &dynamic_parameter_, parameters, score_calculator);
    if (micro_session_context) {
      delete micro_session_context;
      micro_session_context = nullptr;
    }
    return ret;
  }

  void EnableDebugInfo(bool flag) {
    enable_debug_info_printing_ = flag;
  }

 private:
  bool SetEnvironmentAttr() {
    static const std::string kEnvCurrentTimeMs = "ENV_current_time_ms";
    int64 curr_time_ms = base::GetTimestamp() / base::Time::kMicrosecondsPerMillisecond;
    dynamic_parameter_.Set(kEnvCurrentTimeMs, curr_time_ms);
    return true;
  }

 private:
  base::DynamicParameter dynamic_parameter_;
  base::ScoreDynamicParameterHandler dynamic_parameter_holder_;
  Json *override_parameter_ = nullptr;
  base::ScoreCalculator *score_calculator_ = nullptr;

  FormulaEvalContext context_;
  const int32 kLogInterval = 100000;
  base::Json *config_ = nullptr;
  bool enable_debug_info_printing_ = false;

  DISALLOW_COPY_AND_ASSIGN(ScoreCalcHandler);
};
}  // namespace platform
}  // namespace ks
