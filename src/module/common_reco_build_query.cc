#include "dragon/src/module/common_reco_build_query.h"

#include <algorithm>
#include <set>
#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/logging_util.h"

namespace ks {
namespace platform {
// 根据字符串 raw_string , 解析其中 {{ }} 内容: attr_string_ 存放花括号中 attr,
// attrs_: attr 对应的真实值
void IndexQueryTemplate::GetBraceContent(const std::string &raw_string,
                                         const ReadableRecoContextInterface *context) {
  attrs_.clear();
  attr_string_.clear();
  raw_string_ = raw_string;
  int begin_query_pos = 0, end_query_pos = 0;

  std::string sub_raw_string = raw_string;

  // 增加单个索引串
  if (((begin_query_pos = sub_raw_string.find("{{")) == std::string::npos) &&
      ((end_query_pos = sub_raw_string.find("}}")) == std::string::npos)) {
    std::vector<std::string> attrs;
    attrs.push_back(raw_string_);
    attrs_.push_back(attrs);
    attr_string_.push_back(raw_string_);
    return;
  }

  std::vector<std::string> attr_keys;
  std::vector<std::string> attrs;
  std::vector<std::string> string_nums;
  std::vector<int> int_nums;
  std::vector<int> ids;
  while (!sub_raw_string.empty()) {
    // 遍历取 {{ }} 中间的部分
    if ((begin_query_pos = sub_raw_string.find("{{")) == std::string::npos) return;
    if ((end_query_pos = sub_raw_string.find("}}")) == std::string::npos) return;

    std::string attr_string = sub_raw_string.substr(begin_query_pos + 2, end_query_pos - begin_query_pos - 2);
    sub_raw_string = sub_raw_string.substr(end_query_pos + 2, sub_raw_string.length() - end_query_pos - 2);
    attr_string_.push_back(attr_string);

    attr_keys.clear();
    base::SplitString(attr_string, ",", &attr_keys);
    attrs.clear();
    for (std::string &attr_key : attr_keys) {
      // attr list
      base::TrimWhitespaces(&attr_key);
      if (!TryGetAttrString(context, attr_key, &attrs)) {
        // id
        string_nums.clear();
        int_nums.clear();
        ids.clear();
        base::SplitString(attr_key, "~", &string_nums);
        int num = 0;
        for (const std::string &str_num : string_nums) {
          if (!base::StringToInt(str_num, &num)) {
            // 很多情况下这里会是因为 common attr 缺失而失败, 改为 VLOG
            VLOG(100) << "failed to parse query string: " << attr_key << ", str_num:" << str_num
                      << ", you may lose some retrieval data!" << RecoUtil::GetRequestInfoForLog(context);
            break;
          }
          int_nums.push_back(num);
        }
        if (!int_nums.empty()) {
          for (int i = int_nums[0]; i <= int_nums[int_nums.size() - 1]; ++i) {
            ids.push_back(i);
          }
          if (!GetIdString(ids, &attrs)) break;
        }
      }
    }
    attrs_.push_back(attrs);
  }
}

/**
 * 这个类的作用是，把 每个 query 所有 {{}}
 * 中间配置的属性列表中的每个属性解析出真实值，
 * 并根据是属性还是 id 类型，分别存储到 attrs_ 中,
 * 解析后的数据，会在 IndexQueryGenerator.GenerateQuery() 中用与输出到 request
 * 中
 */
IndexQueryTemplate::IndexQueryTemplate(
    const base::Json *json, const ReadableRecoContextInterface *context, int default_search_num,
    bool default_random_search, int default_expire_second,
    const colossusdb::FieldReplaceKconfHolder *field_replace_kconf_holder) {
  search_num_ = RecoUtil::GetDynamicIntParamFromContext(context, json->Get("search_num"), default_search_num);
  random_search_ = (bool)RecoUtil::GetDynamicIntParamFromContext(context, json->Get("random_search"),
                                                                 (int64)default_random_search);
  expire_second_ =
      RecoUtil::GetDynamicIntParamFromContext(context, json->Get("expire_second"), default_expire_second);
  max_attr_num_ = RecoUtil::GetDynamicIntParamFromContext(context, json->Get("max_attr_num"), -1);
  if (max_attr_num_ < 0) {
    // 默认最多限制 10000
    max_attr_num_ = 10000;
  }

  if (json->Get("lat") && json->Get("lon") && json->Get("unit_size")) {
    InitNearbyQuery(json, context);
  } else {
    raw_string_ = json->GetString("query", "");
    std::string replaced;
    if (field_replace_kconf_holder &&
        field_replace_kconf_holder->ReplaceInvertedQuery(raw_string_, &replaced)) {
      raw_string_ = replaced;
    }
    GetBraceContent(raw_string_, context);
  }
}

IndexQueryTemplate::IndexQueryTemplate(const IndexQueryTemplate &query) {
  this->raw_string_ = query.raw_string_;
  this->attr_string_ = query.attr_string_;
  this->attrs_ = query.attrs_;
  this->random_search_ = query.random_search_;
  this->search_num_ = query.search_num_;
  this->expire_second_ = query.expire_second_;
  this->max_attr_num_ = query.max_attr_num_;

  this->lon_value_ = query.lon_value_;
  this->lat_value_ = query.lat_value_;
  this->nearby_terms_ = query.nearby_terms_;
  this->unit_values_ = query.unit_values_;
}

void IndexQueryTemplate::InitNearbyQuery(const base::Json *json,
                                         const ReadableRecoContextInterface *context) {
  nearby_terms_ = json->GetInt("nearby_terms", 1);
  auto lat = context->GetDoubleCommonAttr(json->GetString("lat", ""));
  if (lat) {
    lat_value_ = *lat;
  }
  auto lon = context->GetDoubleCommonAttr(json->GetString("lon", ""));
  if (lon) {
    lon_value_ = *lon;
  }
  auto *unit_json = json->Get("unit_size");
  if (unit_json->IsString()) {
    auto unit = context->GetDoubleCommonAttr(unit_json->StringValue(""));
    if (unit) {
      unit_values_.emplace_back(*unit);
    }
    auto units = context->GetDoubleListCommonAttr(unit_json->StringValue(""));
    if (units) {
      int count = std::min((int)units->size(), max_attr_num_);
      unit_values_.resize(count);
      std::copy(units->begin(), units->end(), unit_values_.begin());
      if (count < units->size()) {
        CL_LOG_ERROR("build_query", "too_many_units")
            << "unable to append unit value from context, current size: " << units->size()
            << ", max_attr_num: " << max_attr_num_ << RecoUtil::GetRequestInfoForLog(context);
      }
    }
  } else if (unit_json->IsDouble() || unit_json->IsInteger()) {
    unit_values_.emplace_back(unit_json->NumberValue(0.0));
  } else if (unit_json->IsArray()) {
    for (const auto &unit : unit_json->array()) {
      if (unit_values_.size() >= max_attr_num_) {
        CL_LOG_ERROR("build_query", "too_many_units")
            << "unable to append unit value from json, current size: " << unit_json->size()
            << ", max_attr_num: " << max_attr_num_ << RecoUtil::GetRequestInfoForLog(context);
        break;
      }
      if (unit->IsDouble() || unit->IsInteger()) {
        unit_values_.emplace_back(unit->NumberValue(0.0));
      }
    }
  }
  if (unit_values_.empty()) {
    unit_values_.emplace_back(8.0);
    CL_LOG_ERROR("build_query", "unit_size_invalid")
        << "no valid unit_size defined, it should be a string or a number or an array of number";
  }

  std::string default_prefix = "GPS_P_";
  raw_string_ = json->GetString("prefix", default_prefix);
  if (raw_string_.find(":") != std::string::npos) {
    CL_LOG_ERROR("build_query", "invalid_prefix: " + raw_string_)
        << "prefix should not contain \":\", using default prefix:" << default_prefix;
    raw_string_ = default_prefix;
  }
  GetBraceContent(raw_string_, context);
}

bool IndexQueryTemplate::AddString(absl::string_view str, std::vector<std::string> *strings) {
  if (!strings || (strings->size() >= max_attr_num_) || str.empty()) return false;
  strings->emplace_back(str.data(), str.size());
  return true;
}

bool IndexQueryTemplate::GetIdString(const std::vector<int> &ids, std::vector<std::string> *strings) {
  if (!strings) return false;
  for (const int id : ids) {
    if (!AddString(base::IntToString(id), strings)) return false;
  }
  return true;
}

bool IndexQueryTemplate::TryGetAttrString(const ReadableRecoContextInterface *context,
                                          const std::string &attr_name, std::vector<std::string> *strings) {
  if (attr_name.empty() || !strings) return true;

  // NOTE(fangjianbing): 按 int_list, string_list, int, string 的顺序尝试类型
  if (auto p = context->GetIntListCommonAttr(attr_name)) {
    for (auto value : *p) {
      if (!AddString(base::Int64ToString(value), strings)) {
        break;
      }
    }
    return true;
  }

  if (auto p = context->GetStringListCommonAttr(attr_name)) {
    for (auto v : *p) {
      if (!v.empty() && !AddString(v, strings)) {
        break;
      }
    }
    return true;
  }

  if (auto p = context->GetIntCommonAttr(attr_name)) {
    if (!AddString(base::Int64ToString(*p), strings)) {
      CL_LOG_ERROR_EVERY("build_query", "append_string_fail", 100)
          << "failed to append query string, current size: " << strings->size()
          << ", max_attr_num: " << max_attr_num_ << RecoUtil::GetRequestInfoForLog(context);
    }
    return true;
  }

  if (auto p = context->GetStringCommonAttr(attr_name)) {
    if (!p->empty() && !AddString(*p, strings)) {
      CL_LOG_ERROR_EVERY("build_query", "append_string_fail", 100)
          << "failed to append query string, current size: " << strings->size()
          << ", max_attr_num: " << max_attr_num_ << RecoUtil::GetRequestInfoForLog(context);
    }
    return true;
  }

  if (context->GetDoubleCommonAttr(attr_name) || context->GetDoubleListCommonAttr(attr_name)) {
    CL_LOG_ERROR_EVERY("build_query", "unsupported_double_attr:" + attr_name, 100)
        << "double/float and double/float list common "
        << "attr are not supported: " << attr_name << RecoUtil::GetRequestInfoForLog(context);
    return true;
  }

  return false;
}
// 一个 query template, 产生的所有 query string
void IndexQueryTemplate::OutputQueryString(std::vector<std::string> *query_vecs) const {
  int size = attr_string_.size();
  if (size <= 0) return;
  std::vector<std::string> attrs;
  std::unordered_set<std::string> query_strings;
  // 这里考虑到有些 attr_string 没有 value 的情况，这里的处理是不输出
  for (int i = 0; i < attrs_[0].size(); ++i) {
    Output(0, i, &query_strings, &attrs, size, query_vecs);
  }
  if (nearby_terms_ > 0) {
    GenNearbySuffix(query_vecs);
  }
}

void IndexQueryTemplate::GenNearbySuffix(std::vector<std::string> *query_vecs) const {
  std::vector<std::string> suffixes;
  for (const auto &unit_size : unit_values_) {
    double km_per_dgree = 40000.0 / 360;
    double unit = unit_size / km_per_dgree;
    int x = lat_value_ / unit;
    int y = lon_value_ / unit;
    //根据四舍五入与向下取整的差值来控制取九宫格区域左上、左下及右上、右下四区域
    int dx = round(lat_value_ / unit) - x;
    int dy = round(lon_value_ / unit) - y;
    std::string prefix = base::IntToString(unit_size * 10) + ":";
    if (nearby_terms_ >= 1) {
      suffixes.push_back(prefix + base::IntToString(x) + "_" + base::IntToString(y));
    }
    if (nearby_terms_ >= 4) {
      suffixes.push_back(prefix + base::IntToString(x) + "_" + base::IntToString(y - 1 + 2 * dy));
      suffixes.push_back(prefix + base::IntToString(x - 1 + 2 * dx) + "_" + base::IntToString(y));
      suffixes.push_back(prefix + base::IntToString(x - 1 + 2 * dx) + "_" +
                         base::IntToString(y - 1 + 2 * dy));
    }
    if (nearby_terms_ == 9) {
      suffixes.push_back(prefix + base::IntToString(x) + "_" + base::IntToString(y + 1 - 2 * dy));
      suffixes.push_back(prefix + base::IntToString(x - 1 + 2 * dx) + "_" +
                         base::IntToString(y + 1 - 2 * dy));
      suffixes.push_back(prefix + base::IntToString(x + 1 - 2 * dx) + "_" + base::IntToString(y));
      suffixes.push_back(prefix + base::IntToString(x + 1 - 2 * dx) + "_" +
                         base::IntToString(y - 1 + 2 * dy));
      suffixes.push_back(prefix + base::IntToString(x + 1 - 2 * dx) + "_" +
                         base::IntToString(y + 1 - 2 * dy));
    }
  }
  if (!suffixes.empty()) {
    std::vector<std::string> queries;
    queries.reserve(query_vecs->size() * suffixes.size());
    for (const auto &prefix : *query_vecs) {
      for (const auto &suffix : suffixes) {
        queries.push_back(prefix + suffix);
      }
    }
    query_vecs->swap(queries);
  }
}

//以 row 和 col 起始的所有 query: row 是{{}}的编号，col 是{{}}里面的元素
//(真实值) 编号
void IndexQueryTemplate::Output(int row, int col, std::unordered_set<std::string> *query_strings,
                                std::vector<std::string> *attrs, int size,
                                std::vector<std::string> *query_vecs) const {
  attrs->push_back(attrs_[row][col]);
  if (row == size - 1) {
    // 进行替换
    std::string result_string = raw_string_;
    std::string tmp_query_string;
    std::string tmp_pre_query_string;
    int pos = 0;
    for (int i = 0; i < attr_string_.size(); ++i) {
      tmp_query_string = "{{" + attr_string_[i] + "}}";
      if ((pos = result_string.find(tmp_query_string)) != std::string::npos) {
        tmp_pre_query_string = result_string.substr(0, pos);
      }
      // 为了确定的替换，比如 view_count:{{1-3}} AND show_log: {{1-3}}
      result_string = base::StringReplace(result_string, tmp_pre_query_string + tmp_query_string,
                                          tmp_pre_query_string + attrs->at(i), false);
    }
    if (query_strings->find(result_string) == query_strings->end()) {
      query_strings->insert(result_string);
      query_vecs->push_back(result_string);
    }
    attrs->pop_back();
    return;
  }

  for (int i = 0; i < attrs_[row + 1].size(); ++i) {
    Output(row + 1, i, query_strings, attrs, size, query_vecs);
  }
  attrs->pop_back();
}

std::vector<std::pair<bool, int>> IndexQueryGenerator::BuildQuery(
    const base::Json *json, const ReadableRecoContextInterface *context, QueryBuilderBase *request_builder,
    folly::F14FastMap<int, int> *query_number_to_reason_index, uint64 additional_total_number,
    const colossusdb::FieldReplaceKconfHolder *field_replace_kconf_holder) {
  // 判空
  CHECK(json);
  CHECK(context);
  CHECK(request_builder);

  // number limit
  static const uint64 kMaxAdditionSize = 10000;
  if (additional_total_number > kMaxAdditionSize) {
    additional_total_number = kMaxAdditionSize;
  }

  // 解析 querys json
  auto querys = json->Get("querys");
  if (!querys) {
    CL_LOG_ERROR_EVERY("build_query", "no_query_config", 100)
        << "no query in config." << RecoUtil::GetRequestInfoForLog(context);
    return {};
  }
  // 解析 default_
  int default_search_num = json->GetInt("default_search_num", 0);
  int default_random_search = json->GetInt("default_random_search", 1);
  int default_expire_second = json->GetInt("default_expire_second", 0);

  // querys 写成数组形式
  std::vector<IndexQueryTemplate> query_templates;
  if (querys->IsArray()) {
    for (const auto query_json : querys->array()) {
      // IndexQueryTemplate
      query_templates.emplace_back(IndexQueryTemplate(query_json, context, default_search_num,
                                                      default_random_search, default_expire_second,
                                                      field_replace_kconf_holder));
    }
  }
  // 解析 total_request_num
  int default_total_request_num =
      RecoUtil::GetDynamicIntParamFromContext(context, json->Get("default_total_request_num"), (int64)0) +
      additional_total_number;
  request_builder->SetTotalRequestNum(default_total_request_num);
  // 生成 query ，放入 request_builder 中
  return GenerateQuery(query_templates, request_builder, query_number_to_reason_index);
}

// 获取目标 query, 放入 request_builder 中
std::vector<std::pair<bool, int>> IndexQueryGenerator::GenerateQuery(
    const std::vector<IndexQueryTemplate> &query_templates, QueryBuilderBase *request_builder,
    folly::F14FastMap<int, int> *query_number_to_reason_index) {
  int random_search = 1;    // 索引查找方式
  int search_num = 0;       // 索引查找数目
  int expire_second = 200;  // 索引请求超时时间

  std::vector<std::pair<bool, int>> ret_value;
  ret_value.reserve(query_templates.size());
  std::vector<std::string> attr_string_vec;
  int query_number = 0;
  for (int i = 0; i < query_templates.size(); ++i) {
    const auto &query_temp = query_templates[i];
    search_num = query_temp.search_num_;
    expire_second = query_temp.expire_second_;
    random_search = query_temp.random_search_;
    attr_string_vec.clear();
    query_temp.OutputQueryString(&attr_string_vec);

    if (VLOG_IS_ON(50)) {
      CL_LOG(INFO) << "attr_string_vec (" << attr_string_vec.size() << "):\n"
                   << base::JoinStrings(attr_string_vec, "\n");
    }

    if (search_num == 0) {
      ret_value.push_back(std::make_pair(false, attr_string_vec.size()));
    } else {
      ret_value.push_back(std::make_pair(true, attr_string_vec.size()));
      for (const auto &query : attr_string_vec) {
        request_builder->AddQuery(query, random_search, search_num, expire_second);
        if (query_number_to_reason_index) {
          (*query_number_to_reason_index)[query_number] = i;
        }
        ++query_number;
      }
    }
  }
  return ret_value;
}
}  // namespace platform
}  // namespace ks
