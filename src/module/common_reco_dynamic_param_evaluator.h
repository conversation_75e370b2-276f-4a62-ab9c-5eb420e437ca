#pragma once
#include <string>

#include "base/common/basic_types.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/core/common_reco_shared_gflags.h"
#include "ks/serving_util/ab_test_util.h"
#include "serving_base/retrieval/score_dynamic_parameter_handler.h"

DECLARE_bool(use_micro_world);

namespace ks {
namespace platform {

// 用于 score 公式计算中的动态参数配置：
/* 支持五种类型：
 *
 *   1. pred(user x item): 从 predict-server 中获取到的各种信息，如各种 pxtr
 *   2. item(item_info): 从正排中拿到的信息，例如 click_count
 *   3. user(user_info): 从 user_info 中拿到的信息（reader），例如：地理位置 等
 *   4. ab(abtest): 从 abtest 中获取的参数，与静态参数类似，但是 by user 配置，
 *   5. 数值: 默认参数类型
 *   6. kconf: 从 kconf 中获取的参数
 *   一个示例配置：
 *  '''
 *   "dynamic_parameter" : {
 *     # 基于 predict 结果的动态参数
 *     "pctr" : "pred/pctr,item/emp_ctr,0.0",
 *     "pltr" : "pred/pltr",
 *     "pwtr" : "pred,,0.01",
 *     # 基于 item 的动态参数
 *     "click_cnt" : "item/click",
 *     # 基于 abtest 的参数
 *     "click_decay_coeff" :"ab/click_decay",
 *     # 基于 user_info 的参数
 *     "click_cnt" : "user/click_count",
 *     # 基于 数值 的默认参数
 *     "click_cnt" : "10",
 *     # 基于 kconf 默认参数
 *     "pctr_rate" : "kconf/pctr_rate",
 *   },
 **/
/**
 * NOTE(fangjianbing): 以上是从旧架构代码迁移过来后保留的注释信息，新架构下
 * 强烈建议 pred/ 替换为 item/ 方式获取，ab/ 和 kconf/ 替换为 user/ 方式
 */
class CommonRecoDynamicParamEvaluator {
 public:
  // 请求相关的上下文，用于 user_info 的参数 和 ab 的动态参数计算
  struct RequestContext {
    RequestContext(const MutableRecoContextInterface *reco_context, const ks::SessionContext *session_context,
                   const ks::SessionContext *micro_session_context = nullptr)
        : reco_context_(reco_context)
        , session_context_(session_context)
        , micro_session_context_(micro_session_context) {}

    const MutableRecoContextInterface *reco_context_;
    const ks::SessionContext *session_context_;
    const ks::SessionContext *micro_session_context_;
  };

  // 解析 parameters
  /**
   * 支持两种形式：
   *  1. score_calculator != nullptr 遍历 score_calculator,
   * 解析其中使用到的动态参数
   *  2. score_calculator == nullptr 遍历 parameters 中的每一个参数，并解析
   */
  static bool ProcessDynamicParams(const RequestContext &request_context,
                                   base::ScoreDynamicParameterHandler *dynamic_parameter_holder,
                                   base::DynamicParameter *dynamic_parameter, const base::Json *parameters,
                                   const base::ScoreCalculator *score_calculator = nullptr);
  // 对请求级别参数解析
  static bool EvaluateAndFillRequestLevelParam(const RequestContext &request_context,
                                               base::ScoreDynamicParameterHandler *dynamic_parameter_holder,
                                               base::DynamicParameter *dynamic_parameter);

  // 对 Item 级别参数解析
  static bool EvaluateItemLevelParam(const uint64 item_key, const MutableRecoContextInterface *context,
                                     base::ScoreDynamicParameterHandler *dynamic_parameter_holder,
                                     base::DynamicParameter *dynamic_parameter);

  static bool EvaluateItemLevelParam(const uint64 item_key);

  // 请求级别的参数解析： user_info, abtest, 数值类型
  static bool EvaluateOneRequestLevelParam(const RequestContext &request_context,
                                           const base::internal::ScoreDynamicParam *one_param, double *value);

  static const ks::SessionContext *GetDefaultThreadSessionContext(uint64 user_id,
                                                                  const std::string &device_id) {
    return GetThreadSessionContext(user_id, device_id);
  }

  // 用于 Debug Item 级别的参数使用
  static bool DebugOneItemLevelParam(const uint64 item_key,
                                     const base::internal::ScoreDynamicParam *one_param,
                                     const MutableRecoContextInterface *context, double *value,
                                     std::string *str_value);

 private:
  // Item 级别的参数解析： pred, item 类型
  static bool EvaluateOneItemLevelParam(const uint64 item_key,
                                        const base::internal::ScoreDynamicParam *one_param,
                                        const MutableRecoContextInterface *context, double *value,
                                        std::string *str_value = nullptr);
  //    基于 user_info 的参数
  static bool CalculateUserSource(const RequestContext &request_context,
                                  const base::internal::ScoreDynamicParam::Source &one_source, double *value);
  //    基于 abtest 的参数
  static bool CalculateAbTestSource(const RequestContext &request_context,
                                    const base::internal::ScoreDynamicParam::Source &one_source,
                                    double *value);

  //    基于 item 的动态参数
  static bool CalculateItemSource(const base::internal::ScoreDynamicParam::Source &one_source,
                                  const MutableRecoContextInterface *context, uint64 item_key, double *value,
                                  std::string *str_value = nullptr);

  // 基于 Kconf 的参数
  static bool CalculateKconfSource(const RequestContext &request_context,
                                   const base::internal::ScoreDynamicParam::Source &one_source,
                                   double *value);

  //    获取 数值 参数
  static bool CalculateDefaultSource(const base::internal::ScoreDynamicParam::Source &one_source,
                                     double *value);

  // 释放内存
  static void FreeSessionContextMem(ks::SessionContext *ptr) {
    if (ptr) {
      delete ptr;
      ptr = nullptr;
    }
  }
};
}  // namespace platform
}  // namespace ks
