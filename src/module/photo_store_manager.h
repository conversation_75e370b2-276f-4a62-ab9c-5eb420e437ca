#pragma once

#include <memory>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "absl/strings/str_join.h"
#include "base/thread/internal/synchronization/lock.h"
#include "ks/common_reco/index/common_query_handler.h"
#include "ks/common_reco/index/index_manager.h"
#include "ks/photo_store/dynamic_photo_store.h"
#include "ks/photo_store/dynamic_photo_store_util.h"
#include "ks/photo_store/photo_store.h"
#include "ks/photo_store/photo_store_kconf_parser.h"
#include "ks/realtime_reco/index/common_def.h"
#include "ks/util/macros.h"

DECLARE_bool(use_distributed_index);
DECLARE_int32(retrieval_max_days);
DECLARE_int32(retrieval_old_showed_photo_forward_index_max_day);
DECLARE_string(photo_info_backup_redis_cluster);

namespace ks {
namespace photo_store {

static std::set<std::string> dump_paths;
static base::Lock config_lock;

class PhotoStore;
class DynamicPhotoStore;

}  // namespace photo_store
}  // namespace ks

namespace ks {
namespace platform {
namespace ps = ks::photo_store;


static ps::PhotoCache::Config GetPhotoCacheConfig(
    ks::photo_store::PhotoStoreKconfParser *photo_store_kconf_parser = nullptr) {
  auto config = ps::PhotoCache::Config();
  if (photo_store_kconf_parser) {
    config.config_kconf_path = photo_store_kconf_parser->photo_store_kconf_path;
  }
  config.capacity = (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocCacheSize())
                        ? photo_store_kconf_parser->item_doc_cache_size
                        : FLAGS_item_doc_cache_size;
  config.shard_num = (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocCacheShardNum())
                         ? photo_store_kconf_parser->item_doc_cache_shard_num
                         : FLAGS_item_doc_cache_shard_num;
  config.dump_file_name = (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocDumpFileName())
                              ? photo_store_kconf_parser->item_doc_dump_file_name
                              : FLAGS_item_doc_dump_file_name;
  config.dump_interval_sec =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocDumpIntervalSec())
          ? photo_store_kconf_parser->item_doc_dump_interval_sec
          : FLAGS_item_doc_dump_interval_sec;
  config.btq_real_time_queue_name =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocCacheRealTimeQueueName())
          ? photo_store_kconf_parser->item_doc_cache_realtime_queue_name
          : FLAGS_item_doc_cache_realtime_queue_name;
  bool item_doc_has_important_queue =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocHasImportantQueue())
          ? photo_store_kconf_parser->item_doc_has_important_queue
          : FLAGS_item_doc_has_important_queue;
  if (item_doc_has_important_queue) {
    config.btq_real_time_important_queue_name = config.btq_real_time_queue_name + "(important)";
  }
  // 部分配置可提供 kconf key 来动态更新
  config.dump_interval_sec_kconf_key =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocDumpIntervalSecKconfKey())
          ? photo_store_kconf_parser->item_doc_dump_interval_sec_kconf_key
          : FLAGS_item_doc_dump_interval_sec_kconf_key;
  config.dump_item_num_per_sec_kconf_key =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocDumpItemNumPerSecKconfKey())
          ? photo_store_kconf_parser->item_doc_dump_item_num_per_sec_kconf_key
          : FLAGS_item_doc_dump_item_num_per_sec_kconf_key;
  config.btq_real_time_max_delay_sec_kconf_key =
      (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocBtqMaxDelaySecKconfKey())
          ? photo_store_kconf_parser->item_doc_btq_max_delay_sec_kconf_key
          : FLAGS_item_doc_btq_max_delay_sec_kconf_key;
  config.field_mask = (photo_store_kconf_parser && photo_store_kconf_parser->FetchItemDocFieldMask())
                          ? photo_store_kconf_parser->item_doc_field_mask
                          : FLAGS_item_doc_field_mask;

  return config;
}

static ps::PhotoStore::Config GetPhotoStoreConfig(const std::string &kconf_key = "") {
  ks::photo_store::PhotoStoreKconfParser photo_store_kconf_parser;
  auto config = ps::PhotoStore::Config();
  if (!kconf_key.empty() && photo_store_kconf_parser.Init(kconf_key)) {
    config.service_name = photo_store_kconf_parser.FetchItemDocGrpcServiceName()
                              ? photo_store_kconf_parser.item_doc_grpc_service_name
                              : FLAGS_item_doc_grpc_service_name;
    config.event_loop_num = photo_store_kconf_parser.FetchItemDocGrpcIoThreadNum()
                                ? photo_store_kconf_parser.item_doc_grpc_io_thread_num
                                : FLAGS_item_doc_grpc_io_thread_num;
    config.grpc_timeout =
        photo_store_kconf_parser.FetchItemDocCacheGrpcTimeoutMs()
            ? std::chrono::milliseconds(photo_store_kconf_parser.item_doc_cache_grpc_timeout_ms)
            : std::chrono::milliseconds(FLAGS_item_doc_cache_grpc_timeout_ms);
    config.item_ttl = photo_store_kconf_parser.FetchItemDocCacheTtlSec()
                          ? std::chrono::milliseconds(photo_store_kconf_parser.item_doc_cache_ttl_sec * 1000)
                          : std::chrono::milliseconds(FLAGS_item_doc_cache_ttl_sec * 1000);
    config.max_async_refresh_ops = photo_store_kconf_parser.FetchItemDocMaxAsyncRefreshOps()
                                       ? photo_store_kconf_parser.item_doc_max_async_refresh_ops
                                       : FLAGS_item_doc_max_async_refresh_ops;
    config.go_cache_rate_key = photo_store_kconf_parser.FetchItemDocGoCacheRateKey()
                                   ? photo_store_kconf_parser.item_doc_go_cache_rate_key
                                   : FLAGS_item_doc_go_cache_rate_key;
    config.backup_redis_cluster = photo_store_kconf_parser.FetchPhotoInfoBackupRedisCluster()
                                      ? photo_store_kconf_parser.photo_info_backup_redis_cluster
                                      : FLAGS_photo_info_backup_redis_cluster;
    config.backup_redis_max_req_len_key =
        photo_store_kconf_parser.FetchItemDocBackupRedisMaxReqLenKconfKey()
            ? photo_store_kconf_parser.item_doc_backup_redis_max_req_len_kconf_key
            : FLAGS_item_doc_backup_redis_max_req_len_kconf_key;
    config.grpc_max_req_len_key = photo_store_kconf_parser.FetchItemDocGrpcMaxReqLenKconfKey()
                                      ? photo_store_kconf_parser.item_doc_grpc_max_req_len_kconf_key
                                      : FLAGS_item_doc_grpc_max_req_len_kconf_key;
    config.cache_config = GetPhotoCacheConfig(&photo_store_kconf_parser);
  } else {
    config.service_name = FLAGS_item_doc_grpc_service_name;
    config.event_loop_num = FLAGS_item_doc_grpc_io_thread_num;
    config.grpc_timeout = std::chrono::milliseconds(FLAGS_item_doc_cache_grpc_timeout_ms);
    config.item_ttl = std::chrono::milliseconds(FLAGS_item_doc_cache_ttl_sec * 1000);
    config.max_async_refresh_ops = FLAGS_item_doc_max_async_refresh_ops;
    config.go_cache_rate_key = FLAGS_item_doc_go_cache_rate_key;
    config.backup_redis_cluster = FLAGS_photo_info_backup_redis_cluster;
    config.backup_redis_max_req_len_key = FLAGS_item_doc_backup_redis_max_req_len_kconf_key;
    config.grpc_max_req_len_key = FLAGS_item_doc_grpc_max_req_len_kconf_key;
    config.cache_config = GetPhotoCacheConfig();
  }
  return config;
}

template<class PSConfig>
static void CheckDumpFileName(PSConfig *config) {
  base::AutoLock auto_lock(ps::config_lock);
  if (ps::dump_paths.find(config->cache_config.dump_file_name) == ps::dump_paths.end()) {
    ps::dump_paths.insert(config->cache_config.dump_file_name);
  } else if (!config->cache_config.dump_file_name.empty()) {
    LOG(ERROR) << " Deduplicate dump_file_name " << config->cache_config.dump_file_name;
    config->cache_config.dump_file_name = "";
  }
}

template <typename ItemFactory>
class PhotoStoreManager {
 public:
  SINGLETON(PhotoStoreManager);

  ~PhotoStoreManager() {}

  void Stop() {
    base::AutoLock auto_lock(photo_store_lock_);
    for (auto &pair : photo_store_map_) {
      if (pair.second) {
        pair.second->Stop();
        LOG(INFO) << "PhotoStore stopped, kconf_key = " << pair.first;
      }
    }
    photo_store_map_.clear();
    ::google::FlushLogFiles(::google::INFO);
  }

  ks::photo_store::PhotoStore *GetPhotoStore(const std::string &kconf_key = "",
                                             std::shared_ptr<ItemFactory> factory_ptr = nullptr) {
    base::AutoLock auto_lock(photo_store_lock_);
    // NOTE(XXX): 下面逻辑保证对每一个 kconf_key 只会创建一个 photo_store。
    // 若没有配置 kconf_key，则 photo_store 默认是根据 flag 配置值创建的。
    auto it = photo_store_map_.find(kconf_key);
    if (it == photo_store_map_.end()) {
      LOG(INFO) << "Init photo store singleton, key: " << kconf_key;

      auto config = ks::platform::GetPhotoStoreConfig(kconf_key);
      CheckDumpFileName(&config);
      auto photo_store = std::make_unique<ps::PhotoStore>(
          config, factory_ptr ? factory_ptr : std::make_shared<ItemFactory>());
      auto photo_store_ptr = photo_store.get();
      photo_store_map_.insert(std::make_pair(kconf_key, std::move(photo_store)));
      return photo_store_ptr;
    } else {
      return it->second.get();
    }
  }

 private:
  PhotoStoreManager() {}

 private:
  static base::Lock photo_store_lock_;
  folly::F14FastMap<std::string, std::unique_ptr<ks::photo_store::PhotoStore>> photo_store_map_;

  DISALLOW_COPY_AND_ASSIGN(PhotoStoreManager);
};

template <typename DynamicItemFactory>
class DynamicPhotoStoreManager {
 public:
  SINGLETON(DynamicPhotoStoreManager);

  ~DynamicPhotoStoreManager() {}

  void Stop() {
    base::AutoLock auto_lock(dynamic_photo_store_lock_);
    for (auto &pair : dynamic_photo_store_map_) {
      if (pair.second) {
        pair.second->Stop();
        LOG(INFO) << "DynamicPhotoStore stopped, kconf_key = " << pair.first;
      }
    }
    dynamic_photo_store_map_.clear();
    ::google::FlushLogFiles(::google::INFO);
  }

  ks::photo_store::DynamicPhotoStore *GetDynamicPhotoStore(
      const std::string &kconf_key = "", std::shared_ptr<DynamicItemFactory> factory_ptr = nullptr,
      std::set<std::string> *attrs = nullptr, const std::string &packed_key = "") {
    base::AutoLock auto_lock(dynamic_photo_store_lock_);
    // NOTE(XXX): 下面逻辑保证对每一个 kconf_key 只会创建一个 photo_store。
    // 若没有配置 kconf_key，则 photo_store 默认是根据 flag 配置值创建的。
    auto it = dynamic_photo_store_map_.find(kconf_key);
    if (it == dynamic_photo_store_map_.end()) {
      LOG(INFO) << "Init dynamic photo store singleton, key: " << kconf_key;
      auto config = ks::photo_store::GetPhotoStoreConfig(kconf_key);
      CheckDumpFileName(&config);
      if (attrs) {
        config.cache_config.field_mask = absl::StrJoin(*attrs, ",");
        config.cache_config.enable_convert_filed_mask_to_request_attrs = true;
      }
      if (!packed_key.empty()) {
        config.packed_key = packed_key;
      }
      auto dynamic_photo_store = std::make_unique<ps::DynamicPhotoStore>(
          config, factory_ptr ? factory_ptr : std::make_shared<DynamicItemFactory>());
      auto photo_store_ptr = dynamic_photo_store.get();
      dynamic_photo_store_map_.insert(std::make_pair(kconf_key, std::move(dynamic_photo_store)));
      return photo_store_ptr;
    } else {
      return it->second.get();
    }
  }

 private:
  DynamicPhotoStoreManager() {}

 private:
  static base::Lock dynamic_photo_store_lock_;
  folly::F14FastMap<std::string, std::unique_ptr<ks::photo_store::DynamicPhotoStore>>
      dynamic_photo_store_map_;

  DISALLOW_COPY_AND_ASSIGN(DynamicPhotoStoreManager);
};

template <typename ItemFactory>
base::Lock PhotoStoreManager<ItemFactory>::photo_store_lock_;
template <typename DynamicItemFactory>
base::Lock DynamicPhotoStoreManager<DynamicItemFactory>::dynamic_photo_store_lock_;
}  // namespace platform
}  // namespace ks
