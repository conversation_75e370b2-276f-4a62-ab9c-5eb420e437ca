#include "dragon/src/pb_converter/ad_infer_pb_converter.h"

#include <zstd.h>

#include <algorithm>
#include <fstream>
#include <memory>
#include <limits>
#include <iostream>
#include <string>
#include <vector>

#include "base/encoding/base64.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "dragon/src/module/traceback_util.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "third_party/lz4/lib/lz4.h"

DEFINE_string(ad_request_ptr_key, "ad_request_ptr", "ad request ptr key");
DEFINE_string(ad_request_str_key, "", "ad request ptr key");
DEFINE_string(ad_user_info_ptr_key, "ad_user_info_ptr", "ad user info key");
DEFINE_string(reco_user_info_key, "reco_user_info_ptr", "reco user info key");
DEFINE_string(ad_item_type_key, "ad_item_type", "ad item type");
DEFINE_string(fixed_request_type, "default", "fixed request type");
DEFINE_bool(verify_reco_user_info_decompress_size, false, "verify reco user info decompress size");
DEFINE_bool(print_debug_message, false, "print debug message");
DEFINE_string(request_llsid_key, "request_llsid_key", "request llsid");
DEFINE_string(debug_failed_req_path, "", "request llsid");
DEFINE_bool(print_ad_user_info, false, "print debug message");
DEFINE_string(print_ad_user_info_path, "", "print ad user info path");
DEFINE_bool(save_request_pb, false, "save request pb");
DEFINE_string(save_request_pb_path, "", "save request pb path");
DEFINE_bool(print_debug_respone, false, "print debug respone");
DEFINE_string(save_respone_path, "", "save respone path");
DEFINE_bool(response_base64_compress, false, "response base64 compress");
DEFINE_bool(skip_parse_as_user_info, false, "skip parse ad user info");
// 在 klearner_online_parameter_attr_enricher.cc 中定义，暂时挪过来
DEFINE_bool(base64_compress_log, true, "");

namespace ks {
namespace platform {

// ad 业务相关定义
static const char K_New_Type[] = "knews";

ZSTD_DCtx *get_dctx_per_thread() {
  static thread_local ZSTD_DCtx *dctx;
  if (dctx == nullptr) {
    dctx = ZSTD_createDCtx();
  }
  return dctx;
}

#define COMPRESS_TYPE_ZSTD 1

#define DECOMPRESS_ZSTD(value_data, value_size, flag)                                  \
  do {                                                                                 \
    size_t dsize = ZSTD_getFrameContentSize(value_data, value_size);                   \
    if (ZSTD_isError(dsize)) {                                                         \
      flag = false;                                                                    \
      break;                                                                           \
    }                                                                                  \
    buf.resize(std::max(dsize, size_t(1024 * 300)));                                   \
    dsize = ZSTD_decompressDCtx(dctx, buf.data(), buf.size(), value_data, value_size); \
    if (ZSTD_isError(dsize)) {                                                         \
      flag = false;                                                                    \
      break;                                                                           \
    }                                                                                  \
    value_data = buf.data();                                                           \
    value_size = dsize;                                                                \
    flag = true;                                                                       \
  } while (0);

#define DECOMPRESS_LZ4(value_data, value_size, flag)                                 \
  do {                                                                               \
    int dsize = LZ4_decompress_safe(value_data, buf.data(), value_size, buf.size()); \
    if (dsize < 0) {                                                                 \
      flag = false;                                                                  \
      break;                                                                         \
    }                                                                                \
    value_data = buf.data();                                                         \
    value_size = dsize;                                                              \
    flag = true;                                                                     \
  } while (0);

#define DECOMPRESS(compress_type, value_data, value_size, flag) \
  do {                                                          \
    if (compress_type == COMPRESS_TYPE_ZSTD) {                  \
      DECOMPRESS_ZSTD(value_data, value_size, flag);            \
    } else {                                                    \
      DECOMPRESS_LZ4(value_data, value_size, flag);             \
    }                                                           \
  } while (0);

int AdInferPBConverter::ParseUserInfo(const kuaishou::ad::algorithm::UserInfoBinary &raw_user_info,
                                      bool parse_base, kuaishou::ad::algorithm::UserInfo *user_info) {
  static thread_local std::vector<char> buf(1 * 1024 * 1024);
  auto dctx = get_dctx_per_thread();
  if (parse_base) {
    auto *bin = &raw_user_info.base();
    char *value_data = const_cast<char *>(bin->data());
    size_t value_size = bin->size();
    if (value_size <= 0) {
      return -100;
    }

    if (raw_user_info.base_compress()) {
      bool succ = true;
      DECOMPRESS(raw_user_info.base_compress(), value_data, value_size, succ);
      if (!succ) {
        LOG_EVERY_N(WARNING, 1000) << "decompress base size fail.";
        falcon::Inc("ad_picasso_client.get_req_pb_parse_fail", 1);
        return -101;
      }
    }
    if (!user_info->ParseFromArray(value_data, value_size)) {
      LOG_EVERY_N(WARNING, 1000) << "picasso pb parse fail. ";
      falcon::Inc("ad_picasso_client.get_req_pb_parse_fail", 1);
      return -102;
    }
  }
  uint64_t field_mask = 0;
  int multi_field_size = raw_user_info.multi_field_size();
  for (int j = 1; j < multi_field_size; j += 2) {
    auto &key = raw_user_info.multi_field(j - 1);
    auto &value = raw_user_info.multi_field(j);
    char *value_data = const_cast<char *>(value.data());
    size_t value_size = value.size();
    bool s = false;
    if (raw_user_info.multi_field_compress()) {
      bool dc_succ = true;
      DECOMPRESS(raw_user_info.multi_field_compress(), value_data, value_size, dc_succ);
      if (!dc_succ) {
        LOG_EVERY_N(WARNING, 1000) << "multi field decompress fail. type=" << key;
        return -103;
      }
    }
    if (key == "DSP_FIELD") {
      field_mask |= kuaishou::ad::algorithm::AD_DSP_FIELD;
      s = user_info->mutable_dsp_field_feature()->ParseFromArray(value_data, value_size);
    } else if (key == "ADX_FIELD") {
      field_mask |= kuaishou::ad::algorithm::AD_ADX_FIELD;
      s = user_info->mutable_adx_field_feature()->ParseFromArray(value_data, value_size);
    } else if (key == "FANS_TOP_FIELD") {
      field_mask |= kuaishou::ad::algorithm::AD_FANS_TOP_FIELD;
      s = user_info->mutable_fanstop_field_feature()->ParseFromArray(value_data, value_size);
    } else if (key == "ALLIANCE_FIELD") {
      field_mask |= kuaishou::ad::algorithm::AD_ALLIANCE_FIELD;
      s = user_info->mutable_alliance_field_feature()->ParseFromArray(value_data, value_size);
    } else if (key == "AD_K_NEWS_FIELD") {
      s = user_info->mutable_k_news_user_info()->ParseFromArray(value_data, value_size);
    } else {
      LOG_EVERY_N(WARNING, 1000) << "bad multi field type. type=" << key;
      return -104;
    }
    if (!s) {
      LOG_EVERY_N(WARNING, 1000) << "multi field parse fail. type=" << key;
      return -105;
    }
  }

  int action_field_size = raw_user_info.action_size();
  for (int j = 1; j < action_field_size; j += 2) {
    auto &key = raw_user_info.action(j - 1);
    auto &value = raw_user_info.action(j);
    char *value_data = const_cast<char *>(value.data());
    size_t value_size = value.size();
    if (raw_user_info.action_compress()) {
      bool dc_succ = true;
      DECOMPRESS(raw_user_info.action_compress(), value_data, value_size, dc_succ);
      if (!dc_succ) {
        LOG_EVERY_N(WARNING, 1000) << "action field decompress fail. type=" << key;
        return -106;
      }
    }
    bool s = false;
    if (key == "merge") {
      s = user_info->mutable_fanstop_action_list_with_tag()->ParseFromArray(value_data, value_size);
    } else {
      LOG_EVERY_N(WARNING, 1000) << "bad action field type. type=" << key;
      return -107;
    }
    if (!s) {
      LOG_EVERY_N(WARNING, 1000) << "action field parse fail. type=" << key;
      return -108;
    }
  }
  user_info->set_multi_field_mask(field_mask);
  LOG_EVERY_N(INFO, 10000) << "multi field user info debug sampling. action json_str = "
                           << user_info->mutable_fanstop_action_list_with_tag()->ShortDebugString().substr(
                                  0, 100);
  return 0;
}

int AdInferPBConverter::ParseUserInfo(const kuaishou::ad::algorithm::UniversePredictRequest &request,
                                      kuaishou::ad::algorithm::UserInfo *user_info) {
  if (!request.has_user_info_bin()) {
    return -1;
  }
  // 这里可能存在问题，咨询杨加林后，依然不确定有没有填充 ad_user_info 字段
  bool is_unlogin = request.has_ad_user_info() && request.ad_user_info().is_unlogin_user();
  int status = 0;
  if (!is_unlogin) {
    status = ParseUserInfo(request.user_info_bin(), true, user_info);
  } else {
    if (request.app_id() == K_New_Type) {
      status = ParseUserInfo(request.user_info_bin(), false, user_info);
    } else {
      status = ParseUserInfo(request.user_info_bin(), true, user_info);
    }
  }
  if (status != 0) {
    LOG(ERROR) << "ad user info parse failed, error status : " << status;
    return status;
  }
  if (!request.realtime_action().empty()) {
    bool succ = user_info->mutable_user_real_time_action()->ParseFromString(request.realtime_action());
    if (!succ) {
      status = -201;
    }
  }
  return status;
}

#undef COMPRESS_TYPE_ZSTD

ConverterStatus AdInferPBConverter::ConvertRequest(
    const kuaishou::ad::algorithm::UniversePredictRequest &req_in, ks::platform::CommonRecoRequest *req_out) {
  ConverterStatus ret = ConverterStatus::SUCCESS;
  int parse_status = 0;
  if (!FLAGS_skip_parse_as_user_info && (parse_status = ParseUserInfo(req_in, user_info_)) != 0) {
    LOG(ERROR) << "Deserialize ad user info failed.";
    if (FLAGS_debug_failed_req_path.size()) {
      std::string serialized_ad_user_info;
      req_in.SerializeToString(&serialized_ad_user_info);
      auto ts = base::GetTimestamp();
      std::string full_path = FLAGS_debug_failed_req_path + "_ad_user_info_fail_" + std::to_string(ts) + "_" +
                              std::to_string(parse_status);
      std::ofstream outFile(full_path, std::ios::out);
      std::string encoded;
      if (FLAGS_base64_compress_log && base::Base64Encode(serialized_ad_user_info, &encoded)) {
        outFile << encoded;
      } else {
        outFile << serialized_ad_user_info;
      }
      outFile.close();
      full_path = FLAGS_debug_failed_req_path + "_debug_msg_ad_user_info_fail_" + std::to_string(ts) + "_" +
                  std::to_string(parse_status);
      std::ofstream dbgFile(full_path, std::ios::out);
      dbgFile << req_in.DebugString();
      dbgFile.close();
    }
    ret = ConverterStatus::FAILED_TO_PARSE_USER_INFO;
    return ret;
  }
  if (FLAGS_print_ad_user_info && !FLAGS_print_ad_user_info_path.empty()) {
    auto ts = base::GetTimestamp();
    std::string full_path = FLAGS_print_ad_user_info_path + "_debug_msg_ad_user_info_" + std::to_string(ts);
    std::ofstream dbgFile(full_path, std::ios::out);
    dbgFile << user_info_->DebugString();
    dbgFile.close();
  }

  if (!ConvertRequest(user_info_, req_in, reco_user_info_, req_out)) {
    LOG(ERROR) << "request protocol convert failed! (UniversePredictRequest -> CommonRecoRequest)";
    ret = ConverterStatus::FAILED_TO_CONVERT_REQUEST;
    return ret;
    // response->set_llsid(request->llsid());
    // response->set_status(kuaishou::ad::algorithm::PredictStatus::INTERNAL_ERROR);
    // return grpc::Status::OK;
  }
  return ret;
}

bool AdInferPBConverter::Decompress(const std::string &input, const int &compress_flag,
                                    const uint32_t &expect_length, std::string *output) {
  bool status = false;
  if (input.empty() || (FLAGS_verify_reco_user_info_decompress_size && expect_length <= 0)) {
    return status;
  }
  if (compress_flag == 1) {
    // compress_flag == 1, 使用 zstd 解压
    status = traceback_util::zstd_util_decompress(input.data(), input.size(), output);
  } else if (compress_flag == 2) {
    // compress_flag == 2, 使用 lz4 解压
    // 外部接口传进来的 expect_length， 不确定正确不正确，万一不正确，先开个很大的空间
    int buffer_length = (expect_length > 0 ? expect_length : std::numeric_limits<unsigned int>::max());
    output->resize(buffer_length);
    status = LZ4_decompress_safe(input.data(), const_cast<char *>(output->data()), input.size(),
                                 buffer_length) > 0;
  }
  if (FLAGS_verify_reco_user_info_decompress_size) {
    // 这个参数设置为 true，说明用户确定外面传入了正确的未压缩数据长度，所以校验一下长度
    return status && output->size() == expect_length;
  }
  return status;
}

bool AdInferPBConverter::ConvertRequest(const kuaishou::ad::algorithm::UserInfo* user_info,
                                        const kuaishou::ad::algorithm::UniversePredictRequest &req_in,
                                        ks::reco::UserInfo *reco_user_info,
                                        ks::platform::CommonRecoRequest *req_out) {
  req_out->set_time_ms(base::GetTimestamp() / 1000);
  if (req_in.has_ad_user_info() && req_in.ad_user_info().is_unlogin_user()) {
    req_out->set_user_id(0);
  } else {
    req_out->set_user_id(req_in.user_id());
  }
  req_out->set_device_id(req_in.ad_user_info().device_id());
  req_out->set_request_id(std::to_string(req_in.llsid()));
  std::string request_type = FLAGS_fixed_request_type;
  if (req_in.cmd_size()) {
    thread_local std::vector<std::string> cmd_fields;
    cmd_fields.clear();
    // 上游 request 中的 cmd 字段格式 : path:model_name
    base::SplitStringWithOptions(req_in.cmd(0), ":", true, true, &cmd_fields);
    if (cmd_fields.size() >= 2) {
      const std::string model_name = cmd_fields[1];
      const std::string prefix = cmd_fields[0];
      cmd_fields.clear();
      base::SplitStringWithOptions(prefix, "/", true, true, &cmd_fields);
      // cmd_fields 为空也不符合约定，必须有正确的 prefix
      if (cmd_fields.size()) {
        cmd_fields.push_back(model_name);
        base::FastJoinStrings(cmd_fields, "_", &request_type);
      }
    }
  }
  req_out->set_request_type(request_type);
  {
    // item type 字段保留下来, 原本是 enum，转成 string 便于排查问题
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_STRING_ATTR);
    attr->set_name(FLAGS_ad_item_type_key);
    attr->set_string_value(kuaishou::ad::algorithm::ItemType_Name(req_in.item_type()));
  }
  {
    // ad user info 字段
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_INT_ATTR);
    attr->set_name(FLAGS_ad_user_info_ptr_key);
    attr->set_int_value(reinterpret_cast<int64>(user_info));
  }
  {
    // llsid 字段
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_INT_ATTR);
    attr->set_name(FLAGS_request_llsid_key);
    attr->set_int_value(req_in.llsid());
  }
  {
    // ad request ptr
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_INT_ATTR);
    attr->set_name(FLAGS_ad_request_ptr_key);
    attr->set_int_value(reinterpret_cast<int64>(&req_in));
  }
  {
    // reco user info 字段
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_INT_ATTR);
    attr->set_name(FLAGS_reco_user_info_key);
    if (!req_in.reco_user_info_compress_flag() && !req_in.serialized_reco_user_info().empty() &&
        reco_user_info->ParseFromString(req_in.serialized_reco_user_info())) {
      // 避免拷贝一次，不压缩的就不去解压函数走一圈了
      attr->set_int_value(reinterpret_cast<int64>(reco_user_info));
    } else {
      thread_local std::string decompressed_reco_user_info;
      decompressed_reco_user_info.clear();
      if (Decompress(req_in.serialized_reco_user_info(), req_in.reco_user_info_compress_flag(),
                     req_in.reco_user_info_uncompressed_size(), &decompressed_reco_user_info) &&
          reco_user_info->ParseFromString(decompressed_reco_user_info)) {
        attr->set_int_value(reinterpret_cast<int64>(reco_user_info));
      } else {
        // 这里和模型的人约定是要填充的, 当心可能影响结果
        reco_user_info->set_id(req_in.user_id());
        reco_user_info->set_device_id(req_in.ad_user_info().device_id());
        attr->set_int_value(reinterpret_cast<int64>(reco_user_info));
      }
    }
  }
  {
    // item key / attr
    for (int i = 0; i < req_in.item_id_size(); i++) {
      auto item = req_out->add_item_list();
      item->set_item_id(req_in.item_id(i));
      item->set_item_type(req_in.item_type());
    }
  }
  req_out->set_request_num(req_in.item_id_size());
  // 咨询剑冰，据说这个字段设置为 true，可以提升性能
  req_out->set_use_packed_item_attr(true);
  if (FLAGS_print_debug_message) {
    LOG(INFO) << "[DEBUG], item_id_size : " << req_in.item_id_size();
    LOG(INFO) << "UniversePredictRequest : " << req_in.DebugString();
    LOG(INFO) << "CommonRecoRequest : " << req_out->DebugString();
  }
  std::string serialized_req;
  if (!FLAGS_ad_request_str_key.empty()) {
    req_in.SerializeToString(&serialized_req);
    // ad request ptr
    auto attr = req_out->add_common_attr();
    attr->set_type(kuiba::CommonSampleEnum_AttrType_STRING_ATTR);
    attr->set_name(FLAGS_ad_request_str_key);
    attr->set_string_value(serialized_req);
  }

  if (FLAGS_save_request_pb && !FLAGS_save_request_pb_path.empty()) {
    if (serialized_req.empty()) {
      req_in.SerializeToString(&serialized_req);
    }
    std::string full_path = FLAGS_save_request_pb_path + "_req_" + std::to_string(base::GetTimestamp()) +
                            "_" + std::to_string(req_in.llsid());
    std::ofstream outFile(full_path, std::ios::out);
    outFile << serialized_req;
    outFile.close();
  }
  return true;
}

ConverterStatus AdInferPBConverter::ConvertResponse(
    const kuaishou::ad::algorithm::UniversePredictRequest &req_in,
    const ks::platform::CommonRecoResponse &resp_in,
    kuaishou::ad::algorithm::UniversePredictResponse *resp_out) {
  ConverterStatus ret = ConverterStatus::SUCCESS;
  resp_out->set_llsid(req_in.llsid());
  resp_out->set_status(kuaishou::ad::algorithm::PredictStatus::STATUS_OK);
  // 因为 request 中的 use_packed_item_attr 设置为 true 了，所以数据从 CommonRecoResponse 的 item_attr 中取
  auto &item_attrs = resp_in.item_attr();
  for (int i = 0; i < item_attrs.item_keys_size(); i++) {
    auto predict_result = resp_out->add_predict_result();
    predict_result->set_item_id(Util::GetId(item_attrs.item_keys(i)));
  }

  for (int i = 0; i < item_attrs.attr_values_size(); i++) {
    auto &attr_value = item_attrs.attr_values(i);
    if (attr_value.value_length_size() && attr_value.value_length_size() != item_attrs.item_keys_size()) {
      LOG(ERROR) << "attr name : " << attr_value.name()
                 << ", attr value length : " << attr_value.value_length_size()
                 << ", item size : " << item_attrs.item_keys_size() << ", not equal";
      continue;
    }

    const float *float_data_ptr = nullptr;
    const double *double_data_ptr = nullptr;
    // value_type 优先于 type
    if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT32) {
      float_data_ptr = reinterpret_cast<const float *>(attr_value.value().data());
    } else if (attr_value.value_type() == PackedItemAttrValue_ValueType_FLOAT64 ||
               attr_value.type() == kuiba::CommonSampleEnum::FLOAT_ATTR) {
      double_data_ptr = reinterpret_cast<const double *>(attr_value.value().data());
    }

    for (int j = 0; j < item_attrs.item_keys_size(); j++) {
      auto predict_result = resp_out->mutable_predict_result(j);
      if ((!attr_value.value_length_size() || attr_value.value_length(j) == 1) &&
          (float_data_ptr || double_data_ptr)) {
        if (float_data_ptr) {
          predict_result->add_value(*float_data_ptr);
          float_data_ptr++;
        } else if (double_data_ptr) {
          predict_result->add_value(*double_data_ptr);
          double_data_ptr++;
        }
      } else {
        predict_result->add_value(0.0);
      }
    }
  }
  if (FLAGS_print_debug_respone && !FLAGS_save_respone_path.empty()) {
    std::string serialized_response;
    resp_out->SerializeToString(&serialized_response);
    std::string full_path = FLAGS_save_respone_path + "_" + std::to_string(req_in.llsid());
    std::ofstream outFile(full_path, std::ios::out);
    std::string encoded;
    if (FLAGS_response_base64_compress && base::Base64Encode(serialized_response, &encoded)) {
      outFile << encoded;
    } else {
      outFile << serialized_response;
    }
    outFile.close();
    full_path = FLAGS_save_respone_path + "_" + "debug_message" + "_" + std::to_string(req_in.llsid());
    std::ofstream dbgFile(full_path, std::ios::out);
    dbgFile << resp_out->DebugString();
    dbgFile.close();
  }
  return ret;
}

AdInferPBConverter::AdInferPBConverter() {
  arena_.reset(new ::google::protobuf::Arena());
  user_info_ = ::google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UserInfo>(arena_.get());
  reco_user_info_ = ::google::protobuf::Arena::CreateMessage<ks::reco::UserInfo>(arena_.get());
}

void AdInferPBConverter::Clear() {
  arena_->Reset();
  user_info_ = ::google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UserInfo>(arena_.get());
  reco_user_info_ = ::google::protobuf::Arena::CreateMessage<ks::reco::UserInfo>(arena_.get());
}

}  // namespace platform
}  // namespace ks
