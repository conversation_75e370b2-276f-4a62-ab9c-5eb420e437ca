#pragma once

#include <memory>
#include <string>

#include "ks/reco_proto/proto/reco.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"

namespace ks {
namespace platform {

enum class ConverterStatus {
  SUCCESS = 0,
  FAILED_TO_PARSE_USER_INFO = 1,
  FAILED_TO_CONVERT_REQUEST = 2,
  FAILED_TO_CONVERT_RESPONSE = 3
};

class AdInferPBConverter {
 public:
  AdInferPBConverter();

  ConverterStatus ConvertRequest(const kuaishou::ad::algorithm::UniversePredictRequest &req_in,
                                 ks::platform::CommonRecoRequest *req_out);

  ConverterStatus ConvertResponse(const kuaishou::ad::algorithm::UniversePredictRequest &req_in,
                                  const ks::platform::CommonRecoResponse &resp_in,
                                  kuaishou::ad::algorithm::UniversePredictResponse *resp_out);
  void Clear();

 private:
  int ParseUserInfo(const kuaishou::ad::algorithm::UserInfoBinary &raw_user_info, bool parse_base,
                    kuaishou::ad::algorithm::UserInfo *user_info);
  int ParseUserInfo(const kuaishou::ad::algorithm::UniversePredictRequest &request,
                    kuaishou::ad::algorithm::UserInfo *user_info);
  bool ConvertRequest(const kuaishou::ad::algorithm::UserInfo* user_info,
                      const kuaishou::ad::algorithm::UniversePredictRequest &req_in,
                      ks::reco::UserInfo *reco_user_info,
                      ks::platform::CommonRecoRequest *req_out);
  bool Decompress(const std::string &input, const int &compress_flag, const uint32_t &expect_length,
                  std::string *output);

  std::unique_ptr<::google::protobuf::Arena> arena_;
  kuaishou::ad::algorithm::UserInfo* user_info_;
  ks::reco::UserInfo* reco_user_info_;
};

}  // namespace platform
}  // namespace ks
