#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_util.h"
#include "perfutil_coarse/perfutil_coarse.h"

DEFINE_int64(attr_usage_perflog_interval, 100, "perflog interval for attr usage perf");
DEFINE_string(enable_attr_usage_perflog_kconf_key, "", "perflog kconf switch for attr usage perf");
DEFINE_bool(enable_attr_usage_perflog, false, "perflog switch for attr usage perf");
DEFINE_bool(record_filter_reason, false, "record filtered items with reason");
DEFINE_bool(all_processor_record_filter_reason, true, "all processor record filtered items with reason");
DEFINE_int32(attr_type_check_level, 0, "set attr_type from json and check");
DEFINE_bool(check_attr_type_get, false, "check attr_type for get attr");
DEFINE_bool(save_desired_attr_type, true, "save attr_type in table, for ad to the insert order of attr");
DEFINE_bool(record_reason_list, false, "control whether _REASON_LIST_ is generated");
DEFINE_int64(attr_counter_interval, 1, "count interval for attr get/set interface");
DEFINE_bool(convert_nan_inf_to_zero, false, "convert NaN/Inf value to 0 for double value attr set");
DEFINE_int64(attr_value_size_growth_threshold, 0, "attr allocation threshold");
DEFINE_double(attr_value_size_growth_factor, 2, "attr growth factor");
DEFINE_int64(attr_value_size_growth_step, 0, "attr growth step");

#define OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(TYPE, INDEX, FLAT_INDEX_ADDR)                    \
  {                                                                                        \
    if (auto p = GetSingularValue<TYPE>(INDEX, FLAT_INDEX_ADDR)) {                         \
      absl::SubstituteAndAppend(&debug_string, "($0): $1", type_name, *p);                 \
    } else {                                                                               \
      absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name); \
    }                                                                                      \
    break;                                                                                 \
  }

#define OUTPUT_LIST_ITEM_ATTR_TO_STREAM(TYPE, INDEX, FLAT_INDEX_ADDR)                      \
  {                                                                                        \
    if (auto p = GetListValue<TYPE>(INDEX, FLAT_INDEX_ADDR)) {                             \
      absl::SubstituteAndAppend(&debug_string, "($0[$1]): [", type_name, p->size());       \
      if (!p->empty()) {                                                                   \
        absl::StrAppend(&debug_string, p->front());                                        \
        std::for_each(std::next(p->begin()), p->end(),                                     \
                      [&](const auto &v) { absl::StrAppend(&debug_string, ", ", v); });    \
      }                                                                                    \
      absl::StrAppend(&debug_string, "]");                                                 \
    } else {                                                                               \
      absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name); \
    }                                                                                      \
    break;                                                                                 \
  }

#define GET_ITEM_ATTR_VALUE_FROM_RESULT(DEFAULT_RETURN, FUNC) \
  {                                                           \
    if (unlikely(!item_attr)) return DEFAULT_RETURN;          \
    if (unlikely(check_multi_table)) {                        \
      const AttrTable *table = GetTable();                    \
      if (!item_attr->IsOwnedBy(table)) {                     \
        item_attr = table->GetAttr(item_attr->name());        \
      }                                                       \
      if (!item_attr) return DEFAULT_RETURN;                  \
    }                                                         \
    if (item_attr->is_from_flat_index && flat_index_addr) {   \
      return item_attr->FUNC(attr_index, flat_index_addr);    \
    } else {                                                  \
      return item_attr->FUNC(attr_index);                     \
    }                                                         \
  }

#define SET_ITEM_ATTR_VALUE_FROM_RESULT(FUNC, ...)             \
  {                                                            \
    if (unlikely(!item_attr)) return false;                    \
    if (unlikely(check_multi_table)) {                         \
      AttrTable *table = const_cast<AttrTable *>(GetTable());  \
      if (!item_attr->IsOwnedBy(table)) {                      \
        item_attr = table->GetOrInsertAttr(item_attr->name()); \
      }                                                        \
    }                                                          \
    return item_attr->FUNC(attr_index, ##__VA_ARGS__);         \
  }

namespace ks {
namespace platform {

// 封装一组针对高性能场景的 item_attr get/set 接口
bool CommonRecoResult::HasAttr(const AttrValue *item_attr, bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(false, HasValue);
}
absl::optional<int64> CommonRecoResult::GetIntAttr(const AttrValue *item_attr, bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetIntValue);
}
absl::optional<double> CommonRecoResult::GetDoubleAttr(const AttrValue *item_attr,
                                                       bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetDoubleValue);
}
absl::optional<absl::string_view> CommonRecoResult::GetStringAttr(const AttrValue *item_attr,
                                                                  bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetStringValue);
}
absl::optional<absl::Span<const int64>> CommonRecoResult::GetIntListAttr(const AttrValue *item_attr,
                                                                         bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetIntListValue);
}
absl::optional<absl::Span<const double>> CommonRecoResult::GetDoubleListAttr(const AttrValue *item_attr,
                                                                             bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetDoubleListValue);
}
absl::optional<std::vector<absl::string_view>> CommonRecoResult::GetStringListAttr(
    const AttrValue *item_attr, bool check_multi_table) const {
  GET_ITEM_ATTR_VALUE_FROM_RESULT(absl::nullopt, GetStringListValue);
}
const boost::any *CommonRecoResult::GetExtraAttr(const AttrValue *item_attr, bool check_multi_table) const {
  if (!item_attr) return nullptr;
  if (check_multi_table) {
    const DataFrame *table = GetTable();
    if (!item_attr->IsOwnedBy(table)) {
      item_attr = table->GetAttr(item_attr->name());
    }
    if (!item_attr) return nullptr;
  }
  return item_attr->GetExtraValue(attr_index);
}
bool CommonRecoResult::ClearAttr(AttrValue *item_attr, bool check_multi_table) const {
  return item_attr->ClearValue(attr_index, false);
}
bool CommonRecoResult::SetIntAttr(AttrValue *item_attr, int64 val, bool if_not_exist, bool check_overwrite,
                                  bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetIntValue, val, if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetDoubleAttr(AttrValue *item_attr, double val, bool if_not_exist,
                                     bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetDoubleValue, val, if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetStringAttr(AttrValue *item_attr, std::string val, bool if_not_exist,
                                     bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetStringValue, std::move(val), if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetIntListAttr(AttrValue *item_attr, std::vector<int64> &&val, bool if_not_exist,
                                      bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetIntListValue, std::move(val), if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetDoubleListAttr(AttrValue *item_attr, std::vector<double> &&val, bool if_not_exist,
                                         bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetDoubleListValue, std::move(val), if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetStringListAttr(AttrValue *item_attr, std::vector<std::string> &&val,
                                         bool if_not_exist, bool check_overwrite,
                                         bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetStringListValue, std::move(val), if_not_exist, check_overwrite);
}
bool CommonRecoResult::AppendIntListAttr(AttrValue *item_attr, int64 val, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(AppendIntListValue, val);
}
bool CommonRecoResult::AppendDoubleListAttr(AttrValue *item_attr, double val, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(AppendDoubleListValue, val);
}
bool CommonRecoResult::AppendStringListAttr(AttrValue *item_attr, std::string val,
                                            bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(AppendStringListValue, std::move(val));
}
bool CommonRecoResult::ResetIntListAttr(AttrValue *item_attr, int capacity, bool if_not_exist,
                                        bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(ResetIntListValue, capacity, if_not_exist, check_overwrite);
}
bool CommonRecoResult::ResetDoubleListAttr(AttrValue *item_attr, int capacity, bool if_not_exist,
                                           bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(ResetDoubleListValue, capacity, if_not_exist, check_overwrite);
}
bool CommonRecoResult::ResetStringListAttr(AttrValue *item_attr, int capacity, bool if_not_exist,
                                           bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(ResetStringListValue, capacity, if_not_exist, check_overwrite);
}
bool CommonRecoResult::SetExtraAttr(AttrValue *item_attr, boost::any &&val, bool if_not_exist,
                                    bool check_overwrite, bool check_multi_table) const {
  SET_ITEM_ATTR_VALUE_FROM_RESULT(SetExtraValue, std::move(val), if_not_exist, check_overwrite);
}
template <>
const ::google::protobuf::Message *CommonRecoResult::GetPtrAttr<::google::protobuf::Message>(
    const AttrValue *item_attr, bool check_multi_table) const {
  if (!item_attr) return nullptr;
  if (check_multi_table) {
    const DataFrame *table = GetTable();
    if (!item_attr->IsOwnedBy(table)) {
      item_attr = table->GetAttr(item_attr->name());
    }
    if (!item_attr) return nullptr;
  }
  return item_attr->GetPtrValue<::google::protobuf::Message>(attr_index);
}
template <>
const std::vector<float> *CommonRecoResult::GetPtrAttr<const std::vector<float>>(
    const AttrValue *item_attr, bool check_multi_table) const {
  if (!item_attr) return nullptr;
  if (check_multi_table) {
    const DataFrame *table = GetTable();
    if (!item_attr->IsOwnedBy(table)) {
      item_attr = table->GetAttr(item_attr->name());
    }
    if (!item_attr) return nullptr;
  }
  return item_attr->GetPtrValue<const std::vector<float>>(attr_index);
}

void AppendPbMessageInfo(std::string *debug_string, const ::google::protobuf::Message *pb,
                         const std::string &multi_line_prefix) {
  if (!multi_line_prefix.empty()) {
    auto pb_message_info = pb->Utf8DebugString();
    std::vector<absl::string_view> lines = absl::StrSplit(pb_message_info, '\n');
    if (lines.empty()) return;
    if (lines.back().empty()) lines.pop_back();
    for (const auto &line : lines) {
      absl::StrAppend(debug_string, multi_line_prefix, line, "\n");
    }
  } else {
    absl::StrAppend(debug_string, pb->ShortDebugString());
  }
}

std::string AttrValue::GetDebugString(const std::string &multi_line_prefix) const {
  static const CommonRecoResult first_row = CommonRecoResult(0, 0, 0);
  return GetDebugString(first_row, multi_line_prefix);
}

std::string AttrValue::GetDebugString(const CommonRecoResult &result,
                                      const std::string &multi_line_prefix) const {
  std::string debug_string;
  absl::StrAppend(&debug_string, name_);
  auto attr_index = result.GetAttrIndex();
  auto *flat_index_addr = result.GetFlatIndexItemAddr();
  if (!HasValue(attr_index, flat_index_addr, false)) {
    if (!HasDefaultValue()) {
      absl::StrAppend(&debug_string, ": NULL");
      return debug_string;
    } else {
      absl::StrAppend(&debug_string, "?");
    }
  }
  auto type_name = RecoUtil::GetAttrTypeName(value_type);
  absl::StrAppend(&debug_string, IsReadOnly() ? "*" : "", " ");

  if (printer_) {
    absl::SubstituteAndAppend(&debug_string, printer_(result));
    return debug_string;
  }
  switch (value_type) {
    case AttrType::INT:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(int64, attr_index, flat_index_addr);
    case AttrType::INT32:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(int32, attr_index, flat_index_addr);
    case AttrType::INT16:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(int16, attr_index, flat_index_addr);
    case AttrType::INT8:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(int8, attr_index, flat_index_addr);
    case AttrType::FLOAT:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(double, attr_index, flat_index_addr);
    case AttrType::FLOAT32:
      OUTPUT_SINGLE_ITEM_ATTR_TO_STREAM(float, attr_index, flat_index_addr);
    case AttrType::FLOAT16: {
      if (auto p = GetSingularValue<float16_t>(attr_index, flat_index_addr)) {
        absl::SubstituteAndAppend(&debug_string, "($0, print as int16): $1", type_name, p->value);
      } else {
        absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
      }
      break;
    }
    case AttrType::STRING: {
      if (auto p = GetStringValue(attr_index, flat_index_addr)) {
        absl::SubstituteAndAppend(&debug_string, "($0[$1]): $2", type_name, p->size(), *p);
      } else {
        absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
      }
      break;
    }
    case AttrType::EXTRA: {
      if (auto *p = GetPtrValue<::google::protobuf::Message>(attr_index)) {
        absl::SubstituteAndAppend(&debug_string, "($0): {\n", p->GetTypeName());
        AppendPbMessageInfo(&debug_string, p, multi_line_prefix);
        absl::StrAppend(&debug_string, "  }");
      } else if (auto *p = GetPtrValue<const std::vector<float>>(attr_index)) {
        absl::SubstituteAndAppend(&debug_string, "(float_vec[$0]): [", p->size());
        if (!p->empty()) {
          absl::StrAppend(&debug_string, p->front());
          std::for_each(std::next(p->begin()), p->end(),
                        [&](const auto &v) { absl::StrAppend(&debug_string, ", ", v); });
        }
        absl::StrAppend(&debug_string, "]");
      } else {
        absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
      }
      break;
    }
    case AttrType::INT_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(int64, attr_index, flat_index_addr);
    case AttrType::INT32_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(int32, attr_index, flat_index_addr);
    case AttrType::INT16_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(int16, attr_index, flat_index_addr);
    case AttrType::INT8_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(int8, attr_index, flat_index_addr);
    case AttrType::FLOAT_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(double, attr_index, flat_index_addr);
    case AttrType::FLOAT32_LIST:
      OUTPUT_LIST_ITEM_ATTR_TO_STREAM(float, attr_index, flat_index_addr);
    case AttrType::FLOAT16_LIST: {
      if (auto p = GetListValue<float16_t>(attr_index, flat_index_addr)) {
        absl::SubstituteAndAppend(&debug_string, "($0[$1], print as int16_list): [", type_name, p->size());
        if (!p->empty()) {
          absl::StrAppend(&debug_string, p->front().value);
          std::for_each(std::next(p->begin()), p->end(),
                        [&](const auto &v) { absl::StrAppend(&debug_string, ", ", v.value); });
        }
        absl::StrAppend(&debug_string, "]");
      } else {
        absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
      }
      break;
    }
    case AttrType::STRING_LIST: {
      if (auto p = GetStringListValue(attr_index, flat_index_addr)) {
        absl::SubstituteAndAppend(&debug_string, "($0[$1]): [", type_name, p->size());
        if (!p->empty()) {
          absl::StrAppend(&debug_string, p->front());
          std::for_each(std::next(p->begin()), p->end(),
                        [&](const auto &v) { absl::StrAppend(&debug_string, ", ", v); });
        }
        absl::StrAppend(&debug_string, "]");
      } else {
        absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
      }
      break;
    }
    default:
      absl::SubstituteAndAppend(&debug_string, "($0): ??? (unprintable data)", type_name);
  }
  return debug_string;
}

void AttrTable::Clear(const std::string &request_type, int64 process_counter) {
  reco_results_.clear();

  if (!is_logical_) {
    attr_payload_->Clear(name_, request_type, process_counter);
  }

  int attr_perf_steps = std::min(FLAGS_attr_usage_perflog_interval, process_counter);

  bool enable_attr_check = true;
  if (FLAGS_attr_counter_interval > 0) {
    enable_attr_check = (process_counter % FLAGS_attr_counter_interval == 0);
  }

  // item 侧属性必须保留预开空间, for 循环内部 clear 后不要再执行 item_attrs_.Clear() !!!
  for (auto &attr_pair : item_attrs_) {
    auto &attr = attr_pair.second;
    if (!attr->IsOwnedBy(this)) {
      continue;
    }
    // 在 Clear 前对 counter 进行一次性上报
    if (FLAGS_attr_type_check_level >= 1 && !RecoUtil::IsBuiltInAttrName(attr->name()) &&
        (!for_common_data_ || (!absl::StartsWith(attr->name(), "_if_control_attr_") &&
                               !absl::StartsWith(attr->name(), "_else_control_attr_") &&
                               !absl::StartsWith(attr->name(), "_elseif_control_attr_") &&
                               !absl::StartsWith(attr->name(), "_switch_control_attr_")))) {
      CL_LOG_WARNING_COUNT(attr->wrong_type_get_counter, "attr_wrong_type_get",
                           absl::StrCat((for_common_data_ ? "" : name_ + "::"), attr->name()))
          << "get attr " << (for_common_data_ ? "" : name_ + "::") << attr->name() << " wrong type for "
          << attr->wrong_type_get_counter << " times";
      CL_LOG_ERROR_COUNT(attr->wrong_type_set_counter, "attr_wrong_type_set",
                         absl::StrCat((for_common_data_ ? "" : name_ + "::"), attr->name()))
          << "set attr " << (for_common_data_ ? "" : name_ + "::") << attr->name() << " wrong type for "
          << attr->wrong_type_set_counter << " times";
    }
    if (!for_common_data_) {
      CL_LOG_EXCEPTION_COUNT(attr->inconsistent_counter,
                             "[DANGER!!!] inconsistent_item_attr_type: " + attr->name())
          << "[DANGER!!!] set item_attr '" << attr->name() << "' inconsistent type for "
          << attr->inconsistent_counter << " times";
    }
    CL_LOG_ERROR_COUNT(attr->read_only_counter, "[ATTENTION] try_overwrite_read_only_attr", attr->name())
        << "[ATTENTION] try to overwrite read-only item_attr '" << attr->name() << "' for "
        << attr->read_only_counter << " times";
    CL_LOG_ERROR_COUNT(attr->no_expand_counter, "[ATTENTION] try_resize_no_expand_attr", attr->name())
        << "[ATTENTION] try to resize no-expand item_attr '" << attr->name() << "' for "
        << attr->no_expand_counter << " times";
    if (attr->overwrite_counter > 0) {
      CL_PERF_COUNT(attr->overwrite_counter, kPerfNs, "attr_overwrite." + LevelInfo(),
                    GlobalHolder::GetServiceIdentifier(), request_type, attr->name());
      CL_LOG_EVERY_N(INFO, 1000) << "overwrite item_attr for " << attr->overwrite_counter
                                 << " times: " << attr->name();
    }
    bool clear_attr_counter = false;
    if (process_counter % FLAGS_attr_usage_perflog_interval ==
        attr->index() % FLAGS_attr_usage_perflog_interval) {
      clear_attr_counter = true;
      if (IsAttrUsagePerflogEnabled()) {
        ks::infra::PerfUtil::IntervalLogStash(
            attr->used_counter * 1000 / attr_perf_steps, kPerfNs, "attr_used." + LevelInfo(),
            GlobalHolder::GetServiceIdentifier(), request_type, attr->name());
        ks::infra::PerfUtil::IntervalLogStash(
            attr->access_counter * 1000 / attr_perf_steps, kPerfNs, "attr_access." + LevelInfo(),
            GlobalHolder::GetServiceIdentifier(), request_type, attr->name());
      }
    }
    attr->Clear(clear_attr_counter);
    if (enable_attr_check) {
      attr->EnableAttrCheck();
    } else {
      attr->DisableAttrCheck();
    }
  }

  roaring::Roaring new_bitmap;
  attr_index_bit_map_.swap(new_bitmap);
  attr_index_to_item_.clear();
  filter_results_.clear();
  filter_reason_offsets_.clear();
  filter_reasons_.clear();
  filter_reason_map_.clear();
}

void AttrTable::ClearFilterResults() {
  filter_results_.clear();
}

void AttrPayload::Clear(const std::string &table_name, const std::string &request_type,
                        int64 process_counter) {
  item_attr_index_map_.clear();
  max_item_index_ = -1;
  item_flat_index_addr_map_.clear();
}

}  // namespace platform
}  // namespace ks
