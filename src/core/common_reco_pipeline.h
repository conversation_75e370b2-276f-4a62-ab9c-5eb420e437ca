#pragma once

#include <algorithm>
#include <cstddef>
#include <iterator>
#include <memory>
#include <set>
#include <string>
#include <thread>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/common/base.h"
#include "base/common/closure.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/thread/blocking_queue.h"
#include "base/thread/thread_pool.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/core/common_reco_shared_gflags.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/core/degrader_manager.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/module/traceback_util.h"
#include "dragon/src/util/ktrace_util.h"
#include "dragon/src/util/logging_util.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "ktrace/core/scope_kspan.h"
#include "ktrace/core/tracer_manager.h"
#include "serving_base/util/factory.h"
#include "serving_base/util/json_util.h"
#include "serving_base/util/scope_exit.h"
#include "third_party/abseil/absl/hash/hash.h"

namespace ks {
namespace platform {

/**
 * NOTE(fangjianbing):
 * CommonRecoLeaf Processor 基类
 * 注意：原则上禁止 Leaf 开发者直接继承该类进行新 Processor 的实现
 */
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
class BaseProcessor : public base::JsonFactoryClass {
  static_assert(std::is_base_of<ImmutableContext, MutableContext>::value,
                "ImmutableContext must be a base of MutableContext");

 public:
  // 是否是异步请求的 Processor
  virtual bool IsAsync() const {
    return is_async_;
  }

  // 对于异步请求 Processor 标记他的下游依赖 Processor 名
  virtual const std::string &GetDownstreamProcessor() const {
    return downstream_processor_;
  }

  // Processor 处理的结果集所属的 table 名
  virtual const std::string &GetTableName() const {
    static const std::string empty_table = "";
    return table_name_ ? table_name_.value() : empty_table;
  }

  virtual const base::Json *ItemFromTables() const {
    return nullptr;
  }

  virtual bool HasTableName() const {
    return table_name_.has_value();
  }

  // 本地异步处理 Processor 的 Wait 优先级，优先级高的异步 Processor
  // 将被优先等待结果，同等优先级的 Processor 按调用先后顺序等待
  virtual int GetAsyncWaitPriority() const {
    return 0;
  }

  // 是否跳过该 Processor Run() 的执行
  virtual bool Skip(ImmutableContext *context, typename Container::const_iterator begin,
                    typename Container::const_iterator end) const {
    return false;
  }

  // 是否记录该 Processor 的 step info (用于 debug 的详细数据)
  virtual bool RecordStepInfoDetails(ImmutableContext *context, bool default_traceback) const {
    return false;
  }

  // Traceback 增量更新的情况下，这条配置表示强制打出该 Processor 的所有 traceback 数据
  virtual bool RecordStepInfoFullDetails() const {
    return false;
  }

  // 获取 processor 的 meta data
  virtual const MetaData *GetMetaData() const {
    return &meta_data_;
  }

  // 获取 processor 的降级策略名
  virtual std::string GetDegradeKey(ReadableRecoContextInterface *context) const = 0;

  // 获取 processor 降级的截断数 attr
  virtual std::string GetDegradeLimitAttr() const = 0;

  // 设置 processor 降级截断状态
  virtual void SetDegradeLimit() = 0;

  // 动态注册 degrader
  virtual void RegistDynamicDegrader(ReadableRecoContextInterface *context) = 0;

  // Processor 核心执行逻辑
  virtual void Run(MutableContext *context, Container *results) = 0;

  // Run() 执行前调用
  virtual void OnEnter(ImmutableContext *context) = 0;

  // Run() 执行后调用
  virtual void OnExit(ImmutableContext *context) = 0;

  // pipeline 执行按照 processor Run 的顺序逆序调度
  virtual void OnPipelineExit(ImmutableContext *context) = 0;

  // 在每个请求处理结束后, 框架层算子级别的一些清理工作
  virtual void InternalClear(ImmutableContext *context) = 0;

  void OnPipelineExitImpl(ImmutableContext *context) {
    InternalClear(context);
    OnPipelineExit(context);
  }

  virtual void InspectAttrs(ImmutableContext *context, typename Container::const_iterator begin,
                            typename Container::const_iterator end, bool is_input,
                            bool is_async_delayed) const {}

  // 用于清理 processor 自管理资源的闭包回调, 由上层框架决定在合适的时机调用
  virtual std::function<void(void)> Purge() {
    return []() {};
  }

  virtual ProcessorType GetType() const {
    return ProcessorType::BASE;
  }

  virtual bool IsBranchController() const {
    return false;
  }

  const std::string &GetName() const {
    return name();
  }
  void SetTableName(const std::string &name) {
    table_name_ = name;
  }
  const std::string &GetPipelineName() const {
    return pipeline_name_;
  }
  void SetPipelineName(const std::string &pipeline_name) {
    pipeline_name_ = pipeline_name;
  }
  int GetPipelinePosition() const {
    return pipeline_position_;
  }
  void SetPipelinePosition(int position) {
    pipeline_position_ = position;
  }

  bool GetHasBeenRun() const {
    return has_been_run_;
  }

  void SetHasBeenRun(bool rs) {
    has_been_run_ = rs;
  }

  void SetRunStatus(RunStatus status) {
    switch (status) {
      case RunStatus::FAILED:
        need_update_cache_ = false;
        break;
      case RunStatus::SUCCESS:
      default:
        break;
    }
  }

  virtual std::string GetInfo() const {
    return "processorName: " + GetName();
  }
  virtual ~BaseProcessor() {}

  bool Initialize() final {
    InitConfigData();
    is_async_ = config()->GetString("downstream_processor", &downstream_processor_);
    auto item_table = config()->Get("item_table");
    if (item_table && item_table->IsString()) {
      table_name_ = item_table->StringValue();
    } else {
      table_name_ = absl::nullopt;
    }
    if (!InitEmbeddedPipeline()) {
      return false;
    }
    return InitProcessor();
  }

  // 构造的时候初始化, 被 Initialize 调用
  virtual bool InitProcessor() {
    return true;
  }

  std::string GetAsyncStatusAttr() const {
    return config()->GetString("save_async_status_to", "");
  }

 protected:
  BaseProcessor() {}
  // 初始化 config 相关的数据
  virtual void InitConfigData() {}
  // processor 的 meta data
  MetaData meta_data_;

 protected:
  // 初始化嵌套 pipeline
  virtual bool InitEmbeddedPipeline() {
    return true;
  }

 protected:
  // 是否写缓存
  bool need_update_cache_ = false;
  std::vector<uint64_t> miss_cache_item_keys_;

 private:
  // processor 所属 pipeline 的 name
  std::string pipeline_name_;
  // 异步 processor 的第一个下游
  std::string downstream_processor_;
  // processor 操作的表名
  absl::optional<std::string> table_name_;
  // processor 是所属 pipeline 的第几个 processor
  int pipeline_position_ = 0;
  // 是否为异步 processor
  bool is_async_ = false;
  // 是否执行过 run
  bool has_been_run_ = false;

  DISALLOW_COPY_AND_ASSIGN(BaseProcessor);
};

// CommonRecoLeaf 的 Processor 类型
typedef BaseProcessor<RecoProcessorMetaData, AddibleRecoContextInterface, ReadableRecoContextInterface,
                      std::vector<CommonRecoResult>>
    BaseRecoProcessor;
typedef std::vector<CommonRecoResult>::iterator RecoResultIter;
typedef std::vector<CommonRecoResult>::const_iterator RecoResultConstIter;

/**
 * NOTE(fangjianbing):
 * 用于执行一系列 BaseProcessor 的 Pipeline 结构
 */
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
class CommonPipeline {
  static_assert(std::is_base_of<ImmutableContext, MutableContext>::value,
                "ImmutableContext must be a base of MutableContext");
  typedef BaseProcessor<MetaData, MutableContext, ImmutableContext, Container> CommonProcessor;

 public:
  explicit CommonPipeline(const std::string &name) : name_(name) {}
  virtual ~CommonPipeline() {}

  bool Initialize(const base::Json *config, const base::Json *processor_config,
                  const base::Json *combo_processor_config = nullptr);

  // pipeline 执行逻辑
  void Execute(MutableContext *context, Container *results);

  virtual std::string GetInfo() const;

  // XXX(bixiaodong) 判断一次请求中，针对每个 processor 是否设置详细的 traceback 信息
  // 判断优先级：kconf 配置 > 请求携带配置 > processor 的 traceback 配置 > gflags 默认值
  bool NeedTracebackDetails(ImmutableContext *context, CommonProcessor *processor);

  const std::string &GetTableName() {
    return table_name_;
  }

  void PipelineExit(ImmutableContext *);

  bool IsPostResponse() const {
    return post_response_;
  }

  const std::string &GetName() {
    return name_;
  }

 protected:
  void RecordStepInfo(MutableContext *context, CommonProcessor *processor, bool processor_skipped,
                      int64 duration, int size_pre, Container *results, const std::string &trigger_alias = "",
                      const std::string &type_name = "", bool record_result_and_attrs = false);

  void SaveCommonAttrs(ImmutableContext *context, CommonRecoStepInfo *info);
  void SaveCommonData(ImmutableContext *context, CommonRecoStepInfo *info);
  void AddDataTable(MutableContext *context, CommonRecoStepInfo *info, absl::string_view table_name,
                    const std::vector<ItemAttr *> &accessors = std::vector<ItemAttr *>());
  void SaveItemData(MutableContext *context, CommonProcessor *processor, CommonRecoStepInfo *info);

  bool IsKtraceEnabled(const CommonProcessor *processor);

  std::vector<ItemAttr *> SetupTracebackBaseInfo(MutableContext *context, const Container *results,
                                                 CommonProcessor *processor, CommonRecoStepInfo *info,
                                                 bool include_list_attr,
                                                 bool record_result_and_attrs = false);

  void SetTracebackOmitInfo(MutableContext *context, CommonProcessor *processor);

  bool NeedResult(CommonProcessor *processor);

  CommonRecoStepInfo *SetStepInfoBaseInfo(ImmutableContext *context, CommonProcessor *processor,
                                          bool processor_skipped, int64 duration, int size_pre,
                                          Container *results, const std::string &trigger_alias,
                                          const std::string &type_name);

  ItemAttr *GetTracebackAccessor(ImmutableContext *context, std::string *compressed_attrs,
                                 const std::string &attr_name, bool include_list_attr,
                                 CommonProcessor *processor = nullptr);

  std::vector<ItemAttr *> GetTracebackItemAttrAccessors(MutableContext *context, CommonProcessor *processor,
                                                        const std::set<absl::string_view> *output_item_attrs,
                                                        std::string *compressed_attrs, bool include_list_attr,
                                                        bool record_result_and_attrs);

  std::string GetTableName(const std::string &attr_name);

  std::unordered_map<std::string, std::vector<ItemAttr *>> GetMultiTableTracebackItemAttrAccessors(
      MutableContext *context, CommonProcessor *processor,
      const std::set<absl::string_view> *output_item_attrs, std::string *compressed_attrs,
      bool include_list_attr, bool record_result_and_attrs);

  bool NeedRecordItemAttr(CommonProcessor *processor, const std::set<absl::string_view> *output_item_attrs,
                          const std::set<std::string> *downstream_item_attrs, const std::string &attr_name,
                          bool report_multi_table);

  void SaveResultsToPackedItemValue(MutableContext *context, const Container *results,
                                    CommonProcessor *processor, CommonRecoStepInfo *info,
                                    bool record_result_and_attrs = false);

  void SaveResultsToCompressItem(MutableContext *context, const Container *results,
                                 CommonProcessor *processor, CommonRecoStepInfo *info);

  void SaveResultsToPostResult(ImmutableContext *context, const Container *results, CommonRecoStepInfo *info);

  void SaveFilterReason(MutableContext *context, CommonRecoStepInfo *info);

  void ClearFilterReason(MutableContext *context);

  CommonProcessor *CreateProcessor(const std::string &name, const base::Json *config);

  void ProcessorInitWithQueue(const std::string name, const base::Json *config,
                              thread::BlockingQueue<CommonProcessor *> *queue);

  const base::Json *GetProcessorConfig(const std::string &name, const base::Json *processor_config);

  bool FastParallelRegisterProcessor(const base::Json *processor_config,
                                     const std::vector<std::string> &all_processors);

  bool ParallelRegisterProcessor(const base::Json *processor_config,
                                 const std::vector<std::string> &all_processors);

  bool SerialRegisterProcessor(const base::Json *processor_config,
                               const std::vector<std::string> &all_processors);

  bool RegisterProcessor(const std::string &name, const base::Json *processor_config);

  virtual void InitPipeline() {}

  CommonRecoStepInfo *GetNewStepInfo();

  void WaitForCallbacks(MutableContext *context, CommonProcessor *processor, Container *results,
                        const std::string &trigger_alias, bool self_wait = false);

  std::string name_;
  base::AutoDeleteHash<CommonProcessor> processors_;
  std::vector<CommonProcessor *> processor_run_;
  // 记录 pipeline 内每个 processor 的执行情况, 作为 debug 信息
  // 为复用 step_infos_ 中的 CommonRecoStepInfo 结构, 避免反复构造, 用一个变量单独记录 step_infos_
  // 中已被使用的 CommonRecoStepInfo 数目
  int step_infos_count_ = 0;
  std::vector<std::unique_ptr<CommonRecoStepInfo>> step_infos_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<std::set<std::string>>>> ktrace_processor_whitelist_;

 public:
  const std::string &GetName() const;
  const base::AutoDeleteHash<CommonProcessor> &GetProcessors();
  const std::vector<CommonProcessor *> &GetProcessorRun();

  // NOTE(fangjianbing): 该接口需要在外层配合 GetProcessorStepInfosCount() 使用以获取真实的 StepInfo 数目
  CommonRecoStepInfo *GetProcessorStepInfo(int index);
  int GetProcessorStepInfosCount();

 private:
  // pipeline 单次执行逻辑
  void DoExecute(MutableContext *context, Container *results);

  // pipeline 循环执行逻辑
  void ForLoopExecute(MutableContext *context, Container *results);
  void DoWhileLoopExecute(MutableContext *context, Container *results);

 private:
  int max_loop_count_ = 0;
  std::string loop_control_attr_ = "";
  std::string table_name_ = "";
  std::string loop_on_ = "";
  std::string loop_index_ = "";
  std::string loop_value_ = "";
  bool post_response_ = false;

  folly::F14FastMap<std::string, std::pair<AttrTable *, AttrValue *>> table_column_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonPipeline);
};

// CommonRecoLeaf 的 pipeline 类型
typedef CommonPipeline<RecoProcessorMetaData, AddibleRecoContextInterface, ReadableRecoContextInterface,
                       std::vector<CommonRecoResult>>
    CommonRecoPipeline;

}  // namespace platform
}  // namespace ks
