#pragma once

#include <limits>
#include <memory>
#include <string>

#include "glog/logging.h"
#include "google/protobuf/service.h"
#include "grpc++/grpc++.h"
#include "grpc++/server_context.h"
#include "kess/rpc/brpc/server_context_helper.h"
#include "kess/rpc/grpc/server_context_helper.h"
#include "kess/rpc/krpc/server_context_helper.h"
#include "krpc/rpc_controller.h"

#include "dragon/src/core/common_reco_base.h"

namespace ks {
namespace platform {

/**
 * CommonRecoLeafRpcControllerInterface
 *
 * Cancel the gap between grpc/brpc/generic_rpc
 */
class CommonRecoRpcControllerInterface {
 public:
  virtual ~CommonRecoRpcControllerInterface() {}

  virtual bool IsRequestCancelled() const = 0;

  // time left for processing in ms
  virtual int32_t GetTimeLeft() const = 0;

  virtual RpcType Type() const = 0;

  // 获取 rpc 主调服务名称
  virtual const std::string &GetCaller() const = 0;

  // DummyRpcController 返回为 nullptr，业务方需自我保护
  virtual const ::grpc::ServerContext *GrpcServerContext() const {
    return nullptr;
  }

  virtual const ::ks::kess::rpc::ServerContext *GrpcStreamingServerContext() const {
    return nullptr;
  }

  virtual const ::brpc::Controller *BrpcController() const {
    return nullptr;
  }
};

/*
 * Default implementations for CommonRecoRpcControllerInterface
 */
class CommonRecoDummyRpcController : public CommonRecoRpcControllerInterface {
 public:
  explicit CommonRecoDummyRpcController(const std::string &caller = "") : caller_(caller) {}
  virtual ~CommonRecoDummyRpcController() {}

  bool IsRequestCancelled() const override {
    return false;
  }

  int32_t GetTimeLeft() const override {
    return std::numeric_limits<int32_t>::max();
  }

  RpcType Type() const override {
    return RpcType::UNKNOWN;
  }

  const std::string &GetCaller() const override {
    return caller_;
  }

 protected:
  const std::string caller_;
};

/*
 * The Implementation of CommonRecoRpcControllerInterface for Grpc
 */
class CommonRecoGrpcController : public CommonRecoDummyRpcController {
 public:
  explicit CommonRecoGrpcController(const ::grpc::ServerContext *server_context)
      : CommonRecoDummyRpcController(
            server_context ? ks::kess::rpc::grpc::ServerContextHelper::GetCallerService(server_context) : "")
      , server_context_(CHECK_NOTNULL(server_context)) {}
  virtual ~CommonRecoGrpcController() {}

  static std::unique_ptr<CommonRecoRpcControllerInterface> MakeRpcController(
      const ::grpc::ServerContext *server_context) {
    return server_context ? std::make_unique<CommonRecoGrpcController>(server_context)
                          : std::make_unique<CommonRecoDummyRpcController>();
  }

  bool IsRequestCancelled() const final {
    return server_context_->IsCancelled();
  }

  int32_t GetTimeLeft() const final {
    gpr_timespec deadline = server_context_->raw_deadline();
    return gpr_time_to_millis(gpr_time_sub(deadline, gpr_now(deadline.clock_type)));
  }

  RpcType Type() const final {
    return RpcType::GRPC;
  }

  const ::grpc::ServerContext *GrpcServerContext() const override {
    return server_context_;
  }

 private:
  const ::grpc::ServerContext *server_context_;
};

/*
 * The Implementation of CommonRecoRpcControllerInterface for streaming Grpc
 */
class CommonRecoGrpcStreamingController : public CommonRecoDummyRpcController {
 public:
  explicit CommonRecoGrpcStreamingController(const ::ks::kess::rpc::ServerContext *server_context)
      : CommonRecoDummyRpcController(server_context ? server_context->caller_service() : "")
      , server_context_(CHECK_NOTNULL(server_context)) {}
  virtual ~CommonRecoGrpcStreamingController() {}

  static std::unique_ptr<CommonRecoRpcControllerInterface> MakeRpcController(
      const ::ks::kess::rpc::ServerContext *server_context) {
    return server_context ? std::make_unique<CommonRecoGrpcStreamingController>(server_context)
                          : std::make_unique<CommonRecoDummyRpcController>();
  }

  bool IsRequestCancelled() const final {
    return server_context_->IsCancelled();
  }

  int32_t GetTimeLeft() const final {
    gpr_timespec deadline = server_context_->raw_deadline();
    return gpr_time_to_millis(gpr_time_sub(deadline, gpr_now(deadline.clock_type)));
  }

  RpcType Type() const final {
    return RpcType::GRPC;
  }

  const ::ks::kess::rpc::ServerContext *GrpcStreamingServerContext() const override {
    return server_context_;
  }

 private:
  const ::ks::kess::rpc::ServerContext *server_context_;
};

/*
 * The Implementation of CommonRecoRpcControllerInterface for generic RPC
 */
class CommonRecoGenericRpcController : public CommonRecoDummyRpcController {
 public:
  explicit CommonRecoGenericRpcController(const ::google::protobuf::RpcController *rpc_controller,
                                          const std::string &caller = "")
      : CommonRecoDummyRpcController(caller), rpc_controller_(CHECK_NOTNULL(rpc_controller)) {}
  virtual ~CommonRecoGenericRpcController() {}

  bool IsRequestCancelled() const final {
    return rpc_controller_->IsCanceled();
  }

  RpcType Type() const final {
    return RpcType::BRPC;
  }

 private:
  const ::google::protobuf::RpcController *rpc_controller_;
};

/*
 * The Implementation of CommonRecoRpcControllerInterface for Brpc
 */
class CommonRecoBrpcController : public CommonRecoGenericRpcController {
 public:
  explicit CommonRecoBrpcController(const ::brpc::Controller *cntl)
      : CommonRecoGenericRpcController(
            static_cast<const ::google::protobuf::RpcController *>(CHECK_NOTNULL(cntl)),
            cntl ? ks::kess::rpc::brpc::ServerContextHelper::GetCallerService(cntl) : "")
      , cntl_(cntl) {}
  virtual ~CommonRecoBrpcController() {}

  static std::unique_ptr<CommonRecoRpcControllerInterface> MakeRpcController(const ::brpc::Controller *cntl) {
    return cntl ? std::make_unique<CommonRecoBrpcController>(cntl)
                : std::make_unique<CommonRecoDummyRpcController>();
  }

  const ::brpc::Controller *BrpcController() const override {
    return cntl_;
  }

 private:
  const ::brpc::Controller *cntl_;
};

/*
 * The Implementation of CommonRecoRpcControllerInterface for Krpc
 */
class CommonRecoKrpcController : public CommonRecoDummyRpcController {
 public:
  explicit CommonRecoKrpcController(const ::krpc::RpcController *cntl)
      : CommonRecoDummyRpcController(cntl ? ks::kess::rpc::krpc::ServerContextHelper::GetCallerService(cntl)
                                          : "")
      , cntl_(CHECK_NOTNULL(cntl)) {}
  virtual ~CommonRecoKrpcController() {}

  static std::unique_ptr<CommonRecoRpcControllerInterface> MakeRpcController(
      const ::krpc::RpcController *cntl) {
    return cntl ? std::make_unique<CommonRecoKrpcController>(cntl)
                : std::make_unique<CommonRecoDummyRpcController>();
  }

  bool IsRequestCancelled() const final {
    return cntl_->IsCanceled();
  }

  RpcType Type() const final {
    return RpcType::KRPC;
  }

 private:
  const ::krpc::RpcController *cntl_;
};
}  // namespace platform
}  // namespace ks
