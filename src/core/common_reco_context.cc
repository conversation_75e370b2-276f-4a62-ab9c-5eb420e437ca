#include <memory>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"
#include "dragon/src/util/cpu_cost_util.h"
#include "dragon/src/util/id_mapping_util.h"
#include "dragon/src/util/streaming_handler.h"

DEFINE_bool(terminate_cancelled_request, true, "terminate cancelled request or not");
DEFINE_bool(zero_copy_subflow_output, false, "enable attr swap in context merge");
DEFINE_bool(clear_external_table_after_request, false, "clear external table at end of request");

#define HANDLE_EXTRA_ITEM_ATTR_READ_BY_KEY(DEFAULT_RETURN, FUNC)         \
  {                                                                      \
    if (!IsValid()) {                                                    \
      return DEFAULT_RETURN;                                             \
    }                                                                    \
    AttrValue *attr = GetItemAttr(attr_name);                            \
    if (!attr) return DEFAULT_RETURN;                                    \
    if (!attr->is_from_flat_index) {                                     \
      auto index = running_processor_table_->GetItemAttrIndex(item_key); \
      if (!index) {                                                      \
        ++attr->access_counter;                                          \
        return DEFAULT_RETURN;                                           \
      }                                                                  \
      return attr->FUNC(index.value());                                  \
    } else {                                                             \
      ++attr->access_counter;                                            \
      return DEFAULT_RETURN;                                             \
    }                                                                    \
  }

#define HANDLE_EXTRA_ITEM_ATTR_READ_BY_KEY_AND_ATTR(FUNC) \
  {                                                       \
    if (!item_attr || !IsValid()) {                       \
      return nullptr;                                     \
    }                                                     \
    auto *table = item_attr->GetOwner();                  \
    if (!item_attr->is_from_flat_index) {                 \
      auto index = table->GetItemAttrIndex(item_key);     \
      if (!index) {                                       \
        ++item_attr->access_counter;                      \
        return nullptr;                                   \
      }                                                   \
      return item_attr->FUNC(index.value());              \
    } else {                                              \
      ++item_attr->access_counter;                        \
      return nullptr;                                     \
    }                                                     \
  }

#define HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(FUNC)                 \
  {                                                                 \
    if (!item_attr || !IsValid()) {                                 \
      return absl::nullopt;                                         \
    }                                                               \
    auto *table = item_attr->GetOwner();                            \
    if (!item_attr->is_from_flat_index) {                           \
      auto index = table->GetItemAttrIndex(item_key);               \
      if (!index) {                                                 \
        ++item_attr->access_counter;                                \
        return absl::nullopt;                                       \
      }                                                             \
      return item_attr->FUNC(index.value(), nullptr);               \
    } else {                                                        \
      auto flat_index_addr = table->GetItemFlatIndexAddr(item_key); \
      return item_attr->FUNC(0, flat_index_addr);                   \
    }                                                               \
  }

#define HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(FUNC, ...)                              \
  {                                                                                    \
    auto *table = attr->GetOwner();                                                    \
    auto index = const_cast<AttrTable *>(table) -> GetOrInsertItemAttrIndex(item_key); \
    return attr->FUNC(index, ##__VA_ARGS__);                                           \
  }

#define HANDLE_ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(FUNC, ...) \
  { return attr->FUNC(item_result.GetAttrIndex(), ##__VA_ARGS__); }

#define HANDLE_ITEM_ATTR_WRITE_BY_KEY(FUNC, ...)                               \
  {                                                                            \
    AttrValue *attr = running_processor_table_->GetOrInsertAttr(attr_name);    \
    auto index = running_processor_table_->GetOrInsertItemAttrIndex(item_key); \
    return attr->FUNC(index, ##__VA_ARGS__);                                   \
  }

#define MOVE_COMMON_ATTR(VAR)                               \
  {                                                         \
    to_common_attr->VAR = std::move(from_common_attr->VAR); \
    break;                                                  \
  }

namespace ks {
namespace platform {

CommonRecoContext::CommonRecoContext() : rpc_controller_(new CommonRecoDummyRpcController()) {
  // 初始化 lua_state
  lua_state_ = luaL_newstate();
  CHECK(lua_state_);
  luaL_openlibs(lua_state_);
  // TODO(fangjianbing): 后续改为 c lib 方式引入
  lua::RegisterCFunctions(lua_state_);
  common_data_ = std::make_unique<AttrTable>("", true);
  common_data_->SetOwner(this);
  default_item_data_ = GetOrInsertDataTable("");
  running_processor_table_ = default_item_data_;
}

CommonRecoContext::~CommonRecoContext() {
  ClearContext();
  for (auto &df_pair : item_data_) {
    if (!df_pair.second->IsExternal()) {
      if (df_pair.second->IsOwnedBy(this)) {
        delete df_pair.second;
      }
    }
  }
  if (lua_state_) lua_close(lua_state_);
}

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
void CommonRecoContext::ShadowCloneItemAttrTable(ReadableRecoContextInterface *context,
                                                 const std::string &table_name) {
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);
  auto *from_table = from_context->GetOrInsertDataTable(table_name);
  item_data_.insert({table_name, from_table});
}
#endif

void CommonRecoContext::ClearContext() {
  if (AttrIOCheckUtil::IsEnabled()) {
    AttrIOCheckUtil::Report(GetProcessCounter(), &invalid_io_counter_);
  }

  // 请求计数器加 1
  process_counter_++;

  if (FLAGS_attr_counter_interval > 0) {
    enable_attr_check = (process_counter_ % FLAGS_attr_counter_interval == 0);
  } else {
    enable_attr_check = true;
  }

  if (FLAGS_clear_external_table_after_request) {
    for (auto it = item_data_.begin(); it != item_data_.end();) {
      if (it->second->IsExternal()) {
        it = item_data_.erase(it);
      } else {
        ++it;
      }
    }
    for (auto *table : replaced_internal_tables_) {
      if (table != nullptr) {
        item_data_.insert_or_assign(table->name(), table);
      }
    }
    replaced_internal_tables_.clear();
  }
  for (auto &df_pair : item_data_) {
    if (!df_pair.second->IsExternal()) {
      if (df_pair.second->IsOwnedBy(this)) {
        df_pair.second->Clear(GetRequestType(), process_counter_);
      }
    }
  }

  if (common_data_->IsOwnedBy(this)) {
    common_data_->Clear(GetRequestType(), process_counter_);
  }

  if (reco_browse_set_result_) {
    base::perfutil::PerfUtilWrapper::CountLogStash(reco_browse_set_result_->FilterByPidCount(), kPerfNs,
                                                   "RecoBrowseSet.filterByPidCount",
                                                   GlobalHolder::GetServiceIdentifier(), GetRequestType());
    base::perfutil::PerfUtilWrapper::CountLogStash(reco_browse_set_result_->FilterByFilterCount(), kPerfNs,
                                                   "RecoBrowseSet.filterByFilterCount",
                                                   GlobalHolder::GetServiceIdentifier(), GetRequestType());
    reco_browse_set_result_.reset();
  }

  for (auto &[k, waiter] : local_async_waiters_) {
    waiter->Clear();
  }
  for (auto &[k, waiter] : batch_waiters_) {
    waiter->Clear();
  }
  for (std::vector<std::string> *vec : async_upstream_processors_) {
    vec->clear();
  }

  if (need_pipeline_cpu_cost_) {
    pipeline_cpu_cost_ns_.Clear();
    need_pipeline_cpu_cost_ = false;
  }

  if (need_table_cpu_cost_) {
    table_cpu_cost_ns_.Clear();
    need_table_cpu_cost_ = false;
  }

  execution_status_ = ExecutionStatus::UNKNOWN;

  user_id_ = 0;
  request_time_ms_ = 0;
  device_id_.clear();
  request_type_.clear();
  request_id_.clear();
  request_num_ = 0;
  debug_ = false;
  request_start_time_ = 0;

  common_attrs_from_request_.clear();

  force_insert_map_.clear();
  browse_set_.clear();

  request_ = nullptr;
  flatten_request_ = nullptr;
  valid_ = true;
  running_processor_ = nullptr;
  running_processor_table_ = default_item_data_;

  subflow_step_infos_count_ = 0;
  traceback_sequence_.clear();
  need_step_info_ = false;
  should_compress_item_ = false;
  should_drop_studio_field_ = true;
  traceback_compress_mode_ = PackedItemAttrValue_CompressMode_NO_COMPRESS;
  traceback_item_attrs_.clear();
  traceback_common_attrs_.clear();
  traceback_processors_.reset();
  traceback_processors_blacklist_.reset();
  result_changed_ = false;
  accumulated_traceback_tables_.clear();
  accumulated_traceback_item_attrs_.clear();
  traceback_collect_mode_ = UNKNOWN_COLLECT_MODE;
  traceback_white_list_user_report_all_processors_ = false;

  streaming_loop_index_ = -1;

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  common_data_backup_ = nullptr;
#endif
}

void CommonRecoContext::ClearSubflowStepInfo() {
  for (int i = 0; i < subflow_step_infos_.size(); i++) {
    subflow_step_infos_[i]->Clear();
  }
  subflow_step_infos_.clear();
  subflow_step_infos_count_ = 0;
}

CommonRecoStepInfo *CommonRecoContext::GetNewStepInfo() {
  if (subflow_step_infos_count_ >= subflow_step_infos_.size()) {
    subflow_step_infos_.push_back(std::make_unique<CommonRecoStepInfo>());
  } else {
    subflow_step_infos_[subflow_step_infos_count_]->Clear();
  }
  return subflow_step_infos_[subflow_step_infos_count_++].get();
}

int CommonRecoContext::GetSubflowStepInfosCount() {
  return subflow_step_infos_count_;
}

void CommonRecoContext::IncrTracebackSequence() {
  if (traceback_sequence_.empty()) {
    traceback_sequence_.push_back(0);
  } else {
    traceback_sequence_.back()++;
  }
}

const std::vector<int> &CommonRecoContext::GetTracebackSequence() const {
  return traceback_sequence_;
}

void CommonRecoContext::SetTracebackSequence(std::vector<int> &&sequence) {
  traceback_sequence_ = std::move(sequence);
}

bool CommonRecoContext::GetNeedStepInfo() const {
  return need_step_info_;
}

void CommonRecoContext::SetNeedStepInfo(bool value) {
  need_step_info_ = value;
}

bool CommonRecoContext::GetShouldCompressItem() {
  return should_compress_item_;
}

void CommonRecoContext::SetShouldCompressItem(bool value) {
  should_compress_item_ = value;
}

const std::string &CommonRecoContext::GetTracebackDataVersion() const {
  return traceback_data_version_;
}

void CommonRecoContext::SetTracebackDataVersion(const std::string &data_version) {
  traceback_data_version_ = data_version;
}

bool CommonRecoContext::GetShouldDropStudioField() {
  return should_drop_studio_field_;
}

void CommonRecoContext::SetShouldDropStudioField(bool value) {
  should_drop_studio_field_ = value;
}

PackedItemAttrValue::CompressMode CommonRecoContext::GetTracebackCompressMode() {
  return traceback_compress_mode_;
}

void CommonRecoContext::SetTracebackCompressMode(PackedItemAttrValue::CompressMode compress_mode) {
  traceback_compress_mode_ = compress_mode;
}

const std::vector<std::string> *CommonRecoContext::GetTracebackItemAttrs() const {
  if (traceback_item_attrs_.size() == 0) return nullptr;
  return &traceback_item_attrs_;
}

void CommonRecoContext::SetTracebackItemAttrs(absl::Span<const std::string> return_item_attrs) {
  traceback_item_attrs_.assign(return_item_attrs.begin(), return_item_attrs.end());
  if (traceback_util::CompareTracebackDataVersion(GetTracebackDataVersion(), "v3.0.0") >= 0) {
    for (auto &item_attr : traceback_item_attrs_) {
      if (item_attr.find("::") == std::string::npos) {
        item_attr = "::" + item_attr;
      }
    }
  }
}

const std::vector<std::string> *CommonRecoContext::GetTracebackCommonAttrs() const {
  if (traceback_common_attrs_.size() == 0) return nullptr;
  return &traceback_common_attrs_;
}

void CommonRecoContext::SetTracebackCommonAttrs(absl::Span<const std::string> return_common_attrs) {
  traceback_common_attrs_.assign(return_common_attrs.begin(), return_common_attrs.end());
}

std::shared_ptr<folly::F14FastSet<std::string>> CommonRecoContext::GetTracebackProcessors() const {
  return traceback_processors_;
}

void CommonRecoContext::SetTracebackProcessors(
    std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors) {
  traceback_processors_ = traceback_processors;
}

std::shared_ptr<folly::F14FastSet<std::string>> CommonRecoContext::GetTracebackProcessorsBlacklist() const {
  return traceback_processors_blacklist_;
}

void CommonRecoContext::SetTracebackProcessorsBlacklist(
    std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors_blacklist) {
  traceback_processors_blacklist_ = traceback_processors_blacklist;
}
CommonRecoStepInfo *CommonRecoContext::GetProcessorStepInfo(int index) {
  CHECK(index < subflow_step_infos_count_) << ", processor step info index out of range";
  return subflow_step_infos_[index].get();
}

bool CommonRecoContext::IsTracebackResultChanged() const {
  return result_changed_;
}

void CommonRecoContext::SetTracebackResultChanged(bool value) {
  result_changed_ = value;
}

void CommonRecoContext::AddTracebackResultChanged(const std::string &table_name) {
  accumulated_traceback_tables_.insert(table_name);
}

folly::F14FastSet<std::string> *CommonRecoContext::GetAccumulatedTracebackTables() {
  return &accumulated_traceback_tables_;
}

void CommonRecoContext::AddAccumulatedTracebackItemAttrs(const std::string &attr_name) {
  accumulated_traceback_item_attrs_.insert(attr_name);
}

folly::F14FastSet<std::string> *CommonRecoContext::GetAccumulatedTracebackItemAttrs() {
  return &accumulated_traceback_item_attrs_;
}

void CommonRecoContext::SetTracebackCollectMode(CollectMode collect_mode) {
  traceback_collect_mode_ = collect_mode;
}
CollectMode CommonRecoContext::GetTracebackCollectMode() const {
  return traceback_collect_mode_;
}

void CommonRecoContext::SetTracebackWhiteListUserReportAllProcessors(bool report_all_processors) {
  traceback_white_list_user_report_all_processors_ = report_all_processors;
}

bool CommonRecoContext::GetTracebackWhiteListUserReportAllProcessors() const {
  return traceback_white_list_user_report_all_processors_;
}

const CommonRecoRequest *CommonRecoContext::GetRequest() const {
  return request_;
}

std::shared_ptr<void> CommonRecoContext::GetSharedRequest() {
  return shared_request_;
}

ExecutionStatus CommonRecoContext::GetExecutionStatus() const {
  return execution_status_.load();
}

void CommonRecoContext::SetExecutionStatus(ExecutionStatus status) {
  execution_status_ = status;
}

uint64 CommonRecoContext::GetUserId() const {
  return user_id_;
}

const std::string &CommonRecoContext::GetDeviceId() const {
  return device_id_;
}

const std::string &CommonRecoContext::GetRequestType() const {
  return request_type_;
}

int64 CommonRecoContext::GetRequestTime() const {
  return request_time_ms_;
}

int64 CommonRecoContext::GetProcessCounter() const {
  return process_counter_;
}

int64 CommonRecoContext::GetRequestNum() const {
  return request_num_;
}

const std::string &CommonRecoContext::GetRequestId() const {
  return request_id_;
}

bool CommonRecoContext::IsDebugRequest() const {
  return debug_;
}

bool CommonRecoContext::NeedTraceback() const {
  return need_traceback_;
}

const std::unordered_set<std::string> &CommonRecoContext::GetCommonAttrsInRequest() const {
  return common_attrs_from_request_;
}

const std::unordered_set<std::string> &CommonRecoContext::GetItemAttrsInRequest() const {
  return item_attrs_from_request_;
}

const CommonRecoBaseProcessor *CommonRecoContext::GetRunningProcessor() const {
  return running_processor_;
}

const RecoProcessorMetaData *CommonRecoContext::GetRunningProcessorMetaData() const {
  static RecoProcessorMetaData default_processor_meta_data = {};
  if (running_processor_) {
    return running_processor_->GetMetaData();
  } else {
    return &default_processor_meta_data;
  }
}

void CommonRecoContext::SetRunningProcessor(const void *processor) {
  auto p = static_cast<const CommonRecoBaseProcessor *>(processor);
  running_processor_ = p;
  if (p && !p->GetTableName().empty()) {
    running_processor_table_ = GetOrInsertDataTable(p->GetTableName());
  } else {
    running_processor_table_ = default_item_data_;
  }
}

void CommonRecoContext::SetMainTable(const std::string &table_name) {
  running_processor_table_ = GetOrInsertDataTable(table_name);
}

int64 CommonRecoContext::GetElapsedTime() const {
  return base::GetTimestamp() - request_start_time_;
}

void CommonRecoContext::ResetStageStartTime(int64 stage_time) {
  stage_start_time_ = stage_time == 0 ? base::GetTimestamp() : stage_time;
}

int64 CommonRecoContext::RecordStageInfo(const std::string &stage_name) {
  return RecordStageInfo(stage_name, running_processor_table_->GetName());
}

int64 CommonRecoContext::RecordStageInfo(const std::string &stage_name, const std::string &table_name) {
  auto *table = GetOrInsertDataTable(table_name);
  int64 now_time = base::GetTimestamp();
  int64 duration = now_time - stage_start_time_;
  base::perfutil::PerfUtilWrapper::IntervalLogStash(duration, kPerfNs, "stage_run_time",
                                                    GlobalHolder::GetServiceIdentifier(), GetRequestType(),
                                                    stage_name, table_name);
  base::perfutil::PerfUtilWrapper::IntervalLogStash(table->GetCommonRecoResults().size(), kPerfNs,
                                                    "stage_item_num", GlobalHolder::GetServiceIdentifier(),
                                                    GetRequestType(), stage_name, table_name);
  stage_start_time_ = now_time;
  return duration;
}

int64 CommonRecoContext::GetStageStartTime() const {
  return stage_start_time_;
}

CommonRecoResult CommonRecoContext::NewCommonRecoResult(uint64 item_key, int reason, double score,
                                                        int channel,
                                                        absl::optional<absl::string_view> table_name) {
  if (table_name && table_name->data()) {
    auto *data_frame = GetOrInsertDataTable(table_name.value());
    return data_frame->NewCommonRecoResult(item_key, reason, score, channel);
  } else {
    return running_processor_table_->NewCommonRecoResult(item_key, reason, score, channel);
  }
}

void CommonRecoContext::SetResultReasonAttr(const CommonRecoResult &result, bool override_reason_attr) {
  // 在这块进行记录 item 的 reason 到 _REASON_ item_attr 中，只记录第一个 reason
  if (!reason_attr_) {
    reason_attr_ = GetOrInsertItemAttr(kReasonAttr);
  }
  result.SetIntAttr(reason_attr_, result.reason, !override_reason_attr);
}

// NOTE(zhaoyang09): override_reason_attr 是否可以复写系统默认 _REASON_ attr
CommonRecoResult &CommonRecoContext::AddCommonRecoResult(uint64 item_key, int reason, double score,
                                                         int channel, bool override_reason_attr) {
  auto &result = running_processor_table_->AddCommonRecoResult(item_key, reason, score, channel, nullptr,
                                                               override_reason_attr);
  return result;
}

// NOTE(zhaoyang09): override_reason_attr 是否可以复写系统默认 _REASON_ attr
CommonRecoResult &CommonRecoContext::AddCommonRecoResultToTable(AttrTable *table, uint64 item_key, int reason,
                                                                double score, int channel,
                                                                bool override_reason_attr) {
  return table->AddCommonRecoResult(item_key, reason, score, channel, nullptr, override_reason_attr);
}

CommonRecoResult &CommonRecoContext::AddCommonRecoResultToTable(absl::string_view table_name, uint64 item_key,
                                                                int reason, double score, int channel,
                                                                bool override_reason_attr) {
  auto *table = GetOrInsertDataTable(table_name);
  return table->AddCommonRecoResult(item_key, reason, score, channel, nullptr, override_reason_attr);
}

void CommonRecoContext::AddCommonRecoResultFromRequestToTable(AttrTable *table, CommonRecoResult *result) {
  table->AddCommonRecoResultFromRequest(result);
}

CommonRecoResult &CommonRecoContext::AddCommonRecoResult(const CommonRecoResult &result,
                                                         bool override_reason_attr) {
  auto &new_result = running_processor_table_->AddCommonRecoResult(
      result.item_key, result.reason, result.score, result.channel, result.GetFlatIndexItemAddr());
  return new_result;
}

const std::vector<CommonRecoResult> &CommonRecoContext::GetCommonRecoResults() {
  return running_processor_table_->GetCommonRecoResults();
}

const std::vector<CommonRecoResult> &CommonRecoContext::GetCommonRecoResults(absl::string_view table_name) {
  auto *table = GetOrInsertDataTable(table_name);
  return table->GetCommonRecoResults();
}

// CommonAttr Get APIs
AttrType CommonRecoContext::GetCommonAttrType(absl::string_view attr_name) const {
  auto *attr = GetCommonAttr(attr_name);
  return attr ? attr->value_type : AttrType::UNKNOWN;
}

bool CommonRecoContext::HasCommonAttr(absl::string_view attr_name) const {
  auto *attr = GetCommonAttr(attr_name);
  return attr && attr->HasValue();
}

absl::optional<int64> CommonRecoContext::GetIntCommonAttr(absl::string_view attr_name) const {
  return GetSingleCommonAttr<int64>(attr_name);
}

absl::optional<int64> CommonRecoContext::GetIntCommonAttr(const CommonAttr *attr) const {
  return GetSingleCommonAttr<int64>(attr);
}

absl::optional<double> CommonRecoContext::GetDoubleCommonAttr(absl::string_view attr_name) const {
  return GetSingleCommonAttr<double>(attr_name);
}

absl::optional<double> CommonRecoContext::GetDoubleCommonAttr(const CommonAttr *attr) const {
  return GetSingleCommonAttr<double>(attr);
}

absl::optional<absl::string_view> CommonRecoContext::GetStringCommonAttr(absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_common_attrs, absl::nullopt);
  auto *attr = GetCommonAttr(attr_name);
  return attr ? attr->GetStringValue() : absl::nullopt;
}

absl::optional<absl::string_view> CommonRecoContext::GetStringCommonAttr(const CommonAttr *attr) const {
  CHECK_INVALID_IO_BY_ATTR(attr, input_common_attrs, absl::nullopt);
  return attr ? attr->GetStringValue() : absl::nullopt;
}

absl::optional<absl::Span<const int64>> CommonRecoContext::GetIntListCommonAttr(
    absl::string_view attr_name) const {
  return GetListCommonAttr<int64>(attr_name);
}

absl::optional<absl::Span<const int64>> CommonRecoContext::GetIntListCommonAttr(
    const CommonAttr *attr) const {
  return GetListCommonAttr<int64>(attr);
}

absl::optional<absl::Span<const double>> CommonRecoContext::GetDoubleListCommonAttr(
    absl::string_view attr_name) const {
  return GetListCommonAttr<double>(attr_name);
}

absl::optional<absl::Span<const double>> CommonRecoContext::GetDoubleListCommonAttr(
    const CommonAttr *attr) const {
  return GetListCommonAttr<double>(attr);
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetStringListCommonAttr(
    absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_common_attrs, absl::nullopt);
  auto *attr = GetCommonAttr(attr_name);
  return attr ? attr->GetStringListValue() : absl::nullopt;
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetStringListCommonAttr(
    const CommonAttr *attr) const {
  CHECK_INVALID_IO_BY_ATTR(attr, input_common_attrs, absl::nullopt);
  return attr ? attr->GetStringListValue() : absl::nullopt;
}

const boost::any *CommonRecoContext::GetExtraCommonAttr(absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_common_attrs, nullptr);
  if (!IsValid()) {
    return nullptr;
  }
  auto *attr = GetCommonAttr(attr_name);
  return attr ? attr->GetExtraValue() : nullptr;
}

const boost::any *CommonRecoContext::GetExtraCommonAttr(const CommonAttr *attr) const {
  CHECK_INVALID_IO_BY_ATTR(attr, input_common_attrs, nullptr);
  if (!IsValid()) {
    return nullptr;
  }
  return attr ? attr->GetExtraValue() : nullptr;
}

// CommonAttr Set APIs
bool CommonRecoContext::ClearCommonAttr(absl::string_view attr_name, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetCommonAttr(attr_name);
  if (!attr) return true;
  return attr->ClearValue(0, check_overwrite);
}

bool CommonRecoContext::ClearCommonAttr(CommonAttr *attr, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  if (!attr) return true;
  return attr->ClearValue(0, check_overwrite);
}

bool CommonRecoContext::SetIntCommonAttr(absl::string_view attr_name, int64 value, bool if_not_exist,
                                         bool check_overwrite) {
  return SetSingleCommonAttr<int64>(attr_name, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetIntCommonAttr(CommonAttr *attr, int64 value, bool if_not_exist,
                                         bool check_overwrite) {
  return SetSingleCommonAttr<int64>(attr, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleCommonAttr(absl::string_view attr_name, double value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<double>(attr_name, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleCommonAttr(CommonAttr *attr, double value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<double>(attr, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32CommonAttr(absl::string_view attr_name, float value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<float>(attr_name, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32CommonAttr(CommonAttr *attr, float value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<float>(attr, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16CommonAttr(absl::string_view attr_name, float16_t value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<float16_t>(attr_name, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16CommonAttr(CommonAttr *attr, float16_t value, bool if_not_exist,
                                            bool check_overwrite) {
  return SetSingleCommonAttr<float16_t>(attr, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringCommonAttr(absl::string_view attr_name, std::string value, bool if_not_exist,
                                            bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetOrInsertCommonAttr(attr_name);
  return attr->SetStringValue(std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringCommonAttr(CommonAttr *attr, std::string value, bool if_not_exist,
                                            bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  return attr ? attr->SetStringValue(std::move(value), if_not_exist, check_overwrite) : false;
}

bool CommonRecoContext::ResetIntListCommonAttr(absl::string_view attr_name, int capacity, bool if_not_exist,
                                               bool check_overwrite) {
  return ResetListCommonAttr<int64>(attr_name, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetIntListCommonAttr(CommonAttr *attr, int capacity, bool if_not_exist,
                                               bool check_overwrite) {
  return ResetListCommonAttr<int64>(attr, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetDoubleListCommonAttr(absl::string_view attr_name, int capacity,
                                                  bool if_not_exist, bool check_overwrite) {
  return ResetListCommonAttr<double>(attr_name, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetDoubleListCommonAttr(CommonAttr *attr, int capacity, bool if_not_exist,
                                                  bool check_overwrite) {
  return ResetListCommonAttr<double>(attr, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetStringListCommonAttr(absl::string_view attr_name, int capacity,
                                                  bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetOrInsertCommonAttr(attr_name);
  return attr->ResetStringListValue(capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetStringListCommonAttr(CommonAttr *attr, int capacity, bool if_not_exist,
                                                  bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  return attr ? attr->ResetStringListValue(capacity, if_not_exist, check_overwrite) : false;
}

bool CommonRecoContext::AppendIntListCommonAttr(absl::string_view attr_name, int64 value) {
  return AppendListCommonAttr<int64>(attr_name, value);
}

bool CommonRecoContext::AppendIntListCommonAttr(CommonAttr *attr, int64 value) {
  return AppendListCommonAttr<int64>(attr, value);
}

bool CommonRecoContext::AppendDoubleListCommonAttr(absl::string_view attr_name, double value) {
  return AppendListCommonAttr<double>(attr_name, value);
}

bool CommonRecoContext::AppendDoubleListCommonAttr(CommonAttr *attr, double value) {
  return AppendListCommonAttr<double>(attr, value);
}

bool CommonRecoContext::AppendStringListCommonAttr(absl::string_view attr_name, std::string value) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetOrInsertCommonAttr(attr_name);
  return attr->AppendStringListValue(std::move(value));
}

bool CommonRecoContext::AppendStringListCommonAttr(CommonAttr *attr, std::string value) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  return attr ? attr->AppendStringListValue(std::move(value)) : false;
}

bool CommonRecoContext::SetIntListCommonAttr(absl::string_view attr_name, std::vector<int64> &&value,
                                             bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<int64>(attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetIntListCommonAttr(CommonAttr *attr, std::vector<int64> &&value, bool if_not_exist,
                                             bool check_overwrite) {
  return SetListCommonAttr<int64>(attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleListCommonAttr(absl::string_view attr_name, std::vector<double> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<double>(attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleListCommonAttr(CommonAttr *attr, std::vector<double> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<double>(attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ListCommonAttr(absl::string_view attr_name, std::vector<float> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<float>(attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ListCommonAttr(CommonAttr *attr, std::vector<float> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<float>(attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16ListCommonAttr(absl::string_view attr_name, std::vector<float16_t> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<float16_t>(attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16ListCommonAttr(CommonAttr *attr, std::vector<float16_t> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  return SetListCommonAttr<float16_t>(attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringListCommonAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetOrInsertCommonAttr(attr_name);
  return attr->SetStringListValue(std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringListCommonAttr(CommonAttr *attr, std::vector<std::string> &&value,
                                                bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  return attr ? attr->SetStringListValue(std::move(value), if_not_exist, check_overwrite) : false;
}

bool CommonRecoContext::SetExtraCommonAttr(absl::string_view attr_name, boost::any &&value, bool if_not_exist,
                                           bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
  auto *attr = GetOrInsertCommonAttr(attr_name);
  return attr->SetExtraValue(std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetExtraCommonAttr(CommonAttr *attr, boost::any &&value, bool if_not_exist,
                                           bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
  return attr ? attr->SetExtraValue(std::move(value), if_not_exist, check_overwrite) : false;
}

bool CommonRecoContext::InitItemAttr(absl::string_view attr_name, AttrType attr_type, size_t max_attr_index) {
  auto *attr = GetOrInsertItemAttr(attr_name);
  // NOTE(zhaoyang09): flat_index 无法进行预分配, 跳过。
  if (attr->is_from_flat_index) {
    return true;
  }
  return attr->ResizeItemAttrValue(attr_type, max_attr_index);
}

// NOTE(zhaoyang09): read_only 表示 attr 是否修改为只读模式
void CommonRecoContext::CloneCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                        bool shadow_copy, bool read_only) {
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);
  auto *common_attr_ptr = from_context->GetCommonAttrAccessor(attr_name);
  if (shadow_copy) {
    if (!ShadowCloneCommonAttr(attr_name, common_attr_ptr, read_only)) {
      CL_LOG_WARNING("attr_clone_fail", absl::StrCat("common attr already exists: ", attr_name))
          << "failed to clone common attr: " << attr_name << ", already exists in subflow";
    }
  } else {
    DeepCloneCommonAttr(common_attr_ptr);
  }
}

std::shared_ptr<std::unordered_set<std::string>> CommonRecoContext::GetFixedAttrs() const {
  return fixed_common_attrs_from_request_;
}

bool CommonRecoContext::ForceSendTraceback() const {
  auto tag = GetIntCommonAttr("_dryrun_with_prophet_enable");
  return tag.value_or(0);
}

void CommonRecoContext::CloneFixedCommonAttr(CommonRecoContext *from_context, bool shadow_copy) {
  auto fixed_from_context = from_context->GetFixedAttrs();
  for (const auto &attr_name : *fixed_from_context) {
    CloneCommonAttr(from_context, attr_name, shadow_copy, true);
  }
  if (shadow_copy) {
    fixed_common_attrs_from_request_ = fixed_from_context;
  } else {
    fixed_common_attrs_from_request_ = std::make_shared<std::unordered_set<std::string>>(*fixed_from_context);
  }
}

// NOTE(zhaoyang09): read_only 表示 attr 是否修改为只读模式
void CommonRecoContext::CloneItemAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                                      bool shadow_copy, bool read_only) {
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);
  auto *item_attr_ptr = from_context->GetItemAttrAccessor(attr_name);
  if (shadow_copy) {
    if (!ShadowCloneItemAttr(attr_name, item_attr_ptr, read_only)) {
      CL_LOG_WARNING("attr_clone_fail", absl::StrCat("item attr already exists: ", attr_name))
          << "failed to clone item attr: " << attr_name << ", already exists in subflow";
    }
  } else {
    DeepCloneItemAttr(item_attr_ptr);
  }
}

void CommonRecoContext::CloneTable(ReadableRecoContextInterface *context,
                                   const std::vector<std::string> &attrs, std::string table_name,
                                   bool shadow_copy, bool read_only) {
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);
  auto *from_table = from_context->GetOrInsertDataTable(table_name);
  auto *to_table = GetOrInsertDataTable(table_name);

  auto *from_results = from_table->GetRecoResults();
  to_table->CloneTargetResults(from_table, from_results->begin(), from_results->end());

  for (const auto &attr_name : attrs) {
    auto *from_item_attr = from_table->GetOrInsertAttr(attr_name);
    if (shadow_copy) {
      auto *to_item_attr = to_table->GetOrBorrowAttr(from_item_attr);
      if (to_item_attr != from_item_attr) {
        CL_LOG_ERROR("attr_clone_fail", absl::StrCat("item attr already exists: ", attr_name))
            << "failed to clone item attr: " << attr_name << ", already exists in subflow";
      } else {
        to_item_attr->IncrRefCount();
        if (read_only) {
          to_item_attr->MarkReadOnly();
        }
      }
    } else {
      auto *to_item_attr = to_table->GetOrInsertAttr(attr_name);
      to_item_attr->DeepCloneFrom(from_item_attr);
    }
  }
}

// 用于完整的克隆结果集
bool CommonRecoContext::CloneResults(CommonRecoContext *from_context) {
  return CloneTargetResults(from_context, from_context->GetCommonRecoResults().begin(),
                            from_context->GetCommonRecoResults().end());
}

// 用于带迭代器的克隆结果集
bool CommonRecoContext::CloneTargetResults(CommonRecoContext *from_context, RecoResultConstIter begin,
                                           RecoResultConstIter end) {
  if (from_context == nullptr) {
    return false;
  }
  auto *from_table = from_context->GetOrInsertDataTable(running_processor_table_->GetName());
  return running_processor_table_->CloneTargetResults(from_table, begin, end);
}

bool CommonRecoContext::DeepCloneCommonAttr(const CommonAttr *from) {
  auto *attr = GetOrInsertCommonAttr(from->name());
  return attr->DeepCloneFrom(from);
}

bool CommonRecoContext::ShadowCloneCommonAttr(absl::string_view attr_name, CommonAttr *from, bool read_only) {
  auto *attr = common_data_->GetOrBorrowAttr(from);
  if (attr != from) {
    return false;
  }
  attr->IncrRefCount();
  if (read_only) {
    attr->MarkReadOnly();
  }
  return true;
}

bool CommonRecoContext::DeepCloneItemAttr(const ItemAttr *from) {
  auto *attr = GetOrInsertItemAttr(from->name());
  return attr->DeepCloneFrom(from);
}

bool CommonRecoContext::ShadowCloneItemAttr(absl::string_view attr_name, ItemAttr *from, bool read_only) {
  auto *attr = running_processor_table_->GetOrBorrowAttr(from);
  if (attr != from) {
    return false;
  }
  attr->IncrRefCount();
  if (read_only) {
    attr->MarkReadOnly();
  }
  return true;
}

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
// NOTE(caohongjin): subflow 嵌套情况下保持传递性, 同时避免循环引用
void CommonRecoContext::BackupCommonAttrTable(ReadableRecoContextInterface *context) {
  CommonRecoContext *from_context = static_cast<CommonRecoContext *>(context);
  if (from_context->common_data_backup_) {
    common_data_backup_ = from_context->common_data_backup_;
  } else {
    common_data_backup_ = from_context->common_data_.get();
  }
  if (common_data_backup_ == common_data_.get()) {
    common_data_backup_ = nullptr;
  }
}
#endif

// ItemAttr Get APIs
AttrType CommonRecoContext::GetItemAttrType(absl::string_view attr_name) const {
  auto *attr = GetItemAttr(attr_name);
  return attr ? attr->value_type : AttrType::UNKNOWN;
}

bool CommonRecoContext::HasItemAttr(absl::string_view attr_name) const {
  auto *attr = GetItemAttr(attr_name);
  return attr && attr->value_type != AttrType::UNKNOWN;
}

bool CommonRecoContext::HasItemAttr(uint64 item_key, absl::string_view attr_name) const {
  ITEM_ATTR_READ_BY_KEY(false, HasValue);
}

bool CommonRecoContext::HasItemAttr(const CommonRecoResult &item_result, const ItemAttr *item_attr) const {
  ITEM_ATTR_READ_BY_RESULT_AND_ATTR(false, HasValue);
}

absl::optional<int64> CommonRecoContext::GetIntItemAttr(uint64 item_key, absl::string_view attr_name) const {
  if (!enable_attr_check) {
    return GetSingleItemAttrWithoutCheck<int64>(item_key, attr_name);
  } else {
    return GetSingleItemAttr<int64>(item_key, attr_name);
  }
}

absl::optional<int64> CommonRecoContext::GetIntItemAttr(const CommonRecoResult &item_result,
                                                        const ItemAttr *item_attr) const {
  if (!enable_attr_check) {
    return GetSingleItemAttrWithoutCheck<int64>(item_result, item_attr);
  } else {
    return GetSingleItemAttr<int64>(item_result, item_attr);
  }
}

absl::optional<int64> CommonRecoContext::GetIntItemAttr(uint64 item_key, const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetSingularValue<int64>);
}

absl::optional<double> CommonRecoContext::GetDoubleItemAttr(uint64 item_key,
                                                            absl::string_view attr_name) const {
  if (!enable_attr_check) {
    return GetSingleItemAttrWithoutCheck<double>(item_key, attr_name);
  } else {
    return GetSingleItemAttr<double>(item_key, attr_name);
  }
}

absl::optional<double> CommonRecoContext::GetDoubleItemAttr(const CommonRecoResult &item_result,
                                                            const ItemAttr *item_attr) const {
  if (!enable_attr_check) {
    return GetSingleItemAttrWithoutCheck<double>(item_result, item_attr);
  } else {
    return GetSingleItemAttr<double>(item_result, item_attr);
  }
}

absl::optional<double> CommonRecoContext::GetDoubleItemAttr(uint64 item_key,
                                                            const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetSingularValue<double>);
}

absl::optional<absl::string_view> CommonRecoContext::GetDefaultStringItemAttr(
    absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
  auto *item_attr = GetItemAttr(attr_name);
  return item_attr ? item_attr->GetDefaultStringValue() : absl::nullopt;
}

absl::optional<absl::string_view> CommonRecoContext::GetDefaultStringItemAttr(
    const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  return item_attr ? item_attr->GetDefaultStringValue() : absl::nullopt;
}

absl::optional<absl::string_view> CommonRecoContext::GetStringItemAttr(uint64 item_key,
                                                                       absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
  ITEM_ATTR_READ_BY_KEY(absl::nullopt, GetStringValue);
}

absl::optional<absl::string_view> CommonRecoContext::GetStringItemAttr(const CommonRecoResult &item_result,
                                                                       const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  ITEM_ATTR_READ_BY_RESULT_AND_ATTR(absl::nullopt, GetStringValue);
}

absl::optional<absl::string_view> CommonRecoContext::GetStringItemAttr(uint64 item_key,
                                                                       const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetStringValue);
}

absl::optional<absl::Span<const int64>> CommonRecoContext::GetIntListItemAttr(
    uint64 item_key, absl::string_view attr_name) const {
  return GetListItemAttr<int64>(item_key, attr_name);
}

absl::optional<absl::Span<const int64>> CommonRecoContext::GetIntListItemAttr(
    const CommonRecoResult &item_result, const ItemAttr *item_attr) const {
  return GetListItemAttr<int64>(item_result, item_attr);
}

absl::optional<absl::Span<const int64>> CommonRecoContext::GetIntListItemAttr(
    uint64 item_key, const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetListValue<int64>);
}

absl::optional<absl::Span<const double>> CommonRecoContext::GetDoubleListItemAttr(
    uint64 item_key, absl::string_view attr_name) const {
  return GetListItemAttr<double>(item_key, attr_name);
}

absl::optional<absl::Span<const double>> CommonRecoContext::GetDoubleListItemAttr(
    const CommonRecoResult &item_result, const ItemAttr *item_attr) const {
  return GetListItemAttr<double>(item_result, item_attr);
}

absl::optional<absl::Span<const double>> CommonRecoContext::GetDoubleListItemAttr(
    uint64 item_key, const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetListValue<double>);
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetDefaultStringListItemAttr(
    absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
  auto *item_attr = GetItemAttr(attr_name);
  return item_attr ? item_attr->GetDefaultStringListValue() : absl::nullopt;
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetDefaultStringListItemAttr(
    const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  return item_attr ? item_attr->GetDefaultStringListValue() : absl::nullopt;
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetStringListItemAttr(
    uint64 item_key, absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
  ITEM_ATTR_READ_BY_KEY(absl::nullopt, GetStringListValue);
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetStringListItemAttr(
    const CommonRecoResult &item_result, const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  ITEM_ATTR_READ_BY_RESULT_AND_ATTR(absl::nullopt, GetStringListValue);
}

absl::optional<std::vector<absl::string_view>> CommonRecoContext::GetStringListItemAttr(
    uint64 item_key, const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
  HANDLE_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetStringListValue);
}

const boost::any *CommonRecoContext::GetExtraItemAttr(uint64 item_key, absl::string_view attr_name) const {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, nullptr);
  HANDLE_EXTRA_ITEM_ATTR_READ_BY_KEY(nullptr, GetExtraValue);
}

const boost::any *CommonRecoContext::GetExtraItemAttr(const CommonRecoResult &item_result,
                                                      const ItemAttr *item_attr) const {
  CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, nullptr);
  return item_attr->GetExtraValue(item_result.GetAttrIndex());
}

const boost::any *CommonRecoContext::GetExtraItemAttr(uint64 item_key, const ItemAttr *item_attr) const {
  HANDLE_EXTRA_ITEM_ATTR_READ_BY_KEY_AND_ATTR(GetExtraValue);
}

// ItemAttr Set APIs
bool CommonRecoContext::ClearItemAttrForAll(absl::string_view attr_name, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  auto *attr = GetItemAttr(attr_name);
  if (!attr) return true;
  return attr->ClearAllValues(check_overwrite);
}

bool CommonRecoContext::ClearItemAttr(uint64 item_key, absl::string_view name) {
  CHECK_INVALID_IO_BY_ATTR_NAME(name, output_item_attrs, false);
  AttrValue *attr = GetItemAttr(name);
  // NOTE(fangjianbing): clear 单个 item 属性时不要重置 value_type 值
  if (!attr) return true;
  auto index = running_processor_table_->GetItemAttrIndex(item_key);
  if (!index) return true;
  return attr->ClearValue(index.value(), false);
}

bool CommonRecoContext::ClearItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                      bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  if (!attr) return true;
  return attr->ClearValue(item_result.GetAttrIndex(), check_overwrite);
}

bool CommonRecoContext::SetIntItemAttr(uint64 item_key, absl::string_view attr_name, int64 value,
                                       bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<int64>(item_key, attr_name, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<int64>(item_key, attr_name, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetIntItemAttr(uint64 item_key, ItemAttr *attr, int64 value, bool if_not_exist,
                                       bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetSingularValue<int64>, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetIntItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int64 value,
                                       bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<int64>(item_result, attr, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<int64>(item_result, attr, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetDoubleItemAttr(uint64 item_key, absl::string_view attr_name, double value,
                                          bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<double>(item_key, attr_name, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<double>(item_key, attr_name, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetDoubleItemAttr(uint64 item_key, ItemAttr *attr, double value, bool if_not_exist,
                                          bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetSingularValue<double>, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, double value,
                                          bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<double>(item_result, attr, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<double>(item_result, attr, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetFloat32ItemAttr(uint64 item_key, absl::string_view attr_name, float value,
                                          bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<float>(item_key, attr_name, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<float>(item_key, attr_name, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetFloat32ItemAttr(uint64 item_key, ItemAttr *attr, float value, bool if_not_exist,
                                          bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetSingularValue<float>, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, float value,
                                          bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<float>(item_result, attr, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<float>(item_result, attr, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetFloat16ItemAttr(uint64 item_key, absl::string_view attr_name, float16_t value,
                                          bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<float16_t>(item_key, attr_name,
                                                    value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<float16_t>(item_key, attr_name, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetFloat16ItemAttr(uint64 item_key, ItemAttr *attr, float16_t value,
                                          bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetSingularValue<float16_t>, value, if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                          float16_t value, bool if_not_exist, bool check_overwrite) {
  if (!enable_attr_check) {
    return SetSingleItemAttrWithoutCheck<float16_t>(item_result, attr, value, if_not_exist, check_overwrite);
  } else {
    return SetSingleItemAttr<float16_t>(item_result, attr, value, if_not_exist, check_overwrite);
  }
}

bool CommonRecoContext::SetStringItemAttr(uint64 item_key, absl::string_view attr_name, std::string value,
                                          bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_KEY(SetStringValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringItemAttr(uint64 item_key, ItemAttr *attr, std::string value,
                                          bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetStringValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                          std::string value, bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetStringValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetIntListItemAttr(uint64 item_key, absl::string_view attr_name,
                                           std::vector<int64> &&value, bool if_not_exist,
                                           bool check_overwrite) {
  return SetListItemAttr<int64>(item_key, attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetIntListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<int64> &&value,
                                           bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetListValue<int64>, std::move(value), if_not_exist,
                                         check_overwrite);
}

bool CommonRecoContext::SetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                           std::vector<int64> &&value, bool if_not_exist,
                                           bool check_overwrite) {
  return SetListItemAttr<int64>(item_result, attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name,
                                              std::vector<double> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<double>(item_key, attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<double> &&value,
                                              bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetListValue<double>, std::move(value), if_not_exist,
                                         check_overwrite);
}

bool CommonRecoContext::SetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                              std::vector<double> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<double>(item_result, attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                              std::vector<float> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<float>(item_result, attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ListItemAttr(uint64 item_key, absl::string_view attr_name,
                                              std::vector<float> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<float>(item_key, attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat32ListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<float> &&value,
                                              bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetListValue<float>, std::move(value), if_not_exist,
                                         check_overwrite);
}

bool CommonRecoContext::SetFloat16ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                              std::vector<float16_t> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<float16_t>(item_result, attr, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16ListItemAttr(uint64 item_key, absl::string_view attr_name,
                                              std::vector<float16_t> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  return SetListItemAttr<float16_t>(item_key, attr_name, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetFloat16ListItemAttr(uint64 item_key, ItemAttr *attr,
                                              std::vector<float16_t> &&value,
                                              bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetListValue<float16_t>, std::move(value), if_not_exist,
                                         check_overwrite);
}

bool CommonRecoContext::SetStringListItemAttr(uint64 item_key, absl::string_view attr_name,
                                              std::vector<std::string> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_KEY(SetStringListValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringListItemAttr(uint64 item_key, ItemAttr *attr,
                                              std::vector<std::string> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetStringListValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                              std::vector<std::string> &&value, bool if_not_exist,
                                              bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetStringListValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetExtraItemAttr(uint64 item_key, absl::string_view attr_name, boost::any &&value,
                                         bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY(SetExtraValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetExtraItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                         boost::any &&value, bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetExtraValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::SetExtraItemAttr(uint64 item_key, ItemAttr *attr, boost::any &&value,
                                         bool if_not_exist, bool check_overwrite) {
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(SetExtraValue, std::move(value), if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetIntListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity,
                                             bool if_not_exist, bool check_overwrite) {
  return ResetListItemAttr<int64>(item_key, attr_name, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetIntListItemAttr(uint64 item_key, ItemAttr *attr, int capacity, bool if_not_exist,
                                             bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(ResetListValue<int64>, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                             int capacity, bool if_not_exist, bool check_overwrite) {
  return ResetListItemAttr<int64>(item_result, attr, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity,
                                                bool if_not_exist, bool check_overwrite) {
  return ResetListItemAttr<double>(item_key, attr_name, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, int capacity,
                                                bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(ResetListValue<double>, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                                int capacity, bool if_not_exist, bool check_overwrite) {
  return ResetListItemAttr<double>(item_result, attr, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetStringListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity,
                                                bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_KEY(ResetStringListValue, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetStringListItemAttr(uint64 item_key, ItemAttr *attr, int capacity,
                                                bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(ResetStringListValue, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::ResetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                                int capacity, bool if_not_exist, bool check_overwrite) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(ResetStringListValue, capacity, if_not_exist, check_overwrite);
}

bool CommonRecoContext::AppendIntListItemAttr(uint64 item_key, absl::string_view attr_name, int64 value) {
  return AppendListItemAttr<int64>(item_key, attr_name, value);
}

bool CommonRecoContext::AppendIntListItemAttr(uint64 item_key, ItemAttr *attr, int64 value) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(AppendListValue<int64>, value);
}

bool CommonRecoContext::AppendIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                              int64 value) {
  return AppendListItemAttr<int64>(item_result, attr, value);
}

bool CommonRecoContext::AppendDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, double value) {
  return AppendListItemAttr<double>(item_key, attr_name, value);
}

bool CommonRecoContext::AppendDoubleListItemAttr(uint64 item_key, ItemAttr *attr, double value) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(AppendListValue<double>, value);
}

bool CommonRecoContext::AppendDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                                 double value) {
  return AppendListItemAttr<double>(item_result, attr, value);
}

bool CommonRecoContext::AppendStringListItemAttr(uint64 item_key, absl::string_view attr_name,
                                                 std::string value) {
  CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_KEY(AppendStringListValue, std::move(value));
}

bool CommonRecoContext::AppendStringListItemAttr(uint64 item_key, ItemAttr *attr, std::string value) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  HANDLE_ITEM_ATTR_WRITE_BY_KEY_AND_ATTR(AppendStringListValue, std::move(value));
}

bool CommonRecoContext::AppendStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                                 std::string value) {
  CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
  ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(AppendStringListValue, std::move(value));
}

ItemAttr *CommonRecoContext::GetItemAttrAccessor(absl::string_view attr_name) {
  return GetOrInsertItemAttr(attr_name);
}

AttrValue *CommonRecoContext::GetItemAttrFromTable(absl::string_view attr_name,
                                                   absl::string_view table_name) {
  auto table = GetOrInsertDataTable(table_name);
  return table->GetAttr(attr_name);
}

AttrValue *CommonRecoContext::GetOrInsertItemAttrFromTable(absl::string_view attr_name,
                                                           absl::string_view table_name) {
  auto table = GetOrInsertDataTable(table_name);
  return table->GetOrInsertAttr(attr_name);
}

const std::vector<AttrValue *> &CommonRecoContext::GetAllCommonAttrs() const {
  return common_data_->GetAllItemAttrs();
}

const std::vector<AttrValue *> &CommonRecoContext::GetAllItemAttrs() const {
  return running_processor_table_->GetAllItemAttrs();
}

const std::vector<AttrValue *> &CommonRecoContext::GetAllItemAttrsInTable(absl::string_view table_name) {
  auto table = GetOrInsertDataTable(table_name);
  return table->GetAllItemAttrs();
}

CommonAttr *CommonRecoContext::GetCommonAttrAccessor(absl::string_view attr_name) {
  return GetOrInsertCommonAttr(attr_name);
}

void CommonRecoContext::SpecifyCommonAttrType(const std::string &attr_name, AttrType attr_type) {
  if (FLAGS_save_desired_attr_type) {
    common_data_->SpecifyAttrType(attr_name, attr_type);
  } else {
    auto *common_attr = common_data_->GetOrInsertAttr(attr_name);
    common_attr->SetDesiredAttrType(attr_type);
  }
}

void CommonRecoContext::SpecifyItemAttrType(const std::string &table_name, const std::string &attr_name,
                                            AttrType attr_type) {
  auto *table = GetOrInsertDataTable(table_name);
  if (FLAGS_save_desired_attr_type) {
    table->SpecifyAttrType(attr_name, attr_type);
  } else {
    auto *item_attr = table->GetOrInsertAttr(attr_name);
    item_attr->SetDesiredAttrType(attr_type);
  }
}

bool CommonRecoContext::InBrowseSet(uint64 item_key) const {
  if (reco_browse_set_result_ && reco_browse_set_result_->MightContain(item_key)) {
    return true;
  }

  return browse_set_.find(item_key) != browse_set_.end();
}

int64 CommonRecoContext::GetBrowseSetSize() const {
  if (reco_browse_set_result_) {
    return reco_browse_set_result_->GetBrowsedPids().size();
  }
  return browse_set_.size();
}

std::vector<uint64> CommonRecoContext::GetLatestBrowsedItems(int count) const {
  std::vector<uint64> items;
  if (reco_browse_set_result_) {
    if (request_ && request_->has_browse_set()) {
      const auto &browse_set = request_->browse_set();
      int browse_set_pid_size = browse_set.browsed_ids_size();
      int item_num = count == 0 ? browse_set_pid_size : std::min(std::abs(count), browse_set_pid_size);
      items.reserve(item_num);
      for (int i = 0; i < item_num; ++i) {
        int index = count < 0 ? i : browse_set_pid_size - 1 - i;
        items.push_back(browse_set.browsed_ids(index));
      }
    }
    if (count == 0 && items.empty() && reco_browse_set_result_->GetBrowsedPidsSize() > 0) {
      const auto &browsed_pids = reco_browse_set_result_->GetBrowsedPids();
      std::copy(browsed_pids.begin(), browsed_pids.end(), std::back_inserter(items));
    }
  } else {
    if (request_) {
      const auto &browse_set_package = request_->browse_set_package();
      int total_browse_size = browse_set_package.item_size();
      int item_num = count == 0 ? total_browse_size : std::min(std::abs(count), total_browse_size);
      items.reserve(item_num);
      for (int i = 0; i < item_num; ++i) {
        int index = count < 0 ? i : total_browse_size - 1 - i;
        const auto &item = browse_set_package.item(index);
        items.push_back(Util::GenKeysign(item.item_type(), item.item_id()));
      }
    }

    if (count == 0 && items.empty() && !browse_set_.empty()) {
      std::copy(browse_set_.begin(), browse_set_.end(), std::back_inserter(items));
    }
  }

  return items;
}

void CommonRecoContext::AddItemToBrowseSet(uint64 item_key) {
  browse_set_.insert(item_key);
}

void CommonRecoContext::SetupRecoBrowseSet(const ks::reco::BrowseSet &browse_set) {
  auto browse_set_result = std::make_shared<ks::reco::BrowseSetResult>();
  browse_set_result->Init(browse_set);
  reco_browse_set_result_ = browse_set_result;
}

std::shared_ptr<const ks::reco::BrowseSetResult> CommonRecoContext::GetRecoBrowseSet() const {
  return reco_browse_set_result_;
}

bool CommonRecoContext::ShadowCopyRecoBrowseSet(const CommonRecoContext *context) {
  if (context->GetRecoBrowseSet()) {
    reco_browse_set_result_ = context->GetRecoBrowseSet();
    return true;
  }
  return false;
}

const std::unordered_map<uint64, int> &CommonRecoContext::GetForceInsertMap() const {
  return force_insert_map_;
}

void CommonRecoContext::AddForceInsertMapItem(uint64 item_key, int position) {
  force_insert_map_[item_key] = position;
}

// other necessary Getters/Setters

CommonRecoKessBatchWaiter *CommonRecoContext::GetBatchWaiter(const std::string &trigger,
                                                             bool create_if_missing) {
  auto it = batch_waiters_.find(trigger);
  if (it != batch_waiters_.end()) {
    return it->second.get();
  }
  if (create_if_missing) {
    auto waiter = std::make_unique<CommonRecoKessBatchWaiter>(trigger);
    auto pr = batch_waiters_.insert({trigger, std::move(waiter)});
    return pr.first->second.get();
  }
  return nullptr;
}

CommonRecoLocalAsyncWaiter *CommonRecoContext::GetLocalAsyncWaiter(const std::string &trigger,
                                                                   bool create_if_missing) {
  auto it = local_async_waiters_.find(trigger);
  if (it != local_async_waiters_.end()) {
    return it->second.get();
  }
  if (create_if_missing) {
    auto waiter = std::make_unique<CommonRecoLocalAsyncWaiter>(trigger);
    auto pr = local_async_waiters_.insert({trigger, std::move(waiter)});
    return pr.first->second.get();
  }
  return nullptr;
}

std::vector<std::string> *CommonRecoContext::GetAsyncUpstreamProcessors(absl::string_view processor_name,
                                                                        bool create_if_missing) {
  if (create_if_missing) {
    return async_upstream_processors_.GetOrInsert(processor_name.data(), processor_name.size());
  } else {
    return async_upstream_processors_.Get(processor_name.data(), processor_name.size());
  }
}

void CommonRecoContext::SetLocalAsyncTimeoutWaiter(CommonRecoLocalAsyncConcurrentWaiter *waiter) {
  local_async_timeout_waiter_ptr_ = waiter;
}

CommonRecoLocalAsyncConcurrentWaiter *CommonRecoContext::GetLocalAsyncTimeoutWaiter() {
  return local_async_timeout_waiter_ptr_;
}

void CommonRecoContext::SetDetachedBatchWaiter(CommonRecoKessBatchConcurrentWaiter *waiter) {
  detached_batch_waiter_ptr_ = waiter;
}

CommonRecoKessBatchConcurrentWaiter *CommonRecoContext::GetDetachedBatchWaiter() {
  return detached_batch_waiter_ptr_;
}

int CommonRecoContext::WaitAllCallbacks() {
  int num = 0;
  for (auto &[k, waiter] : local_async_waiters_) {
    if (waiter->Size() > 0) {
      num += waiter->Size();
      waiter->Wait();
    }
  }
  for (auto &[k, waiter] : batch_waiters_) {
    if (waiter->Size() > 0) {
      num += waiter->Size();
      waiter->Wait();
    }
  }
  if (num > 0) {
    std::string upstream_processors;
    for (std::vector<std::string> *vec : async_upstream_processors_) {
      for (const auto &name : *vec) {
        absl::StrAppend(&upstream_processors, name, ",");
      }
      vec->clear();
    }
    CL_LOG(INFO) << num << " async wait executed, before: pipeline end, produced by: " << upstream_processors;
  }
  return num;
}

std::vector<CommonRecoResult> *CommonRecoContext::GetRecoResults() {
  return running_processor_table_->GetRecoResults();
}

std::vector<CommonRecoResult> *CommonRecoContext::GetRecoResults(absl::string_view table_name) {
  auto *table = GetOrInsertDataTable(table_name);
  return table->GetRecoResults();
}

std::string CommonRecoContext::GetCaller() const {
  return rpc_controller_->GetCaller();
}

void CommonRecoContext::SetRpcController(std::unique_ptr<CommonRecoRpcControllerInterface> rpc_controller) {
  rpc_controller_.swap(rpc_controller);
}

bool CommonRecoContext::IsRequestCancelled() const {
  if (FLAGS_terminate_cancelled_request) {
    return rpc_controller_->IsRequestCancelled();
  } else {
    return false;
  }
}

int32 CommonRecoContext::GetTimeLeft() const {
  return rpc_controller_->GetTimeLeft();
}

RpcType CommonRecoContext::GetRpcType() const {
  return rpc_controller_->Type();
}

void CommonRecoContext::SetRequest(const CommonRecoRequest *req) {
  request_ = req;
  user_id_ = request_->user_id();
  device_id_ = request_->device_id();
  request_type_ = request_->request_type();
  request_time_ms_ = request_->time_ms();
  request_id_ = request_->request_id();
  request_num_ = request_->request_num();
  debug_ = request_->debug();
  need_traceback_ = request_->need_traceback();
  request_start_time_ = base::GetTimestamp();
  stage_start_time_ = base::GetTimestamp();

  LoggingUtil::ResetLoggingEnabled(user_id_, device_id_);
  AttrIOCheckUtil::Reset(GetProcessCounter());

  // 注入 built-in CommonAttr
  SetIntCommonAttr(kUserIdAttr, user_id_);
  SetStringCommonAttr(kDeviceIdAttr, device_id_);
  SetStringCommonAttr(kRequestIdAttr, request_id_);
  SetStringCommonAttr(kRequestTypeAttr, request_type_);
  SetIntCommonAttr(kRequestTimeAttr, request_time_ms_);
  SetIntCommonAttr(kRequestNumAttr, request_num_);
  if (reco_browse_set_result_) {
    SetIntCommonAttr(kBrowseSetSizeAttr, reco_browse_set_result_->GetBrowsedPidsSize());
  } else {
    SetIntCommonAttr(kBrowseSetSizeAttr, request_->browse_set_package().item_size());
  }
}

void CommonRecoContext::SetSharedRequest(std::shared_ptr<void> req) {
  shared_request_ = req;
}

void CommonRecoContext::SetFlattenRequest(const ks::platform::fbs::FlattenCommonRecoRequest *request) {
  flatten_request_ = request;
  user_id_ = request->user_id();
  device_id_ = request->device_id() ? request->device_id()->str() : "";
  request_type_ = request->request_type() ? request->request_type()->str() : "";
  request_time_ms_ = request->time_ms();
  request_id_ = request->request_id() ? request->request_id()->str() : "";
  request_num_ = request->request_num();
  debug_ = request->debug();
  request_start_time_ = base::GetTimestamp();
  stage_start_time_ = base::GetTimestamp();

  LoggingUtil::ResetLoggingEnabled(user_id_, device_id_);
  AttrIOCheckUtil::Reset(GetProcessCounter());

  // 注入 built-in CommonAttr
  SetIntCommonAttr(kUserIdAttr, user_id_);
  SetStringCommonAttr(kDeviceIdAttr, device_id_);
  SetStringCommonAttr(kRequestIdAttr, request_id_);
  SetStringCommonAttr(kRequestTypeAttr, request_type_);
  SetIntCommonAttr(kRequestTimeAttr, request_time_ms_);
  SetIntCommonAttr(kRequestNumAttr, request_num_);
  SetIntCommonAttr(kBrowseSetSizeAttr, 0);
}

void CommonRecoContext::AddCommonAttrFromRequest(absl::string_view attr_name) {
  // TODO(fangjianbing): change to absl::string_view
  common_attrs_from_request_.insert(std::string(attr_name));
}

void CommonRecoContext::AddItemAttrFromRequest(absl::string_view attr_name) {
  // TODO(fangjianbing): change to absl::string_view
  item_attrs_from_request_.insert(std::string(attr_name));
}

void CommonRecoContext::AddFixedAttrs(absl::string_view attr_name) {
  // TODO(fangjianbing): change to absl::string_view
  fixed_common_attrs_from_request_->insert(std::string(attr_name));
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetUserId(uint64 uid) {
  user_id_ = uid;
  LoggingUtil::ResetLoggingEnabled(user_id_, device_id_);
  SetIntCommonAttr(kUserIdAttr, user_id_);
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetDeviceId(const std::string &did) {
  device_id_ = did;
  LoggingUtil::ResetLoggingEnabled(user_id_, device_id_);
  SetStringCommonAttr(kDeviceIdAttr, device_id_);
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetRequestTime(int64 time_ms) {
  request_time_ms_ = time_ms;
  SetIntCommonAttr(kRequestTimeAttr, request_time_ms_);
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetRequestType(const std::string &req_type) {
  request_type_ = req_type;
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetRequestNum(int64 req_num) {
  request_num_ = req_num;
}

// ONLY FOR DEBUG USE!!!
void CommonRecoContext::PurposelyResetRequestId(const std::string &req_id) {
  request_id_ = req_id;
}

void CommonRecoContext::DereferenceItemAttr(absl::string_view attr_name) {
  ItemAttr *attr = GetItemAttrAccessor(attr_name);
  attr->DescRefCount();
}

void CommonRecoContext::DereferenceCommonAttr(absl::string_view attr_name) {
  CommonAttr *attr = GetCommonAttrAccessor(attr_name);
  attr->DescRefCount();
}

// MergeItemAttr
// 需要 MergeResult 生成的 merged_result_map, 请严格保证 MergeResult 函数在 MergeItemAttr 函数之前执行。
void CommonRecoContext::MergeItemAttrs(
    CommonRecoContext *from_context, AttrTable *table, AttrTable *from_table,
    const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
    const folly::F14FastMap<const CommonRecoResult *, const CommonRecoResult *> &merged_result_map,
    bool merge_and_overwrite, bool could_zero_copy) {
  for (const auto &attr : merge_item_attrs) {
    const auto &attr_name = attr.first;
    const auto &attr_alias = attr.second.alias;
    const auto &copy_mode = attr.second.copy_mode;
    CHECK_INVALID_IO_BY_ATTR_NAME_CONTINUE(attr_name, output_item_attrs);
    ItemAttr *to_attr = table->GetOrInsertAttr(attr_alias);
    ItemAttr *from_attr = from_table->GetOrInsertAttr(attr_name);
    // NOTE(zhaoyang09): attr 指针传递不合并
    if (!from_attr || from_attr->value_type == AttrType::UNKNOWN || from_attr == to_attr) {
      continue;
    }

    // OVERWRITE 模式下可以优先 move 值。
    if (copy_mode == CopyMode::OVERWRITE) {
      if (to_attr->value_type == AttrType::UNKNOWN && could_zero_copy && FLAGS_zero_copy_subflow_output) {
        to_attr->SwapPayloadFrom(from_attr);
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "subflow_merge_item_attr.swap", GlobalHolder::GetServiceIdentifier(), GetRequestType(),
            running_processor_->GetName(), attr_name, from_table->name());
      } else {
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "subflow_merge_item_attr.copy", GlobalHolder::GetServiceIdentifier(), GetRequestType(),
            running_processor_->GetName(), attr_name, from_table->name());

        if (to_attr->value_type == AttrType::UNKNOWN) {
          to_attr->value_type = from_attr->value_type;
        } else if (to_attr->value_type != from_attr->value_type) {
          CL_LOG_ERROR("[DANGER!!!] inconsistent_item_attr_type", to_attr->name())
              << "[DANGER!!!] failed to merge item_attr '" << to_attr->name()
              << "' due to inconsistent type: " << static_cast<int>(to_attr->value_type) << " vs "
              << static_cast<int>(from_attr->value_type);
          continue;
        }
        // internal index merge，不需要真实拷贝数据
        if (from_attr->is_from_flat_index) {
          to_attr->is_from_flat_index = true;
          to_attr->flat_index_offset = from_attr->flat_index_offset;
          to_attr->flat_index_order = from_attr->flat_index_order;
          to_attr->MarkReadOnly();
          continue;
        }
        // 直接使用 item index 来进行 merge
        for (const auto &result_pair : merged_result_map) {
          size_t from_index = result_pair.first->GetAttrIndex();
          size_t to_index = result_pair.second->GetAttrIndex();
          if (!merge_and_overwrite && to_attr->HasValue(to_index)) {
            continue;
          }
          if (table->IsValidItemAttrIndex(to_index)) {
            bool move_value = (to_attr->GetOwner() == table && !table->IsShared());
            to_attr->MergeItemValue(from_attr, from_index, to_index, false, true, move_value);
          }
        }
      }
    } else {
      // 非 OVERWRITE 模式
      if (copy_mode == CopyMode::CONCAT) {
        to_attr->ConvertSingularValueToListValue();
        if (to_attr->value_type != AttrType::UNKNOWN &&
            to_attr->value_type != ConvertToListType(from_attr->value_type)) {
          CL_LOG_ERROR("[DANGER!!!] inconsistent_item_attr_type", to_attr->name())
              << "[DANGER!!!] failed to concat merge item_attr '" << to_attr->name()
              << "' due to inconsistent type: " << static_cast<int>(to_attr->value_type) << " vs "
              << static_cast<int>(from_attr->value_type);
          continue;
        }
      }
      switch (from_attr->value_type) {
        case AttrType::INT_LIST:
        case AttrType::FLOAT_LIST:
        case AttrType::STRING_LIST:
          CL_LOG_ERROR("[DANGER!!!] invalid_copy_mode", to_attr->name())
              << "[DANGER!!!] failed to merge item_attr '" << to_attr->name()
              << "' due invalid_copy_mode: " << int(copy_mode)
              << " with value type: " << static_cast<int>(from_attr->value_type);
          break;
        case AttrType::STRING: {
          for (const auto &result_pair : merged_result_map) {
            const auto &from_result = result_pair.first;
            const auto &to_result = result_pair.second;
            if (auto string_view_value = from_context->GetStringItemAttr(*from_result, from_attr)) {
              std::string string_value(string_view_value->data(), string_view_value->size());
              if (copy_mode == CopyMode::CONCAT) {
                AppendStringListItemAttr(*to_result, to_attr, string_value);
              } else {
                CL_LOG_ERROR("[DANGER!!!] invalid_copy_mode", to_attr->name())
                    << "[DANGER!!!] failed to merge item_attr '" << to_attr->name()
                    << "' due invalid_copy_mode: " << int(copy_mode)
                    << " with value type: " << static_cast<int>(from_attr->value_type);
              }
            }
          }
          break;
        }
        case AttrType::INT: {
          for (const auto &result_pair : merged_result_map) {
            const auto &from_result = result_pair.first;
            const auto &to_result = result_pair.second;
            if (auto int_value = from_context->GetIntItemAttr(*from_result, from_attr)) {
              if (copy_mode == CopyMode::CONCAT) {
                AppendIntListItemAttr(*to_result, to_attr, int_value.value());
              } else {
                auto origin_value = GetIntItemAttr(*to_result, to_attr);
                int64 new_value = int_value.value();
                if (origin_value) {
                  if (copy_mode == CopyMode::MAX && origin_value.value() > new_value) {
                    new_value = origin_value.value();
                  } else if (copy_mode == CopyMode::MIN && origin_value.value() < new_value) {
                    new_value = origin_value.value();
                  } else if (copy_mode == CopyMode::SUM) {
                    new_value += origin_value.value();
                  }
                }
                SetIntItemAttr(*to_result, to_attr, new_value);
              }
            }
          }
          break;
        }
        case AttrType::FLOAT: {
          for (const auto &result_pair : merged_result_map) {
            const auto &from_result = result_pair.first;
            const auto &to_result = result_pair.second;
            if (auto double_value = from_context->GetDoubleItemAttr(*from_result, from_attr)) {
              if (copy_mode == CopyMode::CONCAT) {
                AppendDoubleListItemAttr(*to_result, to_attr, double_value.value());
              } else {
                auto origin_value = GetDoubleItemAttr(*to_result, to_attr);
                double new_value = double_value.value();
                if (origin_value) {
                  if (copy_mode == CopyMode::MAX && origin_value.value() > new_value) {
                    new_value = origin_value.value();
                  } else if (copy_mode == CopyMode::MIN && origin_value.value() < new_value) {
                    new_value = origin_value.value();
                  } else if (copy_mode == CopyMode::SUM) {
                    new_value += origin_value.value();
                  }
                }
                SetDoubleItemAttr(*to_result, to_attr, new_value);
              }
            }
          }
          break;
        }
        default:
          break;
      }
    }
  }
}

int CommonRecoContext::MergeTable(CommonRecoContext *from_context, const std::string &pipeline_name,
                                  const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
                                  const std::string &table_name, bool merge_and_overwrite, bool merge_result,
                                  bool deduplicate_results, int result_num,
                                  bool merge_item_attr_for_all_items, int64 item_channel) {
  auto *table = GetOrInsertDataTable(table_name);
  auto *from_table = from_context->GetOrInsertDataTable(table_name);

  if (table->GetRecoResults()->size() == 0 && FLAGS_zero_copy_subflow_output) {
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "subflow_merge_table.swap",
                                                   GlobalHolder::GetServiceIdentifier(), GetRequestType(),
                                                   running_processor_->GetName(), table_name);
    table->SwapPayloadFrom(from_table);
    return table->GetRecoResults()->size();
  }

  base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "subflow_merge_table.copy",
                                                 GlobalHolder::GetServiceIdentifier(), GetRequestType(),
                                                 running_processor_->GetName(), table_name);

  int merged_count = 0;
  if (merge_result) {
    merged_count = MergeResult(from_context, result_num, deduplicate_results, pipeline_name, table,
                               from_table, item_channel);
    table->MergeFilterReason(from_table);
  }

  if (!merge_item_attrs.empty()) {
    // from_result 到 to_result 指针的映射，必须保证在结果集扩充完成后再取地址（在 MergeResult 后执行）
    folly::F14FastMap<const CommonRecoResult *, const CommonRecoResult *> merged_result_map;
    if (merge_item_attr_for_all_items) {
      // 防止析构
      std::vector<CommonRecoResult> fake_result_vec;
      fake_result_vec.reserve(from_table->GetCommonRecoResults().size());
      for (const CommonRecoResult &from_result : from_table->GetCommonRecoResults()) {
        auto fake_index = table->GetItemAttrIndex(from_result.key());
        if (!fake_index) {
          continue;
        }
        CommonRecoResult fake_result(fake_index.value(), from_result.key(), from_result.reason);
        fake_result_vec.push_back(fake_result);
        merged_result_map[&from_result] = &(fake_result_vec.back());
      }
      MergeItemAttrs(from_context, table, from_table, merge_item_attrs, merged_result_map,
                     merge_and_overwrite, !merge_result);
    } else {
      // 临时的 item_key -> from_result 映射，为了遍历 to_results 时能快速找到对应的指针并插入到最终结果里
      folly::F14FastMap<uint64, const CommonRecoResult *> from_result_map;
      for (const CommonRecoResult &merge_result : from_table->GetCommonRecoResults()) {
        from_result_map[merge_result.item_key] = &merge_result;
      }
      // 遍历 to_results 并为每一个 from_result 找到对应的地址
      for (const CommonRecoResult &to_result : table->GetCommonRecoResults()) {
        auto it = from_result_map.find(to_result.item_key);
        if (it != from_result_map.end()) {
          merged_result_map[it->second] = &to_result;
        }
      }
      MergeItemAttrs(from_context, table, from_table, merge_item_attrs, merged_result_map,
                     merge_and_overwrite, !merge_result);
    }
  }

  auto &flat_index_addr_map = from_table->GetItemFlatIndexAddrMap();
  if (flat_index_addr_map.size() > 0) {
    for (const auto &it : flat_index_addr_map) {
      table->SetItemFlatIndexAddr(it.first, it.second);
    }
    // 遍历结果集，为 item 添加上 internal index 指针
    for (auto &result : (*table->GetRecoResults())) {
      if (!result.GetFlatIndexItemAddr()) {
        auto addr = table->GetItemFlatIndexAddr(result.item_key);
        if (addr) {
          result.SetFlatIndexItemAddr(addr);
        }
      }
    }
  }
  return merged_count;
}

int CommonRecoContext::MergeContext(CommonRecoContext *from_context, int result_num, bool deduplicate_results,
                                    const std::unordered_map<std::string, std::string> &merge_common_attrs,
                                    const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
                                    const std::string &pipeline_name, bool merge_and_overwrite,
                                    bool merge_result, bool merge_item_attr_for_all_items,
                                    int64 item_channel) {
  MergeCommonAttrs(from_context, merge_common_attrs, merge_and_overwrite);

  if (need_pipeline_cpu_cost_) {
    auto &from_pipeline_cpu_cost = from_context->GetPipelineCpuCost();
    auto from_pipeline_list = from_pipeline_cpu_cost.GetAllKeys();
    std::string main_pipeline_name = "";
    main_pipeline_name = running_processor_->GetPipelineName();
    for (const auto &name : from_pipeline_list) {
      int64 cost = from_pipeline_cpu_cost.Get(name);
      pipeline_cpu_cost_ns_.Add(name, cost);
      pipeline_cpu_cost_ns_.Add(main_pipeline_name, cost);
    }
  }
  if (need_table_cpu_cost_) {
    auto &from_table_cpu_cost = from_context->GetTableCpuCost();
    auto from_table_list = from_table_cpu_cost.GetAllKeys();
    for (const auto &name : from_table_list) {
      table_cpu_cost_ns_.Add(name, from_table_cpu_cost.Get(name));
    }
  }

  const auto &table_name = running_processor_table_->GetName();
  return MergeTable(from_context, pipeline_name, merge_item_attrs, table_name, merge_and_overwrite,
                    merge_result, deduplicate_results, result_num, merge_item_attr_for_all_items,
                    item_channel);
}

void CommonRecoContext::SetCpuAffinityInfo(const CpuAffinityInfo &cpu_affinity_info) {
  cpu_affinity_info_ = cpu_affinity_info;
}

const CpuAffinityInfo *CommonRecoContext::GetCpuAffinityInfo() const {
  return &cpu_affinity_info_;
}

void CommonRecoContext::SetNumaId(int id) {
  numa_id_ = id;
}

int CommonRecoContext::GetNumaId() const {
  return numa_id_;
}

StreamingHandler *CommonRecoContext::GetStreamingHandler() const {
  return streaming_handler_;
}

void CommonRecoContext::SetStreamingHandler(StreamingHandler *streaming_handler) {
  streaming_handler_ = streaming_handler;
}

StreamingStatus CommonRecoContext::GetStreamingStatus() const {
  return streaming_handler_->GetStreamingStatus();
}

void CommonRecoContext::SetStreamingStatus(StreamingStatus value) {
  streaming_handler_->SetStreamingStatus(value);
}

int CommonRecoContext::GetStreamingLoopIndex() const {
  return streaming_loop_index_;
}

void CommonRecoContext::SetStreamingLoopIndex(int index) {
  streaming_loop_index_ = index;
}

bool CommonRecoContext::EvalIntParamFromLuaExpr(std::vector<std::string> *common_attrs,
                                                absl::string_view expr, int64 *int_val) const {
  if (!ExecLuaExpr(common_attrs, expr)) {
    return false;
  }
  int type = lua_type(lua_state_, -1);
  switch (type) {
    case LUA_TNUMBER: {
      if (lua_isinteger(lua_state_, -1)) {
        *int_val = lua_tointeger(lua_state_, -1);
        return true;
      } else {
        return false;
      }
    }
    case LUA_TBOOLEAN: {
      *int_val = lua_toboolean(lua_state_, -1);
      return true;
    }
    default:
      CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << type << " lua expr: " << expr;
      return false;
  }
}

bool CommonRecoContext::EvalDoubleParamFromLuaExpr(std::vector<std::string> *common_attrs,
                                                   absl::string_view expr, double *double_val,
                                                   bool try_int_attr) const {
  if (!ExecLuaExpr(common_attrs, expr)) {
    return false;
  }
  int type = lua_type(lua_state_, -1);
  if (type == LUA_TNUMBER) {
    if (lua_isinteger(lua_state_, -1)) {
      if (try_int_attr) {
        *double_val = lua_tointeger(lua_state_, -1);
        return true;
      } else {
        return false;
      }
    } else {
      *double_val = lua_tonumber(lua_state_, -1);
      return true;
    }
  } else {
    CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << type << " lua expr: " << expr;
    return false;
  }
}

bool CommonRecoContext::EvalStringParamFromLuaExpr(std::vector<std::string> *common_attrs,
                                                   absl::string_view expr, std::string *string_val) const {
  if (!ExecLuaExpr(common_attrs, expr)) {
    return false;
  }
  int type = lua_type(lua_state_, -1);
  if (type == LUA_TSTRING) {
    size_t size;
    const char *p = lua_tolstring(lua_state_, -1, &size);
    *string_val = std::string(p, size);
    return true;
  } else {
    CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << type << " lua expr: " << expr;
    return false;
  }
}

bool CommonRecoContext::EvalIntListParamFromLuaExpr(std::vector<std::string> *common_attrs,
                                                    absl::string_view expr,
                                                    std::vector<int64> *int_list_val) const {
  if (!ExecLuaExpr(common_attrs, expr)) {
    return false;
  }
  int type = lua_type(lua_state_, -1);
  if (type == LUA_TTABLE) {
    for (int i = 1;; ++i) {
      lua_rawgeti(lua_state_, -1, i);
      base::ScopeExit scope_exit([this] { lua_pop(lua_state_, 1); });
      int item_type = lua_type(lua_state_, -1);
      if (item_type == LUA_TNIL || item_type == LUA_TNONE) {
        return true;
      }
      if ((item_type != LUA_TNUMBER && item_type != LUA_TBOOLEAN) || !lua_isinteger(lua_state_, -1)) {
        CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << item_type << " lua expr: " << expr;
        return false;
      } else {
        int_list_val->push_back(lua_tointeger(lua_state_, -1));
      }
    }
  } else {
    CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << type << " lua expr: " << expr;
    return false;
  }
}

bool CommonRecoContext::EvalDoubleListParamFromLuaExpr(std::vector<std::string> *common_attrs,
                                                       absl::string_view expr,
                                                       std::vector<double> *double_list_val) const {
  if (!ExecLuaExpr(common_attrs, expr)) {
    return false;
  }
  int type = lua_type(lua_state_, -1);
  if (type == LUA_TTABLE) {
    for (int i = 1;; ++i) {
      lua_rawgeti(lua_state_, -1, i);
      base::ScopeExit scope_exit([this] { lua_pop(lua_state_, 1); });
      int item_type = lua_type(lua_state_, -1);
      if (item_type == LUA_TNIL || item_type == LUA_TNONE) {
        return true;
      }
      if (item_type != LUA_TNUMBER) {
        CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << item_type << " lua expr: " << expr;
        return false;
      } else {
        double_list_val->push_back(lua_tonumber(lua_state_, -1));
      }
    }
  } else {
    CL_LOG_ERROR("lua", "lua_type_error") << "lua type error: " << type << " lua expr: " << expr;
    return false;
  }
}

void CommonRecoContext::MarkInvalid() {
  valid_ = false;
}

bool CommonRecoContext::IsValid() const {
  return valid_.load();
}

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
CommonAttr *CommonRecoContext::GetOrInsertCommonAttr(absl::string_view attr_name,
                                                     bool return_null_if_not_exist) {
  auto attr = common_data_->GetOrInsertAttr(attr_name);
  if (attr->value_type != AttrType::UNKNOWN) {
    return attr;
  }
  if (!common_data_backup_) {
    return return_null_if_not_exist ? nullptr : attr;
  }
  if (attr->backup_attr) {
    // NOTE(caohongjin): attr 的 value_type 在 clear 时被配置为 UNKNOWN，这里重置为 backup_attr 的类型
    attr->value_type = attr->backup_attr->value_type;
    return attr;
  }
  auto backup_attr = common_data_backup_->GetAttr(attr_name);
  if (backup_attr == attr) {
    // NOTE(caohongjin): 浅拷贝情况下有可能会循环引用，这里检测到循环依赖则不设置 backup_attr
    return return_null_if_not_exist ? nullptr : attr;
  }
  if (backup_attr) {
    attr->backup_attr = backup_attr;
    attr->value_type = backup_attr->value_type;
    return attr;
  }
  return return_null_if_not_exist ? nullptr : attr;
}
#endif

CommonAttr *CommonRecoContext::GetOrInsertCommonAttr(absl::string_view attr_name) {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  return GetOrInsertCommonAttr(attr_name, false);
#else
  return common_data_->GetOrInsertAttr(attr_name);
#endif
}

CommonAttr *CommonRecoContext::GetCommonAttr(absl::string_view attr_name) const {
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  return const_cast<CommonRecoContext *>(this)->GetOrInsertCommonAttr(attr_name, true);
#else
  return common_data_->GetAttr(attr_name);
#endif
}

ItemAttr *CommonRecoContext::GetOrInsertItemAttr(absl::string_view name) {
  return running_processor_table_->GetOrInsertAttr(name);
}

ItemAttr *CommonRecoContext::GetItemAttr(absl::string_view name) const {
  return running_processor_table_->GetAttr(name);
}

AttrTable *CommonRecoContext::GetOrInsertDataTable(absl::string_view table_name) {
  auto it = item_data_.find(table_name);
  if (it == item_data_.end()) {
    auto *data_frame = new AttrTable(std::string(table_name.data(), table_name.size()));
    data_frame->SetOwner(this);
    auto pr = item_data_.insert({data_frame->GetName(), data_frame});
    return pr.first->second;
  }
  return it->second;
}

bool CommonRecoContext::CreateLogicalTable(absl::string_view table_name, AttrTable *from_table) {
  auto it = item_data_.find(table_name);
  if (it == item_data_.end()) {
    auto *logical_table = new AttrTable(std::string(table_name.data(), table_name.size()), from_table);
    logical_table->SetOwner(this);
    auto pr = item_data_.insert({logical_table->GetName(), logical_table});
    return true;
  }

  auto logical_table = it->second;
  if (logical_table->IsLogical()) {
    // 检查两个 table 是否有相同的 payload
    return (logical_table->GetAttrPayload().get() == from_table->GetAttrPayload().get());
  }

  if (logical_table->GetAttrPayload()->GetItemIndexNum() == 0) {
    logical_table->SetLogical(true);
    logical_table->SetAttrPayload(from_table->GetAttrPayload());
    return true;
  }
  return false;
}

const AttrTable *CommonRecoContext::GetTable(absl::string_view table_name) const {
  return GetMutableTable(table_name);
}

AttrTable *CommonRecoContext::GetMutableTable(absl::string_view table_name) const {
  auto it = item_data_.find(table_name);
  if (it == item_data_.end()) {
    return nullptr;
  } else {
    return it->second;
  }
}

bool CommonRecoContext::InsertExternalTable(AttrTable *table) {
  if (!table || table->name().empty()) {
    CL_LOG_ERROR("insert_extern_managed_table", "insert_failed")
        << "insert extern managed table failed, empty name";
    return false;
  }

  table->MarkExternal();
  if (FLAGS_clear_external_table_after_request) {
    auto it = item_data_.find(table->name());
    // 缓存被 GetOrInsertDataTable 创建的 table, 避免外部还有持有者, 不析构
    if (it != item_data_.end() && !(it->second->IsExternal())) {
      replaced_internal_tables_.insert(it->second);
    }
  }
  item_data_.insert_or_assign(table->name(), table);
  return true;
}

int CommonRecoContext::MergeResult(CommonRecoContext *from_context, int result_num, bool deduplicate_results,
                                   const std::string &pipeline_name, AttrTable *table, AttrTable *from_table,
                                   int64 item_channel) {
  const auto &merge_results = from_table->GetCommonRecoResults();
  const auto &results = table->GetCommonRecoResults();

  if (result_num < 0) {
    result_num = merge_results.size();
  }

  folly::F14FastMap<uint64, size_t> item_key_to_attr_index;
  if (deduplicate_results) {
    item_key_to_attr_index.reserve(merge_results.size() + results.size());
    for (const auto &result : results) {
      item_key_to_attr_index.insert({result.item_key, result.GetAttrIndex()});
    }
  }

  // NOTE(zhaoyang09): 此处需要严格保证先去重，再截断！
  int traversed_result_num = 0;
  int merged_result_num = 0;
  folly::F14FastMap<int, int> reason_count;
  for (const CommonRecoResult &merge_result : merge_results) {
    if (merged_result_num >= result_num) break;
    traversed_result_num++;
    if (deduplicate_results) {
      auto result_index_it = item_key_to_attr_index.find(merge_result.item_key);
      if (result_index_it != item_key_to_attr_index.end()) {
        continue;
      }
    }

    // NOTE（zhaoyang09): item_channel 三元式保证了 retrieve_by_sub_flow 有修改 channel 的能力。
    CommonRecoResult &result =
        AddCommonRecoResultToTable(table, merge_result.item_key, merge_result.reason, merge_result.score,
                                   item_channel >= 0 ? item_channel : merge_result.channel);
    item_key_to_attr_index.insert({result.item_key, result.GetAttrIndex()});
    merged_result_num++;
    ++reason_count[result.reason];
  }

  for (const auto [reason, count] : reason_count) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(count, kPerfNs, "merge_item_num",
                                                      GlobalHolder::GetServiceIdentifier(), GetRequestType(),
                                                      std::to_string(reason), running_processor_->GetName());
  }

  if (merge_results.size() != 0) {
    int64 ratio = 1000000 * (1.0 * traversed_result_num / merge_results.size());
    base::perfutil::PerfUtilWrapper::IntervalLogStash(ratio, kPerfNs, "merge_used_item_percent",
                                                      GlobalHolder::GetServiceIdentifier(), GetRequestType(),
                                                      pipeline_name);
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
        traversed_result_num - merged_result_num, kPerfNs, "merge_dedup_remove_item_num",
        GlobalHolder::GetServiceIdentifier(), GetRequestType(), pipeline_name);
  }
  return merge_results.size();
}

void CommonRecoContext::MergeCommonAttrs(
    CommonRecoContext *from_context, const std::unordered_map<std::string, std::string> &merge_common_attrs,
    bool merge_and_overwrite) {
  // merge common_attrs
  for (const auto &attr_config : merge_common_attrs) {
    const std::string &from_attr = attr_config.first;
    const std::string &to_attr = attr_config.second;
    MergeCommonAttr(from_context, from_attr, to_attr, merge_and_overwrite);
  }
}

bool CommonRecoContext::MergeCommonAttr(CommonRecoContext *from_context, const std::string &from_attr,
                                        const std::string &to_attr, bool merge_and_overwrite) {
  CHECK_INVALID_IO_BY_ATTR_NAME(to_attr, output_common_attrs, false);
  // merge common_attr
  if (!from_context->HasCommonAttr(from_attr) || (!merge_and_overwrite && HasCommonAttr(to_attr))) {
    return false;
  }
  CommonAttr *to_common_attr = GetCommonAttrAccessor(to_attr);
  CommonAttr *from_common_attr = from_context->GetCommonAttrAccessor(from_attr);
  if (to_common_attr->value_type == AttrType::UNKNOWN) {
    to_common_attr->value_type = from_common_attr->value_type;
  } else if (to_common_attr->value_type != from_common_attr->value_type) {
    CL_LOG_EXCEPTION("inconsistent_common_attr_type:" + to_common_attr->name())
        << "[DANGER!!!]failed to merge common_attr '" << to_common_attr->name()
        << "' due to inconsistent type: " << static_cast<int>(to_common_attr->value_type) << " vs "
        << static_cast<int>(from_common_attr->value_type);
    return false;
  }
  // NOTE(zhaoyang09): attr 指针传递不合并
  if (to_common_attr == from_common_attr) {
    return false;
  }
  return to_common_attr->MoveItemValue(from_common_attr, 0, 0);
}

bool CommonRecoContext::ExecLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr) const {
  lua_settop(lua_state_, 0);
  if (common_attrs) {
    RecoUtil::ImportCommonAttrsToLuaState(this, *common_attrs, lua_state_);
  }
  CHECK(lua_checkstack(lua_state_, 1)) << "cannot allocate extra 1 slots in lua stack!";

  if (luaL_loadbuffer(lua_state_, expr.data(), expr.size(), "dynamic_param")) {
    CL_LOG_ERROR("lua", "lua_error: load expression fail")
        << "lua expr load error: " << lua_tostring(lua_state_, -1);
    return false;
  }
  int err_code = lua_pcall(lua_state_, 0, 1, 0);
  if (err_code) {
    const char *message = lua_tostring(lua_state_, -1);
    CL_LOG_ERROR("lua", std::string("lua_error:") + message)
        << "failed to evaluate dynamic param, error code: " << err_code << ", message: " << message
        << ", expression: " << expr;
    return false;
  }
  return true;
}
const folly::F14FastMap<absl::string_view, AttrTable *, absl::Hash<absl::string_view>> &
CommonRecoContext::GetAllItemTable() const {
  return item_data_;
}

roaring::Roaring *CommonRecoContext::GetResultBitMap() {
  return running_processor_table_->GetResultBitMap();
}

bool CommonRecoContext::AddFilterResult(size_t attr_index, const std::string &processor_name) {
  return running_processor_table_->AddFilterResult(attr_index, processor_name);
}

bool CommonRecoContext::SetFilterReason(const CommonRecoResult &result, const std::string &filter_reason) {
  return running_processor_table_->SetFilterReason(result, filter_reason);
}

absl::optional<absl::string_view> CommonRecoContext::GetFilterReason(const CommonRecoResult &result) const {
  return running_processor_table_->GetFilterReason(result);
}

const CommonRecoResult *CommonRecoContext::GetFilterResult(int index) const {
  return running_processor_table_->GetFilterResult(index);
}

void CommonRecoContext::ReserveFilterResults() {
  running_processor_table_->ReserveFilterResults();
}

void CommonRecoContext::ClearFilterResults() {
  running_processor_table_->ClearFilterResults();
}

int CommonRecoContext::GetFilterResultsSize() const {
  return running_processor_table_->GetFilterResultsSize();
}

void CommonRecoContext::SetTracebackFilterReason(const CommonRecoResult &result,
                                                 const std::string &filter_reason) {
  if (GetNeedStepInfo()) {
    CommonRecoRetrieveResult filter_result(result.item_key, result.reason);
    filter_results_.emplace_back(std::move(filter_result));
    auto it = std::find(filter_reasons_.begin(), filter_reasons_.end(), filter_reason);
    if (it != filter_reasons_.end()) {
      filter_reason_offsets_.emplace_back(it - filter_reasons_.begin());
    } else {
      filter_reason_offsets_.emplace_back(filter_reasons_.size());
      filter_reasons_.emplace_back(filter_reason);
    }
  }
}

const std::vector<CommonRecoRetrieveResult> &CommonRecoContext::GetTracebackFilterResult() const {
  return filter_results_;
}

const std::vector<int32> &CommonRecoContext::GetTracebackFilterReasonOffset() const {
  return filter_reason_offsets_;
}

const std::vector<std::string> &CommonRecoContext::GetTracebackFilterReason() const {
  return filter_reasons_;
}

void CommonRecoContext::ClearTracebackFilterResult() {
  filter_results_.clear();
}
void CommonRecoContext::ClearTracebackFilterResultOffset() {
  filter_reason_offsets_.clear();
}

void CommonRecoContext::ClearTracebackFilterReason() {
  filter_reasons_.clear();
}

void CommonRecoContext::SetupAttrTypesFromConfig(const base::Json *config) {
  if (!config) return;
  if (auto *common_attr_types = config->Get("common_attr_types")) {
    for (const auto &common_attr_type : common_attr_types->array()) {
      if (common_attr_type && common_attr_type->IsObject()) {
        SpecifyCommonAttrType(common_attr_type->GetString("attr_name"),
                              RecoUtil::ParseAttrType(common_attr_type->GetString("attr_type")));
      }
    }
  }

  if (auto *item_attr_types = config->Get("item_attr_types")) {
    for (const auto &item_attr_type : item_attr_types->array()) {
      if (item_attr_type && item_attr_type->IsObject()) {
        SpecifyItemAttrType(item_attr_type->GetString("table_name"), item_attr_type->GetString("attr_name"),
                            RecoUtil::ParseAttrType(item_attr_type->GetString("attr_type")));
      }
    }
  }
}

void CommonRecoContext::AddPipelineCpuCost(const std::string &pipeline_name, int64 cpu_cost) {
  pipeline_cpu_cost_ns_.Add(pipeline_name, cpu_cost);
}

void CommonRecoContext::AddTableCpuCost(const std::string &table_name, int64 cpu_cost) {
  table_cpu_cost_ns_.Add(table_name, cpu_cost);
}

CpuTimeContainer &CommonRecoContext::GetPipelineCpuCost() {
  return pipeline_cpu_cost_ns_;
}

CpuTimeContainer &CommonRecoContext::GetTableCpuCost() {
  return table_cpu_cost_ns_;
}

void CommonRecoContext::SetNeedPipelineCpuCost(bool need_pipeline_cpu_cost) {
  need_pipeline_cpu_cost_ = need_pipeline_cpu_cost;
}
bool CommonRecoContext::GetNeedPipelineCpuCost() const {
  return need_pipeline_cpu_cost_;
}

void CommonRecoContext::SetNeedTableCpuCost(bool need_table_cpu_cost) {
  need_table_cpu_cost_ = need_table_cpu_cost;
}
bool CommonRecoContext::GetNeedTableCpuCost() const {
  return need_table_cpu_cost_;
}

IncrCpuTimer CommonRecoContext::GetIncrCpuTimer() {
  if (running_processor_ != nullptr) {
    return IncrCpuTimer(running_processor_->GetPipelineName(), running_processor_->GetTableName(),
                        need_pipeline_cpu_cost_, need_table_cpu_cost_, &pipeline_cpu_cost_ns_,
                        &table_cpu_cost_ns_);
  }
  return IncrCpuTimer("", "", false, false, &pipeline_cpu_cost_ns_, &table_cpu_cost_ns_);
}

}  // namespace platform

}  // namespace ks
