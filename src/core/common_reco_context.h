#pragma once

#include <algorithm>
#include <atomic>
#include <memory>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_set.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/core/common_reco_kess_batch_concurrent_waiter.h"
#include "dragon/src/core/common_reco_kess_batch_waiter.h"
#include "dragon/src/core/common_reco_rpc_controller.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/module/lua_c_functions.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/cpu_cost_util.h"
#include "dragon/src/util/cpuinfo_util.h"
#include "dragon/src/util/logging_util.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco/common/browse_set/browse_set_result.h"
#include "ks/reco_proto/common_reco/leaf/fbs/flatten_common_reco_generated.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "ks/reco_proto/proto/reco_browse_set.pb.h"
#include "ks/reco_pub/reco/distributed_photo_info/protoutil/flattened_attr_kv_item.h"
#include "serving_base/perfutil/perfutil_wrapper.h"
#include "serving_base/util/duplicate_hash.h"
#include "serving_base/util/scope_exit.h"
#include "third_party/abseil/absl/hash/hash.h"
#include "third_party/croaring/roaring.hh"

DECLARE_bool(enable_numa_aware);
DECLARE_bool(enable_grpc_streaming_server);

namespace ks {
namespace platform {

typedef std::vector<CommonRecoResult>::const_iterator RecoResultConstIter;

class CommonRecoHandler;
class StreamingHandler;

/**
 * CommonRecoLeaf CommonRecoContext
 */
class CommonRecoContext : public AddibleRecoContextInterface {
 public:
  CommonRecoContext();
  virtual ~CommonRecoContext();

  void ClearContext();

  void ClearSubflowStepInfo() override;
  CommonRecoStepInfo *GetNewStepInfo() override;
  int GetSubflowStepInfosCount() override;
  void IncrTracebackSequence() override;
  const std::vector<int> &GetTracebackSequence() const override;
  void SetTracebackSequence(std::vector<int> &&sequence) override;
  bool GetNeedStepInfo() const override;
  void SetNeedStepInfo(bool value) override;
  bool GetShouldCompressItem() override;
  void SetShouldCompressItem(bool value) override;
  const std::string &GetTracebackDataVersion() const override;
  void SetTracebackDataVersion(const std::string &data_version) override;
  bool GetShouldDropStudioField() override;
  void SetShouldDropStudioField(bool value) override;
  PackedItemAttrValue::CompressMode GetTracebackCompressMode() override;
  void SetTracebackCompressMode(PackedItemAttrValue::CompressMode compress_mode) override;
  const std::vector<std::string> *GetTracebackItemAttrs() const override;
  void SetTracebackItemAttrs(absl::Span<const std::string> return_item_attrs) override;
  const std::vector<std::string> *GetTracebackCommonAttrs() const override;
  void SetTracebackCommonAttrs(absl::Span<const std::string> return_common_attrs) override;
  std::shared_ptr<folly::F14FastSet<std::string>> GetTracebackProcessors() const override;
  void SetTracebackProcessors(std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors) override;
  std::shared_ptr<folly::F14FastSet<std::string>> GetTracebackProcessorsBlacklist() const override;
  void SetTracebackProcessorsBlacklist(
      std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors_blacklist) override;
  CommonRecoStepInfo *GetProcessorStepInfo(int index) override;
  bool IsTracebackResultChanged() const override;
  void SetTracebackResultChanged(bool value) override;
  void AddTracebackResultChanged(const std::string &table_name) override;
  folly::F14FastSet<std::string> *GetAccumulatedTracebackTables() override;
  void AddAccumulatedTracebackItemAttrs(const std::string &attr_name) override;
  folly::F14FastSet<std::string> *GetAccumulatedTracebackItemAttrs() override;
  void SetTracebackCollectMode(CollectMode collect_mode) override;
  CollectMode GetTracebackCollectMode() const override;
  void SetTracebackWhiteListUserReportAllProcessors(bool report_all_processors) override;
  bool GetTracebackWhiteListUserReportAllProcessors() const override;

  const CommonRecoRequest *GetRequest() const override;

  std::shared_ptr<void> GetSharedRequest();

  ExecutionStatus GetExecutionStatus() const override;

  void SetExecutionStatus(ExecutionStatus status) override;

  uint64 GetUserId() const override;
  const std::string &GetDeviceId() const override;
  const std::string &GetRequestType() const override;
  int64 GetRequestTime() const override;
  int64 GetProcessCounter() const override;
  int64 GetRequestNum() const override;
  const std::string &GetRequestId() const override;
  bool IsDebugRequest() const override;
  bool NeedTraceback() const override;

  const std::unordered_set<std::string> &GetCommonAttrsInRequest() const override;

  const std::unordered_set<std::string> &GetItemAttrsInRequest() const override;

  const CommonRecoBaseProcessor *GetRunningProcessor() const override;

  const RecoProcessorMetaData *GetRunningProcessorMetaData() const override;

  void SetRunningProcessor(const void *processor) override;

  void SetMainTable(const std::string &table_name);

  int64 GetElapsedTime() const final;

  void ResetStageStartTime(int64 stage_time = 0);

  int64 RecordStageInfo(const std::string &stage_name) override;

  int64 RecordStageInfo(const std::string &stage_name, const std::string &table_name);

  int64 GetStageStartTime() const;

  CommonRecoResult NewCommonRecoResult(uint64 item_key, int reason, double score = 0.0, int channel = 0,
                                       absl::optional<absl::string_view> table_name = absl::nullopt);

  // NOTE(zhaoyang09): override_reason_attr 是否可以复写系统默认 _REASON_ attr
  CommonRecoResult &AddCommonRecoResultToTable(AttrTable *table, uint64 item_key, int reason,
                                               double score = 0.0, int channel = 0,
                                               bool override_reason_attr = false) final;

  CommonRecoResult &AddCommonRecoResultToTable(absl::string_view table_name, uint64 item_key, int reason,
                                               double score = 0.0, int channel = 0,
                                               bool override_reason_attr = false) final;

  void AddCommonRecoResultFromRequestToTable(AttrTable *table, CommonRecoResult *result);

  CommonRecoResult &AddCommonRecoResult(uint64 item_key, int reason, double score = 0.0, int channel = 0,
                                        bool override_reason_attr = false) final;
  CommonRecoResult &AddCommonRecoResult(const CommonRecoResult &result,
                                        bool override_reason_attr = false) final;

  const std::vector<CommonRecoResult> &GetCommonRecoResults(absl::string_view table_name) final;
  const std::vector<CommonRecoResult> &GetCommonRecoResults() final;

  // CommonAttr Get APIs
  AttrType GetCommonAttrType(absl::string_view attr_name) const override;

  bool HasCommonAttr(absl::string_view attr_name) const override;

  absl::optional<int64> GetIntCommonAttr(absl::string_view attr_name) const override;
  absl::optional<int64> GetIntCommonAttr(const CommonAttr *attr) const override;
  absl::optional<double> GetDoubleCommonAttr(absl::string_view attr_name) const override;
  absl::optional<double> GetDoubleCommonAttr(const CommonAttr *attr) const override;
  absl::optional<absl::string_view> GetStringCommonAttr(absl::string_view attr_name) const override;
  absl::optional<absl::string_view> GetStringCommonAttr(const CommonAttr *attr) const override;
  absl::optional<absl::Span<const int64>> GetIntListCommonAttr(absl::string_view attr_name) const override;
  absl::optional<absl::Span<const int64>> GetIntListCommonAttr(const CommonAttr *attr) const override;
  absl::optional<absl::Span<const double>> GetDoubleListCommonAttr(
      absl::string_view attr_name) const override;
  absl::optional<absl::Span<const double>> GetDoubleListCommonAttr(const CommonAttr *attr) const override;
  absl::optional<std::vector<absl::string_view>> GetStringListCommonAttr(
      absl::string_view attr_name) const override;
  absl::optional<std::vector<absl::string_view>> GetStringListCommonAttr(
      const CommonAttr *attr) const override;
  const boost::any *GetExtraCommonAttr(absl::string_view attr_name) const override;
  const boost::any *GetExtraCommonAttr(const CommonAttr *attr) const override;

  // CommonAttr Set APIs
  bool ClearCommonAttr(absl::string_view attr_name, bool check_overwrite = true) override;
  bool ClearCommonAttr(CommonAttr *attr, bool check_overwrite = true) override;
  bool SetIntCommonAttr(absl::string_view attr_name, int64 value, bool if_not_exist = false,
                        bool check_overwrite = true) override;
  bool SetIntCommonAttr(CommonAttr *attr, int64 value, bool if_not_exist = false,
                        bool check_overwrite = true) override;
  bool SetDoubleCommonAttr(absl::string_view attr_name, double value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetDoubleCommonAttr(CommonAttr *attr, double value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetFloat32CommonAttr(absl::string_view attr_name, float value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetFloat32CommonAttr(CommonAttr *attr, float value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetFloat16CommonAttr(absl::string_view attr_name, float16_t value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetFloat16CommonAttr(CommonAttr *attr, float16_t value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetStringCommonAttr(absl::string_view attr_name, std::string value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool SetStringCommonAttr(CommonAttr *attr, std::string value, bool if_not_exist = false,
                           bool check_overwrite = true) override;
  bool ResetIntListCommonAttr(absl::string_view attr_name, int capacity = 0, bool if_not_exist = false,
                              bool check_overwrite = true) override;
  bool ResetIntListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                              bool check_overwrite = true) override;
  bool ResetDoubleListCommonAttr(absl::string_view attr_name, int capacity = 0, bool if_not_exist = false,
                                 bool check_overwrite = true) override;
  bool ResetDoubleListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                                 bool check_overwrite = true) override;
  bool ResetStringListCommonAttr(absl::string_view attr_name, int capacity = 0, bool if_not_exist = false,
                                 bool check_overwrite = true) override;
  bool ResetStringListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                                 bool check_overwrite = true) override;
  bool AppendIntListCommonAttr(absl::string_view attr_name, int64 value) override;
  bool AppendIntListCommonAttr(CommonAttr *attr, int64 value) override;
  bool AppendDoubleListCommonAttr(absl::string_view attr_name, double value) override;
  bool AppendDoubleListCommonAttr(CommonAttr *attr, double value) override;
  bool AppendStringListCommonAttr(absl::string_view attr_name, std::string value) override;
  bool AppendStringListCommonAttr(CommonAttr *attr, std::string value) override;
  bool SetIntListCommonAttr(absl::string_view attr_name, std::vector<int64> &&value,
                            bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetIntListCommonAttr(CommonAttr *attr, std::vector<int64> &&value, bool if_not_exist = false,
                            bool check_overwrite = true) override;
  bool SetDoubleListCommonAttr(absl::string_view attr_name, std::vector<double> &&value,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleListCommonAttr(CommonAttr *attr, std::vector<double> &&value, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool SetFloat32ListCommonAttr(absl::string_view attr_name, std::vector<float> &&value,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ListCommonAttr(CommonAttr *attr, std::vector<float> &&value, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool SetFloat16ListCommonAttr(absl::string_view attr_name, std::vector<float16_t> &&value,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ListCommonAttr(CommonAttr *attr, std::vector<float16_t> &&value, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool SetStringListCommonAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringListCommonAttr(CommonAttr *attr, std::vector<std::string> &&value, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool SetExtraCommonAttr(absl::string_view attr_name, boost::any &&value, bool if_not_exist = false,
                          bool check_overwrite = true) override;
  bool SetExtraCommonAttr(CommonAttr *attr, boost::any &&value, bool if_not_exist = false,
                          bool check_overwrite = true) override;

  bool InitItemAttr(absl::string_view attr_name, AttrType attr_type, size_t max_attr_index);

  // NOTE(zhaoyang09): read_only 表示 attr 是否修改为只读模式
  void CloneCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name, bool shadow_copy,
                       bool read_only = false);

  void CloneFixedCommonAttr(CommonRecoContext *from_context, bool shadow_copy);

  // NOTE(zhaoyang09): read_only 表示 attr 是否修改为只读模式
  void CloneItemAttr(ReadableRecoContextInterface *context, absl::string_view attr_name, bool shadow_copy,
                     bool read_only = false);

  void CloneTable(ReadableRecoContextInterface *context, const std::vector<std::string> &attrs,
                  std::string table_name, bool shadow_copy, bool read_only = false);

  // 用于完整的克隆结果集
  bool CloneResults(CommonRecoContext *from_context);
  // 用于带迭代器的克隆结果集
  bool CloneTargetResults(CommonRecoContext *from_context, RecoResultConstIter begin,
                          RecoResultConstIter end);

  bool DeepCloneCommonAttr(const CommonAttr *from);

  bool ShadowCloneCommonAttr(absl::string_view attr_name, CommonAttr *from, bool read_only = false);

  bool DeepCloneItemAttr(const ItemAttr *from);

  bool ShadowCloneItemAttr(absl::string_view attr_name, ItemAttr *from, bool read_only = false);

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  // NOTE(caohongjin): 危险！下面接口禁止私自使用，使用后导致线程安全问题的由使用者负责
  void BackupCommonAttrTable(ReadableRecoContextInterface *context);
  void ShadowCloneItemAttrTable(ReadableRecoContextInterface *context, const std::string &table_name);
#endif

  // ItemAttr Get APIs
  AttrType GetItemAttrType(absl::string_view attr_name) const override;

  bool HasItemAttr(absl::string_view attr_name) const override;

  bool HasItemAttr(uint64 item_key, absl::string_view attr_name) const override;

  bool HasItemAttr(const CommonRecoResult &item_result, const ItemAttr *item_attr) const override;

  absl::optional<int64> GetIntItemAttr(uint64 item_key, absl::string_view attr_name) const override;

  absl::optional<int64> GetIntItemAttr(const CommonRecoResult &item_result,
                                       const ItemAttr *item_attr) const override;

  absl::optional<int64> GetIntItemAttr(uint64 item_key, const ItemAttr *item_attr) const override;

  absl::optional<double> GetDoubleItemAttr(uint64 item_key, absl::string_view attr_name) const override;

  absl::optional<double> GetDoubleItemAttr(const CommonRecoResult &item_result,
                                           const ItemAttr *item_attr) const override;

  absl::optional<double> GetDoubleItemAttr(uint64 item_key, const ItemAttr *item_attr) const override;

  absl::optional<absl::string_view> GetDefaultStringItemAttr(absl::string_view attr_name) const override;

  absl::optional<absl::string_view> GetDefaultStringItemAttr(const ItemAttr *item_attr) const override;

  absl::optional<absl::string_view> GetStringItemAttr(uint64 item_key,
                                                      absl::string_view attr_name) const override;

  absl::optional<absl::string_view> GetStringItemAttr(const CommonRecoResult &item_result,
                                                      const ItemAttr *item_attr) const override;

  absl::optional<absl::string_view> GetStringItemAttr(uint64 item_key,
                                                      const ItemAttr *item_attr) const override;

  absl::optional<absl::Span<const int64>> GetIntListItemAttr(uint64 item_key,
                                                             absl::string_view attr_name) const override;

  absl::optional<absl::Span<const int64>> GetIntListItemAttr(const CommonRecoResult &item_result,
                                                             const ItemAttr *item_attr) const override;

  absl::optional<absl::Span<const int64>> GetIntListItemAttr(uint64 item_key,
                                                             const ItemAttr *item_attr) const override;

  absl::optional<absl::Span<const double>> GetDoubleListItemAttr(uint64 item_key,
                                                                 absl::string_view attr_name) const override;

  absl::optional<absl::Span<const double>> GetDoubleListItemAttr(const CommonRecoResult &item_result,
                                                                 const ItemAttr *item_attr) const override;

  absl::optional<absl::Span<const double>> GetDoubleListItemAttr(uint64 item_key,
                                                                 const ItemAttr *item_attr) const override;

  absl::optional<std::vector<absl::string_view>> GetDefaultStringListItemAttr(
      absl::string_view attr_name) const override;

  absl::optional<std::vector<absl::string_view>> GetDefaultStringListItemAttr(
      const ItemAttr *item_attr) const override;

  absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      uint64 item_key, absl::string_view attr_name) const override;

  absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      const CommonRecoResult &item_result, const ItemAttr *item_attr) const override;

  absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      uint64 item_key, const ItemAttr *item_attr) const override;

  const boost::any *GetExtraItemAttr(uint64 item_key, absl::string_view attr_name) const override;

  const boost::any *GetExtraItemAttr(const CommonRecoResult &item_result,
                                     const ItemAttr *item_attr) const override;

  const boost::any *GetExtraItemAttr(uint64 item_key, const ItemAttr *item_attr) const override;

  // ItemAttr Set APIs
  bool ClearItemAttrForAll(absl::string_view attr_name, bool check_overwrite = true) override;

  bool ClearItemAttr(uint64 item_key, absl::string_view attr_name) override;

  bool ClearItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                     bool check_overwrite = true) override;

  bool SetIntItemAttr(uint64 item_key, absl::string_view attr_name, int64 value, bool if_not_exist = false,
                      bool check_overwrite = true) override;
  bool SetIntItemAttr(uint64 item_key, ItemAttr *attr, int64 value, bool if_not_exist = false,
                      bool check_overwrite = true) override;
  bool SetIntItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int64 value,
                      bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleItemAttr(uint64 item_key, absl::string_view attr_name, double value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleItemAttr(uint64 item_key, ItemAttr *attr, double value, bool if_not_exist = false,
                         bool check_overwrite = true) override;
  bool SetDoubleItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, double value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringItemAttr(uint64 item_key, absl::string_view attr_name, std::string value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringItemAttr(uint64 item_key, ItemAttr *attr, std::string value, bool if_not_exist = false,
                         bool check_overwrite = true) override;
  bool SetStringItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::string value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ItemAttr(uint64 item_key, absl::string_view attr_name, float value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ItemAttr(uint64 item_key, ItemAttr *attr, float value, bool if_not_exist = false,
                         bool check_overwrite = true) override;
  bool SetFloat32ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, float value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ItemAttr(uint64 item_key, absl::string_view attr_name, float16_t value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ItemAttr(uint64 item_key, ItemAttr *attr, float16_t value, bool if_not_exist = false,
                         bool check_overwrite = true) override;
  bool SetFloat16ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, float16_t value,
                         bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetIntListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<int64> &&value,
                          bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetIntListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<int64> &&value,
                          bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::vector<int64> &&value,
                          bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<double> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<double> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::vector<double> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<float> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<float> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat32ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::vector<float> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<float16_t> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<float16_t> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetFloat16ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                             std::vector<float16_t> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<std::string> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<std::string> &&value,
                             bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                             std::vector<std::string> &&value, bool if_not_exist = false,
                             bool check_overwrite = true) override;
  bool SetExtraItemAttr(uint64 item_key, absl::string_view attr_name, boost::any &&value,
                        bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetExtraItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, boost::any &&value,
                        bool if_not_exist = false, bool check_overwrite = true) override;
  bool SetExtraItemAttr(uint64 item_key, ItemAttr *attr, boost::any &&value, bool if_not_exist = false,
                        bool check_overwrite = true) override;
  bool ResetIntListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                            bool if_not_exist = false, bool check_overwrite = true) override;
  bool ResetIntListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0, bool if_not_exist = false,
                            bool check_overwrite = true) override;
  bool ResetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                            bool if_not_exist = false, bool check_overwrite = true) override;
  bool ResetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool ResetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool ResetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool ResetStringListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool ResetStringListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0, bool if_not_exist = false,
                               bool check_overwrite = true) override;
  bool ResetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                               bool if_not_exist = false, bool check_overwrite = true) override;
  bool AppendIntListItemAttr(uint64 item_key, absl::string_view attr_name, int64 value) override;

  bool AppendIntListItemAttr(uint64 item_key, ItemAttr *attr, int64 value) override;

  bool AppendIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int64 value) override;

  bool AppendDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, double value) override;

  bool AppendDoubleListItemAttr(uint64 item_key, ItemAttr *attr, double value) override;

  bool AppendDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, double value) override;

  bool AppendStringListItemAttr(uint64 item_key, absl::string_view attr_name, std::string value) override;

  bool AppendStringListItemAttr(uint64 item_key, ItemAttr *attr, std::string value) override;

  bool AppendStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                std::string value) override;

  ItemAttr *GetItemAttrAccessor(absl::string_view attr_name) override;

  ItemAttr *GetItemAttr(absl::string_view attr_name) const override;

  AttrValue *GetItemAttrFromTable(absl::string_view attr_name, absl::string_view table_name) override;

  AttrValue *GetOrInsertItemAttrFromTable(absl::string_view attr_name, absl::string_view table_name) override;

  CommonAttr *GetCommonAttrAccessor(absl::string_view attr_name) override;

  CommonAttr *GetCommonAttr(absl::string_view attr_name) const override;

  bool InBrowseSet(uint64 item_key) const override;

  int64 GetBrowseSetSize() const override;

  std::vector<uint64> GetLatestBrowsedItems(int count = 0) const override;

  void AddItemToBrowseSet(uint64 item_key);

  void SetupRecoBrowseSet(const ks::reco::BrowseSet &browse_set);

  std::shared_ptr<const ks::reco::BrowseSetResult> GetRecoBrowseSet() const;

  bool ShadowCopyRecoBrowseSet(const CommonRecoContext *context);

  const std::unordered_map<uint64, int> &GetForceInsertMap() const override;

  void AddForceInsertMapItem(uint64 item_key, int position);

  // other necessary Getters/Setters
  CommonRecoKessBatchWaiter *GetBatchWaiter(const std::string &trigger, bool create_if_missing) final;

  CommonRecoLocalAsyncWaiter *GetLocalAsyncWaiter(const std::string &trigger, bool create_if_missing) final;

  std::vector<std::string> *GetAsyncUpstreamProcessors(absl::string_view processor_name,
                                                       bool create_if_missing) override;

  void SetLocalAsyncTimeoutWaiter(CommonRecoLocalAsyncConcurrentWaiter *waiter);

  CommonRecoLocalAsyncConcurrentWaiter *GetLocalAsyncTimeoutWaiter() override;

  void SetDetachedBatchWaiter(CommonRecoKessBatchConcurrentWaiter *waiter);

  CommonRecoKessBatchConcurrentWaiter *GetDetachedBatchWaiter() override;

  int WaitAllCallbacks() override;

  std::vector<CommonRecoResult> *GetRecoResults();

  std::vector<CommonRecoResult> *GetRecoResults(absl::string_view table_name);

  std::string GetCaller() const;

  void SetRpcController(std::unique_ptr<CommonRecoRpcControllerInterface> rpc_controller);

  bool IsRequestCancelled() const override;

  int32 GetTimeLeft() const override;

  RpcType GetRpcType() const override;

  void SetRequest(const CommonRecoRequest *req);

  void SetSharedRequest(std::shared_ptr<void> req);

  void SetFlattenRequest(const ks::platform::fbs::FlattenCommonRecoRequest *request);

  void AddCommonAttrFromRequest(absl::string_view attr_name);

  void AddItemAttrFromRequest(absl::string_view attr_name);

  void AddFixedAttrs(absl::string_view attr_name);

  std::shared_ptr<std::unordered_set<std::string>> GetFixedAttrs() const;

  bool ForceSendTraceback() const;

  // ONLY FOR DEBUG USE!!!
  void PurposelyResetUserId(uint64 uid) override;
  // ONLY FOR DEBUG USE!!!
  void PurposelyResetDeviceId(const std::string &did) override;
  // ONLY FOR DEBUG USE!!!
  void PurposelyResetRequestTime(int64 time_ms) override;
  // ONLY FOR DEBUG USE!!!
  void PurposelyResetRequestType(const std::string &req_type);
  // ONLY FOR DEBUG USE!!!
  void PurposelyResetRequestNum(int64 req_num);
  // ONLY FOR DEBUG USE!!!
  void PurposelyResetRequestId(const std::string &req_id);

  void CopyMetaInfo(const CommonRecoContext *from_context) {
    PurposelyResetUserId(from_context->GetUserId());
    PurposelyResetDeviceId(from_context->GetDeviceId());
    PurposelyResetRequestId(from_context->GetRequestId());
    PurposelyResetRequestTime(from_context->GetRequestTime());
    PurposelyResetRequestType(from_context->GetRequestType());
    PurposelyResetRequestNum(from_context->GetRequestNum());

    // sub_flow pipline 里面 context 绑核信息未初始化时，进行初始化。
    if (FLAGS_enable_numa_aware) {
      if (from_context->GetNumaId() >= 0 && GetNumaId() == -1) {
        SetNumaId(from_context->GetNumaId());
      }
    } else {
      if (from_context->GetCpuAffinityInfo()->thread_index >= 0 && GetCpuAffinityInfo()->thread_index < 0) {
        SetCpuAffinityInfo(*from_context->GetCpuAffinityInfo());
      }
    }

    if (FLAGS_enable_grpc_streaming_server) {
      SetStreamingHandler(from_context->GetStreamingHandler());
    }
  }

  // merge table，拷贝/move 指定的 item_attrs，同时可以选择将结果集一起合并
  int MergeTable(CommonRecoContext *from_context, const std::string &pipeline_name,
                 const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
                 const std::string &table_name, bool merge_and_overwrite, bool merge_result,
                 bool deduplicate_results = false, int result_num = -1,
                 bool merge_item_attr_for_all_items = false, int64 item_channel = -1);

  // merge 结果集、CommonAttr、ItemAttr，同时回收 attr 的引用计数
  int MergeContext(CommonRecoContext *from_context, int result_num, bool deduplicate_results,
                   const std::unordered_map<std::string, std::string> &merge_common_attrs,
                   const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
                   const std::string &pipeline_name, bool merge_and_overwrite, bool merge_result = true,
                   bool merge_item_attr_for_all_items = false, int64 item_channel = -1);

  // 需要 MergeResult 生成的 merged_index_pairs, 请严格保证 MergeResult 函数在 MergeItemAttr 函数之前执行。
  void MergeItemAttrs(
      CommonRecoContext *from_context, AttrTable *table, AttrTable *from_table,
      const folly::F14FastMap<std::string, AttrConfig> &merge_item_attrs,
      const folly::F14FastMap<const CommonRecoResult *, const CommonRecoResult *> &merged_index_map,
      bool merge_and_overwrite, bool could_zero_copy);

  bool MergeCommonAttr(CommonRecoContext *from_context, const std::string &attr_name,
                       const std::string &attr_alias, bool merge_and_overwrite);

  void MergeCommonAttrs(CommonRecoContext *from_context,
                        const std::unordered_map<std::string, std::string> &merge_common_attrs,
                        bool merge_and_overwrite);

  void DereferenceItemAttr(absl::string_view attr_name);
  void DereferenceCommonAttr(absl::string_view attr_name);

  void SetCpuAffinityInfo(const CpuAffinityInfo &cpu_affinity_info);

  const CpuAffinityInfo *GetCpuAffinityInfo() const;

  void SetNumaId(int id);

  int GetNumaId() const override;

  StreamingHandler *GetStreamingHandler() const override;

  int GetStreamingLoopIndex() const override;

  void SetStreamingLoopIndex(int streaming_index);

  void SetStreamingHandler(StreamingHandler *streaming_handler);

  StreamingStatus GetStreamingStatus() const override;

  void SetStreamingStatus(StreamingStatus status) override;

  bool EvalIntParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                               int64 *int_val) const override;

  bool EvalDoubleParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                  double *double_val, bool try_int_attr = false) const override;

  bool EvalStringParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                  std::string *string_val) const override;

  bool EvalIntListParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                   std::vector<int64> *int_list_val) const override;

  bool EvalDoubleListParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                      std::vector<double> *double_list_val) const override;

  void MarkInvalid();

  bool IsValid() const;

  AttrTable *GetOrInsertDataTable(absl::string_view table_name) override;

  bool CreateLogicalTable(absl::string_view table_name, AttrTable *from_table) override;

  const AttrTable *GetTable(absl::string_view table_name) const override;

  AttrTable *GetMutableTable(absl::string_view table_name) const override;

  const std::vector<AttrValue *> &GetAllCommonAttrs() const override;

  const std::vector<AttrValue *> &GetAllItemAttrs() const override;

  const std::vector<AttrValue *> &GetAllItemAttrsInTable(absl::string_view table_name) override;

  roaring::Roaring *GetResultBitMap();

  bool AddFilterResult(size_t attr_index, const std::string &processor_name);

  bool SetFilterReason(const CommonRecoResult &result, const std::string &filter_reason) override;

  absl::optional<absl::string_view> GetFilterReason(const CommonRecoResult &result) const;

  void ReserveFilterResults();

  void ClearFilterResults();

  int GetFilterResultsSize() const;

  const CommonRecoResult *GetFilterResult(int index) const;

  void SetTracebackFilterReason(const CommonRecoResult &result, const std::string &filter_reason);
  const std::vector<CommonRecoRetrieveResult> &GetTracebackFilterResult() const;
  const std::vector<int32> &GetTracebackFilterReasonOffset() const;
  const std::vector<std::string> &GetTracebackFilterReason() const;
  void ClearTracebackFilterResult();
  void ClearTracebackFilterResultOffset();
  void ClearTracebackFilterReason();

  const folly::F14FastMap<absl::string_view, AttrTable *, absl::Hash<absl::string_view>> &GetAllItemTable()
      const override;

  void SpecifyCommonAttrType(const std::string &attr_name, AttrType attr_type);
  void SpecifyItemAttrType(const std::string &table_name, const std::string &attr_name, AttrType attr_type);

  // 插入一个外部管理数据的 table，context 不会清除该 table 的值，也不负责该 table 的内存回收
  // 注意！如果已存在和插入 table 同名的内部 table，有内存泄漏的风险！
  bool InsertExternalTable(AttrTable *table) override;

  void SetupAttrTypesFromConfig(const base::Json *config);

  void AddPipelineCpuCost(const std::string &pipeline_name, int64 cpu_cost) final;
  void AddTableCpuCost(const std::string &table_name, int64 cpu_cost) final;
  CpuTimeContainer &GetPipelineCpuCost() final;
  CpuTimeContainer &GetTableCpuCost() final;
  void SetNeedPipelineCpuCost(bool need_pipeline_cpu_cost);
  bool GetNeedPipelineCpuCost() const final;
  void SetNeedTableCpuCost(bool need_table_cpu_cost);
  bool GetNeedTableCpuCost() const final;
  IncrCpuTimer GetIncrCpuTimer() final;

  void ReportCpuTimeCost();

 protected:
  CommonAttr *GetOrInsertCommonAttr(absl::string_view attr_name);

#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  CommonAttr *GetOrInsertCommonAttr(absl::string_view attr_name, bool return_null_if_not_exist);
#endif

  ItemAttr *GetOrInsertItemAttr(absl::string_view attr_name);

 private:
  int MergeResult(CommonRecoContext *from_context, int result_num, bool deduplicate_results,
                  const std::string &pipeline_name, AttrTable *table, AttrTable *from_table,
                  int64 item_channel = -1);

  bool ExecLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr) const;

  void SetResultReasonAttr(const CommonRecoResult &result, bool override_reason_attr);

 protected:
  const CommonRecoRequest *request_ = nullptr;
  const fbs::FlattenCommonRecoRequest *flatten_request_ = nullptr;

  std::atomic<ExecutionStatus> execution_status_{ExecutionStatus::UNKNOWN};

  uint64 user_id_ = 0;
  std::string device_id_;
  std::string request_type_;
  int64 request_time_ms_ = 0;
  std::string request_id_;
  int64 request_num_ = 0;
  bool debug_ = false;
  bool need_traceback_ = false;
  int64 request_start_time_ = 0;
  int64 stage_start_time_ = 0;

  int64 process_counter_ = 0;

  const CommonRecoBaseProcessor *running_processor_ = nullptr;

  // 策略中间状态数据表，仅支持单行 AttrTable
  std::unique_ptr<AttrTable> common_data_;
#ifdef DRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA
  // NOTE(caohongjin): 用于二次查询, 默认关闭，目前只有打开 mix_by_sub_flow 的 direct_access_parent_flow_data
  // 时才会生效
  AttrTable *common_data_backup_ = nullptr;
#endif
  AttrTable *default_item_data_ = nullptr;
  // 结果集表（支持多结果集产出），支持多行 AttrTable
  folly::F14FastMap<absl::string_view, AttrTable *, absl::Hash<absl::string_view>> item_data_;
  std::unordered_set<AttrTable *> replaced_internal_tables_;

  std::unordered_set<std::string> common_attrs_from_request_;
  std::unordered_set<std::string> item_attrs_from_request_;
  std::shared_ptr<std::unordered_set<std::string>> fixed_common_attrs_from_request_ =
      std::make_shared<std::unordered_set<std::string>>();

  // 存储 request 携带的 item 强插信息
  std::unordered_map<uint64, int> force_insert_map_;

  folly::F14FastSet<uint64> browse_set_;

  folly::F14FastMap<std::string, std::unique_ptr<CommonRecoKessBatchWaiter>> batch_waiters_;

  folly::F14FastMap<std::string, std::unique_ptr<CommonRecoLocalAsyncWaiter>> local_async_waiters_;

  base::AutoDeleteHash<std::vector<std::string>> async_upstream_processors_;

  CommonRecoLocalAsyncConcurrentWaiter *local_async_timeout_waiter_ptr_ = nullptr;

  CommonRecoKessBatchConcurrentWaiter *detached_batch_waiter_ptr_ = nullptr;

  std::unique_ptr<CommonRecoRpcControllerInterface> rpc_controller_;

  // 计算 lua 表达式的 state
  lua_State *lua_state_ = nullptr;

  // 新 browseSet
  std::shared_ptr<const ks::reco::BrowseSetResult> reco_browse_set_result_ = nullptr;

  // traceback 相关数据
  std::vector<std::unique_ptr<CommonRecoStepInfo>> subflow_step_infos_;
  int subflow_step_infos_count_ = 0;
  std::vector<int> traceback_sequence_;
  bool need_step_info_ = false;
  bool should_compress_item_ = false;
  std::string traceback_data_version_ = kTracebackDataVersion;
  bool should_drop_studio_field_ = true;
  PackedItemAttrValue::CompressMode traceback_compress_mode_ = PackedItemAttrValue_CompressMode_NO_COMPRESS;
  std::vector<std::string> traceback_item_attrs_;
  std::vector<std::string> traceback_common_attrs_;
  std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors_ = nullptr;
  std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors_blacklist_ = nullptr;
  bool result_changed_ = false;
  folly::F14FastSet<std::string> accumulated_traceback_tables_;
  folly::F14FastSet<std::string> accumulated_traceback_item_attrs_;
  CollectMode traceback_collect_mode_ = UNKNOWN_COLLECT_MODE;
  bool traceback_white_list_user_report_all_processors_ = false;
  std::vector<CommonRecoRetrieveResult> filter_results_;
  std::vector<int32> filter_reason_offsets_;
  std::vector<std::string> filter_reasons_;

  // Cpu 消耗
  bool need_pipeline_cpu_cost_ = false;
  bool need_table_cpu_cost_ = false;
  CpuTimeContainer pipeline_cpu_cost_ns_;
  CpuTimeContainer table_cpu_cost_ns_;

  CpuAffinityInfo cpu_affinity_info_;
  int numa_id_ = -1;

  bool enable_attr_check = true;

  StreamingHandler *streaming_handler_ = nullptr;
  int streaming_loop_index_ = -1;

 private:
  // NOTE(zhaoyang09): 记录本 context 中 _REASON_ item_attr 的地址
  ItemAttr *reason_attr_ = nullptr;
  // NOTE(zhaoyang09): 用来简单的存放 request_ 的 shared_ptr 用来控制延迟释放。
  std::shared_ptr<void> shared_request_;

  std::atomic_bool valid_{true};
};

}  // namespace platform
}  // namespace ks
