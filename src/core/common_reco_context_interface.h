#pragma once

#include <google/protobuf/message.h>
#include <kess/rpc/batch_waiter.h>
#include <boost/any.hpp>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/common/basic_types.h"
#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/core/common_reco_kess_batch_waiter.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/util/attr_io_check_util.h"
#include "dragon/src/util/cpu_cost_util.h"
#include "dragon/src/util/cpuinfo_util.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/streaming_handler.h"
#include "folly/concurrency/ConcurrentHashMap.h"
#include "folly/container/F14Set.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"

namespace ks {
namespace platform {

class CommonRecoLocalAsyncWaiter;
class CommonRecoLocalAsyncConcurrentWaiter;
class CommonRecoKessBatchConcurrentWaiter;
class CommonRecoBaseProcessor;
class StreamingHandler;

enum class ExecutionStatus : int {
  UNKNOWN = -1,
  SUCCESS = 0,
  FAILED = 1,
  TERMINATED = 2,
  CANCELLED,
  PERMISSION_DENIED
};

#define CHECK_INVALID_IO_BY_ATTR_NAME(ATTR_NAME, META_TYPE, RETURN_VALUE)                              \
  if (unlikely(AttrIOCheckUtil::IsEnabled())) {                                                        \
    auto meta_data = GetRunningProcessorMetaData();                                                    \
    if (meta_data) {                                                                                   \
      const auto &attr_set = meta_data->META_TYPE;                                                     \
      if (attr_set && attr_set->find(ATTR_NAME) == attr_set->end()) {                                  \
        auto it = invalid_io_counter_processor_pos_mapping_.find(meta_data->processor_type_name);      \
        if (it == invalid_io_counter_processor_pos_mapping_.end()) {                                   \
          invalid_io_counter_.emplace_back(                                                            \
              std::make_unique<std::pair<std::string, absl::flat_hash_map<std::string, int64>>>(       \
                  std::make_pair(meta_data->processor_type_name,                                       \
                                 absl::flat_hash_map<std::string, int64>())));                         \
          auto pr = invalid_io_counter_processor_pos_mapping_.emplace(meta_data->processor_type_name,  \
                                                                      invalid_io_counter_.size() - 1); \
          it = pr.first;                                                                               \
        }                                                                                              \
        auto &invalid_attr_set = invalid_io_counter_[it->second] -> second;                            \
        auto attr_it = invalid_attr_set.find(ATTR_NAME);                                               \
        if (attr_it != invalid_attr_set.end()) {                                                       \
          (attr_it->second)++;                                                                         \
        } else {                                                                                       \
          if (invalid_attr_set.size() < FLAGS_attr_io_check_report_max_count_per_processor) {          \
            invalid_attr_set.emplace(ATTR_NAME, 1);                                                    \
          }                                                                                            \
        }                                                                                              \
        if (FLAGS_attr_io_check_level > 1) {                                                           \
          return RETURN_VALUE;                                                                         \
        }                                                                                              \
      }                                                                                                \
    }                                                                                                  \
  }

#define CHECK_INVALID_IO_BY_ATTR_NAME_CONTINUE(ATTR_NAME, META_TYPE)                                   \
  if (AttrIOCheckUtil::IsEnabled()) {                                                                  \
    auto meta_data = GetRunningProcessorMetaData();                                                    \
    if (meta_data) {                                                                                   \
      const auto &attr_set = meta_data->META_TYPE;                                                     \
      if (attr_set && attr_set->find(ATTR_NAME) == attr_set->end()) {                                  \
        auto it = invalid_io_counter_processor_pos_mapping_.find(meta_data->processor_type_name);      \
        if (it == invalid_io_counter_processor_pos_mapping_.end()) {                                   \
          invalid_io_counter_.emplace_back(                                                            \
              std::make_unique<std::pair<std::string, absl::flat_hash_map<std::string, int64>>>(       \
                  std::make_pair(meta_data->processor_type_name,                                       \
                                 absl::flat_hash_map<std::string, int64>())));                         \
          auto pr = invalid_io_counter_processor_pos_mapping_.emplace(meta_data->processor_type_name,  \
                                                                      invalid_io_counter_.size() - 1); \
          it = pr.first;                                                                               \
        }                                                                                              \
        auto &invalid_attr_set = invalid_io_counter_[it->second] -> second;                            \
        auto attr_it = invalid_attr_set.find(ATTR_NAME);                                               \
        if (attr_it != invalid_attr_set.end()) {                                                       \
          (attr_it->second)++;                                                                         \
        } else {                                                                                       \
          if (invalid_attr_set.size() < FLAGS_attr_io_check_report_max_count_per_processor) {          \
            invalid_attr_set.emplace(ATTR_NAME, 1);                                                    \
          }                                                                                            \
        }                                                                                              \
        if (FLAGS_attr_io_check_level > 1) {                                                           \
          continue;                                                                                    \
        }                                                                                              \
      }                                                                                                \
    }                                                                                                  \
  }

#define CHECK_INVALID_IO_BY_ATTR(ATTR_PTR, META_TYPE, RETURN_VALUE)                                    \
  if (unlikely(!ATTR_PTR)) {                                                                           \
    return RETURN_VALUE;                                                                               \
  }                                                                                                    \
  if (unlikely(AttrIOCheckUtil::IsEnabled())) {                                                        \
    const auto &attr_name_str = ATTR_PTR->name();                                                      \
    auto meta_data = GetRunningProcessorMetaData();                                                    \
    if (meta_data) {                                                                                   \
      const auto &attr_set = meta_data->META_TYPE;                                                     \
      if (attr_set && attr_set->find(attr_name_str) == attr_set->end()) {                              \
        auto it = invalid_io_counter_processor_pos_mapping_.find(meta_data->processor_type_name);      \
        if (it == invalid_io_counter_processor_pos_mapping_.end()) {                                   \
          invalid_io_counter_.emplace_back(                                                            \
              std::make_unique<std::pair<std::string, absl::flat_hash_map<std::string, int64>>>(       \
                  std::make_pair(meta_data->processor_type_name,                                       \
                                 absl::flat_hash_map<std::string, int64>())));                         \
          auto pr = invalid_io_counter_processor_pos_mapping_.emplace(meta_data->processor_type_name,  \
                                                                      invalid_io_counter_.size() - 1); \
          it = pr.first;                                                                               \
        }                                                                                              \
        auto &invalid_attr_set = invalid_io_counter_[it->second] -> second;                            \
        auto attr_it = invalid_attr_set.find(attr_name_str);                                           \
        if (attr_it != invalid_attr_set.end()) {                                                       \
          (attr_it->second)++;                                                                         \
        } else {                                                                                       \
          if (invalid_attr_set.size() < FLAGS_attr_io_check_report_max_count_per_processor) {          \
            invalid_attr_set.emplace(attr_name_str, 1);                                                \
          }                                                                                            \
        }                                                                                              \
        if (FLAGS_attr_io_check_level > 1) {                                                           \
          return RETURN_VALUE;                                                                         \
        }                                                                                              \
      }                                                                                                \
    }                                                                                                  \
  }

#ifndef DISABLE_FLAT_INDEX
#define ITEM_ATTR_READ_BY_RESULT_AND_ATTR(DEFAULT_RETURN, FUNC)            \
  {                                                                        \
    if (!item_attr) return DEFAULT_RETURN;                                 \
    auto flat_index_addr = item_result.GetFlatIndexItemAddr();             \
    if (item_attr->is_from_flat_index && flat_index_addr) {                \
      return item_attr->FUNC(item_result.GetAttrIndex(), flat_index_addr); \
    } else {                                                               \
      return item_attr->FUNC(item_result.GetAttrIndex());                  \
    }                                                                      \
  }
#else
#define ITEM_ATTR_READ_BY_RESULT_AND_ATTR(DEFAULT_RETURN, FUNC) \
  {                                                             \
    if (!item_attr) return DEFAULT_RETURN;                      \
    return item_attr->FUNC(item_result.GetAttrIndex());         \
  }
#endif

#ifndef DISABLE_FLAT_INDEX
#define ITEM_ATTR_READ_BY_KEY(DEFAULT_RETURN, FUNC)                                    \
  {                                                                                    \
    AttrValue *attr = running_processor_table_->GetAttr(attr_name);                    \
    if (!attr) {                                                                       \
      return DEFAULT_RETURN;                                                           \
    }                                                                                  \
    if (!attr->is_from_flat_index) {                                                   \
      auto index = running_processor_table_->GetItemAttrIndex(item_key);               \
      if (index) {                                                                     \
        return attr->FUNC(index.value());                                              \
      } else {                                                                         \
        ++attr->access_counter;                                                        \
        return DEFAULT_RETURN;                                                         \
      }                                                                                \
    } else {                                                                           \
      auto flat_index_addr = running_processor_table_->GetItemFlatIndexAddr(item_key); \
      return attr->FUNC(0, flat_index_addr);                                           \
    }                                                                                  \
  }
#else
#define ITEM_ATTR_READ_BY_KEY(DEFAULT_RETURN, FUNC)                    \
  {                                                                    \
    AttrValue *attr = running_processor_table_->GetAttr(attr_name);    \
    if (!attr) {                                                       \
      return DEFAULT_RETURN;                                           \
    }                                                                  \
    auto index = running_processor_table_->GetItemAttrIndex(item_key); \
    if (index) {                                                       \
      return attr->FUNC(index.value());                                \
    } else {                                                           \
      ++attr->access_counter;                                          \
      return DEFAULT_RETURN;                                           \
    }                                                                  \
  }
#endif

#define ITEM_ATTR_WRITE_BY_KEY(FUNC, ...)                                      \
  {                                                                            \
    AttrValue *attr = running_processor_table_->GetOrInsertAttr(attr_name);    \
    auto index = running_processor_table_->GetOrInsertItemAttrIndex(item_key); \
    return attr->FUNC(index, ##__VA_ARGS__);                                   \
  }

#define ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(FUNC, ...) \
  { return attr->FUNC(item_result.GetAttrIndex(), ##__VA_ARGS__); }

/**
 * CommonRecoLeaf Immutable Context Interface
 */
class ReadableRecoContextInterface {
 public:
  virtual const std::string &GetRequestId() const = 0;
  virtual const std::string &GetRequestType() const = 0;
  virtual int64 GetRequestTime() const = 0;
  virtual int64 GetProcessCounter() const = 0;
  virtual int64 GetRequestNum() const = 0;
  virtual uint64 GetUserId() const = 0;
  virtual const std::string &GetDeviceId() const = 0;
  virtual const CommonRecoRequest *GetRequest() const = 0;
  virtual bool IsDebugRequest() const = 0;
  virtual bool NeedTraceback() const = 0;
  virtual ExecutionStatus GetExecutionStatus() const = 0;
  virtual const std::unordered_set<std::string> &GetCommonAttrsInRequest() const = 0;
  virtual const std::unordered_set<std::string> &GetItemAttrsInRequest() const = 0;
  virtual int64 GetElapsedTime() const = 0;
  virtual int64 RecordStageInfo(const std::string &stage_name) = 0;

  // CommonAttr Get APIs
  virtual CommonAttr *GetCommonAttr(absl::string_view attr_name) const = 0;
  virtual AttrType GetCommonAttrType(absl::string_view attr_name) const = 0;
  virtual bool HasCommonAttr(absl::string_view attr_name) const = 0;
  template <typename T>
  absl::optional<T> GetSingleCommonAttr(absl::string_view attr_name) const {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_common_attrs, absl::nullopt);
    auto *attr = GetCommonAttr(attr_name);
    return attr ? attr->GetSingularValue<T>() : absl::nullopt;
  }
  template <typename T>
  absl::optional<T> GetSingleCommonAttr(const CommonAttr *attr) const {
    CHECK_INVALID_IO_BY_ATTR(attr, input_common_attrs, absl::nullopt);
    return attr ? attr->GetSingularValue<T>() : absl::nullopt;
  }
  virtual absl::optional<int64> GetIntCommonAttr(absl::string_view attr_name) const = 0;
  virtual absl::optional<int64> GetIntCommonAttr(const CommonAttr *attr) const = 0;
  virtual absl::optional<double> GetDoubleCommonAttr(absl::string_view attr_name) const = 0;
  virtual absl::optional<double> GetDoubleCommonAttr(const CommonAttr *attr) const = 0;
  virtual absl::optional<absl::string_view> GetStringCommonAttr(absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::string_view> GetStringCommonAttr(const CommonAttr *attr) const = 0;
  template <typename T>
  absl::optional<absl::Span<const T>> GetListCommonAttr(absl::string_view attr_name) const {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_common_attrs, absl::nullopt);
    auto *attr = GetCommonAttr(attr_name);
    return attr ? attr->GetListValue<T>() : absl::nullopt;
  }
  template <typename T>
  absl::optional<absl::Span<const T>> GetListCommonAttr(const CommonAttr *attr) const {
    CHECK_INVALID_IO_BY_ATTR(attr, input_common_attrs, absl::nullopt);
    return attr ? attr->GetListValue<T>() : absl::nullopt;
  }
  virtual absl::optional<absl::Span<const int64>> GetIntListCommonAttr(absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::Span<const int64>> GetIntListCommonAttr(const CommonAttr *attr) const = 0;
  virtual absl::optional<absl::Span<const double>> GetDoubleListCommonAttr(
      absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::Span<const double>> GetDoubleListCommonAttr(const CommonAttr *attr) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetStringListCommonAttr(
      absl::string_view attr_name) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetStringListCommonAttr(
      const CommonAttr *attr) const = 0;

  // ItemAttr Get APIs
  // 获取当前目标 table 的 item_attr
  virtual ItemAttr *GetItemAttr(absl::string_view attr_name) const = 0;
  virtual AttrType GetItemAttrType(absl::string_view attr_name) const = 0;
  virtual AttrValue *GetItemAttrFromTable(absl::string_view attr_name, absl::string_view table_name) = 0;
  virtual AttrValue *GetOrInsertItemAttrFromTable(absl::string_view attr_name,
                                                  absl::string_view table_name) = 0;
  virtual bool HasItemAttr(absl::string_view attr_name) const = 0;
  virtual bool HasItemAttr(uint64 item_key, absl::string_view attr_name) const = 0;
  virtual bool HasItemAttr(const CommonRecoResult &item_result, const ItemAttr *item_attr) const = 0;
  template <typename T>
  __attribute__((noinline)) absl::optional<T> GetSingleItemAttr(uint64 item_key,
                                                                absl::string_view attr_name) const {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
    ITEM_ATTR_READ_BY_KEY(absl::nullopt, GetSingularValue<T>);
  }
  template <typename T>
  __attribute__((noinline)) absl::optional<T> GetSingleItemAttrWithoutCheck(
      uint64 item_key, absl::string_view attr_name) const {
    ITEM_ATTR_READ_BY_KEY(absl::nullopt, GetSingularValueWithoutCheck<T>);
  }
  template <typename T>
  __attribute__((noinline)) absl::optional<T> GetSingleItemAttr(const CommonRecoResult &item_result,
                                                                const ItemAttr *item_attr) const {
    CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
    ITEM_ATTR_READ_BY_RESULT_AND_ATTR(absl::nullopt, GetSingularValue<T>);
  }
  template <typename T>
  __attribute__((noinline)) absl::optional<T> GetSingleItemAttrWithoutCheck(
      const CommonRecoResult &item_result, const ItemAttr *item_attr) const {
    ITEM_ATTR_READ_BY_RESULT_AND_ATTR(absl::nullopt, GetSingularValueWithoutCheck<T>);
  }
  virtual absl::optional<int64> GetIntItemAttr(uint64 item_key, absl::string_view attr_name) const = 0;
  virtual absl::optional<int64> GetIntItemAttr(const CommonRecoResult &item_result,
                                               const ItemAttr *item_attr) const = 0;
  virtual absl::optional<int64> GetIntItemAttr(uint64 item_key, const ItemAttr *item_attr) const = 0;
  int64 GetIntItemAttr(uint64 item_key, absl::string_view attr_name, int64 default_value) const {
    return GetIntItemAttr(item_key, attr_name).value_or(default_value);
  }
  virtual absl::optional<double> GetDoubleItemAttr(uint64 item_key, absl::string_view attr_name) const = 0;
  virtual absl::optional<double> GetDoubleItemAttr(const CommonRecoResult &item_result,
                                                   const ItemAttr *item_attr) const = 0;
  virtual absl::optional<double> GetDoubleItemAttr(uint64 item_key, const ItemAttr *item_attr) const = 0;
  double GetDoubleItemAttr(uint64 item_key, absl::string_view attr_name, double default_value) const {
    return GetDoubleItemAttr(item_key, attr_name).value_or(default_value);
  }
  virtual absl::optional<absl::string_view> GetDefaultStringItemAttr(absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::string_view> GetDefaultStringItemAttr(const ItemAttr *item_attr) const = 0;
  virtual absl::optional<absl::string_view> GetStringItemAttr(uint64 item_key,
                                                              absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::string_view> GetStringItemAttr(const CommonRecoResult &item_result,
                                                              const ItemAttr *item_attr) const = 0;
  virtual absl::optional<absl::string_view> GetStringItemAttr(uint64 item_key,
                                                              const ItemAttr *item_attr) const = 0;
  template <typename T>
  absl::optional<absl::Span<const T>> GetListItemAttr(uint64 item_key, absl::string_view attr_name) const {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, input_item_attrs, absl::nullopt);
    ITEM_ATTR_READ_BY_KEY(absl::nullopt, GetListValue<T>);
  }
  template <typename T>
  absl::optional<absl::Span<const T>> GetListItemAttr(const CommonRecoResult &item_result,
                                                      const ItemAttr *item_attr) const {
    CHECK_INVALID_IO_BY_ATTR(item_attr, input_item_attrs, absl::nullopt);
    ITEM_ATTR_READ_BY_RESULT_AND_ATTR(absl::nullopt, GetListValue<T>);
  }
  virtual absl::optional<absl::Span<const int64>> GetIntListItemAttr(uint64 item_key,
                                                                     absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::Span<const int64>> GetIntListItemAttr(const CommonRecoResult &item_result,
                                                                     const ItemAttr *item_attr) const = 0;
  virtual absl::optional<absl::Span<const int64>> GetIntListItemAttr(uint64 item_key,
                                                                     const ItemAttr *item_attr) const = 0;
  virtual absl::optional<absl::Span<const double>> GetDoubleListItemAttr(
      uint64 item_key, absl::string_view attr_name) const = 0;
  virtual absl::optional<absl::Span<const double>> GetDoubleListItemAttr(const CommonRecoResult &item_result,
                                                                         const ItemAttr *item_attr) const = 0;
  virtual absl::optional<absl::Span<const double>> GetDoubleListItemAttr(uint64 item_key,
                                                                         const ItemAttr *item_attr) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetDefaultStringListItemAttr(
      absl::string_view attr_name) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetDefaultStringListItemAttr(
      const ItemAttr *item_attr) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      uint64 item_key, absl::string_view attr_name) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      const CommonRecoResult &item_result, const ItemAttr *item_attr) const = 0;
  virtual absl::optional<std::vector<absl::string_view>> GetStringListItemAttr(
      uint64 item_key, const ItemAttr *item_attr) const = 0;

  virtual bool InitItemAttr(absl::string_view attr_name, AttrType attr_type, size_t max_attr_index) = 0;
  virtual ItemAttr *GetItemAttrAccessor(absl::string_view attr_name) = 0;

  virtual CommonAttr *GetCommonAttrAccessor(absl::string_view attr_name) = 0;

  // 用户自定义变量读取接口
  virtual const boost::any *GetExtraCommonAttr(absl::string_view attr_name) const = 0;
  virtual const boost::any *GetExtraCommonAttr(const CommonAttr *attr) const = 0;
  virtual const boost::any *GetExtraItemAttr(uint64 item_key, absl::string_view attr_name) const = 0;
  virtual const boost::any *GetExtraItemAttr(const CommonRecoResult &item_result,
                                             const ItemAttr *item_attr) const = 0;
  virtual const boost::any *GetExtraItemAttr(uint64 item_key, const ItemAttr *item_attr) const = 0;

  // traceback 相关接口
  // XXX(qingjun): 未来 traceback 相关接口应删掉，不通过 context 传输
  virtual CommonRecoStepInfo *GetProcessorStepInfo(int index) = 0;
  virtual int GetSubflowStepInfosCount() = 0;
  virtual const std::vector<int> &GetTracebackSequence() const = 0;
  virtual bool GetNeedStepInfo() const = 0;
  virtual bool GetShouldCompressItem() = 0;
  virtual const std::string &GetTracebackDataVersion() const = 0;
  virtual bool GetShouldDropStudioField() = 0;
  virtual PackedItemAttrValue::CompressMode GetTracebackCompressMode() = 0;
  virtual const std::vector<std::string> *GetTracebackItemAttrs() const = 0;
  virtual const std::vector<std::string> *GetTracebackCommonAttrs() const = 0;
  virtual std::shared_ptr<folly::F14FastSet<std::string>> GetTracebackProcessors() const = 0;
  virtual std::shared_ptr<folly::F14FastSet<std::string>> GetTracebackProcessorsBlacklist() const = 0;
  virtual bool IsTracebackResultChanged() const = 0;
  virtual folly::F14FastSet<std::string> *GetAccumulatedTracebackItemAttrs() = 0;
  virtual CollectMode GetTracebackCollectMode() const = 0;
  virtual bool GetTracebackWhiteListUserReportAllProcessors() const = 0;
  virtual const std::vector<CommonRecoRetrieveResult> &GetTracebackFilterResult() const = 0;
  virtual const std::vector<int32> &GetTracebackFilterReasonOffset() const = 0;
  virtual const std::vector<std::string> &GetTracebackFilterReason() const = 0;

  virtual void AddPipelineCpuCost(const std::string &pipeline_name, int64 cpu_cost) = 0;
  virtual void AddTableCpuCost(const std::string &table_name, int64 cpu_cost) = 0;
  virtual CpuTimeContainer &GetPipelineCpuCost() = 0;
  virtual CpuTimeContainer &GetTableCpuCost() = 0;
  virtual bool GetNeedPipelineCpuCost() const = 0;
  virtual bool GetNeedTableCpuCost() const = 0;
  virtual IncrCpuTimer GetIncrCpuTimer() = 0;

  virtual bool EvalIntParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                       int64 *int_val) const = 0;
  virtual bool EvalDoubleParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                          double *double_val, bool try_int_attr = false) const = 0;
  virtual bool EvalStringParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                          std::string *string_val) const = 0;
  virtual bool EvalIntListParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                           std::vector<int64> *int_list_val) const = 0;
  virtual bool EvalDoubleListParamFromLuaExpr(std::vector<std::string> *common_attrs, absl::string_view expr,
                                              std::vector<double> *double_list_val) const = 0;

  // 统一的模板类封装
  template <typename T>
  const T *GetAnyCommonAttr(absl::string_view attr_name) const {
    try {
      return boost::any_cast<T>(GetExtraCommonAttr(attr_name));
    } catch (const boost::bad_any_cast &e) {
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();
      return nullptr;
    }
  }

  template <typename T>
  const T *GetAnyItemAttr(uint64 item_key, absl::string_view attr_name) const {
    try {
      return boost::any_cast<T>(GetExtraItemAttr(item_key, attr_name));
    } catch (const boost::bad_any_cast &e) {
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();
      return nullptr;
    }
  }

  template <typename T>
  const T *GetAnyItemAttr(const CommonRecoResult &result, const ItemAttr *item_attr) const {
    try {
      return boost::any_cast<T>(GetExtraItemAttr(result, item_attr));
    } catch (const boost::bad_any_cast &e) {
      CL_LOG_ERROR("extra_attr", "boost::bad_any_cast") << e.what();
      return nullptr;
    }
  }

  // 与 SetPtrCommonAttr 成对使用
  template <typename T>
  const T *GetPtrCommonAttr(absl::string_view attr_name) const {
    auto *ptr = GetAnyCommonAttr<std::shared_ptr<const T>>(attr_name);
    return ptr ? ptr->get() : nullptr;
  }

  // 与 SetPtrItemAttr 成对使用
  template <typename T>
  const T *GetPtrItemAttr(uint64 item_key, absl::string_view attr_name) const {
    auto *ptr = GetAnyItemAttr<std::shared_ptr<const T>>(item_key, attr_name);
    return ptr ? ptr->get() : nullptr;
  }

  // 与 SetPtrItemAttr 成对使用
  template <typename T>
  const T *GetPtrItemAttr(const CommonRecoResult &result, const ItemAttr *item_attr) const {
    auto *ptr = GetAnyItemAttr<std::shared_ptr<const T>>(result, item_attr);
    return ptr ? ptr->get() : nullptr;
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *>
  GetProtoMessagePtrCommonAttr(absl::string_view attr_name) const {
    auto *ptr = GetPtrCommonAttr<google::protobuf::Message>(attr_name);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrCommonAttr<T>(attr_name);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *>
  GetProtoMessagePtrItemAttr(uint64 item_key, absl::string_view attr_name) const {
    auto *ptr = GetPtrItemAttr<google::protobuf::Message>(item_key, attr_name);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrItemAttr<T>(item_key, attr_name);
    }
  }

  template <typename T>
  std::enable_if_t<std::is_base_of<google::protobuf::Message, T>::value, const T *>
  GetProtoMessagePtrItemAttr(const CommonRecoResult &result, const ItemAttr *item_attr) const {
    auto *ptr = GetPtrItemAttr<google::protobuf::Message>(result, item_attr);
    if (ptr) {
      return google::protobuf::down_cast<const T *>(ptr);
    } else {
      return GetPtrItemAttr<T>(result, item_attr);
    }
  }

  // browse set 查询接口
  virtual bool InBrowseSet(uint64 item_key) const = 0;
  virtual int64 GetBrowseSetSize() const = 0;

  // NOTE(fangjianbing): 查询 browse set 中最近浏览过的 count 个 item key,
  // count 为 0 时取全部, count 小于 0 时取最远浏览过的 abs(count) 个 item key
  virtual std::vector<uint64> GetLatestBrowsedItems(int count = 0) const = 0;

  // 获取 item 强插信息
  virtual const std::unordered_map<uint64, int> &GetForceInsertMap() const = 0;

  // 该请求是否已被取消
  virtual bool IsRequestCancelled() const = 0;

  // 该请求距离 deadline 剩余的毫秒数
  virtual int32 GetTimeLeft() const = 0;

  // RPC 服务类型
  virtual RpcType GetRpcType() const = 0;

  // 获取 BatchWaiter 接口
  virtual CommonRecoKessBatchWaiter *GetBatchWaiter(const std::string &trigger, bool create_if_missing) = 0;

  // 获取 LocalAsyncWaiter 接口
  virtual CommonRecoLocalAsyncWaiter *GetLocalAsyncWaiter(const std::string &trigger,
                                                          bool create_if_missing) = 0;

  // 获取 LocalAsyncWaiter 中严格超时的 Processor 最终等待 waiter
  virtual CommonRecoLocalAsyncConcurrentWaiter *GetLocalAsyncTimeoutWaiter() = 0;

  virtual CommonRecoKessBatchConcurrentWaiter *GetDetachedBatchWaiter() = 0;

  // 从 AsyncUpstreamProcessors 中获取 processor 等待的所有异步 processor 的 name
  virtual std::vector<std::string> *GetAsyncUpstreamProcessors(absl::string_view processor_name,
                                                               bool create_if_missing) = 0;
  // 等待并执行所有已注册的 callback
  virtual int WaitAllCallbacks() = 0;

  // 获取 common leaf 的主调服务名称
  virtual std::string GetCaller() const {
    return "";
  }

  // 获取 context 中保存的绑核信息
  virtual const CpuAffinityInfo *GetCpuAffinityInfo() const = 0;

  // 获取 context 中保持的 numa 绑定信息
  virtual int GetNumaId() const = 0;

  // 获取 streaming handler
  virtual StreamingHandler *GetStreamingHandler() const = 0;

  // 获取 streaming loop index
  virtual int GetStreamingLoopIndex() const = 0;

  // 获取 streaming 的状态信息
  virtual StreamingStatus GetStreamingStatus() const = 0;

  // 获取正在运行的 processor
  virtual const CommonRecoBaseProcessor *GetRunningProcessor() const = 0;

  // 获取正在运行的 processor 的 meta 信息
  virtual const RecoProcessorMetaData *GetRunningProcessorMetaData() const = 0;

  virtual ~ReadableRecoContextInterface() {}

  // 获取当前目标 table 的结果集
  virtual const std::vector<CommonRecoResult> &GetCommonRecoResults() = 0;
  // 获取指定 table 的结果集
  virtual const std::vector<CommonRecoResult> &GetCommonRecoResults(absl::string_view table_name) = 0;

  // 获取当前 common table 的所有列
  virtual const std::vector<AttrValue *> &GetAllCommonAttrs() const = 0;

  // 获取当前目标 table 的所有列
  virtual const std::vector<AttrValue *> &GetAllItemAttrs() const = 0;
  // 获取指定 table 的所有列
  virtual const std::vector<AttrValue *> &GetAllItemAttrsInTable(absl::string_view table_name) = 0;

  virtual const folly::F14FastMap<absl::string_view, AttrTable *, absl::Hash<absl::string_view>> &
  GetAllItemTable() const = 0;

  // 获取指定 table 的指针
  virtual const AttrTable *GetTable(absl::string_view table_name) const = 0;

 protected:
  AttrTable *running_processor_table_ = nullptr;

  // NOTE(caohongjin)
  // 属性依赖检测在上报阶段支持将数据进行均摊上报，为了避免上报丢失，保证数据的顺序性，这里采用 vector 而不是
  // hashmap 作为存储结构，数组中每一项含义: pair.first 表示 processor 名称；pair.second
  // 表示非法属性的统计信息，其中 key 为 attr 名称，value 为计数
  mutable std::vector<std::unique_ptr<std::pair<std::string, absl::flat_hash_map<std::string, int64>>>>
      invalid_io_counter_;
  // processor 名称到 invalid_io_counter 下标的映射
  mutable absl::flat_hash_map<std::string, int> invalid_io_counter_processor_pos_mapping_;
};

/**
 * CommonRecoLeaf Mutable Context Interface
 */
class MutableRecoContextInterface : public ReadableRecoContextInterface {
 public:
  virtual void SetExecutionStatus(ExecutionStatus status) = 0;
  virtual void SetStreamingStatus(StreamingStatus value) = 0;
  virtual CommonRecoResult NewCommonRecoResult(
      uint64 item_key, int reason = 0, double score = 0.0, int channel = 0,
      absl::optional<absl::string_view> table_name = absl::nullopt) = 0;

  // CommonAttr Set APIs
  virtual void CloneCommonAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                               bool shadow_copy, bool read_only = false) = 0;
  virtual void CloneItemAttr(ReadableRecoContextInterface *context, absl::string_view attr_name,
                             bool shadow_copy, bool read_only = false) = 0;
  virtual bool DeepCloneCommonAttr(const CommonAttr *attr_ptr) = 0;
  virtual bool DeepCloneItemAttr(const ItemAttr *attr_ptr) = 0;
  virtual bool ClearCommonAttr(absl::string_view attr_name, bool check_overwrite = true) = 0;
  virtual bool ClearCommonAttr(CommonAttr *attr, bool check_overwrite = true) = 0;
  template <typename T>
  bool SetSingleCommonAttr(absl::string_view attr_name, T value, bool if_not_exist = false,
                           bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
    auto *attr = GetCommonAttrAccessor(attr_name);
    return attr->SetSingularValue(0, value, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetSingleCommonAttr(CommonAttr *attr, T value, bool if_not_exist = false,
                           bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
    return attr ? attr->SetSingularValue(0, value, if_not_exist, check_overwrite) : false;
  }
  virtual bool SetIntCommonAttr(absl::string_view attr_name, int64 value, bool if_not_exist = false,
                                bool check_overwrite = true) = 0;
  virtual bool SetIntCommonAttr(CommonAttr *attr, int64 value, bool if_not_exist = false,
                                bool check_overwrite = true) = 0;
  virtual bool SetDoubleCommonAttr(absl::string_view attr_name, double value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetDoubleCommonAttr(CommonAttr *attr, double value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetStringCommonAttr(absl::string_view attr_name, std::string value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetStringCommonAttr(CommonAttr *attr, std::string value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetFloat32CommonAttr(absl::string_view attr_name, float value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetFloat32CommonAttr(CommonAttr *attr, float value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetFloat16CommonAttr(absl::string_view attr_name, float16_t value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  virtual bool SetFloat16CommonAttr(CommonAttr *attr, float16_t value, bool if_not_exist = false,
                                   bool check_overwrite = true) = 0;
  template <typename T>
  bool ResetListCommonAttr(absl::string_view attr_name, int capacity = 0, bool if_not_exist = false,
                           bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
    auto *attr = GetCommonAttrAccessor(attr_name);
    return attr->ResetListValue<T>(capacity, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool ResetListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                           bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
    return attr ? attr->ResetListValue<T>(capacity, if_not_exist, check_overwrite) : false;
  }
  virtual bool ResetIntListCommonAttr(absl::string_view attr_name, int capacity = 0,
                                      bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetIntListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                                      bool check_overwrite = true) = 0;
  virtual bool ResetDoubleListCommonAttr(absl::string_view attr_name, int capacity = 0,
                                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetDoubleListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                                         bool check_overwrite = true) = 0;
  virtual bool ResetStringListCommonAttr(absl::string_view attr_name, int capacity = 0,
                                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetStringListCommonAttr(CommonAttr *attr, int capacity = 0, bool if_not_exist = false,
                                         bool check_overwrite = true) = 0;
  template <typename T>
  bool AppendListCommonAttr(absl::string_view attr_name, T value) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
    auto *attr = GetCommonAttrAccessor(attr_name);
    return attr->AppendListValue<T>(value);
  }
  template <typename T>
  bool AppendListCommonAttr(CommonAttr *attr, T value) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
    return attr ? attr->AppendListValue<T>(value) : false;
  }
  virtual bool AppendIntListCommonAttr(absl::string_view attr_name, int64 value) = 0;
  virtual bool AppendIntListCommonAttr(CommonAttr *attr, int64 value) = 0;
  virtual bool AppendDoubleListCommonAttr(absl::string_view attr_name, double value) = 0;
  virtual bool AppendDoubleListCommonAttr(CommonAttr *attr, double value) = 0;
  virtual bool AppendStringListCommonAttr(absl::string_view attr_name, std::string value) = 0;
  virtual bool AppendStringListCommonAttr(CommonAttr *attr, std::string value) = 0;
  template <typename T>
  bool SetListCommonAttr(absl::string_view attr_name, std::vector<T> &&value, bool if_not_exist = false,
                         bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_common_attrs, false);
    auto *attr = GetCommonAttrAccessor(attr_name);
    return attr->SetListValue<T>(std::move(value), if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetListCommonAttr(CommonAttr *attr, std::vector<T> &&value, bool if_not_exist = false,
                         bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_common_attrs, false);
    return attr ? attr->SetListValue<T>(std::move(value), if_not_exist, check_overwrite) : false;
  }
  virtual bool SetIntListCommonAttr(absl::string_view attr_name, std::vector<int64> &&value,
                                    bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetIntListCommonAttr(CommonAttr *attr, std::vector<int64> &&value, bool if_not_exist = false,
                                    bool check_overwrite = true) = 0;
  virtual bool SetDoubleListCommonAttr(absl::string_view attr_name, std::vector<double> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetDoubleListCommonAttr(CommonAttr *attr, std::vector<double> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringListCommonAttr(absl::string_view attr_name, std::vector<std::string> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringListCommonAttr(CommonAttr *attr, std::vector<std::string> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ListCommonAttr(absl::string_view attr_name, std::vector<float> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ListCommonAttr(CommonAttr *attr, std::vector<float> &&value,
                                       bool if_not_exist = false,
                                       bool check_overwrite = true) = 0;
  virtual bool SetFloat16ListCommonAttr(absl::string_view attr_name, std::vector<float16_t> &&value,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ListCommonAttr(CommonAttr *attr, std::vector<float16_t> &&value,
                                       bool if_not_exist = false,
                                       bool check_overwrite = true) = 0;

  //  封装一组接口，方便在模版函数中使用
  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, bool> SetScalarCommonAttr(absl::string_view attr_name, T value,
                                                                         bool if_not_exist = false,
                                                                         bool check_overwrite = true) {
    return SetIntCommonAttr(attr_name, value, if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, bool> SetScalarCommonAttr(absl::string_view attr_name,
                                                                               T value,
                                                                               bool if_not_exist = false,
                                                                               bool check_overwrite = true) {
    return SetDoubleCommonAttr(attr_name, value, if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_integral<T>::value, bool> SetListCommonAttr(absl::string_view attr_name,
                                                                       const T *value, int size,
                                                                       bool if_not_exist = false,
                                                                       bool check_overwrite = true) {
    std::vector<int64> tmp_value(value, value + size);
    return SetIntListCommonAttr(attr_name, std::move(tmp_value), if_not_exist, check_overwrite);
  }

  template <typename T>
  std::enable_if_t<std::is_floating_point<T>::value, bool> SetListCommonAttr(absl::string_view attr_name,
                                                                             const T *value, int size,
                                                                             bool if_not_exist = false,
                                                                             bool check_overwrite = true) {
    std::vector<double> tmp_value(value, value + size);
    return SetDoubleListCommonAttr(attr_name, std::move(tmp_value), if_not_exist, check_overwrite);
  }

  // ItemAttr Set APIs
  virtual bool ClearItemAttrForAll(absl::string_view attr_name, bool check_overwrite = true) = 0;
  virtual bool ClearItemAttr(uint64 item_key, absl::string_view attr_name) = 0;
  virtual bool ClearItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                             bool check_overwrite = true) = 0;
  template <typename T>
  bool SetSingleItemAttr(uint64 item_key, absl::string_view attr_name, T value, bool if_not_exist = false,
                         bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_KEY(SetSingularValue<T>, value, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetSingleItemAttrWithoutCheck(uint64 item_key, absl::string_view attr_name, T value,
                                     bool if_not_exist = false, bool check_overwrite = true) {
    ITEM_ATTR_WRITE_BY_KEY(SetSingularValueWithoutCheck<T>, value, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetSingleItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, T value,
                         bool if_not_exist = false, bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetSingularValue<T>, value, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetSingleItemAttrWithoutCheck(const CommonRecoResult &item_result, ItemAttr *attr, T value,
                                     bool if_not_exist = false, bool check_overwrite = true) {
    if (unlikely(!attr)) {
      return false;
    }
    ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetSingularValueWithoutCheck<T>, value, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetDefaultSingleItemAttr(ItemAttr *attr, T value) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
    return attr ? attr->SetDefaultValue<T>(value) : false;
  }
  virtual bool SetIntItemAttr(uint64 item_key, absl::string_view attr_name, int64 value,
                              bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetIntItemAttr(uint64 item_key, ItemAttr *attr, int64 value, bool if_not_exist = false,
                              bool check_overwrite = true) = 0;
  virtual bool SetIntItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int64 value,
                              bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetDoubleItemAttr(uint64 item_key, absl::string_view attr_name, double value,
                                 bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetDoubleItemAttr(uint64 item_key, ItemAttr *attr, double value, bool if_not_exist = false,
                                 bool check_overwrite = true) = 0;
  virtual bool SetDoubleItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, double value,
                                 bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringItemAttr(uint64 item_key, absl::string_view attr_name, std::string value,
                                 bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringItemAttr(uint64 item_key, ItemAttr *attr, std::string value,
                                 bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::string value,
                                 bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ItemAttr(uint64 item_key, absl::string_view attr_name, float value,
                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ItemAttr(uint64 item_key, ItemAttr *attr, float value, bool if_not_exist = false,
                         bool check_overwrite = true) = 0;
  virtual bool SetFloat32ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, float value,
                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ItemAttr(uint64 item_key, absl::string_view attr_name, float16_t value,
                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ItemAttr(uint64 item_key, ItemAttr *attr, float16_t value, bool if_not_exist = false,
                         bool check_overwrite = true) = 0;
  virtual bool SetFloat16ItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, float16_t value,
                         bool if_not_exist = false, bool check_overwrite = true) = 0;
  template <typename T>
  bool SetListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<T> &&value,
                       bool if_not_exist = false, bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_KEY(SetListValue<T>, std::move(value), if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, std::vector<T> &&value,
                       bool if_not_exist = false, bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(SetListValue<T>, std::move(value), if_not_exist, check_overwrite);
  }
  template <typename T>
  bool SetDefaultListItemAttr(ItemAttr *attr, std::vector<T> &&value) {
    return attr ? attr->SetDefaultListValue<T>(std::move(value)) : false;
  }
  virtual bool SetIntListItemAttr(uint64 item_key, absl::string_view attr_name, std::vector<int64> &&value,
                                  bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetIntListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<int64> &&value,
                                  bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                  std::vector<int64> &&value, bool if_not_exist = false,
                                  bool check_overwrite = true) = 0;
  virtual bool SetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name,
                                     std::vector<double> &&value, bool if_not_exist = false,
                                     bool check_overwrite = true) = 0;
  virtual bool SetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<double> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                     std::vector<double> &&value, bool if_not_exist = false,
                                     bool check_overwrite = true) = 0;
  virtual bool SetStringListItemAttr(uint64 item_key, absl::string_view attr_name,
                                     std::vector<std::string> &&value, bool if_not_exist = false,
                                     bool check_overwrite = true) = 0;
  virtual bool SetStringListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<std::string> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                     std::vector<std::string> &&value, bool if_not_exist = false,
                                     bool check_overwrite = true) = 0;
  virtual bool SetFloat32ListItemAttr(uint64 item_key, absl::string_view attr_name,
                                     std::vector<float> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<float> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat32ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                     std::vector<float> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ListItemAttr(uint64 item_key, absl::string_view attr_name,
                                     std::vector<float16_t> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ListItemAttr(uint64 item_key, ItemAttr *attr, std::vector<float16_t> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetFloat16ListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                     std::vector<float16_t> &&value,
                                     bool if_not_exist = false, bool check_overwrite = true) = 0;
  template <typename T>
  bool ResetListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                         bool if_not_exist = false, bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_KEY(ResetListValue<T>, capacity, if_not_exist, check_overwrite);
  }
  template <typename T>
  bool ResetListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                         bool if_not_exist = false, bool check_overwrite = true) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(ResetListValue<T>, capacity, if_not_exist, check_overwrite);
  }
  virtual bool ResetIntListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                                    bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetIntListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0,
                                    bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                                    bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetDoubleListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetStringListItemAttr(uint64 item_key, absl::string_view attr_name, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetStringListItemAttr(uint64 item_key, ItemAttr *attr, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool ResetStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int capacity = 0,
                                       bool if_not_exist = false, bool check_overwrite = true) = 0;
  template <typename T>
  bool AppendListItemAttr(uint64 item_key, absl::string_view attr_name, T value) {
    CHECK_INVALID_IO_BY_ATTR_NAME(attr_name, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_KEY(AppendListValue<T>, value);
  }
  template <typename T>
  bool AppendListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, T value) {
    CHECK_INVALID_IO_BY_ATTR(attr, output_item_attrs, false);
    ITEM_ATTR_WRITE_BY_RESULT_AND_ATTR(AppendListValue<T>, value);
  }
  virtual bool AppendIntListItemAttr(uint64 item_key, absl::string_view attr_name, int64 value) = 0;
  virtual bool AppendIntListItemAttr(uint64 item_key, ItemAttr *attr, int64 value) = 0;
  virtual bool AppendIntListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, int64 value) = 0;
  virtual bool AppendDoubleListItemAttr(uint64 item_key, absl::string_view attr_name, double value) = 0;
  virtual bool AppendDoubleListItemAttr(uint64 item_key, ItemAttr *attr, double value) = 0;
  virtual bool AppendDoubleListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                        double value) = 0;
  virtual bool AppendStringListItemAttr(uint64 item_key, absl::string_view attr_name, std::string value) = 0;
  virtual bool AppendStringListItemAttr(uint64 item_key, ItemAttr *attr, std::string value) = 0;
  virtual bool AppendStringListItemAttr(const CommonRecoResult &item_result, ItemAttr *attr,
                                        std::string value) = 0;

  // 用户自定义变量写入接口
  virtual bool SetExtraCommonAttr(absl::string_view attr_name, boost::any &&value, bool if_not_exist = false,
                                  bool check_overwrite = true) = 0;
  virtual bool SetExtraCommonAttr(CommonAttr *attr, boost::any &&value, bool if_not_exist = false,
                                  bool check_overwrite = true) = 0;
  virtual bool SetExtraItemAttr(uint64 item_key, absl::string_view attr_name, boost::any &&value,
                                bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetExtraItemAttr(const CommonRecoResult &item_result, ItemAttr *attr, boost::any &&value,
                                bool if_not_exist = false, bool check_overwrite = true) = 0;
  virtual bool SetExtraItemAttr(uint64 item_key, ItemAttr *item_attr, boost::any &&value,
                                bool if_not_exist = false, bool check_overwrite = true) = 0;

  // 针对指针类型提供统一的插入接口
  template <typename Ptr>
  void SetPtrCommonAttr(absl::string_view attr_name, Ptr &&ptr) {
    using T = typename std::pointer_traits<std::remove_reference_t<Ptr>>::element_type;
    if (std::is_same<T *, std::remove_reference_t<Ptr>>::value) {
      // 裸指针转为 shared_ptr 存储，但是 context 不负责释放，调用方需要自己保证这个指针在 context
      // 的生命周期内可访问。
      // FIXME(huiyiqun): 这个调用方式相对比较危险，最好可以避免这种 use case
      SetExtraCommonAttr(attr_name, std::shared_ptr<const T>(&*ptr, [](auto) {}));
    } else {
      // 其他指针类型(包括 unique_ptr) 转为 shared_ptr 存储
      SetExtraCommonAttr(attr_name, std::shared_ptr<const T>(std::forward<Ptr>(ptr)));
    }
  }

  template <typename Ptr>
  void SetPtrItemAttr(uint64 item_key, absl::string_view attr_name, Ptr &&ptr) {
    using T = typename std::pointer_traits<std::remove_reference_t<Ptr>>::element_type;
    if (std::is_same<T *, std::remove_reference_t<Ptr>>::value) {
      // 裸指针转为 shared_ptr 存储，但是 context 不负责释放，调用方需要自己保证这个指针在 context
      // 的生命周期内可访问。
      // FIXME(huiyiqun): 这个调用方式相对比较危险，最好可以避免这种 use case
      SetExtraItemAttr(item_key, attr_name, std::shared_ptr<const T>(&*ptr, [](auto) {}));
    } else {
      // 其他指针类型(包括 unique_ptr) 转为 shared_ptr 存储
      SetExtraItemAttr(item_key, attr_name, std::shared_ptr<const T>(std::forward<Ptr>(ptr)));
    }
  }

  template <typename Ptr>
  void SetPtrItemAttr(const CommonRecoResult &result, ItemAttr *item_attr, Ptr &&ptr) {
    using T = typename std::pointer_traits<std::remove_reference_t<Ptr>>::element_type;
    if (std::is_same<T *, std::remove_reference_t<Ptr>>::value) {
      // 裸指针转为 shared_ptr 存储，但是 context 不负责释放，调用方需要自己保证这个指针在 context
      // 的生命周期内可访问。
      // FIXME(huiyiqun): 这个调用方式相对比较危险，最好可以避免这种 use case
      SetExtraItemAttr(result, item_attr, std::shared_ptr<const T>(&*ptr, [](auto) {}));
    } else {
      // 其他指针类型(包括 unique_ptr) 转为 shared_ptr 存储
      SetExtraItemAttr(result, item_attr, std::shared_ptr<const T>(std::forward<Ptr>(ptr)));
    }
  }

  // 与 SetPtrCommonAttr 成对使用
  template <typename T>
  T *GetMutablePtrCommonAttr(absl::string_view attr_name) {
    auto *ptr = GetAnyCommonAttr<std::shared_ptr<const T>>(attr_name);
    return ptr ? const_cast<T *>(ptr->get()) : nullptr;
  }

  // 与 SetPtrItemAttr 成对使用
  template <typename T>
  T *GetMutablePtrItemAttr(uint64 item_key, absl::string_view attr_name) {
    auto *ptr = GetAnyItemAttr<std::shared_ptr<const T>>(item_key, attr_name);
    return ptr ? const_cast<T *>(ptr->get()) : nullptr;
  }

  // 与 SetPtrItemAttr 成对使用
  template <typename T>
  T *GetMutablePtrItemAttr(const CommonRecoResult &result, const ItemAttr *item_attr) {
    auto *ptr = GetAnyItemAttr<std::shared_ptr<const T>>(result, item_attr);
    return ptr ? const_cast<T *>(ptr->get()) : nullptr;
  }

  virtual void SetRunningProcessor(const void *processor) = 0;

  virtual AttrTable *GetOrInsertDataTable(absl::string_view table_name) = 0;

  virtual bool CreateLogicalTable(absl::string_view table_name, AttrTable *from_table) = 0;

  // 获取指定 table 的指针
  virtual AttrTable *GetMutableTable(absl::string_view table_name) const = 0;

  virtual bool InsertExternalTable(AttrTable *table) = 0;

  virtual bool SetFilterReason(const CommonRecoResult &result, const std::string &filter_reason) = 0;

  virtual CommonRecoStepInfo *GetNewStepInfo() = 0;
  virtual void ClearSubflowStepInfo() = 0;
  virtual void IncrTracebackSequence() = 0;
  virtual void SetTracebackSequence(std::vector<int> &&sequence) = 0;
  virtual void SetNeedStepInfo(bool value) = 0;
  virtual void SetShouldCompressItem(bool value) = 0;
  virtual void SetTracebackDataVersion(const std::string &data_version) = 0;
  virtual void SetShouldDropStudioField(bool value) = 0;
  virtual void SetTracebackCompressMode(PackedItemAttrValue::CompressMode compress_mode) = 0;
  virtual void SetTracebackItemAttrs(absl::Span<const std::string> return_item_attrs) = 0;
  virtual void SetTracebackCommonAttrs(absl::Span<const std::string> return_common_attrs) = 0;
  virtual void SetTracebackProcessors(
      std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors) = 0;
  virtual void SetTracebackProcessorsBlacklist(
      std::shared_ptr<folly::F14FastSet<std::string>> traceback_processors_blacklist) = 0;
  virtual void SetTracebackResultChanged(bool value) = 0;
  virtual void AddTracebackResultChanged(const std::string &table_name) = 0;
  virtual folly::F14FastSet<std::string> *GetAccumulatedTracebackTables() = 0;
  virtual void AddAccumulatedTracebackItemAttrs(const std::string &attr_name) = 0;
  virtual void SetTracebackCollectMode(CollectMode collect_mode) = 0;
  virtual void SetTracebackWhiteListUserReportAllProcessors(bool report_all_processors) = 0;
  virtual void SetTracebackFilterReason(const CommonRecoResult &result, const std::string &filter_reason) = 0;
  virtual void ClearTracebackFilterResult() = 0;
  virtual void ClearTracebackFilterResultOffset() = 0;
  virtual void ClearTracebackFilterReason() = 0;

  // ONLY FOR DEBUG USE!!!
  virtual void PurposelyResetUserId(uint64 uid) = 0;
  // ONLY FOR DEBUG USE!!!
  virtual void PurposelyResetDeviceId(const std::string &did) = 0;
  // ONLY FOR DEBUG USE!!!
  virtual void PurposelyResetRequestTime(int64 time_ms) = 0;

  virtual ~MutableRecoContextInterface() {}
};

/**
 * CommonRecoLeaf Addible Context Interface
 */
class AddibleRecoContextInterface : public MutableRecoContextInterface {
 public:
  // 用于添加召回结果进入候选结果集
  virtual CommonRecoResult &AddCommonRecoResultToTable(AttrTable *table, uint64 item_key, int reason,
                                                       double score = 0.0, int channel = 0,
                                                       bool override_reason_attr = false) = 0;
  // AddCommonRecoResultToTable 低性能版本。
  virtual CommonRecoResult &AddCommonRecoResultToTable(absl::string_view table_name, uint64 item_key,
                                                       int reason, double score = 0.0, int channel = 0,
                                                       bool override_reason_attr = false) = 0;
  virtual CommonRecoResult &AddCommonRecoResult(uint64 item_key, int reason, double score = 0.0,
                                                int channel = 0, bool override_reason_attr = false) = 0;
  virtual CommonRecoResult &AddCommonRecoResult(const CommonRecoResult &result,
                                                bool override_reason_attr = false) = 0;

  virtual ~AddibleRecoContextInterface() {}
};

}  // namespace platform
}  // namespace ks
