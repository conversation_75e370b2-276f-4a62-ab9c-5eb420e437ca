#include "dragon/src/core/traceback_manager.h"

DEFINE_int32(traceback_async_queue_max_size, 100, "max size of traceback queue");
DEFINE_int32(traceback_send_thread_num, 1, "size of traceback thread pool");
DEFINE_string(traceback_config_kconf_key, "", "kconf key for traceback config");
DEFINE_bool(enable_async_traceback, true, "enable async send traceback message");

namespace ks {
namespace platform {}  // namespace platform
}  // namespace ks
