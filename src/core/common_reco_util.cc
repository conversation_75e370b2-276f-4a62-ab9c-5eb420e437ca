#include "dragon/src/core/common_reco_util.h"

namespace ks {
namespace platform {
DEFINE_bool(zero_copy_request_data, false, "fixed value zero copy in request ");
// NOTE(zhaoyang09): 危险！该优化可行性待验证中，严禁开启 zero_copy_response_data 开关。
DEFINE_bool(zero_copy_response_data, false, "fixed value zero copy in response");

std::vector<std::string> RecoUtil::attr_type_name_vec_ = [] {
  std::vector<std::string> vec{"int",         "double",      "string",    "int_list",   "double_list",
                               "string_list", "extra",       "int32",     "int32_list", "int16",
                               "int16_list",  "int8",        "int8_list", "float32",    "float32_list",
                               "float16",     "float16_list"};
  return vec;
}();

RecoUtil::AttrTypeMap RecoUtil::attr_type_map_ = [] {
  RecoUtil::AttrTypeMap map;
  map.insert(std::make_pair("int", AttrType::INT));
  map.insert(std::make_pair("integer", AttrType::INT));
  map.insert(std::make_pair("float", AttrType::FLOAT));
  map.insert(std::make_pair("double", AttrType::FLOAT));
  map.insert(std::make_pair("str", AttrType::STRING));
  map.insert(std::make_pair("string", AttrType::STRING));
  map.insert(std::make_pair("int_list", AttrType::INT_LIST));
  map.insert(std::make_pair("integer_list", AttrType::INT_LIST));
  map.insert(std::make_pair("float_list", AttrType::FLOAT_LIST));
  map.insert(std::make_pair("double_list", AttrType::FLOAT_LIST));
  map.insert(std::make_pair("str_list", AttrType::STRING_LIST));
  map.insert(std::make_pair("string_list", AttrType::STRING_LIST));
  map.insert(std::make_pair("extra", AttrType::EXTRA));
  map.insert(std::make_pair("float16", AttrType::FLOAT16));
  map.insert(std::make_pair("float16_list", AttrType::FLOAT16_LIST));
  map.insert(std::make_pair("float32", AttrType::FLOAT32));
  map.insert(std::make_pair("float32_list", AttrType::FLOAT32_LIST));
  return map;
}();

RecoUtil::AbtestParamTypeMap RecoUtil::ab_param_type_map_ = [] {
  RecoUtil::AbtestParamTypeMap map;
  map.insert(std::make_pair("int", AbtestParamType::INT));
  map.insert(std::make_pair("integer", AbtestParamType::INT));
  map.insert(std::make_pair("float", AbtestParamType::DOUBLE));
  map.insert(std::make_pair("double", AbtestParamType::DOUBLE));
  map.insert(std::make_pair("str", AbtestParamType::STRING));
  map.insert(std::make_pair("string", AbtestParamType::STRING));
  map.insert(std::make_pair("bool", AbtestParamType::BOOLEAN));
  map.insert(std::make_pair("boolean", AbtestParamType::BOOLEAN));
  return map;
}();

RecoUtil::KconfParamTypeMap RecoUtil::kconf_param_type_map_ = [] {
  RecoUtil::KconfParamTypeMap map;
  map.insert(std::make_pair("int64", KconfParamType::INT64));
  map.insert(std::make_pair("int", KconfParamType::INT64));
  map.insert(std::make_pair("double", KconfParamType::DOUBLE));
  map.insert(std::make_pair("string", KconfParamType::STRING));
  map.insert(std::make_pair("json", KconfParamType::JSON));
  map.insert(std::make_pair("bool", KconfParamType::BOOLEAN));
  map.insert(std::make_pair("list_int64", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("set_int64", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("int64_list", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("int64_set", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("int_list", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("int_set", KconfParamType::INT64_LIST));
  map.insert(std::make_pair("list_double", KconfParamType::DOUBLE_LIST));
  map.insert(std::make_pair("set_double", KconfParamType::DOUBLE_LIST));
  map.insert(std::make_pair("double_list", KconfParamType::DOUBLE_LIST));
  map.insert(std::make_pair("double_set", KconfParamType::DOUBLE_LIST));
  map.insert(std::make_pair("list_string", KconfParamType::STRING_LIST));
  map.insert(std::make_pair("set_string", KconfParamType::STRING_LIST));
  map.insert(std::make_pair("string_list", KconfParamType::STRING_LIST));
  map.insert(std::make_pair("string_set", KconfParamType::STRING_LIST));
  map.insert(std::make_pair("list_bool", KconfParamType::BOOLEAN_LIST));
  map.insert(std::make_pair("set_bool", KconfParamType::BOOLEAN_LIST));
  map.insert(std::make_pair("bool_list", KconfParamType::BOOLEAN_LIST));
  map.insert(std::make_pair("bool_set", KconfParamType::BOOLEAN_LIST));
  return map;
}();

RecoUtil::DegraderTypeMap RecoUtil::degrader_type_map_ = [] {
  RecoUtil::DegraderTypeMap map;
  map.insert(std::make_pair("CIRCUIT_BREAKER", DegraderType::CIRCUIT_BREAKER));
  map.insert(std::make_pair("CircuitBreaker", DegraderType::CIRCUIT_BREAKER));
  map.insert(std::make_pair("circuit_breaker", DegraderType::CIRCUIT_BREAKER));
  map.insert(std::make_pair("BULK_HEAD", DegraderType::BULK_HEAD));
  map.insert(std::make_pair("BulkHead", DegraderType::BULK_HEAD));
  map.insert(std::make_pair("bulk_head", DegraderType::BULK_HEAD));
  map.insert(std::make_pair("ADAPTIVE_LIMITER", DegraderType::ADAPTIVE_LIMITER));
  map.insert(std::make_pair("AdaptiveLimiter", DegraderType::ADAPTIVE_LIMITER));
  map.insert(std::make_pair("adaptive_limiter", DegraderType::ADAPTIVE_LIMITER));
  map.insert(std::make_pair("RANDOM_DROP", DegraderType::RANDOM_DROP));
  map.insert(std::make_pair("RandomDrop", DegraderType::RANDOM_DROP));
  map.insert(std::make_pair("random_drop", DegraderType::RANDOM_DROP));
  return map;
}();

RecoUtil::AnnUserKeyModeMap RecoUtil::ann_user_key_mode_map_ = [] {
  RecoUtil::AnnUserKeyModeMap map;
  map.insert(std::make_pair("did_only", AnnUserKeyMode::DID_ONLY));
  map.insert(std::make_pair("uid_only", AnnUserKeyMode::UID_ONLY));
  map.insert(std::make_pair("uid_or_did", AnnUserKeyMode::UID_OR_DID));
  return map;
}();

}  // namespace platform
}  // namespace ks
