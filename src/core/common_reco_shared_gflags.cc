#include "dragon/src/core/common_reco_shared_gflags.h"

// 业务方自己的 abtestbiz_seq_num 请在 ks/base/abtest/abtest_globals.h 中查找
DEFINE_int64(abtestbiz_seq_num, -1, "default abtest biz name enum int value");
DEFINE_string(abtest_biz_name, "", "default abtest biz name");
// 本地索引 flag
DEFINE_string(index_dir, "../reco_index", "reco index dir");
DEFINE_string(index_queue, "", "reco index queue");

// 查询索引的线程数
DEFINE_int32(index_query_thread_num, 0, "index query thread num");

// GRPC 多 eventloop
DEFINE_bool(dragon_grpc_use_multi_eventloop, false, "grpc client use multiple eventloops");
