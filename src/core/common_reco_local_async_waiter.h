//
// Created by yangtao03 on 2020-08-12.
//

#pragma once

#include <algorithm>
#include <chrono>
#include <functional>
#include <future>
#include <memory>
#include <queue>
#include <string>
#include <utility>

#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/core/common_reco_local_async_concurrent_waiter.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/logging_util.h"

namespace ks {
namespace platform {

class CommonRecoLocalAsyncWaiter {
 public:
  explicit CommonRecoLocalAsyncWaiter(const std::string &name) : name_(name) {}
  CommonRecoLocalAsyncWaiter(const CommonRecoLocalAsyncWaiter &) = delete;
  CommonRecoLocalAsyncWaiter &operator=(const CommonRecoLocalAsyncWaiter &) = delete;

  template <typename T, typename Callback>
  void Add(ReadableRecoContextInterface *context, std::future<T> &&future, Callback &&callback,
           const std::string &processor_name, int priority, int64 timeout_ms = 0,
           std::function<void()> timeout_cb = nullptr) {
    std::function<void(T)> cb = std::forward<Callback>(callback);
    // 闭包绑定，背景 : future 不能拷贝 + c++11 闭包不支持 std::move
    auto future_ptr = std::make_shared<std::future<T>>(std::move(future));
    auto start_ts = std::chrono::system_clock::now();
    auto action = [context, future_ptr = std::move(future_ptr), cb = std::move(cb), processor_name,
                   waiter_name = Name(), start_ts = std::move(start_ts), timeout_ms,
                   timeout_cb = std::move(timeout_cb)]() {
      if (!future_ptr->valid()) {
        CL_LOG_ERROR("local_async", "invalid_future: " + processor_name)
            << "add invalid future! processor: " << processor_name;
        return;
      }

      if (timeout_ms <= 0 || context->GetLocalAsyncTimeoutWaiter() == nullptr ||
          future_ptr->wait_until(start_ts + std::chrono::milliseconds(timeout_ms)) !=
              std::future_status::timeout) {
        cb(std::move(future_ptr->get()));
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "processor_async_success_count", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), processor_name, waiter_name);
      } else {
        if (timeout_cb) {
          timeout_cb();
        }
        cb(nullptr);
        int64 timeout_st = base::GetTimestamp();
        context->GetLocalAsyncTimeoutWaiter()->Add<T, std::function<void(T)>>(
            context, std::move(*future_ptr),
            [timeout_st, processor_name](T) {
              CL_LOG_EVERY_N(INFO, 100)
                  << "local async strict timeout processor wait cost: "
                  << (base::GetTimestamp() - timeout_st) / 1000.0 << "ms, processor: " << processor_name;
            },
            processor_name, 0);
        CL_LOG_WARNING_EVERY("local_async", "timeout: " + processor_name, 100)
            << "local async wait timeout: " << timeout_ms << "ms, processor: " << processor_name;
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "processor_async_timeout_count", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), processor_name, waiter_name);
      }

      auto async_time =
          std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now() - start_ts);
      CL_PERF_INTERVAL(
          async_time.count(), kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(),
          context->GetRequestType(), processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
    };
    queue_.emplace(priority, queue_.size(), std::move(action));
  }

  void Wait() {
    while (!queue_.empty()) {
      queue_.top()();
      queue_.pop();
    }
  }

  void Clear() {
    if (queue_.empty()) return;
    std::priority_queue<WaitTask> empty;
    std::swap(queue_, empty);
  }

  const std::string &Name() const {
    return name_;
  }

  size_t Size() const {
    return queue_.size();
  }

 private:
  class WaitTask {
   public:
    WaitTask(int priority, int sequence, std::function<void()> &&task)
        : priority_(priority), sequence_(sequence), task_(task) {}

    bool operator<(const WaitTask &t) const {
      if (priority_ == t.priority_) return sequence_ > t.sequence_;
      return priority_ < t.priority_;
    }

    void operator()() const {
      task_();
    }

   private:
    int priority_;
    int sequence_;
    std::function<void()> task_;
  };

 private:
  std::string name_;
  std::priority_queue<WaitTask> queue_;
};
}  // namespace platform
}  // namespace ks
