#include "dragon/src/core/sub_flow_thread_pool_manager.h"

#include <atomic>
#include <memory>
#include <vector>

#include "gtest/gtest.h"

static const int kMaxNumList = 500;
static const int ThreadNum = 5;
static const int NumaCount = 5;

class SubFlowThreadPoolManagerTest : public ::testing::Test {
 protected:
  void SetUp() override {}
};

// TODO(zhaoyang09): 暂时只测试厨除绑核逻辑外的逻辑，绑核逻辑需要修改 1. cpu_info 增加支持单测的自定义接口 2.
// 需要研究下 gflag 跟 gtest 的联动。
// 为了不影响代码设计，暂时关闭单测。

/*
TEST_F(SubFlowThreadPoolManagerTest, GetThreadPoolWithNuma) {
  std::atomic_int count_num{0};
  std::vector<ks::platform::SubFlowThreadPoolManager::ThreadPoolParam> params;
  params.reserve(NumaCount);
  for (int i = 0; i < NumaCount; i++) {
    params.emplace_back(i, ThreadNum, [&count_num](int index) { count_num++; });
  }
  ks::platform::SubFlowThreadPoolManager *m = ks::platform::SubFlowThreadPoolManager::GetInstance();
  m->SetupThreadPools(std::move(params));
  EXPECT_EQ(count_num, ThreadNum * NumaCount);
  for (int i = 0; i < NumaCount; i++) {
    auto *ptr = m->GetThreadPool(i);
    EXPECT_NE(nullptr, ptr);
  }
  auto *ptr = m->GetThreadPool(NumaCount);
  EXPECT_EQ(nullptr, ptr);
}

TEST_F(SubFlowThreadPoolManagerTest, GetThreadPool) {
  std::vector<ks::platform::SubFlowThreadPoolManager::ThreadPoolParam> params;
  params.reserve(1);
  params.emplace_back(0, ThreadNum, nullptr);
  ks::platform::SubFlowThreadPoolManager *m = ks::platform::SubFlowThreadPoolManager::GetInstance();
  m->SetupThreadPools(std::move(params));
  auto *ptr = m->GetThreadPool(NumaCount);
  EXPECT_EQ(nullptr, ptr);
  ptr = m->GetThreadPool(-1);
  EXPECT_NE(nullptr, ptr);
}
*/
