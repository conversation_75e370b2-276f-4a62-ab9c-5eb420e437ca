//
// Created by wa<PERSON><PERSON><PERSON> on 2019/4/2.
// Copied from <kess/rpc/batch_waiter.h> by fan<PERSON><PERSON><PERSON><PERSON> on 2021/11/2
//

#pragma once

#include <kenv/context.h>
#include <kess/common/blocking_queue.h>
#include <kess/rpc/future.h>

#include <atomic>
#include <functional>
#include <memory>
#include <string>
#include <type_traits>
#include <utility>
#include <vector>

#include "base/time/timestamp.h"
#include "dragon/src/module/future_action_wrapper.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/perf_report_util.h"
#include "third_party/tbb/include/tbb/concurrent_queue.h"

namespace ks {
namespace platform {

// Note: CommonRecoKessBatchConcurrentWaiter is thread safe.
class CommonRecoKessBatchConcurrentWaiter {
 public:
  explicit CommonRecoKessBatchConcurrentWaiter(const std::string &name) : name_(name) {}

  CommonRecoKessBatchConcurrentWaiter(const CommonRecoKessBatchConcurrentWaiter &) = delete;
  CommonRecoKessBatchConcurrentWaiter &operator=(const CommonRecoKessBatchConcurrentWaiter &) = delete;

  template <
      typename T, typename Action,
      typename std::enable_if<ks::kess::rpc::internal::is_invocable<Action, const T &>::value, int>::type = 0>
  void Add(ks::kess::rpc::Future<T> future, Action &&action, const std::string &processor_name) {
    int64 start_ts = base::GetTimestamp();
    // Convert lambda to std::function to be fixed size, this may fix struct check error
    std::function<void(const T &)> callback = std::forward<Action>(action);
    std::string request_type = GlobalHolder::GetCurrentRequestType();
    future.Submit([this, start_ts, processor_name, request_type = std::move(request_type),
                   waiter_name = Name(), callback = std::move(callback)](const T &result) mutable {
      int64 async_ready = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(async_ready, kPerfNs, "processor_async_ready", GlobalHolder::GetServiceIdentifier(),
                       request_type, processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
      queue_.emplace([callback = std::move(callback), start_ts, request_type = std::move(request_type),
                      processor_name = std::move(processor_name), waiter_name = std::move(waiter_name),
                      result, context = ks::infra::kenv::Context::Current()->Copy()]() mutable {
        context->Run([callback = std::move(callback), result]() { callback(result); });
        int64 async_time = base::GetTimestamp() - start_ts;
        CL_PERF_INTERVAL(
            async_time, kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(), request_type,
            processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
      });
    });
    future_closures_.emplace([future = std::move(future)]() {});
    queue_size_++;
  }

  template <typename T, typename Action,
            typename std::enable_if<!ks::kess::rpc::internal::is_invocable<Action, const T &>::value,
                                    int>::type = 0>
  void Add(ks::kess::rpc::Future<T> future, Action &&action, const std::string &processor_name) {
    Add(std::move(future),
        ks::kess::rpc::internal::TupleAction<typename std::remove_reference<Action>::type>{
            std::forward<Action>(action)},
        processor_name);
  }

  template <typename T, typename Action>
  void Add(CommonRecoFutureActionWrapper<T> future, Action &&action, const std::string &processor_name) {
    int64 start_ts = base::GetTimestamp();
    std::function<void(const T &)> callback = std::forward<Action>(action);
    std::string request_type = GlobalHolder::GetCurrentRequestType();
    queue_.emplace(0, [future = std::move(future), callback = std::move(callback), start_ts,
                       request_type = std::move(request_type), processor_name = std::move(processor_name),
                       waiter_name = Name()]() mutable {
      T result = future.Get();
      int64 async_ready = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(async_ready, kPerfNs, "processor_async_ready", GlobalHolder::GetServiceIdentifier(),
                       request_type, processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
      callback(result);
      int64 async_time = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(
          async_time, kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(), request_type,
          processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
    });
    queue_size_++;
  }

  /**
   * Wait all finish
   */
  void Wait() {
    std::function<void()> task;
    while (queue_.try_pop(task)) {
      if (task != nullptr) {
        task();
        queue_size_--;
      }
    }
  }

  void Clear() {
    queue_.clear();
    future_closures_.clear();
    queue_size_ = 0;
  }

  const std::string &Name() const {
    return name_;
  }

  size_t Size() const {
    return queue_size_;
  }

 private:
  std::string name_;
  std::atomic<int64> queue_size_{0};  // tbb::concurrent_queue 无线程安全 size() 函数，只有 unsafe_size();
  tbb::concurrent_queue<std::function<void()>> queue_;
  tbb::concurrent_queue<std::function<void()>> future_closures_;
};

}  // namespace platform
}  // namespace ks
