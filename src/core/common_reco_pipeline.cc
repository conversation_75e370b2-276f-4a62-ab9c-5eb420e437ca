#include "dragon/src/core/common_reco_pipeline.h"
#include <string>

#include "dragon/src/module/traceback_util.h"
#include "dragon/src/util/attr_io_check_util.h"
#include "dragon/src/util/cpu_cost_util.h"
#include "dragon/src/util/cpuinfo_util.h"
#include "dragon/src/util/global_thread_pool.h"
#include "dragon/src/util/perf_report_util.h"
#include "folly/Optional.h"
#include "folly/system/ThreadName.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"

DEFINE_bool(default_traceback_config_value, true, "default value of processor json config 'traceback'");

DEFINE_int32(traceback_json_config_max_bytes, 10 * 1024,
             "traceback processor's json_config max size, default 10K");

DEFINE_int32(parallel_init_processor_concurrency, 0, "parallel init processor concurrency");

DEFINE_string(ktrace_processor_whitelist_kconf_key, "", "kconf key of ktrace processor whitelist");
DEFINE_string(early_return_if_timeout_kconf_key, "",
              "kconf switch for pipeline early return if deadline exceeded");

DEFINE_bool(enable_processor_output_attr_cache, false, "control whether processor enables local attr cache");
DEFINE_bool(enable_processor_retrieve_item_cache, false,
            "control whether processor enables local retrieve cache");
DEFINE_bool(enable_grpc_streaming_server, false, "open grpc streaming server");
DEFINE_bool(enable_processor_type_name_as_thread_name, false,
            "set thread name to current processor's type name");

namespace ks {
namespace platform {
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::Initialize(
    const base::Json *config, const base::Json *processor_config, const base::Json *combo_processor_config) {
  if (!processor_config) {
    LOG(ERROR) << "dynamic_json_config has no processor config";
    return false;
  }

  auto *pipeline_processors = config->Get("pipeline");
  if (!pipeline_processors) {
    LOG(ERROR) << "dynamic_json_config has no pipeline config";
    return false;
  }

  std::unordered_map<std::string, std::vector<std::string>> combo_processor;
  if (combo_processor_config) {
    for (const auto &pr : combo_processor_config->objects()) {
      if (base::StartsWith(pr.first, "__", true)) {
        VLOG(100) << "skip private key name: " << pr.first;
        continue;
      }
      std::vector<std::string> processors;
      if (!RecoUtil::ExtractStringListFromJsonConfig(pr.second, &processors, true, true)) {
        LOG(ERROR) << "'combo_processor' should be a string array! Value found: " << pr.second->ToString();
        return false;
      }
      combo_processor.insert({pr.first, std::move(processors)});
    }
  }

  std::vector<std::string> all_processors;
  for (auto *processor_name : pipeline_processors->array()) {
    auto it = combo_processor.find(processor_name->StringValue());
    if (it == combo_processor.end()) {
      all_processors.push_back(processor_name->StringValue());
    } else {
      std::copy(it->second.begin(), it->second.end(), std::back_inserter(all_processors));
    }
  }

  table_name_ = config->GetString("item_table", "");

  post_response_ = config->GetBoolean("post_response", false);

  if (FLAGS_parallel_init_processor_concurrency > 0) {
    if (FLAGS_parallel_init_pipeline_concurrency > 0) {
      if (!FastParallelRegisterProcessor(processor_config, all_processors)) {
        return false;
      }
    } else {
      if (!ParallelRegisterProcessor(processor_config, all_processors)) {
        return false;
      }
    }
  } else {
    if (!SerialRegisterProcessor(processor_config, all_processors)) {
      return false;
    }
  }

  if (!FLAGS_ktrace_processor_whitelist_kconf_key.empty()) {
    auto default_val = std::make_shared<std::set<std::string>>();
    ktrace_processor_whitelist_ =
        ks::infra::KConf().GetSet(FLAGS_ktrace_processor_whitelist_kconf_key, std::move(default_val));
  }

  loop_control_attr_ = config->GetString("loop_control_attr", "");
  max_loop_count_ = config->GetInt("max_loop_count", 0);
  loop_on_ = config->GetString("loop_on", "");
  loop_index_ = config->GetString("loop_index", "");
  loop_value_ = config->GetString("loop_value", "");

  if (loop_on_.empty()) {
    if (!loop_control_attr_.empty() && max_loop_count_ <= 0) {
      LOG(ERROR) << "max_loop_limit must be set and greater than 0 when not using loop_on";
      return false;
    }
  } else {
    if (loop_value_.empty()) {
      LOG(ERROR) << "loop_value should not be empty when using loop_on";
      return false;
    }
  }

  InitPipeline();
  ::google::FlushLogFiles(::google::ERROR);

  LOG(INFO) << "pipeline " << GetName() << " created successfully, processor num: " << processors_.size();
  return true;
}

// pipeline 执行逻辑
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::Execute(MutableContext *context,
                                                                                    Container *results) {
  if (loop_control_attr_.empty() && loop_on_.empty()) {
    int64 st = base::GetTimestamp();
    DoExecute(context, results);
    CL_LOG(INFO) << "pipeline finished, pipeline: " << GetName()
                 << ", total time: " << (base::GetTimestamp() - st) / 1000.0 << " ms";
  } else if (loop_on_.empty()) {
    DoWhileLoopExecute(context, results);
  } else {
    ForLoopExecute(context, results);
  }
}

// pipeline 执行逻辑
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::DoWhileLoopExecute(
    MutableContext *context, Container *results) {
  int loop_count = 0;
  absl::optional<int64> break_signal;
  int64 st = base::GetTimestamp();
  do {
    DoExecute(context, results);
    if (!loop_control_attr_.empty()) {
      break_signal = context->GetIntCommonAttr(loop_control_attr_);
    }
    loop_count++;
  } while (loop_count < max_loop_count_ && break_signal.value_or(0));

  if (max_loop_count_ > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(loop_count, kPerfNs, "pipeline_loop_count",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), name_);
    CL_LOG(INFO) << "pipeline loop finished, pipeline: " << GetName() << ", loop count: " << loop_count
                 << ", total time: " << (base::GetTimestamp() - st) / 1000.0 << " ms";
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::ForLoopExecute(
    MutableContext *context, Container *results) {
  int64 st = base::GetTimestamp();
  int64 loop_count = 0;
  auto attr_accessor = context->GetCommonAttrAccessor(loop_on_);
  switch (attr_accessor->value_type) {
    case AttrType::INT_LIST: {
      auto values = attr_accessor->GetIntListValue();
      if (values) {
        for (const auto int_val : *values) {
          if (!loop_index_.empty()) {
            context->SetIntCommonAttr(loop_index_, loop_count);
          }
          context->SetIntCommonAttr(loop_value_, int_val);
          DoExecute(context, results);
          ++loop_count;
          if ((max_loop_count_ > 0 && loop_count >= max_loop_count_) ||
              (!loop_control_attr_.empty() && !context->GetIntCommonAttr(loop_control_attr_).value_or(0))) {
            break;
          }
        }
      }
      break;
    }
    case AttrType::FLOAT_LIST: {
      auto values = attr_accessor->GetDoubleListValue();
      if (values) {
        for (const auto double_val : *values) {
          if (!loop_index_.empty()) {
            context->SetIntCommonAttr(loop_index_, loop_count);
          }
          context->SetDoubleCommonAttr(loop_value_, double_val);
          DoExecute(context, results);
          ++loop_count;
          if ((max_loop_count_ > 0 && loop_count >= max_loop_count_) ||
              (!loop_control_attr_.empty() && !context->GetIntCommonAttr(loop_control_attr_).value_or(0))) {
            break;
          }
        }
      }
      break;
    }
    case AttrType::STRING_LIST: {
      auto values = attr_accessor->GetStringListValue();
      if (values) {
        for (const auto str_val : *values) {
          if (!loop_index_.empty()) {
            context->SetIntCommonAttr(loop_index_, loop_count);
          }
          context->SetStringCommonAttr(loop_value_, std::string(str_val.data(), str_val.size()));
          DoExecute(context, results);
          ++loop_count;
          if ((max_loop_count_ > 0 && loop_count >= max_loop_count_) ||
              (!loop_control_attr_.empty() && !context->GetIntCommonAttr(loop_control_attr_).value_or(0))) {
            break;
          }
        }
      }
      break;
    }
    default:
      CL_LOG_ERROR("pipeline_loop_fail", absl::StrCat("unsupport attr type: ", loop_on_))
          << "unsupport attr type: " << static_cast<int>(attr_accessor->value_type) << " attr: " << loop_on_
          << " pipeline: " << name_;
  }

  if (loop_count > 0) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(loop_count, kPerfNs, "pipeline_loop_count",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), name_);
    CL_LOG(INFO) << "pipeline loop finished, pipeline: " << GetName() << ", loop count: " << loop_count
                 << ", loop_on: " << loop_on_ << ", total time: " << (base::GetTimestamp() - st) / 1000.0
                 << " ms";
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::PipelineExit(
    ImmutableContext *context) {
  std::for_each(processor_run_.rbegin(), processor_run_.rend(), [context](auto &processor) {
    if (processor->GetHasBeenRun()) {
      processor->OnPipelineExitImpl(context);
      processor->SetHasBeenRun(false);
    }
  });
}

// pipeline 单次执行逻辑
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::DoExecute(
    MutableContext *context, Container *pipeline_results) {
  int64 pipeline_start_ts = base::GetTimestamp();
  if (context->GetNeedPipelineCpuCost()) {
    auto &pipeline_cpu_cost_counter = context->GetPipelineCpuCost();
    pipeline_cpu_cost_counter.TimingStart(name_);
  }

  std::unique_ptr<ks::infra::ktrace::ScopeKSpan> pipeline_scope_guard;
  std::shared_ptr<ks::infra::ktrace::KSpan> pipeline_kspan;
  if (ks::platform::KtraceUtil::IsKtraceEnabledForPipeline() &&
      ks::infra::ktrace::TracerManager::IsSampled()) {
    pipeline_scope_guard = std::make_unique<ks::infra::ktrace::ScopeKSpan>("PIPELINE");
    pipeline_kspan = pipeline_scope_guard->Get();
    if (pipeline_kspan) {
      pipeline_kspan->SetComponentType("LOCAL");
      pipeline_kspan->SetComponentName(name_);
      pipeline_kspan->SetTag("request_type", context->GetRequestType());
    }
  }
  base::ScopeExit on_pipeline_end([this, context, pipeline_start_ts] {
    // 如果 pipeline 结束后还有未处理的异步请求, 这里兜底处理一下, 防止数据依赖问题穿越 pipeline 存在
    int64 wait_start = base::GetTimestamp();
    int wait_num = context->WaitAllCallbacks();
    if (wait_num > 0) {
      int64 wait_time = base::GetTimestamp() - wait_start;
      std::string tag_name = "PIPELINE_END:" + GetName();
      CL_PERF_INTERVAL(wait_time, kPerfNs, "async_waiting_time", GlobalHolder::GetServiceIdentifier(),
                       context->GetRequestType(), tag_name);
      CL_PERF_INTERVAL(wait_num, kPerfNs, "async_concurrency", GlobalHolder::GetServiceIdentifier(),
                       context->GetRequestType(), tag_name);
      CL_LOG(INFO) << "all callbacks executed, num: " << wait_num << ", wait time: " << wait_time / 1000.0
                   << " ms, before pipeline end: " << GetName();
    }

    int64 duration = base::GetTimestamp() - pipeline_start_ts;
    CL_PERF_INTERVAL(duration, kPerfNs, "pipeline_run_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), name_);
    CL_LOG(INFO) << "pipeline end: " << name_ << ", request_type: " << context->GetRequestType()
                 << ", is_debug: " << context->IsDebugRequest() << ", time cost: " << (duration / 1000.0)
                 << " ms";
    if (context->GetNeedPipelineCpuCost()) {
      auto &pipeline_cpu_cost_counter = context->GetPipelineCpuCost();
      pipeline_cpu_cost_counter.TimingStep(name_);
    }
    context->SetRunningProcessor(nullptr);
  });

  static auto early_return_if_timeout_kconf =
      ks::infra::KConf().Get(FLAGS_early_return_if_timeout_kconf_key, true);
  bool early_return_if_timeout = early_return_if_timeout_kconf->Get();
  bool degrade_is_enabled = DegraderManager::Singleton()->IsEnabled();

  step_infos_count_ = 0;
  for (int i = 0; i < processor_run_.size(); ++i) {
    if (context->GetExecutionStatus() != ExecutionStatus::UNKNOWN) {
      return;
    }

    if (FLAGS_enable_grpc_streaming_server) {
      if (context->GetStreamingStatus() != StreamingStatus::NORMAL) {
        VLOG(10) << "streaming status: " << static_cast<int>(context->GetStreamingStatus())
                 << " break processor loop";
        break;
      }
    }

    if (early_return_if_timeout && context->IsRequestCancelled()) {
      CL_LOG_EVERY_N(WARNING, 100) << "request cancelled, terminating pipeline execution (" << i << "/"
                                   << processor_run_.size() << ")! time_left: " << context->GetTimeLeft()
                                   << "ms";
      return;
    }

    auto *processor = processor_run_[i];
    auto *results = pipeline_results;
    auto processor_table_name = processor->GetTableName();
    if (processor->HasTableName()) {
      results = static_cast<CommonRecoContext *>(context)->GetRecoResults(processor_table_name);
    } else {
      processor_table_name = GetTableName();
    }

    const auto *item_from_tables = processor->ItemFromTables();
    std::vector<CommonRecoResult> alternative_items;
    if (item_from_tables && (processor->GetType() == ProcessorType::OBSERVER ||
                             processor->GetType() == ProcessorType::ENRICHER)) {
      alternative_items.clear();
      for (const auto &table_name : item_from_tables->array()) {
        if (table_name->IsString()) {
          const auto &items = context->GetCommonRecoResults(json_string_value(table_name->get()));
          alternative_items.insert(alternative_items.end(), items.begin(), items.end());
        }
      }
      results = &alternative_items;
    }

    context->IncrTracebackSequence();
    // 同一个 processor 实例可能被多个 pipeline 调用, 这里需要重新设置一下位置
    processor->SetPipelinePosition(i);
    // 检查是否有依赖的异步 callback 需要先处理
    WaitForCallbacks(context, processor, results, processor->GetName());
    if (context->GetExecutionStatus() != ExecutionStatus::UNKNOWN) {
      return;
    }

    context->SetRunningProcessor(processor);

    int size_pre = results->size();
    bool processor_skipped = true;
    bool missing_downstream = false;

    if (context->GetNeedStepInfo() && i == 0) {
      RecordStepInfo(context, processor, false, 0, size_pre, results, "", "pipeline_start", true);
    }
    int64 start_ts = base::GetTimestamp();

    if (!processor->Skip(context, std::cbegin(*results), std::cend(*results))) {
      // 处理熔断前，检查是否需要动态注册 degrader
      processor->RegistDynamicDegrader(context);
      // 只对异步 Processor 处理熔断
      bool is_degrade = false;
      if (processor->IsAsync() && degrade_is_enabled &&
          !(DegraderManager::Singleton()->IsAllowedToRun(processor->GetDegradeKey(context)))) {
        // 上报降级信息，设置降级字段
        std::string status_attr = processor->GetAsyncStatusAttr();
        if (!status_attr.empty()) {
          context->SetIntCommonAttr(status_attr, 2);
        }
        base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "processor_degrade",
                                                       GlobalHolder::GetServiceIdentifier(),
                                                       context->GetRequestType(), processor->GetName());
        CL_LOG(INFO) << "Processor degraded: " << processor->GetName()
                     << ", request_type: " << context->GetRequestType();

        // limit 配置下，仍然执行算子
        if (!(processor->GetDegradeLimitAttr().empty())) {
          processor->SetDegradeLimit();
        } else {
          is_degrade = true;
        }
      }

      if (!is_degrade) {
        if (processor->IsAsync() && processor->GetDownstreamProcessor().empty()) {
          missing_downstream = true;
          CL_LOG(INFO) << "missing 'downstream_processor' config for async processor: "
                       << processor->GetName()
                       << ", its callback wait will be executed immediately after itself.";
        }
        std::unique_ptr<ks::infra::ktrace::ScopeKSpan> scope_guard;
        std::shared_ptr<ks::infra::ktrace::KSpan> kspan;
        if (ks::platform::KtraceUtil::IsKtraceEnabledForProcessor() &&
            ks::infra::ktrace::TracerManager::IsSampled() && IsKtraceEnabled(processor)) {
          scope_guard = std::make_unique<ks::infra::ktrace::ScopeKSpan>("PROCESSOR");
          kspan = scope_guard->Get();
          if (kspan) {
            kspan->SetComponentType("LOCAL");
            kspan->SetComponentName(processor->GetName());
            if (processor->GetMetaData()->span_tags) {
              for (auto &tag : *processor->GetMetaData()->span_tags) {
                kspan->SetTag(tag.first, tag.second);
              }
            }
          }
        }
        if (context->GetNeedTableCpuCost()) {
          context->GetTableCpuCost().TimingStart(processor_table_name);
        }

        processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), true, false);

        processor->OnEnter(context);
        processor->Run(context, results);
        processor->OnExit(context);

        processor->InspectAttrs(context, std::cbegin(*results), std::cend(*results), false,
                                processor->IsAsync());
        processor->SetHasBeenRun(true);

        processor_skipped = false;
        if (context->GetNeedTableCpuCost()) {
          context->GetTableCpuCost().TimingStep(processor_table_name);
        }
      }
    }

    int64 duration = base::GetTimestamp() - start_ts;
    if (!processor_skipped) {
      if (processor->IsAsync()) {
        CL_PERF_INTERVAL(
            duration, kPerfNs, "processor_async_prepare", GlobalHolder::GetServiceIdentifier(),
            context->GetRequestType(), processor->GetName(), processor->type_name(),
            processor->GetDownstreamProcessor(), GlobalHolder::GetJsonConfigVersion());
      } else {
        if (processor->IsBranchController()) {
          VLOG(50) << "branch_controller " << processor->GetName() << " run_time: " << (duration / 1000.0)
                   << " ms";
        } else {
          CL_PERF_INTERVAL(duration, kPerfNs, "processor_run_time", GlobalHolder::GetServiceIdentifier(),
                           context->GetRequestType(), processor->GetName(), processor->type_name(), "",
                           GlobalHolder::GetJsonConfigVersion());
        }
      }
    }
    CL_LOG(INFO) << "Processor " << (processor_skipped ? "skip" : "done") << " (" << (i + 1) << "/"
                 << processor_run_.size() << "): [" << processor->GetName() << "]"
                 << " is_async: " << processor->IsAsync() << ", item num: " << size_pre << " -> "
                 << results->size() << ", time cost: " << (duration / 1000.0) << " ms";

    if (missing_downstream) {
      // 如果 async processor 漏配了 downstream_processor 则直接在 processor 结束后执行一次 wait
      WaitForCallbacks(context, processor, results, processor->GetName() + " (self_end)", true);
    }

    if (context->GetNeedStepInfo()) {
      // 由于可能存在删除又重新召回的 item 使用之前的 item_attr 情况，内存限制先知需要重新上报下
      // 现在 pipeline 最后一个 processor 上报一下所有需要 traceback 的 item_attr
      RecordStepInfo(context, processor, processor_skipped, duration, size_pre, results, "", "",
                     i == processor_run_.size() - 1);
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
std::string CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetInfo() const {
  std::ostringstream oss;
  oss << "[";
  for (auto *processor : processor_run_) {
    oss << processor->GetName() << ", ";
  }
  oss << "]";
  return oss.str();
}

// XXX(bixiaodong) 判断一次请求中，针对每个 processor 是否设置详细的 traceback 信息
// 判断优先级：kconf 配置 > 请求携带配置 > processor 的 traceback 配置 > gflags 默认值
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::NeedTracebackDetails(
    ImmutableContext *context, CommonProcessor *processor) {
  if (auto black_list = context->GetTracebackProcessorsBlacklist()) {
    if (!black_list->empty() && black_list->count(processor->GetName())) {
      return false;
    }
  }
  if (auto white_list = context->GetTracebackProcessors()) {
    if (!white_list->empty()) {
      if (white_list->count(processor->GetName())) {
        return true;
      } else {
        return processor->RecordStepInfoDetails(context, false);
      }
    }
  }

  return processor->RecordStepInfoDetails(context, FLAGS_default_traceback_config_value);
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
CommonRecoStepInfo *
CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SetStepInfoBaseInfo(
    ImmutableContext *context, CommonProcessor *processor, bool processor_skipped, int64 duration,
    int size_pre, Container *results, const std::string &trigger_alias, const std::string &type_name) {
  auto *info = GetNewStepInfo();
  info->set_skip(processor_skipped);
  info->set_processor_name(trigger_alias.empty() ? processor->GetName() : trigger_alias);
  info->set_type_name(type_name.empty() ? processor->type_name() : type_name);
  info->set_stage_name(processor->GetPipelineName());
  if (processor->GetMetaData()->json_config_string.size() > FLAGS_traceback_json_config_max_bytes) {
    info->set_json_config("{\"json_config_too_large\": " +
                          base::Int64ToString(processor->GetMetaData()->json_config_string.size()) + "}");
  } else {
    info->set_json_config(processor->GetMetaData()->json_config_string);
  }
  info->set_duration(duration);
  info->set_pre_item_num(size_pre);
  info->set_post_item_num(results->size());
  info->set_hide(!NeedTracebackDetails(context, processor));
  if (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.1") >= 0) {
    // v2.0.1 为记录 subflow 的版本
    traceback_util::RecordTracebackSequence(info, context->GetTracebackSequence());
  }
  return info;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::RecordStepInfo(
    MutableContext *context, CommonProcessor *processor, bool processor_skipped, int64 duration, int size_pre,
    Container *results, const std::string &trigger_alias, const std::string &type_name,
    bool record_result_and_attrs) {
  // NOTE(caohongjin) 记录 traceback 数据时会读取 attr，为了避免误检，先临时关闭属性检测功能
  bool attr_check_enabled = AttrIOCheckUtil::IsEnabled();
  if (attr_check_enabled) {
    AttrIOCheckUtil::DisableCheck();
  }
  base::ScopeExit scope_exit([attr_check_enabled] {
    if (attr_check_enabled) {
      AttrIOCheckUtil::EnableCheck();
    }
  });

  if (!record_result_and_attrs &&
      traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.1.0") >= 0 &&
      !(context->GetTracebackWhiteListUserReportAllProcessors() &&
        context->GetTracebackCollectMode() == WHITE_LIST) &&
      !NeedTracebackDetails(context, processor)) {
    SetTracebackOmitInfo(context, processor);
    if (processor->type_name() == "CommonRecoPipelineRetriever" ||
        processor->type_name() == "CommonRecoPipelineEnricher" ||
        processor->type_name() == "CommonRecoPipelineArranger" ||
        processor->type_name() == "CommonRecoPipelineMixer") {
      SetStepInfoBaseInfo(context, processor, processor_skipped, duration, size_pre, results, trigger_alias,
                          type_name);
    }
    return;
  }

  // 如果需要每一步的详情数据
  int64 start_ts = base::GetTimestamp();
  auto *info = SetStepInfoBaseInfo(context, processor, processor_skipped, duration, size_pre, results,
                                   trigger_alias, type_name);

  if (!processor_skipped) {
    // 多表版本
    if (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v3.0.0") >= 0) {
      SaveCommonData(context, info);
      SaveItemData(context, processor, info);
    } else if (context->GetShouldCompressItem() || context->GetShouldDropStudioField()) {
      // 结果集和 item_attr 继承上报版本
      SaveCommonAttrs(context, info);
      if (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.0") >= 0) {
        SaveResultsToPackedItemValue(context, results, processor, info, record_result_and_attrs);
      } else {
        SaveResultsToCompressItem(context, results, processor, info);
      }
    } else if (NeedTracebackDetails(context, processor)) {
      SaveCommonAttrs(context, info);
      SaveResultsToPostResult(context, results, info);
    }
  }
  // 支持上报 subflow 数据
  if (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.1") >= 0) {
    for (int i = 0; i < context->GetSubflowStepInfosCount(); ++i) {
      if (context->GetProcessorStepInfo(i)->sequence_size() > 0) {
        auto *step_info = GetNewStepInfo();
        step_info->Swap(context->GetProcessorStepInfo(i));
      }
    }
    context->ClearSubflowStepInfo();
  }
  SaveFilterReason(context, info);

  int64 record_time = base::GetTimestamp() - start_ts;
  base::perfutil::PerfUtilWrapper::IntervalLogStash(record_time, kPerfNs, "step_info_record_time",
                                                    GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), processor->GetName());
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveCommonData(
    ImmutableContext *context, CommonRecoStepInfo *info) {
  const auto *traceback_common_attrs = context->GetTracebackCommonAttrs();
  if (!traceback_common_attrs) return;

  std::vector<AttrValue *> attr_accessors;
  attr_accessors.reserve(traceback_common_attrs->size());
  for (const auto &attr_name : *traceback_common_attrs) {
    attr_accessors.push_back(context->GetCommonAttrAccessor(attr_name));
  }
  std::vector<CommonRecoResult> items;
  items.push_back(CommonRecoResult(0, 0, 0));
  auto save_attr_succ_count = RecoUtil::BuildPackedTableColumns(items.begin(), items.end(), attr_accessors,
                                                                info->mutable_common_data());
  for (int i = 0; i < save_attr_succ_count.size(); i++) {
    if (save_attr_succ_count[i] == 0) {
      CL_LOG_EVERY_N(INFO, 100) << "save common attr to step info failed, attr_name: "
                                << traceback_common_attrs->at(i)
                                << ", processor_name: " << info->processor_name();
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveCommonAttrs(
    ImmutableContext *context, CommonRecoStepInfo *info) {
  if (!context->GetTracebackCommonAttrs()) return;
  for (const auto &attr_name : *context->GetTracebackCommonAttrs()) {
    auto *attr = info->add_common_attrs();
    if (!interop::LoadSampleAttrFromCommonAttr(context, attr_name, attr)) {
      attr->set_name(attr_name);
      CL_LOG_EVERY_N(INFO, 100) << "save common attr to step info failed, attr_name: " << attr_name
                                << ", processor_name: " << info->processor_name();
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::AddDataTable(
    MutableContext *context, CommonRecoStepInfo *info, absl::string_view table_name,
    const std::vector<ItemAttr *> &accessors) {
  auto *item_table = info->add_item_data();
  item_table->set_name({table_name.data(), table_name.size()});

  auto *table = context->GetOrInsertDataTable(table_name);
  const auto &table_results = table->GetCommonRecoResults();
  RecoUtil::BuildPackedTableColumns(table_results.begin(), table_results.end(), accessors, item_table);
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveItemData(
    MutableContext *context, CommonProcessor *processor, CommonRecoStepInfo *info) {
  const auto *output_item_attrs = processor->GetMetaData()->output_item_attrs.get();
  const auto &table_attrs =
      GetMultiTableTracebackItemAttrAccessors(context, processor, output_item_attrs, nullptr, true, false);
  for (auto it = table_attrs.begin(); it != table_attrs.end(); ++it) {
    AddDataTable(context, info, it->first, it->second);
  }

  auto *table_changed = context->GetAccumulatedTracebackTables();
  if (NeedResult(processor)) {
    if (processor->GetType() == ProcessorType::MIXER) {
      const auto *modify_item_tables = processor->GetMetaData()->modify_item_tables.get();
      if (modify_item_tables) {
        for (const auto &table : *modify_item_tables) {
          table_changed->insert({table.data(), table.size()});
        }
      }
    } else {
      table_changed->insert(processor->GetTableName());
    }
  }
  if (!table_changed->empty()) {
    for (const std::string &table : *table_changed) {
      if (table_attrs.find(table) == table_attrs.end()) {
        AddDataTable(context, info, table);
      }
    }
    table_changed->clear();
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::IsKtraceEnabled(
    const CommonProcessor *processor) {
  if (absl::StrContains(processor->GetName(), "_branch_controller_")) return false;
  if (ktrace_processor_whitelist_) {
    auto ktrace_whitelist_set = ktrace_processor_whitelist_->Get();
    if (ktrace_whitelist_set->find(processor->GetName()) != ktrace_whitelist_set->end()) {
      return true;
    }
    if (ktrace_whitelist_set->find(processor->type_name()) != ktrace_whitelist_set->end()) {
      return true;
    }
    return false;
  }
  return true;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::NeedRecordItemAttr(
    CommonProcessor *processor, const std::set<absl::string_view> *output_item_attrs,
    const std::set<std::string> *downstream_item_attrs, const std::string &attr_name,
    bool report_multi_table) {
  // 如果 output_item_attrs 和 downstream_item_attrs 均为空
  // 说明业务方生成 json 未打开自动填充 item_attr 开关，则全部都打出。
  // 如果存在 output_item_attrs or 存在 downstream_item_attrs 的时候
  // 其中一个含有即表示应该输出该 item_attr

  // 如果强制打出全部 traceback 数据，则不做 item_attrs 的判断
  if (!processor->RecordStepInfoFullDetails()) {
    if (output_item_attrs || downstream_item_attrs) {
      if (output_item_attrs && !output_item_attrs->empty()) {
        for (const auto &output_item_attr : *output_item_attrs) {
          std::string output_attr = {output_item_attr.data(), output_item_attr.size()};
          if (processor->GetType() != ProcessorType::MIXER && report_multi_table) {
            output_attr = absl::StrCat(processor->GetTableName(), "::", output_attr);
          }
          if (output_attr == attr_name) {
            return true;
          }
        }
      }
      if (downstream_item_attrs && !downstream_item_attrs->empty() &&
          downstream_item_attrs->count(attr_name) != 0) {
        return true;
      }
      return false;
    }
  }
  return true;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
ItemAttr *CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetTracebackAccessor(
    ImmutableContext *context, std::string *compressed_attrs, const std::string &attr_name,
    bool include_list_attr, CommonProcessor *processor) {
  ItemAttr *accessor;
  size_t id = attr_name.find("::");
  if (id != std::string::npos) {
    accessor = context->GetOrInsertItemAttrFromTable(attr_name.substr(id + 2, attr_name.size() - id - 2),
                                                     attr_name.substr(0, id));
  } else {
    accessor = context->GetItemAttrAccessor(attr_name);
  }

  bool should_record = false;
  switch (accessor->value_type) {
    case AttrType::INT:
    case AttrType::FLOAT:
    case AttrType::STRING:
      should_record = true;
      break;
    case AttrType::INT_LIST:
    case AttrType::FLOAT_LIST:
    case AttrType::STRING_LIST:
      should_record = include_list_attr;
      break;
    default:
      should_record = false;
      break;
  }

  if (should_record) {
    if (compressed_attrs) {
      if (!compressed_attrs->empty()) {
        compressed_attrs->append(kTracebackAttrNameSeparator);
      }
      compressed_attrs->append(attr_name);
    }
    return accessor;
  }
  return nullptr;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
std::vector<ItemAttr *>
CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetTracebackItemAttrAccessors(
    MutableContext *context, CommonProcessor *processor, const std::set<absl::string_view> *output_item_attrs,
    std::string *compressed_attrs, bool include_list_attr, bool record_result_and_attrs) {
  std::vector<ItemAttr *> accessors;
  const auto *downstream_item_attrs = processor->GetMetaData()->downstream_item_attrs.get();

  if (context->GetTracebackItemAttrs()) {
    for (const auto &attr_name : *context->GetTracebackItemAttrs()) {
      if (record_result_and_attrs ||
          NeedRecordItemAttr(processor, output_item_attrs, downstream_item_attrs, attr_name, false)) {
        auto accessor =
            GetTracebackAccessor(context, compressed_attrs, attr_name, include_list_attr, processor);
        if (accessor) {
          accessors.emplace_back(accessor);
        }
      }
    }
  }
  if (auto *accumulated_traceback_item_attrs = context->GetAccumulatedTracebackItemAttrs()) {
    for (const auto &attr_name : *accumulated_traceback_item_attrs) {
      ItemAttr *accessor = GetTracebackAccessor(context, compressed_attrs, attr_name, include_list_attr);
      if (accessor) {
        accessors.emplace_back(accessor);
      }
    }
    accumulated_traceback_item_attrs->clear();
  }
  return accessors;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
std::string CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetTableName(
    const std::string &attr_name) {
  size_t id = attr_name.find("::");
  if (id != std::string::npos) {
    return attr_name.substr(0, id);
  }
  return "";
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
std::unordered_map<std::string, std::vector<ItemAttr *>> CommonPipeline<
    MetaData, MutableContext, ImmutableContext,
    Container>::GetMultiTableTracebackItemAttrAccessors(MutableContext *context, CommonProcessor *processor,
                                                        const std::set<absl::string_view> *output_item_attrs,
                                                        std::string *compressed_attrs, bool include_list_attr,
                                                        bool record_result_and_attrs) {
  std::unordered_map<std::string, std::vector<ItemAttr *>> table_attrs;
  const auto *downstream_item_attrs = processor->GetMetaData()->downstream_item_attrs.get();

  if (context->GetTracebackItemAttrs()) {
    for (const auto &attr_name : *context->GetTracebackItemAttrs()) {
      if (record_result_and_attrs ||
          NeedRecordItemAttr(processor, output_item_attrs, downstream_item_attrs, attr_name, true)) {
        auto accessor =
            GetTracebackAccessor(context, compressed_attrs, attr_name, include_list_attr, processor);
        if (accessor) {
          table_attrs[GetTableName(attr_name)].emplace_back(accessor);
        }
      }
    }
  }
  if (auto *accumulated_traceback_item_attrs = context->GetAccumulatedTracebackItemAttrs()) {
    for (const auto &attr_name : *accumulated_traceback_item_attrs) {
      ItemAttr *accessor = GetTracebackAccessor(context, compressed_attrs, attr_name, include_list_attr);
      if (accessor) {
        table_attrs[GetTableName(attr_name)].emplace_back(accessor);
      }
    }
    accumulated_traceback_item_attrs->clear();
  }
  return table_attrs;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::NeedResult(
    CommonProcessor *processor) {
  const auto *output_item_attrs = processor->GetMetaData()->output_item_attrs.get();
  const bool downstream_new_result = processor->GetMetaData()->downstream_new_result;
  bool need_result = !output_item_attrs || processor->GetType() == ProcessorType::ARRANGER ||
                     processor->GetType() == ProcessorType::RETRIEVER ||
                     processor->GetType() == ProcessorType::MIXER || downstream_new_result;
  return need_result;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
std::vector<ItemAttr *>
CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SetupTracebackBaseInfo(
    MutableContext *context, const Container *results, CommonProcessor *processor, CommonRecoStepInfo *info,
    bool include_list_attr, bool record_result_and_attrs) {
  const auto *output_item_attrs = processor->GetMetaData()->output_item_attrs.get();
  auto *compressed_item = info->mutable_compressed_items();
  std::string *compressed_attrs = compressed_item->mutable_compressed_attrs();

  std::vector<ItemAttr *> accessors = GetTracebackItemAttrAccessors(
      context, processor, output_item_attrs, compressed_attrs, include_list_attr, record_result_and_attrs);

  uint64 uint64_val = 0;
  // int64 int64_val = 0;
  int32 int32_val = 0;
  float float_val = 0.0;

  auto *compressed_item_ids = compressed_item->mutable_compressed_item_ids();
  auto *compressed_item_types = compressed_item->mutable_compressed_item_types();
  auto *compressed_reasons = compressed_item->mutable_compressed_reasons();
  auto *compressed_channels = compressed_item->mutable_compressed_channels();
  auto *compressed_scores = compressed_item->mutable_compressed_scores();

  bool need_result = NeedResult(processor) || record_result_and_attrs;
  if (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.1.0") >= 0) {
    need_result = need_result || context->IsTracebackResultChanged();
    context->SetTracebackResultChanged(false);
  }
  if (need_result) {
    compressed_item_ids->reserve(results->size() * sizeof(uint64));
    compressed_item_types->reserve(results->size() * sizeof(int32));
    compressed_reasons->reserve(results->size() * sizeof(int32));
    compressed_channels->reserve(results->size() * sizeof(int32));
    compressed_scores->reserve(results->size() * sizeof(float));
    for (const auto &result : *results) {
      uint64_val = result.GetId();
      compressed_item_ids->append(reinterpret_cast<const char *>(&uint64_val), sizeof(uint64));
      int32_val = result.GetType();
      compressed_item_types->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
      int32_val = result.reason;
      compressed_reasons->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
      int32_val = result.channel;
      compressed_channels->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
      float_val = result.score;
      compressed_scores->append(reinterpret_cast<const char *>(&float_val), sizeof(float));
    }
  } else if (!accessors.empty()) {
    compressed_item_ids->reserve(results->size() * sizeof(uint64));
    compressed_item_types->reserve(results->size() * sizeof(int32));
    for (const auto &result : *results) {
      uint64_val = result.GetId();
      compressed_item_ids->append(reinterpret_cast<const char *>(&uint64_val), sizeof(uint64));
      int32_val = result.GetType();
      compressed_item_types->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
    }
  }
  return accessors;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SetTracebackOmitInfo(
    MutableContext *context, CommonProcessor *processor) {
  const auto *output_item_attrs = processor->GetMetaData()->output_item_attrs.get();
  const auto *downstream_item_attrs = processor->GetMetaData()->downstream_item_attrs.get();
  bool report_multi_table =
      (traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v3.0.0") >= 0);
  if (context->GetTracebackItemAttrs()) {
    for (const auto &attr_name : *context->GetTracebackItemAttrs()) {
      if (NeedRecordItemAttr(processor, output_item_attrs, downstream_item_attrs, attr_name,
                             report_multi_table)) {
        context->AddAccumulatedTracebackItemAttrs(attr_name);
      }
    }
  }
  if (NeedResult(processor)) {
    if (report_multi_table) {
      if (processor->GetType() == ProcessorType::MIXER) {
        const auto *modify_item_tables = processor->GetMetaData()->modify_item_tables.get();
        if (modify_item_tables) {
          for (const auto &table : *modify_item_tables) {
            context->AddTracebackResultChanged({table.data(), table.size()});
          }
        }
      } else {
        context->AddTracebackResultChanged(processor->GetTableName());
      }
    } else {
      context->SetTracebackResultChanged(true);
    }
  }
  return;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveResultsToPackedItemValue(
    MutableContext *context, const Container *results, CommonProcessor *processor, CommonRecoStepInfo *info,
    bool record_result_and_attrs) {
  if (results->empty()) return;

  // 处理 TraceBack 日志中的基础信息，只有变化的 accessors 才会进行处理，新版本支持 list 数据类型
  std::vector<ItemAttr *> accessors =
      SetupTracebackBaseInfo(context, results, processor, info, true, record_result_and_attrs);
  auto *compressed_item = info->mutable_compressed_items();

  // item 为 row, attr 为 column, offset 是 二维矩阵拍平的位置。
  for (auto *accessor : accessors) {
    auto *attr_value = compressed_item->add_packed_item_attr();
    for (const auto &result : *results) {
      if (accessor->value_type == AttrType::INT) {
        traceback_util::BuildIntPackedTracebackData(attr_value, result.GetIntAttr(accessor));
      } else if (accessor->value_type == AttrType::FLOAT) {
        traceback_util::BuildDoublePackedTracebackData(attr_value, result.GetDoubleAttr(accessor));
      } else if (accessor->value_type == AttrType::STRING) {
        traceback_util::BuildStringPackedTracebackData(attr_value, result.GetStringAttr(accessor));
      } else if (accessor->value_type == AttrType::INT_LIST) {
        traceback_util::BuildIntListPackedTracebackData(attr_value, result.GetIntListAttr(accessor));
      } else if (accessor->value_type == AttrType::FLOAT_LIST) {
        traceback_util::BuildDoubleListPackedTracebackData(attr_value, result.GetDoubleListAttr(accessor));
      } else if (accessor->value_type == AttrType::STRING_LIST) {
        traceback_util::BuildStringListPackedTracebackData(attr_value, result.GetStringListAttr(accessor));
      }
    }
    // 在该 itemAttr 处理完之后，进行对 value 字段的压缩操作
    // 压缩算法目前仅对 string 和 string list 处理
    if (context->GetTracebackCompressMode() != PackedItemAttrValue_CompressMode_NO_COMPRESS &&
        (accessor->value_type == AttrType::STRING_LIST || accessor->value_type == AttrType::STRING)) {
      bool compress_success =
          traceback_util::CompressPackedTracebackData(attr_value, context->GetTracebackCompressMode());
      if (!compress_success) {
        CL_LOG_WARNING("traceback_compress_fail",
                       PackedItemAttrValue_CompressMode_Name(context->GetTracebackCompressMode()))
            << "Compress Error. Compress mode is: "
            << PackedItemAttrValue_CompressMode_Name(context->GetTracebackCompressMode());
      }
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveResultsToCompressItem(
    MutableContext *context, const Container *results, CommonProcessor *processor, CommonRecoStepInfo *info) {
  if (results->empty()) return;

  // 处理 TraceBack 日志中的基础信息，只有变化的 accessors 才会进行处理，老版本不支持 list 类型数据
  std::vector<ItemAttr *> accessors = SetupTracebackBaseInfo(context, results, processor, info, false);
  auto *compressed_item = info->mutable_compressed_items();

  // uint64 uint64_val = 0;
  int64 int64_val = 0;
  int32 int32_val = 0;
  float float_val = 0.0;

  auto *compressed_int_offsets = compressed_item->mutable_compressed_int_offsets();
  auto *compressed_int_values = compressed_item->mutable_compressed_int_values();
  auto *compressed_float_offsets = compressed_item->mutable_compressed_float_offsets();
  auto *compressed_float_values = compressed_item->mutable_compressed_float_values();
  auto *compressed_string_offsets = compressed_item->mutable_compressed_string_offsets();
  auto *compressed_string_value_indices = compressed_item->mutable_compressed_string_value_indices();
  auto *compressed_string_value_bytes = compressed_item->mutable_compressed_string_values();

  // 将二维的 item attr 拍平成一维的 数组, 用 offset 代表二维矩阵中的位置。
  // item_index = offset / attrs.size(); attr_index = offset % attrs.size();
  // int_offsets.size() == int_values.size(); float_offsets.size() == float_values.size();
  int32 value_offset = 0;
  int32 string_index_offset = 0;
  folly::F14FastMap<absl::string_view, int32, absl::Hash<absl::string_view>> string_value_index;
  // item 为 row, attr 为 column, offset 是 二维矩阵拍平的位置。
  for (int col = 0; col < accessors.size(); ++col) {
    auto *accessor = accessors[col];
    string_value_index.clear();
    for (int row = 0; row < results->size(); ++row) {
      const auto &result = (*results)[row];
      value_offset = row * accessors.size() + col;
      if (accessor->value_type == AttrType::INT) {
        if (auto int64_p = result.GetIntAttr(accessor)) {
          compressed_int_offsets->append(reinterpret_cast<const char *>(&value_offset), sizeof(int32));
          int64_val = *int64_p;
          compressed_int_values->append(reinterpret_cast<const char *>(&int64_val), sizeof(int64));
        }
      } else if (accessor->value_type == AttrType::FLOAT) {
        if (auto double_p = result.GetDoubleAttr(accessor)) {
          compressed_float_offsets->append(reinterpret_cast<const char *>(&value_offset), sizeof(int32));
          float_val = *double_p;
          compressed_float_values->append(reinterpret_cast<const char *>(&float_val), sizeof(float));
        }
      } else if (accessor->value_type == AttrType::STRING) {
        if (auto str_p = result.GetStringAttr(accessor)) {
          compressed_string_offsets->append(reinterpret_cast<const char *>(&value_offset), sizeof(int32));
          auto pr = string_value_index.emplace(*str_p, string_value_index.size());
          int32_val = pr.first->second + string_index_offset;
          compressed_string_value_indices->append(reinterpret_cast<const char *>(&int32_val), sizeof(int32));
          static constexpr size_t kStringValueMaxSize = (1 << sizeof(char)) - 1;
          size_t str_size = std::min(str_p->size(), kStringValueMaxSize);
          compressed_string_value_bytes->push_back(static_cast<char>(str_size));
          compressed_string_value_bytes->append(str_p->data(), str_size);
        }
      }
    }
    string_index_offset += string_value_index.size();
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveResultsToPostResult(
    ImmutableContext *context, const Container *results, CommonRecoStepInfo *info) {
  info->mutable_post_result()->Reserve(results->size());
  for (const auto &result : *results) {
    auto *p = info->add_post_result();
    p->set_item_id(result.GetId());
    p->set_item_type(result.GetType());
    p->set_reason(result.reason);
    p->set_score(result.score);
    if (!context->GetTracebackItemAttrs()) continue;
    for (const auto &attr_name : *context->GetTracebackItemAttrs()) {
      auto *attr = p->add_item_attr();
      if (!interop::LoadSampleAttrFromItemAttr(context, result.item_key, attr_name, attr)) {
        attr->set_name(attr_name);
      }
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::ClearFilterReason(
    MutableContext *context) {
  context->ClearTracebackFilterResult();
  context->ClearTracebackFilterResultOffset();
  context->ClearTracebackFilterReason();
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SaveFilterReason(
    MutableContext *context, CommonRecoStepInfo *info) {
  const auto &filter_results = context->GetTracebackFilterResult();
  if (filter_results.empty()) return;
  const auto &filter_result_offsets = context->GetTracebackFilterReasonOffset();
  const auto &filter_reasons = context->GetTracebackFilterReason();
  if (filter_results.size() != filter_result_offsets.size()) {
    ClearFilterReason(context);
    return;
  }
  auto *compressed_filter = info->mutable_compressed_filters();
  auto *compressed_filter_item_ids = compressed_filter->mutable_compressed_item_ids();
  auto *compressed_filter_item_types = compressed_filter->mutable_compressed_item_types();
  auto *compressed_filter_reasons = compressed_filter->mutable_compressed_reasons();
  auto *compressed_filter_offsets = compressed_filter->mutable_compressed_filter_reason_offsets();
  int filter_result_size = filter_results.size();
  compressed_filter_item_ids->reserve(filter_result_size * sizeof(int64));
  compressed_filter_item_types->reserve(filter_result_size * sizeof(int32));
  compressed_filter_reasons->reserve(filter_result_size * sizeof(int32));
  compressed_filter_offsets->reserve(filter_result_size * sizeof(int32));

  for (int i = 0; i < filter_result_size; i++) {
    const auto &filter_result = filter_results[i];
    uint64 id = Util::GetId(filter_results[i].item_key);
    int type = Util::GetType(filter_results[i].item_key);
    compressed_filter_item_ids->append(reinterpret_cast<const char *>(&id), sizeof(int64));
    compressed_filter_item_types->append(reinterpret_cast<const char *>(&type), sizeof(int32));
    compressed_filter_reasons->append(reinterpret_cast<const char *>(&filter_results[i].reason),
                                      sizeof(int32));
    compressed_filter_offsets->append(reinterpret_cast<const char *>(&filter_result_offsets[i]),
                                      sizeof(int32));
  }
  for (const auto &reason : filter_reasons) {
    compressed_filter->add_filter_reasons(reason);
  }
  ClearFilterReason(context);
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
typename CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::CommonProcessor *
CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::CreateProcessor(
    const std::string &name, const base::Json *config) {
  VLOG(100) << "creating processor " << name << ", type_name: " << config->GetString("type_name");
  folly::Optional<std::string> previous_thread_name;
  if (FLAGS_enable_processor_type_name_as_thread_name) {
    // NOTE(caohongjin) 线程名最多存储 15 个字符，这里尽量保留有用信息，比如 CommonRecoLuaAttrEnricher
    // 会记录为 monRecoLuaAttrE, 最后的 E 代表 Enricher
    static constexpr int kThreadNameMaxLength = 15;
    int start_pos = 0;
    previous_thread_name = folly::getCurrentThreadName();
    const std::string type_name = config->GetString("type_name");
    int processor_type_pos = 0;
    if ((processor_type_pos = type_name.rfind("Enricher")) != std::string::npos ||
        (processor_type_pos = type_name.rfind("Retriever")) != std::string::npos ||
        (processor_type_pos = type_name.rfind("Arranger")) != std::string::npos ||
        (processor_type_pos = type_name.rfind("Observer")) != std::string::npos ||
        (processor_type_pos = type_name.rfind("Mixer")) != std::string::npos) {
      start_pos = std::max(processor_type_pos - kThreadNameMaxLength + 1, 0);
    }
    std::string short_type_name = type_name.substr(start_pos, kThreadNameMaxLength);
    LOG(INFO) << "Processor type name mapping: " << type_name << " -> " << short_type_name;
    folly::setThreadName(short_type_name);
  }
  auto pr = base::JsonFactoryClass::NewWithNameAndErrorCode<CommonProcessor>(config, name);
  if (FLAGS_enable_processor_type_name_as_thread_name) {
    folly::setThreadName(previous_thread_name ? previous_thread_name.value() : "DragonDefaultThreadName");
  }
  int err_code = pr.first;
  if (err_code == 0) {
    auto *processor = pr.second;
    CHECK_NOTNULL(processor);
    if (!processor->HasTableName()) {
      processor->SetTableName(table_name_);
    }
    return processor;
  }

  if (err_code == 2) {
    LOG(ERROR) << "failed to create processor: " << name << " <" << config->GetString("type_name")
               << ">, which is NOT compiled by dragon, please check your binary version, "
               << "or the BUILD file and compile options!";
  } else if (err_code == 4) {
    LOG(ERROR) << "json config:\n"
               << config->ToString(2) << "\nfailed to initialize processor: " << name << " <"
               << config->GetString("type_name")
               << ">, please check the previous ERROR log for detail reason!";
  } else {
    LOG(ERROR) << "json config:\n"
               << config->ToString(2) << "\nfailed to create processor: " << name << " <"
               << config->GetString("type_name") << ">, error code: " << err_code;
  }
  return nullptr;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::ProcessorInitWithQueue(
    const std::string name, const base::Json *config, thread::BlockingQueue<CommonProcessor *> *queue) {
  auto *processor = CreateProcessor(name, config);
  if (processor) {
    queue->Put(processor);
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
const base::Json *CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetProcessorConfig(
    const std::string &name, const base::Json *processor_config) {
  auto it = processor_config->objects().find(name);
  if (it == processor_config->objects().end()) {
    LOG(ERROR) << "cannot find processor in processor config: " << name;
    return nullptr;
  }

  if (it->second == nullptr) {
    LOG(ERROR) << "cannot create processor: " << name << ", config is null!";
    return nullptr;
  }
  return it->second;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::FastParallelRegisterProcessor(
    const base::Json *processor_config, const std::vector<std::string> &all_processors) {
  int32 concurrency = FLAGS_parallel_init_processor_concurrency;
  thread::BlockingQueue<CommonProcessor *> q;
  folly::F14FastSet<std::string> duplicate_processors(all_processors.begin(), all_processors.end());
  auto it = duplicate_processors.begin();
  while (it != duplicate_processors.end()) {
    std::vector<std::future<int>> futures;
    for (int i = 0; i < concurrency && it != duplicate_processors.end(); ++i, ++it) {
      const auto &name = *it;
      if (processors_.Lookup(name) >= 0) {
        VLOG(100) << "processor already exists: " << name;
        q.Put(processors_.Get(name));
        continue;
      }
      auto *config = GetProcessorConfig(name, processor_config);
      if (config == nullptr) {
        LOG(ERROR) << "failed to register processor: " << name;
        return false;
      }
      std::string type_name = config->GetString("type_name");
      if (!DualLevelThreadPool::HasSubPipeline(type_name)) {
        VLOG(100) << "init processor in LeafNode: " << type_name;
        futures.emplace_back(
            DualLevelThreadPool::GetInstance()->AsyncRunForLeafNode([this, &name, config, &q]() {
              ProcessorInitWithQueue(name, config, &q);
              return 0;
            }));
      } else {
        VLOG(100) << "init processor in NonLeafNode: " << type_name;
        futures.emplace_back(
            DualLevelThreadPool::GetInstance()->AsyncRunForNonLeafNode([this, &name, config, &q]() {
              ProcessorInitWithQueue(name, config, &q);
              return 0;
            }));
      }
    }
    for (auto &fut : futures) {
      fut.wait();
    }
  }

  while (!q.Empty()) {
    auto *p = q.Take();
    processors_.Insert(p->GetName(), p);
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "processor_register",
                                                   GlobalHolder::GetServiceIdentifier(), p->type_name());
    VLOG(100) << "processor created: " << p->GetName() << ", type_name: " << p->type_name();
  }
  processor_run_.clear();
  for (const auto &name : all_processors) {
    auto *processor = processors_.Get(name);
    if (!processor) {
      LOG(ERROR) << "cannot find the instance of processor: " << name;
      return false;
    }
    processor->SetPipelineName(name_);
    processor_run_.push_back(processor);
  }
  return true;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::ParallelRegisterProcessor(
    const base::Json *processor_config, const std::vector<std::string> &all_processors) {
  thread::BlockingQueue<CommonProcessor *> q;
  thread::ThreadPool pool(FLAGS_parallel_init_processor_concurrency);
  folly::F14FastSet<std::string> duplicate_processors(all_processors.begin(), all_processors.end());
  for (const auto &name : duplicate_processors) {
    if (processors_.Lookup(name) >= 0) {
      VLOG(100) << "processor already exists: " << name;
      q.Put(processors_.Get(name));
      continue;
    }
    auto *config = GetProcessorConfig(name, processor_config);
    if (config == nullptr) {
      LOG(ERROR) << "failed to register processor: " << name;
      return false;
    }
    pool.AddTask(::NewCallback(
        this, &CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::ProcessorInitWithQueue,
        name, config, &q));
  }
  pool.JoinAll();

  while (!q.Empty()) {
    auto *p = q.Take();
    processors_.Insert(p->GetName(), p);
    base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "processor_register",
                                                   GlobalHolder::GetServiceIdentifier(), p->type_name());
    VLOG(100) << "processor created: " << p->GetName() << ", type_name: " << p->type_name();
  }
  processor_run_.clear();
  for (const auto &name : all_processors) {
    auto *processor = processors_.Get(name);
    if (!processor) {
      LOG(ERROR) << "cannot find the instance of processor: " << name;
      return false;
    }
    processor->SetPipelineName(name_);
    processor_run_.push_back(processor);
  }
  return true;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::SerialRegisterProcessor(
    const base::Json *processor_config, const std::vector<std::string> &all_processors) {
  processor_run_.clear();
  for (const auto &name : all_processors) {
    if (!RegisterProcessor(name, processor_config)) {
      LOG(ERROR) << "failed to register processor: " << name;
      return false;
    }
    auto *processor = processors_.Get(name);
    if (!processor) {
      LOG(ERROR) << "cannot find the instance of processor: " << name;
      return false;
    }
    processor->SetPipelineName(name_);
    processor_run_.push_back(processor);
  }
  return true;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
bool CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::RegisterProcessor(
    const std::string &name, const base::Json *processor_config) {
  if (processors_.Lookup(name) >= 0) {
    VLOG(100) << "processor already exists: " << name;
    return true;
  }
  auto *config = GetProcessorConfig(name, processor_config);
  if (config == nullptr) {
    return false;
  }

  auto *processor = CreateProcessor(name, config);
  if (processor) {
    processors_.Insert(name, processor);
    return true;
  } else {
    return false;
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
CommonRecoStepInfo *CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetNewStepInfo() {
  if (step_infos_count_ >= step_infos_.size()) {
    step_infos_.push_back(std::make_unique<CommonRecoStepInfo>());
  } else {
    step_infos_[step_infos_count_]->Clear();
  }
  return step_infos_[step_infos_count_++].get();
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
void CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::WaitForCallbacks(
    MutableContext *context, CommonProcessor *processor, Container *results, const std::string &trigger_alias,
    bool self_wait) {
  // NOTE(fangjianbing): 空字符串作为 key 的 callback 视为缺失 downstream 的情况, 将异步退化为同步处理
  std::pair<int, int64> pr =
      RecoUtil::WaitCallbacks(context, self_wait ? "" : processor->GetName(), trigger_alias);
  if (pr.first > 0) {
    CL_PERF_INTERVAL(pr.second, kPerfNs, "async_waiting_time", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), trigger_alias);
    CL_PERF_INTERVAL(pr.first, kPerfNs, "async_concurrency", GlobalHolder::GetServiceIdentifier(),
                     context->GetRequestType(), trigger_alias);

    std::unique_ptr<ks::infra::ktrace::ScopeKSpan> scope_guard;
    std::shared_ptr<ks::infra::ktrace::KSpan> kspan;
    if (ks::platform::KtraceUtil::IsKtraceEnabledForProcessor() &&
        ks::infra::ktrace::TracerManager::IsSampled() && IsKtraceEnabled(processor)) {
      scope_guard = std::make_unique<ks::infra::ktrace::ScopeKSpan>("PROCESSOR");
      kspan = scope_guard->Get();
      if (kspan) {
        kspan->SetComponentType("LOCAL");
        kspan->SetComponentName("ASYNC_WAIT@" + trigger_alias);
      }
      // todo(qingjun): 统计 WaitCallbacks 的时间, 之前版本会 core
    }
    if (context->GetNeedTableCpuCost()) {
      context->GetTableCpuCost().TimingStart(processor->GetTableName());
    }
    if (context->GetNeedStepInfo()) {
      // > v2.0.0 日志，会将数据聚焦在这个位置
      int64 start_ts = base::GetTimestamp();
      if ((context->GetShouldCompressItem() || context->GetShouldDropStudioField()) &&
          traceback_util::CompareTracebackDataVersion(context->GetTracebackDataVersion(), "v2.0.0") >= 0) {
        RecordStepInfo(context, processor, false, pr.second, results->size(), results, trigger_alias,
                       "callback");
      } else {
        // 为 debug request 记录 step info
        auto *info = GetNewStepInfo();
        info->set_processor_name(trigger_alias);
        info->set_type_name("callback");
        info->set_duration(pr.second);
        info->set_hide(true);
      }
      int64 step_info_duration = base::GetTimestamp() - start_ts;
      base::perfutil::PerfUtilWrapper::IntervalLogStash(step_info_duration, kPerfNs, "step_info_record_time",
                                                        GlobalHolder::GetServiceIdentifier(),
                                                        context->GetRequestType(), trigger_alias);
    }
    if (context->GetNeedTableCpuCost()) {
      context->GetTableCpuCost().TimingStep(processor->GetTableName());
    }
  }
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
const std::string &CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetName() const {
  return name_;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
const base::AutoDeleteHash<
    typename CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::CommonProcessor>
    &CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetProcessors() {
  return processors_;
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
const std::vector<
    typename CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::CommonProcessor *>
    &CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetProcessorRun() {
  return processor_run_;
}

// NOTE(fangjianbing): 该接口需要在外层配合 GetProcessorStepInfosCount() 使用以获取真实的 StepInfo 数目
template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
CommonRecoStepInfo *
CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetProcessorStepInfo(int index) {
  CHECK(index < step_infos_count_) << ", processor step info index out of range";
  return step_infos_[index].get();
}

template <typename MetaData, typename MutableContext, typename ImmutableContext, typename Container>
int CommonPipeline<MetaData, MutableContext, ImmutableContext, Container>::GetProcessorStepInfosCount() {
  return step_infos_count_;
}

template class CommonPipeline<RecoProcessorMetaData, AddibleRecoContextInterface,
                              ReadableRecoContextInterface, std::vector<CommonRecoResult>>;

}  // namespace platform
}  // namespace ks
