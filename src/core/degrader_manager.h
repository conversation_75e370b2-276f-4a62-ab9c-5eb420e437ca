#pragma once

#include <folly/concurrency/ConcurrentHashMap.h>
#include <kess/scheduler/adaptive_limiter.h>
#include <kess/scheduler/bulk_head.h>
#include <kess/scheduler/circuit_breaker.h>
#include <kess/scheduler/random_drop.h>
#include <algorithm>
#include <memory>
#include <string>

#include "base/thread/internal/synchronization/lock.h"
#include "dragon/src/core/common_reco_util.h"
#include "kconf/kconf.h"
#include "serving_base/jansson/json.h"
#include "serving_base/util/duplicate_hash.h"

DECLARE_string(degrade_config_kconf_key);
DECLARE_string(degrade_switch_kconf_key);
DECLARE_string(processor_breaker_kconf_key);

namespace ks {
namespace platform {

class KconfConfig {
 public:
  explicit KconfConfig(const std::string &kconf_key, const std::string &strategy_name = "") {
    kconf_key_ = kconf_key;
    strategy_name_ = strategy_name;
    degrader_config_ = ks::infra::KConf().Get(kconf_key, std::make_shared<::Json::Value>());
  }

 protected:
  bool GetBoolFromJson(const std::string &field, bool default_value = false) const {
    std::shared_ptr<::Json::Value> config = degrader_config_->Get();
    const auto &value_config = strategy_name_.empty() ? (*config)[field] : (*config)[strategy_name_][field];
    if (value_config.isBool()) {
      return value_config.asBool();
    }
    return default_value;
  }
  int64 GetIntFromJson(const std::string &field, int64 default_value = 0) const {
    std::shared_ptr<::Json::Value> config = degrader_config_->Get();
    const auto &value_config = strategy_name_.empty() ? (*config)[field] : (*config)[strategy_name_][field];
    if (value_config.isIntegral()) {
      return value_config.asInt64();
    }
    return default_value;
  }
  double GetDoubleFromJson(const std::string &field, double default_value = 0.0) const {
    std::shared_ptr<::Json::Value> config = degrader_config_->Get();
    const auto &value_config = strategy_name_.empty() ? (*config)[field] : (*config)[strategy_name_][field];
    if (value_config.isNumeric()) {
      return value_config.asDouble();
    }
    return default_value;
  }

  std::string GetStringFromJson(const std::string &field, const std::string &default_value = "") const {
    std::shared_ptr<::Json::Value> config = degrader_config_->Get();
    const auto &value_config = strategy_name_.empty() ? (*config)[field] : (*config)[strategy_name_][field];
    if (value_config.isString()) {
      return value_config.asString();
    }
    return default_value;
  }

  std::string strategy_name_;
  std::string kconf_key_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> degrader_config_;
};

class CircuitBreakerKconfConfig
    : public ks::kess::scheduler::CircuitBreaker::Config
    , public KconfConfig {
 public:
  explicit CircuitBreakerKconfConfig(const std::string &kconf_key, const std::string &strategy_name = "")
      : KconfConfig(kconf_key, strategy_name) {}
  // 是否启用熔断功能
  bool Enabled() override {
    return GetBoolFromJson("enabled", false);
  }
  // 触发熔断的请求失败率, 最高 100, 若 <=0 则只要有一个失败就熔断
  int FailureRateThreshold() override {
    return std::min<int>(GetIntFromJson("failureRateThreshold", 50), 100);
  }
  // 统计失败率的窗口大小
  int RequestWindowSize() override;
  // 熔断一旦触发多久后重试
  int DurationSecondsInOpen() override;
};

class BulkHeadKconfConfig
    : public ks::kess::scheduler::BulkHead::Config
    , public KconfConfig {
 public:
  explicit BulkHeadKconfConfig(const std::string &kconf_key, const std::string &strategy_name = "")
      : KconfConfig(kconf_key, strategy_name) {}
  bool Enabled() override {
    return GetBoolFromJson("enabled", false);
  }
  int MaxConcurrency() override;
};

class AdaptiveLimiterKconfConfig
    : public ks::kess::scheduler::AdaptiveLimiter::Config
    , public KconfConfig {
 public:
  explicit AdaptiveLimiterKconfConfig(const std::string &kconf_key, const std::string &strategy_name = "")
      : KconfConfig(kconf_key, strategy_name) {}
  bool Enabled() override {
    return GetBoolFromJson("enabled", false);
  }
  double Factor() override {
    return GetDoubleFromJson("factor", 2.0);
  }
  int RequestWindowSize() override;
};

class RandomDropKconfConfig
    : public ks::kess::scheduler::RandomDrop::Config
    , public KconfConfig {
 public:
  explicit RandomDropKconfConfig(const std::string &kconf_key, const std::string &strategy_name = "")
      : KconfConfig(kconf_key, strategy_name) {}
  bool Enabled() override {
    return GetBoolFromJson("enabled", false);
  }
  double Percentage() override {
    return GetDoubleFromJson("percentage", 0.0);
  }
};

using DegradeFunc = std::function<bool(ks::kess::scheduler::Degradable *)>;

class DegraderManager {
 public:
  static DegraderManager *Singleton() {
    static DegraderManager manager;
    return &manager;
  }

  bool InitDegraders(const base::Json *config);

  bool IsAllowedToRun(const std::string &key);

  void BeginCall(const std::string &key);

  void EndCall(const std::string &key, bool success);

  bool Contain(const std::string &key);

  ks::kess::scheduler::Degradable *Get(const std::string &key);

  void Insert(const std::string &key, ks::kess::scheduler::Degradable *degrader);

  ks::kess::scheduler::Degradable *CreateDegraderFromGlobalConfig(const std::string &strategy);

  static std::string GenDegraderKey(const std::string &request_type, const std::string &processor,
                                    const std::string &key = "", const std::string &prefix = "") {
    std::string res;
    if (!key.empty()) {
      absl::StrAppend(&res, prefix, key);
    } else {
      absl::StrAppend(&res, request_type, "$", processor);
    }
    return res;
  }

  bool IsEnabled() const {
    return degrade_switch_config_->Get();
  }

 protected:
  DegraderManager() : initialized_(false) {
    // 默认打开
    degrade_switch_config_ = ks::infra::KConf().Get(FLAGS_degrade_switch_kconf_key, true);

    global_degrader_config_ =
        ks::infra::KConf().Get(FLAGS_degrade_config_kconf_key, std::make_shared<::Json::Value>());
  }

  void Clear() {
    for (auto &pr : degrader_map_) {
      if (pr.second) {
        delete pr.second;
      }
    }
    degrader_map_.clear();
  }

  void RegisterDegrader(DegraderType type, const std::string &request_type, const std::string &processor,
                        const std::string &kconf_key, const std::string &strategy = "");

  bool DoWithDegraders(const std::string &key, DegradeFunc func);

 private:
  static ks::kess::scheduler::Degradable *CreateDegrader(DegraderType degrader_type,
                                                         const std::string &kconf_key,
                                                         const std::string &strategy);

 private:
  folly::ConcurrentHashMap<std::string, ks::kess::scheduler::Degradable *> degrader_map_;
  base::Lock lock_;
  std::atomic<bool> initialized_;
  std::shared_ptr<ks::infra::KsConfig<bool>> degrade_switch_config_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> global_degrader_config_;
};

class ProcessorCircuitBreaker {
 public:
  static bool ShouldBreak(const std::string &request_type, const std::string &processor_name);
};

}  // namespace platform
}  // namespace ks
