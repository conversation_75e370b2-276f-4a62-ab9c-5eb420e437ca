#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include "base/common/logging.h"
#include "base/common/sleep.h"
#include "base/random/pseudo_random.h"
#include "base/thread/blocking_queue2.h"
#include "base/thread/internal/synchronization/lock.h"
#include "base/thread/thread_pool.h"
#include "base/time/timestamp.h"
#include "dragon/src/module/traceback_util.h"
#include "dragon/src/util/common_util.h"
#include "dynamic_kafka_client/dynamic_kafka_client.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"

DECLARE_int32(traceback_async_queue_max_size);
DECLARE_int32(traceback_send_thread_num);
DECLARE_string(traceback_config_kconf_key);
DECLARE_bool(enable_async_traceback);

namespace ks {
namespace platform {
class TracebackManager {
 public:
  static TracebackManager *Singleton() {
    static TracebackManager manager;
    return &manager;
  }

  TracebackManager()
      : running_(true)
      , traceback_req_queue_(FLAGS_traceback_async_queue_max_size)
      , traceback_thread_pool_(FLAGS_traceback_send_thread_num) {
    traceback_config_ =
        ks::infra::KConf().Get(FLAGS_traceback_config_kconf_key, std::make_shared<::Json::Value>());
    auto trace_config = traceback_config_->Get();
    if (trace_config->isObject() && (*trace_config)["kafka_topic"].isString()) {
      std::string trace_topic = (*trace_config)["kafka_topic"].asString();
      if (!trace_topic.empty()) {
        kafka_producer_ = ks::infra::kfk::DynamicKafkaClient::GetProducerByLogicalTopicId(trace_topic);
        if (!kafka_producer_) {
          CL_LOG_ERROR("traceback", "kafka_client_fail:" + trace_topic)
              << "failed to get kafka producer client for topic: " << trace_topic;
        }
      }
    }
  }

  void Run() {
    traceback_thread_pool_.AddTask(NewCallback(this, &TracebackManager::RunBatchQuery));
  }

  void Stop() {
    running_.exchange(false);
  }

  void WaitStopped() {
    traceback_thread_pool_.StopAll();
  }

  void RunBatchQuery() {
    while (running_) {
      int64 queue_size = traceback_req_queue_.Size();
      if (queue_size > 0) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(queue_size, kPerfNs, "traceback_queue_size",
                                                          GlobalHolder::GetServiceIdentifier());
      }
      ks::platform::CommonRecoTraceback *rsp_traceback;
      int ret = traceback_req_queue_.TryTake(&rsp_traceback);
      if (ret == 1) {
        SendTracebackKafka(rsp_traceback);
      } else {
        base::SleepForMilliseconds(10);
      }
    }
  }

  void PutRequestQueue(ks::platform::CommonRecoTraceback *rsp_traceback) {
    // 尝试添加元素 |e| 到队列尾部
    // 如果队列满, 马上返回
    // 返回 0, 成功 put
    // 返回 1, 队列满
    // 返回 -1, 队列已经关闭
    int ret = traceback_req_queue_.TryPut(rsp_traceback);
    if (ret == 1) {
      CL_LOG_WARNING("traceback", "traceback_queue_put_fail: queue is full.")
          << "failed to put async traceback queue. max size exceeded. max size is" +
                 std::to_string(FLAGS_traceback_async_queue_max_size);
    } else if (ret == -1) {
      CL_LOG_WARNING("traceback", "traceback_queue_put_fail: queue is closed")
          << "queue is closed. failed to put async traceback queue.";
    }
  }

  void CopyTracebackBaseInfo(ks::platform::CommonRecoTraceback *to, ks::platform::CommonRecoTraceback *from) {
    to->set_biz(from->biz());
    to->set_data_version(from->data_version());
    to->set_data_source(from->data_source());
    to->set_user_id(from->user_id());
    to->set_device_id(from->device_id());
    to->set_timestamp_ms(from->timestamp_ms());
    to->set_request_id(from->request_id());
    to->set_request_num(from->request_num());
    to->set_request_type(from->request_type());
    to->set_traceback_type(from->traceback_type());
    to->set_allocated_request(from->release_request());
    to->set_time_cost_us(from->time_cost_us());
    to->set_allocated_compact_response(from->release_compact_response());
  }

  void SendTracebackKafka(ks::platform::CommonRecoTraceback *rsp_traceback) {
    int64 start = base::GetTimestamp();
    std::string req_type = rsp_traceback->request().request_type();
    ks::platform::CommonRecoTraceback traceback;
    CopyTracebackBaseInfo(&traceback, rsp_traceback);

    // 由于 kafka 单条消息大小的限制，这里把 step_info 拆分成大小合适的 patch。
    // patch_end 记录每个 patch 在 step_info 的终止下标。
    std::vector<int> patch_end = traceback_util::SliceTracebackData(*rsp_traceback, traceback.ByteSize());
    traceback.set_seq_num(patch_end.size());
    int patch_begin = 0;
    for (int i = 0; i < patch_end.size(); i++) {
      traceback.set_seq_id(i + 1);
      while (patch_begin < patch_end[i] && patch_begin < rsp_traceback->step_info_size()) {
        traceback.mutable_step_info()->AddAllocated(rsp_traceback->mutable_step_info(patch_begin));
        patch_begin++;
      }

      std::string payload;
      traceback.SerializeToString(&payload);
      RdKafka::ErrorCode err = kafka_producer_->Produce(payload);
      if (err != RdKafka::ERR_NO_ERROR) {
        std::string err_str = RdKafka::err2str(err);
        ks::infra::PerfUtil::CountLogStash(1, kPerfNs, "error", GlobalHolder::GetServiceIdentifier(),
                                           req_type, "send_fail:" + err_str, "traceback");
        std::string processors;
        for (int i = 0; i < traceback.step_info_size(); i++) {
          processors += traceback.step_info(i).processor_name();
          processors += " ";
          if (i > 3) {
            if (i == traceback.step_info_size() - 1) {
              processors += "...";
            }
            break;
          }
        }
        LOG_EVERY_N(ERROR, 100) << "failed to send traceback log to kafka, error: " << err_str
                                << ", seq_id: " << traceback.seq_id()
                                << ", req_id: " << traceback.request_id() << ", uid: " << traceback.user_id()
                                << ", did: " << traceback.device_id() << ", payload size: " << payload.size()
                                << ", processors: " << processors;
      }

      // request/response 数据只对第一个 patch 数据记录即可
      traceback.clear_request();
      traceback.clear_compact_response();
      while (traceback.step_info_size() > 0) {
        traceback.mutable_step_info()->ReleaseLast();
      }
    }
    delete rsp_traceback;
    int64 duration = base::GetTimestamp() - start;
    base::perfutil::PerfUtilWrapper::IntervalLogStash(duration, kPerfNs, "async_traceback_send_time",
                                                      GlobalHolder::GetServiceIdentifier(), req_type);
  }

 private:
  // trace_log
  std::shared_ptr<ks::infra::kfk::Producer> kafka_producer_;
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> traceback_config_;
  std::atomic<bool> running_;
  thread::ThreadPool traceback_thread_pool_;
  thread::BlockingQueue2<ks::platform::CommonRecoTraceback *> traceback_req_queue_;
};
}  // namespace platform
}  // namespace ks
