#pragma once

#include <algorithm>
#include <functional>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <utility>
#include <vector>

#include "base/common/basic_types.h"
#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/module/async_task_thread_pool.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/cpuinfo_util.h"
#include "ks/numa_aware/numa_util.h"

DECLARE_int64(sub_flow_task_queue_max_size);
DECLARE_bool(set_pthread_affinity_by_cpu_core);
DECLARE_bool(enable_pthread_affinity);
DECLARE_bool(enable_numa_aware);
DECLARE_bool(use_exclusive_arena_for_worker);
DECLARE_int64(sub_flow_thread_num);
DECLARE_double(sub_flow_thread_num_per_worker);
DECLARE_double(sub_flow_thread_num_upscale_ratio);
DECLARE_int64(sub_flow_task_queue_num);
DECLARE_int64(sub_flow_async_thread_num);
DECLARE_double(sub_flow_async_thread_num_ratio);

namespace ks {
namespace platform {

class ThreadPoolSet {
 public:
  explicit ThreadPoolSet(int64 numa_id, int thread_num, std::function<void(int)> after_start = nullptr)
      : numa_id_(numa_id), thread_num_(thread_num), after_start_(after_start) {
    max_thread_num_ =
        std::max(static_cast<int>(thread_num * FLAGS_sub_flow_thread_num_upscale_ratio), thread_num);
    Resize(FLAGS_sub_flow_task_queue_num);
  }

  void Resize(int size) {
    if (size < 0) {
      return;
    }
    std::unique_lock<std::shared_timed_mutex> lock(lock_);
    if (size <= thread_pools_.size()) {
      return;
    }
    int start = thread_pools_.size();
    thread_pools_.resize(size);
    for (int i = start; i < thread_pools_.size(); i++) {
      std::string thread_pool_name = absl::StrCat("SubFlowTaskPool-", i);
      thread_pools_[i] = CreatThreadPool(thread_pool_name, thread_num_, max_thread_num_,
                                         FLAGS_sub_flow_task_queue_max_size, after_start_);
      LOG(INFO) << "Sub Flow Thread Pool " << thread_pool_name << " Created numa_id: " << numa_id_
                << " min_thread_num: " << thread_num_ << " max_thread_num: " << max_thread_num_
                << " queue_size: " << FLAGS_sub_flow_task_queue_max_size;
    }
    return;
  }

  static std::unique_ptr<AsyncTaskThreadPool<CommonRecoContext *>> CreatThreadPool(
      const std::string &thread_pool_name, int64 min_thread_num, int64 max_thread_num, int64 queue_size,
      std::function<void(int)> after_start) {
    auto ret = std::make_unique<AsyncTaskThreadPool<CommonRecoContext *>>(
        thread_pool_name, min_thread_num, max_thread_num, queue_size, after_start);
    ret->WaitForInitDone();
    ret->SetKtraceSpanName("SUB_FLOW");
    return ret;
  }

  int GetThreadPoolNum() {
    std::shared_lock<std::shared_timed_mutex> lock(lock_);
    return thread_pools_.size();
  }

  AsyncTaskThreadPool<CommonRecoContext *> *GetThreadPool(int index) {
    if (index < 0) {
      return nullptr;
    }
    std::shared_lock<std::shared_timed_mutex> lock(lock_);
    if (index >= thread_pools_.size()) {
      return nullptr;
    }
    return thread_pools_[index].get();
  }

 private:
  int64 thread_num_ = 0;
  int64 max_thread_num_ = 0;
  int64 numa_id_ = 0;
  std::function<void(int)> after_start_ = nullptr;
  std::vector<std::unique_ptr<AsyncTaskThreadPool<CommonRecoContext *>>> thread_pools_;
  std::shared_timed_mutex lock_;
};

class SubFlowThreadPoolManager {
 public:
  struct ThreadPoolParam {
    int numa_node_id = -1;
    int thread_num = 0;
    std::function<void(int)> after_start = nullptr;
    ThreadPoolParam(int numa_node_id, int thread_num, std::function<void(int)> after_start = nullptr)
        : numa_node_id(numa_node_id), thread_num(thread_num), after_start(std::move(after_start)) {}
  };

  static SubFlowThreadPoolManager *GetInstance() {
    static SubFlowThreadPoolManager m_instance;
    return &m_instance;
  }

  void Initialize() {
    int main_worker_num = GlobalHolder::GetWorkerThreadNum();

    int total_sub_flow_thread_num = 0;
    if (FLAGS_sub_flow_thread_num_per_worker > 0) {
      total_sub_flow_thread_num = main_worker_num * FLAGS_sub_flow_thread_num_per_worker;
    } else {
      total_sub_flow_thread_num =
          main_worker_num * FLAGS_sub_flow_async_thread_num * FLAGS_sub_flow_async_thread_num_ratio;
    }

    // 此处强制设置线程数不少于 main worker 工作线程数。
    total_sub_flow_thread_num = std::max(main_worker_num, total_sub_flow_thread_num);
    // 至少保证线程池内有 16 个线程
    total_sub_flow_thread_num = std::max(total_sub_flow_thread_num, 16);

    // FLAGS_sub_flow_thread_num 视为强制设定，不再受最低线程数保护。
    if (FLAGS_sub_flow_thread_num > 0) {
      total_sub_flow_thread_num = FLAGS_sub_flow_thread_num;
    }

    // Initialize 参数为右值， param_vec 只能使用一次。
    std::vector<ThreadPoolParam> param_vec;

    if (FLAGS_enable_pthread_affinity && !FLAGS_set_pthread_affinity_by_cpu_core) {
      CpuInfoUtil *cpu_info = CpuInfoUtil::GetInstance();
      std::vector<CpuAffinityInfo> masks = cpu_info->GetCpuSetVectorByNuma();
      param_vec.reserve(masks.size());
      for (auto mask : masks) {
        int thread_num_per_numa = total_sub_flow_thread_num / masks.size() + 1;
        param_vec.emplace_back(mask.numa_node_id, thread_num_per_numa, [mask](int thread_index) {
          WarmupGlogAndPerflog();
          mask.BindCpuAffinity("[sub_flow] ");
        });
      }
    } else if (FLAGS_enable_numa_aware && ks::numa_aware::NumaUtil::GetBindList().first) {
      const auto &bind_list = ks::numa_aware::NumaUtil::GetBindList().second;
      for (auto numa_id : bind_list) {
        int thread_num_per_numa = total_sub_flow_thread_num / bind_list.size() + 1;
        param_vec.emplace_back(numa_id, thread_num_per_numa, [numa_id](int thread_index) {
          WarmupGlogAndPerflog();
          int error_code = ks::numa_aware::NumaUtil::BindToNumaNodes({numa_id});
          if (error_code == 0) {
            LOG(INFO) << "sub flow worker tid: " << std::this_thread::get_id()
                      << " success bind to numa: " << numa_id;
          } else {
            LOG(ERROR) << "sub flow worker tid: " << std::this_thread::get_id()
                       << " failed bind to numa: " << numa_id << " error_code: " << error_code;
          }
        });
      }
    } else {
      param_vec.reserve(1);
      param_vec.emplace_back(0, total_sub_flow_thread_num, [](int thread_index) { WarmupGlogAndPerflog(); });
    }

    size_t pools_num = param_vec.size();
    SetupThreadPools(std::move(param_vec));
    LOG(INFO) << "init " << pools_num << " thread pools for sub_flow with " << total_sub_flow_thread_num
              << " threads in total";
  }

  ThreadPoolSet *GetThreadPoolSet(int numa_node_id) {
    if (thread_pools_.empty()) {
      return nullptr;
    }

    // 兼容全局唯一池子的情况。
    if (numa_node_id < 0) {
      return thread_pools_[0].get();
    }

    if (numa_node_id >= thread_pools_.size()) {
      return nullptr;
    }
    return thread_pools_[numa_node_id].get();
  }

  void Resize(int64 index) {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto &thread_pool_set : thread_pools_) {
      if (!thread_pool_set) continue;
      thread_pool_set->Resize(index);
    }
  }

 private:
  SubFlowThreadPoolManager() = default;

  bool CreateThreadPool(int numa_node_id, int thread_num, std::function<void(int)> after_start = nullptr) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (numa_node_id >= thread_pools_.size()) {
      return false;
    }
    if (thread_pools_[numa_node_id]) {
      return true;
    }

    thread_pools_[numa_node_id] =
        std::make_unique<ThreadPoolSet>(numa_node_id, thread_num, std::move(after_start));

    return true;
  }

  void SetupThreadPools(std::vector<ThreadPoolParam> &&thread_pool_params) {
    int max_numa_node_id = -1;
    for (auto &param : thread_pool_params) {
      max_numa_node_id = std::max(max_numa_node_id, param.numa_node_id);
    }
    thread_pools_.resize(max_numa_node_id + 1);
    for (auto &param : thread_pool_params) {
      CreateThreadPool(param.numa_node_id, param.thread_num, std::move(param.after_start));
    }
  }

 private:
  std::vector<std::unique_ptr<ThreadPoolSet>> thread_pools_;
  std::mutex mutex_;
  DISALLOW_COPY_AND_ASSIGN(SubFlowThreadPoolManager);
};
}  // namespace platform
}  // namespace ks
