//
// Created by wa<PERSON><PERSON><PERSON> on 2019/4/2.
// Copied from <kess/rpc/batch_waiter.h> by fan<PERSON><PERSON><PERSON><PERSON> on 2021/11/2
//

#pragma once

#include <kenv/context.h>
#include <kess/common/blocking_queue.h>
#include <kess/rpc/future.h>

#include <functional>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/time/timestamp.h"
#include "dragon/src/module/future_action_wrapper.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/logging_util.h"
#include "dragon/src/util/perf_report_util.h"

namespace ks {
namespace platform {

// Note: CommonRecoKessBatchWaiter is not thread safe.
class CommonRecoKessBatchWaiter {
 public:
  explicit CommonRecoKessBatchWaiter(const std::string &name)
      : name_(name)
      , remains_(0)
      , queue_(std::make_shared<ks::kess::common::BlockingQueue<std::function<void()>>>()) {}

  CommonRecoKessBatchWaiter(const CommonRecoKessBatchWaiter &) = delete;
  CommonRecoKessBatchWaiter &operator=(const CommonRecoKessBatchWaiter &) = delete;

  template <
      typename T, typename Action,
      typename std::enable_if<ks::kess::rpc::internal::is_invocable<Action, const T &>::value, int>::type = 0>
  void Add(ks::kess::rpc::Future<T> future, Action &&action, const std::string &processor_name) {
    int64 start_ts = base::GetTimestamp();
    // Convert lambda to std::function to be fixed size, this may fix struct check error
    std::function<void(const T &)> callback = std::forward<Action>(action);
    std::string request_type = GlobalHolder::GetCurrentRequestType();
    future.Submit([queue = queue_, start_ts, processor_name, request_type = std::move(request_type),
                   waiter_name = Name(), callback = std::move(callback)](const T &result) mutable {
      int64 async_ready = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(async_ready, kPerfNs, "processor_async_ready", GlobalHolder::GetServiceIdentifier(),
                       request_type, processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
      queue->Push([callback = std::move(callback), start_ts, request_type = std::move(request_type),
                   processor_name = std::move(processor_name), waiter_name = std::move(waiter_name), result,
                   context = ks::infra::kenv::Context::Current()->Copy()]() mutable {
        context->Run([callback = std::move(callback), result]() { callback(result); });
        int64 async_time = base::GetTimestamp() - start_ts;
        CL_PERF_INTERVAL(
            async_time, kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(), request_type,
            processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
      });
    });
    future_closures_.emplace_back([future = std::move(future)]() {});
    remains_++;
  }

  template <typename T, typename Action,
            typename std::enable_if<!ks::kess::rpc::internal::is_invocable<Action, const T &>::value,
                                    int>::type = 0>
  void Add(ks::kess::rpc::Future<T> future, Action &&action, const std::string &processor_name) {
    Add(std::move(future),
        ks::kess::rpc::internal::TupleAction<typename std::remove_reference<Action>::type>{
            std::forward<Action>(action)},
        processor_name);
  }

  // template <typename T, typename Action,
  // typename std::enable_if<!std::is_invocable<Action, const T &>::value, int>::type = 0>
  template <typename T, typename Action>
  void Add(CommonRecoFutureActionWrapper<T> future, Action &&action, const std::string &processor_name) {
    int64 start_ts = base::GetTimestamp();
    std::function<void(const T &)> callback = std::forward<Action>(action);
    std::string request_type = GlobalHolder::GetCurrentRequestType();
    queue_->Push([future = std::move(future), callback = std::move(callback), start_ts,
                  request_type = std::move(request_type), processor_name = std::move(processor_name),
                  waiter_name = Name()]() mutable {
      T result = future.Get();
      callback(result);
      int64 async_time = base::GetTimestamp() - start_ts;
      CL_PERF_INTERVAL(
          async_time, kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(), request_type,
          processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
    });
    future_closures_.emplace_back([]() {});
    remains_++;
  }

  /**
   * Wait all finish
   */
  void Wait() {
    while (remains_ > 0) {
      queue_->Pop()();  // Pop and call
      remains_--;
    }
    future_closures_.clear();
  }

  void Clear() {
    remains_ = 0;
    queue_ = std::make_shared<ks::kess::common::BlockingQueue<std::function<void()>>>();
    future_closures_.clear();
  }

  const std::string &Name() const {
    return name_;
  }

  size_t Remains() const {
    return remains_;
  }

  size_t Size() const {
    return future_closures_.size();
  }

 private:
  std::string name_;
  int remains_;
  std::shared_ptr<ks::kess::common::BlockingQueue<std::function<void()>>> queue_;
  std::vector<std::function<void()>> future_closures_;
};

}  // namespace platform
}  // namespace ks
