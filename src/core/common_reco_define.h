#pragma once

#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "absl/strings/string_view.h"
#include "base/common/basic_types.h"
#include "folly/container/F14Map.h"

namespace ks {
namespace platform {

static constexpr char kTracebackAttrNameSeparator[] = "|";
static constexpr char kMetaData[] = "$metadata";
static constexpr char kModifyItemTables[] = "$modify_item_tables";
static constexpr char kInputItemAttrs[] = "$input_item_attrs";
static constexpr char kOutputItemAttrs[] = "$output_item_attrs";
static constexpr char kInputCommonAttrs[] = "$input_common_attrs";
static constexpr char kOutputCommonAttrs[] = "$output_common_attrs";
static constexpr char kDownstreamItemAttrs[] = "$downstream_item_attrs";
static constexpr char kDownstreamNewResult[] = "$downstream_new_result";
static constexpr char kEvalCommonAttrs[] = "$eval_common_attrs";

using StringViewSet = std::set<absl::string_view>;

struct RecoProcessorMetaData {
  std::unique_ptr<StringViewSet> modify_item_tables = nullptr;
  std::unique_ptr<StringViewSet> input_item_attrs = nullptr;
  std::unique_ptr<StringViewSet> output_item_attrs = nullptr;
  std::unique_ptr<StringViewSet> input_common_attrs = nullptr;
  std::unique_ptr<StringViewSet> output_common_attrs = nullptr;
  std::unique_ptr<std::set<std::string>> downstream_item_attrs = nullptr;
  std::unique_ptr<std::vector<std::string>> eval_common_attrs = nullptr;
  std::unique_ptr<std::unordered_map<std::string, std::string>> span_tags = nullptr;
  bool downstream_new_result = false;
  std::string json_config_string;
  std::string processor_name;
  std::string processor_type_name;

  // XXX(caohongjin) 由于做属性依赖检测时 `StringViewSet = std::set<std::string>` 无法同时处理 std::string 和
  // absl::string_view 并且 third_party/absl 尚不支持 btree_set，这里使用 vector 管理所有属性的生命周期，这样
  // StringViewSet 配置成 std::set<std::string_view> 即可。待 third_party/absl 支持 btree_set 后，将
  // StringViewSet 切换成 absl::btree_set<std::string> 并删除 attr_names 成员即可
  std::vector<std::unique_ptr<std::string>> attr_names;
};

struct float16_t;

void Float2Half(const float *floats, size_t size, float16_t *halfs);

void Double2Half(const double *doubles, size_t size, float16_t *halfs);

void Half2Float(const float16_t *halfs, size_t size, float *floats);

struct float16_t {
  int16 value;

  float16_t() {}

  explicit float16_t(const float value) {
    Float2Half(&value, 1, this);
  }

  explicit float16_t(const double value) {
    Double2Half(&value, 1, this);
  }

  operator float() const {
    float a;
    Half2Float(this, 1, &a);
    return a;
  }

  operator double() const {
    float a;
    Half2Float(this, 1, &a);
    return static_cast<double>(a);
  }
} __attribute__((aligned(2)));

enum class RpcType {
  UNKNOWN = -1,
  GRPC,
  BRPC,
  KRPC,
};

// 字段拷贝模式
enum class CopyMode {
  UNKNOWN = -1,

  // 覆盖
  OVERWRITE,
  // concat
  CONCAT,
  // 取最大值
  MAX,
  // 取最小值
  MIN,
  // 累加和
  SUM,
};

static const folly::F14FastMap<std::string, CopyMode> kCopyModeMap = {{"OVERWRITE", CopyMode::OVERWRITE},
                                                                      {"COPY", CopyMode::OVERWRITE},
                                                                      {"CONCAT", CopyMode::CONCAT},
                                                                      {"MAX", CopyMode::MAX},
                                                                      {"MIN", CopyMode::MIN},
                                                                      {"SUM", CopyMode::SUM}};

CopyMode TransCopyModeFromString(const std::string &copy_mode);

// dragonfly 底层 attr 支持的类型
// 无后缀的数值类型默认为 64 位的实现，如 INT(int64)，FLOAT(double)
enum class AttrType : int {
  UNKNOWN = -1,
  INT,
  FLOAT,
  STRING,
  INT_LIST,
  FLOAT_LIST,
  STRING_LIST,
  EXTRA,
  INT32,
  INT32_LIST,
  INT16,
  INT16_LIST,
  INT8,
  INT8_LIST,
  FLOAT32,
  FLOAT32_LIST,
  FLOAT16,
  FLOAT16_LIST,
};

// processor 执行结果
enum class RunStatus : int {
  SUCCESS = 0,
  FAILED,
};

size_t GetSingleValueAttrTypeSize(AttrType type);

template <typename T>
AttrType GetAttrType() {
  return AttrType::UNKNOWN;
}

template <>
inline AttrType GetAttrType<int64>() {
  return AttrType::INT;
}

template <>
inline AttrType GetAttrType<double>() {
  return AttrType::FLOAT;
}

template <>
inline AttrType GetAttrType<int32>() {
  return AttrType::INT32;
}

template <>
inline AttrType GetAttrType<int16>() {
  return AttrType::INT16;
}

template <>
inline AttrType GetAttrType<int8>() {
  return AttrType::INT8;
}

template <>
inline AttrType GetAttrType<float>() {
  return AttrType::FLOAT32;
}

template <>
inline AttrType GetAttrType<float16_t>() {
  return AttrType::FLOAT16;
}

template <typename T>
AttrType GetListAttrType() {
  return AttrType::UNKNOWN;
}

template <>
inline AttrType GetListAttrType<int64>() {
  return AttrType::INT_LIST;
}

template <>
inline AttrType GetListAttrType<double>() {
  return AttrType::FLOAT_LIST;
}

template <>
inline AttrType GetListAttrType<int32>() {
  return AttrType::INT32_LIST;
}

template <>
inline AttrType GetListAttrType<int16>() {
  return AttrType::INT16_LIST;
}

template <>
inline AttrType GetListAttrType<int8>() {
  return AttrType::INT8_LIST;
}

template <>
inline AttrType GetListAttrType<float>() {
  return AttrType::FLOAT32_LIST;
}

template <>
inline AttrType GetListAttrType<float16_t>() {
  return AttrType::FLOAT16_LIST;
}

inline bool IsListAttrType(AttrType type) {
  switch (type) {
    case AttrType::INT:
    case AttrType::FLOAT:
    case AttrType::STRING:
    case AttrType::INT32:
    case AttrType::INT16:
    case AttrType::INT8:
    case AttrType::FLOAT32:
    case AttrType::FLOAT16:
    case AttrType::EXTRA:
      return false;
    case AttrType::INT_LIST:
    case AttrType::FLOAT_LIST:
    case AttrType::STRING_LIST:
    case AttrType::INT32_LIST:
    case AttrType::INT16_LIST:
    case AttrType::INT8_LIST:
    case AttrType::FLOAT32_LIST:
    case AttrType::FLOAT16_LIST:
      return true;
    default:
      return false;
  }
}

inline AttrType ConvertToListType(AttrType type) {
  switch (type) {
    case AttrType::INT:
      return AttrType::INT_LIST;
    case AttrType::FLOAT:
      return AttrType::FLOAT_LIST;
    case AttrType::STRING:
      return AttrType::STRING_LIST;
    case AttrType::INT32:
      return AttrType::INT32_LIST;
    case AttrType::INT16:
      return AttrType::INT16_LIST;
    case AttrType::INT8:
      return AttrType::INT8_LIST;
    case AttrType::FLOAT32:
      return AttrType::FLOAT32_LIST;
    case AttrType::FLOAT16:
      return AttrType::FLOAT16_LIST;
    case AttrType::EXTRA:
    case AttrType::INT_LIST:
    case AttrType::FLOAT_LIST:
    case AttrType::STRING_LIST:
    case AttrType::INT32_LIST:
    case AttrType::INT16_LIST:
    case AttrType::INT8_LIST:
    case AttrType::FLOAT32_LIST:
    case AttrType::FLOAT16_LIST:
    default:
      return AttrType::UNKNOWN;
  }
}

}  // namespace platform
}  // namespace ks
