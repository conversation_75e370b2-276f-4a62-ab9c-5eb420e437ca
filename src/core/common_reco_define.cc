#include "dragon/src/core/common_reco_define.h"

#include <x86intrin.h>
#include <immintrin.h>
#include <f16cintrin.h>


namespace ks {
namespace platform {

size_t GetSingleValueAttrTypeSize(AttrType type) {
  switch (type) {
    case AttrType::INT:
      return sizeof(int64);
    case AttrType::FLOAT:
      return sizeof(double);
    case AttrType::INT32:
      return sizeof(int32);
    case AttrType::INT16:
      return sizeof(int16);
    case AttrType::INT8:
      return sizeof(int8);
    case AttrType::FLOAT32:
      return sizeof(float);
    case AttrType::FLOAT16:
      return sizeof(float16_t);
    default:
      return 0;
  }
}

CopyMode TransCopyModeFromString(const std::string &copy_mode) {
  auto it = kCopyModeMap.find(copy_mode);
  if (it != kCopyModeMap.end()) {
    return it->second;
  } else {
    return CopyMode::UNKNOWN;
  }
}

void Float2Half(const float *floats, size_t size, float16_t *halfs) {
  size_t k = 0, vsize = size - size % 8;
  for (; k < vsize; k += 8) {
    __m256 float_vector = _mm256_loadu_ps(floats + k);       // NOLINT
    __m128i half_vector = _mm256_cvtps_ph(float_vector, 0);  // NOLINT
    _mm_storeu_si128((__m128i *)(halfs + k), half_vector);   // NOLINT
  }
  for (; k < size; ++k) {
    halfs[k].value = _cvtss_sh(floats[k], 0);  // NOLINT
  }
}

void Double2Half(const double *doubles, size_t size, float16_t *halfs) {
  size_t k = 0, vsize = size - size % 4;
  for (; k < vsize; k += 4) {
    __m256d double_vector = _mm256_loadu_pd(doubles + k);   // NOLINT
    __m128 single_vector = _mm256_cvtpd_ps(double_vector);  // NOLINT
    __m128i half_vector = _mm_cvtps_ph(single_vector, 0);   // NOLINT
    //  提取低 64 位
    int64_t half_data = _mm_extract_epi64(half_vector, 0);  // NOLINT
    *reinterpret_cast<int64_t *>(halfs + k) = half_data;    // NOLINT
  }
  for (; k < size; ++k) {
    float tmp = doubles[k];
    halfs[k].value = _cvtss_sh(tmp, 0);  // NOLINT
  }
}

void Half2Float(const float16_t *halfs, size_t size, float *floats) {
  size_t k = 0, vsize = size - size % 8;
  for (; k < vsize; k += 8) {
    __m256 vector = _mm256_cvtph_ps(_mm_loadu_si128((__m128i *)(halfs + k)));  // NOLINT
    _mm256_storeu_ps(floats + k, vector);                                          // NOLINT
  }
  for (; k < size; ++k) {
    floats[k] = _cvtsh_ss(halfs[k].value);  // NOLINT
  }
}

}  // namespace platform
}  // namespace ks
