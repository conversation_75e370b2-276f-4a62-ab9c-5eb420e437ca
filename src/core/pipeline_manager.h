#pragma once

#include <sstream>
#include <string>
#include <unordered_set>
#include <vector>

#include "dragon/src/util/global_thread_pool.h"
#include "folly/container/F14Map.h"
#include "serving_base/jansson/json.h"
#include "serving_base/util/duplicate_hash.h"

DECLARE_int32(parallel_init_pipeline_concurrency);

namespace ks {
namespace platform {

template <typename PipelineType>
class PipelineManager {
 public:
  PipelineManager() {}
  virtual ~PipelineManager() {}

  bool Initialize(const base::Json *config,
                  const std::unordered_set<std::string> *target_pipelines = nullptr) {
    if (!config) {
      LOG(ERROR) << "PipelineManager init error: get null json config";
      return false;
    }

    const auto *pipeline_map = config->Get("pipeline_map");
    if (!pipeline_map) {
      LOG(ERROR) << "PipelineManager init error: get null pipeline_map config";
      return false;
    }

    const auto *base_pipeline = config->Get("base_pipeline");
    if (!base_pipeline) {
      LOG(ERROR) << "PipelineManager init error: get null base_pipeline config";
      return false;
    }

    const auto *processor_config = base_pipeline->Get("processor");
    if (!processor_config) {
      LOG(ERROR) << "PipelineManager init error: get null processor config in base_pipeline";
      return false;
    }

    const auto *combo_processor_config = base_pipeline->Get("combo_processor");

    if (FLAGS_parallel_init_pipeline_concurrency > 0) {
      VLOG(100) << "pipeline size: " << pipeline_map->objects().size();
      int32 concurrency = FLAGS_parallel_init_pipeline_concurrency;
      auto it = pipeline_map->object_begin();
      while (it != pipeline_map->object_end()) {
        std::vector<std::future<int>> futures;
        folly::F14FastMap<std::string, PipelineType *> local_pipeline_map;
        for (int i = 0; it != pipeline_map->object_end() && i < concurrency; ++i, ++it) {
          if (target_pipelines && !target_pipelines->empty() &&
              target_pipelines->find(it->first) == target_pipelines->end()) {
            continue;
          }
          const auto &name = it->first;
          const auto *pipeline_config = it->second;
          VLOG(100) << "pipeline config for " << name << ":\n" << pipeline_config->ToString(2);
          auto *pipeline = new PipelineType(name);
          local_pipeline_map[name] = pipeline;
          futures.emplace_back(DualLevelThreadPool::GetInstance()->AsyncRunForNonLeafNode(
              [pipeline, &name, pipeline_config, processor_config, combo_processor_config]() {
                if (!pipeline->Initialize(pipeline_config, processor_config, combo_processor_config)) {
                  LOG(ERROR) << "new Pipeline Error, pipeline name: " << name;
                  return -1;
                }
                VLOG(100) << "new Pipeline Ok, pipeline name: " << name;
                return 0;
              }));
        }
        for (auto &fut : futures) {
          int code = fut.get();
          if (code != 0) {
            LOG(ERROR) << "new Pipeline Error, code: " << code << ". please check previous ERROR log";
            return false;
          }
        }
        if (VLOG_IS_ON(100)) {
          std::ostringstream oss;
          for (auto &pr : local_pipeline_map) {
            oss << pr.first << ",";
          }
          LOG(INFO) << "parallel init pipeline, concurrency: " << concurrency
                    << " size: " << local_pipeline_map.size() << " pipeline: " << oss.str();
        }
        for (auto &pr : local_pipeline_map) {
          pipeline_map_.Insert(pr.first, pr.second);
        }
      }
      VLOG(100) << "pipeline_map size: " << pipeline_map_.size();
      return true;
    }

    for (const auto &pr : pipeline_map->objects()) {
      if (target_pipelines && !target_pipelines->empty() &&
          target_pipelines->find(pr.first) == target_pipelines->end()) {
        continue;
      }
      const auto &name = pr.first;
      const auto *pipeline_config = pr.second;
      VLOG(100) << "pipeline config for " << name << ":\n" << pipeline_config->ToString(2);

      auto *pipeline = new PipelineType(name);
      if (!pipeline->Initialize(pipeline_config, processor_config, combo_processor_config)) {
        LOG(ERROR) << "new Pipeline Error, pipeline name: " << name;
        return false;
      }

      pipeline_map_.Insert(name, pipeline);
    }

    if (pipeline_map_.size() == 0) {
      LOG(ERROR) << "PipelineManager init error: empty pipeline map.";
      return false;
    }

    return true;
  }

  PipelineType *GetPipeline(const std::string &name) {
    return pipeline_map_.Get(name);
  }

  auto begin() const {
    return pipeline_map_.begin();
  }
  auto begin() {
    return pipeline_map_.begin();
  }
  auto end() const {
    return pipeline_map_.end();
  }
  auto end() {
    return pipeline_map_.end();
  }

 private:
  base::AutoDeleteHash<PipelineType> pipeline_map_;

 private:
  DISALLOW_COPY_AND_ASSIGN(PipelineManager);
};

}  // namespace platform
}  // namespace ks
