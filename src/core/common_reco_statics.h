#pragma once

namespace ks {
namespace platform {

static constexpr char kReasonKey[] = "reason";

// 框架层保留使用的 common_attr 名称
static constexpr char kUserIdAttr[] = "_USER_ID_";
static constexpr char kDeviceIdAttr[] = "_DEVICE_ID_";
static constexpr char kRequestIdAttr[] = "_REQ_ID_";
static constexpr char kRequestTypeAttr[] = "_REQ_TYPE_";
static constexpr char kRequestTimeAttr[] = "_REQ_TIME_";
static constexpr char kRequestNumAttr[] = "_REQ_NUM_";
static constexpr char kBrowseSetSizeAttr[] = "_BROWSE_SET_SIZE_";
static constexpr char kPerfLogExtraA[] = "_PERFLOG_CUSTOM_EXTRA_A_";
static constexpr char kPerfLogExtraB[] = "_PERFLOG_CUSTOM_EXTRA_B_";
static constexpr char kPerfLogExtraC[] = "_PERFLOG_CUSTOM_EXTRA_C_";
static constexpr char kSimulationRequest[] = "_IS_SIMULATION_REQUEST_";
static constexpr char kSimulationUserId[] = "_SIMULATION_FROM_USER_ID_";
static constexpr char kSimulationDeviceId[] = "_SIMULATION_FROM_DEVICE_ID_";

// 框架层保留使用的 item_attr 名称
static constexpr char kReasonAttr[] = "_REASON_";
static constexpr char kScoreAttr[] = "_SCORE_";
static constexpr char kReasonListAttr[] = "_REASON_LIST_";

// model retrieve 指标相关的属性名
static constexpr char kModelRetrieveLeftAttrKey[] = "$model_retrieval_left_attr";
static constexpr char kModelRetrieveRightAttrKey[] = "$model_retrieval_right_attr";
static constexpr char kModelRetrieveRightTermBenefitKey[] = "$model_retrieval_right_term_benefit";

// infer server 的 pxtr 值存储及返回格式
static constexpr char kInferOutputType[] = "$infer_output_type";

static constexpr char kKessConfigKey[] = "kess_config";

// abtest 用户标签属性名
static constexpr char kAbtestUserTagNames[] = "_ABTEST_USER_TAG_NAMES_";
static constexpr char kAbtestUserTagValues[] = "_ABTEST_USER_TAG_VALUES_";

}  // namespace platform
}  // namespace ks
