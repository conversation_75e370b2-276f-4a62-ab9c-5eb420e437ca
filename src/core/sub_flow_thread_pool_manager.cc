
#include "dragon/src/core/sub_flow_thread_pool_manager.h"

DEFINE_int64(sub_flow_task_queue_num, 1, "multiple sub flow thread pools size");
DEFINE_int64(sub_flow_task_queue_max_size, 10000, "sub flow thread pool task queue len");
DEFINE_int64(sub_flow_thread_num, 0, "thread size limit for sub flow");
DEFINE_double(sub_flow_thread_num_per_worker, 0, "sub flow thread num per main worker");
DEFINE_double(sub_flow_thread_num_upscale_ratio, 1.0, "sub flow thread num upscale ratio");
// 废弃中，请尽量使用 sub_flow_thread_num && sub_flow_thread_num_upscale_ratio 调整子线程池线程数量
DEFINE_int64(sub_flow_async_thread_num, 4, "thread size limit for sub flow processor");
// 废弃中，请尽量使用 sub_flow_thread_num && sub_flow_thread_num_upscale_ratio 调整子线程池线程数量
DEFINE_double(sub_flow_async_thread_num_ratio, 0.3,
              "the ratio for convert sub_flow_async_thread_num into real sub flow thread pool size");

namespace ks {
namespace platform {}  // namespace platform
}  // namespace ks
