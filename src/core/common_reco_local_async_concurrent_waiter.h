#pragma once

#include <chrono>
#include <functional>
#include <future>
#include <memory>
#include <queue>
#include <string>
#include <utility>

#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/logging_util.h"
#include "third_party/tbb/include/tbb/concurrent_priority_queue.h"

namespace ks {
namespace platform {
class CommonRecoLocalAsyncConcurrentWaiter {
 public:
  explicit CommonRecoLocalAsyncConcurrentWaiter(const std::string &name) : name_(name) {}
  CommonRecoLocalAsyncConcurrentWaiter(const CommonRecoLocalAsyncConcurrentWaiter &) = delete;
  CommonRecoLocalAsyncConcurrentWaiter &operator=(const CommonRecoLocalAsyncConcurrentWaiter &) = delete;

  template <typename T, typename Callback>
  void Add(ReadableRecoContextInterface *context, std::future<T> &&future, Callback &&callback,
           const std::string &processor_name, int priority, int64 timeout_ms = 0,
           std::function<void()> timeout_cb = nullptr) {
    std::function<void(T)> cb = std::forward<Callback>(callback);
    // 闭包绑定，背景 : future 不能拷贝 + c++11 闭包不支持 std::move
    auto future_ptr = std::make_shared<std::future<T>>(std::move(future));
    auto start_ts = std::chrono::system_clock::now();
    auto action = [context, future_ptr = std::move(future_ptr), cb = std::move(cb), processor_name,
                   waiter_name = Name(), start_ts = std::move(start_ts), timeout_ms,
                   timeout_cb = std::move(timeout_cb)]() {
      if (!future_ptr->valid()) {
        CL_LOG_ERROR("local_async", "invalid_future: " + processor_name)
            << "add invalid future! processor: " << processor_name;
        return;
      }
      cb(std::move(future_ptr->get()));

      auto async_time =
          std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now() - start_ts);
      CL_PERF_INTERVAL(
          async_time.count(), kPerfNs, "processor_async_time", GlobalHolder::GetServiceIdentifier(),
          context->GetRequestType(), processor_name, waiter_name, "", GlobalHolder::GetJsonConfigVersion());
    };
    queue_.emplace(priority, queue_.size(), std::move(action));
  }

  void Wait() {
    WaitTask t;
    while (queue_.try_pop(t)) {
      if (t.is_valid()) {
        t();
      }
    }
  }

  void Clear() {
    WaitTask t;
    while (queue_.try_pop(t)) {
    }
  }

  const std::string &Name() const {
    return name_;
  }

  size_t Size() const {
    return queue_.size();
  }

 private:
  class WaitTask {
   public:
    WaitTask() = default;
    WaitTask(int priority, int sequence, std::function<void()> &&task)
        : priority_(priority), sequence_(sequence), task_(task) {}

    WaitTask(const WaitTask &t) = default;

    WaitTask(WaitTask &&t) {
      priority_ = t.priority_;
      sequence_ = t.sequence_;
      task_ = std::move(t.task_);
    }

    WaitTask &operator=(const WaitTask &t) = default;

    WaitTask &operator=(WaitTask &&t) {
      priority_ = t.priority_;
      sequence_ = t.sequence_;
      task_ = std::move(t.task_);
      return *this;
    }

    bool operator<(const WaitTask &t) const {
      if (priority_ == t.priority_) return sequence_ > t.sequence_;
      return priority_ < t.priority_;
    }

    void operator()() const {
      task_();
    }

    bool is_valid() const {
      return task_ != nullptr;
    }

   private:
    int priority_ = 0;
    int sequence_ = 0;
    std::function<void()> task_ = nullptr;
  };

 private:
  std::string name_;
  tbb::concurrent_priority_queue<WaitTask> queue_;
};
}  // namespace platform
}  // namespace ks
