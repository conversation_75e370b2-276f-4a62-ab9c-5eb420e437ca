#pragma once

#include <memory>
#include <string>

#include "dragon/src/core/common_reco_context.h"
#include "dragon/src/core/common_reco_pipeline.h"
#include "dragon/src/core/sub_flow_thread_pool_manager.h"
#include "ks/numa_aware/numa_util.h"
#include "serving_base/util/future_pool.h"

DECLARE_bool(enable_numa_aware);

namespace ks {
namespace platform {

// 封装 context 和单条 pipeline，避免初始化所有的 processor
class SinglePipelineExecutor {
 public:
  SinglePipelineExecutor() {}

  bool Initialize(const base::Json *config, const std::string &pipeline_name) {
    if (!config) {
      LOG(ERROR) << "SinglePipelineExecutor init error: no config.";
      return false;
    }

    const auto *pipeline_map = config->Get("pipeline_map");
    if (!pipeline_map) {
      LOG(ERROR) << "SinglePipelineExecutor init error: no pipeline_map in config.";
      return false;
    }

    const auto *pipeline_config = pipeline_map->Get(pipeline_name);
    if (!pipeline_config) {
      LOG(ERROR) << "SinglePipelineExecutor init error: pipeline " << pipeline_name
                 << " not exist in pipeline map.";
      return false;
    }

    const auto *base_pipeline = config->Get("base_pipeline");
    if (!base_pipeline) {
      LOG(ERROR) << "SinglePipelineExecutor init error: get null base_pipeline config";
      return false;
    }

    const auto *processor_config = base_pipeline->Get("processor");
    if (!processor_config) {
      LOG(ERROR) << "SinglePipelineExecutor init error: get null processor config in base_pipeline";
      return false;
    }

    const auto *combo_processor_config = base_pipeline->Get("combo_processor");

    pipeline_ = std::make_unique<CommonRecoPipeline>(pipeline_name);
    if (!pipeline_->Initialize(pipeline_config, processor_config, combo_processor_config)) {
      LOG(ERROR) << "SinglePipelineExecutor init error: new pipeline " << pipeline_name << " failed";
      return false;
    }
    processor_num_ = pipeline_->GetProcessorRun().size();
    context_.SetupAttrTypesFromConfig(GlobalHolder::GetDynamicJsonConfig().get());
    return true;
  }

  void Reset(ReadableRecoContextInterface *context, const bool fill_common_attrs_from_request,
             const bool fill_browse_set_from_request,
             const folly::F14FastSet<std::string> *exclude_common_attrs_from_request = nullptr,
             const std::string &table_name = "", bool direct_access_parent_flow_data = false,
             bool shadow_copy_fixed_attrs = true) {
    CommonRecoContext *common_reco_context = static_cast<CommonRecoContext *>(context);
    auto request = common_reco_context->GetRequest();
    context_.ClearContext();
    // NOTE(caohongjin): mix_by_sub_flow 开启 direct_access_parent_flow_data 无需设置 running_processor_table_
    // 如果这里设置了 running_processor_table_ ，那么后面 CloneContext 中 ShadowCloneItemAttrTable 会引用失败
    if (!direct_access_parent_flow_data) {
      context_.SetMainTable(table_name);
    }
    // reset request after clear
    context_.SetRequest(request);
    context_.CopyMetaInfo(common_reco_context);

    if (fill_common_attrs_from_request && request) {
      for (const auto &attr : request->common_attr()) {
        if (exclude_common_attrs_from_request && exclude_common_attrs_from_request->size() > 0 &&
            exclude_common_attrs_from_request->count(attr.name()) > 0) {
          continue;
        }
        interop::SaveSampleAttrToCommonAttr(&context_, attr);
        context_.AddCommonAttrFromRequest(attr.name());
      }
    }
    if (fill_browse_set_from_request) {
      // TODO(huzengyi): 旧的 browse set 后续也应该支持浅拷贝的方式
      for (const auto &item : request->browse_set_package().item()) {
        context_.AddItemToBrowseSet(Util::GenKeysign(item.item_type(), item.item_id()));
      }
      if (request->has_browse_set() && !context_.ShadowCopyRecoBrowseSet(common_reco_context)) {
        context_.SetupRecoBrowseSet(request->browse_set());
      }
    }
    context_.SetSharedRequest(common_reco_context->GetSharedRequest());
    if (context->GetLocalAsyncTimeoutWaiter() != nullptr) {
      context_.SetLocalAsyncTimeoutWaiter(context->GetLocalAsyncTimeoutWaiter());
    }
    if (context->GetDetachedBatchWaiter() != nullptr) {
      context_.SetDetachedBatchWaiter(context->GetDetachedBatchWaiter());
    }
    context_.SetNeedPipelineCpuCost(context->GetNeedPipelineCpuCost());
    context_.SetNeedTableCpuCost(context->GetNeedTableCpuCost());
    context_.CloneFixedCommonAttr(common_reco_context, shadow_copy_fixed_attrs);
  }

  void Clear() {
    context_.ClearContext();
  }

  void Cancel() {
    context_.SetExecutionStatus(ExecutionStatus::CANCELLED);
  }

  void Run() {
    // 在非主线程环境下重置 thread_local 变量值
    RecoUtil::ResetThreadLocals(&context_);
    pipeline_->Execute(&context_, context_.GetRecoResults());
  }

  int GetProcessorNum() {
    return processor_num_;
  }

  CommonRecoContext *GetContext() {
    return &context_;
  }

  int GetProcessorStepInfosCount() {
    return pipeline_->GetProcessorStepInfosCount();
  }

  CommonRecoStepInfo *GetProcessorStepInfo(int index) {
    return pipeline_->GetProcessorStepInfo(index);
  }

  AsyncTaskThreadPool<CommonRecoContext *> *GetAsyncPool(int index = 0) {
    auto thread_pool_set = SubFlowThreadPoolManager::GetInstance()->GetThreadPoolSet(
        FLAGS_enable_numa_aware ? context_.GetNumaId() : context_.GetCpuAffinityInfo()->numa_node_id);
    if (thread_pool_set == nullptr) {
      return nullptr;
    }
    return thread_pool_set->GetThreadPool(index);
  }

  void HandlePipelineExit() {
    pipeline_->PipelineExit(&context_);
  }

 private:
  std::unique_ptr<base::Json> pipeline_manager_config_holder_;
  std::unique_ptr<CommonRecoPipeline> pipeline_;
  CommonRecoContext context_;
  int processor_num_;
};

}  // namespace platform
}  // namespace ks
