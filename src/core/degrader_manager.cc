#include "dragon/src/core/degrader_manager.h"

#include <unordered_map>
#include <vector>

#include "dragon/src/util/common_util.h"
#include "dragon/src/util/random.h"
#include "serving_base/perfutil/perfutil_wrapper.h"

DEFINE_string(degrade_config_kconf_key, "", "kconf path for degrader config");
DEFINE_string(degrade_switch_kconf_key, "", "kconf path to enable all degrade or not");
DEFINE_string(processor_breaker_kconf_key, "", "kconf key for processor circuitbreak map");

namespace ks {
namespace platform {

int CircuitBreakerKconfConfig::RequestWindowSize() {
  int size = GetIntFromJson("requestWindowSize", 0);
  if (size <= 0) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Invalid requestWindowSize value: " << size << ", should be > 0!"
                                << " kconf key: " << kconf_key_ << " strategy name: " << strategy_name_;
  }
  return size;
}

int CircuitBreakerKconfConfig::DurationSecondsInOpen() {
  int duration = GetIntFromJson("durationSecondsInOpen", 0);
  if (duration <= 0) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Invalid durationSecondsInOpen value: " << duration << ", should be > 0!"
                                << " kconf key: " << kconf_key_ << " strategy name: " << strategy_name_;
  }
  return duration;
}

int BulkHeadKconfConfig::MaxConcurrency() {
  int max_concurrency = GetIntFromJson("maxConcurrency", 0);
  if (max_concurrency < 0) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Invalid maxConcurrency value: " << max_concurrency << ", should be >= 0!"
                                << " kconf key: " << kconf_key_ << " strategy name: " << strategy_name_;
  }
  return max_concurrency;
}

int AdaptiveLimiterKconfConfig::RequestWindowSize() {
  int window_size = GetIntFromJson("requestWindowSize", 0);
  if (window_size <= 0) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Invalid requestWindowSize value: " << window_size << ", should be > 0!"
                                << " kconf key: " << kconf_key_ << " strategy name: " << strategy_name_;
  }
  return window_size;
}

bool DegraderManager::Contain(const std::string &key) {
  return degrader_map_.find(key) != degrader_map_.end();
}

ks::kess::scheduler::Degradable *DegraderManager::Get(const std::string &key) {
  auto it = degrader_map_.find(key);
  if (it == degrader_map_.end()) {
    return nullptr;
  }
  return it->second;
}

void DegraderManager::Insert(const std::string &key, ks::kess::scheduler::Degradable *degrader) {
  degrader_map_.insert(key, degrader);
}

bool DegraderManager::InitDegraders(const base::Json *degrade_config) {
  base::AutoLock lock(lock_);

  if (initialized_) return true;

  Clear();

  if (!FLAGS_degrade_config_kconf_key.empty()) {
    auto degrade_config = global_degrader_config_->Get();
    for (const std::string &strategy : degrade_config->getMemberNames()) {
      const auto &degrader_config = (*degrade_config)[strategy]["degrader"];
      std::string degrader_type_name = degrader_config.isString() ? degrader_config.asString() : "";
      DegraderType degrader_type = RecoUtil::ParseDegraderType(degrader_type_name);
      if (degrader_type == DegraderType::UNKNOWN) {
        LOG(ERROR) << "unknown degrader type: " << degrader_type_name;
        return false;
      }
      RegisterDegrader(degrader_type, "", "", FLAGS_degrade_config_kconf_key, strategy);
    }
    initialized_ = true;
  }

  if (!degrade_config) {
    initialized_ = true;
    return true;
  }

  for (auto *config : degrade_config->array()) {
    DegraderType degrader_type = RecoUtil::ParseDegraderType(config->GetString("degrader_type"));
    if (degrader_type == DegraderType::UNKNOWN) {
      LOG(ERROR) << "unknown degrader type: " << config->GetString("degrader_type");
      return false;
    }

    std::string kconf_key = config->GetString("kconf_key");
    auto *processor_config = config->Get("processor");
    if (!processor_config) {
      LOG(ERROR) << "degrade config missing 'processor' field";
      return false;
    }

    std::vector<std::string> processor_list;
    if (processor_config->IsString()) {
      processor_list.push_back(processor_config->StringValue());
    } else if (processor_config->IsArray()) {
      for (auto *proc : processor_config->array()) {
        if (proc->IsString()) {
          processor_list.push_back(proc->StringValue());
        }
      }
    }

    for (const auto &processor : processor_list) {
      auto *request_type_config = config->Get("request_type");
      if (request_type_config) {
        if (request_type_config->IsString()) {
          RegisterDegrader(degrader_type, request_type_config->StringValue(), processor, kconf_key);
        } else if (request_type_config->IsArray()) {
          for (auto *request_type : request_type_config->array()) {
            if (request_type->IsString()) {
              RegisterDegrader(degrader_type, request_type->StringValue(), processor, kconf_key);
            }
          }
        }
      } else {
        RegisterDegrader(degrader_type, "", processor, kconf_key);
      }
    }
  }

  initialized_ = true;
  return true;
}

void DegraderManager::RegisterDegrader(DegraderType degrader_type, const std::string &request_type,
                                       const std::string &processor, const std::string &kconf_key,
                                       const std::string &strategy) {
  std::string key = GenDegraderKey(request_type, processor, strategy);
  if (degrader_map_.find(key) == degrader_map_.end()) {
    ks::kess::scheduler::Degradable *degrader = CreateDegrader(degrader_type, kconf_key, strategy);
    if (degrader) {
      degrader_map_.insert(key, degrader);
      LOG(INFO) << "degrader registered, request_type: " << request_type << ", processor: " << processor
                << ", kconf_key: " << kconf_key << ", strategy: " << strategy
                << ", degrader num: " << degrader_map_.size();
    }
  }
}

ks::kess::scheduler::Degradable *DegraderManager::CreateDegraderFromGlobalConfig(
    const std::string &strategy) {
  auto degrade_config = global_degrader_config_->Get();

  const auto &degrader_config = (*degrade_config)[strategy]["degrader"];
  std::string degrader_type_name = degrader_config.isString() ? degrader_config.asString() : "";
  DegraderType degrader_type = RecoUtil::ParseDegraderType(degrader_type_name);
  if (degrader_type == DegraderType::UNKNOWN) {
    CL_LOG(ERROR) << "unknown degrader type: " << degrader_type_name;
    return nullptr;
  }
  return CreateDegrader(degrader_type, FLAGS_degrade_config_kconf_key, strategy);
}

ks::kess::scheduler::Degradable *DegraderManager::CreateDegrader(DegraderType degrader_type,
                                                                 const std::string &kconf_key,
                                                                 const std::string &strategy) {
  ks::kess::scheduler::Degradable *degrader = nullptr;
  if (degrader_type == DegraderType::CIRCUIT_BREAKER) {
    auto config = std::make_shared<CircuitBreakerKconfConfig>(kconf_key, strategy);
    degrader = new ks::kess::scheduler::CircuitBreaker(config);
  } else if (degrader_type == DegraderType::BULK_HEAD) {
    auto config = std::make_shared<BulkHeadKconfConfig>(kconf_key, strategy);
    degrader = new ks::kess::scheduler::BulkHead(config);
  } else if (degrader_type == DegraderType::ADAPTIVE_LIMITER) {
    auto config = std::make_shared<AdaptiveLimiterKconfConfig>(kconf_key, strategy);
    degrader = new ks::kess::scheduler::AdaptiveLimiter(config);
  } else if (degrader_type == DegraderType::RANDOM_DROP) {
    auto config = std::make_shared<RandomDropKconfConfig>(kconf_key, strategy);
    degrader = new ks::kess::scheduler::RandomDrop(config);
  }
  return degrader;
}

bool DegraderManager::DoWithDegraders(const std::string &key, DegradeFunc func) {
  ks::kess::scheduler::Degradable *degrader = degrader_map_[key];
  if (degrader && !func(degrader)) {
    return false;
  }
  return true;
}

bool DegraderManager::IsAllowedToRun(const std::string &key) {
  return DoWithDegraders(
      key, [this](ks::kess::scheduler::Degradable *degrader) -> bool { return degrader->AllowRequest(); });
}

void DegraderManager::BeginCall(const std::string &key) {
  DoWithDegraders(key, [](ks::kess::scheduler::Degradable *degrader) -> bool {
    degrader->BeginCall();
    return true;
  });
}

void DegraderManager::EndCall(const std::string &key, bool success) {
  DoWithDegraders(key, [success](ks::kess::scheduler::Degradable *degrader) -> bool {
    degrader->EndCall(success);
    return true;
  });
}

bool ProcessorCircuitBreaker::ShouldBreak(const std::string &request_type,
                                          const std::string &processor_name) {
  if (!FLAGS_processor_breaker_kconf_key.empty()) {
    static auto processor_breaker_map_kconf = ks::infra::KConf().GetUnorderedMap<std::string, double>(
        FLAGS_processor_breaker_kconf_key, std::make_shared<std::unordered_map<std::string, double>>(),
        [](const std::string &value, std::string *key) {
          *key = value;
          return true;
        });

    auto processor_breaker_map = processor_breaker_map_kconf->Get();
    if (processor_breaker_map && !processor_breaker_map->empty()) {
      auto it = processor_breaker_map->find(processor_name);
      if (it != processor_breaker_map->end() && ks::platform::uniform_real<double>() < it->second) {
        base::perfutil::PerfUtilWrapper::CountLogStash(
            kPerfNs, "processor_breaker", GlobalHolder::GetServiceIdentifier(), request_type, processor_name);
        return true;
      }
    }
  }

  return false;
}

}  // namespace platform
}  // namespace ks
