from gevent import monkey
monkey.patch_all()

from gevent import pywsgi
import sys
import logging.handlers
import logging

LOG_FILE = 'main.log'
logging.basicConfig(
    filename=LOG_FILE,
    filemode='w',
    format='[%(levelname)s] %(asctime)s [%(filename)s:%(lineno)d, %(funcName)s] %(message)s',
    level=logging.INFO)

from flask_compress import Compress
from flask import Flask, send_file, send_from_directory
from flask_cas import CAS 
from flask_cas import login_required
from infra.perflog import create_perf_context

app = Flask(__name__)
Compress(app)

cas = CAS()
cas.init_app(app, url_prefix='/')

app.url_map.strict_slashes = False

app.secret_key = 'GANDEPIAOLIANG'
app.config['CAS_SERVER'] = 'https://sso.corp.kuaishou.com'
app.config['CAS_AFTER_LOGIN'] = ''

@app.route('/', methods=['GET'])
@login_required
def index():
  request_user = cas.username
  log_ctx = create_perf_context(namespace='dragonfly_tools', subtag='visit_wiki', extra1=request_user, biz_def='reco')
  log_ctx.logstash(count=1)    
  return send_file('index.html')

@app.route('/image/<filename>', methods=['GET'])
def image(filename):
  return send_from_directory('image', filename)

@app.route('/js/<filename>', methods=['GET'])
def js(filename):
  return send_from_directory('js', filename)

@app.route('/css/<filename>', methods=['GET'])
def css(filename):
  return send_from_directory('css', filename)

@app.route('/wiki/<filename>', methods=['GET'])
@login_required
def wiki(filename):
  return send_from_directory('wiki', filename)

@app.route('/api/<filename>', methods=['GET'])
@login_required
def api(filename):
  return send_from_directory('api', filename)

@app.route('/<filename>', methods=['GET'])
@login_required
def docs_files(filename):
  return send_file(filename)

# use gevent's pywsgi.WSGIServer to enable non-blocking request
port = int(sys.argv[1])
server = pywsgi.WSGIServer(('0.0.0.0', 3000), app)
server.serve_forever()