from datetime import datetime
from elasticsearch import Elasticsearch
import requests
import time
import re
from infra.perflog import create_perf_context
from kconf.get_config import get_json_config

def create_index(es):
  global ES_INDEX_NAME
  index_settings = {
    "settings": {
      "analysis" : {
        "analyzer" : {
        "ik" : {"tokenizer" : "ik_max_word"}
        }
      }
    },
    "mappings" : {
      "properties" : {
        "id" : {"type": "keyword"},
        "title" : {"type": "text", "analyzer": "ik_max_word"},
        "content": {"type": "text", "analyzer": "ik_max_word"},
        "tagList": {"type": "text", "analyzer": "ik_max_word"},
        "answerCount":{"type": "integer"},
        "followCount":{"type": "integer"},
        "viewCount":{"type": "integer"},
        "answerList": {
          "properties":{
            "username":{"type": "text"},
            "content":{"type": "text", "analyzer": "ik_max_word"},
          }
        },
        "birthTime" : {"type": "keyword"},
        "url": {"type": "keyword"}
      }
    }
  }
  if es.indices.exists(index = ES_INDEX_NAME):
    res = es.indices.delete(index = ES_INDEX_NAME)
    #print('delete',res)
  res = es.indices.create(index = ES_INDEX_NAME, body = index_settings)
  #print('create',res)

ES_INDEX_NAME = 'kstackdragonfly'
es = Elasticsearch('localhost:9200')
#create_index(es)

url = 'https://is-gateway.corp.kuaishou.com/token/get'
params = get_json_config('reco.debug.dragonfly_open_api')
response = requests.get(url = url, params=params)
access_token = response.json()['result']['accessToken']

headers={'Authorization': 'Bearer '+ access_token}
url = 'https://is-gateway.corp.kuaishou.com/tech/api/groups/questions'
params = {'groupId': 92,  'pageNum': 1, 'pageSize': 1}
response = requests.get(url=url, params=params, headers=headers)
question_num = response.json()['result']['total']

params = {'groupId': 92,  'pageNum': 1, 'pageSize': question_num}
response = requests.get(url=url, params=params, headers=headers)
questions = response.json()['result']['list']


for question in questions:
  time.sleep(0.1)
  url_answer = 'https://is-gateway.corp.kuaishou.com/tech/api/answer/list'
  params_answer = {'questionId': question['id'],  'pageNum': 1, 'pageSize': 10}
  response_answer = requests.get(url=url_answer, params=params_answer, headers=headers)
  tag_list =[]
  for tag in question['tagList']:
    tag_list.append(tag['name'])
  answers = response_answer.json()['result']['list']
  answer_list = []
  for answer in answers:
    answer_dict = {}
    answer_dict['username'] = answer['user']['username']
    answer_dict['content'] = re.sub('<[^>]+?>', '', answer['content'])
    answer_list.append(answer_dict)

  url_kstack = 'https://kstack.corp.kuaishou.com/tech/web/ask/info/' + str(question['id'])
  birth_time = datetime.now()
  birth_time = birth_time.strftime('%a, %b %d %H:%M')
  data = {
    'id': question['id'],
    'title': re.sub('<[^>]+?>', '', question['title']),
    'content': re.sub('<[^>]+?>', '', question['content']),
    'answerCount': question['answerCount'],
    'followCount': question['followCount'],
    'viewCount': question['viewCount'],

    'tagList': tag_list,
    'answerList': answer_list,
    'birthTime': birth_time,
    'url': url_kstack
  }
  try:
    res = es.index(index = ES_INDEX_NAME, id = question['id'], body = data)
    log_ctx = create_perf_context(namespace='dragonfly_tools', subtag='get_kstack', extra1=res['result'], biz_def='reco')
    log_ctx.logstash(count=1)
  except Exception as err:
    log_ctx = create_perf_context(namespace='dragonfly_tools', subtag='error',extra1='save_kstack_to_es', extra2=str(err), biz_def='reco')
    log_ctx.logstash(count=1)
'''
dsl = {
  "query" : {
    "multi_match" : {
        "query" : "kgnn初始化报错",
        "fields": ["title", "content", "tagList", "answerList.content"],
        "analyzer": "ik_smart"
    }
  }, 
  "highlight": {
      "pre_tags": [
          "<font color=#42b983>"
      ],
      "post_tags": [
          "</font>"
      ],
      "fields" : {
          "title" : {},
          "content": {},
          "answerList.content" : {}
      }
  }
}
res = es.search(index=ES_INDEX_NAME, body= dsl)
print(res)
'''
