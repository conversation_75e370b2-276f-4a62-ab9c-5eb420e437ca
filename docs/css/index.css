.markdown-section pre > code {
  padding: 6px 0px;
}

.markdown-section figure,
.markdown-section p {
  margin: 0.8em 0 0 0;
}

.markdown-section .api-section h1 {
  margin: 25px 0 0.8rem -30px;
}

.markdown-section .api-section h2 {
  font-size: 1.4rem;
  margin: 25px 0 0.8rem -30px;
  border-bottom: 1px solid darkgrey;
  border-left: 6px solid #42b983;
  padding: 0 0 4px 6px;
}

.markdown-section .api-section .edit-button-section p,
.markdown-section .edit-button-section p {
  right: 16px;
  text-align: right;
  position: relative;
}

.markdown-section h2 {
  font-size: 1.4rem;
  margin: 25px 0 0.8rem 0;
}

.markdown-section h3 {
  font-size: 1.1rem;
  margin: 40px 0 0.6rem 0px;
}

.markdown-section h5 {
  font-size: 1.1rem;
  margin: 40px 0 0.6rem 0px;
  border-bottom: 1px solid darkgrey;
}

.topic h2, .updates h2 {
  margin: 40px 0 0.6rem 0px;
  border-bottom: 1px solid darkgrey;
}

.markdown-section h4 {
  font-size: 1rem;
}

img.cpp-logo {
  position: absolute;
  margin: 0.2rem;
}

span.cpp-text {
  margin-left: 28px;
}

img.py-logo {
  position: absolute;
  margin: 0.2rem;
  margin-top: 0.33rem;
}
span.py-text {
  margin-left: 25px;
}

.sidebar {
  border-right: none;
  transition: unset;
}

img.search-logo {
  margin: -0.1rem;
  margin-right: 0.3rem;
}

p.question-focus {
  margin-top: -10px;
}

.new-sidebar-toggle {
  background-color: transparent;
  background-color: hsla(0, 0%, 100%, 0.8);
  border: 0;
  outline: none;
  padding: 10px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  transition: opacity 0.3s;
  width: 35px;
  z-index: 30;
  cursor: pointer;
}

.new-sidebar-toggle:hover .sidebar-toggle-button {
  opacity: 0.4;
}

.new-sidebar-toggle span {
  background-color: var(--theme-color, #42b983);
  display: block;
  margin-bottom: 4px;
  width: 16px;
  height: 2px;
}

body.sticky .sidebar,
body.sticky .new-sidebar-toggle {
  position: fixed;
}

.matching-post {
  word-break: break-all;
}

p.answer-tag {
  background-color: hsla(220, 4%, 58%, 0.1);
  display: inline-block;
  padding: 0px 4px;
  height: 20px;
  line-height: 18px;
  font-size: 11px;
  color: #909399;
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid hsla(220, 4%, 58%, 0.2);
  white-space: nowrap;
  width: 34px;
  margin-bottom: -10px;
}

#banner {
  background: #f19b55;
  color: white;
  font-weight: bold;
  position: fixed;
  top: 0;
  height: 36px;
  text-align: center;
  width: 100%;
  padding-top: 7px;
  z-index: 999;
  margin: 0;
}

#banner a {
  color: white;
}

#banner a:hover {
  transition: color 0.3s ease-in-out;
  color: blue;
}

.content {
  padding-top: 45px;
}

.search {
  border-bottom: none !important;
  margin-bottom: 0px !important;
  padding: 0px 6px !important;
}

.search-input-wrap {
  position: fixed;
  top: 0px;
  width: 277px;
  background: white;
  padding-top: 10px;
}

.el-input__inner {
  background: #f8f8f8;
  border: 0px #eee solid !important;
  border-radius: 20px;
  outline: none !important;
}

.el-input-wrap {
  background: #f8f8f8;
  border: 1px #eee solid !important;
  border-radius: 20px;
  outline: none !important;
  line-height: 36px !important;
  font-size: 14px !important;
  z-index: 999;
}

.results-panel {
  padding-top: 50px;
}

.sidebar-nav {
  padding-top: 50px;
}

.matching-post h2 {
  padding-left: 2px;
}

.underline-text {
  border-bottom: 1px dashed #34495e;
}

.keyword_link {
  font-size: 12px;
  color: grey;
  float: right;
  text-decoration: underline;
}

.item_info {
  margin-top: -8px;
}

.drawer_header {
  color: gray;
  font-size: 13px;
  position: relative;
  top: 5px;
  margin-bottom: 15px;
}

.drawer_creator {
  margin-left: 1px;
  position: relative;
  top: 114px;
}

.text_creator {
  color: Gray;
  top: 108px;
  position: relative;
  padding: 0 4px;
  font-size: 13px;
  width: 50px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.drawer_run_times {
  position: relative;
  top: 118px;
}

.text_run_times {
  display: inline;
  color: Gray;
  font-size: 13px;
  position: relative;
  top: 112px;
}

.drawer_run_icon {
  position: relative;
  top: -53px;
  right: 23px;
  margin-right: 0px;
  float: right;
  border-radius: 3px;
}

.drawer_run_icon > :hover {
  color: white !important;
  background-color: #11a15f !important;
}

.like_count {
  font-size: 18px;
  position: relative;
  top: 8px;
}

.drawer_like {
  position: relative;
  top: 15px;
}

.drawer_dislike {
  position: relative;
  top: 8px;
}

.drawer_like:hover {
  cursor: pointer;
}

.drawer_dislike:hover {
  cursor: pointer;
}

.el-aside {
  text-align: center;
  height: 300px;
  background-color: #efefef;
}

.el-main {
  padding: 0px;
}

.drawer_pre {
  margin: 0px 0px;
}

.matching-post h2 {
  font-size: 17px;
  margin: 10px 0;
}
.matching-post a {
  color: inherit;
  line-height: 1rem;
}
.sidebar-nav .matching-post {
  border-bottom: 1px solid #eee;
}

.matching-post p {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.sidebar-nav .search-keyword {
  color: #42b983;
  font-style:normal
}

.sidebar-nav a:hover {
  color: #42b983 !important;
  text-decoration: none !important;
}

.matching-results {
  margin-top: 26px;
}