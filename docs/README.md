# 欢迎来到 Dragonfly 暨 Common Leaf 服务文档

<img width="150" src="image/dragon-icon.svg" alt="logo of dragon">

## 什么是 Dragonfly?
Dragonfly 是针对搜广推场景的一整套图引擎框架及相关工具生态，利用简洁易用的 DSL 提升策略迭代效率。

一个用 dragonfly 编写的简易推荐流程（访问双列线上召回和预估服务）：[Playground 示例](https://dragonfly.corp.kuaishou.com/playground#id=11220654856363071933)

更多概念性介绍见 kstack 文章：
[Dragonfly: 快手通用策略 DSL 框架](https://kstack.corp.kuaishou.com/tech/web/article/info/1151)

## Dragonfly 能带来什么好处?
- 极简的配置内容
- 更高的配置可读性
- 更少的犯错机会（基本没有需要人工对齐 key/name 的地方）
- 屏蔽同步和异步 processor 的使用差别
- 更灵活的分支逻辑控制（`if_` / `else_` 和 `switch_` / `case_` 原语）
- 配置合法性检查
- 支持自定义模板方法，以提供更高阶的抽象接口

## 新手入门

- [2023 新人培训视频](https://school.corp.kuaishou.com/school/web/play/KC20234635#section=13492)
- [Dragonfly 入门教程](https://docs.corp.kuaishou.com/d/home/<USER>
- [Dragonfly 代码开发流程](https://docs.corp.kuaishou.com/d/home/<USER>

## 问题交流及 oncall
?> Dragonfly 没有传统的 KIM 用户群或 oncall 群（[why?](https://kstack.corp.kuaishou.com/article/3359)），我们希望提供一个开放的社区问答模式，以更好的沉淀问答知识，通用性问题和建议请在 KStack 的 [Dragonfly 圈子](https://kstack.corp.kuaishou.com/group/92)内提问，oncall 会定时解答，知道答案的热心同学可能比 oncall 回答的更及时😊，我们每个季度也会对这些热心同学给与奖品激励。

提问时请注意：
1. 是[发布问题](https://kstack.corp.kuaishou.com/question/edit?groupId=92)，不是发布文章！要发布到 [Dragonfly 圈子](https://kstack.corp.kuaishou.com/group/92)里，否则 oncall 和圈子里的同学收不到问题提醒！
2. 总结好问题标题，方便其他人快速了解是否是自己知道或感兴趣的问题，请意识到你是在向社区提问，而不是单单向 oncall 提问。
3. 写清楚问题内容，一定要有问题现象和问题现场，强烈建议在 [Dragonfly Playground](https://dragonfly.corp.kuaishou.com/playground) 上构造一个最小可复现 case 并附上短链接，方便大家无偏差理解问题。
4. 如果你有较强的排查能力，在**做好第 3 条的前提下**欢迎给出自己的怀疑点和排查线索，不要在没有任何问题现象描述的情况下自己给自己诊断原因。
5. **线上紧急问题**（如服务大面积 core）或**不具共性的问题**可直接 KIM 联系 oncall。非线上问题但自己比较着急，可在 KStack 提好问后把问题链接 KIM 发给 oncall 催答。

打开 KRP 用户群，在群名中查找 “L” 后面的同学名字即是当周 oncall。

<img width="500" src="image/oncall.png" alt="oncall">

<img width="360" src="image/KRP_user_group.png" alt="KRP用户群二维码">
