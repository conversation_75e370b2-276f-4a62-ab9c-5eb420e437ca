function locationHashToKV(locationHash) {
  //location.hash = decodeURI(location.hash);
  var suffixKV = {};
  var indexQuestionMark = locationHash.indexOf("?");
  if (indexQuestionMark > 0) {
    suffixContent = location.hash.substr(indexQuestionMark + 1).split("&");
    for (var i in suffixContent) {
      [attr, val] = suffixContent[i].split("=");
      suffixKV[attr] = val;
    }
  }
  return suffixKV;
}
