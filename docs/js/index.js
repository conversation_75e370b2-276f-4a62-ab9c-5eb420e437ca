window.$docsify = {
  name: "",
  repo: "",
  loadSidebar: true,
  subMaxLevel: 4,
  auto2top: true,
  noEmoji: true,
  basePath: ".",
  // complete configuration parameters
  search: {
    maxAge: 86400000, // Expiration time, the default one day
    paths: "auto",
    placeholder: "搜本站、搜 KStack",
    noData: "本站无相关内容...",
    // Headline depth, 1 - 6
    depth: 6,
    hideOtherSidebarContent: false, // whether or not to hide other sidebar content
  },
  vueGlobalOptions: {
    data() {
      return {
        drawerOpen: false,
        itemList: [],
        drawerSize: "30%",
        queryScriptLoading: false,
        search_input: "",
        show_wiki_results: true,
        show_kstack_results: true,
        kstack_err_message: "",
        resultList: [],
      };
    },
    mounted: function () {
      const width = parseFloat($(".sidebar")[0].style.width) - 18;
      if ($(".search-input-wrap")[0]) {
        $(".search-input-wrap")[0].style.width = `${width}px`;
      }

      let bannerContent = '';
      // bannerContent = 'Dragonfly 10月更新通告，<a href="https://docs.corp.kuaishou.com/k/home/<USER>/fcAB9raWBr-5OBtHfR0jhKvgt" target="_blank">点击查看 >></a>';
      if (bannerContent) {
        $(`<p id='banner' >${bannerContent}</p>`).appendTo("body");
        $(".content").css("top", "10px");
        $(".sidebar").css("top", "36px");
        $(".search-input-wrap")[0].style.top = "33px";
      }
    },
    computed: {
      filteredResults() {
        return this.resultList.filter(
          (result) =>
            (result.source === "wiki" && this.show_wiki_results) ||
            (result.source === "kstack" && this.show_kstack_results)
        );
      },
      wiki_result_num() {
        return this.resultList.filter((result) => result.source === "wiki")
          .length;
      },
      kstack_result_num() {
        return this.resultList.filter((result) => result.source === "kstack")
          .length;
      },
    },
    methods: {
      doSearch: function () {
        const indexKey = "docsify.search.index";
        const indexes = localStorage.getItem(indexKey);
        let INDEXS = {};
        if (indexes) {
          try {
            INDEXS = JSON.parse(indexes);
          } catch (err) {
            console.error(err);
            console.error("parse search index in localStorage failed");
          }
        }
        data = [];
        Object.keys(INDEXS).forEach(function (key) {
          data = data.concat(
            Object.keys(INDEXS[key]).map(function (page) {
              return INDEXS[key][page];
            })
          );
        });
        this.resultList = [];
        try{
          this.search(data);
        } catch(error) {
          console.log('search wiki error: ', error);
        }
        try{
          this.searchEs();
        } catch(error) {
          console.log('search Es error: ', error);
        }

        let $dividingLine = Docsify.dom.find("#dividingLine");
        let $content = Docsify.dom.find(".content");
        let $sidebar = Docsify.dom.find(".sidebar");
        $sidebar.scrollTop = "0";
        let $searchInputWrap = Docsify.dom.find(".search-input-wrap");
        if (!this.search_input) {
          $sidebar.style.width = "300px";
          $searchInputWrap.style.width = "282px";
          $dividingLine.style.left = "300px";
          $content.style.left = "300px";
        } else if (
          !$sidebar.style.width ||
          parseInt($sidebar.style.width.slice(0, -2), 10) <
            window.innerWidth / 3
        ) {
          $sidebar.style.width = `${window.innerWidth / 3}px`;
          $dividingLine.style.left = `${window.innerWidth / 3}px`;
          $content.style.left = `${window.innerWidth / 3}px`;
          $searchInputWrap.style.width = `${window.innerWidth / 3 - 20}px`;
        }
      },
      searchEs: async function () {
        this.kstack_err_message = "";
        const query = {
          query: {
            multi_match: {
              query: this.search_input.trim(),
              fields: ["title", "content", "tagList", "answerList.content"],
              analyzer: "ik_smart",
            },
          },
          highlight: {
            pre_tags: ["<font color=#42b983>"],
            post_tags: ["</font>"],
            fields: {
              title: {},
              content: {},
              "answerList.content": {},
            },
          },
        };
        let kstack_hits = [];
        try {
          const res = await axios.post("/kstackdragonfly/_search", query);
          kstack_hits = _.get(res, "data.hits.hits", []);
        } catch (err) {
          console.error(err);
          console.error("search es kstack failed");
          this.kstack_err_message = "搜索失败";
        }
        kstack_hits.forEach((post) => {
          html_kstack =
            '<div class="matching-post">\n<a href="' +
            post._source.url +
            '" target="_blank">\n<h2>';
          html_kstack += ' <img class="search-logo" src="../image/kstack.svg">';
          const highlight_title = _.get(post, "highlight.title", "");
          if (highlight_title) html_kstack += highlight_title;
          else html_kstack += post._source.title;
          html_kstack += `</h2>\n</a><p class = "question-focus" style="font-size:80%"><font color =grey>回答 ${post._source.answerCount} &nbsp&nbsp关注 ${post._source.followCount} &nbsp&nbsp浏览 ${post._source.viewCount}</font></p>`;
          const highlight_content = _.get(post, "highlight.content", "");
          if (highlight_content) html_kstack += highlight_content;
          else html_kstack += post._source.content;
          html_kstack += `<p class = "answer-tag" style="font-size:80%">回答</p>`;

          const highlight_answer = _.get(
            post,
            "highlight['answerList.content']",
            ""
          );
          if (highlight_answer) {
            html_kstack += highlight_answer;
          } else {
            html_kstack += post._source.answerList.map((x) => x.content);
          }
          html_kstack += "</div>";

          var matchingPost = {
            source: "kstack",
            title: highlight_title ? highlight_title : post._source.title,
            content: highlight_content
              ? highlight_content
              : post._source.content,
            url: post._source.url,
            html: html_kstack,
          };
          this.resultList.push(matchingPost);
        });
      },
      search: function (data) {
        var matchingResults = [];
        var keywords = this.search_input.trim().split(/[\s\-，,\\/]+/);

        var loop = function (i) {
          var post = data[i];
          var matchesScore = 0;
          var resultStr = "";
          var handlePostTitle = "";
          var handlePostContent = "";
          var postTitle = post.title && post.title.trim();
          var postContent = post.body && post.body.trim();
          var postUrl = post.slug || "";

          if (postTitle) {
            keywords.forEach(function (keyword) {
              //From https://github.com/sindresorhus/escape-string-regexp
              var regEx = new RegExp(
                keyword.replace(/[|\\{}()[\]^$+*?.]/g, "\\$&"),
                "gi"
              );
              var indexTitle = -1;
              var indexContent = -1;
              handlePostTitle = postTitle;
              handlePostContent = postContent;

              indexTitle = postTitle ? handlePostTitle.search(regEx) : -1;
              indexContent = postContent ? handlePostContent.search(regEx) : -1;

              if (indexTitle >= 0 || indexContent >= 0) {
                matchesScore += indexTitle >= 0 ? 3 : indexContent >= 0 ? 2 : 0;
                if (indexContent < 0) {
                  indexContent = 0;
                }

                var start = 0;
                var end = 0;

                start = indexContent < 11 ? 0 : indexContent - 10;
                end = start === 0 ? 70 : indexContent + keyword.length + 60;

                if (postContent && end > postContent.length) {
                  end = postContent.length;
                }

                var matchContent =
                  "..." +
                  handlePostContent
                    .substring(start, end)
                    .replace(regEx, function (word) {
                      return '<em class="search-keyword">' + word + "</em>";
                    }) +
                  "...";

                resultStr += matchContent;
              }
            });

            if (matchesScore > 0) {
              let html =
                '<div class="matching-post">\n<a href="' + postUrl + '">\n<h2>';
              html +=
                '<img class="search-logo" src="../image/wiki-searchlogo.svg" width="16px" height="16px">';
              let content = postContent ? resultStr : "";
              html +=
                handlePostTitle + "</h2>\n<p>" + content + "</p>\n</a>\n</div>";
              var matchingPost = {
                source: "wiki",
                title: handlePostTitle,
                content: content,
                url: postUrl,
                score: matchesScore,
                html: html,
              };
              matchingResults.push(matchingPost);
            }
          }
        };

        for (var i = 0; i < data.length; i++) loop(i);
        matchingResults.sort(function (r1, r2) {
          return r2.score - r1.score;
        });
        this.resultList = matchingResults;
      },
      onLikeClick: async function (id, is_like) {
        let item = this.itemList[id];
        if (is_like) item.liked = !item.liked;
        else item.disliked = !item.disliked;
        if (item.liked && item.disliked) {
          if (is_like) item.disliked = false;
          else item.liked = false;
        }
        item.like_style = "cursor:hand;";
        if (item.liked) {
          item.like_style +=
            "filter:sepia(79%) saturate(2476%) hue-rotate(353deg) brightness(90%) contrast(119%);";
        }
        item.dislike_style = "cursor:hand;";
        if (item.disliked) {
          item.dislike_style +=
            "filter:sepia(79%) saturate(2476%) hue-rotate(353deg) brightness(90%) contrast(119%);";
        }
        try {
          const res = await axios.get(
            `https://dragonfly.corp.kuaishou.com/like_script?id=${item.id}&liked=${item.liked}&disliked=${item.disliked}`,
            { timeout: 10000 }
          );
          item.like_count = _.get(res, "data.data", item.like_count);
        } catch (err) {
          console.error(err);
          console.error("like script failed");
          this.$message({
            showClose: true,
            message: "反馈失败 " + err.message,
            type: "error",
          });
        }
      },
      queryScript: async function (e) {
        const query = e.currentTarget.id;
        this.itemList = [];
        try {
          this.queryScriptLoading = true;
          const res = await axios.get(
            "https://dragonfly.corp.kuaishou.com/script/_search?processor=" +
              query,
            { timeout: 10000 }
          );
          console.debug(res);
          const result = _.get(res, "data.data", []);
          for (const item of result) {
            const scriptHtml = Prism.highlight(
              item.script,
              Prism.languages.python,
              "python"
            );
            let like_style = "cursor:hand;";
            if (item.liked === true) {
              like_style +=
                "filter:sepia(79%) saturate(2476%) hue-rotate(353deg) brightness(90%) contrast(119%);";
            }
            let dislike_style = "cursor:hand;";
            if (item.disliked === true) {
              dislike_style +=
                "filter:sepia(79%) saturate(2476%) hue-rotate(353deg) brightness(90%) contrast(119%);";
            }
            this.itemList.push({
              script: scriptHtml,
              id: item.id,
              creator: item.creator || "UNKNOWN",
              run_times: item.run_times,
              liked: item.liked,
              disliked: item.disliked,
              like_count: item.like_count,
              like_style: like_style,
              dislike_style: dislike_style,
            });
          }
          this.drawerSize = _.isEmpty(result) ? "30%" : "40%";
          this.drawerOpen = true;
        } catch (err) {
          console.error(err);
          console.error("axios get es failed");
          this.$message({
            showClose: true,
            message: "查询示例失败 " + err.message,
            type: "error",
          });
        } finally {
          this.queryScriptLoading = false;
        }
      },
    },
  },
  plugins: [
    function (hook, vm) {
      hook.afterEach(function (html) {
        if (vm.route.file.includes("api")) {
          let routeLen = vm.route.file.length;
          let moduleName = vm.route.file.substring(4, routeLen - 3);
          let url =
            "https://git.corp.kuaishou.com/reco-cpp/dragon/-/blob/master/dragonfly/ext/" +
            moduleName +
            "/" +
            moduleName +
            "_api_mixin.py";
          let editHtml =
            "<p><a href=" +
            url +
            ' target="_blank" rel="noopener"><img class="emoji" src="image/memo.png" alt="memo"> 帮助改进文档</a></p>';
          return (
            '<div class="api-section">' +
            '<div class="edit-button-section">' +
            editHtml +
            "</div>" +
            html +
            "</div>"
          );
        } else {
          let url =
            "https://git.corp.kuaishou.com/reco-cpp/dragon/-/tree/master/docs/" +
            vm.route.file;
          let editHtml =
            "<p><a href=" +
            url +
            ' target="_blank" rel="noopener"><img class="emoji" src="image/memo.png" alt="memo"> 帮助改进文档</a></p>';
          return (
            '<div class="edit-button-section">' + editHtml + "</div>" + html
          );
        }
      });
      hook.mounted(function () {
        let isSidebarOpen = true;
        let contentDom = $(".content")[0];
        let sidebarDom = $(".sidebar")[0];
        let inputWrapDom = $(".input-wrap")[0];
        inputWrapDom.style.display = "none";

        $(
          "<div id='dividingLine' style='width: 11px;position: fixed;height: 100%;left: 300px;display: block;cursor: col-resize;'></div>"
        ).appendTo("main");
        $(
          "<div id='dividingLineLogo' style='border-left:#cacaca 1px solid;border-right:#cacaca 1px solid;position: relative;top: 50%;height: 20px;left:2px;width: 9px;cursor: col-resize;'></div>"
        ).appendTo("#dividingLine");
        $(
          "<div id='dividingLinestl' style='border-left: solid 1px #cacaca;position: relative;top: -20px;height: 100%;left:6px;cursor: col-resize;'></div>"
        ).appendTo("#dividingLine");

        let sidebarButtonDom = $(".sidebar-toggle")[0];
        sidebarButtonDom.parentNode.removeChild(sidebarButtonDom);

        $("<button class = 'new-sidebar-toggle'></button>").appendTo("main");
        $("<div class = 'sidebar-toggle-button'></div>").appendTo(
          ".new-sidebar-toggle"
        );
        $("<span></span><span></span><span></span>").appendTo(
          ".sidebar-toggle-button"
        );

        $("#dividingLine").draggable({
          axis: "x",
          containment: [300, 0, 900, 0],
          drag: function (event, ui) {
            contentDom.style.left = `${ui.position.left}px`;
            sidebarDom.style.width = `${ui.position.left}px`;
            $(".search-input-wrap")[0].style.width = `${
              ui.position.left - 18
            }px`;
          },
        });
        $(".new-sidebar-toggle").click(function (event, ui) {
          if (isSidebarOpen) {
            isSidebarOpen = false;
            sidebarDom.style.width = "0px";
            searchInputDom.style.width = "0px";
            $(".search-input-wrap")[0].style.display = "none";

            contentDom.style.left = "0px";
            $("#dividingLine")[0].style.display = "none";
          } else {
            isSidebarOpen = true;
            sidebarDom.style.width = "300px";
            $(".search-input-wrap")[0].style.width = "282px";
            $(".search-input-wrap")[0].style.display = "";
            contentDom.style.left = "300px";
            $("#dividingLine")[0].style.left = "300px";
            $("#dividingLine")[0].style.display = "";
          }
        });

        let $search = Docsify.dom.find("div.search");
        let $input = Docsify.dom.find($search, "input");

        let suffixKV = locationHashToKV(location.hash);
        if (suffixKV["q"]) {
          $input.value = decodeURIComponent(suffixKV["q"]);
          setTimeout(() => {
            const event = new Event("input");
            $input.dispatchEvent(event);
          }, 200);
        }
      });
    },
  ],
};
