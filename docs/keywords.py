keywords = [
  {
    "keyword": "动态参数",
    "description": 
    """
    标有动态参数的配置为可以通过 <span v-pre>`{{xxx}}`</span> 这样格式的字符串来在程序运行时动态地获取参数值。<span v-pre>`{{}}`</span>中的内容可以是一个common attr的名字，
    运行时取到的就是这个common attr当前的值，例如在配置项写<span v-pre>`{{showCount}}`</span>。
    <span v-pre>`{{}}`</span>中的内容也可以是return expression的形式（expression可以填一个lua表达式），
    表达式中如果出现变量会从对应的common attr取当前值代入进行计算，运行时取到的就是这个表达式计算的结果，例如在配置项写<span v-pre>`{{return showCount * 3}}`</span>
    """
  },
  {
    "keyword": "proto 动态注册",
    "description":
    """
    proto 动态注册是指在运行时动态加载 proto 文件，而不是在编译时将 proto 文件编译成 pb 文件。
    注册方式：通过 service.register_proto 方式注册 proto。
    目前支持 api 有 build_protobuf, parse_protobuf_from_string, enrich_by_generic_rpc，如果其他 api 想使用，需要在 api 的实现中添加对应的 proto 动态注册的支持。
    详细文档请参考：https://docs.corp.kuaishou.com/d/home/<USER>
    如有疑问请联系：@邢家远
    """
  },
  {
    "keyword": "RedisErrorCode",
    "description": 
    """
    KS_INF_REDIS_ERR_UNKNOWN = -1,
    KS_INF_REDIS_NO_ERROR = 0,
    KS_INF_REDIS_PROTOCOL_ERROR = 1,
    KS_INF_REDIS_CONN_ERROR = 2,
    KS_INF_REDIS_REPLY_ERROR = 3,
    KS_INF_REDIS_ERR_NONEXIST = 4,
    KS_INF_REDIS_ERR_EXIST = 5,
    KS_INF_REDIS_UNEXPECT_TYPE = 6,
    KS_INF_REDIS_PARTIAL_FAIL = 7,
    KS_INF_REDIS_ERR_REPLYSIZE = 8,
    KS_INF_REDIS_ERR_PARAM = 9,
    KS_INF_REDIS_ERR_OUT_OF_RANGE = 10,
    KS_INF_REDIS_ERR_TIMEOUT = 11,
    KS_INF_REDIS_INVALID_VAL = 12,
    KS_INF_REDIS_ERR_INTERNAL = 13,
    KS_INF_REDIS_ERR_INIT = 14,
    KS_INF_REDIS_CONNPOOL_ERROR = 15,
    KS_INF_REDIS_PIPELINE_INIT_ERR = 16,
    KS_INF_REDIS_ERR_PIPELINE_FAIL = 17,
    KS_INF_REDIS_ERR_PIPELINE_RESULT_TYPE_MISMATCH = 18,
    KS_INF_REDIS_ERR_PIPELINE_RESULT_OVERFLOW = 19,
    KS_INF_REDIS_ERR_PIPELINE_RESULT_NOT_READY = 20,
    KS_INF_REDIS_NOT_AUTH = 21,
    KS_INF_REDIS_DEGRADABLE = 22,
    KS_INF_REDIS_ERR_NUM = 23,
    """
  }
]
