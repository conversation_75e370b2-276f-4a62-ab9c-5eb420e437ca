"""
flask_cas.__init__
"""

import os
from functools import wraps

import flask
from flask import current_app, jsonify

# Find the stack on which we want to store the database connection.
# Starting with Flask 0.9, the _app_ctx_stack is the correct one,
# before that we need to use the _request_ctx_stack.
try:
  from flask import _app_ctx_stack as stack
except ImportError:
  from flask import _request_ctx_stack as stack

from . import routing

class CAS:
  """
  Required Configs:
  |Key             |
  |----------------|
  |CAS_SERVER      |
  |CAS_AFTER_LOGIN |
  Optional Configs:
  |Key                        | Default               |
  |---------------------------|-----------------------|
  |CAS_TOKEN_SESSION_KEY      | _CAS_TOKEN            |
  |CAS_USERNAME_SESSION_KEY   | CAS_USERNAME          |
  |CAS_ATTRIBUTES_SESSION_KEY | CAS_ATTRIBUTES        |
  |CAS_LOGIN_ROUTE            | '/cas'                |
  |CAS_LOGOUT_ROUTE           | '/cas/logout'         |
  |CAS_VALIDATE_ROUTE         | '/cas/serviceValidate'|
  |CAS_AFTER_LOGOUT           | None                  |
  """

  def __init__(self, app=None, url_prefix=None):
    """ init function """
    self._app = app
    if app is not None:
      self.init_app(app, url_prefix)

  def init_app(self, app, url_prefix=None):
    """ init_app """
    # Configuration defaults
    app.config.setdefault('CAS_TOKEN_SESSION_KEY', '_CAS_TOKEN')
    app.config.setdefault('CAS_USERNAME_SESSION_KEY', 'CAS_USERNAME')
    app.config.setdefault('CAS_ATTRIBUTES_SESSION_KEY', 'CAS_ATTRIBUTES')
    app.config.setdefault('CAS_LOGIN_ROUTE', '/cas')
    app.config.setdefault('CAS_LOGOUT_ROUTE', '/cas/logout')
    app.config.setdefault('CAS_VALIDATE_ROUTE', '/cas/serviceValidate')
    # Requires CAS 2.0 
    app.config.setdefault('CAS_AFTER_LOGOUT', None)
    # Register Blueprint
    app.url_map.strict_slashes = False
    app.register_blueprint(routing.blueprint, url_prefix=url_prefix)

    # Use the newstyle teardown_appcontext if it's available,
    # otherwise fall back to the request context
    if hasattr(app, 'teardown_appcontext'):
      app.teardown_appcontext(self.teardown)
    else:
      app.teardown_request(self.teardown)

  def teardown(self, exception):
    """ teardown """
    ctx = stack.top

  @property
  def app(self):
    """ app """
    return self._app or current_app

  @property
  def username(self):
    """ return username """
    return flask.session.get(
      self.app.config['CAS_USERNAME_SESSION_KEY'], None)

  @property
  def attributes(self):
    """ return attributes """
    return flask.session.get(
      self.app.config['CAS_ATTRIBUTES_SESSION_KEY'], None)

  @property
  def token(self):
    """ return token """
    return flask.session.get(
      self.app.config['CAS_TOKEN_SESSION_KEY'], None)

def login():
  """ send login request to frontend """
  return flask.redirect(flask.url_for('cas.login', _external=True))
  #return jsonify(status_code=302,
                 #msg="Redirect",
                 #data=None)

def logout():
  """ handle logout """
  return flask.redirect(flask.url_for('cas.logout', _external=True))

def login_required(function):
  """ @login_required decoration """
  @wraps(function)
  def wrap(*args, **kwargs):
    cas_username_session_key = current_app.config['CAS_USERNAME_SESSION_KEY']
    if cas_username_session_key in flask.session:
      return function(*args, **kwargs)
    if 'TEST_USER' in os.environ:
      flask.session[cas_username_session_key] = os.environ['TEST_USER']
      return function(*args, **kwargs)
    # We use header['Referer'] as target url instead of request.path for all ajax calls
    # flask.session['CAS_AFTER_LOGIN_SESSION_URL'] = flask.request.path
    flask.session['CAS_AFTER_LOGIN_SESSION_URL'] = flask.request.headers.get('Referer')
    return login()
  return wrap
