"""
flask_cas.routing
Functions for creating new cas related routing app.
"""

import logging
import flask
from flask import current_app
import sys

from xmltodict import parse
from .cas_urls import create_cas_login_url, create_cas_logout_url, create_cas_validate_url

try:
  from urllib import urlopen
  from urlparse import urljoin, urlparse
except ImportError:
  from urllib.request import urlopen
  from urllib.parse import urljoin, urlparse

blueprint = flask.Blueprint('cas', __name__)

@blueprint.route('/login', methods=['GET'])
def login():
  """
  This route has two purposes. First, it is used by the user
  to login. Second, it is used by the CAS to respond with the
  `ticket` after the user logs in successfully.
  When the user accesses this url, they are redirected to the CAS
  to login. If the login was successful, the CAS will respond to this
  route with the ticket in the url. The ticket is then validated.
  If validation was successful the logged in username is saved in
  the user's session under the key `CAS_USERNAME_SESSION_KEY` and
  the user's attributes are saved under the key
  'CAS_USERNAME_ATTRIBUTE_KEY'
  """
  cas_token_session_key = current_app.config['CAS_TOKEN_SESSION_KEY']

  redirect_url = create_cas_login_url(
    current_app.config['CAS_SERVER'],
    current_app.config['CAS_LOGIN_ROUTE'],
    flask.url_for('cas.login', _external=True))
  if 'ticket' in flask.request.args:
    flask.session[cas_token_session_key] = flask.request.args['ticket']
  if cas_token_session_key in flask.session:
    if validate(flask.session[cas_token_session_key]):
      # if 'CAS_AFTER_LOGIN_SESSION_URL' in flask.session:
        # redirect_url = flask.session.pop('CAS_AFTER_LOGIN_SESSION_URL')
      # else:
        # 去掉 host_url 中的端口信息
      url = urlparse(flask.request.host_url)
      host = '{}://{}/'.format(url.scheme, url.hostname)
      redirect_url = urljoin(host, current_app.config['CAS_AFTER_LOGIN'])
    else:
      del flask.session[cas_token_session_key]
  
  current_app.logger.debug('Redirecting to: {0}'.format(redirect_url))
  return flask.make_response(flask.redirect(redirect_url))


def validate(ticket):
  """
  Will attempt to validate the ticket. If validation fails, then False
  is returned. If validation is successful, then True is returned
  and the validated username is saved in the session under the
  key `CAS_USERNAME_SESSION_KEY` while the validated attributes dictionary
  is saved under the key 'CAS_ATTRIBUTES_SESSION_KEY'.
  """
  cas_username_session_key = current_app.config['CAS_USERNAME_SESSION_KEY']
  cas_attributes_session_key = current_app.config['CAS_ATTRIBUTES_SESSION_KEY']

  current_app.logger.debug("validating token {0}".format(ticket))

  cas_validate_url = create_cas_validate_url(
    current_app.config['CAS_SERVER'],
    current_app.config['CAS_VALIDATE_ROUTE'],
    flask.url_for('.login', _external=True),
    ticket)

  current_app.logger.debug("Making GET request to {0}".format(
    cas_validate_url))

  xml_from_dict = {}
  isValid = False

  try:
    xmldump = urlopen(cas_validate_url).read().strip().decode('utf8', 'ignore')
    xml_from_dict = parse(xmldump)
    isValid = "cas:authenticationSuccess" in xml_from_dict["cas:serviceResponse"]
  except ValueError:
    current_app.logger.error("CAS returned unexpected result")

  if isValid:
    current_app.logger.debug("valid")
    xml_from_dict = xml_from_dict["cas:serviceResponse"]["cas:authenticationSuccess"]
    username = xml_from_dict["cas:user"]
    attributes = xml_from_dict.get("cas:attributes", {})

    if "cas:memberOf" in attributes:
      attributes["cas:memberOf"] = str(attributes["cas:memberOf"]).lstrip('[').rstrip(']').split(',')
      for group_number in range(0, len(attributes['cas:memberOf'])):
        attributes['cas:memberOf'][group_number] = attributes['cas:memberOf'][group_number].lstrip(' ').rstrip(' ')

    flask.session[cas_username_session_key] = username
    flask.session[cas_attributes_session_key] = attributes
    logging.info("user [%s] login succeed, attributes: %s", username, attributes)
  else:
    current_app.logger.debug("invalid")

  return isValid
