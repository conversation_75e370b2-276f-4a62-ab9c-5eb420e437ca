ps -ef | grep 'docs_server.py 3000' | grep -v grep | awk '{print $2}' | xargs -i kill {} 
cd docs
__conda_setup="$('/home/<USER>/miniconda3/bin/conda' 'shell.bash' 'hook'     2> /dev/null)"
if [ $? -eq 0 ]; then
    eval "$__conda_setup"
else
    if [ -f "/home/<USER>/miniconda3/etc/profile.d/conda.sh" ]; then
        . "/home/<USER>/miniconda3/etc/profile.d/conda.sh"
    else
        export PATH="/home/<USER>/miniconda3/bin:$PATH"
    fi
fi
unset __conda_setup
conda activate py36
source /data/project/dragonfly-playground/venv/bin/activate
nohup python3 docs_server.py 3000 >/dev/null 2>&1 &