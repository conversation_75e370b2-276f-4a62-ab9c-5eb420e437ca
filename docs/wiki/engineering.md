# 工程化及最佳实践

## 服务降级

### 服务端自身过载保护

通过 FLAG **--slow_list_length_limit_kconf_key=_xxx.yyy.zzz_** 配置一个 <font color = Blue><b>int64 类型</b></font>的 kconf 配置，其值将被用于动态设定 leaf 服务端的 slow_list（相当于一个请求缓存队列）长度限制，超过限制的请求将不会排队处理而是直接丢弃掉，对 leaf 服务进行过载保护（防止被上游打挂）。

该 FLAG 配置默认为空时 slow_list 将使用默认长度限制 100（100 是一个比较大的数）。

那么最后一个问题：kconf 配置的值如何确定？配置的值其实要视请求处理速度以及 grpc_thread_num 的不同而不同，最好的方法是通过压测观察可用性临界点，同时在 leaf 的 grafana 监控中查看 “**异常计数 - 请求等待队列 slow_list 长度**” 的值变化，来确定一个适合当前服务状态的配置值。一般情况下 slow_list 的长度限制应小于 grpc_thread_num，建议为 grpc_thread_num 的 0.2~0.5 倍。

### 算子熔断摘除

通过 FLAG **--processor_breaker_kconf_key=xxx.yyy.zzz** 配置一个 <font color = Blue><b>map_string_double 类型</b></font>的 kconf 配置，其值将被用于动态摘除 leaf 服务中的算子。其具体的应用场景是当线上发生紧急情况，通过监控发现某个算子耗时异常变高拖垮自身可用性，或是算子代码有问题导致服务 crash，通过算子熔断能力，可以将出现问题的算子进行快速摘除，解除稳定性风险。

另外一个问题：kconf 配置的值如何配置？举一个例子就明白了
```json
{
  // key 为算子名，可通过监控查阅；value 是熔断概率，取值 1.0 时表示全熔断跳过
  "cascading::cascading_house_tower::global_data_enricher_test" : 0.9
}
```

### 异步算子限流降级

?> 如果是 kess RPC 请求，可优先使用 kess 平台提供的熔断降级功能，详见文档[KESS 熔断降级配置](https://halo.corp.kuaishou.com/help/docs/072d623c9c2a6b89f7a7dd0954718a34)

Dragonfly 支持对<font color = Red><b>异步 Processor</b></font> 进行限流降级，即满足限流条件后 Processor 将不被执行，限流策略的参数值通过 json 类型的 kconf 配置获取。

支持四种类型的限流器：

| 名称             | 作用                                                                                 |
|------------------|--------------------------------------------------------------------------------------|
| circuit_breaker  | 熔断策略，满足条件时直接熔断，即所有请求都不执行，等待一定时间后关闭熔断状态开始重试 |
| bulk_head        | 并发控制，对当前超过最大并发数的这部分请求拒绝执行                                   |
| adaptive_limiter | 限流策略，满足条件时，按一定概率对请求进行拒绝执行                                   |
| random_drop | 限流策略，按一定概率随机对请求进行拒绝执行                                   |


详细介绍参见：

[C++ 熔断限流基础组件](https://https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=276003608)

[resilience4j限流组件](https://resilience4j.readme.io/docs/circuitbreaker)

推荐阅读：[关于服务容错能力的几点思考](https://kstack.corp.kuaishou.com/tech/web/article/info/344)

相关配置如下：

***新方法：统一策略配置 + 算子策略名配置***

**服务 gflag 配置**
```python
# 统一限流策略配置的 kconf 路径，json格式，内容可以参考下方示例
--degrade_config_kconf_key=xxx
# 限流功能全局开关的 kconf 路径，bool格式，默认为true
--degrade_switch_kconf_key=xxx
```

**统一限流策略的 kconf 配置示例**
```json
{
    "P0": {     // 限流策略名
        "degrader": "circuit_breaker", // 限流器类型，共支持 4 种限流器
        "enabled": true,               // 是否开启限流功能
        "failureRateThreshold": 50,    // 0-100 的百分比值，失败比例可以参考可熔断业务的重要性，按照重要性由低到高经验值范围是（50 ~ 95）
        "requestWindowSize": 200,      // 统计失败率的窗口大小，针对QPS较高的服务，可以考虑适当调大，按照 QPS 由低到高可以配置成（200 ~ 1000）
        "durationSecondsInOpen": 10    // 一次熔断维持多少秒后重试，推荐范围是（10s ~ 60s）
    },
    "P1": {     // 限流策略名
        "degrader": "bulk_head",      // 限流器类型，共支持 4 种限流器
        "enabled": true,              // 是否开启限流功能
        "maxConcurrency": 1           // 允许最大并发度，需大于等于0
    },
    "P2": {     // 限流策略名
        "degrader": "adaptive_limiter",// 限流器类型，共支持 4 种限流器
        "enabled": true,               // 是否开启限流功能
        "requestWindowSize": 500,      // 统计失败率的窗口大小
        "factor": 1                    // 限流因子，当下游可用性低于 1/factor 时，开始随机丢弃，
                                       // 丢弃概率为 max(requestWindowSize - factor * succeed, 0) / requestWindowSize
                                       // 例如值为 2 表示当失败率达到 50% 时开始按概率随机丢弃请求
                                       // 一些对应的常用可用性容忍阈值:
                                       // 1 = 100%(只要可用性不满就开始随机拒绝), 1.05 = 95%, 1.11 = 90%, 1.25 = 80%, 2 = 50%

    }, 
    "P3": {     // 限流策略名
        "degrader": "random_drop",    // 限流器类型，共支持 4 种限流器
        "enabled": true,              // 是否开启限流功能
        "percentage": 0.4             // 随机丢弃的请求比例
    }
}
```

**Dragonfly 算子配置**
可以为算子增加 degrade_strategy 配置项，指定算子使用的限流策略名
```python
# 可以为算子增加 degrade_strategy 配置项，指定算子使用的限流策略名
# 多个算子可共用同一个限流策略；这一组算子会共享并发控制策略的并发数，或者共同参加有条件判断的限流策略的条件判断
# degrade_strategy 支持动态参数
flow = LeafFlow(name="test") \
  .retrieve_by_sub_flow(sub_flow=flow1, timeout_ms=100, degrade_strategy="P0") \
  .retrieve_by_sub_flow(sub_flow=flow2, timeout_ms=100, degrade_strategy="P0") \
  .retrieve_by_sub_flow(sub_flow=flow3, timeout_ms=100, degrade_strategy="{{strategy_name}}") \
  .retrieve_by_sub_flow(sub_flow=flow4, timeout_ms=100, degrade_strategy="P1") \
  .retrieve_by_sub_flow(sub_flow=flow5, timeout_ms=100, degrade_strategy="P1") 
```
enricher算子可以使用 degrade_limit 配置，在命中降级时不跳过算子的执行，而是将计算的结果集限制到一定数量
```python
# enricher算子可以使用 degrade_limit 配置，在命中降级时不跳过算子的执行，而是将计算的结果集限制到一定数量
# degrade_limit 是一个 string 类型配置，指定存储数量限制的 int common attr
flow = LeafFlow(name="test") \
  .delegate_enrich(sub_flow=flow1, timeout_ms=100, degrade_strategy="P0", degrade_limit="num_limit")
```

***旧方法（不推荐）：Dragonfly统一配置***
```python
# 注意：add_degrade_config 需在 add_leaf_flows 之后调用！
service.add_leaf_flows(leaf_flows = [flow], request_type="default", as_default=True) \
       .add_degrade_config(degrader_type="circuit_breaker", kconf_key="reco.yinYueTai.leafDegradeForAll", \
                           processor=["fr_predict_ACG0M3"]) \
       .add_degrade_config(degrader_type="adaptive_limiter", kconf_key="reco.yinYueTai.leafDegradeForSensitive", \
                           request_type=["yinyuetai", "yinyuetai_live"], \
                           processor=["fr_predict_ACG0M3"]) \
       .build()
```

> <font color = Blue>如果 kconf 配置了非法值，将忽略该次配置变更，继续沿用上一次的合法配置！</font>

**circuit_breaker 的 kconf 配置**
```json
{
  "enabled": true,              // 是否开启限流功能
  "failureRateThreshold": 60,   // 0-100 的百分比值，失败比例可以参考可熔断业务的重要性，按照重要性由低到高经验值范围是（50 ~ 95）
  "requestWindowSize": 500,     // 统计失败率的窗口大小，针对QPS较高的服务，可以考虑适当调大，按照 QPS 由低到高可以配置成（200 ~ 1000）
  "durationSecondsInOpen": 30   // 一次熔断维持多少秒后重试，推荐范围是（10s ~ 60s）
}
```
**bulk_head 的 kconf 配置**
```json
{
  "enabled": true,              // 是否开启限流功能
  "maxConcurrency": 1000        // 允许最大并发度，需大于0
}
```
**adaptive_limiter 的 kconf 配置**
```json
{
  "enabled": true,              // 是否开启限流功能
  "requestWindowSize": 500,     // 统计失败率的窗口大小
  "factor": 2,                  // 限流因子，当下游可用性低于 1/factor 时，开始随机丢弃，丢弃概率为 max(requestWindowSize - factor * succeed, 0) / requestWindowSize
                                // 例如值为 2 表示当失败率达到 50% 时开始按概率随机丢弃请求
                                // 一些对应的常用可用性容忍阈值:
                                // 1 = 100%(只要可用性不满就开始随机拒绝), 1.05 = 95%, 1.11 = 90%, 1.25 = 80%, 2 = 50%
}
```
**random_drop 的 kconf 配置**
```json
{
  "enabled": true,              // 是否开启限流功能
  "percentage": 0.3,            // 随机丢弃的概率，范围为 0.0 - 1.0 的浮点数
}
```

当降级实际发生后，可在 Grafana 监控中查看到相关数据（位于”异步请求“栏下）：

<img width="700" src="image/grafana-request.png" alt="grafana-request">


## 算子稳定命名

稳定的 processor 命名具有以下好处：

1. 能够通过监控对比这个 processor 天级、周级的耗时变化，帮助排查耗时增长问题。
2. 使用可视化的图 diff 功能时，是依赖于 processor 是稳定命名的，需要先开启该功能。
3. 后续开展的无用代码自动删除、图顺序优化等功能都依赖于processor 是稳定命名的。

然而目前默认的自动命名方式因为会对 processor 配置内容生成 hash tag 作为命名的后缀，导致只要有配置发生变更，就会改变自己甚至别的 processor 名字。

如果想获得稳定的命名，在以前只能靠人工指定 `name` 配置的方式，会对开发者造成一定负担，新的 processor 稳定命名模式尝试解决这个问题，在不增加人工命名负担的前提下，尽力保证自动命名的稳定性。

开启方法：给 LeafService 对象加上 `ENABLE_PROCESSOR_STABLE_NAME=True` 设置（**强烈建议用户开启**）。
``` python
service.ENABLE_PROCESSOR_STABLE_NAME=True
service.add_leaf_flows()
```

注意：当前方案并不能保证 100% 的命名稳定且无冲突，开启设置后可能会造成部分存量的 processor 命名冲突，这时需要给冲突的 processor 手动加上 `name` 来解决。我们会持续给各个 processor 选取较为固定、必要、合理的字段来生成 name hash，在配置内容的稳定性和可区分性之间寻找一个平衡，后续的更新可能会带来少量的新冲突需要逐步解决。

如有不符合预期的情况请及时反馈给我们，你的持续反馈能让自动命名更快地进入长期稳定😁。


## 函数式编程及模块化
函数式模块化代码库的核心代码，包括负责模块管理的 module.py， 以及负责数据管理的 data_manager.py

### `module` 详解

#### module 基类介绍
* 功能: 基于类装饰器/上下文管理器，将函数或代码段落封装成模块。
* 属性:
  1. name: [string] 模块名称，可缺省，若缺省直接使用函数名为模块名称。

#### 使用方法
  1. 装饰器模式: 将单个函数封装为模块。
      * 代码示例:
        ``` python
        from dragonfly.common_leaf_dsl import current_flow as flow
        from dragonfly.modular.module import module

        // 添加这个装饰器
        @module()
        def predict():
          flow() \
            .gen_common_attr_by_lua() \
            .delegate_enrich()

        @module()
        def ensemble_sort():
          flow() \
            .calc_weighted_sum() \
            .sort()
        ```

  2. 上下文管理器模式: 将大函数内的算子段落封装为模块。
      * 代码示例:
        ``` python
        from dragonfly.common_leaf_dsl import current_flow
        from dragonfly.modular.module import module

        def default_flow():
          flow = current_flow()

          with module(name="target_type_1"):
            flow \
              .if_("target_type == 1") \
                .prepare() \
                .retrieve_type_1() \
                .rank_score() \
              .end_()
          with module(name="target_type_2"):
            flow \
              .if_("target_type == 2") \
                .prepare() \
                .retrieve_type_2() \
                .rank_score() \
              .end_()
        ```

  3. 模块复用: 通过给函数模块载入不同参数方式，完成模块的复用。
      * 注意：函数需传入 name 参数为模块命名，以保证不出现重复模块。
      * 代码示例:
        ``` python
        from dragonfly.common_leaf_dsl import current_flow as flow
        from .module.get_user_embedding import get_user_embedding

        CascadeUserEmbeddingV1Config = {
          "name": "cascade_user_embedding_v1",
          "enable": ["enableCascadeLearningUserEmbeddingV1", False],
          "kess_service": ["cascadeLearningUserEmbeddingServerV1", "grpc_ksibCascadeV1UserTopEmbedServer"],
          "embedding_name": "cascade_user_embedding_v1"
        }

        CascadeUserEmbeddingV2Config = {
          "name": "cascade_user_embedding_v2",
          "enable": ["enableCascadeLearningUserEmbeddingV2", False],
          "kess_service": ["cascadeLearningUserEmbeddingServerV2", "grpc_ksibCascadeV2UserTopEmbedServer"],
          "embedding_name": "cascade_user_embedding_v2"
        }

        flow() \
          .do(
            # **config 表示传入字典参数，函数内可通过 config[k] 的方式使用参数
            get_user_embedding(**CascadeUserEmbeddingV1Config),
            get_user_embedding(**CascadeUserEmbeddingV2Config)
          )
        ```

  4. 异步模块: 将 module 装饰器与 async_retrieve, async_enrich, 或 parallel 装饰器组合使用，完成异步模块。
      * 代码示例:
        ``` python
        from dragonfly.common_leaf_dsl import current_flow as flow
        from dragonfly.decorators import async_retrieve
        from dragonfly.modular.module import module

        @module()
        @async_retrieve()
        def swing_i2i_retrieve():
          flow() \
            .delegate_retrieve(
              kess_service="grpc_swing_i2i_server",
              timeout_ms=50,
              send_common_attrs_in_request=False,
              send_common_attrs=[
                "bucket",
                {"name": "swing_int_trigger", "as": "trigger_list"},
                {"name": "swing_i2i_retr_num", "as": "request_num"}
              ],
              reason=2201
            )
        ```


### `data_manager` 详解

#### data_manager 基类介绍
* 功能: 自动获取 ab 参数，kconf 配置, 不需手动集中获取。

* 属性: 
    1. AB_SUFFIX: [string] ab 参数后缀配置。
    2. BIZ_NAME: [string] 获取 ab 的 biz name。
   
* ab_param: 获取 ab 参数的语法糖
   * 参数:
     1. `param_name`: [string|dict] ab 参数的名称。
     2. `default_value`: [int|double|string|bool] ab 参数获取失败时取用的默认值。
     3. `attr_name`: [string] 可缺省，要写入的 CommonAttr 名称，若缺省或为空将直接取 `param_name` 的值。
     4. `biz_name`: [string] 可缺省，业务 biz 信息，若缺省使用 flag 中的默认 biz。

   * 返回值: [string] ab 参数的 common attr 名称。
  
* kconf_param: 获取 kconf 配置的语法糖
   * 参数:
     1. `kconf_key`: [string] [动态参数] kconf 中的 key。
     2. `default_value`: [int|double|string|bool|list] kconf 配置获取失败时取用的默认值, 该值的类型需与 kconf_key 实际配置的值类型一致！
     3. `attr_name`: [string] 将 kconf 值写入指定的 CommonAttr。
     4. `json_path`: [动态参数] 可缺省，若 kconf_key 对应的值为 json 类型，可通过该项配置获取指定 json_path 下的值。

   * 返回值: [string] kconf 配置的 common attr 名称。
  
* set_ab_suffix: 设置 get_abtest_params 的 prioritized_suffix。
  * 参数：
    1. `ab_suffix`: [string] ab prioritized suffix。
   
* set_biz_name: 设置 get_abtest_params 的 biz_name。
  * 参数:
    1. `biz_name`: [string] ab biz name。

#### 使用方法
  1. 在 flow 实例创建后，需要用 with 创建一个 data_manager 的上下文环境，即可通过 ab_param 和 kconf_param 获取数据。
     * 代码示例:
       ```python
       from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
       from dragonfly.common_leaf_dsl import LeafFlow, current_flow as flow
       from dragonfly.common_leaf_util import get_dynamic_param

       flow = LeafFlow("demo_flow")

       # flow 的全部流程都在 data_manager 的上下文环境中
       with flow, data_manager: 
         # flow 会在执行模块之前，进行外部数据获取
         flow.do(
           initialize(),
           retrieve(),
           filter(),
           rank()
         )

       def filter():
         flow() \
           .if_(f'{ab("enable_filter", False)} == 1') \
             .filter_by_common_attr(common_attr=[kconf("kconf.key.name", [], "bad_photo_ids")]) \
           .end_if_()
       ```

  2. 在函数模块内创建 data_manager 上下文，可单独为函数获取外部参数。
     * 代码示例:
       ```python
       from dragonfly.modular.data_manager import data_manager, ab_param as ab
       from dragonfly.common_leaf_dsl import LeafFlow, current_flow as flow
       from dragonfly.common_leaf_util import get_dynamic_param

       @module()
       def kai_predictor(**config):
         with data_manager:
           flow() \ 
             .if_(f'{ab("enable_kai_predictor", False)} > 0') \
               .delegate_enrich(
                 kess_service = get_dynamic_param(ab("predictor_service_name", "grpc_ksDemoInfer")),
                 partition_size = config["partition_size"],
                 recv_item_attrs = config["recv_item_attrs"]
               ) \
             .end_if_()
       ```

  3. Subflow 的数据会单独获取，可以省略 with data_manager。
     * 代码示例:
       ```python
       from dragonfly.modular.data_manager import data_manager, ab_param as ab
       from dragonfly.common_leaf_dsl import LeafFlow, current_flow as flow
       from dragonfly.common_leaf_util import get_dynamic_param

       @module()
       @async_retrieve()
       def swing_i2i_retrieve():
         flow() \ # 写法上省略 "with data_manager:"，将会在首次使用 ab_param， kconf_param 语法糖之前的位置进行数据获取。
           .if_(f'{ab("swing_i2i_retr_num", 200)} > 0 and swing_int_trigger ~= nil') \
             .delegate_retrieve(
               kess_service=get_dynamic_param(ab("swing_i2i_kess_name", "grpc_fake_server")),
               timeout_ms=get_dynamic_param(ab("swing_i2i_timeout", 50)),
               send_common_attrs_in_request=False,
               send_common_attrs=[
                 "bucket",
                 {"name": "swing_int_trigger", "as": "trigger_list"},
                 {"name": "swing_i2i_retr_num", "as": "request_num"}
               ],
               reason=2201,
             ) \
           .end_if_()
       ```

  4. ab 参数配置
     * 参数配置：
        - 默认 biz_name 设置方式为：在 server_static.flags 中
          ```
          --abtest_biz_name=RECO
          ```
        - 设置 ab suffix 方式: 提供接口 ```DataManager.set_ab_suffix(PRIORITIZED_SUFFIX)```
