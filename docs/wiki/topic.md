<div class="topic">

# 专题介绍
## Dragonfly 数据模型
Dragonfly 框架底层数据存储和基本概念的介绍；item_table 的介绍和 table 间数据运算的技巧；subflow 与 flow 数据传递机制的简介；算子依赖推导的原理介绍。

[查看完整内容](https://docs.corp.kuaishou.com/d/home/<USER>

## Dragonfly 线程模型
Dragonfly 框架底层线程管理介绍；NUMA aware 优化介绍；图引擎 DAG 调度算法详解；异步算子实现细节，包括 subflow 算子、RPC/Redis 异步算子，以及异步各个阶段耗时的含义。
[查看完整内容](https://kstack.corp.kuaishou.com/article/12442)

## FormulaOne 公式引擎
在迭代公式计算的过程中，你是否遇到过上线效率低、理解和编写代码不便或是需要反复实现一些常用逻辑等问题？此时，你需要的可能只是一套好用的在线公式计算引擎。快来看看 FormulaOne 是如何加速公式迭代周期至分钟级的！

[查看完整内容](https://kstack.corp.kuaishou.com/article/8158)


## Dragonfly Python UDF：替代 Lua 的高性能 Python 算子
在需要对 attr 进行 enrich 操作时，你是否对 Lua 的高耗时与 C++ 的难编写感到苦恼？ enrich_attr_by_py 可以帮助到你，用你所熟悉的 Python 编写特征处理逻辑，且以 C++ 的速度运行，兼顾性能与灵活性，快来看看吧！

[查看完整内容](https://kstack.corp.kuaishou.com/article/10539)

</div>
