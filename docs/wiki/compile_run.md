# 安装与运行

?> 推荐使用 Python 3.10 或更高版本进行 DSL 开发，DSL build 速度比 Python 3.6 快 30%

?> 推荐使用 vscode 进行 dragonfly 配置的编写，并安装 [dragonfly-copilot 插件](https://kstack.corp.kuaishou.com/article/6262)以获得智能提示、自动补全、文档说明等便捷功能。如果使用云开发机开发，可以参考 [vscode ssh 远程开发](https://docs.corp.kuaishou.com/k/home/<USER>/fcACZHLUiK3zZXrQd_HRNQXPj) 进行远程开发配置。如果需要开发 C++ 代码，可以参考 [vscode c++ 使用 clangd 插件教程](https://docs.corp.kuaishou.com/d/home/<USER>

## 配置示例

通过以下的 dragonfly 脚本生成对应的 dynamic_json_config.json 配置文件，dragonfly 中提供的方法基本一一对应某个 processor 的调用行为，配置格式也保持一致：

**dragonfly_demo.py**
```python
import os
from dragonfly.common_leaf_dsl import LeafService, LeafFlow

flow = LeafFlow(name="test") \
    .fake_retrieve(num=5, reason=999) \
    .gen_random_item_attr(
      attr_name = "random_int_attr",
      attr_type = "int"
    ) \
    .pack_item_attr(
      item_source = {
        "reco_results": True,
      },
      mappings = [{
        "from_item_attr": "random_int_attr",
        "to_common_attr": "random_common_attr",
      }]
    )
 
current_folder = os.path.dirname(os.path.abspath(__file__))
service = LeafService(kess_name="grpc_demoCommonLeaf")
# 通过 add_leaf_flows 方法将一组 leaf flow 绑定到对应的 request_type 下，as_default 可设置其为默认匹配的 request_type（全缺省则使用第一个 add_leaf_flows 作为 default）
service.add_leaf_flows(leaf_flows=[flow], request_type="default", as_default=True) \
       .build(output_file=os.path.join(current_folder, "dragonfly_demo.json"))
```


## 如何引入 Dragonfly
<font color=Green><b>方式一（推荐）：</b></font>适合有 [dragon](https://git.corp.kuaishou.com/reco-cpp/dragon/) 仓库权限的同学，直接引用源代码，能够随着 git pull 自动获取到最新版本：

```python
# 修改 PYTHONPATH, 注意：下面的 {DRAGON_GIT_ROOT_PATH} 需改为你自己本地 dragon git 仓库的根目录路径！！！
export PYTHONPATH={DRAGON_GIT_ROOT_PATH}:$PYTHONPATH
```
使用 vscode 的同学可参考这篇帖子设置：[VS Code Python 自定义路径设置](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=450799034)

**dragonfly_config.py**
```python
# 引入 dragonfly 相关的模块
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
```

**确认引入的库地址是否正确**
```bash
$ python3
>>> import dragonfly
>>> print(dragonfly.__path__)
# {DRAGON_GIT_ROOT_PATH} 是你自己本地 dragon git 仓库的路径
['{DRAGON_GIT_ROOT_PATH}/dragonfly']
```

**方式二：**仅针对没有 dragon 仓库权限的同学（有权限的同学请用方式一），需要依赖公司的 pypi 源，版本发布不及时，有需要请联系 @fangjianbing 发布，或参考这个 [MR](https://ksurl.cn/OA6HEpq2) 自助升级版本号，核准合入后将自动触发发版。

**安装 dragonfly 库**
```bash
# 安装
pip3 install -i https://pypi.corp.kuaishou.com/kuaishou/prod/+simple/ dragonfly
# 升级
pip3 install --upgrade -i https://pypi.corp.kuaishou.com/kuaishou/prod/+simple/ dragonfly
```
**dragonfly_config.py**
```python
# 引入 dragonfly 相关的模块
from dragonfly.common_leaf_dsl import LeafService, LeafFlow, IndexSource
```

**确认引入的库地址是否正确**
```bash
$ python3
>>> import dragonfly
>>> print(dragonfly.__path__)
# 期望输出 site-packages 下的路径
['/usr/local/lib/python3.6/site-packages/dragonfly']
```

## 在线运行配置

Dragonfly 提供 python 接口加载 C++ 模块并运行 LeafFlow 配置，<font color=Blue>可直接在线测试配置执行效果，无需服务部署！</font>

在线运行工具：[Dragonfly Online Playground](https://dragonfly.corp.kuaishou.com/playground)

<img width="900" src="image/dragonfly-playground.png" alt="dragonfly-playground">


## 离线运行配置

### 云开发机环境

1. 在 dragon 代码库下编译 .so 文件

``` bash
# 仅编译 pywrap.so 文件
export DRAGON_BINARY_TARGET="pywrap"
kbuild build --base_tag=HEAD_GCC10_LLD dragon/server/
```
编译完后请通过 `ll` 命令确认 dragon/dragonfly/common_reco_pipeline_executor_pywrap.so 软链**指向的目标文件**确实存在。

2. 设置运行时依赖的动态库

``` bash
# 如果是 gcc10 编译则将路径换为 ./build_tools/gcc-10.3.0/lib64/libstdc++.so.6
export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 将 YOUR_MKL_LIB_PATH 修改为你自己本地的 mkl_lib 目录路径，譬如 ~/mkl_lib
# 本地若没有 mkl_lib 可自行下载解压：wget http://download.corp.kuaishou.com/reco/mkl_lib.tar.gz
export LD_LIBRARY_PATH={YOUR_MKL_LIB_PATH}
```

3. 配置 PYTHONPATH

``` bash
# YOUR_DRAGON_REPO_ROOT_PATH 修改为你自己 dragon 代码仓库的路径，譬如 ~/repo/ks/dragon
export PYTHONPATH=YOUR_DRAGON_REPO_ROOT_PATH:${PYTHONPATH}
```

4. 运行自己的 py 脚本（或者尝试下面这个示例）

``` python
from dragonfly.common_leaf_dsl import LeafService, LeafFlow

flow = LeafFlow(name="test") \
    .fake_retrieve(num=5, reason=666) \
    .debug_log()

service = LeafService(kess_name="grpc_CommonLeafTest")
service.add_leaf_flows(leaf_flows=[flow])
leaf = service.executor()
leaf.run("test")
```


### ~~生产机环境~~ (已废弃)

某些特殊情况下需要访问在线资源，又无法在 playground 上运行，比如某些 processor 的初始化时间太长，或者你想在本地交互式地调试，可以离线执行配置。

从 [这里](https://halo.corp.kuaishou.com/devcloud/product/version/5023) 或 [这里](https://halo.corp.kuaishou.com/devcloud/product/version/5098)（包含更多 processor，可能需要申请 halo 上 /kuaishou/reco/model 节点的 viewer 权限），下载最新的安装包，解压，执行如下命令

```bash
$ source path/to/pkg/activate
mio tensorflow enabled
```

之后可以像 Playground 一样运行配置。

如果需要自己编译代码更新，可以执行以下编译命令后上传本地编译好的 dragonfly 依赖库

```bash
export ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true
kbuild dragon/server/BUILD
rsync -avzL dragon/dragonfly path/to/pkg/python/
```