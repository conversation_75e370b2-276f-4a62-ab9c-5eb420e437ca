# FAQ

## Q: 能不能让 processor 只对结果集的某个子集做操作

可以的，有多个 processor 通用配置可以按不同的方式筛选一个 item list 子集来进行处理：
- range_start
- range_end
- target_item
- target_item_type
- target_reason
- select_item

详细用法和介绍可参见 [Processor 通用配置](https://dragonfly.corp.kuaishou.com/#/wiki/processor_config?id=通用配置)。

## Q: 为什么在 log_debug_info 里配置了打印 score 属性打出来却全是 NULL

score 是 item 的一个元信息，并未作为普通的 attr 存在 context 中，而是作为 CommonRecoResult 结构体的一个成员变量（reason 也是另外一个元信息），正因为 context 中没有一个名为 "score" 的 attr，所以才会打印出 NULL。如果想把 score 和 reason 元数据也在 context 中用 attr 保存一份另做他用，可通过 copy_item_meta_info 中的 save_score_to_attr 和 save_reason_to_attr 配置项来达到目的。

## Q: 如何将 item 的各个 item_attr 通过 RPC 返回给调用端

可以通过 Dragonfly 中 LeafService 对象的 return_item_attrs 接口添加需要返回的 item_attr 名称列表：

```python
service = LeafService(kess_name="grpc_demoCommonLeaf")
# 注意: return_item_attrs 需在 add_leaf_flows 之前调用
service.return_item_attrs(["photo_id_attr", "author_id_attr", "upload_time_attr", "content_safety_level_attr", "level_attr"])
service.add_leaf_flows(leaf_flows = [flow], request_type="default", as_default=True) \
       .build()
```

被指定返回的这些 item_attr 将被放置在 CommonRecoItem 中的 item_attr 字段（repeated kuiba.SampleAttr 类型）里，你也可以通过[回查页](https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=187942068#id-%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3-CommonRecoLeaf-7.%E5%9B%9E%E6%9F%A5%E9%A1%B5LeafStudio)查看到这些返回的 item_attr 值：


<img width="1000" src="image/find-item-attr.png" alt="find-item-attr">


## Q: 如何快速 debug attr 的信息？
你还在用 log_debug_info() 和 perflog_attr_value() 来 debug attr 的信息吗，需要额外写一整个 processor 呢，还得写上想看的 common/item attr 名字，用 debug_log 和 perf_output 这两个通用配置可以直接把某个 processor 涉及到的输入输出 common/item attr 进行打印和上报，可以节省不少 debug 时间。

例如：原来对于这样的 enrich_attr_by_lua() 想打印 "common2" 和 "item2" 得加 log_debug_info，现在加上配置 `debug_log=True` 就行。
```python
flow = LeafFlow(name="test_perf") \
  .enrich_attr_by_lua(
    import_common_attr = ["common1"],
    export_common_attr = ["common2"],
    import_item_attr = ["item1"],
    export_item_attr = ["item2"],
    function_for_common = "calculate",
    function_for_item = "calc",
    lua_script = '''
      function calculate()
        return common1
      end
      function calc()
        return item1
      end
    ''', 
    debug_log=True,
  )\
```

<img width="800" src="image/debug_log.png" alt="debug_log">
