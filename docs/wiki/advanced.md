# 高阶功能

## 算子自动合并
框架支持 DSL 编译阶段将相同作用域内的同类型算子按照自定义规则进行合并去重，合并去重后的配置被最前面的算子继承，其他算子被删除。业务侧可以使用该功能将多次请求合并成一次，从而节约系统资源。例如将多次 redis 请求合并成一次请求、将搜索 Profile 平台的多次请求合并成一次请求等等。

以 `SearchProfileEnricher` 算子为例，算子支持合并去重需要重写如下两个接口 `enable_auto_merge` 和 `auto_merge_config` 并设置 `LeafService.ENABLE_PROCESSOR_AUTO_MERGE=True
`

```python
class SearchProfileEnricher(LeafEnricher):

  @property
  @strict_types
  def enable_auto_merge(self) -> bool:
    """
    表明该类型的算子是否支持自动合并去重，默认为 False，
    业务算子如果想支持自动合并去重能力，需要重写此接口并返回 True
    """
    return True
  
  @strict_types
  def auto_merge_config(self, other_config: dict) -> bool:
    """
    如果算子支持了自动合并去重，框架会自动调用此接口将其他同类型算子的配置与当
    前算子进行合并去重，合并结果需要放在当前算子 self._config 的配置中

    返回值：
     - True 表示合并成功，框架会删除被合并的算子
     - False 表示无法合并，框架层对算子不做任何变动
    """

    for field in ["biz", "profile_type", "input_source_type", "input_attr_name"]:
      if self._config[field] != other_config[field]: 
        return False
    # 将两个 list 进行合并去重，结果放到当前算子中
    features_lhs = self._config["features"]
    features_rhs = other_config["features"]
    merged_list = features_lhs + features_rhs
    merged_set = set(tuple(sorted(d.items())) for d in merged_list)
    merged_list_without_duplicates = [dict(t) for t in merged_set]
    self._config["features"] = merged_list_without_duplicates
    return True
```

## 算子输出结果缓存

对算子产出的 common attr、item attr、retrieve items 数据进行全局缓存，如果当前请求的 cache key 命中缓存，则不再实际执行算子逻辑，而是直接取用缓存结果，再进行后续流程处理。

鉴于用户的请求具备连续性、热点数据等情况，对实时性要求不高但是 IO 或计算密集型的算子，利用该缓存功能能够有效降低服务时延或 CPU 资源。

需要注意的是实际缓存效果和使用场景有关，如果有些场景本身的局部性效果很差，或者缓存轻量级的算子都不会有较好的结果。

相关 gflags:
```ini
#
# attr 缓存相关配置
#

# 服务是否开启 Enricher 算子输出的 attr 缓存功能，默认值：false
--enable_processor_output_attr_cache=true
# 本地 attr 缓存 key 容量，默认值：10000
--processor_output_cache_capacity_size=100000
# 本地 attr 缓存时间，默认值：300 秒
--processor_output_cache_expire_seconds=300
# 缓存 attr 延迟删除时间，默认值：10 秒。一般情况下不需要关注
--processor_output_cache_delay_delete_seconds=10
# attr 写缓存事件的异步队列长度，默认值 10000。一般情况下不需要关注
--processor_output_cache_task_queue_size=10000

#
# retrieve 缓存相关配置
#

# 服务是否开启 Retriever 算子输出的 retrieve items 缓存功能，默认值：false
--enable_processor_retrieve_item_cache=true
# 本地 retrieve 缓存 key 容量，默认值：10000
--processor_retrieve_cache_capacity_size=100000
# 本地 retrieve 缓存时间，默认值：300 秒
--processor_retrieve_cache_expire_seconds=300
# 缓存 retrieve 延迟删除时间，默认值：10 秒。一般情况下不需要关注
--processor_retrieve_cache_delay_delete_seconds=10
# retrieve 写缓存事件的异步队列长度，默认值 10000。一般情况下不需要关注
--processor_retrieve_cache_task_queue_size=10000
```

用法1：缓存单个 Enricher 算子的 attr 结果
```python
flow.get_common_attr_by_sample_list(
    cache_outputs={
        "enable": True,
        "common_cache_key": "user_id",
    },
    kess_service="grpc_feasuryProxy",
    include_attrs=[
        "uStandardMerchantBuyItemIdList",
        "uMerchantBuyerStatList",
        "uMerchantBuyItemCate1IdList",
        "uMerchantBuyItemCate2IdList",
        "uMerchantBuyItemCate3IdList",
    ],
    user_id="{{user_id}}}",
    device_id="{{device_id}}",
    attr_config="Test",
    timeout_ms=20,
)
```

用法2：缓存一组算子的最终输出 attr 结果（通过 enrich_by_sub_flow 实现）
```python
subflow \
    .build_protobuf(
        class_name="kuaishou::relation::GetPersonListRequest",
        inputs=[
            {"common_attr": "user_id", "path": "user_id"},
        ],
        output_common_attr="_uFollowList_req_",
    ) \
    .enrich_by_generic_grpc(
        cache_outputs={
            "enable": True,
            "common_cache_key": key,
        },
        kess_service=self.__kess_service,
        timeout_ms=self.__timeout_ms,
        method_name="/kuaishou.relation.RelationRpc/GetFollowingList",
        request_attr="_uFollowList_req_",
        response_attr="_uFollowList_resp_",
        response_class="kuaishou::relation::GetPersonListResponse",
    ) \
    .enrich_with_protobuf(
        from_extra_var="_uFollowList_resp_",
        attrs=[
            dict(name="uFollowList", path="person_id"),
        ]
    )

main_flow \
    enrich_by_sub_flow(
        sub_flow=subflow,
        cache_outputs={
            "enable": True,
            "common_cache_key": "user_id"
        }
    )
```

用法3：缓存单个 Retriever 算子的 retrieve 结果
```python
flow.delegate_retrieve(
     kess_service = 'xxx',
     recv_item_attrs = [{'name':'recall_score', 'as': 'recall_score'}],
     timeout_ms = 50,
     cache_outputs = {
        "enable": True,
        "retrieve_cache_key": "user_id"
     }
 ) \
```

详细配置说明可参见通用配置 [cache_outputs](https://dragonfly.corp.kuaishou.com/#/wiki/processor_config?id=cache_outputs)

## Flow 循环执行
框架支持循环执行 Flow，适用于一些需要复用 flow 处理不同数据的场景，例如针对不同的 userid 运行相同的 flow 处理流程，当前有两种模式

### 按退出信号循环

模拟 do-while 循环结构, Flow 至少会执行一次, 并且支持提前退出

LeafFlow 的参数配置：

- `loop_if`: [string] 必填项，Common Attr的名称, 类型为int, 控制是否提前结束循环，flow 每次执行完成后会检测 `loop_if` 的值, 当 `loop_if` 为 0 或不存在时，循环结束执行。备注：通常由 flow 内部改变 `loop_if` 的值来控制提前退出循环

- `loop_limit`: [int] 必填项，循环次数限制, 必须大于 0

🎯 [Try in Playground](http://ksurl.cn/pdPudNHH)
```python
from dragonfly.common_leaf_dsl import LeafService, LeafFlow

# 最多循环 10 次，当 loop_if 为 0 时，提前结束循环。通常由 flow 内部改变 loop_if 的值来控制提前退出循环
flow = LeafFlow(name="test", loop_if="break_loop", loop_limit = 10) \
    .enrich_attr_by_lua(
        import_common_attr = ["id"],
        export_common_attr = ["shard_id", "break_loop"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            return id % 10, util.Random() > 0.2
          end
        """,
        debug_log = True
    )
    
service = LeafService(kess_name="grpc_CommonLeafTest")
service.add_leaf_flows(leaf_flows=[flow], request_type="default")

leaf = service.executor()
leaf["id"] = 16
leaf.run("test")
```

### 按 list 类型 common attr 数据循环

模拟 for 循环结构，对某个 common attr list 进行循环遍历。该模式下，每次循环时把当前元素的值和下标设置到指定的 Common Attr 供 flow 内部使用, 具体循环次数由 `loop_on` 中的元素个数、 `loop_if` 、`loop_limit` 共同决定

LeafFlow 的参数配置：

- `loop_on`: [str] 必配项，用于循环的 Common Attr 名称，目前仅支持 int_list | double_list | string_list 类型数据

- `loop_value`: [str] 必配项，当前循环指向元素的 Common Attr 名称，具体类型与 `loop_on` 相关，例如 `loop_on` 为 `int_list` 时，`loop_value` 为 `int` 类型

- `loop_index`: [str] 选配项，存储当前循环指向元素的 index 的 int 类型 Common Attr 名称，index 从 0 开始依次递增

- `loop_if`: [str] 选配项，指定一个 Common Attr 的名称作为循环退出信号，类型为 int，flow 每轮循环执行完后会检测该 attr 值, 当为 0 或不存在时，循环结束执行，否则继续循环。备注：通常由 flow 内部改变 `loop_if` 的值来控制提前退出循环。

- `loop_limit`: [int] 选配项，循环次数限制, 大于 0 时才生效, 默认为 0，即不限制最大循环次数

🎯 [Try in Playground](http://ksurl.cn/HDyGleDe)
```python
from dragonfly.common_leaf_dsl import LeafService, LeafFlow

# flow 最多执行3次
flow = LeafFlow(name="test", loop_on="id_list", loop_index="id_index", loop_value="id", loop_if="break_loop", loop_limit = 3) \
    .enrich_attr_by_lua(
        import_common_attr = ["id", "id_index"],
        export_common_attr = ["shard_id", "break_loop"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            return id % 10 + id_index, util.Random() > 0.2
          end
        """,
        debug_log = True
    )
    
service = LeafService(kess_name="grpc_CommonLeafTest")
service.add_leaf_flows(leaf_flows=[flow], request_type="default")

leaf = service.executor()
leaf["id_list"] = [10, 12, 13, 14]
leaf.run("test")
```

### @for_loop 装饰器

以上循环能力也可以通过 `@for_loop` 装饰器来达到同样的效果，使用更便捷：

🎯 [Try in Playground](http://ksurl.cn/LHWHFQ4E)
```python
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.decorators import for_loop

@for_loop(loop_on="id_list", loop_index="id_index", loop_value="id", loop_if="break_loop", loop_limit=3)
def loop(flow):
    flow.enrich_attr_by_lua(
        import_common_attr = ["id", "id_index"],
        export_common_attr = ["shard_id", "break_loop"],
        function_for_common = "calc",
        lua_script = """
          function calc()
            return id % 10 + id_index, util.Random() > 0.2
          end
        """,
        debug_log = True
    )

flow = LeafFlow(name="test")
loop(flow)

service = LeafService(kess_name="grpc_CommonLeafTest")
service.add_leaf_flows(leaf_flows=[flow], request_type="default")

leaf = service.executor()
leaf["id_list"] = [10, 12, 13, 14]
leaf.run("test")
```
