# 周边工具

Dragonfly 受欢迎不仅仅是因为其高性能的服务，也是因为 Dragonfly 拥有非常丰富的周边生态支持，能够帮助其开发者更好更快更舒适的开发自己的推荐服务。

## 周边 Debug 工具

| 名字 | 说明 | 链接 |
| ------- | ------- | ------- |
| Dragonfly Wiki | 就是你所看到的这个网站，通过这个网站，你可以知道 Dragonfly 的一切. | https://dragonfly.corp.kuaishou.com |
| Dragonfly Kstack | 类似 stackoverflow，关于 Dragonfly 的一切问题都可以在此网站提问，一般会在 24h 内收到回复. | https://kstack.corp.kuaishou.com/group/92	 |
| 推荐云 KRP | 一站式推荐服务网站，能够在该网站轻松完成推荐系统的部署和上下线，完成推荐系统的搭建和维护相关工作. | https://krp.corp.kuaishou.com/	 |
| Grafana 监控 | 目前已有的监控数据包括：服务可用性（上下游）、请求计数、召回计数、总体耗时、pipeline 耗时等数据。s | https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong |
| 先知 [历史回查](https://xianzhi.corp.kuaishou.com/new_reco/leaf-studio-list?activeTab=history_review) 和 [模拟请求](https://ksurl.cn/mcHXS4ir) | 一站式推荐问题定位平台，先知平台专注于排查定位快手各业务推荐问题。以各团队的推荐策略为基础，在用户和作品层面纵向拓展，提供Bad Case定位、策略调优和体验评估等能力，助力各推荐团队改善用户体验，持续提升核心业务指标. | 可参照 [CommonLeaf：如何接入先知平台](https://docs.corp.kuaishou.com/d/home/<USER>//xianzhi.corp.kuaishou.com/ |
| Playground | Dragonfly 在线调试工具，能够实时测试自己的推荐代码，提高研发效率. | https://dragonfly.corp.kuaishou.com/playground |
| DragonflyViz | DAG 可视化工具，在该页面能够轻松生成当前配置的 DAG 流程图，清晰地了解整体逻辑脉络，以及各个 processor 之间的并行调度关系。 | https://dragonfly-viz.corp.kuaishou.com/ |
| Dragonfly Copilot | vscode 插件，提供算子文档、配置提示、配置纠错、代码补全、AB参数预览、问答机器人等开发辅助功能 | [安装使用说明](https://docs.corp.kuaishou.com/d/home/<USER>
| KRP-debug | 部分模块的 Debug 网站，目前含有 Ann，Common Index，有序倒排，Rodis，Router 的工具的 debug 网站 | https://krp-debug.corp.kuaishou.com/	 |
| Ktrace | 可视化反应内部 Processor 清晰的调用链和拓扑结构的工具，能够非常明确的定位到单次请求的延迟瓶颈的位置. | https://ktrace.corp.kuaishou.com/dragon <br> 如何快速接入：https://kstack.corp.kuaishou.com/article/4295 |
| Syslab |  支持生成服务火焰图和堆栈信息，能够快速定位 CPU，内存 异常问题，例如 内存泄漏 等情况. | https://syslab.corp.kuaishou.com/ |

## DAG 可视化

>本地需安装 graphviz 库，安装命令: pip install graphviz

只需给 LeafService 额外增加一个 <font color=Blue><b>.draw()</b></font> 方法调用即可为生成当前配置的 DAG 流程图，清晰地了解整体逻辑脉络，以及各个 processor 之间的并行调度关系。

**draw()** 方法额外参数配置：

* **dag_folder**: [string] DAG 图输出文件夹的绝对路径，默认为当前执行路径
* **dag_format**: [string] DAG 图的文件格式类型, 可选值: gv/pdf/svg/png/..., 默认为 svg。更多的可选格式可参照[这里](https://graphviz.org/doc/info/output.html)
* **request_type**: [string | list] 需要绘制 DAG 图的 request_type 列表，可为单值或 list，默认全绘制（每个 request_type 生成一个 DAG 图）
* **keep_gv_file**: [bool] 除了生成指定格式的图文件之外，是否同时生成一份 .gv 文件，默认为 False

示例：
```python
import os
current_folder = os.path.dirname(os.path.abspath(__file__))
service = LeafService(kess_name="grpc_demoCommonLeaf")
service.add_leaf_flows(leaf_flows=[flow], request_type="default", as_default=True) \
       .draw(dag_folder=current_folder) \
       .build(output_file=os.path.join(current_folder, "dragonfly_demo.json"))
```
执行该脚本后将在指定目录下为各个 request_type 生成相应的流程图文件：DAG-{service_name}-{request_type}.gv.{gv|pdf|svg|png|...}

若设置了 keep_gv_file=True 将还有额外的 gv 文件：DAG-{service_name}-{request_type}.gv

在 vscode 里的预览效果如下（建议安装 [Svg Preview](https://marketplace.visualstudio.com/items?itemName=SimonSiefke.svg-preview) 或 [Graphviz Preview](https://marketplace.visualstudio.com/items?itemName=EFanZh.graphviz-preview) 或 [vscode-pdf](https://marketplace.visualstudio.com/items?itemName=tomoki1207.pdf) 插件以支持在 vscode 中远程预览开发机上的 DAG 文件）：

<img width="900" src="image/dag-preview.png" alt="dag-preview">