<div class="updates">

# Monthly Updates

## 2024.10
- enrich_attr_by_py 添加新 API，支持 runner 等服务类型，支持 Mac 开发，无缝接入，轻松部署！
- 算子通用配置 select_item 支持 range 比较
- delegate_enrich 支持以只读状态发送 common attr 防止下游修改
- @async_xxx 装饰器支持继承 flow 的 namespace
- Formula One 新增 4 个分桶 udf
- truncate_by_attr 算子的 limit 和 ratio 配置项升级为动态参数

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB9raWBr-5OBtHfR0jhKvgt)


## 2024.09
- 新增算子熔断摘除能力
- retrieve_by_sub_flow 支持传入主 flow 的 item 结果集
- 新增 @for_loop 装饰器进行循环控制
- FormulaOne 新增 udf QUANTILE_IF
- lookup_kconf 支持从 list 类型的 common attr 取 lookup_attr
- delegate_enrich 算子支持按指定 attr 值进行一致性 hash 分发
- export_attr_to_kafka 支持指定 kafka 的 user_params 参数

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcABUI9JYAmuSrg1WEv0QmkJO)


## 2024.08
- Retriever 算子支持缓存召回结果
- filter_by_common_attr 算子新增 cancel_num/pardon_num 配置
- enrich_attr_by_lua 算子支持 GeoHash
- dispatch_common_attr 算子支持分发 list 数据
- light function 新增 SetPtrCommonAttr 接口
- 支持查询 attr 被哪些算子生成或使用
- wiki 增加并行初始化相关 gflags 的使用说明
- 返回上游的 return attr 配置支持重命名
- enrich_attr_by_py 算子新增导入导出 attr 重命名及其他新功能接口
- 可视化耗时图功能升级：支持算子序优化建议、关键路径、算子搜索、局部缩放、连线优化

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcABhcMFWZyfAgI6_XDYIngv5)


## 2024.07
- klaunch 支持直播 leaf 和召回中心联调部署能力
- klaunch 新增自助排查建议功能
- 支持 redis 算子异步模式 DATA_READY 耗时展示
- delegate_retrieve 算子增加一致性哈希支持
- 异步算子的降级结果支持记录到指定 common attr
- select_list_values 支持输出单值
- 支持 server 端 bidirectional streaming rpc 模式（For 大模型对话场景）
- playground 支持超长脚本的分享链接
- Formula One debug 信息支持输出到 stdout

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADlKPLGS2Hc8MVyMb2yNWOj)


## 2024.06
- enrich_attr_by_py 算子新增 util 工具类支持，欢迎接入使用
- get_common_attr_from_redis 的 key_prefix 支持动态参数
- Formula One 支持筛选 exp_key 和 group_id 
- 先知上报多表内容
- klaunch 支持单条流水线触发单列 老leaf & rank_leaf 仅配置变更测试场景
- klaunch 支持单列指定C6机器测试能力
- observer 类型异步算子 callback 部分从pipleline结束执行切换至请求结束后执行

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAoF-jFcGf6JyKZ_aHHDV_y)


## 2024.05
- sub_flow 支持多级独立线程池执行模式
- 新增 random_stable_shuffle 算子
- 新增 rodis 读写算子
- pack_item_attr_to_item_attr 算子支持对 to_item_attr 中的元素去重
- pack_attr_to_bytes 算子新增 fp16 类型支持
- klaunch 支持本地开发机/mac 执行命令拉取远端测试环境日志文件/目录
- klaunch 容器云环境增加 gdb2，支持使用 coredump 提供的 debug 工具

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcAA2DJdVCL2pWX43-_tmRGfB)


## 2024.04
- get_common_attr_from_redis 支持配置查询 key 前缀
- 异步算子限流熔断策略功能升级
- 提供自建线程的 cpu time 统计工具
- common attr 支持全流程只读模式
- log_debug_info 增加输出标记
- 请求处理计数和业务处理耗时监控增加上游调用筛选
- 升级 attr 读写类型不一致的检查功能
- FormulaOne 新增滑动窗口均值 UDF
- FormulaOne 支持将分值以 int 类型导出
- Klaunch 新增 krp_common_leaf_runner 类型服务测试
- Klaunch 支持业务 MR klaunch 测试卡点

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAAC0qYlCj_XDi6hwFgbu02)


## 2024.03
- log_debug_info 支持打印所有 common attr 的功能
- get_common_attr_from_redis 和 get_item_attr_from_redis 算子支持异步获取数据
- sort 支持指定多字段按优先级排序
- Formula One 支持自定义额外 perf 打点维度
- Enricher 算子支持缓存 item attr
- 支持编写 Python 动态脚本

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcACMwJmqmhpTYLjmHsVIaOhF)


## 2024.02
- 完善 attr 检查逻辑并优化 DSL build 速度
- DSL build 报错信息新增代码定位功能
- filter_by_rule 算子和 select_item 通用配置的 in 和 not in 匹配规则新增对 list 类型数据的支持
- retrieve_by_remote_index 支持分 shard 查询
- 新增 retrieve_by_remote_colossusdb_index 算子

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADwr-_Fks_QwA9W-aOsNeIO)


## 2024.01
- Flow 支持按 list 数据循环执行
- Enricher 算子支持缓存 attr 输出结果
- delegate_retrieve 算子支持请求并解析多 table 数据
- select_item 通用配置支持通过 reason 筛选 item
- pack_item_attr_to_item_attr 算子支持拼接 list 类型的 attr
- enrich_attr_by_lua 算子新增 XXHash 自定义函数
- enrich_attr_by_json 算子支持从 item attr 解析 json
- filter_by_rule 算子 compare_to 升级为动态参数，并新增 compare_to_item_attr 配置
- enrich_by_sub_flow 算子支持将 subflow 中所有 item 的指定 attr 都 merge 回主 flow

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADoSSv-4A-gma7yX9auqafw)


## 2023.12
- 新增 else_if_() 分支语法支持
- 通用配置 select_item 支持限制最大选取数目
- 支持在 InitProcessor() 方法内通过 GetName() 获取算子名称
- 新增算子自动合并功能
- filter_by_rule 算子支持忽略非法的 rule
- calc_by_formula1 算子支持导出数据为 CommonAttr
- FormulaOne 新增 vector 数据及临时变量支持
- FormulaOne 新增随机数 UDF
- vscode 插件 Dragonfly Copilot 新增 Light Function 脚手架

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADDzS3O93LTRDYivR0zzuQY)

## 2023.11
- vscode 插件 Dragonfly Copilot 新增 AB 参数预览功能
- DragonflyViz 可视化新增 DAG 图 diff 展示功能
- Dragonfly Playground 支持 gflag 设置
- 通用配置 select_item 新增 is_null 和 not_null 比较符
- FormulaOne 支持冻结指定公式项不被 AB 实验影响
- FormulaOne 监控报错优化
- FormulaOne 支持分桶功能函数
- sort 算子支持排序但不改变 item score 字段
- 多表算子支持 concat list 类型字段，支持拷贝 extra  类型字段
- log_debug_info 算子增加 caller 信息打印
- 新增 cast_attr_type 算子
- 新增 sub_flow 超时比例监控
- 支持 protobuf 及 rpc_method 动态注册
- 新增对使用动态参数时漏写 {{}} 的格式检查
- Attr 读写接口性能优化
- 新增 perf 采样上报接口

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADL2dYCbC7hF6qvQgxDUSW1)


## 2023.10
- 新增过滤原因上报接口
- Arranger/Observer 类型算子支持 select_item 通用配置
- Main Build 版本支持 numa-aware 优化

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcACa1KffvhYSPlheWY9m-Mwr)

## 2023.09
- 新增 Processor 稳定命名模式
- Processor 耗时监控统计升级
- Dragonfly Copilot vscode 插件全新升级（内测）
- Dragonfly Python 库集成函数式编程模块
- DragonflyViz 可视化新增图 diff 功能
- 实验效率平台新增 FormulaOne 专用管理系统
- FormulaOne 新增分位数 UDF 支持 QUANTILE
- Numa-aware 性能优化
- Processor 支持注册自定义 http 接口

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcADFdmTQfSaC0Q-CM05FxBXB)

## 2023.08
- 新增非阻塞异步处理支持
- FormulaOne 公式配置支持优先级后缀
- calc_by_formula1 算子支持将公式写在本地
- 新增 mark_attr_readonly 算子
- 升级先知 traceback 数据上报逻辑
- 支持自定义 attr 的 debug log 打印内容
- retrieve_by_sub_flow 算子支持对 merge attr 进行数据聚合
- enrich_by_wasm 算子支持 compile_to_wat

[查看完整内容](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAsgn_AaLirAjA8DE0lvd16)

## 2023.07
- 新增多表功能算子
- 新增逻辑表支持
- 算子后台并行初始化
- 新增通用配置 select_item
- 新增 lookup_kconf 算子
- debug log 支持打印当前所有 item attr
- get_abtest_params 算子升级
- calc_by_formula1 算子易用性升级
- delegate_enrich 算子支持一致性哈希的请求发送模式

[查看完整内容](https://docs.corp.kuaishou.com/d/home/<USER>

</div>