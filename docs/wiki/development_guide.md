# 开发指南
## DSL 开发

### Processor 介绍
Processor 是 leaf 工作流中的最基础单元，各个 Processor 按照在 pipeline 中定义的顺序
<font color=Red>依次串行执行</font>。这意味着：先执行的同步 Retriever 召回结果一定会排在后执行的同步 Retriever 召回结果之前。

<font color=Blue>所有 Processor 都是围绕 <b>RecoResults</b>（一个存储 CommonRecoResult 结构的数组）和 Context 中的 <b>CommonAttr / ItemAttr</b>（kv 结构的存储介质）进行交互的（读取或写入数据），每个 Processor 所需的数据输入/输出源（包括但不限于 Context、索引、abtest、及其它外部 gRPC 服务）强烈建议只有一种！否则该 Processor 大概率需要拆分。</font>


中台 leaf 已经内置了许多开箱即用的 Processor，能够满足绝大部分的推荐场景需求：

- 对于大部分初级业务方：只需了解这些 Processor 能完成哪些功能、如何进行配置，无需关心 Processor 内部实现，更不需要进行 Processor 的代码开发。
- 对于小部分高级业务方：如需涉及新功能的 Processor 开发，请务必联系方剑冰对功能确认后再开始编码工作，避免无谓的重复劳动。


#### Processor 角色分类

**为什么要对 Processor 进行角色分类？**

通过角色分类来合理约束 Processor 的行为边界，使得整个 pipeline 行为可控，提高使用者对 Processor 的 confidence，并且方便对异常行为定位的快速推断。

<table>
  <thead>
    <tr>
      <th rowspan="2">角色</th>
      <th rowspan="2">功能定位</th>
      <th rowspan="2">应用场景</th>
      <th colspan="2" align="center">Context 操作</th>
      <th colspan="4" align="center">item 结果集操作</th>
    </tr>
    <tr>
      <th align="center">读</th>
      <th align="center">写</th>
      <th align="center">遍历</th>
      <th align="center">增加</th>
      <th align="center">修改</th>
      <th align="center">删除</th>
    </tr>
  </thead>
<tbody>
  <tr>
    <td>Retriever</td>
    <td>对结果集追加 item</td>
    <td>触发源召回 item</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">❌</td>
  </tr>
  <tr>
    <td>Enricher</td>
    <td>丰富 Context 内容</td>
    <td>增加 CommonAttr / ItemAttr 数据</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">❌</td>
    <td align="center">❌</td>
  </tr>
  <tr>
    <td>Arranger</td>
    <td>对结果集基于某些规则进行编排</td>
    <td>打分、排序、过滤、截断、打散</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
  </tr>
  <tr>
    <td>Observer</td>
    <td>观察 leaf 内部状态，不会对结果集造成任何影响</td>
    <td>打印或输出 Attr 类调试信息，给样本拼接服务发送 leaf show</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">✅</td>
    <td align="center">❌</td>
    <td align="center">❌</td>
    <td align="center">❌</td>
  </tr>
  <tr>
    <td>Mixer</td>
    <td>对多个 table 进行结果集追加、编排或字段增改</td>
    <td>合并两个 table 的结果集，聚合、拼接两个 table 的数据产出一个新 table</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
    <td align="center">✅</td>
  </tr>
</tbody></table>

#### 常用 Processor 

##### 触发源召回

此类 Processor 名称均以 "Retriever" 结尾，一般在整个 pipeline 的第一个阶段执行，为后续 Processor 填充可操作的候选结果集。

|  | Processor | 触发源 | 功能说明 | 
| ------ | ------ | ------ | ------ |
| 1 | [retrieve_by_remote_index()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_remote_index) | 通用索引/有序倒排服务中召回 | 从别的通用索引/有序倒排服务中进行 item 召回 | 
| 2 | [retrieve_by_common_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_common_attr) | CommonRecoRequest | 通过 int_list 类型 CommonAttr 从 request 中传入一组指定的 item 进行召回 | 
| 3 | [retrieve_by_ann_embedding()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_ann_embedding) | Embedding 触发服务（ANN） |从通用 Embedding 触发服务召回相似 item | 
| 4 | [retrieve_by_model()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_model) | Model Retrieval 服务 | 从 Model Retrieval 服务召回 item|
| 5 | [retrieve_by_explore_rpc()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_explore_rpc) | 主站发现页 Leaf 服务	| 从主站的发现页 Leaf 服务获取视频作为召回 item |
| 6 | [retrieve_by_sub_flow()](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_sub_flow) | 本地pipeline召回 | 异步执行 leaf 本地的一条 pipeline，召回 item |
| 7 | [delegate_retrieve()](https://dragonfly.corp.kuaishou.com/#/api/common?id=delegate_retrieve) | 其他leaf服务 | 调用异地部署的 common_leaf 获取召回 item |

##### Attr 外部数据注入

此类 Processor 名称一般以 "Enricher" 结尾，一般在各个 Retriever 之后执行，从外部服务模块获取相关属性数据，为后续 Arranger 类 Processor 提供必要的数据支持。


|    | Processor                                  | 外部输入数据源 | 输出 Attr 类型 | 功能说明                                                                   | 
|----|--------------------------------------------|----------------|----------------|----------------------------------------------------------------------------|
| 1  | [get_item_attr_by_local_index()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_item_attr_by_local_index)       | 本地正排索引   | ItemAttr       | 从 leaf 的本地索引查询 item 的正排属性，并写入 ItemAttr                    | 
| 2  | [common_predict()](https://dragonfly.corp.kuaishou.com/#/api/common?id=common_predict)    | Predict Server | ItemAttr       | 从 kuiba 的通用预估服务获取 item 的 ctr 等预估分数，并写入 ItemAttr        |
| 3  | [get_abtest_params()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_abtest_params)         | abtest 系统    | CommonAttr     | 从 abtest 系统获取指定的参数值，并写入 CommonAttr                          | 
| 4  | [get_item_attr_by_remote_index()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_item_attr_by_remote_index)      | 异地正排索引   | ItemAttr       | 从异地部署的 common_index 索引服务查询 item 正排属性，并写入 ItemAttr      | ItemAttrEnricher | 分布式索引     | ItemAttr       | 从分布式索引查询 item 的 PhotoInfo，并抽取部分属性写入 ItemAttr            | 
| 6  | [get_item_attr_by_predict_fetcher()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_item_attr_by_predict_fetcher)   | 双塔预估服务   | ItemAttr       | 从双塔模型的预估服务获取 item 的 ctr 等预估分数，并写入 ItemAttr           | 
| 7  | [tf_serving_predict()](https://dragonfly.corp.kuaishou.com/#/api/common?id=tf_serving_predict)         | TF 预估服务    | ItemAttr       | 从 kuiba tf_serving 的预估服务获取 item 的 ctr 等预估分数，并写入 ItemAttr |
| 8  | [get_common_attr_by_sample_list()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_common_attr_by_sample_list)     | 样本拼接服务   | CommonAttr     | 从样本拼接服务（SampleList）获取 User 侧属性，并写入 CommonAttr            |
| 9  | [get_kconf_params()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_kconf_params)          | Kconf 配置     | CommonAttr     | 从 Kconf 里获取指定的配置值，并写入 CommonAttr                             |  
| 10 | [get_common_attr_from_redis()](https://dragonfly.corp.kuaishou.com/#/api/common?id=get_common_attr_from_redis)          | Redis Cache    | CommonAttr     | 从redis服务用指定的key获取value，并写入CommonAttr                          | 

##### Attr 内部属性转换

与”Attr 外部数据注入“类似，不同点是数据获取不再通过外部服务，而是对内部已有的 Attr 数据进行各类变换来生成新的 CommonAttr 或 ItemAttr。

> 此类 Processor 灵活度极高，通过组合使用可以解决诸多复杂需求，建议熟练掌握

|  | Processor | 输入Attr类型 | 输出Attr类型 | 功能说明 | 
| ------ | ------ | ------ | ------ | ------ | 
| 1 | [enrich_attr_by_lua()](https://dragonfly.corp.kuaishou.com/#/api/common?id=enrich_attr_by_lua) | ItemAttr/CommonAttr	 | ItemAttr/CommonAttr	 | 读取 CommonAttr 和 ItemAttr，利用 Lua 脚本自定义处理逻辑，并生成新的 ItemAttr 为后续操作（排序、过滤、打散等等）做准备	 |    
| 2 | [pack_item_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=pack_item_attr) | ItemAttr	 | CommonAttr | 把一组 item 的某个 ItemAttr value 值打包拼接成一个 list attr 写入 CommonAttr 中	 |   
| 3 | [transform_item_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=transform_item_attr) | ItemAttr/CommonAttr	| ItemAttr | 通过判定 item 的某个 ItemAttr value 值是否属于某个集合或范围，来生成一个新的 ItemAttr 标记判定结果	 |   
| 4 | [copy_user_meta_info()](https://dragonfly.corp.kuaishou.com/#/api/common?id=copy_user_meta_info) | Request Info	 | CommonAttr | 读取 Request 结构中的 user_id、device_id、request_id、request_type 数据作为 CommonAttr 另存一份，以供其他 Processor 获取处理	 |   
| 5 | [copy_item_meta_info()](https://dragonfly.corp.kuaishou.com/#/api/common?id=copy_item_meta_info) | CommonRecoResult	 | ItemAttr | 将 CommonRecoResult 结构中的 item_key、item_id、item_type、reason、score 数据作为 ItemAttr 另存一份，以供其他 Processor 获取处理	 |   
| 6 | [count_item_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=count_item_attr)	 | ItemAttr/CommonAttr	 | ItemAttr | 统计 item 的某个 ItemAttr value 值在指定集合中出现的次数（集合可为某个 list 类型的 CommonAttr），并将统计次数写入一个新的 ItemAttr 中	 | 
| 7 | [gen_ensemble_seq_num()](https://dragonfly.corp.kuaishou.com/#/api/common?id=gen_ensemble_seq_num) | ItemAttr | ItemAttr | 对指定的一组 ItemAttr 进行 ensemble sort，并将各个 item 排序后所处的序号位置写入新的 ItemAttr 中供后续使用	 |
| 8 | [pack_common_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=pack_common_attr) | CommonAttr | CommonAttr | 把一组 CommonAttr 值 打包拼接成一个 list attr 写入 CommonAttr 中 |
| 9 | [map_item_list_attr](https://dragonfly.corp.kuaishou.com/#/api/common?id=map_item_list_attr) | ItemAttr | ItemAttr | 对某个存储在 int_list 类型 item_attr 的 item 列表进行相关 attr 抽取，并将抽取的 attr list 写入指定的 item_attr 中 |
| 10 | [dispatch_common_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=dispatch_common_attr)	 | CommonAttr | ItemAttr | 把给定的 common_attr 分配到特定的 item_attr |
| 11 | [dispatch_item_attr()](https://dragonfly.corp.kuaishou.com/#/api/common?id=dispatch_item_attr) | ItemAttr | ItemAttr/CommonAttr | 把部分 item 的 attr 按指定的新名称拷贝分发至其它 item 下 |

##### 结果集处理

此类 Processor 名称均以 "Arranger" 结尾，一般会直接影响到推荐结果的直观效果，也是策略配置的核心区域。

|  | Processor | 结果集影响 | 功能说明 | 
| ------ | ------ | ------ | ------ | 
| 1 | [sort()](https://dragonfly.corp.kuaishou.com/#/api/common?id=sort) | 输出数目 == 输入数目 | 按当前分数对结果集进行排序（从高到低非稳定排序） |    
| 2 | [variant()](https://dragonfly.corp.kuaishou.com/#/api/common?id=variant) | 输出数目 == 输入数目	 | 按指定的一个或多个 ItemAttr 对结果集进行打散重排	 |    
| 3 | [deduplicate()](https://dragonfly.corp.kuaishou.com/#/api/common?id=deduplicate) | 输出数目 <= 输入数目	| 按 item_key 对结果集进行去重	|    
| 4 | [filter_by_browse_set()](https://dragonfly.corp.kuaishou.com/#/api/common?id=filter_by_browse_set)	 | 输出数目 <= 输入数目 | 按 request 传入的 browse set 对结果集进行过滤	 |    
| 5 | [limit()](https://dragonfly.corp.kuaishou.com/#/api/common?id=limit) | 输出数目 <= 输入数目	| 按指定数目对结果集进行截断 |   
| 6 | [filter_by_attr](https://dragonfly.corp.kuaishou.com/#/api/common?id=filter_by_attr) | 输出数目 <= 输入数目 | 按某个 ItemAttr 的值对结果集进行过滤 |  
| 7 | [filter_by_common_attr](https://dragonfly.corp.kuaishou.com/#/api/common?id=filter_by_common_attr) | 输出数目 <= 输入数目	 | 按一组 CommonAttr 的值对结果集进行过滤	 | 
| 8 | [force_insert()](https://dragonfly.corp.kuaishou.com/#/api/common?id=force_insert) | 输出数目 == 输入数目 | 根据指定的位置或指定的奖励分，对 item 进行位置强插或位置提升 | 
| 9 | [intermix()](https://dragonfly.corp.kuaishou.com/#/api/common?id=intermix) | 输出数目 == 输入数目 | 根据自定义规则将不同的 item_type 进行交错编排 |
| 10 | [shuffle()](https://dragonfly.corp.kuaishou.com/#/api/common?id=shuffle) | 输出数目 == 输入数目	| 对结果集进行 shuffle 均匀随机打乱顺序 |
| 11 | [rotate()](https://dragonfly.corp.kuaishou.com/#/api/common?id=rotate)	 | 输出数目 == 输入数目	| 对结果集中的 item 进行位置旋转（同 std::rotate 效果） |


##### 辅助工具类
此类 Processor 名称均以 "Observer" 结尾，不会对本次推荐结果造成任何影响。

|  | Processor |  功能说明 | 
| ------ |  ------ | ------ | 
| 1 | [log_debug_info()](https://dragonfly.corp.kuaishou.com/#/api/common?id=log_debug_info)	 | 打印 leaf 的某个切片状态到本地 INFO LOG 中，包括当前的 request 相关信息，abtest 命中信息，指定的 CommonAttr/ItemAttr，以及当前结果集，常用于系统调试	 |    
| 2 | [leaf_show()](https://dragonfly.corp.kuaishou.com/#/api/common?id=leaf_show) | 给样本拼接服务发送 leaf show，用于模型训练	 |  
| 3 | [perflog_reason_count()](https://dragonfly.corp.kuaishou.com/#/api/common?id=perflog_reason_count) | 统计当前各个 reason 的数目，上报 perflog 并可在 grafana 监控中查看统计结果	 |
| 4 | [perflog_attr_value()](https://dragonfly.corp.kuaishou.com/#/api/common?id=perflog_attr_value) |统计当前 double 或 int 类型的 item_attr 均值，上报 perflog 并可在 grafana 监控中查看统计结果	|

### 同步/异步 Processor
>dragonfly 已帮你屏蔽了同步和异步 processor 的使用区别，感兴趣可以了解下底层原理 :-)

同步 processor 很好理解，其逻辑是被同步执行的，执行完后其影响立即可见，例如同步 Retriever 执行完后被召回的 item 一定存在于结果集中。

而异步的 processor 一般都是通过 gRPC 请求外部服务数据，耗时较高，为了最大化并行效果，其执行只是简单的发送 gRPC 请求然后接着执行后续的 processor，并不会阻塞在那等待结果返回，所以异步的 Retriever 执行完后结果集中并没有被召回的 item，而是要等到异步 Retriever 所指定的 downstream_processor 被执行之前，所请求的 gRPC 结果（即召回的 item）才会被处理并加入结果集（若执行到 downstream_processor 之前 gRPC 结果还未返回，则会阻塞等待），其执行过程如下图：

<img width="1000" src="image/async-processor-flow.png" alt="async-processor-flow">


为了让 leaf 得知整个数据链路的依赖关系，从而对异步 Processor 进行自动并行化，所有的异步 Processor 都有 "downstream_processor" 配置项，通过该项为每个异步 processor 指定其返回数据的下游依赖：

**异步 Processor 的 downstream_processor 配置**

``` json
{
  "type_name": "CommonRecoEmbeddingRetriever",
  ...,
  // 将 downstream 指定为 ANOTHER_DEPENDENT_PROCESSOR，同时意味着在 ANOTHER_DEPENDENT_PROCESSOR 之前的
  // Processor 都不需要使用 CommonRecoEmbeddingRetriever 的返回结果
  "downstream_processor": "ANOTHER_DEPENDENT_PROCESSOR"
}
```

<font color=Red> dragonfly 下 downstream 会根据算子出入输出自行推断，强烈建议不要自己特殊指定。如特意指定，会造成不必要异常。</font>

如果用户自行指定，且<font color=Blue>错配</font>了异步 processor 的 "<font color=Blue>downstream_processor</font>" 设置，那么该异步 processor 将会<font color=Blue> 无法正确的执行请求回调部分 </font>！（也就是说异步请求的结果不会在期望的地方执行, 可能会导致 attr 缺失、复写、错写等异常）

### 同步流程开发
``` python
# 书写一个 flow，使得其按顺序执行 processorA(), processorB(), processorC()

# 写法一
flow.processorA() \
		.processorB() \
		.processorC() \

# 写法二
flow.processorA() \
flow.processorB() \
flow.processorC() \

# 写法三
def AddProcessorA(flow):
  return flow.processorA()
def AddProcessorB(flow):
  return flow.processorB()
def AddProcessorC(flow):
  return flow.processorC()
flow = AddProcessorA(flow)
flow = AddProcessorB(flow)
flow = AddProcessorC(flow)

#写法四
class HlgCommonFlow(LeafFlow):
  # 可以打包很多个 Processor 为一个功能模块，增强可读性
  def copy_by_gender(self):
    return self \
      # gender 为一个 CommonAttr，这里的语句会生成一个 lua 来控制分支运行
  		.if_("gender == 'male'") \
  			.copy_user_meta_info(save_user_id_to_attr="male") \
  		.else_() \
  			.copy_user_meta_info(save_user_id_to_attr="female") \
  		.end_if_()
  
flow = HlgCommonFlow(name="test") # 创建的时候选择自己 Class
flow.fake_retrieve() \
		.copy_by_gender() \ # 这里直接调用 Class 内的 method
```
### 异步流程开发
``` python
# retrieve_by_sub_flow 分多路召回
@async_retrieve(timeout_ms=100)
def retrieve_channel_1(flow):
  flow.fake_retrieve(num=2, reason=1)

@async_retrieve(timeout_ms=100)
def retrieve_channel_2(flow):
  flow.fake_retrieve(num=3, reason=2)

@async_retrieve(timeout_ms=100)
def retrieve_channel_3(flow):
  flow.fake_retrieve(num=4, reason=3)

flow = LeafFlow(name="test")
retrieve_channel_1(flow)
retrieve_channel_2(flow)
retrieve_channel_3(flow)
flow.log_debug_info( # 召回结果在 log_debug_info 前返回
  for_debug_request_only=False,
  to="stdout",
  item_attrs=["none"]
)
```

### module 开发
函数式 DSL 在生成配置文件时会丢失算子的业务层级信息，模块化可以便捷得将层级信息保留，并适配到可视化展示等相关工具中。在下面这段代码示例中，通过将 rough rank，predict，ensemble sort 三个函数封装成 module，业务逻辑在可视化上从 processor 串联变为了 rank module 对 predict module，sort module 有层级的调度。
详细介绍：[函数式模块化介绍](https://docs.corp.kuaishou.com/k/home/<USER>/fcADDkGQmBMgu0jTlPKC7HWCt)
``` python
@module()
def rough_rank():
  flow() \
    .do(
      predict(),
      ensemble_sort(),
      boost(),
      variant()
    )
  
@module()
def predict():
  flow() \
    .do(
      kai_predictor(FullPredictConfig),
      kai_predictor(SvrPredictConfig),
      kai_predictor(SimPredictConfig),
      kai_predictor(CascadePredictConfig)
    )

class DemoLeafFlow(LeafFlow, OverseaApiMixin):
  pass

with DemoLeafFlow(name='demo_flow'):
  flow().do(
    rough_rank()
  )

```

## Processor 开发

已有的 Processor 能够满足大部分需求，如需涉及新功能的 Processor 开发，请务必联系 @方剑冰 对功能确认后再开始编码工作，避免无谓的重复劳动。

下文将介绍如何进行具有新功能的 Processor 的开发。

### 环境准备及开发流程

涉及代码拉取、编译环境、部署测试等流程的介绍，详见文档 [Dragonfly 代码开发流程](https://docs.corp.kuaishou.com/d/home/<USER>

### Processor 类结构关系

<img width="1000" src="image/processor-develop.png" alt="processor-develop">

开发者按需继承 CommonRecoBaseEnricher、 CommonRecoBaseRetriever、 CommonRecoBaseArranger 或 CommonRecoBaseObserver 来实现具体的 processor 即可。

对于一个具有新功能的 Processor 开发，需要进行开发的文件有以下四个:

> 所有业务方的自定义 processor 代码统一存放在 dragonfly/ext/ 和 src/processor/ext/ 下，文件夹结构：
<br/>dragonfly/ext/{模块名}/{模块名}_api_mixin.py
<br/>dragonfly/ext/{模块名}/{模块名}_{arranger, enricher, retriever, observer}.py 
<br/>src/processor/ext/{模块名}/{arranger, enricher, retriever, observer}/{*.h, *.cc}

其中 py 部分分析 dragonfly 调用脚本中各个 processor 之间的上下游依赖关系，来生成 json 配置文件；c++ 部分完成该 processor 功能的实现。

### 新建 ext 模块（如需）

> 如果是在已有的 ext 模块里新增 Processor 可以跳过此节步骤。

假设要新建的 ext 模块名为 "xyz"，那么创建 `dragonfly/ext/xyz/xyz_api_mixin.py` 文件，并初始化以下内容：
```python
#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin

class XyzApiMixin(CommonLeafBaseMixin):
  """
  - 模块介绍：（让用户知道该模块是干啥的）
  - 使用场景：（让用户知道什么情况下可以或应该用该模块）
  - 维护人：（让用户知道有问题可以找谁）
  - 维护团队：（如果维护人不在了，让用户知道有问题可以找哪个团队解决）
  """

  # 在后面添加该模块算子的 Python 接口
  def xxx(self, **kwargs):
    pass
```

### Processor 开发参考例子
这里以 enricher 角色类型的为例，其他 processor 的开发流程也是类似的。[另一个 retriever 的例子](https://docs.corp.kuaishou.com/d/home/<USER>

可以参考支持 [zstd 压缩解压缩](https://dragonfly.corp.kuaishou.com/#/api/common?id=zstd) enricher processor 的代码作为一个简单的例子。该 processor 属于 common 模块，需要开发的部分包括以下四个文件:

- dragonfly/ext/common/common_api_mixin.py

- dragonfly/ext/common/common_leaf_enricher.py

- src/processor/common/enricher/common_reco_zstd_enricher.h

- src/processor/common/enricher/common_reco_zstd_enricher.cc

这四个文件分别可以通过下图中的链接进行跳转获得：

<img width="500" src="image/zstd.png" alt="zstd">

接下来将分别介绍这四个文件中的各个类与函数的作用。

#### python 接口
路径与文件名：dragonfly/ext/{业务名}/{业务名}_api_mixin.py，api_mixin.py 文件具有介绍 api 接口和将该 processor 加入 LeafFlow 的功能。

在该文件中增加一个该 processor_api 名的函数，并在紧接着该函数名的下一行用 `"""` 这样 docstring 的方式介绍该 api ，docstring 中的内容会在 [dragonfly 文档](https://dragonfly.corp.kuaishou.com) 中对应的 api 下进行展示。

docstring 中需包括：

- Enricher 结尾的 processor 名

- processor 作用介绍
  
- 参数配置
  
- 参数名：[参数类型]必填项或选配项，参数作用，其中标有动态参数的配置为可以通过<span v-pre>`{{xxx}}`</span>这样的语法来动态得引用获取名为 `"xxx"` 的 common_attr 的值，也可以是正常的参数值

- 调用示例

#### python 定义
路径与文件名：dragonfly/ext/{业务名}/{业务名}_enricher.py，在该文件中增加一个该 processor 的类，以 CommonRecoZstdEnricher 为例，该类中需要开发的函数包括：
```python
class CommonRecoZstdEnricher(LeafEnricher):  
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    # 返回该 processor_api 名的字符串，需与 api_mixin.py 中的 processor_api 保持一致
    return "zstd"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    mode = self._config.get("mode")
    # check_arg 这一方法类似于 assert，判断结果是否为真，为假的话会报错，报错内容为第二个参数的字符串
    check_arg(mode and mode in {"compress", "decompress"}, '`mode` 必须选自 {"compress", "decompress"}')
    check_arg(self._config.get("input_common_attr"), "`input_common_attr` 是必选项")
    check_arg(self._config.get("output_common_attr"), "`output_common_attr` 是必选项")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    attrs = set()
    # 在 zstd 中有 "input_common_attr" 这一配置来指定该 processor 需要用到的 common_attr 的 name，因此需要在 attrs 中加入
    # 以便 c++ 代码中能够通过 "input_common_attr" 来取到 common_attr 的 name，进而取到对应 name 下的 common_attr 值
    attrs.add(self._config["input_common_attr"])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 输出所存储的 common_attrs 集合
    attrs = set()    
    # 在 zstd 中有 "output_common_attr" 这一配置来指定该 processor 输出到的 common_attr 的 name，因此需要在 attrs 中加入
    # 以便 c++ 代码中能够通过 "output_common_attr" 来取到 common_attr 的 name，进而把结果输出到对应 name 下的 common_attr 
    attrs.add(self._config["output_common_attr"])
    return attrs

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    # 表明该 processor 是否是异步的，默认值为 False，当该 processor 为异步时，需要重写这个函数将值设为 True
    # zstd 这一 processor 不是异步的，这里也可以不写这一函数
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 item_attrs
    # zstd 这一 processor 没有需要输入的 item_attr_name，因此返回为空，也可以不写这一函数
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # 该 processor 输出所存储的 item_attrs
    # zstd 这一 processor 没有需要输出的 item_attr_name，因此返回为空，也可以不写这一函数
    return set()
```

#### c++ 头文件 
路径与文件：dragon/src/processor/ext/{业务名}/enricher/processor_enricher.h
在该文件中定义该 processor 类，继承 `CommonRecoBaseEnricher` 类

`InitProcessor()` 在服务启动时调用一次，读 json 的配置，初始化这个 processor，不随服务运行变化的信息都可以放在这里初始化。

在 `InitProcessor()` 中，需要检查 dragonfly 生成的 json 中，对于该 processor 调用的配置是否合法，在服务成功启动前发现问题。其他成员变量与函数根据具体的processor功能需求进行实现。

#### c++ 源文件
路径与文件：dragon/src/processor/ext/{业务名}/enricher/processor_enricher.cc
在该文件中实现该 processor 类

`Enrich()` 函数为该processor具体功能的实现。
```
void CommonRecoZstdEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
  if (is_common_attr_) {
    ProcessStrCommonAttr(context);
  } else {
    ProcessStrItemAttr(context, begin, end);
  }
}
```
在文件的最后，如下代码将 zstd processor 注册到工厂类中。运行时根据这个注册找到对应的 processor 来运行。
```cpp
typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonRecoZstdEnricher, CommonRecoZstdEnricher)
```

## 上下游接入
common_reco.proto 文件位置：
 - [C++](https://git.corp.kuaishou.com/reco-cpp/reco_proto/-/blob/master/common_reco/leaf/proto/common_reco.proto)
 - [Java](https://git.corp.kuaishou.com/reco/kuaishou-reco-platform-proto/-/blob/master/src/main/proto/kuaishou/reco/platform/leaf/common_reco.proto)

### Proto 及结构定义

#### CommonRecoRequest Proto 定义
```protobuf
message CommonRecoRequest {
    // 标识请求类型：对应 leaf 的一组 pipeline
    string request_type = 1;
    uint64 user_id = 2;
    string device_id = 3;
    // 请求时间
    int64 time_ms = 4;
    // 请求期望的结果返回数目
    int32 request_num = 5;
    // 是否为 debug 请求
    bool debug = 6;

    // 以下字段为 reco-router 填充信息用，api 勿填
    BrowseSetPackage browse_set_package = 8;
    // 传递给服务的用户侧数据
    repeated kuiba.SampleAttr common_attr = 9;

    // 后台提供的请求id，唯一标识一次请求，建议都填写。有的业务可以用于拼接与去重。
    string request_id = 11;
    // 通过 request 携带来的 item 及其 item_attr, 这些 item 将被加入到初始的结果集中, 并且对应的 item_attr 将被注入到 context 中
    repeated CommonRecoItem item_list = 13;
    // 指定需要随 item 一起返回的 item_attr 列表（将与 json 配置中的 return_item_attrs 列表 共同生效）
    repeated string return_item_attrs = 14;
    // 指定 在 step info 中返回的 common_attrs
    repeated string return_common_attrs = 15;
    // 针对高性能 item_attr 数据交换场景的连续内存存储格式, 旨在替代 CommonRecoItem item_list 中的 item_attr 字段
    PackedItemAttr item_attr = 17;
    // 控制是否只用 PackedItemAttr 格式返回 item_attr 数据
    bool use_packed_item_attr = 18;
    // 通过 request 携带的 browse Set 数据
    ks.reco.BrowseSet browse_set = 19;
}
```

#### CommonRecoResponse Proto 定义
```protobuf
message CommonRecoItem {
    uint64 item_id = 1;
    int32 item_type = 2;
    int32 reason = 3;
    double score = 4;
    // 用于返回 item_attr 等 item 侧信息
    repeated kuiba.SampleAttr item_attr = 7;
}


message CommonRecoResponse {
    // 0 标识服务正常返回，其余标识异常
    int32 status_code = 1;
    repeated CommonRecoItem item = 2;
    // 回查数据，调试用
    CommonRecoTraceback traceback_info = 6;
    // 返回的 common attr 信息
    repeated kuiba.SampleAttr common_attr = 7;
    // 针对高性能 item_attr 数据交换场景的连续内存存储格式, 旨在替代 CommonRecoItem item 中的 item_attr 字段
    PackedItemAttr item_attr = 8;
}
```

#### CommonRecoResult 结构体定义
```cpp
struct CommonRecoResult {
  // item keysign (item_type + item_id 的合并签名)
  uint64 item_key = 0;
  // 分数
  double score = 0.0;
  // 推荐原因
  int reason = 0;
}
