# Processor 配置

## 通用配置
> 为了方便对各个 Processor 的调度和使用，以下为**通用配置**，没有一一列在各个 Processor 的接口配置说明中，但它们能够将 Processor 的功能更灵活地放大和加强，建议熟练掌握！

##### name
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[string] 指定此 Processor 的名称。未配置此项的 processor 会自动生成带哈希后缀的 Processor 名。默认值：""。

##### skip
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[bool][动态参数] 是否跳过某个 Processor 的执行，默认值：false。（**但更推荐用 `if_()` 分支包裹来实现**）

##### item_table
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[string] 指定算子数据读写的 item_table 名。默认值：""。

##### range_start
适用 Processor 类型：Arranger / Enricher / Observer

[int][动态参数] 表示 Processor 处理 item 范围区间的开始点（包含），为负数时表示倒数第几个。默认值：0。

##### range_end
适用 Processor 类型：Arranger / Enricher / Observer 

[int][动态参数] 表示 Processor 处理 item 范围区间的结束点（不包含），为负数时表示倒数第几个，为 0 时表示直到 item 末尾。默认值：0。

##### partition_size
适用 Processor 类型：异步 Enricher

[int][动态参数] 对当前结果集进行分块多批处理，配置每一批处理的 item 数目，通常用于异步的 Enricher，防止一次请求发送过多 item 导致耗时过长，值为 0 时不分块。默认值：0。

##### reset_item_type
适用 Processor 类型：Retriever

[int][动态参数] 该值若大于等于 0，则将 Retrieve 服务返回的所有 item_type 重置为该设定值。默认值：-1。

##### save_async_status_to
适用 Processor 类型：所有异步 Processor 

[string] 将异步执行的结果状态记录到指定的 int 类型 common attr 中，0 表示成功，1 表示失败，2 表示被降级。默认值 ""。

##### save_result_to_common_attr
适用 Processor 类型：Retriever

[string] 默认值为空，如果不为空，则将 Retrieve 服务返回的 item_id 存入指定的 common_attr 中，<font color=Green>而不是加入到召回结果集</font>！该配置一般用于之后再串联一个新的 Retriever 作为其输入，从而进行 u2u2i / u2i2i 的级联触发。默认值 ""。

##### save_result_to_common_attr_by_id
适用 Processor 类型：Retriever

[bool] 该项需要配和 save_result_to_common_attr 进行使用，如果为 true，则将 item_id 存入指定的 common_attr 中，如果为 false，则将 item_key 存入指定的 common_attr 中。默认值：true。

##### add_reason_to_attr
适用 Processor 类型：Retriever

<font color=Green>TIPS:</font> 另外还有 deduplicate processor 的 append_reason_to 配置项能完成类似功能

为该 Retriever 所召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason。默认值为空。

##### item_list_from_attr
适用 Processor 类型：Arranger / Enricher / Observer

[string] 若该配置不为空，则会让 processor 处理指定 int_list/int 类型 CommonAttr 下所存放的 item 列表，而非当前结果集内的 item 举个例子，你可以利用 deduplicate processor 的功能来去重某个 int_list CommonAttr 下的值。默认值 ""。

##### debug_log
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[bool] 是否将这个 processor 涉及到的输入输出 common/item attr 通过 glog 打印到日志，该配置是 log_debug_info() 的便捷版本，便于快速查看 processor 的输入输出数据。默认值：false。

##### debug_log_to
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[string] debug_log 内容输出至指定的输出流，该值非空时 debug_log 配置默认值将自动变为 `true`，可选值：log/log!/stdout/stderr, 其中 log 会遵守日志用户级的采样约束，log! 则会忽略采样规则每条必打。默认值："log"。

##### debug_log_item_num
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[int] 在开启 debug_log 配置项时，控制打印 item 数据的最大数量。默认值：10。

##### perf_output
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[bool] 是否将这个 processor 所有输出的 common/item attr 均值 perflog 到 grafana，仅支持 <font color=Blue>int/int_list/double/double_list 类型的 common/item attr</font>，该配置使用的 checkpoint 是 "OUTPUT " + processor name，该配置是 perflog_attr_value()。默认值：false。

##### target_item
适用 Processor 类型：Arranger / Enricher / Observer

[object] 默认值：{}。仅对 item_attr 满足指定条件的 item 进行处理，存在多个条件时规则为需要<font color=Blue><b>全部满足</b></font>。换句话说，带有该配置的 Processor 只能看到满足条件的 item，对它而言待处理的 item 结果集是筛选之后的一个子集。

该配置与target_item && target_reason 互斥！同时存在时，优先级target_reason>target_item。 书写格式为：<br/>{<br/>&nbsp;&nbsp;&nbsp;&nbsp;"ITEM_ATTR_NAME": ITEM_ATTR_VALUE \| [ITEM_ATTR_VALUE_LIST] <br/>} <br/> value 亦可用使用动态参数，具体使用方法可以看示例：<br/> <font color=Green># 仅对 tag 这个 item_attr 为 1 的 item 处理</font><br/>target_item = { "tag": 1 }<br/><font color=Green># 仅对 tag 这个 item_attr 为 1 或 2 的 item 处理</font><br/>target_item = { "tag": [1, 2] }<br/><font color=Green># 仅对 tag 这个 item_attr 为 1，且 category 为 "food" 的 item 处理</font> <br/>target_item = {<br/>&nbsp;&nbsp;"tag": 1,<br/>&nbsp;&nbsp;"category": "food"<br/>}<font color=Green># 动态参数版本，其 target_tag 将会从 common_attr 中取值 </font><br/> <span v-pre>target_item = { "tag": "{{target_tag}}"}</span>

##### target_item_type
适用 Processor 类型：Arranger / Enricher。

[int] 仅对指定的 item_type 进行处理（默认值 -1 表示对所有 item_type 都处理）。 <br/><font color=Red><b>注意</b></font>：<font color=Blue><b>对版本号 <= 1.0.847 的二进制，该配置用于 arranger 时，处理完后满足条件的 item_type 将全部位于结果集头部</b></font>。默认值：-1。

##### target_reason
适用 Processor 类型：Arranger / Enricher / Observer

[int/int_list] 仅对 reason 满足指定条件的 item 进行处理，与target_item && target_reason<font color=Blue><b>互斥！同时存在时，优先级target_reason>target_item </b></font>。 示例：<font color=Green># 仅对 reason 为 1, 2, 3 的 item 处理</font> target_reason = [1, 2, 3]。默认值：[]。

##### select_item
适用 Processor 类型：Arranger / Enricher / Observer

[dict] 仅对满足条件的 item 进行该 processor 的处理，相比 target_item。默认值：{}。

`join`: [string] 复合选择的子模块结合方式，支持 or 和 and 两种方式，可缺省

`filters`: [list] 由一组 rule 组成（可嵌套）
  - `enable`: [bool] [动态参数] 单个选择模块的开关
  - `attr_name`: [string] 单个选择模块的 Item attr 名称
  - `check_reason`: [bool] 是否使用 Item reason 代替 Item attr 的值进行检查判断，默认为 false
  - `select_if`: [string] 单个选择模块的比较运算符，可选值："<", "<=", ">", ">=", "==", "!=", "in", "not in", "contain", "not contain", "intersect", "not intersect", "is null", "not null"
  - `compare_to`: 
    - 形式1: [int/double/string/int_list/string_list] [动态参数] 单个选择模块的select_if 被比较的值，默认值为 0，double 类型值对于 ==、in、contain、intersect 等精确比较场景不生效。
    - 形式2: [dict]
        - "range": [dict] 配置一个范围，区间为左闭右开，其中 "lower_bound" 和 "upper_bound" 均为动态参数，示例：<span v-pre>{"range": {"lower_bound": "{{lb}}", "upper_bound": "{{ub}}"}}</span>，支持的运算符："in", "no in", "intersect", "not intersect"
  - `select_if_attr_missing`: [bool] 如果 item 没有该 attr 是否要选，默认值 false

`limit`: [int][动态参数]限制整个 select_item 规则选择 item 的最大数量 

``` python
select_item = {
    "attr_name": "p_str",
    "compare_to": "str",
    "select_if": "==",
    "select_if_attr_missing": True
}

select_item = { 
    "join": "and",
    "filters": [{
        "attr_name": "p_int",
        "select_if": ">",
        "compare_to": 3,
    }, {
        "attr_name": "p_double",
        "select_if": "<",
        "compare_to": 5.0,
    }]，
    "limit": 100,
}

select_item = { 
    "join": "or",
    "filters": [{
        "join": "and",
        "filters": [{
            "attr_name": "p_int",
            "select_if": ">",
            "compare_to": 3,
        }, {
            "attr_name": "p_double",
            "select_if": "<",
            "compare_to": 5.0,
        }]
    }, {
        "attr_name": "p_string",
        "select_if": "==",
        "compare_to": "aa",
    }]，
    "limit": 100,
}
```

##### traceback
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[bool] 对于开启了 cache response 配置的请求，是否记录该 processor 执行后的详情数据。当 processor 极多造成 debug 数据处理和发送耗时过大时可手动配置该项以减少数据量。默认值：--default_traceback_config_value 的 flag 值（默认 true）。

##### full_traceback
适用 Processor 类型：Arranger / Enricher / Observer / Retriever

[bool] 对于开启了增量更新 traceback 的业务，也可以开启这个配置，使得在该 processor 能够全量打出 traceback 日志，一般用于特殊的 debug 需求。默认值：false。

##### cache_outputs
适用 Processor 类型：Enricher / Retriever

[dict] 缓存该 processor 输出的 attr，对于 Enricher 类型的算子，需要满足输出的 attr 只为 common_attr 或者只为 item_attr，具体根据 cache key 的类型而定。**需要 gflag enable_processor_output_attr_cache=true 开启后才能使用该功能**。支持进行 cache 的 attr 类型为基本类型 int/double/string/int_list/double_list/string_list，PtrAttr 类型 Message/std::vector\<std::vector\<int64_t\>\>/std::vector\<std::vector\<double\>\>。默认值：{}。对于 Retriever 类型的算子，**需要 gflag enable_processor_retrieve_item_cache=true 开启后才能使用该功能**。

`enable`: [bool][动态参数] 该算子是否启用 cache。默认值：false。

Enricher 专用配置:

`common_cache_key`: [string] common_attr 名称，用于 cache key 的生成，要求 attr 类型只能为 int/string/int_list/string_list，list 类型需要保证 size 为 1。如需生效，则需保证此算子只输出 common_attr。优先级大于 item_attr 相关的 cache。默认值：""。对应的 cache value 就是该算子输出的所有 common attr，支持混合类型。

`item_cache_key`: [string] item_attr 名称，用于 cache key 的生成，attr 要求同上。如需生效，则需保证此算子只输出 item_attr。默认值：""。对应的 cache value 就是该算子输出的所有 item attr，支持混合类型。

`item_cache_key_use_item_key`: [bool] 是否将 item_key 用于 cache key 的生成。优先级大于 item_cache_key。默认值：false。

`item_cache_key_use_uid_and_did`: [bool] 是否将 uid did 用于 cache key 的生成。默认值：false。

`allow_write_null_value`: [bool] 是否允许写入空值。默认值：true，避免缓存穿透问题。对于 `fetch_kgnn_neighbor` 算子，如果开启了分片访问，可以改成 false，以便 cache 能够正常写入。相关问题：https://kstack.corp.kuaishou.com/question/6859?answerId=14371

``` python
cache_outputs = { 
    "enable": True,
    "common_cache_key": "user_id"
}
```

Retriever 专用配置:

`retrieve_cache_key`: [string] common_attr 名称，用于 cache key 的生成，要求 attr 类型只能为 int/string/int_list/string_list，list 类型需要保证 size 为 1。默认值：""。对应的 cache value 就是 Retriever 获取的所有 Item。

``` python
cache_outputs = { 
    "enable": True,
    "retrieve_cache_key": "user_id"
}
```

##### short_circuit_threshold
适用 Processor 类型：Retriever

[int][动态参数] 该值若大于 0，则当前结果集大小超过该阈值时取消当前 Retriever 的执行（即对该 Retriever 进行短路），一般用于作为兜底的。默认值：0。

##### item_channel
适用 Processor 类型：Retriever

[int]对召回的 item 增加 channel 标识，用于标记某个场景下的召回，便于之后分场景处理。示例：<font color=Green># 对召回的 item 增加值为 1 的 channel</font>  item_channel = 1。默认值：0 注： retrieve_by_sub_flow 中 item_channel 默认值为 -1，当该算子配置 item_channel >=0 时，则会覆盖子 flow 中原有的 item_channel。

<!-- | ?? | downstream_processor <br/><font color=Red><b>这是一剂副作用很强的处方药，99% 的情况你不需要用到它</b></font>              | string          | ""    | 所有异步 Processor  | 指定在哪个后续 Processor 之前收集全异步请求数据，方便自动最大化异步请求并行度  | <font color=Red><b>该配置 Dragonfly 会自动检测填充，不用手动配置！ 如有必要请在专家指导下使用！</b></font> | -->

##### degrade_strategy
适用 Processor 类型：所有异步 processor

[string][动态参数] 指定算子适用的限流策略名。使用方式参考[异步算子限流降级配置](https://dragonfly.corp.kuaishou.com/#/wiki/engineering?id=%e5%bc%82%e6%ad%a5%e7%ae%97%e5%ad%90%e9%99%90%e6%b5%81%e9%99%8d%e7%ba%a7)


## 专用配置
详情见 [Processor API](https://dragonfly.corp.kuaishou.com/#/api/common) 下每个 processor 介绍
