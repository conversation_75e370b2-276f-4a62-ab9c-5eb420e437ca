# GFLAGS 与 Processor 配置

## GFLAGS

!>严正声明：别瞎鸡儿抄别人业务方的配置！自己不明白其作用和影响的配置项不要抄来抄去，坑人坑己，需要的配置项自行在该 wiki 中查阅功能后按需添加！

server_static.flags 文件里定义了各个 FLAG 参数设置，一个包含了最少配置项的示例：

```ini
# 服务的 log 输出目录
--log_dir=../log/
```

如果使用了 common index 本地索引（调用了 retrieve_by_local_index 或 get_item_attr_by_local_index），则需要以下额外配置：
```ini
################# BEGIN 本地索引相关配置 #################
# 索引数据的 bt_queue name (类似 kafka topic), 生产方将数据打入这个队列
--index_queue=YOUR_COMMON_INDEX_BTQ
# 索引 item 的最大数目（选配），这里设置的为默认值，表示不超过 2^24 = 16,777,216 个
--common_index_doc_bits=24
# 索引倒排 term 的最大数目（选配），这里设置的为默认值，表示不超过 2^24 = 16,777,216 个
--common_index_unigram_bits=24

# 索引底层库会用到这两个 flag, 必须配置, 照抄就行, 不要私自改动!!!
--dynamic_index_static_dict_filename=../data/static_dict.dat
--index_builder_static_dict_filename=../data/static_dict.dat
################# END 本地索引相关配置 #################
```

如果你的 flag 文件中有与 port 相关的配置（`grpc_server_port`、`web_server_port`），**强烈建议删除掉**！

其他一些常用的选配 FLAG：


##### grpc_thread_num
--grpc_thread_num=128

[int] 默认值：0。手动指定 leaf 服务的 grpc 工作线程数，若大于 0 生效优先级高于 grpc_thread_num_per_core 配置项。


##### grpc_thread_num_per_core <font color=Green>(推荐)</font>
--grpc_thread_num_per_core=1.5

[double] 默认值：2.0。根据 CPU 核数动态确定 leaf 服务的 grpc 工作线程数，默认为 CPU 核数 * 2，可为小数。 如果服务的网络 IO 较多，可适当调高此值以提高 CPU 利用率。

##### grpc_cqs_num
--grpc_cqs_num=4

[int] 默认值：1。如果服务启动了多个 grpc server，譬如一个接收外部请求另一个后台同步数据，互相之间会争抢 grpc 的并发限制，吞吐受限进而触发过载保护，可在**架构同学的指导下**调高 cqs 数来缓解，[更多介绍](https://halo.corp.kuaishou.com/help/docs/339bd647e92e57a62706a61f6e678d4d#%E6%8C%87%E5%AE%9A-cqs-%E4%B8%AA%E6%95%B0)。


##### worker_thread_num
--worker_thread_num=128

[int] 默认值：同 grpc 线程数。独立指定 worker 的数目，与 grpc 线程数解耦。

##### sub_flow_thread_num
--sub_flow_thread_num=128

[int] 默认值：同 grpc 线程数。指定用于运行 sub_flow 的线程池大小，如果服务的 [sub_flow 排队耗时](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=5026)较高，可适当提高该值。

##### sub_flow_thread_num_per_worker <font color=Green>(推荐)</font>
--sub_flow_thread_num_per_worker=3

[double] 默认值：0。若大于 0 将用该值乘以主 thread_num 后作为 sub_flow 全局线程池的大小，不宜设置过大，一般 2-4 即可（优先级低于 sub_flow_thread_num）。

##### sub_flow_thread_num_upscale_ratio
--sub_flow_thread_num_upscale_ratio=1.0

[double] 默认值：1.0。控制 sub_flow 线程池线程数的动态上浮比例，也就是说当用 sub_flow_thread_num/sub_flow_thread_num_per_worker 确定好 sub_flow 线程初始总数后，允许在原有线程不够用的情况下临时创建新线程来处理积压任务，该系数控制的就是允许总线程数最多上浮到初始数目的多少倍。

##### enable_fast_stop
--enable_fast_stop=true

[bool] 默认值：false。在停止服务时是否利用 SIGKILL 命令直接强杀进程，以达到快速退出的效果。 <font color=Red>注意：</font> <font color=Blue>该功能只可对不需要优雅退出的无状态服务开启</font>（即无任何本地数据需要保存，包括但不限于：本地索引、带本地 shm 的各种缓存数据），否则可能使本地数据受损，影响下次启动

##### enable_pthread_affinity
--enable_pthread_affinity=true

[bool] 默认值：false。是否开启 numa 亲和性绑核，<font color=Red>注意：</font><font color=Blue>该功能仅限物理机和独占容器，其他容器部署方式无法保证有效</font>

##### enable_numa_aware
--enable_numa_aware=true

同时配置环境变量 `MALLOC_CONF=numa_aware:true`，如果遗漏了此环境变量则退化成 `enable_pthread_affinity=true`

[bool] 默认值：false。是否开启 Dragonfly 框架和 Jemalloc 的 numa aware 性能优化。打开后 Dragonfly 框架管理的线程池（包括 worker 线程池和 subflow 线程池）会均匀的绑定到物理机或容器所能使用的 numa 节点上，绑定的内容包括 numa 亲和性绑核、内存分配绑定(堆内存和栈内存均优先从 local memory 分配)，相比 `enable_pthread_affinity` 更彻底的减少 memory remote access，从而提升服务性能。

##### parallel_init_handler
--parallel_init_handler=true

[bool] 默认值：false。控制 grpc worker 线程是否进行并行初始化（即：hander 是否并行初始化）。

注意，当开启了 `--enable_pthread_affinity=true` 或 `--enable_numa_aware=true`，grpc worker 线程也是并行初始化的，与此 `--parallel_init_handler=true` 效果相同。

##### parallel_init_pipeline_concurrency 
--parallel_init_pipeline_concurrency=8

[int] 默认值：0。控制每个 grpc worker (handler) 内部的 pipeline 的并行初始化粒度，默认为0，即 handler 内部的 pipeline 串型初始化。

注意，设置 `parallel_init_pipeline_concurrency` 大于0的值，除了会将 pipeline 并行初始化之外，还会将“仅配置更新”移动到后台运行，使得仅配置更新期间的服务可用性得到提高。(但是由于非 common 算子可能存在线程安全隐患，不再建议使用仅配置更新)

##### parallel_init_processor_concurrency
--parallel_init_processor_concurrency=8

[int] 默认值：0。控制 pipeline 内部 processor 的并行初始化粒度，默认为0，即 pipeline 内部的 processor 串型初始化。

##### server_type
--server_type=brpc

[string] 默认值：grpc。指定在线服务的类型，默认为 grpc，其它可选值：brpc / flatten_grpc / flatten_brpc。

##### grpc_caller_whitelist_kconf_key
--grpc_caller_whitelist_kconf_key=xxx.yyy.zzz

[string] 默认值：""。通过一个 set_string 类型的 kconf 白名单来限制允许访问的上游调用端（若白名单为空将不限制），未在 kess name 白名单内的上游请求将被直接返回 `grpc::StatusCode::UNAUTHENTICATED`。 

##### processor_breaker_kconf_key
--processor_breaker_kconf_key=xxx.yyy.zzz

[string] 默认值：""。通过一个 map_string_double 类型的 kconf 在发生紧急情况的时候摘除算子。配置后可在 [CommonLeaf监控](https://grafana.corp.kuaishou.com/d/yjQHxkkHk/commonleafjian-kong?orgId=3&fullscreen&panelId=7320)查看请求熔断数目。下面是一个配置示例：

```json
{
  // key 为算子名,可通过监控查阅； value是熔断概率（40%的概率不执行该算子）。
  "cascading::cascading_house_tower::global_data_enricher_test" : 0.4
}
```

##### enable_item_type_and_keysign
--enable_item_type_and_keysign=false

[bool] 默认值：true。是否对 item_type 和 item_id 做 keysign 处理生成 item_key，关闭后将直接使用 item_id 作为 item_key，item_type 恒为 0，同时其他所有与 item_type 有关的 Processor 配置项和 Flag 设置都将无效。 如果业务方的 item_id 用到了 int64 高 8 位，可关闭此开关以绕过 keysign 对 item_id 的影响。

##### enable_debug_log_for_all
--enable_debug_log_for_all=true

[bool] 默认值：false。是否忽略所有 log_debug_info 的 for_debug_request_only 配置，对所有请求都强制打印 debug info 内容。

##### enable_ktrace
--enable_ktrace=true

[bool] 默认值：false。ktrace 功能的总开关，打开后可在 [trace 平台](https://tianwen.corp.kuaishou.com/trace-analysis)查看执行链路。

##### enable_pipeline_ktrace
--enable_pipeline_ktrace=true

[bool] 默认值：true。是否开启服务内部 pipeline 粒度的 ktrace 打点记录。

##### enable_processor_ktrace
--enable_processor_ktrace=true

[bool] 默认值：false。是否开启服务内部 processor 粒度的 ktrace 打点记录。


##### ~~leaf_show_use_id~~ <font color=Red>（已迁移至 leaf_show() json config: use_item_id）</font>
控制发送 leaf show 时使用 item_id（为 true 时）还是 item_key（为 false 时）作为 item 拼接 key 

##### ~~leaf_show_use_request_id~~ <font color=Red>（已迁移至 leaf_show() json config: use_request_id）</font>
控制发送 leaf show 时使用 request_id（为 true 时）还是 user_id/device_id（为 false 时）作为 request 拼接 key

##### enable_request_cache
--enable_request_cache=true

[bool] 默认值：false。是否缓存线上请求到 Redis 以供后续 debug 回查（具体参见 [Leaf回查页-线上请求](/wiki/leaf_studio?id=线上请求) 一节介绍） <font color=Red>注意：</font>因为存储资源有限，小业务可直接开启（使用 recoKrpLeaf 共享集群），**QPS > 100** 的业务请提前联系 @方剑冰 评估是否需要单开集群存储。

##### abtest_biz_name
 --abtest_biz_name=RECO_PUSH

[string] 默认值：""。abtest 网站中左侧栏全英文大写的<font color=Blue><b>“所属业务”</b></font>名称（[Abtest实验设置](/wiki/abtest_experiment_settings?id=abtest-实验设置)）

##### ~~abtestbiz_seq_num~~ <font color=Red>（请改用 abtest_biz_name FLAG 或 CommonRecoAbtestCommonAttrEnricher 中的 biz_name 配置）</font>
~~abtest 的 biz 序号，使用 abtest 实验时需要指定该值以正确获取实验参数值（[Abtest实验设置](/wiki/abtest_experiment_settings?id=abtest-实验设置)）~~ 

#####  logging_switch_uid_mod_divisor / logging_switch_uid_mod_divisor_kconf_key
--logging_switch_uid_mod_divisor=1000

--logging_switch_uid_mod_divisor_kconf_key=xxx.yyy.zzz

[int/string] 默认值：1/""。日志量太大时可通过该 flag 减少日志量，针对 user level 进行采样打印（应用于源代码中的 CL_LOG），规则为满足 user_id % logging_switch_uid_mod_divisor == 0 时才进行日志打印（对 user_id 为 0 的情况会改用 device_id 的 hash 值做取模运算，对 user_id 为 0 且 device_id 为空的情况会用随机数做取模运算），配置为 0 可直接关掉 log。 <font color=Red><b>【更建议】</b></font><font color=Blue>使用 kconf 配置版本的 FLAG: logging_switch_uid_mod_divisor_kconf_key</font>，kconf 值类型为 int64，方便动态调整日志采样率，<b>无需重启！</b> <font color=Blue><b>线上服务务必配置该项</b>，并设置一个较大值，否则很可能因为磁盘 IO 压力影响服务性能！若发生索引 batch 切换导致的耗时尖峰，也可通过降低日志采样率来缓解。</font>

##### logging_switch_tail_number_kconf_key
--logging_switch_tail_number_kconf_key=xxx.yyy.zzz

[string] 默认值：""。与上述 logging_switch_uid_mod_divisor 和 logging_switch_uid_mod_divisor_kconf_key 相同地，日志量太大时可通过该 flag 减少日志量，从 kconf 中读取 tail_number 类型的值，根据 tail_number 判断返回 true 的才进行日志打印。规则为 user_id 是否在 tail_number 中的黑名单里，若在返回 false，若不在则判断 user_id % tail_number.divisor 是否命中 tail_number 中的 range，若命中返回 true，不命中则判断是否在白名单里来返回 true 或 false。（对 user_id 为 0 的情况会改用 device_id 的 hash 值做取模运算） <font color=Red><b>注意：</b></font><font color=Blue><b>该 flag 优先级高于上述 logging_switch_uid_mod_divisor 和 logging_switch_uid_mod_divisor_kconf_key</b></font>，在配了该 flag 时将忽视上述两个 flag。

##### is_test_host
--is_test_host=true

[bool] 默认值：false。在 kess 的测试环境启动服务时需开启此开关，否则无法访问<font color=Blue>（介于公司测试环境搭建不完善，用这个功能上下游衔接有坑，</font><font color=Red><b>慎用！</b></font>

##### slow_list_length_limit_kconf_key
--slow_list_length_limit_kconf_key=xxx.yyy.zzz

[string] 默认值：""。kconf 配置（<font color=Blue><b>int64 类型</b></font>）的 key 名称，用于动态设定 leaf 服务端的 slow_list（相当于一个请求缓存队列）长度限制，超过限制的请求将不会排队处理而是直接丢弃掉，对 leaf 进行过载保护（防止被上游打挂）。 该配置为空时 slow_list 将使用默认长度限制 100。

##### default_traceback_config_value
--default_traceback_config_value=false

[bool] 默认值：true。是否默认对每个 processor 执行后的 item 结果集及各个 common_attr、item_attr 进行详细的数据记录，用于后续在 leaf studio 中进行 debug 回查。 对于数据量特别大导致 leaf studio 回查速度太慢、或记录线上历史对请求增加太多额外耗时的业务方，可将此 flag 置为 false，并在需要 debug 数据的 Processor 或 dragonfly 方法内增加通用配置 traceback = True 来手动开启打点，以减小数据量和请求等待时间。

<!-- 废弃中
##### sub_flow_async_thread_num
--sub_flow_async_thread_num=15

[int] 默认值：4。subflow 的线程池数量配置，如果太少会导致 subflow 阻塞，retrieve 阶段耗时增加；如果配置过高，会导致过多的线程切换，增加系统负载。 注意：每个服务线程都持有一个自己的 subflow 线程池，所以总的线程数量是 sub_flow_async_thread_num * grpc_thread_num, 请根据 grpc_thread_num 数目适当调整 -->

##### attr_perf_sample_rate
--attr_perf_sample_rate=0.001 

[double] 默认值：1.0。flat index 正排命中率信息上报的采样率。正排信息上报会产生 cpu 和时延的消耗，可以适当降低上报频率以提升性能。默认值为 1.0（全上报）。 

##### enable_attr_usage_perflog 
--enable_attr_usage_perflog=true 

[bool] 默认值：false。是否上报 common attr / item attr 的使用频次，默认值为 false(不上报)。注意开启后可能会导致 perf 信息的组合数大量增长。监控链接参见[[common_attr 使用频次](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=4604)] [[item_attr 使用频次](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&fullscreen&panelId=5094)]  

##### attr_type_check_level
--attr_type_check_level=1

[int] 默认值：0。检查 processor 读写 attr 的类型是否与预设的一致。level=0: 不做检查；level=1: attr_type 默认设为第一次 set 的类型，后续类型不一致的 get set 报 warning 与 error；level=2: 检查上报存在读写类型不一致的 attr，且读写操作将失败（上报，且对读写结果产生实际影响）。

##### check_attr_type_get
--check_attr_type_get=true

[bool] 默认值：false。对于 get 操作是否进行检查，检查力度与 attr_type_check_level 一致。


##### attr_io_check_level 
--attr_io_check_level=1

[int] 默认值：0。检查 processor input/output attr 的声明是否准确。level=0: 不做检查；level=1: 检查并上报未声明的 input/output attr，对未声明的 attr Get/Set 操作正常执行；level=2: 检查、上报，并且对未声明的 attr Get/Set 操作将失败。**注意：**检查功能会有 10% 左右的性能损耗，请勿对线上服务大面积开启！

##### enable_branch_condition_result_perflog
--enable_branch_condition_result_perflog=false

[bool] 默认值：true。上报分支语句的运行结果，用于定位无用 processor，上报内容过多导致 perf 失败的服务可选择关闭

##### pipeline_cpu_cost_sample_rate
--pipeline_cpu_cost_sample_rate=0.001

[double] 默认值：0.0。记录 pipeline 的 cpu 消耗并上报 grafana，默认为 0.0 则不记录。double 取值范围为[0, 1] 

##### table_cpu_cost_sample_rate 
--table_cpu_cost_sample_rate=0.001

[double ] 默认值：0.0。记录 table 的 cpu 消耗并上报 grafana，默认为 0.0 则不记录。double 取值范围为[0, 1]

## 通用索引 GFLAGS
用到 local_index 的 krp_common_leaf 服务和 krp_common_query_server 服务

##### common_index_full_random_search
--common_index_full_random_search=true 

[bool] 默认值：false。是否对索引的倒排检索开启全范围随机模式。 索引随机检索的默认模式为从倒排链一个随机位置开始连续选取 N 个 item，开启全随机后每个 item 都将从一个独立的随机位置选取。<font color=Red><b>注意：</b></font><font color=Blue>全随机模式性能损耗较大，</font> 建议仅在上游数据不易提前打乱的情况下使用。 


##### common_index_doc_bits
--common_index_doc_bits=26

[int] 默认值：24。common index 通用索引中能容纳的 doc 数目上限，默认为 24（即最多存储 2^24 个 doc），<font color=Red><b>最大不可超过 29</b></font> 

##### common_index_unigram_bits
--common_index_unigram_bits=26

[int] 默认值：24。common index 通用索引中能容纳的倒排 term 数目上限，默认为 24（即最多存储 2^24 个倒排 term），<font color=Red><b>最大不可超过 29</b></font> 

##### common_index_log_interval
--common_index_log_interval=10000 

[int] 默认值：1000。common index 通用索引查询相关日志的打印频率，若小于 0 则不打印。 <font color=Blue>若发生索引 batch 切换导致的耗时尖峰，一般为硬盘 IO 阻塞日志打印造成，可通过调大该值（或置 -1）来缓解，如果是本地索引还需同时调整 logging_switch_uid_mod_divisor_kconf_key 配置来降低 leaf 的日志。</font> 

##### common_index_force_complete_transfer
--common_index_force_complete_transfer=false 

[bool] 默认值：true。若该项为 true，common index 通用索引在 <font color=Blue><b>全新部署</b>时会强制等待 2 个完整的 batch 数据</font>接收完毕后才进入 available 状态（在已部署成功过的服务上重新部署不用等待），以保证对外 serving 时所有数据的完整性，这也意味着可能最多需要等待 2 个 batch finish 的时间才能等待服务部署完毕，<font color=Red>如果对数据完整性无要求</font>且希望缩短部署等待耗时可将此 flag 设置为 false。 

##### common_index_btqueue_backward_second
--common_index_btqueue_backward_second=14400 

[int] 默认值：1800。设置索引服务启动时，追溯 BTQ 中从过去多少秒开始的数据。

##### common_index_available_version_second 
--common_index_available_version_second=36000 

[int] 默认值：3600*5。如果最新一个 batch finish 的版本号时间戳早于当前时间戳减去该 flag 值的结果，那么会认为当前 batch 版本过老从而在 log 中出现 "current_version too old" 的等待信息，对 batch 数据量大、需要超过默认 5 小时才能打完的业务方可调大该 flag 来避免索引无法启动 

##### common_index_enable_btq_trace_back
--common_index_enable_btq_trace_back=true 

[bool] 默认值：false。是否开启本地索引快速追溯历史数据的功能，<font color=Blue>该功能需要索引BTQ 的业务类型为<b>”通用索引(HDFS 持久化)“</b>，同时将 FLAG common_index_btqueue_backward_second 设置为能覆盖</font><font color=Red>连续两个 batch finish 消息的间隔时长</font>。 开启后若索引已存在本地数据且可用，则跳过追溯直接启动，否则将从 BTQ 过去 common_index_btqueue_backward_second 内的数据中自动加载历史数据，无需等待上游索引 runner 重新发送 batch 数据，用于全新部署的快速启动。


##### common_index_read_important_queue 
--common_index_read_important_queue=false

[bool] 默认值：true。默认情况下除了所配置的 bt_queue 名称，common index 还会读取一个带 "(important)" 后缀的 bt_queue 用于接收重要的实时数据，如果确无此需求，想避免 bt_queue client 因尝试读取 important queue 失败而导致的报错，可设置该 flag 为 false 以禁用 important queue
