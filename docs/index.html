<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>欢迎来到 Dragonfly Docs 文档</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <!-- 设置浏览器图标 -->
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="image/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="image/favicon-16x16.png"
    />
    <link rel="manifest" href="site.webmanifest" />
    <link rel="shortcut icon" href="image/favicon.ico" />
    <meta name="description" content="Document" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0"
    />
    <link rel="stylesheet" href="css/vue.min.css" />
    <link rel="stylesheet" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" href="css/element_ui_index.css" />
    <link rel="stylesheet" href="css/index.css" />
  </head>

  <body>
    <div id="app"></div>
    <script src="js/util.js"></script>
    <!-- Docsify v4 -->

    <script src="js/vue.min.js"></script>
    <script src="js/lodash.min.js"></script>
    <script src="js/element_ui_index.js"></script>
    <script src="js/index.js"></script>

    <!-- From https://github.com/docsifyjs/docsify/pull/1657 Feature: Support vue template in sidebar -->
    <script src="js/docsify.min.js"></script>
    <script src="js/axios.min.js"></script>
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-ui.min.js"></script>
    <script src="js/search.js"></script>

    <script src="https://h1.static.yximgs.com/udata/pkg/ks-track-platform-new/weblogger/3.9.11/plugins/autopv.min.js"></script>
    <script src="https://h1.static.yximgs.com/udata/pkg/ks-track-platform-new/weblogger/3.9.11/log.browser.min.js"></script>
    <script src="https://h1.static.yximgs.com/udata/pkg/ks-track-platform-new/weblogger/3.9.11/plugins/autotrack.min.js"></script>
    <script>
      const weblog = new Weblog(
        {
          env: "production",
          plugins: [new AutoPV(), new AutoTrack()],
        },
        {
          product_name: "dragon_wiki", // 申请的产品标识，HTTP 上报必填
        }
      );
    </script>

    <script src="js/prism-protobuf.min.js"></script>
    <script src="js/prism-c.min.js"></script>
    <script src="js/prism-cpp.min.js"></script>
    <script src="js/prism-python.min.js"></script>
    <script src="js/prism-json.min.js"></script>
    <script src="js/prism-bash.min.js"></script>
    <script src="js/prism-ini.min.js"></script>
  </body>
</html>
