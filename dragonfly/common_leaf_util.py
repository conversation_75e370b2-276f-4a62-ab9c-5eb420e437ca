#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_util.py
description: common_leaf dynamic_json_config DSL intelligent builder, util module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""

import os
import re
import sys
import json
import hashlib
import getpass
import socket
import time
import contextlib
from typing import get_type_hints, Union, Optional

# Note(zhaoyang09): 用来记录 lupa 是否 正常 import 
LUPA_AVAILABLE = False

try:
  import lupa
  LUPA_AVAILABLE = True
except ImportError:
  pass



LUA_UTIL_SCRIPT = """

math = require("math")

function math.beta_dist()
return 0.1
end

util = {}

function util.GetTimestamp()
return 1
end

function util.GetId()
return 1
end

function util.GetType()
return 1
end

function util.GenKeysign()
return 1
end

function util.CityHash64()
return 1
end

function util.Random()
return 0.1
end

function util.GetDistance()
return 0.1
end

"""

BLAME_ERROR_CODE = os.environ.get("BLAME_ERROR_CODE", "false") == "true"
PRINT_ALL_ERROR = os.environ.get("PRINT_ALL_ERROR", "false") == "true"
ERR_MSG_SET = set()

# XXX(huiyiqun): valid for CPython3.6 only, be careful
def flatten_union(t):
  ret = set()
  if hasattr(t, '__origin__') and t.__origin__ is Union:
    for sub_t in t.__args__:
      ret.update(flatten_union(sub_t))
  else:
    ret.add(t)
  return ret

ENABLE_TYPE_CHECK = "--enable_type_check" in sys.argv or "--type_check" in sys.argv

def strict_types(function):
  def type_checker(*args, **kwargs):
    if not ENABLE_TYPE_CHECK:
      return function(*args, **kwargs)
    hints = get_type_hints(function)
    all_args = kwargs.copy()
    all_args.update(dict(zip(function.__code__.co_varnames, args)))
    for argument, argument_type in ((i, type(j)) for i, j in all_args.items()):
      if argument in hints:
        type_hint = hints[argument]
        allowed_types = tuple(flatten_union(type_hint))
        if not issubclass(argument_type, allowed_types):
          raise TypeError(f"Type of {argument} is {argument_type}, which should be {type_hint}")
    result = function(*args, **kwargs)
    if 'return' in hints:
      type_hint = hints['return']
      allowed_types = tuple(flatten_union(type_hint))
      if not issubclass(type(result), allowed_types):
        raise TypeError(f"Type of function ({function}) return is {type(result)}, which should be {type_hint}")
    return result
  return type_checker

@strict_types
def is_number(s: str) -> bool:
  try:
    float(s)
    return True
  except:
    return False

@strict_types
def dump_to_json(obj: dict, indent: Optional[int] = 2, sort_keys: bool = True) -> str:
  def fallback_dump(o):
    if isinstance(o, set):
      return sorted(o)
    elif hasattr(o, '__dict__'):
      return o.__dict__
    else:
      raise ArgumentError(f"发现未定义 json 序列化行为的配置数据: {o}")

  return json.dumps(obj, indent=indent, sort_keys=sort_keys, allow_nan=False, ensure_ascii=False, default=fallback_dump)

class ArgumentError(Exception):
  """ 配置参数错误 """

class LogicError(Exception):
  """ 配置逻辑错误 """

@strict_types
def check_arg(condition, message: str) -> None:
  if not condition:
    raise ArgumentError(f"❌ [配置参数错误] {message}")

@strict_types
def check_common_query_syntax(query: str) -> None:
  check_parenthesis_closed(query, "query ")
  for sub_query in query.split(" AND "):
    for leaf_node in sub_query.split(" OR "):
      leaf_node = leaf_node.strip()
      if leaf_node.startswith("NOT "):
        leaf_node = leaf_node[4:]
      #检查括号个数
      #检查':'和'='的错误用法
      for sep in [":", "="]:
        expressions = leaf_node.split(sep)
        if len(expressions) > 2:
          raise ArgumentError(f"❌ [query 格式错误：存在多个符号'{sep}'] {query}")
        for exp in expressions:
          if len(exp) == 0:
            raise ArgumentError(f"❌ [query 格式错误：'{sep}'左右的值不能为空] {query}")

@strict_types
def extract_common_attrs(expr) -> list:
  """ 从 {{}} 中提取 common_attr 名称 """
  if not isinstance(expr, str):
    return []
  return [attr for attr in re.findall(r"{{([\w\.]+)}}", expr) if not attr.isdigit()]

@strict_types
def is_dynamic_expr(expr) -> bool:
  return expr.startswith("{{return ")


IF_BRANCH_CONTROL_COUNTER = 0

@strict_types
def gen_if_branch_control_attr() -> str:
  """ 生成一个唯一的 if 分支控制 common_attr 名称 """
  global IF_BRANCH_CONTROL_COUNTER
  IF_BRANCH_CONTROL_COUNTER += 1
  return f"_if_control_attr_{IF_BRANCH_CONTROL_COUNTER}"

@strict_types
def gen_else_branch_control_attr() -> str:
  """ 生成一个唯一的 if 分支控制 common_attr 名称 """
  global IF_BRANCH_CONTROL_COUNTER
  IF_BRANCH_CONTROL_COUNTER += 1
  return f"_else_control_attr_{IF_BRANCH_CONTROL_COUNTER}"

@strict_types
def gen_else_if_branch_control_attr() -> str:
  """ 生成一个唯一的 else_if 分支控制 common_attr 名称 """
  global IF_BRANCH_CONTROL_COUNTER
  IF_BRANCH_CONTROL_COUNTER += 1
  return f"_elseif_control_attr_{IF_BRANCH_CONTROL_COUNTER}"

SWITCH_BRANCH_CONTROL_COUNTER = 0

@strict_types
def gen_switch_branch_control_attr() -> str:
  """ 生成一个唯一的 switch 分支控制 common_attr 名称 """
  global SWITCH_BRANCH_CONTROL_COUNTER
  SWITCH_BRANCH_CONTROL_COUNTER += 1
  return f"_switch_control_attr_{SWITCH_BRANCH_CONTROL_COUNTER}"

@strict_types
def gen_str_hash_hexstr(s: str, length: int) -> str:
  """ 对给定的 string 生成指定长度的 hash 字符串 """
  res = hashlib.md5(s.encode('utf-8')).hexdigest()
  return res[:length].upper()

@strict_types
def gen_dict_hash_hexstr(d: dict, length: int) -> str:
  """ 对给定的 dict 结构生成指定长度的 hash 字符串 """
  res = hashlib.md5(bytes(dump_to_json(d, indent=None, sort_keys=True), 'utf-8')).hexdigest()
  return res[:length].upper()

@strict_types
def _remove_literal_string(text: str) -> str:
  """ 删除表达式中用 '' 或 "" 包裹的字符串内容 """
  res = ''
  quotation = None
  open_index = -1
  for i, c in enumerate(text):
    if quotation is None:
      if c == '"' or c == "'":
        quotation = c
        open_index = i
        res += ' '
      else:
        res += c
    elif c == quotation and text[i-1] != '\\':
      quotation = None
      open_index = -1
  if open_index >= 0:
    raise ArgumentError(f"Lua 表达式中存在未关闭的引号:\n{'~' * len(text)}\n{text}\n{'~' * open_index + '^'}")
  return res

@strict_types
def extract_attrs_from_expr(expr: str) -> list:
  """ 从 lua 表达式中识别并抽取可能的 common_attr 列表 """
  # TODO(fangjianbing): 先用正则简单实现, 后续有必要时可改为抽取真实语法数据
  reserved_keywords = ("_G", "and", "break", "do", "else", "elseif", "end", "false", "for", "function", "if", \
      "in", "local", "nil", "not", "or", "repeat", "return", "then", "true", "until", "while", "table", \
      "tonumber", "tostring", "type", "unpack", "next", "function", "return")
  reserved_prefixes = ("math.", "string.", "table.", "os.", "util.")
  stripped_expr = _remove_literal_string(expr)
  attrs = set(w for w in re.split(r"[^a-zA-Z0-9_\.\"']+", stripped_expr) if w not in reserved_keywords \
      and all(not w.startswith(p) for p in reserved_prefixes) and re.match(r"^[a-zA-Z_][\w\.]*$", w))
  if "_G" in expr:
    attrs.update(re.findall(r"\_G\s*\[\s*'(.*?)'\s*\]", expr))
    attrs.update(re.findall(r'\_G\s*\[\s*"(.*?)"\s*\]', expr))
  return sorted(attrs)

@strict_types
def check_lua_script(expr: str, function_list: list=[]) -> None:
  check_arg("!=" not in expr, "在 Lua 中用 '~=' 表示'不等于'，你可能使用了错误的符号: '!='")
  check_arg("else if" not in expr, "你可能使用了错误的 lua else if 语法，请将 'else if' 更改为 'elseif'")
  check_arg("elif" not in expr, "你可能使用了错误的 lua else if 语法，请将 'elif' 更改为 'elseif'")
  check_arg("math.pow" not in expr, "请使用指数运算符 '^' 取代 math.pow() 函数")
  check_arg("bit." not in expr, "Lua 5.4 版本已不支持 bit 模块，位运算请直接使用位操作符，参考介绍：https://www.lua.org/manual/5.4/manual.html#3.4.2")
  check_arg(" True" not in expr, "lua 中的 bool 真值为全小写 true，你可能使用了错误的拼写")
  check_arg(" False" not in expr, "lua 中的 bool 假值为全小写 false，你可能使用了错误的拼写")

  if function_list:
    check_arg(LUPA_AVAILABLE, "import lupa 失败 请参考: https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/zhaoyang09_public/install_lupa.sh ")

  if LUPA_AVAILABLE:
    try:
      lua = lupa.LuaRuntime(unpack_returned_tuples=True)
      lua.execute(LUA_UTIL_SCRIPT)
      # 如果 function_list 中存在动态参数，则改为检测脚本中的所有函数
      if any(func.startswith("{{") and func.endswith("}}") for func in function_list):
        function_names_before_run = [name for name, value in lua.globals().items() if callable(value)]
        lua.execute(expr)
        function_names_after_run = [name for name, value in lua.globals().items() if callable(value)]
        function_list = list(set(function_names_after_run) - set(function_names_before_run))
      else:
        lua.execute(expr)
    except lupa.LuaSyntaxError as e:
      raise ArgumentError(f"Lua 表达式 '{expr}' 中的语法错误: {e}") from e
    except lupa.LuaError as e:
      raise ArgumentError(f"Lua 表达式 '{expr}' 中的运行错误: {e}") from e
    except Exception as e:
      raise ArgumentError(f"Lua 表达式 '{expr}' 未知错误: {e}") from e
    
    # 深度检查 lua 脚本
    for func in function_list:
      lua_globals = lua.globals()
      if func not in lua_globals:
        raise ArgumentError(f"Lua 表达式 '{expr}' 中缺少函数 '{func}'")
      try:
        lua_globals[func]()
      except lupa.LuaSyntaxError as e:
        raise ArgumentError(f"Lua 表达式 '{expr}' 中的函数 '{func}' 中语法存在错误: {e}") from e
      except lupa.LuaError as e:
        raise ArgumentError(f"Lua 表达式 '{expr}' 中的函数 '{func}' 存在运行错误: {e}") from e
      except Exception as e:
        raise ArgumentError(f"Lua 表达式 '{expr}' 中的函数 '{func}' 存在未知错误: {e}") from e


@strict_types
def check_parenthesis_closed(expr: str, err_message: str) -> None:
  """ 检查表达式中的括号是否成对匹配 """
  open_count = 0
  first_open = -1
  unclose_index = -1
  for i, c in enumerate(expr):
    if c == '(':
      if open_count == 0:
        first_open = i
      open_count += 1
    elif c == ')':
      if open_count <= 0:
        unclose_index = i
        break
      open_count -= 1
  if open_count != 0:
    unclose_index = first_open
  if unclose_index >= 0:
    raise ArgumentError(f"{err_message}中存在未关闭的括号:\n{'~' * len(expr)}\n{expr}\n{'~' * unclose_index + '^'}")

@strict_types
def check_abnormal_if_expr(expr: str) -> None:
  if os.environ.get("CHECK_ABNORMAL_IF_EXPR", "false") == "false":
    return
  
  def split_conditions(complex_condition: str):
    """
    拆分一个复杂的条件表达式字符串拆分为独立的条件判断列表，
    例如将 "(condition0 == 0 and ((condition1 and condition1 > 0) or (not condition2)))"
    拆分为 ['condition0 == 0', 'condition1', 'condition1 > 0', 'condition2']
    """
    # 从内到外匹配并移除所有括号
    condition_no_parentheses_list = []
    while '(' in complex_condition:
      start = complex_condition.rindex('(')
      end = complex_condition.index(')', start)
      inner_content: str = complex_condition[start+1:end]
      if ' ' in inner_content:
        condition_no_parentheses_list.append(inner_content)
        complex_condition = complex_condition[:start] + complex_condition[end+1:]
      else:
        complex_condition = complex_condition[:start] + inner_content + complex_condition[end+1:]
    condition_no_parentheses_list.append(complex_condition)  
        
    # 使用 'and' 或 'or' 为分隔符拆分字符串
    conditions_lists = [re.split(r'\s+(and|or)\s+', conditions) for conditions in condition_no_parentheses_list]
    conditions = [condition.strip() for conditions_list in conditions_lists for condition in conditions_list]
    keyword_list = ["and", "or", "not", "and not", "or not"]
    conditions = [condition for condition in conditions if not condition in keyword_list]
    # 移除 'not' 关键字
    remove_not = lambda condition: re.sub(r'not\s+', '', condition)
    conditions = [remove_not(condition).strip() for condition in conditions]
    return conditions

  def filter_conditions(conditions: list, conditions_str: str):
    """
    找到条件判断列表中，仅进行「参数存在性」判断的条件
    例如将 ['condition0 == 0', 'condition1', 'condition1 > 0', 'condition2']
    过滤为 ['condition2']
    """
    operators = ['>', '<', '>=', '<=', '==', '~=']
    pattern = '|'.join(operators)
    left_conditions = []
    removed_conditions = []

    # 第一轮筛选：参数自身是否符合条件
    for condition in conditions:
      # 移除进行了参数值比较的判断条件
      if re.search(pattern, condition):
        removed_conditions.append(condition)
        continue
      # 移除恒真/恒假/常数的判断条件
      if condition == "true" or condition == "false" or re.match(r'^[\+\-\*\/]?\d*$', condition):
        removed_conditions.append(condition)
        continue
      # 移除 string.match 类型的判断条件（例：string.match(perf_bucket_name, bucket_name)）
      if condition.startswith("string.") or re.search(rf'string\..*(.*{condition}.*)', conditions_str):
        removed_conditions.append(condition)
        continue
      # 移除"参数 or 默认值"类型的判断条件
      if re.search(rf'{condition} or (-?(\d)+)|(".*")', conditions_str):
        removed_conditions.append(condition)
        continue
      # 如果不满足所有移除条件，则暂时加入列表
      left_conditions.append(condition)

    # 第二轮筛选：如果存在参数存在性判断，但在判断存在性的同时也在其它条件中进行了参数值比较，则也合法，应被过滤掉
    is_substring = lambda target, substrings: any(target in substring for substring in substrings)
    left_conditions = [condition for condition in left_conditions if not is_substring(condition, removed_conditions)]

    return left_conditions

  def raise_exception_or_print_warning(abnormal_conditions: list):
    make_red = lambda string: "\033[91m" + string + "\033[0m"
    warning_message = f"""
      ❌ 存在以下形如 if_(do_something) 的危险条件判断，它们检查参数存在性而非取值，请注意修复
      (详见: https://docs.corp.kuaishou.com/d/home/<USER>
      当前判断方式尚不完善，若发现误判，请联系 @lixinrui07 修复，并通过 if_("condition", check_expr=False) 暂时抑制检查\n
      条件判断: {expr}, 危险变量: {abnormal_conditions}
    """
    raise ArgumentError(make_red(warning_message))
  
  conditions_list = split_conditions(expr)
  abnormal_conditions = filter_conditions(conditions_list, expr)
  if abnormal_conditions:
    raise_exception_or_print_warning(abnormal_conditions)

@strict_types
def cast_to_lua_variable_str(val) -> str:
  """ 将 python 的变量值转换为字符串形式的 lua 变量表示，用于 lua 脚本拼接 """
  if val is None:
    return "nil"
  elif isinstance(val, str):
    if '"' in val and "'" in val:
      raise ArgumentError(f"不支持表达式中同时包含单引号和双引号: {val}")
    return f'"{val}"' if "'" in val else f"'{val}'"
  elif isinstance(val, bool):
    return "true" if val else "false"
  elif isinstance(val, (int, float)):
    return str(val)
  else:
    raise ArgumentError(f"不支持将 {type(val).__name__} 类型的值转换为 lua 表示")

@strict_types
def iterate_all_processors(leaf_flows: list):
  """ 返回一组 leaf_flows 中所有 processor 的迭代器 """
  for flow in leaf_flows:
    for processor in flow._processors:
      yield processor

@strict_types
def get_item_reorder_priority(type_name: str) -> int:
  """ 获取指定类型 processor 对改变 item 结果集顺序的优先级, 0 表示不会改变, 值越大优先级越高 """
  priority_map = {
    "CommonRecoStickyArranger": 30,
    "CommonRecoIntermixArranger": 20,
    "CommonRecoVariantArranger": 20,
    "CommonRecoScoreSortArranger": 10,
    # "CommonRecoRotateArranger": 1,
    # "CommonRecoShuffleArranger": 1,
  }
  return priority_map.get(type_name, 0)

@strict_types
def has_intersection(s1: set, s2: set, ignore_empty_value=True) -> bool:
  if ignore_empty_value:
    return any(x for x in (s1 & s2))
  else:
    return len(s1 & s2) > 0

@strict_types
def gen_attr_name_with_browse_set_channel(attr: str, browsed_count: int) -> str:
  return f"{attr}@#{browsed_count}" if browsed_count is not None else attr

@strict_types
def gen_attr_name_with_item_attr_channel(attr: str, channel: str) -> str:
  return f"{attr}@@{channel}" if channel else attr

@strict_types
def gen_attr_name_with_common_attr_channel(attr: str, channel: str) -> str:
  return f"{attr}@{channel}" if channel else attr

@strict_types
def is_browse_set_channel(channel: str) -> bool:
  return channel.startswith("#")

@strict_types
def is_item_attr_channel(channel: str) -> bool:
  return channel.startswith("@")

@strict_types
def is_common_attr_channel(channel: str) -> bool:
  return len(channel) > 0 and not is_item_attr_channel(channel) and not is_browse_set_channel(channel)

@strict_types
def is_plain_attr(attr: str) -> bool:
  return "@" not in attr and not attr.startswith("_") and not attr.endswith("_")

@strict_types
def extract_attr_and_channel_name(attr: str) -> tuple:
  t = attr.split("@", 1)
  channel = None if len(t) == 1 else t[1]
  return t[0], channel

@strict_types
def strip_channel_name(attrs: set) -> set:
  return set(extract_attr_and_channel_name(attr)[0] for attr in attrs)

@strict_types
def find_processor(processors: list, pred):
  for i, processor in enumerate(processors):
    if pred(processor):
      return i, processor
  return -1, None

@strict_types
def extract_attr_names(config: list, field: str) -> set:
  ret = set()
  for c in config:
    if isinstance(c, str):
      ret.add(c)
    elif isinstance(c, dict):
      if (field in c):
        ret.add(c[field])
      else:
        ret.add(c["name"])
    else:
      raise ArgumentError(f"该 list 中存在不支持的配置类型 {type(c)}: {config}")
  return ret

@strict_types
def extract_table_and_attr_name(attr: str) -> tuple:
  if os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "false":
    return "", attr
  t = attr.split("::", 1)
  if len(t) == 1:
    return "", attr
  else:
    return t[0], t[1]

@strict_types
def extract_table_attrs_config(attrs: set) -> list:
  tables_config = {}
  for attr in attrs:
    table_name, attr_name = extract_table_and_attr_name(attr)
    if table_name in tables_config:
      tables_config[table_name]["attrs"].append(attr_name)
    else:
      tables_config[table_name] = {"table_name": table_name, "attrs": [attr_name]}
  res = list(tables_config.values())
  return res

@strict_types
def aggregate_table_attrs_config(attrs_config: list) -> set:
  attrs = set()
  for table in attrs_config:
    table_name = table.get("table_name")
    table_attrs = table.get("attrs", [])
    attrs.update(try_add_table_name(table_name, table_attrs))
  return attrs

def try_add_table_name(table_name: str, attrs: set) -> set:
  if not table_name or os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "false":
    return attrs
  return set([f'{table_name}::{attr_name}' if "::" not in attr_name else attr_name for attr_name in attrs])

@contextlib.contextmanager
def set_env(**variables):
  """ `with` context temporarily modify the environment variables"""
  backup_env = os.environ.copy()
  try:
    os.environ.update(variables)
    yield
  finally:
    os.environ = backup_env

def get_dynamic_param(name):
  return "{{" + name + "}}"

_CURRENT_USER = ""

def get_current_user() -> str:
  global _CURRENT_USER
  if _CURRENT_USER:
    return _CURRENT_USER
  user = getpass.getuser()
  if user and user not in {"root", "web_server"}:
    _CURRENT_USER = user
    return _CURRENT_USER
  if user == "root":
    git_username = os.popen("git config user.name").readlines()
    if git_username:
      git_username = git_username[0].strip()
      if git_username:
        _CURRENT_USER = git_username
        return _CURRENT_USER
  hostname = socket.gethostname()
  if hostname:
    _CURRENT_USER = hostname
    return _CURRENT_USER

__CONFIG_VERSION = ""

def get_config_version() -> str:
  version = os.environ.get("DRAGONFLY_CONFIG_VERSION", "")
  if version:
    return version
  global __CONFIG_VERSION
  if __CONFIG_VERSION:
    return __CONFIG_VERSION
  ci_job_id = os.environ.get("CI_JOB_ID", "")
  if ci_job_id:
    git_repo_url = os.environ.get("GIT_REPO_URL", "")
    if git_repo_url:
      git_branch = os.environ.get("GIT_BRANCH", "")
      git_commit_id = os.environ.get("COMMIT_ID", "")
      # 以 *************************:reco-cpp/dragon.git 为例
      # 去掉:前面和 .git，得到 reco-cpp/dragon
      git_repo_url = git_repo_url[git_repo_url.find(":")+1:git_repo_url.rfind(".")]
      __CONFIG_VERSION = f"{git_repo_url}@{git_branch}@{git_commit_id}"
      return __CONFIG_VERSION
    else:
      __CONFIG_VERSION = ci_job_id
      return __CONFIG_VERSION
  username = get_current_user()
  __CONFIG_VERSION = hashlib.md5(f"{username},{time.time()}".encode('utf-8')).hexdigest()
  __CONFIG_VERSION = f"{__CONFIG_VERSION}_local"
  return __CONFIG_VERSION

@strict_types
def RaiseError(processor_name: str, processor_code_line: str, err_msg: str):
  global BLAME_ERROR_CODE
  if BLAME_ERROR_CODE:
    err_msg = f"[算子名称]\n{processor_name}\n[代码调用]\n{processor_code_line}[错误信息]\n{err_msg}"
  global PRINT_ALL_ERROR
  if not PRINT_ALL_ERROR:
    raise LogicError(err_msg)
  global ERR_MSG_SET
  ERR_MSG_SET.add(err_msg)
