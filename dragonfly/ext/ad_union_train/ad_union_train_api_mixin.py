#!/usr/bin/env python3
# coding=utf-8
"""
filename: ad_union_train_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, ad_union_train api mixin
author: z<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-03-16 20:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_union_train_retriever import *
from .ad_union_train_enricher import *
from .ad_union_train_observer import *

class AdUnionTrainApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 是 ad-reco 联合数据流训练的的专用 Mixin, 仅允许添加 ad_union_train 相关的接口, 具体包含:
  - RecoWithAdLogRetriever
  - MixSampleLogRetriever
  - SlotSignFeatureRemapEnricher
  - KlearnerParameterAttrEnricher
  - AdJointLabeldLogRetriever
  - AdlogParameterAttrEnricher
  - AdlogOnlineParameterAttrEnricher
  - ExtractCachedPtrAttrEnricher
  - KlearnerOnlineParameterAttrEnricher
  - CommonRecoDistributedIndexAdPredictItemAttrEnricher
  """
  def retrieve_from_reco_with_ad_log(self, **kwargs):
    """
    RecoWithAdLogRetriever
    ------
    从 `ks::reco::RecoWithAdLog` 触发

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 `ks::reco::RecoWithAdLog`。

    `reason_reco`: [int] 触发 reco_photo 的 reason，默认为 0。

    `reason_ad`: [int] 触发 ad_photo 的 reason，默认为 0。

    `save_flag_reco_to`: [string] item 侧存储是否包含 reco_photo_info 的 item attr，默认不存。

    `save_flag_ad_to`: [string] item 侧存储是否包含 ad_photo_info 的 item attr，默认不存。

    `save_reco_photo_to`: [string] 存储 reco_photo_info 的 item attr，默认不存。

    `save_ad_photo_to`: [string] 存储 ad_photo_info 的 item attr，默认不存。

    `save_ad_label_info_to`: [string] 存储 ad_label_info 的 item attr，默认不存。

    `ttl_seconds`: [int] 选配项，默认底层会复用 protobuf 的对象空间，可通过该项配置来定期清理内存空间

    调用示例
    ------
    ``` python
    .retrieve_from_reco_with_ad_log(
      from_extra_var="reco_with_ad_log",
      save_flag_reco_to="flag_reco",
      save_flag_ad_to="flag_ad",
      save_reco_photo_to="reco_photo_info",
      save_ad_photo_to="ad_photo_info",
      save_ad_label_info_to="ad_label_info",
    )
    ```
    """
    self._add_processor(RecoWithAdLogRetriever(kwargs))
    return self

  def retrieve_from_mix_sample_log(self, **kwargs):
    """
    MixSampleLogRetriever
    ------
    从 `ks::log::MixSampleLog` 触发

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 `ks::log::MixSampleLog`。

    `save_ad_context_to`: [string] 存储 ad context 的 item attr，默认不存。

    `save_reco_live_user_info_to`: [string] 存储 reco live user info 的 item attr，默认不存。

    `save_reco_user_info_to`: [string] 存储 reco user info 的 item attr，默认不存。

    `save_ad_user_info_to`: [string] 存储 ad user info 的 item attr，默认不存。

    `save_reco_live_photo_to`: [string] 存储 reco live photo info 的 item attr，默认不存。

    `save_ad_photo_to`: [string] 存储 ad photo info 的 item attr，默认不存。

    `save_sample_info_to`: [string] 存储 sample info 的 item attr，默认不存。

    `ttl_seconds`: [int] 选配项，默认底层会复用 protobuf 的对象空间，可通过该项配置来定期清理内存空间

    调用示例
    ------
    ``` python
    .retrieve_from_reco_with_ad_log(
      from_extra_var="mix_sample_log",
      save_ad_context_to="ad_context",
      save_reco_live_user_info_to="reco_live_user_info",
      save_reco_user_info_to="reco_user_info",
      save_ad_user_info_to="ad_user_info",
      save_reco_live_photo_to="reco_live_photo_info",
      save_ad_photo_to="ad_photo_info",
      save_ad_label_info_to="ad_label_info",
    )
    ```
    """
    self._add_processor(MixSampleLogRetriever(kwargs))
    return self

  def remap_slot_sign_feature(self, **kwargs):
    """SlotSignFeatureRemapEnricher
    ------
    使用 SlotSignFeatureRemapEnricher 进行特征 slot 和 sign 前缀转换。

    注意：输入 slot 和 sign 前缀不一定相等， 例如 mio 抽取的特征，当 slot 超过 16 时，sign 前缀比 slot 小 16。

    参数配置
    ------
    `common_attrs_input`: [list] User 侧需要映射的特征名列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `common_slots_input`: [list] 从哪些 common_attr 中读出 common_slots 列表，需要和 common_parameters_input 对齐，默认为空。

    `common_parameters_input`: [list] 从哪些 common_attr 中读出 common_signs 列表，需要和 common_slots_input 对齐，默认为空。

    `item_attrs_input`: [list] Item 侧需要映射的特征列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `item_slots_input`: [list] 从哪些 item_attr 中读出 item_slots 列表，需要和 item_parameters_input 对齐，默认为空。

    `item_parameters_input`: [list] 从哪些 item_attr 中读出 item_signs 列表，需要和 item_slots_input 对齐，默认为空。

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16]。

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16]。

    `common_map_list`: [list] common 侧配置信息， list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶。

    `item_map_list`: [list] item 侧配置信息，含义同 `common_map_list` 。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不输出 User 侧特征。

    `common_parameters_output`: [string] User 侧 sign 输出到给定 common attr，默认不输出 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不输出 Item 侧特征。

    `item_parameters_output`: [string] Item 侧 sign 输出到给定 item attr，默认不输出 Item 侧特征。

    `slot_as_attr_name`: [bool] 是否将 slot 作为 attr name，sign 作为 value，如果为 True 则 common_slots_output，common_parameters_output，item_slots_output，item_parameters_output 都需要为空。

    `slot_as_attr_name_prefix`: [string] `slot_as_attr_name` 为 True 时 attr name 的前缀， False 时不生效，默认为空字符串。

    `use_murmur3hash64`: [bool] 是否使用 murmur3hash64 方式进行hash; 默认值为 False。

    `use_djb2_hash64`: [bool] 是否使用 use_djb2_hash64 方式进行hash; 默认值为 False。

    调用示例
    ------
    ``` python


    .remap_slot_sign_feature(
      common_attrs_input=['reco_37', 'reco_38'],
      common_slots_input=['reco_common_slots', 'reco_common_cofea_slots'],
      common_parameters_input=['reco_common_parameters', 'reco_common_cofea_parameters'],
      item_attrs_input=['reco_18', 'reco_26'],
      item_slots_input=['reco_item_slots'],
      item_parameters_input=['reco_item_parameters'],
      sign_prefix_bit_num_input=16,
      sign_prefix_bit_num_output=10,
      common_map_list=[['reco_37', 7, 7], ['reco_38', 8, 8], [1880, 180, 181, 10001], [1884, 184, 181]},
      item_map_list=[['reco_18', 18, 18, 200001], ['reco_26', 16, 16], [1068, 568, 668]],
      common_slots_output='common_slots',
      common_parameters_output='common_signs',
      item_slots_output='item_slots',
      item_parameters_output='item_signs'
    )
    ```
    """
    self._add_processor(SlotSignFeatureRemapEnricher(kwargs))
    return self

  def extract_klearner_parameter(self, **kwargs):
    """
    KlearnerParameterAttrEnricher
    -----
    抽取Klearner Parameter

    参数配置
    -----
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::RecoWithAdLog

    `ad_user_info_attr`: [string] 从给定的  读取 `mix::kuaishou::ad::algorithm::UserInfo`

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `mix::kuaishou::ad::algorithm::Item` 对象

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个extractor

    `output_prefix`: [string] 输出参数slot的前缀，默认是 klearner_

    调用示例
    -----
    ```python
    .extract_klearner_parameter(
        from_extra_var = "reco_with_ad_log",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(KlearnerParameterAttrEnricher(kwargs))
    return self

  def extract_mix_log_klearner_parameter(self, **kwargs):
    """
    MixLogKlearnerParameterAttrEnricher
    -----
    根据mix sample log 抽取Klearner Parameter

    参数配置
    -----
    `from_extra_var`: [string] 从给定的 Extra Var 读取 kuaishou::log::MixSampleLog

    `ad_user_info_attr`: [string] 从给定的common_attr 读取 `mix::kuaishou::ad::algorithm::UserInfo`

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `mix::kuaishou::ad::algorithm::Item` 对象

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个extractor

    `ad_context_attr`: [string] 从给定的common_attr 读取 `mix::kuaishou::ad::algorithm::Context`

    `output_prefix`: [string] 输出参数slot的前缀，默认是 klearner_

    调用示例
    -----
    ```python
    .extract_klearner_parameter(
        from_extra_var = "mix_sample_log",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        ad_context_attr = "ad_context_info",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(MixLogKlearnerParameterAttrEnricher(kwargs))
    return self

  def retrieve_from_ad_joint_labeled_log(self, **kwargs):
    """
    AdJointLabeldLogRetriever
    ------
    从 mix::kuaishou::ad::algorithm::AdJointLabeledLog 触发

    参数配置
    ------
    `from_extra_var`: [string] 必配项，从给定的 Extra Var 读取 mix::kuaishou::ad::algorithm::AdJointLabeledLog。

    `reason`: [int] 选配项，触发 photo 的 reason，默认为 0。

    `save_item_to`: [string] 选配项，存储 pb 的 `item` 字段到哪个 item attr，默认不存。

    调用示例
    ------
    ``` python
    .retrieve_from_ad_joint_labeled_log(
      from_extra_var="ad_joint_labeled_log",
      save_item_to="photo_info",
    )
    ```
    """
    self._add_processor(AdJointLabeldLogRetriever(kwargs))
    return self

  def extract_adlog_parameter(self, **kwargs):
    """
    AdlogParameterAttrEnricher
    -----
    抽取 Adlog Parameter

    参数配置
    -----
    `from_extra_var`: [string] 读取 mix.kuaishou.ad.algorithm.AdJointLabeledLog 的 Serialized String

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个 extractor

    `output_prefix`: [string] 输出参数 slot 的前缀，默认是 klearner_

    调用示例
    -----
    ```python
    .extract_adlog_parameter(
        from_extra_var = "serialized_ad_log",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(AdlogParameterAttrEnricher(kwargs))
    return self

  def extract_adlog_online_parameter(self, **kwargs):
    """
    AdlogOnlineParameterAttrEnricher
    -----
    抽取在线 Adlog Parameter

    参数配置
    -----
    `ad_request_info_attr`: [string] 从给定的 common attr 中取出 request 信息，预估会使用。

    `ad_user_info_attr`: [string] 从给定的  读取 `kuaishou::ad::algorithm::UserInfo`。

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `kuaishou::ad::algorithm::Item` 对象。

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个 extractor。

    `output_prefix`: [string] 输出参数 slot 的前缀，默认是 klearner_。

    `enable_remap`: [bool] 这是为了支持性能优化，线上将 remap 的功能合并到抽特征一起，设为 true 才会生效。

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16]; enable_remap 为 true 才有意义。

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16]; enable_remap 为 true 才有意义。

    `common_map_list`: [list] common 侧配置信息， list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶; enable_remap 为 true 才有意义。

    `item_map_list`: [list] item 侧配置信息，含义同 `common_map_list`; enable_remap 为 true 才有意义。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不输出 User 侧特征; enable_remap 为 true 才有意义。

    `common_parameters_output`: [string] User 侧 sign 输出到给定 common attr，默认不输出 User 侧特征; enable_remap 为 true 才有意义。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不输出 Item 侧特征; enable_remap 为 true 才有意义。

    `item_parameters_output`: [string] Item 侧 sign 输出到给定 item attr，默认不输出 Item 侧特征; enable_remap 为 true 才有意义。

    `input_prefix`: [string] `input_prefix` 为 True 时 attr name 的前缀， False 时不生效，默认为空字符串; enable_remap 为 true 才有意义。

    `use_murmur3hash64`: [bool] 是否使用 murmur3hash64 方式进行hash; 默认值为 False; enable_remap 为 true 才有意义。

    `use_djb2_hash64`: [bool] 是否使用 use_djb2_hash64 方式进行hash; 默认值为 False; enable_remap 为 true 才有意义。

    调用示例
    -----
    ```python
    .extract_adlog_online_parameter(
        ad_request_info_attr = "add_request_info_ptr",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(AdlogOnlineParameterAttrEnricher(kwargs))
    return self

  def extract_cached_ptr(self, **kwargs):
    """
    ExtractCachedPtrAttrEnricher
    -----
    在有些场合下，会把一些大型对象保存在某处，然后通过 request 中的 attr 传递下去，使用的时候需要取到指针

    参数配置
    -----
    `input_attr`: [string] 持有指针值的 attr 名字;

    `output_attr`: [string] 去除的指针 ptr 保存到的 common attr 名字;

    调用示例
    -----
    ```python
    .extract_cached_ptr(
        input_attr = "ad_user_info_ptr",
        output_attr = "ad_user_info")
    ```
    """
    self._add_processor(ExtractCachedPtrAttrEnricher(kwargs))
    return self

  def extract_klearner_online_parameter(self, **kwargs):
    """
    KlearnerOnlineParameterAttrEnricher
    -----
    线上预估抽取Klearner Parameter

    参数配置
    -----
    `ad_request_info_attr`: [string] 从给定的 common attr 中取出 request 信息，预估会使用

    `ad_user_info_attr`: [string] 从给定的  读取 `mix::kuaishou::ad::algorithm::UserInfo`

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `mix::kuaishou::ad::algorithm::Item` 对象

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个extractor

    `output_prefix`: [string] 输出参数slot的前缀，默认是 klearner_

    调用示例
    -----
    ```python
    .extract_klearner_online_parameter(
        ad_request_info_attr = "add_request_info_ptr",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(KlearnerOnlineParameterAttrEnricher(kwargs))
    return self

  def get_ad_item_attr_by_distributed_index(self, **kwargs):
    """
    CommonRecoDistributedIndexAdPredictItemAttrEnricher
    ------
    从分布式索引(distributed_photo_info)获取 `kuaishou::ad::algorithm::Item` 数据并抽取字段作为 ItemAttr 写入 Context 供后续 Processor 使用

    注意 : 这个 processor 只用于获取广告的物料信息，不可被其它业务使用

    参数配置
    ------
    `photo_store_kconf_key`: [string] 必填项，kconf_key， kconf 内容为本地缓存配置和被调服务信息。[kconf配置说明参考](https://docs.corp.kuaishou.com/d/home/<USER>
    
    `use_dynamic_photo_store`: [bool] 选配项，是否使用 dynamic_photo_store，默认 false。推荐设置成 true，性能更好，但是只能通过 photo_store_kconf_key 对应的 kconf 进行配置，不再使用本地的 flag。

    `attrs`: [list] 选配项，将 PhotoInfo 中的字段作为 ItemAttr 进行保存，改列表中的 value 支持两种格式的值：
      - string 格式：可直接把 proto 中第一层的字段填入同名 item_attr 中
      - dict 格式: 用于取嵌套的内层字段，包含 name 和 path 两个配置项，示例：{"name": "author_id", "path": "author.id"}，将 author 里的 id 字段填入名为 author_id 的 item_attr 中

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `save_item_info_to_attr`: [string] 选配项，可将分布式索引返回的 PhotoInfo 直接存到指定的 extra 类型 ItemAttr 中，默认不存

    `additional_item_source`: [dict] 选配项，若配置则为下列数据源里的 item 获取 ItemAttr，忽略当前结果集里的 item
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr
      - `latest_browse_set_item`: [int] 需要从 BrowseSet 中获取 item 的数目（最近浏览的），值为 0 时全部抽取，值小于 0 时获取最远浏览的 item，不需要时需删除该项配置

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    调用示例
    ------
    ``` python
    .get_ad_item_attr_by_distributed_index(
      photo_store_kconf_key = "reco.distributedIndex.hotPhotoStoreConfig",
      use_dynamic_photo_store = true,
      attrs = [
        "photo_id",
        "caption",
        "subtitle",
        { "name": "author_id", "path": "author.id" }
        "upload_time",
        "show_count",
        "click_count",
        "like_count",
        "follow_count",
        "forward_count",
        "unlike_count",
        "comment_count",
      ]
    )
    ```
    """
    self._add_processor(CommonRecoDistributedIndexAdPredictItemAttrEnricher(kwargs))
    return self

  def opt_remap_slot_sign_feature(self, **kwargs):
    """OptSlotSignFeatureRemapEnricher
    ------
    使用 OptSlotSignFeatureRemapEnricher 进行特征 slot 和 sign 前缀转换。

    注意：输入 slot 和 sign 前缀不一定相等， 例如 mio 抽取的特征，当 slot 超过 16 时，sign 前缀比 slot 小 16。

    参数配置
    ------
    `common_attrs_input`: [list] User 侧需要映射的特征名列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `common_slots_input`: [list] 从哪些 common_attr 中读出 common_slots 列表，需要和 common_parameters_input 对齐，默认为空。

    `common_parameters_input`: [list] 从哪些 common_attr 中读出 common_signs 列表，需要和 common_slots_input 对齐，默认为空。

    `item_attrs_input`: [list] Item 侧需要映射的特征列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `item_slots_input`: [list] 从哪些 item_attr 中读出 item_slots 列表，需要和 item_parameters_input 对齐，默认为空。

    `item_parameters_input`: [list] 从哪些 item_attr 中读出 item_signs 列表，需要和 item_slots_input 对齐，默认为空。

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16]。

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16]。

    `common_map_list`: [list] common 侧配置信息， list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶。

    `item_map_list`: [list] item 侧配置信息，含义同 `common_map_list` 。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不输出 User 侧特征。

    `common_parameters_output`: [string] User 侧 sign 输出到给定 common attr，默认不输出 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不输出 Item 侧特征。

    `item_parameters_output`: [string] Item 侧 sign 输出到给定 item attr，默认不输出 Item 侧特征。

    `slot_as_attr_name`: [bool] 是否将 slot 作为 attr name，sign 作为 value，如果为 True 则 common_slots_output，common_parameters_output，item_slots_output，item_parameters_output 都需要为空。

    `slot_as_attr_name_prefix`: [string] `slot_as_attr_name` 为 True 时 attr name 的前缀， False 时不生效，默认为空字符串。

    `use_murmur3hash64`: [bool] 是否使用 murmur3hash64 方式进行hash; 默认值为 False。

    `use_djb2_hash64`: [bool] 是否使用 use_djb2_hash64 方式进行hash; 默认值为 False。

    调用示例
    ------
    ``` python


    .opt_remap_slot_sign_feature(
      common_attrs_input=['reco_37', 'reco_38'],
      common_slots_input=['reco_common_slots', 'reco_common_cofea_slots'],
      common_parameters_input=['reco_common_parameters', 'reco_common_cofea_parameters'],
      item_attrs_input=['reco_18', 'reco_26'],
      item_slots_input=['reco_item_slots'],
      item_parameters_input=['reco_item_parameters'],
      sign_prefix_bit_num_input=16,
      sign_prefix_bit_num_output=10,
      common_map_list=[['reco_37', 7, 7], ['reco_38', 8, 8], [1880, 180, 181, 10001], [1884, 184, 181]},
      item_map_list=[['reco_18', 18, 18, 200001], ['reco_26', 16, 16], [1068, 568, 668]],
      common_slots_output='common_slots',
      common_parameters_output='common_signs',
      item_slots_output='item_slots',
      item_parameters_output='item_signs'
    )
    ```
    """
    self._add_processor(OptSlotSignFeatureRemapEnricher(kwargs))
    return self


  def adunion_live_v2_gsu_with_cluster(self, **kwargs):
    """
    AdunionLiveV2GsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .adunion_live_v2_gsu_with_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(AdunionLiveV2GsuWithClusterEnricher(kwargs))
    return self


  def adunion_v1_live_gsu_aid2aid(self, **kwargs):
    """
    AdunionV1LiveGsuAid2AidEnricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .adunion_v1_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[346, 347, 349, 348, 350, 353, 354],
                      mio_slots_id=[346, 347, 349, 348, 350, 353, 354]
                      )
    ```
    """
    self._add_processor(AdunionV1LiveGsuAid2AidEnricher(kwargs))
    return self


  def opt_extract_adlog_online_parameter(self, **kwargs):
    """
    AdlogOptOnlineParameterAttrEnricher
    -----
    抽取在线 Adlog Parameter

    参数配置
    -----
    `ad_request_info_attr`: [string] 从给定的 common attr 中取出 request 信息，预估会使用。

    `ad_user_info_attr`: [string] 从给定的  读取 `kuaishou::ad::algorithm::UserInfo`。

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `kuaishou::ad::algorithm::Item` 对象。

    `extractor_list`: [list[dict]] 输入的 extractor 配置，list 中的每个 dict 定义一个 extractor。

    `output_prefix`: [string] 输出参数 slot 的前缀，默认是 klearner_。

    `enable_remap`: [bool] 这是为了支持性能优化，线上将 remap 的功能合并到抽特征一起，设为 true 才会生效。

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16]; enable_remap 为 true 才有意义。

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16]; enable_remap 为 true 才有意义。

    `common_map_list`: [list] common 侧配置信息， list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶; enable_remap 为 true 才有意义。

    `item_map_list`: [list] item 侧配置信息，含义同 `common_map_list`; enable_remap 为 true 才有意义。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不输出 User 侧特征; enable_remap 为 true 才有意义。

    `common_parameters_output`: [string] User 侧 sign 输出到给定 common attr，默认不输出 User 侧特征; enable_remap 为 true 才有意义。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不输出 Item 侧特征; enable_remap 为 true 才有意义。

    `item_parameters_output`: [string] Item 侧 sign 输出到给定 item attr，默认不输出 Item 侧特征; enable_remap 为 true 才有意义。

    `input_prefix`: [string] `input_prefix` 为 True 时 attr name 的前缀， False 时不生效，默认为空字符串; enable_remap 为 true 才有意义。

    `use_murmur3hash64`: [bool] 是否使用 murmur3hash64 方式进行hash; 默认值为 False; enable_remap 为 true 才有意义。

    `use_djb2_hash64`: [bool] 是否使用 use_djb2_hash64 方式进行hash; 默认值为 False; enable_remap 为 true 才有意义。

    `missing_item_filling_zero`: [bool] 是否开启索引 miss 的 item 预估值置 0 的开关。

    `missing_item_attr_name`: [bool] `missing_item_filling_zero` 为 true 的情况下，把 item 索引是否 miss 的 flag 写到 `missing_item_attr_name` 中。

    调用示例
    -----
    ```python
    .opt_extract_adlog_online_parameter(
        ad_request_info_attr = "add_request_info_ptr",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        extractor_list = [
          {
            "class": "ExtractUserId",
            "category": "dense_user",
            "field": 1,
            "size": 20000001
          },
          {
            "class": "ExtractUserFollow",
            "category": "dense_user",
            "field": 2,
            "size": 10000001
          },
        ],
        output_prefix = "klearner_")
    ```
    """
    self._add_processor(AdlogOptOnlineParameterAttrEnricher(kwargs))
    return self

  def dump_online_adlog(self, **kwargs):
    """
    DumpOnlineAdLogObserver
    -----
    ad 使用 reco infer 进行线上预估需要构建特定格式的数据，用于一致性对比

    参数配置
    -----
    `ad_request_info_attr`: [string] 从给定的 common attr 中取出 request 信息，预估会使用。

    `ad_user_info_attr`: [string] 从给定的  读取 `kuaishou::ad::algorithm::UserInfo`。

    `ad_photo_attr`: [string] 从指定的 item_attr 中获取 `kuaishou::ad::algorithm::Item` 对象。

    `to_file_folder`: [string] 指定将数据保存到的目录。

    `to_file_name`: [string] 指定保存数据的文件名，可以采用别的 processor 动态生成的 common attr 作为文件名。

    `base64_compress`: [bool] 保存的数据是否使用 base64 进行压缩；默认值为 true，开启 base64 压缩。

    调用示例
    -----
    ```python
    .dump_online_adlog(
        ad_request_info_attr = "add_request_info_ptr",
        ad_user_info_attr = "ad_user_info",
        ad_photo_attr = "ad_photo_info",
        to_file_name = "name_from_other_processor",
        to_file_folder = "../log",
        base64_compress = True
        )
    ```
    """
    self._add_processor(DumpOnlineAdLogObserver(kwargs))
    return self


  def gsu_with_cluster_neighbor(self, **kwargs):
    """
    GsuWithClusterNeighborEnricher
    ------
    liuxin GsuWithClusterNeighborEnricher

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    示例
    ------
    ``` python
    .gsu_with_cluster_neighbor(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(GsuWithClusterNeighborEnricher(kwargs))
    return self
