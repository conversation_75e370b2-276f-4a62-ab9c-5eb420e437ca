#!/usr/bin/env python3
"""
filename: ad_union_train_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for ad_union_train
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-03-16 20:34:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever


class RecoWithAdLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_reco_with_ad_log"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "save_reco_photo_to" in self._config:
      attrs.add(self._config["save_reco_photo_to"])
    if "save_ad_photo_to" in self._config:
      attrs.add(self._config["save_ad_photo_to"])
    if "save_flag_reco_to" in self._config:
      attrs.add(self._config["save_flag_reco_to"])
    if "save_flag_ad_to" in self._config:
      attrs.add(self._config["save_flag_ad_to"])
    if "save_ad_label_info_to" in self._config:
      attrs.add(self._config["save_ad_label_info_to"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])


class MixSampleLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_mix_sample_log"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "save_reco_live_photo_to" in self._config:
      attrs.add(self._config["save_reco_live_photo_to"])
    if "save_ad_photo_to" in self._config:
      attrs.add(self._config["save_ad_photo_to"])
    if "save_sample_info_to" in self._config:
      attrs.add(self._config["save_sample_info_to"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if 'save_ad_context_to' in self._config:
      attrs.add(self._config['save_ad_context_to'])
    if 'save_reco_live_user_info_to' in self._config:
      attrs.add(self._config['save_reco_live_user_info_to'])
    if 'save_ad_user_info_to' in self._config:
      attrs.add(self._config['save_ad_user_info_to'])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

class AdJointLabeldLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_ad_joint_labeled_log"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("from_extra_var"), "`from_extra_var` 不能为空 ")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "save_item_to" in self._config:
      attrs.add(self._config["save_item_to"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])


