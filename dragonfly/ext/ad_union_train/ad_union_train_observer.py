#!/usr/bin/env python3
# coding=utf-8

"""
filename: ad_union_train_observer.py 
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for ad_union_train
author: <EMAIL>
date: 2021-10-11 14:10:00
"""
import operator

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class DumpOnlineAdLogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_online_adlog"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["ad_photo_attr"]:
      if key not in self._config or len(self._config[key]) < 1:
        raise ArgumentError(f"value of {key} does not find in config or value is empty, config: {self._config}")
      attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("to_file_name")))
    for key in ["ad_user_info_attr", "ad_request_info_attr"]:
      if key not in self._config or len(self._config[key]) < 1:
        raise ArgumentError(f"value of {key} does not find in config or value is empty, config: {self._config}")
      attrs.add(self._config[key])
    return attrs
