#!/usr/bin/env python3
"""
filename: ad_union_train_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for ad_union_train
author: z<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-03-19 19:34:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher


class SlotSignFeatureRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "remap_slot_sign_feature"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_attrs_input", "item_slots_input", "item_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("item_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_attrs_input", "common_slots_input", "common_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("common_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs


class AdunionLiveV2GsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_live_v2_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret



class AdunionV1LiveGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adunion_v1_live_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class KlearnerParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_klearner_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["from_extra_var", "ad_user_info_attr"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["ad_photo_attr"]])

class MixLogKlearnerParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_mix_log_klearner_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["from_extra_var", "ad_user_info_attr", "ad_context_attr"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["ad_photo_attr"]])

class AdlogParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_adlog_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["from_extra_var"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

class CommonRecoDistributedIndexAdPredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ad_item_attr_by_distributed_index"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = { self._config.get("output_attr_prefix", "") + v["name"] if isinstance(v, dict) else v \
        for v in self._config.get("attrs", []) }
    save_item_info_to_attr = self._config.get("save_item_info_to_attr")
    if save_item_info_to_attr is not None:
      attrs.add(save_item_info_to_attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

class ExtractCachedPtrAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_cached_ptr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["input_attr"]:
      attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_attr"]:
      attrs.add(self._config[key])
    return attrs

class KlearnerOnlineParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_klearner_online_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["ad_request_info_attr", "ad_user_info_attr"]:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["ad_photo_attr"]])

class AdlogOnlineParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_adlog_online_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["ad_request_info_attr", "ad_user_info_attr"]:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["ad_photo_attr"]])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get('enable_remap', False):
      return attrs
    if 'item_slots_output' in self._config:
      attrs.add(self._config['item_slots_output'])
    if 'item_parameters_output' in self._config:
      attrs.add(self._config['item_parameters_output'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if not self._config.get('enable_remap', False):
      return attrs
    if 'common_slots_output' in self._config:
      attrs.add(self._config['common_slots_output'])
    if 'common_parameters_output' in self._config:
      attrs.add(self._config['common_parameters_output'])
    return attrs

class OptSlotSignFeatureRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "opt_remap_slot_sign_feature"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_attrs_input", "item_slots_input", "item_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("item_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_attrs_input", "common_slots_input", "common_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("common_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs

class AdlogOptOnlineParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "opt_extract_adlog_online_parameter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["ad_request_info_attr", "ad_user_info_attr"]:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["ad_photo_attr"])
    if self._config.get('missing_item_filling_zero', False) and len(self._config.get('missing_item_attr_name', '')) > 0:
      ret.add(self._config['missing_item_attr_name'])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get('enable_remap', False):
      return attrs
    if 'item_slots_output' in self._config:
      attrs.add(self._config['item_slots_output'])
    if 'item_parameters_output' in self._config:
      attrs.add(self._config['item_parameters_output'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if not self._config.get('enable_remap', False):
      return attrs
    if 'common_slots_output' in self._config:
      attrs.add(self._config['common_slots_output'])
    if 'common_parameters_output' in self._config:
      attrs.add(self._config['common_parameters_output'])
    return attrs


class GsuWithClusterNeighborEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_neighbor"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret