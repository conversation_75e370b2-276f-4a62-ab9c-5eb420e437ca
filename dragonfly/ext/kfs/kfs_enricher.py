#!/usr/bin/env python3
"""
filename: kfs_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for KFS
author: <EMAIL>
date: 2021-07-28
"""
import yaml

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher


class KFSFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kfs_run"

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    return set(self._config["kfs_req_attr_name_map"].values())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    feature_yaml = yaml.full_load(self._config["feature_yaml"])
    return set([
      feature["name"] for feature in feature_yaml["cfg"]
      if feature.get("output", True) and not feature.get("share", False)])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    feature_yaml = yaml.full_load(self._config["feature_yaml"])
    return set([
      feature["name"] for feature in feature_yaml["cfg"]
      if feature.get("output", True) and feature.get("share", False)])

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    kfs_req_attr_name_map = self._config["kfs_req_attr_name_map"]
    attrs = set()
    for opt_field in ["item_id", "author_id", "source"]:
      attr = kfs_req_attr_name_map.get(opt_field)
      if attr:
        attrs.add(attr)
    for attr in kfs_req_attr_name_map["item_attrs_to_extra_info"]:
      if attr:
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    kfs_req_attr_name_map = self._config["kfs_req_attr_name_map"]
    attrs = set([kfs_req_attr_name_map["session_id"]])
    for opt_field in ["query", "query_id"]:
      attr = kfs_req_attr_name_map.get(opt_field)
      if attr:
        attrs.add(attr)
    attrs.update(self._config.get("kfs_data_source",{}).values())
    return attrs


class JubaoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "jubao_feature_service"

  @strict_types
  def _get_input_attrs(self) -> set:
    return set(self._config["request_config"].values())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    feature_yaml_str = self._config.get("extract_output_attrs_from_feature_yaml", "")
    if len(feature_yaml_str) > 0:
      feature_yaml = yaml.full_load(feature_yaml_str)
      attrs = set([
        feature["name"] for feature in feature_yaml["cfg"]
        if feature.get("output", True) and not feature.get("share", False)
      ])
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    feature_yaml_str = self._config.get("extract_output_attrs_from_feature_yaml", "")
    if len(feature_yaml_str) > 0:
      feature_yaml = yaml.full_load(feature_yaml_str)
      attrs = set([
        feature["name"] for feature in feature_yaml["cfg"]
        if feature.get("output", True) and feature.get("share", False)
      ])
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    request_config = self._config["request_config"]
    attrs = set()
    for opt_field in ["item_id", "author_id"]:
      attr = request_config.get(opt_field)
      if attr:
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    request_config = self._config["request_config"]
    attrs = set([request_config["session_id"]])
    for opt_field in ["query"]:
      attr = request_config.get(opt_field)
      if attr:
        attrs.add(attr)
    attrs.update(self._config.get("data_source", {}).values())
    return attrs
