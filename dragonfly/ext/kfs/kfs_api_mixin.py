#!/usr/bin/env python3
# coding=utf-8
"""
filename: kfs_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, KFS api mixin
author: <EMAIL> <EMAIL>
date: 2021-10-26
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kfs_enricher import *


class KFSApiMixin(CommonLeafBaseMixin):
    """
    该 Mixin 包含 KFS 相关的 Processor 接口:
    - KFSFeatureEnricher : 特征工程动态库调用形式
    - JubaoEnricher : 特征工程 RPC 调用形式 （推荐使用）
    """

    def kfs_run(self, **kwargs):
        """
        KFSFeatureEnricher
        ------
        执行 R-KFS run，并根据 feature_yaml 将结果分别导入当前的 ItemAttr 和 CommonAttr 中

        参数配置
        ------
        `feature_yaml`: [string] 必填 R-KFS feature.yaml 配置

        `kfs_req_attr_name_map`: [dict] 必填 用于构造 KFS request 的 attr name mapping 。 其中，key 为 KFS request 中的 field name，value 为当前的 attr name
          - `session_id`: [string] 必填, 从 string_common_attr 取值
          - `item_id`: [string] 必填, 从 string_item_attr 取值
          - `query`: [string] 选填, 从 string_common_attr 取值
          - `query_id`: [string] 选填, 从 string_common_attr 取值
          - `author_id`: [string] 选填, 从 int_item_attr 取值
          - `source`: [string] 选填, 从 string_item_attr 取值
          - `score`: [bool] 选填, 从 CommonRecoResult.score取值
          - `common_attrs_to_extra_info`: [string list] 选填 用于将 common attrs 追加到 KFS request.ctx_info.extra_info 的 extra 字段 。 value 为当前的 common attr name list
          - `item_attrs_to_extra_info`: [string list] 选填 用于将 item attrs 追加到 KFS request.ctx_info.item.extra_info 的 extra 字段 。 value 为当前的 item attr name list

        `kfs_data_source`: [dict] 选填 从指定的 common_attr 中构造 KFS request 的 PbDataSource，value 为对应 DataSource 的字段序列化之后的 string，默认为空

        `gflags`: [string]  选填 KFS processor 是通过加载动态库实现的，因此 KFS 的 gflags 需要单独设置，如果未指定 gflags 时(即 gflags 为空时)，将采用以下缺省值初始化动态库，**注意: 如果有多个 KFSProcessor，只有第一个的 gflags 有效**
        ```
        --service_name="grpc_" + GetKessServiceName()
        --taskflow_worker_threads=200
        --kfs_resident_arena_initial_block=2097152
        ```

        `kfs_business`: [string]: 选填 `feature.yaml` 对应的业务名，影响监控，默认为 `dragon`

        `kfs_skip_item_feature`: [bool] 选填 是否跳过提取 item 侧特征，默认 `false`

        `kfs_request_info_debug`: [bool] 选填 是否打出 kfs request 信息， 默认 `false`
        调用示例
        ------
        ``` python
        .kfs_run(
          kfs_req_attr_name_map={
            "session_id": "string_common_attr1",
            "query": "string_common_attr2",
            "query_id": "string_common_attr3",
            "item_id": "string_item_attr1",
            "author_id": "int_item_attr1",
            "common_attrs_to_extra_info": ["common_attr1","common_attr2"],
            "item_attrs_to_extra_info": ["item_attr1","item_attr2"],
          },
          kfs_data_source={
            "refer_photo_id": "string_common_attr4",
            "trending": "string_common_attr5"
          },
          gflags='''
          --service_name=your_service_name
          --taskflow_worker_threads=100
          --kfs_resident_arena_initial_block=2097152
          --other_flag=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
          ''',
          feature_yaml=open("./feature.yaml").read()),
          kfs_skip_item_feature=false,
          kfs_business='my_test')
        ```
        """
        self._add_processor(KFSFeatureEnricher(kwargs))
        return self

    def jubao_feature_service(self, **kwargs):
        """
        JubaoEnricher
        ------
        JubaoEnricher，jubao（聚宝，将特征视为宝藏），将 lib 化的特征工程服务化，是对 KFS 的升级
        用户通过 yaml 文件配置特征来源（reader）、特征处理方式（transformer）、特征输出名（cfg）
        最终根据 yaml 文件配置的特征输出到对应的 common_attr 或者 item_attr

        参数配置
        ------

        `request_config`: [dict] 必填 用于构造 JubaoRequest 的 attr name mapping 。 其中，key 为 JubaoRequest 中的 field name，value 为当前的 attr name:
          - `session_id`: [string] 必填, 从 string_common_attr 取值
          - `item_id`: [string] 必填, 从 string_item_attr 取值
          - `query`: [string] 选填, 从 string_common_attr 取值
          - `author_id`: [string] 选填, 从 int_item_attr 取值
          - `item_extra_info`: [string] 选填 序列化的 FeatureList，对应 JubaoItem 中的 extra_info
          - `extra_info`: [string] 选填 序列化的 FeatureList，对应 JubaoRequest 中的 extra_info
          - `recall_source`: [string] 选填 对应 JubaoItem 中的 source
          - `recall_type`: [uint32] 选填 对应 JubaoItem 中的 recall_type

        `data_source`: [dict] 选填 从指定的 common_attr 中构造 JubaoRequest 的 PbDataSource，value 为对应 DataSource 的字段序列化之后的 string，默认为空

        `business`: [string]: 必填 业务名，对应一个 feature.yaml

        `skip_item_feature`: [bool] 选填 是否跳过提取 item 侧特征，默认 `false`

        `output_common_attrs`: [list] 选填 输出的 common 特征名

        `output_item_attrs`: [list] 选填 输出的 item 特征名

        `extract_output_attrs_from_feature_yaml`: [string] 选填 从 feature.yaml 中提取需要输出的特征

        `request_debug`: [bool] 选填 是否打印 request 信息， 默认 `false`

        `response_debug`: [bool] 选填 是否打印 response 信息， 默认 `false`

        `service_name`: [string] 选填 主调服务名 默认通过 `GetKessServiceName()` 获取

        `rpc_name`: [string] 必填 rpc 服务名， 默认为空

        `timeout_ms`: [int] 选填 请求超时时间， 默认 60ms

        `debug_level`: [int] 选填 debug 级别， 默认 0

        调用示例
        ------
        ``` python
        .jubao_feature_service(
          request_config={
            "session_id": "string_common_attr_name",
            "query": "string_common_attr_name",
            "item_id": "string_item_attr_name",
            "author_id": "int_item_attr_name",
            "item_extra_info": "string_item_attr_name",
            "extra_info": "string_common_attr_name",
            "recall_source": "string_item_attr_name",
            "recall_type": "int_item_attr_name"
          },
          output_common_attrs: ["name", "age", "click_embedding", "like_embedding"],
          output_item_attrs: ["item_id", "item_embedding"],
          data_source={
            "refer_photo_id": "string_common_attr4",
            "trending": "string_common_attr5"
          },
          skip_item_feature=false,
          business='xxx',
          rpc_name='xxxx',
          timeout_ms=50,
          debug_level=1,
          request_debug=true,
          response_debug=true)
        ```
        """
        self._add_processor(JubaoEnricher(kwargs))
        return self
