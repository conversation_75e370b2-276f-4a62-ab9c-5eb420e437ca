#!/usr/bin/env python3
# coding=utf-8
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .follow_mix_rank_retriever import *


class FollowMixRankApiMixin(CommonLeafBaseMixin):
    def follow_mix_rank_pre_process(self, **kwargs):
        """
        FollowMixRankPreProcessRetriever
        ------
        关注页混排预处理processor

        参数配置
        ------
        `mix_rank_req_attr`:[string] 必须参数，反序列化后的kuaishou::reco::FollowMixRankRequest存储在这里

        调用示例
        ------
        ```
        .follow_mix_rank_pre_process(
            mix_rank_req_attr = "mix_rank_request"
        )
        ```
        """
        self._add_processor(FollowMixRankPreProcessRetriever(kwargs))

        return self

    def follow_mix_sort_process(self, **kwargs):
        self._add_processor(FollowMixSortProcessor(kwargs))
        return self

    def follow_mix_gen_response(self, **kwargs):
        """
        FollowMixGenResponseProcessor
        ------
        关注页混排填充结果processor

        """
        self._add_processor(FollowMixGenResponseProcessor(kwargs))
        return self

    def follow_mix_fill_item_features(self, **kwargs):
        self._add_processor(FollowMixEnrichItemAttrEnricher(kwargs))
        return self

    def follow_mix_global_data_enricher(self, **kwargs):
        """
        FollowMixGlobalDataEnricher
        ------
        从内存中获取指定类型的数据指针保存到 common attr 中

        参数配置
        ------
        `data_key`: [string] [动态参数] 数据的 key

        `data_type`: [string] 数据类型

        `save_data_ptr_to_attr`: [string] 保存数据指针的 common attr
        """
        self._add_processor(FollowMixGlobalDataEnricher(kwargs))
        return self

    def follow_mix_final_process(self, **kwargs):
        self._add_processor(FollowMixFinalProcessor(kwargs))
        return self


    def follow_mix_write_to_redis_no_wait(self, **kwargs):
        """
        FollowMixWriteToRedisObserver
        ------
        通用写 redis 插件, 能够将 CommonAttr 或者 ItemAttr 写入到指定的 redis 之中。

        如果填写的是 CommonAttr，那么存储格式就是会存储一个 StringValue 进去。

        如果填写的是 ItemAttr，那么对于每个 Item 都将会填写一个 StringValue 进去。

        其 redis 读取 processor 为 CommonRecoRedisCommonAttrEnricher

        参数配置
        ------
        `kcc_cluster`: [string] 必填，redis 的 kcc cluster name

        `timeout_ms`: [int] 选配项，redis client 的超时时间，单位 ms，默认为 10 ms

        `key_prefix`: [string] [动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

        `expire_second`: [int] [动态参数]，单位秒, 负数或者 0 表示不设置过期时间，默认 -1

        `list_separator`: [string] 如果存储的 value 是 list 类型，那么我们会用所给的分隔符来拼接成一个 string 存入到 redis 中，默认为 ','

        以下两种 key 二选一：

        `key`: [string] [动态参数] 指定写入 redis 的 key，如果配置了动态参数且该 common_attr 不存在将忽略写入，动态参数支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

        `key_from_item_attr`: [string] 从指定的 item_attr 中获取动态的 redis key，如果该 item_attr 不存在将被跳过。支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

        对于 value 有两种方式，二选一:

        `value`: [string] [动态参数] 指定写入 redis 的 value。

        `value_from_item_attr`: [string] 仅针对 `key_from_item_attr`，会根据其 item_attr 一一对应的去写。
        """
        self._add_processor(FollowMixWriteToRedisObserver(kwargs))
        return self
    def follow_mix_generator_pre(self, **kwargs):
        """
        FollowMixGeneratorPreProcessor
        ------
        在生成序列之前对业务进行处理  主要是业务价值计算 校准 bonus 获取分位数等 
        """
        self._add_processor(FollowMixGeneratorPreProcessor(kwargs))
        return self
    def follow_mix_live_ensemble_sort_processor(self, **kwargs):
        """
        FollowMixLiveEnsembleSortProcessor
        ------
        直播乘法公式排序
        """
        self._add_processor(FollowMixLiveEnsembleSortProcessor(kwargs))
        return self
    def follow_mix_photo_ensemble_sort_processor(self, **kwargs):
        """
        FollowMixPhotoEnsembleSortProcessor
        ------
        视频乘法公式排序
        """
        self._add_processor(FollowMixPhotoEnsembleSortProcessor(kwargs))
        return self
    def follow_mix_critic_model_predict(self, **kwargs):
        """
        FollowMixCriticModelPredictProcessor
        ------
        视频乘法公式排序
        """
        self._add_processor(FollowMixCriticModelPredictProcessor(kwargs))
        return self
    def follow_mix_merchant_evaluator_predict(self, **kwargs):
        """
        FollowMixMerchantEvaluatorModelPredictProcessor
        ------
        关注页混排电商序列评估器预估算子
        """
        self._add_processor(FollowMixMerchantEvaluatorModelPredictProcessor(kwargs))
        return self
    def follow_mix_ue_evaluator_predict(self, **kwargs):
        """
        FollowMixUeEvaluatorPredictProcessor
        ------
        关注页混排UE独立序列评估器预估算子
        """
        self._add_processor(FollowMixUeEvaluatorPredictProcessor(kwargs))
        return self
    def follow_mix_seq_score_ensemble(self, **kwargs):
        """
        FollowMixSeqScoreEnsembleProcessor
        ------
        关注页混排多序列评估器融合排序算子
        """
        self._add_processor(FollowMixSeqScoreEnsembleProcessor(kwargs))
        return self
    def follow_mix_evaluator_post(self, **kwargs):
        """
        FollowMixEvaluatorPostProcessor
        ------
        在前序阶段 evaluator 选出最优序列后，进行后处理工作
        """
        self._add_processor(FollowMixEvaluatorPostProcessor(kwargs))
        return self
    def follow_mix_ltr_model_predict(self, **kwargs):
        """
        FollowMixLtrModelPredictProcessor
        ------
        调用 ltr model 进行打分
        """
        self._add_processor(FollowMixLtrModelPredictProcessor(kwargs))
        return self