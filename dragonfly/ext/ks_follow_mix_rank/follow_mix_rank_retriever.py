#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import LeafRetriever
from ...common_leaf_processor import LeafEnricher
from ...common_leaf_processor import LeafObserver
from ...common_leaf_util import strict_types, check_arg


class FollowMixRankPreProcessRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_rank_pre_process"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("mix_rank_req_attr"))
        return attrs


class FollowMixSortProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_sort_processor"

    @strict_types
    def is_async(self) -> bool:
        return False

class FollowMixGenResponseProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_gen_response_processor"

    @strict_types
    def is_async(self) -> bool:
        return False

class FollowMixEnrichItemAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_enrich_item_attr_enricher"

    @strict_types
    def is_async(self) -> bool:
        return False

class FollowMixGlobalDataEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_global_data_enricher"

    @strict_types
    def depend_on_items(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("data_key")))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_data_ptr_to_attr"))
        return attrs


class FollowMixFinalProcessor(LeafRetriever):

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_rank_final_process"

    @strict_types
    def is_async(self) -> bool:
        return False


class FollowMixWriteToRedisObserver(LeafObserver):

    @strict_types
    def is_async(self) -> bool:
        return False

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_write_to_redis"

    @strict_types
    def _check_config(self) -> None:
        check_arg("key_from_common_attr" not in self._config,
                  "key_from_common_attr 配置项已废弃，请改用 key 配置项！")
        for key in ["key_from_item_attr", "value_from_item_attr"]:
            check_arg(not self._config.get(key, "").startswith("{{"),
                      f"{key} 配置不支持动态参数")

    @strict_types
    def depend_on_items(self) -> bool:
        return any(self.input_item_attrs)

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for key in ["key_prefix", "expire_second", "key", "value"]:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        ret = set()
        for key in ["key_from_item_attr", "value_from_item_attr"]:
            ret.add(self._config.get(key))
        return ret

class FollowMixLiveEnsembleSortProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_live_ensemble_sort_processor"

    @strict_types
    def is_async(self) -> bool:
        return False
class FollowMixPhotoEnsembleSortProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_photo_ensemble_sort_processor"

    @strict_types
    def is_async(self) -> bool:
        return False
class FollowMixGeneratorPreProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_generator_pre"
    @strict_types
    def is_async(self) -> bool:
        return False
class FollowMixEvaluatorPostProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_evaluator_post"
    @strict_types
    def is_async(self) -> bool:
        return False
class FollowMixCriticModelPredictProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_critic_model_predict"
    @strict_types
    def is_async(self) -> bool:
        return False
class FollowMixMerchantEvaluatorModelPredictProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_merchant_evaluator_predict"
    @strict_types
    def is_async(self) -> bool:
        return True 
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        # 该 processor 依赖的 common_attrs 集合
        attrs = set()
        # 在 zstd 中有 "input_common_attr" 这一配置来指定该 processor 需要用到的 common_attr 的 name，因此需要在 attrs 中加入
        # 以便 c++ 代码中能够通过 "input_common_attr" 来取到 common_attr 的 name，进而取到对应 name 下的 common_attr 值
        attrs.update(self.extract_dynamic_params(self._config.get("input_common_attr")))
        return attrs
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        # 该 processor 输出所存储的 common_attrs 集合
        attrs = set()    
        # 在 zstd 中有 "output_common_attr" 这一配置来指定该 processor 输出到的 common_attr 的 name，因此需要在 attrs 中加入
        # 以便 c++ 代码中能够通过 "output_common_attr" 来取到 common_attr 的 name，进而把结果输出到对应 name 下的 common_attr 
        attrs.add(self._config.get("output_common_attr"))
        return attrs
class FollowMixUeEvaluatorPredictProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_ue_evaluator_predict"
    @strict_types
    def is_async(self) -> bool:
        return True
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        # 该 processor 依赖的 common_attrs 集合
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("input_common_attr")))
        return attrs
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        # 该 processor 输出所存储的 common_attrs 集合
        attrs = set()    
        attrs.add(self._config.get("output_common_attr"))
        return attrs
class FollowMixSeqScoreEnsembleProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_seq_score_ensemble"
    @strict_types
    def is_async(self) -> bool:
        return False
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        # 该 processor 依赖的 common_attrs 集合
        attrs = set()
        attrs.update(self._config.get("input_common_attr", []))
        return attrs
class FollowMixLtrModelPredictProcessor(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_ltr_model_predict"
    @strict_types
    def is_async(self) -> bool:
        return False