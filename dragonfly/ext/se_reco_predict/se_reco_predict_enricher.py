#!/usr/bin/env python3
"""
filename: se_reco_common_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for se reco
author: <EMAIL>
date: 2022-09-01
"""
from ...common_leaf_util import check_arg, strict_types, extract_attr_names
from ...common_leaf_processor import Leaf<PERSON>nricher, try_add_table_name

class SeRecoAttrFitDoubleListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fit_feature_into_double_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for i, attr in enumerate(self._config["common_attr_set"]):
      if "common_output_attr_set" in self._config and i < len(self._config["common_output_attr_set"]):
        attrs.add(self._config["common_output_attr_set"][i])
      else:
        attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["item_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for i, attr in enumerate(self._config["item_attr_set"]):
      if "item_output_attr_set" in self._config and i < len(self._config["item_output_attr_set"]):
        attrs.add(self._config["item_output_attr_set"][i])
      else:
        attrs.add(attr)
    return attrs

class SeRecoVirtualEmbGenerateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "transfer_virtual_emb"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attr_set"]:
      attrs.add(attr + '_virtual_emb')
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["item_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["item_attr_set"]:
      attrs.add(attr + '_virtual_emb')
    return attrs

class SeRecoMergeAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_dense_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["import_common_attr"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["export_common_attr"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["import_item_attr"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["export_item_attr"]:
      attrs.add(attr)
    return attrs

class SeRecoCommonProfileV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_profile_v1_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_attr_name", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("err_code_attr_name", ""))
    attrs.append(self._config.get("output_attr_name", ""))
    return set(attrs)

class SeRecoPBListScatterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pb_list_scatter_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("common_pb_list_attr", ""))
    attrs.append(self._config.get("common_key_list_attr", ""))
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("item_key_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("item_pb_attr", "")]
    return set(attrs)

class SeRecoUnfieldProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_ups"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      if "key_attr_name" in type_config:
        attrs.add(type_config.get("key_attr_name"))
    return attrs

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      for feature in type_config.get("features", []):
        if "export_name" in feature:
          attrs.add(feature["export_name"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self._is_common_attr else self._get_output_attrs()

class SeRecoPredictPBListScatterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "srp_pb_list_scatter_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("common_pb_list_attr", ""))
    attrs.append(self._config.get("common_key_list_attr", ""))
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("item_key_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("item_pb_attr", "")]
    return set(attrs)

class SeRecoPredictCommonProfileV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "srp_common_profile_v1_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_attr_name", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("err_code_attr_name", ""))
    attrs.append(self._config.get("output_attr_name", ""))
    return set(attrs)

class SeRecoPredictGetAttrByProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "srp_enrich_by_profile"

  @strict_types
  def is_async(self) -> bool:
    return True

  def is_common(self):
    return self._config.get("is_common_attr", False)

  def is_multi_field(self):
    return self._config.get("is_multi_field", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      if self.is_multi_field():
        fields = self._config.get("fields", [])
        for field in fields:
          if isinstance(field, str):
            attrs.add(field)
          else:
            attrs.add(field.get("export_name", field["field_name"]))
      else:
        attrs.add(self._config.get("output_attr_name", ""))
      is_success_attr_name =  self._config.get("is_success_attr_name", "")
      if is_success_attr_name:
        attrs.add(is_success_attr_name)
    return attrs

  @property
  @strict_types
  def enable_auto_merge(self) -> bool:
    return True

  @strict_types
  def auto_merge_config(self, other_config: dict) -> bool:
    if self._config.get("is_multi_field", False) == False:
      return False
    for field in ["is_common_attr", "biz_name", "profile_name", "input_attr_name"]:
      if self._config[field] != other_config[field]:
        return False
    fields_lhs = self._config["fields"]
    fields_rhs = other_config["fields"]
    merged_list = fields_lhs + fields_rhs
    merged_set = []
    field_map = {}
    for field in merged_list:
      if isinstance(field, str):
        field_name = field
        export_name = field
      else:
        field_name = field["field_name"]
        export_name = field.get("export_name", field["field_name"])
      if field_name in field_map and export_name != field_map[field_name]:
        raise Exception(f"{field_name} export name diff {field_map[field_name]} : {export_name}")
      if field_name not in field_map:
        field_map[field_name] = export_name
        merged_set.append(field)
    self._config["fields"] = merged_set
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      if self.is_multi_field():
        fields = self._config.get("fields", [])
        for field in fields:
          if isinstance(field, str):
            attrs.add(field)
          else:
            attrs.add(field.get("export_name", field["field_name"]))
      else:
        attrs.add(self._config.get("output_attr_name", ""))
      is_success_attr_name =  self._config.get("is_success_attr_name", "")
      if is_success_attr_name:
        attrs.add(is_success_attr_name)
    return attrs

class SeRecoPredictUnfieldProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "srp_se_reco_ups"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      if "key_attr_name" in type_config:
        attrs.add(type_config.get("key_attr_name"))
      for feature in type_config.get("import_features", []):
        if "import_name" in feature:
          attrs.add(feature["import_name"])
    return attrs

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      for feature in type_config.get("features", []):
        if "export_name" in feature:
          attrs.add(feature["export_name"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self._is_common_attr else self._get_output_attrs()

class SeRecoUnpackAttrFromBytesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unpack_attr_from_bytes"

  @strict_types
  def _check_config(self) -> None:
    check_arg("is_common_attr" in self._config, f"`is_common_attr` 是必选项")
    check_arg("unpack_attr_names" in self._config, f"`unpack_attr_names` 是必选项")
    unpack_attr_names = self._config.get("unpack_attr_names")
    check_arg(isinstance(unpack_attr_names, list) and all(isinstance(name, str) for name in unpack_attr_names),
              f"`unpack_attr_names` 的类型必须是 str_list")
    tp = self._config.get("type")
    if tp:
      check_arg(tp in {"int", "double"}, '`type` 只能为 int 或 double')

    has_default_value = self._config.get("has_default_value")
    if has_default_value:
      check_arg(isinstance(has_default_value, list) and all(isinstance(val, int) for val in has_default_value),
                f"`has_default_value` 的类型必须是 int_list")
      check_arg(len(has_default_value) == len(unpack_attr_names), f"如果填写 `has_default_value`，则长度必须与 `unpack_attr_names` 一致")
      check_arg("default_values" in self._config, f"如果填写 `has_default_value`，则 `default_values` 是必选项")
      default_values = self._config.get("default_values")
      type_map = {
        "int": int,
        "double": float,
      }
      check_arg(isinstance(default_values, list) and all(isinstance(val, type_map[tp]) for val in default_values),
                f"`default_values` 的类型必须是 list 且与元素类型 type 对应（如果是 double 则整数需表示为 3.）")
      check_arg(len(default_values) == len(unpack_attr_names), f"如果填写 `default_values`，则其长度必须与 `unpack_attr_names` 一致")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.add(self._config.get("pack_attr_name", "PACK_ATTR"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.update(self._config.get("unpack_attr_names"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.add(self._config.get("pack_attr_name", "PACK_ATTR"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.update(self._config.get("unpack_attr_names"))
    return attrs

class SeRecoUnpackListFromBytesEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unpack_list_from_bytes"

  def get_pack_attr_names(self) -> list:
    if "pack_attr_names" in self._config:
      return self._config.get("pack_attr_names")
    else:
      unpack_attr_names = self._config.get("unpack_attr_names")
      return unpack_attr_names # [s + "_packed" for s in unpack_attr_names]

  @strict_types
  def _check_config(self) -> None:
    check_arg("is_common_attr" in self._config, f"`is_common_attr` 是必选项")
    check_arg("unpack_attr_names" in self._config, f"`unpack_attr_names` 是必选项")

    unpack_attr_names = self._config.get("unpack_attr_names")
    check_arg(isinstance(unpack_attr_names, list) and all(isinstance(name, str) for name in unpack_attr_names),
              f"`unpack_attr_names` 的类型必须是 str_list")
    if "pack_attr_names" in self._config:
      pack_attr_names = self._config.get("pack_attr_names")
      check_arg(isinstance(pack_attr_names, list) and all(isinstance(name, str) for name in pack_attr_names),
                f"`pack_attr_names` 的类型必须是 str_list")
      check_arg(len(pack_attr_names) == len(unpack_attr_names),
                f"`pack_attr_names` 与 `unpack_attr_names` 的长度必须相同")
    else:
      print(f"`pack_attr_names` is unspecified. use: {self.get_pack_attr_names()}")

    check_arg("type" in self._config, f"`type` 是必选项")
    t = self._config.get("type")
    check_arg(t in {"int", "int32", "int16", "double", "float", "float16"}, '`type` 只能为 int（64 位）、int32、int16、double、float、float16')

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.update(self.get_pack_attr_names())
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", True):
      attrs.update(self._config.get("unpack_attr_names"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.update(self.get_pack_attr_names())
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", True):
      attrs.update(self._config.get("unpack_attr_names"))
    return attrs

# mostly copy from dragon/dragonfly/ext/common/common_leaf_enricher.py CommonRecoDelegateEnricher
class SeRecoDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_delegate_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    if "strip_unused_attrs" in self._config:
      check_arg(isinstance(self._config["strip_unused_attrs"], bool), f"`strip_unused_attrs` 必须是 bool")
    if "json_filename" in self._config:
      check_arg(isinstance(self._config["json_filename"], str), f"`json_filename` 必须是 str")

    if "pack_type" in self._config:
      pack_type = self._config.get("pack_type")
      check_arg("pack_list_common_attrs" in self._config, f"`pack_type` 仅用于 `pack_list_common_attrs`")
      check_arg(isinstance(pack_type, list) and all(isinstance(t, str) for t in pack_type) and 0 < len(pack_type) <= 2,
                f"`unpack_attr_names` 的类型必须是长为 1 或 2 的 str_list，分别指定浮点和整数的类型")
      for t in pack_type:
        check_arg(t in {"int64", "int32", "int16", "double", "float", "float16"}, '`type` 只能为 int64、int32、int16、double、float、float16')

    if "item_from_tables" in self._config:
      check_arg("use_packed_item_attr" in self._config and self._config["use_packed_item_attr"] == True,
                f"{self.get_type_alias()} 配置了 item_from_tables 的情况下必须同时配置 use_packed_item_attr 为 True")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("hash_id")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("sample_list_ptr_attr"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_blacklist_key")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    if "item_from_tables" in self._config:
      table_attrs = set()
      for tables in self._config["item_from_tables"]:
        table_attrs.update(try_add_table_name(tables, attrs))
      return table_attrs
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    if "item_from_tables" in self._config:
      table_attrs = set()
      for tables in self._config["item_from_tables"]:
        table_attrs.update(try_add_table_name(tables, attrs))
      return table_attrs
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

  # FIXME(fangjianbing): 这个 bug fix 对直播的耗时增长影响很大，暂时先注释掉
  # @strict_types
  # def depend_on_all_item_attrs(self) -> bool:
  #   return bool(self._config.get("send_item_attrs_in_name_list", ""))

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    return ["kess_service", "return_item_attrs"]
