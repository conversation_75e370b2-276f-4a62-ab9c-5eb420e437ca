#!/usr/bin/env python3
# coding=utf-8
"""
filename: se_reco_common_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, se reco api mixin
author: <EMAIL>
date: 2022-09-06 09:50:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .se_reco_predict_enricher import *

class SeRecoPredictApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 搜索推荐方向infer服务相关的 Processor 接口, 主要应用于搜索 Query 推荐的各个场景：包括搜索首页猜搜、框词、Sugg、Subtag、RS、搜索气泡、评论区飘蓝等, 由搜索推荐团队负责维护
  """

  def fit_feature_into_double_list(self, **kwargs):
    """
    SeRecoAttrFitDoubleListEnricher
    ------
    将common/item特征转换成double_list以适配uni_predict_fused
    common/item_output_attr_set为输出attr的名字，默认不配置或者配置为空的时候，输入输出同名，否则输入输出按配置来
    调用示例
    ------
    ``` python
      .fit_feature_into_double_list(
        common_attr_set=[],
        item_attr_set=[],
	common_output_attr_set=[],
	item_output_attr_set=[]
      )
    ```
    """
    self._add_processor(SeRecoAttrFitDoubleListEnricher(kwargs))
    return self

  def transfer_virtual_emb(self, **kwargs):
    """
    SeRecoVirtualEmbGenerateEnricher
    ------
    给定common/item特征，如果有值，则生成相应virtual_emb特征，值为1，int类型
    新生成的特征主要用来特征抽取
    调用示例
    ------
    ``` python
      .transfer_virtual_emb(
        common_attr_set=["attr_1"],
        item_attr_set=["attr_2"]
      )
    ```
    以上示例表示：
      如果存在名为attr_1的common_attr，则生成一个attr_1_virtual_emb的新common特征，int类型，值为1
      如果存在名为attr_2的item_attr，则生成一个attr_2_virtual_emb的新item特征，int类型，值为1
    """
    self._add_processor(SeRecoVirtualEmbGenerateEnricher(kwargs))
    return self

  def merge_dense_attr(self, **kwargs):
    """
    SeRecoMergeAttrEnricher
    ------
    将m个输入的common/item特征，按照mapping规则融合成n个输出的common/item特征
    输入的特征必须为double list类型
    mapping为一个整型数组，长度为n，数组之和为m，其每个元素表示mapping[i]个输入特征融合成第i个输出特征
    mapping必须满足以下两个条件，否则部署会出core：
      len(import_attr) = sum(mapping)
      len(export_attr) = len(mapping)
    调用示例
    ------
    ``` python
      .merge_dense_attr(
        import_common_attr=["attr_1", "attr_2", "attr_3", "attr_4", "attr_5"],
        export_common_attr=["new_attr_1", "new_attr_2", "new_attr_3"],
        common_mapping=[2, 1, 2],
        import_item_attr=[],
        export_item_attr=[],
        item_mapping=[]
      )
    ```
    以上示例表示：
        attr_1, attr_2 -> new_attr_1
        attr_3         -> new_attr_2
        attr_4, attr_5 -> new_attr_3
    """
    self._add_processor(SeRecoMergeAttrEnricher(kwargs))
    return self

  def srp_common_profile_v1_enrich(self, **kwargs):
    """
    配置参数请看 se_reco 目录下的 common_profile_v1_enrich 算子，该算子只是为了解决 se_reco 的编译问题而新增的
    """
    self._add_processor(SeRecoPredictCommonProfileV1Enricher(kwargs))
    return self

  def srp_pb_list_scatter_enricher(self, **kwargs):
    """
    配置参数请看 se_reco 目录下的 pb_list_scatter_enricher 算子，该算子只是为了解决 se_reco 的编译问题而新增的
    """
    self._add_processor(SeRecoPredictPBListScatterEnricher(kwargs))
    return self

  def srp_se_reco_ups(self, **kwargs):
    """
    配置参数请看 se_reco 目录下的 se_reco_ups 算子，该算子只是为了解决 se_reco 的编译问题而新增的
    """
    self._add_processor(SeRecoPredictUnfieldProfileEnricher(kwargs))
    return self

  def srp_enrich_by_profile(self, **kwargs):
    """
    配置参数请看 se_reco 目录下的 enrich_by_profile 算子，该算子只是为了解决 se_reco 的编译问题而新增的
    """
    self._add_processor(SeRecoPredictGetAttrByProfileEnricher(kwargs))
    return self

  def unpack_list_from_bytes(self, **kwargs):
    """
    SeRecoUnpackListFromBytesEnricher
    ------
    解析上游传入的某个 attr 的 bytes（内容为这个 attr list value 的内存序列），将 bytes 对应的 list 写入到另一个 attr（也可覆盖当前 attr）的 IntList/DoubleList 中。
    适用于将 common attr 中过长的 int/double list 打包为 string attr；或上游非 dragon 且有大量 item 时将每个 item 过长的 int/double list attr 打包为 string attr。

    可以传入多个 pack attr 并解析到多个 unpack attr。

    参数配置
    ------
    `pack_attr_names`: [string_list] 选填，上游传入的保存各 attr val bytes 的 attr 名。可传入多个。
    如果不填写则默认与 unpack_attr_names 一致，这样不用修改 dsl 的 attrs_from_request，但 dragon 可能会报 set inconsistent type 的 error（没影响只是很多）。

    `unpack_attr_names`: [string_list] 必填，将 pack_attr 的值解析到哪个 attr。可传入多个，与 pack_attr_names 一一对应。

    `is_common_attr`: [bool] 必填，传入和解析的 attr 是 common 还是 item。

    `type`: [string] 必填，上游传递的各 attr list 值的类型（需要相同）。目前只能为 int（64 位）、int32、int16、double、float、float16。
    注意，不管 type 的精度是多少，都会将 unpack_attr 作为 int64/double attr set 到 context 中，与 dragon 原本的处理一致。type 只影响传输时的数据大小。

    调用示例
    ------
    ``` python
    # 读取上游传入的 string attr `gsu_emb`，按 float 解析 str.data，然后写入到 double list attr `gsu_emb`
    .unpack_list_from_bytes(
      unpack_attr_names = ["gsu_emb"],
      is_common_attr = True,
      type = "float",
    )
    # 当上游传入的 packed attr 与要填充的 attr 名字不同时可指定 pack_attr_names
    # 这样可以同时传递 emb_packed 与 emb 并比较，便于验证 pack 正确性
    .unpack_list_from_bytes(
      pack_attr_names = ["click_photo_emb_packed", "like_photo_emb_packed"],
      unpack_attr_names = ["click_photo_emb", "like_photo_emb"],
      is_common_attr = False,
      type = "double",
    )
    ```
    """
    self._add_processor(SeRecoUnpackListFromBytesEnricher(kwargs))
    return self

  def unpack_attr_from_bytes(self, **kwargs):
    """
    SeRecoUnpackAttrFromBytesEnricher
    ------
    解析上游传入的某个 attr 的 bytes（内容为 `[pair(hash(attr_name), attr_value)]`，哈希算法和结果需与上游一致），将每个 attr_value 反序列化写入到对应 attr_name 中。
    适用于上游非 dragon 且有大量 item 时将每个 item 的所有 int, double attr 打包为一个 attr（目前是行存，上游必须是 dragon 才能用效率更高的列存）。

    可以为 attr 设置默认值（逻辑等同于自己调用 set_attr_default_value）；当 attr 值等于默认值时上游可以不传递该 attr（设置的默认值必须与上游一致）。

    默认值可以随意设一个出现频次高的值，用上更好，没用上或者不设也没什么影响。

    TODO：支持任意类型；将 packed item attr 放到 common 里实现列存而非行存。

    参数配置
    ------
    `pack_attr_name`: [string] 选填，上游传入的保存各 attr val bytes 的 attr 名，默认 PACK_ATTR。

    `unpack_attr_names`: [string_list] 必填，将 pack_attr 的值解析到哪些 attr。打包到一个 attr 的所有 attr 的类型及 readonly 和 fixed 字段必须相同。顺序不需要与上游一致。

    `is_common_attr`: [bool] 必填，传入和解析的 attr 是 common 还是 item。

    `type`: [string] 选填，所有 attr 值的类型（需要相同），默认 int。目前只能为 int 和 double。

    `has_default_value`：[int_list] 选填，每个 attr 是否有默认值，0 代表无，非 0 代表有。如果填了则长度必须与 unpack_attr_names 一致。

    `default_values`：[int_list / double_list] 选填，每个 attr 的默认值，仅在 has_default_value 对应位置非 0 时生效（0 位置的值仍要填写并会被忽略）。如果填了 has_default_value 则必须填写且长度要一致。值类型需与 type 对应（如果是 double 则整数需要加 .）。

    调用示例
    ------
    ``` python
    .unpack_attr_from_bytes(
        pack_attr_name = "PACK_ATTR",
        unpack_attr_names = ["ctr", "ltr", "lvtr"],
        is_common_attr = True,
        type = "int",
    )
    .unpack_attr_from_bytes(
      pack_attr_name = "PACK_ATTR_ITEM",
      unpack_attr_names = ["ctr", "ltr", "lvtr"],
      has_default_value = [1, 0, 1],
      default_values = [-1., 0., 39.5],
      is_common_attr = False,
      type = "double",
    )
    ```
    """
    self._add_processor(SeRecoUnpackAttrFromBytesEnricher(kwargs))
    return self

  def se_delegate_enrich(self, **kwargs):
    """
    SeRecoDelegateEnricher
    ------
    在 [delegate_enrich](https://dragonfly.corp.kuaishou.com/#/api/common?id=delegate_enrich) 基础上添加两个针对推理服务的优化（如果均不启用则行为与 delegate_enrich 一致）：

    1. 避免向 infer server 发送无用 attr：需要配置 `strip_unused_attrs=True`（默认启用），并在仓库 [Search Infer Config](https://git.corp.kuaishou.com/search_infer/search-infer-config) 中与 infer server 相同 kess 名的目录下上传 json 配置文件。
    如果之后修改相同 kess name 下模型的`attrs_from_request`，需要更新 json 文件或将`strip_unused_attrs`设为 false。

    2. 将指定的 list common attr 打包为 bytes (string attr) 发送，infer 需使用 unpack_list_from_bytes 解析。默认不使用，如果启用需手动指定。
    仅用于处理 list 类型的 common attr（delegate_enrich 会使用 dragon 自带的 pack item attr 功能，但没有处理 common；非 list attr 的 pack 需下游实现对应的算子，暂未实现且价值不高）。

    参数配置
    ------
    **strip 相关参数：**

    `strip_unused_attrs`：[bool] 选填，是否通过服务的 kess name 在 Search Infer Config 仓库的对应目录下读取 json 配置文件并以此删除多余 attr，默认 true。

    `json_filename`：[string] 选填，默认 dynamic_json_config.json，如果仓库中的 json 文件名不是这个可以指定。

    **pack 相关参数：**

    `pack_list_common_attrs`：[string_list] 选填，将指定的 list common attr 打包为 bytes 发送，默认为空即不使用。pack 后的 attr 会取代原 attr 发送，名称不变。

    `pack_type`：[string_list] 选填，数据以什么类型打包到 bytes 中发送。如不填写则默认整数 attr 使用 int64，浮点 attr 使用 float；如填写则可选值为：int64/int32/int16 和 double/float/fp16，可填写最多两个分别适用于整数和浮点。

    注意 double list attr 默认会被转为 float 发送，接收端 unpack_list_from_bytes 需与此对应（即`type`需等于该`pack_type`，对于 double 默认要是 "float"）。但不管精度如何接收端都会将其转为 int64/double attr，这与 dragon 原本的处理一致：整数按 int64 发送存储为 int64 attr，浮点按 float 发送但在接收端转为 double attr 保存。

    调用示例
    ------
    使用 strip 时与 delegate_enrich 一致，只需要按需配置`strip_unused_attrs`与`json_filename`即可。

    使用 pack：

    ```python
    .se_delegate_enrich(
      kess_service = "kws-kuaishou-search-arch-uni-gpu-predict-prerank-server",
      send_item_attrs = item_attrs,
      send_common_attrs = common_attrs,
      send_common_attrs_in_request = False,
      recv_item_attrs = return_item_attr,
      recv_common_attrs = [],
      request_type = "default",
      use_packed_item_attr = True,
      pack_list_common_attrs = ["gsu_emb", "query_emb"],
      pack_type = ["float"], # 将每个 double attr 以 float 类型发送
    )
    # infer 侧
    .unpack_list_from_bytes(
      unpack_attr_names = ["gsu_emb", "query_emb"],
      is_common_attr = True,
      type = "float", # 按 float 解析这些 packed attr
    )
    ```
    """
    self._add_processor(SeRecoDelegateEnricher(kwargs))
    return self