#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_model_enricher.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-09-05 18:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class ExploreListwiseSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_listwise_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("selector_map_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_slot_attr"))
    attrs.add(self._config.get("item_sign_attr"))
    for item in self._config.get("extra_item_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_item_slots_to_attr"))
    attrs.add(self._config.get("save_item_signs_to_attr"))
    attrs.add(self._config.get("sample_tag_attr"))
    for item in self._config.get("extra_item_attrs", {}).items():
      attrs.add(item[1] + "_list")
    return attrs
