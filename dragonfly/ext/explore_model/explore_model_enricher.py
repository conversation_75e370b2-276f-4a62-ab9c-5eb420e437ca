#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_model_enricher.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-09-05 18:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher
from ...common_leaf_util import gen_attr_name_with_item_attr_channel

class ExploreUniversalFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_extract_universal_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("tab")))
    for item in self._config.get("user_info_attrs", {}).items():
      attrs.add(item[1])
    for item in self._config.get("context_info_common_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for item in self._config.get("photo_info_attrs", {}).items():
      attrs.add(item[1])
    for item in self._config.get("context_info_item_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_common_slots_to_attr"))
    attrs.add(self._config.get("save_common_signs_to_attr"))
    for item in self._config.get("extra_common_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_item_slots_to_attr"))
    attrs.add(self._config.get("save_item_signs_to_attr"))
    for item in self._config.get("extra_item_attrs", {}).items():
      attrs.add(item[1])
    return attrs

class ExploreListwiseSelectionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_select_listwise_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("tab")))
    for item in self._config.get("context_info_common_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for item in self._config.get("context_info_item_attrs", {}).items():
      attrs.add(item[1])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_selector_map_to_attr"))
    return attrs
  
class ExploreDispatchAnnListToItemEnricher(LeafEnricher):
  """
  ExploreDispatchAnnListToItemEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_dispatch_ann_list_to_item"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_common_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(gen_attr_name_with_item_attr_channel(self._config.get("item_source_attr", ""), self._config.get("from_common_attr", "")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("to_item_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

class ExplorePrerankGenAnnMaskEnricher(LeafEnricher):
  """
  ExplorePrerankGenAnnMaskEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_prerank_gen_ann_mask"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("rank_all_item_attr", ""))
    attrs.add(self._config.get("final_rank_item_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr_list = [
      "explore_filter_cascade_mask_attr",
      "explore_filter_cascade_s1_mask_attr",
      "explore_distill_fr_mask_attr",
      "explore_distill_rerank_mask_attr",
      "photo_random_num_attr",
    ]
    for str_label in attr_list:
      attr = self._config.get(str_label, "")
      if attr:
        attrs.add(attr)
    return attrs