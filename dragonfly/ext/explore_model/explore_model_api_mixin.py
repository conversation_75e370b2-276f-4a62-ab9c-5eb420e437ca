#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_model_api_mixin.py
description:
author: huze<PERSON><PERSON>@kuaishou.com
date: 2023-09-05 18:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .explore_model_enricher import *
from .explore_model_retriever import *

class ExploreModelApiMixin(CommonLeafBaseMixin):
  def explore_extract_universal_feature(self, **kwargs):
    """
    ExploreUniversalFeatureEnricher
    """
    self._add_processor(ExploreUniversalFeatureEnricher(kwargs))
    return self

  def explore_select_listwise_sample(self, **kwargs):
    """
    ExploreListwiseSelectionEnricher
    """
    self._add_processor(ExploreListwiseSelectionEnricher(kwargs))
    return self

  def explore_retrieve_listwise_sample(self, **kwargs):
    """
    ExploreListwiseSampleRetriever
    """
    self._add_processor(ExploreListwiseSampleRetriever(kwargs))
    return self

  def explore_dispatch_ann_list_to_item(self, **kwargs):
    """
    ExploreDispatchAnnListToItemEnricher
    """
    self._add_processor(ExploreDispatchAnnListToItemEnricher(kwargs))
    return self

  def explore_prerank_gen_ann_mask(self, **kwargs):
    """
    ExplorePrerankGenAnnMaskEnricher
    """
    self._add_processor(ExplorePrerankGenAnnMaskEnricher(kwargs))
    return self
