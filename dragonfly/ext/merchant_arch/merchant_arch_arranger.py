#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class MerchantUnifyChannelSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_unify_channel_sort"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("allow_score_to_be_any_value")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_queue_names")))
    attrs.update(self.extract_dynamic_params(self._config.get("force_last_queue_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("last_queue_all_selected", False)))
    attrs.update(self.extract_dynamic_params(self._config.get("replenish_queue_names", [])))
    attrs.update(self.extract_dynamic_params(self._config.get("no_quota_early_return_old")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    if 'queue_sub_quota_allocation_methed' in self._config.keys():
      attrs.update(self._config.get("queue_sub_quota_allocation_methed"))
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_quota' in value.keys():
              attrs.update(self.extract_dynamic_params(value.get("sub_quota")))
    
    if 'devrsity_rules' in self._config.keys():
      config = self._config.get("devrsity_rules")
      for key in ["max_satisfied_pick", "perflog_enabled"]:
        attrs.update(self.extract_dynamic_params(config.get(key)))
      prev_items_from_attr = config.get("prev_items_from_attr")
      if prev_items_from_attr:
        attrs.add(prev_items_from_attr)
      for r in config.get("rules", []):
        for key in ["priority", "window_size", "max_num", "min_num", "enabled", "consider_prev_items"]:
          attrs.update(self.extract_dynamic_params(r.get(key)))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_queue_score_attrs' in value.keys():
              attrs.update(value.get("sub_queue_score_attrs"))
            if 'sub_queue_flag_attrs' in value.keys():
              attrs.update(value.get("sub_queue_flag_attrs"))
    
    if 'devrsity_rules' in self._config.keys():
      prev_attrs = set()
      config = self._config.get("devrsity_rules")
      for r in config.get("rules", []):
        attrs.add(r.get("attr_name"))
        if r.get("consider_prev_items"):
          prev_attrs.add(r.get("attr_name"))
      prev_items_from_attr = config.get("prev_items_from_attr")
      if prev_items_from_attr:
        t = set(gen_attr_name_with_common_attr_channel(v, prev_items_from_attr) for v in prev_attrs)
        attrs.update(t)

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    prefix = self._config.get("prefix", "mc")
    attrs.add(prefix + '_channel_origin_rank')
    attrs.add(prefix + '_channel_origin_name')
    attrs.add("sub_" + prefix + '_channel_origin_name')
    attrs.add("sub_" + prefix + '_channel_origin_rank')
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    prefix = self._config.get("prefix", "mc")
    attrs.add(prefix + '_channelsort_info_str')
    attrs.add(prefix + '_channelsort_target_info_str')

    return attrs

class MerchantUnifyItemBarrierArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_unify_barrier_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([attr for attr, val in self._config.get("pass_flags", dict()).items()])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantUnifyChannelTruncateArranger(LeafArranger):
  def __init__(self, config:dict):
    super().__init__(config)
    self._input_common_attrs = set()
    self._input_item_attrs = set()
    for channel_type in ["pass_channels", "channels"]:
      if channel_type in self._config:
        for channel in self._config.get(channel_type, list()):
          if "select_item" in channel:
            self.extract_config_tree(channel["select_item"])

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_unify_channel_truncate"

  @strict_types
  def is_async(self) -> bool:
    return False

  def extract_config_tree(self, config):
    attrs = []
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("enable")))
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("compare_to")))
    if config.get("attr_name"):
      self._input_item_attrs.add(config.get("attr_name"))
    if config.get("compare_to_item_attr"):
      self._input_item_attrs.add(config.get("compare_to_item_attr"))
    
    if config.get("filters"):
      for ft in config.get("filters"):
        self.extract_config_tree(ft)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    assert "limit" in self._config
    self._input_common_attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    self._input_common_attrs.update(self._config.get("import_common_attrs", []))

    for channel_type in ["pass_channels", "channels"]:
      if channel_type in self._config:
        for channel in self._config.get(channel_type, list()):
          self._input_common_attrs.update(self.extract_dynamic_params(channel.get("enable")))
          self._input_common_attrs.update(self.extract_dynamic_params(channel.get("reason")))
          self._input_common_attrs.update(self.extract_dynamic_params(channel.get("pass_version")))
          self._input_common_attrs.update(self.extract_dynamic_params(channel.get("re_cmp_expr")))
    
    return self._input_common_attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    assert "primary_score" in self._config
    self._input_item_attrs.add(self._config.get("primary_score"))
    for channel_type in ["pass_channels", "channels"]:
      if channel_type in self._config:
        for channel in self._config.get(channel_type, list()):
          for attr_name in ["sort_score"]:
            if channel.get(attr_name):
              self._input_item_attrs.add(channel.get(attr_name))

    for channel in self._config.get("channels", list()):
      assert "type" in channel
      assert "name" in channel
      self._input_item_attrs.add(channel.get("sort_score"))
      if channel.get("type") == "score":
        assert "sort_score" in channel
    for channel in self._config.get("pass_channels", list()):
      channel["type"] = "pass_channels"
      assert "name" in channel
      assert "limit" in channel

    self._input_item_attrs.update(self._config.get("import_item_attrs", []))

    return self._input_item_attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config.get("channels", list()):
      for set_attr_value in channel.get("set_attr_value", list()):
        assert "attr_name" in set_attr_value
        assert "op" in set_attr_value
        assert "value" in set_attr_value
        assert "default_val" in set_attr_value
        attrs.add(set_attr_value.get("attr_name"))
    for channel in self._config.get("pass_channels", list()):
      for set_attr_value in channel.get("set_attr_value", list()):
        assert "attr_name" in set_attr_value
        assert "op" in set_attr_value
        assert "value" in set_attr_value
        assert "default_val" in set_attr_value
        attrs.add(set_attr_value.get("attr_name"))
    return attrs

class MerchantUnifyDiversityRulesArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_unify_diversify_by_rules"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["max_satisfied_pick", "perflog_enabled", "front_fixed_num"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      attrs.add(prev_items_from_attr)
    for r in self._config.get("rules", []):
      for key in ["priority", "window_size", "max_num", "min_num", "enabled", "consider_prev_items"]:
        attrs.update(self.extract_dynamic_params(r.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    for r in self._config.get("rules", []):
      attrs.add(r.get("attr_name"))
      if r.get("consider_prev_items"):
        prev_attrs.add(r.get("attr_name"))
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_items_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs
