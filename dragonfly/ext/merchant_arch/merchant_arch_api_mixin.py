#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_arch_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, merchant_arch api mixin
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .merchant_arch_arranger import *
from .merchant_arch_enricher import *
from ..common.common_leaf_enricher import CommonRecoDelegateEnricher


class MerchantArchApiMixin(CommonLeafBaseMixin):
    def merchant_unify_channel_sort(self, **kwargs):
        """
        MerchantUnifyChannelSortArranger
        -------
        多通道排序
        参数配置
        ------
        - `channel_queue_names`: [string list][动态参数] 实际队列的名字

        - `input_count_threshold`: [int][动态参数] 进行多通道排序最小输入 Item 数量

        - `output_count`: [int][动态参数] 进行多通道排序输出 Item 数量

        - `queue_weight_attrs`: [string list] 表示各队列的权重的 Common Attr 名字

        - `queue_score_attrs`: [string list] 表示各队列的分数的 Item Attr 名字

        - `queue_flag_attrs`: [string list] 标志是否属于某队列的 Item Attr 名字

        - `enable_dryrun_mode`: [bool] 标志是否进行粗排精排重复率的衡量

        - `dryrun_mode_result_attr`: [string] dryrun 模式结果保存的 Item Attr 名字

        - `last_queue_all_selected`: [bool][动态参数] 是否将最后一个队列的所有候选都选中作为打散留足空间，默认值为 false

        - `replenish_queue_names`: [string list][动态参数] 补充队列的队列名， 补充队列优先级最低， 在打散、截断后进行补充，补充的候选会被二次打散。

        - `devrsity_rules`: [dict] 打散规则配置。不配置则关闭打散。参数和实现对齐 `merchant_unify_diversify_by_rules` 算子

        - `no_quota_early_return_old`: [bool] [动态参数] 是否在没有 quota 的情况下直接返回（老版本），默认值为 true

        - `allow_score_to_be_any_value`: [bool] [动态参数] 是否允许 score 为任意值，默认值为 false
        ------
        ``` python
        .merchant_unify_channel_sort(
            channel_queue_names="{{mc_channel_queue_names}}"
            input_count_threshold=200,
            output_count=200,
            queue_weight_attrs=["mc_csqw_follow, "mc_csqw_hot"],
            queue_score_attrs=["mc_csqs_follow", "mc_csqs_hot"],
            queue_flag_attrs=["mc_csqf_follow", "mc_csqf_hot"],
            enable_dryrun_mode=False,
            dryrun_mode_result_attr = "merchant_dryrun_mode_result",
            last_queue_all_selected=True,
            replenish_queue_names="{{mc_replenish_queue_names}}",
            devrsity_rules=dict(
                max_satisfied_pick=10,
                top_priority=8,
                rules = [
                    # 对作者 id 做滑动窗口 6 出 1 打散
                    dict(priority=6, window_size=6, max_num=1, attr_name="author_id_attr"),
                    # 在结果集的前 10 个结果中强插 2 个直播短视频， 注意该情况下非直播短视频的 "is_living" 为缺省状态
                    dict(priority=2, window_type="top", window_size=10, min_num=2, attr_name="is_living"),
                    # 以上规则配置中，第一条规则的优先级高于第二条规则 (6 > 2), 规则引擎会优先保证第一条规则被满足
                ]
            ),
        )
        ```
        """
        self._add_processor(MerchantUnifyChannelSortArranger(kwargs))
        return self
    
    def merchant_unify_diversify_by_rules(self, **kwargs):
        """
        MerchantUnifyDiversityRulesArranger
        ------
        规则引擎，根据打散和强插规则，对结果集的顺序进行调整，保证输出的前 max_satisfied_pick 个结果是当前规则配置下的最优解。

        - 支持 slide 窗口的打散（强插）逻辑: 每 window_size 个结果中最多（最少）出现 max_num (min_num) 个某属性的结果。
        - 支持 top 窗口的打散 (强插) 逻辑: 结果集的前 window_size 个结果中最多 (最少) 出现 max_num (min_num) 个某属性的结果。

        执行原理: 将打散和强插规则区分为不同优先级，针对每个待选位置，检测候选项的规则命中情况，使用贪心算法优先保证高优先级规则被满足。
        - 高优先级与低优先级规则冲突时，高优先级规则生效。
        - 同优先级的打散和强插规则冲突时，配置靠前的规则生效。
        - 同优先级的同类规则(强插规则之间 或 打散规则之间)冲突时，按照输入规则引擎时的原顺序选取候选项。

        算法的时间复杂度：O(kn+kmn+nlogn)，k 是规则的数目，m 是需要生成打散的结果的数目，n是候选集数目

        参数配置
        ------
        `top_priority`: [int] 选配项，设定规则体系的最高优先级，需为 >=0 的整数，默认值为 9。限定 top_priority 后，各规则 priority 属性的可取值范围为 [0, top_priority]。

        `max_satisfied_pick`: [int] [动态参数] 必配项，需为正整数，输出结果集的前 max_satisfied_pick 个结果是根据规则选取出的结果，max_satisfied_pick 之后的结果按照原顺序排序。 不考虑降级情况时，max_satisfied_pick 可设置为单次请求下发的 page_size。

        `perflog_enabled`: [bool] [动态参数] 选配项，是否开启打散计数监控，打散生效计数监控位置在 其他 -> 打散命中规则计数，默认值为 false。

        `prev_items_from_attr`: [string] 选配项，从指定的 int_list CommonAttr 中读取上一刷的 item_key 用于跨屏打散

        `front_fixed_num`: [int][动态参数] 选配项，固定前k个候选位置不变，同时计入规则，默认值为0。不支持负数

        `rules`: [list] 必配项，具体的规则配置
        - `enabled`: [bool] [动态参数] 选配项，控制规则是否生效，默认值为 true
        - `attr_name`: [string] 必配项，该条规则依赖的 item_attr (支持 int/str/int_list/str_list 类型)，对于 list 类 attr 使用 any of 的逻辑，不同规则的 attr_name 可重复
        - `window_type`: [string] 选配项，控制该条规则是 slide 窗口还是 top 窗口，可选配置为 "slide" 和 "top", 默认值为 "slide"
        - `window_size`: [int] [动态参数] 必配项，该条规则的窗口大小
        - `max_num`: [int] [动态参数] 选配项，适用于打散规则，控制窗口内最多出现多少个某属性的结果，默认值为 INT_MAX
        - `min_num`: [int] [动态参数] 选配项，适用于强插规则，控制窗口内最少出现多少个某属性的结果，默认值为 0
        - `priority`: [int] [动态参数] 必配项，该条规则的优先级，参数取值范围 [0, top_priority]，取值越大优先级越高，强烈不建议将大部分规则集中于同一优先级
        - `consider_prev_items`: [bool] [动态参数] 选配项，控制规则是否跨屏生效，默认值为 false

        注意事项
        ------
        针对某个 item_attr 配置打散/强插规则时，其所有的取值都会被执行打散/强插。如果只希望对其中的部分取值执行打散/强插，请将不参与打散/强插的取值清空。

        例如，当某个 item_attr 有 0 和 1 两种取值时，如果只需要对取值为 1 的 item 执行打散， 则需要在调用本方法前将 0 值清空。

        调用示例
        ------
        ``` python
        .diversify_by_rules(
            max_satisfied_pick=10,
            top_priority=8,
            rules = [
                # 对作者 id 做滑动窗口 6 出 1 打散
                dict(priority=6, window_size=6, max_num=1, attr_name="author_id_attr"),
                # 在结果集的前 10 个结果中强插 2 个直播短视频， 注意该情况下非直播短视频的 "is_living" 为缺省状态
                dict(priority=2, window_type="top", window_size=10, min_num=2, attr_name="is_living"),
                # 以上规则配置中，第一条规则的优先级高于第二条规则 (6 > 2), 规则引擎会优先保证第一条规则被满足
            ]
        )
        ```
        """
        self._add_processor(MerchantUnifyDiversityRulesArranger(kwargs))
        return self

    def merchant_unify_barrier_arranger(self, **kwargs):
        """
        MerchanUnifytItemBarrierArranger
        -------
        对 pass_flags 候选保量，对其余后选进行等比例控量, 最终保留 size_limit 个候选。
        
        [设计文档](https://docs.corp.kuaishou.com/d/home/<USER>

        参数
        ------
        
        - `size_limit`: [必填][int][动态参数] 截断后 item 数量。
        
        - `pass_flags`: [必填][dict] 保送标记；如果保送候选数量大于控量总数，那么也等比例取 top
            
        调用示例
        ------
        ``` python
        merchant_unify_barrier_arranger(
            size_limit=1500,
            pass_flags={
                "is_redirect_flag": 1, # 对重定向保送
            },
        )
        ```
        """
        self._add_processor(MerchantUnifyItemBarrierArranger(kwargs))

    def merchant_unify_channel_truncate(self, **kwargs):
        """
        MerchantUnifyChannelTruncateArranger
        -----
        [统一截断、归因和保量算子](https://docs.corp.kuaishou.com/k/home/<USER>/fcACnXxaRNsJGovPVoDPT5pk4?ro=false)

        参数
        -----
        - `primary_score`: [必填][string][动态参数] 排序分数的 item attr name

        - `limit`: [必填][int][动态参数] 截断数量

        - `import_common_attrs`: [选填][string list] 需要导入的 common attr 名称列表

        - `import_item_attrs`: [选填][string list] 需要导入的 common attr 名称列表
        
        - `channels`: [必填][dict list] 染色&打标通道: 1.染色和增量打标：越前后染色 reason 优先级越高；2. 
        非增量打标：满足条件就打标。dict 参数：

            * `type`: [必填][string] 通道类型 `score` or `condition`

            * `name`: [必填][string] 通道名称

            * `sort_score` [选填][string] 通道对应的排序分

            * `reason` [选填][int][动态参数] 通道对应的 reason

            * `enable` [选填][int][动态参数] 是否生效的 common attr name

            * `select_item` 语法和 [select_item](https://dragonfly.corp.kuaishou.com/#/wiki/processor_config?id=select_item) 语法和能力保持一致

            * `set_attr_value` [选填][dict list] 打标参数

                * `attr_name`: [必填][string] 打标的 attr name

                * `op`: [必填][string] 打标的操作符 `=` 、 `|=`

                * `value`: [必填][int/string] 打标的值

                * `default_val`: [必填][int/string] 打标的值

                * `is_unique`: [选填][bool] 该染色操作是否先对于其它 channel 唯一，默认 False
        
        - `pass_channels`: [必填][dict list] 保量通道； 1. 越靠前优先级越高； 2. 如果 item 已经能透出，不占用替换名额

            * `pass_version`: [动态参数][int] 1 插入到最前面 或者从头到尾遍历替换 ； 0 插入的最末尾 或者从尾到头遍历替换； 默认值为 0

            * `limit`: [必填][int] 最多替换几个候选

            * `re_cmp_expr`: [选填][string][动态参数] 替换表达式， 为空时无条件直接强插； 使用改项是, 需要设置 `import_common_attrs` 和 `import_item_attrs` ; 语法举例: `from.a < to.a && from.a > ab_thr && to.b < 0.5`

            * 其它参数同 `channels` 的 `type` 为 `condition` 的所有取值

        调用示例
        ------
        ``` python
        merchant_unify_channel_truncate(
            primary_score="traffic_worth_score",
            limit=5,
            channels=[ 
                {
                    "type": "score",  # 如果按当前 score 排序不能透出，则染色当前 reason
                    "name": "xx_bonus",
                    "attr_name": "traffic_worth_score_no_xxx",
                    "reason": 1234,
                    "set_attr_value": [# 打标
                        {"attr_name": "is_xxx_flag", "op": "=", "value": 1, "default_val": 0},
                        {"attr_name": "upliftPvFlag", "op": "|=", "value": 1<<10, "default_val": 0}
                    ]
                },
                {
                    "type": "condition",  # 基于条件染色，e.g.心智染色
                    "name": "fe",
                    "enable_attr": "enable_unify_truncate_fe_channel", # 支持小流量实验
                    "select_item": { 
                        "attr_name": "p_str",
                        "compare_to": "str",
                        "select_if": "==",
                        "select_if_attr_missing": True
                    },
                    "reason": 1235
                    "set_attr_value": [# 打标
                        ...
                    ]
                }
            ],
            pass_channels=[ # 1. 越靠后优先级越高；2. 如果 item 已经能透出，则不替换
                {
                    "target_item": "is_xxx",  # 圈选 is_xxx == 1 的候选
                    "name": "force_insert",
                    "pass_version": 0, # `head` or `tail`
                    "select_item": { 
                        "attr_name": "p_str",
                        "compare_to": "str",
                        "select_if": "==",
                        "select_if_attr_missing": True
                    },
                    "reason": 1236,
                    "limit": 1, # 最多替换几个候选
                    "set_attr_value": [# 打标
                        ...
                    ],
                    "re_cmp_expr": "from.a < to.a && from.a > ab_thr && to.b < 0.5" # 替换表达式
                }
            ]
        )
        ```
        """
        self._add_processor(MerchantUnifyChannelTruncateArranger(kwargs))
        return self

    def dynamic_computation_allocation_feature_extraction_enrich(self, **kwargs):
        """
        DynamicComputationAllocationFeatureExtractionEnricher
        ------
        DynamicComputationAllocationFeatureExtractionEnricher 用于调用 dcaf 服务进行特征补充

        参数配置
        ------
        - `biz_name`: [common attr][string][动态参数] 业务名称，绑定 kconf
        - `pack_user_features`: [common attr list] 用户侧特征，[common attr][int/double]
        - `pack_context_features`: [item attr list] item attr 先聚合为 common attr list，再打包。当 item 请求不足 quota_upper 上限时，pack_context_features 自动补 0
        - `dcaf_features`: [string] 产出的特征，拼接成一维的
        
        调用示例
        ------
        ``` python
            dynamic_computation_allocation_feature_extraction_enrich(
                biz_name="merchant_live_1pp",
                pack_user_features=[],
                pack_context_features=[],
                dcaf_features="",
            )
        ```
        """
        self._add_processor(DynamicComputationAllocationFeatureExtractionEnricher(kwargs))
        return self 

    def dynamic_computation_allocation_sample_creation_enrich(self, **kwargs):
        """
        DynamicComputationAllocationSampleCreationEnricher
        ------
        DynamicComputationAllocationSampleCreationEnricher 用于调用 dcaf 服务进行样本补充

        参数配置
        ------
        - `biz_name`: [common attr][string][动态参数] 业务名称，绑定 kconf
        - `sample_maker`: [common attr][int][动态参数] 样本构造器：1. 基于粗排多队列 Zigzag merge 序构造；2. 连续模拟多次算力截断构造；3. 基于粗排分构造
        - `aggregator`: [common attr][string][动态参数] `mtb_sum`: sum(egpm*mtb_rate); `max`: max(egpm)；`sum`: sum(egpm)
        - `is_rerank`: [common attr][int][动态参数] 1 表示是 rerank 请求
        - `last_egpm`: [common attr][double][动态参数] rerank
        - `output_json`: [common attr][string] 输出的 common attr 名
        - `common_attrs`: [common attr list] 用户侧额外落盘字段，支持任意类型
        - `limit_num`: [common attr][int] 截断数量
        - `dcaf_features`: [common attr] 产出的特征，拼接成一维的
        - `mc_truncate_seq`: [item attr][int] 粗排截断序; 仅用于样本构造器1
        - `mc_quota_level`: [item attr][int] 粗排多队列截断序; 仅用于样本构造器2
        - `mc_score`: [item attr][double] 粗排排序分；仅用于样本构造器3
        - `rank_score`: [item attr][int] 内部混排/精排 排序分
        - `egpm`: [item attr][double]
        - `mtb_rate`: [item attr][double] 穿透率; 仅用于聚合函数为 mtb_sum

        调用示例
        ------
        ``` python
            dynamic_computation_allocation_sample_creation_enrich(
                biz_name="merchant_live_1pp",
                common_attrs=[],
                dcaf_features="",
                sample_maker=1,
                mc_truncate_seq="",
                mc_quota_level="",
                mc_score=""
                rank_score=""
                aggregator=""
                egpm=""
                mtb_rate=""
                is_rerank=""
                last_egpm=""
                output_json=""
            )
        ```
        """
        self._add_processor(DynamicComputationAllocationSampleCreationEnricher(kwargs))
        return self 

    def gen_merchant_ab_suffix(self, **kwargs):
        """
        GenMerchantAbSuffixEnricher
        ------
        根据请求类型生成商家 AB 后缀，支持 kconf 配置化。
        
        将原始的 lua 脚本逻辑转换为 dragon 算子，通过解析不同的请求类型来生成相应的后缀和标记。
        支持多层后缀构建，包括基础后缀、请求类型后缀和应用来源前缀。

        参数配置
        ------
        - `app_source_type`: [string] 应用来源类型的 common attr 名称，默认为 "app_source_type"
        
        - `import_common_attrs`: [string list] 需要导入的 common attr 名称列表，通常包括 'origin_request_type' 等
        
        - `export_common_attrs`: [string list] 需要导出的 common attr 名称列表，包括各种标记和后缀信息
        
        - `kconf_key`: [string] kconf 配置的 key，用于获取业务配置
        
        - `prioritized_suffix`: [string] 优先级后缀列表的 common attr 名称，默认为 "prioritized_suffix"
        
        - `sample_tag`: [string] 样本标签的 common attr 名称，默认为 "sample_tag"

        调用示例
        ------
        ``` python
        flow.gen_merchant_ab_suffix(
            app_source_type="app_source_type",
            import_common_attrs=['origin_request_type'],
            export_common_attrs=['is_slide', 'is_nearby', 'is_explore', 'is_gold_coin', 'is_buy_home', 'is_square', 'is_explore_inner', 'is_record', 'is_slide_inner', 'prioritized_suffix', 'sample_tag'],
            kconf_key="xxx.xxx.xxx",
            prioritized_suffix="prioritized_suffix",
            sample_tag="sample_tag"
        )
        ```
        """
        self._add_processor(GenMerchantAbSuffixEnricher(kwargs))
        return self 