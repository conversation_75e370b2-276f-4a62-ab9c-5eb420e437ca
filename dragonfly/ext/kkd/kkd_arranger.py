#!/usr/bin/env python3
# coding=utf-8
"""
filename: kkd_arranger.py
description: 
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-07-06 20:40:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class EyeshotVariantClusterSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "eyeshot_variant_cluster_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["size_limit", "global_cut_ratio", "min_survival"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr",""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name",""))
        return attrs


    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs