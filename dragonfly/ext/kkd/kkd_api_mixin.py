#!/usr/bin/env python3
# coding=utf-8
"""
filename: kkd_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kkd api mixin
author: <EMAIL>
date: 2020-05-16 00:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kkd_retriever import *
from .kkd_enricher import *
from .kkd_arranger import *

class KkdApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 kkd 相关的 Processor 接口
  """

  def retrieve_from_kkd_leaf(self, **kwargs):
    """
      CommonRecoKkdLeafRetriever
      ------
      通过 kkd leaf 获取召回结果

      参数配置
      ------
      `kess_service`: [string] 原 RecoLeaf 的 kess 服务名称

      `service_group`: [string] 原 RecoLeaf 的 kess 服务组，默认值为 "PRODUCTION"

      `service_shard`: [string] kess 服务的 shard，默认值是 s0

      `timeout_ms`: [int] 原 RecoLeaf 的超时时间，默认值为 1500

      `reason`: [int] 召回原因

      `count`: [int] [动态参数] 请求的 item 数目

      `combo_index`: [bool] [动态参数] 是否使用 combo index 索引 key 生成方案，默认 False

      `is_debug`: [bool] [动态参数] 是否打印 debug 信息，默认 False

      `leaf_request_base64` [string] 从指定的 CommonAttr 中获取 base64 格式的 RecoLeafRequest 序列化流

      调用示例
      ``` python
      .retrieve_from_kkd_leaf(
        kess_service = "grpc_xx",
        reason = 100,
        count = 8,
        combo_index = False,
        is_debug = False,
      )
      ```
    """
    self._add_processor(CommonRecoKkdLeafRetriever(kwargs))
    return self

  def retrieve_from_kkd_grpc_i2i(self, **kwargs):
    """
      CommonRecoKkdGrpcI2iRetriever
      -----
        通过I2I Grpc 服务召回, Eg: W2V I2I, DSSM I2I

        参数配置
      -----
      `itemcf_service_name`: [string] 必填，Grpc 服务名称

      `itemcf_service_cluster`: [string] 必填，Grpc Cluster 名称

      `i2i_app_name`: [string] 必填，I2I App 名称

      `reason`: [int] 必填，召回原因

      `itemcf_service_timeout`: [int] 选填，超时时间， 默认值 1000

      `items_from_attr`: [string list] 必填，从指定的 CommonAttr 中获取 Trigger idList

      `attr_single_limit`: [int] 选填，每种 CommonAttr 作为Trigger id的最大数量，默认-1

      `duid`: [int] 选填，用户id

      `filter_threshold`: [float] 选填，截断阈值，默认值 0

      `ret_num `: [int] 必填，召回结果数

        调用示例
        ``` python
        .retrieve_from_kkd_grpc_i2i(
            itemcf_service_name = "grpc_PearlCommonLeafItemCf",
            reason = 1200,
            items_from_attr = ["recent_click_list"],
            attr_single_limit = 200,
            duid = 1234567,
            filter_threshold = 0.01,
        )
        ```
    """
    self._add_processor(CommonRecoKkdGrpcI2iRetriever(kwargs))
    return self

  def retrieve_from_kkd_grpc_i2i_pgc(self, **kwargs):
    """
      CommonRecoKkdGrpcI2iPgcRetriever
      -----
        通过I2I Grpc 服务召回 (PGC), Eg: W2V I2I, DSSM I2I

        参数配置
      -----
      `itemcf_service_name`: [string] 必填，Grpc 服务名称

      `itemcf_service_cluster`: [string] 必填，Grpc Cluster 名称

      `i2i_app_name`: [string] 必填，I2I App 名称

      `reason`: [int] 必填，召回原因

      `itemcf_service_timeout`: [int] 选填，超时时间， 默认值 1000

      `items_from_attr`: [string list] 必填，从指定的 CommonAttr 中获取 Trigger idList

      `attr_single_limit`: [int] 选填，每种 CommonAttr 作为Trigger id的最大数量，默认-1

      `duid`: [int] 选填，用户id

      `filter_threshold`: [float] 选填，截断阈值，默认值 0

      `ret_num `: [int] 必填，召回结果数

        调用示例
        ``` python
        .retrieve_from_kkd_grpc_i2i_pgc(
            itemcf_service_name = "grpc_PearlCommonLeafItemCf",
            reason = 1200,
            items_from_attr = ["recent_click_list"],
            attr_single_limit = 200,
            duid = 1234567,
            filter_threshold = 0.01,
        )
        ```
    """
    self._add_processor(CommonRecoKkdGrpcI2iPgcRetriever(kwargs))
    return self

  def retrieve_from_kkd_grpc_u2u(self, **kwargs):
    """
      CommonRecoKkdGrpcU2uRetriever
      -----
        通过U2U Grpc 服务召回, Eg: U2U, Applist U2U，Applist

        参数配置
      -----
      `u2u_service_name`: [string] 必填，Grpc 服务名称

      `u2u_service_cluster`: [string] 必填，Grpc Cluster 名称

      `u2u_retr_type`: [string] 必填，I2I App 名称

      `reason`: [int] 必填，召回原因

      `u2u_service_timeout`: [int] 选填，超时时间， 默认值 1000

      `items_from_attr`: [string list] 必填，从指定的 CommonAttr 中获取 Trigger idList

      `duid`: [int] 选填，用户id

      `ret_cnt`: [int] 必填，召回结果数

        调用示例
        ``` python
        .retrieve_from_kkd_grpc_u2u(
            u2u_service_name = "grpc_PearlCommonLeafItemCf",
            reason = 1200,
            items_from_attr = ["recent_click_list"],
            attr_single_limit = ["200"],
            duid = 1234567,
            filter_threshold = 0.01,
        )
        ```
    """
    self._add_processor(CommonRecoKkdGrpcU2uRetriever(kwargs))
    return self

  def retrieve_by_common_attr_kkd(self, **kwargs):
    """
    CommonRecoKkdCommonAttrRetriever
    ------
    从某个 int 或 int_list 类型的 CommonAttr 中获取 item_id 进行召回并填入结果集

    支持kkd的64位itemid转换

    参数配置
    ------
    `attr`: [string] 待召回的 CommonAttr 名称

    `reason`: [int] 召回原因

    `num_limit`: [int] 选配项, 限制从该 CommonAttr 里召回的最大数目

    `retrieval_item_type`: [int] 选配项, 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `need_id_transfer`: [bool] 选配项，是否将id进行48位截断

    `is_string`: [bool] 选配项，是否需要进行字符串转int操作

    调用示例
    ------
    ``` python
    .retrieve_by_common_attr_kkd(attr="like_list", reason=999)
    ```
    """
    attr = {
      "name": kwargs.pop("attr"),
      "reason": kwargs.pop("reason"),
    }
    for key in ["num_limit", "retrieval_item_type", "need_id_transfer", "is_string"]:
      if key in kwargs:
        attr[key] = kwargs.pop(key)
    conf = { "attrs": [attr] }
    conf.update(kwargs)
    self._add_processor(CommonRecoKkdCommonAttrRetriever(conf))
    return self


  def eyeshot_variant_cluster_sort(self, **kwargs):
    """
    EyeshotVariantClusterSortArranger
    """
    self._add_processor(EyeshotVariantClusterSortArranger(kwargs))
    return self

  def retrieve_by_kkd_mix_redis(self, **kwargs):
    """
    CommonRecoKkdRedisMixRetriever
    ------
    从 redis 读取 string value，按照正则格式解析出 item_id 根据key score加权后排序输出

    参数配置
    ------
    `reason`: [int] 召回原因

    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限

    `cluster_name`: [string] redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 10

    `key`: [string] 指定一个固定的 redis key (注意：`key` 与 `key_from_attr` 需至少配置一项)

    `key_from_attr`: [string] 从指定的 common_attr 中获取动态的 redis key，支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string (注意：`key` 与 `key_from_attr` 需至少配置一项)

    `key_prefix`: [string] [动态参数] 选配项，为 `key_from_attr` 中每个 redis key 的值添加一个前缀，默认为空

    `key_score_from_attr`: [string] 选配项，从指定的 common_attr 中获取动态的 redis key 的分数，double 类型，需要与 `key_from_attr` 长度一致。

    `retrieve_num_per_key`: [int] [动态参数] 选配项，单个 key 召回的数量上限，默认为 10000

    `save_src_key_to_attr`: [string] 选配项，将 key 填入到召回 item 的指定 item attr 中 (不包含 key_prefix)

    `item_regex`: [string] 单个 item 信息的正则格式，第一个元素应为 item_id

    `extra_item_attrs`: [list] 选配项，每个 item 需要额外填充的 item_attrs 列表，个数与 item_pattern 中除 item_id 外的 '()' 个数对应
      - `name`: [string] 作为 item_attr 的名称
      - `type`: [string] 按什么值类型对该 attr 进行抽取，仅支持: int/double/string, 默认为 string
      - `as_score`: [bool] 选配项, 是否将该 item_attr 抽取后的值作为 item 的初始 score, 默认为 False, 注意：若设置为 True 则 `type` 必须为 double 或 int。

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    调用示例
    ------
    ``` python
    # 对应 redis 格式示例
    # redis_value = "38680800976_0.99_cat,38670790701_0.38_dog,38650272587_0.33_pig"
    # [item1] item_id: 38680800976, item_attrs: {emp_ctr=0.99, tag="cat"}
    # [item2] item_id: 38670790701, item_attrs: {emp_ctr=0.38, tag="dog"}
    # [item3] item_id: 38650272587, item_attrs: {emp_ctr=0.33, tag="pig"}
    .retrieve_by_kkd_mix_redis(
      reason = 100,
      retrieve_num = 1000,
      cluster_name = "my_kcc_cluster",
      key_from_attr = "source_pid_list",
      key_score_from_attr = "source_pid_score_list",
      item_regex = r"(\\d+)_([0-9]{1,}[.][0-9]*)_([a-z]*)",
      extra_item_attrs = [
        {"name": "emp_ctr", "type": "double"},
        {"name": "tag", "type": "string"}
      ]
    )
    ```
    """
    self._add_processor(CommonRecoKkdRedisMixRetriever(kwargs))
    return self

  def retrieve_kkd_user_attention_category(self, **kwargs):
    """
    CommonRecoKkdUserCategoryRetriever
    ------
    召回用户 category 用于 hard attention

    参数配置
    ------
    `reason`: [int] 选配项，召回原因，默认值 1

    `category_list_attr`: [string] 选配项， attention category， 默认为全部

    `max_category_num`: [int] 选配项，召回 category 结果的数量上限，默认值 50

    `max_click_list_size`: [int] 选配项，点击序列上限，默认值 64

    `output_sign_attr`: [string] 选配项，sign 输出 attr name，默认值 category_attr_signs

    `output_slot_attr`: [string] 选配项，slot 输出 attr name，默认值 category_attr_slots

    `default_category_id`: [int] 选配项，默认值 0

    调用示例
    ------
    ``` python
    .retrieve_kkd_user_attention_category()
    ```
    """
    self._add_processor(CommonRecoKkdUserCategoryRetriever(kwargs))
    return self

  def retrieve_kkd_attention_cross_category(self, **kwargs):
    """
    CommonRecoKkdCrossCategoryRetriever
    ------
    召回用户 category 用于 hard attention
    CommonRecoKkdUserCategoryRetriever 改进版

    参数配置
    ------
    `reason`: [int] 选配项，召回原因，默认值 1

    `output_sign_attr`: [string] 选配项，sign 输出 attr name，默认值 category_attr_signs

    `output_slot_attr`: [string] 选配项，slot 输出 attr name，默认值 category_attr_slots

    `default_category_id`: [int] 选配项，默认值 0

    `feature_config`: [list] 特征配置：
      - `name`: [string] 必配，特征 attr
      - `slot`: [int] 必配，特征 kuiba slot
      - `converter`: [string] 必配，id/list/list_size

    调用示例
    ------
    ``` python
    .retrieve_kkd_attention_cross_category()
    ```
    """
    self._add_processor(CommonRecoKkdCrossCategoryRetriever(kwargs))
    return self

  def calc_kkd_ensemble_score(self, **kwargs):
    """
    CommonRecoKkdEnsembleScoreEnricher
    ------
    对指定的 N 个队列计算用于 ensemble sort 的综合分，计算方式:

    每个 item 在各个队列 channels[i] 的单项分公式: `Si = func(formula_version)`

    每个 item 最后输出到 `output_attr` 的综合分数值为: `S1 + S2 +...+ Sn` (`n` 为 channels 个数)

    注意：该方法只生成 ensemble score, 如需根据该综合分进行排序, 请在之后自行调用 `.sort(score_from_attr="${output_attr}")`

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (SEQ_ON_ATTR(channels[i].name) ^ regulator + smooth)`
      - 1: `Si = channels[i].weight * (1 - min(1, SEQ_ON_ATTR(channels[i].name) / item_num) ^ regulator)`
      - 2: `Si = channels[i].weight * (e ^ (channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)) - e ^ (-channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)))`

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `bucket_attr`: [string] 将从同名 int 类型 item_attr 获取分桶值
      - `bucket_size`: [int] 桶数量（超过部分进入默认桶），默认为 0 不限制
      - `quantile_key`: [string] 存储分位点的 redis key
      - `hyper_scala`: [double] [动态参数] 该队列的双曲调节因子
      - `as_seq_value`: [bool] 是否跳过对该队列的排序操作，直接使用该 attr 的值作为 seq 序号参与 score 计算，默认为 False
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `regulator`: [double] [动态参数] ensemble sort 各队列的调节因子

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `start_seq`: [int] 选配项，序号的起始值，默认为 0

    `cliff_ratio`: [double] [动态参数] 选配项，ensemble 队列排序序号开始降权 position 的比例点，即对排名 `队列长度 * cliff_ratio` 之后的 item 进行打压

    `cliff_height`: [int] [动态参数] 选配项，ensemble 队列降权系数，即对排名 `队列长度 * cliff_ratio` 之后的 item 序号额外加上该值作为惩罚

    `stable_sort`: [bool] 选配项，是否使用稳定排序来计算各 item 序号，默认值 False

    `allow_accumulation_step`: [bool] 选配项，是否允许累计步长，默认值 False

    `accumulation_span`: [int] 选配项，步长跨度，默认值 0

    `max_accumulation_limit`: [int] 选配项，最大步长限制，默认值 20

    `default_bucket`: [int] 选配项，默认桶，默认值 0

    `max_bucket_size`: [int] 选配项，最大桶数量（超过部分进入默认桶），默认值 0 不限制

    `bucket_merge`: [bool] 选配项，bucket 内步长是否乘 bucket 数量，默认值 False

    调用示例
    ------
    ``` python
    .calc_kkd_ensemble_score(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      regulator = "{{ensemble_regulator}}",
      smooth = "{{ensemble_smooth}}",
      output_attr = "ensemble_score",
    )
    ```
    """
    self._add_processor(CommonRecoKkdEnsembleScoreEnricher(kwargs))
    return self