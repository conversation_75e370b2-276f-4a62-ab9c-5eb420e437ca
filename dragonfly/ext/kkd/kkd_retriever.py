#!/usr/bin/env python3
# coding=utf-8
"""
filename: kkd_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for kap
author: <EMAIL>
date: 2020-05-16 00:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class CommonRecoKkdLeafRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_kkd_leaf"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    attrs.update(self.extract_dynamic_params(self._config.get("combo_index")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_debug")))
    if self._config.get("leaf_request_base64", ""):
      attrs.add(self._config.get("leaf_request_base64", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("count"), (int, str)), "count 需为整数或字符串类型")
    check_arg(isinstance(self._config.get("combo_index"), bool), "combo_index 需为布尔类型")
    check_arg(isinstance(self._config.get("is_debug"), bool), "is_debug 需为布尔类型")
    if isinstance(self._config["count"], int):
      check_arg(self._config["count"] > 0, "count 为整数时需大于 0")
    if isinstance(self._config["count"], str):
      check_arg(self._config["count"].startswith("{{") and self._config["count"].endswith("}}"), "count 为字符串时需满足动态参数 {{}} 格式")

class CommonRecoKkdGrpcI2iRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "retrieve_from_kkd_grpc_i2i"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("duid", "")))
        for key in ["itemcf_service_name", "i2i_app_name", "itemcf_service_cluster", "itemcf_service_timeout", "filter_threshold", "ret_num"]:
           attrs.add(self._config.get(key, ""))
        return attrs

    @strict_types
    def _check_config(self) -> None:
        check_arg(isinstance(self._config.get("itemcf_service_name"), str) and self._config["itemcf_service_name"], "itemcf_service_name 需为非空字符串")
        check_arg(isinstance(self._config.get("i2i_app_name"), str) and self._config["i2i_app_name"], "i2i_app_name 需为非空字符串")
        check_arg(isinstance(self._config.get("itemcf_service_cluster"), str) and self._config["itemcf_service_cluster"], "itemcf_service_cluster 需为非空字符串")
        check_arg(isinstance(self._config.get("itemcf_service_timeout"), int) and self._config["itemcf_service_timeout"] > 0, "itemcf_service_timeout 需为大于0的整数")
        check_arg(isinstance(self._config.get("filter_threshold"), float) and self._config["filter_threshold"] > 0, "filter_threshold 需为大于0的复数")
        check_arg(isinstance(self._config.get("ret_num"), int) and self._config["ret_num"] > 0, "ret_num 需为大于0的整数")

class CommonRecoKkdGrpcI2iPgcRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "retrieve_from_kkd_grpc_i2i_pgc"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("duid", "")))
        for key in ["itemcf_service_name", "i2i_app_name", "itemcf_service_cluster", "itemcf_service_timeout", "filter_threshold", "ret_num"]:
           attrs.add(self._config.get(key, ""))
        return attrs

    @strict_types
    def _check_config(self) -> None:
        check_arg(isinstance(self._config.get("itemcf_service_name"), str) and self._config["itemcf_service_name"], "itemcf_service_name 需为非空字符串")
        check_arg(isinstance(self._config.get("i2i_app_name"), str) and self._config["i2i_app_name"], "i2i_app_name 需为非空字符串")
        check_arg(isinstance(self._config.get("itemcf_service_cluster"), str) and self._config["itemcf_service_cluster"], "itemcf_service_cluster 需为非空字符串")
        check_arg(isinstance(self._config.get("itemcf_service_timeout"), int) and self._config["itemcf_service_timeout"] > 0, "itemcf_service_timeout 需为大于0的整数")
        check_arg(isinstance(self._config.get("filter_threshold"), float) and self._config["filter_threshold"] > 0, "filter_threshold 需为大于0的复数")
        check_arg(isinstance(self._config.get("ret_num"), int) and self._config["ret_num"] > 0, "ret_num 需为大于0的整数")

class CommonRecoKkdGrpcU2uRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "retrieve_from_kkd_grpc_u2u"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("duid", "")))
        for key in ["u2u_service_name", "u2u_retr_type", "u2u_service_cluster", "u2u_service_timeout", "ret_cnt"]:
           attrs.add(self._config.get(key, ""))
        return attrs

    @strict_types
    def _check_config(self) -> None:
        check_arg(isinstance(self._config.get("u2u_service_name"), str) and self._config["u2u_service_name"], "u2u_service_name 需为非空字符串")
        check_arg(isinstance(self._config.get("u2u_retr_type"), str) and self._config["u2u_retr_type"], "u2u_retr_type 需为非空字符串")
        check_arg(isinstance(self._config.get("u2u_service_cluster"), str) and self._config["u2u_service_cluster"], "u2u_service_cluster 需为非空字符串")
        check_arg(isinstance(self._config.get("u2u_service_timeout"), int) and self._config["u2u_service_timeout"] > 0, "u2u_service_timeout 需为大于0的整数")
        check_arg(isinstance(self._config.get("ret_cnt"), int) and self._config["ret_cnt"] > 0, "ret_cnt 需为大于0的整数")

class CommonRecoKkdCommonAttrRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "retrieve_by_common_attr_kkd"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = { v["name"] for v in self._config["attrs"] }
        if "exclude_items_in_attr" in self._config:
            attrs.add(self._config["exclude_items_in_attr"])
        return attrs

    @strict_types
    def _check_config(self) -> None:
        check_arg(isinstance(self._config.get("attrs"), list), "attrs 需为 list 类型")
        check_arg(all(isinstance(v, dict) for v in self._config["attrs"]), "attrs 中的每项需为 dict 类型")
        check_arg(all("name" in v for v in self._config["attrs"]), "attrs 中的每项需包含 name 字段")
        check_arg(all(isinstance(v["name"], str) and v["name"] for v in self._config["attrs"]), "name 字段需为非空字符串")
        check_arg(all("reason" in v for v in self._config["attrs"]), "attrs 中的每项需包含 reason 字段")
        check_arg(all(isinstance(v["reason"], int) and v["reason"] > 0 for v in self._config["attrs"]), "reason 字段需为 > 0 的整数")

class CommonRecoKkdRedisMixRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_kkd_mix_redis"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("extra_item_attrs", []):
      attrs.add(config["name"])

    save_src_key_to_attr = self._config.get("save_src_key_to_attr")
    if save_src_key_to_attr is not None:
      attrs.add(save_src_key_to_attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("key_prefix", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num_per_key", "")))
    for key in ["key_from_attr", "key_score_from_attr"]:
      if self._config.get(key):
        attrs.add(self._config.get(key))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("item_regex"), str) and self._config["item_regex"],
              "item_regex 需为 非空字符串")
    check_arg(isinstance(self._config.get("retrieve_num"), int) and self._config["retrieve_num"] > 0 or
              isinstance(self._config.get("retrieve_num"), str),
              "retrieve_num 需为大于 0 的整数")
    check_arg(any(self._config.get(v) for v in ["key", "key_from_attr"]), "缺少 key 或 key_from_attr 配置")
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and self._config["cache_bits"] >= 0,
                "cache_bits 需为大于等于 0 的整数")
    if "cache_delay_delete_ms" in self._config:
      check_arg(isinstance(self._config.get("cache_delay_delete_ms"), int) and self._config["cache_delay_delete_ms"] > 0,
                "cache_delay_delete_ms 需为大于 0 的整数")
    if "cache_expire_second" in self._config:
      check_arg(isinstance(self._config.get("cache_expire_second"), int) and self._config["cache_expire_second"] > 0,
                "cache_expire_second 需为大于 0 的整数")
    for config in self._config.get("extra_item_attrs", []):
      if config.get("as_score") is True:
        check_arg(config.get("type") in ("int", "double"), "as_score 为 True 时 type 必须为 int 或 double")

    extra_item_attr_as_score = self._config.get("extra_item_attr_as_score")
    if extra_item_attr_as_score:
      check_arg(isinstance(extra_item_attr_as_score, str), "extra_item_attr_as_score 应为非空字符串")
      score_attr = filter(lambda x: x["name"] == extra_item_attr_as_score and x["type"] in ("int", "double"), self._config.get("extra_item_attrs", []))
      check_arg(next(score_attr, None), "extra_item_attr_as_score 应为 extra_item_attrs 中一项，且类型为 int / double")

class CommonRecoKkdUserCategoryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_kkd_user_attention_category"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if self._config.get(key) is not None:
        attrs.add(self._config.get(key))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["category_list_attr"]:
      if self._config.get(key):
        attrs.add(self._config.get(key))
    return attrs

  @strict_types
  def _check_config(self) -> None:

    if "reason" in self._config:
      check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] >= 0,
                "reason 需为大于等于 0 的整数")

    if "max_category_num" in self._config:
      check_arg(isinstance(self._config.get("max_category_num"), int) and self._config["max_category_num"] > 0,
                "max_category_num 需为大于 0 的整数")

    if "max_click_list_size" in self._config:
      check_arg(isinstance(self._config.get("max_click_list_size"), int) and self._config["max_click_list_size"] > 0,
                "max_click_list_size 需为大于 0 的整数")

    if "default_category_id" in self._config:
      check_arg(isinstance(self._config.get("default_category_id"), int) and self._config["default_category_id"] >= 0,
                "max_click_list_size 需为大于等于 0 的整数")

class CommonRecoKkdCrossCategoryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_kkd_attention_cross_category"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if self._config.get(key) is not None:
        attrs.add(self._config.get(key))
    return attrs

  @strict_types
  def _check_config(self) -> None:

    check_arg(isinstance(self._config.get("feature_config"), list) and len(self._config["feature_config"]) > 0,
        "feature_config 不能为空")

    attr_vaild = True
    slot_vaild = True
    converter_vaild = True
    for cgf in self._config.get("feature_config", []):
      attr_vaild = cgf.get("name", False) and attr_vaild
      slot_vaild = cgf.get("slot", False) and slot_vaild
      converter_vaild = (cgf.get("converter", "") in ["id", "list", "list_size"]) and converter_vaild

    check_arg(attr_vaild, "feature_config name 不能为空")
    check_arg(slot_vaild, "feature_config slot 不能为空")
    check_arg(converter_vaild, "feature_config converter 不能为空，只能为(id/list/list_size)")

    if "reason" in self._config:
      check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] >= 0,
                "reason 需为大于等于 0 的整数")

    if "default_category_id" in self._config:
      check_arg(isinstance(self._config.get("default_category_id"), int) and self._config["default_category_id"] >= 0,
                "max_click_list_size 需为大于等于 0 的整数")
