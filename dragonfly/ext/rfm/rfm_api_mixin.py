#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .rfm_enricher import *
from .rfm_retriever import *


class RfmApiMixin(CommonLeafBaseMixin):
  """
  rfm Processor API 接口的 Mixin 实现

  背景：由于这个文件最早是用于营收项目，统计 rfm 特征: 最近一次消费 (recency)、消费频率 (frequency)、消费金额 (monetary)。所以把这个子目录命名为 rfm (recency frequency monetary)。但这些特征实际上是可以用在直播的其他场景中

  主要 API: 该子目录主要用于存放通过 colossus 获取用户历史行为记录，统计用户行为特征的 API。现包括：
      common_live_colossus_gift_author_feature_resp
      common_live_colossus_gift_author_feature_v2_resp
  """

  def common_live_colossus_gift_author_feature_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureRespEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    示例
    ------
    ``` python
    common_live_colossus_gift_author_feature_resp(
       colossus_photos_attr="colossus_photos",
       author_id_attr="author_id"
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureRespEnricher(kwargs))
    return self

  def common_live_a2a_feature_v1_resp(self, **kwargs):
    """
    CommonLiveA2AFeatureV1RespEnricher
    ------
    获取 用户在A2A扩展出主播的消费信息。

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_a2a_feature_v1_resp(
       colossus_photos_attr="colossus_photos",
       author_id_attr="author_id",
       filter_time_len=0
    )
    ```
    """
    self._add_processor(CommonLiveA2AFeatureV1RespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_feature_v2_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureV2RespEnricher
    ------
    获取 user-author 的统计特征v2版本,在 common_live_colossus_gift_author_feature_resp 的基础上增加了最近1天，最近3天，最近7天，最近14天，最近28天的该用户在该主播的打赏，观看时长等特征。

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_colossus_gift_author_feature_v2_resp(
       colossus_photos_attr="colossus_photos",
       author_id_attr="author_id",
       filter_time_len=0
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureV2RespEnricher(kwargs))
    return self

  def user_author_followtime_resp(self, **kwargs):
    """
    UserAuthorFollowtimeRespEnricher
    ------
    获取 user-author 的统计特征v2版本,在 common_live_colossus_gift_author_feature_resp 的基础上增加了最近1天，最近3天，最近7天，最近14天，最近28天的该用户在该主播的打赏，观看时长等特征。

    参数
    ------
    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    user_author_followtime_resp(
       author_id_attr="author_id",
       filter_time_len=0
    )
    ```
    """
    self._add_processor(UserAuthorFollowtimeRespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_feature_v3_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureV3RespEnricher
    ------
    获取 user-author 的统计特征v3版本,在 common_live_colossus_gift_author_feature_resp 的基础上增加了最近1天，最近3天，最近7天，最近14天，最近28天的该用户在该主播的打赏，观看时长等特征。

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    `photolive_pid_list_attr`: [string] 可选。photolive曝光pid序列输入的 attr

    `photolive_ts_list_attr`: [string] 可选。photolive曝光时间序列输入的 attr

    `filter_time_len` : [int] 过滤的时间长度
    `ua_ltv_reward_decay_rate` : [float] 营收衰减系数
    `ua_ltv_time_decay_rate` : [float] 时长衰减系数
    `ua_ltv_watch_time_window` : [int] 时长统计窗口
    `ua_ltv_reward_time_window` : [int] 打赏统计窗口;
    示例
    ------
    ``` python
    common_live_colossus_gift_author_feature_v3_resp(
       colossus_photos_attr="colossus_photos",
       author_id_attr="author_id",
       photolive_pid_list_attr='uStandardLivingPhotoShowIDSeqPhotoIDList',
       photolive_ts_list_attr='uStandardLivingPhotoShowIDSeqTimeList',
       filter_time_len=0,
       ua_ltv_watch_time_window="time_window_len",
       ua_ltv_reward_time_window="reward_window_len",
       ua_ltv_time_decay_rate="time_decay_rate",
       ua_ltv_reward_decay_rate="reward_decay_rate",
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureV3RespEnricher(kwargs))
    return self

  def common_live_lifelong_seq_resp(self, **kwargs):
    """
    CommonLiveLifelongSeqEnricher
    ------
    获取 user 和 user-author 的历史行为序列

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_lifelong_seq_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveLifelongSeqEnricher(kwargs))
    return self
  
  def common_live_lifelong_seq_v2_resp(self, **kwargs):
    """
    CommonLiveLifelongSeqV2Enricher
    ------
    获取 user-author 的长周期历史行为序列，并区分消费和营收状态

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_lifelong_seq_v2_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveLifelongSeqV2Enricher(kwargs))
    return self

  def common_live_lifelong_seq_v4_resp(self, **kwargs):
    """
    CommonLiveLifelongSeqV4Enricher
    ------
    使用ColossusV4来获取 user-author 的长周期历史行为序列，并区分消费和营收状态

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_lifelong_seq_v4_resp(
     colossus_resp_attr='colossus_v4_resp',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveLifelongSeqV4Enricher(kwargs))
    return self
  
  def common_live_user_seq(self, **kwargs):
    """
    CommonLiveUserSeqEnricher
    ------
    使用ColossusV4来获取 user 的近期历史行为序列

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `enable_pid_aggre`: [bool] 是否对 pid 进行聚合

    `enable_filter_e_shop`: [bool] 是否过滤电商直播

    `enable_filter_outside_item`: [bool] 是否过滤间外直播

    `u_latest_live_seq_length`: [int] 用户近期序列长度

    `enable_filter_channel`: [bool] 是否允许通过页面过滤

    `filter_channels` : [string] 需要过滤的页面

    `filter_time_len` : [int] 过滤的时间长度
    
    示例
    ------
    ``` python
    common_live_user_seq(
     colossus_resp_attr='colossus_v4_resp',
     enable_pid_aggre=true,
     enable_filter_e_shop=true,
     enable_filter_outside_item=true,
     enable_filter_channel=true,
     filter_channels="37,38",
     u_latest_live_seq_length=50)

    ```
    """
    self._add_processor(CommonLiveUserSeqEnricher(kwargs))
    return self

  def common_live_multi_feedback_rfm_resp(self, **kwargs):
    """
    CommonLiveMultiFeedbackRFMRespEnricher
    ------
    获取 user-author 的 多反馈 统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_multi_feedback_rfm_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveMultiFeedbackRFMRespEnricher(kwargs))
    return self

  def live_segment_extract_enricher(self, **kwargs):
    """
    LiveSegmentExtractEnricher
    ------
    直播间语义段特征 batch 解析

    参数
    ------
    - `pid_list`: [必填]
    - `input_int_attr`: [必填]

    调用示例
    ------
    ```python
    live_segment_extract_enricher(
        pid_list="item_key_list",
        input_int_attr="int_output_attrs"
    )
    ```
    """
    self._add_processor(LiveSegmentExtractEnricher(kwargs))
    return self

  def common_live_multi_feedback_rfm_v2_resp(self, **kwargs):
    """
    CommonLiveMultiFeedbackRFMV2RespEnricher
    ------
    获取 user-author 的 实时短播 统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_multi_feedback_rfm_v2_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveMultiFeedbackRFMV2RespEnricher(kwargs))
    return self
  
  def common_live_multi_feedback_rfm_v3_resp(self, **kwargs):
    """
    CommonLiveMultiFeedbackRFMV3RespEnricher
    ------
    获取 user-author 的 多反馈 RFM (时间戳采用 UA 最近一次的曝光时间)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_multi_feedback_rfm_v3_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveMultiFeedbackRFMV3RespEnricher(kwargs))
    return self

  def common_live_m3_rfm_resp(self, **kwargs):
    """
    CommonLiveM3RFMRespEnricher
    ------
    获取 user-author 的间内间外点击统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_m3_rfm_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveM3RFMRespEnricher(kwargs))
    return self

  
  def common_live_same_cluster_author_list_resp(self, **kwargs):
    """
    CommonLiveSameClusterAuthorListRespEnricher
    ------
    获取相同 cluster 的主播历史

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    示例
    ------
    ``` python
    common_live_same_cluster_author_list_resp(
     colossus_resp_attr='colossus_output_2',
    )

    ```
    """
    self._add_processor(CommonLiveSameClusterAuthorListRespEnricher(kwargs))
    return self


  def common_video_colossus_live_sideinfo_rfm_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusLiveSideInfoRFMRespEnricher
    ------
    获取直播序列的Aid Hard Search对应的Side Info

    参数
    ------
    `colossus_resp_attr`: [string] 短视频列表, V2

    示例
    ------
    ``` python
    common_video_colossus_live_sideinfo_rfm_resp_enricher(
     colossus_resp_attr='colossus_output_2',
    )

    ```
    """
    self._add_processor(CommonVideoColossusLiveSideInfoRFMRespEnricher(kwargs))
    return self


  def common_video_live_mixed_sequence_resp(self, **kwargs):
    """
    CommonVideoLiveMixedRespEnricher
    ------
    获取 user-author 的间内间外点击统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_video_live_mixed_sequence_resp(
     video_colossus_resp_attr = 'colossus_output_2',
     live_colossus_resp_attr = 'colossus_output_v4',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonVideoLiveMixedRespEnricher(kwargs))
    return self

  def common_video_live_mixed_sequence_v2_resp(self, **kwargs):
    """
    CommonVideoLiveMixedV2RespEnricher
    ------
    获取 user 在短视频和直播的auther序列

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_video_live_mixed_v2_sequence_resp(
     video_colossus_resp_attr = 'colossus_output_2',
     live_colossus_resp_attr = 'colossus_output_v4',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonVideoLiveMixedV2RespEnricher(kwargs))
    return self

  def common_live_click_rfm_resp(self, **kwargs):
    """
    CommonLiveClickRFMRespEnricher
    ------
    获取 user-author 的间内间外点击统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_click_rfm_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)
    ```
    """
    self._add_processor(CommonLiveClickRFMRespEnricher(kwargs))
    return self

  def common_live_cluster_rfm_resp(self, **kwargs):
    """
    CommonLiveClusterRFMRespEnricher
    ------
    获取 user-cluster 的间内间外点击统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_cluster_rfm_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",)

    ```
    """
    self._add_processor(CommonLiveClusterRFMRespEnricher(kwargs))
    return self

  def common_live_tag_seq_resp(self, **kwargs):
    """
    CommonLiveTAGSeqRespEnricher
    ------
    获取 user live-streaming tag sequence,支持基于每天绝对时间过滤

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度

    `cur_time_threhold`: [int] 默认为0，表示不按照绝对时间过滤；取值大于0时，取绝对时间附近cur_time_threhold长度的窗口

    `topk`: [int] 选取高频cluster的topk
    
    示例
    ------
    ``` python
    common_live_tag_seq_resp(
     colossus_resp_attr='colossus_xxx',
    )

    ```
    """
    self._add_processor(CommonLiveTAGSeqRespEnricher(kwargs))
    return self
  
  def common_live_tag_seqv2_resp(self, **kwargs):
    """
    CommonLiveTAGSeqV2RespEnricher
    ------
    获取 user live-streaming tag sequence,支持基于每天绝对时间过滤

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度

    `cur_time_threhold`: [int] 默认为0，表示不按照绝对时间过滤；取值大于0时，取绝对时间附近cur_time_threhold长度的窗口

    `topk`: [int] 选取高频cluster的topk
    
    示例
    ------
    ``` python
    common_live_tag_seqv2_resp(
     colossus_resp_attr='colossus_xxx',
    )

    ```
    """
    self._add_processor(CommonLiveTAGSeqV2RespEnricher(kwargs))
    return self
  
  def common_live_rfm_seq_resp(self, **kwargs):
    """
    CommonLiveRFMSeqRespEnricher
    ------
    获取 user live-streaming rfm sequence

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表, V4

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_rfm_seq_resp(
     colossus_resp_attr='colossus_xxx',
    )

    ```
    """
    self._add_processor(CommonLiveRFMSeqRespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_rank_rfm_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorRankRfmRespEnricher
    ------
    获取 user-author 的 rank 统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_colossus_gift_author_rank_rfm_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",) 

    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorRankRfmRespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_rank_rfm_v2_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorRankRfmV2RespEnricher
    ------
    获取 user-author 的 rank v2 统计特征版本

    参数
    ------
    `colossus_resp_attr`: [string] colossus 列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_colossus_gift_author_rank_rfm_v2_resp(
     colossus_resp_attr='colossus_output_2',
     author_id_attr="aId",) 

    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorRankRfmV2RespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_feature_v4_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureV4RespEnricher
    ------
    获取 user-author 的统计特征v4版本中为异构RFM

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_colossus_gift_author_feature_v4_resp(
       colossus_photos_attr="colossus_photos",
       filter_time_len=0
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureV4RespEnricher(kwargs))
    return self


  def common_live_colossus_gift_author_feature_v5_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureV5RespEnricher
    ------

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    `author_id_attr` : [string] pid对应的aid attr name

    `filter_time_len` : [int] 过滤的时间长度
    示例
    ------
    ``` python
    common_live_colossus_gift_author_feature_v5_resp(
       colossus_photos_attr="colossus_photos",
       author_id_attr="author_id",
       filter_time_len=0
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureV5RespEnricher(kwargs))
    return self

  def common_live_colossus_gift_author_feature_v6_resp(self, **kwargs):
    """
    CommonLiveColossusGiftAuthorFeatureV6RespEnricher
    ------
    获取 user 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards` : [int]

    `max_pids_per_request` : [int]
    示例
    ------
    ``` python
    .common_live_colossus_gift_author_feature_v6_resp(
      colossus_resp_attr="colossus_resp",
      shards=4,
      filter_future_attr=False
    )
    ```
    """
    self._add_processor(CommonLiveColossusGiftAuthorFeatureV6RespEnricher(kwargs))
    return self

  def colossus_user_stats_feature_resp(self, **kwargs):
    """
    ColossusUserStatsFeatureRespEnricher
    ------
    获取 user 的统计特征。

    只适用于 grpc_colossusSimV2，必须在 gsu_retriever_with_colossus_resp_v2 或
    gsu_common_colossus_resp_retriever 之后使用。

    参数
    ------

    `colossus_duration_attr`: [string] colossus 中的 duration，单位为 s

    `colossus_play_attr`: [string] colossus 中的 play time，单位为 s

    `colossus_label_attr`: [string] colossus 中的 label

    `colossus_channel_attr`: [string] colossus 中的 channel

    `colossus_time_attr`: [string] colossus 中的 timestamp

    `filter_older_actions_seconds`: [int] 只统计距离请求时间多少秒的行为，单位为 s

    `filter_recent_actions_seconds`: [int] 不统计距离请求时间多少秒内的行为，单位为 s

    `max_action_cnt_after_filter`: [int] 统计的最大行为数

    示例
    ------
    ``` python
    .colossus_user_stats_feature_resp(
      item_list_from_attr="colossus_pid",
      colossus_duration_attr="colossus_duration",
      colossus_play_attr="colossus_play",
      colossus_label_attr="colossus_label",
      colossus_channel_attr="colossus_channel"
      colossus_time_attr="colossus_time"
    )
    ```
    """
    self._add_processor(ColossusUserStatsFeatureRespEnricher(kwargs))
    return self
  
  def colossus_user_cate_stats_feature_resp(self, **kwargs):
    """
    ColossusUserCateStatsFeatureRespEnricher
    ------
    分类获取 user 的统计特征。

    只适用于 grpc_colossusSimV2，必须在 gsu_retriever_with_colossus_resp_v2 或
    gsu_common_colossus_resp_retriever 之后使用。

    参数
    ------

    `colossus_duration_attr`: [string] colossus 中的 duration，单位为 s

    `colossus_play_attr`: [string] colossus 中的 play time，单位为 s

    `colossus_label_attr`: [string] colossus 中的 label

    `colossus_channel_attr`: [string] colossus 中的 channel

    `colossus_time_attr`: [string] colossus 中的 timestamp

    示例
    ------
    ``` python
    .colossus_user_cate_stats_feature_resp(
      item_list_from_attr="colossus_pid",
      colossus_duration_attr="colossus_duration",
      colossus_play_attr="colossus_play",
      colossus_label_attr="colossus_label",
      colossus_channel_attr="colossus_channel"
      colossus_time_attr="colossus_time"
    )
    ```
    """
    self._add_processor(ColossusUserCateStatsFeatureRespEnricher(kwargs))
    return self
  
  def colossus_user_timely_feature_resp(self, **kwargs):
    """
    CommonUserTimelyFeatureEnricher
    ------
    获取 user 侧特征

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `lasted_num`:[int] 取最近N个
    `lasted_play_over_threshold`:[int] 取最近N个观看时长大于等于该阈值的直播
    `lasted_play_less_threshold`:[int] 取最近N个观看时长小于该阈值的直播
    `filter_channel`: [boolean] 是否过滤非单列channel
    `filter_e_shop`: [boolean] 是否过滤电商直播
    `use_private_filter`:[boolean] 是否自定义过滤时间
    `private_filter_timestamp`:[string] 自定义过滤时间字段

    示例
    ------
    ``` python
    .colossus_user_timely_feature_resp(
      colossus_resp_attr="colossus_resp",
    )
    ```
    """
    self._add_processor(CommonUserTimelyFeatureEnricher(kwargs))
    return self

  def colossus_user_follow_seq_resp(self, **kwargs):
    """
    CommonLiveUserFollowSeqEnricher
    ------
    获取 user 侧特征

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `lasted_num`:[int] 取最近N个
    `filter_channel`: [boolean] 是否过滤非单列channel
    `filter_e_shop`: [boolean] 是否过滤电商直播
    `use_filter_time`: [boolean] 是否指定自定义过滤时间(默认无限制)
    `filter_time`: [commonAttr] 自定义过滤时间

    示例
    ------
    ``` python
    .colossus_user_follow_seq_resp(
      colossus_resp_attr="colossus_resp",
      use_filter_time=False,
      filter_time=""
    )
    ```
    """
    self._add_processor(CommonLiveUserFollowSeqEnricher(kwargs))
    return self
  
  def colossus_user_eff_click_seq_resp(self, **kwargs):
    """
    CommonLiveUserEffectiveClickSeqEnricher
    ------
    获取 user 侧有效点击序列特征

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `lasted_num`:[int] 取最近N个
    `filter_channel`: [boolean] 是否过滤非单列channel
    `filter_e_shop`: [boolean] 是否过滤电商直播
    `use_filter_time`: [boolean] 是否指定自定义过滤时间(默认无限制)
    `filter_time`: [commonAttr] 自定义过滤时间

    示例
    ------
    ``` python
    .colossus_user_eff_click_seq_resp(
      colossus_resp_attr="colossus_resp",
      use_filter_time=False,
      filter_time=""
    )
    ```
    """
    self._add_processor(CommonLiveUserEffectiveClickSeqEnricher(kwargs))
    return self
  
  def colossus_user_mix_follow_seq_resp(self, **kwargs):
    """
    CommonLiveUserMixFollowSeqEnricher
    ------
    获取关注与隐式关注的author列表

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `pick_num`:[int] 取最近N个观看记录
    `filter_channel`: [boolean] 是否过滤非单列channel
    `filter_e_shop`: [boolean] 是否过滤电商直播
    `use_filter_time`: [boolean] 是否指定自定义过滤时间(默认7天)
    `filter_time`: [commonAttr] 自定义过滤时间
    

    示例
    ------
    ``` python
    .colossus_user_mix_follow_seq_resp(
      colossus_resp_attr="colossus_resp",
      pick_num=20,
      filter_channel=False,
      filter_e_shop=True,
      use_filter_time=False,
      filter_time=""
    )
    ```
    """
    self._add_processor(CommonLiveUserMixFollowSeqEnricher(kwargs))
    return self

  
  def colossus_unit_liveid_sideinfo_resp(self, **kwargs):
    """
    ColossusSideinfoFromLiveidEnricher
    ------
    ------
    根据Item侧的liveid获取sideinfo，并填充到item侧，一般用于检索sim29检索出的liveid序列

    (基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp
    `live_id_list`[List[int]] itemAttr 直播间id序列
    `begin_time`: [string] CommonAttr 开始时间(秒级时间戳),非必填
    `end_time`: [string] CommonAttr 结束时间(秒级时间戳),非必填
    

    示例
    ------
    ``` python
    .colossus_unit_liveid_sideinfo_resp(
      colossus_resp_attr="colossus_resp",
      live_id_list="unit_item_live_id_list"
    )
    ```
    """
    self._add_processor(ColossusUnitLiveidSideinfoEnricher(kwargs))
    return self

  def common_reco_colossus_user_feature_resp(self, **kwargs):
    """
    CommonRecoColossusUserFeatureRespEnricher
    ------
    获取 user 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    示例
    ------
    ``` python
    .common_reco_colossus_user_feature_resp(
      colossus_resp_attr="colossus_resp",
      filter_future_attr=False
    )
    ```
    """
    self._add_processor(CommonRecoColossusUserFeatureRespEnricher(kwargs))
    return self

  def common_reco_colossus_user_feature_resp(self, **kwargs):
    """
    CommonRecoColossusUserFeatureRespEnricher
    ------
    获取 user 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    示例
    ------
    ``` python
    .common_reco_colossus_user_feature_resp(
      colossus_resp_attr="colossus_resp",
      filter_future_attr=False
    )
    ```
    """
    self._add_processor(CommonRecoColossusUserFeatureRespEnricher(kwargs))
    return self

  def common_reco_colossus_user_feature_v2_resp(self, **kwargs):
    """
    CommonRecoColossusUserFeatureV2RespEnricher
    ------
    获取 user 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `colossus_aid_list_attr`: [string]

    `sorted_item_idx_attr`: [string]

    `top_n`: [int]

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `output_aid_list_attr`: [string] 返回 top_n aid

    示例
    ------
    ``` python
    .common_reco_colossus_user_feature_v2_resp(
      colossus_resp_attr="colossus_resp",
      colossus_aid_list_attr="colossus_aid_list",
      sorted_item_idx_attr="sorted_item_index",
      top_n=50,
      filter_future_attr=False
    )
    ```
    """
    self._add_processor(CommonRecoColossusUserFeatureV2RespEnricher(kwargs))
    return self

  def common_reco_colossus_user_feature_v3_resp(self, **kwargs):
    """
    CommonRecoColossusUserFeatureV3RespEnricher
    ------
    获取 user 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards` : [int]

    `max_pids_per_request` : [int]
    示例
    ------
    ``` python
    .common_reco_colossus_user_feature_v3_resp(
      colossus_resp_attr="colossus_resp",
      shards=4,
      filter_future_attr=False
    )
    ```
    """
    self._add_processor(CommonRecoColossusUserFeatureV3RespEnricher(kwargs))


  def common_live_colossus_user_consum_feature_enricher(self, **kwargs):
    """
    CommonLiveColossusUserConsumFeatureEnricher
    ------
    获取 user 直播消费标志特征(基于 ItemV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 直播列表
    `week_ctr_threshold`: [string] 
    `month_ctr_threshold`: [string]
    `week_eff_click_threshold`: [int]
    `month_eff_click_threshold`: [int]

    示例
    ------
    ``` python
    .common_live_colossus_user_consum_feature_enricher(
      colossus_resp_attr="live_colossus_output",
      time_ms_common_attr="_REQ_TIME_",
      week_ctr_threshold = "0.1",
      month_ctr_threshold = "0.1",
      week_eff_click_threshold = 10,
      month_eff_click_threshold = 30,
    )
    ```
    """
    self._add_processor(CommonLiveColossusUserConsumFeatureEnricher(kwargs))
    return self
  

  def common_video_colossus_user_consum_feature_enricher(self, **kwargs):
    """
    CommonVideoColossusUserConsumFeatureEnricher
    ------
    获取 user 短视频消费标志特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表
    `week_ctr_threshold`: [string] 
    `month_ctr_threshold`: [string]
    `week_eff_click_threshold`: [int]
    `month_eff_click_threshold`: [int]

    示例
    ------
    ``` python
    .common_video_colossus_user_consum_feature_enricher(
      colossus_photos_attr="video_colossus_output",
      time_ms_common_attr="_REQ_TIME_",
      week_ctr_threshold = "0.3",
      month_ctr_threshold = "0.3",
      week_eff_click_threshold = 30,
      month_eff_click_threshold = 200,
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserConsumFeatureEnricher(kwargs))
    return self
  

  def colossus_user_author_feature_resp(self, **kwargs):
    """
    ColossusUserAuthorFeatureRespEnricher
    ------
    获取 user 与 author 的交叉统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `item_author_id_attr`: [string] item的取author_id字段名

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .colossus_user_author_feature_resp(
      colossus_resp_attr="colossus_resp",
      filter_future_attr=False,
      item_author_id_attr="author_id"
    )
    ```
    """
    self._add_processor(ColossusUserAuthorFeatureRespEnricher(kwargs))
    return self

  def colossus_ug_gsu_with_cluster(self, **kwargs):
    """
    ColossusUgGsuWithClusterEnricher
    ------
    获取用户站内行为序列对应的cluster, 与投放的广告素材cluster进行匹配

    参数
    ------
    `photo_id_attr`: [string] 序列photo id common字段名

    `timestamp_attr`: [string] 序列timestamp common字段名

    `author_id_attr`: [string] 序列author id common字段名

    `tag_id_attr`: [string] 序列tag id common字段名

    `playtime_id_attr`: [string] 序列pid playtime common字段名

    `duration_attr`: [string] 序列duration_attr common字段名
    
    `item_pid_attr`: [string] item取pid字段名

    `target_cluster_attr` : [string] item取cluster id字段名

    示例
    ------
    ``` python
    .colossus_ug_gsu_with_cluster(
      photo_id_attr="video_photo_id_list",
      timestamp_attr="timestamp_list",
      author_id_attr="author_id_list",
      tag_id_attr="tag_id_list",
      playtime_id_attr="playtime_id_list",
      duration_attr="duration_list",
      item_pid_attr="photo_id",
      output_cluster_attr_="photo_cluster_id"
    )
    ```
    """
    self._add_processor(ColossusUgGsuWithClusterEnricher(kwargs))
    return self

  def dual_live_sort_item_list_enricher(self, **kwargs):
    """
    DualLiveSortItemListAttrEnricher
    ------
    dualsim item 侧特征排序

    参数
    ------
    `item_dual_uid`: [string]  dual uid

    `item_dual_ts`: [string] dual uid ts

    `item_dual_weight`: [string] dual uid weight

    `weight_thresh`: [int] weight筛选门槛

    `weight_bucket`: [int] weight 分桶

    `diff_bucket`: [int] 时间差分桶

    `fplay_uid_list` : [string] dual uid list

    `fplay_time_list` : [string] dual uid time list

    `fplay_diff_list` : [string] dual uid timediff list

    `fplay_playing_list` : [string] dual uid playing list

    示例
    ------
    ``` python
    .dual_live_sort_item_list_enricher(
      item_dual_uid="play_uid_list",
      item_dual_ts="play_ts_list",
      item_dual_weight="play_time_list",
      limit=50,
      weight_thresh=10,
      weight_bucket=5,
      diff_bucket=5,
      fplay_uid_list="fplay_uid_list",
      fplay_time_list="fplay_time_list",
      fplay_diff_list="fplay_diff_list",
      fplay_playing_list="fplay_playing_list",
    )
    ```
    """
    self._add_processor(DualLiveSortItemListAttrEnricher(kwargs))
    return self

  def live_colossus_user_short_term_attr_enricher(self, **kwargs):
    """
    LiveColossusUserShortTermAttrEnricher
    ------
    获取 user 的一天内短期行为信息（直播&短视频）

    参数
    ------
    `live_colossus_resp_attr`: [string] colossus 直播列表
    `video_colossus_resp_attr`: [string] colossus 视频列表
    `day_live_num_limit`: [int] 当天直播统计展现上限
    `day_video_num_limit`: [int] 当天视频统计展现上限
    `live_list_len`: [int] 直播 list 特征长度
    `video_list_len`: [int] 视频 list 特征长度

    示例
    ------
    ``` python
    .live_colossus_user_short_term_attr_enricher(
      live_colossus_resp_attr='colossus_output_2',
      video_colossus_resp_attr='video_colossus_output',
      live_list_len=10,
      video_list_len=50,
      day_live_num_limit=100,
      day_video_num_limit=500,
    )
    ```
    """
    self._add_processor(LiveColossusUserShortTermAttrEnricher(kwargs))
    return self

  def common_live_colossus_liveid_feature_resp_enricher(self, **kwargs):
    """
    CommonLiveColossusLiveidFeatureRespEnricher
    ------
    获取 user 的统计特征(基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    示例
    ------
    ``` python
    .common_live_colossus_liveid_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonLiveColossusLiveidFeatureRespEnricher(kwargs))
    return self

  def common_ad_live_colossus_liveid_feature_resp_enricher(self, **kwargs):
    """
    CommonAdLiveColossusLiveidFeatureRespEnricher
    ------
    获取 user 的统计特征(基于 grpc_colossusSimAdLiveItem)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    示例
    ------
    ``` python
    .common_live_colossus_liveid_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonAdLiveColossusLiveidFeatureRespEnricher(kwargs))
    return self

  def common_reco_colossus_lite_feature(self, **kwargs):
    """
    CommonLiveColossusLiteFeatureRespEnricher
    ------
    获取 lite 的行为序列(基于grpc_colossusLiveAuthorItem)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp

    示例
    ------
    ``` python
    .common_reco_colossus_lite_feature(
      colossus_resp_attr="colossus_resp",
    )
    ```
    """
    self._add_processor(ColossusLiteFeatureEnricher(kwargs))
    return self
 
  def common_reco_colossus_session_feature(self, **kwargs):
    """
    CommonLiveColossusLiveidFeatureRespEnricher
    ------
    获取 user 的统计特征(基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp

    `session_min` : [int] session 长度定义

    示例
    ------
    ``` python
    .common_reco_colossus_session_feature(
      colossus_resp_attr="colossus_resp",
      session_min=360
    )
    ```
    """
    self._add_processor(ColossusSessionFeatureEnricher(kwargs))
    return self
    
  def live_revenue_colossus_author_feature_v5_resp(self, **kwargs):
    """
    LiveRevenueColossusAuthorFeatureV5RespEnricher
    ------
    获取 user 的长期序列特征(基于colossusV5)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp

    示例
    ------
    ``` python
    .live_revenue_colossus_author_feature_v5_resp(
      colossus_resp_attr="colossus_resp",
    )
    ```
    """
    self._add_processor(LiveRevenueColossusAuthorFeatureV5RespEnricher(kwargs))
    return self

  def live_revenue_colossus_author_feature_dislike_resp(self, **kwargs):
      """
      LiveRevenueColossusAuthorFeatureDislikeRespEnricher
      ------
      获取 user 的负反馈序列特征(基于colossusV4)

      参数
      ------
      `colossus_resp_attr`: [string] colossus resp
      `u_long_reward_author_id_attr`: [string] u_long_reward_author_id_attr

      示例
      ------
      ``` python
      .live_revenue_colossus_author_feature_dislike_resp(
        colossus_resp_attr="colossus_resp",
        u_long_reward_author_id_attr="u_long_reward_author_id_attr"
      )
      ```
      """
      self._add_processor(LiveRevenueColossusAuthorFeatureDislikeRespEnricher(kwargs))
      return self

  def common_ad_live_colossus_pxs_author_feature_resp_enricher(self, **kwargs):
    """
    CommonAdLiveColossusPxsAuthorFeatureRespEnricher
    ------
    获取 author 的行为统计特征(基于 grpc_colossusSimAdLiveItem)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    示例
    ------
    ``` python
    .common_live_colossus_pxs_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonAdLiveColossusPxsAuthorFeatureRespEnricher(kwargs))
    return self

  def common_video_colossus_user_living_seq_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserLivingSeqRespEnricher
    ------
    获取 user 的 Living 观看序列特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_living_seq_resp_enricher(
      colossus_photos_attr="video_colossus_output",
      time_ms_common_attr="_REQ_TIME_",
      u_latest_living_seq_len=50,
      use_pb_format = 1,
      filter_time_len = 60, 
    ) \
    ```
    """
    self._add_processor(CommonVideoColossusUserLivingSeqRespEnricher(kwargs))
    return self
  
  def common_video_colossus_user_living_video_click_seq_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserLivingVideoClickSeqRespEnricher
    ------
    获取 user 的 Living 短视频点击直播序列特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_living_video_click_seq_resp_enricher(
      colossus_photos_attr="video_colossus_output",
      time_ms_common_attr="_REQ_TIME_",
      living_video_click_pid_list_attr="uStandarduStandardLivingPhotoClickIDSeqPhotoIDList",
      living_video_click_ts_list_attr="uStandarduStandardLivingPhotoClickIDSeqTimeList",
      u_latest_living_video_click_seq_len=50, # 必须50 再长没有
      use_pb_format = 1, # 必须为1 0 没实现
      filter_time_len = 60, 
    ) \
    ```
    """
    self._add_processor(CommonVideoColossusUserLivingVideoClickSeqRespEnricher(kwargs))
    return self

  def common_video_colossus_user_author_feature_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureRespEnricher
    ------
    获取 user 的短视频观看统计特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表
    
    `use_pb_format`: [int] (默认1) 是否使用protobuf格式封装colossus取回的数据, 详见[Colossus](https://dragonfly.corp.kuaishou.com/#/api/gsu?id=colossus)

    `gen_lasted_video`: [Boolean] 是否生成最近50个video的统计信息

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_resp_enricher(
      colossus_photos_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureRespEnricher(kwargs))
    return self

  def common_video_colossus_user_author_feature_v6_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV6RespEnricher
    ------
    获取 user 的短视频观看统计特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表
    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v6_resp_enricher(
      colossus_photos_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV6RespEnricher(kwargs))
    return self


  def common_video_colossus_user_author_feature_v7_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV7RespEnricher
    ------
    获取 同 hetu tag 的短视频序列（有效播放）

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v7_resp_enricher(
      colossus_photos_attr="colossus_resp",
      author_hetu_tag_level1_attr = "xx",
      author_hetu_tag_level1_attr = "yy",
      author_hetu_tag_level1_attr = "zz",
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV7RespEnricher(kwargs))
    return self


  def common_live_colossus_hetu_search_resp_enricher(self, **kwargs):
    """
    CommonLiveColossusHetuSearchRespEnricher
    ------
    获取 同 live hetu level 3 的 user 侧直播观看历史序列特征(基于 colossus V4)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表
    示例
    ------
    ``` python
    .common_live_colossus_hetu_search_resp_enricher(
      colossus_photos_attr = "colossus_resp",
      filter_time = 10,
      long_view_threshold = 60,
      live_hetu_level3_seq_length = 50,
    )
    ```
    """
    self._add_processor(CommonLiveHetuHardSearchRespEnricher(kwargs))
    return self


  def common_video_colossus_user_author_feature_v2_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV2RespEnricher
    ------
    获取 user 的短视频观看序列特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v2_resp_enricher(
      colossus_photos_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV2RespEnricher(kwargs))
    return self

  def common_video_colossus_user_author_feature_v3_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV3RespEnricher
    ------
    获取 user 的短视频RFM Seq特征(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v3_resp_enricher(
      colossus_photos_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV3RespEnricher(kwargs))
    return self
  

  def user_interest_clock_enricher(self, **kwargs):
    """
    UserInterestClockEnricher
    ------
    获取 user 和 ua 侧的 兴趣时钟特征

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .user_interest_clock_enricher(
      colossus_photos_attr="video_colossus_output",
      time_ms_common_attr="_REQ_TIME_",
      use_pb_format = 1,
      author_id_attr="aId",
      filter_time_len = 0
    )
    ```
    """
    self._add_processor(UserInterestClockEnricher(kwargs))
    return self

  def common_video_colossus_user_author_feature_v4_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV4RespEnricher
    ------
    获取 user 在当前时刻附近一小时的历史高频兴趣tag(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v4_resp_enricher(
      colossus_photos_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV4RespEnricher(kwargs))
    return self
  
  def common_video_colossus_user_author_feature_v5_resp_enricher(self, **kwargs):
    """
    CommonVideoColossusUserAuthorFeatureV5RespEnricher
    ------
    获取 user 的序列（目前主要为长播序列）。 use_pb_format 必须为 1

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .common_video_colossus_user_author_feature_v5_resp_enricher(
      colossus_photos_attr="colossus_resp",
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserAuthorFeatureV5RespEnricher(kwargs))
    return self

  def user_video_interest_cluster_feature_enricher(self, **kwargs):
    """
    UserVideoInterestClusterFeatureEnricher
    ------
    获取 user 在历史高频兴趣簇序列，并引入行为side info特征 (基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表

    示例
    ------
    ``` python
    .user_video_interest_cluster_feature_enricher(
      colossus_photos_attr="colossus_resp",
    )
    ```
    """
    self._add_processor(UserVideoInterestClusterFeatureEnricher(kwargs))
    return self

  def common_video_colossus_user_interests_sideinfo_enricher(self, **kwargs):
    """
    CommonVideoColossusUserInterestsSideInfoEnricher
    ------
    获取 user 的短视频sidinfo侧兴趣(基于 SimItemV2)

    参数
    ------
    `colossus_photos_attr`: [string] colossus 视频列表
    
    `use_pb_format`: [bool] (默认True) 是否使用protobuf格式封装colossus取回的数据, 详见[Colossus](https://dragonfly.corp.kuaishou.com/#/api/gsu?id=colossus)

    `gen_lasted_video`: [Boolean]  (默认False) 是否生成最近50个video的统计信息

    `gen_lasted_video_sideinfo`: [Boolean] (默认False) 是否生成最近50个video的sideinfo信息

    示例
    ------
    ``` python
    .common_video_colossus_user_interests_sideinfo_enricher(
      colossus_photos_attr="colossus_resp",
      gen_lasted_video=True
    )
    ```
    """
    self._add_processor(CommonVideoColossusUserInterestsSideInfoEnricher(kwargs))
    return self

  def common_live_colossus_author_feature_resp_light_enricher(self, **kwargs):
    """
    CommonLiveColossusAuthorFeatureLightRespEnricher
    ------
    目前仅用于Infer服务, 需要获取RFM特征请使用 `CommonLiveColossusAuthorFeatureRespEnricher`
    获取 user 的统计特征 (基于colossusV4)
    
    此版本为 CommonLiveColossusAuthorFeatureRespEnricher 的Light版
    
    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表
    
    `enable_add_fix_ua_watch_attr`: [int] (默认0) 部分特征的计算方式
    
    `play_time_threshold` : [int] 确定是否计入播放次数的阈值

    缺陷
    ------
    缺陷表示实现的逻辑不符合变量的定义，不代表他没有效果，请自行判断是否使用
    - 不fix的版本计算day将时间戳用作加法, 计算天数有误差
    - fix的attr使用play_time_threshold作为阈值判断是否为算有效播放次数和播放天数, 但是在avg_day_watch_time_fix计算的时候不是有效播放天数的时间也会计入
    
    示例
    ------
    ``` python
    .common_live_colossus_author_feature_resp_light_enricher(
      colossus_resp_attr="colossus_resp",
    )
    ```
    """
    
    self._add_processor(CommonLiveColossusAuthorFeatureLightRespEnricher(kwargs))
    return self
  
  def common_live_colossus_author_feature_resp_enricher(self, **kwargs):
    """
    CommonLiveColossusAuthorFeatureRespEnricher
    ------
    获取 user 的统计特征(基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    `enable_add_fix_ua_watch_attr` : [int] 增加一组特征

    `play_time_threshold` : [int] 确定是否计入播放次数的阈值

    `utc_diff` : [int] 时区的偏移, 中国为8, 不填为0

    缺陷
    ------
    缺陷表示实现的逻辑不符合变量的定义，不代表他没有效果，请自行判断是否使用
    - 不fix的版本计算day将时间戳用作加法, 计算天数有误差，计算播放次数没有阈值，可能会导致曝光过的样本都算作播放次数
    - fix的attrs使用play_time_threshold作为阈值判断是否为算有效播放次数和播放天数, 但是在avg_day_watch_time_fix, avg_cnt_watch_time_fix计算的时候不是有效播放天数/次数的时间也会计入
    
    示例
    ------
    ``` python
    .common_live_colossus_author_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonLiveColossusAuthorFeatureRespEnricher(kwargs))
    return self

  def common_reco_colossus_multi_session_feature(self, **kwargs):
    """
    CommonLiveColossusLiveidFeatureRespEnricher
    ------
    获取 user 的统计特征(基于colossusV4)

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp

    `multi_session_min` : [intlist] session 长度定义
    
    `only_hot` : [bool] 是否只取精选和极速发现页

    示例
    ------
    ``` python
    .common_reco_colossus_multi_session_feature(
      colossus_resp_attr="colossus_resp",
      multi_session_min=[360]
    )
    ```
    """
    self._add_processor(ColossusMultiSessionFeatureEnricher(kwargs))
    return self
  
  def photo_gsu_with_live_author_list(self, **kwargs):
    """
    PhotolivePhotoColossusLiveaidSearchPidEnricher
    根据 clickLiveAuthor序列，使用aid对photo colossus进行hard search，返回photo序列。
    ------
    上游 colossus 必须配置 parse_pb_=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `uClickLiveAuthorListHot_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListFollow_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListNear_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorList_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListSlHot_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListSlFollow_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListSlNear_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListJXLiveTabSingle_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListBlHot_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListBlNear_attr` : [string] living click list 同名 common attr
    
    `uClickLiveAuthorListBlFollow_attr` : [string] living click list 同名 common attr

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    示例
    ------
    ``` python
    .photo_gsu_with_live_author_list(
        colossus_resp_attr='photo_colossus_output',
        limit_num=50,
        uClickLiveAuthorListHot_attr="uClickLiveAuthorListHot"
        uClickLiveAuthorListFollow_attr="uClickLiveAuthorListFollow"
        uClickLiveAuthorListNear_attr="uClickLiveAuthorListNear"
        uClickLiveAuthorList_attr="uClickLiveAuthorList"
        uClickLiveAuthorListSlHot_attr="uClickLiveAuthorListSlHot"
        uClickLiveAuthorListSlFollow_attr="uClickLiveAuthorListSlFollow"
        uClickLiveAuthorListSlNear_attr="uClickLiveAuthorListSlNear"
        uClickLiveAuthorListJXLiveTabSingle_attr="uClickLiveAuthorListJXLiveTabSingle"
        uClickLiveAuthorListBlHot_attr="uClickLiveAuthorListBlHot"
        uClickLiveAuthorListBlNear_attr="uClickLiveAuthorListBlNear"
        uClickLiveAuthorListBlFollow_attr="uClickLiveAuthorListBlFollow"
        output_sign_attr="photo_gsu_signs_by_livingAid_hardSearch",
        output_slot_attr="photo_gsu_slots_by_livingAid_hardSearch",
        cluster_id_service_type="embedding_server",
        #kess_service='kws-kuaishou-full-rank-embedding-mmu-hetu-cluster-id-long',
        kess_service='mmu_hetu_cluster_id_query_server_nearby_follow_online',
        shards=4,
        timeout_ms=10 * 5000,
        slots_id=[41000, 41001, 41002, 41003, 41004, 41005, 41006, 41007, 41008, 41009, 41010, 41011],
        mio_slots_id=[41000, 41001, 41002, 41003, 41004, 41005, 41006, 41007, 41008, 41009, 41010, 41011],
    )
    ```
    """
    self._add_processor(PhotolivePhotoColossusLiveaidSearchPidEnricher(kwargs))
    return self

  def common_live_colossus_author_merge_feature_enricher(self, **kwargs):
    """
    CommonLiveColossusAuthorMergeFeatureEnricher
    ------
    按 author 聚合, 获取 user 的直播观看序列

    参数
    ------
    `live_colossus_attr`: [string] 必填项, colossus 直播列表, 需为 pb 格式的 colossus
    
    `filter_time_len`: [int] 默认值 0

    `enable_filter_e_shop`: [bool] 默认值 true

    `enable_filter_outside_item`: [bool] 默认值 true

    `enable_filter_long_view_item`: [bool] 默认值 true

    `long_view_threshold`: [int] 默认值 60s

    `enable_time_seq`: [bool] 默认值 false, 生成 u_recent_live_recent_clk_ts_list_attr

    `latest_n_live`: 遍历最近的 n 个 item, 默认值 50

    `latest_valid_n_live`: 过滤后, 遍历符合规则最近的 n 个 item, 默认值 50

    示例
    ------
    ``` python
    .common_live_colossus_author_merge_feature_enricher(
      live_colossus_attr="colossus_resp",
      u_recent_live_author_list_attr="u_recent_live_author_list",
      u_recent_live_reward_list_attr="u_recent_live_author_reward_list",
      u_recent_live_cluster_list_attr="u_recent_live_author_cluster_list",
      u_recent_live_playtime_list_attr="u_recent_live_author_playtime_list",
      u_recent_live_position_list_attr="u_recent_live_author_position_list"
      u_recent_live_consume_reward_list_attr="u_recent_live_author_consume_reward_list"
      u_recent_live_mix_reward_list_attr="u_recent_live_author_mix_reward_list",
      u_recent_live_recent_clk_ts_list_attr="u_recent_live_recent_clk_ts_list"
    )
    ```
    """
    self._add_processor(CommonLiveColossusAuthorMergeFeatureEnricher(kwargs))
    return self

  def common_video_colossus_action_info_enricher(self, **kwargs):
    """
    CommonVideoColossusActionInfoEnricher 
    ------
    统计用户近期观看短视频 action list, 参考 CommonVideoColossusUserAuthorFeatureRespEnricher, 1) 增加短播过滤;2)消费 reward 重定义

    参数
    ------
    `video_colossus_attr`: [string] 必填项, colossus 直播列表, 训练和 infer 目前用不同格式
    
    `filter_time_len`: [int] 默认值 0

    示例
    ------
    ``` python
    .common_video_colossus_action_info_enricher(
      video_colossus_attr="video_colossus_resp",
      u_recent_video_author_list_attr="u_recent_video_author_list",
      u_recent_video_label_list_attr="u_recent_video_label_list",
      u_recent_video_lag_list_attr="u_recent_video_lag_list",
      u_recent_video_pts_list_attr="u_recent_video_pt_list",
      u_recent_video_page_list_attr="u_recent_video_page_list",
      u_recent_video_photo_list_attr="u_recent_video_pid_list",
      u_recent_video_tag_list_attr="u_recent_video_tag_list"
    )
    ```
    """
    self._add_processor(CommonVideoColossusActionInfoEnricher(kwargs))
    return self

  def common_live_colossus_ua_lifelong_feature_enricher(self, **kwargs):
    """
    CommonLiveColossusUaLifelongFeatureEnricher
    ------
    获取 user-author 的直播统计序列

    参数
    ------
    `live_colossus_attr`: [string] 必填项, colossus 直播列表, 需为 pb 格式的 colossus
    
    `filter_time_len`: [int] 默认值 0

    `enable_filter_e_shop`: [bool] 默认值 true

    `enable_filter_outside_item`: [bool] 默认值 true

    `latest_n_live`: [动态参数] 遍历最近的 n 个 item, 默认值 50

    `latest_valid_n_live`: [动态参数] 过滤后, 遍历符合规则最近的 n 个 item, 默认值 50

    `ua_group_num`: [动态参数] 每个用户最多取的 ua 个数, 默认值 10

    `user_author_latest_n_action`: [动态参数] 单个 author 最多取的 行为 个数, 默认值 10


    示例
    ------
    ``` python
    .common_live_colossus_ua_lifelong_feature_enricher(
      live_colossus_attr="colossus_resp",
      u_recent_live_author_list_attr="u_recent_live_author_list",
      u_recent_live_cluster_list_attr="u_recent_live_author_cluster_list",
      u_recent_live_clickcnt_list_attr="u_recent_live_author_clickcnt_list"
      u_recent_live_likecnt_list_attr="u_recent_live_author_likecnt_list"
      u_recent_live_commentcnt_list_attr="u_recent_live_author_commentcnt_list"
      u_recent_live_playtime_list_attr="u_recent_live_author_playtime_list",
      u_user_authors_group_action_list_attr="u_user_authors_group_action_list",
      u_user_author_mask_num_list_attr="u_user_author_mask_num_list"
    )
    ```
    """
    self._add_processor(CommonLiveColossusUaLifelongFeatureEnricher(kwargs))
    return self
  
  def common_p2l_reco_colossus_user_feature_enricher(self, **kwargs):
    """
    CommonP2lRecoColossusUserFeatureEnricher
    ------
    从 RecoColossus 中获取RFM统计特征

    参数
    ------
    `colossus_resp_attr`: [string] 必填项, reco colossus的返回值

    `print_items`: [Boolean] 非必填项, 是否打印RFM计算过程的输出

    `u_minute_play_cnt_attr`: [string] 非必填项, 统计分钟级播放次数list
    `u_minute_play_time_attr`: [string] 非必填项, 统计分钟级播放时长list
    `u_minute_play_playend_cnt_attr`: [string] 非必填项, 统计分钟级播放完次数list
    `u_minute_sum_label_attr`: [string] 非必填项, 统计分钟级label聚合分list
    `u_minute_count_profile_attr`: [string] 非必填项, 统计分钟级profile（进入主页）list
    `u_minute_count_forward_attr`: [string] 非必填项, 统计分钟级转发次数list
    `u_minute_count_like_attr`: [string] 非必填项, 统计分钟级点赞次数list
    `u_minute_count_comment_attr`: [string] 非必填项, 统计分钟级评论次数list
    `u_minute_count_hate_attr`: [string] 非必填项, 统计分钟级不喜欢次数list
    `u_minute_count_follow_attr`: [string] 非必填项, 统计分钟级关注次数list

    `u_hour_play_cnt_attr`: [string] 非必填项, 统计小时级播放次数list
    `u_hour_play_time_attr`: [string] 非必填项, 统计小时级播放时长list
    `u_hour_play_playend_cnt_attr`: [string] 非必填项, 统计小时级播放完次数list
    `u_hour_sum_label_attr`: [string] 非必填项, 统计小时级label聚合分list
    `u_hour_count_profile_attr`: [string] 非必填项, 统计小时级profile（进入主页）list
    `u_hour_count_forward_attr`: [string] 非必填项, 统计小时级转发次数list
    `u_hour_count_like_attr`: [string] 非必填项, 统计小时级点赞次数list
    `u_hour_count_comment_attr`: [string] 非必填项, 统计小时级评论次数list
    `u_hour_count_hate_attr`: [string] 非必填项, 统计小时级不喜欢次数list
    `u_hour_count_follow_attr`: [string] 非必填项, 统计小时级关注次数list
    
    `u_day_play_cnt_attr`: [string] 非必填项, 统计天级播放次数list
    `u_day_play_time_attr`: [string] 非必填项, 统计天级播放时长list
    `u_day_play_playend_cnt_attr`: [string] 非必填项, 统计天级播放完次数list
    `u_day_sum_label_attr`: [string] 非必填项, 统计天级label聚合分list
    `u_day_count_profile_attr`: [string] 非必填项, 统计天级profile（进入主页）list
    `u_day_count_forward_attr`: [string] 非必填项, 统计天级转发次数list
    `u_day_count_like_attr`: [string] 非必填项, 统计天级点赞次数list
    `u_day_count_comment_attr`: [string] 非必填项, 统计天级评论次数list
    `u_day_count_hate_attr`: [string] 非必填项, 统计天级不喜欢次数list
    `u_day_count_follow_attr`: [string] 非必填项, 统计天级关注次数list
    
    示例
    ------
    ``` python
    .common_p2l_reco_colossus_user_feature_enricher(
      colossus_resp_attr='colossus_output',
      print_items=False,
      fliter_time_buffer=60,  # filter误拼写成fliter，线上已经使用所以暂不修改
      name="common_p2l_reco_colossus_user_feature_enricher_C1234C"
    )
    ```
    """
    self._add_processor(CommonP2lRecoColossusUserFeatureEnricher(kwargs))
    return self
  def colossus_user_author_cross_static_resp(self, **kwargs):
    """
    ColossusUserAuthorCrossStaticRespEnricher
    ------
    获取 user 与 author 的交叉统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `item_author_id_attr`: [string] item的取author_id字段名

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .colossus_user_author_feature_resp(
      colossus_resp_attr="colossus_resp",
      filter_future_attr=False,
      item_author_id_attr="author_id"
    )
    ```
    """
    self._add_processor(ColossusUserAuthorCrossStaticRespEnricher(kwargs))
    return self

  def common_p2l_reco_colossus_user_feature_v1_enricher(self, **kwargs):
    """
    CommonP2lRecoColossusUserFeatureV1Enricher
    ------
    从 RecoColossus 中获取RFM统计特征

    参数
    ------
    `colossus_resp_attr`: [string] 必填项, reco colossus的返回值

    `print_items`: [Boolean] 非必填项, 是否打印RFM计算过程的输出

    `u_minute_play_cnt_attr`: [string] 非必填项, 统计分钟级播放次数list
    `u_minute_play_time_attr`: [string] 非必填项, 统计分钟级播放时长list
    `u_minute_play_playend_cnt_attr`: [string] 非必填项, 统计分钟级播放完次数list
    `u_minute_sum_label_attr`: [string] 非必填项, 统计分钟级label聚合分list
    `u_minute_count_profile_attr`: [string] 非必填项, 统计分钟级profile（进入主页）list
    `u_minute_is_effective_play1_attr`: [string] 非必填项
    `u_minute_is_effective_play2_attr`: [string] 非必填项

    `u_hour_play_cnt_attr`: [string] 非必填项, 统计小时级播放次数list
    `u_hour_play_time_attr`: [string] 非必填项, 统计小时级播放时长list
    `u_hour_play_playend_cnt_attr`: [string] 非必填项, 统计小时级播放完次数list
    `u_hour_sum_label_attr`: [string] 非必填项, 统计小时级label聚合分list
    `u_hour_count_profile_attr`: [string] 非必填项, 统计小时级profile（进入主页）list
    
    `u_day_play_cnt_attr`: [string] 非必填项, 统计天级播放次数list
    `u_day_play_time_attr`: [string] 非必填项, 统计天级播放时长list
    `u_day_play_playend_cnt_attr`: [string] 非必填项, 统计天级播放完次数list
    `u_day_sum_label_attr`: [string] 非必填项, 统计天级label聚合分list
    `u_day_count_profile_attr`: [string] 非必填项, 统计天级profile（进入主页）list

    `u_minute_play_time_ratio_attr`: [string] 非必填项
    `u_minute_play_playend_cnt_ratio_attr`: [string] 非必填项
    `u_minute_is_effective_play1_ratio_attr`: [string] 非必填项
    `u_minute_is_effective_play2_ratio_attr`: [string] 非必填项

    示例
    ------
    ``` python
    .common_p2l_reco_colossus_user_feature_v1_enricher(
      colossus_resp_attr='colossus_output',
      print_items=False,
      filter_time_buffer=60,
      name="common_p2l_reco_colossus_user_feature_enricher_DC198P"
    )
    ```
    """
    self._add_processor(CommonP2lRecoColossusUserFeatureV1Enricher(kwargs))
    return self
  
  def colossus_user_tag_cross_static_resp(self, **kwargs):
    """
    ColossusUserTagCrossStaticRespEnricher
    ------
    获取 user 与 tag 的交叉统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `filter_future_attr` : [boolean] 是否只统计时间在 request time 之前的 colossus items

    `item_tag_attr`: [string] item的取tag字段名

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .colossus_user_tag_cross_static_resp(
      colossus_resp_attr="colossus_resp",
      filter_future_attr=False,
      item_tag_attr="tag"
    )
    ```
    """
    self._add_processor(ColossusUserTagCrossStaticRespEnricher(kwargs))
    return self

  def common_p2l_reco_colossus_user_feature_v2_enricher(self, **kwargs):
    """
    CommonP2lRecoColossusUserFeatureV2Enricher
    ------
    从 RecoColossus 中获取RFM统计特征

    参数
    ------
    `colossus_resp_attr`: [string] 必填项, reco colossus的返回值

    `print_items`: [Boolean] 非必填项, 是否打印RFM计算过程的输出

    `u_minute_play_cnt_attr`: [string] 非必填项, 统计分钟级播放次数list
    
    示例
    ------
    ``` python
    .common_p2l_reco_colossus_user_feature_v2_enricher(
      colossus_resp_attr='colossus_output',
      print_items=False,
      fliter_time_buffer=60,  # filter误拼写成fliter，线上已经使用所以暂不修改
      name="common_p2l_reco_colossus_user_feature_v2_enricher_C1234C"
    )
    ```
    """
    self._add_processor(CommonP2lRecoColossusUserFeatureV2Enricher(kwargs))
    return self

  def rfm_with_follow_seq(self, **kwargs):
    """
    CommonLiveRfmWithLiveFollowEnricher, 根据用户关注序列，检索直播colossus
    ------
    上游 colossus 必须配置 parse_pb_=False, 通过用户直播关注主播id list hit colossus

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `id_list_attr`: [string] 关注序列id list

    `limit_num`: [int] 返回数目 attr

    `limit_min_play_time`: [int] 限制序列内的最小播放时长

    `limit_max_play_time`: [int] 限制序列内的最大播放时长

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .rfm_with_follow_seq(
        colossus_resp_attr='live_colossus_output',
        id_list_attr='follow_id_list',
        limit_num=50,
        limit_min_play_time=10,
        limit_max_play_time=59,
        output_sign_attr="living_gsu_signs_ev",
        output_slot_attr="living_gsu_slots_ev",
        slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
        mio_slots_id=[21000, 21001, 21002, 21003, 21004, 21005, 31000, 31001, 31002, 31003, 31004, 31005],
    )
    ```
    """
    self._add_processor(CommonLiveRfmWithLiveFollowEnricher(kwargs))
    return self


