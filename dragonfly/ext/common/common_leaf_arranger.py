#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg, is_number, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class CommonRecoAttrFilter<PERSON>rranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_by_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["attr_name"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["compare_to", "pardon_num", "cancel_num"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class CommonRecoRuleFilter<PERSON>rranger(LeafArranger):
  def __init__(self, config:dict):
    super().__init__(config)
    self._input_common_attrs = set()
    self._input_item_attrs = set()
    self.extract_config_tree(self._config["rule"])
    
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_by_rule"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._input_item_attrs

  def extract_config_tree(self, config):
    attrs = []
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("enable")))
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("compare_to")))
    if config.get("attr_name"):
      self._input_item_attrs.add(config.get("attr_name"))
    if config.get("compare_to_item_attr"):
      self._input_item_attrs.add(config.get("compare_to_item_attr"))
    

    if config.get("filters"):
      for ft in config.get("filters"):
        self.extract_config_tree(ft)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    self._input_common_attrs.update(self.extract_dynamic_params(self._config.get("pardon_num")))
    self._input_common_attrs.update(self.extract_dynamic_params(self._config.get("cancel_num")))
    return self._input_common_attrs

  def check_filter_config(self, config):
    if "join" in config:
      for attr in ["attr_name", "compare_to", "remove_if", "remove_if_attr_missing", "compare_to_item_attr"]:
        check_arg(attr not in config, f" join 参数不应与 {attr} 参数同时配置")

      filters = config.get("filters")
      check_arg(filters, f"规则组合模式下应配置 filters 参数")
      for ft in filters:
        self.check_filter_config(ft)
    else:
      check_arg(("check_reason" in config and config["check_reason"] is True) or ("attr_name" in config), "过滤应用模式下，必须配置 attr_name 或者 check_reason")
      check_arg(not ("compare_to" in config and "compare_to_item_attr" in config), 
                "compare_to 和 compare_to_item_attr 不可以同时出现")
      check_arg(config.get("remove_if_attr_missing", False) or (("compare_to" in config or "compare_to_item_attr" in config) and "remove_if" in config),
                "过滤应用模式下，若没有配置 remove_if_attr_missing, 则必须配置 compare_to 和 remove_if")

  @strict_types
  def _check_config(self) -> None:
    self.check_filter_config(self._config["rule"])

class CommonRecoCommonAttrFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_by_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config["common_attr"])
    attrs.update(self.extract_dynamic_params(self._config.get("pardon_num")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("on_item_attr", "")
    return { attr } if attr else set()


class CommonRecoBrowseSetFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_by_browse_set"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attr = self._config.get("save_filtered_items_to_common_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("check_id_in_attr", "")
    return { attr } if attr else set()

class CommonRecoResultsDeduplicateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "deduplicate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("append_reason_for_top_distinct_items")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("on_item_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_dup_count_to", "append_reason_to", "append_order_to", "append_reason_order_to"]:
      if self._config.get(key, ""):
        attrs.add(self._config.get(key, ""))
    return attrs

class CommonRecoScoreCalcArranger(LeafArranger):
  @classmethod
  def get_type_alias(cls) -> str:
    return "calculate_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for _, param in self._config.get("dynamic_parameter", {}).items():
      attr_list = list(filter(lambda x: x and x.startswith("user/"), [v.strip() for v in param.split(",")]))
      attrs.update({ x[5:] for x in attr_list })
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for _, param in self._config.get("dynamic_parameter", {}).items():
      attr_list = list(filter(lambda x: x and x.startswith("item/"), [v.strip() for v in param.split(",")]))
      attrs.update({ x[5:] for x in attr_list })
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("save_for_debug_request_only", False):
      if self._config.get("save_formula_score", False):
        return { self._config.get("save_formula_score_prefix", "") + v for v in [*self._config["formula"]] }
      elif self._config.get("save_score_to_attr", ""):
        return { self._config.get("save_score_to_attr") }
    return set()

class CommonRecoLuaScoreCalcArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calculate_score_by_lua"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for _, param in self._config.get("global_D", {}).items():
      delimiter = "," if "," in param else "|"
      attr_list = list(filter(lambda x: x and x.startswith("user/") and not is_number(x), [v.strip() for v in param.split(delimiter)]))
      attrs.update({ x[5:] for x in attr_list })
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for _, param in self._config.get("global_D", {}).items():
      delimiter = "," if "," in param else "|"
      attr_list = list(filter(lambda x: x and not x.startswith("user/") and not is_number(x), [v.strip() for v in param.split(delimiter)]))
      attrs.update({ x[5:] if x.startswith("item/") else x for x in attr_list })
    return attrs

class CommonRecoScoreSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sort_by_score"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    score_from_attr = self._config.get("score_from_attr", "")
    if score_from_attr and isinstance(score_from_attr, str):
      ret.add(score_from_attr)
    elif score_from_attr and isinstance(score_from_attr, list):
      ret.update(score_from_attr)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { "_SCORE_" }
  
  @strict_types
  def _check_config(self) -> None:  
    check_arg(isinstance(self._config.get("score_from_attr",""), str) or isinstance(self._config.get("score_from_attr",[]), list),
              "score_from_attr 需为 str 或者 list")

class CommonRecoTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "truncate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["size_limit"]))
    for val in self._config.get("backfill_to", {}).values():
      attrs.update(self.extract_dynamic_params(val))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("backfill_to", {}).keys())

class CommonRecoReasonTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "truncate_by_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("min_survival")))
    for cfg in self._config.get("reason_config", []):
      attrs.update(self.extract_dynamic_params(cfg.get("min_survival")))
    return attrs

class CommonRecoReasonLimitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "limit_by_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("reason_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("min_survival")))
    reason_config = self._config.get("reason_config")
    if isinstance(reason_config, list):
      for cfg in reason_config:
        attrs.update(self.extract_dynamic_params(cfg.get("min_survival")))
        attrs.update(self.extract_dynamic_params(cfg.get("limit")))
        attrs.update(self.extract_dynamic_params(cfg.get("ratio")))
        attrs.update(self.extract_dynamic_params(cfg.get("reason")))
    default_reason_config = self._config.get("default_reason_config", {})
    attrs.update(self.extract_dynamic_params(default_reason_config.get("min_survival")))
    attrs.update(self.extract_dynamic_params(default_reason_config.get("limit")))
    attrs.update(self.extract_dynamic_params(default_reason_config.get("ratio")))
    return attrs

class CommonRecoDiversityRulesArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "diversify_by_rules"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["max_satisfied_pick", "perflog_enabled"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      attrs.add(prev_items_from_attr)
    for r in self._config.get("rules", []):
      for key in ["priority", "window_size", "max_num", "min_num", "enabled", "consider_prev_items"]:
        attrs.update(self.extract_dynamic_params(r.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    for r in self._config.get("rules", []):
      attrs.add(r.get("attr_name"))
      if r.get("consider_prev_items"):
        prev_attrs.add(r.get("attr_name"))
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_items_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

class CommonRecoVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      attrs.add(prev_items_from_attr)
    for key in ["allow_item_deletion", "max_undecay_select", "min_select"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for v in self._config["variant_config"].values():
      if isinstance(v, dict):
        for val in v.values():
          attrs.update(self.extract_dynamic_params(val))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = { k for k, v in self._config["variant_config"].items() if isinstance(v, dict) }
    init_decay_score_from_attr = self._config.get("init_decay_score_from_attr")
    if init_decay_score_from_attr:
      attrs.add(init_decay_score_from_attr)
    prev_items_from_attr = self._config.get("prev_items_from_attr")
    if prev_items_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_items_from_attr) for v in attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attr = self._config.get("save_decay_score_to_attr", "")
    return { attr } if attr else set()

class CommonRecoRotateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rotate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("head")))
    return attrs

class CommonRecoShuffleArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "shuffle"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("weight_attr", "")
    return { attr } if attr else set()

class CommonRecoPartitionArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "partition"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config["target"].keys())

class CommonRecoStickyArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "force_insert"

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("position_from_attr", ""):
      check_arg(not self._config.get("reason"), "在配置了 position_from_attr 的情况下不可同时配置: reason")
      check_arg(not self._config.get("position"), "在配置了 position_from_attr 的情况下不可同时配置: position")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("reason")))
    attrs.update(self.extract_dynamic_params(self._config.get("position")))
    attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("position_from_attr", "")
    return { attr } if attr else set()

class CommonRecoMmrDiversityArranger(LeafArranger):
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmr_lambda")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmr_score_name")))
    return attrs

class CommonRecoIntermixArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "intermix"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("mix_pattern")))
    attrs.update(self.extract_dynamic_params(self._config.get("num_limit")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    mix_on_attr = self._config.get("mix_on_attr")
    return { mix_on_attr } if mix_on_attr else set()

class CommonRecoAttrTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "truncate_by_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("attr_name"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["size_limit"]))
    for q in self._config.get("queues", []):
      attrs.update(self.extract_dynamic_params(q.get("limit")))
      attrs.update(self.extract_dynamic_params(q.get("ratio")))
    return attrs

class CommonRecoPipelineArranger(LeafArranger):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    from ...common_leaf_dsl import LeafFlow
    check_arg(isinstance(self.__sub_flow, LeafFlow), f"sub_flow 类型必须为 LeafFlow。但是取到的类型为: {type(self.__sub_flow)} Tips: 请检查下有没有继承 LeafFLow、链式调用的函数有没有返回 self")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = "merge_common_attrs" not in self._config
    self._auto_detect_merge_item_attrs = "merge_item_attrs" not in self._config

  @strict_types
  def get_type_alias(self) -> str:
    return "arrange_by_sub_flow"

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("expected_partition_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("task_queue_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("merge_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_item_attrs", []))
    attrs.add(self._config.get("group_by"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("merge_item_attrs", []))

  @strict_types
  def _check_config(self) -> None:
    group_by = self._config.get("group_by")
    expected_partition_size = self._config.get("expected_partition_size")
    check_arg(bool(group_by) ^ bool(expected_partition_size), "group_by, expected_partition_size 需且仅需配置一个")
    if group_by:
      check_arg(isinstance(group_by, str), "group_by 需为 string 类型")
    if expected_partition_size:
      check_arg(isinstance(expected_partition_size, (int, str)), "expected_partition_size 需为 正整数或动态参数")
      if isinstance(self._config.get("expected_partition_size"), int):
        check_arg(self._config["expected_partition_size"] > 0, "expected_partition_size 需为 正整数或动态参数")
      else:
        check_arg(self._config["expected_partition_size"].startswith("{{"), "expected_partition_size 需为 正整数或动态参数")
        check_arg(self._config["expected_partition_size"].endswith("}}"), "expected_partition_size 需为 正整数或动态参数")

    for io_attr in ["pass_common_attrs", "pass_item_attrs", "merge_common_attrs", "merge_item_attrs"]:
      check_arg(isinstance(self._config.get(io_attr, []), list), io_attr + " 需为 list 类型")

    for processor in self.__sub_flow._processors:
      check_arg(self.item_table not in processor.output_item_tables,
                f"sub_flow {self.__sub_flow.name} 中不可包含 retriever 类型 processor: {processor.name or processor.get_type_alias()}")

class CommonRecoItemResultsFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_by_item_results"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("remove_if_not_in", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    remove_if_not_in_value = self._config.get("remove_if_not_in")
    check_arg(remove_if_not_in_value and isinstance(remove_if_not_in_value, list),
              "remove_if_not_in 需为非空 list")
    for sub_flow in remove_if_not_in_value:
      check_arg(isinstance(sub_flow, str), "remove_if_not_in 需为字符串 list")

class CommonRecoItemReverseArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reverse_item"


class CommonRecoRandomStableShuffleArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "random_stable_shuffle"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("shuffle_by_reason", False) or self._config.get("shuffle_by_channel", False), "shuffle_by_reason and shuffle_by_channel both empty")
