#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafObserver

class CommonRecoLeafShowObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    if config.get("include_sample_list_user_info", False):
      config["include_sample_list_attr"] = True
      config["sample_list_common_attr_key"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "leaf_show"

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    return self._config.get("include_sample_list_attr", True) and bool(self._config.get("sample_list_common_attr_key"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("extra_common_attrs", []))
    for key in ["sub_biz_name", "action_log_item_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    sample_list_attr = self._config.get("sample_list_common_attr_key", "")
    if sample_list_attr:
      attrs.add(sample_list_attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("attrs", []) + self._config.get("extra_attrs", []))
    for key in ["action_value", "forward_predict_item_from_attr", "use_item_key_from_attr"]:
      val = self._config.get(key)
      if val:
        attrs.add(val)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("enable_leaf_show" in self._config, "缺少 enable_leaf_show 配置")
    check_arg("extra_attrs" not in self._config, "leaf_show 的 extra_attrs 配置项已废弃, 如需以 extra_attrs 形式发送请将 extra_attrs 内容合并至 attrs 中，并增加配置 as_extra_attrs=True")
    check_arg(isinstance(self._config.get("biz_name"), str) or isinstance(self._config.get("reco_biz"), str), "需指定 biz_name 或 reco_biz")
    if not self._config.get("reco_biz"):
      check_arg("action_type" not in self._config, "leaf_show() 在未配置 reco_biz 的情况下不允许配置 action_type")
      check_arg("action_value" not in self._config, "leaf_show() 在未配置 reco_biz 的情况下不允许配置 action_type")
      check_arg("action_log_common_attrs" not in self._config, "leaf_show() 在未配置 reco_biz 的情况下不允许配置 action_type")
      check_arg("action_log_item_type" not in self._config, "leaf_show() 在未配置 reco_biz 的情况下不允许配置 action_type")
    if self._config.get("biz_name"):
      if self._config.get("producer_type","") == "kafka":
        check_arg(self._config.get("kafka_topic"), "producer_type 为 kafka 时，需指定 kafka_topic")
      else:
        check_arg(self._config.get("group") or self._config.get("bt_queue_name"), "需指定 bt_queue_name 或 group")
    if self._config.get("forward_predict_item_from_attr"):
      check_arg(not self._config.get("attrs") and not self._config.get("extra_common_attrs") and \
          not self._config.get("exclude_common_attrs"), "forward_predict_item_from_attr 不可与 attrs/extra_common_attrs/exclude_common_attrs 同时配置")

class CommonRecoDebugInfoObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "log_debug_info"

  @strict_types
  def depend_on_items(self) -> bool:
    if self._config.get("shadow_mode", False):
      return False
    return self._config.get("item_num_limit", 10) > 0 and bool(self._config.get("item_attrs", []))

  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    return self._config.get("print_all_item_attrs", False)

  @strict_types
  def depend_on_all_common_attrs(self) -> bool:
    return self._config.get("print_all_common_attrs", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("shadow_mode", False):
      return set()
    attrs = set(self._config.get("common_attrs", []))
    if self._config.get("to", "") == "file":
      attrs.update(self.extract_dynamic_params(self._config.get("to_file_name")))
    for key in ["respect_sample_logging", "trace_user_ids", "trace_device_ids"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("shadow_mode", False):
      return set()
    return set(self._config.get("item_attrs", []))

class CommonRecoGlobalHolderObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "save_to_global_holder"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config["numerator"]])
    if "denominator" in self._config:
      attrs.add(self._config["denominator"])
    if "group_name" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["group_name"]))
    return attrs

class CommonRecoReasonCountPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog_reason_count"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "log_info_from_attr" in self._config:
      attrs.add(self._config["log_info_from_attr"])
    attrs.update(self.extract_dynamic_params(self._config.get("check_point"), check_format=False))
    return attrs

class CommonRecoDummyObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "do_nothing"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

class CommonRecoBTQueueObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_with_btq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "common_attr" in self._config:
      attrs.add(self._config["common_attr"])
      if "hash_attr" in self._config:
        attrs.add(self._config.get("hash_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attr" in self._config:
      attrs.add(self._config["item_attr"])
      if "hash_attr" in self._config:
        attrs.add(self._config.get("hash_attr"))
    return attrs

class CommonRecoStdoutObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_stdout"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["common_attr"] }

class CommonRecoKafkaObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_with_kafka"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config["common_attr"]])
    attrs.update(self.extract_dynamic_params(self._config.get("kafka_tag")))
    return attrs

class CommonRecoAttrExportObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "export_attr_to_kafka"

  @strict_types
  def _check_config(self) -> None:
    check_arg("kafka_topic" in self._config, f"{self.get_type_alias()} 缺少 kafka_topic 配置")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_attrs"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

class CommonRecoAttrValuePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog_attr_value"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self.input_item_attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("check_point"), check_format=False))
    attrs.update(self._config.get("common_attrs", []))
    return attrs

class CommonRecoCsvObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_csv"

  @strict_types
  def _get_input_attrs(self) -> set:
    return set(self._config.get("attrs", []))

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(filter(lambda x: x in self.preceding_output_item_attrs or \
                                x not in self.preceding_output_common_attrs, self._get_input_attrs()))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(filter(lambda x: x in self.preceding_output_common_attrs or \
                                x not in self.preceding_output_item_attrs, self._get_input_attrs()))

class CommonRecoAttrDiffObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "check_attr_diff"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr_pair in self._config.get("attr_pairs", []):
      ret.add(attr_pair['attr_a'])
      ret.add(attr_pair['attr_b'])
    return ret

class CommonRecoWriteToRedisObserver(LeafObserver):
  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_redis"

  @strict_types
  def _check_config(self) -> None:
    check_arg("key_from_common_attr" not in self._config, "key_from_common_attr 配置项已废弃，请改用 key 配置项！")
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      check_arg(not self._config.get(key, "").startswith("{{"), f"{key} 配置不支持动态参数")

  @strict_types
  def depend_on_items(self) -> bool:
    return any(self.input_item_attrs)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["key_prefix", "expire_second", "key", "value"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      ret.add(self._config.get(key))
    return ret

class CommonRecoAbtestMetricsObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_abtest_metrics"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("metric_name_prefix")))
    attrs.update(extract_attr_names(self._config.get("metrics", []), "name"))
    return attrs

class CommonRecoSleepObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sleep"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["sleep_ms"]))
    return attrs

class CommonRecoKtraceSpanObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ktrace_span"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("type") == "start":
      attrs.update(self.extract_dynamic_params(self._config["span_name"]))
    return attrs

class CommonRecoMarkStageObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mark_stage_end"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def need_reorder(self, _: bool = False) -> bool:
    return False

class CommonRecoPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["value", "namespace", "subtag", "extra1", "extra2", "extra3", "extra4", "extra5", "extra6"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key), check_format=False))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mode"), "缺少 mode 配置")
    check_arg(self._config.get("namespace"), "缺少 namespace 配置")
    check_arg(self._config.get("subtag"), "缺少 subtag 配置")

class CommonRecoPipelineObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    from ...common_leaf_dsl import LeafFlow
    check_arg(isinstance(self.__sub_flow, LeafFlow), f"sub_flow 类型必须为 LeafFlow。但是取到的类型为: {type(self.__sub_flow)} Tips: 请检查下有没有继承 LeafFLow、链式调用的函数有没有返回 self")
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = False
    self._auto_detect_merge_item_attrs = False

  @strict_types
  def get_type_alias(self) -> str:
    return "observe_by_sub_flow"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("task_queue_id")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_item_attrs", []))
    return attrs

class CommonRecoMetricReportObserver(LeafObserver): 
 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    # 返回该 processor_api 名的字符串，需与 api_mixin.py 中的 processor_api 保持一致
    return "report_metric_to_kafka"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    check_arg(self._config.get("item_type"), "`item_type` 是必选项")
    check_arg(self._config.get("metric_kconf_path"), "`metric_kconf_path` 是必选项")
    check_arg(self._config.get("owner_id_attr_name"), "`owner_id_attr_name` 是必选项")
    check_arg(isinstance(self._config.get("common_attr_names"), list), "`common_attr_names` 是必选项")
    check_arg(isinstance(self._config.get("long_metric_attr_names"), list), "`long_metric_attr_names` 是必选项")
    check_arg(isinstance(self._config.get("double_metric_attr_names"), list), "`double_metric_attr_names` 是必选项")
    check_arg(self._config.get("service_stage"), "`service_stage` 是必选项")
    check_arg(self._config.get("sub_stage"), "`sub_stage` 是必选项")
    check_arg(self._config.get("product_common_attr_name"), "`product_common_attr_name` 是必选项")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    attrs = set()
    common_attr_names = self._config.get("common_attr_names", [])
    for name in common_attr_names:
      attrs.add(name)
    attrs.add(self._config["product_common_attr_name"])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 输出所存储的 common_attrs 集合
    return set()

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    # 表明该 processor 是否是异步的，默认值为 False，当该 processor 为异步时，需要重写这个函数将值设为 True
    # zstd 这一 processor 不是异步的，这里也可以不写这一函数
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 item_attrs
    attrs = set()
    attrs.add(self._config["owner_id_attr_name"])
    long_metric_attr_names = self._config.get("long_metric_attr_names", [])
    for name in long_metric_attr_names:
      attrs.add(name)
    double_metric_attr_names = self._config.get("double_metric_attr_names", [])
    for name in double_metric_attr_names:
      attrs.add(name)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # 该 processor 输出所存储的 item_attrs
    return set()

class CommonRecoRodisAttrObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_rodis"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"], self._config["value_attr"]}

class CommonRecoClothoAttrObserver(LeafObserver):
  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_clotho"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    value_attr = self._config.get("value_attr",[])
    valid_list = []
    for attr in value_attr:
      if isinstance(attr, dict):
        valid_list.append(attr.get("from"))
      elif isinstance(attr, str):
        valid_list.append(attr)
    valid_list.append(self._config["key_attr"])
    ret = set(valid_list)
    for attr in ["timeout_ms"]:
      ret.update(self.extract_dynamic_params(self._config.get(attr)))
    return ret

class CommonRecoRodisUniqueListObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rodis_unique_list_observer"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kess_name"), "`kess_name` 是必选项")
    check_arg(self._config.get("domain"), "`domain` 是必选项")
    check_arg(self._config.get("payload_id"), "`payload_id` 是必选项")
    check_arg(self._config.get("key_attr"), "`key_attr` 是必选项")
    check_arg(self._config.get("unique_key_attr"), "`unique_key_attr` 是必选项")
    check_arg(self._config.get("sort_key_attr"), "`sort_key_attr` 是必选项")
    check_arg(self._config.get("data_attr"), "`data_attr` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_attr"))
    attrs.add(self._config.get("unique_key_attr"))
    attrs.add(self._config.get("sort_key_attr"))
    attrs.add(self._config.get("data_attr"))
    return attrs
