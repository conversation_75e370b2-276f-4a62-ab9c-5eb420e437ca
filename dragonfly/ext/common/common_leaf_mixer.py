#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_mixer.py
description: common_leaf dynamic_json_config DSL intelligent builder, mixer module
author: qian<PERSON><EMAIL>
date: 2022-12-28
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafMixer, try_add_table_name

class CommonRecoPipelineMixer(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    check_arg("sub_flow" in config, "缺少 sub_flow 配置")
    self.__sub_flow = config.pop("sub_flow")
    from ...common_leaf_dsl import LeafFlow
    check_arg(isinstance(self.__sub_flow, LeafFlow), f"sub_flow 类型必须为 LeafFlow。但是取到的类型为: {type(self.__sub_flow)} Tips: 请检查下有没有继承 LeafFLow、链式调用的函数有没有返回 self")
    super().__init__(config)
    # mix_by_sub_flow 不支持自动推导 item attrs
    self._auto_detect_pass_item_attrs = False
    self._auto_detect_merge_item_attrs = False
    self._auto_detect_pass_common_attrs =  "pass_common_attrs" not in self._config
    self._auto_detect_merge_common_attrs =  "merge_common_attrs" not in self._config
    self._auto_detect_input_item_tables = "input_tables" not in self._config
    self._auto_detect_enrich_item_tables = "enrich_tables" not in self._config
    self._auto_detect_retrieve_item_tables = "retrieve_tables" not in self._config

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_by_sub_flow"

  @strict_types
  def pass_common_attrs_in_request(self) -> bool:
    return self._config.get("pass_common_attrs_in_request", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pass_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("merge_common_attrs", []), "as")


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      table_name = table.get("table_name")
      table_attrs = table.get("attrs", [])
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for table in (self._config.get("enrich_tables", []) + self._config.get("retrieve_tables", [])):
      table_name = table.get("table_name")
      table_attrs = table.get("attrs", [])
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def input_item_tables(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      attrs.add(table.get("table_name"))
    return attrs

  @property
  @strict_types
  def output_item_tables(self) -> set:
    attrs = set()
    for table in  self._config.get("retrieve_tables", []):
      attrs.add(table.get("table_name"))
    return attrs

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

  @strict_types
  def _check_config(self) -> None:
    input_tables = set()
    for table in self._config.get("input_tables", []):
      input_tables.add(table.get("table_name"))
    for table in self._config.get("enrich_tables", []):
      check_arg(table.get("table_name") in input_tables, "enrich_table 必须同时是 input_table")

class CommonRecoTableUnionUpdateMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "union_table"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    append_attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("name"))
        if (attr.get("copy_mode") == "APPEND"):
          append_attrs.add(attr.get("as") or attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")

    if "if_attr" in self._config:
      attrs.add(self._config.get("if_attr"))
    attrs = try_add_table_name(self._config.get("from_table", ""), attrs)
    append_attrs = try_add_table_name(self._config.get("to_table", ""), append_attrs)
    attrs.update(append_attrs)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("as") or attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")
    if "concat_source_table_key_as" in self._config:
      attrs.add(self._config.get("concat_source_table_key_as"))
    return try_add_table_name(self._config.get("to_table", ""), attrs)

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    tables.add(self._config.get("from_table", ""))
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class CommonRecoTableGroupByMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "group_by"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")
    attrs.add(self._config["by_attr"])

    attrs = try_add_table_name(self._config.get("from_table", ""), attrs)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("as") or attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")
    if ("save_key_as" in self._config):
      attrs.add(self._config["save_key_as"])
    return try_add_table_name(self._config.get("to_table", ""), attrs)

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    tables.add(self._config.get("from_table", ""))
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class CommonRecoTableJoinMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lelft_join_table"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")

    if ("join_attr_right" in self._config):
      attrs.add(self._config["join_attr_right"])
    attrs = try_add_table_name(self._config.get("right_table", ""), attrs)

    if ("join_attr_left" in self._config):
      left_attrs = [self._config["join_attr_left"]]
      attrs.update(try_add_table_name(self._config.get("left_table", ""), left_attrs))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif isinstance(attr, dict):
        check_arg(attr.get("name"), "copy_attrs 的元素必须是 string 或者包含 'name' 字段的 dict")
        attrs.add(attr.get("as") or attr.get("name"))
      else:
        check_arg(False, "copy_attrs 的元素必须是 string 或者 dict")

    return try_add_table_name(self._config.get("left_table", ""), attrs)

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("left_table", ""))
    tables.add(self._config.get("right_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class CommonRecoCreateLogicTableMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "create_logic_table"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if (self._config.get("ignore_input_attr_source", False)):
      return attrs
    for attr in self._config.get("select_attr", []):
      attrs.add(attr)
    return attrs 

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("select_attr", []):
      attrs.add(attr)
    attrs = try_add_table_name(self._config.get("logic_table", ""), attrs)
    return attrs

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self.item_table)
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("logic_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    tables = self.output_item_tables
    if self._config.get("remove_selected_items", False):
      tables.add(self.item_table)
    return tables
  
