#!/usr/bin/env python3
# coding=utf-8
"""
filename: vision_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for vision server
author: <EMAIL>
date: 2020-10-15 14:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class VisionEyeshotRpcRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "vision_retrieve_by_eyeshot_rpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    attrs.update(self.extract_dynamic_params(self._config.get("tab_id_from_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("extra_request_ctx_from_attr")))
    if self._config.get("web_pass_info_from_attr", ""):
      attrs.add(self._config.get("web_pass_info_from_attr", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("count"), (int, str)), "count 需为整数或字符串类型")
    if isinstance(self._config["count"], int):
      check_arg(self._config["count"] > 0, "count 为整数时需大于 0")
    if isinstance(self._config["count"], str):
      check_arg(self._config["count"].startswith("{{") and self._config["count"].endswith("}}"), "count 为字符串时需满足动态参数 {{}} 格式")

class VisionRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "vision_redis_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("key")))
    attrs.update(self.extract_dynamic_params(self._config.get("prefix")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieval_num")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"], "cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("retrieval_num"), (int, str)), "retrieval_num 需为整数或字符串类型")
    if isinstance(self._config["retrieval_num"], int):
      check_arg(self._config["retrieval_num"] > 0, "retrieval 为整数时需大于 0")
    if isinstance(self._config["retrieval_num"], str):
      check_arg(self._config["retrieval_num"].startswith("{{") and self._config["retrieval_num"].endswith("}}"), "retrieval_num 为字符串时需满足动态参数 {{}} 格式")

  
class VisionFollowRpcRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "vision_retrieve_by_follow_rpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")