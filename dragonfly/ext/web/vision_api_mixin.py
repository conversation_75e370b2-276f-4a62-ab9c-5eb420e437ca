#!/usr/bin/env python3
# coding=utf-8
"""
filename: vision_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, vision api mixin
author: <EMAIL>
date: 2020-10-15 14:00:00
"""

from ...common_leaf_util import ArgumentError
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .vision_retriever import *
from .vision_enricher import *

class VisionApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含（定制的） vision 相关的 Processor 接口:
  - VisionEyeshotRpcRetriever
  """

  def vision_retrieve_by_eyeshot_rpc(self, **kwargs):
    """
    VisionEyeshotRpcRetriever  
    ------
    Web 端调用栏目接口来获取横屏的分钟级视频，用于 web 快手的首页横屏视频推荐

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] 栏目 RPC 服务的 kess 服务名

    `service_group`: [string] 栏目 RPC 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求栏目 RPC 服务的超时时间，建议最少 500，默认值为 1000

    `source`: [int] 请求发现页的类型，默认值为 0

    `browse_type`: [int] 主 APP 不同的 UI 版本。1：正常版，2：上下滑，3：滑滑设置版，0：其它。默认值为 0

    `locale`: [string] Embedding 服务的 kess 服务组，默认值为 "zh_CN"

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `tab_id_from_attr`: [string] 栏目的 tab id，从 common attr 中获取指定 key 的值，如不设置则会将 tab_id 设置为 0

    `request_type`: [string] 请求栏目 RPC 服务时的请求类型，"UNKNOWN", "SLIDE", "WATERFALL"，"WEB", 默认值为 "UNKONW"

    `extra_request_ctx_from_attr`: [string] 请求栏目 RPC 服务时由 api 传入的 json 串，包含 llsid 和 page 参数，默认值为 ""

    `web_pass_info_from_attr`: [string] 若设置则从指定的 CommonAttr 中获取 web_pass_info 填充到 request 中，可缺省

    `count`: [int] [动态参数] 请求的 item 数目（注意：如果不设置count数量，则每次只返回 6 个结果视频）

    调用示例
    ------
    ``` python
    .vision_retrieve_by_eyeshot_rpc(
      reason = 600,
      kess_service = "grpc_recoEyeshot",
      timeout_ms = 1000,
      retrieval_item_type = 0,
      tab_id_from_attr = "tabId",
      request_type = "WEB",
      count = 150
    )
    ```
    """
    self._add_processor(VisionEyeshotRpcRetriever(kwargs))
    return self

  def vision_redis_retrieve(self, **kwargs):
    """
    VisionRedisRetriever  
    ------
    Web 项目，从 redis 中召回不同地域感兴趣的视频列表

    参数配置
    ------
    `reason`: [int] 召回原因

    `cluster_name`: [string] redis cluster name

    `timeout_ms`: [int] 选配项，redis client 超时时间，默认为 15ms

    `key`: [string] [动态参数] redis key

    `prefix`: [string] [动态参数] 选配项，redis key 的前缀

    `retrieval_num`: [int] [动态参数] 召回的结果数， 如缺省，则 redis 召回多少就返回多少

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    调用示例
    ------
    ``` python
    .vision_redis_retrieve(
      reason = 600,
      cluster_name = "recoRealTimeCF",
      timeout_ms = 15,
      key = "{{redis_key}}",
      prefix = "idx_",
      retrieval_num = 1200
    )
    ```
    """
    self._add_processor(VisionRedisRetriever(kwargs))
    return self

  def vision_retrieve_by_follow_rpc(self, **kwargs):
    """
    VisionFollowRpcRetriever  
    ------
    Web 端调用关注页接口来获取关注页结果，用于 web 快手的关注页推荐

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] 栏目 RPC 服务的 kess 服务名

    `service_group`: [string] 栏目 RPC 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求栏目 RPC 服务的超时时间，建议最少 500，默认值为 1000

    `retrieval_item_type`: [int] 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    调用示例
    ------
    ``` python
    .vision_retrieve_by_follow_rpc(
      reason = 800,
      kess_service = "grpc_test",
      timeout_ms = 2000,
      retrieval_item_type = 0,
    )
    ```
    """
    self._add_processor(VisionFollowRpcRetriever(kwargs))
    return self

  def vision_transform_json_str(self, **kwargs):
    """
    VisionTransformJsonStrEnricher  
    ------
    接受一个 json_str 作为输入，然后向 json_str 中加入 action_list 字段

    参数配置
    ------
    `input_common_attr`: [list] 各个 action_list 所对应的 common attr 列表

    `output_common_attr`: [string] 存储转换后的 json_attr

    `internal_attr`: [list] 各个 action_list 在 json_str 中对应的字段名称
    
    `item_list_length`: [int] 各个 action_list 的最大长度

    调用示例
    ------
    ``` python
    .vision_transform_json_str(
      input_common_attr = ["click_item_list", "like_item_list", "follow_item_list", "hate_item_list"],
      output_common_attr = "semSignal",
      internal_attr = ["click_list", "like_list", "follow_list", "hate_list"],
      item_list_length = 200,
    )
    ```
    """
    self._add_processor(VisionTransformJsonStrEnricher(kwargs))
    return self