#!/usr/bin/env python3
# coding=utf-8
"""
filename: kap_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kap api mixin
author: <EMAIL>
date: 2020-03-11 20:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kap_observer import *
from .kap_enricher import *
from .kap_retriever import *

class KapApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 Kap 相关的 Processor 接口:
  - KapSampleFillBuilderObserver
  """
  def fill_kap_sample(self, **kwargs):
    """
    KapSampleFillBuilderObserver
    ------
    收集特定 Attr 的参数填充到 kap::SampleBatchDataBuilders

    参数配置
    `common_dense_attrs`: [list] 从指定 common attr 获取 dense attr，成员为 attr name，默认为空。

    `common_sparse_attrs`: [list] 从指定 common attr 获取 sparse attr，成员为 object，slot 和 sign 为 attr name，默认为空。

    `item_dense_attrs`: [list] 从指定 item attr 获取 dense attr，成员为 attr name，默认为空。

    `item_sparse_attrs`: [list] 从指定 item attr 获取 sparse attr，成员为 object，slot 和 sign 为 attr name，默认为空。

    `slot_as_attr_name`: [list] 从指定 item attr 获取 sparse attr，成员为 int，表示以这些 slot 字符串为 key 的 attr 将构建成 sparse 特征。

    调用示例
    ------
    ``` python
    .fill_kap_sample(
      item_dense_attrs=["rChannelId"],
      common_dense_attrs=[],
      item_sparse_attrs=[dict(slot="slots", sign="parameters")],
      common_sparse_attrs=[])
    ```
    """
    self._add_processor(KapSampleFillBuilderObserver(kwargs))
    return self

  def retrieve_from_common_sample(self, **kwargs):
    """
    KapCommonSampleToMioContextRetriever
    ------
    访问 common sample 服务获取样本填充到 context

    参数配置
    `slot_attr`: [string] 从指定 item attr 获取 slot。

    `keysign_attr`: [string] 从指定 item attr 获取 keysign。

    调用示例
    ------
    ``` python
    .retrieve_from_common_sample(
      slot_attr=["slots"],
      keysign_attr=["signs"])
    ```
    """
    self._add_processor(KapCommonSampleToMioContextRetriever(kwargs))
    return self

  def fetch_kuiba_sample_reader(self, **kwargs):
    """
    KuibaSampleReaderEnricher
    ------
    使用 kuiba::SampleReader 读取消息， 消息放到专用的 KuibaSampleReaderManager thread_local 结构中
    参数配置
    `sample_reader`: [object] 对应 kuiba dynamic_json_config.json 里面sample_reader 的配置

    调用示例
    ------
    ``` python
    .fetch_kuiba_sample_reader(
      sample_reader=kuiba_dynamic_json_config["sample_reader"]
      )
    ```
    """
    self._add_processor(KuibaSampleReaderEnricher(kwargs))
    return self

  def extract_kuiba_sample_parser(self, **kwargs):
    """
    KuibaSampleParserRetriever
    ------
    使用 kuiba::SampleParser 抽取 fetch_kuiba_sample_reader 读取到的消息, 输出
    参数配置
    `fetch_sample`: [object] 对应 kuiba dynamic_json_config.json 里面 fetch_sample 的配置

    `kuiba_sample_filter`: [object] 对应 kuiba dynamic_json_config.json 里面 样本过滤器

    `kuiba_loss_function`: [object] 对应 kuiba dynamic_json_config.json 里面 loss_functions

    `kuiba_loss_function_sample_filter`: [object] 对应 kuiba dynamic_json_config.json 里面 loss functions 的过滤器

    `slots_mapping`: [object] kuiba 的 feature name 到 kai slot 的映射

    `config`: [object] kuiba 的特征抽取配置

    `item_slots`: [string] 输出 item slots 的位置, 默认为 slots

    `item_signs`: [string] 输出 item signs 的位置, 默认为 signs

    `auc_uid_attr`: [string] 使用哪个 attr 作为 auc uid

    调用示例
    ------
    ``` python
    # 参数一般都是自动翻译脚本填充，用户自己不需关心
    .extract_kuiba_sample_parser(
      fetch_sample=kuiba_dynamic_json_config["krp_tf_learner_server"]["fetch_sample"]
      kuiba_sample_filter=xxx, 
      kuiba_loss_function_sample_filter=xxx,
      kuiba_loss_function=xxx,
      slots_mapping=xxx,
      config=xxx,
      item_slots="item_slots",
      item_signs="item_signs"
      )
    ```
    """
    self._add_processor(KuibaSampleParserRetriever(kwargs))
    return self


