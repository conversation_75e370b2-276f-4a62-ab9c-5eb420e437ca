#!/usr/bin/env python3
"""
filename: kap_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for kap
author: <EMAIL>
date: 2020-04-28 19:00:00
"""

import operator
import itertools

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever


class KapCommonSampleToMioContextRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_common_sample"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {self._config["slot_attr"], self._config["keysign_attr"]}

class KuibaSampleParserRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kuiba_sample_parser_retriever"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["item_slots"])
    attrs.add(self._config["item_signs"])
    for name, slot_id in self._config["slots_mapping"].items():
      if slot_id == -1:
        attrs.add(name)
    return attrs

