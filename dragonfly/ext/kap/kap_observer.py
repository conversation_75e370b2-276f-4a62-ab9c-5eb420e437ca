#!/usr/bin/env python3
"""
filename: kap_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for kap
author: <EMAIL>
date: 2020-04-28 19:00:00
"""

import operator
import itertools
import copy

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver


class KapSampleFillBuilderObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_kap_sample"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = copy.deepcopy(self._config["item_dense_attrs"]);
    for sparse_attr in self._config["item_sparse_attrs"]:
      attrs.append(sparse_attr["sign"])
      attrs.append(sparse_attr["slot"])
    return set(attrs)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = copy.deepcopy(self._config["common_dense_attrs"]);
    for sparse_attr in self._config["common_sparse_attrs"]:
      attrs.append(sparse_attr["sign"])
      attrs.append(sparse_attr["slot"])
    return set(attrs)
