#!/usr/bin/env python3
"""
filename: kuiba_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kuiba
author: <EMAIL>
date: 2020-01-16 18:34:00
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher

class KuibaSampleReaderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kuiba_sample_reader_enricher"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

