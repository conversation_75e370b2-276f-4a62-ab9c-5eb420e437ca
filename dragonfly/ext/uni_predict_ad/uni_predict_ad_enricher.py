#!/usr/bin/env python3
"""
filename: uni_predict_ad_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for uni_predict_ad
author: <EMAIL>
date: 2024-09-14 16:34:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import <PERSON><PERSON><PERSON>richer, LeafRetriever

class AdTwinTowerStartupEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_twin_tower_startup"

class AdConstructBsLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "construct_bs_log_opt"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_ptr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("bs_log", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdBuildUserTargetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_user_target_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_ptr", ""))
    attrs.add(self._config.get("bs_log", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_index", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdResultHandlerRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_result_handler"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("target_index", ""))
    attrs.update(self._config.get("output_ops", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdFeatureDiffEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_feature_diff"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("base_uni_response", ""))
    attrs.add(self._config.get("sparse_feature", ""))
    attrs.add(self._config.get("dense_feature", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("has_diff", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdExtractUserFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_ad_feature_by_bslog"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_ptr", ""))
    attrs.add(self._config.get("bs_log", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("sparse_feature", ""))
    attrs.add(self._config.get("dense_feature", ""))
    attrs.add(self._config.get("sparse_fields_attr", ""))
    attrs.add(self._config.get("sparse_signs_attr", ""))
    attrs.add(self._config.get("dense_fields_attr", ""))
    attrs.add(self._config.get("dense_values_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdUniPredictFusedAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_predict_fused_ad"

  @staticmethod
  @strict_types
  def _is_common(config)->bool:
    if config.get("common", False):
      return True
    elif config.get("compress_group", "") == "USER":
      return True
    else:
      return False

  @strict_types
  def _check_config(self) -> None:
    # 检查 embedding fetcher type
    common_input_tensor_names = list()
    item_input_tensor_names = list()
    inputs = self._config.get("inputs", None)
    check_arg(inputs and isinstance(inputs, list), "'inputs' not be configured, FIX it ...")
    for e in inputs:
      if self._is_common(e):
        common_input_tensor_names.append(e["tensor_name"])
      else:
        item_input_tensor_names.append(e["tensor_name"])

    if "embedding_fetchers" in self._config:
      for fetcher_config in self._config.get("embedding_fetchers"):
        fetcher_type = fetcher_config.get("fetcher_type", "")
        check_arg(fetcher_type and fetcher_type in {"SparseHybridEmbeddingFetcher", "DenseEmbeddingFetcher"},
          '`embedding_fetchers::type` 必须选自 {"SparseHybridEmbeddingFetcher", "DenseEmbeddingFetcher"}')

        is_common = self._is_common(fetcher_config)
        for c in fetcher_config["fields_config"]:
          if is_common:
            check_arg(c["tensor_name"] in common_input_tensor_names, f"unexpected common tensor_name:{c['tensor_name']} in embedding_fetcher:{fetcher_type}")
          else:
            check_arg(c["tensor_name"] in item_input_tensor_names, f"unexpected item tensor_name:{c['tensor_name']} in embedding_fetcher:{fetcher_type}")

  @strict_types
  def _get_input_attrs(self, flag_common) -> set:
    attrs = set()
    for fetcher_config in self._config.get("embedding_fetchers", []):
      if self._is_common(fetcher_config) == flag_common:
        for e in fetcher_config["fields"]:
          attrs.add(e)
        for e in fetcher_config.get("signs", list()):
          attrs.add(e)
        for e in fetcher_config.get("values", list()):
          attrs.add(e)
    return attrs

  @strict_types
  def _get_output_attrs(self, flag_common) -> set:
    attrs = set()
    for output in self._config.get("outputs", []):
      if self._is_common(output) == flag_common:
        attrs.add(output["output_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs(True)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs(False)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs(True)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self._get_output_attrs(False)

class AdFetchHybridEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_predict_fetch_hybrid_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    slot_config = self._config.get("slots_config", [])
    attrs = set()

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    attrs = set()

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdResultCacheRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_result_cache_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_ptr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_read", False):
      attrs.add(self._config.get("hit_cache_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class AdCombinieDenseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_combine_dense"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("uid_attr"))
    ret.update(self._config.get("user_action_attr_list", []))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_sign"))
    ret.add(self._config.get("output_match_cnt"))
    return ret

