#!/usr/bin/env python3
# coding=utf-8

"""
filename: kai_fg_observer.py
description: common_leaf dynaminc_json_config DSL intelligent builder, observer module for kai_fg
author: ji<PERSON>ow<PERSON>@kuaishou.com
date: 2023-04-18 14:38:00
"""

import operator
from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafObserver

class SlotSignDiffObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slot_sign_diff"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get('common_slot_signs', False):
      for item in self._config.get('common_slot_signs'):
        attrs.add(item['slots_a'])
        attrs.add(item['slots_b'])
        attrs.add(item['signs_a'])
        attrs.add(item['signs_b'])
    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get('item_slot_signs', False):
      for item in self._config.get('item_slot_signs'):
        attrs.add(item['slots_a'])
        attrs.add(item['slots_b'])
        attrs.add(item['signs_a'])
        attrs.add(item['signs_b'])
    return attrs


class FlatKvObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "log_flatkv"

  @strict_types
  def _check_config(self) -> None:
    for attr_name in ["common", "attr_name", "kconf_path"]:
      check_arg(attr_name in self._config, f"缺少 {attr_name} 配置")

  def get_attr_names(self, is_common: bool) -> set:
    if self._config["common"] != is_common:
      return set()
    return {self._config["attr_name"]}

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.get_attr_names(True)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.get_attr_names(False)


class LogSlotSignObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "log_slot_sign"

  @strict_types
  def _check_config(self) -> None:
    for attr_name in ["slots_selected"]:
      check_arg(attr_name in self._config, f"缺少 {attr_name} 配置")
    check_arg(("common_slots" in self._config) == ("common_signs" in self._config),
              "common_slots 和 common_signs 必须同时配置")
    check_arg(("item_slots" in self._config) == ("item_signs" in self._config),
              "item_slots 和 item_signs 必须同时配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "common_slots" in self._config:
      return {"common_slots", "common_signs"}
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "item_slots" in self._config:
      return {"item_slots", "item_signs"}
    else:
      return set()

class AttrDiffObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "attr_diff"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "common_attrs" in self._config:
      ret = set()
      for attr_pair in self._config["common_attrs"]:
        ret.add(attr_pair["attr_a"])
        ret.add(attr_pair["attr_b"])
      return ret
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "item_attrs" in self._config:
      ret = set()
      for attr_pair in self._config["item_attrs"]:
        ret.add(attr_pair["attr_a"])
        ret.add(attr_pair["attr_b"])
      return ret
    else:
      return set()

class AttrTypeObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "attr_type"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr in self._config["attrs"]:
      ret.add(attr)
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in self._config["attrs"]:
      ret.add(attr)
    return ret

