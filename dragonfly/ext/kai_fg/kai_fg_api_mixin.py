#!/usr/bin/env python3
# coding=utf-8
"""
filename: kai_fg_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kai_fg api mixin
author: <EMAIL>
date: 2023-03-13 15:23:30
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kai_fg_enricher import *
from .kai_fg_observer import *


class KaiFGApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 kai_fg 相关 processor 接口
  - KaiFGFVExtractSlotSignAttrEnricher

  相比 Kuiba 和 Mio 的特征生成方式，KaiFG 采用图驱动的方式进行特征生成，能够提供更高的性能.

  维护人: <EMAIL>
  """

  def kaifg_extract_slot_sign(self, **kwargs):
    """
    KaiFGFVExtractSlotSignAttrEnricher
    ------
    以图驱动的方式生成 slot 和 sign

    参数配置
    ------
    `module`: [dict] 特征生成的图配置.
      - `name`: [string] module name.
      - `nodes`: [dict list] module 包含的计算节点.
        - `name`: [string] 计算节点的 name.
        - `op`: [string] 计算节点的 op.
        - `attr`: [dict] 计算节点的 attr, 有些 op 可能为空.
        - `input`: [list] 计算节点的输入.

    `outputs`: [dict list] slot sign 特征生成的 output 配置, 若配置该参数，表明生成 slot sign 特征.
      - `name`: [string] 输出的名字.
      - `slot`: [int] 输出的 slot 配置.
      - `slot_key_type`: [int] 输出的 slot 配置.

    `named_outputs`: [dict list] 普通特征生成的 output 配置，若配置该参数，表明生成普通特征. `outputs` 和 `named_outputs` 可以一起配置输出.
      - `column_name`: 特征生成计算图输出的列名.
      - `output_name`: 特征生成计算图输入的列对应的存储到 context 中的位置.

    `inputs`: [dict list] 特征生成图的 input 配置.
      - `name`: [string] 输入的 name.
      - `type`: [string] 输入的数据类型.

    `input_maps`: [dict list] 对 `inputs` 的来源做映射, 默认不配置, 所有 input 都来自 dragon context 对应 name 的 attr
      - `name`: [string] 对应 `inputs.name`
      - `source_type`: [dict] 来源, 默认是 context. 可选范围 [context, flatkv]
      - `attr_name`: [string] `source_type` 为 `flatkv` 时，指定 flatkv 的 attr_name; `context` 时, 指定 `name` 本身对应的 context 属性名, 缺省等于 `name`
      - `field_name`: [string] `source_type` 为 `flatkv` 时，指定 `name` 对应到 flatkv 中的哪个 field
      - `kconf_path`: [string] `source_type` 为 `flatkv` 时, 必须设置其 schema 所在的 kconf 路径
      - `common`: [bool] 指定用于生成 slots/signs 的 attr 是来自 user 侧还是 item 侧; 默认与外层 common 相同; 注意生成 user 侧特征时不可配置为 false.

    `slots_output`: [string] 输出 slots 的位置, 仅当生成 slot sign 特征时需要配置.

    `signs_output`: [string] 输出 signs 的位置, 仅当生成 slot sign 特征时需要配置.

    调用示例
    ------
    ```python
    kaifg_config = json.load(open("kaifg_config.json"))
    .kaifg_extract_slot_sign(
      module=kaifg_config['module'],
      outputs=kaifg_config['outputs'],
      inputs=kaifg_config['inputs'],
      slots_output="slots",
      signs_output="signs",
    )
    ```
    """
    self._add_processor(KaiFGFVExtractSlotSignAttrEnricher(kwargs))
    return self

  def slot_sign_diff(self, **kwargs):
    """
    SlotSignDiffObserver
    ------
    Diff KaiFG 生成的 Slot 和 Sign 和其他特征抽取模块生成的 Slot 和 Sign 是否一致

    参数配置
    ------
    `common_slot_signs`: [dict] user 侧特征生成的 Slot 和 Sign 的配置
      - `slots_a`: user 侧特征 slots_a 的 name
      - `slots_b`: user 侧特征 slots_b 的 name
      - `signs_a`: 需要对比的 user 侧特征 signs_a 的 name
      - `signs_b`: 需要对比的 user 侧特征 signs_b 的 name

    `item_slot_signs`: [dict] item 侧特征生成的 Slot 和 Sign 的配置
      - `slots_a`: 需要对比的 item 侧特征 slots_a 的 name
      - `slots_b`: 需要对比的 item 侧特征 slots_b 的 name
      - `signs_a`: 需要对比的 item 侧特征 signs_a 的 name
      - `signs_b`: 需要对比的 item 侧特征 signs_b 的 name

    `ignore_order`: [bool] 比较时，是否忽视同一个 slot 内 sign 顺序的不同. 默认 False.

    `print_sign_value`: [bool], 是否除去 sign 的 slot 前缀，直接打印真实值，默认 False.

    `ignore_diff_slots`: [list], 比较时，忽略这些 slots 的 diff，默认空.

    调用示例
    -----
    ```python
    .slot_sign_diff(
      common_slot_signs=[
        {
          'slots_a': 'kaifg_common_slots',
          'slots_b': 'kuiba_common_slots',
          'signs_a': 'kaifg_common_signs',
          'signs_b': 'kuiba_common_signs',
        }
      ],
      item_slot_signs=[
        {
          'slots_a': 'kaifg_item_slots',
          'slots_b': 'kuiba_item_slots',
          'signs_a': 'kaifg_item_signs',
          'signs_b': 'kuiba_item_signs',
        }
      ]
    )
    ```
    """
    self._add_processor(SlotSignDiffObserver(kwargs))
    return self

  def log_flatkv(self, **kwargs):
    """
    FlatKvObserver
    ------
    解析并打印指定的 flatkv(bytes) 到本地的 INFO log 中

    配置参数
    ------
    `common`: [bool] 必填项, 是否 common 侧

    `attr_name`: [string] 必填项, flatkv 从哪个 attr_name 获取

    `kconf_path`: [string] 必填项, flatkv 的 schema 定义在哪个 kconf 路径下

    调用示例
    ------
    ```python
    .log_flatkv(
      common = False,
      attr_name = "FlatKvString",
      kconf_path = "reco.distributedIndex.flatkvSchemaTest",
    )
    ```
    """
    self._add_processor(FlatKvObserver(kwargs))
    return self

  def flatkv_to_json(self, **kwargs):
    """
    FlatKvToJsonEnricher
    ------
    将指定的 flatkv 转为 json string, 保存到 context 中

    配置参数
    ------
    `common`: [bool] 必填项, 是否 common 侧

    `from_attr`: [string] 必填项, flatkv 从哪个 attr 获取

    `kconf_path`: [string] 必填项, flatkv 的 schema 定义在哪个 kconf 路径下

    `save_to`: [string] 必填项, 转成 json string 后保存到哪个 attr

    调用示例
    ------
    ```python
    .flatkv_to_json(
      common = False,
      from_attr = "FlatKvString",
      kconf_path = "reco.distributedIndex.flatkvSchemaTest",
      save_to = "FlatKvJson",
    )
    ```
    """
    self._add_processor(FlatKvToJsonEnricher(kwargs))
    return self

  def test_extract_from_protobuf(self, **kwargs):
    """
    TestExtractFromProtobufEnricher
    ------
    NOTICE: 测试使用，后续会被删除且不会通知
    测试 KV 化参数时，hetu tag (list of list) 等不好从 pb 中提取的原始特征，在这里提取.
    特征列表：
    UserInfo::UserProfileV1::video_playing_stat::hetu_tag_level_info::hetu_tag_level_one(uint32_list*)
    UserInfo::UserProfileV1::video_playing_stat::hetu_tag_level_info::hetu_tag_level_two(uint32_list*)
    UserInfo::UserProfileV1::video_playing_stat::hetu_tag_level_info::hetu_tag_level_three(uint32_list*)

    配置参数
    ------
    `user_info_from_attr`: [string] 必填项, 从哪个 common_attr 获取 user_info 的 pb 指针

    调用示例
    ------
    ```python
    .test_extract_from_protobuf(
      user_info_from_attr = "user_info",
    )
    ```
    """
    self._add_processor(TestExtractFromProtobufEnricher(kwargs))
    return self

  def log_slot_sign(self, **kwargs):
    """
    LogSlotSignObserver
    ------
    打印出指定 slot 对应的 sign;

    配置参数
    ------
    `common_slots`: [string], 从哪个 common_attr 获取 common_slots.

    `common_signs`: [string], 从哪个 common_attr 获取 common_signs. 和 common_slots 共同出现.

    `item_slots`: [string], 从哪个 item_attr 获取 item_slots.

    `item_signs`: [string], 从哪个 item_attr 获取 item_signs. 和 item_slots 共同出现.

    `print_sign_value`: [bool], 是否除去 sign 的 slot 前缀，直接打印真实值，默认 false.

    `item_limit`: [int], 打印 item_signs 时，是否对 item 数作限制，默认 -1 也就是不限制.

    `slots_selected`: [int list] 必填项, 指定需要打印的 slots.

    调用示例
    ------
    ```python
    .log_slot_sign(
      common_slots = "common_slots",
      common_signs = "common_signs",
      print_sign_value = True,
      slots_selected = [50, 60],
    )
    ```
    """
    self._add_processor(LogSlotSignObserver(kwargs))
    return self

  def enrich_with_flatkv(self, **kwargs):
    """
    FlatKvAttrEnricher
    ------
    从指定的 flatkv 提取 attr 保存到 context

    配置参数
    ------
    `common`: [bool] 必填项, 是否 common 侧

    `from_attr`: [string] 必填项, flatkv 从哪个 attr 获取

    `kconf_path`: [string] 必填项, flatkv 的 schema 定义在哪个 kconf 路径下

    `attrs`: [list of object] 必填项, 需要抽取的属性列表，每项值为字符串或 {"name"="xxx", "path"="yyy"} 的 dict 格式。在线上使用，请务必只配置需要使用的 attr！
      - `name`: [string] attr 的名称。
      - `path`: [string] flatkv 内的名称。


    调用示例
    ------
    ```python
    .flatkv_to_json(
      common = False,
      from_attr = "FlatKvString",
      kconf_path = "reco.distributedIndex.flatkvSchemaTest",
      attrs = ["author__fans_count", {"name": "picture_type", "path": "picture_type"}],
    )
    ```
    """
    self._add_processor(FlatKvAttrEnricher(kwargs))
    return self

  def enrich_attr_by_kaifg(self, **kwargs):
    """
    KaiFGAttrEnricher
    ------
    指定自定义的 Python 函数，对 common_attr 和 item_attr 的某些字段进行操作（读取和生成）

    也可以支持对存储在 attr 中的 flatkv 对象进行操作

    会使用 KaiFG 对你的自定义函数进行编译，大幅提高运行速度

    运行本算子需要的系统需求: 1. Python >= 3.6   2. KaiFG >= 2.0.3

    编译需要的系统需求: 1. Python >= 3.9   2. KaiFG >= 2.0.3    3. PATH 中有 clang >= 13

    - 云开发机 clang 在 /data/soft/distcc/clang-14.0.6/bin，可以指定到 PATH 中: export PATH=$PATH:/data/soft/distcc/clang-14.0.6/bin

    - 在线部署可以使用我们指定的镜像，通过 kaiserving 部署的 krp_gpu_uni_predict_server 和 krp_cpu_uni_predict_server 镜像自带

    KaiFG 支持编译的 Python 语法范围和丰富用例:

    参考文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcADNWDG9b2Re86JAeTAPeYWK


    配置参数
    ------
    `function_for_common`: [list] 选配项，指定使用 Python 的自定义函数们，这些函数对 common_attr 的某些字段进行操作。

    `function_for_item`: [list] 选配项，指定使用 Python 的自定义函数们，这些函数对 item_attr 的某些字段进行操作。可以读取 common_attr 但是无法写入 common_attr。

    `skip_compilation_test`: [boolean] 选配项，是否跳过在 dragonfly 生成配置时测试 functions 能否成功被编译，默认 False，< Python 3.9 时默认跳过(True)。

    `import_attrs`: [dict] 选配项，指定需要导入到 Python 函数入参的 attr 名字。dict 格式是 {"Python 函数名": {"入参 a 名字": "attr 名字 a", "入参 b 名字": "attr 名字 b"}}
                    或者 {"Python 函数名": ["attr 名字 a", "attr 名字 b"]}。没有特殊情况，使用后一种。
                    如果 import_attrs 找不到某个 Python 函数名，则这个函数的入参名字直接去对应 attr 中的名字,
                    例如 `def my_add(click_times: int, view_times:int) -> int` 并且 `my_add` 被设置到 function_for_item,
                    则这个 Python 函数会去找 `click_times` 和 `view_times` 的 int item_attr.
      - `attr_name`: [string] 该入参对应的 attr 名字
      - `attr_type`: [int] 框架会自动从 Python 函数 annotation 解析，一般不需要自己配置
      - `common`: [boolean] 是否是来自 common_attr
      - `field_name`: [string] 如果是 flatkv 类型的数据块，需要输入字段名字
      - `kconf_path`: [string] 如果是 flatkv 类型的数据块，需要输入 schema 的 kconf
      - `force_default`: [boolean] 当 code 中的对应参数指定了默认值时才能设为 True，该参数为 True 时，强制将默认值传递给抽取函数
      - `default`: 当前参数的默认值，设置该字段会覆盖 code 中指定的默认值，当 code 中设置了默认值时有效
      - `source_type`: [string] 指定该入参来自哪里，支持 {`context`, `flatkv`, `protobuf`}，默认 context 即来自 dragon 的 attr
      - `pb_attr`: [string] 当 `source_type` 为 `protobuf` 时必须配置，存 pb 指针的 dragon 的 attr 名称
      - `pb_field_path`: [string] 当 `source_type` 为 `protobuf` 时必须配置，protobuf 结构内数据路径，类似 json path 的句点分隔格式
      - `pb_skip_unset_field`: [bool] 当 `source_type` 为 `protobuf` 时可选配置，对 pb 中未设置过值的非 repeated 字段是否跳过取值，给对应的 attr 留空，默认为 False（将取对应类型的默认值）
      - `pb_mode`: [string] 当 `source_type` 为 `protobuf` 时可选配置，指定特殊处理模式，配置成 `offsets` 表示当遇到 list of list 嵌套时，结果为偏移位置数组，结果是 int list 类型

    `export_attrs`: [dict] 必填项，指定 Python 函数计算的结果，存在哪个 attr 上。dict 格式是 {"Python 函数名": ["output_attr_a", "output_attr_b"]}
                    或者 {"Python 函数名": [{"attr_name": "output_attr_a"...}, ...]}。没有特殊情况使用前一种
      - `attr_name`: [string] 第 i 个返回值存储到的 attr 名字
      - `attr_type`: [string] 框架会自动从 Python 函数的 return annotation 解析，一般不需要自己配置
      - `common`: [boolean] 第 i 个返回值是否存储到 common attr
      - `slots_attr`: [string] 如果生成 slot sign 类型的 attr, 则必填, 生成的 slot 存放的 attr
      - `signs_attr`: [string] 如果生成 slot sign 类型的 attr, 则必填, 生成的 sign 存放的 attr
      - `slot`: [int] 如果生成 slot sign 类型的 attr, 则必填, slot 配置
      - `slot_key_type`: [int] 如果生成 slot sign 类型的 attr, 则必填, slot_key_type 配置
      - `ignore_slot`: [Boolean] 默认值为 False, 如果在 py 代码中生成最终的 sign 则需要配置该选项为 True, 这是在写到 context 中之前不会再使用 slot 和 slot_key_type 重新生成 sign 了, 而是将 py 的计算结果写到 context 中.

    `code`: [str] 选配项，指定运行的 Python 代码。如果配置 code，则在 `function_for_common` 和 `function_for_item` 中配置函数名；
                  如果不配置 code，则在 `function_for_common` 和 `function_for_item` 中配置函数体（函数对象）。参考调用示例

    注意事项
    ------
    1. 在 dragonfly playground 中，只能使用配置 `code` 的方式。
    2. `function_for_common` 和 `function_for_item` 中配置的函数名必须是唯一的。
    3. 在 Python3.9+、配置好 clang、pip install kuaishou-kaifg 的情况下，运行 dsl 时函数会被 KaiFG 编译一次。强烈建议
       在运行 dsl 时编译，防止在线上出现编译失败的情况。

    调用示例
    ------
    ```Python
    ## 1. 简单直观配置方法
    from typing import List, Tuple

    def myadd(a:List[int], b:List[int])->List[int]:
      ret = []
      for (x, y) in zip(a, b):
        ret.append(x+y+1)
      return ret

    .enrich_attr_by_kaifg(
      # 直接将函数体配置在 function_for_item
      function_for_item=[my_add],
      # 直接按照参数顺序，填需要被计算的 attr. 如果需要配置某个 attr 的 common 与否，使用 dict(attr_name="attr_b", common=True)
      import_attrs={"myadd": ["attr_a", "attr_b"]},
      export_attrs={"myadd": ["attr_output"]},
    )

    ## 2. 配置 code 的方法
    code = \"\"\"
    from typing import List, Tuple
    def myadd(a:List[int], b:List[int])->List[int]:
      ret = []
      for (x, y) in zip(a, b):
        ret.append(x+y+1)
      return ret
    \"\"\"
    .enrich_attr_by_kaifg(
      # 代码通过字符串配置
      code=code,
      # 将函数名配置在 function_for_item
      function_for_item=["my_add"]
      import_attrs={"my_add", [dict(attr_name="attr_a", common=False), dict(attr_name="attr_b", common=True)]},
      export_attrs={"my_add": ["attr_output"]},
    )

    ## 3. 生成 slot sign 方便 embedding 查询
    from typing import List, Tuple

    def myadd(a:List[int], b:List[int])->List[int]:
      ret = []
      for (x, y) in zip(a, b):
        ret.append(x+y+1)
      return ret

    .enrich_attr_by_kaifg(
      # 直接将函数体配置在 function_for_item
      function_for_item=[my_add],
      import_attrs={"myadd": ["attr_a", "attr_b"]},
      # 这样会把 myadd 的结果，按照 slot=101 的形式，存在 'my_slots' 和 'my_signs' 两个 attr 中
      export_attrs={"myadd": [dict(slot=101, slots_attr="my_slots", signs_attr="my_signs")]},
    )

    ## 4. 迁移和更多样例
    参考文档 https://docs.corp.kuaishou.com/k/home/<USER>/fcADNWDG9b2Re86JAeTAPeYWK
    ```
    """

    self._add_processor(KaiFGAttrEnricher(kwargs))
    return self

  def attr_diff(self, **kwargs):
    """
    AttrDiffObserver
    -----
    比较一组 attr pair 的值, 将 diff 结果打印到日志中.

    参数配置
    -----
    `common_attrs`: [list[tuple]] 选配项, 需要比较的 common attr pair 列表.

    `item_attrs`: [list[tuple]] 选配项, 需要比较的 item attr pair 列表.

    `default_error_tolerance`: [float] 选配项, 浮点类型的 attr 允许的误差范围, 默认为 1e-5.

    调用示例
    ------
    ```Python
    .attr_diff(
      common_attrs=[(attr_a, attr_b) for attr_a, attr_b in zip(common_attrs_to_compare_a, common_attrs_to_compare_b)],
      item_attrs=[(attr_a, attr_b) for attr_a, attr_b in zip(item_attrs_to_compare_a, item_attrs_to_compare_b)],
      default_error_tolerance=compare_tolerance,
    )
    ```
    """
    conf = {}
    if "common_attrs" in kwargs:
      conf["common_attrs"] = []
      common_attr_pairs = kwargs.pop("common_attrs")
      for item in common_attr_pairs:
        assert len(item) == 2
        attr_pair = dict(attr_a=item[0], attr_b=item[1])
        conf["common_attrs"].append(attr_pair)

    if "item_attrs" in kwargs:
      conf["item_attrs"] = []
      item_attr_pairs = kwargs.pop("item_attrs")
      for item in item_attr_pairs:
        assert len(item) == 2
        attr_pair = dict(attr_a=item[0], attr_b=item[1])
        conf["item_attrs"].append(attr_pair)
    conf.update(kwargs)
    self._add_processor(AttrDiffObserver(conf))
    return self

  def attr_type(self, **kwargs):
    """
      AttrTypeObserver
      -----
      打印 attr 的数据类型, 以及是 user 侧 attr 或者是 item 侧 attr 的相关信息.

      参数配置
      -----
      `attrs`: [list[str]] 待打印的 attr 列表.

      调用示例
      -----
      ```python
      .attr_type(
        attrs=["attr1", "attr2", ...],
      )
      ```
    """
    self._add_processor(AttrTypeObserver(kwargs))
    return self
