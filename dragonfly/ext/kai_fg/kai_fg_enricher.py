#!/usr/bin/env python3
# coding=utf-8
"""
filename: kai_fg_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kaifg
author: <EMAIL>
date: 2023-03-13 15:23:30
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher

import collections
import inspect
import tempfile
import textwrap
import uuid
import os
import sys
import types


class KaiFGFVExtractSlotSignAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kaifg_extract_slot_sign"

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    if self._config.get('slots_output', None) and self._config.get('signs_output', None):
      attrs.add(self._config['slots_output'])
      attrs.add(self._config['signs_output'])
    if self._config.get('named_outputs', None):
      for item in self._config['named_outputs']:
        attrs.add(item['output_name'])
    return attrs

  @strict_types
  def _get_input_maps(self) -> dict:
    inputs = set()
    for config in self._config['inputs']:
      inputs.add(config['name'])

    input_maps = dict()
    for config in self._config.get('input_maps', []):
      if config['name'] in inputs:
        input_maps[config['name']] = config

    for col_name in inputs:
      if col_name not in input_maps:
        input_maps[col_name] = dict(
            name=col_name,
            source_type='context',
            attr_name=col_name.replace(':0', ''),
            common=self._config['common']
        )
      else:
        conf = input_maps[col_name]
        source_type = conf.get('source_type', 'context')
        check_arg(source_type in {
                  'flatkv', 'context'}, "input_maps: {} 有不支持的 source_type: {}.".format(col_name, source_type))
        if source_type == 'flatkv':
          check_arg('attr_name' in conf, 'flatkv input: {} 在 input_maps 缺少 `attr_name`'.format(col_name))
          check_arg('field_name' in conf, 'flatkv input: {} 在 input_maps 缺少 `field_name`'.format(col_name))
          check_arg('kconf_path' in conf, 'flatkv input: {} 在 input_maps 缺少 `kconf_path`'.format(col_name))
        attr_name = conf.get('attr_name', col_name.replace(':0', ''))
        is_input_common = conf.get('common', self._config['common'])
        input_maps[col_name] = dict(
            name=col_name,
            attr_name=attr_name,
            source_type=source_type,
            common=is_input_common
        )

    return input_maps

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config['common']:
      return set()
    else:
      return self._get_output_attrs()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config['common']:
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    inputs = set()
    input_maps = self._get_input_maps()
    for _, v in input_maps.items():
      if not v['common']:
        inputs.add(v['attr_name'])
    return inputs


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    input_maps = self._get_input_maps()
    inputs = set()
    for _, v in input_maps.items():
      if v['common']:
        inputs.add(v['attr_name'])
    return inputs


class FlatKvToJsonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "flatkv_to_json"

  @strict_types
  def _check_config(self) -> None:
    for attr_name in ["common", "from_attr", "kconf_path", "save_to"]:
      check_arg(attr_name in self._config, f"缺少 {attr_name} 配置")

  def get_input_attr_names(self, is_common: bool) -> set:
    if self._config["common"] != is_common:
      return set()
    return {self._config["from_attr"]}

  def get_output_attr_names(self, is_common: bool) -> set:
    if self._config["common"] != is_common:
      return set()
    return {self._config["save_to"]}

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.get_input_attr_names(True)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.get_input_attr_names(False)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attr_names(True)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attr_names(False)


class TestExtractFromProtobufEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "test_extract_from_protobuf"

  @strict_types
  def _check_config(self) -> None:
    for attr_name in ["user_info_from_attr"]:
      check_arg(attr_name in self._config, f"缺少 {attr_name} 配置")

  @strict_types
  def _output_common_attr_list(self) -> list:
    attr_list = ["KV.UserInfo.user_profile_v1.video_playing_stat.hetu_tag_level_info.hetu_level_one",
                 "KV.UserInfo.user_profile_v1.video_playing_stat.hetu_tag_level_info.hetu_level_two",
                 "KV.UserInfo.user_profile_v1.video_playing_stat.hetu_tag_level_info.hetu_level_three"]
    offset_list = [item + "@" for item in attr_list]
    return attr_list + offset_list

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_info_from_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._output_common_attr_list())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

class FlatKvAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_flatkv"

  @strict_types
  def _check_config(self) -> None:
    for attr_name in ["common", "from_attr", "kconf_path", "attrs"]:
      check_arg(attr_name in self._config, f"缺少 {attr_name} 配置")

  def get_input_attr_names(self, is_common: bool) -> set:
    if self._config["common"] != is_common:
      return set()
    return {self._config["from_attr"]}

  def get_output_attr_names(self, is_common: bool) -> set:
    if self._config["common"] != is_common:
      return set()
    attrs = set()
    for attr in self._config["attrs"]:
      if isinstance(attr, str):
        attrs.add(attr)
        continue
      check_arg(attr.get("name"), "no `name` found")
      attrs.add(attr.get("name"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.get_input_attr_names(True)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.get_input_attr_names(False)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attr_names(True)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attr_names(False)


class KaiFGAttrEnricher(LeafEnricher):
    InputAttr = collections.namedtuple(
        "InputAttr",
        [
            "attr_name",
            "attr_type",
            "common",
            "source_type",
            "kconf_path",
            "field_name",
            "pb_attr",
            "pb_field_path",
            "pb_skip_unset_field",
            "pb_mode",
            "force_default",
            "with_default",
            "default",
        ],
    )
    global_scope_id = 0

    @strict_types
    def __init__(self, config: dict):
        super().__init__(config)
        self.ignore_default_attrs = config.pop("ignore_default_attrs", [])
        self.skip_compilation_test = self._config.get(
            "skip_compilation_test", False if sys.version_info >= (3, 9) else True
        )
        # only compilation for checking, so use a random name
        self.random_module_name = uuid.uuid4().hex[0:10]
        if not self.skip_compilation_test:
            from kaifg_jit.fgcodegen.codegen import CodeGen

            gen = CodeGen()
            gen.clear()
            gen.set_module_name(self.random_module_name)

        # import attrs
        self.in_common_attrs = set()
        self.in_item_attrs = set()
        import_attrs = self._config.get("import_attrs", {})
        common_functions = self._config.get("function_for_common", [])
        item_functions = self._config.get("function_for_item", [])
        code_snippet = self._config.get("code", "")
        assert (
            len(common_functions) > 0 or len(item_functions) > 0
        ), "function_for_common 和 function_for_item 至少设置一个"
        # 载入配置的代码
        user_module = None
        if code_snippet != None and len(code_snippet) > 0:
            user_module = types.ModuleType(self.random_module_name)
            exec(code_snippet, user_module.__dict__)
        common_snippet, common_func_objects = self._process_functions(
            common_functions, True, user_module
        )
        item_snippet, item_func_objects = self._process_functions(item_functions, False, user_module)
        # 插入预先 import 的代码
        if code_snippet == None or len(code_snippet) == 0:
            code_snippet = "from typing import List, Tuple, Set, Dict\nfrom kaifg_jit.decorators import *\n\n"
        self._config["code"] = (
            code_snippet + "\n" + common_snippet + "\n" + item_snippet
        )
        # 检查代码是否能通过编译
        self._check_code_by_compilation(
            self._config["code"],
            self._config.get("function_for_common")
            + self._config.get("function_for_item"),
        )
        # import attrs
        common_func_import_attrs = self._process_import_attrs(
            common_func_objects, import_attrs, True
        )
        item_func_import_attrs = self._process_import_attrs(
            item_func_objects, import_attrs, False
        )
        self._merge_import_attrs(common_func_import_attrs, item_func_import_attrs)

        # export attrs
        self.export_common_attrs = set()
        self.export_item_attrs = set()
        export_attrs = self._config.get("export_attrs", {})
        # 上面 import 确认过了 common_function 和 item_function 名字没有冲突的
        self._process_export_attrs(common_func_objects, export_attrs, True)
        self._process_export_attrs(item_func_objects, export_attrs, False)
        # 为每个 KaiFG processor 加上 scope 信息
        self._add_scope_id()

    def _add_scope_id(self):
        self._config["scope"] = KaiFGAttrEnricher.global_scope_id
        KaiFGAttrEnricher.global_scope_id += 1

    def _get_type_str(self, fg_type, python_type):
        from kaifg_jit.fgir.var_type import VECTOR, Prim, LIST

        type_to_str = {
            VECTOR[Prim.INT]: "int64",
            VECTOR[Prim.DOUBLE]: "float64",
            VECTOR[Prim.STRING]: "string",
            VECTOR[LIST[Prim.INT]]: "int64_list",
            VECTOR[LIST[Prim.DOUBLE]]: "float64_list",
            VECTOR[LIST[Prim.STRING]]: "string_list",
        }
        assert fg_type in type_to_str, f"type: {python_type} not support yet"
        return type_to_str[fg_type]

    def _safe_load_source_code(self, func_obj):
        try:
            func_src = textwrap.dedent(inspect.getsource(func_obj))
        except OSError:
            raise RuntimeError(
                "在 playground 不能使用 function object 来定义 enrich_attr_by_kaifg, 需要使用把自定义函数通过字符串传入来定义, 参考文档"
            )
        except Exception as e:
            raise RuntimeError(f"An unspected error occurred: {e}")
        return func_src

    def _safe_insert_func_src(self, func_mapping, func_name, func_src):
        if func_name in func_mapping:
            base_src = func_mapping.get(func_name)
            if base_src != func_src:
                raise RuntimeError(
                    f"function {func_name} is defined in multiple places: {base_src}\nvs.\n{func_src}"
                )
        else:
            func_mapping[func_name] = func_src

    def _process_functions(self, functions, is_common_function, user_module):
        """
        函数有两种配置方式：
        1. 直接配置函数对象 function_for_common/item = [my_add, my_div]
        2. 配置函数名 function_for_common/item = ["my_add", "my_div"] + 配置代码字符串 code = "...."
           代码字符串中有 my_add 和 my_div 两个函数
        这个函数把这两种配置方式都转成 2 的方法给 C++.
        """
        assert isinstance(
            functions, list
        ), "function_for_common/function_for_item should be list"
        from kaifg_jit.parse_function_meta import parse_function_calls

        new_code = ""
        entry_functions = []
        entry_function_src = {}
        inline_function_src = {}
        for f in functions:
            assert inspect.isfunction(f) or isinstance(
                f, str
            ), "function in function_for_common/function_for_item should be function object or string"
            if inspect.isfunction(f):
                func = f
                func_src = self._safe_load_source_code(func)
                # 解析 inline 函数调用
                func_calls = parse_function_calls(func, inspect.getmodule(func))
                for sub_func in func_calls:
                    src = self._safe_load_source_code(sub_func)
                    # 这里会对所有的 inline function 做去重, 保证不同的 inline function 要是不一样的名字
                    self._safe_insert_func_src(inline_function_src, sub_func.__name__, src)
                if func.__code__.co_name != func.__name__:
                    # 函数源码中的函数名，应该和 func.__name__ 一致；因为通常 upgrader 会改__name__
                    func_src = func_src.replace(
                        func.__code__.co_name, func.__name__, 1
                    )
                self._safe_insert_func_src(entry_function_src, func.__name__, func_src)
            else:
                assert user_module != None, "user_module should not be None"
                func = getattr(user_module, f)
                assert func != None, f"function name: {f} is not found in code snippet"
                assert inspect.isfunction(func), f"object name: {f} is not a function"
            entry_functions.append(func)
        # 写入 code
        for _, src in inline_function_src.items():
            new_code += "\n\n" + src
        for _, src in entry_function_src.items():
            new_code += "\n\n" + src
        # 函数名写入配置
        if is_common_function:
            self._config["function_for_common"] = [f.__name__ for f in entry_functions]
        else:
            self._config["function_for_item"] = [f.__name__ for f in entry_functions]
        return new_code, entry_functions

    def _check_code_by_compilation(self, code_snippet, entry_functions):
        # 通过编译检查代码是否合法
        if not self.skip_compilation_test:
            from kaifg_jit.jit import compile_file_to_lib

            with tempfile.NamedTemporaryFile(
                mode="w", prefix="kaifg_code", suffix=".py"
            ) as code_file, tempfile.NamedTemporaryFile(
                mode="wb", prefix="kaifg_lib", suffix=".so"
            ) as lib_file:
                clang_path = os.getenv(
                    "CLANG_PATH", "/data/soft/distcc/clang-14.0.6/bin"
                )
                code_file.write(code_snippet)
                code_file.flush()
                compile_file_to_lib(
                    code_file.name,
                    entry_functions,
                    clang_path=os.path.join(clang_path, "clang"),
                    output_path=lib_file.name,
                )

    def _process_list_input_attrs(
        self, input_attrs, parsed_from_annotation, common_function
    ):
        # 解析 list 类型的 input_attrs 映射配置，包括 list[str] 和 list[dict]
        assert isinstance(input_attrs, list)
        assert len(parsed_from_annotation) == len(
            input_attrs
        ), f"len(parsed_from_annotation): {len(parsed_from_annotation)} != len(input_attrs): {len(input_attrs)}"
        ret = {}
        for i, (param_name, attr_types) in enumerate(parsed_from_annotation.items()):
            this_param_config = (
                dict(attr_name=input_attrs[i])
                if isinstance(input_attrs[i], str)
                else input_attrs[i]
            )
            attr_name = this_param_config.get("attr_name", param_name)
            attr_type = self._get_type_str(attr_types["fg"], attr_types["python"])
            common = this_param_config.get("common", common_function)
            source_type = this_param_config.get("source_type", "context")
            kconf_path = this_param_config.get("kconf_path", "")
            field_name = this_param_config.get("field_name", "")
            pb_attr = this_param_config.get("pb_attr", "")
            pb_field_path = this_param_config.get("pb_field_path", "")
            pb_skip_unset_field = this_param_config.get("pb_skip_unset_field", False)
            pb_mode = this_param_config.get("pb_mode", "")
            force_default = this_param_config.get("force_default", False)
            with_default = attr_types.get("with_default", False)
            default = attr_types.get("default", None)
            default = this_param_config.get("default", default)
            if force_default:
                assert with_default, "`with_default` must set when `force_default`"
            input_attr = self.InputAttr(
                attr_name,
                attr_type,
                common,
                source_type,
                kconf_path,
                field_name,
                pb_attr,
                pb_field_path,
                pb_skip_unset_field,
                pb_mode,
                force_default,
                with_default,
                default,
            )
            ret[param_name] = input_attr._asdict()
        return ret

    def _process_dict_input_attrs(
        self, input_attrs, parsed_from_annotation, common_function
    ):
        # 解析 dict 类型的 input_attrs 映射配置
        assert isinstance(input_attrs, dict)
        ret = {}
        for param_name, attr_types in parsed_from_annotation.items():
            attr_type = self._get_type_str(attr_types["fg"], attr_types["python"])
            force_default = False
            with_default = attr_types.get("with_default", False)
            default = attr_types.get("default", None)
            if param_name in input_attrs:
                attr_name = input_attrs[param_name].get("attr_name", param_name)
                common = input_attrs[param_name].get("common", common_function)
                kconf_path = input_attrs[param_name].get("kconf_path", "")
                field_name = input_attrs[param_name].get("field_name", "")
                source_type = input_attrs[param_name].get("source_type", "context")
                pb_attr = input_attrs[param_name].get("pb_attr", "")
                pb_field_path = input_attrs[param_name].get("pb_field_path", "")
                pb_skip_unset_field = input_attrs[param_name].get(
                    "pb_skip_unset_field", False
                )
                pb_mode = input_attrs[param_name].get("pb_mode", "")
                # 如果配置了默认值,则使用配置的默认值覆盖函数签名中的默认值
                force_default = input_attrs[param_name].get("force_default", False)
                default = input_attrs[param_name].get("default", default)
                if force_default:
                    assert with_default, "`with_default` must set when `force_default`"
            else:
                # 如果没有配置字段映射，则使用入参名直接作为映射
                attr_name = param_name
                common = common_function
                kconf_path = ""
                field_name = ""
                source_type = "context"
                pb_attr = ""
                pb_field_path = ""
                pb_skip_unset_field = False
                pb_mode = ""
            ret[param_name] = self.InputAttr(
                attr_name,
                attr_type,
                common,
                source_type,
                kconf_path,
                field_name,
                pb_attr,
                pb_field_path,
                pb_skip_unset_field,
                pb_mode,
                force_default,
                with_default,
                default,
            )._asdict()
        return ret

    def _collect_input_attrs(self, remapped_attrs, common_function):
        for _, attr in remapped_attrs.items():
            attr_name = attr["attr_name"]
            common = attr["common"]
            source_type = attr["source_type"]
            if source_type != "flatkv":
                attr.pop("kconf_path")
                attr.pop("field_name")
            if source_type != "protobuf":
                attr.pop("pb_attr")
                attr.pop("pb_field_path")
                attr.pop("pb_skip_unset_field")
                attr.pop("pb_mode")
            with_default = attr["with_default"]
            if not with_default:
                attr.pop("default")
                attr.pop("with_default")
            if attr_name in self.ignore_default_attrs:
                continue
            input_attr_name = attr_name
            if source_type == "protobuf":
                input_attr_name = attr["pb_attr"]
                assert (
                    input_attr_name and attr["pb_field_path"]
                ), "`pb_attr` and `pb_field_path` must be set when source_type is `protobuf`."
            if common_function:
                assert common, "common function can only use common attr."
                self.in_common_attrs.add(input_attr_name)
            else:
                (
                    self.in_common_attrs.add(input_attr_name)
                    if common
                    else self.in_item_attrs.add(input_attr_name)
                )

    def _process_import_attrs(self, functions, import_attrs, common_function):
        from kaifg_jit.parse_function_meta import parse_param_annotation

        # 解析函数并生成缺省的 import_attrs
        modified_import_attrs = {}
        if len(functions) == 0:
            return {}
        for func in functions:
            func_name = func.__name__
            signature = inspect.signature(func)
            func_params = parse_param_annotation(signature)
            remapped_attrs = import_attrs.get(func_name, {})
            if isinstance(remapped_attrs, list):
                modified_attrs = self._process_list_input_attrs(
                    remapped_attrs, func_params, common_function
                )
            else:
                modified_attrs = self._process_dict_input_attrs(
                    remapped_attrs, func_params, common_function
                )
            self._collect_input_attrs(modified_attrs, common_function)
            modified_import_attrs[func_name] = modified_attrs
        return modified_import_attrs

    def _merge_import_attrs(self, common_import_attrs, item_import_attrs):
        for func_name, attrs in item_import_attrs.items():
            if func_name in common_import_attrs:
                raise ValueError(
                    "function: {} already defined in function_for_common".format(
                        func_name
                    )
                )
            else:
                common_import_attrs[func_name] = attrs
        self._config["import_attrs"] = common_import_attrs

    def _process_export_attrs(self, functions, export_attrs, common_function):
        from kaifg_jit.parse_function_meta import parse_return_annotation
        from kaifg_jit.fgir.var_type import VECTOR, Prim, LIST

        for func in functions:
            func_name = func.__name__
            assert func_name in export_attrs, f"func: {func_name} not in export_attrs"
            outputs = export_attrs[func_name]
            ret_types = parse_return_annotation(inspect.signature(func))
            assert len(outputs) == len(
                ret_types
            ), f"output number should equal to number of annotation, in function: {func_name}"
            modified_export_attrs = []
            for item, type_ in zip(outputs, ret_types):
                attr_type = self._get_type_str(type_["fg"], type_["python"])
                if isinstance(item, str):
                    # 简单配置输出 attr，写到 context 中
                    modified_export = dict(
                        attr_name=item,
                        common=common_function,
                        attr_type=attr_type,
                    )
                else:
                    # 规范一下 attr_type
                    common = item.get("common", common_function)
                    if common_function:
                        assert common, "common function can only generate common attr"
                    # 规范一下 attr_type
                    modified_export = dict(
                        attr_name=item.get("attr_name", None),
                        common=common,
                        attr_type=attr_type,
                        slots_attr=item.get("slots_attr", None),
                        signs_attr=item.get("signs_attr", None),
                        slot=item.get("slot", None),
                        slot_key_type=item.get("slot_key_type", None),
                        ignore_slot=item.get("ignore_slot", False)
                    )

                # 规范一下 attr_type
                attr_name = modified_export.get("attr_name")
                slots_attr = modified_export.get("slots_attr")
                signs_attr = modified_export.get("signs_attr")
                if not attr_name and (not slots_attr and not signs_attr):
                    assert False, "neither general attr nor slot/sign attr"
                if attr_name and (slots_attr and signs_attr):
                    assert False, "cannot be general attr and slot/sign attr"
                if attr_name:
                    # delete redundant slot related attributes. To make config shorter
                    modified_export.pop("slots_attr", None)
                    modified_export.pop("signs_attr", None)
                    modified_export.pop("slot", None)
                    modified_export.pop("slot_key_type", None)
                    if common_function:
                        self.export_common_attrs.add(attr_name)
                    else:
                        self.export_item_attrs.add(attr_name)
                if slots_attr and signs_attr:
                    # slot sign 类型只支持 list[int] / int 类型输出
                    assert type_["fg"] in (
                        VECTOR[LIST[Prim.INT]],
                        VECTOR[Prim.INT],
                    ), f"slot sign type: {type_['python']} not support yet"
                    # 必须设置 slot
                    slot = modified_export.get("slot", None)
                    assert (
                        slot is not None and slot >= 0
                    ), f"函数 {func_name} 的输出属于哪个 slot 必须设置"
                    modified_export.pop("attr_name", None)
                    if common_function:
                        self.export_common_attrs.add(slots_attr)
                        self.export_common_attrs.add(signs_attr)
                    else:
                        self.export_item_attrs.add(slots_attr)
                        self.export_item_attrs.add(signs_attr)
                modified_export_attrs.append(modified_export)
            self._config["export_attrs"][func_name] = modified_export_attrs

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "enrich_attr_by_kaifg"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        return self.in_common_attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        return self.in_item_attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        return self.export_common_attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        return self.export_item_attrs
