#!/usr/bin/env python3
# coding=utf-8
"""
filename: mmu_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,mmu api mixin
author: <EMAIL>
date: 2020-07-19 10:58:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .mmu_retriever import *
from .mmu_enricher import *


class MmuApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 mmu 相关的 Processor 接口
  - MmuBatchRetriever
  - MmuPhotoSearchRetriever
  - MmuRelatedVideoSearchRetriever
  - MmuTriggerEnricher
  """

  def retrieve_by_mmu_batch(self, **kwargs):
    """
    MmuBatchRetriever
    ------
    从 MmuBatchRetrieval 服务召回相似视频

    参数配置
    ------
    `kess_service`: [string] [动态参数] MmuBatchRetrieval 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `source_photo_attr`: [string] 获取种子视频列表 的 CommonAttr 名称，支持 int / int_list 类型

    `reason`: [int] 召回原因

    `data_type`: [string] 设置请求中的 data_type 枚举类型

    `source_type`: [string] 设置请求中的 source_type 枚举类型

    `top_k`: [int] [动态参数] 每个 photo_id 召回相似视频的个数，默认不做限制（以 client 端为准）

    调用示例
    ------
    ``` python
    .retrieve_by_mmu_batch(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      source_photo_attr = "source_photoid_list",
      reason = 999,
    )
    ```
    """
    self._add_processor(MmuBatchRetriever(kwargs))
    return self

  def retrieve_by_mmu_photo_search(self, **kwargs):
    """
    MmuPhotoSearchRetriever
    ------
    从 MmuPhotoSearch 服务召回相似视频

    参数配置
    ------
    `kess_service`: [string] [动态参数] MmuPhotoSearch 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `source_photo_attr`: [string] 获取种子视频列表 的 CommonAttr 名称，支持 int 类型

    `reason`: [int] 召回原因


    调用示例
    ------
    ``` python
    .retrieve_by_mmu_photo_search(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      source_photo_attr = "source_photoid_list",
      reason = 999
    )
    ```
    """
    self._add_processor(MmuPhotoSearchRetriever(kwargs))
    return self

  def retrieve_by_mmu_related_video_search(self, **kwargs):
    """
    MmuRelatedVideoSearchRetriever
    ------
    从 mmu 召回相关视频（如剧集），召回结果有顺序关系

    参数配置
    ------
    `kess_service`: [string] [动态参数] MmuRelatedVideoSearch 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `mmu_id`: [string] [动态参数] 在推荐理由服务中生成的 mmu_id

    `source_photo_id`: [int] [动态参数] 源 photo_id

    `source_photo_author_id`: [int] [动态参数] 源 photo 的 author_id

    `page_number`: [int] [动态参数] 召回分页结果的第几页

    `page_size`: [int] [动态参数] 召回结果的分页大小

    `save_page_down_to_attr`: [string] 如果不为空，则将是否有后续页的信息存入该 common_attr 中（类型为 int），可缺省

    `reason`: [int] 召回原因


    调用示例
    ------
    ``` python
    .retrieve_by_mmu_related_video_search(
      kess_service = "grpc_mmu_relatedVideoSearcher",
      timeout_ms = 200,
      mmu_id = "{{mmu_id}}",
      source_photo_id = "{{photo_id}}",
      source_photo_author_id = "{{author_id}}",
      page_number = "{{page_number}}",
      page_size = "{{page_size}}",
      reason = 999
    )
    ```
    """
    self._add_processor(MmuRelatedVideoSearchRetriever(kwargs))
    return self

  def fetch_mmu_trigger(self, **kwargs):
    """
    MmuTriggerEnricher
    ------
    从 MmuRelatedVideoTrigger 服务获取视频推荐理由

    参数配置
    ------
    `kess_service`: [string] [动态参数] MmuPhotoSearch 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `output_attr_name`: [string] 保存推荐理由的 ItemAttr 名称，默认为 string 类型

    `save_mmu_id_to_attr`: [string] 保存 mmu_id 的 ItemAttr 名称，默认为 string 类型

    调用示例
    ------
    ``` python
    .fetch_mmu_trigger(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      output_attr_name = "trigger_reason"
    )
    ```
    """
    self._add_processor(MmuTriggerEnricher(kwargs))
    return self

  def retrieve_by_elastic_search(self, **kwargs):
    """
    KalamiElasticSearchRetriever
    ------
    从 elastic search 里面进行召回操作，需要自己写 json 的召回语句。 item_key 来自于随机值或者从某个 es 的属性中确认，score 来自 es 召回的 score。

    参数配置
    ------
    `clusters`: [string][动态参数] 必填, es 的 ip:port

    `reason`: [int] 召回原因，默认为 0

    `index`: [string][动态参数] 表示 es 的索引，详细 es 的说明可以见官方文档，这里不做赘述。

    `type`: [string] 表示 es 的类型，同上

    `scroll`: [string] 表示 es 的scroll查询超时时间，如5m，同上
    
    `query`: [string][动态参数] 表示 es 的查询语句，同上

    `timeout_ms`: [int] 超时时间，单位 ms，默认值 200

    `itemkey_from` : [string] 在不使用随机 itemkey 的情况下，指定 itemkey 来自 source 中的哪个字段，该字段必须为 int 类型

    `item_attrs`: [array] string_list 类型，表示获取的 es item_attr 名字，不填即为空。

    `item_attrs_type`: [array] string_list，表示获取的 item_attrs 类型，和上面一一对应，如果为空，则表示都为 string 类型.

    调用示例
    ------
    ``` python
    .retrieve_by_elastic_search(
      clusters="localhost:8000,localhost:8001",
      reason=200,
      index="index",
      query="query",
      type="type",
      item_attrs=["thread", "pod_name", "filename"]
    )
    ```
    """
    self._add_processor(KalamiElasticSearchRetriever(kwargs))
    return self

  def normalize_common(self, **kwargs):
    """
    MmuCommonNormalizeEnricher
    ------
    L2 norm for common attr

    参数配置
    ------
    `input_common_attr`: [string] input CommonAttr 名称，默认为 string 类型

    `output_common_attr`: [string] output ItemAttr 名称，默认为 string 类型

    调用示例
    ------
    ``` python
    .normalize_common(
      input_common_attr = "double_list_common_attr_name"
      output_common_attr = "output_double_list_common_attr_name"
    )
    ```
    """
    self._add_processor(MmuCommonNormalizeEnricher(kwargs))
    return self

  def common_list_filter(self, **kwargs):
    """
    MmuCommonListFilterEnricher
    ------
    filter val for common attr

    参数配置
    ------
    `input_common_attr`: [string] input CommonAttr 名称，默认为 string 类型

    `filter_val`: [int] 过滤的值，暂时只支持int类型

    `output_common_attr`: [string] output ItemAttr 名称，默认为 string 类型

    调用示例
    ------
    ``` python
    .common_list_filter(
      input_common_attr = "int_list_common_attr_name"
      filter_val = -1
      output_common_attr = "output_int_list_common_attr_name"
    )
    ```
    """
    self._add_processor(MmuCommonListFilterEnricher(kwargs))
    return self
