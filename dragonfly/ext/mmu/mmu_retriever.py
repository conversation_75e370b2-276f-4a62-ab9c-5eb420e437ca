#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever


class MmuBatchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("source_photo_attr"), str) and self._config["source_photo_attr"],
              "source_photo_attr 需为非空字符串")


class MmuPhotoSearchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_photo_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("source_photo_attr"), str) and self._config["source_photo_attr"],
              "source_photo_attr 需为非空字符串")


class MmuRelatedVideoSearchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_related_video_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kess_service", "mmu_id", "source_photo_id", "source_photo_author_id", "page_number", "page_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("save_page_down_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("mmu_id"), str) and self._config["mmu_id"],
              "mmu_id 需为非空字符串")
    for key in ["source_photo_id", "source_photo_author_id", "page_number", "page_size"]:
      if isinstance(self._config[key], int):
        check_arg(self._config[key] >= 0, key + " 为整数时需大于等于 0")
      if isinstance(self._config[key], str):
        check_arg(self._config[key].startswith("{{") and self._config[key].endswith("}}"),
                  key + " 为字符串时需满足动态参数 {{}} 格式")

class KalamiElasticSearchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_elastic_search"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("index")))
    attrs.update(self.extract_dynamic_params(self._config.get("query", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("clusters", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("itemkey_start", 20000)))
    return attrs
