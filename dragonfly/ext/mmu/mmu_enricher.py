#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class MmuTrigger<PERSON>n<PERSON>er(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fetch_mmu_trigger"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_attr_name"))
        attr = self._config.get("save_mmu_id_to_attr", "")
        if attr:
            attrs.add(attr)
        return attrs;



    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @strict_types
    def _check_config(self) -> None:
        check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
                  "kess_service 需为非空字符串")
        check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
                  "timeout_ms 需为大于 0 的整数")
        check_arg(isinstance(self._config.get("output_attr_name"), str) and self._config["output_attr_name"],
                  "output_attr_name 需为非空字符串")

class MmuCommonNormalizeEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "normalize_common"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self._config.get("input_common_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_common_attr"))
        return attrs;

class MmuCommonListFilterEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "common_list_filter"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("filter_val")))
        attrs.update(self._config.get("input_common_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_common_attr"))
        return attrs;
