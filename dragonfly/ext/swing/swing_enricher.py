#!/usr/bin/env python3
# coding=utf-8
"""
filename: swing_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, embedding server with calculator enricher
author: <EMAIL>
date: 2021-08-05 16:45:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class SwingI2ISimAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_swing_i2i_sim"

  @strict_types
  def is_async(self) -> bool:
    return False 

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("import_data_type", 0) == 2:
      for key in ["reduce_users_in_common_attr", "item_2hop_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("import_data_type", 0) == 1:
      for key in ["item_2hop_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["users_attr", "reduce_users_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["sim_item_attr", "sim_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class SwingFilterNeighorsAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_swing_neighors"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["users_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["reduce_users_to_common_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["reduce_users_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class SwingI2ISimAttrV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_swing_i2i_sim_v2"

  @strict_types
  def is_async(self) -> bool:
    return False 

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("import_data_type", 0) == 2:
      for key in ["reduce_users_in_common_attr", "item_2hop_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("import_data_type", 0) == 1:
      for key in ["item_2hop_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["users_attr", "reduce_users_attr", "watch_time_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["sim_item_attr", "sim_value_attr", "sim_aug_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

