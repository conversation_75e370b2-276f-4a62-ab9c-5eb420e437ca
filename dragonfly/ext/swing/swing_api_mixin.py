#!/usr/bin/env python3
# coding=utf-8
"""
filename: swing_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kgnn api mixin
author: <EMAIL>
date: 2021-08-05 16:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .swing_enricher import *

class SwingApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 Swing 相关更新 processor 接口
  """

  def calc_swing_i2i_sim(self, **kwargs):
    """
    SwingI2ISimAttrEnricher
    ------
    更新 item to item 的相似性

    参数配置:
    ------
    `import_data_type` : [int] 2hop item list 的获取方式，
                              =1, 从 item attr 中获取
                              =2, 从 common attr 中获取

    `users_attr` : [list[int]] item 对应的 1-hop user neighbor list

    `reduce_users_attr` : [list[int]] item attr, 删减后的 item 1-hop user neighbor list

    `reduce_users_in_common_attr` : [list[int]] common attr, reduce 后的 item 之间共现的 user neighbor list,
                                  import_data_type=2 时生效

    `item_2hop_attr` : [list[int]] item 对应的 2-hop item neighbor list , 长度必须为 neighbors list 的整数倍,
                                  import_data_type=1 时从 item attr 获取，与 reduce_users_attr 形成映射关系
                                  =2 时从 common attr 获取, 与 reduce_users_in_common_attr 形成映射关系

    `sim_item_attr` : [list[double]] item to item 的相似性 neighbors

    `sim_value_attr` : [list[double]] item to item 的相似性

    `alpha` : [double] i2i sim 计算超参

    调用示例
    ------
    ``` python
    .calc_swing_sim_result(
      users_attr="user_neighbor",
      reduce_users_attr="user_neighbor_filtered",
      item_2hop_attr="2hop_item_neigbhor",
      sim_item_attr='sim_neighbors',
      sim_value_attr='i2i_sim',
      alpha=1.0)
    ```
    """

    self._add_processor(SwingI2ISimAttrEnricher(kwargs))
    return self

  def calc_swing_i2i_sim_v2(self, **kwargs):
    """
    SwingI2ISimAttrV2Enricher
    ------
    更新 item to item 的相似性

    参数配置:
    ------
    `import_data_type` : [int] 2hop item list 的获取方式，
                              =1, 从 item attr 中获取
                              =2, 从 common attr 中获取

    `users_attr` : [list[int]] item 对应的 1-hop user neighbor list

    `watch_time_list_attr`: [list[int]] item 对应的 1-hop user watch time

    `reduce_users_attr` : [list[int]] item attr, 删减后的 item 1-hop user neighbor list

    `reduce_users_in_common_attr` : [list[int]] common attr, reduce 后的 item 之间共现的 user neighbor list,
                                  import_data_type=2 时生效

    `item_2hop_attr` : [list[int]] item 对应的 2-hop item neighbor list , 长度必须为 neighbors list 的整数倍,
                                  import_data_type=1 时从 item attr 获取，与 reduce_users_attr 形成映射关系
                                  =2 时从 common attr 获取, 与 reduce_users_in_common_attr 形成映射关系

    `sim_item_attr` : [list[double]] item to item 的相似性 neighbors

    `sim_value_attr` : [list[double]] item to item 的相似性
    `sim_aug_value_attr_` : [list[double]] item to item 的增强相似性

    `alpha` : [double] i2i sim 计算超参

    调用示例
    ------
    ``` python
    .calc_swing_sim_result(
      users_attr="user_neighbor",
      watch_time_list_attr="watch_time_list",
      reduce_users_attr="user_neighbor_filtered",
      item_2hop_attr="2hop_item_neigbhor",
      sim_item_attr='sim_neighbors',
      sim_value_attr='i2i_sim',
      alpha=1.0)
    ```
    """

    self._add_processor(SwingI2ISimAttrV2Enricher(kwargs))
    return self
  
  def filter_swing_neighors(self, **kwargs):
    """
    SwingFilterNeighorsAttrEnricher
    ------
    每个 item 有多个 user neighbor，如果 item 的 user neighbor 是 item 特有（与任意其他 item 都不共享），这删除该 user neighbor

    参数配置:
    ------
    `users_attr` : [list[int]] item 对应的 1-hop user neighbor list

    `reduce_users_attr` : [list[int]] item attr, 删减后的 item 1-hop user neighbor list

    调用示例
    ------
    ``` python
    .filter_swing_neighors(
      users_attr="user_neighbor",
      reduce_users_attr="user_neighbor_filtered")
    ```
    """

    self._add_processor(SwingFilterNeighorsAttrEnricher(kwargs))
    return self
