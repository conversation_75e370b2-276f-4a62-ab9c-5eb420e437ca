#!/usr/bin/env python3
# coding=utf-8
"""
filename: se_sug_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, se_sug api mixin
author: <EMAIL>
date: 2022-03-16 16:35:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .se_sug_retriever import *

class SeSugApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 search suggest 业务相关的 Processor 接口

  背景：目前主要为 搜索补全 场景提供召回及排序服务所需 dragon 插件，如 pair wise 样本生成

  现有维护人：wangmengxuan fuyanzhuo

  """

  def retrieve_pair_wise_from_raw_sample_package(self, **kwargs):
    """
    SugPairWiseRawSamplePackageRetriever
    ------
    从 RawSamplePackage 触发出样本。组合点击item与非点击item生成pairwise样本。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 RawSamplePackage。

    `input_label`: RawSamplePackage 中哪个 attr 是记录的 label ，当前仅支持单label

    `save_label_to`: 用于存储 label 的 attr 的名字

    `pid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 photo id，抽取后将作为该样本的 item_id，默认为 pId。

    `uid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 UserId 值，默认为 uId。

    `device_id_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 DeviceId 值，默认为 dId。

    `save_locale_to`: [string] 用于存储 locale 的 attr 的名字，默认为 rLocale。

    `save_channel_to`: [string] 用于存储 channel 的 attr 的名字，默认为 rChannel。

    `item_type`: [int] 触发的 item type，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `save_common_attr_names_to`: [string] 存储 common_attr 名字列表的 common attr。

    `save_next_common_attr_names_to`: [string] 存储 next_common_attr 名字列表的 common attr。

    `save_item_attr_names_to`: [string] 存储 item_attr 名字列表的 common attr。

    `time_unit`: [string] RawSamplePackage 的 timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    `use_sub_biz`: [bool] label定义中是否包含sub biz，默认为 false。

    `feature_suffix`: [string] 组 pair-wise 时一方的 item attr 名字保持不变，另一方 attr 加上此后缀

    调用示例
    ------
    ``` python
    .retrieve_pair_wise_from_raw_sample_package(
      from_extra_var="raw_sample_package",
      input_label="label",
      "save_label_to"="label",
      "feature_suffix"="_2",
      save_common_attr_names_to="common_attrs"
      save_next_common_attr_names_to="next_common_attrs"
      save_item_attr_names_to="item_attrs")
    ```

    """
    self._add_processor(SugPairWiseRawSamplePackageRetriever(kwargs))
    return self

