#!/usr/bin/env python3
"""
filename: se_sug_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for se_sug
author: <EMAIL>
date: 2022-03-16 16:40:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class SugPairWiseRawSamplePackageRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_pair_wise_from_raw_sample_package"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for save_attr in ["save_common_attr_names_to", "save_next_common_attr_names_to", "save_item_attr_names_to"]:
      if save_attr in self._config:
        attrs.add(self._config[save_attr])
    attrs.add(self._config.get("save_locale_to", "rLocale"))
    attrs.add(self._config.get("save_channel_to", "rChannel"))
    return attrs

