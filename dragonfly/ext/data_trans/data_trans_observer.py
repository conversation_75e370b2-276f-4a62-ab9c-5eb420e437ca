#!/usr/bin/env python3
"""
filename: data_trans_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for data_trans
author: <EMAIL>
date: 2020-12-06 18:32:00
"""

import operator

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver


class CommonIdtObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_idt"
