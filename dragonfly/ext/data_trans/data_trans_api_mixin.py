#!/usr/bin/env python3
# coding=utf-8
"""
filename: data_trans_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kuiba api mixin
author: <EMAIL>
date: 2020-12-06 18:32:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .data_trans_observer import *
from .data_trans_enricher import *

class DataTransApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 data_trans 相关的 Processor 接口，主要用于数据传输。
  涉及到的功能包括业务数据在就近实例之间进行传输等等。。
  """
  def start_common_idt_serving(self, **kwargs):
    """
    CommonIdtObserver

    参数
    ------
    `idt_server_kess_name`: [string] 指定对外提供 idt 服务时的 kess_name，默认由服务名拼接 "_idt" 得出

    `rpc_thread_num`: [int] 指定 idt 服务的线程数，默认值 100

    `rpc_server_port`: [int] 指定 idt 服务的初始端口号，默认从 PORT_INFO.json 中获取

    `server_pod_name`: [string] 当前实例名，默认从环境变量中获取

    示例
    ------
    ``` python
    .start_common_idt_serving(rpc_thread_num=60)
    ```
    """
    self._add_processor(CommonIdtObserver(kwargs))
    return self

  def streaming_sender(self, **kwargs):
    """
    StreamingSenderEnricher
    ------
    增量数据发送 sender，一般与 receiver 配套使用传输数据(如embedding)。默认使用 btq 方式传输，传输过程中支持版本校验等

    参数配置
    ------
    `channel_name`: [string] 必配项，为 "btq topic" 或 "grpc_name" 对应 `channel_type`

    `input_item_attr_names`: [list] 必配项，从哪些 item attr 读取待发送信息，必须是 graph 中有的节点名

    `channel_type`: [string] 选配项，必须为 "btq" 或 "grpc"，数据发送方式，默认为 "btq"

    `input_data_type`: [string] 选配项，输入的数据精度。必须为 "float" 或 "half" 默认为 "float"

    `send_data_type`: [string] 选配项，发送的数据精度。必须为 "float" 或 "half" 默认为 "half"

    示例
    ------
    ``` python
    .streaming_sender(channel_name="test_btq_topic",
               input_item_attr_names=["name1", "name2"],
               channel_type="btq",
               input_data_type="float",
               send_data_type="half")
    ```
    """
    self._add_processor(StreamingSenderEnricher(kwargs))
    return self

  def streaming_receiver(self, **kwargs):
    """
    StreamingReceiverEnricher
    ------
    增量数据 receiver，一般与 sender 配套使用传输数据(如embedding)。默认使用 btq 方式传输，传输过程中支持版本校验等

    参数配置
    ------
    `channel_name`: [string] 必配项，为 "btq topic" 或 "grpc_name" 对应 `channel_type`

    `channel_type`: [string] 选配项，必须为 "btq" 或 "grpc"，数据接收方式，默认为 "btq"

    `embedding_inputs`: [list] of [dict] 必配项，接收数据配置，包括：
      `name` [string] 必配，embedding 名称，对应于 sender 中 `input_item_attr_names`，必须是 graph 中有的节点名
      `length` [int]  必配，embedding 长度
      `max_capacity` [int]  必配，最多存储的 embedding 数量。若接收的数量超出限制，随机逐出
      `data_type` [string] 选配 {"float", "half"} 默认为 "half"
      `transpose` [bool] 选配，接收的 shape 为 [bs, length]，如果 graph 中 shape 为 [length, bs]，需要配置 true

    `snapshot_interval_second`: [int] 选配项，>=3，累计此段时间数据作为一次全量版本。默认值 180s 三分钟。
      若 sender 中有全量版本号，以版本号区分不同版本；若无，以此间隔区分不同版本

    示例
    ------
    ``` python
    .streaming_receiver(channel_name="test_btq_topic",
               channel_type="btq",
               embedding_inputs=[
                 {
                   "name": "embeddding_1",
                   "length": 64,
                   "max_capacity": 15000000,
                   "data_type": "half",
                   "transpose": true
                 },
                 {
                   "name": "embeddding_2",
                   "length": 64,
                   "max_capacity": 15000000,
                   "data_type": "half",
                   "transpose": true
                 }
               ],
               snapshot_interval_second=180)
    ```
    """
    self._add_processor(StreamingReceiverEnricher(kwargs))
    return self

  def version_based_sender(self, **kwargs):
    """
    VersionBasedSenderEnricher
    ------
    全量数据发送 sender，一般与 receiver 配套使用传输数据(如embedding)。默认使用 btq 方式传输，传输过程中支持版本校验等

    参数配置
    ------
    `channel_name`: [string] 必配项，为 "btq topic" 或 "grpc_name" 对应 `channel_type`

    `input_item_attr_names`: [list] 必配项，从哪些 item attr 读取待发送信息，必须是 graph 中有的节点名

    `channel_type`: [string] 选配项，必须为 "btq" 或 "grpc"，数据发送方式，默认为 "btq"

    `input_data_type`: [string] 选配项，输入的数据精度。必须为 "float" 或 "half" 默认为 "float"

    `send_data_type`: [string] 选配项，发送的数据精度。必须为 "float" 或 "half" 默认为 "half"


    示例
    ------
    ``` python
    .version_based_sender(channel_name="test_btq_topic",
               input_item_attr_names=["name1", "name2"],
               channel_type="btq",
               input_data_type="float",
               send_data_type="half")
    ```
    """
    self._add_processor(VersionBasedSenderEnricher(kwargs))
    return self

  def version_based_receiver(self, **kwargs):
    """
    VersionBasedReceiverEnricher
    ------
    全量数据发送 receiver，一般与 sender 配套使用传输数据(如embedding)。默认使用 btq 方式传输，传输过程中支持版本校验等

    参数配置
    ------
    `channel_name`: [string] 必配项，为 "btq topic"

    示例
    ------
    ``` python
    .version_based_receiver(channel_name="test_btq_topic")
    ```
    """
    self._add_processor(VersionBasedReceiverEnricher(kwargs))
    return self

  def updater(self, **kwargs):
    """
    UpdaterEnricher
    ------
    数据更新，从 receiver 接收数据，更新到 uni-predict

    参数配置
    ------
    `trigger_source`: [string] 必配项，更新触发来源，为 "streaming_receiver" 或 "version_based_receiver"

    `key`: [string] 必配项，与 uni-predict processor key 一致

    `bucket_topk_name` 选配项，若使用 `bucket_topk`，需要配置 graph 中 node name

    `snapshot_interval_second`: [string] 选配项，当来源为 "streaming_receiver"，定时更新间隔，默认 180s

    示例
    ------
    ``` python
    .updater(trigger_source="streaming_receiver",
             key="test",
             bucket_topk_name="bucket_topk",
             snapshot_interval_second="180")
    .updater(trigger_source="version_based_receiver",
             key="test")
    ```
    """
    self._add_processor(UpdaterEnricher(kwargs))
    return self