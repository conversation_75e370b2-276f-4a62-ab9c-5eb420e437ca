import operator

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class StreamingSenderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "streaming_sender"

  @strict_types
  def _check_config(self) -> None:
    channel_name = self._config.get("channel_name")
    check_arg(channel_name and channel_name != "", '`channel_name` 必须配置，且不为空字符串')

    input_item_attr_names = self._config.get("input_item_attr_names")
    assert type(input_item_attr_names) is list, "❌streaming_sender 中 input_item_attr_names 必须为 list"
    check_arg(input_item_attr_names and len(input_item_attr_names) >= 1, '`input_item_attr_names` 必须配置')

    if "channel_type" in self._config:
      channel_type = self._config.get("channel_type")
      check_arg(channel_type and channel_type in {"btq", "grpc"}, '`channel_type` 必须选自 {"btq", "grpc"}')

    if "input_data_type" in self._config:
      input_data_type = self._config.get("input_data_type")
      check_arg(input_data_type and input_data_type in {"double", "float", "half", "int"}, '`input_data_type` 必须选自 {"double", "float", "half", "int"}')

    if "send_data_type" in self._config:
      send_data_type = self._config.get("send_data_type")
      check_arg(send_data_type and send_data_type in {"double", "float", "half", "int"}, '`send_data_type` 必须选自 {"double", "float", "half", "int"}')

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._config.get("input_item_attr_names", [])


class StreamingReceiverEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "streaming_receiver"

  @strict_types
  def _check_config(self) -> None:
    channel_name = self._config.get("channel_name")
    check_arg(channel_name and channel_name != "", '`channel_name` 必须配置，且不为空字符串')

    embedding_inputs = self._config.get("embedding_inputs")
    assert type(embedding_inputs) is list, "streaming_receiver 中 embedding_inputs 必须为 list"
    check_arg(embedding_inputs and len(embedding_inputs) >= 1, '`embedding_inputs` 必须配置')
    for input in embedding_inputs:
      name = input["name"]
      check_arg(name and name != "", '`name` 必须配置，且不为空字符串')
      length = input["length"]
      assert type(length) is int, "streaming_receiver 中 length 必须为 int"
      check_arg(length >= 1, "length 必须为正整数")
      if "data_type" in input:
        data_type = input["data_type"]
        check_arg(data_type and data_type in {"half", "float"}, '`data_type` 必须选自 {"half", "float"}')

    if "snapshot_interval_second" in self._config:
      snapshot_interval_second = self._config.get("snapshot_interval_second")
      assert type(snapshot_interval_second) is int, "streaming_receiver 中 snapshot_interval_second 必须为 int"
      check_arg(snapshot_interval_second >= 3, "snapshot_interval_second 必须大于等于3")

class VersionBasedSenderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "version_based_sender"

  @strict_types
  def _check_config(self) -> None:
    channel_name = self._config.get("channel_name")
    check_arg(channel_name and channel_name != "", '`channel_name` 必须配置，且不为空字符串')

    input_item_attr_names = self._config.get("input_item_attr_names")
    assert type(input_item_attr_names) is list, "❌version_based_sender 中 input_item_attr_names 必须为 list"
    check_arg(input_item_attr_names and len(input_item_attr_names) >= 1, '`input_item_attr_names` 必须配置')

    if "channel_type" in self._config:
      channel_type = self._config.get("channel_type")
      check_arg(channel_type and channel_type in {"btq", "grpc"}, '`channel_type` 必须选自 {"btq", "grpc"}')

    if "input_data_type" in self._config:
      input_data_type = self._config.get("input_data_type")
      check_arg(input_data_type and input_data_type in {"double", "float", "half", "int"}, '`input_data_type` 必须选自 {"double", "float", "half", "int"}')

    if "send_data_type" in self._config:
      send_data_type = self._config.get("send_data_type")
      check_arg(send_data_type and send_data_type in {"double", "float", "half", "int"}, '`send_data_type` 必须选自 {"double", "float", "half", "int"}')

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._config.get("input_item_attr_names", [])

class VersionBasedReceiverEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "version_based_receiver"

  @strict_types
  def _check_config(self) -> None:
    channel_name = self._config.get("channel_name")
    check_arg(channel_name and channel_name != "", '`channel_name` 必须配置，且不为空字符串')

class UpdaterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "updater"

  @strict_types
  def _check_config(self) -> None:
    trigger_source = self._config.get("trigger_source")
    check_arg(trigger_source and trigger_source in {"streaming_receiver", "version_based_receiver"}, '`trigger_source` 必须选自 {"streaming_receiver", "version_based_receiver"}')

    key = self._config.get("key")
    check_arg(key and key != "", '`key` 必须配置，取值与`uni-predict`相同')

    if trigger_source == "streaming_receiver":
      snapshot_interval_second = self._config.get("snapshot_interval_second")
      assert type(snapshot_interval_second) is int, "streaming_receiver 中 snapshot_interval_second 必须为 int"
      check_arg(snapshot_interval_second >= 3, "snapshot_interval_second 必须大于等于3")
