#!/usr/bin/env python3
# coding=utf-8
"""
filename: recollm_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, recollm api mixin
author: <EMAIL>
date: 2025-03-11 15:35:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class RecollmGenerateEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "recollm_generate"

    @strict_types
    def _check_config(self) -> None:
        check_arg(self._config.get("recollm_kess_name"), "`recollm_kess_name` 是必选项")
        check_arg(self._config.get("max_tokens"), "`max_tokens` 是必选项")
        sampling_config = self._config.get("sampling_config")
        if sampling_config:
            beam_width = sampling_config.get("beam_width", 1)
            top_k = sampling_config.get("top_k")
            top_p = sampling_config.get("top_p")
            if beam_width > 1:
                check_arg(
                    all(cond is None for cond in [top_k, top_p]),
                    f"beam_width: {beam_width}, 和 top_k: {top_k}, top_p: {top_p}, 目前不支持同时设置 beam_width 和 top_k/top_p",
                )

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("input_token_ids", "recollm_input_token_ids"))
        if self._config.get("encoder_input_token_ids"):
            attrs.add(self._config.get("encoder_input_token_ids"))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for output in [
            "output_token_ids",
            "output_token_ids_index",
        ]:
            attrs.add(self._config.get(output, f"recollm_{output}"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        return set()

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        return set()
