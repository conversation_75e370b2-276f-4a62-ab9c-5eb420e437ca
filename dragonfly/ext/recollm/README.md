RecollmGenerateEnricher
------
通过 RPC 访问 Recollm service 获取 generate tokens

参数配置
------

**request**  
`request_type`: [string] recollm 服务端模型类型,可选值 `["decoder_only" | "encoder_decoder" | "encoder_only"]` 默认 `"decode_only"`  
`recollm_kess_name`: [string]  
`is_wide_input_token_ids`: [bool] input_token_ids 类型是否为 `int64`, 默认 `false`  
`input_token_ids`: [string] 从哪个 common attr 中获取 input_token_ids, 默认 `"recollm_input_token_ids"`  
`is_wide_encoder_input_token_ids`: [bool] encoder_input_token_ids 类型是否为 `int64`, 默认 `false`  
`encoder_input_token_ids`:[string][optional] 从哪个 common attr 中获取 encoder_input_token_ids, 默认 `"recollm_encoder_input_token_ids"`  
`max_tokens`: [int]  

`sampling_config`: [dict]
- `beam_width`: [int]
- `top_k`: [int]
- `top_p`: [float]
- `temperature`: [float]
- `num_return_sequences`: [int]

**response**  
`output_token_ids`: [string] 将所有输出 sequence tokens concat 之后的结果写到哪个 common attr, 默认 `recollm_output_token_ids`  
`output_token_ids_index`: [string] 每个输出 sequence 在 `"output_token_ids"` 的起始索引写到哪个 common attr, 默认 `"recollm_output_token_ids_index"`

调用示例
------
```python
.recollm_generate(
    input_token_ids = "input_tokens",
    max_tokens = 6,
    sampling_config = dict(
        beam_width = 3,
        num_return_sequences = 3,
    )
)
```