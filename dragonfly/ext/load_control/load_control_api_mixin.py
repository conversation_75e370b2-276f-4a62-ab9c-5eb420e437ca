#!/usr/bin/env python3
# coding=utf-8

# from dragonfly.ext.offline.offline_api_mixin import OfflineApiMixin
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from ..load_control.load_control_enricher import *

class LoadControlApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 mix_rank 相关的 Processor 接口
  """

  def load_controller(self, **kwargs):
    self._add_processor(SlideMixRankLoadControllerEnricher(kwargs))
    return self

  