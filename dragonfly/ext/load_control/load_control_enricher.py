#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class SlideMixRankLoadControllerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "load_controller"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attrs"):
      attrs = {name for name in self._config["output_common_attrs"]}
    return attrs