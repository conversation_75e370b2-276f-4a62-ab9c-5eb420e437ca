#!/usr/bin/env python3
"""
filename: oversea_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for oversea
author: <EMAIL>
date: 2020-07-23 18:45:00
"""

import operator
import itertools

from ...common_leaf_util import ArgumentError, strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher
from ..common.common_leaf_enricher import CommonRecoDelegateEnricher
class OverseaLocalCacheAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_local_cache_attr"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)
  
  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if not self._is_common_attr:
      return set()
    if self._config["op"] == "read":
      return { self._config["key_attr_name"] }
    if self._config["op"] == "write":
      return { self._config["key_attr_name"], self._config["value_attr_name"] }
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if not self._is_common_attr:
      return set()
    if self._config["op"] == "read":
      return { self._config["value_attr_name"] }
    if self._config["op"] == "write":
      return set()
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._is_common_attr:
      return set()
    if self._config["op"] == "read":
      return { self._config["key_attr_name"] }
    if self._config["op"] == "write":
      return { self._config["key_attr_name"], self._config["value_attr_name"] }
    return set()
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._is_common_attr:
      return set()
    if self._config["op"] == "read":
      return { self._config["value_attr_name"] }
    if self._config["op"] == "write":
      return set()
    return set()

class OverseaRedisListRangeCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_redis_value_by_list_range"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["redis_key", "range_point_attr"]:
      attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_common_attr"]:
      attrs.add(self._config[key])
    return attrs

class OverseaRodisItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_action_list_from_rodis"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for payload_config in self._config["item_attrs"].values():
      for key in ["len_limit", "after_ts", "before_ts", "min_len"]:
        if key in payload_config:
          attrs.update(self.extract_dynamic_params(payload_config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in self._config["item_attrs"]:
      attrs.add(key)
      if "ts" in self._config["item_attrs"][key]:
        attrs.add(self._config["item_attrs"][key]["ts"])
      if "aid" in self._config["item_attrs"][key]:
        attrs.add(self._config["item_attrs"][key]["aid"])
    return attrs

class OverseaSamplePoolEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_sample_pool"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["bucket_attr"])
    if self._config["mod"] == "load":
      attrs.add(self._config["photo_map_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config["mod"] == "save":
      attrs.add(self._config["history_item_keys_attr"])
      attrs.add(self._config["photo_map_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config["mod"] == "save":
      for key in ["insert_flag_attr"]:
        attrs.add(self._config[key])
      for key in ["record_item_attrs"]:
        attrs.update(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config["mod"] == "load":
      for key in ["record_item_attrs"]:
        attrs.update(self._config[key])
    return attrs

class OverseaInjectContextInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_inject_context_info"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["context_info_attr"])
    for context_attr_config in self._config["inject_configs"]:
      attrs.add(context_attr_config["context_attr"])
    return attrs

  @property
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class OverseaInjectContextInfoWithBlackListEnricher(OverseaInjectContextInfoEnricher):
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "blacklist_kconf_key" in self._config:
      ret.update(self.extract_dynamic_params(self._config["blacklist_kconf_key"]))
    return ret

class OverseaWeightStringSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "split_oversea_weight_str"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["weight_string_attr"] }

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for c in self._config.get("output_common_attrs", []):
      if type(c) is str:
        attrs.add(c)
      else:
        attrs.add(c["as"])
    return attrs

class OverseaItemAttrFallbackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_item_attr_fallback"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for fallback_config in self._config["item_attrs"]:
      attrs.update(fallback_config["input_item_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for fallback_config in self._config["item_attrs"]:
      attrs.add(fallback_config["output_item_attr"])
    return attrs
class KsibCompatDoubleListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_compat_double_list"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config["input_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(self._config["output_attrs"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config["input_attrs"]) == len(self._config["output_attrs"]), f"{self.get_type_alias()} 的 input attrs 与 output attrs 需要为等长 list")
    for index, input_attr in enumerate(self._config["input_attrs"]):
      if input_attr == self._config["output_attrs"][index]:
        raise ArgumentError(f"{self.get_type_alias()} input_attrs 与 output_attrs 不能有一样的值")

class OverseaOnlineMioFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_oversea_online_feature"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_attrs", "save_item_sign_to", "save_item_slot_to"]:
      if key in self._config:
        if isinstance(self._config[key], list):
          ret.update(self._config[key])
        else:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["common_attrs", "save_user_sign_to", "save_user_slot_to"]:
      if key in self._config:
        if isinstance(self._config[key], list):
          ret.update(self._config[key])
        else:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["oversea_reader_info_attr"]])

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["oversea_photo_info_attr"]])
class OverseaPoolRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_pool_rank"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)
  
  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["pool_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    if self._is_common_attr:
      attrs.add(self._config["score_attr_name"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if not self._is_common_attr:
      return set()
    return set([self._config["percent_attr_name"]])

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._is_common_attr:
      return set()
    return set([self._config["score_attr_name"]])
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._is_common_attr:
      return set()
    return set([self._config["percent_attr_name"]])

class OverseaGlobalVariableCacheEnricher(LeafEnricher):
    name_space_cache_size_map = {}
    @strict_types
    def _check_config(self) -> None:
        # check namespace
        check_arg("namespace" in self._config,
                  f"{self.get_type_alias()} 缺少 namespace")
        namespace = self._config["namespace"]
        # check cache_size
        if "cache_size" in self._config:
            check_arg(namespace not in self.name_space_cache_size_map or self.name_space_cache_size_map[
                      namespace] == self._config["cache_size"], f"{self.get_type_alias()} 在 namespace {namespace} 配置了冲突的 cache_size")
            self.name_space_cache_size_map[namespace] = self._config["cache_size"]
            check_arg(self.name_space_cache_size_map[namespace] > 0,
                      f"{self.get_type_alias()} 配置的 cache_size {self.name_space_cache_size_map[namespace]} <= 0")
        elif self._config.get("mode", "") != "read":
            check_arg(namespace in self.name_space_cache_size_map,
                      f"{self.get_type_alias()} 缺少 cache_size")
        # check update_param
        if self._config.get("mode", "") != "read":
            update_param = self._config.get("update_param", [])
            check_arg(isinstance(update_param, list) and len(update_param) > 0,
                      f"{self.get_type_alias()} 缺少 update_param，或 update_param 不为 list")
            for _update_param in update_param:
                check_arg("update_method" in _update_param,
                          f"{self.get_type_alias()} update_param 缺少 update_method 配置")
                check_arg(_update_param["update_method"] in {
                          "replace", "max", "min", "moving_avg"}, f"{self.get_type_alias()} update_method 可选值为 replace/max/min/moving_avg，当前为 {_update_param['update_method']}")
        # check common_attr, list_key_attr, list_value_attr
        common_attr = self._config.get("common_attr", [])
        check_arg(isinstance(common_attr, list),
                  f"{self.get_type_alias()} common_attr 应该为 list")
        check_arg(len(common_attr) > 0 or "list_value_attr" in self._config,
                  f"{self.get_type_alias()} common_attr/list_value_attr 均为空")
        check_arg(
            isinstance(self._config.get("list_key_attr", ""), str) and isinstance(
                self._config.get("list_value_attr", ""), str),
            f"{self.get_type_alias()} list_key_attr/list_value_attr 应该为 str")
        if "list_key_attr" in self._config:
            check_arg(self.extract_dynamic_params(self._config.get("list_key_attr"))
                      != "", f"{self.get_type_alias()} list_key_attr 只能为动态参数")
        if "list_value_attr" in self._config:
            check_arg(self.extract_dynamic_params(self._config.get("list_value_attr"))
                      != "", f"{self.get_type_alias()} list_value_attr 只能为动态参数")
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "oversea_global_variable_cache_enricher"
    @strict_types
    def depend_on_items(self) -> bool:
        return False
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        if "group_name" in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get("group_name")))
        if "list_key_attr" in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get("list_key_attr")))
        if "list_value_attr" in self._config:
            attrs.update(self.extract_dynamic_params(
                self._config.get("list_value_attr")))
        if "mode" in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get("mode", "")))
        if self._config.get("mode", "") != "read":
            attrs.update(self._config.get("common_attr", []))
        return attrs
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        if self._config.get("mode", "") == "write":
            return attrs
        attrs.update(self._config.get("common_attr", []))
        if "list_value_attr" in self._config:
            attrs.update(self.extract_dynamic_params(
                self._config.get("list_value_attr")))
        return attrs

class OverseaRecoEnsembleScoreExpEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_ensemble_score_exp"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1 或 2")
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["regulator", "smooth", "cliff_ratio", "cliff_height"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala", "cliff_ratio", "cliff_height"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      save_score_to = channel.get("save_score_to")
      if save_score_to:
        attrs.add(save_score_to)
    attrs.add(self._config["output_attr"])
    return attrs

class OverseaBucketEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_rr_bucket_ensemble_score"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1 或 2")
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for x in self._config["channels"]:
      attrs.add(x["name"])
    attrs.add(self._config["bucket_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { self._config["weight_string_attr"],
              self._config["bucket_num_attr"],
              self._config["bucket_points_attr"]}
    for key in ["regulator", "smooth", "cliff_ratio", "cliff_height"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala", "cliff_ratio", "cliff_height"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs
    
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

class GenBeamSearchListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_beam_search_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["slide_alpha", "ranking_beta", "slide_pow",
                "ranking_weight", "index_weight", "index_rate", "decay_alpha",
                "decay_beta", "output_len", "beam_size", "candidate_len",
                "item_type", "reward_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["ranking_score_attr", "next_score_attr"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_common_attr"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_item_attr"]:
      attrs.add(self._config.get(key))
    return attrs

class OverseaTransformItemAttrFromCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_transform_item_attr_from_common_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["input_common_attr", "map_weight_string_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["input_item_attr"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "item_attr_map" in self._config:
      for second_map in self._config["item_attr_map"]:
        attrs.add(second_map["output_item_attr"])
    else:
      attrs.add(self._config["output_item_attr"])
    return attrs

class OverseaGenIndexItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_gen_index_item_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["input_index_common_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in self._config["mappings"]:
      if "input_common_attr_name" in key:
        ret.add(key["input_common_attr_name"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["input_item_attr"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "mappings" in self._config:
      for second_map in self._config["mappings"]:
        attrs.add(second_map["output_item_attr_name"])
    return attrs

class OverseaLinerDifferentialEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_gen_liner_differential_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["input_x_list_common_attr", "input_y_list_common_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["input_x_item_attr"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_y_item_attr"] }

class OverseaTagIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_oversea_tag_id"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config["tag_attrs"])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class OverseaAuditMarkEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_audit_mark_match"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "bucket_attr" in self._config:
      attrs.add(self._config["bucket_attr"])
    if "weight_config" in self._config:
      attrs.add(self._config["weight_config"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "comparing_attr_name" in self._config:
      attrs.add(self._config["comparing_attr_name"])
      
    for key in ["audit_type_name", "audit_mark_name"]:
      attrs.add(self._config[key])

    for key in ["audit_bucket_name"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class KsibTagDiversityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_tag_diversity"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for tag_diversity_config in self._config["tag_diversity_configs"]:
      for key in ["enable", "top_k"]:
        attrs.update(self.extract_dynamic_params(tag_diversity_config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["tag_diversity_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for tag_diversity_config in self._config["tag_diversity_configs"]:
      for key in ["attr_name"]:
        attrs.add(tag_diversity_config[key])
    attrs.add(self._config["tag_diversity_attr"])
    return attrs

class OverseaSimilarityItemsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_similarity_items"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "similarity_threshold" in self._config:
      ret.update(self.extract_dynamic_params(self._config["similarity_threshold"]))
    if "max_redundancy_count" in self._config:
      ret.update(self.extract_dynamic_params(self._config["max_redundancy_count"]))
    if "allow_empty_embedding" in self._config:
      ret.update(self.extract_dynamic_params(self._config["allow_empty_embedding"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["export_item_attr"] }

class OverseaPositiveFeedbackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_positive_feedback"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["positive_items_from_attr"])
    if "similarity_threshold" in self._config:
      ret.update(self.extract_dynamic_params(self._config["similarity_threshold"]))
    if "reserve_similar_count" in self._config:
      ret.update(self.extract_dynamic_params(self._config["reserve_similar_count"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(gen_attr_name_with_common_attr_channel(self._config["pos_item_embedding_attr"], self._config["positive_items_from_attr"]))
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["export_item_attr"] }

class KsibNegativeFeedbackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_negative_feedback"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["negative_items_from_attr"])
    if "filter_threshold" in self._config:
      ret.update(self.extract_dynamic_params(self._config["filter_threshold"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(gen_attr_name_with_common_attr_channel(self._config["neg_item_embedding_attr"], self._config["negative_items_from_attr"]))
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["export_item_attr"] }

class KsibInterestExploreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_interest_explore"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["expore_items_from_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(gen_attr_name_with_common_attr_channel(self._config["explore_item_embedding_attr"], self._config["explore_items_from_attr"]))
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["export_item_attr"] }

class OverseaBucketScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_bucket_score"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { self._config["weight_string_attr"],
              self._config["use_max_bucket_size_attr"],
              self._config["regulator_attr"]}
    if self._config["bucket_type"] in ["age", "duration"]:
      attrs.add(self._config["bucket_num_attr"])
      attrs.add(self._config["bucket_points_attr"])
    if self._config["bucket_type"] == "duration" and "des_filter_quantile_attr" in self._config:
      attrs.add(self._config["des_filter_quantile_attr"])
    if "cut_ratio" in self._config:
      attrs.add(self._config["cut_ratio"])
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["score_attr"], self._config["bucket_attr"] }


class OverseaCrossQueueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cross_queue"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { self._config["left_item_str"],
              self._config["right_item_str"],
              self._config["out_item_str"]}
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { "fr_multi_one", "fr_multi_two" }


class KsibOneDimensionalKmeansEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_one_dimensional_kmeans"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for kmeans_config in self._config["kmeans_configs"]:
      attrs.update(self.extract_dynamic_params(kmeans_config["cluster_num"]))
      if "max_iter_num" in kmeans_config:
        attrs.update(self.extract_dynamic_params(kmeans_config["max_iter_num"]))
      if "enable" in kmeans_config:
        attrs.update(self.extract_dynamic_params(kmeans_config["enable"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for kmeans_config in self._config["kmeans_configs"]:
      attrs.add(kmeans_config["output_attr_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for kmeans_config in self._config["kmeans_configs"]:
      attrs.add(kmeans_config["input_attr_name"])
    return attrs

class KsibBitWiseAndEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_bit_wise_and"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["bitwise_b_val"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["bitwise_a_item_attr"])
    return attrs

class OverseaBoostRuleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_boost_rule"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("formula_version", 0) == 0:
      for key in ["const_score", "weight", "temperature"]:
        attrs.update(self.extract_dynamic_params(self._config[key]))

    for key in ["xtr_threshold", "xtr_rank_threshold"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["boost_score_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["boost_score_attr"])
    attrs.add(self._config["xtr_score_attr"])
    if "xtr_rank_attr" in self._config:
      attrs.add(self._config["xtr_rank_attr"])
    return attrs
  
class OverseaScalingBoostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_scaling_boost"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    boost_queue_configs = self._config.get("boost_queue_configs", None)
    check_arg(boost_queue_configs, f"{self.get_type_alias()} 缺少 boost_queue_configs 配置")
    for queue_config in boost_queue_configs.values():
      check_arg(queue_config.get("factors"), f"{self.get_type_alias()} 缺少 factors 配置")
      scale_bound = queue_config.get("scale_bound", None)
      if scale_bound:
        check_arg(isinstance(scale_bound, list) and len(scale_bound) == 2, f"{self.get_type_alias()} scale_bound 不为长度为 2 的 list")
    scale_bound = self._config.get("scale_bound", None)
    check_arg(scale_bound, f"{self.get_type_alias()} 缺少 scale_bound 配置")
    check_arg(isinstance(scale_bound, list) and len(scale_bound) == 2, f"{self.get_type_alias()} scale_bound 不为长度为 2 的 list")
    boost_score_attr = self._config.get("boost_score_attr")
    check_arg(boost_score_attr, f"{self.get_type_alias()} 缺少 boost_score_attr 配置")
    check_arg(isinstance(boost_score_attr, str), f"{self.get_type_alias()} boost_score_attr 不为 string")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    default_scale_bound = self._config.get("scale_bound")
    if default_scale_bound:
      attrs.update(self.extract_dynamic_params(default_scale_bound[0]))
      attrs.update(self.extract_dynamic_params(default_scale_bound[1]))
    for boost_queue_config in self._config["boost_queue_configs"].values():
      if boost_queue_config.get("scale_bound"):
        scale_bound = boost_queue_config["scale_bound"]
        attrs.update(self.extract_dynamic_params(scale_bound[0]))
        attrs.update(self.extract_dynamic_params(scale_bound[1]))
    
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["boost_score_attr"])
    if self._config.get("save_queue_factor_to_item_attr", False):
      for queue in self._config["boost_queue_configs"].keys():
        attrs.add(f"{queue}_factor")
        attrs.add(f"{queue}_factor_before_rescale")
      
    return { self._config["boost_score_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["boost_score_attr"])
    for boost_queue_config in self._config["boost_queue_configs"].values():
      for factor in boost_queue_config["factors"]:
        attrs.add(factor)
    
    return attrs
  

class OverseaMultiBoostRuleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_multi_boost_rule"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("formula_version", 0) == 0:
      for key in ["const_score_str", "weight_str", "temperature_str"]:
        attrs.add(self._config[key])

    for key in ["xtr_score_attr_str", "xtr_rank_attr_str", "xtr_rank_threshold_str"]:
      attrs.add(self._config[key])

    for key in ["enable_reduce_score", "reduce_weight", "reduce_temperature"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["boost_score_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["boost_score_attr"])
    return attrs

class OverseaPositionAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_position_attr_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_items_attrs(self) -> set:
    return set(self._config.get("import_item_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("export_common_attrs", []))

class OverseaListItemsSlotsToCommonSlotsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_list_items_slots_to_common_slots"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_items_attrs(self) -> set:
    return set(self._config.get("import_item_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("export_common_attrs", []))

class OverseaEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_ensemble_score"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1 或 2")
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["regulator", "smooth", "cliff_ratio", "cliff_height",
                "hyper_params", "enable_hyper_param", "quantile_kv",
                "multi_mode", "local_weight", "global_weight", "hyper_params_split"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala", "enable_step", "enable_global", "cliff_point", "split_hyper"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class OverseaMultiplyRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_multiply_rank"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["weight_map_str"])
    for key in ["const_map_str", "temperature_map_str", "rank_threshold_map_str",\
                "user_bias_temperature_str", "formula_version_map_str",\
                "normalization_version_map_str", "sigmoid_temperature_map_str","rles_temperature_str", "rles_second_order_list_str"]:
      if key in self._config:
        attrs.add(self._config[key])

    for key in ["enable_reduce_score", "reduce_weight", "reduce_temperature",\
                "formula_version", "normalization_version", "user_bias_suffix",\
                "enable_different_shape"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    attrs.update(self._config.get("possible_import_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["base_score_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["base_score_attr"])
    attrs.update(self._config.get("possible_import_item_attrs", []))
    return attrs

class OverseaTagExtractByTypeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_tag_extract_by_type"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["extract_type_list"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["type_item_attr", "tag_item_attr"]:
      attrs.add(self._config[key])

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class OverseaAggregateTagWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_aggregate_tag_weight"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["tag_item_attr", "weight_item_attr"]:
      attrs.add(self._config[key])

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_tag_common_attr", "output_weight_common_attr"]:
      attrs.add(self._config[key])

    return attrs

class OverseaScoresIntegradeEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_scores_integrade"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["first_input_attr"])
    attrs.add(self._config["second_input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["alpha"]))
    if "mode" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["mode"]))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    # check_arg(self._config.get("mode"), f"{self.get_type_alias()} 缺少 mode 配置")
    check_arg(self._config.get("alpha"), f"{self.get_type_alias()} 缺少 alpha 配置")

class OverseaBetaDistScoreEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_beta_dist_score"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      save_score_to = channel.get("save_score_to")
      if save_score_to:
        attrs.add(save_score_to)
    output_attr = self._config.get("output_attr")
    if output_attr:
        attrs.add(output_attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "beta_a", "beta_b"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs


  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1")
    # check_arg(self._config.get("mode"), f"{self.get_type_alias()} 缺少 mode 配置")
    # check_arg(self._config.get("alpha"), f"{self.get_type_alias()} 缺少 alpha 配置")
    
class OverseaQuantileDiscountEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_quantile_discount"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["quantile", "discount_coefficient"]:
      attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

class OverseaCrossXtrQuantileDiscountEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_cross_xtr_quantile_discount"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["input_attr"])
    attrs.update(self._config.get("possible_import_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["cross_xtrs_str"]:
      if key in self._config:
        attrs.add(self._config[key])
    for key in ["discount_coefficient"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

class OverseaNuMultiplyRankEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_nu_multiply_rank"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["base_score_attr"])
    attrs.update(self._config.get("possible_import_item_attrs", []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["xtr_temperature_str", "xtr_const_score_str", "xtr_normed_str", "alpha", "eps"]:
      if key in self._config:
        attrs.add(self._config[key])
    # attrs.update(self.extract_dynamic_params(self._config["enable_min_max_scale"]))
    return attrs

class OverseaCorrectRankScoreEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_correct_rank_score"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["score_rank_diff_threshold", "score_delta"]:
      attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["pre_score_attr"])
    attrs.add(self._config["post_score_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_score_attr"])
    return attrs

class OverseaCalcPercentileEnricher(LeafEnricher):
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_xtr_percentile"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([cfg["attr_name"] for cfg in self._config["xtr_configs"]])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update([attr for cfg in self._config["xtr_configs"] for attr in cfg["output_common_attrs"]])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for cfg in self._config["xtr_configs"]:
      for percent in cfg["percentiles"]:
        check_arg(percent >= 0.0 and percent <=1.0, f"{self.get_type_alias()} 的 percentiles 需要在 [0.0, 1.0] 之间")
      check_arg(len(cfg["percentiles"]) == len(cfg["output_common_attrs"]), f"{self.get_type_alias()} 的 percentiles 与 output_common_attrs 需要为等长 list")

class KsibProfileFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_profile_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["reader_info_attr", "filter_time_s"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for k in ["reader_info_attr"]:
      if k in self._config:
        ret.add(self._config[k])
    return set()

class OverseaSelectTopNIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_select_topn_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["source_fingerprint_list_attr"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self._config["save_to_item_context"]:
      for k in ["output_attr"]:
        if k in self._config:
          ret.add(self._config[k])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for field in ["item_fingerprint_attr"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config["save_to_item_context"]:
      for k in ["output_attr"]:
        if k in self._config:
          ret.add(self._config[k])
    return ret

class OverseaGsuWithItemIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_gsu_with_item_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("use_mmu_tag")))
    for key in ["colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["bucket_attr"])
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))

    for key in ["sorted_item_idx_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OverseaGsuWithFingerprintEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_gsu_with_fingerprint"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("use_mmu_tag")))
    for field in [
        "source_fingerprint_list_attr",
        "colossus_pid_attr",
        "author_id_attr",
        "tag_attr",
        "play_time_attr",
        "duration_attr",
        "timestamp_attr"]:
      if field in self._config:
        ret.add(self._config[field])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for field in ["item_fingerprint_attr", "bucket_attr"]:
      if field in self._config:
        ret.add(self._config[field])

    for key in ["channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))

    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OverseaAdModelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_by_ad_model"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["timeout_ms"]))
    for cmd_config in self._config["cmd_configs"]:
      for key in ["cmd_name", "cmd_key", "cmd_value_num"]:
        attrs.update(self.extract_dynamic_params(cmd_config[key]))
      for predict_attr in cmd_config["predict_attrs"]:
        attrs.update(self.extract_dynamic_params(predict_attr["predict_type"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["ad_item_id"])
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cmd_config in self._config["cmd_configs"]:
      for predict_attr in cmd_config["predict_attrs"]:
        attrs.add(predict_attr["attr_name"])
    return attrs

class OfflineStatisticDenseFeatureOptEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "extract_with_statistic_dense_feature_opt"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        for key in ["reader_info_attr"]:
            if key in self._config:
                ret.add(self._config[key])
        for config in self._config.get("match_common_attrs", []):
            ret.add(config)
        return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        ret = set()
        for key in ["context_info_attr"]:
            if key in self._config:
                ret.add(self._config[key])
        for config in self._config.get("match_item_attrs", []):
            ret.add(config)
        for config in self._config.get("match_fields", ["authorid", "mmuclusterid", "mmuclusterv2id", "mmuprimarytagid"]):
            ret.add(config)
        return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        ret = set()
        for key in ["item_statistic_dense_feature_output"]:
            if key in self._config:
                ret.update(self._config.get(key, []))
        return ret

class OfflineStatisticDenseFeatureColossusEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "extract_with_statistic_dense_feature_opt"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        ret.add(self._config["colossus_pid_attr"])
        return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        ret = set()
        for config in self._config.get("match_fields", ["authorid", "ksib_tag_id"]):
            ret.add(config)
        for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "label_attr"]:
          if key in self._config:
            ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))

        return ret

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        ret = set()
        for key in ["item_statistic_dense_feature_output"]:
            if key in self._config:
                ret.update(self._config.get(key, []))
        return ret

class KsibDistanceClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_calc_distance_cluster"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_arg(isinstance(self._config.get("cluster_center_prefix_attr"), str), "cluster_center_prefix_attr 不为 str")
    ret.add(self._config["cluster_center_prefix_attr"])
    check_arg(isinstance(self._config.get("cluster_center_num_attr"), int), "cluster_center_num_attr 不为 int")
    ret.add(self._config["cluster_center_num_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_cluster_attr"] }

class OverseaSimpleMultiplyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "oversea_simple_multiply"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    item_attrs = self._config["input_item_attrs"]
    for item_attr in item_attrs:
      ret.add(item_attr)

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attr = self._config["output_item_attr"]
    ret.add(attr)
    return ret

class OverseaEmbeddingSimilarityCalculateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_embedding_similarity_calculate"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("candidate_embedding_attr_type", "item_attr") == "item_attr":
      ret.add(self._config["candidate_embedding_attr"])
    if self._config.get("target_embedding_attr_type", "common_attr") == "item_attr":
      ret.add(self._config["target_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("candidate_embedding_attr_type", "item_attr") == "item_attr":
      ret.add(self._config["output_similarity_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr in ["embedding_dim"]:
      ret.update(self.extract_dynamic_params(self._config.get(attr), ""))
    if self._config.get("candidate_embedding_attr_type", "item_attr") == "common_attr":
      ret.add(self._config["candidate_embedding_attr"])
    if self._config.get("target_embedding_attr_type", "common_attr") == "common_attr":
      ret.add(self._config["target_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("candidate_embedding_attr_type", "item_attr") == "common_attr":
      ret.add(self._config["output_similarity_attr"])
    return ret

class OverseaDoubleListAnalyseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_double_list_analyse"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr", False) == False:
      ret.add(self._config.get("input_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr", False) == False:
      for mapping in self._config["mappings"]:
        ret.add(mapping.get("output_attr", ""))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for mapping in self._config["mappings"]:
      for attr in ["topN", "compared_to"]:
        if attr in mapping:
          ret.update(self.extract_dynamic_params(mapping[attr]))
    if self._config.get("is_common_attr", False) == True:
      ret.add(self._config.get("input_attr", ""))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr", False) == True:
      for mapping in self._config["mappings"]:
        ret.add(mapping.get("output_attr", ""))
    return ret

class OverseaMultiConstraintsOptimizeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_multi_constraints_optimize"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["optimize_attr"])
    for constraint in self._config["constraints"]:
      ret.add(constraint["name"]) 

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config["limit_size"]))
    for constraint in self._config["constraints"]:
      ret.update(self.extract_dynamic_params(constraint["value"]))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["find_optimal"])
    return ret

class OverseaMultiConstraintsOptimizeV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_multi_constraints_optimize_v2"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["optimize_attr"])
    for constraint in self._config["constraints"]:
      ret.add(constraint["name"]) 

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config["limit_size"]))
    if "sub_lambda_version" in self._config:
      ret.update(self.extract_dynamic_params(self._config["sub_lambda_version"]))
    if "tolerance_version" in self._config:
      ret.update(self.extract_dynamic_params(self._config["tolerance_version"]))
    for constraint in self._config["constraints"]:
      ret.update(self.extract_dynamic_params(constraint["value"]))
      ret.update(self.extract_dynamic_params(constraint["tolerance_value"]))
      if "beta_param" in constraint:
        ret.update(self.extract_dynamic_params(constraint["beta_param"]))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["find_solution"])
    if "output_opt_lambdas" in self._config:
      ret.add(self._config["output_opt_lambdas"])
    if "extra_output_attrs" in self._config:
      ret.add(self._config["extra_output_attrs"])
    return ret

class KsibDelegateEnricher(CommonRecoDelegateEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_delegate_enrich"
