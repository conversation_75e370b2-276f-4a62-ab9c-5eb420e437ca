#!/usr/bin/env python3
"""
filename: kwaime_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kwaime
author: gao<PERSON><EMAIL>
date: 2021-07-14 16:45:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class KwaiMeImplicitNegativeFeedbackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_implicit_negative_feedback"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["cal_candidate_num", "history_implicit_dim_weight", "no_his_max_weight_use",
                "max_his_sim_score_bound", "cal_noclick_num", "real_show_expired_gap",
                "page_num_limit", "time_weight_param", "time_interval_param",
                "effective_play_threshold", "effective_play_rate_threshold", "enable_use_personal_threshold"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    if "input_common_reader_info_attr" in self._config:
      ret.add(self._config["input_common_reader_info_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_impl_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret