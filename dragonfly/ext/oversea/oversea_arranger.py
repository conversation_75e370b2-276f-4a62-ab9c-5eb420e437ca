#!/usr/bin/env python3
"""
filename: oversea_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for oversea
author: <PERSON><PERSON><PERSON>@kuaishou.com
date: 2022-03-08 16:00:00
"""

import operator
import itertools

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class OverseaLimitByReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_limit_by_reason"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for reason_queue in self._config["reason_limit_queue"]:
      attrs.update(self.extract_dynamic_params(reason_queue["limit_size"]))
    return attrs

class OverseaSsdVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_ssd_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "theta", "allow_empty_embedding", "stable_ssd", "cross_screen_variant", "l2_norm_ranking_score", "optimized_ssd"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    if "cross_screen_items_from_attr" in self._config:
      ret.add(self._config["cross_screen_items_from_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    if "cross_screen_variant" in self._config \
        and "cross_screen_item_embedding_attr" in self._config \
        and "cross_screen_items_from_attr" in self._config:
      ret.add(gen_attr_name_with_common_attr_channel(self._config["cross_screen_item_embedding_attr"], self._config["cross_screen_items_from_attr"]))
    return ret

class OverseaTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_truncate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    check_arg(isinstance(self._config.get("size_limit"), (int, str)), "size_limit 不为 int/ 动态参数类型")
    attrs.update(self.extract_dynamic_params(self._config["size_limit"]))
    if "bucket_name_attr" in self._config:
      attrs.add(self._config["bucket_name_attr"])
    if "need_isolation" in self._config:
      check_arg(isinstance(self._config.get("need_isolation"), (bool, str)), "need_isolation 不为 bool/ 动态参数类型")
      attrs.update(self.extract_dynamic_params(self._config["need_isolation"]))
    if "back_fill_ratio" in self._config:
      check_arg(isinstance(self._config.get("back_fill_ratio"), (float, str)), "back_fill_ratio 不为 float/ 动态参数类型")
      attrs.update(self.extract_dynamic_params(self._config["back_fill_ratio"]))
    if "backfill_to" in self._config:
      check_arg(isinstance(self._config.get("backfill_to"), list), "backfill_to 不为 list 参数类型")
      for k in self._config["backfill_to"]:
        check_arg(isinstance(k, dict), "backfill_to 成员不为 dict 类型")
        if "limit" in k:
          attrs.update(self.extract_dynamic_params(k["limit"]))
        elif "ratio" in k:
          attrs.update(self.extract_dynamic_params(k["ratio"]))
        elif "priority" in k:
          attrs.update(self.extract_dynamic_params(k["priority"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "backfill_to" in self._config:
      for k in self._config.get("backfill_to"):
        check_arg(isinstance(k.get("attr_name"), str), "attr_name 不为 str 类型")
        attrs.add(k["attr_name"])
    return attrs

class OverseaQueueTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_queue_truncate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    check_arg(isinstance(self._config.get("size_limit"), (int, str)), "size_limit 不为 int/ 动态参数类型")
    attrs.update(self.extract_dynamic_params(self._config["size_limit"]))
    if "bucket_name_attr" in self._config:
      attrs.add(self._config["bucket_name_attr"])
    if "need_perf_topk" in self._config:
      check_arg(isinstance(self._config.get("need_perf_topk"), (bool, str)), "need_perf_topk 不为 bool/ 动态参数类型")
      attrs.update(self.extract_dynamic_params(self._config["need_perf_topk"]))
    if "need_isolation" in self._config:
      check_arg(isinstance(self._config.get("need_isolation"), (bool, str)), "need_isolation 不为 bool/ 动态参数类型")
      attrs.update(self.extract_dynamic_params(self._config["need_isolation"]))
    if "backfill_ratio" in self._config:
      check_arg(isinstance(self._config.get("backfill_ratio"), (float, str)), "backfill_ratio 不为 float/ 动态参数类型")
      attrs.update(self.extract_dynamic_params(self._config["backfill_ratio"]))
    if "backfill_to" in self._config:
      check_arg(isinstance(self._config.get("backfill_to"), list), "backfill_to 不为 list 参数类型")
      for k in self._config["backfill_to"]:
        check_arg(isinstance(k, dict), "backfill_to 成员不为 dict 类型")
        if "limit" in k:
          attrs.update(self.extract_dynamic_params(k["limit"]))
        elif "ratio" in k:
          attrs.update(self.extract_dynamic_params(k["ratio"]))
        elif "priority" in k:
          attrs.update(self.extract_dynamic_params(k["priority"]))
    if "queue_config" in self._config:
      check_arg(isinstance(self._config.get("queue_config"), list), "queue_config 不为 list 参数类型")
      for k in self._config.get("queue_config"):
        check_arg(isinstance(k.get("queue_name"), str), "queue_name 不为 str 类型")
        if "queue_limit" in k:
          attrs.update(self.extract_dynamic_params(k["queue_limit"]))
        elif "queue_ratio" in k:
          attrs.update(self.extract_dynamic_params(k["queue_ratio"]))
        else:
          check_arg(False, "queue_limit 和 queue_ratio 需要配置一个")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "backfill_to" in self._config:
      for k in self._config.get("backfill_to"):
        check_arg(isinstance(k.get("attr_name"), str), "attr_name 不为 str 类型")
        attrs.add(k["attr_name"])
    return attrs

class OverseaDeepDppArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(self) -> str:
    return "oversea_deep_dpp_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "epsilon", "theta", "allow_empty_embedding", "allow_results_less_limit", "need_normalize"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr", "decay_lambda_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OverseaDppVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(self) -> str:
    return "oversea_dpp_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["epsilon", "limit", "theta", "allow_empty_embedding", "allow_results_less_limit", "use_stride_dpp", "stride", "allow_less_stride"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OverseaWeightedSsdVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_weighted_ssd_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "theta", "allow_empty_embedding", "stable_ssd", "cross_screen_variant", "l2_norm_ranking_score", "optimized_ssd", "gamma"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    if "cross_screen_items_from_attr" in self._config:
      ret.add(self._config["cross_screen_items_from_attr"])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    if "cross_screen_variant" in self._config \
        and "cross_screen_item_embedding_attr" in self._config \
        and "cross_screen_items_from_attr" in self._config \
        and "cross_screen_item_weight_from_attr" in self._config :
      ret.add(gen_attr_name_with_common_attr_channel(self._config["cross_screen_item_embedding_attr"], self._config["cross_screen_items_from_attr"]))
      ret.add(gen_attr_name_with_common_attr_channel(self._config["cross_screen_item_weight_from_attr"], self._config["cross_screen_items_from_attr"]))
    return ret

class OverseaQuantizationDppArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(self) -> str:
    return "oversea_quantization_dpp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "epsilon", "in_similarity", "cluster_num", "allow_results_less_limit",
                "decay_ratio", "decay_min"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    ret.add(self._config["similarity_matrix_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_cluster_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OverseaMultiReasonRandomSelectArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_multi_reason_random_select"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reason_list_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs
