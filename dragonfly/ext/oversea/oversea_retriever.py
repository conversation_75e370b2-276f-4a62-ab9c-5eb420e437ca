#!/usr/bin/env python3
# coding=utf-8
"""
filename: oversea_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module
author: x<PERSON><PERSON>ay<PERSON>@kuaishou.com
date: 2020-12-05 14:50:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class OverseaDynamicReasonCommonAttrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_retrieve_by_common_attrs_with_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = { v["name"] for v in self._config["attrs"] }
    if "exclude_items_in_attr" in self._config:
      attrs.add(self._config["exclude_items_in_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("attrs"), list), "attrs 需为 list 类型")
    check_arg(all(isinstance(v, dict) for v in self._config["attrs"]), "attrs 中的每项需为 dict 类型")
    check_arg(all("name" in v for v in self._config["attrs"]), "attrs 中的每项需包含 name 字段")
    check_arg(all(isinstance(v["name"], str) and v["name"] for v in self._config["attrs"]), "name 字段需为非空字符串")
    check_arg(all("reason_from_attr" in v for v in self._config["attrs"]), "attrs 中的每项需包含 reason 字段")
    check_arg(all(isinstance(v["reason_from_attr"], str) for v in self._config["attrs"]), "reason 字段需为 > 0 的整数")

class OverseaMixRankBeamSearchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_mix_rank_beam_search"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs = { v["item_key_attr"] for v in self._config["extra_lists"] }
    attrs.add(self._config.get("reco_list_attr"))
    for v in self._config["extra_lists"]:
      if "item_type_attr" in v:
        attrs.add(v["item_type_attr"])
    attrs.add(self._config.get("is_first_request"))
    attrs.add(self._config.get("min_gap_attr"))
    attrs.add(self._config.get("min_start_idx_attr"))
    attrs.add(self._config.get("last_item_gap_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_item_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("extra_lists"), list), "extra_lists 需为 list 类型")
    check_arg(all(isinstance(v, dict) for v in self._config["extra_lists"]), "extra_lists 中的每项需为 dict 类型")
    check_arg(all("type" in v for v in self._config["extra_lists"]), "extra_lists 中的每项需包含 type 字段")

class OverseaSaveTopkToItemRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_save_topk_to_item"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    if self._config.get("from_table"):
      tables.add(self._config.get("from_table"))
    else:
      tables.add(self.item_table)
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    # 在 _check_config 时, to_table 配置项调整到 item_table 处
    # 由于 output_item_tables 在 _check_config 前后均使用, 故须同时观察二者
    tables = set()
    if self._config.get("to_table"):
      tables.add(self._config.get("to_table"))
    elif self._config.get("item_table"):
      tables.add(self._config.get("item_table"))
    else:
      tables.add(self.item_table)
    return tables

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("list_length")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("attr_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config.get("reason") > 0, "请提供正整数作为召回理由 reason")
    check_arg(isinstance(self._config.get("attr_name"), str) and self._config.get("attr_name") != "", "请提供非空字符串作为 attr_name")
    check_arg(not self._config.get("from_table") or isinstance(self._config.get("from_table"), str), "若选配 from_table 项, 请提供字符串")
    check_arg(not self._config.get("to_table") or isinstance(self._config.get("to_table"), str), "若选配 to_table 项, 请提供字符串")
    # 在 __init__ 的时候, processor.item_table 尚未被 leaf.item_table 更新
    # 因此，下面这段代码（对 from/to_table 的更新）无法加在 __init__ 函数中
    if not self._config.get("from_table"):
      self._config["from_table"] = self.item_table
    if self._config.get("to_table"):
      self._config["item_table"] = self._config.get("to_table")
      self.item_table = self._config.get("to_table")
      self._config.pop("to_table")