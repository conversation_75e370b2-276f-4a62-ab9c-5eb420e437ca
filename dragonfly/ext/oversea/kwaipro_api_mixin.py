#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwaipro_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kwaipro api mixin
author: qich<PERSON>@kuaishou.com
date: 2021-08-27 16:16:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kwaipro_arranger import *

class KwaiProApiMixin(CommonLeafBaseMixin):
  """
  kwaipro 相关 processor 接口
  - KwaiProMmrDiversityArranger
  - KwaiProDppDiversityArranger
  """

  def kwaipro_mmr_diversity(self, **kwargs):
    """
    KwaiProMmrDiversityArranger
    ------
    kwaipro mmr 多样性策略

    参数配置
    ------
    'limit': [int] [动态参数] mmr 计算的 item 数

    'mmr_lambda': [double] [动态参数] 相关性和多样性权重, 取值范围 [0, 1)

    'item_embedding_attr': [string] [动态参数] mmr 使用的 embedding attr

    'ranking_score_attr': [string] [动态参数] mmr 计算使用的 ranking score attr

    'output_item_mmr_score_attr': [string] [动态参数] 计算 mmr 时生成的 item attr mmr_score

    'allow_empty_embedding": [bool] [动态参数] 允许 item embedding 为空, 默认为 False

    调用示例
    ------
    ``` python
    .kwaipro_mmr_diversity(
      limit=8,
      mmr_lambda=0.5,
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score",
      output_item_mmr_score_attr="mmr_score"
    )
    ```
    """
    self._add_processor(KwaiProMmrDiversityArranger(kwargs))
    return self

  def kwaipro_dpp_diversity(self, **kwargs):
    """
    KwaiProDppDiversityArranger
    ------
    kwaipro dpp 多样性策略

    参数配置
    ------
    'limit': [int] [动态参数] dpp 计算的 item 数

    'theta': [double] [动态参数] 相关性和多样性权重 (0, 1)

    'epsilon': [double] [动态参数] 信息增益阈值, 一般取值范围(0, 0.000001)

    'item_embedding_attr': [string] [动态参数] dpp 使用的 item embedding attr

    'ranking_score_attr': [string] [动态参数] dpp 计算使用的 ranking score attr

    'allow_empty_embedding': [bool] [动态参数] 允许 item embedding 为空, 默认为 False

    'allow_results_less_limit': [bool] [动态参数] 允许 dpp 计算结果数小于 limit, 默认为 False

    'output_score_attr': [string] [可选] dpp 输出分, 用来算 es dpp rank

    调用示例
    ------
    ``` python
    .kwaipro_dpp_diversity(
      limit=8,
      theta=0.5,
      epsilon=0.000001,
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score"
    )
    ```
    """
    self._add_processor(KwaiProDppDiversityArranger(kwargs))
    return self
