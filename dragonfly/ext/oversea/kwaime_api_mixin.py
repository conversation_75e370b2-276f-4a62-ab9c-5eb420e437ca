#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwaime_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kwaime api mixin
author: gao<PERSON><EMAIL>
date: 2021-07-12 18:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kwaime_enricher import *
from .kwaime_arranger import *

class KwaiMeApiMixin(CommonLeafBaseMixin):
  """
  kwaime 相关 processor 接口
  - KwaiMeImplicitNegativeFeedbackEnricher
  - KwaiMeMmrVariantArranger
  """

  def kwaime_implicit_negative_feedback(self, **kwargs):
    """
    KwaiMeImplicitNegativeFeedbackEnricher
    ------
    kwaime 计算 leaf 当前视频与历史隐式负反馈相似度

    参数配置
    ------
    'input_common_reader_info_attr': [string] common attr 中的 reader_info_bytes_str
    'output_item_impl_score_attr': [string] 输出的隐式负反馈相似度分数
    'cal_candidate_num': [int] [动态参数] 需要进行隐式负反馈相似度的视频数量
    'history_implicit_dim_weight': [string] [动态参数] 隐式负反馈纬度和权重
    'no_his_max_weight_use': [bool] [动态参数] 是否在计算时最大权重归一
    'max_his_sim_score_bound': [double] [动态参数] 相似度上界
    'cal_noclick_num': [int] [动态参数] 展示未点击视频数量
    'real_show_expired_gap': [int] [动态参数] 计算的 realshow 时间间隔
    'page_num_limit': [int] [动态参数] 在多少刷内计算
    'time_weight_param': [double] [动态参数] 时间衰减权重
    'time_interval_param': [double] [动态参数] 跟时间衰减权重同时使用
    'effective_play_threshold': [double] [动态参数] 有效观看时长阈值
    'effective_play_rate_threshold': [double] [动态参数] 有效观看完成率阈值
    'enable_use_personal_threshold': [bool] [动态参数] 是否启用个性化阈值

    调用示例
    ------
    ``` python
    .kwaime_implicit_negative_feedback(
      input_common_reader_info_attr = "reader_info_bytes_str",
      output_item_impl_score_attr = "impl_score",
      cal_candidate_num = 96,
      history_implicit_dim_weight = "primary_tag_id:1.0",
      no_his_max_weight_use = true,
      max_his_sim_score_bound = 1.0,
      cal_noclick_num = 20,
      real_show_expired_gap = 1800,
      page_num_limit = 10,
      time_weight_param = 0.1,
      time_interval_param = 1500,
      effective_play_threshold = 10,
      effective_play_rate_threshold = 0.5,
      enable_use_personal_threshold = true
    )
    ```
    """
    self._add_processor(KwaiMeImplicitNegativeFeedbackEnricher(kwargs))
    return self

  def kwaime_mmr_variant(self, **kwargs):
    """
    KwaiMeMmrVariantArranger
    ------
    kwaime mmr 多样性策略

    参数配置
    ------
    'input_item_ensemble_score_attr': [string] item attr es 分数
    'input_item_impl_score_attr': [string] 隐式负反馈相似度分数
    'output_item_mmr_score_attr': [string] mmr 分数
    'mmr_need_diversity_num': [int] [动态参数] 返回的 mmr 个数
    'mmr_cal_candidate_num': [int] [动态参数] 参与计算的视频数量
    'mmr_fetch_once_num': [int] [动态参数] 一次计算返回的视频数量（加速计算）
    'mmr_lambda': [double] [动态参数] 相关性和多样性权重
    'mmr_gamma': [double] [动态参数] 隐式负反馈权重
    'mmr_max_sim_score_bound': [double] [动态参数] 相似度上界
    'mmr_diversity_dim_weight': [string] [动态参数] 多样性纬度权重
    'mmr_no_max_weight_use': [bool] [动态参数] 是否在计算时最大权重归一
    'enable_mmr_his_feedback_diversity': [bool] [动态参数] 是否负反馈加入计算
    'enable_mmr_es_score_order': [bool] [动态参数] 候选item是否按原ensemble score 排序

    调用示例
    ------
    ``` python
    .kwaime_mmr_variant(
      input_item_ensemble_score_attr = "es_score",
      input_item_impl_score_attr = "impl_score",
      output_item_mmr_score_attr = "mmr_score",
      mmr_need_diversity_num = 8,
      mmr_cal_candidate_num = 96,
      mmr_fetch_once_num = 1,
      mmr_lambda = 0.5,
      mmr_gamma = 0.5,
      mmr_max_sim_score_bound = 1.0,
      mmr_diversity_dim_weight = "primary_tag_id:1.0:1",
      mmr_no_max_weight_use = true,
      enable_mmr_his_feedback_diversity = true,
      enable_mmr_es_score_order = false
    )
    ```
    """
    self._add_processor(KwaiMeMmrVariantArranger(kwargs))
    return self

  def kwaime_dpp_variant(self, **kwargs):
    """
    KwaiMeDppVariantArranger
    ------
    kwaime dpp 多样性策略

    参数配置
    ------
    'input_photo_similarities_attr': [[]float] item与其他item的相似度列表,是list形式
    'input_rank_score_attr': [float] 用户与photo的相关性分数
    'output_selected_items_attr': [[]int] 选出的推荐列表在原来排序的位置, 从0记数
    'dpp_max_length': [int] [动态参数] 最多打散多少个
    'dpp_epsilon': [double] [动态参数] 提前退出条件,默认值为1e-10
    'dpp_enable_similarities_normalization': [bool] [动态参数] 是否需要对相似度分数归一化, 默认值为True

    调用示例
    ------
    ``` python
    .kwaime_dpp_variant(
      input_photo_similarities_attr = "photo_similarities",
      input_rank_score_attr = "rank_score",
      output_selected_items_attr = "dpp_output_selected_items",
      dpp_max_length = 40,
      dpp_epsilon = 1e-10
    )
    ```
    """
    self._add_processor(KwaiMeDppVariantArranger(kwargs))
    return self
