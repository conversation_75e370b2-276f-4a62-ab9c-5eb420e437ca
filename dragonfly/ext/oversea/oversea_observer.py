
from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafObserver

class OverseaAttrPercentilePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog_attr_percentile"

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self.input_item_attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("check_point"), check_format=False))
    return attrs


class OverseaMutexSleepObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_mutex_sleep"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("sleep_ms"), check_format=False))
    return attrs
