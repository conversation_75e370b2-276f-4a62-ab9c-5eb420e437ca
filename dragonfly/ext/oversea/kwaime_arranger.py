#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwaime_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module
author: gao<PERSON><PERSON>@kuaishou.com
date: 2021-07-13 11:45:00
"""

from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class KwaiMeMmrVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_mmr_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["mmr_need_diversity_num", "mmr_cal_candidate_num", "mmr_fetch_once_num",
                "mmr_lambda", "mmr_gamma", "mmr_max_sim_score_bound",
                "mmr_diversity_dim_weight", "mmr_no_max_weight_use", "enable_mmr_his_feedback_diversity",
                "enable_mmr_es_score_order"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["input_item_ensemble_score_attr", "input_item_impl_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_mmr_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class KwaiMeDppVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_dpp_variant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["dpp_max_length", "dpp_epsilon", 
        "dpp_enable_similarities_normalization"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["input_rank_score_attr", "input_photo_similarities_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_selected_items_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
