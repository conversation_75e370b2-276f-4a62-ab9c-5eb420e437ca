#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwaipro_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module
author: q<PERSON><PERSON>@kuaishou.com
date: 2021-08-27 14:35:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class KwaiProMmrDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_mmr_diversity"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "mmr_lambda", "allow_empty_embedding"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_mmr_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class KwaiProDppDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(self) -> str:
    return "kwaipro_dpp_diversity"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["limit", "epsilon", "theta", "allow_empty_embedding", "allow_results_less_limit",
                "enable_simscore_scale", "sim_scale_params_str", "beta", "enable_weighted_embedding", "secondary_embed_len"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "ranking_score_attr", "mmu_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
