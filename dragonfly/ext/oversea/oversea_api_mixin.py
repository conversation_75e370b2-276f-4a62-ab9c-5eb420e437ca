#!/usr/bin/env python3
# coding=utf-8
"""
filename: oversea_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, oversea api mixin
author: <EMAIL>
date: 2020-07-23 18:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .oversea_enricher import *
from .oversea_arranger import *
from .oversea_retriever import *
from .oversea_observer import *

class OverseaApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 oversea 相关的 Processor 接口:
  - OverseaOnlineMioFeatureEnricher
  """

  def oversea_local_cache_attr(self, **kwargs):
    """
    OverseaLocalCacheAttrEnricher
    ------
    海外本地 kv 缓存，用于存储一些中间结果，可以跨请求使用
    目前 key_attr 类型支持 int, string. value_attr 类型支持 int, double, string
    参数配置
    ------
    `op`: [string] 操作类型，可选值为 "write" 或者 "read"

    `is_common_attr`: [bool] 选配项，是否是 common attr, 默认为 True

    `key_attr_name`: [string] 存储 key 的 attr 名字

    `value_attr_name`: [string] 存储 value 的 attr 名字

    `value_attr_type`: [string] 读取 value 时存储类型。仅在 op = read 时生效。

    `cache_name`: [string] 本地 cache 的名称, 用于区分不同的 cache，不同的 cache 之间不会相互影响

    `cache_bits`: [int] 本地 cache 的位数，用于计算 cache 的大小，cache 的大小为 2^cache_bits，同 cache_name 的 cache_bits 需相同

    `cache_delay_delete_ms`: [int] 选配项，本地 cache 的延迟删除时间，单位为 ms, 默认为 10 * 1000。同 cache_name 的 cache_delay_delete_ms 需相同

    `cache_expire_second`: [int] 选配项，本地 cache 的过期时间，单位为 s, 默认为 60 * 60。同 cache_name 的 cache_expire_second 需相同

    调用示例
    ------
    ``` python
    .oversea_local_cache_attr(
      op="write",
      key_attr_name="key_attr_name",
      value_attr_name="value_attr_name",
      cache_name="cache_name",
      cache_bits=20,
      cache_delay_delete_ms=2000,
      cache_expire_second=3600
    )
    .oversea_local_cache_attr(
      op="read",
      key_attr_name="key_attr_name",
      value_attr_name="value_attr_name",
      cache_name="cache_name",
      cache_bits=20,
      cache_delay_delete_ms=2000,
      cache_expire_second=3600
    ```
    """
    self._add_processor(OverseaLocalCacheAttrEnricher(kwargs))
    return self

  def get_redis_value_by_list_range(self, **kwargs):
    """
    OverseaRedisListRangeCommonAttrEnricher
    ------
    访问 redis 的 list 的 range 接口，获取 list 中的数据
    参数配置
    ------

    `redis_name`: [string] redis 的名称

    `redis_key`: [string] redis 的 key 的 string common attr

    `range_point_attr`: [string] 输入的 range point 的 double list common attr 名字

    `output_common_attr`: [string] 输出的 int list common attr 名字

    调用示例
    ------
    ``` python
    .get_redis_value_by_list_range(
      redis_name="redis_name",
      redis_key="redis_key_attr",
      range_point_attr="range_point_attr",
      output_common_attr="target_p_ids"
    )
    ```
    """
    self._add_processor(OverseaRedisListRangeCommonAttrEnricher(kwargs))
    return self

  def get_action_list_from_rodis(self, **kwargs):
    """
    OverseaRodisItemAttrEnricher
    ------
    将结果集的 photo 分桶存储到 一个 queue 里面，供之后取用
    payload id 配置参考：https://git.corp.kuaishou.com/ksib-reco/ksib-reco/-/blob/master/ksib-reco-proto/src/main/proto/ksib/reco/common/rodis_payload.proto 里面的 enum KwaiActionTypePayloadId
    参数配置
    ------

    `domain`: [string] Rodis 数据的 domain

    `kess_service`: [string] Rodis 服务的 kess 名称

    `service_group`: [string] Rodis 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 超时时间，默认值为 50

    `item_attrs`: [object] 写入 action list 的 item attr 配置
      - `payload_id`: [int] 获取 action list 用的 payload id
      - `len_limit`: [int] [动态参数] 设置 action list 的 长度，最大为 200
      - `after_ts`: [int] [动态参数] 选配项，action list 时间范围的上限
      - `before_ts`: [int] [动态参数] 选配项，action list 时间范围的下限
      - `min_len`: [int] [动态参数] 选配项，action list 至少返回多少个
      - `ts`: [string] 选配项 存储 timestamp 的 item attr 名字
      - `aid`: [string] 选配项 存储 aid 的 item attr 名字
    调用示例
    ------
    ``` python
    .get_action_list_from_rodis(
      domain="KSIB_UID",
      kess_service="grpc_rodisKsib",
      item_list_from_attr = "user_ids",
      item_attrs = {
        "click_list": {
          "payload_id": 2,
          "len_limit": 2,
          "ts": "click_list_ts",
          "aid": "click_list_aid"
        }
      }
    )
    ```
    """
    self._add_processor(OverseaRodisItemAttrEnricher(kwargs))
    return self

  def oversea_sample_pool(self, **kwargs):
    """
    OverseaSamplePoolEnricher
    ------
    将结果集的 photo 分桶存储到 一个 queue 里面，供之后取用

    参数配置
    ------

    `mod`: [string] 存入 (save) 或取出 (load) PhotoInfo.

    `record_item_attrs`: [list] 存入或取出的 item attr 名称。

    `photo_map_attr`: [string] 存储sample poll 的 common attr

    `bucket_attr`: [string] 桶信息

    `queue_size`: [int] photo map 队列长度，分桶，可缺省，默认 为 10000。

    `insert_flag_attr`: [string] 标记是否存入 photo map 的 int item attr.

    `history_item_keys_attr`: [string] 输出 photo map 中 item key 的 common attr

    调用示例
    ------
    ``` python
    .oversea_sample_pool(
        mod="save",
        insert_flag_attr = "insert_flag",
        bucket_attr = "bucket",
        record_item_attrs = ["pxtr", "slot", "label"],
        photo_map_attr = "photo_map",
        history_item_keys_attr = "history_item_keys"
    )
    .oversea_sample_pool(
        mod = "load",
        bucket_attr = "bucket",
        photo_map_attr = "photo_map",
        record_item_attrs = ["pxtr", "slot", "label"]
    )
    ```
    """
    self._add_processor(OverseaSamplePoolEnricher(kwargs))
    return self

  def split_oversea_weight_str(self, **kwargs):
    """
    CommonRecoStringSplitEnricher
    ------
    将 weight string attr 切割成多个 double attr

    参数配置
    ------
    `weight_string_attr`: [string] 输入的 string common attr。

    `output_common_attrs`: [list] 输出的 double common attrs。

    `keys_separator`: [string] 选配项 分割不同 key 之间的字符，默认为 ','

    `kv_separator`: [string] 选配项 分割 key 和 value 的字符，默认为 ':'

    调用示例
    ------
    ``` python
    .split_oversea_weight_str(
      weight_string_attr = "ensemble_weight_str",
      output_common_attrs = ["wftr", "wctr"],
    )
    ```
    """
    self._add_processor(OverseaWeightStringSplitEnricher(kwargs))
    return self

  def oversea_item_attr_fallback(self, **kwargs):
    """
    OverseaItemAttrFallbackEnricher
    ------
    实现 fr_xtr or rr_xtr or emp_xtr 的逻辑
    目前仅支持 double item attr

    参数配置
    ------
    `item_attrs`: [list] fallback 配置
      - `input_item_attrs`: [list] 输入的 double item attrs，顺序为 fallback 顺序。
      - `output_item_attr`: [string] 输出的 double item attr。

    调用示例
    ------
    ``` python
    .oversea_item_attr_fallback(
      item_attrs=[
        {
          "input_item_attrs": ["fr_ctr", "rr_ctr", "emp_ctr"],
          "output_item_attr": "ctr",
        }
      ]
    )
    ```
    """
    self._add_processor(OverseaItemAttrFallbackEnricher(kwargs))
    return self

  def gen_oversea_tag_id(self, ** kwargs):
    """
    OverseaTagIdEnricher
    ------
    生成 tag id
    输入 attr 可以是 int 或者 int list 类型，如果是 int list 类型 attr 选择 list 的第一个值。
    从前往后访问 attr 选择第一个 > 0 的值生成 tag id。输出值是 int 类型的 item attr
    若没有任何满足的值，则输出为空

    参数配置
    ------
    `tag_attrs`: [list] 输入的 item attr 列表

    `output_item_attr`: [string] 输出的 tag id attr

    调用示例
    ------
    ``` python
    .gen_oversea_tag_id(
      tag_attrs = ["tag1", "tag2"],
      output_item_attr = "tag_id"
    )
    ```
    """

    self._add_processor(OverseaTagIdEnricher(kwargs))
    return self

  def oversea_audit_mark_match(self, **kwargs):
    """
    OverseaAuditMarkEnricher
    ------
    海外审核逻辑打 tag 逻辑
    支持按位匹配和按值匹配

    参数配置
    ------
    `audit_type_name`: [string] 输入的 audit type item attr

    `audit_mark_name`: [string] 输入的 audit mark item attr

    `audit_bucket_name`: [string][选配项] 输入的 audit bucket item attr

    `match_type`: [string] 匹配类型，可选值为 "bit" 或者 "num" 或者 "compare"

    `bucket_attr`: [string][选配项] 需要匹配 bucket 的 common attr, 如果没有指定, 则不用匹配 bucket

    `match_value_config`: [list] 匹配值配置，用于 bit 或 num 匹配类型
      - `type_value`: [int] type 的匹配值, int 单值
      - `mark_set`: [list] [动态参数] mark 的匹配值, int list 多值

    `weight_config`: [string] 代表 audit type 对应权重的 common attr, 用于 compare 匹配类型

    `comparing_attr_name`: [string] 被比较的 item attr, 用于 compare 匹配类型

    `output_item_attr`: [string] 输出的 item attr, 匹配成功则输出 1

    调用示例
    ------
    ``` python
    .oversea_audit_mark_match(
      audit_type_name = "p_audit_mark_type",
      audit_mark_name = "p_audit_mark_mark",
      match_type = "bit",
      match_value_config = [
        {
          "type_value": 17,
          "mark_set": [8]
        }
      ],
      output_item_attr = "is_block_match"
    )
    .oversea_audit_mark_match(
      audit_type_name = "p_audit_mark_type",
      audit_mark_name = "p_audit_mark_mark",
      match_type = "num",
      match_value_config = [
        {
          "type_value": 15,
          "mark_set": [8, 9, 13, 17, 80, 33, 4625, 68, 21, 22, 25, 4924, 5086, 63, 35, 36]
        },
        {
          "type_value": 3,
          "mark_set": [8,  9,  14, 17,  19,  80, 68,  21, 22, 25, 4924, 5086]
        },
        {
          "type_value": 4,
          "mark_set": [28]
        },
        {
          "type_value": 11,
          "mark_set": [5086]
        }
      ],
      output_item_attr = "is_block_match"
    )
    .oversea_audit_mark_match(
      audit_type_name = "p_audit_mark_type",
      audit_mark_name = "p_audit_mark_mark",
      audit_bucket_name = "p_audit_mark_bucket",
      bucket_attr = "bucket",
      match_type = "num",
      match_value_config = [
        {
          "type_value": 4,
          "mark_set": [1, 27]
        },
      ],
      output_item_attr = "is_block_match"
    )
    .oversea_audit_mark_match(
      audit_type_name = "p_audit_mark_type",
      audit_mark_name = "p_audit_mark_mark",
      audit_bucket_name = "p_audit_mark_bucket",
      bucket_attr = "bucket",
      match_type = "compare",
      weight_config="map_attr",
      comparing_attr_name="server_show",
      output_item_attr = "filter_flag"
    )
    ```
    """
    self._add_processor(OverseaAuditMarkEnricher(kwargs))
    return self

  def extract_with_oversea_online_mio_feature(self, **kwargs):
    """
    OverseaOnlineMioFeatureEnricher
    ------
    使用 OverseaOnlineMioFeatureEnricher 进行特征抽取，供线上使用

    参数配置
    ------
    `feature_list`: [list] 特征列表

    `oversea_reader_info_attr`: [string] ReaderInfo PB 指针的 common attr name

    `oversea_photo_info_attr`: [string] PhotoInfo PB 指针的 item attr name

    `common_attrs`: [list] 存储 user 侧的 common attr name

    `item_attrs`: [list] 存储 item 侧的 item attr name

    `save_user_sign_to`: [string] 存储 user 侧 sign 的 common attr name，默认不存

    `save_user_slot_to`: [string] 存储 user 侧 slot 的 common attr name，默认不存

    `save_item_sign_to`: [string] 存储 item 侧 sign 的 item attr name，默认不存

    `save_item_slot_to`: [string] 存储 item 侧 slot 的 item attr name，默认不存


    调用示例
    ------
    ``` python


    .extract_with_oversea_online_mio_feature(
      feature_list=[
        "ExtractOverseaSignUserId",
        "ExtractOverseaSignAfterClickUserId",
        "ExtractOverseaSignAfterClickUserIdV2",
        "ExtractOverseaSignUserDeviceId",
        "ExtractOverseaEmbeddingAfterClickUserDeviceId",
        "ExtractOverseaEmbeddingAfterClickUserDeviceIdV2",
        "ExtractOverseaEmbeddingUserClickV3",
        "ExtractOverseaEmbeddingUserClickExtraInfoV3",
        "ExtractOverseaEmbeddingUserLikeV3",
        "ExtractOverseaEmbeddingUserLikeExtraInfoV3",
        "ExtractOverseaEmbeddingUserFollowV3",
        "ExtractOverseaEmbeddingUserForwardV3",
        "ExtractOverseaEmbeddingUserForwardExtraInfoV3",
        "ExtractOverseaSignUserInstallAppList",
        "ExtractOverseaSignUserGpsLocation",
        "ExtractOverseaSignUserGender",
        "ExtractOverseaSignUserEvrWithSmooth",
        "ExtractOverseaSignUserClickCtrWithSmooth",
        "ExtractOverseaSignUserGlobalTagPrefer",
        "ExtractOverseaSignBucketIsNewUser",
        "ExtractOverseaSignUserIp",
        "ExtractOverseaSignUserMod",
        "ExtractOverseaSignUserNet",
        "ExtractOverseaSignUserPromotionId",
        "ExtractOverseaSignUserClientPage",
        "ExtractOverseaSignUserSvrWithSmooth",
        "ExtractOverseaSignNonClickClusterRealshowV2",
        "ExtractOverseaSignNonClickRealshow",
        "ExtractOverseaSignNonClickPhotoTimeDiff",
        "ExtractOverseaSignBucketAuthorId",
        "ExtractOverseaEmbeddingAfterClickAuthorId",
        "ExtractOverseaEmbeddingAfterClickAuthorIdV2",
        "ExtractOverseaSignBucketPhotoId",
        "ExtractOverseaEmbeddingAfterClickPhotoId",
        "ExtractOverseaEmbeddingAfterClickBucketPhotoId",
        "ExtractOverseaSignBucketPhotoMmuTag",
        "ExtractOverseaSignBucketPhotoImgClusterV2",
        "ExtractOverseaSignBucketPhotoExploreStatCtrWithSmooth",
        "ExtractOverseaSignBucketPhotoMmuDefinition",
        "ExtractOverseaSignBucketPhotoLowQualityScore",
        "ExtractOverseaSignBucketPhotoExploreSvrWithSmooth",
        "ExtractOverseaSignPhotoUploadType",
        "ExtractOverseaSignBucketPhotoAge",
        "ExtractOverseaSignBucketPhotoGender",
        "ExtractOverseaSignBucketPhotoExploreRealShowCount",
        "ExtractOverseaSignBucketPhotoExploreClickCount",
        "ExtractOverseaSignBucketPhotoExploreLikeCount",
        "ExtractOverseaSignPhotoIdMmu32EmbPsuedoHolder",
        "ExtractOverseaSignUserShortViewV2",
        "ExtractOverseaSignUserEffectiveViewV3",
        "ExtractOverseaSignRealshowClickAndNonClickNum",
        "ExtractOverseaSignUserMixSpeed",
        "ExtractOverseaSignUserActiveHour",
        "ExtractOverseaSignUserActiveWeek"
      ],
      oversea_reader_info_attr='oversea_reader_info',
      oversea_photo_info_attr='oversea_photo_info',
      common_attrs = [
        "lineid",
        "user_hash",
        "time_ms",
        "user_type",
        "user_bucket",
        "user_emp_ctr",
        "user_emp_ftr",
        "user_emp_evr"
      ],
      item_attrs = [
        "pid",
        "duration",
        "emp_ctr",
        "emp_ftr",
        "is_slide"
      ],
      save_item_slot_to='item_slot',
      save_item_sign_to='item_sign',
      save_user_slot_to='common_slot',
      save_user_sign_to='common_sign')
    ```
    """
    self._add_processor(OverseaOnlineMioFeatureEnricher(kwargs))
    return self
  def oversea_pool_rank(self, **kwargs):
    """
    OverseaPoolRankEnricher
    ------
    计算当前 common attr 在 pool 中的 rank
    参数配置
    ------
    `is_common_attr`: [bool] 选配项，是否是 common attr, 默认为 True

    `pool_name`: [string][动态参数] 单独的 pool 的名字。同一个 pool 的 pool_name 需相同，可缺省，默认为 default

    `candidate_pool_names`: [list] 选配项，候选 pool 的名字，pool_name 必须在 candidate_pool_names 中。默认为 [default], 当 pool_name 不在 candidate_pool_names 中时，会用 default pool

    `score_attr_name`: [string] 输入的 double common attr, 为 request 的 评分

    `percent_attr_name`: [string] 输出的 double common attr, 为 request 的 rank

    `precision`: [int] 选配项，计算 rank 时的精度，默认为 10000

    `queue_size_max`: [int] 选配项，pool 的大小，默认为 10000

    调用示例
    ------
    ``` python
    .oversea_pool_rank(
      score_attr_name="score",
      percent_attr_name="score_percent",
      precision=10000,
      queue_size_max=10000
    )
    ```
    """
    self._add_processor(OverseaPoolRankEnricher(kwargs))
    return self

  def oversea_fetch_global_varaible_cache(self, **kwargs):
    """
    OverseaGlobalVariableCacheEnricher
    ------
    oversea 在本地存储使用全局变量
    参数配置
    ------
    `namespace`: [string] 本地 cache 存储的命名空间，使不同任务 cache 之间硬分隔管理
    `group_name`: [string] [动态参数] 选配项，cache 分组
    `mode`: [string] [动态参数] 选配项，'read'/'write'/'' 不配或为空时写入本地 cache，并读取最新 cache 填充到 common_attr
    `use_local`: [bool] 选配项，默认值 true，true 时各线程 processor 内部独立存取 cache，false 时每台机器共同存取一份 cache
    `cache_size`: [int] 选配项，namespace 存储的 cache 数，不配置时该算子不会初始化 cache；多个同 namespace 的 cache 配置的 cache_size 需相同
    `common_attr`: [list] 选配项，存取的 commonAttr 名
    `list_value_attr`: [string] [动态参数] 选配项，list 类型的 attr，需要存取的值，与 list_key_attr 配合使用
    `list_key_attr`: [string] [动态参数] 选配项，list 类型的 attr，指定 list_value_attr 的每个 value 对应的 key，缺省时为 _0, _1, _2, _3, _4, ...
    `update_param`: [list] 选配项
      - `update_method`: [string] 更新方式，可选值：replace/max/min/moving_avg
      - `moving_decay`: [double] 选配项，使用 moving_avg 时的衰减因子，默认值0.999
    调用示例
    ------
    ``` python
    .oversea_fetch_global_varaible_cache(
      namespace="user_interest_score",
      group_name="{{user_group}}",
      mode="write",
      use_local=True,
      list_key_attr="{{common_str_list_attr1}}",
      list_value_attr="{{common_double_list_attr2}}"
      cache_size=20,
      common_attr=["score_1", "score_2"],
      update_param=[{"moving_decay": 0.99, "update_method": "moving_avg"}]
    )
    ```
    """
    self._add_processor(OverseaGlobalVariableCacheEnricher(kwargs))
    return self

  def oversea_calc_ensemble_score_exp(self, **kwargs):
    """
    OverseaRecoEnsembleScoreExpEnricher
    ------
    ensemble sort 优化，实验阶段，慎用！

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (SEQ_ON_ATTR(channels[i].name) ^ regulator + smooth)`
      - 1: `Si = channels[i].weight * (1 - min(1, SEQ_ON_ATTR(channels[i].name) / item_num) ^ regulator)`
      - 2: `Si = channels[i].weight * (e ^ (channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)) - e ^ (-channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)))`

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `hyper_scala`: [double] [动态参数] 该队列的双曲调节因子
      - `as_seq_value`: [bool] 是否跳过对该队列的排序操作，直接使用该 attr 的值作为 seq 序号参与 score 计算，默认为 False
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `regulator`: [double] [动态参数] ensemble sort 各队列的调节因子

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `cliff_ratio`: [double] [动态参数] 选配项，ensemble 队列排序序号开始降权 position 的比例点，即对排名 `队列长度 * cliff_ratio` 之后的 item 进行打压

    `cliff_height`: [int] [动态参数] 选配项，ensemble 队列降权系数，即对排名 `队列长度 * cliff_ratio` 之后的 item 序号额外加上该值作为惩罚

    `epsilon`: [double] 选配项，在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    调用示例
    ------
    ``` python
    .oversea_calc_ensemble_score_exp(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      regulator = "{{ensemble_regulator}}",
      smooth = "{{ensemble_smooth}}",
      output_attr = "ensemble_score",
    )
    ```
    """
    self._add_processor(OverseaRecoEnsembleScoreExpEnricher(kwargs))
    return self

  def calc_rr_bucket_ensemble_score(self, **kwargs):
    """
    OverseaBucketEnsembleScoreEnricher
    ------
    rr ensemble sort 支持不同分桶维度与数量，桶权重优化，实验阶段，慎用

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (SEQ_ON_ATTR(channels[i].name) ^ regulator + smooth)`
      - 1: `Si = channels[i].weight * (1 - min(1, SEQ_ON_ATTR(channels[i].name) / item_num) ^ regulator)`
      - 2: `Si = channels[i].weight * (e ^ (channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)) - e ^ (-channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)))`

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `hyper_scala`: [double] [动态参数] 该队列的双曲调节因子
      - `as_seq_value`: [bool] 是否跳过对该队列的排序操作，直接使用该 attr 的值作为 seq 序号参与 score 计算，默认为 False
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `regulator`: [double] [动态参数] ensemble sort 各队列的调节因子

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `cliff_ratio`: [double] [动态参数] 选配项，ensemble 队列排序序号开始降权 position 的比例点，即对排名 `队列长度 * cliff_ratio` 之后的 item 进行打压

    `cliff_height`: [int] [动态参数] 选配项，ensemble 队列降权系数，即对排名 `队列长度 * cliff_ratio` 之后的 item 序号额外加上该值作为惩罚

    `epsilon`: [double] 选配项，在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    新参数，对 duration 分桶使用配置 num 均分或使用阈值分桶配置，并进行桶权重配置
    ------
    `bucket_attr`: [string] 基于此item_attr分桶

    `weight_string_attr`: [string] 桶权重kv配置

    `bucket_num_attr`: [string] 平均分桶数量

    `bucket_points_attr`: [string] 阈值分桶配置
    """
    self._add_processor(OverseaBucketEnsembleScoreEnricher(kwargs))
    return self

  def gen_beam_search_list(self, **kwargs):
    """
    GenBeamSearchListEnricher
    ------
    rerank阶段beam search序列生成

    参数配置
    ------
    `ranking_score_attr`: [string] 用于计算 reward 的 item attr

    `next_score_attr`: [string] 预测是否下滑的 item attr

    `output_item_attr`: [string] 每个生成的序列存储在的哪个 item attr 中

    `output_common_attr`: [string] 生成的多个序列存储在哪个 common attr 中

    `item_type`: [int] [动态参数] 选配项，item 类型

    `candidate_len`: [int] [动态参数] 选配项，候选 item 个数

    `beam_size`: [int] [动态参数] 选配项，beam search size

    `output_len`: [int] [动态参数] 选配项，生成序列长度

    `reward_type`: [int] [动态参数] 计算list reward的方式

    `slide_alpha`: [double] [动态参数] 选配项，融合打分中 slide 的权重

    `ranking_beta`: [double] [动态参数] 选配项，融合打分中 ranking 的权重

    `slide_pow`: [double] [动态参数] 选配项，融合打分中 slide 的指数，默认为1

    `ranking_weight`: [double] [动态参数] 选配项，融合打分中 ranking score 的权重

    `index_weight`: [double] [动态参数] 选配项，融合打分中 index score 的权重

    `index_rate`: [double] [动态参数] 选配项，序列转换为分数的参数

    `decay_alpha`: [double] [动态参数] 选配项，slide decay 底数

    `decay_beta`: [double] [动态参数] 选配项，slide decay 权重

    调用示例
    ------
    ``` python
    .gen_beam_search_list(
      ranking_score_attr="ranking_score",
      next_score_attr="next_score",
      output_item_attr="item_keys",
      output_common_attr="retrieval_list_keys",
      candidate_len=100,
      beam_size=10,
      output_len=8,
      reward_type=0,
      slide_alpha=0.2,
      ranking_beta=0.4,
      slide_pow=1.0,
      ranking_weight=1.0,
      index_weight=1.0,
      index_rate=0.95,
      decay_alpha=0.5,
      decay_beta=1.0
    )
    ```
    """
    self._add_processor(GenBeamSearchListEnricher(kwargs))
    return self

  def oversea_mix_rank_beam_search(self,**kwargs):
    """
    OverseaMixRankBeamSearchRetriever
    ------
    混排阶段beam search序列生成

    参数配置
    ------
    `reco_list_attr`: [string] 视频的 common attr名称

    `extra_lists`: [list] 额外插入的业务集合
      - `type`: [string] 业务类型
      - `item_key_attr`: [string] 业务的item_key列表， common attr名称
      - `type_enum_count`: [int] 选配项，业务的type的枚举个数
      - `type_enums`: [list] 选配项，如果type_enum_count > 1，业务的type的枚举值
      - `item_type_attr`: [string] 选配项，如果type_enum_count > 1，item_list对应的type类型列表

    `is_first_request_attr`: [string] 是否首刷

    `min_gap_attr`: [string] 业务之间的间隔约束

    `min_start_idx_attr`: [string] 首刷时业务首出位置约束

    `last_item_gap_attr`; [string] 当前业务和上一刷中同类型业务item之间的间隔约束

    `reason`: [int] 召回原因

    `output_item_attr`; [string] 输出的item attr名称，保存beam search的结果

    调用示例
    ------
    ``` python
    .oversea_mix_rank_beam_search(
      reco_list_attr = "reco_item_key_list",
      extra_lists = [
        {
          "type": "live",
          "item_key_attr": "live_item_key_list"
        },
        {
          "type": "inno_live",
          "item_key_attr":"inno_live_item_key_list"
        },
        {
          "type": "ad",
          "item_key_attr":"ad_item_key_list",
          "item_type_attr": "ad_item_type_list",
          "type_enum_count" : 2,
          "type_enums" : [2, 5]
        },
      ],
      is_first_request_attr = "is_first_request",
      min_gap_attr = "min_gap_str",
      min_start_idx_attr = "min_start_idx_str",
      last_item_gap_attr = "last_item_gap_str",
      reason = 666,
      output_item_attr = "beam_search_res_list"
    )
    ```
    """
    self._add_processor(OverseaMixRankBeamSearchRetriever(kwargs))
    return self

  def oversea_save_topk_to_item(self, **kwargs):
    """
    OverseaSaveTopkToItemRetriever
    ------
    向结果集的末尾增加一个结果，用来记录当前 RecoResults 的前 K 个元素列表。

    这个结果的 item_key 是"当前 RecoResults 的前 K 个元素列表"的 hash_id"。

    它拥有一个以 attr_name 为名的 ItemAttr ，其值为"当前 RecoResults 的前K个元素列表"。

    参数配置
    ------

    `reason`: [int] 新结果的召回原因

    `attr_name`: [string] 插入的 ItemAttr 的名称

    `list_length`: [int] [动态参数] 设定 TopK 的长度，若超过结果集长度则取整个结果集

    `from_table`: [string] 选配项，指定结果集的来源表名，默认为算子自身的主 ItemTable

    `to_table`: [string] 选配项，指定新增结果的目标表名，默认为算子自身的主 ItemTable

    调用示例
    ------
    ``` python
    .oversea_save_topk_to_item(
      reason = 999,
      attr_name = "item_keys",
      list_length = 50,
      from_table = "",
      to_table = "topk_lists"
    )
    ```
    """
    self._add_processor(OverseaSaveTopkToItemRetriever(kwargs))
    return self

  def oversea_transform_item_attr_from_common_attr(self, **kwargs):
    """
    OverseaTransformItemAttrFromCommonAttrEnricher
    ------
    将 map_weight string attr 切割成<key, value>map 查找 input_item_attr 在 map 中对应值，输出至 output_item_attr

    is_transform_map 参数配置1, 默认值1
    ------
    `map_weight_string_attr`: [string] 输入的 string 类型的 common attr

    `input_item_attr`: [string] 输入的 int|string 类型的 item attr

    `output_item_attr`: [string] 输出的 int|double 类型的 item attr

    `keys_separator`: [string] 选配项 分割不同 key 之间的字符，默认为 ','

    `kv_separator`: [string] 选配项 分割 key 和 value 的字符，默认为 ':'

    `input_kv_type` : [int] 选配项 分割 map_weight_string_attr 的 key 和 value 后值的类型，默认为 1, '1' <int, int> '2' <int, double> '3' <string, int> '4' <string, double>

    ------
    查找 input_common_attr 数据格式的 string 分割成 map<key, map<k,v>>，查找 input_item_attr 在外层 map 时，输出 item_attr_map 中 key 在内层 map 下对应的 attr 值

    is_transform_map 参数配置2
    ------
    `input_common_attr`: [string] 输入的 string 类型的 common attr

    `input_item_attr`: [string] 输入的 item attr

    `input_item_attr_type`: [int] 选配项 [0,1] 输入的 item attr 类型 int/string, 默认为int

    `keys_separator`: [string] 选配项 分割第一层不同 key 之间的字符，默认为 ','

    `kv_separator`: [string] 选配项 分割第一层 key 和 value 的字符，默认为 ':'

    `second_keys_separator`: [string] 选配项 分割第二层 key 和 value 的字符，默认为 ','

    `second_kv_separator`: [string] 选配项 分割第二层 key 和 value 的字符，默认为 ':'

    `item_attr_map`: [list] 输出item_attr的映射 配置
      - `key`: [string] 输入的 查找attr_name
      - `output_item_attr`: [string] 输出的 double item attr

    `need_default_first_key`: [bool] 选配项 input_item_attr 不在外层 map, 是否用 default_first_key 去外层 map 查，默认为 false

    `default_first_key`: [int] 选配项 input_item_attr 不在外层 map , 且 need_default_first_key 为 true 用 need_default_first_key 去外层 map 查, 默认为 0

    ------
    查找 input_item_attr 值在不在 input_common_attr[string_list/int_list]中, 存在输出至 output_item_attr, value:1

    is_transform_map 参数配置0
    ------
    `input_common_attr`: [string] 输入的 int_list|string_list common attr

    `input_item_attr`: [string] 输入的 int_list|string_list item attr

    `output_item_attr`: [string] 输出的 int item attr

    `input_common_attr_type`: [string] 选配项 输入common_attr_type，默认为 'int_list'

    `is_transform_map` : [int] 选配项[0,1,2] 选择参数配置1\参数配置2\参数配置0， 配置0必须配置0

    调用示例
    ------
    ``` python
    参数配置1
    .oversea_transform_item_attr_from_common_attr(
      map_weight_string_attr = "second_tag_id_user_select_index",
      input_item_attr = "p_second_tag_id",
      output_item_attr = "user_select_id"
    )
    参数配置2
    .oversea_transform_item_attr_from_common_attr(
      input_common_attr = "second_tag_id_user_select_index",
      input_item_attr = "p_second_tag_id",
      keys_separator = ";",
      kv_separator = "@",
      second_keys_separator = ",",
      second_kv_separator = ":",
      item_attr_map = [
        {
          "key" : "ctr",
          "output_item_attr" : "cs_ctr"
        },
        {
          "key" : "ftr",
          "output_item_attr" : "cs_ftr"
        }
      ],
      is_transform_map = 2
    )
    参数配置0
    .transform_item_attr_from_common_attr(
      input_common_attr = "second_tag_id_user_select_index",
      input_item_attr = "p_second_tag_id",
      output_item_attr = "user_select_id",
      is_transform_map = 0,
    )
    ```
    """
    self._add_processor(OverseaTransformItemAttrFromCommonAttrEnricher(kwargs))
    return self

  def oversea_gen_index_item_attr(self, **kwargs):
    """
    OverseaGenIndexItemAttrEnricher
    ------
    海外获取 item attr 在输入的 common_attr_list 中的 index, 获取 mapping 中 
    
    common_attr_list 里对应 index 位置值, 输出为新的 item_attr
  
    参数配置
    ------
    `input_index_common_attr`: [string] 输入的 int_list common attr

    `input_item_attr`: [string] 输入的 int item attr

    `mappings`: [list] 输出item_attr的映射 配置
      - `input_common_attr_name`: [string] 输入的 int_list_common/double_list_common attr_name 
      - `output_item_attr_name`: [string] 输出的 int_list_item/double_list_item attr
      - `topN`: [int] 选配项，输出的 list item attr 最大长度, 默认 -1 不截断

    调用示例
    ------
    ``` python
    .oversea_gen_index_item_attr(
      input_item_attr = "merchant_tag_id",
      input_index_common_attr = "index_common_merchant_tag_id",
      mappings = [
        {
          "input_common_attr_name": "gsu_pid_list",
          "output_item_attr_name": "pid_list",
          "topN": 50
        },
        {
          "input_common_attr_name": "gsu_aid_list",
          "output_item_attr_name": "aid_list",
          "topN": 50
        },
      ]
    )
    ```
    """
    self._add_processor(OverseaGenIndexItemAttrEnricher(kwargs))
    return self

  def oversea_gen_liner_differential_attr(self, **kwargs):
    """
    OverseaLinerDifferentialEnricher
    ------
    海外线线插值计算，线性差分计算，支持多条线性差分计算
  
    参数配置
    ------
    `input_x_list_common_attr`: [string] 输入的 double_list common attr

    `input_y_list_common_attr`: [string] 输入的 double_list common attr

    `input_x_item_attr`: [string] 输入的 double item attr

    `output_y_item_attr`: [string] 输出的 double item attr

    调用示例
    ------
    ``` python
    .oversea_gen_liner_differential_attr(
      input_x_list_common_attr = "x_common_attr",
      input_y_list_common_attr = "y_common_attr",
      input_x_item_attr = "x_input_item_attr",
      output_y_item_attr = "y_output_item_attr"
    )
    ```
    """
    self._add_processor(OverseaLinerDifferentialEnricher(kwargs))
    return self

  def oversea_truncate(self, **kwargs):
    """
    OverseaTruncateArranger
    ------
    海外截断时保量，支持整体保量上限，被保量 photo 归属单个 attr , 保量 attr 支持优先级配置

    参数配置
    ------
    `size_limit`: [int] [动态参数] 截断当前结果集，最多保留前多少个 item，需大于或等于 0

    `back_fill_ratio`: [dobule] [动态参数] 选配项(0,1] 保量 item 数量占截断数量的比例，最大保量值 size_limit * back_fill_ratio
                        不配置时等同不进行保量, 直接截断

    `bucket_name_attr`: [string] 选配项，用来 perf 覆盖 request_type值

    `need_isolation`: [bool] [动态参数] 选配项，单个 photo 归属多个保量的 attr 时，是否只归属到优先级最高的 attr, 默认 true

    `backfill_to`: [list] 选配项，根据配置对 item 进行保量 attr_name 保量 attr 名称，limit 和 ratio 配置一个，limit 最多保量数量，
                    ratio 保量占截断比例 limit 优先级高，注意：整体保量后可能造成超过最大保留比例 会根据实际 attr 保量数量做截断
                    [0, size_limit * back_fill_ratio] 中保量值不计入保量最大值，最大保量值只针对 [size_limit * back_fill_ratio 至 size_limit]
    - `attr_name`: [string] 必须指定为保量 item_attr 名称用于保量标记
    - `limit`: [int] [动态参数] 被保 item_attr 的最多数量超过 size_limit * back_fill_ratio 会重置为 0
    - `ratio`: [double] [动态参数] 被保 item_attr 占总截断 size_limit 的比例, 超过 size_limit * back_fill_ratio 会重置为 0
    - `priority`: [int] [动态参数] 被保量 photo 属于多个保量 attr 时，优先归属被保量 attr 的优先级，且单个视频只归属一个保量 attr

    调用示例
    ------
    ``` python
    .oversea_truncate(
      size_limit = 200,
      back_fill_ratio = 0.5,
      bucket_name_attr = "bucket_name",
      backfill_to = [
        {
          "attr_name":"partner_flag",
          "limit":20,
          "priority":1,
        },
        {
          "attr_name":"partner_flag",
          "ratio":0.1,
          "priority":2,
        },
      ]
    )
    ```
    """
    self._add_processor(OverseaTruncateArranger(kwargs))
    return self

  def oversea_queue_truncate(self, **kwargs):
    """
    OverseaQueueTruncateArranger
    ------
    海外截断时保量，支持整体保量上限，被保量 photo 归属单个 attr , 保量 attr 支持优先级配置, 不统计 (0, size_limit * backfill_ratio]

    参数配置
    ------
    `size_limit`: [int] [动态参数] 截断当前结果集，最多保留前多少个 item，需大于或等于 0

    `backfill_ratio`: [dobule] [动态参数] 选配项(0,1] 保量 item 数量占截断数量的比例，最大保量值 size_limit * backfill_ratio
                        不配置时等同不进行保量, 直接截断

    `bucket_name_attr`: [string] 选配项，用来 perf 覆盖 request_type值

    `need_perf_topk`: [bool] [动态参数] 选配项，是否 perf 单个保量区间前的每个分组的数, 默认 true

    `need_isolation`: [bool] [动态参数] 选配项，单个 photo 归属多个保量的 attr 时，是否只归属到优先级最高的 attr, 默认 true

    `backfill_to`: [list] 选配项，根据配置对 item 进行保量 attr_name 保量 attr 名称，limit 和 ratio 配置一个，limit 最多保量数量，
                    ratio 保量占所属分组的比例 limit 优先级高，注意：整体保量后可能造成超过最大保留比例 会根据实际 attr 保量数量做截断
                    [0, size_limit * backfill_ratio] 中保量值不计入保量最大值，最大保量值只针对 [size_limit * backfill_ratio 至 size_limit]
    - `attr_name`: [string] 必须指定为保量 item_attr 名称用于保量标记
    - `limit`: [int] [动态参数] 被保 item_attr 的最多数量超过所属 queue_limit 会重置为 0
    - `ratio`: [double] [动态参数] 被保 item_attr 占所属 queue_limit 的比例, 超过 queue_limit * ratio 会重置为 0
    - `priority`: [int] [动态参数] 被保量 photo 属于多个保量 attr 时，优先归属被保量 attr 的优先级，且单个视频只归属一个保量 attr
    - `queue_name`: [string] 必须指定 photo 属于那个队列，且必须归属在 queue_config 的队列里

    `queue_config`: [list] 必须指定队列整体配置, queue_limit 和 queue_ratio 至少配置一个, 都配置优先级 queue_limit 大于 queue_ratio
    - `queue_name`: [string] 队列名称
    - `queue_limit`: [int] [动态参数] 队列最大保量数量，等于 min(queue_limit, size_limit * backfill_ratio), 队列下的所有保量分组超过队列上限会等比例截断
    - `queue_ratio`: [double] [动态参数] 队列最大保量比例，值等于 int(size_limit * backfill_ratio) * queue_ratio, 队列下的所有保量分组超过队列上限会等比例截断

    调用示例
    ------
    ``` python
    .oversea_queue_truncate(
      size_limit = 500,
      backfill_ratio = 0.1,
      bucket_name_attr = "bucket_name",
      backfill_to = [
        {
          "attr_name":"partner_flag",
          "limit":20,
          "priority":1,
          "queue_name": "produce"
        },
        {
          "attr_name":"cs_flag",
          "limit":20,
          "priority":2,
          "queue_name": "produce"
        },
        {
          "attr_name":"consume_flag",
          "limit":20,
          "priority":3,
          "queue_name": "consume"
        },
      ],
      queue_config = [
        {
          "queue_name":"produce",
          "queue_limit":20,
        },
        {
          "queue_name":"consume",
          "queue_limit":20,
        },
      ]
    )
    ```
    """
    self._add_processor(OverseaQueueTruncateArranger(kwargs))
    return self

  def oversea_limit_by_reason(self, ** kwargs):
    """
    OverseaLimitByReasonArranger
    ------
    对结果集大小分 reason 进行阶段，支持不同 reason 截断不同数量。

    参数配置
    ------
    `reason_limit_queue`: [list] 每个 reason 需被截断的大小
      - `reason`: [int] 被截断 reason 值
      - `limit_size`: [int] [动态参数] 截断的数目

    调用示例
    ------
    ``` python
    .oversea_limit_by_reason(
      reason_limit_queue = [
        {
          "reason": 2401,
          "limit_size": 500
        },
        {
          "reason": 2402,
          "limit_size": "{{2402_limit_size}}"
        }
      ]
    )
    ```
    """

    self._add_processor(OverseaLimitByReasonArranger(kwargs))
    return self

  def oversea_calc_similarity_items(self, **kwargs):
    """
    OverseaSimilarityItemsEnricher
    ------
    生成候选集的相似度矩阵,存储候选集后面元素与前面元素相似度大于 threshold 的元素的相似度分.
    用于过滤与前面 item 相似度较高的 item.

    参数配置
    ------
    `item_embedding_attr`: [string] item embedding

    `export_item_attr`: [string] 输出的负反馈得分

    `allow_empty_embedding`: [bool] 默认值为 False, 不允许. 若为 True 则随机数初始化 embedding.

    `similarity_threshold`: [double] 动态参数, 是 (-1, 1) 之间的数, 一般是设一个 (0.5, 1) 之间的数

    `max_redundancy_count`: [int] 动态参数, 是 [0, total_num) 之间的数, 排在当前视频后面且相似度大于 similarity_threshold 的视频数, 默认值 0.

    调用示例
    ------
    ``` python
    .oversea_calc_similarity_items(
        name=name("calc_similarity_items"),
        item_embedding_attr="tower_embedding",
        similarity_threshold=0.7,
        export_item_attr="filter_similarity_score",
    ```
    """
    self._add_processor(OverseaSimilarityItemsEnricher(kwargs))
    return self

  def oversea_calc_positive_feedback(self, **kwargs):
    """
    OverseaPositiveFeedbackEnricher
    ------
    用于候选集与正反馈集相似内容的保量策略. 使用前联系 author

    参数配置
    ------
    `item_embedding_attr`: [string] item embedding

    `pos_item_embedding_attr`: [string] 正反馈 items 的 embedding_attr_name

    `export_item_attr`: [string] 输出的负反馈得分

    `positive_items_from_attr`: [string] 正反馈 items 列表, common_attr

    `allow_empty_embedding`: [bool] 默认值为 False, 不允许. 若为 True 则随机数初始化 embedding.

    `similarity_threshold`: [double] 动态参数, (-1, 1) 之间的数, 一般是设一个 (0.5, 1) 之间的数, 默认值 1.0.

    `reserve_similar_count`: [int] 动态参数, [0, total_size] 之间的数, 要保量的与正反馈item 相似候选集的个数, 根据需求设值, 默认值 0.

    调用示例
    ------
    ``` python
    .oversea_calc_positive_feedback(
        name=name("calc_positive_feedback"),
        item_embedding_attr="tower_embedding",
        pos_item_embedding_attr="pos_tower_embedding",
        export_item_attr="posiive_feedback_score",
        positive_items_from_attr="positive_items",
        similarity_threshold=0.8,
        reserve_similar_count=5)
    ```
    """
    self._add_processor(OverseaPositiveFeedbackEnricher(kwargs))
    return self

  def oversea_calc_negative_feedback(self, **kwargs):
    """
    KsibNegativeFeedbackEnricher
    ------
    根据负反馈 items embedding 矩阵和候选集 items embedding 矩阵做乘积,
    生成候选集与负反馈集的相似度矩阵,再按照 rowwise sum 计算候选集与负反馈集
    的相似分之和,和越大表示候选 item 与负反馈集越相似.

    参数配置
    ------
    `item_embedding_attr`: [string] item embedding

    `neg_item_embedding_attr`: [string] 负反馈 items 的 embedding_attr_name

    `export_item_attr`: [string] 输出的负反馈得分

    `negative_items_from_attr`: [string] 负反馈 items 列表, common_attr

    `allow_empty_embedding`: [bool] 默认值为 False, 不允许. 若为 True 则随机数初始化 embedding.

    `enable_filter`: [bool] 默认值为 False, 若为 True 要设置 filter_threshold, 过滤掉 sim_score >= filter_threshold 的 items

    `filter_threshold`: [double] 动态参数, enable_filter 为 True 时设置, 是 (-1, 1) 之间的数, 一般是设一个 (0.5, 1) 之间的数

    调用示例
    ------
    ``` python
    .oversea_calc_negative_feedback(
        name=name("calc_negative_feedback"),
        item_embedding_attr="tower_embedding",
        neg_item_embedding_attr="neg_tower_embedding",
        export_item_attr="negative_feedback_score",
        negative_items_from_attr="negative_items")
    ```
    """
    self._add_processor(KsibNegativeFeedbackEnricher(kwargs))
    return self

  def oversea_calc_interest_explore(self, **kwargs):
    """
    KsibInterestExploreEnricher
    ------
    计算用户看过的 items 和候选 items 相似度, 越不相似则探索分越大

    参数配置
    ------
    `item_embedding_attr`: [string] item embedding

    `explore_item_embedding_attr`: [string] 用于兴趣探索的 items 的 embedding_attr_name

    `explore_items_from_attr`: [string] 用于兴趣探索 items 列表, common_attr

    `export_item_attr`: [string] 输出的兴趣探索得分

    `allow_empty_embedding`: [bool] 默认值为 False, 不允许. 若为 True 则随机数初始化 embedding.

    `enable_reset_similarity`: [bool] 默认值为 False, 若为 True 要设置 filter_threshold, 过滤掉 sim_score >= filter_threshold 的 items

    调用示例
    ------
    ``` python
    .oversea_calc_interest_explore(
        name=name("calc_interest_explore"),
        item_embedding_attr="tower_embedding",
        explore_items_from_attr="explore_items",
        explore_item_embedding_attr="explore_tower_embedding",
        export_item_attr="interest_explore_score"
    )
    ```
    """
    self._add_processor(KsibInterestExploreEnricher(kwargs))
    return self

  def oversea_calc_tag_diversity(self, **kwargs):
    """
    KsibTagDiversityEnricher
    ------
    按照 item 的 tag 计算分组，每种 tag value 的前 n 个 item 值加一
    需要先用 set_default_value 设置默认值

    参数配置
    ------
    `tag_diversity_attr`: [string] 输出的 tag diversity attr

    `tag_diversity_configs`: [list] tag diversity 配置
      - `enable`: [bool] [动态参数] 是否开启这路 tag diversity
      - `attr_name`: [string] 输入的 tag attr
      - `top_k`: [int] [动态参数] tag value 的前 top_k 个 item 值加一

    调用示例
    ------
    ``` python
    .set_default_value(
      item_attrs=[
        {
          "name": "tag_diversity",
          "type": "int",
          "value": 0
        }
      ]
    ).oversea_calc_tag_diversity(
      tag_diversity_attr = "tag_diversity",
      tag_diversity_configs = [
        {
          "enable": True,
          "attr_name": "a_id",
          "top_k": 10
        },
        {
          "enable": "{{enable_tag}}",
          "attr_name": "primary_tag_id",
          "top_k": "{{top_k_tag}}"
        }
      ]
    )
    ```
    """

    self._add_processor(KsibTagDiversityEnricher(kwargs))
    return self

  def calc_bucket_score(self, **kwargs):
    """
    OverseaBucketScoreEnricher
    ------
    对recoResults计算分桶分, 分桶方式支持tag/age/duration

    参数配置
    ------
    `score_attr`: [string] 基于此item_attr桶内排序

    `bucket_attr`: [string] 基于此item_attr分桶

    `output_item_attr`: [string] 计算分桶分存入此item_attr

    `weight_string_attr`: [string] 桶权重kv配置

    `bucket_num_attr`: [string] 平均分桶数量

    `bucket_points_attr`: [string] 阈值分桶配置

    `use_max_bucket_size_attr`: [string] es不同长度桶使用最大值

    `regulator_attr`: [string] es公式参数

    `bucket_type`: [string] 支持age/tag/duration

    `cut_ratio`: [string] 对每个桶末尾数据进行截断
    """
    self._add_processor(OverseaBucketScoreEnricher(kwargs))
    return self

  def calc_cross_queue(self, **kwargs):
    """
    OverseaCrossQueueEnricher
    ------
    乘法队列

    参数配置
    ------
    `left_item_str`: [string] left项

    `right_item_str`: [string] right项

    `output_item_str`: [string] 输出attr
    """
    self._add_processor(OverseaCrossQueueEnricher(kwargs))
    return self

  def ksib_one_dimensional_kmeans(self, **kwargs):
    """
    KsibOneDimensionalKmeansEnricher
    ------
    一维 kmeans 聚类，初始点为分数排序后的 k 分点
    输出结果 cluster_id 按照分数排序，越大的分数，聚类 id 越小，cluster_id 从 0 开始

    参数配置
    ------
    `kmeans_configs`: [list] kmeans 聚类 配置
      - `enable`: [bool][动态参数] 该 item_attr 聚类开关，默认为 true
      - `input_attr_name`: [string] 输入的 item_attr, 聚类的 attr
      - `output_attr_name`: [string] 输出的 item_attr, 聚类 cluster 输出值，输出类型为 int
      - `cluster_num`: [int][动态参数] 聚类数量
      - `max_iter_num`: [int] 选配项，最大迭代次数，默认为 50 次

    调用示例
    ------
    ``` python
    .ksib_one_dimensional_kmeans(
      name="ksib_one_dimensional_kmeans",
      kmeans_configs=[
        {
          "input_attr_name": "pxtr",
          "output_attr_name": "pxtr_cluster",
          "cluster_num": 10,
          "max_iter_num": 10
        }
      ]
    )
    ```
    """
    self._add_processor(KsibOneDimensionalKmeansEnricher(kwargs))
    return self

  def oversea_calc_bit_wise_and(self, **kwargs):
    """
    KsibBitWiseAndEnricher
    ------
    对 item attr 做按位与运算

    参数配置
    ------
    `bitwise_a_item_attr`: [string] 被按位与的 item attr

    `bitwise_b_val`: [int][动态参数] 按位与的值

    `output_item_attr`: [string] 按位与结果输出的 item attr

    调用示例
    ------
    ``` python
    .oversea_calc_bit_wise_and(
      bitwise_a_item_attr="tag",
      bitwise_b_val=3,
      output_item_attr="bitwise_and_output_attr"
    )
    ```
    """
    self._add_processor(KsibBitWiseAndEnricher(kwargs))
    return self

  def oversea_boost_rule(self, **kwargs):
    """
    OverseaBoostRuleEnricher
    ------
    对 recoResults 计算 boost 值

    参数配置
    ------
    `formula_version`: [int] 选配项，计算公式版本，默认为 0
      - 0: `boost_score = boost_score * (const_score + xtr_score * weight) ^ temperature`

    `boost_score_attr`: [string] 被 boost 的 score 字段

    `xtr_score_attr`: [string] 输入的 xtr score 字段

    `xtr_threshold`: [double] [动态参数] 选配项 xtr_score_attr 小于等于此值时，不计算，如果不设置，则忽略此项过滤。

    `xtr_rank_attr`: [string] 选配项 输入的 xtr rank 字段

    `xtr_rank_threshold`: [int] [动态参数] 选配项 xtr_rank_attr 大于等于此值时，不计算，如果不设置，则忽略此项过滤。

    `const_score`: [double][动态参数] 常数值

    `weight`: [double][动态参数] 权重

    `temperature`: [double][动态参数] 调节因子

    调用示例
    ------
    ``` python
    .oversea_boost_rule(
      # 全部 item 计算
      boost_score_attr="es_score",
      xtr_score_attr="evr",
      const_score=1.0,
      weight=0.3,
      temperature=1
    )
    .oversea_boost_rule(
      # 对 evr_seq 的 top 100 的 item 进行计算
      boost_score_attr="es_score",
      xtr_score_attr="evr",
      xtr_rank_attr="evr_seq",
      xtr_rank_threshold=100,
      const_score=1.0,
      weight=0.3,
      temperature=1
    )
    .oversea_boost_rule(
      # 对 evr > 0.5 的 item 进行计算
      boost_score_attr="es_score",
      xtr_score_attr="evr",
      xtr_threshold=0.5,
      const_score=1.0,
      weight=0.3,
      temperature=1
    )
    ```
    """
    self._add_processor(OverseaBoostRuleEnricher(kwargs))
    return self

  def oversea_scaling_boost(self, **kwargs):
    """
    OverseaScalingBoostEnricher
    ------
    对 score 进行 boost，并按照业务组对 boost 系数进行线性 rescale， rescale 公式：
    "if(queue_factor > lower_bound and MAX('queue_factor') > upper_bound, lower_bound + (upper_bound-lower_bound) * (queue_factor - 1) / (MAX('queue_factor') - 1), queue_factor)"
    
    queue_factor：当前队列所有 boost 系数连乘的分数。

    MAX('queue_factor'): 当前队列所有 boost 系数连乘的最大分数。
    参数配置
    ------
    `boost_queue_configs`: [dict] key 为业务组名称，value 为该业务组的 boost 配置。
      - `factors`: [list] 当前业务队列的 boost 系数 item attr 名称列表，系数 item attr 必须为 double 类型。
      - `scale_bound`: [list] [动态参数] 选配项，当前业务组的 rescale 的上下界，支持 double 类型动态参数，不配置则使用默认上下界。

    `scale_bound`: [list] [动态参数] 二元数组表示默认的 rescale 的上下界，支持 double 类型动态参数。

    `boost_score_attr`: [string] 需要被 boost 的 score item attr，item attr 必须为 double 类型。

    `save_queue_factor_to_item_attr`: [bool] 是否将队列系数（rescale 前后）存入 item attr 中，形式为 queue_name + '_factor_before_rescale' (rescale 前系数)
                                              和 queue_name + 'factor' （rescale 后系数），可缺省，默认 false。

    调用示例
    ------
    ``` python
    .oversea_scaling_boost(
      boost_queue_configs = {
        # 消费队列
        "consume": {
          "factors": ["consume_factor_1", "consume_factor_2"],
          "scale_bound": [5.0, 10.0]
        },
        "ecommerce": {
          "factors": ["ecommerce_factor_1"]
        }
      },
      scale_bound = ["{{scale_lower_bound}}", "{{scale_upper_bound}}"],
      boost_score_attr = "pre_rank_score"
    )
    ```
    """
    self._add_processor(OverseaScalingBoostEnricher(kwargs))
    return self
  

  def oversea_multi_boost_rule(self, **kwargs):
    """
    OverseaMultiBoostRuleEnricher
    ------
    对 recoResults 计算 boost 值
    *注意： 所有拼接参数的size必须一致
           此processor 会跳过依赖检查，使用前请咨询dingzhaoying

    参数配置
    ------
    `formula_version`: [int] 选配项，计算公式版本，默认为 0
      - 0: `boost_score = boost_score * (const_score + xtr_score * weight) ^ temperature`

    `boost_score_attr`: [string] 被 boost 的 score 字段

    `xtr_score_attr_str`: [string] 输入的 xtr score 字段, 支持多字段拼接为一个字符串, common_attr

    `xtr_rank_attr_str`: [string] 输入的 xtr rank 字段, 支持多字段拼接为一个字符串, common_attr

    `xtr_rank_threshold_str`: [string] xtr_rank_attr的threshold, 小于此值时，才会被计算，支持多个字段拼接为一个字符串, common_attr

    `const_score_str`: [string] 常数值, 支持多值拼接为一个字符串, common_attr

    `weight_str`: [string] 权重, 支持多值拼接为一个字符串, common_attr

    `temperature_str`: [string] 调节因子, 支持多值拼接为一个字符串, common_attr

    `delimiters`: [string] 选配项 字符串拼接分隔符，如果不设置，默认为','

    `enable_reduce_score`: [bool] [动态参数] 默认false, 如果为true， 则对boost_score进行压缩

    `reduce_weight`: [double] [动态参数] 默认0.0, 压缩boost_score的权重

    `reduce_temperature`: [double] [动态参数] 默认1.0, 压缩boost_score的调节因子

    调用示例
    ------
    ``` python
    .oversea_multi_boost_rule(
      # 对 evr_seq 的 top 100 和 lvr_seq 的 top 10 的 item 进行计算
      # ab_xtr_score_attr_str = "fr_es_evr,fr_es_lvr"
      # ab_xtr_rank_attr_str = "fr_es_evr_rank,fr_es_lvr_rank"
      # ab_xtr_rank_attr_str = "100,10"
      boost_score_attr="es_score",
      xtr_score_attr_str="ab_xtr_score_attr_str",
      xtr_rank_attr_str="ab_xtr_rank_attr_str",
      xtr_rank_threshold_str="ab_xtr_rank_threshold_str",
      const_score_str="ab_const_score_str",
      weight_str="ab_weight_str",
      temperature_str="ab_temperature_str"
    )
    ```
    """
    self._add_processor(OverseaMultiBoostRuleEnricher(kwargs))
    return self

  def oversea_ssd_variant(self, **kwargs):
    """
    OverseaSsdVariantArranger
    ------
    ssd 算法打散, 支持跨屏打散. 使用前请校验 embedding 的覆盖率以及返回值, 防止出现大量空或为0向量的情况.

    参数配置
    ------

    `limit`: [int] 动态参数,需要返回的计算个数

    `theta`: [double] 动态参数, (0, 1) 之间的浮点数

    `allow_empty_embedding`: [bool] 动态参数, 是否允许 item embedding 为空

    `stable_ssd`: [bool] 动态参数,是否用稳定 ssd, 默认为 true

    `optimized_ssd`: [bool] 动态参数,是否用性能优化版 ssd, 默认为 false

    `cross_screen_variant`: [bool] 动态参数,是否开启跨屏打散, 默认为 false

    `cross_screen_items_from_attr`: [string] 跨屏打散时需要输入的 跨屏 item 集合, common attr

    `cross_screen_item_embedding_attr`: [string] 跨屏 item 集合 对应的 item embedding attr

    `item_embedding_attr`: [string] 候选集 item embedding

    `ranking_score_attr`: [string] 使用的排序分

    调用示例
    ------
    ``` python
    .oversea_ssd_variant(
      limit=8,
      theta=0.4,
      allow_empty_embedding=False,
      stable_ssd=True,
      cross_screen_variant=True,
      cross_screen_items_from_attr="prev_items",
      cross_screen_item_embedding_attr="tower_embedding",
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score"
    )
    ```
    """

    self._add_processor(OverseaSsdVariantArranger(kwargs))
    return self

  def oversea_compat_double_list(self, **kwargs):
    """
    KsibCompatDoubleListEnricher
    ------
    用于兼容精排结果，可能输出 double 或者 double list, 都处理为 double

    参数配置
    ------
    `input_attrs`: [list] 必配项, 输出的 double list attrs

    `output_attrs`: [list] 必配项, 输出的 double attrs

    调用示例
    ------
    ``` python
    .oversea_compat_double_list(
      input_attrs=["pevr_list", "pftr_list"],
      output_attrs=["pevr_0", "pevr_1"]
    )
    ```
    """
    self._add_processor(KsibCompatDoubleListEnricher(kwargs))
    return self

  def oversea_position_attr_enricher(self, **kwargs):
    """
    OverseaPositionAttrEnricher
    ------
    生成一次请求不同 position 的 attr

    参数配置
    ------
    `import_item_attrs`: [list] 必配项, 支持 double/double_list/int/int_list/string/string_list 类型

    `export_common_attrs`: [list] 必配项, 不同 position 的 attr, 例如一刷 4 个视频, ["evr_0", "evr_1", "evr_2", "evr_3"]

    `topk`: [int] 只计算前 topk 个 item, 默认值为 8


    调用示例
    ------
    ``` python
    .oversea_position_attr_enricher(
      import_item_attrs=["pevr", "pftr"],
      export_common_attrs=["pevr_0", "pevr_1", "pevr_2", "pevr_3", "pftr_0", "pftr_1", "pftr_2" "pftr_3"],
      topk=8
    )
    ```
    """
    self._add_processor(OverseaPositionAttrEnricher(kwargs))
    return self

  def oversea_list_items_slots_to_common_slots(self, **kwargs):
    """
    OverseaListItemsSlotsToCommonSlotsEnricher
    ------
    list-wise rerank 样本流,将多个 item 的 item_slots 变为 common_slots.
    例如 4 个 item, offset=100, item_slots=["34", "35"], 则 common_slots=["3400", "3401", "3402", "3403", "3500", "3501", "3502" "3503"]

    参数配置
    ------

    `import_item_attrs`: [list] 必配项, item_slots

    `export_common_attrs`: [list] 必配项, 多个 item 的 item_slots 转变为 common_slots

    `page_size`: [int]  将几个 item 合并为 1 个 list item, 默认值为 8

    `offset`: [int]  新的common_attr pos_item_slot=item_slot * offset + i, i=[0, page_size), 默认值为 100


    调用示例
    ------
    ``` python
    .oversea_list_items_slots_to_common_slots(
      import_item_attrs=["34", "35"],
      export_common_attrs=["3400", "3401", "3402", "3403", "3500", "3501", "3502" "3503"]
      page_size=4,
      offset=100
    )
    ```
    """

    self._add_processor(OverseaListItemsSlotsToCommonSlotsEnricher(kwargs))
    return self

  def oversea_ensemble_score(self, **kwargs):
    """
    OverseaEnsembleScoreEnricher
    ------
    计算es score, 支持QuantileTrans、累积步长HyperTrans两种方式

    参数配置
    ------
    `channels`: [list] 队列配置, 每个队列需配置
      - `name`: [string] 队列名
      - `enabled`: [bool] [动态参数] 默认True
      - `weight`: [double] [动态参数] 该队列的权重
      - `as_seq_value`: [bool] 默认False, 如果传入_rank序attr, 需要转换成原始值使用

    `output_attr`: [string] 将最后计算出的ensemble综合分存入指定的item_attr

    `hyper_params`: [string] [动态参数] HyperTrans参数

    `quantile_channel`: [string] [动态参数] 选配项, 指定使用QuantileTrans的channel

    `quantile_point`: [string] [动态参数] 选配项, QuantileTrans参数

    `default_value`: [double] 选配项, 若item不存在指定的item_attr则使用该默认值参与排序, 默认0

    `epsilon`: [double] 选配项, 比较是否相等时的精度要求, 默认1e-9

    """
    self._add_processor(OverseaEnsembleScoreEnricher(kwargs))
    return self

  def oversea_multiply_rank(self, **kwargs):
    """
    OverseaMultiplyRankEnricher
    ------
    乘法融合排序
    *注意： 此processor 会跳过依赖检查, 使用前请咨询dingzhaoying

    参数配置
    ------
    `formula_version`: [int] [动态参数], 计算公式版本, 默认为0, 公式1和2只对xtr_score做变换, 再通过公式0计算最终score,公式1中的score输入为rank值, 公式2需要配合normalization为2使用
      - 0: `base_score_attr = base_score_attr * (const_score + xtr_score * weight) ^ temperature`
      - 1: `xtr_score = weight * (e ^ (hyper_scala * (2.0 * (score / item_num) - 1.0)) - e ^ (-hyper_scala * (2.0 * (score / item_num) - 1.0)))`
      - 2: `xtr_score = scale_factor * log(1 / (score + 0.01) - shift_factor)`
      - 3: `base_score_attr = log(base_score_attr * (const_score + xtr_score * weight) ^ temperature)`

    `base_score_attr`: [string] 乘法的基数字段

    `weight_map_str`: [string] 输入的 xtr 及其权重, kv 格式， key 是 xtr name, value 是权重, common_attr

    `rank_threshold_map_str`: [string] 选配项， xtr_rank_attr的threshold, 配置时， rank小于此值时才会被计算, 默认全部计算, common_attr

    `const_map_str`: [string] 选配项， 常数值, kv格式, key 是 xtr name, 默认1.0, common_attr

    `temperature_map_str`: [string] 选配项，调节因子, key 是 xtr name, 默认1.0, common_attr

    `user_bias_temperature_str` : [string] 选配项, user_bias的temperature, key 是 xtr name, 默认为空, common_attr
      - xtr_weight *= (1.0 + xtr_bias) ^ temperture

    `user_bias_suffix` : [string] [动态参数] 选配项, xtr name 的后缀, 默认"_bias", 组合为个性化调权的xtr_bias

    `enable_reduce_score`: [bool] [动态参数] 默认false, 如果为true, 则对base_score进行压缩
      - true: base_score_attr = (1.0 + reduce_weight * base_score_attr) ^ reduce_temperature

    `reduce_weight`: [double] [动态参数] 默认0.0, 压缩base_score的权重

    `reduce_temperature`: [double] [动态参数] 默认1.0, 压缩base_score的调节因子

    `normalization_version`: [int] [动态参数] 默认为0, 不做任何归一化
      - 1: `score = (score - min_score) / (max_score - min_score)`
      - 2: `score = (max_score - score) / (max_score - min_score)`

    `possible_import_common_attrs`: [string_list] processor 依赖的 common_attr

    `possible_import_item_attrs`: [string_list] processor 依赖的 item_attr

    `enable_different_shape`: [bool] [动态参数] 默认false, 如果为true, 则可对不同队列做不同归一化和公式变换
    `formula_version_map_str`: [string] 选配项, 队列变换公式版本, 默认为空, key为xtr name, value为变换公式版本, 与formula_version参数值相一致
    `normalization_version_map_str`: : [string] 选配项, 队列归一化版本, 默认为空, key为xtr name, value为归一化版本, 与normalization_version参数值相一致

    `sigmoid_temperature_map_str`: [string] 选配项, formula_version为4时的调节因子, key 是 xtr name, 默认100.0, common_attr
    `rles_temperature_str`: [string] 选配项, rles action 对weight的缩放控制
    `rles_second_order_list_str`: [string] 选配项, rles sec order 的xtr list

    调用示例
    ------
    ``` python
    .oversea_multiply_rank(
      # ab_weight_map_str = "fr_es_evr:0.5,fr_es_lvr:0.1"
      # ab_rank_threshold_map_str = "fr_es_evr_rank:100"
      base_score_attr="es_score",
      weight_map_str="ab_weight_map_str",
      rank_threshold_map_str = "ab_rank_threshold_map_str"
    )
    ```
    """
    self._add_processor(OverseaMultiplyRankEnricher(kwargs))
    return self

  def oversea_deep_dpp_variant(self, **kwargs):
    """
    OverseaDeepDppArranger
    ------
    kwaipro dpp 多样性策略

    参数配置
    ------
    `limit`: [int] [动态参数] dpp 计算的 item 数

    `theta`: [double] [动态参数] 相关性和多样性权重 (0, 1)

    `epsilon`: [double] [动态参数] 信息增益阈值, 一般取值范围(0, 0.000001)

    `item_embedding_attr`: [string] dpp 使用的 item embedding attr

    `ranking_score_attr`: [string] 选配项, dpp 计算使用的 ranking score attr

    `decay_lambda_attr`: [string] 选配项, dpp 计算使用的 decay lambda attr

    `allow_empty_embedding`: [bool] [动态参数] 允许 item embedding 为空, 默认为 False

    `allow_results_less_limit`: [bool] [动态参数] 允许 dpp 计算结果数小于 limit, 默认为 False

    `need_normalize`: [int] 选配项，是否需要对 embedding 归一化，默认为 True

    `output_score_attr`: [string] [可选] dpp 输出分, 用来算 es dpp rank

    调用示例
    ------
    ``` python
    .oversea_deep_dpp_variant(
      limit=8,
      theta=0.5,
      epsilon=0.000001,
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score"
    )
    ```
    """
    self._add_processor(OverseaDeepDppArranger(kwargs))
    return self

  def oversea_dpp_variant(self, **kwargs):
    """
    OverseaDppVariantArranger
    ------
    oversea dpp 算法

    参数配置
    ------
    `limit`: [int] [动态参数] 选配项, dpp 计算的 item 数, 默认值 8

    `theta`: [double] [动态参数] 必配项, 相关性和多样性权重 (0, 1)

    `epsilon`: [double] 选配项, 信息增益阈值, 一般取值范围(0, 0.000001), 默认值 0.000001

    `item_embedding_attr`: [string] 必配项, dpp 使用的 item embedding attr

    `ranking_score_attr`: [string] 必配项, dpp 计算使用的 ranking score attr

    `allow_empty_embedding`: [bool] [动态参数] 选配项, 允许 item embedding 为空, 默认为 False

    `allow_results_less_limit`: [bool] [动态参数] 选配项, 允许 dpp 计算结果数小于 limit, 默认为 False

    `use_stride_dpp`: [bool] [动态参数] 选配项, 使用 stride dpp, 默认值 False

    `stride`: [int] [动态参数] use_stride_dpp=True 时使用, 值域 [1, limit], 一般 stride <= 40 默认值为 40

    `allow_less_stride`: [bool] [动态参数] 选配项, 允许 StrideDpp 一次 stride 计算时选取个数小于 stride 个, 默认为 True

    `output_score_attr`: [string] 选配项, dpp 输出分, 用来算 es dpp rank

    调用示例
    ------
    ``` python
    .oversea_dpp_variant(
      limit=100,
      theta=0.5,
      use_stride_dpp=True,
      stride=40,
      allow_less_stride=True,
      epsilon=0.000001,
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score",
      output_score_attr="stride_dpp_score"
    )
    ```
    """
    self._add_processor(OverseaDppVariantArranger(kwargs))
    return self

  def oversea_inject_context_info(self, **kwargs):
    """
    OverseaInjectContextInfoEnricher
    ------
    优化 ksib::reco::ContextInfo 的生成，通过字符串拼接的方式，将 context_attr 写入到 context_info_attr 中

    参数配置
    ------
    `context_info_attr`: [string] 输出的 context_info string ItemAttr

    `inject_configs`: [object] 输入的 context attr 配置
      - `context_attr`: [string] 输入的 context attr 的 item attr 名字
      - `rename_to`: [string] 选配项, 重命名输出的 context attr

    `output_item_attr`: [string] 输出拼接得到的 context_info string ItemAttr

    调用示例
    ------
    ``` python
    .oversea_inject_context_info(
      context_info_attr = "NewSerializedContextInfo",
      inject_configs = [
        {
            "context_attr": "int_value",
            "rename_to": "int_val"
        },
        {
            "context_attr": "float_value",
            "rename_to": "float_val"
        },
        { "context_attr": "string_value" },
        { "context_attr": "int_list_value" },
        { "context_attr": "float_list_value" },
        { "context_attr": "string_list_value" }
      ],
      output_item_attr = "NewSerializedContextInfo"
    )
    ```
    """
    self._add_processor(OverseaInjectContextInfoEnricher(kwargs))
    return self

  def oversea_inject_context_info_with_black_list(self, **kwargs):
    """
    OverseaInjectContextInfoWithBlackListEnricher
    ------
    优化 ksib::reco::ContextInfo 的生成，通过字符串拼接的方式，将 context_attr 写入到 context_info_attr 中

    参数配置
    ------
    `context_info_attr`: [string] 输出的 context_info string ItemAttr

    `inject_configs`: [object] 输入的 context attr 配置
      - `context_attr`: [string] 输入的 context attr 的 item attr 名字
      - `rename_to`: [string] 选配项, 重命名输出的 context attr

    `output_item_attr`: [string] 输出拼接得到的 context_info string ItemAttr

    `blacklist_kconf_key`: [string] [动态参数] 输入的 context attr 的 blacklist kconf key

    调用示例
    ------
    ``` python
    .oversea_inject_context_info(
      context_info_attr = "NewSerializedContextInfo",
      inject_configs = [
        {
            "context_attr": "int_value",
            "rename_to": "int_val"
        },
        {
            "context_attr": "float_value",
            "rename_to": "float_val"
        },
        { "context_attr": "string_value" },
        { "context_attr": "int_list_value" },
        { "context_attr": "float_list_value" },
        { "context_attr": "string_list_value" }
      ],
      output_item_attr = "NewSerializedContextInfo"
    )
    ```
    """
    self._add_processor(OverseaInjectContextInfoWithBlackListEnricher(kwargs))
    return self

  def oversea_tag_extract_by_type(self, **kwargs):
    """
    OverseaTagExtractByTypeEnricher
    ------
    按照 int list 类型的 type item attr， 从 int list 类型的 tag item attr 中筛选出指定的 type 对应的 tag

    例如： tag = [101, 202, 303], type = [1, 1, 2], type_list = [1]

    输出一个list attr： [101, 202]

    参数配置
    ------
    `tag_item_attr`: [string] 输入的 tag attr, 需要是 int list 类型

    `type_item_attr`: [string] 输入的 type attr, 需要是 int list 类型

    `extract_type_list`: [int list] [动态参数] 需要筛选的type

    `output_item_attr`: [string] 输出的item attr，是筛选得到的tag

    调用示例
    ------
    ``` python
    .oversea_tag_extract_by_type(
      tag_item_attr = "tag_list",
      type_item_attr = "type_list",
      extract_type_list = [1, 2],
      output_item_attr = "selected_tag"
    )
    ```
    """
    self._add_processor(OverseaTagExtractByTypeEnricher(kwargs))
    return self

  def oversea_aggregate_tag_weight(self, **kwargs):
    """
    OverseaAggregateTagWeight
    ------
    按照 item 的 weight，对 item 的 tag 进行统计聚合

    用于统计 item 列表中的每个 tag 分别有多少个（加权的情况下就是多少总权重）

    参数配置
    ------
    `tag_item_attr`: [string] 输入的 tag attr, 支持 int | int list | string | string list

    `weight_item_attr`: [string] item 的权重，float 类型

    `output_tag_common_attr`: [string] 输出的 tag 列表，根据 tag item attr的类型，选择 int | string 类型

    `output_weight_common_attr`: [string] 输出的 weight 列表，对应 tag common attr 的每一项的权重

    `tag_count_threshold`: [int] 计数超过多少的 tag 才输出，默认 0

    调用示例
    ------
    ``` python
    .oversea_aggregate_tag_weight(
      tag_item_attr = "tag_list",
      weight_item_attr = "weight",
      output_tag_common_attr = "stat_tag_list",
      output_weight_common_attr = "stat_weight_list",
      tag_count_threshold = 3
    )
    ```
    """
    self._add_processor(OverseaAggregateTagWeightEnricher(kwargs))
    return self

  def oversea_weighted_ssd_variant(self, **kwargs):
    """
    OverseaWeightedSsdVariantArranger
    ------
    weighted ssd 算法打散, 支持跨屏打散.

    参数配置
    ------

    `limit`: [int] 动态参数,需要返回的计算个数

    `theta`: [double] 动态参数, (0, 1) 之间的浮点数

    `gamma`: [double] 动态参数, (0, 1) 之间的浮点数, 用于跨屏weight变换

    `allow_empty_embedding`: [bool] 动态参数, 是否允许 item embedding 为空

    `stable_ssd`: [bool] 动态参数,是否用稳定 ssd, 默认为 true

    `optimized_ssd`: [bool] 动态参数,是否用性能优化版 ssd, 默认为 false

    `cross_screen_variant`: [bool] 动态参数,是否开启跨屏打散, 默认为 false

    `cross_screen_items_from_attr`: [string] 跨屏打散时需要输入的 跨屏 item 集合, common attr

    `cross_screen_item_embedding_attr`: [string] 跨屏 item 集合 对应的 item embedding attr

    `cross_screen_item_weight_from_attr`: [string] 跨屏 item 集合 对应的 weight  attr

    `item_embedding_attr`: [string] 候选集 item embedding

    `ranking_score_attr`: [string] 使用的排序分

    调用示例
    ------
    ``` python
    .oversea_weighted_ssd_variant(
      limit=8,
      theta=0.4,
      gamma=0.5,
      allow_empty_embedding=False,
      stable_ssd=True,
      cross_screen_variant=True,
      cross_screen_items_from_attr="prev_items",
      cross_screen_item_embedding_attr="tower_embedding",
      cross_screen_item_weight_from_attr="cross_screen_items_weight",
      item_embedding_attr="tower_embedding",
      ranking_score_attr="es_score"
    )
    ```
    """

    self._add_processor(OverseaWeightedSsdVariantArranger(kwargs))
    return self

  def oversea_scores_integrade(self, **kwargs):
    """
    OverseaScoresIntegradeEnricher
    ------
    两个 score 进行融合, 乘法融合score1 * (score2^alpha)或加法融合alpha * score1 + (1 - alpha) * score2

    参数配置
    ------
    `first_input_attr`: [string] item_attr, 进行融合的第一个 attr.

    `second_input_attr`: [string] item_attr, 进行融合的第二个 attr.

    `output_attr`: [string] item attr, 输出的融合后的结果.

    `mode`: [string] 动态参数, add or multiply, 默认 add.

    `alpha`: [double] 动态参数, 必填项, 加法融合使用的超参数. 加法融合 alpha 值域(0, 1)

    调用示例
    ------
    ``` python
    .oversea_scores_integrade(
      first_input_attr="es_score",
      second_input_attr="multiply_score",
      mode="add",
      alpha=0.2
    )
    ```
    """
    self._add_processor(OverseaScoresIntegradeEnricher(kwargs))
    return self

  def oversea_beta_dist_score(self, **kwargs):
    """
    OverseaBetaDistScoreEnricher
    ------
    根据单通道 xtr 预估值计算 beta 分布，并根据 beta 分布统计量计算通道的 xtr score

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0
      - 0: beta 分布 cdf 面积积分
      - 1: beta 分布 1 / (1 - cdf(xtr, 1))

    `channels`: [list] 需要计算 beta dist score 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `beta_a`: [string] beta 分布 alpha 值
      - `beta_b`: [string] beta 分布 beta 值
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `output_attr`: [string] 选配项，将最后计算出的 ensemble 综合分存入指定的 item_attr
    调用示例
    ------
    ``` python
    .oversea_beta_dist_score(
      channels = [
        { "name": "pctr", "save_score_to": "pctr_beta_score", "beta_a": 7.5, "beta_b": 4.9 },
        { "name": "pltr", "save_score_to": "pltr_beta_score", "beta_a": 2.0, "beta_b": 4.6 },
        { "name": "pftr", "save_score_to": "pftr_beta_score", "beta_a": 2.2, "beta_b": 6.2 },
      ],
      output_attr = "beta_dist_score"
    )
    ```
    """
    self._add_processor(OverseaBetaDistScoreEnricher(kwargs))
    return self

  def oversea_quantile_discount(self, **kwargs):
    """
    OverseaQuantileDiscountEnricher
    ------
    候选集中小于指定分位数的item attr 的值进行 discount

    参数配置
    ------
    `input_attr`: [string] item_attr, 输入的 item attr.

    `output_attr`: [string] item attr, 输出的 item attr, 可与 input_attr 相同.

    `quantile`: [double] 动态参数, common attr, 指定的分位数, input_attr 降序后, 对小于分位数的候选集的 input_attr discount. 值域 (0, 1).

    `discount_coefficient`: [double] 动态参数, common attr, discount 系数. 对小于分位数的 items, discount_coefficient * input_attr = output_attr.

    调用示例
    ------
    ``` python
    .oversea_quantile_discount(
      input_attr="play_score",
      output_attr="play_score",
      quantile=0.3,
      discount_coefficient=0.0001
    )
    ```
    """
    self._add_processor(OverseaQuantileDiscountEnricher(kwargs))
    return self

  def oversea_cross_xtr_quantile_discount(self, **kwargs):
    """
    OverseaCrossXtrQuantileDiscountEnricher
    ------
    交叉 xtr 都低于各自分位数时 discount

    参数配置
    ------
    `input_attr`: [string] 要对降权的的 item_attr

    `output_attr`: [string] 降权后的 item_attr

    `cross_xtrs_str`: [string] 交叉 xtr 字符串. 例如 fr_evr*fr_lvr:0.3,fr_ltr*fr_wtr:0.2

    `discount_coefficient`: [double] 动态参数, discount 系数
    ------
    """
    self._add_processor(OverseaCrossXtrQuantileDiscountEnricher(kwargs))
    return self

  def oversea_nu_multiply_rank(self, **kwargs):
    """
    OverseaNuMultiplyRankEnricher
    ------
    新用户乘法公式, base_score * ((const_score_1 + xtr_1)^ temperature_1) * ((const_score_n + xtr_n)^ temperature_n), 使用的 xtr 是 xtr_temperature_str 和 xtr_const_score_str 的交集

    参数配置
    ------
    `base_score_attr`: [string] item attr, 原始 score, 为 0 时 output_attr 也会是 0.

    `output_attr`: [string] item_atte, 计算后输出的结果 score.

    `xtr_temperature_str`: [string] common_attr, 不能为空, (const_score + xtr)^temperature, 格式必须为 xtr1:temperature_1,...,xtrn:termperature_n

    `xtr_const_score_str`: [string] common_attr, 不能为空, (const_score + xtr)^temperature, 格式必须为 xtr1:const_score_1,...,xtrn:const_score_n:

    `xtr_normed_str`: [string] common attr, 是否对 xtr 做 norm, (const_score + norm(xtr))^temperature, 默认值为空, 不做 norm. 参数必须为 xtr1:1,xtr2:0,...,xtrn:1, 0/1 值, 1 表示启用 norm.

    `eps`: [double] common_attr, 浮点数精度, 小于 eps 时认为相等, 默认值 0.000001.

    `alpha`: [double] common_attr, 需要避免 min-max scale 后出现 0 值的一个平滑项, 默认值 0.000001.

    `possible_import_item_attrs`: [string list] processor 依赖的 item_attrs

    调用示例
    ------
    ``` python
    .oversea_nu_multiply_rank(
      base_score_attr="es_score",
      output_attr="rank_score",
      xtr_temperature_str="nu_fr_xtr_temperature_str",
      xtr_const_score_str="nu_fr_xtr_const_score_str",
      xtr_normed_str="nu_fr_xtr_normed_str",
      possible_import_item_attrs=["fr_evr", "fr_ltr", "fr_wtr"]
    )
    ```
    """
    self._add_processor(OverseaNuMultiplyRankEnricher(kwargs))
    return self

  def oversea_correct_rank_score(self, **kwargs):
    """
    OverseaCorrectRankScoreEnricher
    ------
    分数重置逻辑，将分数重新映射回 pre_score 空间。
    boost 前后分数进行排序，计算 排序 seq diff。
    new_rank = post_rank > pre_rank ? std::min(post_rank, pre_rank + threshold) : std::max(post_rank, pre_rank - threshold)
    new_score = pre_score[new_rank] ± (超过阈值 ? score_delta : 0.0)

    参数配置
    ------
    `pre_score_attr`: [string] 之前 score item attr
    `post_score_attr`: [string] 之后 score item attr
    `output_score_attr`: [string] 输出的 score item attr
    `score_rank_diff_threshold`: [int][动态参数] score rank 排序 diff threashold
    `score_delta`: [double][动态参数] 重算时候的 delta

    调用示例
    ------
    ``` python
    .oversea_correct_rank_score(
      pre_score_attr="pre_score",
      post_score_attr="post_score",
      score_rank_diff_threshold = 3,
      score_delta = 0.01,
      output_score_attr = "output_score"
    )
    ```
    """
    self._add_processor(OverseaCorrectRankScoreEnricher(kwargs))
    return self

  def oversea_calc_xtr_percentile(self, **kwargs):
    """
    OverseaCalcPercentileEnricher
    ------
    计算 double item attr 的 p(xx) 比如 p90，将结果存入 common attr。

    参数配置
    ------
    `xtr_configs`: [list] 计算 xtr 的 配置
      - `attr_name`: [string] 被计算的 item attr
      - `percentiles`: [list] 计算 percentile, 数字类型，值域范围 [0.0, 1.0]
      - `output_common_attrs`: [list] 输出的 common attr 名称，需要和 percentiles 长度一致。

    调用示例
    ------
    ``` python
    .oversea_calc_xtr_percentile(
      xtr_configs = [
        {
          "attr_name": "evr",
          "percentiles": [0.25, 0.50, 0.90],
          "output_common_attrs": ["evr_p25", "evr_p50", "evr_p90"],
        },
        {
          "attr_name": "lvr",
          "percentiles": [0.25, 0.50, 0.90],
          "output_common_attrs": ["lvr_p25", "lvr_p50", "lvr_p90"],
        }
      ]
    )
    ```
    """
    self._add_processor(OverseaCalcPercentileEnricher(kwargs))
    return self

  def oversea_retrieve_by_common_attrs_with_reason(self, **kwargs):
    """
    CommonRecoCommonAttrRetriever
    ------
    从多个 int_list 类型的 CommonAttr 中获取 item_key 进行召回并填入结果集（注意不是 item_id !!!）

    参数配置
    ------
    `attrs`: [dict]
      - `name`: [string] 待召回的 CommonAttr 名称
      - `reason_from_attr`: [string] 召回原因, 来自于 item attr, item attr 值 需要大于 0，若不存在或者小于等于 0，则跳过此 item
      - `num_limit`: [int] 选配项, 限制从该 CommonAttr 里召回的最大数目
      - `random_pick`: [bool] 选配项，当配置了 `num_limit` 时可选择是否随机选取，随机选取的结果也将保持原 list 中的相对顺序，默认值为 False
      - `retrieval_item_type`: [int] 选配项, 召回 item 的 item_type，和 item_id 一起产生 item_key，默认值为 0

    `total_limit`: [int] 选配项, 若不小于 0 则控制从所有 CommonAttr 中召回的所有数目不超过该值

    `exclude_items_in_attr`: [string] 选配项, 指定一个 int/int_list 类型的 CommonAttr 对待召回 item 进行提前过滤

    调用示例
    ------
    ``` python
    .oversea_retrieve_by_common_attrs_with_reason(
      attrs=[{
        "name": "like_list",
        "reason_from_attr": "reason",
        "num_limit": 150,
      }, {
        "name": "click_list",
        "reason_from_attr": "reason",
        "num_limit": 150,
      }],
      total_limit=200
    )
    ```
    """
    self._add_processor(OverseaDynamicReasonCommonAttrRetriever(kwargs))
    return self

  def oversea_quantization_dpp(self, **kwargs):
    """
    OverseaQuantizationDppArranger
    ------
    quantization dpp

    参数配置
    ------
    'limit': [int] [动态参数] dpp 计算的 item 数

    'epsilon': [double] [动态参数] 信息增益阈值, 一般取值范围 (0, 0.000001)

    'in_similarity': [double] [动态参数] cluster 内部相似度 [0.0, 1.0]

    'cluster_num': [int] [动态参数] cluster 数量

    'allow_results_less_limit': [bool] [动态参数] 允许 dpp 计算结果数小于 limit, 默认为 False

    'similarity_matrix_attr': [string] cluster 间相似度

    'item_cluster_attr': [string] item cluster attr

    'ranking_score_attr': [string] dpp 计算使用的 ranking score attr

    'output_score_attr': [string] dpp 输出分, 用来算 es dpp rank

    调用示例
    ------
    ``` python
    .oversea_quantization_dpp(
      limit=8,
      epsilon=0.000001,
      item_cluster_attr="p_ksib_m128_cluster_id_v21",
      ranking_score_attr="es_score"
    )
    ```
    """
    self._add_processor(OverseaQuantizationDppArranger(kwargs))
    return self

  def ksib_profile_filter(self, **kwargs):
    """
    KsibProfileFilterEnricher
    ------
    对 user_profile 进行过滤
    1. 丢掉最近一段时间的用户行为

    参数配置
    ------
    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `filter_time_s`: [int] 丢掉最近多久的，单位 s，默认0。

    调用示例
    ------
    ``` python
    .ksib_profile_filter(reader_info_attr="reader_info",
                          filter_time_s=1200)
    ```
    """
    self._add_processor(KsibProfileFilterEnricher(kwargs))
    return self

  def oversea_select_topn_index(self, **kwargs):
    """
    OverseaSelectTopNIndexEnricher
    -----
    给定一个指纹 list 和每个 item 的指纹，根据指纹匹配度筛选 topn 个相似视频，返回其 index

    参数配置
    -----
    'select_type': [string] hamming: 按照 hamming 距离选择相近视频; match: 匹配才筛选，超过 topN 则随机挑选

    'source_fingerprint_list_attr': [string] 指纹list，应为int list

    'item_fingerprint_attr': [string] item 指纹的 attr

    'top_n': [int] 取 top 的数量

    'output_attr': [string] 输出 attr

    'output_type': [int] 0: 输出为int list类型; 1: 输出为 vector<int> ptr

    'save_to_item_context': [bool] True: 输出到item context; False: 输出到 common attr

    'stable_sort': [bool] 默认True, 相同距离取最近的item

    调用示例
    ------
    ``` python
    .oversea_select_topn_index(
      select_type = "hamming",
      source_fingerprint_list_attr = "src_fg_list",
      item_fingerprint_attr = "fingerprint",
      top_n = 50,
      output_attr = "fg_topn",
      output_type = 0,
      save_to_item_context = False
    )
    ```
    """
    self._add_processor(OverseaSelectTopNIndexEnricher(kwargs))
    return self

  def oversea_gsu_with_item_index(self, **kwargs):
    """
    OverseaGsuWithItemIndexEnricher
    ------
    与 kwaipro_gsu_with_index 类似，但 index 来源于 item_attr 而不是 common_attr, 适用于 top 内容数量不固定的情况
    根据 item_attr 中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots
    kwaipro 自定义：pid & aid 拼 bucket

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    示例
    ------
    ``` python
    .oversea_gsu_with_item_index(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    bucket_attr="photo_bucket",
                    top_n=50)
    ```
    """
    self._add_processor(OverseaGsuWithItemIndexEnricher(kwargs))
    return self

  def oversea_gsu_with_fingerprint(self, **kwargs):
    """
    OverseaGsuWithFingerprintEnricher
    -----
    给定一个指纹 list 和每个 item 的指纹，根据指纹匹配度筛选 topn 个相似视频，返回其 index

    参数配置
    -----
    'select_type': [string] hamming: 按照 hamming 距离选择相近视频; match: 匹配才筛选，超过 topN 则随机挑选

    'source_fingerprint_list_attr': [string] 指纹list，应为int list

    'item_fingerprint_attr': [string] item 指纹的 attr

    'top_n': [int] 取 top 的数量

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `bucket_attr` : [string] bucket attr

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `slot_as_attr_name` : [bool] 默认 False, 是否直接用 slot 作为 attr 的名字

    调用示例
    ------
    ``` python
    .oversea_gsu_with_fingerprint(
        select_type = "hamming",
        source_fingerprint_list_attr = "source_fingerprint_list",
        item_fingerprint_attr = "fingerprint",
        top_n = 50,
        output_attr = "sorted_item_index",
        stable_sort = False,
        colossus_pid_attr="colossus_pid",
        author_id_attr="colossus_aid_list",
        tag_attr="colossus_tag_list",
        play_time_attr="colossus_play_time_list",
        duration_attr="colossus_duration_list",
        timestamp_attr="colossus_timestamp_list",
        bucket_attr="photo_bucket",
        output_sign_attr="gsu_signs",
        output_slot_attr="gsu_slots",
        output_item_colossus_pid_attr="gsu_pids",
        slots_id=[123, 122, 692, 694, 693],
        mio_slots_id=[690, 691, 692, 694, 693],
    )
    ```
    """
    self._add_processor(OverseaGsuWithFingerprintEnricher(kwargs))
    return self

  def predict_by_ad_model(self, **kwargs):
    """
    OverseaAdModelEnricher
    ------
    通过请求 Ad 团队的 cmd 模型，ad_item_id 作为视频 id， 从远程服务获取 pxtr。
    参数配置
    ------
    `kess_service`: [string] [动态参数] cmd 模型服务的 kess 名称

    `service_group`: [string] cmd 模型服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 超时时间，可缺省,默认值为 50

    `ad_item_id`: [string] 表示 Ad 侧视频 id 的 item_attr

    `cmd_configs`: [object] cmd 模型相关配置，所有配置均必填
      - `cmd_name`: [string] [动态参数] cmd 模型名称
      - `cmd_key`: [string] [动态参数] cmd 模型 key
      - `cmd_value_num`: [int] [动态参数] cmd 模型预估分数个数
      - `predict_attrs`: [list] 预估字段的列表
          - `attr_name`: [string] 需要预估的 attr 名称
          - `predict_type`: [int] [动态参数] 预估字段类型，表示预估值在返回结果中的位置
    调用示例
    ------
    ``` python
    .predict_by_ad_model(
      service_group="PRODUCTION",
      kess_service = "grpc_adI18nPsDspNNRouterServerNew",
      timeout_ms = 100,
      ad_item_id = "creative_id",
      cmd_configs = [
        {
          "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
          "cmd_key": "{{cmd_key}}",
          "cmd_value_num": "{{cmd_value_num}}",
          "predict_attrs": [
            {
              "attr_name": "ad_conversion_ctr",
              "predict_type": "{{predict_type}}"
            }
          ]
        }
      ]
    )
    ```
    """
    self._add_processor(OverseaAdModelEnricher(kwargs))
    return self

  def extract_with_statistic_dense_feature_opt(self, **kwargs):
      """
      OfflineStatisticDenseFeatureOpt
      ------
      国际化基于统计的 dense 特征抽取

      参数配置
      ------

      `item_statistic_dense_feature_output`: [list] Item 侧统计 dense 特征输出到给定 item attr。

      `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

      `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

      `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，线上一定不要开启，默认 False。

      `match_common_attrs`: [list] 统计匹配时的用户侧 attr。

      `match_item_attrs`: [list] 统计匹配是用到的photo侧 attr。

      `max_time_gap`: [int] 统计最近时间内的匹配计数的最大时间窗口，单位为秒，默认为 7776000 (90天)。

      `max_last_size`: [int] 统计最近N个数据的匹配计数的最大数据个数，单位为个，默认为 200。

      `match_fields`: [list] 需要匹配的字段, 支持以下类型:
        - authorid
        - mmuclusterid
        - mmuclusterv2id
        - mmuprimarytagid
        - trinitym32
        - trinitym128
        - trinitym1000
        - trinitym4000
        - trinityprimarytag
        - trinitysecondtag

      `match_action_fields`: [list] 需要匹配的action_list字段, 支持以下类型:
        - realshow
        - click
        - like
        - follow
        - forward
        - slideenter
        - finishplay
        - effectiveview
        - longview
        - shortview
        - hate
        - enterprofileunion
        - downloadunion
        - commentunion
        - playingtime

      `count_types`: [list] window_size 字段，支持以下类型:
        - last20
        - last100
        - last200
        - 1h
        - 1d
        - 7d
        - 30d
        - 90d
        - last1
        - last5
        - 1min
        - 5min
      `output_ptr`: [bool] 是否以指针类型输出特征值，默认为false(输出为double list类型)。

      调用示例
      ------
      ``` python
      .extract_with_statistic_dense_feature_opt(
          item_statistic_dense_feature_output=[
            "ds_upm_authorid",
          ],
          reader_info_attr='reader_info')
      ```
      """
      self._add_processor(OfflineStatisticDenseFeatureOptEnricher(kwargs))
      return self

  def extract_with_statistic_dense_feature_colossus(self, **kwargs):
      """
      OfflineStatisticDenseFeatureColossus
      ------
      国际化基于统计的 dense 特征抽取

      参数配置
      ------

      `item_statistic_dense_feature_output`: [list] Item 侧统计 dense 特征输出到给定 item attr。


      `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，线上一定不要开启，默认 False。


      `max_time_gap`: [int] 统计最近时间内的匹配计数的最大时间窗口，单位为秒，默认为 7776000 (90天)。

      `max_last_size`: [int] 统计最近N个数据的匹配计数的最大数据个数，单位为个，默认为 10000。

      `match_fields`: [list] 需要匹配的字段, 支持以下类型:
        - authorid
        - ksib_tag_id


      `match_action_fields`: [list] 需要匹配的action_list字段, 支持以下类型:
        - show
        - finishplay
        - effectiveview
        - longview
        - shortview
        - playtime

      `count_types`: [list] window_size 字段，支持以下类型:
        - last20
        - last100
        - last200
        - 1h
        - 1d
        - 7d
        - 30d
        - 90d
        - last1
        - last5
        - last500
        - last1000
        - last2000
        - last5000
        - last10000
        - 1min
        - 5min

      `output_ptr`: [bool] 是否以指针类型输出特征值，默认为false(输出为double list类型)。

      `colossus_pid_attr` : [string] 取自colossus的pid列表

      `author_id_attr` : [string] pid对应的aid attr name

      `tag_attr` : [string] pid对应的tag attr name

      `play_time_attr` : [string] pid对应的play time attr name

      `duration_attr` : [string] pid对应的duration attr name

      `timestamp_attr` : [string] pid对应的timestamp attr name

      `match_colossus_fields`: [list] 需要匹配的字段, 支持以下类型:
        - aid
        - tag

      调用示例
      ------
      ``` python
      .extract_with_statistic_dense_feature_colossus(
          item_statistic_dense_feature_output=[
            "ds_upm_colossus_aid", "ds_upm_colossus_ksib_tag_id",
          ])
      ```
      """
      self._add_processor(OfflineStatisticDenseFeatureColossusEnricher(kwargs))
      return self

  def oversea_calc_distance_cluster(self, **kwargs):
    """
    KsibDistanceClusterEnricher
    ------
    根据 cluster center embedding 矩阵和候选集 items embedding 矩阵计算欧拉距离,
    返回距离最近的cluster类别.

    参数配置
    ------
    `item_embedding_attr`: [string] item embedding

    `cluster_center_prefix_attr`: [string] 聚类common attr 前缀

    `cluster_center_num_attr`: [int] 聚类common attr 个数，类别数

    `output_cluster_attr`: [string] 输出最近的类别

    调用示例
    ------
    ``` python
    .oversea_calc_distance_cluster(
        name=name("calc_distance_cluster"),
        item_embedding_attr="rr_cluster_pxtr_list",
        cluster_center_prefix_attr="rr_center_list_",
        cluster_center_num_attr=16,
        output_cluster_attr="rr_cluster_id")
    ```
    """
    self._add_processor(KsibDistanceClusterEnricher(kwargs))
    return self

  def perflog_attr_percentile(self, **kwargs):
    """
    OverseaAttrPercentilePerflogObserver
    ------
    对 attr 的 percentile 统计值上报 perflog

    注意：如果要监测多个点，请用 `check_point` 区分，否则会合并统计数据

    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `item_attrs`: [list] 需要统计的 item_attr 名称列表

    `percentiles`: [string list] 需要统计的百分位数string，格式为 "min", "max", 或者 "p99" (p 后面跟百分位数字)三种。

    `local`: [bool] 选配项，统计结果直接写到本地 perflog.log 文件而不是上传到 clickhouse，默认为 False。

    `perf_base`: [int] 打点时的放大倍数，用于保留小数点，默认为 1000000L，注意修改这个值会导致 Grafana 上打点显示异常，使用时需要注意。

    调用示例
    ------
    ``` python
    .perflog_attr_percentile(
      check_point="default.ranking",
      item_attrs=["pctr", "pltr", "pftr"],
      percentiles=["p90", "p95", "p99"]
    )
    ```
    """
    self._add_processor(OverseaAttrPercentilePerflogObserver(kwargs))
    return self

  def oversea_simple_multiply(self, **kwargs):
    """
    OverseaSimpleMultiplyEnricher
    ------
    一组 double item attrs 相乘，并将结果存于一个 double item attr。

    参数配置
    ------
    `input_item_attrs`: [list] 需要相乘的 double item attr 列表
    `output_item_attr`: [string] 乘法计算输出的 double item attr 名称

    调用示例
    ------
    ``` python
    .oversea_simple_multiply(
      input_item_attrs = [
        "pre_rank_multiply_boost_factor", "pre_rank_age_boost_factor", "pre_rank_tag_debias_boost_factor"
      ],
      output_item_attr = "final_score"
    )
    ```
    """
    self._add_processor(OverseaSimpleMultiplyEnricher(kwargs))
    return self
  
  def oversea_multi_reason_random_select(self, **kwargs):
    """
    OverseaMultiReasonRandomSelectArranger
    ------
    对于多路召回中重复的 item，从这些召回的 reason 中随机选择一个更新 item reason。

    参数配置
    ------
    `reason_list_attr`: [string] deduplicate 时保留 reason 的 int_list 类型 item attr 字段

    `default_reason`: [int] reason_list_attr 取值为空时的回落值，默认为 999

    调用示例
    ------
    ``` python
    .oversea_multi_reason_random_select(
      reason_list_attr = "reason_list",
      default_reason = 999
    )
    ```
    """
    self._add_processor(OverseaMultiReasonRandomSelectArranger(kwargs))
    return self

  def oversea_embedding_similarity_calculate(self, **kwargs):
    """
    OverseaEmbeddingSimilarityCalculateEnricher
    ------
    计算 candidate_embedding 和 target_embedding 集合的相似度得分。

    参数配置
    ------
    `candidate_embedding_attr_type`: [string] 选填，存储 candidate embedding 的 attr 类型，可选值为 "common_attr" 和 "item_attr" 两种，默认为 "item_attr"

    `candidate_embedding_attr`: [string] 必填，存储 candidate embedding 的 attr 名称

    `target_embedding_attr_type`: [string] 选填，存储 target embedding 的 attr 类型，可选值为 "common_attr" 和 "item_attr" 两种，默认为 "common_attr"

    `target_embedding_attr`: [string] 必填，存储 candidate embedding 的 attr 名称

    `output_similarity_attr`: [string] 必填，输出 embedding 相似度的 attr，类型与 candidate_embedding_attr_type 保持一致

    `embedding_dim`: [int] [动态参数] 必填，参与计算的 embedding 维度

    调用示例
    ------
    ```
    .oversea_embedding_similarity_calculate(
      candidate_embedding_attr_type = "item_attr",
      candidate_embedding_attr = "candidate_embedding",
      target_embedding_attr_type = "common_attr",
      target_embedding_attr = "target_embedding",
      output_similarity_attr = "embedding_similarity_score",
      embedding_dim = 128
    )
    ```
    """
    self._add_processor(OverseaEmbeddingSimilarityCalculateEnricher(kwargs))
    return self

  def oversea_double_list_analyse(self, **kwargs):
    """
    OverseaDoubleListAnalyseEnricher
    ------
    对 double_list 类型的 attr 进行统计分析

    参数配置
    ------
    `is_common_attr`: [bool] 选填，是否为 common_attr，不是则为 item_attr，默认为 False

    `input_attr`: [string] 必填，输入 attr 的名称

    `mappings`: [list] 必填，需要进行的统计分析：
      - `metric`: [string] 必填，需要进行的统计分析，可选值如下：
        - "mean"，均值，返回类型均为 double
        - "topN_mean"，topN 数据的均值，返回类型均为 double
        - "max"，最大值，返回类型均为 double
        - "min"，最小值，返回类型均为 double
        - "greater_than"，大于 compared_to 的数据个数，返回类型为 int
        - "not_greater_than"，不大于 compared_to 的数据个数，返回类型为 int
        _ "smaller_than"，小于 compared_to 的数据个数，返回类型为 int
        - "not_less_than"，不小于 compared_to 的数据个数，返回类型为 int
      - `topN`: [int] [动态参数] 选填，当 analysis_metric 包含 "topN_mean" 时，需要指定 N 的大小，默认为 1
      - `compared_to`: [double] [动态参数] 选填，当 analysis_metric 包含比较 "xxx_than" 时，需要指定比较的阈值，默认为 0.0
      - `output_attr`: [string] 必填，输出计算结果 attr 的名称

    调用示例
    ------
    ```
    .oversea_double_list_analyse(
      is_common_attr = False,
      input_attr = "similarity_score",
      mappings = [
        {
          "metric": "mean",
          "output_attr": "similarity_score_mean",
        },
        {
          "metric": "topN_mean",
          "output_attr": "similarity_score_topN_mean",
          "topN": 3,
        },
        {
          "metric": "max",
          "output_attr": "similarity_score_max",
        },
        {
          "metric": "greater_than",
          "output_attr": "similarity_score_greater_than",
          "compared_to": 0.5,
        }
      ]
    )
    ```
    """
    self._add_processor(OverseaDoubleListAnalyseEnricher(kwargs))
    return self

  def oversea_multi_constraints_optimize(self, **kwargs):
    """
    oversea_multi_constraints_optimize
    ------
    多目标约束优化算子,作用于从候选集中选topK的场景。在满足多个约束项的情况下最优化某个attr的值,底层基于二分查找实现。两个约束、 5000选2000场景下耗时约为3ms

    参数配置
    ------
    `optimize_attr`: [string] 必填，优化目标attr_name
    
    `limit_size`: [int|动态参数] 必填，topK的值
    
    `constraints`: [list] 必填，约束条件列表
     - `attr_name`: [string] 必填，约束项attr_name
     - `type`: [string] 必填，type取值为[constant|delta|roi]其中之一。"constant"表示常量约束,即约束项在选出的topK集合中该item_attr的和不能低于此常量,可以用于保量等约束。"delta"表示比例约束,即约束项在选出的topK集合中需要满足的保留比例,如原topK中,rank_score的和为n,在新选择的topK比例应该大于 value*n。"roi"表示投入产出约束，即被优化目标收益与该约束项损失大于该值。
     - `value`: [int|double|动态参数] 必填，约束项的设置值
    
    `max_loop_times`: [int] 选填，搜索次数，默认为2
    
    `epsilon`: [double] 选填，二分查找精度，默认为0.0001
    
    `find_optimal`: [string] 必填，输出是否找到最优解，取值为[1|0]
    
    `output_attr`: [string] 必填，输出topK的标记，item_attr 取值为[1|0], 0表示不在topK中, 1表示在topK中。

    `output_opt_lambdas`: [string] 选填，默认值是''，用于存放lambdas的值。备注:前N-1维存的是所有约束对应的lambda的值，最后一维度是优化目标前面的系数lambda0, 如果没有找到更优的值，返回全为-1的list

    `is_strict`: [int] 选填, 默认值是0, 控制二分查找更优lambda时的判断阈值，0表示best_opt初始值是0, 1表示best_opt为初始状态的优化目标和。
    
    调用示例
    ------
    ```
    .oversea_multi_constraints_optimize(
      optimize_attr = "opt_score",
      limit_size = 2000,
      max_loop_times = 2,
      epsilon = 0.00001,
      constraints = [{
        "name" : "rank_score",
        "type" : "roi",
        "value" : 2.0
      }],
      output_attr = "go_flag",
      is_strict = 0,
      output_opt_lambdas = "output_opt_lambdas_value",
      find_optimal = "find_go_boost_optimal"
    ) 
    ```
    """
    self._add_processor(OverseaMultiConstraintsOptimizeEnricher(kwargs))
    return self
  
  def oversea_multi_constraints_optimize_v2(self, **kwargs):
    """
    oversea_multi_constraints_optimize_v2
    ------
    多目标约束优化算子,作用于从候选集中选topK的场景。在满足多个约束项的情况下最优化某个attr的值, 底层基于二分查找实现。
    step1: 如果能找到最优解，采用最优解结果(预计3个约束下覆盖率大约有98%+)
    step2: 如果找不到最优解，选择满足约束上限的能够使优化目标最大的次优解; 有两种方案找次优解：目标值最大的次优解/求约束差距尽可能小的次优解
    step3: 如果找不到次优解，则放弃寻找解，优化目标采用默认值。

    参数配置
    ------
    `optimize_attr`: [string] 必填，优化目标attr_name
    
    `limit_size`: [int|动态参数] 必填，topK的值
    
    `constraints`: [list] 必填，约束条件列表
     - `attr_name`: [string] 必填，约束项attr_name
     - `type`: [string] 必填，type取值为[constant|delta|roi]其中之一。"constant"表示常量约束,即约束项在选出的topK集合中该item_attr的和不能低于此常量,可以用于保量等约束。"delta"表示比例约束,即约束项在选出的topK集合中需要满足的保留比例,如原topK中,rank_score的和为n,在新选择的topK比例应该大于 value*n。"roi"表示投入产出约束，即被优化目标收益与该约束项损失大于该值。
     - `value`: [int|double|动态参数] 必填，约束项的设置值。
     - `tolerance_value`: [int|double|动态参数] 必填，宽松约束项的设置值，且取 min(value, tolerance_value)
     - `beta_param`: [int|double|动态参数] 选填，默认值是0.1, 单轮搜索约束强度设置
    
    `max_loop_times`: [int] 选填，搜索次数，默认为2
    
    `epsilon`: [double] 选填，二分查找精度，默认为0.00001
    
    `find_solution`: [string] 必填，输出是否找到解（最优/次优），取值为[1|0]
    
    `output_attr`: [string] 必填，输出topK的标记，item_attr 取值为[1|0], 0表示不在topK中, 1表示在topK中。

    `output_opt_lambdas`: [string] 选填，默认值是''，用于存放lambdas的值。备注:前N-1维存的是所有约束对应的lambda的值，最后一维度是优化目标前面的系数lambda0, 如果没有找到更优的值，返回全为-1的list

    `is_strict`: [int] 选填, 默认值是1, 控制二分查找更优lambda时的判断阈值，0表示best_opt初始值是0, 1表示best_opt为初始状态的优化目标和。

    `sub_lambda_version`: [int] 选填, 默认值0, 在获得满足宽松约束情况下求次优解的条件。若为0，目标值最大的次优解；若为1，求约束差距尽可能小的次优解。

    `tolerance_version`: [int] 选填, 默认值0, 次优解的约束条件。若为0，表示需要满足约束差距是所有约束项的约束值总和；若为1，表示需要分别满足每个约束项。

    `extra_output_attrs`: [string] 选填, 用于存放输出common attrs，[find_optimal(是否找到最优解), find_suboptimal(是否找到次优解), upgrade_opt(目标值提升幅度), sub_max_opt(次优解下的目标值), sub_min_delta(次优解下的约束差值)] 
    
    调用示例
    ------
    ```
    .oversea_multi_constraints_optimize_v2(
      optimize_attr = "opt_score",
      limit_size = 2000,
      max_loop_times = 2,
      epsilon = 0.00001,
      constraints = [{
        "name" : "rank_score",
        "type" : "roi",
        "value" : 0.98,
        "tolerance_value" : 0.97,
        "beta_param": 0.1
      }],
      is_strict = 1,
      sub_lambda_version = 0,
      tolerance_version = 0,
      output_attr = "opt_output_flag",
      output_opt_lambdas = "output_opt_lambdas_value",
      find_solution = "find_solution_value",
      extra_output_attrs = "extra_output_attrs_value",
    ) 
    ```
    """
    self._add_processor(OverseaMultiConstraintsOptimizeV2Enricher(kwargs))
    return self

  def ksib_delegate_enrich(self, **kwargs):
    """
    KsibDelegateEnricher
    ------
    调用另一个基于 CommonLeaf 协议的远程服务进行计算，并填充返回的属性

    参数配置
    ------
    `kess_service`: [string] [动态参数] 调用的 CommonLeaf 的 kess service

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `kess_group`: [string] [动态参数] 选填项，调用的 CommonLeaf 的 kess_group，默认为 ""

    `shard_num`: [int] 被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并，默认为 1。

    `shard_id_offset`: [int] 被调服务的 shard_id 起始偏移量，默认为 0，即 shard 号从 s0 开始

    `shard_by_item_attr`: [string] 按给定的 item attr 将 item list 拆分成不同的 shard 去请求下游服务，默认为空，此时对于各个 shard 的请求完全相同。

    `consistent_hash`: [bool] 是否对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    `hash_id`: [string] [动态参数] 优先使用该 id 对请求进行一致性 hash 的分发，可缺省，缺省时使用 user_id 或 device_id 进行分发， consistent_hash 为 True 时生效

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `use_item_id_in_attr`: [string] 选填项，若设置则使用指定的 ItemAttr 下的 int 值替代实际 Item 的 item_key，填充进 request 用于发送给下游（注意是替换 item_key，不是 item_id，配置名中的 use_item_id 字眼有歧义）

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_browse_set`: [bool] 选填项，是否在请求中填充 browse_set，默认为 false

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `for_predict`: [bool] 选填项，标记是否是为预估服务请求，若为 true 则会对接收的 double 类型 item_attr 按 pxtr 进行监控上报，默认为 true

    `use_packed_item_attr`: [bool] [动态参数] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，缺省时会优先尝试使用 packed_item_attr 格式，若获取数据失败会取消 packed_item_attr 格式的使用

    `infer_output_type`: [int] 选填项，要求 tower infer server 用指定的 output_type（与服务端 [fetch_tower_remote_pxtr](https://dragonfly.corp.kuaishou.com/#/api/embed_calc?id=fetch_tower_remote_pxtr) 的 output_type 配置功能相同）返回 pxtr 数据，默认为 -1

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，从指定的 string_list common attr 中获取 sample_list attr 所在的 attr name 列表

    `sample_list_ptr_attr`: [string] 选填项，从指定的 kuiba::PredictItem 类型的 ptr common attr 中获取 sample_list attt, 若与 sample_list_common_attr_key 同时配置，会取并集

    `flatten_sample_list_attr`: [bool] 选填项，是否使用压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `flatten_sample_list_attr_to`: [string] 选填项，将 flatten 后的 sample_list attr 以指定的 attr name 发送，仅当 flatten_sample_list_attr=true 时有效，默认为 "kuiba_user_attrs"

    `ttl_seconds`: [int] 选配项，在创建 request 的时候，底层默认会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间，默认值为 3600

    `random_shift_window`: [int] 选配项，清理 request 会每隔 ttl_seconds 集中在 random_shift_window 时间段，该值过小会使部分服务稳定性抖动较大，默认值为 120

    `dynamic_recv_item_attr_kconf`: [string] 选配项，通过 kconf 获取接收的 item attr 列表，kconf 类型需要是 map<string, string>, key 为接受的 infer pxtr 名字，value 为存储到 CommonRecoContext 的 attr_name 名字. for_predict 监控等配置也都支持

    调用示例
    ------
    ``` python
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    # 如果有 attr 需要改名，或设 readonly
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId", "readonly": True}],
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      request_type = "default",
      use_packed_item_attr = True,
    )
    # 访问 krp-infer-server 可能需要配置从 sample_list, request 获取发送的 common_attr
    .delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      send_common_attrs_in_request = True,
      exclude_common_attrs = ["userFollowList"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    ```
    """
    self._add_processor(KsibDelegateEnricher(kwargs))
    return self

  def oversea_mutex_sleep(self, **kwargs):
    """
    OverseaMutexSleepEnricher
    ------
    互斥 sleep 算子，主要用于在多线程环境下控制 qps

    参数配置
    ------

    `sleep_ms`: [int]【动态参数】 睡眠时间，单位为 ms, 默认值为 0 ms

    调用示例
    ------
    ``` python
    .oversea_mutex_sleep(
      sleep_ms = 1000
    )
    ```
    """
    self._add_processor(OverseaMutexSleepObserver(kwargs))
    return self
