#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, extract_attr_names, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class CommonRecoGsuWithTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class GsuWithMultiTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_multi_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if (not self._config.get("gsu_with_top_tags", False)):
        ret.add(self._config["target_tag_attr"])
        if (self._config.get("additional_target_tag_attr", "")):
          ret.add(self._config["additional_target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if (self._config.get("gsu_with_top_tags", False)):
        ret.add(self._config["target_tag_attr"])
    return ret

class GsuWithMultiTagV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_multi_tag_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if (not self._config.get("gsu_with_top_tags", False)):
        ret.add(self._config["target_tag_attr"])
        if (self._config.get("additional_target_tag_attr", "")):
          ret.add(self._config["additional_target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["padding_size_attr"])
    if (self._config.get("gsu_with_top_tags", False)):
        ret.add(self._config["target_tag_attr"])
    return ret

class CommonRecoGsuWithEntityTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_entity_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    try:
      ret.add(self._config["target_cluster_attr"])
    except Exception:
      print("target_cluster_attr is not exsits")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret


class KwaiproGsuWithTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_with_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["bucket_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("use_mmu_tag")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("service_name"):
      raise ArgumentError("service_name 不能为空")
    if not self._config.get("shard_number"):
      raise ArgumentError("shard_number 不能为空")

class CommonRecoGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    try:
      ret.add(self._config["target_cluster_attr"])
    except Exception:
      print("target_cluster_attr is not exsits")

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_latest_live_id", "u_latest_author_id", "u_latest_page",
      "u_latest_play_time", "u_latest_time_gap"
    }

class CommonRecoGsuWithClusterColossusV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_colossusv3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    try:
      ret.add(self._config["target_cluster_attr"])
    except Exception:
      print("target_cluster_attr is not exsits")

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("output_mmu_cluster", False):
      ret.add(self._config["output_mmu_cluster_pid_attr"])
      ret.add(self._config["output_mmu_cluster_cid_attr"])
    return ret

class CommonRecoGsuWithClusterShortTermInterestsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_short_term_interests"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    if self._config.get("use_dumped_mmu_cluster", False):
      ret.add(self._config["mmu_cluster_pid_attr"])
      ret.add(self._config["mmu_cluster_cid_attr"])
    try:
      ret.add(self._config["enable_short_cid_smooth_attr"])
      ret.add(self._config["short_cid_smooth_factor_attr"])
    except:
      print("short_cid_smooth not enabled.")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

class CommonRecoStaticGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "static_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    try:
      ret.add(self._config["target_cluster_attr"])
    except Exception:
      print("target_cluster_attr is not exsits")

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuTowerClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    try:
      ret.add(self._config["target_cluster_attr"])
    except Exception:
      print("target_cluster_attr is not exsits")

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuDeepMatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_deep_match_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("search_by_target", True):
        ret.add(self._config["output_sign_attr"])
        ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("search_by_target", True):
        ret.add(self._config["output_sign_attr"])
        ret.add(self._config["output_slot_attr"])
    if (self._config.get("output_clutser_attr")):
        ret.add(self._config["output_clutser_attr"])
    return ret

class CommonRecoGsuWithClusterFollowEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_follow"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonLiveGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterEnricherAid(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterEnricherAidV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_aid_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonLiveGsuWithAidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_kgnn_aids"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_aids_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterEnricherV4(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_cluster_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_live_list"])
    return ret

class GsuWithTimeClockEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_time_clock"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class GsuWithLivingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_living"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["living_pid_list_attr"])
    ret.add(self._config["living_ts_list_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class GsuWithLivingV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_living_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["living_pid_list_attr"])
    ret.add(self._config["living_ts_list_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuLongtermItemDeriveEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_longterm_item_derive"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["cal_scope"])
    ret.add(self._config["cal_basis"])
    ret.add(self._config["k_num"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterEnricherReward(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_reward"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveLongShortGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_long_short_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_last_sign_attr"])
    ret.add(self._config["output_last_slot_attr"])
    return ret

class CommonLiveGsuWithAidPhotoColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_photo_gsu_with_aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_aid_attr"])
    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoLongShortGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_long_short_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_last_sign_attr"])
    ret.add(self._config["output_last_slot_attr"])
    return ret

class CommonLiveGsuWithClusterFollowEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_live_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_cluster_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("output_cluster_attr", False):
      ret.add(self._config["output_cluster_attr"])
    if self._config.get("u_latest_live_id", False):
      ret.add(self._config["u_latest_live_id"])
      ret.add(self._config["u_latest_author_id"])
      ret.add(self._config["u_latest_page"])
      ret.add(self._config["u_latest_play_time"])
      ret.add(self._config["u_latest_time_gap"])
    return ret

class CommonLivePrerankGsuWithClusterV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_prerank_gsu_with_cluster_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["limit_play_time"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("output_cluster_attr", False):
      ret.add(self._config["output_cluster_attr"])
    if self._config.get("u_latest_live_id", False):
      ret.add(self._config["u_latest_live_id"])
      ret.add(self._config["u_latest_author_id"])
      ret.add(self._config["u_latest_page"])
      ret.add(self._config["u_latest_play_time"])
      ret.add(self._config["u_latest_time_gap"])
    return ret

class MerchantGsuAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsu1ppEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_1pp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsuAid2AidPhotoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_gsu_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuWith1wClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1w_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

class CommonRecoGsuWith1wClusterMerchantEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1w_cluster_merchant"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonCommertialGsuWithClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonCommertialGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_pid_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonCommertialGsuWithAidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonCommertialGsuWithAidClusterEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_1k_cluster_commertial"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class LiveGsuWithAidClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_aid_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonMerchantGsuWithCommodityClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_commodity_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_good_list_attr", "target_good_cluster_list_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonMerchantGsuWithPhotoClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_photo_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonMerchantGsuWithPhotoClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_photo_cluster_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonMerchantGsuWithPhotoClusterExtraEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_photo_cluster_extra"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["follow_aid_list_attr"])
    ret.add(self._config["like_pid_list_attr"])
    ret.add(self._config["forward_pid_list_attr"])
    ret.add(self._config["hate_pid_list_attr"])
    ret.add(self._config["comment_pid_list_attr"])
    ret.add(self._config["profile_aid_list_attr"])
    ret.add(self._config["follow_action_stat_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoUserBiasByDurationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_user_bias_by_duration"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add("duration_ms")
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

class CommonRecoUserBiasByClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_user_bias_by_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

class CommonRecoGsuWithTagAndClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_tag_and_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuTowerTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuRecentActionsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_recent_photos"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["play_time_threshold_attr"])
    ret.add(self._config["action_list_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonRecoGsuRecentActionsCol2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_recent_photos_col2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["play_time_threshold_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "input_colossus_resp_attr" in self._config:
      ret.add(self._config["input_colossus_resp_attr"])
    if 'input_attr' in self._config:
      ret.add(self._config["input_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class FetchEmbeddingServerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_fetch_emb_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonRecoBatchColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_colossus"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["candidate_uid_attr"])
    ret.add(self._config["candidate_uid_top_realshow"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonRecoBatchColossusEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_colossus_v2"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["candidate_uid_attr"])
    ret.add(self._config["candidate_uid_top_realshow"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class LiveGsuTowerMultiSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_tower_multi_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["colossus_pid_attr"])
    attrs.update(self.extract_dynamic_params(self._config["channel_sizes"]))
    attrs.update(self.extract_dynamic_params(self._config["channel_weights"]))
    attrs.update(self._config["common_distance_ptr_attrs"])
    attrs.update(self._config["common_distance_filter_attrs"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "page_type_attr", "timestamp_attr", "optag_attr"]:
      ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      ret.update(map(str, self._config.get("mio_slots_ids", [371, 372, 375, 373, 374, 376, 377])))
    else:
      for key in ["output_sign_attr", "output_slot_attr"]:
        ret.add(self._config[key])
    for key in ["output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class LiveGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "page_type_attr", "timestamp_attr", "optag_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr",
                "output_item_distance_attr", "output_item_raw_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class LongTermResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_long_term_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["photo_id_attr", "author_id_attr", "duration_attr", \
        "play_time_attr", "tag_attr", "label_attr", "timestamp_attr"]:
      attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { self._config["dump_to_attr"] }

class KwaiproGsuRoughTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_get_rough_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["bucket_common_attr"])
    ret.add(self._config["target_tag_common_attrs"])
    ret.update(self.extract_dynamic_params(self._config.get("use_mmu_tag")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_common_attr"])
    ret.add(self._config["output_slot_common_attr"])
    return ret

  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("service_name"):
      raise ArgumentError("service_name 不能为空")
    if not self._config.get("shard_number"):
      raise ArgumentError("shard_number 不能为空")

class GsuUserQueryHashEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_query_hash"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    key = "user_query_list_attr"
    if key in self._config:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    key = "item_query_str_attr"
    if key in self._config:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    key = "user_query_list_attr"
    if key in self._config:
      ret.add(self._config["save_hash_to_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    key = "item_query_str_attr"
    if key in self._config:
      ret.add(self._config["save_hash_to_attr"])
    return ret

class GsuWithTimeProfileCommonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_time_profile"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    ret.add(self._config["cycle_hour_gap"])
    ret.add(self._config["cycle_photo_limit"])
    ret.add(self._config["total_profile_photo_limit"])
    ret.add(self._config["total_profile_author_limit"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithTimeCommonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_time"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "label_attr", "channel_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))

      for key in ["output_item_colossus_pid_attr", "output_item_distance_attr",
                  "output_item_raw_distance_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr",
                  "output_item_distance_attr", "output_item_raw_distance_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class CalcLocationDistanceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_location_distance"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_user_lat_attr", "common_user_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_lat_attr", "item_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_poi_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
class GsuWithIndexV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithIndexV3SubSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_v3_sub_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithIndexLiveEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_live_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_pxtrs_attr", "sorted_item_idx_attr", "colossus_pid_attr", "sorted_item_index_list"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr", "output_item_colossus_unitid_attr", "output_item_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithIndexV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in self._config["item_fields_slots"]:
      ret.add(gen_attr_name_with_common_attr_channel(key["item_fields"], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithIndexEnricherNearby(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_nearby"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr", "common_user_lat_attr", "common_user_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr",
                "item_lat_attr", "item_lon_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    for key in ["target_item_lat_attr", "target_item_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithDurationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_duration"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuWithRemoteDotProductEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_remote_dot_product"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["colossus_resp_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "target_embedding_attr" in self._config:
      ret.add(self._config["target_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    if "enable_embedding_similarity_rfm_for_nearby_items" in self._config:
      for key in ["output_embedding_similarity_rfm_show_attr_list",
                  "output_embedding_similarity_rfm_effective_view_attr_list",
                  "output_embedding_similarity_rfm_watch_time_attr_list",
                  "output_embedding_similarity_rfm_glabel_attr_list"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class KwaiProGsuWithIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_with_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("use_mmu_tag")))
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["bucket_attr"])
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class KsibGsuAidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_gsu_with_aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_resp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["target_attr", "bucket_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class GsuWithIndexMultiMergeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_index_multi_merge"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr"]:
      if key in self._config:
        ret.update(set(self._config[key]))
    for key in ["colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuTowerMultiSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_multi_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["colossus_pid_attr"])
    attrs.update(self.extract_dynamic_params(self._config["channel_sizes"]))
    attrs.update(self.extract_dynamic_params(self._config["channel_weights"]))
    if "user_channel_weights" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["user_channel_weights"]))
    attrs.update(self._config["common_distance_ptr_attrs"])
    attrs.update(self._config["common_distance_filter_attrs"])
    if "user_distance_ptr_attrs" in self._config:
      attrs.update(self._config["user_distance_ptr_attrs"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "label_attr", "channel_attr", "play_time_attr",
                "duration_attr", "timestamp_attr"]:
      ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      ret.update(map(str, self._config.get("mio_slots_ids", [346, 347, 349, 348, 350])))
    else:
      for key in ["output_sign_attr", "output_slot_attr"]:
        ret.add(self._config[key])
    for key in ["output_item_colossus_pid_attr", "output_item_colossus_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class MerchantLiveGsuWithTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_gsu_with_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class MerchantGsuWithLiveClipClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_with_live_clip_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["live_colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class KwaiMeGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    ret.add(self._config["bucket_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    for attr in ["output_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

class KwaiProGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr",
                "output_item_distance_attr", "output_item_raw_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class SnackGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr",
                "output_item_distance_attr", "output_item_raw_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuCityhashEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_cityhash"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(input_attr["attr_name"] for input_attr in self._config["input_attrs"] if input_attr.get("is_common", False))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(input_attr["attr_name"] for input_attr in self._config["input_attrs"] if not input_attr.get("is_common", False))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set([self._config["output_item_attr"]])


class GsuBertTokenizationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_bert_tokenization"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr", True):
      ret.add(self._config["sentence_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common_attr", True):
      ret.add(self._config["sentence_list_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common_attr", True):
      for key in ["output_token_attr", "output_id_attr", "output_mask_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common_attr", True):
      for key in ["output_token_attr", "output_id_attr", "output_mask_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class CommonRecoGsuWithClusterTowerMatchEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_tower_match"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["match_cluster_list_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_offset_attr"])
    return ret

class PhotoTagGsuForRetrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_tag_gsu_for_retr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class LiveTagGsuForRetrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_tag_gsu_for_retr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoSimMatchUserTopClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_match_user_top_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["output_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    sign_prefix = self._config["output_sign_prefix"]
    slot_prefix = self._config["output_slot_prefix"]
    num = self._config["top_cluster_num"]
    for idx in range(num):
        ret.add(sign_prefix + str(idx))
        ret.add(slot_prefix + str(idx))
    return ret

class CommonRecoSimMatchClusterSideAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_match_cluster_side_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    sign_prefix = self._config["input_sign_attr_prefix"]
    slot_prefix = self._config["input_slot_attr_prefix"]
    num = self._config["top_cluster_num"]
    for idx in range(num):
        ret.add(sign_prefix + str(idx))
        ret.add(slot_prefix + str(idx))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonRecoGsuWithClusterForRetrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_for_retr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveGsuWithClusterForRetrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_for_retr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class KwaiproGsuForRetrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_for_retr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["bucket_attr"])
    for retr_config in self._config["retr_config"]:
      ret.update(self.extract_dynamic_params(retr_config["count"]))
      if retr_config["sort_type"] == "tag":
        ret.update(self.extract_dynamic_params(retr_config["tag_count"]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for retr_config in self._config["retr_config"]:
      ret.add(retr_config["save_to_attr"])
    return ret

class PicassoAdSimEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "picasso_ad_sim_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

class CommonRecoGsuTwinsNetFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_twinsNet_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["common_slots_name"])
    ret.add(self._config["common_signs_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_slots_name"])
    ret.add(self._config["item_signs_name"])
    return ret

class KwaiMeGsuWithClusterEnricherLite(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_with_cluster_lite"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    for attr in ["colossus_cluster_attr"]:
      if attr in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[attr], self._config["colossus_photos_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_searched_photos_attr"])
    return ret

class KwaiMeGsuTowerSortPostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_tower_sort_post"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["common_distance_ptr_attr"])
    ret.add(self._config["common_colossus_photos_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_colossus_photos_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_searched_photos_attr", "output_item_distance_attr", "output_item_raw_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuTowerSortPostNearbyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_tower_sort_post_nearby"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_distance_ptr_attr", "colossus_pid_attr", "user_lat_attr", "user_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "item_lat", "item_lon"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    for key in ["target_item_lat_attr", "target_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))

      for key in ["output_item_colossus_pid_attr", "output_item_distance_attr",
                  "output_item_raw_distance_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr",
                  "output_item_distance_attr", "output_item_raw_distance_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class KwaiMeGsuSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["searched_colossus_photos_attr"])
    ret.add(self._config["bucket_attr"])
    for key in ["photo_id_attr", "author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config.get("colossus_photos_attr", self._config["searched_colossus_photos_attr"])))
    for key in ["target_tag_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class KwaiProGsuWithIndexMultiMergeEnricher(LeafEnricher):
  """same as KwaiProGsuWithIndexEnricher"""
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_with_index_multi_merge_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr"]:
      if key in self._config:
        ret.update(set(self._config[key]))
    for key in ["colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["bucket_attr"])
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))

class FillColossusItemsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_colossus_items"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_colossus_item_attr" in self._config:
      ret.add(self._config["output_colossus_item_attr"])
    else:
      ret.add("colossus_items")
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class ExtractFeatureFromColossusResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_feature_from_colossus_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output"])
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_signs_attr"])
    ret.add(self._config["output_slots_attr"])
    if "output_pid_signs_attr" in self._config:
      ret.add(self._config["output_pid_signs_attr"])
    if "output_pid_slots_attr" in self._config:
      ret.add(self._config["output_pid_slots_attr"])
    if "output_mask_bias_attr" in self._config:
      ret.add(self._config["output_mask_bias_attr"])
    if "colossus_item_num_attr" in self._config:
      ret.add(self._config["colossus_item_num_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class ExtractIntervalFeatureFromColossusResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_interval_feature_from_colossus_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output"])
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_signs_attr"])
    ret.add(self._config["output_slots_attr"])
    if "output_pid_signs_attr" in self._config:
      ret.add(self._config["output_pid_signs_attr"])
    if "output_pid_slots_attr" in self._config:
      ret.add(self._config["output_pid_slots_attr"])
    if "output_aid_slots_attr" in self._config:
      ret.add(self._config["output_aid_slots_attr"])
    if "output_aid_signs_attr" in self._config:
      ret.add(self._config["output_aid_signs_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class ExtractUserStatFeatureFromColossusResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_user_stat_feature_from_colossus_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output"])
    for kw in ["colossus_output_type", "rng_bar_ratio", "lower_bound_num"]:
      if kw in self._config:
        ret.add(self._config[kw])
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_values_attr"])
    for kw in ["output_dura1_attr", "output_dura2_attr", "output_dura3_attr"]:
      if kw in self._config:
        ret.add(self._config[kw])
    ret.add(self._config["output_perc_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret
class GeneralExtractFeatureFromColossusResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "general_extract_feature_from_colossus_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_output", "bucket_attr", "colossus_item_index_attr", "join_pid_attr"]:
      if key in self._config: ret.add(self._config[key])
    if "colossus_output_type" in self._config and (self._config["colossus_output_type"] == "colossusv2" or self._config["colossus_output_type"] == "ksib_colossusv2"):
      for key in ["colossusv2_reflection_input_attr", "colossusv2_item_datas_input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      if "colossus_output" in self._config:
        ret.add(self._config["colossus_output"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_signs_attr", "output_slots_attr", "output_mask_bias_attr", "output_colossus_item_key_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class GeneralExtractFeatureByColossusReflectEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "general_extract_feature_by_colossus_reflect"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_output", "bucket_attr", "colossus_item_index_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_signs_attr", "output_slots_attr", "output_mask_bias_attr", "output_colossus_item_key_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class GenerateSlotSignAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_slot_sign_for_ids"

  @strict_types
  def from_common(self) -> bool:
    r = self._config.get("from_common_attr", "")
    return len(r) > 0

  @strict_types
  def get_output_attrs(self) -> set:
    ret = set()
    for key in ["output_signs_attr"]:
      if key in self._config: ret.add(self._config[key])
    output_slots = self._config.get("output_slots_attr", list())
    if output_slots:
      for c in output_slots: ret.add(c["attr_name"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["bucket_attr", "from_common_attr", "uid_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["from_item_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.get_output_attrs() if self.from_common() else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.get_output_attrs() if not self.from_common() else set()

class KsibExtractFeatureFromColossusResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_extract_feature_from_colossus_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "colossus_output_type" in self._config and (self._config["colossus_output_type"] == "colossusv2" or self._config["colossus_output_type"] == "ksib_colossusv2"):
      for key in ["colossusv2_reflection_input_attr", "colossusv2_item_datas_input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      if "colossus_output" in self._config:
        ret.add(self._config["colossus_output"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_signs_attr"])
    ret.add(self._config["output_slots_attr"])
    if "output_mask_bias_attr" in self._config:
      ret.add(self._config["output_mask_bias_attr"])
    if "colossus_item_num_attr" in self._config:
      ret.add(self._config["colossus_item_num_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class CommonAdLiveGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["cluster_id_type"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class GsuWithMultiHeadIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_multi_head_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("top_n_common_attr")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["topn_index_attr"])
    if "topn_value_attr" in self._config:
      ret.add(self._config["topn_value_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    if "output_item_colossus_pid_attr" in self._config:
      ret.add(self._config["output_item_colossus_pid_attr"])
    if "output_item_colossus_ts_attr" in self._config:
      ret.add(self._config["output_item_colossus_ts_attr"])
    if "cluster_size_attr" in self._config:
      ret.add(self._config["cluster_size_attr"])
    if "topn_loc_attr" in self._config:
      ret.add(self._config["topn_loc_attr"])
    if "topn_loc_value_attr" in self._config:
      ret.add(self._config["topn_loc_value_attr"])
    return ret

class KsibGsuWithMultiHeadIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_multi_head_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "colossus_output_type" in self._config and (self._config["colossus_output_type"] == "colossusv2" or self._config["colossus_output_type"] == "ksib_colossusv2"):
      for key in ["colossusv2_reflection_input_attr", "colossusv2_item_datas_input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      if "colossus_output" in self._config:
        ret.add(self._config["colossus_output"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["topn_index_attr"])
    if "topn_value_attr" in self._config:
      ret.add(self._config["topn_value_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    if "output_item_colossus_pid_attr" in self._config:
      ret.add(self._config["output_item_colossus_pid_attr"])
    return ret

class KsibGsuReadjustSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_gsu_readjust_sign"

  @strict_types
  def adjust_common_attr(self) -> bool:
    return self._config.get("common", False)

  @strict_types
  def get_input_attr(self) -> set:
    ret = set()
    for key in ["gsu_slots_attr", "gsu_signs_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("bucket_attr", ""))
    ret.update(self.get_input_attr() if self.adjust_common_attr() else set())
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.get_input_attr() if not self.adjust_common_attr() else set()

class CommonGoodsGsuWithCateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_item_cate2_attr"])
    ret.add(self._config["target_item_cate3_attr"])
    ret.add(self._config["target_item_spu_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_id_attr"])
    return ret
class CommonGoodsUserIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_goods_user_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["goods_id_list_attr"])
    ret.add(self._config["user_id_list_attr"])
    ret.add(self._config["label_list_attr"])
    ret.add(self._config["sorted_index_list_attr"])
    ret.add(self._config["output_id_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class GsuWithClusterGeneralEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_general"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_num")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["target_cluster_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_cluster_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = [
        self._config.get("past_days_mio_slot"),
        self._config.get("play_duration_mio_slot"),
      ] + [conf.get("mio_slot", conf.get("slot")) for conf in self._config.get("extra_sign_feature_configs", [])]
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    return ret

class LiveAuthorListFromColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_author_list_from_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    prefix = self._config.get("output_prefix", "colossus_author_")
    ret = ["author_id", "play_time_sum", "reward_sum", "reward_count", "unseen_days",
           "comment_count", "like_count", "follow_count", "record_count", "avg_acu"]
    ret = set([prefix + x for x in ret])
    return ret

class VideoAuthorListFromColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "video_author_list_from_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    prefix = self._config.get("output_prefix", "video_author_")
    ret = ["author_id", "duration_sum", "play_time_sum", "unseen_days",
           "like_count", "follow_count", "foward_count", "comment_count", "record_count"]
    ret = set([prefix + x for x in ret])
    return ret

class LiveTagGsuForRetrV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_tag_gsu_for_retr_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get('is_train_mode', True):
      ret.add(self._config['target_cluster_attr'])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self._config.get('is_train_mode', True):
      ret.add(self._config["output_sign_attr"])
      ret.add(self._config["output_slot_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get('is_train_mode', True):
      ret.add(self._config["output_sign_attr"])
      ret.add(self._config["output_slot_attr"])
    return ret

class GsuWithClusterUniqueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_cluster_uniq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_resp_attr", "colossus_item_index_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["max_uniq_item_num", "max_uniq_cluster_num", "limit_num", "skip_latest_items_num", "skip_latest_items_seconds"]:
      if key in self._config:
        ret.update(extract_common_attrs(self._config[key]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["target_item_cluster_id_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["uniq_topk_items_attr", "uniq_topk_items_cluster_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["target_topk_index_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class CommonSlideGsuWithClusterPidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_cluster_attr",
        "output_aid_attr",
        "output_pid_attr",
        "output_diffs_attr",
        "output_label_attr",
        "output_duration_attr",
        "output_playtime_attr"] if k in self._config)

class KmeansAggregateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kmeans_aggregate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["cluster_action_index_common_attr_name", "cluster_action_num_common_attr_name", "action_list_id_common_attr_name"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_common_attr_name"])
    return ret

class ExtractFeatureFromRecoLongTermEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_feature_from_reco_long_term_action_list"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "colossus_resp_attr"] if k in self._config)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_slot_attr",
        "output_sign_attr",
        "save_pid_to_attr"] if k in self._config)

class CommonLiveGsuWithClusterAidV4Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_with_aid_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_cluster_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class GsuWithExploreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_explore"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_aid_attr"])
    ret.add(self._config["colossus_duration_attr"])
    ret.add(self._config["colossus_play_attr"])
    ret.add(self._config["colossus_time_attr"])
    ret.add(self._config["colossus_label_attr"])
    ret.add(self._config["colossus_tag_attr"])
    ret.add(self._config["colossus_channel_attr"])
    ret.update({
      self._config[key] for key in [
        "limit_item_num", "reco_playtime_threshold_s",
        "search_playtime_threshold_s", "save_tag_stat_feas",
      ] if key in self._config
    })
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_explore_pids_to_attr", "save_explore_aids_to_attr",
        "save_explore_durations_to_attr", "save_explore_plays_to_attr",
        "save_explore_timestamps_to_attr", "save_explore_tags_to_attr",
        "save_explore_channels_to_attr",
        "save_explore_tag_play_avg_to_attr",
        "save_explore_tag_like_rate_to_attr",
        "save_explore_tag_follow_rate_to_attr",
        "save_explore_tag_forward_rate_to_attr",
        "save_explore_tag_hate_rate_to_attr",
        "save_explore_tag_comment_rate_to_attr",
        "save_explore_tag_enter_profile_rate_to_attr",
        "save_explore_tag_enter_comment_rate_to_attr",

        "save_search_pids_to_attr", "save_search_aids_to_attr",
        "save_search_durations_to_attr", "save_search_plays_to_attr",
        "save_search_timestamps_to_attr", "save_search_tags_to_attr",
        "save_search_tag_play_avg_to_attr",
        "save_search_tag_like_rate_to_attr",
        "save_search_tag_follow_rate_to_attr",
        "save_search_tag_forward_rate_to_attr",
        "save_search_tag_hate_rate_to_attr",
        "save_search_tag_comment_rate_to_attr",
        "save_search_tag_enter_profile_rate_to_attr",
        "save_search_tag_enter_comment_rate_to_attr",
      ] if key in self._config
    }

class CommonParseBitwiseLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_bitwise_label"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common", True):
      ret.add(self._config["input_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common", True):
      ret.update([x["output_attr"] for x in self._config["bit_configs"]])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common", True):
      ret.add(self._config["input_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common", True):
      ret.update([x["output_attr"] for x in self._config["bit_configs"]])
    return ret

class ExtractFeatureFromKmeansColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_feature_from_kmeans_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output"])
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_signs_attr"])
    ret.add(self._config["output_slots_attr"])
    ret.add(self._config["output_pid_signs_attr"])
    ret.add(self._config["output_pid_slots_attr"])
    for attr in ["output_mask_bias_attr", "colossus_item_num_attr", "cluster_size_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class AdDspAid2AidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_dsp_aid2aid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_pid_attr","limit_num_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_attr"])
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
class GsuColossusV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if 'query_key' in self._config:
      ret.add(self._config["query_key"])
    if 'concat_variable_item_fields' in self._config:
      ret.add(self._config['concat_variable_item_fields'].values())
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if 'v1_colossus_resp' in self._config:
      return set([self._config['v1_colossus_resp']])
    ret = set()
    if 'do_not_parse_to_common_attr' not in self._config or self._config['do_not_parse_to_common_attr']:
      ret = set(self._config["item_fields"].values())
    attrs = ['reflection_output_attr', 'item_datas_output_attr', 'partial_schema_output_attr']
    for attr in attrs:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

class GsuColossusV2BatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2Batch"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_keys_attr", ""))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_int_attr" in self._config:
      ret.add(self._config["output_int_attr"])
    if "output_str_attr" in self._config:
      ret.add(self._config["output_str_attr"])
    if "output_double_attr" in self._config:
      ret.add(self._config["output_double_attr"])
    if "output_repeated_int_attr" in self._config:
      ret.add(self._config["output_repeated_int_attr"])
    if "output_repeated_str_attr" in self._config:
      ret.add(self._config["output_repeated_str_attr"])
    if "output_repeated_double_attr" in self._config:
      ret.add(self._config["output_repeated_double_attr"])
    return ret

class GsuColossusV2DebugEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2Debug"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "input_int_attr" in self._config:
      ret.add(self._config["input_int_attr"])
    if "input_str_attr" in self._config:
      ret.add(self._config["input_str_attr"])
    if "input_double_attr" in self._config:
      ret.add(self._config["input_double_attr"])
    if "input_repeated_int_attr" in self._config:
      ret.add(self._config["input_repeated_int_attr"])
    if "input_repeated_str_attr" in self._config:
      ret.add(self._config["input_repeated_str_attr"])
    if "input_repeated_double_attr" in self._config:
      ret.add(self._config["input_repeated_double_attr"])
    for attr in ['reflection_input_attr', 'item_datas_input_attr', 'merged_colossusv2_data_from']:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

class GsuColossusV2ClothoMergeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2ClothoMerge"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for input_attr in ['colossus_reflection_from', 'colossus_data_from', 'clotho_data_from']:
      if input_attr in self._config:
        ret.add(self._config[input_attr])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set(self._config["item_fields"].values())
    if 'output_data_to' in self._config:
      ret.add(self._config['output_data_to'])
    return ret

class GsuColossusV2BatchDecodeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "GsuColossusV2BatchDecode"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "input_int_attr" in self._config:
      ret.add(self._config["input_int_attr"])
    if "input_uid_list_attr" in self._config:
      ret.add(self._config["input_uid_list_attr"])
    if "input_uid_score_list_attr" in self._config:
      ret.add(self._config["input_uid_score_list_attr"])
    if "output_int_attr_prefix" in self._config:
      ret.add(self._config["output_int_attr_prefix"])

    if "output_uid_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config.get("output_uid_num")))
    if "output_item_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config.get("output_item_num")))
    return ret
  
class GsuColossusV2CheckpointEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2Checkpoint"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["save_reflection_to_attr", "save_key_to_attr", "save_data_to_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuColossusV2JsonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2Json"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set(["input_key_attr"])
    if "input_service_info_attr" in self._config:
      ret.add(self._config["input_service_info_attr"])
    if "input_colossus_version_attr" in self._config:
      ret.add(self._config["input_colossus_version_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("output_result_attr", []))

class GsuColossusV2ParseUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2ParseUpdate"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if 'input_data_from' in self._config:
      ret.add(self._config["input_data_from"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if 'do_not_parse_to_list' not in self._config or not self._config['do_not_parse_to_list']:
      ret = set(self._config["output_fields"].values())
    for key in ["output_key_attr", "output_reflection_attr", "output_item_data_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class GsuColossusV2BuildUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossusV2BuildUpdate"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set(self._config["input_fields"].values())
    if 'key_from' in self._config:
      ret.add(self._config["key_from"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if 'output_attr' in self._config:
      ret.add(self._config["output_attr"])
    return ret

class GsuSingleEdgeFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "SingleEdgeFeatureEnricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_keys_from"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config["item_fields"].values())

class GsuConcatAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_concat_attr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("colossus_concat_attr", []))
    return attrs


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr_name"])
    return ret

class GsuConcatColossusV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_concat_colossusv2_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("colossus_concat_attr", []))
    attrs.add(self._config["reflection_attr_name"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr_name"])
    return ret

class GsuCalculateColossusLengthEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_calculate_colossus_length_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr_name"])
    return ret

class GsuCalculateColossusV2LengthEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_calculate_colossusv2_length_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    if "colossus_resp_type" not in self._config or self._config["colossus_resp_type"] == "colossusv2":
      ret.add(self._config["reflection_attr_name"])
    return ret


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr_name"])
    return ret

class ColossusConcatRealtimeBehaviorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_concat_realtime_behavior"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_profile_attr"])
    ret.add(self._config["colossus_input_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("realshow_index_recount_minutes")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_output_attr"])
    for key in ["realshow_index0_notgray_count", "realshow_index0_today_count", "realshow_index0_gray_count", "fix_realshow_index0_count", "realshow_index0_gray_label_count"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret


class SimComputeOffloadEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_compute_offload"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_packed_item_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("offload_per_million")))
    attrs.add(self._config.get("sample_list_common_attr_key"))
    attrs.add(self._config.get("send_item_attrs_in_name_list"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_item_attrs", []), "as")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_common_attrs", []), "as")
    attrs.add(self._config.get("compute_offload_name", "use_compute_offload"))
    return attrs

class KsibLiveGsuAidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_live_gsu_with_aids"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit_num_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_aids_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonGsuWithRqVaeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_gsu_with_rq_vae_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["photo_ids"])
    ret.add(self._config["semantic_ids"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_semantic_id"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_filter_first"])
    ret.add(self._config["output_filter_second"])
    ret.add(self._config["output_filter_third"])
    ret.add(self._config["output_match_cnt"])
    return ret

class ExtractAttrForGoodColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_attr_for_good_colossus_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["cate1_list_attr"])
    ret.add(self._config["cate2_list_attr"])
    ret.add(self._config["cate3_list_attr"])
    ret.add(self._config["category_list_attr"])
    ret.add(self._config["flow_type_list_attr"])
    ret.add(self._config["from_list_attr"])
    ret.add(self._config["index_list_attr"])
    ret.add(self._config["photo_id_list_attr"])
    ret.add(self._config["lag_list_attr"])
    ret.add(self._config["real_price_attr"])
    ret.add(self._config["real_seller_id_list_attr"])
    ret.add(self._config["seller_id_list_attr"])
    ret.add(self._config["timestamp_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["resp_item_id_list_attr"])
    ret.add(self._config["resp_seller_id_list_attr"])
    ret.add(self._config["resp_real_seller_id_list_attr"])
    ret.add(self._config["resp_lag_list_attr"])
    ret.add(self._config["resp_cate1_list_attr"])
    ret.add(self._config["resp_cate2_list_attr"])
    ret.add(self._config["resp_cate3_list_attr"])
    ret.add(self._config["resp_category_list_attr"])
    ret.add(self._config["resp_carry_type_list_attr"])
    ret.add(self._config["resp_click_type_list_attr"])
    ret.add(self._config["resp_click_from_list_attr"])
    ret.add(self._config["resp_real_price_list_attr"])
    ret.add(self._config["resp_index_list_attr"])
    ret.add(self._config["resp_lag_hour_list_attr"])
    ret.add(self._config["resp_lag_min_list_attr"])  
    return ret

class ExtractAttrForGoodColossusV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_attr_for_good_colossus_v2_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "cate1_list_attr" in self._config:
      ret.add(self._config["cate1_list_attr"])
    if "cate2_list_attr" in self._config:
      ret.add(self._config["cate2_list_attr"])
    if "cate3_list_attr" in self._config:
      ret.add(self._config["cate3_list_attr"])
    if "category_list_attr" in self._config:
      ret.add(self._config["category_list_attr"])
    if "flow_type_list_attr" in self._config:
      ret.add(self._config["flow_type_list_attr"])
    if "from_list_attr" in self._config:
      ret.add(self._config["from_list_attr"])
    if "index_list_attr" in self._config:
      ret.add(self._config["index_list_attr"])
    if "photo_id_list_attr" in self._config:
      ret.add(self._config["photo_id_list_attr"])
    if "lag_list_attr" in self._config:
      ret.add(self._config["lag_list_attr"])
    if "real_price_attr" in self._config:
      ret.add(self._config["real_price_attr"])
    if "real_seller_id_list_attr" in self._config:
      ret.add(self._config["real_seller_id_list_attr"])
    if "seller_id_list_attr" in self._config:
      ret.add(self._config["seller_id_list_attr"])
    if "timestamp_list_attr" in self._config:
      ret.add(self._config["timestamp_list_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "resp_item_id_list_attr" in self._config:
      ret.add(self._config["resp_item_id_list_attr"])
    if "resp_seller_id_list_attr" in self._config:
      ret.add(self._config["resp_seller_id_list_attr"])
    if "resp_real_seller_id_list_attr" in self._config:
      ret.add(self._config["resp_real_seller_id_list_attr"])
    if "resp_lag_list_attr" in self._config:
      ret.add(self._config["resp_lag_list_attr"])
    if "resp_cate1_list_attr" in self._config:
      ret.add(self._config["resp_cate1_list_attr"])
    if "resp_cate2_list_attr" in self._config:
      ret.add(self._config["resp_cate2_list_attr"])
    if "resp_cate3_list_attr" in self._config:
      ret.add(self._config["resp_cate3_list_attr"])
    if "resp_category_list_attr" in self._config:
      ret.add(self._config["resp_category_list_attr"])
    if "resp_carry_type_list_attr" in self._config:
      ret.add(self._config["resp_carry_type_list_attr"])
    if "resp_click_type_list_attr" in self._config:
      ret.add(self._config["resp_click_type_list_attr"])
    if "resp_click_from_list_attr" in self._config:
      ret.add(self._config["resp_click_from_list_attr"])
    if "resp_real_price_list_attr" in self._config:
      ret.add(self._config["resp_real_price_list_attr"])
    if "resp_index_list_attr" in self._config:
      ret.add(self._config["resp_index_list_attr"])
    if "resp_lag_hour_list_attr" in self._config:
      ret.add(self._config["resp_lag_hour_list_attr"])
    if "resp_lag_min_list_attr" in self._config:
      ret.add(self._config["resp_lag_min_list_attr"])  
    return ret


class ExtractAttrForEshopVideoColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_attr_for_eshop_video_colossus_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["request_time_attr"])
    if "list_limit_size" in self._config:
      ret.add(self._config["list_limit_size"])
    if "filt_type" in self._config:
      ret.add(self._config["filt_type"])
    if "photo_id_list_attr" in self._config:
      ret.add(self._config["photo_id_list_attr"])
    if "author_id_list_attr" in self._config:
      ret.add(self._config["author_id_list_attr"])
    if "duration_list_attr" in self._config:
      ret.add(self._config["duration_list_attr"])
    if "play_time_list_attr" in self._config:
      ret.add(self._config["play_time_list_attr"])
    if "channel_list_attr" in self._config:
      ret.add(self._config["channel_list_attr"])
    if "label_list_attr" in self._config:
      ret.add(self._config["label_list_attr"])
    if "timestamp_list_attr" in self._config:
      ret.add(self._config["timestamp_list_attr"])
    if "spu_id_list_attr" in self._config:
      ret.add(self._config["spu_id_list_attr"])
    if "category_list_attr" in self._config:
      ret.add(self._config["category_list_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "resp_pid_list_attr" in self._config:
      ret.add(self._config["resp_pid_list_attr"])
    if "resp_aid_list_attr" in self._config:
      ret.add(self._config["resp_aid_list_attr"])
    if "resp_duration_list_attr" in self._config:
      ret.add(self._config["resp_duration_list_attr"])
    if "resp_play_time_list_attr" in self._config:
      ret.add(self._config["resp_play_time_list_attr"])
    if "resp_channel_list_attr" in self._config:
      ret.add(self._config["resp_channel_list_attr"])
    if "resp_label_list_attr" in self._config:
      ret.add(self._config["resp_label_list_attr"])
    if "resp_lag_min_list_attr" in self._config:
      ret.add(self._config["resp_lag_min_list_attr"])
    if "resp_spu_id_list_attr" in self._config:
      ret.add(self._config["resp_spu_id_list_attr"])
    if "resp_category_list_attr" in self._config:
      ret.add(self._config["resp_category_list_attr"])
    return ret

class KsibMarmGsuWithIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_marm_gsu_with_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
        ret = ret.union(set(self._config["slots"]))
    else:
        ret.add(self._config["sign_input"])
        ret.add(self._config["slot_input"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["topk_index_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
        ret = ret.union(set(self._config["output_slots"]))
    else:
        ret.add(self._config["output_slot_attr"])
        ret.add(self._config["output_sign_attr"])
    return ret

class KsibSlotSignTransEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_slot_sign_trans"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common", True):
      ret.add(self._config["sign_input"])
      ret.add(self._config["slot_input"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("is_common", True):
      ret = set(self._config["slots"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common", True):
      ret.add(self._config["sign_input"])
      ret.add(self._config["slot_input"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("is_common", True):
      ret = set(self._config["slots"])
    return ret

class MarmColossusJoinEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "marm_colossus_join"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr in ["pid_attr", "join_pid_attr"]:
      ret.add(self._config[attr])
    use_split_mode = self._config.get("use_split_mode", False)
    if use_split_mode:
        for attr in ["time_attr", "emb_attr", "split_time_attr"]:
            ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    use_split_mode = self._config.get("use_split_mode", False)
    if use_split_mode:
        for attr in ["output_history_pid_attr", "output_history_emb_attr", "output_target_pid_attr", "output_target_emb_attr"]:
            ret.add(self._config[attr])
    else:
        for attr in ["output_pid_attr"]:
            ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class GsuClothoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_clotho_get"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
      return set([self._config['output_attr']])
