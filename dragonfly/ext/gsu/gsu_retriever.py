import operator
import itertools

from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafRetriever


class CommonRecoColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("from_colossus_sim_v2", False):
      if "colossus_resp_attr" in self._config:
        ret.add(self._config["colossus_resp_attr"])
    else:
      ret = {
        self._config[key] for key in [
            "photo_id_from", "author_id_from", "duration_from", "play_time_from",
            "tag_from", "label_from", "timestamp_from",
        ] if key in self._config
      }
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_seconds")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError(
            "colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(self._config["colossus_result_list_attr"])
      return ret
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }

class CommonRecoColossusRespV5Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_colossus_resp_retriever_v5"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_pid_attr_to"))
    ret.add(self._config.get("save_aid_attr_to"))
    ret.add(self._config.get("save_play_attr_to"))
    ret.add(self._config.get("save_duration_attr_to"))
    ret.add(self._config.get("save_tag_attr_to"))
    ret.add(self._config.get("save_time_attr_to"))
    ret.add(self._config.get("save_label_attr_to"))
    ret.add(self._config.get("save_channel_attr_to"))

    return ret

class CommonEshopColossusCutRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_eshop_colossus_cut_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["eshop_video_timestamp_list"])
    ret.add(self._config["eshop_video_photo_id_list"])
    ret.add(self._config["eshop_video_author_id_list"])
    ret.add(self._config["eshop_video_play_time_list"])
    ret.add(self._config["eshop_video_channel_list"])
    ret.add(self._config["eshop_video_label_list"])
    ret.add(self._config["eshop_video_duration_list"])
    ret.add(self._config["eshop_video_category_list"])
    ret.add(self._config["eshop_video_spu_id_list"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for i in range(4):
      ret = ret.union({
        f"{self._config[key]}_{i}" for key in [
            "eshop_video_timestamp_cut_prefix", "eshop_video_photo_id_cut_prefix",
            "eshop_video_author_id_cut_prefix", "eshop_video_play_time_cut_prefix",
            "eshop_video_channel_cut_prefix", "eshop_video_label_cut_prefix",
            "eshop_video_duration_cut_prefix", "eshop_video_category_cut_prefix",
            "eshop_video_spu_id_cut_prefix", "eshop_video_save_diff_cut_prefix",
            "eshop_video_save_diff_hour_cut_prefix"
        ] if key in self._config
      })
    return ret

class CommonEshopColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_eshop_colossus_resp_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["eshop_video_timestamp_list"])
    ret.add(self._config["eshop_video_photo_id_list"])
    ret.add(self._config["eshop_video_author_id_list"])
    ret.add(self._config["eshop_video_play_time_list"])
    ret.add(self._config["eshop_video_channel_list"])
    ret.add(self._config["eshop_video_label_list"])
    ret.add(self._config["eshop_video_duration_list"])
    ret.add(self._config["eshop_video_category_list"])
    ret.add(self._config["eshop_video_spu_id_list"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_eshop_video_timestamp_attr_to"))
    ret.add(self._config.get("save_eshop_video_photo_id_attr_to"))
    ret.add(self._config.get("save_eshop_video_author_id_attr_to"))
    ret.add(self._config.get("save_eshop_video_play_time_attr_to"))
    ret.add(self._config.get("save_eshop_video_channel_attr_to"))
    ret.add(self._config.get("save_eshop_video_label_attr_to"))
    ret.add(self._config.get("save_eshop_video_duration_attr_to"))
    ret.add(self._config.get("save_eshop_video_category_1_attr_to"))
    ret.add(self._config.get("save_eshop_video_category_2_attr_to"))
    ret.add(self._config.get("save_eshop_video_category_3_attr_to"))
    ret.add(self._config.get("save_eshop_video_spu_id_attr_to"))
    ret.add(self._config.get("save_diff_attr_to"))
    ret.add(self._config.get("save_diff_hour_attr_to"))
    ret.add(self._config.get("save_hour_of_day_attr_to"))
    ret.add(self._config.get("save_if_positive_seq_attr_to"))
    return ret

class PxtrsColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pxtrs_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("from_v2_service", False):
      if "colossus_resp_attr" in self._config:
        ret.add(self._config["colossus_resp_attr"])
    else:
      for key in ["photo_id_from", "author_id_from", "photo_pxtr_from", "timestamp_from", "label_from",
                  "play_time_from", "duration_from", "hetu_tag_from", "from_v2_service"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_timestamp_to_attr",
            "save_playtime_to_attr", "save_duration_to_attr",
            "save_pctr_to_attr", "save_pvtr_to_attr", "save_pltr_to_attr", "save_pwtr_to_attr",
            "save_pftr_to_attr", "save_plvtr_to_attr", "save_hetu_tag_to_attr", "save_tag_playtime",
            "save_tag_playtime_to_attr", "save_label_to_attr"
        ] if key in self._config
    }


class GsuColossusRespBatchRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retriever_with_colossus_resp_batch_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_resp_attr", "uid_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    ret.update(self.extract_dynamic_params(self._config.get("filter_past_days")))
    if self._config.get("from_colossus_sim_v2", False):
      if "input_reflection_attr" in self._config:
        ret.add(self._config['input_reflection_attr'])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_channel_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
            "save_photo_lat_attr", "save_photo_lon_attr", "save_user_lat_attr", "save_user_lon_attr",
            "save_user_id_to_attr",
        ] if key in self._config
    }


class CommonColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_common_colossus_resp_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("from_colossus_sim_v2", False):
      if "colossus_resp_attr" in self._config:
        ret.add(self._config["colossus_resp_attr"])
    else:
      ret = set(self._config["input_item_fields"].values())
      if "partial_schema_input_attr" in self._config:
        ret.add(self._config['partial_schema_input_attr'])
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("max_item_num")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set()
    return set(self._config["item_fields"].values())

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set(self._config["item_fields"].values())
    return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 获取当前 Processor 增加结果集的 tables """
    if self._config.get("to_common_attr", False):
      return set()
    return super().output_item_tables

class CommonLiveColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_page_type_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_optag_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }


class CommonRecoColossusRespRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retriever_with_colossus_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("colossusv2_reflection_input_attr") and not self._config.get("colossusv2_item_datas_input_attr"):
      if "colossus_resp_attr" in self._config:
        ret.add(self._config["colossus_resp_attr"])
    else:
      ret = {
        self._config[key] for key in [
            "colossusv2_reflection_input_attr",
            "colossusv2_item_datas_input_attr"
        ] if key in self._config
      }
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_channel_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
            "save_photo_lat_attr", "save_photo_lon_attr", "save_user_lat_attr", "save_user_lon_attr",
        ] if key in self._config
    }

class CommonLiveColossusRespRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_retriever_with_colossus_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_page_type_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_optag_to_attr", "save_reward_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }


class CommonLiveColossusRespRetrieverV4(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_retriever_with_colossus_resp_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
          "save_timestamp_to_attr_", "save_author_id_to_attr_", "save_play_time_to_attr_",
          "save_auto_play_time_to_attr_", "save_hetu_tag_to_attr_", "save_channel_to_attr_",
          "save_cluster_id_to_attr_", "save_label_to_attr_", "save_reward_to_attr_",
          "save_reward_count_to_attr_", "save_item_id_to_attr_", "save_audience_count_to_attr_",
          "save_user_latitude_to_attr_", "save_user_longitude_to_attr_", "save_author_latitude_to_attr_",
          "save_author_longitude_to_attr_", "save_order_price_to_attr_"
        ] if key in self._config
    }


class KwaiProColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaipro_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }

class KsibColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "colossus_output_type" in self._config and (self._config["colossus_output_type"] == "colossusv2" or self._config["colossus_output_type"] == "ksib_colossusv2"):
      for key in ["colossusv2_reflection_input_attr", "colossusv2_item_datas_input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    else:
      if "colossus_resp_attr" in self._config:
        ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr", "save_channel_to_attr"
        ] if key in self._config
    }

class SnackColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }


class KwaiMeColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    for attr in ["user_bucket_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_photo_sign_to_attr", "save_author_id_to_attr",
            "save_author_sign_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }


class KwaiMeGsuUserClusterRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwaime_gsu_user_cluster_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("bucket")))
    ret.update(self.extract_dynamic_params(self._config.get("max_user_cluster")))
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr in ["target_cluster_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret


class CommonRecoGsuUserClusterRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_user_cluster_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_resp_attr", "limit_num_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class BatchProcessCheckpointRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retrieve_from_colossus_sim_checkpoint"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    keys = ["save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr", "save_channel_to_attr", "save_cluster_id_to_attr"]
    for key in keys:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class ColossusV2CheckpointRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retrieve_from_colossus_v2_checkpoint"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    keys = ["save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr", "save_channel_to_attr", "save_cluster_id_to_attr"]
    for key in keys:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class BatchProcessKsibCheckpointRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retrieve_from_ksib_colossus_sim_checkpoint"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    keys = ["save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr"]
    for key in keys:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class KsibLiveColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_live_gsu_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    for attr in ["user_bucket_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_live_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr", "save_auto_play_time_to_attr"
        ] if key in self._config
    }

class KsibLiveColossusRespRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_live_gsu_retriever_with_colossus_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    for attr in ["user_bucket_attr"]:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_live_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_label_to_attr", "save_timestamp_to_attr", "save_auto_play_time_to_attr"
        ] if key in self._config
    }


class CommonRecoColossusRespAuthorRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_retriever_author_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_author_id_to_attr",
        ] if key in self._config
    }


class CommonColossusRespRetrieverExtend(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retriever_with_colossus_resp_ext"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_channel_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
            "save_photo_lat_attr", "save_photo_lon_attr", "save_user_lat_attr", "save_user_lon_attr",
        ] if key in self._config
    }
