#!/usr/bin/env python3
"""
filename: ksib_mix_rank_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module for mix rank 
author: yang<PERSON><EMAIL>
date: 2022-03-25 18:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class KsibMixRankAdFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_filter_arranger"

  @strict_types
  def depend_on_items(self) -> bool:
    return True 

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("hit_mix_rank_dynamic_adpos")
    attrs.add("enable_dynamic_adpos")
    attrs.add("upstream_selected_items")
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    dynamic_ad_insert_list_attr = self._config.get("dynamic_ad_insert_list_attr")
    if dynamic_ad_insert_list_attr:
      attrs.add(dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("filter_condition")
    attrs.add("ad_pos_id")
    attrs.add("filter_condition")
    attrs.add("rank_pos_in_req")
    return attrs
