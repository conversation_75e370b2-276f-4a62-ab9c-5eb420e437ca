#!/usr/bin/env python3
# coding=utf-8
"""
filename: ksib_mix_rank_adpi_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, ksib mix rank api mixin
author: yang<PERSON><PERSON>@kuaishou.com
date: 2022-03-25 18:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ksib_mix_rank_retriever import *
from .ksib_mix_rank_enricher import *
from .ksib_mix_rank_arranger import *


class KsibMixRankApiMixin(CommonLeafBaseMixin):
  """
  海外混排服务 Processor API 接口的 Mixin 实现。

  背景：海外广告依据自然作品的排序分进行各种融合的排序，实现个性化的广告 adload，提升广告的曝光和用户体验指标。

  维护人/团队：海外广告(<EMAIL>、<EMAIL>、<EMAIL>)、海外 Reco (<EMAIL>)
  """

  def ksib_mix_rank_ad_common_attr_enricher(self, **kwargs):
    """
    KsibMixRankAdCommonAttrEnricher
    ------
    将广告请求的信息存储到 CommonAttr 中。
    如果推荐用户 uid = 0，则使用广告生成的 uid 覆盖 Context 中 uid。

    参数配置
    ------
    `mix_rank_request`: [string] 必配项，从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 GetAdResponse Message

    `output_ad_request_attr`: [string] 选配项，输出 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `output_dynamic_ad_insert_list_attr`: [string] 选配项，输出动态广告位策略计算得到的最终广告插入位置列表 CommonAttr 的 name，默认值 dynamic_ad_insert_list

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_attr_enricher(
      mix_rank_request="mix_rank_request_pb",
      output_ad_request_attr="ad_request_pb",
      output_dynamic_ad_insert_list_attr="dynamic_ad_insert_list"
    )
    ```
    """
    self._add_processor(KsibMixRankAdCommonAttrEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_retriever(self, **kwargs):
    """
    KsibMixRankAdRetriever
    ------
    遍历混排请求的广告候选集，将自有广告添加到 RecoResults 中，三方广告不做任何处理。
    同时将广告 ad_response 和 ad_result pb 填充到 ItemAttribute中。
    设置广告 tag_id = 2 存储到 ItemAttr 中，用于后续 Process 使用参数 target_item 匹配广告结果。

    参数配置
    ------
    `mix_rank_request`: [string] 必配项，从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 GetAdResponse Message

    `reason`: [int] 选配项，广告的推荐原因，默认为 2

    `item_type`: [int] 选配项，广告的 Item 类型，默认为 2

    `output_ad_response_item_attr`: [string] 选配项，输出 AdResponse protobuf 类型 ItemAttr 的 name，默认值 ad_response_pb

    `output_ad_result_item_attr`: [string] 选配项，输出 AdResult protobuf 类型 ItemAttr 的 name，默认值 ad_result_pb

    `output_rtb_ad_info_attr`: [string] 选配项，输出 RTB 广告 protobuf 类型 ItemAttr 的 name，默认值 rtb_ad_info_pb

    `output_intl_source_type_attr`: [string] 选配项，输出 每个广告对应的广告服务端广告标识，用于区分广告来源 (only RTB or kwai) ItemAttr 的 name，默认值 ad_intl_source_type

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_retriever(
      mix_rank_request="mix_rank_request_pb",
      reason=2,
      item_type=2,
      output_ad_response_item_attr="ad_response_pb",
      output_ad_result_item_attr="ad_result_pb",
      output_rtb_ad_info_attr="rtb_ad_info_pb",
      output_intl_source_type_attr="ad_intl_source_type"
    )
    ```
    """
    self._add_processor(KsibMixRankAdRetriever(kwargs))
    return self

  def ksib_mix_rank_ad_item_attr_enricher(self, **kwargs):
    """
    KsibMixRankAdItemAttrEnricher
    ------
    将候选广告的信息存储到 ItemAttr 中。

    参数配置
    ------
    `mix_rank_request`: [string] 必配项，从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 GetAdResponse Message

    `ad_result_item_attr`: [string] 选配项，输入 AdResult protobuf 类型 ItemAttr 的 name，默认值 ad_result_pb

    `input_rtb_ad_info_attr`: [string] 选配项，输入 RTB 广告 protobuf 类型 ItemAttr 的 name，默认值 rtb_ad_info_pb

    `input_intl_source_type_attr`: [string] 选配项，输入每个广告对应的广告服务端广告标识，用于区分广告来源 (only RTB or kwai) ItemAttr 的 name，默认值 ad_intl_source_type
    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_attr_enricher(
      mix_rank_request="mix_rank_request_pb",
      ad_result_item_attr="ad_result_pb",
      input_rtb_ad_info_attr="rtb_ad_info_pb",
      input_intl_source_type_attr="ad_intl_source_type"
    )
    ```
    """
    self._add_processor(KsibMixRankAdItemAttrEnricher(kwargs))
    return self

  def ksib_mix_rank_dynamic_ad_pos_enricher(self, **kwargs):
    """
    KsibMixRankDynamicAdPosEnricher
    ------
    根据广告 rank_benefit 计算本次请求的动态广告位。
    目前只考虑请求中的第一个广告，即调整第一个广告的展现位置上移或下移。

    参数配置
    ------
    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `output_dynamic_ad_insert_list_attr`: [string] 选配项，输出动态广告位策略计算得到的最终广告插入位置列表 CommonAttr 的 name，默认值 dynamic_ad_insert_list

    调用示例
    ------
    ``` python
    .ksib_mix_rank_dynamic_ad_pos_enricher(
      ad_request_common_attr="ad_request_pb",
      output_dynamic_ad_insert_list_attr="dynamic_ad_insert_list"
    )
    ```
    """
    self._add_processor(KsibMixRankDynamicAdPosEnricher(kwargs))
    return self

  def ksib_mix_rank_listwise_mixrank_enricher(self, **kwargs):
    """
    KsibMixRankListwiseMixrankEnricher
    ------
    参数配置
    ------
    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `output_dynamic_ad_insert_list_attr`: [string] 选配项，输出 listwise 混排策略计算得到的最终广告插入位置列表 CommonAttr 的 name,
                                          默认值 dynamic_ad_insert_list

    调用示例
    ------
    ``` python
    .ksib_mix_rank_listwise_mixrank_enricher(
      ad_request_common_attr="ad_request_pb",
      output_dynamic_ad_insert_list_attr="dynamic_ad_insert_list"
    )
    ```
    """
    self._add_processor(KsibMixRankListwiseMixrankEnricher(kwargs))
    return self

  def ksib_mix_rank_listwise_match_enricher(self, **kwargs):
    """
    KsibMixRankListwiseMatchEnricher
    海外商业化 listwise 混排策略序列召回模块
    需要和 ksib_mix_rank_listwise_rank_enricher 配合使用
    ------
    参数配置
    ------
    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb
    `reco_item_type`: [string] 选配项，自然短视频结果对应的 item_type, 默认值 1
    `ad_item_type`: [string] 选配项，广告结果对应的 item_type, 默认值 2
    `output_seq_candidate_attr`: [string] 选配项，输出 listwise 混排策略候选序列列表 CommonAttr 的 name,
                                          默认值 seq_candidate_result
    `output_seq_item_key_list_attr`: [string] 选配项，输出 listwise 混排策略候选序列对应的item_key key list
                                     CommonAttr 的 name, 默认值 seq_item_key_list
    `output_seq_item_key_size_list_attr`: [string] 选配项，输出 listwise 混排策略候选序列对应的item_key size list
                                     CommonAttr 的 name, 默认值 seq_item_key_size_list
    `output_seq_ad_gap_list_attr`: [string] 选配项，输出 listwise 混排策略候选序列对应的ad_gap key list
                                     CommonAttr 的 name, 默认值 seq_ad_gap_list
    `output_seq_ad_gap_size_list_attr`: [string] 选配项，输出 listwise 混排策略候选序列对应的item_key size list
                                     CommonAttr 的 name, 默认值 seq_ad_gap_size_list
    `output_seq_hash_id_list_attr`:  [string] 选配项，输出 候选序列 hash_id list CommonAttr 的 name, 默认值 seq_hash_id_list

    调用示例
    ------
    ``` python
    .ksib_mix_rank_listwise_match_enricher(
      ad_request_common_attr="ad_request_pb",
      reco_item_type=1,
      ad_item_type=2,
      output_seq_candidate_attr="seq_candidate_result",
      output_seq_item_key_list_attr="seq_item_key_list",
      output_seq_item_key_size_list_attr="seq_item_key_size_list",
      output_seq_ad_gap_list_attr="seq_ad_gap_list",
      output_seq_ad_gap_size_list_attr="seq_ad_gap_size_list",
      output_seq_hash_id_list_attr="seq_hash_id_list"
    )
    ```
    """
    self._add_processor(KsibMixRankListwiseMatchEnricher(kwargs))
    return self

  def ksib_mix_rank_listwise_rank_enricher(self, **kwargs):
    """
    KsibMixRankListwiseRankEnricher
    海外商业化 listwise 混排策略序列排序模块
    需要和 ksib_mix_rank_listwise_match_enricher 配合使用
    ------
    参数配置
    ------
    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `output_dynamic_ad_insert_list_attr`: [string] 选配项，输出 listwise 混排策略计算得到的最终广告插入位置列表 CommonAttr 的 name,
                                          默认值 dynamic_ad_insert_list
    `input_seq_candidate_attr`: [string] 选配项，输入 序列召回候选 CommonAttr 的 name，
                                默认值 seq_candidate_result

    调用示例
    ------
    ``` python
    .ksib_mix_rank_listwise_rank_enricher(
      ad_request_common_attr="ad_request_pb",
      output_dynamic_ad_insert_list_attr="dynamic_ad_insert_list",
      input_seq_candidate_attr="seq_candidate_result"
    )
    ```
    """
    self._add_processor(KsibMixRankListwiseRankEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_filter_arranger(self, **kwargs):
    """
    KsibMixRankAdFilterArranger
    ------
    混排广告过滤策略，遍历广告 RecoResult 集合，根据 Item_key 获取广告 ad_response PB，判断广告插入位置：
	  - 如果存在广告插入位置，则将广告胜出标记和插入位置填充到 ItemAttr 中，同时标记该插入位置已使用；
	  - 如果没有广告插入位置，则广告过滤原因填充到 ItemAttr 中，删除该广告 RecoResult；

    参数配置
    ------
    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `dynamic_ad_insert_list_attr`: [string] 选配项，输入动态广告位策略计算得到的最终广告插入位置列表 CommonAttr 的 name，默认值 dynamic_ad_insert_list

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_filter_arranger(
      ad_request_common_attr="ad_request_pb",
      dynamic_ad_insert_list_attr="dynamic_ad_insert_list"
    )
    ```
    """
    self._add_processor(KsibMixRankAdFilterArranger(kwargs))
    return self

  def ksib_mix_rank_build_ad_response_enricher(self, **kwargs):
    """
    KsibMixRankBuildAdResponseEnricher
    ------
    构造 MixRank 广告的 GetAdResponse 应答，在请求 GetAdResponse 的基础上更新。
    构造请求粒度的 MixRankData 和 广告粒度的 MixRankAdData 数据透传给 AdKappa 服务。
    更新广告粒度 AdResponse 中的部分混排调整的字段。

    参数配置
    ------
    `mix_rank_request`: [string] 必配项，从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 GetAdResponse Message

    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `dynamic_ad_insert_list_attr`: [string] 选配项，输入动态广告位策略计算得到的最终广告插入位置列表 CommonAttr 的 name，默认值 dynamic_ad_insert_list

    `item_type`: [int] 选配项，广告的 Item 类型，默认为 2

    调用示例
    ------
    ``` python
    .ksib_mix_rank_build_ad_response_enricher(
      mix_rank_request="mix_rank_request_pb",
      ad_request_common_attr="ad_request_pb",
      dynamic_ad_insert_list_attr="dynamic_ad_insert_list",
      item_type=2
    )
    ```
    """
    self._add_processor(KsibMixRankBuildAdResponseEnricher(kwargs))
    return self

  def ksib_mix_rank_build_ad_response_v2_enricher(self, **kwargs):
    """
    KsibMixRankBuildAdResponseV2Enricher
    ------
    构造 MixRank 广告的 GetAdResponse 应答，在请求 GetAdResponse 的基础上更新。
    构造请求粒度的 MixRankData 和 广告粒度的 MixRankAdData 数据透传给 AdKappa 服务。
    更新广告粒度 AdResponse 中的部分混排调整的字段。
    V2 版本在原来基础上将广告落日志的相关静态 attr 白盒化，能知道这个 processor 中 使用了哪些 attr

    参数配置
    ------
    `mix_rank_request`: [string] 必配项，从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 GetAdResponse Message

    `ad_request_common_attr`: [string] 选配项，输入 AdRequest protobuf 类型 CommonAttr 的 name，默认值 ad_request_pb

    `attrs`: [dict] 必配项，从 mix rank leaf 服务中传入到该算子使用的所有 attrs，需要交代清楚
      - `name`: [string] leaf 中的 attr 名称
      - `as_attr`: [string] 在算子中使用的 attr 名称
      - `is_common`: [bool] 是否是 common attr, 默认为 true

    `dynamic_ad_insert_list_attr`: [string] 选配项，输入动态广告位策略计算得到的最终广告插入位置列表 CommonAttr 的 name，默认值 dynamic_ad_insert_list

    `item_type`: [int] 选配项，广告的 Item 类型，默认为 2

    调用示例
    ------
    ``` python
    .ksib_mix_rank_build_ad_response_v2_enricher(
      mix_rank_request="mix_rank_request_pb",
      ad_request_common_attr="ad_request_pb",
      attrs = [
        dict(name="ad_pos_id", as_attr="ad_pos_id"),
        dict(name="is_back_user", as_attr="is_back_user", is_common=True),
      ]
      dynamic_ad_insert_list_attr="dynamic_ad_insert_list",
      item_type=2
    )
    ```
    """
    self._add_processor(KsibMixRankBuildAdResponseV2Enricher(kwargs))
    return self

  def ksib_mix_rank_reco_ecology_benefit_enricher(self, **kwargs):
    """
    KsibMixRankRecoEcologyBenefitEnricher
    ------
    reco items对整体生态的贡献分数

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_reco_ecology_benefit_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankRecoEcologyBenefitEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_ecology_benefit_enricher(self, **kwargs):
    """
    KsibMixRankAdEcologyBenefitEnricher
    ------
    ad对整体生态的贡献分数

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_ecology_benefit_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdEcologyBenefitEnricher(kwargs))
    return self

    
  def ksib_mix_rank_ad_ecology_loss_enricher(self, **kwargs):
    """
    KsibMixRankAdEcologyLossEnricher
    ------
    ad对整体生态的损失分数

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_ecology_loss_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdEcologyLossEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_next_request_loss_enricher(self, **kwargs):
    """
    KsibMixRankAdNextRequestLossEnricher
    ------
    ad对下一刷的影响

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_next_request_loss_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdNextRequestLossEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_gap_boost_enricher(self, **kwargs):
    """
    KsibMixRankAdGapBoostEnricher
    ------
    ad boost加强

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_gap_boost_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdGapBoostEnricher(kwargs))
    return self

    
  def ksib_mix_rank_ad_seq_business_priority_enricher(self, **kwargs):
    """
    KsibMixRankAdSeqBusinessPriorityEnricher
    ------
    设置序列的商业优先级

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_seq_business_priority_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdSeqBusinessPriorityEnricher(kwargs))
    return self

  def ksib_mix_rank_ad_fixed_pos_backup_enricher(self, **kwargs):
    """
    KsibMixRankAdFixedPosBackupEnricher
    ------
    当混排策略未计算出插入位置时，利用三方 SDK 广告做一层兜底

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ksib_mix_rank_ad_fixed_pos_backup_enricher(
    )
    ```
    """
    self._add_processor(KsibMixRankAdFixedPosBackupEnricher(kwargs))
    return self
  

    