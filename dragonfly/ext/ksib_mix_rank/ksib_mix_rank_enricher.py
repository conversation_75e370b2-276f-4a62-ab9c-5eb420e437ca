#!/usr/bin/env python3
"""
filename: ksib_mix_rank_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for mix rank
author: yang<PERSON><EMAIL>
date: 2022-03-25 18:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class KsibMixRankAdCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_common_attr_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mix_rank_request"),
              f"{self.get_type_alias()} 的 mix_rank_request 是必配项")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    attrs.add("adload_pid_value_prefix")
    attrs.add("enable_adload_pid_value_clip")
    attrs.add("pid_value_clip_max")
    attrs.add("pid_value_clip_min")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    attrs.add("hit_mix_rank_dynamic_adpos")
    attrs.add("llsid")
    attrs.add("country_code")
    # attrs.add("page_size")
    attrs.add("ad_request_type")
    # attrs.add("app_id_int")
    # attrs.add("app_id")
    attrs.add("ad_start_index")
    attrs.add("ad_show_index_interval")
    attrs.add("adload_pid_value")

    output_ad_request_attr = self._config.get("output_ad_request_attr")
    if output_ad_request_attr:
      attrs.add(output_ad_request_attr)
    else:
      attrs.add("ad_request_pb")

    output_dynamic_ad_insert_list_attr = self._config.get(
        "output_dynamic_ad_insert_list_attr")
    if output_dynamic_ad_insert_list_attr:
      attrs.add(output_dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()


class KsibMixRankAdItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_item_attr_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mix_rank_request"),
              f"{self.get_type_alias()} 的 mix_rank_request 是必配项")

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    attrs.add("hit_mix_rank_dynamic_adpos")
    attrs.add("enable_account_industry_support_boost")
    attrs.add("account_industry_support_cpm_thresh")
    attrs.add("industry_support_whitelist_tag")
    attrs.add("enable_adload_pid_adjust_rank_benefit")
    attrs.add("adload_pid_value")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    ad_result_item_attr = self._config.get("ad_result_item_attr")
    if ad_result_item_attr:
      attrs.add(ad_result_item_attr)
    else:
      attrs.add("ad_result_pb")

    input_rtb_ad_info_attr = self._config.get("input_rtb_ad_info_attr")
    if input_rtb_ad_info_attr:
      attrs.add(input_rtb_ad_info_attr)
    else:
      attrs.add("rtb_ad_info_pb")

    input_intl_source_type_attr = self._config.get(
        "input_intl_source_type_attr")
    if input_intl_source_type_attr:
      attrs.add(input_intl_source_type_attr)
    else:
      attrs.add("ad_intl_source_type")

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    # attrs.add("bid_type")
    # attrs.add("ctr")
    # attrs.add("cvr")
    # attrs.add("ueq")
    # attrs.add("cpm")
    # attrs.add("auction_bid")
    # attrs.add("price")
    # attrs.add("rank_benefit")
    # attrs.add("ad_creative_id")
    # attrs.add("ad_ctr")
    # attrs.add("ad_cvr")
    # attrs.add("ad_ueq")
    # attrs.add("ad_cpm")
    # attrs.add("ad_auction_bid")
    # attrs.add("ad_price")
    # attrs.add("ad_rank_benefit")
    # attrs.add("ad_mtl_p3s")
    # attrs.add("ad_mtl_p5s")
    # attrs.add("ad_mtl_ped")
    # attrs.add("ad_cpm_bonus")
    # attrs.add("ad_dup_photo_id")
    # attrs.add("ad_calib_ctr")
    # attrs.add("ad_calib_cvr")

    attrs.add("mixrank_ad_force_tag")
    attrs.add("bid_type")
    # attrs.add("ctr")
    # attrs.add("cvr")
    # attrs.add("ueq")
    attrs.add("cpm")
    # attrs.add("auction_bid")
    # attrs.add("price")
    attrs.add("rank_benefit")
    attrs.add("ad_ctr")
    attrs.add("ad_cvr")
    attrs.add("ad_ueq")
    attrs.add("ad_cpm")
    attrs.add("ad_auction_bid")
    attrs.add("ad_price")
    attrs.add("ad_rank_benefit")
    attrs.add("ad_mtl_p3s")
    attrs.add("ad_mtl_p5s")
    attrs.add("ad_mtl_ped")
    attrs.add("ad_cpm_bonus")
    attrs.add("ad_dup_photo_id")
    attrs.add("ad_calib_ctr")
    attrs.add("ad_calib_cvr")
    attrs.add("brand_safety_inventory")
    attrs.add("rank_benefit")
    return attrs


class KsibMixRankDynamicAdPosEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_dynamic_ad_pos_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    # 动态广告位策略实验参数虽然在 C++ Process 直接使用，但是 DragonFly 有校验参数是否使用，所以这里将参数作为 Process 的输入
    attrs.add("hit_mix_rank_dynamic_adpos")
    attrs.add("enable_dynamic_adpos")
    attrs.add("enable_sdk_fixed_pos_backup")
    attrs.add("enable_pos_shift_no_zero")


    attrs.add("ad_show_index_interval")
    attrs.add("ad_start_index")
    attrs.add("enable_adload_adjust_with_delta")
    attrs.add("ad_show_index_interval_delta")
    attrs.add("ad_start_index_delta")
    attrs.add("sub_start_idx_score")
    attrs.add("min_start_idx")
    attrs.add("add_start_idx_score")
    attrs.add("max_start_idx")
    attrs.add("sub_gap_score")
    attrs.add("min_ad_gap")
    attrs.add("enable_pos_adjust_with_norm_score")
    attrs.add("enable_ad_norm_score_pow_trans")
    attrs.add("enable_reco_norm_score_pow_trans")
    attrs.add("pow_trans_factor")
    attrs.add("ad_score_alpha")
    attrs.add("enable_offset_range_clip")
    attrs.add("offset_limit_upper_bound")
    attrs.add("offset_limit_lower_bound")
    attrs.add("enable_adjust_more_dynamic_pos")
    attrs.add("reco_score_list")
    attrs.add("ad_norm_score_prefix")
    attrs.add("reco_norm_score_prefix")
    attrs.add("add_gap_score")
    attrs.add("max_ad_gap")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    output_dynamic_ad_insert_list_attr = self._config.get(
        "output_dynamic_ad_insert_list_attr")
    if output_dynamic_ad_insert_list_attr:
      attrs.add(output_dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")
    attrs.add("upstream_selected_items")

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("remix_rank_type")
    attrs.add("rank_benefit")
    attrs.add("ad_fill_type")
    attrs.add("ad_category")
    attrs.add("rank_benefit")

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    attrs.add("ad_pos_id")
    attrs.add("filter_condition")

    return attrs


class KsibMixRankListwiseMixrankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_listwise_mixrank_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    # listwise 混排策略实验参数虽然在 C++ Process 直接使用，但是 DragonFly 有校验参数是否使用，所以这里将参数作为 Process 的输入
    attrs.add("ad_show_index_interval")
    attrs.add("ad_start_index")
    attrs.add("min_start_idx")
    attrs.add("min_ad_gap")
    attrs.add("enable_first_req_strategy")
    attrs.add("first_req_listwise_ad_score_alpha")
    attrs.add("first_req_next_req_loss_weight")
    attrs.add("first_req_gap_loss_weight")
    attrs.add("first_req_gap_loss_interval")
    attrs.add("first_req_raw_ad_score_alpha")
    attrs.add("reco_score_list")
    attrs.add("listwise_ad_norm_score_prefix")
    attrs.add("listwise_reco_norm_score_prefix")
    attrs.add("enable_listwise_reco_norm_score_pow_trans")
    attrs.add("enable_listwise_ad_norm_score_pow_trans")
    attrs.add("listwise_pow_trans_factor")
    attrs.add("listwise_ad_score_alpha")
    attrs.add("listwise_raw_ad_score_alpha")
    attrs.add("enable_raw_score_diff_alpha")
    attrs.add("high_listwise_raw_ad_score_alpha")
    attrs.add("mid_listwise_raw_ad_score_alpha")
    attrs.add("low_listwise_raw_ad_score_alpha")
    attrs.add("first_req_high_listwise_raw_ad_score_alpha")
    attrs.add("first_req_mid_listwise_raw_ad_score_alpha")
    attrs.add("first_req_low_listwise_raw_ad_score_alpha")
    attrs.add("high_rank_benefit_threshold")
    attrs.add("mid_rank_benefit_threshold")
    attrs.add("listwise_reco_norm_default_score")
    attrs.add("listwise_ad_norm_default_score")
    attrs.add("listwise_reco_pos_discount")
    attrs.add("listwise_ad_pos_discount")
    attrs.add("next_req_loss_weight")
    attrs.add("enable_calc_gap_loss_weight")
    attrs.add("gap_loss_weight")
    attrs.add("gap_loss_interval")
    attrs.add("gap_loss_factor")
    attrs.add("enable_raw_ad_score_evaluate")
    attrs.add("listwise_default_rank_benefit")
    attrs.add("enable_raw_reco_score_evaluate")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("sensitive_user_sensitivity_threshold")
    attrs.add("insensitive_user_sensitivity_threshold")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("sensitive_user_alpha_boost_ratio")
    attrs.add("insensitive_user_alpha_boost_ratio")
    attrs.add("sensitive_gap_loss_interval_delta")
    attrs.add("insensitive_gap_loss_interval_delta")
    attrs.add("enable_pos_shift_no_zero")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    output_dynamic_ad_insert_list_attr = self._config.get(
        "output_dynamic_ad_insert_list_attr")
    if output_dynamic_ad_insert_list_attr:
      attrs.add(output_dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("ad_fill_type")
    attrs.add("ad_category")
    attrs.add("ad_resource_type")
    attrs.add("rank_benefit")

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    attrs.add("ad_pos_id")
    attrs.add("filter_condition")

    return attrs


class KsibMixRankListwiseMatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_listwise_match_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    ad_item_type = self._config.get("ad_item_type")
    if ad_item_type:
      attrs.add(ad_item_type)
    reco_item_type = self._config.get("reco_item_type")
    if reco_item_type:
      attrs.add(reco_item_type)

    # listwise 混排策略实验参数虽然在 C++ Process 直接使用，但是 DragonFly 有校验参数是否使用，所以这里将参数作为 Process 的输入
    attrs.add("ad_show_index_interval")
    attrs.add("ad_start_index")
    attrs.add("min_start_idx")
    attrs.add("min_ad_gap")
    attrs.add("soft_min_ad_gap")
    attrs.add("mix_min_ad_gap")
    attrs.add("soft_min_start_idx")
    # XJY 存疑
    # attrs.add("enable_switch_sort_and_hard_ad_mode")
    # attrs.add("final_min_start_idx_control")
    # attrs.add("final_min_ad_gap_control")
    # attrs.add("use_final_control_forced")
    attrs.add("enable_high_value_pv_mixrank_strategy")
    attrs.add("high_value_min_ad_gap")
    attrs.add("high_value_rb_threshold")
    attrs.add("reco_score_list")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("sensitive_user_sensitivity_threshold")
    attrs.add("insensitive_user_sensitivity_threshold")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("no_ad_user_force_insert_round_mod")
    attrs.add("enable_filter_top3_ad")
    attrs.add("enable_first_req_strategy")
    attrs.add("enable_first_req_new_min_ad_gap")
    attrs.add("first_req_min_ad_gap")
    attrs.add("enable_new_listwise_match")
    attrs.add("enable_listwise_match_adgap_bugfix")
    attrs.add("enable_v2_listwise_match")
    attrs.add("enable_beam_search_match")
    attrs.add("listwise_match_beam_search_topk")
    attrs.add("listwise_match_beam_search_size")
    attrs.add("ad_queue_type_list")
    attrs.add("enable_rb_refine_ratio_check")
    attrs.add("rb_refine_ratio_upper_bound")
    attrs.add("rb_refine_ratio_lower_bound")
    attrs.add("match_stage_rb_refine_bid_type_white_set")
    attrs.add("enable_adjust_ecpm_weight_rerank")
    attrs.add("ecpm_new_weight")
    attrs.add("enable_brand_safety")
    attrs.add("enable_ad_time_strategy")
    attrs.add("ts_boost_min_ad_gap")
    attrs.add("ts_boost_min_ad_threshold")
    attrs.add("ts_boost_min_ad_threshold_v2")
    attrs.add("ts_degrade_min_ad_gap")
    attrs.add("ts_degrade_min_ad_threshold")
    attrs.add("ts_degrade_min_ad_threshold_v2")
    # attrs.add("is_contains_live_ad")
    # attrs.add("enable_filter_no_live_ad_seq")
    attrs.add("photo_ids")
    attrs.add("ad_time_gap")
    # attrs.add("req_timegap")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    output_seq_candidate_attr = self._config.get("output_seq_candidate_attr")
    if output_seq_candidate_attr:
      attrs.add(output_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")
    output_seq_item_key_list_attr = self._config.get(
        "output_seq_item_key_list_attr")
    if output_seq_item_key_list_attr:
      attrs.add(output_seq_item_key_list_attr)
    else:
      attrs.add("seq_item_key_list")
    output_seq_item_key_size_list_attr = self._config.get(
        "output_seq_item_key_size_list_attr")
    if output_seq_item_key_size_list_attr:
      attrs.add(output_seq_item_key_size_list_attr)
    else:
      attrs.add("seq_item_key_size_list")
    output_seq_ad_gap_list_attr = self._config.get(
        "output_seq_ad_gap_list_attr")
    if output_seq_ad_gap_list_attr:
      attrs.add(output_seq_ad_gap_list_attr)
    else:
      attrs.add("seq_ad_gap_list")
    output_seq_ad_gap_size_list_attr = self._config.get(
        "output_seq_ad_gap_size_list_attr")
    if output_seq_ad_gap_size_list_attr:
      attrs.add(output_seq_ad_gap_size_list_attr)
    else:
      attrs.add("seq_ad_gap_size_list")

    output_seq_hash_id_list_attr = self._config.get(
        "output_seq_hash_id_list_attr")
    if output_seq_hash_id_list_attr:
      attrs.add(output_seq_hash_id_list_attr)
    else:
      attrs.add("seq_hash_id_list")

    attrs.add("list_match_fail_reason")
    attrs.add("user_ad_sensitivity")
    attrs.add("user_sensitivity_type")
    attrs.add("ad_rank_benefit_map")
    # attrs.add("ad_fill_type_map")
    # attrs.add("ad_category_map")
    # attrs.add("ad_resource_type_map")
    attrs.add("reco_raw_score_map")
    attrs.add("remix_ad_item_key_list")
    attrs.add("after_filter_retr_seq_num")
    attrs.add("seq_match_min_start_idx")
    attrs.add("seq_match_min_ad_gap")
    attrs.add("cur_min_ad_gap")
    attrs.add("seq_match_ad_num")
    attrs.add("before_filter_retr_seq_num")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("p_audit_mark_type")
    attrs.add("p_audit_mark_mark")
    attrs.add("cpm")
    attrs.add("ad_ctr")
    attrs.add("ad_calib_ctr")
    attrs.add("cpm")
    attrs.add("bid_type")
    attrs.add("ad_fill_type")
    attrs.add("ad_category")
    attrs.add("ad_resource_type")
    attrs.add("live_stream_id")
    attrs.add("brand_safety_inventory")
    attrs.add("rank_benefit")
    attrs.add("tag_id")
    attrs.add("remix_rank_type")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    # attrs.add("ad_pos_id")
    # attrs.add("filter_condition")
    # attrs.add("match_stage_refine_ad_pctr")
    # attrs.add("match_stage_refine_rank_benefit")
    attrs.add("match_stage_refine_ad_pctr")
    attrs.add("match_stage_refine_rank_benefit")
    # attrs.add("match_stage_original_rank_benefit")
    return attrs


# 已经没有调用了
class KsibMixRankListwiseRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_listwise_rank_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")
    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")

    # listwise 混排策略实验参数虽然在 C++ Process 直接使用，但是 DragonFly 有校验参数是否使用，所以这里将参数作为 Process 的输入

    attrs.add("min_ad_gap")
    attrs.add("enable_listwise_fixed_pos")
    attrs.add("enable_first_req_strategy")
    attrs.add("first_req_listwise_ad_score_alpha")
    attrs.add("first_req_next_req_loss_weight")
    attrs.add("first_req_gap_loss_weight")
    attrs.add("first_req_gap_loss_interval")
    attrs.add("first_req_raw_ad_score_alpha")
    attrs.add("enable_first_req_new_min_ad_gap")
    attrs.add("first_req_min_ad_gap")
    attrs.add("listwise_ad_norm_score_prefix")
    attrs.add("listwise_reco_norm_score_prefix")
    attrs.add("enable_listwise_reco_norm_score_pow_trans")
    attrs.add("enable_listwise_ad_norm_score_pow_trans")
    attrs.add("listwise_pow_trans_factor")
    attrs.add("listwise_ad_score_alpha")
    attrs.add("listwise_raw_ad_score_alpha")
    attrs.add("enable_raw_score_diff_alpha")
    attrs.add("high_listwise_raw_ad_score_alpha")
    attrs.add("mid_listwise_raw_ad_score_alpha")
    attrs.add("low_listwise_raw_ad_score_alpha")
    attrs.add("enable_mid_grade_nonlinear_boost")
    attrs.add("mid_grade_nonlinear_boost_factor")
    attrs.add("first_req_high_listwise_raw_ad_score_alpha")
    attrs.add("first_req_mid_listwise_raw_ad_score_alpha")
    attrs.add("first_req_low_listwise_raw_ad_score_alpha")
    attrs.add("high_rank_benefit_threshold")
    attrs.add("mid_rank_benefit_threshold")
    attrs.add("listwise_reco_norm_default_score")
    attrs.add("listwise_ad_norm_default_score")
    attrs.add("listwise_reco_pos_discount")
    attrs.add("listwise_ad_pos_discount")
    attrs.add("next_req_loss_weight")
    attrs.add("enable_calc_gap_loss_weight")
    attrs.add("gap_loss_weight")
    attrs.add("gap_loss_interval")
    attrs.add("gap_loss_factor")
    attrs.add("enable_calc_gap_boost_value")
    attrs.add("gap_boost_interval")
    attrs.add("gap_boost_value")
    attrs.add("enable_raw_ad_score_evaluate")
    attrs.add("listwise_default_rank_benefit")
    attrs.add("enable_raw_reco_score_evaluate")
    attrs.add("enable_transform_reco_raw_score")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("sensitive_user_sensitivity_threshold")
    attrs.add("insensitive_user_sensitivity_threshold")
    attrs.add("sensitive_user_alpha_boost_ratio")
    attrs.add("insensitive_user_alpha_boost_ratio")
    attrs.add("sensitive_gap_loss_interval_delta")
    attrs.add("insensitive_gap_loss_interval_delta")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("enable_user_select_policy")
    attrs.add("is_selected_user_type")
    attrs.add("select_user_boost_factor")
    attrs.add("hit_no_ad_user_force_insert")
    attrs.add("enable_amend_cpm_boost")
    attrs.add("mixrank_amend_cpm_boost_factor")
    attrs.add("mixrank_amend_bid_type_white_set")
    # attrs.add("enable_mixrank_auto_param")
    # attrs.add("mixrank_auto_param_exp_name")
    attrs.add("enable_sdk_fixed_pos_backup")
    attrs.add("enable_mixrank_session_strategy")
    attrs.add("session_strategy_factor")
    attrs.add("enable_expose_model")
    attrs.add("max_item_num_per_request")
    attrs.add("seq_expose_model_key")
    attrs.add("expose_score_list")
    attrs.add("enable_mixrank_listwise_model")
    attrs.add("listwise_seq_hash_id")
    attrs.add("listwise_model_reco_score")
    attrs.add("listwise_model_ad_score")
    attrs.add("listwise_model_reco_weight")
    attrs.add("listwise_model_ad_weight")
    attrs.add("mixrank_score_norm_ad_weight")
    attrs.add("mixrank_score_norm_ad_mean")
    attrs.add("mixrank_score_norm_ad_stddev")
    attrs.add("enable_xtr_boost")
    attrs.add("xtr_boost_alpha")
    attrs.add("xtr_boost_factor")
    attrs.add("xtr_boost_beta")
    attrs.add("user_sensitivity_type")
    attrs.add("no_ad_user_force_insert_round_mod")
    attrs.add("enable_filter_top3_ad")
    attrs.add("last_reco_boost_xtr")
    attrs.add("enable_pos_shift_no_zero")

    attrs.add("reco_raw_score_map")
    attrs.add("ad_rank_benefit_map")
    attrs.add("remix_ad_item_key_list")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    output_dynamic_ad_insert_list_attr = self._config.get(
        "output_dynamic_ad_insert_list_attr")
    if output_dynamic_ad_insert_list_attr:
      attrs.add(output_dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")

    attrs.add("list_rank_fail_reason")
    attrs.add("list_rank_fail_reason")
    attrs.add("auto_param_exp_id")
    attrs.add("auto_param_exp_group_name")
    attrs.add("param_group_key")
    attrs.add("high_rank_benefit_score_boost_factor")
    attrs.add("mid_rank_benefit_score_boost_factor")
    attrs.add("low_rank_benefit_score_boost_factor")
    attrs.add("list_rank_fail_reason")
    attrs.add("list_rank_fail_reason")
    attrs.add("seq_reco_value")
    attrs.add("seq_ad_value")
    attrs.add("seq_next_req_loss_value")
    attrs.add("seq_gap_loss_value")
    attrs.add("seq_gap_boost_value")
    attrs.add("seq_final_value")
    attrs.add("seq_business_priority")
    attrs.add("seq_next_req_min_idx")
    attrs.add("seq_rank_min_ad_gap")
    attrs.add("listwise_model_reco_value")
    attrs.add("listwise_model_ad_value")
    attrs.add("upstream_selected_items")
    attrs.add("list_rank_fail_reason")
    attrs.add("list_rank_fail_reason")
    attrs.add("list_rank_fail_reason")
    attrs.add("upstream_selected_items")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("rank_benefit")
    attrs.add("ad_calib_ctr")
    attrs.add("reco_boost_xtr")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()

    # C++ Process 填充的属性做为 output 输出，DragonFly log debug 使用
    # attrs.add("ad_pos_id")
    # attrs.add("filter_condition")
    attrs.add("ad_exposure_rate")
    attrs.add("refine_ad_pctr")
    attrs.add("refine_rank_benefit")

    return attrs


class KsibMixRankBuildAdResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_build_ad_response_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mix_rank_request"),
              f"{self.get_type_alias()} 的 mix_rank_request 是必配项")

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    dynamic_ad_insert_list_attr = self._config.get(
        "dynamic_ad_insert_list_attr")
    if dynamic_ad_insert_list_attr:
      attrs.add(dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")

    attrs.add("hit_mix_rank_dynamic_adpos")
    attrs.add("enable_dynamic_adpos")
    # attrs.add("req_timegap")
    # attrs.add("sess_play_time_sum")
    # attrs.add("sess_real_play_cnt")
    # attrs.add("sess_avg_play_time")
    # attrs.add("req_timegap_strategy_factor")
    # attrs.add("sess_engagement_factor")
    # attrs.add("session_strategy_factor")
    attrs.add("seq_reco_value")
    attrs.add("seq_ad_value")
    attrs.add("seq_next_req_loss_value")
    attrs.add("seq_gap_loss_value")
    attrs.add("seq_gap_boost_value")
    attrs.add("listwise_model_reco_value")
    # attrs.add("listwise_model_pctr1")
    # attrs.add("listwise_model_pctr2")
    attrs.add("seq_item_type_logs")
    attrs.add("last_page_reco_item_list_log")
    # attrs.add("seq_type_flag_final")
    attrs.add("live_insert_pos")
    attrs.add("inno_insert_pos")
    attrs.add("seq_photo_logs")
    attrs.add("listwise_model_ad_value")
    attrs.add("seq_final_value")
    # attrs.add("seq_final_business_priority")
    attrs.add("seq_next_req_min_idx")
    attrs.add("seq_match_min_ad_gap")
    attrs.add("seq_rank_min_ad_gap")
    attrs.add("list_match_fail_reason")
    attrs.add("list_rank_fail_reason")
    attrs.add("seq_match_ad_num")
    attrs.add("before_filter_retr_seq_num")
    attrs.add("after_filter_retr_seq_num")
    attrs.add("no_ad_user_force_insert_round_mod")
    attrs.add("hit_no_ad_user_force_insert")
    attrs.add("seq_match_min_start_idx")
    attrs.add("auto_param_exp_id")
    attrs.add("auto_param_exp_group_name")
    attrs.add("param_group_key")
    attrs.add("high_rank_benefit_score_boost_factor")
    attrs.add("mid_rank_benefit_score_boost_factor")
    attrs.add("low_rank_benefit_score_boost_factor")
    # attrs.add("is_new_user_v2")
    attrs.add("user_sensitivity_type")
    attrs.add("user_ad_sensitivity")
    attrs.add("is_back_user")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    # DragonFly 有校验参数是否使用，将参数作为 Process 的输入
    # attrs.add("ad_response_pb")
    # attrs.add("match_stage_refine_ad_pctr")
    # attrs.add("match_stage_refine_rank_benefit")
    attrs.add("ad_pos_id")
    attrs.add("ad_pos_id")
    attrs.add("filter_condition")
    attrs.add("ad_calib_ctr")
    # attrs.add("refine_ad_pctr")
    # attrs.add("refine_rank_benefit")
    attrs.add("match_stage_refine_ad_pctr")
    attrs.add("match_stage_refine_rank_benefit")
    # attrs.add("ad_exposure_rate")
    attrs.add("rank_pos_in_req")
    attrs.add("live_filter_reason")
    attrs.add("mix_ad_gap")
    attrs.add("soft_ad_gap")
    attrs.add("hard_ad_gap")

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

class KsibMixRankBuildAdResponseV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_build_ad_response_v2_enricher"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mix_rank_request"),
              f"{self.get_type_alias()} 的 mix_rank_request 是必配项")

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    dynamic_ad_insert_list_attr = self._config.get("dynamic_ad_insert_list_attr")
    if dynamic_ad_insert_list_attr:
      attrs.add(dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")

    for v in self._config["attrs"]:
      if "is_common" in v and v["is_common"]:
        attrs.add(v["name"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    for v in self._config["attrs"]:
      if "is_common" not in v or v["is_common"] == False:
        attrs.add(v["name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()


class KsibMixRankRecoEcologyBenefitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_reco_ecology_benefit_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")
    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")

    attrs.add("listwise_pow_trans_factor")
    attrs.add("listwise_reco_pos_discount")
    attrs.add("listwise_ad_pos_discount")
    attrs.add("listwise_pow_trans_factor")
    attrs.add("listwise_reco_norm_default_score")
    attrs.add("enable_listwise_reco_norm_score_pow_trans")
    attrs.add("listwise_reco_norm_score_prefix")
    attrs.add("enable_expose_model")
    # attrs.add("listwise_seq_hash_id")
    # attrs.add("listwise_model_reco_score")
    # attrs.add("listwise_model_ad_score")
    # attrs.add("enable_mixrank_listwise_model")
    # attrs.add("listwise_model_reco_weight")
    # attrs.add("listwise_model_ad_weight")
    attrs.add("listwise_reco_scores_weight")
    attrs.add("listwise_reco_scores_model_search_weight")
    # attrs.add("mixrank_enable_model_search")

    attrs.add("reco_raw_score_map")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    # attrs.add("listwise_model_reco_score")

    output_ecology_benefit_score = self._config.get(
        "output_ecology_benefit_score")
    if output_ecology_benefit_score:
      attrs.add(output_ecology_benefit_score)
    else:
      attrs.add("reco_ecology_benefit_score")
    return attrs


class KsibMixRankAdEcologyBenefitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_ecology_benefit_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("sensitive_user_sensitivity_threshold")
    attrs.add("insensitive_user_sensitivity_threshold")
    attrs.add("sensitive_user_alpha_boost_ratio")
    attrs.add("insensitive_user_alpha_boost_ratio")
    attrs.add("sensitive_gap_loss_interval_delta")
    attrs.add("insensitive_gap_loss_interval_delta")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("enable_user_select_policy")
    attrs.add("is_selected_user_type")
    attrs.add("select_user_boost_factor")
    attrs.add("hit_no_ad_user_force_insert")
    attrs.add("enable_amend_cpm_boost")
    attrs.add("mixrank_amend_cpm_boost_factor")
    attrs.add("mixrank_amend_bid_type_white_set")
    # attrs.add("enable_mixrank_auto_param")
    # attrs.add("mixrank_auto_param_exp_name")
    attrs.add("enable_sdk_fixed_pos_backup")
    # attrs.add("enable_mixrank_session_strategy")
    # attrs.add("session_strategy_factor")
    # attrs.add("enable_expose_model")
    # attrs.add("max_item_num_per_request")
    # attrs.add("seq_expose_model_key")
    # attrs.add("expose_score_list")
    # attrs.add("listwise_ad_score_transform_form")
    # attrs.add("enable_mixrank_listwise_model")
    # attrs.add("listwise_seq_hash_id")
    # attrs.add("listwise_model_reco_score")
    # attrs.add("listwise_model_ad_score")
    # attrs.add("listwise_model_reco_weight")
    # attrs.add("listwise_model_ad_weight")
    # attrs.add("mixrank_score_norm_ad_weight")
    # attrs.add("mixrank_score_norm_ad_mean")
    # attrs.add("mixrank_score_norm_ad_stddev")
    attrs.add("mixrank_score_sigmoid_ad_mean")
    attrs.add("mixrank_score_sigmoid_ad_sloap")
    attrs.add("enable_xtr_boost")
    attrs.add("xtr_boost_alpha")
    attrs.add("xtr_boost_factor")
    attrs.add("xtr_boost_beta")
    # attrs.add("listwise_ad_scores_model_search_weight")
    # attrs.add("mixrank_enable_model_search")
    attrs.add("min_ad_gap")
    attrs.add("cur_min_ad_gap")
    attrs.add("min_start_idx")
    attrs.add("enable_listwise_fixed_pos")
    attrs.add("init_pos_loss")
    attrs.add("enable_first_req_strategy")
    attrs.add("enable_first_view_use_ad_virtual_pos")
    attrs.add("enable_other_view_use_ad_virtual_pos")
    attrs.add("ad_rbfn_norm_val")
    attrs.add("rbfn_ad_score_upper_limit")
    attrs.add("rbfn_ad_score_maximum_val")
    attrs.add("enable_use_rbfn_ad_norm")
    attrs.add("rbfn_ad_norm_weight")
    attrs.add("rbfn_ad_norm_mean")
    attrs.add("first_req_listwise_ad_score_alpha")
    attrs.add("first_req_next_req_loss_weight")
    attrs.add("first_req_gap_loss_weight")
    attrs.add("first_req_gap_loss_interval")
    attrs.add("first_req_raw_ad_score_alpha")
    attrs.add("enable_first_req_new_min_ad_gap")
    attrs.add("first_req_min_ad_gap")
    attrs.add("listwise_ad_norm_score_prefix")
    attrs.add("listwise_reco_norm_score_prefix")
    attrs.add("enable_listwise_reco_norm_score_pow_trans")
    attrs.add("enable_listwise_ad_norm_score_pow_trans")
    attrs.add("listwise_pow_trans_factor")
    attrs.add("listwise_ad_score_alpha")
    attrs.add("listwise_raw_ad_score_alpha")
    attrs.add("enable_raw_score_diff_alpha")
    attrs.add("high_listwise_raw_ad_score_alpha")
    attrs.add("mid_listwise_raw_ad_score_alpha")
    attrs.add("low_listwise_raw_ad_score_alpha")
    attrs.add("enable_mid_grade_nonlinear_boost")
    attrs.add("mid_grade_nonlinear_boost_factor")
    attrs.add("first_req_high_listwise_raw_ad_score_alpha")
    attrs.add("first_req_mid_listwise_raw_ad_score_alpha")
    attrs.add("first_req_low_listwise_raw_ad_score_alpha")
    attrs.add("high_rank_benefit_threshold")
    attrs.add("mid_rank_benefit_threshold")
    attrs.add("listwise_reco_norm_default_score")
    attrs.add("listwise_ad_norm_default_score")
    attrs.add("listwise_reco_pos_discount")
    attrs.add("listwise_ad_pos_discount")
    attrs.add("next_req_loss_weight")
    attrs.add("enable_calc_gap_loss_weight")
    attrs.add("gap_loss_weight")
    attrs.add("gap_loss_interval")
    attrs.add("gap_loss_factor")
    attrs.add("enable_calc_gap_boost_value")
    attrs.add("gap_boost_interval")
    attrs.add("gap_boost_value")
    attrs.add("enable_raw_ad_score_evaluate")
    attrs.add("listwise_default_rank_benefit")
    attrs.add("enable_raw_reco_score_evaluate")
    attrs.add("enable_transform_reco_raw_score")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("user_sensitivity_type")
    attrs.add("last_reco_boost_xtr")


    attrs.add("reco_raw_score_map")
    attrs.add("ad_rank_benefit_map")
    attrs.add("remix_ad_item_key_list")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("listwise_prepare_condition")
    attrs.add("auto_param_exp_id")
    attrs.add("auto_param_exp_group_name")
    attrs.add("param_group_key")
    attrs.add("high_rank_benefit_score_boost_factor")
    attrs.add("mid_rank_benefit_score_boost_factor")
    attrs.add("low_rank_benefit_score_boost_factor")
    attrs.add("list_rank_fail_reason")
    attrs.add("upstream_selected_items")

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("ad_calib_ctr")
    attrs.add("reco_boost_xtr")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("new_raw_ad_score_alpha")
    # attrs.add("listwise_model_ad_score")
    attrs.add("rank_min_ad_gap")
    if "ad_score_list_attr" in self._config:
      attrs.add(self._config["ad_score_list_attr"])
    else:
      attrs.add("ad_score_list")
    if "ad_pos_list_attr" in self._config:
      attrs.add(self._config["ad_pos_list_attr"])
    else:
      attrs.add("ad_pos_list")

    output_ecology_benefit_score = self._config.get(
        "output_ecology_benefit_score")
    if output_ecology_benefit_score:
      attrs.add(output_ecology_benefit_score)
    else:
      attrs.add("ad_ecology_benefit_score")
    return attrs

class KsibMixRankAdEcologyLossEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_ecology_loss_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("enable_calc_gap_loss_weight")
    attrs.add("gap_loss_interval")
    attrs.add("enable_first_req_strategy")
    attrs.add("min_ad_gap")
    attrs.add("gap_loss_weight")
    # attrs.add("listwise_adgap_loss_model_search_weight")
    # attrs.add("mixrank_enable_model_search")
    attrs.add("first_req_gap_loss_interval")
    attrs.add("first_req_gap_loss_weight")
    attrs.add("enable_first_req_new_min_ad_gap")
    attrs.add("first_req_min_ad_gap")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("user_sensitivity_type")
    attrs.add("sensitive_gap_loss_interval_delta")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("insensitive_gap_loss_interval_delta")
    attrs.add("gap_loss_factor")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")
    
    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("listwise_prepare_condition")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_ecology_loss_score = self._config.get(
        "output_ecology_loss_score")
    if output_ecology_loss_score:
      attrs.add(output_ecology_loss_score)
    else:
      attrs.add("ad_ecology_loss_score")
    return attrs

class KsibMixRankAdNextRequestLossEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_next_request_loss_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    # attrs.add("listwise_ad_next_req_loss_model_search_weight")
    # attrs.add("mixrank_enable_model_search")
    attrs.add("next_req_loss_weight")
    attrs.add("enable_first_req_strategy")
    attrs.add("first_req_next_req_loss_weight")
    attrs.add("min_ad_gap")
    attrs.add("enable_first_req_new_min_ad_gap")
    attrs.add("first_req_min_ad_gap")
    attrs.add("enable_user_group_diff_mixrank_strategy")
    attrs.add("user_sensitivity_type")
    attrs.add("sensitive_user_min_gap_delta")
    attrs.add("listwise_ad_pos_discount")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("listwise_prepare_condition")
    attrs.add("next_req_loss_weight")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("seq_next_req_min_idx")
    output_next_request_loss_score = self._config.get(
        "output_next_request_loss_score")
    if output_next_request_loss_score:
      attrs.add(output_next_request_loss_score)
    else:
      attrs.add("ad_next_request_loss_score")
    return attrs


class KsibMixRankAdGapBoostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_gap_boost_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("enable_calc_gap_boost_value")
    attrs.add("gap_boost_interval")
    attrs.add("gap_boost_value")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")
    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("listwise_prepare_condition")

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_boost_score = self._config.get(
        "output_boost_score")
    if output_boost_score:
      attrs.add(output_boost_score)
    else:
      attrs.add("ad_boost_score")
    return attrs

class KsibMixRankAdSeqBusinessPriorityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_seq_business_priority_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("hit_no_ad_user_force_insert")
    attrs.add("enable_ad_force_tag_force_insert")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")
    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("mixrank_ad_force_tag")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_business_priority = self._config.get(
        "output_business_priority")
    if output_business_priority:
      attrs.add(output_business_priority)
    else:
      attrs.add("ad_business_priority")
    return attrs
    
class KsibMixRankAdFixedPosBackupEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_fixed_pos_backup_enricher"
 
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    # attrs.add("listwise_prepare_condition")
    attrs.add("enable_pos_shift_no_zero")
    attrs.add("enable_sdk_fixed_pos_backup")

    attrs.add("remix_ad_item_key_list")

    ad_request_common_attr = self._config.get("ad_request_common_attr")
    if ad_request_common_attr:
      attrs.add(ad_request_common_attr)
    else:
      attrs.add("ad_request_pb")

    input_seq_candidate_attr = self._config.get("input_seq_candidate_attr")
    if input_seq_candidate_attr:
      attrs.add(input_seq_candidate_attr)
    else:
      attrs.add("seq_candidate_result")

    input_dynamic_ad_insert_list_attr = self._config.get(
        "input_dynamic_ad_insert_list_attr")
    if input_dynamic_ad_insert_list_attr:
      attrs.add(input_dynamic_ad_insert_list_attr)
    else:
      attrs.add("dynamic_ad_insert_list")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("upstream_selected_items")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    # output_dynamic_ad_insert_list_att = self._config.get(
    #     "output_dynamic_ad_insert_list_attr")
    # if output_dynamic_ad_insert_list_att:
    #   attrs.add(output_dynamic_ad_insert_list_att)
    # else:
    #   attrs.add("dynamic_ad_insert_list")
    return attrs