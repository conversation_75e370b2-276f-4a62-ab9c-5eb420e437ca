#!/usr/bin/env python3
"""
filename: ksib_mix_rank_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for mix rank 
author: <EMAIL>
date: 2022-03-25 18:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class KsibMixRankAdRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_mix_rank_ad_retriever"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mix_rank_request"), f"{self.get_type_alias()} 的 mix_rank_request 是必配项")

  @strict_types
  def depend_on_items(self) -> bool:
    return True 

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mix_rank_request"])
    attrs.add("hit_mix_rank_dynamic_adpos")

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("tag_id")

    output_ad_response_item_attr = self._config.get("output_ad_response_item_attr")
    if output_ad_response_item_attr:
      attrs.add(output_ad_response_item_attr)
    else:
      attrs.add("ad_response_pb")

    output_ad_result_item_attr = self._config.get("output_ad_result_item_attr")
    if output_ad_result_item_attr:
      attrs.add(output_ad_result_item_attr)
    else:
      attrs.add("ad_result_pb")

    output_rtb_ad_info_attr = self._config.get("output_rtb_ad_info_attr")
    if output_rtb_ad_info_attr:
      attrs.add(output_rtb_ad_info_attr)
    else:
      attrs.add("rtb_ad_info_pb")

    output_intl_source_type_attr = self._config.get("output_intl_source_type_attr")
    if output_intl_source_type_attr:
      attrs.add(output_intl_source_type_attr)
    else:
      attrs.add("ad_intl_source_type")

    return attrs 
