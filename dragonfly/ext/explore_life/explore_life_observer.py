#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import LeafObserver
from ...common_leaf_util import strict_types

class ExploreLifeModelSlotObserver(LeafObserver):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_life_model_slot_observer"

    @strict_types
    def depend_on_items(self) -> bool:
        return bool(self.input_item_attrs)

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("item_slots_attr"))
        return attrs 

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("common_slots_attr"))
        attrs.add(self._config.get("train_item_slots_attr"))
        attrs.add(self._config.get("train_common_slots_attr"))
        return attrs