#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_enricher.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class ExploreLifeDiversityUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_diversity_update_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_info_ptr_attr", "pid_embedding_common_attr", "history_feed_back_version"]:
      attrs.add(self._config.get(name))
    for name in ["dpp_diversity_mgs_topk", "enable_dpp_diversity_mgs_ratio", "history_matrix_set_mode",
                  "dpp_diversity_mgs_ratio", "diversity_history_size", "topk_select_mod",
                  "dim_size", "expected_score_cand_size", "max_interval_second", "min_duration_threshold",
                  "max_playtime_threshold", "enable_use_weight", "weight_version", "ratio_scale", "ratio_pow_weight",
                  "enable_only_user_explore_hate", "enable_only_user_explore_cover_hate", "min_playtime_threshold",
                  "enable_use_explore_history"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_attr"))
    return attrs

class ExploreLifeEmbeddingCandidatesAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_embedding_candidates_attr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_not_click", "enable_play_stat", "enable_hate", "enable_like", "enable_follow",
                "enable_explore_not_click", "enable_fix_low_hit_rate", "session_history_max_size", "enable_source_photo",
                "enable_use_explore"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["user_info_ptr_attr", "source_pid_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_attr"))
    return attrs

class ExploreLifeCustomEmbeddingScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_custom_embedding_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["dim_size", "calc_type", "not_click_weight", "short_view_weight",
                 "hate_weight", "like_weight", "follow_weight", "short_view_threshold",
                 "pos_action_limit_hour", "play_stat_limit_hour", "not_click_limit_hour",
                 "extra_not_click_weight", "enable_fountain_version", "extra_not_click_limit_hour",
                 "enable_fix_low_hit_rate", "enable_fountain_play_stat", "enable_judge_next_photo_stat",
                 "enable_explore_truncate_topk", "not_click_limit_topk", "play_stat_limit_topk",
                 "enable_explore_hetu_topk", "not_hetu_limit_topk", "not_pid_limit_topk",
                 "enable_extra_no_click_stat", "enable_avg_pooling", "source_photo_id",
                 "hate_limit_hour", "hate_weight", "hate_max_num", "play_stat_limit_explore",
                 "enable_not_click"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("embedding_list_attr"))
    attrs.add(self._config.get("source_pids_list_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr"))
    return attrs

class ExploreLifeReverseListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_reverse_list_attr"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("common_attr", ""), str), "common_attr 需为 str 类型")
    check_arg(isinstance(self._config.get("item_attr", ""), str), "item_attr 需为 str 类型")
    check_arg(isinstance(self._config.get("common_attrs", []), list), "common_attrs需为list类型")
    check_arg(isinstance(self._config.get("item_attrs", []), list), "item_attrs需为list类型")
    check_arg("item_attr" in self._config or "common_attr" in self._config
              or "common_attrs" in self._config or "item_attrs" in self._config, "common_attr、item_attr、common_attrs、item_attrs 至少有一项")

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self._config.get("item_attr")) or bool(self._config.get("item_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    if self._config.get("common_attrs"):
      attrs.update(self._config["common_attrs"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("common_attr"):
      attrs.add(self._config["common_attr"])
    if self._config.get("common_attrs"):
      attrs.update(self._config["common_attrs"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    if self._config.get("item_attrs"):
      attrs.update(self._config["item_attrs"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("item_attr"):
      attrs.add(self._config["item_attr"])
    if self._config.get("item_attrs"):
      attrs.update(self._config["item_attrs"])
    return attrs

class ExploreLifeInterestHetuEnricher(LeafEnricher):
  """
  ExploreLifeInterestHetuEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_interest_hetu_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("short_interest_reward_weights_attr"))
    attrs.add(self._config.get("get_short_interest_attr"))
    attrs.add(self._config.get("long_interest_reward_weights_attr"))
    attrs.add(self._config.get("explore_interest_reward_weights_attr"))
    for name in ["enable_top_sv_hetu2", "enable_stat_top_sv_only_fountain", "top_sv_stat_max_show",
                 "enable_top_sv_stat_use_rate", "top_sv_stat_default_svtr", "top_sv_stat_base_show",
                 "enable_stat_short_interest", "enable_stat_long_interest", "enable_stat_short_interest_only_explore_fountain", "short_interest_max_hours",
                 "enable_interest_use_hetu1", "play_time_slope", "play_time_max", "enable_stat_long_interest_only_explore_fountain",
                 "long_interest_max_days", "long_interest_min_days", "long_interest_min_play_time", "short_interest_reward_lower_bound",
                 "short_interest_num", "long_interest_reward_lower_bound", "long_interest_num", "enable_interest_reward_use_rate",
                 "enable_stat_explore_interest", "explore_interest_num", "explore_interest_reward_lower_bound",
                 ]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_user_top_sv_hetu_attr"))
    attrs.add(self._config.get("save_short_interest_attr"))
    attrs.add(self._config.get("save_long_interest_attr"))
    attrs.add(self._config.get("save_explore_interest_attr"))
    return attrs

class ExploreLifeClusterRuleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_cluster_rule_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_user_profile_top_hetu_level_one_cluster", "enable_user_profile_top_hetu_level_two_cluster",
        "user_profile_tag_score_limit", "user_profile_limit_num", "enable_frequency_duration_cluster",
        "use_extra_page", "enable_colossus_top_hetu_one_cluster", "enable_colossus_top_hetu_two_cluster",
        "colossus_top_hetu_level_two_attr", "colossus_top_hetu_level_one_attr", "variant_queue_num",
        "enable_use_real_show_list", "enable_use_click_list", "enable_use_like_list", "enable_use_follow_list",
        "enable_use_forward_list", "enable_use_collect_list", "enable_use_comment_list", "enable_user_video_play_stats",
        "enable_use_download_list", "enable_use_enter_profile_list", "download_weight", "enter_profile_weight",
        "comment_weight", "collect_weight", "video_play_weight", "min_effective_play_length",
        "real_show_weight", "click_weight", "like_weight", "follow_weight", "forward_weight",
        "enable_use_fountain_real_show_list", "enable_use_fountain_click_list", "enable_use_fountain_like_list",
        "enable_use_fountain_follow_list", "enable_use_fountain_forward_list", "fountain_real_show_weight",
        "fountain_click_weight", "fountain_like_weight", "fountain_follow_weight", "fountain_forward_weight",
        "enable_colossus_cluster", "enable_white_author_bucket", "enable_mc_explore_cluster", "hetu_level_two_attr",
        "age_cluster_attr", "enable_use_photo_age_cluster", "mc_age_gap_str", "enable_mc_interact_cluster",
        "enable_user_profile_top_hetu_level_three_cluster", "enable_expired_time_on_action_list",
        "expired_gap_second", "enable_mc_use_realshow_no_click", "enable_uninterested_hetu_level_one_cluster",
        "enable_uninterested_hetu_level_two_cluster", "enable_uninterested_hetu_level_three_cluster", "uninterested_tag_score_limit",
        "uninterested_limit_num", "real_show_no_click_weight", "uninterested_expired_gap_second", "enable_mc_use_xhs_tag",
        "enable_rough_default_cluster","enable_mc_follow_author_cluster", "enable_mc_explore_cluster_mix", "enable_get_default_hetu_level_one_cluster",
        "enable_get_default_hetu_level_three_cluster", "enable_get_default_hetu_level_two_cluster", "enable_set_bucket_limit_num_by_ratio",
        "mc_realtime_bucket_limit_num_ratio", "enable_mc_empctr_cluster", "enable_shortterm_interest_cluster_opt",
        "enable_ignore_profile_candidate_limit_cut", "enable_mc_follow_author_cluster_first",
        "enable_mc_cluster_862_cluster", "enable_mc_cluster_862_one_cluster", "mc_user_interest_cluster_862_count",
        "enable_short_interest_cluster", "enable_default_hetu_cluster", "default_hetu_use_level_one",
        "enable_unbias_interest_cluster", "enable_mmu_interest_hetu_l2_cluster", "enable_mmu_interest_hetu_l1_cluster",
        "mmu_interest_hetu_l1_score_thr", "mmu_interest_hetu_l2_score_thr", "mmu_interest_hetu_l1_cluster_max_num",
        "mmu_interest_hetu_l2_cluster_max_num"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    for name in ["input_colossus_attr_one", "input_colossus_attr_two", "input_colossus_attr_three", "input_colossus_attr_explore",
        "input_colossus_attr_interact", "input_xhs_hetu_tags_attr", "shortterm_hetu_attr", "input_user_interest_cluster_862_attr",
        "input_unbias_interest_attr", "input_mmu_interest_hetu_l2_ids_attr", "input_mmu_interest_hetu_l2_scores_attr",
        "input_mmu_interest_hetu_l1_ids_attr", "input_mmu_interest_hetu_l1_scores_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["hetu_tag_level_info__hetu_level_one", "hetu_tag_level_info__hetu_level_two", "hetu_tag_level_info__hetu_level_three",
                 "duration_ms", "is_picture", "live_photo_info__is_living"])
    for name in ["white_author_attr", "hetu_level_one_attr", "hetu_level_two_attr", "hetu_level_three_attr", "hetu_level_four_attr",
      "is_follow_author_attr", "empctr_cluster_flag_attr", "cluster_862_attr", "unbias_interest_hetu_attr"]:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreLifeUserPositiveHetuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_user_positive_hetu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_use_click_list", "enable_use_like_list", "enable_use_follow_list",
        "enable_use_forward_list", "enable_use_collect_list", "enable_use_comment_list", "enable_user_video_play_stats",
        "comment_weight", "collect_weight", "click_weight", "like_weight", "follow_weight", "forward_weight",
        "video_play_weight", "min_effective_play_length", "action_days_thresh", "action_minutes_thresh", "action_time_mode",
        "save_hetu_count"
        ]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_user_positive_hetu2_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreLifeColossusAuthorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_colossus_author_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_only_life_stat", "time_window_day", "like_weight", "follow_weight",
                 "comment_weight", "forward_weight", "entered_profile_weight", "effective_play_weight",
                 "effective_play_time_second_thresh", "author_score_thresh", "save_author_count"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("colossus_resp_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_interest_authors_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreLifeColossusClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_colossus_cluster_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "finish_rate_threshold", "colossus_day_upper", "colossus_profile_recent_hours",
      "colossus_click_weight", "colossus_play_time_weight", "tag_limit_str", "max_show_ratio_str",
      "enable_mc_explore_cluster", "mc_explore_cluster_limit", "mc_explore_cluster_score_limit",
      "enable_mc_interact_cluster", "enable_mc_explore_cluster_v2",
      "mc_explore_cluster_recent_click_count_limit", "mc_explore_cluster_click_count_limit",
      "mc_explore_cluster_click_time_limit", "enable_mc_explore_cluster_target",
      "mc_explore_cluster_target_count_limit", "mc_explore_cluster_recent_show_count_limit",
      "mc_explore_cluster_recent_click_top_ratio", "mc_explore_cluster_recent_show_top_ratio",
      "enable_longterm_interest_cluster_opt", "enable_longterm_interest_calc_new", "skip_recent_interest_calc",
      "like_weight", "follow_weight", "comment_weight", "forward_weight", "enter_profile_weight",
      "finish_play_weight", "play_time_weight", "play_time_second_threshold", "time_decay_coeff_min"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("colossus_v2_attr_name"))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("high_quality_tags_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_colossus_attr_one"))
    attrs.add(self._config.get("export_colossus_attr_two"))
    attrs.add(self._config.get("export_colossus_attr_three"))
    for name in ["export_colossus_attr_explore", "export_colossus_attr_interact"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

class ExploreLifeBrowseSetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_browse_set_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["realshow_list_size", "time_gap_s"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_realshow_list_attr"))
    attrs.add(self._config.get("output_click_list_attr"))
    attrs.add(self._config.get("output_realshow_timestamp_list_attr"))
    return attrs

class ExploreLifeUninterestTagExitEnricher(LeafEnricher):
  """
  ExploreLifeUninterestTagExitEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_uninterest_tag_exit_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      attrs.add(prev_item_from_attr)
    prev_item_timestamp_from_attr = self._config.get("prev_item_from_attr_timestamp")
    if prev_item_timestamp_from_attr:
      attrs.add(prev_item_timestamp_from_attr)
    prev_click_item_from_attr = self._config.get("prev_click_item_from_attr")
    if prev_click_item_from_attr:
      attrs.add(prev_click_item_from_attr)
    for name in ["realshow_num_threshold", "time_window", "calculate_mode",
        "discount_coef", "realshow_unclick_num"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    attrs.add(self._config.get("cluster_id_attr"))
    attrs.add(self._config.get("input_pctr_attr"))
    prev_attrs.add(self._config.get("cluster_id_attr"))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_pctr_attr")
    if attr:
      attrs.add(attr)
    return attrs

class ExploreLifeUninterestHetuExitEnricher(LeafEnricher):
  """
  ExploreLifeUninterestHetuExitEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_uninterest_hetu_exit_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["realshow_num_threshold", "time_gap_s", "calculate_mode",
                 "discount_coef", "realshow_unclick_num_thr"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_tag_attr"))
    attrs.add(self._config.get("input_pctr_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_pctr_attr")
    if attr:
      attrs.add(attr)
    return attrs

class ExploreLifeUserEmpXtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_life_user_emp_xtr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      for name in ["user_info_ptr_attr"]:
          if name in self._config:
              attrs.add(self._config.get(name))
      attrs.add(self._config.get('colossus_resp_attr'))
      for key in ["enable_colossus_item_limit", "max_colossus_item_num",
                  "user_colossus_min_sec_ago","user_colossus_max_sec_ago",
                  "max_click_cnt", "use_fountain_count_threshold",
                  "init_ctr_alpha", "init_ctr_beta"]:
          if key in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      for name in ["save_user_emp_ltr", "save_user_emp_wtr",
                   "save_user_emp_ftr", "save_user_emp_htr",
                   "save_user_emp_cmtr", "save_user_emp_eptr",
                   "save_user_emp_svtr", "save_user_emp_evtr",
                   "save_user_emp_lvtr", "save_user_emp_fintr",
                   "save_user_emp_watch_time", "save_user_emp_finish_rate",
                   "save_user_emp_watch_time_long_video", "save_user_emp_finish_rate_long_video",
                   "save_user_emp_fountain_time_ratio", "save_user_emp_ctr",
                   "save_user_emp_actiononce_ratio", "save_user_click_count"
                   ]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

class ExploreLifePicCalcClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_pic_calc_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_multi_hit_independent_bucket")))
    cluster_configs = self._config.get("clusters", [])
    for cluster_config in cluster_configs:
      attrs.update(self.extract_dynamic_params(cluster_config.get("enable")))
      attrs.update(self.extract_dynamic_params(cluster_config.get("priority_num")))

      if cluster_config.get("name") == "long_term":
        attrs.add(cluster_config.get("colossus_hetu_l1_tags_attr"))
        attrs.add(cluster_config.get("tag_score_min"))
        attrs.add(cluster_config.get("enable_user_longterm_hetu_distr"))
        attrs.add(cluster_config.get("user_longterm_hetu_distr_attr"))
        attrs.add(cluster_config.get("enable_longterm_default_hetu"))
      elif cluster_config.get("name") == "privilege_tag":
        attrs.add(cluster_config.get("privilege_tags_attr"))
      elif cluster_config.get("name") == "interest_explore":
        attrs.add(cluster_config.get("interest_explore_hetu_list_attr"))
      elif cluster_config.get("name") == "short_term":
        attrs.add(cluster_config.get("only_pic"))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    cluster_configs = self._config.get("clusters", [])
    for cluster_config in cluster_configs:
      if cluster_config.get("name", "") == "short_term":
        attrs.add(cluster_config.get("hetu_level_attr"))

      elif cluster_config.get("name", "") == "long_term":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "privilege_tag":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "interest_explore":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "long_caption":
        attrs.add(cluster_config.get("caption_length_attr_name"))
        attrs.add(cluster_config.get("is_xhs_type_photo_attr_name"))

      elif cluster_config.get("name", "") == "follow_author":
        attrs.add(cluster_config.get("is_follow_author_attr_name"))

      elif cluster_config.get("name", "") == "long_pic_and_pic_set":
        attrs.add(cluster_config.get("upload_type_attr"))
        attrs.add(cluster_config.get("picture_type_attr"))

      elif cluster_config.get("name", "") == "pic_cnt":
        attrs.add(cluster_config.get("picture_count_attr"))

      elif cluster_config.get("name", "") == "pic_default":
        attrs.add(cluster_config.get("upload_type_attr"))
        attrs.add(cluster_config.get("picture_type_attr"))
        attrs.add(cluster_config.get("picture_count_attr"))
      elif cluster_config.get("name", "") == "high_value_pic":
        attrs.add(cluster_config.get("high_value_pic_flag_attr"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class ExploreLifeClusterVariantSortV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_cluster_variant_sort_v2_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["global_cut_ratio", "min_survival", "enable_proportional",
                 "size_limit", "use_power_calc", "use_reciprocal", "use_reciprocal_v2",
                 "action_day",
                 "enable_dynamic_weight_by_user_degree",
                 "enable_variant_cut_ratio", "variant_cut_ratio","rank_smooth", "use_rank_as_score",
                 "realshow_no_click_cluster_ratio_adjust", "realshow_no_click_cluster_ratio",
                 "interest_explore_cluster_ratio_adjust", "interest_explore_cluster_ratio", "explore_pic_user_power_calc_v2", "use_reciprocal_new_value_seq_fusion",
                 "two_times_sort", "first_time_cut_ratio", "use_fractile_in_ensemble_sort", "fractile_in_ensemble_sort_type",
                 "enable_dynamic_interest_ratio", "keep_num", "skip_fillback_result", "interest_weight_str"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_power_weight_attr", ""))
      attrs.add(cfg.get("raw_score_normalize_alpha_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("avg_xtr", ""))
      attrs.add(cfg.get("min_ratio", ""))
      attrs.add(cfg.get("max_ratio", ""))
      attrs.add(cfg.get("dynamic_weight", ""))
      attrs.add(cfg.get("user_xtr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_power_weight_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("explore_pic_es_score_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_common_attr", "save_cluster_cnt_common_attr", "save_cluster_cnt_after_truncaton_common_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_name", ""))
    attrs.add(self._config.get("hetu_level_one_name", ""))
    attrs.add(self._config.get("cluster_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs
