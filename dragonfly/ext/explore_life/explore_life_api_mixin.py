#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_api_mixin.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .explore_life_arranger import *
from .explore_life_enricher import *
from .explore_life_retriever import *
from .explore_life_observer import *

class ExploreLifeApiMixin(CommonLeafBaseMixin):
  """
  双列发现页内外流 Processor API 接口的 Mixin 实现
  """
  def explore_life_retrieval_filter(self, **kwargs):
    """
    ExploreLifeRetrievalFilterArranger
    """
    self._add_processor(ExploreLifeRetrievalFilterArranger(kwargs))
    return self

  def explore_life_calc_ensemble_score(self, **kwargs):
    """
    ExploreLifeEnsembleScoreCalcArranger
    """
    self._add_processor(ExploreLifeEnsembleScoreCalcArranger(kwargs))
    return self
  
  def explore_life_voyage_calc_ensemble_score(self, **kwargs):
    """
    ExploreLifeVoyageEnsembleScoreCalcArranger
    """
    self._add_processor(ExploreLifeVoyageEnsembleScoreCalcArranger(kwargs))
    return self

  def explore_life_diversity_update_enricher(self, **kwargs):
    """
    ExploreLifeDiversityUpdateEnricher
    """
    self._add_processor(ExploreLifeDiversityUpdateEnricher(kwargs))
    return self

  def explore_life_embedding_candidates_attr_enricher(self, **kwargs):
    """
    ExploreLifeEmbeddingCandidatesAttrEnricher
    """
    self._add_processor(ExploreLifeEmbeddingCandidatesAttrEnricher(kwargs))
    return self

  def explore_life_custom_embedding_score_enricher(self, **kwargs):
    """
    ExploreLifeCustomEmbeddingScoreEnricher
    """
    self._add_processor(ExploreLifeCustomEmbeddingScoreEnricher(kwargs))
    return self
  
  def explore_life_interest_hetu_enricher(self, **kwargs):
    """
    ExploreLifeInterestHetuEnricher
    """
    self._add_processor(ExploreLifeInterestHetuEnricher(kwargs))
    return self

  def explore_life_reverse_list_attr(self, **kwargs):
    """
    ExploreLifeReverseListAttrEnricher
    ------
    对 list 类型的 CommonAttr/ ItemAttr 进行 reverse

    参数配置
    ------
    `common_attr`: [string] 指定被 reverse 的 CommonAttr

    `item_attr`: [string] 指定被 reverse 的 ItemAttr

    `common_attrs`: [list] 指定被 reverse 的 CommonAttr List, 自动去重 [recommended]

    `item_attrs`: [list] 指定被 reverse 的 ItemAttr List, 自动去重 [recommended]

    注意：`common_attr` `item_attr` `common_attrs` `item_attrs`至少有一项或两项
          common_attrs/common_attr、item_attrs/item_attr同时出现时取并集(去重)

    调用示例
    ------
    ``` python
    .explore_life_reverse_list_attr(common_attrs=["click_item_id_list", "click_item_category_list"])
    ```
    """
    self._add_processor(ExploreLifeReverseListAttrEnricher(kwargs))
    return self
  
  def explore_life_cluster_rule_enricher(self, **kwargs):
    """
    ExploreLifeClusterRuleEnricher
    """
    self._add_processor(ExploreLifeClusterRuleEnricher(kwargs))
    return self

  def explore_life_user_positive_hetu_enricher(self, **kwargs):
    """
    ExploreLifeUserPositiveHetuEnricher
    """
    self._add_processor(ExploreLifeUserPositiveHetuEnricher(kwargs))
    return self

  def explore_life_colossus_author_enricher(self, **kwargs):
    """
    ExploreLifeColossusAuthorEnricher
    """
    self._add_processor(ExploreLifeColossusAuthorEnricher(kwargs))
    return self

  def explore_life_colossus_cluster_enricher(self, **kwargs):
    """
    ExploreLifeColossusClusterEnricher
    """
    self._add_processor(ExploreLifeColossusClusterEnricher(kwargs))
    return self

  def explore_life_browse_set_enricher(self, **kwargs):
    """
    ExploreLifeBrowseSetEnricher
    """
    self._add_processor(ExploreLifeBrowseSetEnricher(kwargs))
    return self

  def explore_life_uninterest_tag_exit_enricher(self, **kwargs):
    """
    ExploreLifeUninterestTagExitEnricher
    """
    self._add_processor(ExploreLifeUninterestTagExitEnricher(kwargs))
    return self

  def explore_life_uninterest_hetu_exit_enricher(self, **kwargs):
    """
    ExploreLifeUninterestHetuExitEnricher
    """
    self._add_processor(ExploreLifeUninterestHetuExitEnricher(kwargs))
    return self

  def explore_life_user_emp_xtr_enricher(self, **kwargs):
    """
    ExploreLifeUserEmpXtrEnricher
    """
    self._add_processor(ExploreLifeUserEmpXtrEnricher(kwargs))
    return self
  
  def explore_life_pic_calc_cluster(self, **kwargs):
    """
    ExploreLifePicCalcClusterEnricher
    """
    self._add_processor(ExploreLifePicCalcClusterEnricher(kwargs))
    return self

  def explore_life_cluster_variant_sort_v2_enrich(self, **kwargs):
    """
    ExploreLifeClusterVariantSortV2Enricher
    ------

    参数配置
    ------
    """
    self._add_processor(ExploreLifeClusterVariantSortV2Enricher(kwargs))
    return self

  # ----------------------- Observer Processors ------------------------------------ #
  def explore_life_model_slot_observer(self, **kwargs):
    """
    ExploreLifeModelSlotObserver
    """
    self._add_processor(ExploreLifeModelSlotObserver(kwargs))
    return self
