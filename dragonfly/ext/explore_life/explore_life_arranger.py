#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_arranger.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class ExploreLifeR<PERSON>rie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)
    self.__filter_map = {}
    for filter in self._config.get("filters"):
      self.__filter_map[filter.get("name")] = filter

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_retrieval_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr_set = set([
        "topk_audit_white_tag_list_attr", "topk_audit_black_tag_list_attr", "high_hot_audit_white_tag_list_attr", "high_hot_audit_black_tag_list_attr",
        "impression_audit_white_tag_list_attr", "impression_audit_black_tag_list_attr", "browse_screen_aid_list_attr", "follow_author_ignore_exptag_list_attr",
        "follow_author_filter_timegap_attr", "valuable_author_type_list_attr", "filter_content_type_list_attr", "filter_content_type_list_for_pic_attr", "magic_kk_id_list_attr",
        "skip_clicked_hate_item_filter_attr", "enable_short_hate_l5_filter_attr", "hetu_tag_l5_minutes_cut_attr", "filtered_hetu_tag_list_attr",
        "enable_long_hate_filter_attr", "hetu_tag_long_term_minutes_cut_attr", "hetu_l2_long_filter_threshold_attr", "hetu_otherl_long_filter_threshold_attr",
        "filter_pic_wallpaper_hetu_tag", "filter_long_pic_picture_type", "filter_long_pic_upload_type", "enable_pic_wallpaper_filter_caption_keep_attr", "pic_wallpaper_caption_keep_thresh_attr",
        "lower_cover_mmu_map_strs_attr", "lower_cover_mmu_map_tnu_reflux_strs_attr", "tmp_be_risk_user_attr", "explore_user_risk_min_attr", "tmp_be_shuffle_user_attr", "low_real_show_threshold",
        "low_fans_threshold", "black_hetu_set_low_real_show_attr", "black_hetu_set_low_fans_attr", "enable_cart_photo_filter_attr", "rate_of_high_explore_show",
        "min_show_of_high_explore_show", "max_show_of_high_explore_show", "black_hetu_set_high_explore_show_attr", "pic_mmu_low_quality_type_map",
        "explore_server_show_threshold", "explore_ctr_threshold", "black_white_change_risk", "black_white_change_shuffle", "explore_zero_play_days_15d_attr",
        "face_90_degree_pids_data_key", "black_photos_attr","support_author_picture_realshow_threshold", "support_author_picture_ctr_threshold", "support_author_filter_memory_data",
        "impression_audit_gray_show_limit_attr", "emp_realshow_show_threshold_attr", "emphtr_filter_threshold_attr", "skip_beauty_photo_filter_attr",
        "cold_start_breakout_score_threshold_attr", "high_fans_threshold_attr", "ctr_threshold_attr", "higher_action_threshold_attr",
        "need_high_quality_mmu_score_attr", "high_quality_mmu_map_strs_attr","questionaire_info_replace_topk_result", "skip_high_hot_quality_pic_attr",
        "questionaire_info_negtive_rate_threhold_attr", "questionaire_info_positive_rate_threhold_attr", "questionaire_info_unsure_rate_threhold_attr",
        "questionaire_info_credible_total_count_attr", "questionaire_thompson_filter_attr", "questionaire_filter_neg_weight_attr", "questionaire_filter_pos_weight_attr",
        "questionaire_filter_unsure_weight_attr", "questionaire_filter_click_weight_attr", "questionaire_filter_unclick_weight_attr",
        "enable_hetu_filter_attr", "enable_audit_tag_filter_attr", "questionaire_use_global_data_attr",
        "questionaire_info_negtive_rate_high_threhold_attr", "questionaire_info_topk_level_threshold_attr", "questionaire_info_audit_level_threshold_attr",
        "enable_hate_content_reason_filter_attr", "hate_content_reason_minutes_cut_attr", "hetu_tag_l3_minutes_cut_attr",
        "thompson_filter_enable_fountain_cnt","thompson_filter_enable_thanos_cnt","thompson_filter_enable_nebula_cnt", "thompson_filter_enable_explore_cnt",
        "photo_life_max_hours_attr", "enable_skip_follow_author_attr", "thompson_filter_threshold_attr", "enable_interaction_base_attr",
        "thompson_filter_realshow_divisor_attr", "impression_audit_gray_tag_list_attr",
        "enable_hate_cost_attr", "emphtr_filter_ctr_weight_attr", "emphtr_filter_ltr_weight_attr", "emphtr_filter_wtr_weight_attr",
        "emphtr_filter_ftr_weight_attr", "emphtr_filter_cmtr_weight_attr", "emphtr_filter_time_weight_attr", "emphtr_filter_normal_time_weight_attr", "boost_photo_reason_list_attr",
        "emphtr_filter_report_weight_attr", "enable_hate_author_skip_hetu_filter_attr",
        "thompson_filter_ctr_weight_attr", "thompson_filter_ltr_weight_attr", "thompson_filter_wtr_weight_attr", "thompson_filter_ftr_weight_attr", "thompson_filter_cmtr_weight_attr", "thompson_filter_time_weight_attr", "thompson_filter_report_weight_attr", "thompson_filter_normal_time_weight_attr", "thompson_filter_realshow_weight_attr",
        "ignore_reason_attr", "default_cut_off_ratio_attr", "enable_random_cut_off_attr", "lt_longview_ratio_threshold_attr", "sharp_change_confidence_threshold_attr", "over_days_filter_days_limit_attr",
        "skip_high_xtr_dup_filter_attr", "skip_dup_realshow_threshold_attr", "skip_dup_fvtr_threshold_attr", "skip_dup_ctr_threshold_attr","skip_dup_watchtime_threshold_attr",
        "thompson_filter_enable_skip_low_emphtr_attr", "thompson_filter_no_click_weight_attr", "thompson_filter_low_emphtr_threshold_attr", "thompson_filter_lvtr_weight_attr",
        "enable_audit_gray_cover_level_filter_escape_attr","infer_uv_ctr_attr","infer_uv_ctr_threshold_max_attr","infer_uv_ctr_threshold_min_attr","refresh_times_attr","refresh_times_threshold_max_attr","refresh_times_threshold_min_attr",
        "skip_not_audit_zero_value_attr","skip_not_audit_follow_author_attr", "only_filter_picture_long_and_set_attr", "enable_adpt_threshold_attr", "emphtr_filter_threshold_list_attr",
        "mmu_enable_follow_author_exemption_attr", "mmu_enable_impression_good_ignore_attr","user_gender_attr","enable_explore_gender_attr",
        "topk_audit_bad_recall_filter_attr",  "topk_audit_bad_recall_filter_use_global_attr",  "topk_audit_bad_recall_filter_credible_ques_cnt_attr",  "topk_audit_bad_recall_filter_pos_threshold_attr",
        "topk_audit_bad_recall_filter_mode_attr", "topk_audit_bad_recall_filter_unsure_threshold_attr", "topk_audit_bad_recall_filter_neg_threshold_attr", "topk_audit_bad_recall_filter_hate_threshold_attr", "only_filter_high_value_pic_attr",
        "user_sexy_interest_score_attr", "user_sexy_interest_score_ignore_threshold_attr", "user_sexy_interest_exemption_tag_list_attr", "user_sexy_interest_extra_filter_tag_list_attr",
        "enable_user_sexy_interest_exemption_high_hot_white_tag_attr", "enable_user_sexy_interest_exemption_hate_rate_attr", "user_sexy_interest_exemption_hate_threshold_attr",
        "user_sexy_interest_exemption_hate_rate_threshold_attr", "user_sexy_interest_exemption_age_list_attr", "user_sexy_interest_exemption_city_level_list_attr",
        "user_age_segment_attr", "user_city_level_attr",
        ])

    attrs = set()
    for _, filter in self.__filter_map.items():
      attrs.update(self.extract_dynamic_params(filter.get("enable")))
      for item in filter.items():
        if item[0] in common_attr_set:
          attrs.add(item[1])

    if "server_show_aid" in self.__filter_map:
      attrs.add(self.__filter_map["server_show_aid"].get("server_show_aid_list_attr"))
    if "upload_type" in self.__filter_map:
      attrs.add(self.__filter_map["upload_type"].get("filter_type_list_attr"))
      attrs.add(self.__filter_map["upload_type"].get("enable_skip_high_value_pic"))
    if "picture_type" in self.__filter_map:
      attrs.add(self.__filter_map["picture_type"].get("filter_type_list_attr"))
      attrs.add(self.__filter_map["picture_type"].get("enable_skip_high_value_pic"))
    if "source_aid" in  self.__filter_map:
      attrs.add(self.__filter_map["source_aid"].get("source_aid_attr"))
    if "low_fans_lite" in  self.__filter_map:
      attrs.add(self.__filter_map["low_fans_lite"].get("count_threshold_attr"))
    if "low_server_show_lite" in  self.__filter_map:
      attrs.add(self.__filter_map["low_server_show_lite"].get("count_threshold_attr"))
    if "long_term" in  self.__filter_map:
      attrs.add(self.__filter_map["long_term"].get("days_threshold_attr"))
    if "content_dup_v2" in  self.__filter_map:
      filter = self.__filter_map["content_dup_v2"]
      for attr in ["filter_content_type_list_attr", "filter_content_type_list_for_pic_attr",
        "skip_high_hot_quality_pic_attr", "skip_high_xtr_dup_filter_attr", "skip_dup_realshow_threshold_attr",
        "skip_dup_fvtr_threshold_attr", "skip_dup_ctr_threshold_attr", "skip_dup_watchtime_threshold_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "hate_author" in  self.__filter_map:
      attrs.add(self.__filter_map["hate_author"].get("limit_hate_reason_attr"))
    if "short_duration" in self.__filter_map:
       attrs.add(self.__filter_map["short_duration"].get("short_duration_limit_attr"))
    if "emprical_ctr" in  self.__filter_map:
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_filter_threshold_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_threshold_str_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_multi_number_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_base_number_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_filter_realshow_threshold_attr"))
    if "duration_random_filter" in self.__filter_map:
      filter = self.__filter_map["duration_random_filter"]
      for attr in ["ignore_reason_attr", "default_cut_off_ratio_attr", "adjust_cut_off_ratio_attr", "enable_random_cut_off_attr",
        "lt_longview_ratio_threshold_attr", "sharp_change_confidence_threshold_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "duration_emp_watchtime_sample_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_threshold_attr"))
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_base_number_attr"))
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_multi_number_attr"))
    if "pic_exptag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_exptag_filter"].get("pic_exptag_filter_str_attr"))
    if "fresh_request_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["fresh_request_filter"].get("is_fresh_request_attr"))
      attrs.add(self.__filter_map["fresh_request_filter"].get("show_threshold_attr"))
    if "xtab_life_index_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_category_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_category_l2_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_blacklist_category_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_blacklist_category_l2_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_category_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_category_l2_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_blacklist_category_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_blacklist_category_l2_filter_attr"))
    if "lifecate_pic_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["lifecate_pic_filter"].get("explore_lifecate_hetu1_list_attr"))
    if "over_180_days" in  self.__filter_map:
      attrs.add(self.__filter_map["over_180_days"].get("entertainment_hetu_tags_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("entertainment_hetu_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("enable_filter_low_like"))
      attrs.add(self.__filter_map["over_180_days"].get("low_like_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("low_like_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("topn_screen_filter_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("enable_filter_by_audit"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_not_audit_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_gray_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_normal_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_high_quality_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_gray_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_normal_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_high_quality_days_limit_attr"))
    if "multi_audit_gray_filter" in self.__filter_map:
      attrs.add(self.__filter_map["multi_audit_gray_filter"].get("audit_gray_count_threshold_attr"))
      attrs.add(self.__filter_map["multi_audit_gray_filter"].get("multi_audit_gray_days_limit_attr"))
    if "high_hot_audit_gray_show" in self.__filter_map:
      attrs.add(self.__filter_map["high_hot_audit_gray_show"].get("enable_stat_all_page"))
      attrs.add(self.__filter_map["high_hot_audit_gray_show"].get("high_hot_audit_gray_show_threshold"))
    if "audit_gray_cover_level_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("enable_audit_gray_cover_level_part_filter_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("enable_audit_gray_cover_level_part_filter_series_switch_attr"))
    if "audit_rule_adjust_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_rule_adjust_filter"].get("audit_rule_adjust_tags_attr"))
    if "dynamic_xtr_filter" in self.__filter_map:
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_xtrs_threshold_list_attr"))
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_filter_old_photo_days_attr"))
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_filter_save_follow_author_attr"))
    if "empirical_xtr" in self.__filter_map:
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_realshow_threshold_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_upload_date_threshold_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_ctr_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_playtime_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_cross_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_ctr_threshold_str_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_playtime_threshold_str_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_cross_threshold_str_attr"))
    if "merchant_holdout_filter" in self.__filter_map:
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("merchant_author_list_ptr_attr"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("enable_filter_living_merchant_photo"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("enable_filter_living_merchant_author"))
    if "video_quality_assessment_filter" in self.__filter_map:
      attrs.add(self.__filter_map["video_quality_assessment_filter"].get("skip_video_quality_assessment_follow_author_attr"))
    if "be_black_author_filter" in self.__filter_map:
      attrs.add(self.__filter_map["be_black_author_filter"].get("be_black_list_attr"))
    if "negative_retr_filter" in self.__filter_map:
      attrs.add(self.__filter_map["negative_retr_filter"].get("negative_retr_list_attr"))
    if "short_term_negative_filter" in self.__filter_map:
      attrs.add(self.__filter_map["short_term_negative_filter"].get("short_minutes_cut_attr"))
      attrs.add(self.__filter_map["short_term_negative_filter"].get("long_minutes_cut_attr"))
    if "specified_group_gray_audit_filter" in self.__filter_map:
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_impression_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_high_hot_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_topk_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("is_satisfy_user"))
    if "second_tab_hetu_filter" in self.__filter_map:
      attrs.add(self.__filter_map["second_tab_hetu_filter"].get("second_tab_category_id"))
    if "audit_user_experiment_level_filter" in self.__filter_map:
       attrs.add(self.__filter_map["audit_user_experiment_level_filter"].get("audit_user_experiment_level_map_attr"))
    if "personified_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["personified_author_filter"].get("personified_author_filter_flag"))
    if "movie_copyright_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["movie_copyright_holdout_filter"].get("photo_ip_info_map_ptr_attr"))
      attrs.add(self.__filter_map["movie_copyright_holdout_filter"].get("target_filter_flag_attr"))
    if "young_inc_tags_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("young_inc_category_list_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("young_inc_category_hetu_list_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_flag_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_prime_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("upload_time_limit_attr"))
    if "fans_count_random_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("filter_prime_attr"))
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("fans_bucket_list_attr"))
    if "low_comment_cnt_filter" in self.__filter_map:
      attrs.add(self.__filter_map["low_comment_cnt_filter"].get("low_comment_cnt_threshold_attr"))
    if "audit_hack_photo_filter" in self.__filter_map:
      filter = self.__filter_map["audit_hack_photo_filter"]
      for attr in ["audit_hack_tag_set_attr", "min_show_attr", "max_ltr_attr", "max_wtr_attr", "max_cmtr_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "audit_cold_review_level_filter" in self.__filter_map:
      filter = self.__filter_map["audit_cold_review_level_filter"]
      for attr in ["audit_cold_review_level_black_tag_set_attr", "audit_cold_review_level_top_list_inferior_tag_set_attr", "audit_cold_review_level_top_list_inferior_vv_limit_attr", "explore_enable_audit_cold_review_level_for_all_user_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "user_reco_neg_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("reco_neg_photo_list_attr"))
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("candidate_count_attr"))
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("candidate_count_limit"))
    if "data_set_tags_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["data_set_tags_filter"].get("filter_tags_list_attr"))
    if "data_set_tags_bit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["data_set_tags_bit_filter"].get("filter_bits_list_attr"))
    if "hetu_tag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_whitelist_categories_l1_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_whitelist_categories_l2_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_blacklist_categories_l1_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_blacklist_categories_l2_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("enable_low_vv_filter_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("explore_vv_3d_threshold_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("explore_vv_3d_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("is_zero_play_user_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("enable_only_zero_play_filter_attr"))
    if "quality_audit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["quality_audit_filter"].get("filter_tags_list_attr"))
    if "ecom_intent_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["ecom_intent_score_filter"].get("ecom_intent_score_threshold_attr"))
      attrs.add(self.__filter_map["ecom_intent_score_filter"].get("explore_enable_ecom_intent_score_for_all_user_attr"))
    if "hetu_author_category_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["hetu_author_category_holdout_filter"].get("fans_count_limit_attr"))
      attrs.add(self.__filter_map["hetu_author_category_holdout_filter"].get("hetu_author_category_list_attr"))
    if "pic_low_quality_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_quality_filter"].get("pic_low_quality_filter_thresh_attr"))
      attrs.add(self.__filter_map["pic_low_quality_filter"].get("explore_pic_low_quality_tag_list_attr"))
    if "high_photo_count_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("high_photo_count_author_map_ptr_attr"))
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("realshow_threshold_attr"))
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("post_num_base_attr"))
    if "douyin_author_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("filter_flag_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("fans_count_limit_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("hetu_author_category_list_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("douyin_10w_author_set_ptr_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("douyin_100w_author_set_ptr_attr"))
    if "xlife_index_filter" in self.__filter_map:
      attrs.add(self.__filter_map["xlife_index_filter"].get("xlife_low_quality_filter_thresh_attr"))
      attrs.add(self.__filter_map["xlife_index_filter"].get("xlife_low_quality_tag_list_attr"))
    if "quality_control_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_quality_control_threshold_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_audit_gray_weight_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_mmu_score_gray_weight_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("impression_audit_gray_tag_list_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("skip_first_page_control_attr"))
    if "xlife_content_control_filter" in self.__filter_map:
      attrs.add(self.__filter_map["xlife_content_control_filter"].get("pic_vv_threshold_attr"))
      attrs.add(self.__filter_map["xlife_content_control_filter"].get("mmu_40_filter_score_attr"))
    if "audit_b_second_tag_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_b_second_tag_filter"].get("filter_audit_b_second_tag_str_attr"))
    if "passby_user_low_vv_filter" in self.__filter_map:
      attrs.add(self.__filter_map["passby_user_low_vv_filter"].get("low_vv_thresh_attr"))
      attrs.add(self.__filter_map["passby_user_low_vv_filter"].get("high_vv_thresh_attr"))
      attrs.add(self.__filter_map["passby_user_low_vv_filter"].get("act_rate_thresh_attr"))
    if "auto_audit_hot_cover_level_filter" in self.__filter_map:
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("enable_follow_author_exemption_attr"))
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("enable_impression_good_ignore_attr"))
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("auto_audit_bad_show_limit_attr"))
    if "protogenetic_advertise_tags_filter" in self.__filter_map:
      attrs.add(self.__filter_map["protogenetic_advertise_tags_filter"].get("filter_advertise_list_attr"))
    if "tnu_content_filter" in self.__filter_map:
      attrs.add(self.__filter_map["tnu_content_filter"].get("enable_cover_filter"))
      attrs.add(self.__filter_map["tnu_content_filter"].get("enable_impression_filter"))
      attrs.add(self.__filter_map["tnu_content_filter"].get("enable_hate_filter"))
      attrs.add(self.__filter_map["tnu_content_filter"].get("hate_cnt_thresh_attr"))
      attrs.add(self.__filter_map["tnu_content_filter"].get("hate_rate_thresh_attr"))
    if "life_author_filter" in self.__filter_map:
      attrs.add(self.__filter_map["life_author_filter"].get("author_grade_thresh_attr"))
      attrs.add(self.__filter_map["life_author_filter"].get("author_punish_cnt_mode_attr"))
      attrs.add(self.__filter_map["life_author_filter"].get("author_filter_markcode_attr"))
      attrs.add(self.__filter_map["life_author_filter"].get("author_punish_markcode_attr"))
    if "author_shop_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["author_shop_score_filter"].get("author_shop_score_limit_attr"))
      attrs.add(self.__filter_map["author_shop_score_filter"].get("author_shop_zero_protect_attr"))
    if "author_goods_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["author_goods_score_filter"].get("author_goods_score_limit_attr"))
      attrs.add(self.__filter_map["author_goods_score_filter"].get("author_goods_zero_protect_attr"))
    if "pic_sexy_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_sexy_filter"].get("sexy_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_sexy_filter"].get("sexy_pic_cnt_mode_attr"))
    if "pic_bad_cover_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_bad_cover_filter"].get("pic_bad_cover_tags_attr"))
    if "pic_low_cost_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_cost_filter"].get("explore_low_cost_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_low_cost_filter"].get("explore_low_cost_pic_cnt_mode_attr"))
    if "pic_hack_act_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_tags_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_types_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_cnt_mode_attr"))
    if "pic_mmu_hetu_tag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_prob_str_attr"))
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_skip_hv_str_attr"))
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_vv_thr_str_attr"))
    if "pic_data_set_tags_bit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("pic_filter_bits_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("pic_punish_bits_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("skip_filter_mark_cod_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("punish_vv_thresh_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("punish_filter_prob_attr"))
    if "pic_secure_grade_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_secure_grade_filter"].get("secure_grade_filter_code_attr"))
    if "pic_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_author_filter"].get("author_grade_thresh_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_punish_cnt_mode_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_filter_markcode_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_punish_markcode_attr"))
    if "pic_ecology_high_report_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("explore_pic_ecology_high_report_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("explore_pic_ecology_high_report_count_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("pic_ecology_high_report_fans_count_threshold_attr"))
    if "pic_ecology_high_neg_pos_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_neg_pos_rate_filter"].get("explore_pic_ecology_high_neg_pos_rate_threshold_attr"))
    if "pic_ecology_high_short_play_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_short_play_rate_filter"].get("explore_pic_ecology_high_short_play_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_short_play_rate_filter"].get("explore_pic_ecology_neg_rate_threshold_attr"))
    if "pic_ecology_mix_interact_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_avg_view_time_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_vv_threshold_attr"))
    if "pic_mix_interact_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("base_vv_threshold_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("author_filter_mark_cod_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("interact_rate_thresholds_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("vv_thresholds_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("filter_probs_str_attr"))
    if "marketing_static_video_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("static_video_tag_id_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("static_video_tag_prob_thd_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("marketing_mark_cod_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("base_vv_threshold_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("interact_rate_thresholds_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("vv_thresholds_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("filter_probs_str_attr"))
    if "voyage_tnu_content_filter" in self.__filter_map:
      attrs.add(self.__filter_map["voyage_tnu_content_filter"].get("enable_cover_filter"))
      attrs.add(self.__filter_map["voyage_tnu_content_filter"].get("enable_hate_filter"))
      attrs.add(self.__filter_map["voyage_tnu_content_filter"].get("hate_cnt_thresh_attr"))
    if "voyage_vulgar_content_filter" in self.__filter_map:
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("enable_llm_tag_filter"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("enable_mmu_tag_filter"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("enable_low_score_filter"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("enable_safety_review_filter"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("mmu_low_score_attr"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("audit_b_second_tag_attr"))
      attrs.add(self.__filter_map["voyage_vulgar_content_filter"].get("is_picture"))
    if "life_vulgar_content_filter" in self.__filter_map:
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("enable_llm_tag_filter"))
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("enable_mmu_tag_filter"))
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("enable_low_score_filter"))
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("enable_safety_review_filter"))
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("audit_b_second_tag_attr"))
      attrs.add(self.__filter_map["life_vulgar_content_filter"].get("is_picture"))
    if "life_report_hetu_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["life_report_hetu_filter"].get("short_report_hetu_minutes_attr"))
      attrs.add(self.__filter_map["life_report_hetu_filter"].get("long_report_hetu_minutes_attr"))
    if "clickbait_filter" in self.__filter_map:
      attrs.add(self.__filter_map["clickbait_filter"].get("voyage_emp_real_show_threshold_attr"))
      attrs.add(self.__filter_map["clickbait_filter"].get("voyage_emp_low_like_filter_threshold_attr"))
      attrs.add(self.__filter_map["clickbait_filter"].get("voyage_emp_high_comment_filter_threshold_attr"))
      attrs.add(self.__filter_map["clickbait_filter"].get("voyage_enable_pltr_over_pctr_filter_attr"))
      attrs.add(self.__filter_map["clickbait_filter"].get("voyage_enable_comment_over_like_filter_attr"))
    if "tolerance_decline_mmu_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("decline_filter_mmu_tl_min_score"))
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("decline_filter_mmu_tl_max_score"))
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("decline_ratio_mmu_threshold"))
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("decline_user_active_degree_threshold"))
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("retrieval_p_decline_ratio_attr"))
      attrs.add(self.__filter_map["tolerance_decline_mmu_filter"].get("retrieval_mmu_tl_score_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attr_map").values():
      attrs.add(attr)

    if "content_dup" in self.__filter_map:
      attrs.update([
        "mmu_content_ids_3", "mmu_content_ids_8", "mmu_content_ids_9", "mmu_content_ids_10", "mmu_content_ids_14", "mmu_content_ids_15",
        "mmu_content_ids_16", "mmu_content_ids_17", "mmu_content_ids_33"])

    item_attr_set = set([
        "is_break_circle_good_photo_attr", "audit_hot_high_tag_level_attr",
        "explore_operation_c_review_level_attr", "level_hot_online_attr", "audit_b_second_tag_attr", "audit_hot_high_subdivision_level_attr",
        "audit_hot_high_tag_level_attr", "author_id_attr", "is_tnu_extend_index_photo_attr", "auto_audit_black_exempt_level_v1_attr",
        "long_term_high_level_photo_attr", "is_cuckoo_photo_attr", "cuckoo_author_type_attr", "upload_time_attr",
        "explore_operation_c_review_level_attr", "dup_cluster_id_attr", "pic_and_selfdup_id_attr",
        "upload_type_attr", "is_jianguan_risk_photo_attr", "magic_face_id_attr", "kuaishan_id_attr", "outer_material_id_attr",
        "is_eyeshot_longterm_photo_attr", "is_high_other_photo_attr", "show_level_a_attr", "photo_low_report_count_attr",
        "author_low_report_count_attr", "sim_remove_dup_id_attr", "author_fans_count_attr", "explore_real_show_attr", "nebula_real_show_attr",
        "thanos_real_show_attr", "fountain_real_show_attr","mmu_low_quality_model_score_42_attr", "mmu_low_quality_model_score_46_attr",
        "mmu_low_quality_model_score_52_attr", "mmu_low_quality_model_score_63_attr", "mmu_low_quality_model_score_64_attr", "mmu_low_quality_model_score_104_attr",
        "mmu_low_quality_model_score_123_attr", "explore_server_show_attr", "explore_click_attr", "explore_view_length_sum_attr",
        "hetu_tag_level_info_v2__hetu_tag_attr"])

    for filter in self._config.get("filters"):
      for item in filter.items():
        if item[0] == "name" or item[0] == "enable":
          continue

        if item[0] in item_attr_set:
          attrs.add(item[1])

    return attrs

class ExploreLifeVoyageEnsembleScoreCalcArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_voyage_calc_ensemble_score"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_power_weight_attr", ""))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_name", ""))
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    attrs.add(self._config.get("save_ori_ensemble_score_to_attr"))
    attrs.add(self._config.get("save_absolute_score_to_attr"))
    return attrs
  
class ExploreLifeEnsembleScoreCalcArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_life_calc_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["rank_smooth","rank_power_weight","rank_cliff", "rank_height", "cliff_ratio", "user_new_proportion", "user_power_calc",
                 "user_power_calc_v2", "use_reciprocal", "duration_min", "duration_max", "enable_time_cost_opt",
                 "enable_perf_pxtr_pic", "perf_pxtr_pic_num", "duration_add", "action_day",
                 "enable_dynamic_weight_by_user_degree", "fr_rank_max_num", "fr_rank_specified_num", "use_absolute_pow",
                 "use_weighed_sum", "fr_rank_has_sec_str", "use_formula_pow_t", "value_seq_fusion_status",
                 "rank_score_calculate_method", "hyperbolic_scale", "hyperbolic_alpha", "hyperbolic_beta", "hyperbolic_min_num",
                 "two_way_sort", "two_way_sort_total_size", "two_way_sort_coeff", "enable_smooth_rank_formula", "smooth_rank_formula_beta", "enable_use_reciprocal_duration_transform",
                 "enable_power_weight_norm", "power_weight_change_coeff", "min_rank_weight", "use_queue_smooth_as_rank_smooth", "use_queue_value_seq_fusion_status",
                 "use_fractile_in_ensemble_sort", "fractile_in_ensemble_sort_type", "queue_head_boost_index","queue_tail_discount_index", "use_rank_with_absolute_score",
                 "use_rank_sort_weight_adjust", "queue_max_raw_score", "queue_min_raw_score", "enable_2sigma_range_control"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_power_weight_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("avg_xtr", ""))
      attrs.add(cfg.get("min_ratio", ""))
      attrs.add(cfg.get("max_ratio", ""))
      attrs.add(cfg.get("dynamic_weight", ""))
      attrs.add(cfg.get("user_xtr", ""))
      attrs.add(cfg.get("rank_cliff_attr", ""))
      attrs.add(cfg.get("rank_cliff_ratio_attr", ""))
      attrs.add(cfg.get("rank_cliff_min_attr", ""))
      attrs.add(cfg.get("use_new_pow_func", ""))
      attrs.add(cfg.get("new_pow_func_coeff", ""))
      attrs.add(cfg.get("new_pos_func_bias", ""))
      attrs.add(cfg.get("score_threshold", ""))
      attrs.add(cfg.get("skip_diff_judge", ""))
      attrs.add(cfg.get("queue_pow_t", ""))
      attrs.add(cfg.get("rank_height_attr", ""))
      attrs.add(cfg.get("raw_score_normalize_alpha_attr", ""))
      attrs.add(cfg.get("smooth_attr", ""))
      attrs.add(cfg.get("value_seq_fusion_status_attr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_power_weight_attr", ""))
      attrs.add(cfg.get("queue_head_boost_coef", ""))
      attrs.add(cfg.get("queue_tail_discount_coef", ""))
      attrs.add(cfg.get("enable_power_weight_adjust_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_thresh_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_score_thresh_attr", ""))
      attrs.add(cfg.get("raw_score_min_val_attr", ""))
      attrs.add(cfg.get("raw_score_max_val_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    attrs.add(self._config.get("save_ori_ensemble_score_to_attr"))
    attrs.add(self._config.get("save_absolute_score_to_attr"))
    return attrs
