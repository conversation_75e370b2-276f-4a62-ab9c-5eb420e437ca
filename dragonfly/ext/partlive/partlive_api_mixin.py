#!/usr/bin/env python3
# coding=utf-8
"""
filename: partlive_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, partlive api mixin
author: ca<PERSON><PERSON>@kuaishou.com
date: 2021-04-29 17:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .partlive_enricher import *
from .partlive_arranger import *


class partliveApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 partlive 相关的 Processor 接口
  """

  def get_sn_living_icf_retr_src_item_list(self, **kwargs):
    """
    SNLivingIcfRetrSrcListEnricher
    ------
    获取 SNLivingIcfRetr 的召回 source items
    混合添加 live_profile_v1 中最近有 follow 和 like 行为的 author 时间排序
    优先基于有 follow 和 like 行为的作者做触发，用 click 作者作兜底

    参数配置
    ------
    `user_info_attr`: [string] 必配. 存储序列化后的 user_info 的 common_attr
    `retr_source_list_save_to` [string] 必配. 存储召回 source id 的 common_attr
    `enable_rand_drop_pid` [动态参数] 必配. 是否随机丢弃
    `get_follow_list_size` [动态参数] 必配. follow list 的 size 上限
    `get_like_list_size` [动态参数] 必配. like list 的 size 上限
    `live_duration_bound` [动态参数] 必配. click list 的 play time 下限

    调用示例
    ------
    ``` pyhton
    .get_sn_living_icf_retr_src_item_list(
        user_info_attr = "user_info_attr",
        retr_source_list_save_to = "retr_src_list",
        // 以下为 ab 参数
        enable_rand_drop_pid = "{{enable_rand_drop_pid}}",
        get_follow_list_size = "{{get_follow_list_size}}",
        get_like_list_size = "{{get_like_list_size}}",
        live_duration_bound = "{{live_duration_bound}}"
    )
    ```
    """
    self._add_processor(SNLivingIcfRetrSrcListEnricher(kwargs))
    return self

  def get_sn_living_icf_recall_related(self, **kwargs):
    """
    SNLivingIcfRecallRelatedEnricher
    ------
    获取 SNLivingIcfRetr 的召回 src item 对应 author 的得分权重和初始 score

    参数配置
    ------
    `user_info_attr`: [string] 必配. 存储序列化后的 user_info 的 common_attr
    `ann_author_embedding_retr_rerank_use_ensemble_score` [动态参数] 必配. 是否使用 ensemble_score
    `live_author_source_score_save_to` [string] 必配. 存储 author 初始分

    调用示例
    ------
    ``` pyhton
    .get_sn_living_icf_recall_related(
        user_info_attr = "user_info_attr",
        use_ensemble_score = "{{ann_author_embedding_retr_rerank_use_ensemble_score}}",
        live_author_source_score_save_to = "live_author_source_score"
    )
    ```
    """
    self._add_processor(SNLivingIcfRecallRelatedEnricher(kwargs))
    return self

  def sn_living_icf_retrieval_post_processor(self, **kwargs):
    """
    SNLivingIcfRetrPostProcessArranger
    ------
    SNLivingIcfRetr 的后处理逻辑，a2a 召回 时的 distance * author 的初始 score

    参数配置
    ------

    `living_photo_per_author` [int] 选配. 单个 author 的 photo 数限制. 默认 5
    `live_author_source_score` [double list] 必配. author 原始分数的 common attr
    `live_author_distance` 必配. [double list] 必配. a2a 召回时 distance 的 common attr
    `source_author_id` 必配. [int] 必配. a2i 召回时的召回 src id
    `final_score_save_to` 必配. [string] 必配. 存储最后的分数
    `origin_retr_src_item_list` 必配. [string] 必配. a2a 召回时的 src_item_list

    调用示例
    ------
    ``` pyhton
    .sn_living_icf_retrieval_post_processor(
        living_photo_per_author = "living_photo_per_author",
        live_author_source_score = "live_author_source_score",
        live_author_distance = "live_author_distance",
        source_author_id = "src_author_id",
        final_score_save_to = "final_score",
        origin_retr_src_item_list = "ann_retr_src_item_list",
    )
    ```
    """
    self._add_processor(SNLivingIcfRetrPostProcessArranger(kwargs))
    return self

  def livestream_cf_retrieve(self, **kwargs):
    """
    LiveStreamCommonCfRetriever
    ------
    直播 leaf 中使用 ItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `enable_live_icf`: [bool] 是否icf小流量实验

    `config_key`: [string] [动态参数] 必配.召回服务的 config key name

    `save_left_trigger_id_to`: [动态参数] 必配.召回服务的 left trigger

    `enable_icf_follow_list`: [bool] [动态参数] 必配. 小流量实验开关

    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    `user_info_attr`: [string] 必配. user profile 信息，从common attr中反序列化得到

    调用示例
    ------
    ``` python
    .livestream_cf_retrieve(
        reason = Reason_follow_cf_retrieval,
        enable_live_icf = False,
        user_info_attr = "user_info_attr",
        kess_service = "livestream_follow_cf_retr_service",
        config_key = "livestream_follow_cf_config_key",
        save_left_trigger_id_to = "left_trigger",
        enable_icf_follow_list = False,
        timeout_ms = 100
    ) 
    ```
    """
    self._add_processor(LiveStreamCommonCfRetriever(kwargs))
    return self