 #!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types,check_arg
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>,LeafRetriever

class SNLivingIcfRetrPostProcessArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sn_living_icf_retrieval_post_processor"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("living_photo_per_author")))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("live_author_source_score"))
    attrs.add(self._config.get("live_author_distance"))
    attrs.add(self._config.get("source_author_id"))
    attrs.add(self._config.get("origin_retr_src_item_list"))
    return attrs

class LiveStreamCommonCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_cf_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("config_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_icf_follow_list")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")     
