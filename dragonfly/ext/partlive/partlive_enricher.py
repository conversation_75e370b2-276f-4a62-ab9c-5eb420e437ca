#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher


class SNLivingIcfRetrSrcListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_sn_living_icf_retr_src_item_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("retr_source_list_save_to"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_rand_drop_pid")))
    attrs.update(self.extract_dynamic_params(self._config.get("get_follow_list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("get_like_list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("live_duration_bound")))
    attrs.update(self.extract_dynamic_params(self._config.get("get_comment_list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("get_gift_list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("get_search_list_size"))) 
    attrs.update(self.extract_dynamic_params(self._config.get("enable_has_author")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_trigger_weight")))   
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get('retr_source_list_save_to'))

class SNLivingIcfRecallRelatedEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_sn_living_icf_recall_related"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("use_ensemble_score")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("live_author_source_score_save_to"))
    return attrs
