#!/usr/bin/env python3
"""
filename: ad_mixer.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for ad
author: <PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2024-06-12 20:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafMixer

class TwinTowersRetrieval(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_towers_retrieval"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class TwinTowersInitializer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_twin_towers_initializer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class PrepareContext(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "prepare_context"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["input_common_attrs"])


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class ResultFromCache(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "result_from_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attr = set()
    attr.add('hit_cache')
    return attr

class HandleResult(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "handle_result"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config["output_common_attrs"])
    return attrs

class TriggerInit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "twin_towers_trigger_init"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class ConstructADLog(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "construct_adlog"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class ConstructBSLog(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "construct_bslog"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()


class ExtractBSFeature(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_bs_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class ConstructDNNInput(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "construct_dnn_input"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class CalculateEmbedding(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calculate_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class SimActionList(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_action_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class BuildUserTarget(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_user_target"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

class FeatureInitializer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "feature_initializer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()
