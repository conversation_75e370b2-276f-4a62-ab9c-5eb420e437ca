#!/usr/bin/env python3
"""
filename: ad_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for ad
author: x<PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2024-02-01 11:03:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver


class CommonIdtObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_idt"

class CommonKScaleObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kscale"

class AdBsUserInfoValueObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bs_userinfo_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["flatten_data_map_name"])
    attrs.update(["flatten_data_map_value"])
    return attrs

class AdBsItemInfoValueObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bs_iteminfo_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr = self._config.get("common_attr","BslogBsItem")
    attrs = set([common_attr])
    return attrs
  
class AdFeatureValueObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_feature_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr = self._config.get("common_attr","FeatureResult")
    attrs = set([common_attr])
    return attrs
