#!/usr/bin/env python3
"""
filename: ad_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for ad
author: <PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-09-19 13:21:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class AdParseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bs_parse"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config["output_common_attrs"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class AdTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_ad_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config["input_attrs"])
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.add(extract_dynamic_param(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config["output_attrs"])
    for k in ["success_flag_attr_var", "output_level_attr_var", "tdm_sample_type_var"]:
      if k in self._config:
        attrs.add(self._config[k])
    return attrs

class SampleInterfaceRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "read_sample_interface"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("sample_from_attr"):
      attrs.add(self._config.get("sample_from_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    key_score_attrs = set(self._config.get("key_score_features", []))
    common_features = set(self._config.get("extract_common_features", [])) - key_score_attrs
    attrs = set()
    attrs.update(f"{name}_key" for name in key_score_attrs)
    attrs.update(f"{name}_score" for name in key_score_attrs)
    attrs.update(common_features)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    key_score_attrs = set(self._config.get("key_score_features", []))
    item_features = set(self._config.get("extract_item_features", [])) - key_score_attrs
    attrs = set()
    attrs.update(f"{name}_key" for name in key_score_attrs)
    attrs.update(f"{name}_score" for name in key_score_attrs)
    attrs.update(item_features)
    return attrs

class CommonAdLiveColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_retriever_with_ad_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_live_id_to_attr", "save_photo_id_to_attr", "save_author_id_to_attr",  "save_timestamp_to_attr",
        "save_photo_duration_to_attr", "save_photo_play_time_to_attr", "save_live_play_time_to_attr", "save_label_to_attr",
        "save_channel_to_attr", "save_cluster_id_to_attr", "save_tag_to_attr", "save_gmv_to_attr",
      ] if key in self._config
    }

class AdGoodsColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_retriever_with_goods_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_live_id_to_attr", "save_photo_id_to_attr", "save_author_id_to_attr",  "save_timestamp_to_attr",
        "save_photo_duration_to_attr", "save_photo_play_time_to_attr", "save_live_play_time_to_attr", "save_label_to_attr",
        "save_channel_to_attr", "save_cluster_id_to_attr", "save_tag_to_attr", "save_gmv_to_attr",
      ] if key in self._config
    }

class CommonAdLiveColossusRespV2Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_gsu_retriever_with_ad_colossus_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_live_id_to_attr", "save_photo_id_to_attr", "save_author_id_to_attr",  "save_timestamp_to_attr",
        "save_photo_duration_to_attr", "save_photo_play_time_to_attr", "save_live_play_time_to_attr", "save_label_to_attr",
        "save_channel_to_attr", "save_cluster_id_to_attr", "save_tag_to_attr", "save_gmv_to_attr",
      ] if key in self._config
    }

class CommonP2lColossusRespV2Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_gsu_retriever_with_p2l_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_photo_id_to_attr", "save_author_id_to_attr",  "save_timestamp_to_attr",
        "save_photo_duration_to_attr", "save_photo_play_time_to_attr", "save_live_play_time_to_attr", "save_label_to_attr",
        "save_cluster_id_to_attr", "save_aidcluster_to_attr",
      ] if key in self._config
    }

class AdItemKeyRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_key_retrieve"  

class AdLiveSegmentColossusE2eRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_live_segment_colossus_e2e_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_live_segment_id_to_attr",
        "save_live_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr",  "save_play_time_to_attr",
        "save_channel_to_attr", "save_appid_to_attr", "save_interactive_form_to_attr", "save_posid_to_attr",
        "save_label_to_attr", "save_timestamp_to_attr", "save_segment_id_to_attr",
        "save_item_id_to_attr", "save_item_spu_to_attr", "save_item_category_to_attr", "save_item_label_to_attr",
        "save_yellow_cart_index_to_attr", "save_item_price_to_attr_", "save_item_volume_to_attr_",
        "save_face_id_to_attr", "save_living_attr_to_attr_", "save_face_cnt_to_attr", "save_author_age_to_attr",
        "save_author_gender_to_attr", "save_beauty_index_to_attr", "save_is_higlight_to_attr"
      ] if key in self._config
    }

class AdBsRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_bs_retriever"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "limit_num_attr" in self._config:
      ret.add(self._config["limit_num_attr"])
    else:
      ret.add("limit_num")
    if "attr_keys" in self._config:
      attr_keys = self._config["attr_keys"]
      for _, val in enumerate(attr_keys):
        if val["common"]:
          ret.add(val["attr_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "attr_keys" in self._config:
      attr_keys = self._config["attr_keys"]
      for _, val in enumerate(attr_keys):
        if not val["common"]:
          ret.add(val["attr_name"])
    return ret
  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 定义当前 Processor 增加结果集的 tables """
    return set()


class CommonPhotoColossusV2RespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_photo_colossus_v2_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr",
            "save_tag_to_attr", "save_channel_to_attr", "save_label_to_attr", "save_timestamp_to_attr",
            "save_photo_lat_attr", "save_photo_lon_attr", "save_user_lat_attr", "save_user_lon_attr",
        ] if key in self._config
    }

class CommonAdColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_retriever_with_colossus_resp_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret
  
class CommonAdColossusRespV2Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_colossus_resp_retriever_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_pid_attr"))
    ret.add(self._config.get("output_aid_attr"))
    ret.add(self._config.get("output_play_attr"))
    ret.add(self._config.get("output_tag_attr"))
    ret.add(self._config.get("output_time_attr"))
    ret.add(self._config.get("output_label_attr"))
    return ret
  
class CommonAdColossusRespV4Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_colossus_resp_retriever_v4"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_pid_attr"))
    ret.add(self._config.get("output_aid_attr"))
    ret.add(self._config.get("output_play_attr"))
    ret.add(self._config.get("output_tag_attr"))
    ret.add(self._config.get("output_time_attr"))
    ret.add(self._config.get("output_label_attr"))
    ret.add(self._config.get("output_channel_attr"))

    return ret
  
class CommonAdColossusRespV5Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_colossus_resp_retriever_v5"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_pid_attr_to"))
    ret.add(self._config.get("save_aid_attr"))
    ret.add(self._config.get("save_play_attr"))
    ret.add(self._config.get("save_duration_attr"))
    ret.add(self._config.get("save_tag_attr"))
    ret.add(self._config.get("save_time_attr"))
    ret.add(self._config.get("save_label_attr"))
    ret.add(self._config.get("save_channel_attr"))

    return ret
  
class CommonAdColossusRespV3Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_colossus_resp_retriever_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_pid_attr"))
    ret.add(self._config.get("output_aid_attr"))
    ret.add(self._config.get("output_play_attr"))
    ret.add(self._config.get("output_tag_attr"))
    ret.add(self._config.get("output_time_attr"))
    ret.add(self._config.get("output_label_attr"))
    return ret

class AdSamplingCenterRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_sampling_center_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["pos_sample_ids_attr"])
    ret.add(self._config["neg_sample_ids_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("sample_info_attr"))
    return ret

class CommonP2lColossusRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_gsu_retriever_with_p2l_colossus_resp_v1"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "user_evtr_pids_list", "user_evtr_aids_list",  "user_click_pids_list", "user_click_aids_list"
      ] if key in self._config
    }
