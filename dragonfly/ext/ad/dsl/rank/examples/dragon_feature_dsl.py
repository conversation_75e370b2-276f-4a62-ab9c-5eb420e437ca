# coding=UTF-8
###--------------FeatureServer Dragon DSL 生成脚本-----------------------###
### DSL教学文档: https://docs.corp.kuaishou.com/d/home/<USER>
import sys, copy, os
import json
import argparse

import dragonfly.ext.ad.dsl.rank.model_config_util as util
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.ad.ad_api_mixin import AdApiMixin

"""
################ 算法同学根据自身需求修改下方配置 #####################
### 1. Dragon PipeLine 配置, Feature目前预估必要Processor包括 ad_get_item_info, ad_bslog、ad_extractor、ad_merge_feature、ad_request_infer 四个 Processor, 如果需要其他Processor的自行添加
#    a. Dragon 入门教程: https://docs.corp.kuaishou.com/d/home/<USER>
#    b. Processor 相关介绍或Dragon框架问题: https://dragonfly.corp.kuaishou.com/#/
### 2. 非Dragon配置，如模型的root层级特殊配置，在 add_leaf_flows 时写在 extra_fields 中, 如果检查发现和默认配置冲突的，将会报错
"""
### 阶段一: 指定自己的dragon仓库路径, XXXXX/dragon 为 Dragon 仓库路径
# 添加 Dragon 环境变量 export PYTHONPATH=XXXXX/dragon:$PYTHONPATH

### 阶段二: 修改模型各自定制化配置 (Demo: 直播 GMV)
model_info = {  # 模型 hdfs 配置
    "hdfs_root": "/home/<USER>/big_model_rollback/live_roi_ln_single_roi_atv_model", # 例: "/home/<USER>/big_model_rollback/live_roi_ln_single_roi_atv_model",
    "model_feature": "real_model_feature", # 例: "real_model_feature",
    "model_meta": "real_model.meta", # 例: "real_model.meta",
    "dragon_feature_config" : "dragon_feature_config.json"  # 仅在模型有 dragon 特征时需要配置
}
model_config = util.GetFeatureDefaultConfig(model_info)
# 迁移模型Kconf, 用于 检查DSL生成 Json 和 Base Kconf Diff
base_model_kconf_path = "ad.adPsCloudModels.ad_dsp_sim_gmv_roi_atv_model_v3_old"

### 阶段三: 定义 predict pipeline(必须), 无 Dragon 特征可按需删除 enrich_by_sub_flow + ad_merge_feature, 各 Processor 配置+使用方法参考点击链接搜索
#   https://dragonfly.corp.kuaishou.com/#/wiki/development_guide?id=commonrecorequest-proto-%e5%ae%9a%e4%b9%89
class MyLeafFlow(LeafFlow, AdApiMixin):
    def apply(self, callback):
        return callback(self)

sub_flow = MyLeafFlow(name="sub_flow")
predict_pipeline = MyLeafFlow(name="predict")
predict_pipeline.ad_get_feature_processor(
    feature_process_config=[
        {
            "AdLiveItemProcessor": {}
        }
    ],
).ad_get_item_info(
    use_component_item=True,
    component_item= {
        "component_item_btq_config": {
            "creative": {
                "incr_source_name": "ad_predict_item_info_v2_id_incr",
                "is_main_key": True
            },
            "live": {
                "incr_source_name": "ad_predict_live_incr_v2_batched_samples"
            },
            "photo": {
                "incr_source_name": "ad_predict_photo_info_v2_id_incr"
            }
        },
        "component_item_server_config": {
            "creative": {
                "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_creative_opt"
            },
            "live": {
                "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_live_opt"
            },
            "photo": {
                "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_photo_opt"
            }
        }
    }
).ad_bslog(
    generate_bslog=False,
    item_already_ready=True
).enrich_by_sub_flow(
    sub_flow=sub_flow,
    merge_item_attrs=["ad_live_segment_u2u_gsu_signs", "ad_live_segment_u2u_gsu_slots"],
    pass_common_attrs=["BslogBsItem"],
    pass_item_attrs=["item_miss"],
    timeout_ms=50,
).ad_extractor(
    need_feature_processor=False,
    serialize_feature=False,
    output_features_attr="FeatureResult",
    feature_path=util.GetModelPathInfo(model_info, "model_feature"),
    item_feature_cache_capacity=5000000,
    item_feature_cache_capacity_in_gb=model_config["item_feature_cache_capacity_in_gb"],
).ad_merge_feature(
    feature_path=util.GetModelPathInfo(model_info, "model_feature"),
    item_sign_attrs=["ad_live_segment_u2u_gsu_signs"],
    item_slot_attrs=["ad_live_segment_u2u_gsu_slots"],
    infer_feature_list_attr="FeatureResult",
    serialized_features_attr="serialized_features",
    check_duplicated_item=False
).ad_request_infer(
    recv_common_attrs=["debug_info"],
    recv_item_attrs=["predict_value", "result_stat"],
    send_common_attrs=["cmdkey_list", "cmdkey_end_pos", "serialized_features"],
    send_item_attrs=["item_miss"],
    timeout_ms=47,
)

### 阶段四: 定义 sub flow pipeline, 用于 Dragon特征抽取
sub_flow.enrich_attr_by_bslog(
    limit_num_attr="limit_num",
    limit_num_value=50,
    attr_keys=[
        # fmt: off
        {"attr_name": "enable_new_product_category", "enum_num": 95239, "common": True, "list": False, "data_type": 0},
        {"attr_name": "aid", "enum_num": 26615, "common": False, "list": False, "data_type": 0}, 
        {"attr_name": "aid", "enum_num": 47785, "common": False, "list": False, "data_type": 0},
        {"attr_name": "aid", "enum_num": 43663, "common": False, "list": False, "data_type": 0},
        {"attr_name": "aid", "enum_num": 22413, "common": False, "list": False, "data_type": 0},
        {"attr_name": "aid", "enum_num": 39461, "common": False, "list": False, "data_type": 0},
        {"attr_name": "aid", "enum_num": 18156, "common": False, "list": False, "data_type": 0}
        # fmt: on
    ],
).ad_picasso_extract(
    table_name="adPicassoLiveSegmentAction",
    scene_type="lsp",
    picasso_timeout_ms=25,
    cache_capacity=60000,
    expire_time=600,
    output_attr="live_seg_colossus_output",
    picasso_kess_service="grpc_adPicassoGatewayService",
).ad_live_segment_gsu_u2u(
    output_sign_attr="ad_live_segment_u2u_gsu_signs",
    output_slot_attr="ad_live_segment_u2u_gsu_slots",
    colossus_resp_attr="live_seg_colossus_output",
    limit_num_attr="limit_num",
    enable_new_product_category="enable_new_product_category",
    target_aids_attr="aid",
    only_output_basic_slots=True,
    # remap slot sign config
    fused_slot_sign_remap=True,
    sign_prefix_bit_num_input=10,
    sign_prefix_bit_num_output=12,
    use_djb2_hash64=True,
    # fmt: off
    slots_id=[
        912, 914, 915, 916, 917, 934, 935, 936, 937, 943, 944, 945,
        946, 947, 961, 962, 963, 964, 965, 985, 986, 990, 991
    ],
    mio_slots_id=[
        912, 914, 915, 916, 917, 934, 935, 936, 937, 943, 944, 945,
        946, 947, 961, 962, 963, 964, 965, 985, 986, 990, 991
    ],
    item_map_list = [
        ["912", 912, 912, 20000000], ["914", 914, 914, 20000000], ["915", 915, 915, 200000],
        ["916", 916, 916, 200000],   ["917", 917, 917, 200000],   ["934", 934, 934, 2000],
        ["935", 935, 935, 200000],   ["936", 936, 936, 200000],   ["937", 937, 937, 200000],
        ["943", 943, 943, 200000],   ["944", 944, 944, 20000000], ["945", 945, 945, 20000000],
        ["946", 946, 946, 200000],   ["947", 947, 947, 200000],   ["961", 961, 961, 2000],
        ["962", 962, 962, 2000],     ["963", 963, 963, 2000],     ["964", 964, 964, 200000],
        ["965", 965, 965, 200000],   ["985", 985, 985, 200000],   ["986", 986, 986, 200000],
        ["990", 990, 990, 200000],   ["991", 991, 991, 200000]
    ]
    # fmt: on
)

### 阶段五: 生成PipeLine配置, 模型root层级特殊配置写在 extra_fields 里，其他无需修改
service = LeafService(
    kess_name="grpc_test_feature_server",
    common_attrs_from_request=[
                "item_id_info",
                "cmdkey_list",
                "cmdkey_end_pos",
                "llsid",
                "flatten_data_map_name",
                "flatten_data_map_value",
                "cmd",
                "tab_type",
                "bs_debug_raw_data",
                "attr_real_time_action_name",
                "attr_real_time_action_value",
                "item_context"
          ],
    item_attrs_from_request=["callback_event"],
)
tmp_file = "feature_tmp_dsl.json"
idt_pipeline = MyLeafFlow(name="common_idt").common_idt()
kscale_pipeline = MyLeafFlow(name="kscale").kscale()
service.add_leaf_flows(leaf_flows=[predict_pipeline, kscale_pipeline, idt_pipeline], request_type="predict").build(
    output_file=tmp_file,
    extra_fields={
        "request_type_config": {"predict": ["predict"]},
    },
).draw() 

################ 算法同学根据自身需求修改上方配置 #####################

################ 下方为基础配置, 请勿擅动 #####################

output_file = os.path.abspath(__file__).replace(".py", ".json")
util.MergeGenerateFile(
    "",
    output_file,
    "FEATURE",
    tmp_file,
    model_config,
    model_info,
    base_model_kconf_path
)
os.unlink(tmp_file)
