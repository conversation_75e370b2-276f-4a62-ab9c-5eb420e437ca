#!/usr/bin/env python3
# coding=utf-8
"""
filename: vcs_api_mixin.py
description: vcs_api_mixin.py
author: <EMAIL>
date: 2021-11-28 16:50:00
"""

from ...common_leaf_util import strict_types , check_arg
from ...common_leaf_processor import LeafObserver

class VcsWriteToRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "vcs_write_to_redis"

  @strict_types
  def _check_config(self) -> None:
    check_arg("key_from_common_attr" not in self._config, "key_from_common_attr 配置项已废弃，请改用 key 配置项！")
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      check_arg(not self._config.get(key, "").startswith("{{"), f"{key} 配置不支持动态参数")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["key_prefix", "expire_second", "key", "value"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      ret.add(self._config.get(key))
    return ret

class VcsWritePhotoUserListObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_photo_user_list"

  @strict_types
  def _check_config(self) -> None:
    check_arg("key_from_common_attr" not in self._config, "key_from_common_attr 配置项已废弃，请改用 key 配置项！")
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      check_arg(not self._config.get(key, "").startswith("{{"), f"{key} 配置不支持动态参数")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      ret.add(self._config.get(key))
    return ret