#!/usr/bin/env python3
# coding=utf-8
"""
filename: vcs_api_mixin.py
description: vcs_api_mixin.py
author: <EMAIL>
date: 2021-11-28 16:45:00
"""

from itertools import chain
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .video_cold_start_arranger import *
from .video_cold_start_enricher import *
from .video_cold_start_observer import *


class VideoColdStartApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 VideoColdStartApiMixin 相关的 Processor 接口
  
  主要涵盖主站photo冷启动相关的自定义接口，冷启动photo侧实时数据自定义读取，photo冷启召回、排序、数据流自定义算子都将包含于此

  - VcsRedisItemAttrEnricher
  - VcsWriteToRedisObserver
  - VcsPhotoRealTimeFeatEnricher

  """

  def get_common_attr_from_redis_by_mode(self, **kwargs):
    """
    VcsRedisCommonAttrEnricher
    ------
    读取 redis value 写入指定的 common_attr 中。

    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name

    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

    `redis_params`: [list] redis key/value 的相关配置
      - `redis_key`: [string | string_list] [动态参数] redis key 的名称，也可以从 string/string_list 类型的 common_attr 获取 key，如果 key 是 string_list（或动态参数的值是一个 string_list）则必须手动指定 output_attr_type 为对应的 list 类型，会逐个使用 list 中的 key 获取 value 并存入到对应的 list 类型 common_attr 中
      - `redis_value_type`: [string] 读取的 redis value 的类型，仅支持 string，默认为 string 类型
      - `output_attr_name`: [string] value 写入的 commonAttr 名
      - `output_attr_type`: [string] value 写入的 commonAttr 类型，支持 int/double/string/int_list/double_list/string_list，默认为 string 类型

    `skip_empty`: [bool] 是否需要跳过为空的情况，仅对 int/int_list/double/double_list 生效，默认 False

    `skip_parse_fail`: [bool] 是否需要跳过解析错误的情况，仅对 int/int_list/double/double_list 生效，注意空字符串一定会 parse fail, 默认 False

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    `az`: 默认为空

    `mode`: pipeline or mget, 默认mget

    调用示例
    ------
    ``` python
    .get_common_attr_from_redis(
      cluster_name = "redis_cluster_name",
      redis_params = [
        {
          "redis_key": "key1",
          "output_attr_name": "output1"
        }, {
          "redis_key": "{{key2}}",
          "redis_value_type": "string",
          "output_attr_name": "output2",
          "output_attr_type": "int"
        }
      ]
    )
    ```
    """
    self._add_processor(VcsRedisCommonAttrEnricher(kwargs))
    return self

  def get_item_attr_from_redis_by_mode(self, **kwargs):
    """
    VcsRedisItemAttrEnricher
    ------
    兼容 get_item_attr_from_redis，在其基础上增加了通过 pipeline 和 hgetall 方式从 redis 获取 value 和通过 az 参数指定 redis 集群的功能。

    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name

    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

    `redis_key_from`: [string] 从 string 类型的 item_attr 获取 key

    `save_value_to`: [string] 将 redis 获取到的值存入指定的 string 类型的 item_attr 中

    `mode`: [string] mget pipeline hgetall 三种方式, 默认为 mget

    `az`: [string] 指定redis集群地区，默认为空

    `kv_seperator`: [string] 每一对 key value 之间的分隔符,只有在 hgetall 才启用，默认为空格 (" ")

    `field_seperator`: [string] 每一对 field value 之间的分隔符,只有在 hgetall 才启用,默认为空格 (" ")

    调用示例
    ------
    ``` python
    .get_item_attr_from_redis_by_mode(
      cluster_name = "redis_cluster_name",
      redis_key_from="redis_key",
      save_value_to="redis_value",
      mode="pipeline",
      az="YZ",
      kv_seperator=" ",
      field_seperator=" "
    )
    ```
    """
    self._add_processor(VcsRedisItemAttrEnricher(kwargs))
    return self

  def write_to_redis_region(self, **kwargs):
    """
    VcsWriteToRedisObserver
    ------
    兼容write_to_redis功能，在其基础上增加通过 az 参数指定 redis 集群和通过 item_attr_save_if 筛选 item 保存 redis 的功能。
    特别地，如果强制指定了 az 参数，但是初始化时找不到对应 az redis 集群，会 fallback 到不指定该参数时的行为(就近选择)。

    参数配置
    ------
    `kcc_cluster`: [string] 必填，redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，redis client 的超时时间，单位 ms，默认为 10 ms

    `key_prefix`: [string] [动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

    `expire_second`: [int] [动态参数]，单位秒, 负数或者 0 表示不设置过期时间，默认 -1

    `list_separator`: [string] 如果存储的 value 是 list 类型，那么我们会用所给的分隔符来拼接成一个 string 存入到 redis 中，默认为 ','

    以下两种 key 二选一：

    `key`: [string] [动态参数] 指定写入 redis 的 key，如果配置了动态参数且该 common_attr 不存在将忽略写入，动态参数支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

    `key_from_item_attr`: [string] 从指定的 item_attr 中获取动态的 redis key，如果该 item_attr 不存在将被跳过。支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

    对于 value 有两种方式，二选一:

    `value`: [string] [动态参数] 指定写入 redis 的 value。

    `value_from_item_attr`: [string] 仅针对 `key_from_item_attr`，会根据其 item_attr 一一对应的去写。

    `az`: [string] 指定redis地区，默认为空

    `item_attr_save_if`: [string] 指定的item_attr值如果>0才write redis，默认为空，所有item的key都写入redis

    调用示例
    ------
    ``` python
    .write_to_redis_region(
      kcc_cluster="test_cluster",
      timeout_ms=10,
      key_prefix="test_",
      key="test_key",
      value="test_value",
      expire_second=600,
      az="YZ",
    )
    ```
    """
    self._add_processor(VcsWriteToRedisObserver(kwargs))
    return self
  
  def extract_photo_realtime_feat(self, **kwargs):
    """
    VcsPhotoRealTimeFeatEnricher
    ------
    抽取photo实时序列特征

    参数配置
    ------
    `time_stat_cluster_name`: [string] 序列特征的 redis 的 cluster name

    `read_timeout_ms`: [int] 获取 序列特征 client redis client 的超时时间，默认为 100

    `key_attr_name`: [string] 从 string 类型的 item_attr 获取 key

    `mode`: [string] mget或者pipeline，默认为pipeline

    `az`: [string] 指定redis集群地区，默认为空

    `slot_as_attr_name_prefix`: [string] 默认空（目前没生效）

    `slot_as_attr_name`: [bool] 返回的特征是否以slot当key

    `item_slots_output`: [list] 返回的slots列表

    `item_parameters_output`: [list] 返回的signs序列

    `request_time_ms_attr_name`: [string] common_attr 样本请求时间，序列截断时间ms

    `upload_time_ms_attr_name`: [string] item_attr photo上传时间

    `time_gap_min`: [int] 时间分片间隔分钟，默认30

    `extract_name_list`: [string]抽取类名字列表，以逗号分开

    `extract_config`: [string]抽取配置路径

    调用示例
    ------
    ``` python
    .extract_photo_realtime_feat(
      time_stat_cluster_name = "redis_cluster_name",
      read_timeout_ms = 100,
      key_attr_name = "pid_key",
      az = "YZ",
      mode="pipeline",
      slot_as_attr_name = True,
      request_time_ms_attr_name = "request_time",
      upload_time_ms_attr_name = "upload_time_ms"
    )
    ```
    """
    self._add_processor(VcsPhotoRealTimeFeatEnricher(kwargs))
    return self

  def write_photo_user_list(self, **kwargs):
    """
    VcsWritePhotoUserListObserver
    ------

    参数配置
    ------
    `kcc_cluster`: [string] 必填，redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，redis client 的超时时间，单位 ms，默认为 10 ms

    `expire_second`: [int] [动态参数]，单位秒, 负数或者 0 表示不设置过期时间，默认 -1

    `key_from_item_attr`: [string] 从指定的 item_attr 中获取动态的 redis key，如果该 item_attr 不存在将被跳过。支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string

    `value_from_item_attr`: [string] 仅针对 `key_from_item_attr`，会根据其 item_attr 一一对应的去写。

    `az`: [string] 指定redis地区，默认为空

    `item_attr_save_if`: [string] 指定的item_attr值如果>0才write redis，默认为空，所有item的key都写入redis

    `read_mode_`: 读取kcc_cluster的模式,pipeline|mget(pipeline)

    `uid`: uid的item_attr

    `click`: click的item_attr

    `like`: like的item_attr

    `follow`: follow的item_attr

    `forward`: forward的item_attr

    `long_view`: click的item_attr

    `comment`: comment的item_attr

    `enter_profile`: enter_profile的item_attr

    `profile_effective_stay`: profile_effective_stay的item_attr

    `comment_effective_stay`: comment_effective_stay的item_attr

    `double_finish_view`: double_finish_view的item_attr

    `collect`: click的item_attr

    `tab_page`: tab_page的item_attr

    `play_time`: play_time的item_attr

    `slide_realshow`: 单列realshow item_attr

    `request_time_ms`: 样本时间戳ms item_attr

    `effective_view`: effective_view的item_attr

    `max_record_num`:-1

    `pid_signs`: pid signs的item_attr[list]

    `user_hetu_second_ids`: common attr 用户hetu二级兴趣id列表[list]

    `user_cluster_id`:  common attr 用户聚类id [int] 
    
    `write_pu_delay_ms`: 写入kiwi后延迟sleep的ms[0]
    
    `skip_no_action`: 是否过滤没有任何行为的样本[默认是1]

    调用示例
    ------
    ``` python
    .write_photo_user_list(
      kcc_cluster="test_cluster",
      timeout_ms=10,
      az="YZ",
    )
    ```
    """
    self._add_processor(VcsWritePhotoUserListObserver(kwargs))
    return self
  
  def extract_photo_user_list(self, **kwargs):
    """
    VcsPhotoUserListFeatEnricher
    ------

    参数配置
    ------
    `photo_user_resp_str`: [string] 必填, item attr

    `request_time_ms`: [string] 必填，common_attr，请求时间ms

    `slot_as_attr_name`: [bool], 默认false

    `slot_as_attr_name_prefix`: [string], 默认为空

    `output_slot_attr`: [string] 输出 item_attr, slot list

    `output_sign_attr`: [string] 输出 item_attr, sign list

    `user_cluster_id`: [string] common_attr,输入用户聚类attr

    `ret_up_len_list`: [list int] 返回的item_attr，表示用户pid长度

    `ret_ua_len_list`: [list int] 返回的item_attr，表示用户作者长度

    `max_user_num`: [int] 默认50

    `max_pid_num`: [int] 默认20

    `max_aid_num`: [int]默认10

    `ret_actual_user_num`: [int]item_attr,返回用户数量的item_attr

    `keep_actions`: [string] common_attr(lua格式) or 原生string
    
    `is_debug`: [int]默认false
    """
    self._add_processor(VcsPhotoUserListFeatEnricher(kwargs))
    return self

  def enrich_by_bipartite_match_score(self, **kwargs):
    """
    BipartiteMatchScoreEnricher, 其他团队慎用
    ------

    参数配置
    ------
    `kess_service`: [string] 必填, 服务kess名

    `reward_attr` : [string] 必填, item的reward的attr_name

    `output_attr` : [string] 必填, 重新打分后分值输出到的attr_name

    `user_info_attr` : [string] 必填, 用户画像的common attr名
    
    `user_hash_attr` : [string] 必填, 一致性hash的字段, 用于把固定user的请求发到固定实例
    """
    self._add_processor(BipartiteMatchScoreEnricher(kwargs))
    return self

  def vcs_reset_reason_by_item_attr(self, **kwargs):
    """
    VcsResetReasonArranger
    ------
    重设 reason, 代码跟TDMApiMixin的reset_reason_by_item_attr一致, 在使用时不受local index限制
  
    参数
    ------
    `reason_attr`: [string] reason item attr 字段
    
    `default_reason`: [int] reason attr 取值为空时的回落值

    调用示例
    ------
    ``` python
    .reset_reason_by_item_attr(
      reason_attr='final_reason',
      default_reason=633,
    )
    ```
    """
    self._add_processor(VcsResetReasonArranger(kwargs))
    return self