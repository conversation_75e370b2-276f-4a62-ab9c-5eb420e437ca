#!/usr/bin/env python3
# coding=utf-8
"""
filename: vcs_api_mixin.py
description: vcs_api_mixin.py
author: <EMAIL>
date: 2021-11-28 16:45:00
"""
import base64
import inspect

from ...common_leaf_util import strict_types , check_arg
from ...common_leaf_processor import LeafEnricher

class VcsRedisCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_from_redis_by_mode"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("redis_params", []):
      attrs.update(self.extract_dynamic_params(string_config.get("redis_key", "")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    mode = self._config.get("mode")
    mode = self._config.get("mode", "mget")
    check_arg(mode in {"pipeline", "mget", "hgetall"}, '`mode` 必须自{"pipeline", "mget", "hgetall"}')
    
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("redis_params", []):
      attrs.add(string_config["output_attr_name"])
    return attrs

class VcsRedisItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_from_redis_by_mode"

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    mode = self._config.get("mode")
    mode = self._config.get("mode", "mget")
    check_arg(mode in {"pipeline", "mget"}, '`mode` 必须自{"pipeline", "mget"}')

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_key_from"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_value_to"])
    return attrs

class VcsPhotoRealTimeFeatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_photo_realtime_feat"

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    mode = self._config.get("mode")
    mode = self._config.get("mode", "mget")
    check_arg(mode in {"pipeline", "mget"}, '`mode` 必须自{"pipeline", "mget"}')

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["request_time_ms_attr_name"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["upload_time_ms_attr_name", "key_attr_name"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs


class VcsRedisItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_from_redis_by_mode"

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    mode = self._config.get("mode")
    mode = self._config.get("mode", "mget")
    check_arg(mode in {"pipeline", "mget", "hgetall"}, '`mode` 必须自{"pipeline", "mget", "hgetall"}')

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["redis_key_from"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_value_to"])
    return attrs

class VcsPhotoUserListFeatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "vcs_extract_photo_user_list"

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    photo_user_resp_str = self._config.get("photo_user_resp_str")
    check_arg(photo_user_resp_str != "", "photo_user_resp_str 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["request_time_ms", "user_cluster_id"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["photo_user_resp_str", "key_attr_name"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["ret_up_len_list","ret_ua_len_list","output_slot_attr", "output_sign_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

class BipartiteMatchScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_bipartite_match_score"

  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    kess_service = self._config.get("kess_service")
    reward_attr = self._config.get("reward_attr")
    output_attr = self._config.get("output_attr")
    user_info_attr = self._config.get("user_info_attr")
    user_hash_attr = self._config.get("user_hash_attr")
    check_arg(kess_service != "", "kess_service 不能为空")
    check_arg(reward_attr != "", "reward_attr 不能为空")
    check_arg(output_attr != "", "output_attr 不能为空")
    check_arg(user_info_attr != "", "user_info_attr 不能为空")
    check_arg(user_hash_attr != "", "user_hash_attr 不能为空")

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    # 表明该 processor 是否是异步的，默认值为 False，当该 processor 为异步时，需要重写这个函数将值设为 True
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieval_num")))
    for key in ["user_info_attr", 'user_hash_attr']:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["reward_attr"])
    attrs.add(self._config["author_id"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs
