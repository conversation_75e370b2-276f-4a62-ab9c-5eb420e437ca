#!/usr/bin/env python3
# coding=utf-8
"""
filename: video_cold_start_arranger.py
description: video cold start arranger module
author: <EMAIL>
date: 2023-11-07 21:00
"""

from ...common_leaf_util import strict_types  #, check_arg, is_number, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class VcsResetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "vcs_reset_reason_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_reason"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reason_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs