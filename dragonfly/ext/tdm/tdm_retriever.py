import operator
import itertools
from ...common_leaf_util import strict_types, check_arg, ArgumentError, \
    gen_attr_name_with_item_attr_channel, extract_attr_names, check_lua_script
from ...common_leaf_processor import LeafRetriever
### 婷婷added for 电商短视频 live 头像 
class MerchantPhotoTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_merchant_photo_tdm_sample"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class MerchantLiveTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_merchant_live_tdm_sample"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class MerchantCartTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_merchant_cart_tdm_sample"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class RecoTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_reco_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["eval_target_label_item_attrs"]:
      attrs.update(self._config.get(key, []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

### 婷婷added for 电商
class EshopTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_eshop_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])



class CommonRecoTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "source_item_key_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class UserTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_user_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config[k] for k in ["node_id_attr", "photo_id_attr"] if k in self._config]
    attrs = set(attrs + self._config.get("node_attrs", []) +
                self._config.get("leaf_attrs", []))
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = self._config.get("user_attrs", []) \
        + self._config.get("item_attrs", []) + \
        self._config.get("label_attrs", [])
    attrs += [self._config[k]
              for k in ["output_level_attr_var", "output_sub_nodes_attr"] if k in self._config]
    return set(attrs)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()


class CommonOverseaTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_oversea_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class CommonSnackTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_snack_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class KsibTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_ksib_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["reco_log_var", "primary_label_var", "bucket_attr"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
            "item_reco_photo_info_var",
            "sampled_node_duration_var"] if k in self._config)
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["success_flag_attr_var"])

class CommonTdmRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_common_tdm"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["item_type", "kess_service", "timeout_ms", "top_k", "tree_name", "shard_num", "traffic_type", "kconf_name", "enable_consistent_hash"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["llsid_attr_name", "user_id_attr_name", "device_id_attr_name", \
        "user_info_attr_name", "user_feature_slots_attr_name", "user_feature_signs_attr_name",
        "request_time_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class CommonRecoTdmRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_tdm"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kess_service", "timeout_ms", "top_k", "tree_name", "traffic_type", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["llsid_attr_name", "user_id_attr_name", "device_id_attr_name", \
        "reco_user_info_attr_name", "oversea_user_info_attr_name", \
        "snack_user_info_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), (int, str)), "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class TdmRecoRedisRegexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_redis_zstd"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("extra_item_attrs", []):
      attrs.add(config["name"])

    save_src_key_to_attr = self._config.get("save_src_key_to_attr")
    if save_src_key_to_attr is not None:
      attrs.add(save_src_key_to_attr)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("key_prefix", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num_per_key", "")))
    for key in ["key_from_attr", "reason_from_attr"]:
      if self._config.get(key):
        attrs.add(self._config.get(key))
    return attrs

class LiveTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_live_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["primary_label_attr"] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config[k] for k in ["success_flag_attr"] if k in self._config)
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("target_label_attrs", []))
    attrs.update(self._config["primary_label_attr"])
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(self._config[k] for k in [
                "tdm_sample_type_attr", "output_level_attr"] if k in self._config)
    return attrs

class BaseTdmSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "base_tdm_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "as"))
    attrs.update(extract_attr_names(self._config.get("common_slots_attrs", []), "as"))
    attrs.update(extract_attr_names(self._config.get("common_signs_attrs", []), "as"))
    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("common_slots", []))) 
    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("item_slots", [])))
    for key in ["llsid_attr_name", "primary_label_attr", "context_attrs",
        "duration_ms_attr_name"]:
      attrs.update(self._config.get(key, ""))
    # attrs.update(self._config["primary_label_attr"])
    # attrs.update(self._config["context_attrs"])
    # attrs.update(self._config["llsid_attr_name"])
    #attrs = set(self._config[k] for k in [
    #                            "primary_label_attr",
    #                            "slot_as_attr_name",
    #                            "send_common_attrs",
    #                            "context_attrs",
    #                            ] if k in self._config)
    for key in ["kess_service", "tree_name", "request_type", "slot_as_attr_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(extract_attr_names(self._config.get("recv_common_attrs", []), "as"))
    attrs.update(self._config["all_item_slots_attr_name"])
    #attrs = set(self._config[k] for k in ["success_flag_attr_name"] if k in self._config)
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    attrs.update(extract_attr_names(self._config.get("item_slots_attrs", []), "as"))
    attrs.update(extract_attr_names(self._config.get("item_signs_attrs", []), "as"))
    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("common_slots", []))) 
    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("item_slots", [])))

    #attrs = set(self._config[k] for k in [
    #            "source_key_item_attr_name",
    #            "tdm_sample_type_attr_name",
    #            "recv_item_attrs",
    #            "output_level_attr"] if k in self._config)
    # attrs = set(self._config.get("context_attrs", []))
    # attrs.update(self._config["source_key_item_attr_name"])
    attrs.update(self._config["source_key_item_attr_name"])
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    attrs.update(self._config["all_item_slots_attr_name"])
    #attrs = extract_attr_names(self._config.get("recv_item_attrs", []), "name")
    # attrs = set(self._config[k] for k in [
    #             "tdm_sample_type_attr", "output_level_attr"] if k in self._config)
    return attrs