#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .tdm_retriever import *
from .tdm_enricher import *
from .tdm_arranger import *

class TDMApiMixin(CommonLeafBaseMixin):
  """
  Tdm Processor API 接口的 Mixin 实现
  """
  
  def retrieve_merchant_photo_tdm_sample(self, **kwargs):
    """
    MerchantPhoto
    ------
    电商短视频tdm, retriever, (live 头像)
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms
    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名 这个不要了！！！
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info 这个不要了！！！
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名， 这个不要了！！！
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label
    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package
    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度
    `debug`: [int] optional, default=0. 1: 开启 debug
    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型
    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs
    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr eg. ["ctr_label", "cvr_label"]
    `fix_tag`: [bool], default=false, 修 tag 特征
    `eval_only`: [bool], default=false, 不改变contex（只评估）
    调用示例
    ------
    ``` python
    .retrieve_merchant_photo_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(MerchantPhotoTdmSampleRetriever(kwargs))
    return self
  
  def retrieve_merchant_live_tdm_sample(self, **kwargs):
    """
    MerchantLive
    ------
    电商短视频tdm, retriever, (live 头像)
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms
    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名 这个不要了！！！
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info 这个不要了！！！
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名， 这个不要了！！！
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label
    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package
    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度
    `debug`: [int] optional, default=0. 1: 开启 debug
    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型
    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs
    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr eg. ["ctr_label", "cvr_label"]
    `fix_tag`: [bool], default=false, 修 tag 特征
    `eval_only`: [bool], default=false, 不改变contex（只评估）
    调用示例
    ------
    ``` python
    .retrieve_merchant_live_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(MerchantLiveTdmSampleRetriever(kwargs))
    return self

  def retrieve_merchant_cart_tdm_sample(self, **kwargs):
    """
    MerchantCart
    ------
    电商挂车视频tdm, retriever

    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms
    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名 这个不要了！！！
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info 这个不要了！！！
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名， 这个不要了！！！
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label
    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package
    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度
    `debug`: [int] optional, default=0. 1: 开启 debug
    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型
    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs
    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr eg. ["ctr_label", "cvr_label"]
    `fix_tag`: [bool], default=false, 修 tag 特征
    `eval_only`: [bool], default=false, 不改变contex（只评估）
    调用示例
    ------
    ``` python
    .retrieve_merchant_cart_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(MerchantCartTdmSampleRetriever(kwargs))
    return self

  def retrieve_reco_tdm_sample(self, **kwargs):
    """
    RecoTdmSampleRetriever
    ------
    通用 tdm reco 采样，获取树的内部节点的训练样本 
    因为采样会采到很多重复 id, 目是用左最高 8 位 item_type 来保证不重复，确保 learner上 enable_item_type_and_keysign = true ！！
    （一次采样超过 256 的情况下需要修改下代码）
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型

    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs

    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr

    `fix_tag`: [bool], default=false, 修 tag 特征

    `eval_only`: [bool], default=false, 不改变contex（只评估）

    调用示例
    ------
    ``` python
    .retrieve_reco_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(RecoTdmSampleRetriever(kwargs))
    return self




  def retrieve_eshop_tdm_sample(self, **kwargs):
    """
    RecoTdmSampleRetriever
    ------
   电商tdm 采样
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名 这个不要了！！！
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info 这个不要了！！！
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名， 这个不要了！！！
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型

    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs

    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr eg. ["ctr_label", "cvr_label"]

    `fix_tag`: [bool], default=false, 修 tag 特征

    `eval_only`: [bool], default=false, 不改变contex（只评估）

    调用示例
    ------
    ``` python
    .retrieve_eshop_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(EshopTdmSampleRetriever(kwargs))
    return self


  def retrieve_tdm_sample(self, **kwargs):
    """
    CommonRecoTdmSampler
    ------
    tdm采样，获取树的内部节点的训练样本 
    因为采样会采到很多重复 id, 目是用左最高 8 位 item_type 来保证不重复，确保 learner上 enable_item_type_and_keysign = true ！！
    （一次采样超过 256 的情况下需要修改下代码）
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info
    
    `source_item_key_var`: [string] 输出的用于特征拷贝的 item key 的 attr 名
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型

    `fix_tag`: [bool], default=false, 修 tag 特征

    `eval_only`: [bool], default=false, 不改变contex（只评估）

    `replace_photo_info_from_tree_server`: [bool], default=false, 用 tree server 返回的 photo_info 代替，to check
    
    调用示例
    ------
    ``` python
    .retrieve_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineNebulaFr",
      tree_name="longview",
      request_type="fullrank",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      source_item_key_var="source_item_key",  # 保存用于 sim 特征拷贝的item key
      sampled_node_duration_var="duration_ms",  # 新增的样本的duration_ms字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(CommonRecoTdmSampleRetriever(kwargs))
    return self

  def retrieve_user_tdm_sample(self, **kwargs):
    """
    CommonRecoUserTdmSampler
    ------
    user tdm采样，获取树的内部节点的训练样本 
    采样后会将 user_attrs broadcast 到 item attr中
    必须指定 save_result_to_common_attr，用以和原始样本区分
    采样生成的样本的 item_key = murmur_hash(node_id) ^ murmur_hash(photo_id)

    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank"

    `enable_sample`: [bool] 是否开启采样

    `enable_eval`: [bool] 是否开启召回率评估
    
    `node_id_attr`: [string] node_id attr name

    `photo_id_attr`: [string] photo_id attr name

    `user_attrs`: [list] user 相关的 attr 集合，新样本会复制这部分 attr

    `item_attrs`: [list] item 相关的 attr 集合，新样本会复制这部分 attr

    `label_attrs`: [list] label 的 attr 集合，真实样本采样后的样本会复制这部分 attr， 随机负样本会生成 0，必须为 Int 或者 Float 类型
    
    `primary_label`: [string] 召回率评估使用的 label attr name

    `photo_slots`: [list] 召回率评估需要的 photo slots

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    调用示例
    ------
    ``` python
    .retrieve_user_tdm_sample(
      kess_service="grpc_UserTDMTreeServerOfflin",
      tree_name="longview",
      request_type="fullrank",
      node_id_attr="user_id",
      photo_id_attr="photo_id",
      user_attrs=["user_id", "user_slot"],
      item_attrs=["photo_id", "photo_slot],
      label_attrs=["longvew"],
      output_level_attr_var="tdm_level"
    )
    ```
    """
    assert "node_id_attr" in kwargs, "node_id_attr cannot be ignore"
    assert "photo_id_attr" in kwargs, "photo_id_attr cannot be ignore"
    assert "save_result_to_common_attr" in kwargs, "save_result_to_common_attr cannot be ignore"
    self._add_processor(UserTdmSampleRetriever(kwargs))
    return self
  
  def copy_ks_sign(self, **kwargs):
    """
    TdmCopySignEnricher
    ------
    从指定的item拷贝指定的slot和sign
  
    参数
    ------
    `copy_slots`: [list] 需要拷贝的特征清单

    `replace`: [bool] 是否替换原来的slot, default=false
    
    `source_attr_var`: [string] 指定的需要拷贝特征的源item key 的 attr 名
    
    `item_slots_var`: [string] 对应extract processor输出的slot attr 名
    
    `item_signs_var`: [string] 对应extract processor输出的sign attr 名
    
    调用示例
    ------
    ``` python
    .copy_ks_sign(
      copy_slots=[346, 347, 348, 349, 350],  # sim 特征清单
      source_attr_var='source_item_key',  # 指定的需要拷贝特征的源item
      item_slots_var='item_slots',  # 对应extract processor输出的slot attr
      item_signs_var='item_signs'  # 对应extract processor输出的sign attr
    )
    ```
    """
    self._add_processor(TdmCopySignEnricher(kwargs))
    return self

  def retrieve_oversea_tdm_sample(self, **kwargs):
    """
    CommonOverseaTdmSampler
    ------
    海外 tdm 采样，获取树的内部节点的训练样本 
    因为采样会采到很多重复 id, 目前是用最高 8 位 item_type 来保证不重复，确保 learner 上 enable_item_type_and_keysign = true ！！
    （一次采样超过 256 的情况下需要修改下代码）
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型
    
    调用示例
    ------
    ``` python
    .retrieve_oversea_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineOversea",
      tree_name="longview",
      request_type="fullrank",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      sampled_node_duration_var="duration_ms",  # 新增的样本的 duration_ms 字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(CommonOverseaTdmSampleRetriever(kwargs))
    return self

  def retrieve_snack_tdm_sample(self, **kwargs):
    """
    CommonSnackTdmSampler
    ------
    海外 snack tdm 采样，获取树的内部节点的训练样本 
    因为采样会采到很多重复 id, 目前是用最高 8 位 item_type 来保证不重复，确保 learner 上 enable_item_type_and_keysign = true ！！
    （一次采样超过 256 的情况下需要修改下代码）
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型
    
    调用示例
    ------
    ``` python
    .retrieve_snack_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineSnack",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ks_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      sampled_node_duration_var="duration_ms",  # 新增的样本的 duration_ms 字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(CommonSnackTdmSampleRetriever(kwargs))
    return self

  def retrieve_ksib_tdm_sample(self, **kwargs):
    """
    KsibTdmSampleRetriever
    ------
    海外 tdm 采样，获取树的内部节点的训练样本 
    因为采样会采到很多重复 id, 目前是用最高 8 位 item_type 来保证不重复，确保 learner 上 enable_item_type_and_keysign = true ！！
    （一次采样超过 256 的情况下需要修改下代码）
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_var`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `neg_sample_playing_time`: [int] 负样本的 playing time, 默认是 1000 ms

    `reco_log_var`: [string] 指定输入的 reco_log 的 attr 名
    
    `item_reco_photo_info_var`: [string] 输出的 reco_photo_info 的 attr 名，对应上一步骤的抽取的 reco_photo_info
    
    `sampled_node_duration_var`: [string] 新增的内部节点样本的 duration_ms 字段的 attr 名，用于计算 label

    `success_flag_attr_var`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `debug`: [int] optional, default=0. 1: 开启 debug

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型

    `bucket_attr`: [string] 输出的 bucket attr 名
    
    调用示例
    ------
    ``` python
    .retrieve_ksib_tdm_sample(
      kess_service="grpc_ksibTDMTreeServerOffline",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_var="effective_view",  # 指定发送采样请求时作为正负样本的依据
      reco_log_var="ksib_reco_log",  # 指定 reco_log
      item_reco_photo_info_var="reco_photo_info",  # 输出的 reco_photo_info，对应上一步骤的抽取的 reco_photo_info
      sampled_node_duration_var="duration_ms",  # 新增的样本的 duration_ms 字段，用于计算 label
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(KsibTdmSampleRetriever(kwargs))
    return self

  def retrieve_common_tdm(self, **kwargs):
    """
    CommonTdmRetriever
    ------
    通用 TDM 召回
    
    参数
    ------
    `reason`: [int] 召回原因
     
    `item_type`: [int] [动态参数]，默认值为 0

    `kess_service`: [string] [动态参数] tdm 召回服务 kess name

    `timeout_ms`: [int] [动态参数] 请求 tdm 服务的超时时间，默认值为 300
    
    `top_k`: [int] [动态参数]，不可缺省

    `tree_name`: [string] [动态参数]

    `kconf_name`: [string] [动态参数] 默认值为空

    `service_group`: [string] tdm 服务的 kess 服务组，默认值为 "PRODUCTION"

    `llsid_attr_name`: [string] 从哪个 common_attr 获取 llsid，llsid 唯一标识一次用户请求

    `user_id_attr_name`: [string] 从哪个 common_attr 获取 user_id
    
    `device_id_attr_name`: [string] 从哪个 common_attr 获取 device_id

    `user_info_attr_name`: [string] 从哪个 common_attr 获取 user_info

    `user_feature_slots_attr_name`: [string] 从哪个 common_attr 获取 user_feature_slots

    `user_feature_signs_attr_name`: [string] 从哪个 common_attr 获取 user_feature_signs

    `save_score_to_attr`: [string] 如果不为空，则将召回的各个 item 的 score 存入该 item_attr

    `traffic_type`: [int] [动态参数] TDM 检索服务上游标识，会被写入 TDMUserInfo 中

    `start_exp_id_attr_name`: [string] 将 TDM 服务回传的 START exp id 参数写入哪个 common attr

    `request_time_attr_name`: [string] optional, 从哪个 common_attr 获取请求时间，用于回放日志请求

    `enable_consistent_hash`: [bool] [动态参数] 是否采用一致性哈希接口请求 TDM 服务，启用后可以将同一个用户的请求打到同一个 TDM 服务实例上，如果 TDM 服务开启了本地 cache 功能则需要设置为 True
    
    调用示例
    ------
    ``` python
    .retrieve_common_tdm(
      reason=100,
      kess_service="grpc_TDMTreeServerOnline",
      top_k=400,
      tree_name="longview",
      kconf_name="reco.model.TDMTreeServer",
      llsid_attr_name="llsid",
      user_id_attr_name="user_id",
      device_id_attr_name="device_id",
      user_feature_slots_attr_name="user_feature_slots",
      user_feature_signs_attr_name="user_feature_signs",
      start_exp_id_attr_name="start_exp_id"
    )
    ```
    """
    self._add_processor(CommonTdmRetriever(kwargs))
    return self

  def retrieve_tdm(self, **kwargs):
    """
    CommonRecoTdmRetriever
    ------
    TDM 召回
    
    参数
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] [动态参数] tdm 召回服务 kess name

    `timeout_ms`: [int] [动态参数] 请求 tdm 服务的超时时间，默认值为 300
    
    `top_k`: [int] [动态参数]，不可缺省

    `tree_name`: [string] [动态参数]

    `kconf_name`: [string] [动态参数] 默认值为空

    `service_group`: [string] tdm 服务的 kess 服务组，默认值为 "PRODUCTION"

    `llsid_attr_name`: [string] 从哪个 common_attr 获取 llsid，llsid 唯一标识一次用户请求

    `user_id_attr_name`: [string] 从哪个 common_attr 获取 user_id
    
    `device_id_attr_name`: [string] 从哪个 common_attr 获取 device_id，不可缺省

    `reco_user_info_attr_name`: [string] 从哪个 common_attr 获取 user_info，主站业务需要设置

    `oversea_user_info_attr_name`: [string] 从哪个 common_attr 获取 user_info，海外 kwai pro 业务需要设置
    
    `snack_user_info_attr_name`: [string] 从哪个 common_attr 获取 user_info，海外 snack 业务需要设置

    `save_score_to_attr`: [string] 如果不为空，则将召回的各个 item 的 score 存入该 item_attr，可缺省

    `traffic_type`: [int] [动态参数] TDM 检索服务上游标识，会被写入 TDMUserInfo 中

    `start_exp_id_attr_name`: [string] 将 TDM 服务回传的 START exp id 参数写入哪个 common attr
    
    调用示例
    ------
    ``` python
    .retrieve_tdm(
      reason=100,
      kess_service="grpc_TDMTreeServerOnline",
      top_k=400,
      tree_name="longview",
      kconf_name="reco.model.TDMTreeServer",
      llsid_attr_name="llsid",
      user_id_attr_name="user_id",
      device_id_attr_name="device_id",
      reco_user_info_attr_name="reco_user_info",
      traffic_type=3,
      start_exp_id_attr_name="start_exp_id"
    )
    ```
    """
    self._add_processor(CommonRecoTdmRetriever(kwargs))
    return self

  def skip_action_list(self, **kwargs):
    """
    SkipActionListEnricher
    ------
    在 [0, max_skip_step) 之间随机跳过 action list 最近的几个
    
    参数
    ------
    `input_user_info_attr`: [string] 输入 user_info 的 attr 名

    `max_skip_step`: [int] [动态参数] 最大跳过的数量
    
    调用示例
    ------
    ``` python
    .skip_action_list(
      input_user_info_attr="user_info",
      max_skip_step=20
    )
    ```
    """
    self._add_processor(SkipActionListEnricher(kwargs))
    return self

  def enrich_long_term_action_list(self, **kwargs):
    """
    LongTermActionListEnricher
    ------
    抽取 user 侧长期 action list

    参数
    ------
    `long_term_action_filter_config_key_name`: 过滤条件 kconf key

    `colossus_resp_attr`: colossus 返回值

    `limit_num_attr`: 最大 list 长度

    `slots_id`: slot 号, 目前长度必须是 6

    `mio_slots_id`: sign 里实际打的 share emb slot 号

    `use_padding`: 是否 padding

    `dedup_pid`: 是否对 colossus pid 去重，默认值是 false

    `output_slot_attr`: output slot attr

    `output_sign_attr`: output sign attr

    `save_pid_to_common_attr`: optional, 保存 pid list 到 common attr
    """
    self ._add_processor(LongTermActionListEnricher(kwargs))
    return self
  
  def gsu_slot_sign_gather(self, **kwargs):
    """
    GsuSlotSignGatherEnricher
    -----
    按照 indices gather topk 对应的 slot 和 sign
    -----
    `input_common_slot_attr`: gsu 原始 slot 输入
    `input_common_sign_attr`: gsu 原始 sign 输入
    `output_item_slot_attr`: gather 之后 item 对应的 topk slot 序列
    `output_item_sign_attr`: gather 之后 item 对应的 topk sign 序列
    `gsu_indices_attr`: indices 序列
    `slots`: 需要处理的 slots 
    `hint_gsu_seq_len`: 用于预分配中间变量的内存
    """
    self._add_processor(GsuSlotSignGatherEnricher(kwargs))
    return self

  def gsu_with_same_aid_rewards(self, **kwargs):
    """
    GsuWithSameAidRewards
    ------
    从用户的长期行为中，检索一定天数内与当前 photo 作者相同的 photo，并累积收益
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `import_aid_item_attr`: [string] aid item attr

    `import_pid_item_attr`: [string] pid item attr

    `import_timestamp_common_attr`: [string] 当前消费的时间戳

    `limit_day`: [int] 回溯 limit_day 天以前的行为

    `save_photo_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_tag_to_attr`: [string]

    调用示例
    ------
    ``` python
    .gsu_with_same_aid_rewards(
      colossus_resp_attr="colossus_resp_attr",
      import_aid_item_attr="source_author_id",
      import_pid_item_attr="source_photo_id",
      import_timestamp_common_attr="source_timestamp",
      limit_day=7,
      save_photo_id_to_attr="retrieval_photo_id",
      save_duration_to_attr="retrieval_duration",
      save_label_to_attr="retrieval_label",
      save_play_time_to_attr="retrieval_play_time",
      save_timestamp_to_attr="retrieval_timestamp",
      save_tag_to_attr="retrieval_tag"
    )
    ```
    """
    self._add_processor(GsuWithSameAidRewards(kwargs))
    return self

  def enrich_debiased_labels(self, **kwargs):
    """
    DebiasedLabelEnricher
    ------
    定义去偏 label

    参数
    ------
    `colossus_resp_attr`: [string] colossus 返回值

    `photo_info_attr`: [string] photo info

    `context_info_attr`: [string] context info

    `duration_ms_attr`: [string] item duration_ms

    `limit_num`: [int][动态参数] colossus limit

    `history_support`: [int][动态参数] 需要的历史行为数

    `bucket_percent_resolution`: [int][动态参数] 分桶回归目标的百分比分辨率

    `global_percent_resolution`: [int][动态参数] 全局回归目标的百分比分辨率

    `num_bucket`: [int][动态参数] label1 input: 桶数

    `bucket_norm_thresh`: [double][动态参数] label3 input: bucket 缩放最大值 percent

    `global_norm_thresh`: [double][动态参数] label4 input: global 缩放最大值 percent

    `bucket_percent_thresh`: [double][动态参数] label5 input: bucket percent 阈值

    `global_percent_thresh`: [double][动态参数] label6 input: global percent 阈值

    `bucket_percent_output_attr`: [string] label1 output: bucket 分位点回归 label

    `global_percent_output_attr`: [string] label2 output: global 分位点回归 label

    `bucket_normed_output_attr`: [string] label3 output: bucket 缩放回归 label

    `global_normed_output_attr`: [string] label4 output: global 缩放回归 label

    `bucket_threshed_output_attr`: [string] label5 output: bucket 阈值分类 label

    `global_threshed_output_attr`: [string] label6 output: global 阈值分类 label

    `valid_output_attr`: [int] 是否是有效 label
    """
    self ._add_processor(DebiasedLabelEnricher(kwargs))
    return self
  
  def recall_reason(self, **kwargs):
    self._add_processor(RecallReasonEnricher(kwargs))
    return self

  def enrich_exptag_quota(self, **kwargs):
    """
    ExptagQuotaEnricher
    ------
    根据某种分布，调整各 exptag 的 quota
  
    参数
    ------
    `import_common_attr`: [string] 要 follow 的 exptag 分布 common 属性

    `registered_exptag`: [string] 要调整 quota 的 exptag list ，逗号分隔，未注册的 exptag 不参与计算，不影响其他 exptag 序号

    `dynamic_exptag`: [string][动态参数] 要调整 quota 的 exptag list ，逗号分隔，未注册的 exptag 不参与计算，不影响其他 exptag 序号，会覆盖默认的 `registered_exptag`

    `decay_ratio_str`: [string][动态参数] 排序前 n 位的 exptag 分别要保留的比例，冒号分隔

    `exptag_quota_attr_prefix`: [string] 要输出的各 exptag quota 的 attr name 前缀，默认为空，效果为 [prefix]quota_[exptag]

    调用示例
    ------
    ``` python
    .enrich_exptag_quota(
      import_common_attr="user_dist_exptag",
      registered_exptag="191,509,1509,3060,3081,4351,4355,4403",
      dynamic_exptag="191,509,1509,3060,3081,4351,4355,4403",
      decay_ratio_str="1.0:0.8:0.7:0.6:0.5:0.5:0.5",
      exptag_quota_attr_prefix=""
    )
    ```
    """
    self._add_processor(ExptagQuotaEnricher(kwargs))
    return self

  def retrieve_by_redis_zstd(self, **kwargs):
    self._add_processor(TdmRecoRedisRegexRetriever(kwargs))
    return self

  def diversify_simply(self, **kwargs):
    """
    SimpleDiversityArranger
    ------
    简单多样性打散
  
    参数
    ------
    `diversity_item_attr_name`: [string] 要调整多样性的 item 属性，比如 hetu_tag

    `max_count_per_cate`: [double][动态参数] 每个属性取值最多保留多少 item，多余的直接丢弃

    调用示例
    ------
    ``` python
    .diversify_simply(
      diversity_item_attr_name='hetu_tag',
      max_count_per_cate="{{return math.floor(return_num * 0.05)}}",
    )
    ```
    """
    self._add_processor(SimpleDiversityArranger(kwargs))
    return self

  def manage_photo_distribution(self, **kwargs):
    """
    PhotoDistributionArranger
    ------
    调整 photo 的分布，多余的 photo 会被截断
  
    参数
    ------
    `dist_to_follow_common_attr_name`: [string] 要 follow 的分布 common 属性，比如 duration_ms list
    
    `dist_item_attr_name`: [string] 要调整分布的 item 属性，比如 duration_ms

    `bucket_scale`: [double][动态参数] 桶内 photo 数量的调整系数，用于防止截断 photo 过多，也可以用于在保分布同时压缩 photo 数量

    `min_remain_ratio_in_bucket`: [double][动态参数] 桶内 photo 保留的最小比例，用于防止截断 photo 过多，优先级高于 bucket_scale
    
    `bucket_string`: [string][动态参数] 属性的分桶阈值

    `enable_dynamic_bucket`: [bool][动态参数] 动态桶开关

    `dynamic_bucket_num`: [int][动态参数] 动态桶数量

    `enable_remove_item_without_attr`: [bool][动态参数] 是否丢弃 attr 没有取值的 photo

    调用示例
    ------
    ``` python
    .manage_photo_distribution(
      dist_to_follow_common_attr_name='duration_ms_in_fr_cache',
      dist_item_attr_name='duration_ms',
      bucket_string='7000,12000,17000,58000,120000,300000,600000',
      bucket_scale=1.2,
      min_remain_ratio_in_bucket=0.6,
      enable_dynamic_bucket=False,
      dynamic_bucket_num=8,
      enable_remove_item_without_attr=False,
    )
    ```
    """
    self._add_processor(PhotoDistributionArranger(kwargs))
    return self

  def reset_reason_by_item_attr(self, **kwargs):
    """
    ResetReasonArranger
    ------
    重设 reason
  
    参数
    ------
    `reason_attr`: [string] reason item attr 字段
    
    `default_reason`: [int] reason attr 取值为空时的回落值

    调用示例
    ------
    ``` python
    .reset_reason_by_item_attr(
      reason_attr='final_reason',
      default_reason=633,
    )
    ```
    """
    self._add_processor(ResetReasonArranger(kwargs))
    return self

  def enrich_user_from_action_log(self, **kwargs):
    """
    ActionLogUserEnricher
    ------
    从 UserActionLog 获取 user_id, 并支持根据是否命中 cache 丢弃 user_id

    参数
    ------
    `from_extra_var`: UserActionLog 的 common attr name

    `simple_user_info_str_attr_name`: 序列化的 SimpleUserInfo 的 common attr name

    `output_user_id_attr_name`: 输出 user_id 的 common attr name

    `user_cache_max_key_num`: 本地 shm cache 最多存放的 key 数量, 默认值 1000000000

    `user_cache_max_shm_mem_g`: 本地 shm cache 分配的内存空间上限，默认值 100G

    `user_cache_expire_second`: 本地 shm cache 的过期时间，默认值 900

    `user_cache_shm_kv_dir`: 本地 shm cache 的存放路径，默认值 /dev/shm/tdm/
    """
    self ._add_processor(ActionLogUserEnricher(kwargs))
    return self

  def kmeans_cluster_action_list(self, **kwargs):
    """
    KmeansClusterActionListEnricher
    ------
    对用户长期行为进行 Kmeans 聚类

    参数
    ------
    `kmeans_niter`: [int][动态参数] 指定聚类时的迭代次数

    `max_points_per_centroid`: [int][动态参数] 指定聚类时参与训练的 item 上限

    `cluster_center_size`: [int][动态参数] 指定聚类数量, 默认值 0，如果值为 0，则 min_cluster_size 起作用

    `min_cluster_size`: [int][动态参数] 各个聚类至少包含的 item 个数，默认值 50

    `kmeans_verbose`: [bool] 是否打印 kmeans 过程详细的日志

    `embedding_dim`: [int] 单个 item 的 embedding 维度，默认值 32

    `user_id_common_attr_name`: [string] user_id 的 attr name

    `action_list_id_common_attr_name`: [string] 用户长期行为序列的 attr name，不可或缺

    `action_list_emb_common_attr_name`: [string] 用户长期行为序列对应的 embedding 的 attr name，不可或缺

    `output_kv_result_common_attr_name`: [string] 将聚类结果按 CommonKvRequest 格式存储并序列化后存入该 attr name

    `cluster_center_size_common_attr_name`: [string] 存放最终生成的聚类个数的 attr name

    `cluster_center_list_emb_common_attr_name`: [string] 存放生成的聚类中心 embedding 的 attr name

    `cluster_action_list_id_result_common_attr_name`: [string] 存放生成的各个聚类包含的 item id 的 attr name

    `cluster_action_index_common_attr_name`: [string] 存放各个聚类中 item index 的 attr name
    
    `cluster_action_num_common_attr_name`: [string] 存放各个聚类中 item num 的 attr name
    
    `output_truncate`: [bool] 输出是否截断
    """
    self ._add_processor(KmeansClusterActionListEnricher(kwargs))
    return self

  def dye_item_with_whitelist(self, **kwargs):
    """
    ItemDyeingEnricher
    ------
    按照白名单对 item 染色，支持 redis 和 kconf, redis 优先

    参数
    ------
    `redis_cluster`: [string] default: "", redis 集群名, 只支持 (k:string, v:string)

    `kconf_key`: [string] default: "", 和 redis_cluster 不能同时为空, 配置 redis 时 kconf 不生效

    `redis_key`: [string] default: "", 使用 redis 配置的必填项

    `redis_timeout_ms`: [int] default: 100, redis timeout

    `item_seperator`: [string] default: "," redis value item 的分割符

    `random_timeout_s_min`: [int] default: 1800, 远程白名单的随机过期时间的最小值

    `random_timeout_s_max`: [int] default: 3600, 远程白名单的随机过期时间的最大值

    `judge_item_attr`: [string] 必填, 测试白名单的 item attr

    `output_dyeing_attr`: [string] 必填, 输出的染色 item attr
    """
    self._add_processor(ItemDyeingEnricher(kwargs))
    return self

  def retrieve_live_tdm_sample(self, **kwargs):
    """
    LiveTdmSampleRetriever
    ------
    直播 tdm 采样，获取树的内部节点的训练样本 
    
    参数
    ------
    `kess_service`: [string] [动态参数] tdm 采样服务 kess name
    
    `tree_name`: [string] [动态参数] tdm 采样服务 tree name
    
    `request_type`: [string] [动态参数] = "fullrank_random"
    
    `primary_label_attr`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `success_flag_attr`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `tdm_sample_type_attr`: [string] optional, default="tdm_sample_type", 输出样本类型

    调用示例
    ------
    ``` python
    .retrieve_ksib_tdm_sample(
      kess_service="grpc_ksibTDMTreeServerOffline",
      tree_name="longview",
      request_type="fullrank_random",
      primary_label_attr="effective_view",  # 指定发送采样请求时作为正负样本的依据
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level"
    )
    ```
    """
    self._add_processor(LiveTdmSampleRetriever(kwargs))
    return self
  
  def base_tdm_sample(self, **kwargs):
    """
    BaseTdmSampleRetriever
    ------
    直播 tdm 采样，获取树的内部节点的训练样本 
    
    参数
    ------
    `kess_service`: [string]  tdm 采样服务 kess name
    
    `tree_name`: [string]  tdm 采样服务 tree name
    
    `request_type`: [string]  = "fullrank_random"
    
    `primary_label_attr`: [string] 指定发送采样请求时作为正负样本的依据，需要在前置环节定义 label
    
    `success_flag_attr_name`: [string] optional, 返回值，采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr`: [string] optional, item attr, 返回样本到叶子节点的采样高度（从 0 开始）, 不等于实际树高度

    `tdm_sample_type_attr`: [string] optional, default="tdm_sample_type", 输出样本类型

    `eval_target_names`: [list] string, optinal, 评估的 label 名称, 默认取 eval_target_label_item_attrs

    `eval_target_label_item_attrs`: [list] string, 评估的 label item attr eg. ["ctr_label", "cvr_label"]

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。
    
    
    `source_key_item_attr_name`: [bool] 选填项, 采样时，以哪个字段作为 key，正负样本 label 上溯用。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。
    
    `duration_ms_item_attr_name`: duration ms。
    
    `context_attrs`: [list] 选填项，一般是 label  相关字段，正负样本上溯时拷贝label用。
    
    `exclude_set_zero_context_attrs`: [list] 选填项， 与context_attrs配合使用，一般context_attrs负样本填0，不过有些属性例如duration ms可以直接使用node里的信息。

    调用示例
    ------
    ``` python
    .base_tdm_sample(
      kess_service = "grpc_TDMTreeServerOffline",
      tree_name = "longview",
      request_type = "fullrank_random",
      primary_label_attr = "effective_view",  # 指定发送采样请求时作为正负样本的依据
      success_flag_attr_name = "tdm_sampling_success",
      output_level_attr_var="tdm_level",
      source_key_item_attr_name ="photo_id",
      eval_target_names=['evtr', 'lvtr', 'click', 'like', 'follow', 'forward', 'sptr', 'comment', 'rs'],
      eval_target_label_item_attrs=['effective_view', 'long_view', 'click', 'like', 'follow', 'forward', 'switch_profile', 'comment', 'realshow'],
      context_attrs=["effective_view", "duration_ms"],
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}], | ["uId"],
      recv_item_attrs = [{"name": "emp_ctr", "as": "ctr"}],, | ["emp_ctr"],
      recv_common_attrs = [{"name": "emp_ctr", "as": "ctr"}],, | ["emp_ctr"],
      debug = 1,
      ------------------以下二选一-----------------------
      ------------------选项一--------------
      # slot_as_attr_name 开启时，使用以下特征获取值
      common_slots = [38, 34, 112],
      item_slots = [26, 128],
      ----------------选项一结束----------------
      ------------------选项二--------------
      # slot_as_attr_name 未开启时，使用如下四个特征
      common_slots_attr_name = ["common_slots", "top_user_embedding_slots"],
      common_signs_attr_name = ["common_signs", "top_user_embedding_signs"],
      item_slots_attr_name = ["item_slots", "top_item_embedding_slots"],
      item_signs_attr_name = ["item_signs", "top_item_embedding_signs"],
      ----------------选项二结束----------------
      # common_slots common_signs item_slots item_signs 可能有多个输入，最终都合并成一下四个
      all_common_slots_attr_name = "all_common_slots",
      all_common_signs_attr_name = "all_common_signs",
      all_item_slots_attr_name = "all_item_slots",
      all_item_signs_attr_name = "all_item_signs",
      llsid_attr_name = "llsid",
      duration_ms_attr_name = "duration_ms",
      exclude_set_zero_context_attrs = ["duration_ms"]
      common_dense_attr_names = ["ctr", "ltr"],
      item_dense_attr_names = ["exp_ctr", "exp_ltr"],
    )
    ```
    """
    self._add_processor(BaseTdmSampleRetriever(kwargs))
    return self
