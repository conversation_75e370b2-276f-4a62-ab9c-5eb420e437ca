#!/usr/bin/env python3
# coding=utf-8
"""
filename: tdm_arrager.py
description: tdm arranger module
author: <EMAIL>
date: 2022-04-26 10:54:00
"""

from ...common_leaf_util import strict_types  #, check_arg, is_number, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class SimpleDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "diversify_simply"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["max_count_per_cate"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["diversity_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class PhotoDistributionArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "manage_photo_distribution"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["bucket_string", "enable_dynamic_bucket", "dynamic_bucket_num",
        "bucket_scale", "min_remain_ratio_in_bucket", "enable_remove_item_without_attr"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["dist_to_follow_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["dist_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class ResetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reset_reason_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_reason"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reason_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs