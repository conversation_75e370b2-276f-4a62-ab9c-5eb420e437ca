import operator
import itertools

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher


class TdmCopySignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_ks_sign"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["source_attr_var"])
    ret.add(self._config["item_slots_var"])
    ret.add(self._config["item_signs_var"])
    return ret


class SkipActionListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "skip_action_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_user_info_attr"])
    ret.update(self.extract_dynamic_params(self._config["max_skip_step"]))
    return ret

class LongTermActionListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_long_term_action_list"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "colossus_resp_attr",
        "limit_num_attr"] if k in self._config)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_slot_attr",
        "output_sign_attr",
        "save_pid_to_common_attr"] if k in self._config)

class GsuSlotSignGatherEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_slot_sign_gather"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config[k] for k in ["gsu_indices_attr"])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "input_common_slot_attr",
        "input_common_sign_attr"] if k in self._config)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_item_slot_attr",
        "output_item_sign_attr",
    ] if k in self._config)


class GsuWithSameAidRewards(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_same_aid_rewards"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_day")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("import_timestamp_common_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_aid_item_attr"))
    ret.add(self._config.get("import_pid_item_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_photo_id_to_attr"))
    ret.add(self._config.get("save_duration_to_attr"))
    ret.add(self._config.get("save_label_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_timestamp_to_attr"))
    ret.add(self._config.get("save_tag_to_attr"))
    return ret


class DebiasedLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_debiased_labels"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set(self._config[k] for k in [
        "colossus_resp_attr",
        "photo_info_attr",
        "context_info_attr",
        "duration_ms_attr"] if k in self._config)
    for k in [
        "limit_num", "history_support",
        "bucket_percent_resolution", "global_percent_resolution",
        "num_bucket", "bucket_norm_thresh", "global_norm_thresh",
        "bucket_percent_thresh", "global_percent_thresh"]:
      if k in self._config:
        ret.update(self.extract_dynamic_params(self._config[k]))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
        "bucket_percent_output_attr",
        "global_percent_output_attr",
        "bucket_normed_output_attr",
        "global_normed_output_attr",
        "valid_output_attr",
        "bucket_threshed_output_attr",
        "global_threshed_output_attr"
        ] if k in self._config)
  
class RecallReasonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "recall_reason_enricher"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("import_item_attr", "")
    return { attr } if attr else set()

class ExptagQuotaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "exptag_quota_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attr = set()
    attr.add(self._config.get("import_common_attr"))
    attr.update(self.extract_dynamic_params(self._config.get("decay_ratio_str")))
    attr.update(self.extract_dynamic_params(self._config.get("dynamic_exptag")))
    return attr

class ActionLogUserEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_from_action_log"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "from_extra_var",
        "simple_user_info_str_attr_name"] if k in self._config)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_user_id_attr_name"] if k in self._config)

class KmeansClusterActionListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kmeans_cluster_action_list"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "user_id_common_attr_name",
        "action_list_id_common_attr_name",
        "action_list_emb_common_attr_name"] if k in self._config)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_kv_result_common_attr_name",
        "cluster_center_size_common_attr_name",
        "cluster_center_list_emb_common_attr_name",
        "cluster_action_list_id_result_common_attr_name",
        "cluster_action_num_common_attr_name",
        "cluster_action_index_common_attr_name"] if k in self._config)


class ItemDyeingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dye_item_with_whitelist"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config[k] for k in [
        "judge_item_attr",
    ] if k in self._config)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in [
        "output_dyeing_attr",
    ] if k in self._config)
