#!/usr/bin/env python3
"""
filename: ug_rtb_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for user growth
author: ji<PERSON><PERSON><EMAIL>
date: 2025-01-19 14:25:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafArranger

class UgRtbRetrieveZigzagSelectArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_rtb_retrieve_zigzag_select"

  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    if "dedup_item_attr" in conf and isinstance(conf["dedup_item_attr"], str) and conf["dedup_item_attr"] != "":
      attrs.add(conf["dedup_item_attr"])

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    # attrs.update(self.extract_dynamic_params(conf["kconf_key"]))
    attrs.update(self.extract_dynamic_params(conf["total_limit"]))
    attrs.update(self.extract_dynamic_params(conf["recall_reason_list"]))
    attrs.update(self.extract_dynamic_params(conf["reason_limit_list"]))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    conf = self._config
    check_arg("total_limit" in conf, "必须配置 total_limit")
    check_arg("recall_reason_list" in conf, "必须配置 recall_reason_list")
    check_arg("reason_limit_list" in conf, "必须配置 reason_limit_list")
    if "default_reason_limit" in conf:
      check_arg(isinstance(conf["default_reason_limit"], int), "default_reason_limit 必须配置为整数")

class UgRtbZigzagSelectByAttrArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_rtb_zigzag_select_by_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    conf = self._config
    if "group_by_attr" in conf and isinstance(conf["group_by_attr"], str) and conf["group_by_attr"] != "":
      attrs.add(conf["group_by_attr"])
    else:
      # 默认按 biz_type 维度进行 zigzag
      attrs.add("biz_type")

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    conf = self._config
    attrs.update(self.extract_dynamic_params(conf["kconf_key"]))
    attrs.update(self.extract_dynamic_params(conf["total_limit"]))
    # 媒体
    media = conf.get("media_attr_name")
    if isinstance(media, str) and media != "":
      attrs.add(media)
    else:
      attrs.add("media")
    # 产品
    product = conf.get("product_attr_name")
    if isinstance(product, str) and product != "":
      attrs.add(product)
    else:
      attrs.add("product")
    # 新回活状态
    action = conf.get("action_attr_name")
    if isinstance(action, str) and action != "":
      attrs.add(action)
    else:
      attrs.add("device_status")
    return attrs

  @strict_types
  def _check_config(self) -> None:
    conf = self._config
    check_arg("total_limit" in conf, "必须配置 total_limit")
    check_arg("kconf_key" in conf, "必须配置 kconf_key")
    check_arg("group_attr_enum_name_map" in conf and isinstance(conf["group_attr_enum_name_map"], dict), "必须配置 group_attr_enum_name_map 且类型为dict")
    enum_name_map = conf["group_attr_enum_name_map"]
    for k, v in enum_name_map.items():
      check_arg(isinstance(k, int) and isinstance(v, str), "group_attr_enum_name_map 必须为 int->str 类型的 map")
