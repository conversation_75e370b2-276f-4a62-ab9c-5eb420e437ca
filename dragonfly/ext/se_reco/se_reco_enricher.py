#!/usr/bin/env python3
"""
filename: se_reco_common_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for se reco
author: <EMAIL>
date: 2022-09-01
"""
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class SeRecoResourceManagerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "init_resource_manager"

class SeRecoAttrFitDoubleListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fit_feature_into_double_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["item_attr_set"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config["item_attr_set"]:
      attrs.add(attr)
    return attrs

class SeRecoAigcAntispamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "aigc_antispam_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["request_json_attr"])
    attrs.add(self._config["content_list_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["antispam_code_attr"])
    attrs.add(self._config["antispam_status_attr"])
    return attrs

class SeRecoLegacyResourceManagerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "init_legacy_resource_manager"

class SeRecoGetAttrByProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_profile"

  @strict_types
  def is_async(self) -> bool:
    return True

  def is_common(self):
    return self._config.get("is_common_attr", False)

  def is_multi_field(self):
    return self._config.get("is_multi_field", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      if self.is_multi_field():
        fields = self._config.get("fields", [])
        for field in fields:
          if isinstance(field, str):
            attrs.add(field)
          else:
            attrs.add(field.get("export_name", field["field_name"]))
      else:
        attrs.add(self._config.get("output_attr_name", ""))
      is_success_attr_name =  self._config.get("is_success_attr_name", "")
      if is_success_attr_name:
        attrs.add(is_success_attr_name)
    return attrs

  @property
  @strict_types
  def enable_auto_merge(self) -> bool:
    return True

  @strict_types
  def auto_merge_config(self, other_config: dict) -> bool:
    if self._config.get("is_multi_field", False) == False:
      return False
    for field in ["is_common_attr", "biz_name", "profile_name", "input_attr_name"]:
      if self._config[field] != other_config[field]:
        return False
    fields_lhs = self._config["fields"]
    fields_rhs = other_config["fields"]
    merged_list = fields_lhs + fields_rhs
    merged_set = []
    field_map = {}
    for field in merged_list:
      if isinstance(field, str):
        field_name = field
        export_name = field
      else:
        field_name = field["field_name"]
        export_name = field.get("export_name", field["field_name"])
      if field_name in field_map and export_name != field_map[field_name]:
        raise Exception(f"{field_name} export name diff {field_map[field_name]} : {export_name}")
      if field_name not in field_map:
        field_map[field_name] = export_name
        merged_set.append(field)
    self._config["fields"] = merged_set
    return True


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      if self.is_multi_field():
        fields = self._config.get("fields", [])
        for field in fields:
          if isinstance(field, str):
            attrs.add(field)
          else:
            attrs.add(field.get("export_name", field["field_name"]))
      else:
        attrs.add(self._config.get("output_attr_name", ""))
      is_success_attr_name =  self._config.get("is_success_attr_name", "")
      if is_success_attr_name:
        attrs.add(is_success_attr_name)
    return attrs

class SeRecoJubaoRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gys_jubao_rank"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_jubao_request_common_attr_name", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_item_id_item_attr_name", "item_id"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_rank_resp_item_attr_name", ""))
    attrs.add(self._config.get("output_rank_channel_peer_item_attr_name", "rank_jubao_channel_peer"))
    return attrs

class SeRecoBoostItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_boost_position"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_item_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_item_attr", ""))
    return attrs

class SeRecoUnfieldProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_ups"

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      if "key_attr_name" in type_config:
        attrs.add(type_config.get("key_attr_name"))
      for feature in type_config.get("import_features", []):
        if "import_name" in feature:
          attrs.add(feature["import_name"])
    return attrs

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for key in ["user", "photo", "query", "device"]:
      type_config = self._config.get(key, {})
      for feature in type_config.get("features", []):
        if "export_name" in feature:
          attrs.add(feature["export_name"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self._is_common_attr else self._get_output_attrs()

class SeRecoCommonProfileV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_profile_v1_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_attr_name", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("err_code_attr_name", ""))
    attrs.append(self._config.get("output_attr_name", ""))
    return set(attrs)

class SeRecoPBListScatterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pb_list_scatter_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("common_pb_list_attr", ""))
    attrs.append(self._config.get("common_key_list_attr", ""))
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("item_key_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("item_pb_attr", "")]
    return set(attrs)

class SeRecoPBListGatherEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pb_list_gather_enricher"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("item_pb_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("common_pb_list_attr", "")]
    return set(attrs)

class SeRecoQuantizationModelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "embedding_quantization_model"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_comon_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_comon_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "")]
    return set(attrs)

class SeRecoGenericAnnEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_se_ann"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = []
    attrs.append(self._config.get("request_id_attr_name", ""))
    attrs.append(self._config.get("input_attr_name", ""))
    attrs.append(self._config.get("custom_request_attr_name", ""))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = []
    prefix = self._config.get("output_attr_prefix", "")
    attrs.append(prefix + "req_id_list")
    attrs.append(prefix + "resp_size_list")
    attrs.append(prefix + "resp_value_list")
    attrs.append(prefix + "resp_score_list")
    return set(attrs)

class SeRecoGZipEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gzip"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "")]
    return set(attrs)

class SeRecoAttrMapScatterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "attr_map_scatter_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = []
    attrs += self._config.get("common_key_list_attrs", [])
    attrs += self._config.get("common_value_list_attrs", [])
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = self._config.get("item_key_attrs", [])
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = self._config.get("item_value_attrs", [])
    return set(attrs)

class SeRecoNormalizationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normalize_text_attr"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_attr", "")]
    return set(attrs)

class SeRecoStringHelperEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "string_helper_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["input_common_attr", "min_ngram", "max_ngram", "bucket", "offset", "include_word", "mode"]:
      if attr in self._config:
        attrs.update(self._config.get(attr))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "")]
    return set(attrs)

class CommonSearchDictInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_dict_init"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "is_dict_inited")]
    return set(attrs)
  
class SeRecoPrefixEditDistanceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "prefix_edit_distance"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_lev_distance")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "")]
    return set(attrs)

class SeRecoTrendLogEnricher(LeafEnricher):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_gys_trend_log"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [
        "client_info",
        "search_session_id",
        "ctx_query",
        "searched_queries_query",
        "searched_queries_timestamp_attr",
        "reco_user_info",
        "referVideoId",
        "live_id",
        "author_id",
        "referPlayDuration",
        "latest_photo_id_v2",
        "latest_photo_id_v2_play_duration",
        "latest_photo_id_v2_source",
        "latest_photo_id_v2_action_base64",
        "recall_sources_after_merge",
        "recall_sources_item_count_after_merge",
    ]
    input_strategy_common_attrs = self._config.get(
        "input_strategy_common_attrs")
    for meta in input_strategy_common_attrs:
      attrs.append(meta.get("attr_name"))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr",
                              "")
             ]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [
        "item_id",
        "item_score",
        "item_source",
        "item_recall_score",
        "rank_score",
    ]
    item_prerank_score = self._config.get("item_prerank_score", "")
    if item_prerank_score:
        attrs.append(item_prerank_score)
    url = self._config.get("url", "")
    if url:
        attrs.append(url)
    icon_color = self._config.get("icon_color", "")
    if icon_color:
        attrs.append(icon_color)
    icon_text = self._config.get("icon_text", "")
    if icon_text:
        attrs.append(icon_text)
    item_recommend_reason = self._config.get("item_recommend_reason", "")
    if item_recommend_reason:
        attrs.append(item_recommend_reason)
    input_jv_item_attrs = self._config.get("input_jv_item_attrs")
    for meta in input_jv_item_attrs:
      attrs.append(meta.get("attr_name"))
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = []
    return set(attrs)

class SeRecoKvCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kv_cache_op"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    all_config = self._config.get("config")
    if not all_config:
      return set([])
    attrs = [c.get("key_attr", "") for c in all_config if c.get("is_common", True)]
    if "write" == self._config.get("op", "read"):
      attrs += [c.get("value_attr", "") for c in all_config if c.get("is_common", True)]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    all_config = self._config.get("config")
    if not all_config:
      return set([])
    attrs = []
    if "read" == self._config.get("op", "read"):
      attrs += [c.get("value_attr", "") for c in all_config if c.get("is_common", True)]
      attrs += [c.get("hit_attr", "") for c in all_config if c.get("is_common", True)]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    all_config = self._config.get("config")
    if not all_config:
      return set([])
    attrs = [c.get("key_attr", "") for c in all_config if not c.get("is_common", True)]
    if "write" == self._config.get("op", "read"):
      attrs += [c.get("value_attr", "") for c in all_config if not c.get("is_common", True)]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    all_config = self._config.get("config")
    if not all_config:
      return set([])
    attrs = []
    if "read" == self._config.get("op", "read"):
      attrs += [c.get("value_attr", "") for c in all_config if not c.get("is_common", True)]
      attrs += [c.get("hit_attr", "") for c in all_config if not c.get("is_common", True)]
    return set(attrs)

class SeRecoJsonGenerateWithAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "json_generate_with_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("common_attrs"):
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common", True):
      attrs.add(self._config.get("export_json_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attrs"):
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common", True):
      attrs.add(self._config.get("export_json_attr", ""))
    return attrs

class SeRecoQueryNegativeScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_negative_score"

  @strict_types
  def _check_config(self) -> None:
    check_arg("bucket_attr_name" in self._config, "bucket_attr_name 必须配置")
    check_arg("input_str_attr_name" in self._config, "input_str_attr_name 必须配置")
    check_arg("output_score_attr_name" in self._config, "output_score_attr_name 必须配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("bucket_attr_name"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_str_attr_name", ""))
    return attrs

  @property
  @strict_types
  def out_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_score_attr_name", ""))
    return attrs

class SeRecoDictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_dict"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config.get("input_common_attr")])
    if len(attrs) == 0 or attrs == {None}:
      attrs.update(self.extract_dynamic_params(self._config.get("random_sample_count")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([self._config.get("output_common_attr")])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set([self._config.get("input_item_attr")])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([self._config.get("output_item_attr")])
    return attrs


class SeRecoJubaoKVRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gys_jubao_kv_rank"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    input_common_attrs = self._config.get("input_common_attrs")
    for meta in input_common_attrs:
      attrs.add(meta.get("attr_name"))
    if "input_query_common_attr_name" in self._config:
      attrs.add(self._config.get("input_query_common_attr_name"))
    if "input_source_common_attr_name" in self._config:
      attrs.add(self._config.get("input_source_common_attr_name"))
    if "input_photo_id_common_attr_name" in self._config:
      attrs.add(self._config.get("input_photo_id_common_attr_name"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    input_item_attrs = self._config.get("input_item_attrs")
    for meta in input_item_attrs:
      attrs.add(meta.get("attr_name"))
    attrs.add(self._config.get("input_item_id_item_attr_name"))
    if "input_item_recall_reason_item_attr_name" in self._config:
      attrs.add(self._config.get("input_item_recall_reason_item_attr_name"))
    if "input_item_source_item_attr_name" in self._config:
      attrs.add(self._config.get("input_item_source_item_attr_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_rank_resp_item_attr_name", ""))
    if "output_rank_channel_peer_item_attr_name" in self._config:
      attrs.add(self._config.get("output_rank_channel_peer_item_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_rank_debug_info_attr_name"))
    return attrs

class SeRecoGetIdMappingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_id_mapping"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if("save_mapping_id_to_attr" in self._config):
      attrs.add(self._config.get("save_mapping_id_to_attr", ""))
    return attrs


class SeRecoAuthorUpdatePhotosEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_author_update_photos"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config.get("input_click_photos_common_attr_name")])
    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set([self._config.get("input_author_id_item_attr_name", "item_author_id")])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([self._config.get("output_update_photos_item_attr_name", "item_update_photos")])
    return attrs


class SeRecoQueryNomalize2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "query_normalize2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_common_attr")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr")]
    return set(attrs)

class StreamingModelEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    print(config)
    self.__sub_flow = config.pop("sub_flow") if "sub_flow" in config else None
    super().__init__(config)
    self._auto_detect_pass_common_attrs = "pass_common_attrs" not in self._config
    self._auto_detect_pass_item_attrs = "pass_item_attrs" not in self._config
    self._auto_detect_merge_common_attrs = "merge_common_attrs" not in self._config
    self._auto_detect_merge_item_attrs = "merge_item_attrs" not in self._config
  
  @strict_types
  def get_type_alias(self) -> str:
    return "streaming_model_with_subflow"
  
  @strict_types
  def get_sub_flow(self):
    return self.__sub_flow

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("prompt_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("random_seed_attr")))
    # attrs.update(self._config.get("pass_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("answer_attr"))
    # attrs.update(self._config.get("merge_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    # attrs.update(self._config.get("pass_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    # attrs.update(self._config.get("merge_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if self.__sub_flow is not None:
      for processor in self.__sub_flow._processors:
        check_arg(self.item_table not in processor.output_item_tables, f"sub_flow {self.__sub_flow.name} 中不可暴汗 retriever类型 processor: {processor.name or processor.get_type_alias()}")

class SeRecoCheckMatchUserNameEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "check_match_user_name"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_normalized_query_common_attr"))
    attrs.add(self._config.get("input_query_pinyin_common_attr"))
    if self._config.get("is_common_attr", False):
      attrs.add(self._config.get("input_normalized_user_name_attr"))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return set([self._config.get("output_match_pos_attr")])
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return set([self._config.get("input_normalized_user_name_attr")])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return set([self._config.get("output_match_pos_attr")])
    return set()

class SeRecoEmbeddingScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_embedding_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_embedding_attr"))
    attrs.add(self._config.get("input_query_list_attr"))
    attrs.add(self._config.get("max_embedding_num_attr"))
    attrs.add(self._config.get("decay_factor_attr"))
    attrs.add(self._config.get("threshold_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_output_num")))
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_item_id_attr"))
    attrs.add(self._config.get("input_item_embedding_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_query_list_attr"))
    output_score_list_attr =  self._config.get("output_score_list_attr", "")
    if output_score_list_attr:
      attrs.add(output_score_list_attr)
    output_gene_info_attr =  self._config.get("output_gene_info_attr", "")
    if output_gene_info_attr:
      attrs.add(output_gene_info_attr)
    return attrs

class SeRecoMapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_map"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_list_attr_name"))
    attrs.add(self._config.get("value_list_attr_name"))
    if self._config.get("is_common_attr", False):
      attrs.add(self._config.get("input_attr_name"))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return set([self._config.get("output_attr_name")])
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return set([self._config.get("input_attr_name")])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return set([self._config.get("output_attr_name")])
    return set()

class SeRecoWstringLengthEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "wstring_length"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_common_attr")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr")]
    return set(attrs)


class SeRecoItemGenericGrpcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_by_generic_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config["request_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["response_attr"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return True

class SeRecoShardGenericGrpcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_generic_shard_grpc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config["request_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["response_attr"])
    return attrs

class SeRecoSpellingCorrectionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "spelling_correction_from_kconf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_item_attr"))
    return attrs


class SeRecoIntersectionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "is_intersection"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(
        self._config.get("compare_to", ""), check_format=False))
    if self._config.get("is_common_attr", True):
      attrs.add(self._config.get("input_attr_name"))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", True):
      return set([self._config.get("output_attr_name")])
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", True):
      return set([self._config.get("input_attr_name")])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", True):
      return set([self._config.get("output_attr_name")])
    return set()

class SeRecoIndexAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_index_attr"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr")]
    return set(attrs)

class SeRecoTableUDFEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_table_udf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    common_attrs = self._config.get("common_attrs", [])
    input_fields = self._config.get("input_fields", [])
    for input_field in input_fields:
      if input_field in common_attrs:
        attrs.add(input_field)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    common_attrs = self._config.get("common_attrs", [])
    input_fields = self._config.get("input_fields", [])
    for input_field in input_fields:
      if input_field not in common_attrs:
        attrs.add(input_field)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", False):
      attrs = set(self._config.get("output_fields", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", False):
      attrs = set(self._config.get("output_fields", []))
    return attrs


class SeRecoGoodsQrUDFEnricher(SeRecoTableUDFEnricher):
  pass


class SeRecoGetValueFromListByIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_value_from_list_by_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config.get("input_common_attr", "")])
    attrs.update(self.extract_dynamic_params(self._config.get("index")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    output = self._config.get("output_common_attr", "")
    if isinstance(output, str):
      return set([output])
    else:
      return set(output)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output = self._config.get("output_item_attr", "")
    if isinstance(output, str):
      return set([output])
    else:
      return set(output)


class SeRecoUrlEncodeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "url_encode"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = [self._config.get("input_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "")]
    return set(attrs)


class SeRecoHotPersonListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_hot_preson_list"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in [
        "author_id_list_attr_name",
        "click_cnt_list_attr_name",
        "view_cnt_list_attr_name",
        "top_author_id_list_attr_name",
        "top_view_cnt_list_attr_name"
    ]:
      attr_name = self._config.get(name, "")
      if attr_name != "":
        attrs.add(attr_name)
    return set(attrs)
  
class SeRecoRedisToolEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_generic_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr = ["redis_key"]
    attrs_map = {
      "ZSize":[],
      "ZAdd":["redis_value", "redis_score"],
      "ZRemRangeByIndex":["start_idx", "end_idx"],
      "ZGetMembersRevByIndex":["start_idx", "end_idx"],
      "ListPushRight":["redis_value"],
      "ListLength":[],
      "ListRange":[],
      "ListPopRight":[],
      "ListPopLeft":[]
    }

    func_name = self._config.get("func_name", "default")
    dynamic_params = attrs_map.get(func_name, []) + common_attr

    attrs = set()
    [attrs.update(self.extract_dynamic_params(self._config.get(param))) for param in dynamic_params]

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs_map = {
      "ZSize":["output_attr_name"],
      "ZAdd":[],
      "ZRemRangeByIndex":[],
      "ZGetMembersRevByIndex":["output_attr_name", "output_attr_name_1"],
      "ListPushRight":[],
      "ListLength":["output_attr_name"],
      "ListRange":["output_attr_name"],
      "ListPopRight":[],
      "ListPopLeft":[]
    }

    func_name = self._config.get("func_name", "default")
    dynamic_params = attrs_map.get(func_name, [])

    attrs = set()
    attrs.update([self._config.get(param) for param in dynamic_params])

    return attrs

class SeRecoGetAttrByNameFromModelServingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_value_from_model_serving"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config["from_extra_var"]])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    confs = self._config.get("ex_attrs", [])
    for conf in confs:
      attrs.add(conf["output_attr"])
    return attrs


class SeRecoProfile2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_old_profile"

  @strict_types
  def is_async(self) -> bool:
    return True

  def is_common(self):
    return self._config.get("is_common_attr", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      fields = self._config.get("fields", [])
      for field in fields:
        if isinstance(field, str):
          attrs.add(field)
        else:
          attrs.add(field.get("export_name", field["field_name"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config.get("input_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      fields = self._config.get("fields", [])
      for field in fields:
        if isinstance(field, str):
          attrs.add(field)
        else:
          attrs.add(field.get("export_name", field["field_name"]))
    return attrs

class SeRecoSegmentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "segment_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr", "query"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = [self._config.get("output_common_attr", "query_segment_result")]
    return set(attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "")]
    return set(attrs)

class SeRecoBertEmbEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bert_emb_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_ids_attr", "input_ids"))
    attrs.add(self._config.get("input_pos_attr", "input_pos"))
    attrs.add(self._config.get("input_mask_attr", "input_mask"))
    attrs.add(self._config.get("input_seg_attr", "input_seg"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_bert_emb_attr", "output_bert_emb"))
    return attrs
  
class SeRecoBertTokenizerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bert_tokenizer_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_segment_text_attr", "segment_text"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_ids_attr", "tokenizer_output_ids"))
    attrs.add(self._config.get("output_pos_attr", "tokenizer_output_pos"))
    attrs.add(self._config.get("output_mask_attr", "tokenizer_output_mask"))
    attrs.add(self._config.get("output_seg_attr", "tokenizer_output_seg"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = [self._config.get("input_item_segment_text_attr", "")]
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_item_ids_attr", ""))
    attrs.add(self._config.get("output_item_pos_attr", ""))
    attrs.add(self._config.get("output_item_mask_attr", ""))
    attrs.add(self._config.get("output_item_seg_attr", ""))
    return attrs

class SeRecoDedupSegmentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "segment_dedup_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_query_attr", "query"))
    attrs.update(self.extract_dynamic_params(self._config.get("use_process_v3")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_item_source_attr", "item_source"))
    attrs.add(self._config.get("input_item_id_attr", "item_id"))
    return set(attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [self._config.get("output_item_attr", "segment_dedup_is_filter")]
    return set(attrs)

class SeRecoProtobufVisializeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pb_visualize_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common", True):
      attrs.add(self._config.get("pb_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common", True):
      attrs.add(self._config.get("pb_attr", ""))
    return set(attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common", True):
      attrs.add(self._config.get("output_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common", True):
      attrs.add(self._config.get("output_attr", ""))
    return set(attrs)

class SeRecoGetAttrByNameFromDragonResponseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_value_from_dragon_response"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set([self._config["from_extra_var"]])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    confs = self._config.get("response_attr", [])
    for conf in confs:
      attrs.add(conf["output_attr"])
    return attrs

class SeRecoUnifiedFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_ufs"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["output_attrs"]:
      attrs.add(attr)
    return set(attrs)

class SeRecoUploadImageToBlobEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "upload_image_to_blob"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("image")
    attrs.add("image_id")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return set(attrs)

class SeRecoCommonInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_basic_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "adjusted_request_id_to_attr" in self._config:
      attrs.add(self._config.get("ori_request_id_attr"))
      attrs.add(self._config.get("query_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    fields = ["save_is_debug_to_attr", "adjusted_request_id_to_attr"]
    return set([self._config[v] for v in fields if v in self._config])

class SeRecoBaseExecutorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ziya_base_executor"
  
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ZiyaContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        not_export_attr = self._config.get("not_export_attr", False)
        if not not_export_attr:
          attrs.add("ZiyaContextAttrKey")
          attrs.add(self._config.get("exit_flow_attr"))
        return attrs

class SeRecoDragonAttrsToZiyaEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "dragon_attrs_to_ziya"

    @strict_types
    def is_async(self) -> bool:
        return False
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["client_info", 
                     "clientRealActions", "query", "rawQuery",
                     "source", "prerank_recall_samples", "city_without_end",
                     "matched_merchant_live_author_name_list",
                     "matched_ads_live_author_name_list", "brand_name_list",
                     "pb_data_source_set_serialized_str", "sug_sub_query_list",
                     "sug_sub_query_score_list", "refer_query", "refer_photo_query",
                     "matched_live_author_name_list", "matched_live_author_id_list",
                     "ad_ecpm_calc_ad_recommend_response",
                     "requestCount", "logLevel", "debugLevel", "extraInfoList", "prefix_v4emb"
                     ])
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["item_id", "item_author_id", "item_score", "item_source", "item_recall_reason", "item_upload_timestamp"])
        for input_attr in self._config.get("input_extra_info_item_attrs"):
          attrs.add(input_attr)
        for input_attr in self._config.get("input_extra_params_item_attr"):
          attrs.add(input_attr)
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ZiyaContextAttrKey")
        attrs.add(self._config.get("exit_flow_attr"))
        return attrs

class SeRecoZiyaContextEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "enrich_ziya_context"

    @strict_types
    def is_async(self) -> bool:
        return False
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["sugg_duration_opt_2024_q3_reverse"])
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ZiyaContextAttrKey")
        return attrs

class SeRecoDragonAttrsToZiyaV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "dragon_attrs_to_ziya_v2"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["client_info",
                     "clientRealActions", "query", "rawQuery",
                     "source", "prerank_recall_samples", "city_without_end",
                     "matched_merchant_live_author_name_list",
                     "matched_ads_live_author_name_list", "brand_name_list",
                     "pb_data_source_set_serialized_str", "sug_sub_query_list",
                     "sug_sub_query_score_list", "refer_query", "refer_photo_query",
                     "matched_live_author_name_list", "matched_live_author_id_list",
                     "ad_ecpm_calc_ad_recommend_response",
                     "requestCount", "logLevel", "debugLevel", "extraInfoList", "prefix_v4emb", "sugg_duration_opt_2024_q3_reverse", "ZiyaContextAttrKey",
                     "sug_keyword_user_intent_prob", "degrade_strategy_executors", "sug_live_user_rely_level", "intent_goods_level", "buyer_type", 
                     "sug_launch_time", "user_active", "prefix_del_feedback_from_redis", "before_prerank_items", "before_rank_items", "after_rank_items",
                     "recent_searched_query_timestamp_first", "recent_searched_query_first", "bprk_cnt", "bfrk_cnt", "afrk_cnt",
                     "last_search_time_diff", "app_launch_timestamp", "cross_ssid_info_from_redis", "sug_relevance_items_string",
                     "search_user_30d_degree", "age_segment", "user_active_degree", "user_valid_intent_list"
                     ])
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["item_id", "item_author_id", "item_score", "item_source", "item_recall_reason", "item_upload_timestamp"])
        for input_attr in self._config.get("input_extra_info_item_attrs"):
          attrs.add(input_attr)
        for input_attr in self._config.get("input_extra_params_item_attr"):
          attrs.add(input_attr)
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ZiyaContextAttrKey")
        attrs.add(self._config.get("exit_flow_attr"))
        return attrs
    
class SeRecoPlatformDebugLogEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "se_platform_debug_log_enricher"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("debug_log_ptr")
        confs = self._config.get("common_attrs", [])
        for conf in confs:
          attrs.add(conf)
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        confs = self._config.get("item_attrs", [])
        for conf in confs:
          attrs.add(conf)
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("debug_log_ptr")
        return attrs
