#!/usr/bin/env python3
# coding=utf-8
"""
filename: se_reco_common_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, se reco api mixin
author: <EMAIL>
date: 2022-09-06 09:50:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .se_reco_arranger import *
from .se_reco_enricher import *
from .se_reco_retriever import *
from .se_reco_mixer import *
from .se_reco_observer import *
from ..se_reco_predict.se_reco_predict_enricher import SeRecoAttrFitDoubleListEnricher as SeRecoAttrFitDoubleListEnricherNew
from ..common.common_api_mixin import *

class SeRecoApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 搜索推荐服务相关的 Processor 接口, 主要应用于搜索 Query 推荐的各个场景：包括搜索首页猜搜、框词、Sugg、Subtag、RS、搜索气泡、评论区飘蓝等, 由搜索推荐团队负责维护
  """

  def init_resource_manager(self, **kwargs):
    """
    SeRecoResourceManagerEnricher
    ------
    搜索推荐服务资源初始化组件, 负责初始化一些全局资源: DataClientWrapper

    参数配置
    ------
    `init_list`: [string] 必填 需要初始化client的类型集合: redis,grpc,kiwi

    `cluster_set`: [string] 必填 需要初始化client的cluster集合

    `caller_name`: [string] 必填 调用clutser的名字

    `size`: [int] 选填 client大小，默认为10
    
    `timeout`: [int] 选填 连接超时, 默认为40

    调用示例
    ------
    ``` python
    .init_resource_manager(
      init_list = "redis,grpc",
      cluster_set = "rssubtag,searchRs",
      caller_name = "grpc_seRecoSubtagDragonDebug")
    ```
    """
    self._add_processor(SeRecoResourceManagerEnricher(kwargs))
    return self

  def merge_by_quotas(self, **kwargs):
    """
    SeRecoGeneralMergeArranger
    ------
    多路召回合并组件：根据配置的 quota 对各个队列的召回结果进行合并

    参数配置
    ------
    `enable_fill`: [bool] 选填 是否开启填充，默认为false

    `enable_dedup`: [bool] 选填 是否开启去重，默认为true

    `backup_threshold`: [int] 选填 在 merge 结果不足该值后再填充 weight为0 的值，默认值为0

    `count`: [int] 动态参数， 选填 结果大小，默认为 400，

    `item_id_str_attr_name`: [string] 必填，默认为 item_id_str
    
    `item_author_id_attr_name`: [string] 选填，用来对用户去重，默认为空

    `debug`: [bool] 选填 是否开启debug日志，默认为false

    `quotas`: [dict] 必填 各个队列的quota, 每个配置包含name、weight、step。其中name表示召回名称, weight表示权重，step表示每次合并个数，合并时根据item_attr(name="reason_str")获取item的召回名称

    调用示例
    ------
    ``` python
    .merge_by_quotas(
      enable_fill = True,
      enable_dedup = True,
      count = 300,
      quotas = [
            {
                "id": 10001,
                "name": "multi_intent_recall",
                "weight": 1,
                "step": 1
            },
            {
                "id": 10002,
                "name": "subtag_base_recall",
                "is_force_insert": True,
                "weight": 0.5,
                "step": 1
            },
            {
                "id": 10003,
                "name": "subtag_base_recall_v2",
                "weight": 0,
                "step": 1
            }
      ])
    ```
    """
    self._add_processor(SeRecoGeneralMergeArranger(kwargs))
    return self

  def se_reco_base_retrieve_from_redis(self, **kwargs):
    """
    SeRecoBaseKVRetriever
    ------
    搜索推荐服务的基础召回组件：使用DataClientWrapper进行召回

    参数配置
    ------
    `key_source`: [string] 选填 可选值：query,user_id,photo_id, 默认为user_id

    `query_attr_name`: [string] 选填 query的attr名字, 默认为ctx_query

    `photo_id_list_attr_name`: [string] 选填 photo_id_list的attr名字

    `is_use_local_cache`: [bool] 选填 是否开启localcache 默认为true

    `cluster`: [string] 必填 redis集群名称

    `key_prefix`: [string] 必填 key前缀

    `timeout_ms`: [int] 选填 超时时间，默认50ms

    `result_num`: [int] 必填 结果数量

    `single_recall_num`: [int] 必填 单队列数量

    `reason`: [string] 必填 召回名称

    `debug`: [bool] 选填 是否开启debug日志，默认为false

    `item_id_str_attr_name`: [string] 选填 候选的item attr名称， 默认为item_id

    `author_id_attr_name`: [string] 选填 候选的作者item attr名称， 默认为author_id

    `reason_str_attr_name`: [string] 选填 候选的召回名称item attr名称， 默认为reason_str

    `recall_reason_attr_name`: [string] 选填 候选的召回理由item attr名称， 默认为recall_reason

    调用示例
    ------
    ``` python
    .se_reco_base_retrieve_from_redis(
      cluster="rssubtag",
      timeout_ms=30,
      key_prefix="subtag_sug_recall_",
      query_attr_name="ctx_query",
      result_num=50,
      reason="sug_recall",
    )
    ```
    """
    self._add_processor(SeRecoBaseKVRetriever(kwargs))
    return self

  def retrieve_subtag_from_redis(self, **kwargs):
    """
    SubtagKVRetriever 
    ------
    Subtag服务的召回组件：访问redis进行KV召回, value的格式为StoreValue

    参数配置
    ------
    `cluster`: [string] 必填 redis集群名称

    `key_prefix`: [string] 选填 key的前缀, 默认为空

    `timeout_ms`: [int] 选填 超时时间，默认20ms

    `result_num`: [int] 选填 结果数量，默认50

    `io_thread_num`: [int] 选填 redis pipeline client的io thread数量，默认为4

    `is_need_replcation`: [bool] 选填，默认为false

    `query_attr_name`: [string] 选填 query的attr name，默认为ctx_query

    `reason`: [string] 必填 召回名称

    `debug`: [bool] 选填 是否开启debug日志，默认为false

    `item_id_str_attr_name`: [string] 选填 item_id_str的attr name，默认为item_id_str

    `reason_str_attr_name`: [string] 选填 reason_str的attr name，默认为reason_str

    `map_words_attr_name`: [string] 选填 map_words的attr name，默认为map_words

    `source_attr_name`: [string] 选填 source的attr name，默认为source

    `source_list_attr_name`: [string] 选填 source_list的attr name，默认为source_list

    调用示例
    ------
    ``` python
    .retrieve_subtag_from_redis(
      cluster="rssubtag",
      timeout_ms=50,
      key_prefix="query_subtag_v5_",
      query_attr_name="ctx_query",
      result_num=300,
      reason="subtag_base_recall",
      debug=False,
    )
    ```
    """
    self._add_processor(SubtagKVRetriever(kwargs))
    return self

  def retrieve_subtag_by_dataclient(self, **kwargs):
    """
    SubtagKVDataClientRetriever 
    ------
    Subtag服务的召回组件：使用DataClientWrapper进行召回

    参数配置
    ------
    `key_source`: [string] 选填 可选值：query,user_id,photo_id, 默认为query

    `query_attr_name`: [string] 选填 query的attr名字, 默认为ctx_query

    `photo_id_list_attr_name`: [string] 选填 photo_id_list的attr名字

    `is_use_local_cache`: [bool] 选填 是否开启localcache 默认为true

    `cluster`: [string] 必填 redis集群名称

    `key_prefix`: [string] 必填 key前缀

    `timeout_ms`: [int] 选填 超时时间，默认50ms

    `result_num`: [int] 必填 结果数量

    `single_recall_num`: [int] 必填 单队列数量

    `reason`: [string] 必填 召回名称

    `debug`: [bool] 选填 是否开启debug日志，默认为false

    `item_id_str_attr_name`: [string] 选填 item_id_str的attr name，默认为item_id_str

    `reason_str_attr_name`: [string] 选填 reason_str的attr name，默认为reason_str

    `map_words_attr_name`: [string] 选填 map_words的attr name，默认为map_words

    `source_attr_name`: [string] 选填 source的attr name，默认为source

    `source_list_attr_name`: [string] 选填 source_list的attr name，默认为source_list

    调用示例
    ------
    ``` python
    .retrieve_subtag_by_dataclient(
      cluster="rssubtag",
      timeout_ms=30,
      key_prefix="subtag_sug_recall_",
      query_attr_name="ctx_query",
      result_num=50,
      reason="sug_recall",
      debug=False,
    )
    ```
    """
    self._add_processor(SubtagKVDataClientRetriever(kwargs))
    return self

  def retrieve_subtag_by_multi_intent(self, **kwargs):
    """
    SubtagMultiIntentRetriever 
    ------
    Subtag多意图KV召回

    参数配置
    ------
    `key_source`: [string] 选填 可选值：query,user_id,photo_id, 默认为query

    `query_attr_name`: [string] 选填 query的attr名字, 默认为ctx_query

    `multi_intent_attr_name`: [int] 选填 multi_intent的attr名字, 默认为multi_intent

    `kconf_multi_intent_path`: [string] 选填 干预kconf，默认为se.ziyasort.subtag_multiintent_intervene_map

    `is_use_local_cache`: [bool] 选填 是否开启localcache 默认为true

    `cluster`: [string] 必填 redis集群名称

    `key_prefix`: [string] 必填 key前缀

    `timeout_ms`: [int] 选填 超时时间，默认50ms
    
    `result_num`: [int] 必填 结果数量

    `reason`: [string] 必填 召回名称

    `debug`: [bool] 选填 是否开启debug日志，默认为false

    `item_id_str_attr_name`: [string] 选填 item_id_str的attr name，默认为item_id_str

    `reason_str_attr_name`: [string] 选填 reason_str的attr name，默认为reason_str

    `map_words_attr_name`: [string] 选填 map_words的attr name，默认为map_words

    `source_attr_name`: [string] 选填 source的attr name，默认为source

    `source_list_attr_name`: [string] 选填 source_list的attr name，默认为source_list

    `link_attr_name`: [string] 选填 link的attr name，默认为link

    调用示例
    ------
    ``` python
      .retrieve_subtag_by_multi_intent(
        cluster="searchRs",
        timeout_ms=20,
        key_prefix="subtag_multi_intent_",
        query_attr_name="ctx_query",
        reason="multi_intent_recall",
        result_num=50,
        kconf_multi_intent_path="se.ziyasort.subtag_multiintent_intervene_map",
        debug=False,
      )
    ```
    """
    self._add_processor(SubtagMultiIntentRetriever(kwargs))
    return self

  def fit_feature_into_double_list(self, **kwargs):
    """
    SeRecoAttrFitDoubleListEnricher
    ------
    将common/item特征转换成double_list以适配uni_predict_fused
    注意：该processor已经迁移至se_reco_predict目录下，本目录下的该processor已经废弃，新业务接入请使用se_reco_predict，老业务依然兼容，无需修改
    调用示例
    ------
    ``` python
      .fit_feature_into_double_list(
        common_attr_set=[]
        item_attr_set=[]
      )
    ```
    """
    self._add_processor(SeRecoAttrFitDoubleListEnricherNew(kwargs))
    return self

  def user_to_query(self, **kwargs):
    """
    SeRecoUserToQueryRetriever
    ------
    使用一些用户 ID 作为 key 从 profile 平台进行召回

    参数配置
    ------
    `biz`: [string] 必填, profile 的 biz

    `profile`: [string] 必填, profile 的名称 

    `timeout_ms`: [int] 选填 超时时间，默认100ms

    `save_in_seperate_table`: [bool] 选填，是否将召回填充到不同的 table 中，默认为 True

    `confs`: [dict] 必填，每一项代表一路召回
    - id: [int] 召回的ID，必须自行保证全局唯一
    - name: [string] 召回名称
    - field: [string] profile 的字段名称
    - keys: [动态参数] key 的集合
    - num: [int] 召回数量
    - condition: [动态参数] 控制是否执行这路召回

    调用示例
    ------
    ``` python
    .user_to_query(
        biz = "growth",
        profile = "Recall",
        timeout_ms = 100,
        save_in_seperate_table = True, 
        confs = [
            {
                "id" : 10001,
                "name" : "author2query",
                "field" : "rw_a2q_recall_v71",
                "keys" : "{{author_ids}}",
                "num" : 20,
                "condition" : "{{return gys_author2query_exp == 0}}"
            },
            {
                "id" : 10002,
                "name" : "new_author2query",
                "field" : "new_rw_a2q_recall_v71",
                "keys" : "{{author_ids}}",
                "num" : 40,
                "condition" : "{{return new_author2query == 0}}"
            },
            {
                "id" : 10003,
                "name" : "mix_fil_item_cf_v71",
                "field" : "gys_u2q_itemcf",
                "keys" : "{{user_id_str_list}}",
                "num" : 120,
                "condition" : "{{return gys_interest_filter == 3}}"
            },
            {
                "id" : 10004,
                "name" : "mix_fil_fre_searched_query",
                "field" : "ecf_his_searched_query_v71",
                "keys" : "{{user_id_str_list}}",
                "num" : 60
            },
        ]
    )
    ```
    """
    self._add_processor(SeRecoUserToQueryRetriever(kwargs))
    return self

  def profile_recall(self, **kwargs):
    """
    SeRecoProfileRecallMixer 
    ------
    使用 profile 平台进行召回, 结果保存到不同的 table 中(可配置)

    注意：当前支持的 PB 类型为: `::growth::profile::proto::RecallFeature` 和 `::ks::search::recall_profile::RetrievalInfo`

    参数配置
    ------
    `biz`: [string] 必填, profile 的 biz

    `profile`: [string] 必填, profile 的名称 

    `timeout_ms`: [int] 选填 超时时间，默认100ms

    `is_recall_decrease`: [bool] 选填 召回队列是否按照步长为1递减，默认false

    `to_main_table`: [bool] 选填 是否直接插入到主表，默认false

    `confs`: [dict] 必填，每一项代表一路召回
    - id: [int] 召回的ID，必须自行保证全局唯一
    - name: [string] 召回名称, 结果会存储到 table 中, table 名称也是 name
    - field: [string] profile 的字段名称
    - keys: [动态参数] key 的集合
    - num: [int] 召回总数量, 默认 50
    - single_num: [int] 单个 RetrievalInfo 召回数量, 默认 10
    - condition: [动态参数] 控制是否执行这路召回

    调用示例
    ------
    ``` python
    .profile_recall(
        biz = "growth",
        profile = "Recall",
        timeout_ms = 100,
        confs = [
            {
                "id" : 10001,
                "name" : "author2query",
                "field" : "rw_a2q_recall_v71",
                "keys" : "{{author_ids}}",
                "num" : 100,
                "single_num" : 20,
                "condition" : "{{return gys_author2query_exp == 0}}"
            },
            {
                "id" : 10002,
                "name" : "new_author2query",
                "field" : "new_rw_a2q_recall_v71",
                "keys" : "{{author_ids}}",
                "num" : 40,
                "condition" : "{{return new_author2query == 0}}"
            },
            {
                "id" : 10003,
                "name" : "mix_fil_item_cf_v71",
                "field" : "gys_u2q_itemcf",
                "keys" : "{{user_id_str_list}}",
                "num" : 120,
                "condition" : "{{return gys_interest_filter == 3}}"
            },
            {
                "id" : 10004,
                "name" : "mix_fil_fre_searched_query",
                "field" : "ecf_his_searched_query_v71",
                "keys" : "{{user_id_str_list}}",
                "num" : 60
            },
        ]
    )
    ```
    """
    self._add_processor(SeRecoProfileRecallMixer(kwargs))
    return self

  def merge_tables(self, **kwargs):
    """
    SeRecoMergeTablesMixer
    ------
    将多个 Table 合并成一个 Table, 相同 item 的同名单值 attr 会合并成多值 attr, 没有时用默认值填充对齐, 不支持对多值 attr 进行合并

    参数配置
    ------
    `input_tables`: [dict] 必填, 输入 table 列表
    - table_name: [string] table 名称
    - attrs [list] 合并到 output table 的 attr 集合

    `output_table`: [string] 选填，输出 table 名称，默认为 "", 即为默认的主 table

    `convert_black_list`: [list] 选填 不进行转换的 attr 集合
    
    `item_attr_types`: [dict] 必填 标识 input_tables 中的 attrs 的类型, input_tables 中的所有 attr 都必须在这里配置类型
    - name : [string] 必填，attr名称
    - type : [string] 必填，支持的类型：int, float, string

    调用示例
    ------
    ``` python
    .merge_tables(
        input_tables = [
          {"table_name": "author2query", "attrs": [ "item_id_str", "reason_str" ]},
          {"table_name": "new_author2query", "attrs": [ "item_id_str", "reason_str" ]},
        ],
        item_attr_types = [
          {"name":"item_id_str", "type": "string"},
          {"name":"reason_str", "type": "string"},
        ]
        output_table = "",
        convert_black_list = ["item_id_str"],
    )
    ```
    """
    self._add_processor(SeRecoMergeTablesMixer(kwargs))
    return self

  def retrieve_by_store_value_string_list(self, **kwargs):
    """
    SeRecoStoreValueRetriever
    ------
    对一或者单个StoreValue的PB string反序列化后，对其中的kv recall中的recall_item做召回

    参数配置
    ------
    `input_common_attr_list`: [string_list] 必填, StoreValue序列化的string list对应的common attr，也可以支持非list的common attr（优先list）

    `result_num`: [动态参数] 召回的总数量

    `single_recall_num`: [动态参数] 每个pb限制的召回数量

    `item_id_str_attr_name` : [string] 选填，item_id_str的name， 默认值为 item_id_str
    
    `recall_reason_attr_name` : [string] 选填，recall_reason的name， 默认值为 reason_str

    `reason_id` : [int] 必填，大于0的整数

    `recall_name` : [string] 必填，用来标记召回的队列的名字

    `parse_pb_type` : [string] 选填，默认是kv_recall_info，目前只支持 kv_recall_info 和 user_info

    `user_info_item_keyword` : [string] 选填，parse_pb_type等于 user_info时生效，默认为 user_info_item_keyword

    `item_score_name` : [string] 选填，用户可以自定义的score，默认为 item_score

    `item_source_name` : [string] 选填，默认为 item_source 设置召回来源

    `author_id_name` : [string] 选填，默认为 item_author_id

    `is_recall_decrease` : [bool] 选填，默认为 false，用来表示后续的每个召回队列召回个数是否逐步衰减1个

    调用示例
    ------
    ``` python
    .retrieve_by_store_value_string_list(
        input_common_attr_list = "store_value_pb_string_list",
        result_num = "{{result_num}}",
        single_recall_num = "{{single_recall_num}}",
        recall_name="glod_recall",
        reason_id = 101
    )
    ```
    """
    self._add_processor(SeRecoStoreValueRetriever(kwargs))
    return self

  def retrieve_by_protobuf_list(self, **kwargs):
    """
    SeRecoProtobufListRetriever
    ------
    对多个OverseaBarCandidateList的PB list做召回

    参数配置
    ------
    `input_common_attr_list`: [string_list] 必填, RawSamplePackage序列化的string list, 也可以支持非list的common attr（优先list）

    `input_key_attr_name` : [string] 必填，item_id_str的name， 默认值为 item_id_str

    调用示例
    ------
    ``` python
    .retrieve_by_protobuf_list(
        input_common_attr_list = "protobuf_string_list",
        input_key_attr_name = "key_attr_name"
    )
    ```
    """
    self._add_processor(SeRecoProtobufListRetriever(kwargs))
    return self

  def retrieve_by_ziya_executor(self, **kwargs):
    """
    SeRecoZiyaExecutorRetriever
    ------
    直接使用 ziya 召回组件进行召回

    参数配置
    ------
    `conf`: [string] 必填，与 ziya 组件的配置格式相同

    `result_num`: [动态参数] 召回的总数量

    `reason_id`: [int] 必填，大于0的整数

    `recall_name`: [string] 必填，用来标记召回的队列的名字

    `searched_queries_query`: [string] 选填，用户搜索历史词属性名称

    `searched_queries_his_score`: [string] 选填，用户搜索历史词对应的score, 要保证长度和 searched_queries_query 大小相同

    `browse_queries_query`: [string] 选填，用户曝光 Query

    `refer_video_ids`: [string] 选填，refer video id 列表

    `client_info`: [string] 选填，ClientRequestInfo 的序列化 string

    `item_upload_timestamp`: [string] 选填，item_upload_timestamp

    `kwai_source`: [string] 选填，kwai_source

    `user_interest_se_author_first`: [string] 选填，user_interest_se_author_first

    `user_interest_se_author_second`: [string] 选填，user_interest_se_author_second 长度和对应 first 保持一致

    `user_interest_author_first`: [string] 选填，user_interest_author_first

    `user_interest_author_second`: [string] 选填，user_interest_author_second 长度和对应的 first 保持一致

    `user_interest_live_author_first`: [string] 选填，user_interest_live_author_first

    `user_interest_live_author_second`: [string] 选填，user_interest_live_author_second 长度和对应的 first 保持一致

    调用示例
    ------
    ``` python
    .retrieve_by_ziya_executor(
      conf = \"\"\"
        "name": "operation_query",
        "class": "HotspotOperationMemoryRecallExecutor",
        "params": {
            "is_back_up": true,
            "reload_time": 600,
            "rpc_name": "grpc_mmuOperationHotSpotService",
            "timeout_ms": 3000
        }
      \"\"\"
    )
    ```
    """
    self._add_processor(SeRecoZiyaExecutorRetriever(kwargs))
    return self

  def init_legacy_resource_manager(self, **kwargs):
    """
    SeRecoLegacyResourceManagerEnricher
    ------
    初始化 ziya 组件依赖的 resource manager

    参数配置
    ------
    `conf`: [string] 必填，与 ziya 服务配置文件中的 resources 内容格式相同

    调用示例
    ------
    ``` python
    .init_legacy_resource_manager(
      conf = \"\"\" 
        "data_client": {
            "cluster_set": "searchWordsReviewStatus,mmuTrendingSearch,mmuFeatureServerKiwi,rssubtag,rssubtag2,searchExploration,searchDocRealtime,verticalattr,searchQueryReco1,searchQueryReco2,searchQueryReco3,searchQueryReco4,searchQueryReco5,gysTrendingSearch,searchRecoProfileCluster2,searchRecoOperationUser,searchRecoBottomBarInterest,gysTrendingResultCache,searchHotQueryRecall,hotReview,searchRs,searchPhotoFilter,searchGysKiwi,goodsQueryRw,selectionUserTrack",
            "size": 10
        },
        "lru_cache": {
            "enable_cache": true,
            "object": [
                {
                    "name": "kfs_reader",
                    "size": 1000000,
                    "cluster": "rssubtag,rssubtag2,hotReview"
                },
                {
                    "name": "risk_filter",
                    "size": 5000000,
                    "cycle": 1800,
                    "cluster": "searchWordsReviewStatus"
                }
            ]
        },
        "log_collecter": {
            "format": "proto",
            "topic": "search_reco_search_log_gys",
            "is_dump_query_reco_log": true
        },
        "dict_manager": {
            "enable_dict_server": true,
            "dict_service_name": "grpc_gysHomepageServiceV7"
        },
        "thread_num_l1": 32,
        "thread_num_l2": 128,
        "thread_num": 64,
        "is_use_bs_thread_pool" : true,
        "bs_thread_pool_size": 64,
        "bs_thread_pool_size_l1": 128,
        "bs_thread_pool_size_l2": 256,
        "max_recall_queue_num": 70
      \"\"\"
    )
    
    ```
    """
    self._add_processor(SeRecoLegacyResourceManagerEnricher(kwargs))
    return self

  def se_reco_retrieve_by_starry_embedding(self, **kwargs):
    """
    SeRecoStarryEmbeddingRetriever
    -----
    搜索繁星 starry 检索服务插件：实现 i2i,q2i,emb2i，有问题 可咨询 zhangcunyi liuzhanshan
    参数配置
    ------
    `kess_service`: [string] [必填] 服务的 kess 服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] [必填] 召回原因

    `source`: [string] [必填] 区分调用方

    `vertical_id`: [string] [必填] 业务 ID 或 索引 id

    `source_photo_attr`: [string] [必填] CommonListAttr Name

    `source_score_common_attr`: [string] [必填] CommonListAttr 对应的score

    `batch_limit` : [int] [选填] 一次下发一个 batch 请求的上限条数，默认 INT_MAX

    `top_k`: [int] [动态参数]  每个 item 均取 top_k 个，> 0 为有效值；

    `item_id_str_attr_name` : [string] [选填] 默认是 item_id

    `max_distance` : [float] [选填] 默认是 float 最大值，只有 is_q2q 为真时, pb score 大于该值结果丢弃,

    调用示例
    ------
    ``` python
    .se_reco_retrieve_by_starry_embedding(
            debug_log=False,
            kess_service = "grpc_mmu_visionSearchGas",
            timeout_ms = 50,
            source = "live_se_fasttext_recall",
            reason = recall_name_dict["livestream_fasttext"],
            vertical_id = "liveSeContentQueryFasttext",
            source_photo_attr = "searched_queries_query",
            source_score_common_attr = "searched_queries_his_score",
            top_k = 50,
            max_distance = 0.5
        )
    ```
    """
    self._add_processor(SeRecoStarryEmbeddingRetriever(kwargs))
    return self

  def se_reco_query_retrieve_by_starry_embedding(self, **kwargs):
    """
    SeRecoStarryQueryRetriever
    -----
    搜索繁星 starry 检索服务插件：实现 q2q,emb2q 的query召回
    参数配置
    ------
    `kess_service`: [string] [必填] 服务的 kess 服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] [必填] 召回原因

    `source`: [string] [必填] 区分调用方

    `vertical_id`: [string] [必填] 业务 ID 或 索引 id

    `input_type`: [string] [选填] 输入数据类型, 'text':q2q StringCommonAttr/StringListCommonAttr, 'feature':emb2q DoubleListCommonAttr

    `input_common_attr`: [string] [必填] 输入的common attr name

    `batch_limit` : [int] [选填] 一次下发一个 batch 请求的上限条数, 默认 INT_MAX

    `top_k`: [int] [动态参数]  每个 item 均取 top_k 个, > 0 为有效值

    `item_id_str_attr_name` : [string] [选填] 召回query文本存放的item attr name, 默认是 item_id

    `max_distance` : [float] [选填] 用于截断召回结果, 默认是 float 最大值

    调用示例
    ------
    ``` python
    .se_reco_query_retrieve_by_starry_embedding(
            kess_service = "grpc_mmu_visionSearchGasForSearch",
            timeout_ms = 50,
            reason = recall_name_dict["livestream_fasttext"],
            source = "SugAnnV1",
            vertical_id = "SugAnnV1",
            input_type = "feature",
            input_common_attr = "prefix_bert_emb",
            top_k = 20
        )
    ```
    """
    self._add_processor(SeRecoStarryQueryRetriever(kwargs))
    return self
  
  def se_reco_query_retrieve_by_bart_gen(self, **kwargs):
    """
    SeRecoBartGenRetriever
    -----
    基于BART模型的前缀的生成式召回
    参数配置
    ------
    `input_segment_text_attr`: [string] [必填] prefix分词文本

    `input_searched_query_attr`: [string_list] [选填] 历史搜索词list
    
    `enable_searched_query_num`: [int] [选填] 使用几个历史词，默认0

    `vocab_name`: [string] bert tokenizer参数，词典在词典平台的 name，默认 word_vocab_perks_lower

    `bart_service_name`: [string] [必填] kml上部署bart的grpc服务名

    `bart_model_name`: [string] [必填] kml上上传bart的模型仓库名

    `bart_timeout_ms`: [int] [必填] kml服务超时时间

    `reason`: [int] [必填] 召回原因

    `item_id_str_attr_name` : [string] [选填] 召回query文本存放的item attr name, 默认是 item_id

    `stop_gen` : [bool] [选填] 生成特殊字符时停止继续生成，默认是 False

    `ban_risk_query` : [bool] [选填] 对包含风控词语的query进行过滤，默认是 False

    调用示例
    ------
    ``` python
    .se_reco_query_retrieve_by_bart_gen(
            input_segment_text_attr = "query_segment_text",
            input_searched_query_attr = "searched_query_list",
            enable_searched_query_num = 1,
            vocab_name = "perks_vocab_word_c_version_v9_sug",
            bart_service_name = "grpc_sug_prefix_gen_v1",
            bart_model_name = "inner-ann-model",
            stop_gen = False,
            ban_risk_query = False,
            timeout_ms = 100,
            reason = "BartGen",
            item_id_str_attr_name="item_id"
        )
    ```
    """
    self._add_processor(SeRecoBartGenRetriever(kwargs))
    return self

  def se_reco_query_retrieve_by_starry_embedding_v2(self, **kwargs):
    """
    SeRecoStarryQueryV2Retriever
    -----
    搜索繁星 starry 检索服务插件：实现 q2q,emb2q 的query召回
    参数配置
    ------
    `kess_service`: [string] [必填] 服务的 kess 服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] [必填] 召回原因

    `source`: [string] [必填] 区分调用方

    `vertical_id`: [string] [必填] 业务 ID 或 索引 id

    `input_type`: [string] [选填] 输入数据类型, 'text':q2q StringCommonAttr/StringListCommonAttr, 'feature':emb2q DoubleListCommonAttr

    `dim`: [int] [选填] 当 input_type = feature 时默认是一个embeding， 设置此值>0 表示 double list 里面可以是>=1 个 embeding ， 比如 len(doublelist) = 100, dim = 10, 则下发请求会分成 10 个子请求发送， 默认 = 0 不检测 list 长度

    `input_common_attr`: [string] [必填] 输入的common attr name

    `batch_limit` : [int] [选填] 一次下发一个 batch 请求的上限条数, 默认 INT_MAX

    `top_k`: [int] [动态参数]  每个 item 均取 top_k 个, > 0 为有效值

    `item_id_str_attr_name` : [string] [选填] 召回query文本存放的item attr name, 默认是 item_id

    `max_distance` : [float] [选填] 用于截断召回结果, 默认是 float 最大值

    调用示例
    ------
    ``` python
    .se_reco_query_retrieve_by_starry_embedding_v2(
            kess_service = "grpc_mmu_visionSearchGasForSearch",
            timeout_ms = 50,
            reason = recall_name_dict["livestream_fasttext"],
            source = "SugAnnV1",
            vertical_id = "SugAnnV1",
            input_type = "feature",
            input_common_attr = "prefix_bert_emb",
            top_k = 20
        )
    ```
    """
    self._add_processor(SeRecoStarryQueryV2Retriever(kwargs))
    return self

  def se_reco_query_retrieve_by_starry_embedding_v3(self, **kwargs):
    """
    SeRecoStarryQueryV3Retriever
    -----
    搜索繁星 starry 检索服务插件：实现 q2q,emb2q 的query召回
    参数配置
    ------
    `kess_service`: [string] [必填] 服务的 kess 服务名

    `timeout_ms`: [int] [必填] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] [必填] 召回原因

    `source`: [string] [必填] 区分调用方

    `vertical_id`: [string] [必填] 业务 ID 或 索引 id

    `input_type`: [string] [选填] 输入数据类型, 'text':q2q StringCommonAttr/StringListCommonAttr, 'feature':emb2q DoubleListCommonAttr

    `dim`: [int] [选填] 当 input_type = feature 时默认是一个embeding， 设置此值>0 表示 double list 里面可以是>=1 个 embeding ， 比如 len(doublelist) = 100, dim = 10, 则下发请求会分成 10 个子请求发送， 默认 = 0 不检测 list 长度

    `input_common_attr`: [string] [必填] 输入的common attr name

    `batch_limit` : [int] [选填] 一次下发一个 batch 请求的上限条数, 默认 INT_MAX

    `top_k`: [int] [动态参数]  每个 item 均取 top_k 个, > 0 为有效值

    `item_id_str_attr_name` : [string] [选填] 召回query文本存放的item attr name, 默认是 item_id

    `item_trigger_str_attr_name` : [string] [选填] 召回query的query存放的item attr name, 默认是 item_recall_reason

    `max_distance` : [float] [选填] 用于截断召回结果, 默认是 float 最大值

    调用示例
    ------
    ``` python
    .se_reco_query_retrieve_by_starry_embedding_v3(
            kess_service = "grpc_mmu_visionSearchGasForSearch",
            timeout_ms = 50,
            reason = recall_name_dict["livestream_fasttext"],
            source = "SugAnnV1",
            vertical_id = "SugAnnV1",
            input_type = "feature",
            input_common_attr = "prefix_bert_emb",
            input_common_attr2 = "q2q_list",
            top_k = 20
        )
    ```
    """
    self._add_processor(SeRecoStarryQueryV3Retriever(kwargs))
    return self

  def kconf_black_list_ac_match_filter(self, **kwargs):
    """
    KconfBlackListACMatchFilterArranger
    -----
    使用Kconf配置的List进行黑名单过滤，使用AC自动机匹配
    参数配置
    ------
    `item_id_attr_name`: [string] [选填] item_id的字段名, 默认为item_id

    `item_source_attr_name`: : [string] [选填] item_source的字段名, 默认为item_source

    `kconf_black_list_path`: [string] [必填] 黑名单的kconf路径

    `target_sources`: [string] [选填] 已废弃, 效果同filter_sources

    `filter_sources`: [string] [选填] 需要过滤的source, 当有值时item_source不在filter_sources的不会进行过滤

    `no_filter_sources`: [string] [选填] 不需要过滤的source, 当有值时item_source在no_filter_sources的不会进行过滤

    调用示例
    ------
    ``` python
    .kconf_black_list_ac_match_filter(
            debug_log=False,
            item_id_attr_name = "item_id",
            kconf_black_list_path = "mmu.search.trendingBlacklistWords",
        )
    ```
    """
    self._add_processor(KconfBlackListACMatchFilterArranger(kwargs))
    return self

  def kconf_black_list_ac_match_filter_v2(self, **kwargs):
    """
    KconfBlackListACMatchFilterV2Arranger
    -----
    使用Kconf配置的2个List进行黑名单过滤，需要同时命中list1和list2中词，使用AC自动机匹配
    参数配置
    ------
    `item_id_attr_name`: [string] [选填] item_id的字段名, 默认为item_id

    `item_source_attr_name`: : [string] [选填] item_source的字段名, 默认为item_source

    `kconf_black_list_path1`: [string] [必填] 黑名单1的kconf路径

    `kconf_black_list_path2`: [string] [必填] 黑名单2的kconf路径

    `target_sources`: [string] [选填] 已废弃, 效果同filter_sources

    `filter_sources`: [string] [选填] 需要过滤的source, 当有值时item_source不在filter_sources的不会进行过滤

    `no_filter_sources`: [string] [选填] 不需要过滤的source, 当有值时item_source在no_filter_sources的不会进行过滤

    调用示例
    ------
    ``` python
    .kconf_black_list_ac_match_filter_v2(
            debug_log=False,
            item_id_attr_name = "item_id",
            kconf_black_list_path1 = "mmu.search.trendingBlacklistWords1",
            kconf_black_list_path2 = "mmu.search.trendingBlacklistWords2",
        )
    ```
    """
    self._add_processor(KconfBlackListACMatchFilterV2Arranger(kwargs))
    return self

  def illegal_query_filter(self, **kwargs):
    """
    SeRecoIllegalQueryFilterArranger
    ------
    敏感词风控过滤 Redis 过滤，对已经去重的 item_id 过滤，可以设置一些免过滤的 source

    参数配置
    ------
    `review_filter_kconf`: [string] 动态参数, 配置的 kconf, 默认为空

    `item_source_attr_name`: [string] 选填, 默认为 item_source

    `item_id_attr_name`: [string] 选填, 默认为 item_id

    `filter_sources_list`: [list] 选填, item_source 命中该列表，过滤

    `no_filter_sources_list`: [list] 选填, 如果 item_source 命中该列表，不过滤

    调用示例
    ------
    ``` python
    .illegal_query_filter(
        item_source_attr_name = "item_source",
        item_id_attr_name = "item_id",
        no_filter_sources_list = ["ads_recall"],
    )
    ```
    """
    self._add_processor(SeRecoIllegalQueryFilterArranger(kwargs))
    return self

  def enrich_negative_score(self, **kwargs):
    """
    SeRecoQueryNegativeScoreEnricher
    ------
    负向模型分数

    参数配置
    ------
    `bucket_attr_name`: [string] 必填

    `input_str_attr_name`: [string] 必填

    `output_score_attr_name`: [string] 必填

    `service_name`: [string] 负向模型 kml 推理服务名，默认 grpc-oversea-search-query-classification

    `timeout_ms`: [string] 超时设置，默认 100 毫秒

    `model_name_suffix`: [string] 模型名称后缀，默认 -fasttext-textCNN-v1

    `dict_name_suffix`: [string] 词典名称后缀，默认 _sensitive_new

    调用示例
    ------
    ``` python
    .enrich_negative_score(
        bucket_attr_name = "bucket",
        input_str_attr_name = "item_str",
        output_score_attr_name = "negative_score"
    )
    ```
    """
    self._add_processor(SeRecoQueryNegativeScoreEnricher(kwargs))
    return self

  def enrich_by_profile(self, **kwargs):
    """
    SeRecoGetAttrByProfileEnricher
    ------
    profile 召回的结果存入 item attr

    参数配置
    ------
    `biz_name`: [string] 选填, profile 召回使用的 biz, 默认为 growth

    `profile_name`: [string] 选填, 默认为 Query

    `input_attr_name`: [string] 必填, 常为 item_id, 用来作为 profile keys

    `is_multi_field`: [bool] 选填, 是否直接输出到多个filed, 仅支持 int/int_list/float/float_list/string/string_list类型的字段. 默认为False, 会影响其他字段的填写

    `is_common_attr`: [bool] 选填, 输入和输出是否是 common attr, 默认为False

    `output_attr_name`: [string] 选填, 作为 profile 输出的 item attr, is_multi_field=False时必填

    `is_success_attr_name`: [string] 选填, 默认为空, 标识输出的 item attr 是否成功, 为空不输出该 item attr

    `pb_message`: [string] 选填, 标识输出的 PB 信息, is_multi_field=False时必填

    `fields`: [list] 必填  profile 召回对应的 field, 当 is_multi_field=True是可以选择填写json list, 形如 {"feild_name" : "aaa", "export_name" : "bbb"} 来进行输出字段的重命名

    `timeout_ms`: [int] 选填 profile 召回的超时时间, 默认为 50 ms

    调用示例
    ------
    ``` python
    .enrich_by_profile(
        biz_name = "growth",
        profile_name = "Query",
        input_attr_name = "item_id",
        output_attr_name = "query_profile_msg",
        fields=["field1", "field2"],
        pb_message = "growth.profile.proto.QueryFeature"
    )
    ```

    ``` python
    .enrich_by_profile(
        biz_name="growth",
        profile_name="Query",
        input_attr_name="item_id",
        fields=[
            {"field_name": "field1", "export_name": "export1"},  # field1 -> export1
            dict(field_name="field2", export_name="export2"),  # field2 -> export2
            "field3",  # 不进行重命名
        ],
    )
    ```
    """
    self._add_processor(SeRecoGetAttrByProfileEnricher(kwargs))
    return self

  def diversity_dedup(self, **kwargs):
    """
    SeRecoDiversityDedupArranger
    ------
    打散去重

    参数配置
    ------
    `item_id_attr_name`: [string] 选填, 默认为 item_id

    `item_score_attr_name`: [string] 选填, 默认为 item_scoure

    `max_cnt`: [int] 选填, 默认为 12, 对多少 item 处理

    `stop_words_kconf`: [string] 选填, 停用词的 kconf

    `params_kconf`: [string] 选填, 参数的 kconf

    调用示例
    ------
    ``` python
    .diversity_dedup(
        item_id_attr_name = "item_id",
        params_kconf = "se.ziyasort.gys_diversity_dedup_conf",
        stop_words_kconf = "se.ziyasort.gys_stopwords",
        dedup_max_count=12
    )
    ```
    """
    self._add_processor(SeRecoDiversityDedupArranger(kwargs))
    return self

  def kwai_dedup(self, **kwargs):
    """
    SeRecoKwaiDedupArranger
    ------
    字面相似度去重

    参数配置
    ------
    `item_id_attr_name`: [string] 必填

    `item_score_attr_name`: [string] 必填

    `bucket_attr_name`: [string] 必填

    `expect_num`: [int] 选填，动态参数，对前多少项返回结果进行去重

    调用示例
    ------
    ``` python
    .kwai_dedup(
        item_id_attr_name = "item_id",
        item_score_attr_name = "item_score",
        bucket_attr_name = "bucket"
    )
    ```
    """
    self._add_processor(SeRecoKwaiDedupArranger(kwargs))
    return self

  def hot_review_filter(self, **kwargs):
    """
    SeRecoHotReviewFilterArranger
    ------
    高热复审过滤

    参数配置
    ------
    `item_id_attr_name`: [string] 选填, 默认为 item_id

    `capacity_bits`: [int] 选填, cache 大小, 默认为 2 的 19 次方个

    `expire_secs`: [int] 选填, cache 过期时间, 默认 600 秒

    调用示例
    ------
    ``` python
    .hot_review_filter(
        item_id_attr_name = "item_id",
    )
    ```
    """
    self._add_processor(SeRecoHotReviewFilterArranger(kwargs))
    return self

  def ngram_dedup_filter(self, **kwargs):
    """
    SeRecoNgramDedupArranger
    ------
    ngram 去重

    参数配置
    ------
    `item_id_attr_name`: [string] 选填, 默认为 item_id

    `max_ngram_count`: [int] 选填, 默认为 3, 表示出现 max_ngram_count 次则去重

    `ngram`: [int] 选填, 默认为 2, 表示连续几个词

    调用示例
    ------
    ``` python
    .ngram_dedup_filter(
        item_id_attr_name = "item_id",
        max_ngram_count = 2,
        ngram = 2
    )
    ```
    """
    self._add_processor(SeRecoNgramDedupArranger(kwargs))
    return self

  def gys_jubao_rank(self, **kwargs):
    """
    SeRecoJubaoRankEnricher
    ------
    调用Jubao服务进行rank, 获取后续排序需要的score等信息, 会对传入的一整个request进行拆分, 按设置的batch size分别请求

    参数配置
    ------
    `caller_name`: [string] 请求名

    `thread_num`: [int] 选填, rpc异步线程数, 默认8

    `rpc_name`: [string] 请求具体服务的rpc

    `batch_size`: [int] 选填, 单个请求的batch size大小, 默认100

    `time_out`: [int] 选填, 超时时间(ms), 默认100ms

    `enable_ack`: [bool] 选填, 是否使用ack回流, 默认true

    `input_jubao_request_common_attr_name`: [string] 有组装好ks::search::jubao::JubaoRequest的attr名称

    `input_item_id_item_attr_name`: [string] 选填, 获取item id的item attr名称, 默认为item_id

    `output_rank_resp_item_attr_name`: [string] 输出Jubao结果(ks::search::jubao::JubaoItem)的item attr名称

    `output_rank_channel_peer_item_attr_name`:, [string] 选填, 输出item对应Jubao rpc channel peer的item attr名称, 默认为rank_jubao_channel_peer

    调用示例
    ------
    ``` python
    .gys_jubao_rank(
        caller_name = "gys_reco_dragon",
        thread_num = 8,
        rpc_name = "grpc_jubao_rank_test",
        batch_size = 100,
        time_out = 100,
        enable_ack = True,
        input_jubao_request_common_attr_name = "jubao_request",
        input_item_id_item_attr_name = "item_id",
        output_rank_resp_item_attr_name = "jubao_item",
        output_rank_channel_peer_item_attr_name = "rank_jubao_channel_peer",
    )
    ```
    """
    self._add_processor(SeRecoJubaoRankEnricher(kwargs))
    return self

  def enrich_item_boost_position(self, **kwargs):
    """
    SeRecoBoostItemAttrEnricher
    ------
    生成boost位置item特征，一般与force_insert结合使用。
    如果item的自然排序在boost位置之后，则输出特征的值为boost_position；否则输出特征的值为自然排序的位置。
    只设置第一个input_item_attr值在boost_values中的item。

    参数配置
    ------
    `input_item_attr`: [string] boost 依据的特征名称，特征为string类型

    `boost_values`: [string_list] 需要 boost 的值集合

    `boost_position`: [int] boost的位置, 从0开始，即0对应boost到第一位，1对应boost到第二位，以此类推……

    `output_item_attr`: [string] 输出的boost位置特征的名称

    调用示例
    ------
    ``` python
    .enrich_item_boost_position(
        # 以下将 item_source 是 xxx 或 yyy 的第一个item生成boost位置特征
        input_item_attr = "item_source",
        boost_values = ["xxx", "yyy"],
        boost_position = 1,
        output_item_attr = "xxxx_item_boost_pos",
    )
    ```
    """
    self._add_processor(SeRecoBoostItemAttrEnricher(kwargs))
    return self

  def se_reco_ups(self, **kwargs):
    """
    SeRecoUnfieldProfileEnricher
    ------
    调用UPS服务进行特征获取

    参数配置
    ------
    `is_common_attr`: [bool] 选填, 是否为common attr, 默认为True (暂时也只支持common attr)

    `user`: [dict] 选填, 用户侧特征配置
      - `key_attr_name`: [string] 获取特征key的attr名称
      - `timeout_ms`: [int] 请求 ups 的超时时间, 不配置则为 gflag unified_profile_request_timeout 的值, gflag 默认 60
      - `import_features`: [list] 从 dragon 透传给 ups 的特征配置
        - `import_name`: [string] 导入的 common attr 名字
        - `feature_name`: [string] 透传给 ups 的特征名字
      - `features`: [list] 获取的多个特征的配置
        - `feature_name`: [string] ups 返回的特征名
        - `export_name`: [string] 导出的 common attr 名称

    `photo`: [dict] 选填, photo侧特征配置, 配置同user

    `query`: [dict] 选填, query, 配置同user

    `device`: [dict] 选填, device, 配置同user

    调用示例
    ------
    ``` python
    .se_reco_ups(
      debug_log = False,
      user = {
        "key_attr_name" : "user_id",
        "features" : [{
            "feature_name" : "gys_user2author_id_list",
            "export_name" : "user_interest_author_first",
          },
          {
            "feature_name" : "gys_user2author_score_list",
            "export_name" : "user_interest_author_second",
          },
          {
            "feature_name" : "gys_user2search_author_id_list",
            "export_name" : "user_interest_se_author_first",
          },
          {
            "feature_name" : "gys_user2search_author_score_list",
            "export_name" : "user_interest_se_author_second",
          },
          {
            "feature_name" : "gys_user2live_author_id_list",
            "export_name" : "user_interest_live_author_first",
          },
          {
            "feature_name" : "gys_user2live_author_score_list",
            "export_name" : "user_interest_live_author_second",
          },
          {
            "feature_name" : "photo_update_author_ids",
            "export_name" : "photo_update_author_ids",
          },
        ]
      }
    )
    ```
    """
    self._add_processor(SeRecoUnfieldProfileEnricher(kwargs))
    return self

  def common_profile_v1_enrich(self, **kwargs):
    """
    SeRecoCommonProfileV1Enricher
    ------
    调用V1版本的Profile Client，请求profile，拿到对应的profile信息

    参数配置
    ------
    `cluster`: [string] 必填，集群名字

    `table`: [string] 必填, profile类型

    `fields`: [string list] 必填，访问profile的所有fields

    `pb_message`: [string] 必填, profile的PB类型

    `timeout_ms`: [int] 选填，访问profile的超时时间，默认50ms

    `input_attr_name`: [string] 必填, string list common attr, 作为访问profile集群的key

    `output_attr_name`: [string] 必填, ptr common attr, 类型vector<shared_ptr<Message>>, 和input_attr_name一一对应, 存放每个profile结果PB的地址

    `err_code_attr_name`: [string] 选填, int list common attr, 存放每个key访问profile的error code

    调用示例
    ------
    ``` python
    .common_profile_v1_enrich(
      cluster = "searchBigForward2Kiwi_read",
      table = "search.photoProfile",
      timeout_ms = 50,
      input_attr_name = "photo_id_list",
      output_attr_name = "photo_profile_addr_list",
      pb_message = "search.profile.PhotoProfile",
      fields = ["se_mm_v4_photo_emb"]
    )
    ```
    上述例子表示，若有<string list common attr> photo_id_list=["123", "234"], 生成<int list common attr> photo_profile_addr_list=[addr1, addr2]，其中addr1/addr2为(int64)(PhotoProfile*)
    """
    self._add_processor(SeRecoCommonProfileV1Enricher(kwargs))
    return self

  def pb_list_scatter_enricher(self, **kwargs):
    """
    SeRecoPBListScatterEnricher
    ------
    将pb list分发给每个item

    参数配置
    ------
    `common_pb_list_attr`: [string] 必填 [ptr common attr] [input] 存放待分发的pb list，类型为 vector<Message*>

    `common_key_list_attr`: [string] 必填 [intlist / doublelist / stringlist common attr] [input] 存放每一个pb对应的key，长度需与pb list相等

    `item_key_list_attr`: [string] 必填 [int / double / string item attr] [input] 存放每一个item的用来查找pb的key

    `item_pb_attr`: [string] 必填 [ptr item attr] [output] 存放item对应的pb

    调用示例
    ------
    ``` python
    .pb_list_scatter_enricher(
      common_pb_list_attr = "photo_profile_list",
      common_key_list_attr = "photo_id_list",
      item_key_attr = "photo_id",
      item_pb_attr = "photo_profile"
    )
    ```
    以上示例表示，将存放所有的photo id和photo profile的一一对应的两个common attr，根据每个item的photo_id，获取其对应的photo profile的PB指针
    """
    self._add_processor(SeRecoPBListScatterEnricher(kwargs))
    return self

  def pb_list_gather_enricher(self, **kwargs):
    """
    SeRecoPBListGatherEnricher
    ------
    将每个 item 的 pb 合并成 pb list

    参数配置
    ------
    `common_pb_list_attr`: [string] 必填 [ptr common attr] [output] 存放合并后的 pb list, 类型为 vector<Message*>

    `item_pb_attr`: [string] 必填 [ptr item attr] [input] 存放 item 对应的 pb

    调用示例
    ------
    ``` python
    .pb_list_gather_enricher(
      common_pb_list_attr = "photo_profile_list",
      item_pb_attr = "photo_profile"
    )
    ```
    """
    self._add_processor(SeRecoPBListGatherEnricher(kwargs))
    return self

  def embedding_quantization_model(self, **kwargs):
    """
    SeRecoQuantizationModelEnricher
    ------
    搜索的int8量化模型，将string类型的embedding量化为float list类型的embedding，长度为64

    参数配置
    ------
    `input_common_attr`: [string] is_common=True时必填 [string common attr]

    `output_common_attr`: [string] is_common=True时必填 [float list common attr]

    `input_item_attr`: [string] is_common=False时必填 [string item attr]

    `output_item_attr`: [string] is_common=False时必填 [float list item attr]

    `is_common`: [bool] 默认为True,

    `quan_model`: [string] 必填，量化模型，可以通过读二进制文件，base64编码得到

    调用示例
    ------
    ``` python
    .transfer_int_to_ptr_enrich(
      input_item_attr = "photo_embedding_un_quan",
      output_item_attr = "photo_embedding",
      quan_model = "base64://XXXXXX",
      is_common = False
    )
    ```
    """
    self._add_processor(SeRecoQuantizationModelEnricher(kwargs))
    return self

  def enrich_with_se_ann(self, **kwargs):
    """
    SeRecoGenericAnnEnricher
    ------
    搜索的ANN索引服务访问接口
    输出四个common attr，分别为：
      [output_attr_prefix_req_id_list]        输入数据列表                  string list common attr                   length = M
      [output_attr_prefix_resp_size_list]     每个输入数据对应的输出个数      int list common attr                       length = M && sum = N
      [output_attr_prefix_resp_value_list]    输出数据列表                  int list / string list common attr         length = N
      [output_attr_prefix_resp_score_list]    输出数据分数列表               double list common attr                    length = N

    参数配置
    ------
    `rpc_name`: [string] ann索引服务名称，默认为grpc_mmu_visionSearchGas

    `vertical_id`: [string] 索引名称

    `source`: [string] 必填，请求来源标识

    `request_id_attr_name`: [string] 用来填充request id的[string common attr]，如果为空，request ID填写common Leaf的request ID

    `timeout_ms`: [int] rpc访问超时时间，默认为50ms

    `num_of_result`: [int] 返回多少个结果，默认为5

    `input_type`: [string] "image" / "photo_id" / "text"三选一，表示request中数据填充位置，is_custom_request=False时必填

    `input_attr_name`: [string] 输入数据来源 photo_id类型时，其为[int list common attr]，否则为[string list common attr]，is_custom_request=False时必填

    `output_attr_prefix`: [string] 输出数据attr前缀

    `is_custom_request`: [bool] 是否使用自定义request，默认是False

    `custom_request_attr_name`: [string] 如果is_custom_request=True，则必填，自定义request的ptr common attr，需要配合build_protobuf一起使用

    `score_threshold`: [double] 分数阈值，满足阈值条件的才会保留到output attr中，默认为0

    `score_threshold_type`: [string] 支持 <、 <=、 >、 >= 四种类型，表示当ANN分数 <、 <=、 >、 >= score_threshold时，表示满足阈值条件，默认为>=

    调用示例
    ------
    ``` python
    .enrich_with_se_ann(
      rpc_name = "grpc_mmu_visionSearchGas",
      vertical_id = "xxx",
      source = "xxx",
      input_type = "text",
      input_attr_name = "input_attr",
      output_attr_prefix = "output_attr_",
      score_threshold = 0.15,
      score_threshold_type = "<"
    )
    ```
    ``` python
    .enrich_with_se_ann(
      is_custom_request = True,
      custom_request_attr_name = "ann_request"
      score_threshold = 0.15,
      score_threshold_type = "<"
    )
    ```
    """
    self._add_processor(SeRecoGenericAnnEnricher(kwargs))
    return self

  def retrieve_by_es(self, **kwargs):
    """
    CommonRecallEsRetriever
    -------
    通用的ES服务检索插件: 实现keyword查询、多字段/短语查询、sug查询等功能

    参数配置
    -------
    `kess_service`: [string] [必填] ES服务的kess服务名

    `timeout_ms`: [int] [必填] 请求ES服务的超时时间，默认为50

    `app_id`: [string][动态参数] [必填] 用户唯一ID 映射一个索引

    `token`: [string] [非必填] 分配给用户的token

    `keyword`: [string][动态参数] [必填] 查询关键字

    `reason`: [int] [非必填] reason

    `explore`: [string][动态参数] [非必填] 海外场景必传，标识分桶

    `input_key_attr_name`: [string] [必填] item attr name，默认值为"key"

    `section`: [list] [非必填] 多字段查询/短语查询等
      - `list_for_literal`: [bool] 是否批量配置field_name和literal，True时field_name和literal接收list参数，False时接收单值参数，默认false
      - `field_name`: [string/string_list][动态参数] 字段名
      - `weight`: [double] 权重
      - `literal`: [string/string_list] [动态参数] 模糊查询query
      - `use_fuzzy`: [bool] 是否设置fuzzy，默认false
      - `fuzzy_size`: [int][动态参数] fuzzy_size

    `filter`: [list] [非必填] 过滤字段集合,多个过滤字段只能and
      - `field_name`: [string] 字段名
      - `field_value`: [string][动态参数] 字段值
      - `match_type`: [int] enum MatchType {
      MATCH_TYPE_UNKNOWN = 0;
      EQ = 1;
      GT = 2;
      LT = 3;
      GTE = 4;
      LTE = 5;
      GEO_DISTANCE = 6;
      IN = 7;
    }

    `sort`: [list] [非必填]
    排序字段集合，eg：[{field_name:"fans_count",order_type:0,poi:""}]，其中order_type为enum OrderType {
      ASC = 0;
      DESC = 1;
      DISTANCE_ASC = 2;
      DISTANCE_DESC = 3;
    }

    `page`: [int] [必填] 页码，最小为0：表示从第一页开始

    `count`: [int] [必填] 返回结果数目

    `session_id`: [string] [非必填] 一次会话的唯一id

    `user_id`: [int] [非必填]

    `routing`: [string] [非必填] 可根据shard进行路由，提升查询效率

    `source_field`: [list] [非必填] 选择返回的字段，默认是全字段返回

    `use_suggest`: [bool] [非必填] 是否为suggest请求

    `save_dedup_attr`: [bool] [非必填] 重复的item是否将attr以list形式全部保存，只支持单值类型attr

    `sug_fuzziness`: [list] [非必填]sugggest请求下，必须要填，
      - `fuzziness`: [int_list][动态参数] 编辑距离召回方式(0、1、2)
      - `recall_cnt`: [int_list][动态参数] 召回的数量

    调用示例
    -------
    ``` python
    .retrieve_by_es(
      kess_service = "grpc_searchPlatformApi",
      timeout_ms = 150,
      app_id = "kwaipro-search-user-br",
      keyword = "{{query}}",
      explore = "br",
      input_key_attr_name = "user_id",
      section = [{
        "field_name":"normalize_kwai_id",
        "weight":1.0
      },{
        "field_name":"normalize_user_name",
        "weight":1.0
        "literal":"neymar jr",
        "use_fuzzy":True,
        "fuzzy_size":1
      }],
      filter = [{
        "field_name":"explore",
        "match_type":1,
        "field_value":"br"
      },{
        "field_name":"risk_level",
        "match_type":4,
        "field_value":"0"
      }],
      sort = [{
        "field_name":"fans_count",
        "order_type":1
      }],
      sug_fuzziness = [{
        "fuzziness":[0,1,2],
        "recall_cnt":[30,15,15]
      }],
      page = 1,
      count = 5
    )
    ```
    """
    self._add_processor(CommonRecallEsRetriever(kwargs))
    return self
  
  def ups_update(self, **kwargs):
    """
    SeRecoUnfieldProfileUpdateObserver
    ------
    搜索增长UPS更新插件, 能够将 CommonAttr 或者 ItemAttr 更新到/删除UPS指定field。

    参数配置
    ------

    `type`: [sting] 更新/删除的数据类型, 目前仅支持user. 默认为user

    `delete`: [bool] 是否删除, 默认为False

    `field`: [string] 需要更新/删除的field

    以下两种 key 二选一：

    `key`: [string] [动态参数] 指定更新/删除 ups 的 key, 如果配置了动态参数且该 common_attr 不存在将忽略写入. 对user类型来说, 动态参数支持 int/int_list 类型

    `key_from_item_attr`: [string] 从指定的 item_attr 中获取动态的  key, 如果该 item_attr 不存在将被跳过. 对user类型来说, 支持 int/int_list 类型

    当delete为false时, 对于 value 有两种方式, 二选一:

    `value`: [string] [动态参数] 指定更新value. 优先使用

    `value_from_item_attr`: [string] 会根据其item_attr尝试依次更新

    调用示例
    ------
    ``` python
    .ups_update(
      delete=False,
      field="browse_history",
      key="{{user_id}}",
      value_from_item_attr="item_id",
    )
    ```
    """
    self._add_processor(SeRecoUnfieldProfileUpdateObserver(kwargs))
    return self

  def ack_feature_dump(self, **kwargs):
    """
    SeRecoAckFeatureDumpObserver
    ------
    搜索日志dump插件, 用于dump精排/粗排的日志信息

    参数配置
    ------

    `max_send_num`: [int] 选填, 最大dump数量, 默认为100000

    `is_skip_count_limit`: [bool] 选填, 是否跳过召回数量限制, 默认为False. 为True时会忽略最终召回数量进行dump

    `is_dump_extra_item`: [bool] 选填, 是否dump非server show的item, 默认为False

    `cover_item_id_by_author_name`: [bool] 选填, author id 不为空时，是否用 author name 覆盖 item_id, 默认为False

    `extra_dump_num`: [int] 选填, dump非server show的item的数量, 默认为20

    `server_show_not_dump`: [bool] 选填, 是否不dump server show的item, 默认为False

    `dump_ratio`: [int] 选填, dump的session比例, 0~100, 默认为100

    `request_type`: [string] 根据请求jubao时候的type填写, 需要跟请求时对齐

    `count`: [int] [动态参数] 最终召回的item数量

    `item_id_attr_name`: [string] 选填, item id的字段名, 默认为item_id

    `item_author_id_attr_name`: [string] 选填, author id的 字段名, 默认为 item_author_id

    `item_author_name_attr_name`: [string] 选填, author name 的字段名, 默认为 item_author_name

    `item_recall_reason_attr_name`: [string] 选填, item召回原因的attr字段名, 默认为item_recall_reason

    调用示例
    ------
    ``` python
    .ack_feature_dump(
        is_dump_extra_item=True,
        server_show_not_dump=True,
        extra_dump_num=20,
        request_type="gys_rough",
        count="{{request_count}}",
    )
    ```
    """
    self._add_processor(SeRecoAckFeatureDumpObserver(kwargs))
    return self

  def gzip(self, **kwargs):
    """
    SeRecoGZipEnricher
    ------
    GZip 压缩解压插件

    参数配置
    ------

    `mode`: [string] 选填, compress/decompress 默认为compress

    `input_common_attr`: [string] 选填, 输入的common attr

    `output_common_attr`: [string] 选填, 输出的common attr

    `input_item_attr`: [string] 选填, 输入的item attr

    `output_item_attr`: [string] 选填, 输出的item attr

    调用示例
    ------
    ``` python
    .gzip(
        mode="decompress",
        input_common_attr="signal_params_decoded",
        output_common_attr="signal_params_decompressed",
    )
    ```
    """
    self._add_processor(SeRecoGZipEnricher(kwargs))
    return self
  
  def retrieve_from_search_sort_batch_pb(self, **kwargs):
    """
    SeRecoSearchSortBatchPBRetriever
    ------
    搜索推荐从 p2q 召回后，解析对应的 PB response 并根据需要解析 extra_info 中的字段并存到 attr中

    参数配置
    ------
    `reason_id`: [int] 必填 为大于零的整数

    `recall_name`: [string] 必填 用来表示召回队列的名字

    `from_search_sort_pb`: [string] 必填 召回来源的 PB

    `only_use_topn`: [int] 动态参数 必填，用来标识每路召回的数量
  
    `item_id_str_attr_name`: [string] 选填 候选的item attr名称， 默认为item_id

    `item_source_name`: [string] 选填 召回来源，默认为 item_source

    `item_recall_reason_name`: [string] 选填 召回理由的名字，默认为item_recall_reason

    `item_score_name`: [string] 选填 召回分的名字，默认为 item_score

    `item_p2q_source_attr_name`: [string] 选填 ，默认为空，为空表示不生成该 item_attr

    `item_p2q_score_attr_name`: [string] 选填 ，默认为空，为空表示不生成该 item_attr

    `is_refer_source`: [bool] 选填，表示是否是refer 召回，默认为false，为 true 时会输出 item_attr: item_is_refer_source

    `item_extra_attr_types`: [dict] 选填，根据 name 找到 extra_info 中的 kv 并生成新的 item attr
    - name : [string] 必填，extra_info中的 名称
    - item_attr_name : [string] 必填，生成新的 item attr name
    - type : [string] 必填，支持的类型：int, float, string
    
    调用示例
    ------
    ``` python
    .retrieve_from_search_sort_batch_pb(
      reason_id=1001,
      recall_name="refer_recall",
      from_search_sort_pb="recall_pb_msg",
      only_use_topn=50,
      is_refer_source=False,
      item_extra_attr_types = [
        {"name":"poi", "item_attr_name": "item_poi", "type": "string"},
        {"name":"g2q", "item_attr_name": "item_g2q", "type": "string"},
      ]
    )
    ```
    """
    self._add_processor(SeRecoSearchSortBatchPBRetriever(kwargs))
    return self

  def attr_map_scatter_enrich(self, **kwargs):
    """
    SeRecoAttrMapScatterEnricher
    ------
    将给定的common key list / common value list组成map，根据每个item对应的key attr， 获取对应的value attr
    > 若输入是m个key，n个value，则会组成n个map，每个map为std::unordered_map<key1+key2+...+keym, valuei>

    参数配置
    ------

    `common_key_list_attrs`: [string list] 必填, 其中每个attr的类型均为：[int list / double list / string list][common attr]

    `common_value_list_attrs`: [string list] 必填, 其中每个attr的类型均为：[int list / double list / string list][common attr]

    `item_key_attrs`: [string list] 必填, 其中每个attr的类型均为：[int / double / string][item attr]，长度需要与common_key_list_attrs相同，且一一对应

    `item_value_attrs`: [string list] 必填, 如果mapping_len=1, 其中每个attr的类型为：[int / double / string][item attr] 否则，每个attr类型为：[int list / double list / string list]，长度需要与common_value_list_attrs相同，且一一对应

    `mapping_len`: [int list] 选填, key 和 value的对应关系，即一个key对应几个value, 默认全为1，长度与common_value_list_attrs长度相同

    调用示例
    ------
    ``` python
    .attr_map_scatter_enrich(
        common_value_list_attrs = ["vallist1", "vallist2", "vallist3"],
        common_key_list_attrs = ["keylist1", "keylist2"],
        item_key_attrs = ["key1", "key2"],
        item_value_attrs = ["value1", "value2", "value3"],
        mapping_len = [1,2,1]
    )
    ```
    > 其中
    >   keylist1 = [k1,k2,k3], keylist2 = ["k1","k2","k3"]
    >   vallist1 = [v1,v2,v3], vallist2 = ["v1", "v2", "v3", "v4", "v5", "v6"], vallist3 = [v1.0, v2.0, v3.0]
    >   mapping_len = [1,2,1]
    > 某一个item: 
    >   key1 = k2, key2 = "k2"
    > 那么输出结果:
    >   value1 = v2, value2 = ["v3", "v4"], value3 = v2.0
    """
    self._add_processor(SeRecoAttrMapScatterEnricher(kwargs))
    return self

  def normalize_text_attr(self, **kwargs):
    """
    SeRecoNormalizationEnricher
    ------
    对 string 类型 item attr 进行去重音、大小写归一化

    参数配置
    ------

    `input_attr`: [string] 待归一化的text, string 类型

    `output_attr`: [string] 归一化 后生成的 text

    `input_common_attr`: [string] 选填, 输入的common attr

    `output_common_attr`: [string] 选填, 输出的common attr

    `mode`: [string] 归一化模式，默认nfkd，
    {
      "nfkd": NFKD归一化；转小写；处理连续空格；去掉重音
      "nfkd_only": NFKD归一化；处理连续空格；转小写
      "wide_len": 返回宽字编码的长度
      "burst": NFD归一化；转小写；去掉emoji；去掉diacritics；各种连接符、标点符号做空格处理
    }

    调用示例
    ------
    ``` python
    .normalize_text_attr(
      input_attr="query",
      output_attr="normalize_query",
    )
    ```
    """
    self._add_processor(SeRecoNormalizationEnricher(kwargs))
    return self

  def oversea_reco_merge_tables(self, **kwargs):
    """
    OverseaRecoMergeTablesMixer
    ------
    将多个 Table 合并成一个 Table, 相同 item 的同名单值 attr
    会根据merge_type进行合并，merge_type有:
    - default：默认的，返回第一个单值
    - concat：单值合并成多值
    - max：取attr里面最大值作为合并结果，仅支持int、float类型的attr
    - avg：取attr的平均值作为合并结果，仅支持int、float类型的attr
    - sum：取attr的求和值作为合并结果，仅支持int、float类型的attr
    - uniq：对attr进行去重，完全相等的只保留一个
    参数配置
    ------
    `input_tables`: [dict] 必填, 输入 table 列表
    - table_name: [string] table 名称
    - attrs [list] 需要合并到 output table 的 attr 集合，attr名字不要包含‘|’

    `output_table`: [string] 选填，输出 table 名称，默认为 "", 即为默认的主 table

    `item_attr_info`: [dict] 必填 标识 input_tables 中的 attrs 的类型,
    merge的类型, input_tables 中的所有 attr 都必须在这里配置类型
    - name : [string] 必填，attr名称
    - attr_type : [string] 必填，支持的类型：int, float, string
    - merge_type : [string] 非必填，支持的类型：default、max、avg、sum、uniq

    调用示例
    ------
    ``` python
    .oversea_reco_merge_tables(
        input_tables = [
          {"table_name": "term_es", "attrs": [ "negative_scores", "weight", "resource_type" ]},
          {"table_name": "prefix_es", "attrs": [ "negative_scores", "weight", "resource_type" ]}
        ],
        item_attr_info = [
          {"name":"negative_scores", "attr_type": "float", "merge_type": "max"},
          {"name":"weight", "attr_type": "float", "merge_type": "max"},
          {"name":"resource_type", "attr_type": "int"}
        ],
        output_table = ""
    )
    ```
    """
    self._add_processor(OverseaRecoMergeTablesMixer(kwargs))
    return self

  def string_helper_enrich(self, **kwargs):
    """
    SeRecoStringHelperEnricher
    ------
    对 string/string_list 类型 common attr 进行处理

    参数配置
    ------

    `input_common_attr`: [string] 必填, 输入的common attr

    `output_common_attr`: [string] 必填, 输出的common attr

    `mode`: [string] 处理模式， {"get_subwords","get_word_ngrams"}

    调用示例
    ------
    ``` python
    .string_helper_enrich(
      input_common_attr = "query",
      output_common_attr = "qp_emb_subwords",
      mode = "get_subwords"
    )
    ```
    """
    self._add_processor(SeRecoStringHelperEnricher(kwargs))
    return self

  def search_dict_init(self, **kwargs):
    """
    CommonSearchDictInitEnricher
    ------
    初始化搜索词典平台服务

    参数配置
    ------

    `enable_dict_server`: [bool] 词典服务gflags开关

    `dict_service_name`: [string] 词典平台服务名

    `output_common_attr`: [int] 词典初始化状态

    调用示例
    ------
    ``` python
    .search_dict_init(
      enable_dict_server = True,
      dict_service_name = "dict_service_name XXXX",
      output_common_attr = "is_dict_inited"
    )
    ```
    """
    self._add_processor(CommonSearchDictInitEnricher(kwargs))
    return self

  def prefix_edit_distance(self, **kwargs):
    """
    SeRecoPrefixEditDistanceEnricher
    ------
    计算前缀编辑距离

    参数配置
    ------

    `input_common_attr`: [string] prefix

    `input_item_attr`: [string] candidate

    `output_item_attr`: [string] prefix_edit_distance

    `max_lev_distance`: [int][动态参数] max_lev_distance

    `strip_accents`: [bool] 去重音

    `ignore_punctuation`: [bool] 去标点

    `ignore_blank`: [bool] 去空格

    调用示例
    ------
    ``` python
    .prefix_edit_distance(
      input_common_attr = "query",
      input_item_attr = "item_str",
      output_item_attr = "prefix_edit_distance",
      max_lev_distance = 2,
      strip_accents = True,
      ignore_punctuation = True,
      ignore_blank = True,
    )
    ```
    """
    self._add_processor(SeRecoPrefixEditDistanceEnricher(kwargs))
    return self

  def se_reco_gys_trend_log(self, **kwargs):
    """
    SeRecoTrendLogEnricher
    ------
    dump猜搜日志

    参数配置
    ------

    `input_strategy_common_attrs`: [list] 会放入dump日志strategy_info中的common attr

      - `attr_name`: [string] 需要dump的特证名

      - `field_name`: [string] 特征名对应存储的字段名

    `input_jv_item_attrs`: [string] 选填, 输出的common attr

      - `attr_name`: [string] 需要dump的特证名

      - `field_name`: [string] 特征名对应存储的字段名

    `from_page`: [int] 页面

    `max_photo_num`: [int] 从reco user info中dump视频最大数量

    `output_common_attr`: [string] 生成的日志message序列化string存储的common attr

    调用示例
    ------
    ``` python
    .se_reco_gys_trend_log(
        debug_log=False,
        from_page=5,
        max_photo_num=100,
        output_common_attr="gys_trend_log_pb_serialize",
        input_strategy_common_attrs=[
            {
                "attr_name": "kwai_source_0",
                "field_name": "kwai_source"
            }
        ],
        input_jv_item_attrs=[
            {
                "attr_name": "item_recall_reason",
                "field_name": "recall_reason"
            },
            {
                "attr_name": "ads_live_author_type",
                "field_name": "ads_live_author_type"
            },
        ]
    )
    ```
    """
    self._add_processor(SeRecoTrendLogEnricher(kwargs))
    return self

  def kv_cache_op(self, **kwargs):
    """
    SeRecoKvCacheEnricher
    ------
    提供local/remote 两层cache功能，支持读/写

    > 如果要使用RemoteCache，需要使用init_resource_manager算子，将cache_cluste事先初始化下，不然无法正确使用

    参数配置
    ------

    `op`: [string] cache操作：只能是 "read" / "write" / "create"，默认"read"

    `cache_name`: [string] 必填，要读写的cache名字, op="read"/"write"时生效

    `cache_config`: [list] 选填，local cache 的具体配置，只在op=create时生效

    `cache_config::cache_name`: [string] 必填，local cache 名字

    `cache_config::need_local`: [bool] 是否需要local cache，默认为True

    `cache_config::need_remote`: [bool] 是否需要remote cache，默认为False，need_local / need_remote不能同时为False，否则初始化失败

    `cache_config::cache_bit_size`: [int] local cache 大小，默认18，即cache大小为2^18

    `cache_config::cache_expir_s`: [int] local/remote cache 过期时间，单位秒，默认3600，即一小时

    `cache_config::cache_cluster`: [string] 必填，remote cache 集群名称

    `cache_config::cache_timeout_ms`: [int] remote cache 访问超时时间，默认20ms

    `config`: [list] list类型，每一个元素是一个KV配置，op=read/write时必填

    `config::is_common`: [bool] 该配置是common类型还是item类型

    `config::key_attr`: [string] 必填, 用作key的attr name，只支持int/double/string三种类型

    `config::value_attr`: [string] 必填, 用作value的attr name，只支持int/double/string三种类型

    `config::hit_attr`: [string] 选填, 该key是否cache命中，int类型，为1表示cache命中

    `config::key_prefix`: [string] key前缀，默认为空

    `config::value_type`: [string] value的类型，只支持int/double/string三种类型，默认为string，在op=read时生效，在op=write时不生效

    调用示例
    ------
    ``` python
    .kv_cache_op(
      op = "create",
      cache_config = [
        dict(
          cache_name = "local_cache_name_1",
          need_local = True,
          need_remote = True,
          cache_bit_size = 18,      # local
          cache_expir_s = 3600,     # local + remote
          cache_cluster = "xxx",    # remote
          cache_timeout_ms = 20     # remote
        )
      ]
    )
    .kv_cache_op(
      op = "read",
      name = "local_cache_name_1",
      config = [
        dict(
          is_common = True,
          key_attr = "key_attr1",
          value_attr = "value_attr1",
          key_prefix = "xxx_",
          value_type = "double"
        ),
        dict(
          is_common = False,
          key_attr = "key_attr2",
          value_attr = "value_attr2",
          key_prefix = "yyy_",
          value_type = "string"
        )
      ],
    )
    ```
    """
    self._add_processor(SeRecoKvCacheEnricher(kwargs))
    return self

  def json_generate_with_attr(self, **kwargs):
    """
    SeRecoJsonGenerateWithAttrEnricher
    ------
    将指定attr填充为json

    > 支持几种方式：
    > 
    >   common -> common : 所有attr平铺json
    > 
    >   [common +] item -> common : json第一层为两个key(common, item)，其中common值为common attr平铺的json，item值为json list，每个对象是item attr平铺的json
    > 
    >   item -> item : 所有attr平铺json
    > 
    >   common [+ item] -> item : 每个item json格式同上，包含两个key(common, item)，其中common信息是相同的，item信息是每个item各自的

    参数配置
    ------

    `common_attrs`: [string list] 所有输入的common attr，只支持int/int list/double/double list/string/string list六种类型

    `item_attrs`: [string list] 所有输入的item attr，只支持int/int list/double/double list/string/string list六种类型

    `common_json_attrs`: [string list] 选填，输入的common attr里面类型为json的attr集合，会在组成json的时候以json形式而不是string形式，只有在attr类型为string时生效

    `item_json_attrs`: [string list] 选填，输入的item attr里面类型为json的attr集合，会在组成json的时候以json形式而不是string形式，只有在attr类型为string时生效

    `export_json_attr`: [string] 必填，输出json的attr，为string类型的attr

    `is_common`: [bool] 输出的json为common维度还是item维度，默认True

    调用示例
    ------
    ``` python
    .json_generate_with_attr(
      common_attrs = [],
      item_attrs = [],
      common_json_attrs = [],
      item_json_attrs = []
      export_json_attr = ""
      is_common = False,
    )
    ```
    """
    self._add_processor(SeRecoJsonGenerateWithAttrEnricher(kwargs))
    return self

  def query_in_list_filter(self, list_attr_name: str, target_sources: any = None, **kwargs):
    """
    CommonRecoRuleFilterArranger
    ------

    对过滤的一个封装, 实现过滤掉item_id在指定common attr中的item

    参数配置
    ------

    `list_attr_name`: [string] 需要过滤的list_string类型的common attr的名字

    `target_sources`: [string_list] 选填, 只对item_source属于这个list_string的item进行过滤, 不填则对全部item进行过滤

    调用示例
    ------
    ``` python
    .query_in_list_filter('browse_queries', target_sources),
    ```
    """
    rule = {
        "attr_name": "item_id",
        "remove_if": "in",
        "compare_to": "{{" + list_attr_name + "}}"
    }
    if target_sources is not None:
        rule = {
            "join": "and",
            "filters": [
                    rule,
                    {
                        "attr_name": "item_source",
                        "compare_to": target_sources,
                        "remove_if": "in"
                    }
            ]
        }
    kwargs["rule"] = rule
    self.filter_by_rule(**kwargs)
    return self


  def query_length_filter(self, min_length: int = 1, max_length: int = 10, target_sources: any = None, **kwargs):
    """
    CommonRecoRuleFilterArranger
    ------

    对过滤的一个封装, 实现过滤掉item_id长度 <= min_length 和 >= max_length的item

    参数配置
    ------

    `min_length`: [int] 最小长度

    `max_length`: [int] 最大长度

    `target_sources`: [string_list] 选填, 只对item_source属于这个list_string的item进行过滤, 不填则对全部item进行过滤

    调用示例
    ------
    ``` python
    .query_length_filter(max_length=15, target_sources=target_sources)
    ```
    """
    rule = {
        "join": "or",
        "filters": [
                {
                    "attr_name": "item_id_len",
                    "remove_if": ">=",
                    "compare_to": max_length
                },
                {
                    "attr_name": "item_id_len",
                    "remove_if": "<=",
                    "compare_to": min_length
                }
        ]
    }
    if target_sources is not None:
        rule = {
            "join": "and",
            "filters": [
                    rule,
                    {
                        "attr_name": "item_source",
                        "compare_to": target_sources,
                        "remove_if": "in"
                    }
            ]
        }
    kwargs["rule"] = rule
    self.enrich_attr_by_light_function(
        import_item_attr=["item_id"],
        export_item_attr=["item_id_len"],
        function_name="GetItemUTF8CharNum",
        class_name="SeRecoLightFunctionSet",
    ).filter_by_rule(**kwargs)
    return self

  def get_list_attr_size(self, input_common_attr_name: str, output_common_attr_name: str, **kwargs):
    """
    CommonRecoLuaAttrEnricher
    ------

    获取一个list类型的common attr长度, 不存在则返回0

    参数配置
    ------

    `input_common_attr_name`: [string] 需要获取长度的attr名

    `output_common_attr_name`: [string] 长度存储到的attr名

    调用示例
    ------
    ``` python
    .get_list_attr_size(input_common_attr_name="refer_query_list", output_common_attr_name="refer_query_list_size")
    ```
    """
    self.enrich_attr_by_lua(
        import_common_attr=[input_common_attr_name],
        export_common_attr=[output_common_attr_name],
        function_for_common="get_size",
        lua_script=f"""
            function get_size()
                if not {input_common_attr_name} then
                    return 0
                end
                return #{input_common_attr_name}
            end
        """,
        **kwargs
    )
    return self

  def enrich_by_dict(self, **kwargs):
    """
    SeRecoDictEnricher
    ------

    从词典获取结果填充到对应Attr

    参数配置
    ------

    `dict_type`: [string] 词典类型, 目前支持 HashMapData/HashSetData/FileLineData

    `namespace`: [string] 选填, 词典namespace, 默认为video

    `dict_name`: [string] 词典名

    `dict_conf`: [string] 选填, 词典配置, 默认为空, 一般不需要填

    `input_common_attr`: [string] 选填, 输入的common attr, 支持 string和string_list类型

    `output_common_attr`: [string] 选填, 输出的common attr, 有input_common_attr时需要填, HashMapData填充string/string_list类型, HashSetData类型填充int(true=1, false=0)/int_list类型

    `input_item_attr`: [string] 选填, 输入的item attr, 支持 string和string_list类型

    `output_item_attr`: [string] 选填, 输出的item attr, 有input_item_attr时需要填, HashMapData填充string/string_list类型, HashSetData类型填充int(true=1, false=0)/int_list类型

    `random_sample_count` [int][动态参数] 选填, 仅在dict_type为HashSetData/FileLineData时生效, 随机获取一定数量的结果, 填充至output中, 若为item, 则每次都随机. 与input attr互斥

    调用示例
    ------
    ``` python
    .enrich_by_dict(dict_type="HashMapData", dict_name="rs_merchant_query_score", input_item_attr="item_id", output_item_attr="item_gmv_score_str")
    ```

    ``` python
    .enrich_by_dict(dict_type="HashSetData", dict_name="rs_merchant_query_score", output_item_attr="item_gmv_score_str", random_sample_count=50)
    ```
    """
    self._add_processor(SeRecoDictEnricher(kwargs))
    return self

  def gys_jubao_kv_rank(self, **kwargs):
    """
    SeRecoJubaoKVRankEnricher
    ------
    按照kv的形式调用Jubao服务进行rank, 获取后续排序需要的score等信息, 按设置的batch size分别请求

    参数配置
    ------
    `caller_name`: [string] 请求名

    `thread_num`: [int] 选填, rpc异步线程数, 默认8

    `rpc_name`: [string] 请求具体服务的rpc

    `business`: [string] 业务的business标识

    `batch_size`: [int] 选填, 单个请求的batch size大小, 默认100

    `time_out`: [int] 选填, 超时时间(ms), 默认100ms

    `enable_ack`: [bool] 选填, 是否使用ack回流, 默认true

    `input_query_common_attr_name`: [string] 选填, query字段的的common attr名称, 有的业务需要

    `input_source_common_attr_name`: [string] 选填, source字段的的common attr名称, 有的业务需要

    `input_photo_id_common_attr_name`: [string] 选填, photo_id字段的common attr名称, 有的业务需要

    `input_item_id_item_attr_name`: [string] 选填, 获取item id的item attr名称, 默认为item_id

    `input_item_recall_reason_item_attr_name`: [string] 选填, 获取item recall resason的item attr名称, 默认为item_recall_reason

    `input_item_source_item_attr_name`: [string] 选填, 获取item source的item attr名称, 默认为item_source

    `output_rank_resp_item_attr_name`: [string] 输出Jubao结果(ks::search::jubao::JubaoItem)的item attr名称

    `input_common_attrs`: [list] 通过kv形式传给jubao的common attr名和对应字段名

      - `attr_name`: [string] 需要传给jubao的attr名

      - `field_name`: [string] attr对应jubao侧的字段名

    `input_item_attrs`: [list] 通过kv形式传给jubao的item attr名和对应字段名

      - `attr_name`: [string] 需要传给jubao的attr名

      - `field_name`: [string] attr对应jubao侧的字段名

    调用示例
    ------
    ``` python
    .gys_jubao_kv_rank(
        caller_name = "gys_reco_dragon",
        thread_num = 8,
        rpc_name = "grpc_jubao_rank_test",
        business = "cmt_gys",
        batch_size = 100,
        time_out = 100,
        enable_ack = True,
        input_item_id_item_attr_name = "item_id",
        output_rank_resp_item_attr_name = "jubao_item",
        input_source_common_attr_name = "source",
        input_common_attrs = [
            {
                "attr_name": "kwai_source_0",
                "field_name": "kwai_source"
            }
        ],
        input_item_attrs = [
            {
                "attr_name": "query_switch_user_id",
                "field_name": "query_switch_user_id"
            }
        ]
    )
    ```
    """
    self._add_processor(SeRecoJubaoKVRankEnricher(kwargs))
    return self

  def filter_item_results_not_in_any_list(self, **kwargs):
    """
    SeRecoItemResultsFilterArranger
    ------
    filter_by_item_results 改版, 根据需要的指向 result_list 的 extra_common_attrs ， 取交集后为 result_set 。 对现有 result_list 过滤掉不在 result_set 内的 result 。

    一般配合 enrich_by_sub_flow 使用 。

    参数配置
    ------
    
    `remove_if_not_all_in`: [list] 指定需要合并的 result_list 的 extra common attr 列表, 会将列表内指向的 result_lists 取交集为一个 set, 用于过滤掉不在 set 内的 result 。

    `filter_reason_attr_name`: [string] 需要输出过滤的信息的 attr_name, 默认为空

    `item_id_str_attr_name`: [string] 过滤信息打印的 item_attr name, 默认为空

    调用示例
    ------
    ``` python
    results_list=[]
    for filter in filter2_filters:
      filter_name = filter.__name__
      target_sources = get_target_source(filter_name)
      subflow=SeRecoFlow(filter_name)
      filter(subflow, target_sources)
      save_result = filter_name + "_rm_list"
      results_list.append(save_result)
      flow.enrich_by_sub_flow(name = filter_name ,sub_flow = subflow, save_results_to=save_result)
    flow.filter_item_results_not_in_any_list(
        remove_if_not_all_in = results_list
    )
    ```
    """
    self._add_processor(SeRecoItemResultsFilterArranger(kwargs))
    return self
  
  def get_id_mapping(self, **kwargs):
    """
    SeRecoGetIdMappingEnricher
    ------

    参数配置
    `save_mapping_id_to_attr`: [string] 选填, 保存idmapping 指针的的attr名
  
    """
    self._add_processor(SeRecoGetIdMappingEnricher(kwargs))
    return self
    
  def se_reco_author_not_live_filter(self, **kwargs):
    """
    SeRecoAuthorNotLiveFilterArranger
    -----
    过滤掉未开播的用户
    参数配置
    ------
    `filter_by_attr_name`: [string] [必填] 根据哪个item_attr作为author_id过滤, 需要为int类型

    `item_source_attr_name`: : [string] [选填] item_source的字段名, 默认为item_source

    `filter_sources`: [string] [选填] 需要过滤的source, 当有值时item_source不在filter_sources的不会进行过滤

    `no_filter_sources`: [string] [选填] 不需要过滤的source, 当有值时item_source在no_filter_sources的不会进行过滤

    调用示例
    ------
    ``` python
    .se_reco_author_not_live_filter(
            debug_log=False,
            filter_by_attr_name = "item_author_id",
        )
    ```
    """
    self._add_processor(SeRecoAuthorNotLiveFilterArranger(kwargs))
    return self

  def retrieve_by_collection_info(self, **kwargs):
    """
    SeRecoCollectionInfoRetriever
    -----
    从合集服务返回的合集详情 Response 召回续播合集

    参数配置
    ------
    `response_attr_name`: [string] 从合集服务获取详情的response

    `collection_id_list_attr_name`: : [string] 续播合集列表的attr名

    `photo_id_list_attr_name`: [string] 续播合集对应的视频id(加密)的attr名

    `item_source`: [string] 召回的source

    `item_recall_reason`: [string] 召回的reason

    `reason_id`: [int] dragon的reason id

    调用示例
    ------
    ``` python
    .retrieve_by_collection_info(
            response_attr_name = "collection_info_resp",
            collection_id_list_attr_name = "continuePlayCollectionIds",
            photo_id_list_attr_name = "continuePlayCollectionCurrentPhotoIds",
            item_source = "continue_play_collection",
            item_recall_reason = "continue_play_collection",
            reason_id = 999,
        )
    ```
    """
    self._add_processor(SeRecoCollectionInfoRetriever(kwargs))
    return self

  def get_author_update_photos(self, **kwargs):
    """
    SeRecoAuthorUpdatePhotosEnricher
    -----
    获取作者最近更新的且未被当前用户点击的视频

    参数配置
    ------
    `input_author_id_item_attr_name`: : [string] [选填] 获取作者id的item attr名, 默认为item_author_id

    `input_click_photos_common_attr_name`: [string] [必填] 当前用户的点击序列, 用于排除视频

    `output_update_photos_item_attr_name`: [string] [选填] 结果存储的item attr名, 默认为item_update_photos

    `past_days`: [int] [选填] 取最近几天内更新的视频, 默认为 7

    `redis_cluster`: [string] [选填] 获取更新视频的redis名, 默认为 searchRs

    `redis_timeout`: [int] [选填] 从redis获取数据的超时时间(ms), 默认为 20

    `redis_key_prefix`: [string] [选填] 从redis获取数据的key前缀, 默认为 apl_


    调用示例
    ------
    ``` python
    .get_author_update_photos(
            input_author_id_item_attr_name = "item_author_id",
            input_click_photos_common_attr_name = "reco_click_photo_list",
            output_update_photos_item_attr_name = "item_update_photos",
        )
    ```
    """
    self._add_processor(SeRecoAuthorUpdatePhotosEnricher(kwargs))
    return self

  def query_normalize2(self, **kwargs):
    """
    SeRecoQueryNomalize2Enricher
    ------
    sugg使用的 query nomalize v2

    参数配置
    ------

    `input_common_attr`: [string] 选填, 输入的common attr, 支持string和string_list类型

    `output_common_attr`: [string] 选填, 输出的common attr, 根据input格式输出string和string_list类型

    `input_item_attr`: [string] 选填, 输入的item attr, 支持string和string_list类型

    `output_item_attr`: [string] 选填, 输出的item attr, 根据input格式输出string和string_list类型

    调用示例
    ------
    ``` python
    .query_normalize2(
        input_common_attr="user_name",
        output_common_attr="user_name_normalize2",
    )
    ```
    """
    self._add_processor(SeRecoQueryNomalize2Enricher(kwargs))
    return self
  
  def retrieve_from_recall_lib(self, **kwargs):
    """
    SeRecoRecallLibRetriever
    ------
    搜索推荐从 recall lib 召回，并根据需要解析对应的 extra info

    参数配置
    ------
    `reason_id`: [int] 必填 为大于零的整数

    `recall_name`: [string] 必填 用来表示召回队列的名字

    `item_id_attr_name`: [string] 必填，表示召回的 item， 默认为 item_id

    `item_author_id_attr_name` [string] 选填， 召回的 user_id，默认为 item_author_id

    `item_source_name`: [string] 选填 召回来源，默认为 item_source

    `item_recall_reason_attr_name`: [string] 选填 召回理由的名字，默认为item_recall_reason

    `item_score_attr_name_`: [string] 选填 召回分的名字，默认为 item_score

    `prefix_full_recall_dict_name`: [string] 选填，满召词典，如果 query 命中词典，则跳过本次召回，默认为空

    `query_attr_name`: [string] 必填，表示请求的 query，默认为 query

    `raw_query_attr_name`: [string] 选填，表示请求的原始 query，默认为空

    `city_attr_name`: [string] 选填，表示城市名，默认为空

    `refer_photo_id_name`: [string] 选填，referVideoId， 默认为空

    `reco_click_list_photo_id`: [int] 选填，默认为空

    `client_info_attr_name`: [string] 选填，client info 信息

    `embedding_attr_name`: [string] 选填，获取 item embedding 信息

    `item_extra_attr_conf`: [dict] 选填，根据 name 找到 result extra_info 中的 kv 并生成 item attr
    - name : [string] 必填，extra_info中的 名称
    - type : [string] 必填，支持的类型：int, float, string, int_list, float_list, string_list
    
    `common_extra_attr_conf`: [dict] 选填，根据 name 找到 response extra_info 中的 kv 并生成 common attr
    - name : [string] 必填，extra_info中的 名称
    - type : [string] 必填，支持的类型：int, float, string, int_list, float_list, string_list

    调用示例
    ------
    ``` python
    .retrieve_from_recall_lib(
      reason_id=1001,
      recall_name="refer_recall",
      item_id_attr_name="item_id",
      item_extra_attr_types = [
        {"name":"poi",  "type": "string"},
        {"name":"g2q",  "type": "string"},
      ]
    )
    ```
    """
    self._add_processor(SeRecoRecallLibRetriever(kwargs))
    return self

  def retrieve_by_prefix(self, **kwargs):
    """
    SeRecoPrefixRetriever
    ------
    搜索推荐从前缀树召回，并根据需要解析对应的正排，支持分片和单机版，分片需要设置 batch_size 大于 0

    参数配置
    ------
    `result_num`: [int] 必填 返回结果的总数

    `batch_size`: [int] 选填 表示每个分片请求多少结果，分片服务时必填大于0的数，默认为 0

    `min_qlen`: [int] 选填 query 长度小于该值时，跳过本次请求，默认为 0

    `timeout_ms`: [int] 选填 请求前缀索引服务的超时时间，默认为 50

    `async_thread_num`: [int] 选填，batch_size 大于 0 时，设置该值有效，用来设置分片服务的线程数
    
    `reason_id`: [int] 必填 为大于零的整数

    `recall_name`: [string] 必填 用来表示召回队列的名字

    `item_id_attr_name`: [string] 必填，表示召回的 item， 默认为 item_id

    `item_author_id_attr_name` [string] 选填， 召回的 user_id，默认为 item_author_id

    `item_source_name`: [string] 选填 召回来源，默认为 item_source

    `item_recall_reason_attr_name`: [string] 选填 召回理由的名字，默认为item_recall_reason

    `item_score_attr_name_`: [string] 选填 召回分的名字，默认为 item_score

    `prefix_full_recall_dict_name`: [string] 选填，满召词典，如果 query 命中词典，则跳过本次召回，默认为空

    `query_attr_name`: [string] 必填，表示请求的 query，默认为 query

    `raw_query_attr_name`: [string] 选填，表示请求的原始 query，默认为空

    `prefix_score_name`: [string] [动态参数] 选填, 对于支持多 slot 场景下的索引，可以通过该参数指定 slot 对应的分数排序，默认为空

    `item_extra_attr_conf`: [dict] 选填，根据 name 找到 result extra_info 中的 kv 并生成 item attr
    - name : [string] 必填，extra_info中的 名称
    - type : [string] 必填，支持的类型：int, float, string, int_list, float_list, string_list

    调用示例
    ------
    ``` python
    .retrieve_by_prefix(
      reason_id=1001,
      recall_name="refer_recall",
      item_id_attr_name="item_id",
      item_extra_attr_types = [
        {"name":"poi",  "type": "string"},
        {"name":"g2q",  "type": "string"},
      ]
    )
    ```
    """
    self._add_processor(SeRecoPrefixRetriever(kwargs))
    return self

  def check_match_user_name(self, **kwargs):
    """
    SeRecoCheckMatchUserNameEnricher
    ------
    sugg使用check match user name

    参数配置
    ------

    `version`: [int] 选填, 使用的match逻辑版本, 默认为3, 支持3, 4

    `prefix_match_count`: [int] 最小前缀匹配数量, 默认为1

    `input_normalized_query_common_attr`: [string] 归一化后的query attr名

    `input_query_pinyin_common_attr`: [string] 选填, v4版本可以不填, 归一化后的query的拼音 attr 名

    `is_common_attr`: [bool] 选填, 是否是common attr, 默认为False

    `input_normalized_user_name_attr`: [string] 归一化后的用户名attr名, 当 is_common_attr = True时为common attr, 否则为item attr

    `output_match_pos_attr`: [string] 最终match位置的输出attr名, 当 is_common_attr = True时为common attr, 否则为item attr


    调用示例
    ------
    ``` python
    .check_match_user_name(
        version=4,
        prefix_match_count=1,
        input_normalized_query_common_attr="normalized_query",
        input_query_pinyin_common_attr="query_pinyin",
        input_normalized_user_name_attr="normalized_user_name",
        output_match_pos_attr="user_name_match_pos",
    )
    ```
    """
    self._add_processor(SeRecoCheckMatchUserNameEnricher(kwargs))
    return self

  def calc_embedding_score(self, **kwargs):
    """
    SeRecoEmbeddingScoreEnricher
    ------
    计算 item embedding 的相关分数

    参数配置
    ------

    `input_common_embedding_attr`: [string] 必填, 输入的 common embedding attr , 仅支持 float list 类型, 且 size 是 embedding_size 的倍数

    `input_query_list_attr`: [string] 必填, 输入的 common query attr, 仅支持 string list 类型, 且 size 和 input_common_embedding_attr 相同
  
    `input_item_id_attr`: [string] 必填, 输入的 item query attr, 仅支持 string 类型

    `input_item_embedding_attr`: [string] 必填, 输入的 item embedding attr, 仅支持 float list 类型, 且 size 等于 embedding_size

    `embedding_size`: [int] 选填, embeding 的维度, 默认 64

    `max_embedding_num_attr`: [string] 选填, attr value 为参与计算的最多 embedding 个数, 默认为 0, 都参与

    `decay_factor_attr`: [string] 选填, attr value 为衰减因子, 默认 -0.01

    `threshold_attr`: [string] 选填, attr value 为 分数阈值, 默认 0.5

    `max_output_num`: [int] [动态参数] 选填, 最大输出个数, 默认是 0, 都输出

    `output_query_list_attr`: [string] 必填, 输出的 query list attr name

    `output_score_list_attr`: [string] 选填, 输出的 score list attr name

    `output_gene_info_attr`: [string] 选填, 输出的 generate info list attr name


    调用示例
    ------
    ``` python
    .calc_embedding_score(
        input_common_embedding_attr = "photo_embedding_query_list",
        input_query_list_attr = "photo_embedding_vec_list",
        input_item_id_attr = "item_id",
        input_item_embedding_attr = "embedding",
        embedding_size = 64,
        decay_factor = -0.01,
        max_embedding_num = 10,
        output_query_list_attr = "refer_goods_query_list",
        output_score_list_attr = "refer_goods_score_list",
        output_gene_info_attr = "refer_goods_gene_info_list",
    )
    ```
    """
    self._add_processor(SeRecoEmbeddingScoreEnricher(kwargs))
    return self
  
  def enrich_by_map(self, **kwargs):
    """
    SeRecoMapEnricher
    ------
    通过map来设置attr

    参数配置
    ------

    `key_list_attr_name`: [string] 存储map的key列表的common attr名, 支持string_list/int_list

    `value_list_attr_name`: [string] 存储map的value列表的common attr名, 支持string_list/int_list/double_list, 与key对齐

    `is_common_attr`: [bool] 是否为common attr, 默认为true

    `input_attr_name`: [string] 从map中获取结果的key的attr名, 根据key_list_attr的类型决定基础类型, 支持单值和list, 根据is_common_attr决定是否为 common attr

    `output_attr_name`: [string] 结果存储的attr名, 根据value_list_attr的类型决定基础类型, 根据input_attr决定单值或list, 根据is_common_attr决定是否为 common attr


    调用示例
    ------
    ``` python
    .enrich_by_map(
        key_list_attr_name="user_fans_cont_map_keys",
        value_list_attr_name="user_fans_cont_map_values",
        is_common_attr=False,
        input_attr_name="item_author_id",
        output_attr_name="item_fans_count",
    )
    ```
    """
    self._add_processor(SeRecoMapEnricher(kwargs))
    return self

  def wstring_length(self, **kwargs):
    """
    SeRecoWstringLengthEnricher
    ------
    将 string 类型转化成 wstring , 返回宽字符的个数

    参数配置
    ------

    `input_common_attr`: [string] 输入的 common attr, 仅支持 string 类型

    `output_common_attr`: [string] 输出的 common attr, int 类型

    `input_item_attr`: [string] 输入的 item attr, 仅支持 string 类型

    `output_item_attr`: [string] 输出的 item attr, int 类型


    调用示例
    ------
    ``` python
    .wstring_length(
        input_common_attr="query",
        output_common_attr="w_query_size",
    )
    ```
    """
    self._add_processor(SeRecoWstringLengthEnricher(kwargs))
    return self

  def streaming_model_with_subflow(self, **kwargs):
    """
    StreamingModelEnricher
    ------
    流式请求AIP团队FTIE方式部署的模型，使用sub_flow处理流式结果
    
    整个过程分为两个线程：process 线程和 infer 线程：
    其中 infer 线程采用 grpc streaming 的方式和 AIP 交互，拿到 token 之后写入 token 队列；
    process 线程读取 token 队列，然后交给 sub_flow 去处理

    参数配置
    ------

    `service`: 【必填】模型部署的kess_name，默认为空

    `service_group`: 模型部署的kess service group，默认为“PRODUCTION”

    `model_name`: 【必填】模型名字

    `model_suffix`: 模型名字后缀，拼接infer_id使用，默认“base”

    `prompt_attr`: 【必填】输入的prompt变量名【string common】

    `answer_attr`: 模型生成的答案的变量名，默认“streaming_model_answer” 【string common】

    `request_info`: infer_request里面需要传入的参数，为list

    `attr_prefix`: 算子内部与sub_flow交互的attr name的前缀，默认是空字符串

    `infer_total_timeout_ms`: infer最大超时时间，默认30000

    `max_token_size_per_process`: 动态参数，每次最少处理的token数，默认10【攒到10个token才会调用一次sub_flow】

    `sub_flow`: 流式处理 token 的 sub_flow 【串行处理，不会新起线程】

    `merge_common_attrs`: [list] 合并 sub_flow 返回的 common attr

    `pass_common_attrs`: [list] 执行 sub_flow 携带的初始 common attr

    `deep_copy`: [bool] 将 pass_common_attrs 以深拷贝/浅拷贝的方式传入 sub_flow, 默认为false

    > sub_flow 初始输入 common attrs:
    > * attr_prefix + "all_answer": 当前的所有结果
    > * attr_prefix + "un_processed_answer": 本次 sub_flow 要处理的结果
    > * attr_prefix + "current_stopped": 最近一次 token 是否返回 stopped
    > * attr_prefix + "current_finished": 最近一次 token 是否返回 finished
    > * attr_prefix + "current_offset": 最近一次 token 的 offset
    > * attr_prefix + "current_error_code": 最近一次 token 的 error_code
    > * attr_prefix + "current_error_msg": 最近一次 token 的 error_msg
    > * attr_prefix + "is_infer_end": 当前 infer 推理的状态码：0【还未结束】1【正常结束】2【模型推理失败】3【prompt过长】
    > sub_flow 输出 common attrs:
    > * attr_prefix + "should_exit": process线程是否应该立即退出 0【否】1【是】
    > * attr_prefix + "is_mid_stop": infer线程是否应该立即退出，停止推理 0【否】1【是】

    调用示例
    ------
    ``` python
    .streaming_model_with_subflow(
        service = "",
        model_name = "",
        model_suffix = "",
        prompt_attr = "",
        answer_attr = "",
        request_info = [],
        sub_flow = MyFlow
    )
    ```
    """
    self._add_processor(StreamingModelEnricher(kwargs))
    return self

  def enrich_item_by_generic_grpc(self, **kwargs):
    """
    SeRecoItemGenericGrpcEnricher
    ------
    通用 grpc 调用接口。将 request_attr 作为 request 发送到相应的 grpc 服务，将 response 存入 response_attr

    与dragon提供的 enrich_by_generic_grpc 功能基本一致, 只是针对 item 做处理

    参数配置
    ------
    `kess_service`: [string] [动态参数] grpc 服务的 kess 服务名

    `service_group`: [string] grpc 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 grpc 服务的超时时间，默认值为 200

    `method_name`: [string] rpc 服务的方法名, 格式 为 /{rpc.package}.{rpc.service}/{rpc.method}, 当前支持所有链接的 grpc 方法名。

    `request_attr`: [string] 存储将要发送给 grpc 服务的 request 的 item attr

    `response_attr`: [string] 存储 grpc 服务接收到的 request 的 item attr

    `response_class`: [string] grpc 服务接收的 response 的 类型，当前支持所有链接的 Message 类型。

    调用示例
    ------
    ``` python
    .enrich_item_by_generic_grpc(
      kess_service="grpc_SomeUserProfileService",
      timeout_ms=200,
      method_name="/kuaishou.reco.RecoUserProfile/Load",
      request_attr="user_info_request",
      response_attr="user_info_response",
      response_class="kuaishou.reco.RecoUserProfileResponse"
    )
    ```
    """

    kwargs["response_class"] = kwargs["response_class"].replace("::", ".")
    self._add_processor(SeRecoItemGenericGrpcEnricher(kwargs))
    return self

  def enrich_by_generic_shard_grpc(self, **kwargs):
    """
    SeRecoItemGenericGrpcEnricher
    ------
    通用分 shard grpc 调用接口
    
    request_attr 为 request list, 调用 grpc 的所有 shard接口, 将 response list 存入 response_attr,
    
    其类型均为指针类型: std::vector<shared_ptr<::google::protobuf::Message>>*

    与dragon提供的 enrich_by_generic_grpc 功能基本一致, 只是针对 grpc 所有 shard 做处理

    参数配置
    ------
    `kess_service`: [string] [动态参数] grpc 服务的 kess 服务名

    `service_group`: [string] grpc 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 grpc 服务的超时时间，默认值为 200

    `method_name`: [string] rpc 服务的方法名, 格式 为 /{rpc.package}.{rpc.service}/{rpc.method}, 当前支持所有链接的 grpc 方法名。

    `request_attr`: [string] 存储将要发送给 grpc 服务的 request list 的 common attr

    `response_attr`: [string] 存储 grpc 服务接收到的 response list 的 common attr

    `response_class`: [string] grpc 服务接收的 response 的 类型，当前支持所有链接的 Message 类型。

    调用示例
    ------
    ``` python
    .enrich_by_generic_shard_grpc(
      kess_service="grpc_SomeUserProfileService",
      timeout_ms=200,
      method_name="/kuaishou.reco.RecoUserProfile/Load",
      request_attr="user_info_request",
      response_attr="user_info_response",
      response_class="kuaishou.reco.RecoUserProfileResponse"
    )
    ```
    """

    kwargs["response_class"] = kwargs["response_class"].replace("::", ".")
    self._add_processor(SeRecoShardGenericGrpcEnricher(kwargs))
    return self

  def spelling_correction_from_kconf(self, **kwargs):
    """
    SeRecoSpellingCorrectionEnricher
    ------
    根据kconf纠错

    参数配置
    ------
    `input_item_attr`: [string] 必填, 待纠错的 item attr name

    `output_item_attr`: [string] 必填, 输出的 item attr name

    `kconf_path`: [string] kconf 的路径，默认

    调用示例
    ------
    ``` python
    .spelling_correction_from_kconf(
      input_item_attr="item_id",
      output_item_attr="corrected_item_id",
      kconf_path="",
    )
    ```
    """

    self._add_processor(SeRecoSpellingCorrectionEnricher(kwargs))
    return self

  def is_intersection(self, **kwargs):
    """
    SeRecoIntersectionEnricher
    ------
    判断两个list类型的数据是否有交集, 有则设置1, 否则设置0

    参数配置
    ------
    `is_common_attr`: [bool] 是否为common attr, 默认为True

    `input_attr_name`: [string] 必填, 输入的attr名

    `output_attr_name`: [string] 必填, 输出的attr名

    `compare_to` : [list] [动态参数] 必填, 要比较的数据, 可以来自于一个common attr

    调用示例
    ------
    ``` python
    .is_intersection(
      input_attr_name="features",
      output_attr_name="is_intersection",
      compare_to=["A", "B", "C"],
    )
    ```
    """
    self._add_processor(SeRecoIntersectionEnricher(kwargs))
    return self

  def enrich_index_attr(self, **kwargs):
      """
      SeRecoIndexAttrEnricher
      ------
      生成索引相关的 item attr

      参数配置
      ------
      `index_start`: [int] 选填, 索引的起始值, 默认为 0

      `index_pace`: [int] 必填, 每次的步长, 默认为 1

      `output_item_attr`: [string] 必填, 输出的 item attr name

      调用示例
      ------
      ``` python
      .enrich_index_attr(
        output_item_attr="item_index_before_rank",
      )
      ```
      """
      self._add_processor(SeRecoIndexAttrEnricher(kwargs))
      return self
  
  def se_reco_sug_recall_filter(self, **kwargs):
    """
    SeRecoRecallFilterArranger
    ------
    sug recall 的风控 含有多种，敏感词，交互词审核等

    参数配置
    ------
    `review_filter_kconf`: [string] 动态参数, 配置的 kconf, 默认为空

    `skip_review_cfg`: [string] 动态参数, 逃过的召回源 source list

    `item_id_attr_name`: [string] 必填, 默认为 item_id

    `item_source_attr_name`: [string] 必填, 默认 item_source 

    `hotword_rank_type`: [string] 必填, 默认是 hotword_rank_type 含有该 item attr 跳过风控

    调用示例
    ------
    ``` python
    .se_reco_sug_recall_filter(
        item_source_attr_name = "item_source",
        item_id_attr_name = "item_id"
    )
    ```
    """
    self._add_processor(SeRecoRecallFilterArranger(kwargs))
    return self

  def aigc_antispam_enrich(self, **kwargs):
    """
    SeRecoAigcAntispamEnricher
    ------
    aigc 内容的风控，包含 antispam、sensitive_v3、sensitive_v2、kconf 兜底四个过程

    输入输出为 list 类型, 采用异步方式并行处理

    参数配置
    ------
    `request_json_attr`: [string] [string common attr] 选填, antispam 的输入 json 内容

    `content_list_attr`: [string] [string list common attr] 必填, 要过风控的内容列表

    `antispam_code_attr`: [string] [int list common attr] 选填, 风控返回的状态码

    `antispam_status_attr`: [string] [int list common attr] 选填, 风控返回的命中状态 

    `content_type`: [string] content 类型, 用来将每一个 content 替换 json 中的具体字段, 默认 query

    `enable_antispam`: [bool] 是否使用 antispam 能力, 默认为 True

    `enable_sensitive_v2`: [bool] 是否使用 sensitive_v2 能力, 默认为 True

    `enable_sensitive_v3`: [bool] 是否使用 sensitive_v3 能力, 默认为 True

    `enable_kconf`: [bool] 是否使用 kconf 兜底能力, 默认为 True

    `rpc_config::antispam_rpc_name`: [string] antispam rpc 名字, 默认为 grpc_antispamRpcAigcSearchChatService

    `rpc_config::sensitive_v2_rpc_name`: [string] sensitive_v2 rpc 名字, 默认为 grpc_sensitiveV2RpcServiceSearch

    `rpc_config::sensitive_v3_rpc_name`: [string] sensitive_v3 rpc 名字, 默认为 grpc_sensitiveRpcServiceSearch

    `rpc_config::kconf_name`: [string] kconf 名字, 默认为 riskControl.bigFile.SearchQASensitiveWords

    `rpc_config::antispam_timeout_ms`: [int] antispam rpc 超时时间, 默认为 100ms

    `rpc_config::sensitive_v2_timeout_ms`: [int] sensitive_v2 rpc 超时时间, 默认为 50ms

    `rpc_config::sensitive_v3_timeout_ms`: [int] sensitive_v3 rpc 超时时间, 默认为 50ms

    `rpc_config::event_type`: [string] antispam event type, 默认为 AIGC_SEARCH_CHAT

    `rpc_config::channel_v2`: [string] sensitive_v2 channel, 默认为 NEW_COMPREHENSIVE_SEARCH_AI_ANSWER

    `async_config::thread_num`: [int] 线程池大小, 默认为 10

    `async_config::max_task_size`: [int] 线程池中任务队列的大小, 默认为 100

    调用示例
    ------
    ``` python
    .aigc_antispam_enrich(
        request_json_attr = "antispam_json",
        content_list_attr = "query_list",
        content_type = "answer",
        antispam_code_attr = "antispam_code",
        antispam_status_attr = "antispam_status",
        rpc_config = dict(
            antispam_rpc_name = "",
            sensitive_v2_rpc_name = "",
            sensitive_v3_rpc_name = "",
            sensitive_v2_timeout_ms = 50,
            sensitive_v3_timeout_ms = 50,
            antispam_timeout_ms = 100,
            kconf_name = ""
        ),
        async_config = dict(
            thread_num = 10,
            max_task_size = 100
        ),
        enable_sensitive_v2 = True,
        enable_sensitive_v3 = True,
        enable_antispam = True,
        enable_kconf = True
    )
    ```
    """
    self._add_processor(SeRecoAigcAntispamEnricher(kwargs))
    return self

  def retrieve_by_query_answer_proto(self, **kwargs):
    """
    SeRecoQueryAnswerProtoRetriever
    ------
    根据 QueryAnswer proto 数据召回item
    item 为 sequence 维度

    参数配置
    ------
    `from_query_answer_pb`: [string] [common attr] 必填，输入的 attr, 类型为 string 或者 stringlist

    `reason_id`: [int] 召回源 reason, 默认 0

    `recall_name`: [string] 召回源，默认空字符串

    `max_retrieve_count`: [string] [动态参数] 最大召回个数, answer 维度

    `item_source_name` : [string] [string item attr] 存放每个 item 的召回源的 attr

    `item_score_name` : [string] [double item attr] 存放每个 item 的分数的 attr

    `item_fused_name` : [string] [int item attr] 存放每个 item 的融合标志的 attr

    `item_seq_text_name` : [string] [string item attr] 存放每个 item 的句子文本的 attr

    `item_query_id_name` : [string] [int item attr] 存放每个 item 的 query ID 的 attr

    `item_answer_id_name` : [string] [int item attr] 存放每个 item 的 answer ID 的 attr

    `item_sequence_id_name` : [string] [int item attr] 存放每个 item 的 sequence ID 的 attr

    `item_refer_types_name` : [string] [int list item attr] 存放每个 item 的 refer 类型的 attr

    `extra_info` : [dict] 需要解析的 extra_info 字段

    `extra_info::query` : [list] 需要解析的 query extra_info 字段

    `extra_info::answer` : [list] 需要解析的 answer extra_info 字段

    `extra_info::sequence` : [list] 需要解析的 sequence extra_info 字段

    `extra_info::reference` : [list] 需要解析的 reference extra_info 字段

    `extra_info::xxx[i]::pb_name` : [string] 需要解析的 extra_info 字段的 name

    `extra_info::xxx[i]::attr_name` : [string] 需要解析的 extra_info 字段要写进哪个 attr

    `extra_info::xxx[i]::type` : [string] 需要解析的 extra_info 字段的类型，支持 int / double / string / intlist / doublelist / stringlist

    调用示例
    ------
    ``` python
    .retrieve_by_query_answer_proto(
        from_query_answer_pb = "value_list",
        reason_id = 100,
        recall_name = "sequence_recall",
        extra_info = {
          "reference": [
            {"pb_name": "title", "attr_name": "seq_title", "type": "string"},
            {"pb_name": "photo_id", "attr_name": "seq_photo_id", "type": "int"}
          ]
        }
    )
    ```
    """
    self._add_processor(SeRecoQueryAnswerProtoRetriever(kwargs))
    return self

  def goods_qr_udf(self, **kwargs):
    """
    SeRecoGoodsQrUDFEnricher
    ------
    商搜调用qr获取数据

    参数配置
    ------
    `is_common_attr`: [bool] 是否为common attr, 默认为False

    `common_attrs`: [list] 必填, table api 表中common特征

    `input_fields`: [list] 必填, 输入字段

    `output_fields` : [list]  必填, 输出字段

    `caller_name` : [string] 必填, 调用方名称

    `rpc_name` : [string] 必填, qr服务rpc名

    调用示例
    ------
    ``` python
    .goods_qr_udf(
        caller_name="grpc_xxx",
        rpc_name="grpc_mmu_queryRewriterNewSeg",
        input_fields=["query_bytes"],
        output_fields=["l1", "l2", "l3", "l4"]
    )
    ```
    """
    self._add_processor(SeRecoGoodsQrUDFEnricher(kwargs))
    return self

  def get_value_from_list_by_index(self, **kwargs):
    """
    SeRecoGetValueFromListByIndexEnricher
    ------
    根据index获取list中单个元素

    参数配置
    ------

    `input_common_attr`: [string] 选填, 输入的common attr

    `output_common_attr`: [string][string_list] 选填, 输出的common attr, 当index是多个时需要填对应的多个

    `input_item_attr`: [string] 选填, 输入的item attr

    `output_item_attr`: [string][string_list] 选填, 输出的item attr, 当index是多个时需要填对应的多个

    `index`: [int][int_list][动态参数] 必填, 获取元素的位置, 从0开始, 若list长度小于index, 则不设置数值

    `convert_type` : [string][string_list] 选填, 对取出的数据进行数据转换, 支持 string -> int/double 或 int/double -> string, 不支持的转换会忽略, 按index依次设置

    调用示例
    ------
    ``` python
    .get_value_from_list_by_index(
        input_common_attr="list_str",
        output_common_attr="str",
        index="{{index}}"
    )
    ```
    """
    self._add_processor(SeRecoGetValueFromListByIndexEnricher(kwargs))
    return self

  def retrieve_hot_person_list(self, **kwargs):
    """
    SeRecoHotPersonListRetriever
    ------
    召回热门人物榜

    参数配置
    ------

    `reason_id`: [int] 必填, 召回结果的reason id

    `item_source`: [string] 选填, 召回结果的item_source, 默认为hot_preson_list

    调用示例
    ------
    ``` python
    .retrieve_hot_person_list(
        reason_id=999,
    )
    ```
    """
    self._add_processor(SeRecoHotPersonListRetriever(kwargs))
    return self
  

  def get_value_from_model_serving(self, **kwargs):
    """
    SeRecoGetAttrByNameFromModelServingEnricher
    ------
    从mmu::serving::PredictResult的ex_feature中通过key召回value

    参数配置
    ------

    `from_extra_var`: [pb] 必填, 召回结果的输入的pb, 目前只支持mmu::serving::PredictResult

    `ex_attrs::name`: [string] 必填, 需要从ex_feature获取字段的key

    `ex_attrs::type`: [string] 必填, 需要从ex_feature获取字段的vale的数据类型, 目前支持int, float, string, int_list, float_list, string_list

    `ex_attrs::output_attr`: [string] 必填, 输出字段common_attr

    调用示例
    ------
    ``` python
    .get_value_from_model_serving(
        from_extra_var="grpc_external_response",
        ex_attrs=[
          {
            "name":"prob",
            "type":"float",
            "output_attr":"preb_val"
          }
        ], 
    )
    ```
    """
    self._add_processor(SeRecoGetAttrByNameFromModelServingEnricher(kwargs))
    return self

  def enrich_by_generic_redis(self, **kwargs):
    """
    SeRecoRedisToolEnricher
    ------
    通用redis工具, 目前支持 ZSize ZAdd ZRemRangeByIndex ZGetMembersRevByIndex ListPushRight ListLength ListRange ListPopRight

    参数配置
    ------

    `cluster_name`: [string] 必填, kcc/kiwi 集群名字

    `timeout_ms`: [string] 必填, 请求超时时间, 单位：毫秒

    `expire_s`: [string] 选填, 数据过期时间, 单位：秒

    `func_name`: [string] 必填, redis命令, 详见上面支持的操作方法

    `redis_key`: [string] 必填, 输入的common attr

    `redis_value`: [string] 选填, 输入的common attr

    `redis_score`: [string] 选填, 输入的common attr

    `start_idx`: [string] 选填, 输入的common attr

    `end_idx`: [string] 选填, 输入的common attr

    `output_attr_name`: [string] 选填, 输出的common attr

    `output_attr_name_1`: [string] 选填, 输入的item attr

    调用示例
    ------
    ``` python
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ZSize',
        redis_key="{{redis_key}}",
        output_attr_name="redis_value"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ZAdd',
        redis_key="{{redis_key}}",
        redis_value="{{redis_value}}"",
        redis_score="{{redis_score}}"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ZRemRangeByIndex',
        redis_key="{{redis_key}}",
        start_idx=0,
        end_idx="{{end_idx}}"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ZGetMembersRevByIndex',
        redis_key="{{redis_key}}",
        start_idx="{{start_idx}}",
        end_idx="{{end_idx}}",
        output_attr_name="redis_member",
        output_attr_name_1="redis_score"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        expire_s=self.ttl,
        func_name='ListPushRight',
        redis_key="{{redis_key}}",
        redis_value="{{redis_value}}""
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ListLength',
        redis_key="{{redis_key}}",
        output_attr_name="redis_value"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ListRange',
        redis_key="{{redis_key}}",
        output_attr_name="redis_value"
    )
    .enrich_by_generic_redis(
        cluster_name="xxxxxx",
        timeout_ms=20,
        func_name='ListPopRight',
        redis_key="{{redis_key}}"
    )
    ```
    """
    self._add_processor(SeRecoRedisToolEnricher(kwargs))
    return self
  
  def retrieve_by_generic_mysql(self, **kwargs):
    """
    SeRecoMysqlToolRetriever
    ------
    通用mysql工具, 目前支持 Write/Read, Read时数据写入item_table, 方便处理。

    参数配置
    ------

    `cluster_name`: [string] 必填, mysql 集群名字

    `shard_size`: [int] 必填, mysql 分片数

    `func_name`: [string] 必填, mysql命令, Write/Read

    `shard_id`: [string] 必填, 输入的common attr

    `sql_str`: [string] 必填, 输入的common attr

    `input_col_values`: [string] 选填, 输入的common attr, Write模式

    `output_affect_rows`: [string] 选填, 输出的common attr, Write模式

    调用示例
    ------
    ``` python
    .retrieve_by_generic_mysql(
        cluster_name="xxxxxx",
        shard_id="{{shard_id}}",
        func_name='Write',
        sql_str="{{sql_str}}",
        item_table="item_table",
        input_col_values="input_col_values",
        output_affect_rows="output_affect_rows"
    )
    .retrieve_by_generic_mysql(
        cluster_name="xxxxxx",
        shard_id="{{shard_id}}",
        func_name='Read',
        sql_str="{{sql_str}}",
        item_table="item_table"
    )
    ```
    """
    self._add_processor(SeRecoMysqlToolRetriever(kwargs))
    return self

  def url_encode(self, **kwargs):
    """
    SeRecoUrlEncodeEnricher
    ------
    url encode

    参数配置
    ------

    `input_common_attr`: [string] 选填, 输入的common attr

    `output_common_attr`: [string] 选填, 输出的common attr

    `input_item_attr`: [string] 选填, 输入的item attr

    `output_item_attr`: [string] 选填, 输出的item attr

    调用示例
    ------
    ``` python
    .url_encode(
        input_common_attr="signal_params_decoded",
        output_common_attr="signal_params_decompressed",
    )
    ```
    """
    self._add_processor(SeRecoUrlEncodeEnricher(kwargs))
    return self

  def get_hot_preson_list(self, **kwargs):
    """
    SeRecoHotPersonListEnricher
    ------
    获取热门人物榜单

    参数配置
    ------

    `dict_name`: [string] 选填, 使用的词典名, 默认为 general_hot_user

    `author_id_list_attr_name`: [string] 用户列表输出的attr名

    `view_cnt_list_attr_name`: [string] 选填, 用户列表对应浏览数的attr名

    `click_cnt_list_attr_name`: [string] 选填, 用户列表对应点击数的attr名

    调用示例
    ------
    ``` python
    .get_hot_preson_list(
        author_id_list_attr_name="hot_search_preson_id",
        view_cnt_list_attr_name="hot_search_preson_view_cnt",
    )
    ```
    """
    self._add_processor(SeRecoHotPersonListEnricher(kwargs))
    return self

  def enrich_by_old_profile(self, **kwargs):
    """
    SeRecoProfile2Enricher
    ------
    从profile2获取数据, 获取结果为序列化的string, 如果需要请自行转为对应的pb

    参数配置
    ------

    `cluster`: [string] profile集群

    `table`: [string] profile表名

    `table_type`: [string] profile表类型

    `is_common_attr`: [bool] 选填, 输入和输出是否是 common attr, 默认为False

    `fields`: [list] 必填  profile 召回对应的 field, 可以选择填写json list, 形如 {"feild_name" : "aaa", "export_name" : "bbb"} 来进行输出字段的重命名

    `timeout_ms`: [int] 选填 profile 召回的超时时间, 默认为 50 ms

    调用示例
    ------
    ``` python
    .enrich_by_old_profile(
        cluster="searchGoodsFeatureKiwi",
        table="merchantUserProfile",
        table_type="merchant.profile.UserProfile",
        input_attr_name="user_id",
        fields=["click_sequence", "merchant_buyer_info"]
    )
    ```
    """
    self._add_processor(SeRecoProfile2Enricher(kwargs))
    return self

  def bert_tokenizer_enricher(self, **kwargs):
    """
    SeRecoBertTokenizerEnricher
    ------
    bert tokenizer encode

    参数配置
    ------
    `input_segment_text_attr`: [string] 输入分词文本的attr

    `output_ids_attr`: [string] 输出token id编码的attr

    `output_pos_attr`: [string] 输出token 位置编码的attr

    `output_mask_attr`: [string] 输出token mask的attr

    `output_seg_attr`: [string] 输出token seg编码的attr

    `vocab_file`: [string] bert tokenizer参数，词表txt文件的位置，默认 空

    `tokenizer_type`: [int] bert tokenizer参数，0 为字级别的分词，1 为c++版本的分词逻辑，默认 0

    `enable_cls_token`: [bool] bert tokenizer参数，是否对输入的 string 添加 cls, 为 True 时会在输出结果头部加入 cls 的 id，默认 False

    `vocab_name`: [string] bert tokenizer参数，词典在词典平台的 name，默认 word_vocab_perks_lower

    `vocab_cat`: [string] bert tokenizer参数，词典在词典平台的目录，默认 video

    `disabled_chsubword`: [bool] bert tokenizer参数，oov 中文 token 处理开关，为 True 时，将不对中文进行 subword，默认 False

    `enable_unk_buckets`: [bool] bert tokenizer参数，unk 分桶开关，为 True 时进行分桶操作，默认 False

    `vocab_name_for_unk_buckets`: [string] bert tokenizer参数，unk_buckets词典在词典平台的name，默认 空

    `vocab_name_for_unk_buckets_abso_path`: [string] bert tokenizer参数，unk_buckets词典在词典平台的绝对路径，默认 空

    `is_bpe`: [bool] bert tokenizer参数，是否使用 bpe 词表，为True时中文subword时要先判断 ##token 是否在词表中，默认 False

    `enable_ngram`: [bool] bert tokenizer参数，是否使用ngram，默认 False
    
    `vocab_name_ngram`: [string] bert tokenizer参数，粒度词典在词典平台的name，默认 空

    `vocab_name_ngram_abso_path`: [string] bert tokenizer参数，粗粒度词典在词典平台的绝对路径，默认 空

    `max_len`: [int] bert tokenizer参数，最长编码长度，默认 24

    `is_fixed_ids`: [bool] 使用正确的ids，默认 False

    调用示例
    ------
    ``` python
    .bert_tokenizer_enricher(
        input_segment_text_attr = "segment_text",
        output_ids_attr = "tokenizer_output_ids",
        output_pos_attr = "tokenizer_output_pos",
        output_mask_attr = "tokenizer_output_mask",
        output_seg_attr = "tokenizer_output_seg",
        vocab_file = "",
        tokenizer_type = 0,
        enable_cls_token = True,
        vocab_name = "perks_vocab_word_c_version_v7",
        vocab_cat = "video",
        disabled_chsubword = False,
        enable_unk_buckets = True,
        vocab_name_for_unk_buckets = "perks_unkterm2unktoken_v7",
        vocab_name_for_unk_buckets_abso_path = "",
        is_bpe = True,
        enable_ngram = True,
        vocab_name_ngram = "perks_ngram_vocab_v9_v0",
        vocab_name_ngram_abso_path = "",
        is_fixed_ids = True,
    ) 
    ```
    """
    self._add_processor(SeRecoBertTokenizerEnricher(kwargs))
    return self

  def bert_emb_enricher(self, **kwargs):
    """
    SeRecoBertEmbEnricher
    ------
    通过推理服务获取bert emb

    参数配置
    ------
    `input_ids_attr`: [string] bert输入ids attr

    `input_pos_attr`: [string] bert输入pos attr

    `input_mask_attr`: [string] bert输入mask attr

    `input_seg_attr`: [string] bert输入seg attr

    `output_bert_emb_attr`: [string] 存储输出bert emb的common attr, 输出为double类型

    `infer_rpc_name`: [string] 推理服务的rpc name

    `infer_model_name`: [string] 推理服务的模型名称

    `infer_model_version`: [int] 推理服务的模型版本, 默认 -1

    `infer_rpc_timeout_ms`: [int] 推理服务超时设置, 默认 30

    调用示例
    ------
    ``` python
    .bert_emb_enricher(
        input_ids_attr = "input_ids",
        input_pos_attr = "input_pos",
        input_mask_attr = "input_mask",
        input_seg_attr = "input_seg",
        output_bert_emb_attr = "output_bert_emb",
        infer_rpc_name = "grpc_sug_ann_v1",
        infer_model_name = "inner-ann-model",
        infer_model_version = -1,
        infer_rpc_timeout_ms = 30,
    ) 
    ```
    """
    self._add_processor(SeRecoBertEmbEnricher(kwargs))
    return self

  def segment_enricher(self, **kwargs):
    """
    SeRecoSegmentEnricher
    ------
    segment 分词

    参数配置
    ------
    `release_ksdata_prefix`: 默认 "/data/web_server/index/KsdataList"

    `query_parser_segmenter_versions`: 默认 FLAGS_query_parser_segmenter_versions

    `enable_kseg_file_all_in_memory`: 默认 True

    `segmenter_pool_size`: [int] 默认 10

    `input_common_attr`: [string] 输入文本的 common attr name, string 类型

    `output_common_attr`: [string] 输出的 common attr name, 分词结果通过空格合并成字符串, string 类型

    调用示例
    ------
    ``` python
    .segment_enricher(
        release_ksdata_prefix = "/data/web_server/index/KsdataList",
        query_parser_segmenter_versions = "20210815",
        enable_kseg_file_all_in_memory = True,
        segmenter_pool_size = 200,
        input_common_attr = "query",
        output_common_attr = "query_segment_result",
    ) 
    ```
    """
    self._add_processor(SeRecoSegmentEnricher(kwargs))
    return self

  def segment_dedup_enricher(self, **kwargs):
    """
    SeRecoDedupSegmentEnricher
    ------
    segment 去重

    参数配置
    ------
    `release_ksdata_prefix`: 默认 "/data/web_server/index/KsdataList"

    `query_parser_segmenter_versions`: 默认 FLAGS_query_parser_segmenter_versions

    `enable_kseg_file_all_in_memory`: 默认 True

    `segmenter_pool_size`: [int] 默认 10

    `reloader_config`: [string] 初始化 reloader 的配置

    `reload_time`: [int] reloader 的 加载时间, 默认 3600(s)

    `use_process_v3`: [int] 动态参数, 是否用 v3 版本, 默认使用 v1 + v2, 值为 1 使用 v3

    `input_query_attr`: [string] 输入的 query attr name, string 类型

    `input_item_source_attr`: [string] 输入的 item source name, string 类型

    `input_item_id_attr`: [string] 输入的 item id name, string 类型

    `output_item_attr`: [string] 输出的 item attr name, int 类型

    调用示例
    ------
    ``` python
    .segment_dedup_enricher(
        release_ksdata_prefix="/data/web_server/index/KsdataList",
        query_parser_segmenter_versions="20210815",
        enable_kseg_file_all_in_memory=True,
        segmenter_pool_size=200,
        reloader_config = "",
        reload_time=3600,
        use_process_v3 = "{{sug_dedup_v3x}}",
        input_query_attr = "query",
        input_item_source_attr = "item_source",
        input_item_id_attr = "item_id",
        output_item_attr = "sugg_segment_dedup_is_filter",
    ) 
    ```
    """
    self._add_processor(SeRecoDedupSegmentEnricher(kwargs))
    return self

  def pb_visualize_enricher(self, **kwargs):
    """
    SeRecoProtobufVisializeEnricher
    ------
    protobuf 转成 人工可读 的字符串
    json / Utf8DebugString / ShortDebugString

    参数配置
    ------
    `op`: [string] 转换类型， 支持"json" / "Utf8DebugString" / "ShortDebugString", 默认 "json"

    `is_common`: [bool] 默认 True

    `pb_attr`: [string] 输入 Attr, Ptr 类型

    `output_attr`: [string] 输出 Attr, string 类型

    调用示例
    ------
    ``` python
    .pb_visualize_enricher(
        pb_attr = "",
        output_attr = "",
        op = "ShortDebugString"
    ) 
    ```
    """
    self._add_processor(SeRecoProtobufVisializeEnricher(kwargs))
    return self

  def get_value_from_dragon_response(self, **kwargs):
    """
    SeRecoGetAttrByNameFromDragonResponseEnricher
    ------
    从dragon服务的应答(ks::platform::CommonRecoResponse)中根据attr_name获取common_attr_val

    参数配置
    ------

    `from_extra_var`: [pb] 必填, 召回结果的输入的pb, 目前只支持ks::platform::CommonRecoResponse

    `response_attr::name`: [string] 必填, 需要从common_attr获取字段的key

    `response_attr::type`: [string] 必填, 需要从common_attr获取字段的vale的数据类型, 目前支持int, float, string, int_list, float_list, string_list

    `response_attr::output_attr`: [string] 必填, 输出字段common_attr

    调用示例
    ------
    ``` python
    .get_value_from_dragon_response(
        from_extra_var="grpc_external_response",
        response_attr=[
          {
            "name":"llm_answer_list_attr_postprocess_after_backup",
            "type":"string_list",
            "output_attr":"test_list"
          }
        ], 
    )
    ```
    """
    self._add_processor(SeRecoGetAttrByNameFromDragonResponseEnricher(kwargs))
    return self

  def se_reco_ufs(self, **kwargs):
    """
    SeRecoUnifiedFeatureEnricher
    ------
    请求ufs

    参数配置
    ------

    `request_attr`: [string] 必填, attr 是 ks::platform::CommonRecoResponse 类型

    `timeout_ms`: [int] 必填, 超时时间

    `output_attrs`: [string list] 必填, 输出的特征

    调用示例
    ------
    ``` python
    .se_reco_ufs(
        request_attr='ufs_request',
        timeout_ms=50,
        output_attrs=['a', 'b', 'c'],
    )
    ```
    """
    self._add_processor(SeRecoUnifiedFeatureEnricher(kwargs))
    return self

  def upload_image_to_blob(self, **kwargs):
    self._add_processor(SeRecoUploadImageToBlobEnricher(kwargs))
    return self

  def dragon_attrs_to_ziya(self, **kwargs):
    """
    SeRecoDragonAttrsToZiyaEnricher
    ------
    生成 ziya context attr，将 dragon attrs 写入 ziya context

    参数配置
    ------

    调用示例
    ------
    ``` python
    .dragon_attrs_to_ziya()
    ```
    """
    self._add_processor(SeRecoDragonAttrsToZiyaEnricher(kwargs))
    return self

  def dragon_attrs_to_ziya_v2(self, **kwargs):
    """
    SeRecoDragonAttrsToZiyaV2Enricher
    ------
    不生成 ziya context attr，仅将dragon attrs 写入 ziya context

    参数配置
    ------

    调用示例
    ------
    ``` python
    .dragon_attrs_to_ziya()
    ```
    """
    self._add_processor(SeRecoDragonAttrsToZiyaV2Enricher(kwargs))
    return self

  def ziya_attrs_to_dragon(self, **kwargs):
    """
    SeRecoZiyaAttrsToDragonRetriever
    ------
    reset 外增 context

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ziya_attrs_to_dragon()
    ```
    """
    self._add_processor(SeRecoZiyaAttrsToDragonRetriever(kwargs))
    return self

  def ziya_base_executor(self, **kwargs):
    """
    ziya 通用算子
    ------
    SeRecoBaseExecutorEnricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ziya_base_executor()
    ```
    """
    self._add_processor(SeRecoBaseExecutorEnricher(kwargs))
    return self

  def enrich_basic_common_attr(self, **kwargs):
    """
    SeRecoCommonInfoEnricher
    ------
    从 context 中获取一些基础数据

    参数配置
    ------

    `save_is_debug_to_attr`: [string] 将是否是 debug 请求保存在 attr 中

    `ori_request_id_attr`: [string] 输入的 request_id attr name

    `adjusted_request_id_to_attr`: [string] 输出的 request_id attr name. 如果输入的 request_id value 是空时, 重新生成一个 request_id

    `query_attr`: [string] 用于生成 request_id 的 query attr name

    调用示例
    ------
    ``` python
    .enrich_basic_common_attr(
        save_is_debug_to_attr='is_debug',
        ori_request_id_attr='request_id',
        query_attr='query',
        adjusted_request_id_to_attr='request_id',
    )
    ```
    """
    self._add_processor(SeRecoCommonInfoEnricher(kwargs))
    return self

  def dedup_by_source_priority(self, **kwargs):
    """
    SeRecoPriorityDedupArranger
    ------
    根据 source 的优先级去重

    参数配置
    ------

    `item_id_str_attr_name`: [string] item id attr name

    `item_source_attr_name`: [string] source attr name

    `item_author_id_attr_name`: [string] item author id attr name

    `item_multi_sources_attr_name`: [string] 多 source 写入 attr name

    `priority_source_list`: [list] 优先级列表, 第一个优先级最高, 第二个次之... 不在列表里的 source 优先级最低

    调用示例
    ------
    ``` python
    .dedup_by_source_priority(
        item_id_str_attr_name='item_id',
        item_source_attr_name='item_source',
        item_author_id_attr_name='item_author_id',
        item_multi_sources_attr_name='multi_recall_reasons',
        priority_source_list=['a','b','c']
    )
    ```
    """
    self._add_processor(SeRecoPriorityDedupArranger(kwargs))
    return self

  def enrich_ziya_context(self, **kwargs):
    """
    SeRecoZiyaContextEnricher
    ------
    生成 ziya context attr

    参数配置
    ------

    调用示例
    ------
    ``` python
    .enrich_ziya_context()
    ```
    """
    self._add_processor(SeRecoZiyaContextEnricher(kwargs))
    return self

  def unified_retriever(self, **kwargs):
    """
    unified_retriever
    ------
    本地索引召回

    参数配置
    ------

    调用示例
    ------
    ``` python
    .unified_retriever()
    ```
    """
    self._add_processor(UnifiedLocalIndexRetriever(kwargs))
    return self
  
  def se_platform_debug_log_enricher(self, **kwargs):
    """
    se_platform_debug_log_enricher
    ------
    debug平台诶之输出

    参数配置
    ------

    `tag`: [string] 标识

    `title`: [string] 日志title

    `level`: [int] 显示日志等级

    `stage`: [string] 日志阶段目前支持 "UNKNOWN","BEFORE_RECALL","RECALL","AFTER_RECALL","FILTER","PRE_RANK",
              "RE_RANK","RE_RANK","MULTI_QUEUE_ADJUST","GROUP_PK","GROUP_PK","FINAL_ADJUST","DECORATE",
              "BIZ_PROCESS","API_PHOTO_VIEW""API_PHOTO_VIEW","API_LIVE_VIEW","API_USER_VIEW","API_GOODS_VIEW",
              "LEAF_PRE_RANK","REQUEST_INFO","RESPONSE_INFO","CACLQR","ABTEST_INFO","SMART_TITLE"

    `common_attrs`: [list] 输出common attr

    `item_attrs`: [list] 输出item attr

    调用示例
    ------
    ``` python
    .se_platform_debug_log_enricher(
      tag = 'default',
      title = 'test',
      level = 1,
      stage = 'BEFORE_RECALL',
      common_attrs = ['uid'],
      item_attrs = ['item_id']
    )
    ```
    """
    self._add_processor(SeRecoPlatformDebugLogEnricher(kwargs))
    return self