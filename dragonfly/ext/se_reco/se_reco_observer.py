#!/usr/bin/env python3
"""
filename: se_reco_common_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for se reco
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-05-19
"""
from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver


class SeRecoUnfieldProfileUpdateObserver(LeafObserver):
  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ups_update"

  @strict_types
  def depend_on_items(self) -> bool:
    return any(self.input_item_attrs)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["key", "value"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["key_from_item_attr", "value_from_item_attr"]:
      ret.add(self._config.get(key))
    return ret


class SeRecoAckFeatureDumpObserver(LeafObserver):
  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ack_feature_dump"

  @strict_types
  def depend_on_items(self) -> bool:
    return any(self.input_item_attrs)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("cover_item_id_by_author_name", False):
      ret.add(self._config.get("item_author_id_attr_name", "item_author_id"))
      ret.add(self._config.get("item_author_name_attr_name", "item_author_name"))
    for key in ["item_recall_reason_attr_name", "item_id_attr_name"]:
      ret.add(self._config.get(key))
    return ret
