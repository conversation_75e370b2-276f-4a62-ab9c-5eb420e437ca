#!/usr/bin/env python3
"""
filename: se_reco_common_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arrange module for se reco
author: <EMAIL>
date: 2022-09-05
"""
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafArranger

class SeRecoGeneralMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_by_quotas"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("count")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    item_author_id_attr_name = self._config.get("item_author_id_attr_name", "")
    if item_author_id_attr_name:
      attrs.add(item_author_id_attr_name)
    multi_sources = self._config.get("item_multi_sources_attr_name", "")
    if multi_sources:
      attrs.add(self._config.get("item_source_attr_name", "item_source"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("item_multi_sources_attr_name", "")
    if attr:
      attrs.add(attr)
    return attrs

class KconfBlackListACMatchFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kconf_black_list_ac_match_filter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    return attrs

class KconfBlackListACMatchFilterV2Arranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kconf_black_list_ac_match_filter_v2"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    return attrs

class SeRecoIllegalQueryFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "illegal_query_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("review_filter_kconf")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    return attrs


class SeRecoDiversityDedupArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "diversity_dedup"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_score_attr_name", "item_score"))
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    return attrs

class SeRecoKwaiDedupArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kwai_dedup"

  @strict_types
  def _check_config(self) -> None:
    check_arg("bucket_attr_name" in self._config,"bucket_attr_name 必须配置")
    check_arg("item_id_attr_name" in self._config,"item_id_attr_name 必须配置")
    check_arg("item_score_attr_name" in self._config,"item_score_attr_name 必须配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("bucket_attr_name"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_score_attr_name", ""))
    attrs.add(self._config.get("item_id_attr_name", ""))
    return attrs

class SeRecoHotReviewFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_review_filter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    return attrs

class SeRecoNgramDedupArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ngram_dedup_filter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    return attrs

class SeRecoItemResultsFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_item_results_not_in_any_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("remove_if_not_all_in", []))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    item_id_str_attr_name = self._config.get("item_id_str_attr_name", "")
    if item_id_str_attr_name:
      attrs.add(item_id_str_attr_name)
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    filter_reason_attr_name = self._config.get("filter_reason_attr_name", "")
    if filter_reason_attr_name:
      attrs.add(filter_reason_attr_name)
    return attrs


class SeRecoAuthorNotLiveFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_author_not_live_filter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("filter_by_attr_name", "item_id"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    return attrs

class SeRecoRecallFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_sug_recall_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("request_query_attr_name", "query"))
    attrs.update(self.extract_dynamic_params(self._config.get("review_filter_kconf")))
    attrs.update(self.extract_dynamic_params(self._config.get("skip_review_cfg")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    attrs.add(self._config.get("hotword_rank_type", "hotword_rank_type"))
    return attrs

class SeRecoPriorityDedupArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dedup_by_source_priority"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name"))
    attrs.add(self._config.get("item_source_attr_name"))
    attrs.add(self._config.get("item_author_id_attr_name"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_multi_sources_attr_name"))
    return attrs