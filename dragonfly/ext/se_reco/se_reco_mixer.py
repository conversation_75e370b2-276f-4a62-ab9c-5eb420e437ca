#!/usr/bin/env python3
# coding=utf-8
"""
filename: se_reco_mixer.py
description: mixer module for se reco
author: ca<PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-02-07
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafMixer, try_add_table_name

class SeRecoProfileRecallMixer(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "profile_recall"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["confs"]:
      attrs.update(self.extract_dynamic_params(v["keys"]))
      if "condition" in v:
        attrs.update(self.extract_dynamic_params(v["condition"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    public_item_attrs = set()
    public_item_attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    public_item_attrs.add(self._config.get("source_str_attr_name", "item_source"))
    public_item_attrs.add(self._config.get("item_score_attr_name", "item_score"))
    public_item_attrs.add(self._config.get("reason_str_attr_name", "item_recall_reason"))
    public_item_attrs.add(self._config.get("item_inner_index_attr_name", "item_inner_index"))
    public_item_attrs.add(self._config.get("item_author_id_attr_name", "item_author_id"))

    if self._config.get("to_main_table", False):
      return public_item_attrs
    tables = set()
    for v in self._config["confs"]:
      tables.add(v["name"])

    attrs = set()
    for table in tables:
      attrs.update(try_add_table_name(table, public_item_attrs))
    return attrs

  @property
  @strict_types
  def output_item_tables(self) -> set:
    if self._config.get("to_main_table", False):
      return set([""])
    attrs = set()
    for v in self._config["confs"]:
      attrs.add(v["name"])
    return attrs

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class SeRecoMergeTablesMixer(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_tables"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      table_name = table.get("table_name")
      table_attrs = table.get("attrs", [])
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def input_item_tables(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      attrs.add(table.get("table_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    table_name = self._config.get("output_table", "") 
    all_table_attrs = set()
    for table in self._config.get("input_tables", []):
      table_attrs = table.get("attrs", [])
      all_table_attrs.update(table_attrs)
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def output_item_tables(self) -> set:
    attrs = set()
    table_name = self._config.get("output_table", "") 
    attrs.add(table_name)
    return attrs

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class OverseaRecoMergeTablesMixer(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_tables"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      table_name = table.get("table_name")
      table_attrs = table.get("attrs", [])
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def input_item_tables(self) -> set:
    attrs = set()
    for table in self._config.get("input_tables", []):
      attrs.add(table.get("table_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    table_name = self._config.get("output_table", "") 
    all_table_attrs = set()
    for table in self._config.get("input_tables", []):
      table_attrs = table.get("attrs", [])
      all_table_attrs.update(table_attrs)
      attrs.update(try_add_table_name(table_name, table_attrs))
    return attrs

  @property
  @strict_types
  def output_item_tables(self) -> set:
    attrs = set()
    table_name = self._config.get("output_table", "") 
    attrs.add(table_name)
    return attrs

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables
