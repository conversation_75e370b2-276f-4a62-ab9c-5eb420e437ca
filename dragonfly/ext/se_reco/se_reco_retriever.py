#!/usr/bin/env python3
# coding=utf-8
"""
filename: subtag_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for subtag
author: <EMAIL>
date: 2022-09-01
""" 
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class SeRecoBaseKVRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_base_retrieve_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    key_source = self._config.get("key_source", "user_id")
    if key_source == "query":
        attrs.add(self._config.get("query_attr_name", "ctx_query"))
    elif key_source == "photo_ids":
        attrs.add(self._config.get("photo_id_list_attr_name", "ctx_photo_ids"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    attrs.add(self._config.get("author_id_attr_name", "author_id"))
    attrs.add(self._config.get("reason_str_attr_name", "reason_str"))
    attrs.add(self._config.get("recall_reason_attr_name", "recall_reason"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SubtagKVRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_subtag_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("query_attr_name", "ctx_query"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    attrs.add(self._config.get("reason_str_attr_name", "reason_str"))
    attrs.add(self._config.get("source_attr_name", "source"))
    attrs.add(self._config.get("map_words_attr_name", "map_words"))
    attrs.add(self._config.get("source_list_attr_name", "source_list"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SubtagKVDataClientRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_subtag_by_dataclient"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    key_source = self._config.get("key_source", "query")
    if key_source == "query":
        attrs.add(self._config.get("query_attr_name", "ctx_query"))
    elif key_source == "photo_ids":
        attrs.add(self._config.get("photo_id_list_attr_name", "ctx_photo_ids"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    attrs.add(self._config.get("reason_str_attr_name", "reason_str"))
    attrs.add(self._config.get("source_attr_name", "source"))
    attrs.add(self._config.get("map_words_attr_name", "map_words"))
    attrs.add(self._config.get("source_list_attr_name", "source_list"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SubtagMultiIntentRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_subtag_by_multi_intent"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    key_source = self._config.get("key_source", "query")
    if key_source == "query":
        attrs.add(self._config.get("query_attr_name", "ctx_query"))
    elif key_source == "photo_ids":
        attrs.add(self._config.get("photo_id_list_attr_name", "ctx_photo_ids"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    attrs.add(self._config.get("reason_str_attr_name", "reason_str"))
    attrs.add(self._config.get("source_attr_name", "source"))
    attrs.add(self._config.get("map_words_attr_name", "map_words"))
    attrs.add(self._config.get("source_list_attr_name", "source_list"))
    attrs.add(self._config.get("link_attr_name", "link"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoUserToQueryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_to_query"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for v in self._config["confs"]:
      attrs.update(self.extract_dynamic_params(v["keys"]))
      if "condition" in v:
        attrs.update(self.extract_dynamic_params(v["condition"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id_str"))
    attrs.add(self._config.get("reason_str_attr_name", "reason_str"))
    return attrs

class SeRecoStoreValueRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_store_value_string_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr_list"))
    for attr in ["result_num", "single_recall_num"]:
        attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("recall_reason_attr_name", "item_recall_reason"))
    attrs.add(self._config.get("item_source_name", "item_source"))
    attrs.add(self._config.get("item_score_name", "item_score"))
    attrs.add(self._config.get("author_id_name", "item_author_id"))
    parse_pb_type = self._config.get("parse_pb_type", "kv_recall_info")
    if parse_pb_type == "kv_recall_info":
      attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    elif parse_pb_type == "user_info":
      attrs.add(self._config.get("user_info_item_keyword", "user_info_item_keyword"))
    return attrs

class SeRecoProtobufListRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_protobuf_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr_list"))
    attrs.add(self._config.get("input_key_attr_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update({"recall_key", "query", "rough_rank_score", "relevance_score", "recall_source_num", "photo_id", "is_query_item"})
    return attrs


class SeRecoZiyaExecutorRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_ziya_executor"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    user_follow_uid_list = self._config.get("user_follow_uid_list", "")
    if user_follow_uid_list:
        attrs.add(user_follow_uid_list)
    user_interest_se_author_first = self._config.get("user_interest_se_author_first", "")
    if user_interest_se_author_first:
        attrs.add(user_interest_se_author_first)
    user_interest_se_author_second = self._config.get("user_interest_se_author_second", "")
    if user_interest_se_author_second:
        attrs.add(user_interest_se_author_second)
    user_interest_author_first = self._config.get("user_interest_author_first", "")
    if user_interest_author_first:
        attrs.add(user_interest_author_first)
    user_interest_author_second = self._config.get("user_interest_author_second", "")
    if user_interest_author_second:
        attrs.add(user_interest_author_second)
    user_interest_live_author_first = self._config.get("user_interest_live_author_first", "")
    if user_interest_live_author_first:
        attrs.add(user_interest_live_author_first)
    user_interest_live_author_second = self._config.get("user_interest_live_author_second", "")
    if user_interest_live_author_second:
        attrs.add(user_interest_live_author_second)
    searched_queries_query = self._config.get("searched_queries_query", "")
    if searched_queries_query:
        attrs.add(searched_queries_query)
    searched_queries_his_score = self._config.get("searched_queries_his_score", "")
    if searched_queries_his_score:
        attrs.add(searched_queries_his_score)
    searched_queries_set = self._config.get("searched_queries_set", "")
    if searched_queries_set:
        attrs.add(searched_queries_set)
    browse_queries = self._config.get("browse_queries", "")
    if browse_queries:
        attrs.add(browse_queries)
    refer_video_ids = self._config.get("refer_video_ids", "")
    if refer_video_ids:
        attrs.add(refer_video_ids)
    query = self._config.get("query", "")
    if query:
       attrs.add(query)
    client_info = self._config.get("client_info", "")
    if client_info:
        attrs.add(client_info)
    kwai_source = self._config.get("kwai_source", "")
    if kwai_source:
        attrs.add(kwai_source)
    trigger_queries = self._config.get("trigger_queries", "")
    if trigger_queries:
        attrs.add(trigger_queries)
    trigger_query_score = self._config.get("trigger_query_score", "")
    if trigger_query_score:
        attrs.add(trigger_query_score)
    reco_user_info = self._config.get("reco_user_info", "")
    if reco_user_info:
        attrs.add(reco_user_info)
    user_active = self._config.get("user_active", "")
    if user_active:
        attrs.add(user_active)
    city = self._config.get("city", "")
    if city:
        attrs.add(city)
    browse_newhot_queries = self._config.get("browse_newhot_queries", "")
    if browse_newhot_queries:
        attrs.add(browse_newhot_queries)
    growth_user_feature = self._config.get("growth_user_feature", "")
    if growth_user_feature:
        attrs.add(growth_user_feature)
    if "trigger_sources" in self._config:
        for cfg in self._config["trigger_sources"]:
            if "queries" in cfg:
                attrs.add(cfg["queries"])
    for attr in ["result_num"]:
        attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("recall_reason_attr_name", "item_recall_reason"))
    attrs.add(self._config.get("item_source_name", "item_source"))
    attrs.add(self._config.get("item_score_name", "item_score"))
    attrs.add(self._config.get("author_id_name", "item_author_id"))
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    url = self._config.get("item_url", "")
    if url:
        attrs.add(url)
    item_upload_timestamp = self._config.get("item_upload_timestamp", "")
    if item_upload_timestamp:
        attrs.add(item_upload_timestamp)
    kuaishou_trace_id = self._config.get("item_kuaishou_trace_id", "")
    if kuaishou_trace_id:
        attrs.add(kuaishou_trace_id)
    kuaishou_nebula_trace_id = self._config.get("item_kuaishou_nebula_trace_id", "")
    if kuaishou_nebula_trace_id:
        attrs.add(kuaishou_nebula_trace_id)
    item_flow_order_trace_pack = self._config.get("item_flow_order_trace_pack", "")
    if item_flow_order_trace_pack:
        attrs.add(item_flow_order_trace_pack)
    item_author_name = self._config.get("item_author_name", "")
    if item_author_name:
        attrs.add(item_author_name)
    item_icon_type = self._config.get("item_icon_type", "")
    if item_icon_type:
       attrs.add(item_icon_type)
    item_text = self._config.get("item_text", "")
    if item_text:
       attrs.add(item_text)
    item_color = self._config.get("item_color", "")
    if item_color:
       attrs.add(item_color)
    item_ori_query = self._config.get("item_ori_query", "")
    if item_ori_query:
       attrs.add(item_ori_query)
    item_hotword_rank_type = self._config.get("item_hotword_rank_type", "")
    if item_hotword_rank_type:
       attrs.add(item_hotword_rank_type)
    item_qp_score = self._config.get("item_qp_score", "")
    if item_qp_score:
       attrs.add(item_qp_score)
    item_hot_lv = self._config.get("item_hot_lv", "")
    if item_hot_lv:
       attrs.add(item_hot_lv)
    return attrs

class SeRecoStarryEmbeddingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_retrieve_by_starry_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("source_photo_attr"))
      attrs.add(self._config.get("source_score_common_attr"))
      for name in ["top_k"]:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoStarryQueryRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_query_retrieve_by_starry_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("input_common_attr"))
      for name in ["top_k"]:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoStarryQueryV2Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_query_retrieve_by_starry_embedding_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("input_common_attr"))
      for name in ["top_k"]:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoStarryQueryV3Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_query_retrieve_by_starry_embedding_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("input_common_attr"))
      attrs.add(self._config.get("input_common_attr2"))
      for name in ["top_k"]:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    attrs.add(self._config.get("item_trigger_str_attr_name", "item_recall_reason"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoBartGenRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "se_reco_query_retrieve_by_bart_gen"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("input_segment_text_attr"))
      attrs.add(self._config.get("input_searched_query_attr"))
      return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True
      
class SeRecoSearchSortBatchPBRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_search_sort_batch_pb"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_search_sort_pb"))
    attrs.update(self.extract_dynamic_params(self._config.get("only_use_topn")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_str_attr_name", "item_id"))
    attrs.add(self._config.get("item_recall_reason_name", "item_recall_reason"))
    attrs.add(self._config.get("item_source_name", "item_source"))
    attrs.add(self._config.get("item_score_name", "item_score"))
    item_p2q_source_attr_name = self._config.get("item_p2q_source_attr_name", "")
    if item_p2q_source_attr_name:
      attrs.add(item_p2q_source_attr_name)
    item_p2q_score_attr_name = self._config.get("item_p2q_score_attr_name", "")
    if item_p2q_score_attr_name:
      attrs.add(item_p2q_score_attr_name)
    is_refer = self._config.get("is_refer_source", False)
    if is_refer:
      attrs.add(self._config.get("item_is_refer_source", "item_is_refer_source"))
    if "item_extra_attr_types" in self._config:
       for extra_info in self._config["item_extra_attr_types"]:
          if "item_attr_name" in extra_info:
            attrs.add(extra_info["item_attr_name"])
    return attrs

class CommonRecallEsRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_es"

  @strict_types
  def _check_config(self) -> None:
    check_arg("app_id" in self._config,"app_id 必须配置")
    for sc in self._config.get("sug_fuzziness", []):
      check_arg(isinstance(sc.get("fuzziness"), list) or isinstance(sc.get("fuzziness"), str), \
                "fuzziness 需要为 int list or 动态参数")
      check_arg(isinstance(sc.get("recall_cnt"), list) or isinstance(sc.get("recall_cnt"), str), \
                "recall_cnt 需要为 int list or 动态参数")
    for sc in self._config.get("section", []):
      if sc.get("list_for_literal", False):
        check_arg(isinstance(sc.get("field_name"), list) or isinstance(sc.get("field_name"), str), \
                "field_name 需要为 str list or 动态参数")
        check_arg(isinstance(sc.get("literal"), list) or isinstance(sc.get("literal"), str), \
                "literal 需要为 str list or 动态参数")
      else:
        check_arg(isinstance(sc.get("field_name"), str), "field_name 需要为 str")
        check_arg(isinstance(sc.get("literal"), str), "literal 需要为 str")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["app_id", "keyword", "explore"]:
      if attr in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    for fc in self._config.get("filter", []):
      attrs.update(self.extract_dynamic_params(fc.get("field_value")))
    for sc in self._config.get("sug_fuzziness", []):
      attrs.update(self.extract_dynamic_params(sc.get("fuzziness")))
      attrs.update(self.extract_dynamic_params(sc.get("recall_cnt")))
    for sc in self._config.get("section", []):
      attrs.update(self.extract_dynamic_params(sc.get("field_name")))
      attrs.update(self.extract_dynamic_params(sc.get("literal")))
      attrs.update(self.extract_dynamic_params(sc.get("fuzzy_size")))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class SeRecoCollectionInfoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_collection_info"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("response_attr_name"))
    attrs.add(self._config.get("collection_id_list_attr_name"))
    attrs.add(self._config.get("photo_id_list_attr_name"))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("item_id")
    attrs.add("collection_last_index")
    attrs.add("collection_last_photo_id")
    attrs.add("item_source")
    attrs.add("item_recall_reason")
    attrs.add("item_collection_info")
    attrs.add("item_collection_total")
    return attrs
  
class SeRecoQueryAnswerProtoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_query_answer_proto"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_query_answer_pb"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_retrieve_count")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    params = [
      ("item_source_name", "item_source"),
      ("item_score_name", "item_score"),
      ("item_fused_name", "item_fused"),
      ("item_seq_text_name", "item_seq_text"),
      ("item_query_id_name", "query_id"),
      ("item_answer_id_name", "answer_id"),
      ("item_sequence_id_name", "sequence_id"),
      ("item_refer_types_name", "refer_types")
    ]
    for param, default_param in params:
      attrs.add(self._config.get(param, default_param))
    for info_level in self._config.get("extra_info"):
      for info in self._config["extra_info"][info_level]:
        attr_name = info.get("attr_name", info["pb_name"])
        attrs.add(attr_name)
        if "list" in info["type"]:
          attrs.add(attr_name + "_list_size")
    return attrs

class SeRecoRecallLibRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_recall_lib"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("query_attr_name", "query"))
    raw_query_attr_name = self._config.get("raw_query_attr_name", "")
    if raw_query_attr_name:
      attrs.add(raw_query_attr_name)
    city_attr_name = self._config.get("city_attr_name", "")
    if city_attr_name:
      attrs.add(city_attr_name)
    refer_photo_id_name = self._config.get("refer_photo_id_name", "")
    if refer_photo_id_name:
      attrs.add(refer_photo_id_name)
    reco_click_list_photo_id = self._config.get("reco_click_list_photo_id", "")
    if reco_click_list_photo_id:
       attrs.add(reco_click_list_photo_id)
    client_info_attr_name = self._config.get("client_info_attr_name", "")
    if client_info_attr_name:
       attrs.add(client_info_attr_name)
    long_searched_history_attr_name = self._config.get("long_searched_history_attr_name", "")
    if long_searched_history_attr_name:
       attrs.add(long_searched_history_attr_name)
    searched_queries_attr_name = self._config.get("searched_queries_attr_name", "")
    if searched_queries_attr_name:
       attrs.add(searched_queries_attr_name)
    attrs.update(self.extract_dynamic_params(self._config.get("only_use_topn")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if "common_extra_attr_conf" in self._config:
      for extra_info in self._config["common_extra_attr_conf"]:
        if "name" in extra_info:
          attrs.add(extra_info["name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    attrs.add(self._config.get("item_author_id_attr_name", "item_author_id"))
    attrs.add(self._config.get("item_recall_reason_attr_name", "item_recall_reason"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    attrs.add(self._config.get("item_score_attr_name", "item_score"))
    embedding_attr_name = self._config.get("embedding_attr_name", "")
    if embedding_attr_name:
       attrs.add(embedding_attr_name)
    if "item_extra_attr_conf" in self._config:
       for extra_info in self._config["item_extra_attr_conf"]:
          if "name" in extra_info:
            attrs.add(extra_info["name"])
    return attrs

class SeRecoPrefixRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_prefix"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("query_attr_name", "query"))
    raw_query_attr_name = self._config.get("raw_query_attr_name", "")
    if "prefix_score_name" in self._config:
       attrs.update(self.extract_dynamic_params(self._config.get("prefix_score_name")))
    if raw_query_attr_name:
      attrs.add(raw_query_attr_name)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if "common_extra_attr_conf" in self._config:
      for extra_info in self._config["common_extra_attr_conf"]:
        if "name" in extra_info:
          attrs.add(extra_info["name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr_name", "item_id"))
    attrs.add(self._config.get("item_author_id_attr_name", "item_author_id"))
    attrs.add(self._config.get("item_recall_reason_attr_name", "item_recall_reason"))
    attrs.add(self._config.get("item_source_attr_name", "item_source"))
    attrs.add(self._config.get("item_score_attr_name", "item_score"))
    if "item_extra_attr_conf" in self._config:
       for extra_info in self._config["item_extra_attr_conf"]:
          if "name" in extra_info:
            attrs.add(extra_info["name"])
    return attrs

class SeRecoMysqlToolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_generic_mysql"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("shard_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("sql_str")))
    func_name = self._config.get("func_name", "default")
    if (func_name == "Write"):
      attrs.add(self._config.get("input_col_values", ""))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    func_name = self._config.get("func_name", "default")
    if (func_name == "Write"):
      attrs.add(self._config.get("output_affect_rows", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    func_name = self._config.get("func_name", "default")
    if (func_name == "Read"):
      output_item_attrs = self._config.get("output_item_attrs", [])
      attrs.update(output_item_attrs)
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False

class SeRecoHotPersonListRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_hot_person_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("item_id")
    attrs.add("item_source")
    attrs.add("item_recall_reason")
    attrs.add("item_author_id")
    attrs.add("raw_hot_value")
    attrs.add("item_icon_json")
    return attrs


class SeRecoZiyaAttrsToDragonRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ziya_attrs_to_dragon"

    @strict_types
    def is_async(self) -> bool:
        return False
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ZiyaContextAttrKey")
        return attrs
  
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for output_attr in self._config.get("output_common_attrs"):
          attrs.add(output_attr)
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for output_attr in self._config.get("output_item_attrs"):
          attrs.add(output_attr)
        return attrs

class UnifiedLocalIndexRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "unified_retriever"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      attrs.update(self.extract_dynamic_params(self._config.get("single_num")))
      attrs.update(self.extract_dynamic_params(self._config.get("total_num")))
      attrs.update(self.extract_dynamic_params(self._config.get("recall_name", "")))
      attrs.update(self.extract_dynamic_params(self._config.get("recall_score")))
      attrs.add(self._config.get("primary_key"))
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      if "output_attr_names" in self._config:
        for extra_info in self._config["output_attr_names"]:
          if "export_name" in extra_info:
            attrs.add(extra_info["export_name"])

      attrs.add(self._config.get("item_recall_reason_name", "item_recall_reason"))
      attrs.add(self._config.get("item_score_name", "item_score"))
      return attrs
