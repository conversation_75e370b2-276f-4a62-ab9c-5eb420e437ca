import operator
import itertools
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class KsibPersonalizedClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "oversea_personalized_cluster"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_arg(isinstance(self._config.get("cluster_center_num_attr"), int), "cluster_center_num_attr 不为 int")
    ret.add(self._config["cluster_center_num_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_cluster_attr"] }