import operator
import itertools

from ...common_leaf_util import strict_types, check_arg, extract_common_attrs
from ...common_leaf_processor import LeafRetriever

class OverseaLrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_oversea_lr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kess_service", "timeout_ms", "total_limit", "reason"]:
      attrs.update(extract_common_attrs(self._config.get(key)))
    for key in ["oversea_user_info_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), (int, str)), "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
