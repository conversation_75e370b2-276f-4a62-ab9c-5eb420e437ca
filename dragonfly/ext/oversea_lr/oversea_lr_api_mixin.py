#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .oversea_lr_retriever import *
from .oversea_lr_enricher import *

class OverseaLrApiMixin(CommonLeafBaseMixin):
  """
  Oversea Lr Processor API 接口的 Mixin 实现
  """

  def retrieve_oversea_lr(self, **kwargs):
    """
    OverseaLrRetriever
    ------
    Oversea Lr 召回
    
    参数
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string][动态参数] oversea lr 召回服务 kess name

    `timeout_ms`: [int] [动态参数] 请求 oversea lr 服务的超时时间，默认值为 300
    
    `total_limit`: [int] [动态参数], 不可缺省

    `service_group`: [string] oversea lr 服务的 kess 服务组，默认值为 "PRODUCTION"

    `oversea_user_info_attr`: [string] 从哪个 common_attr 获取 user_info，海外业务需要设置
    
    调用示例
    ------
    ``` python
    .retrieve_oversea_lr(
      reason=666,
      kess_service="grpc_OverseaSimLRIcfRetrievalExpV0",
      total_limit=1000,
      oversea_user_info_attr="reader_info_bytes_str"
    )
    ```
    """
    self._add_processor(OverseaLrRetriever(kwargs))
    return self

  def oversea_personalized_cluster(self, **kwargs):
      """
      KsibPersonalizedClusterEnricher
      ------
      根据候选集 items embedding 矩阵个性化Kmeans++聚类,
      返回距离最近的cluster类别.

      参数配置
      ------
      `item_embedding_attr`: [string] item embedding

      `cluster_center_num_attr`: [int] 聚类common attr 个数，类别数

      `output_cluster_attr`: [string] 输出最近的类别

      调用示例
      ------
      ``` python
      .oversea_personalized_cluster(
          name=name("calc_personalized_cluster"),
          item_embedding_attr="rr_cluster_pxtr_list",
          cluster_center_num_attr=16,
          output_cluster_attr="rr_cluster_id")
      ```
      """
      self._add_processor(KsibPersonalizedClusterEnricher(kwargs))
      return self