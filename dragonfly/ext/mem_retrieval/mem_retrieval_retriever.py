#!/usr/bin/env python3
"""
filename: mem_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for mem 
author: liujing<PERSON>@kuaishou.com
date: 2021-12-20
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafRetriever

class BeamSearchGenerateRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "beam_search_generater_retriever"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["cluster_id_attr"])
    ret.add(self._config["duration_ms_attr"])
    for att_ in self._config["pack_slots_attr"]:
      ret.add(att_)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for att_ in self._config["pack_slots_attr"]:
      ret.add(att_)
    ret.add(self._config["photo_list_attr"])
    return ret
