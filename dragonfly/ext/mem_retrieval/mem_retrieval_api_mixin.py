#!/usr/bin/env python3
# coding=utf-8
"""
filename: mem_retrieval_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, mem retrieval api mixin
author: ch<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-02 10:59:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .mem_retrieval_enricher import *
from .mem_retrieval_retriever import *

class MemRetrievalApiMixin(CommonLeafBaseMixin):
  """
  该模块用于调研 memory 在召回的落地，承载相关接口
  可从 colossus 返回结果中筛选长期特征或者训练的 label
  """

  def get_shortterm_seq(self, **kwargs):
    """
    GetShortTermSeqFeaEnricher 
    ------
    get short term seq local build test
    参数
    ------
    ``` python
    .get_shortterm_seq(
         colossus_resp_attr='colossus_output',
         is_simv2 = False,
         slot_as_attr_name = False,
         sampled_slots_name = "sample_slots",
         sampled_signs_name = "sample_signs",
         sampled_minsize_name = "sample_minsize",
         early_return_name = "early_return",
         like = 10.0,
         follow = 20.0,
         forward = 20.0,
         hate = -10.0,
         comment = 2.0,
         enter_profile = 5.0,
         longview = 1.0,
         effview = 0.5,
         trigger_num_per_tag = 5,
         thre_score = 5.0,
         sample_items_num = 50,
       )
    ```
    """
    self._add_processor(GetShortTermSeqFeaEnricher(kwargs))
    return self
  
  def get_longterm_seq(self, **kwargs):
    """
    GetLongTermSeqFeaEnricher 
    ------
    get long term seq local build test
    参数
    ------
    ``` python
    .get_longterm_seq(
         colossus_resp_attr='colossus_output',
         is_simv2 = False,
         slot_as_attr_name = False,
         sampled_slots_name = "sample_slots",
         sampled_signs_name = "sample_signs",
         sampled_minsize_name = "sample_minsize",
         early_return_name = "early_return",
         like = 10.0,
         follow = 20.0,
         forward = 20.0,
         hate = -10.0,
         comment = 2.0,
         enter_profile = 5.0,
         longview = 1.0,
         effview = 0.5,
         trigger_num_per_tag = 5,
         timegap_coffe = 0.05,
         thre_score = 5.0,
         sample_items_num = 50,
         sample_items_range = 600,
       )
    ```
    """
    self._add_processor(GetLongTermSeqFeaEnricher(kwargs))
    return self

  def retrieve_simple_mem_label(self, **kwargs):
    """
    GetSimpleMemFeaEnricher
    ------
    U2I 极简memory网络的 label 抽取
    根据 user colossus 行为, 随机从历史行为中筛选出一些 positive and negative list
    根据有效播放划分正负样本
    参数
    ------
    ``` python
    .retrieve_simple_mem_label(
         colossus_resp_attr='colossus_output',
         slot_as_attr_name = False,
         sampled_slots_name = "sample_slots",
         sampled_signs_name = "sample_signs",
         sampled_labels_name = "sample_labels",
         sampled_time_diff_name = "sample_timediffs",
         sampled_minsize_name = "sample_minsize",
         early_return_name = "early_return",
         sample_items_num = 50,
         sample_items_range = 600,
       )
    ```
    """
    self._add_processor(GetSimpleMemFeaEnricher(kwargs))
    return self
  
  def retrieve_rnd_sample_fea(self, **kwargs):
    """
    GetRandomNegFeaEnricher 
    ------
    随机采样，每个item挂载随机样本，只记录 pid 和 aid
    参数
    ------
    ``` python
    .retrieve_rnd_sample_fea(
         slot_as_attr_name = True,
         buffer_size = 100000,
         filter_pids_attrs = ["current_pids"],
         sampling_cnt_per_item = 1,
       )
    ```
    """
    self._add_processor(GetRandomNegFeaEnricher(kwargs))
    return self

  def user_graph_with_cluster_enricher(self, **kwargs):
    self._add_processor(CommonRecoUserGraphWithClusterEnricher(kwargs))
    return self

  def beam_search_generater_retriever(self, **kwargs):
    """
    BeamSearchGenerateRetriever
    ------
    BeamSearchGenerateRetriever
    ------
    ``` python
    .beam_search_generater(
        org_item_len = "{{item_num}}",
        max_beam_width = 6,
        page_size = 6,
        reason = 666,
        start_idx = 0,
        photo_list_attr = "photo_id_list",
        author_id_attr = "author_id",
        cluster_id_attr = "cluster_id",
        duration_ms_attr = "duration_ms",
        diversity_thresh = 1.0,
        pack_slots_attr = ["26", 128]
    )
    ```
    """
    self._add_processor(BeamSearchGenerateRetriever(kwargs))
    return self

  def gsu_twinsNet_feature_enricherV2(self, **kwargs):
    """
    CommonRecoGsuTwinsNetFeaEnricherV2
    ------
    U2U 双塔孪生网络的召回特征抽取 colossus V2

    参数
    ------
    ``` python
    gsu_twinsNet_feature_enricherV2(
         colossus_resp_attr='colossus_output',
         sample_items_num = 50,
         view_thresh = 10,
         common_slots_name = "user_slots",
         common_signs_name = "user_signs",
         item_slots_name = "sample_slots",
         item_signs_name = "sample_signs",
       )
    ```
    """
    self._add_processor(GetContraTwinsNetFeaV2Enricher(kwargs))
    return self

  def gsu_twinsNet_feature_enricher_v3(self, **kwargs):
    """
    GetContrastiveTwinsFeatV3Enricher
    ------
    U2U 双塔孪生网络的召回特征抽取 colossus V3

    参数
    ------
    ``` python
      gsu_twinsNet_feature_enricher_v3(
        colossus_resp_attr="colossus_output",
        short_common_slots_name="short_common_slots",
        short_common_signs_name="short_common_signs",
        short_item_slots_name="short_item_slots",
        short_item_signs_name="short_item_signs",
        long_common_slots_name="long_common_slots",
        long_common_signs_name="long_common_signs",
        long_item_slots_name="long_item_slots",
        long_item_signs_name="long_item_signs",
        short_pos_sample_items_num=10,
        short_pos_threshold_num=50,
        short_pos_sample_items_range=100,
        short_all_sample_items_num=50,
        short_all_sample_items_range=500,
        long_all_sample_items_num=1000,
        long_all_sample_items_range=2000,
        feature_for_infer=False,  # train 时需要设置为 False 防止信息穿越
        remove_commercial=True,
        commertial_strict=False,
        remove_picture=False,
        remove_recend_aid=False,  # 抽取样本时序列时每个 aid 只有1个 photo
        enable_specified_slots_and_signs=False,
        specified_slots_str_list="",  # "3336,3337,3338,3339,3340,3341,3342,3343,3344,3345",
        specified_signs_str_list="",  # "26,128,1338,1339,1340,1341,1342,1343,1344,1345",
      )
    ```
    """
    self._add_processor(GetContrastiveTwinsFeatV3Enricher(kwargs))
    return self

  def gsu_ironman_feature_label_enricher(self, **kwargs):
    """
    GetIronManFeaEnricher 
    ------
    LTV label

    参数
    ------
    ``` python
    gsu_ironman_feature_label_enricher(
         colossus_resp_attr='colossus_output',
       )
    ```
    """
    self._add_processor(GetIronManFeaEnricher(kwargs))
    return self
  
  def gsu_twinsNet_feature_enricherV2_copy(self, **kwargs):
    """
    GetContraTwinsNetFeaV2EnricherCopy 
    ------
    用于并行着再来一个数据流，用于训练调数据流
    U2U 双塔孪生网络的召回特征抽取 colossus V2

    参数
    ------
    ``` python
    gsu_twinsNet_feature_enricherV2(
         colossus_resp_attr='colossus_output',
         sample_items_num = 50,
         view_thresh = 10,
         common_slots_name = "user_slots",
         common_signs_name = "user_signs",
         item_slots_name = "sample_slots",
         item_signs_name = "sample_signs",
       )
    ```
    """
    self._add_processor(GetContraTwinsNetFeaV2CopyEnricher(kwargs))
    return self


  def cxk_remap_slot_sign_feature(self, **kwargs):
    """CxkSlotSignFeatureRemapEnricher
    ------
    使用 CxkSlotSignFeatureRemapEnricher 进行特征 slot 和 sign 前缀转换。

    注意：输入 slot 和 sign 前缀不一定相等， 例如 mio 抽取的特征，当 slot 超过 16 时，sign 前缀比 slot 小 16。

    参数配置
    ------
    `common_attrs_input`: [list] User 侧需要映射的特征名列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `common_slots_input`: [list] 从哪些 common_attr 中读出 common_slots 列表，需要和 common_parameters_input 对齐，默认为空。

    `common_parameters_input`: [list] 从哪些 common_attr 中读出 common_signs 列表，需要和 common_slots_input 对齐，默认为空。

    `item_attrs_input`: [list] Item 侧需要映射的特征列表，每个特征名对应一个 slot 的 sign 列表，默认为空。

    `item_slots_input`: [list] 从哪些 item_attr 中读出 item_slots 列表，需要和 item_parameters_input 对齐，默认为空。

    `item_parameters_input`: [list] 从哪些 item_attr 中读出 item_signs 列表，需要和 item_slots_input 对齐，默认为空。

    `sign_prefix_bit_num_input`: [int] 需要映射的 sign 的前缀位数，有效范围 (0, 16]。

    `sign_prefix_bit_num_output`: [int] 输出 sign 的前缀位数，有效范围 (0, 16]。

    `common_map_list`: [list] common 侧配置信息， list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶。

    `item_map_list`: [list] item 侧配置信息，含义同 `common_map_list` 。

    `common_slots_output`: [string] User 侧 slots 输出到给定 common attr，默认不输出 User 侧特征。

    `common_parameters_output`: [string] User 侧 sign 输出到给定 common attr，默认不输出 User 侧特征。

    `item_slots_output`: [string] Item 侧 slots 输出到给定 item attr，默认不输出 Item 侧特征。

    `item_parameters_output`: [string] Item 侧 sign 输出到给定 item attr，默认不输出 Item 侧特征。

    `slot_as_attr_name`: [bool] 是否将 slot 作为 attr name，sign 作为 value，如果为 True 则 common_slots_output，common_parameters_output，item_slots_output，item_parameters_output 都需要为空。

    `slot_as_attr_name_prefix`: [string] `slot_as_attr_name` 为 True 时 attr name 的前缀， False 时不生效，默认为空字符串。

    `use_murmur3hash64`: [bool] 是否使用 murmur3hash64 方式进行hash; 默认值为 False。

    `use_djb2_hash64`: [bool] 是否使用 use_djb2_hash64 方式进行hash; 默认值为 False。

    调用示例
    ------
    ``` python


    .cxk_remap_slot_sign_feature(
      common_attrs_input=['reco_37', 'reco_38'],
      common_slots_input=['reco_common_slots', 'reco_common_cofea_slots'],
      common_parameters_input=['reco_common_parameters', 'reco_common_cofea_parameters'],
      item_attrs_input=['reco_18', 'reco_26'],
      item_slots_input=['reco_item_slots'],
      item_parameters_input=['reco_item_parameters'],
      sign_prefix_bit_num_input=16,
      sign_prefix_bit_num_output=10,
      common_map_list=[['reco_37', 7, 7], ['reco_38', 8, 8], [1880, 180, 181, 10001], [1884, 184, 181]},
      item_map_list=[['reco_18', 18, 18, 200001], ['reco_26', 16, 16], [1068, 568, 668]],
      common_slots_output='common_slots',
      common_parameters_output='common_signs',
      item_slots_output='item_slots',
      item_parameters_output='item_signs'
    )
    ```
    """
    self._add_processor(CxkSlotSignFeatureRemapEnricher(kwargs))
    return self
