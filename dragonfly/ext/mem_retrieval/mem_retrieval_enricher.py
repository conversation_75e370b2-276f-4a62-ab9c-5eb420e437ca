#!/usr/bin/env python3
"""
filename: mem_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for mem 
author: ch<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-02 11:10:00
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher

class GetShortTermSeqFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_shortterm_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      ret.add(prefix + "1846")
      ret.add(prefix + "1847")
      ret.add(prefix + "1849")
      ret.add(prefix + "1850")
      ret.add(prefix + "1851")
      ret.add(prefix + "1852")
    else:
      ret.add(self._config["sampled_slots_name"])
      ret.add(self._config["sampled_signs_name"])
    ret.add(self._config["early_return_name"])
    ret.add(self._config["sampled_minsize_name"])
    return ret

class GetLongTermSeqFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_longterm_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      ret.add(prefix + "1646")
      ret.add(prefix + "1647")
      ret.add(prefix + "1649")
      ret.add(prefix + "1650")
      ret.add(prefix + "1651")
      ret.add(prefix + "1652")
    else:
      ret.add(self._config["sampled_slots_name"])
      ret.add(self._config["sampled_signs_name"])
    ret.add(self._config["early_return_name"])
    ret.add(self._config["sampled_minsize_name"])
    return ret

class GetSimpleMemFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_simple_mem_label"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sampled_slots_name"])
    ret.add(self._config["sampled_signs_name"])
    ret.add(self._config["sampled_labels_name"])
    ret.add(self._config["sampled_time_diff_name"])
    ret.add(self._config["sampled_minsize_name"])
    ret.add(self._config["early_return_name"])
    if (self._config["save_org_item_info"]):
        ret.add(self._config["pids_attr_name"])
        ret.add(self._config["aids_attr_name"])
    return ret

class GetRandomNegFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_rnd_sample_fea"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for att_ in self._config["filter_pids_attrs"]:
      ret.add(att_)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("rnd_early_return")
    return ret

class CommonRecoUserGraphWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_graph_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    if self._config.get("use_dumped_mmu_cluster", False):
      ret.add(self._config["mmu_cluster_pid_attr"])
      ret.add(self._config["mmu_cluster_cid_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      slots = self._config.get("mio_slots_id")
      for slot in slots:
        ret.add(prefix + str(slot))
    else:
      ret.add(self._config["output_slot_attr"])
      ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_c2i_masks_attr"])
    ret.add(self._config["output_c2c_relations_attr"])
    return ret

class GetContraTwinsNetFeaV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_twinsNet_feature_enricherV2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["common_slots_name"])
    ret.add(self._config["common_signs_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_slots_name"])
    ret.add(self._config["item_signs_name"])
    return ret

class GetContrastiveTwinsFeatV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_twinsNet_feature_enricher_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["short_common_slots_name"])
    ret.add(self._config["short_common_signs_name"])
    ret.add(self._config["long_common_slots_name"])
    ret.add(self._config["long_common_signs_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["short_item_slots_name"])
    ret.add(self._config["short_item_signs_name"])
    ret.add(self._config["long_item_slots_name"])
    ret.add(self._config["long_item_signs_name"])
    return ret

class GetIronManFeaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_ironman_feature_label_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("feature_for_infer", False):
      if self._config.get("slot_as_attr_name", False):
        ret.add("2346")
        ret.add("2347")
        ret.add("2348")
        ret.add("2349")
        ret.add("2350")
      else:
        ret.add(self._config["sampled_slots_name"])
        ret.add(self._config["sampled_signs_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("feature_for_infer", False):
      if self._config.get("slot_as_attr_name", False):
        ret.add("2346")
        ret.add("2347")
        ret.add("2348")
        ret.add("2349")
        ret.add("2350")
      else:
        ret.add(self._config["sampled_slots_name"])
        ret.add(self._config["sampled_signs_name"])
    return ret

class GetContraTwinsNetFeaV2CopyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_twinsNet_feature_enricherV2_copy"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["common_slots_name"])
    ret.add(self._config["common_signs_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_slots_name"])
    ret.add(self._config["item_signs_name"])
    return ret

class CxkSlotSignFeatureRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cxk_remap_slot_sign_feature"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_attrs_input", "item_slots_input", "item_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("item_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_attrs_input", "common_slots_input", "common_parameters_input"]:
      if key in self._config:
        attrs.update(set(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "slot_as_attr_name" in self._config:
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      vals = [row[1] for row in self._config.get("common_map_list", [[]]) if row]
      attrs.update({"{}{}".format(prefix, val) for val in vals})
    return attrs
