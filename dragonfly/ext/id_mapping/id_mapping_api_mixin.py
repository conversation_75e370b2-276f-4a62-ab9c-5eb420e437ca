#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .id_mapping_enricher import *


class IdMappingMixin(CommonLeafBaseMixin):
  def ab_xid_cache(self, **kwargs):
    """
    AbXidCacheEnricher
    ------
    ab_xid 本地 cache

    参数配置
    ------
    `operation`: [string] 针对 cache 操作 只允许设置 "get" 和 "set" 操作。
    
    `ab_uid_attr`: [string]  ab_uid 属性 attr 名称 operation 为 get 时 为输出, set 时为输入
  
    `ab_did_attr`: [string]  ab_did 属性 attr 名称 operation 为 get 时 为输出, set 时为输入

    `ab_xid_cache_hit_flag`: [string] 选配项 存储 ab_xid cache 命中标识的 attr 名称 operation 为 get 时为必配项，set 时为选配项 

    `allow_only_uid`: [bool] 选配项 是否允许无did请求。默认为false

    `expect_skip_sleep_times`: [int] 选配项 是否允许访问存储之后可以跳过多少次 sleep

    调用示例
    ------
    ``` python

    flow.ab_xid_cache(
      operation = "get",
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      ab_xid_cache_hit_flag = "cache_git_flag"
    )

    ```
    """
    
    self._add_processor(AbXidCacheEnricher(kwargs))
    return self

  def uid_did_computer_ab_xid(self, **kwargs):
    
    """
    UidDidComputerABxidEnricher
    ------
    ab_xid 计算算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `ab_xid_write_flag`: [string] 存储 ab_xid 写入标识的 attr 名称。

    调用示例
    ------
    ``` python

    flow.uid_did_computer_ab_xid(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      ab_xid_write_flag = "ab_xid_write_flag"
    )

    ```
    """

    self._add_processor(UidDidComputerABxidEnricher(kwargs))
    return self


  def get_ab_xid_from_redis(self, **kwargs):
    
    """
    GetAbXidFromRedisEnricher
    ------
    从 redis 读取 ab_xid 计算算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `kcc_cluster`: [string] redis 。

    `timeout_ms`: [int] redis 超时时间，默认 10 ms。

    `redis_error_code`: [string] 存储 redis 错误代码。

    调用示例
    ------
    ``` python

    flow.get_ab_xid_from_redis(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      kcc_cluster = "xxx",
      redis_error_code = "redis_error_code",
      timeout_ms = 20
    )

    ```
    """

    self._add_processor(GetAbXidFromRedisEnricher(kwargs))
    return self


  def write_ab_xid_to_redis(self, **kwargs):
    
    """
    WriteAbXidToRedisEnricher
    ------
    写生成的 ab_xid 到 redis 缓存算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `kcc_cluster`: [string] redis 。

    `timeout_ms`: [int] redis 超时时间，默认 10 ms。

    `ab_xid_write_flag`: [string] 存储 ab_xid 写入标识的 attr 名称。

    `redis_error_code`: [string] 存储 redis 错误代码。

    调用示例
    ------
    ``` python

    flow.write_ab_xid_to_redis(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      ab_xid_write_flag = "ab_xid_write_flag",
      kcc_cluster = "xxx",
      redis_error_code = "redis_error_code",
      timeout_ms = 20
    )

    ```
    """

    self._add_processor(WriteAbXidToRedisEnricher(kwargs))
    return self

  def get_ab_xid_from_rodis(self, **kwargs):
    
    """
    GetAbXidFromRodisEnricher
    ------
    从 rodis 读取 ab_xid 计算算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `rodis_kess_name`: [string] redis 。

    `rodis_domain`: [string] redis 。

    `timeout_ms`: [int] redis 超时时间，默认 10 ms。

    `redis_error_code`: [string] 存储 redis 错误代码。

    `allow_only_uid`: [bool] 选配项 是否允许无did请求。默认为false

    调用示例
    ------
    ``` python

    flow.get_ab_xid_from_redis(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      rodis_kess_name = "xxx",
      rodis_domain = "xxx",
      redis_error_code = "redis_error_code",
      timeout_ms = 20
    )

    ```
    """

    self._add_processor(GetAbXidFromRodisEnricher(kwargs))
    return self

  def write_ab_xid_to_rodis(self, **kwargs):
    
    """
    WriteAbXidToRodisEnricher
    ------
    写生成的 ab_xid 到 rodis 缓存算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `rodis_kess_name`: [string] redis 。

    `rodis_domain`: [string] redis 。

    `timeout_ms`: [int] redis 超时时间，默认 10 ms。

    `ab_xid_write_flag`: [string] 存储 ab_xid 写入标识的 attr 名称。

    `redis_error_code`: [string] 存储 redis 错误代码。

    调用示例
    ------
    ``` python

    flow.write_ab_xid_to_redis(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      ab_xid_write_flag = "ab_xid_write_flag",
      rodis_kess_name = "xxx",
      rodis_domain = "xxx",
      redis_error_code = "redis_error_code",
      timeout_ms = 20
    )

    ```
    """

    self._add_processor(WriteAbXidToRodisEnricher(kwargs))
    return self


  def package_mapping_id(self, **kwargs):
    
    """
    PackageMappingIdEnricher
    ------
    写生成的 ab_xid 到 redis 缓存算子

    参数配置
    ------
    
    `key`: [string] 映射的 key 的名称。 
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 

    `ab_did_attr`: [string] ab_did 属性 attr 名称

    `pb_str_attr_name`: [string] 输出 pb 序列化结果的 attr 名称。

    `allow_only_uid`: [bool] 选配项 是否允许无 did 请求。默认为 false

    `complement_empty_mapping_id`: [bool] 选配项 是否允许补充 miss 的 mapping id。默认为 true 注意: 补充方案为已登录全部补充为 uid 未登录全部补充为 did，只有 uid 只补充 ab_uid 。

    调用示例
    ------
    ``` python

    flow.package_mapping_id(
      key = "test",
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      pb_str_attr_name = "pb_str"
    )

    ```
    """

    self._add_processor(PackageMappingIdEnricher(kwargs))
    return self

  def get_ab_xid_from_rodis_multi(self, **kwargs):
    
    """
    GetAbXidFromRodisMultiEnricher
    ------
    从 rodis 读取 ab_xid 计算算子

    参数配置
    ------
    
    `ab_uid_attr`: [string] ab_uid 属性 attr 名称 
    
    `ab_did_attr`: [string] ab_did 属性 attr 名称 

    `rodis_kess_name`: [string] redis 。

    `rodis_domain`: [string] redis 。

    `timeout_ms`: [int] redis 超时时间，默认 10 ms。

    `redis_error_code`: [string] 存储 redis 错误代码。

    调用示例
    ------
    ``` python

    flow.get_ab_xid_from_redis(
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      rodis_kess_name = "xxx",
      rodis_domain = "xxx",
      redis_error_code = "redis_error_code",
      timeout_ms = 20
    )

    ```
    """

    self._add_processor(GetAbXidFromRodisMultiEnricher(kwargs))
    return self

  def ab_xid_cache_multi(self, **kwargs):
    """
    AbXidCacheMultiEnricher
    ------
    ab_xid 本地 cache

    参数配置
    ------
    `operation`: [string] 针对 cache 操作 只允许设置 "get" 和 "set" 操作。
    
    `ab_uid_attr`: [string]  ab_uid 属性 attr 名称 operation 为 get 时 为输出, set 时为输入
  
    `ab_did_attr`: [string]  ab_did 属性 attr 名称 operation 为 get 时 为输出, set 时为输入

    `ab_xid_cache_hit_flag`: [string] 选配项 存储 ab_xid cache 命中标识的 attr 名称 operation 为 get 时为必配项，set 时为选配项 

    调用示例
    ------
    ``` python

    flow.ab_xid_cache(
      operation = "get",
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      ab_xid_cache_hit_flag = "cache_git_flag"
    )

    ```
    """
    
    self._add_processor(AbXidCacheMultiEnricher(kwargs))
    return self



  def package_mapping_id_multi(self, **kwargs):
    
    """
    PackageMappingIdMultiEnricher
    ------
    写生成的 ab_xid 到 redis 缓存算子

    参数配置
    ------
    
    `key`: [string] 映射的 key 的名称。 
    
    `uid_list_attr`: [string] ab_uid 属性 attr 名称 

    `did_list_attr`: [string] ab_did 属性 attr 名称

    `id_mapping_pb_str_list`: [string] 输出 pb 序列化结果的 attr 名称。


    调用示例
    ------
    ``` python

    flow.multi_test(
      key = "test",
      uid_list_attr = "uid_list",
      did_list_attr = "did_list",
      id_mapping_pb_str_list = "id_mapping_pb_str_list"
    )
    ```
    """

    self._add_processor(PackageMappingIdMultiEnricher(kwargs))
    return self


  def ab_xid_bloom_filter(self, **kwargs):
    
    """
    AbXidBloomFilterEnricher
    ------
    ab_xid 本地布隆过滤器

    参数配置
    ------
    `operation`: [string] 针对布隆过滤器操作 只允许设置 "get" 和 "set" 操作。
    
    `ab_uid_attr`: [string]  ab_uid 属性 attr 名称 operation 为 get 时 为输出, set 时为输入
  
    `ab_did_attr`: [string]  ab_did 属性 attr 名称 operation 为 get 时 为输出, set 时为输入

    `bloomfilter_hit_flag`: [string] 选配项 存储 ab_xid bloom filter 命中标识的 attr 名称 operation 为 get 时为必配项，set 时为选配项 

    调用示例
    ------
    ``` python

    flow.ab_xid_cache(
      operation = "get",
      ab_uid_attr = "ab_uid",
      ab_did_attr = "ab_did",
      bloomfilter_hit_flag = "bloom_hit_flag"
    )

    ```
    """

    self._add_processor(AbXidBloomFilterEnricher(kwargs))
    return self
