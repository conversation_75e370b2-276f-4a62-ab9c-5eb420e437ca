#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class AbXidCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ab_xid_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("operation") == "set":
      attrs.add(self._config.get("ab_uid_attr", ""))
      attrs.add(self._config.get("ab_did_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("operation") == "get":
      attrs.add(self._config.get("ab_uid_attr", ""))
      attrs.add(self._config.get("ab_did_attr", ""))
      attrs.add(self._config.get("ab_xid_cache_hit_flag", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("operation", "") in ["get", "set"], "operation 只能是 get 或 set")
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")

    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("operation", ""), str), "operation 需为 string")
    if self._config.get("operation") == "get":
      check_arg(len(self._config.get("ab_xid_cache_hit_flag", "")) > 0, "ab_xid_cache_hit_flag 为必配项")
      check_arg(isinstance(self._config.get("ab_xid_cache_hit_flag", ""), str), "ab_xid_cache_hit_flag 需为 string")
      if("expect_skip_sleep_times" in self._config):
        check_arg(isinstance(self._config.get("expect_skip_sleep_times", ""), int), "expect_skip_sleep_times 需为 int")
        check_arg(self._config.get("expect_skip_sleep_times", 0) > 0, "expect_skip_sleep_times 需 > 0")



class UidDidComputerABxidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uid_did_computer_ab_xid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("ab_xid_write_flag", "")) > 0 , "ab_xid_write_flag 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_xid_write_flag", ""), str), "ab_xid_write_flag 需为 string")

class WriteAbXidToRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_ab_xid_to_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    attrs.add(self._config.get("redis_error_code", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("ab_xid_write_flag", "")) > 0 , "ab_xid_write_flag 为必配项")
    check_arg(len(self._config.get("kcc_cluster", "")) > 0 , "kcc_cluster 为必配项")
    check_arg(len(self._config.get("redis_error_code", "")) > 0 , "redis_error_code 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_xid_write_flag", ""), str), "ab_xid_write_flag 需为 string")
    check_arg(isinstance(self._config.get("kcc_cluster", ""), str), "kcc_cluster 需为 string")
    check_arg(isinstance(self._config.get("redis_error_code", ""), str), "redis_error_code 需为 string")

class GetAbXidFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ab_xid_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    attrs.add(self._config.get("ab_xid_cache_hit_flag", ""))
    attrs.add(self._config.get("redis_error_code", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("kcc_cluster", "")) > 0 , "kcc_cluster 为必配项")
    check_arg(len(self._config.get("ab_xid_cache_hit_flag", "")) > 0, "ab_xid_cache_hit_flag 为必配项")
    check_arg(len(self._config.get("redis_error_code", "")) > 0 , "redis_error_code 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("kcc_cluster", ""), str), "kcc_cluster 需为 string")
    check_arg(isinstance(self._config.get("ab_xid_cache_hit_flag", ""), str), "ab_xid_cache_hit_flag 需为 string")
    check_arg(isinstance(self._config.get("redis_error_code", ""), str), "redis_error_code 需为 string")

class WriteAbXidToRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_ab_xid_to_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    attrs.add(self._config.get("rodis_error_code", ""))
    if "log_status_attr" in self._config:
      attrs.add(self._config.get("log_status_attr", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("ab_xid_write_flag", "")) > 0 , "ab_xid_write_flag 为必配项")
    check_arg(len(self._config.get("rodis_kess_name", "")) > 0 , "rodis_kess_name 为必配项")
    check_arg(len(self._config.get("rodis_domain", "")) > 0 , "rodis_domain 为必配项")
    check_arg(len(self._config.get("rodis_error_code", "")) > 0 , "rodis_error_code 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_xid_write_flag", ""), str), "ab_xid_write_flag 需为 string")
    check_arg(isinstance(self._config.get("rodis_kess_name", ""), str), "rodis_kess_name 需为 string")
    check_arg(isinstance(self._config.get("rodis_domain", ""), str), "rodis_domain 需为 string")
    check_arg(isinstance(self._config.get("rodis_error_code", ""), str), "rodis_error_code 需为 string")
    check_arg(isinstance(self._config.get("log_status_attr", ""), str), "log_status_attr 需为 string")

class GetAbXidFromRodisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ab_xid_from_rodis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    attrs.add(self._config.get("ab_xid_cache_hit_flag", ""))
    attrs.add(self._config.get("rodis_error_code", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("rodis_kess_name", "")) > 0 , "rodis_kess_name 为必配项")
    check_arg(len(self._config.get("rodis_domain", "")) > 0 , "rodis_domain 为必配项")
    check_arg(len(self._config.get("ab_xid_cache_hit_flag", "")) > 0, "ab_xid_cache_hit_flag 为必配项")
    check_arg(len(self._config.get("rodis_error_code", "")) > 0 , "rodis_error_code 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("rodis_kess_name", ""), str), "rodis_kess_name 需为 string")
    check_arg(isinstance(self._config.get("rodis_domain", ""), str), "rodis_domain 需为 string")
    check_arg(isinstance(self._config.get("ab_xid_cache_hit_flag", ""), str), "ab_xid_cache_hit_flag 需为 string")
    check_arg(isinstance(self._config.get("rodis_error_code", ""), str), "rodis_error_code 需为 string")


class PackageMappingIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "package_mapping_id"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ab_uid_attr", ""))
    attrs.add(self._config.get("ab_did_attr", ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pb_str_attr_name", ""))

    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
    check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
    check_arg(len(self._config.get("key", "")) > 0 , "key 为必配项")
    check_arg(len(self._config.get("pb_str_attr_name", "")) > 0, "pb_str_attr_name 为必配项")
    check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    check_arg(isinstance(self._config.get("key", ""), str), "key 需为 string")
    check_arg(isinstance(self._config.get("pb_str_attr_name", ""), str), "pb_str_attr_name 需为 string")

class AbXidCacheMultiEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ab_xid_cache_multi"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("uid_list_attr", ""))
    attrs.add(self._config.get("did_list_attr", ""))
    if self._config.get("operation") == "set":
      attrs.add(self._config.get("ab_uid_list_attr", ""))
      attrs.add(self._config.get("ab_did_list_attr", ""))
      attrs.add(self._config.get("ab_xid_cache_hit_flag_list", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("operation") == "get":
      attrs.add(self._config.get("ab_uid_list_attr", ""))
      attrs.add(self._config.get("ab_did_list_attr", ""))
      attrs.add(self._config.get("ab_xid_cache_hit_flag_list", ""))
      attrs.add(self._config.get("ab_xid_all_hit_flag", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("operation", "") in ["get", "set"], "operation 只能是 get 或 set")
    check_arg(len(self._config.get("uid_list_attr", "")) > 0, "uid_list_attr 为必配项")
    check_arg(len(self._config.get("did_list_attr", "")) > 0, "did_list_attr 为必配项")
    check_arg(len(self._config.get("ab_uid_list_attr", "")) > 0, "ab_uid_list_attr 为必配项")
    check_arg(len(self._config.get("ab_did_list_attr", "")) > 0, "ab_did_list_attr 为必配项")

    check_arg(isinstance(self._config.get("uid_list_attr", ""), str), "uid_list_attr 需为 string")
    check_arg(isinstance(self._config.get("did_list_attr", ""), str), "did_list_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_uid_list_attr", ""), str), "ab_uid_list_attr 需为 string")
    check_arg(isinstance(self._config.get("ab_did_list_attr", ""), str), "ab_did_list_attr 需为 string")
    check_arg(isinstance(self._config.get("operation", ""), str), "operation 需为 string")
    if self._config.get("operation") == "get":
      check_arg(len(self._config.get("ab_xid_cache_hit_flag_list", "")) > 0, "ab_xid_cache_hit_flag_list 为必配项")
      check_arg(isinstance(self._config.get("ab_xid_cache_hit_flag_list", ""), str), "ab_xid_cache_hit_flag_list 需为 string")
      check_arg(len(self._config.get("ab_xid_all_hit_flag", "")) > 0, "ab_xid_all_hit_flag 为必配项")
      check_arg(isinstance(self._config.get("ab_xid_all_hit_flag", ""), str), "ab_xid_all_hit_flag 需为 string")


class GetAbXidFromRodisMultiEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ab_xid_from_rodis_multi"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["uid_list_attr","did_list_attr","ab_uid_list_attr","ab_did_list_attr","ab_xid_cache_hit_flag_list"]:
      attrs.add(self._config.get(attr, ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for attr in ["ab_uid_list_attr", "ab_did_list_attr"]:
      attrs.add(self._config.get(attr, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for attr in ["uid_list_attr","did_list_attr","ab_uid_list_attr","ab_did_list_attr","ab_xid_cache_hit_flag_list"]:
      check_arg(len(self._config.get(attr, "")) > 0, "{} 为必配项".format(attr))
      check_arg(isinstance(self._config.get(attr, ""), str), "{} 需为 string".format(attr))


class PackageMappingIdMultiEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "package_mapping_id_multi"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["uid_list_attr", "did_list_attr", "ab_uid_list_attr", "ab_did_list_attr"]:
      attrs.add(self._config.get(attr, ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("id_mapping_pb_str_list", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for attr in ["uid_list_attr","did_list_attr","ab_uid_list_attr","ab_did_list_attr", "id_mapping_pb_str_list"]:
      check_arg(len(self._config.get(attr, "")) > 0, "{} 为必配项".format(attr))
      check_arg(isinstance(self._config.get(attr, ""), str), "{} 需为 string".format(attr))
  
class AbXidBloomFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ab_xid_bloom_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if(self._config["operation"] == "set"):
      attrs.add(self._config.get("ab_uid_attr", ""))
      attrs.add(self._config.get("ab_did_attr", ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if(self._config["operation"] == "get"):
      attrs.add(self._config.get("bloomfilter_hit_flag", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("operation", ""), str), "operation 需为 string")
    check_arg(self._config.get("operation", "") in ["get", "set"], "operation 只能是 get 或 set")
    if self._config.get("operation") == "set":
      check_arg(len(self._config.get("ab_uid_attr", "")) > 0, "ab_uid_attr 为必配项")
      check_arg(len(self._config.get("ab_did_attr", "")) > 0, "ab_did_attr 为必配项")
      check_arg(isinstance(self._config.get("ab_uid_attr", ""), str), "ab_uid_attr 需为 string")
      check_arg(isinstance(self._config.get("ab_did_attr", ""), str), "ab_did_attr 需为 string")
    if self._config.get("operation") == "get":
      check_arg(len(self._config.get("bloomfilter_hit_flag", "")) > 0, "bloomfilter_hit_flag 为必配项")