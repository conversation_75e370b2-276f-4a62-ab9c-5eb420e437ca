#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class SlideCommercialDefaultMixRankByCodeProcessor(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_commercial_default_mix_rank_by_code"

  @strict_types
  def is_async(self) -> bool:
    return False

class NebulaMixRankListProcessor(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nebula_mix_rank_list_process"

  @strict_types
  def is_async(self) -> bool:
    return False

class NebulaListwiseRerankProcessor(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nebula_listwise_rerank_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class NebulaRecoEnsembleSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nebula_reco_ensemble_sort_rerank"

  @strict_types
  def is_async(self) -> bool:
    return False

class AdPackEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pack_ad_info"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")

class MixRankPostProcessEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_post_process"

  @strict_types
  def is_async(self) -> bool:
    return False 

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_resp_attr"))
    return attrs

class SlideMixRankLastJudgeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_last_judge"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchHistoryFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_history_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchSubsidyFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_subsidy_from_redis"

class MixRankFetchBigRMarkFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_big_r_mark_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchProduceHotAuthorFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_produce_hot_author_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchSensitivityFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_sensitivity_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchSenPluginFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_sen_plugin_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchMerchantSensitivityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_merchant_sensitivity_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchRoiHistoryFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_roi_history_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchRoiHistoryV2FromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_roi_history_v2_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchCoeffectFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_coeffect_data_from_redis"

class MixRankFetchLiveRevenueFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_live_revenue_from_redis"


class MixRankFetchFollowLiveMixScoreFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_follow_live_mix_score_from_redis"

class NebulaMixRankSampleProcessor(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nebula_mix_rank_sample"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankUpdateAdPriceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_update_ad_price"

class MixRankPostUpdateAdPriceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_post_update_ad_price"

class MixRankWriteAdSctrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_write_ad_sctr_enricher"

class WriteUserAdValueToRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_user_ad_value_to_redis_enricher"

class WriteBatchAdValueForRecoRLEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_batch_ad_value_for_reco_rl_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixItemShowCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_item_show_count_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchUserCommercialValueFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_user_commercial_value_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchAdItemAttrFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mix_ad_item_attr_from_redis"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainListRerankProcessor(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_list_rerank"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankAdAuctionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_ad_auction_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankFetchLoadRedisKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_fetch_load_redis_key"

  @strict_types
  def is_async(self) -> bool:
    return False

class ParallelAbtestCommonAttrEnricher(LeafEnricher):
    @strict_types
    def __init__(self, config: dict):
        TYPE_NAME_MAP = {
            "int": "int",
            "float": "double",
            "str": "string",
            "bool": "bool",
        }
        prioritized_suffix = config.get("prioritized_suffix")
        for param in config["ab_params"]:
            param_name = param.get("param_name")
            check_arg(isinstance(param_name, (str, dict)),
                      "ab_params 里的 param_name 值必须为 string 或 dict 类型")
            check_arg(param_name, "ab_params 里的 param_name 不可为空")
            if prioritized_suffix or isinstance(param_name, dict):
                check_arg(
                    param.get("attr_name", ""),
                    f"配置了 prioritized_suffix 或 param_name 为 dict 类型的情况下必须指定 attr_name"
                )
            if "param_type" not in param:
                type_name = type(param["default_value"]).__name__
                check_arg(
                    type_name in TYPE_NAME_MAP,
                    f"非法的 abtest param default_value: {param['default_value']}"
                )
                param["param_type"] = TYPE_NAME_MAP[type_name]
        super().__init__(config)

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "get_abtest_params"

    @strict_types
    def depend_on_items(self) -> bool:
        return False

    def __get_attr_names(self) -> set:
        return {
            ab.get("attr_name") or ab["param_name"]
            for ab in self._config["ab_params"]
        }

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["biz_name", "prioritized_suffix", "user_id", "device_id", "session_id", \
                     "product", "platform", "app_version"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        # 下面两个静态 Common Attrs 一般不应该使用的，就不出现 API 文档里面了
        attrs.add("_ABTEST_USER_TAG_NAMES_")
        attrs.add("_ABTEST_USER_TAG_VALUES_")
        for ab in self._config["ab_params"]:
            attrs.update(self.extract_dynamic_params(ab.get("report_ab_hit")))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        return set() if self._config.get("for_item_level",
                                         False) else self.__get_attr_names()

class MixRankAbtestTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_enrich_abtest_user_tag"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_req_attr"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("_ABTEST_USER_TAG_NAMES_")
    attrs.add("_ABTEST_USER_TAG_VALUES_")
    return attrs
