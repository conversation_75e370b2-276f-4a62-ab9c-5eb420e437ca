#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class MixRankPreProcessRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_pre_process"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_req_attr"))
    return attrs

class HotMixRankRecoPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_reco_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False
class HotSingleRecoPhotoGenESProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_gen_es_processor"

  @strict_types
  def is_async(self) -> bool:
    return False
class HotSingleRecoPhotoGenModel(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_gen_model"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoPreProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_pre_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoPostProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_post_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixMetaLogObserver(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_meta_log_observer"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixPostProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_post_process"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoRuleGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_rule_generator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoModelGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_model_generator"

  @strict_types
  def is_async(self) -> bool:
    return True

class HotSingleRecoPhotoAsyncModelGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_async_model_generator"

  @strict_types
  def is_async(self) -> bool:
    return True

class HotSingleRecoPhotoModelGenerator2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_model_generator2"

  @strict_types
  def is_async(self) -> bool:
    return True

class HotSingleRecoPhotoEvaluator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_evaluator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRandomRecoPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_random_reco_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRelationRecoPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_relation_reco_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoLiveProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_live_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotLiveSeqGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_live_seq_generator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotRelaSeqGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_rela_seq_generator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMultiQueueGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_multi_queue_generator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixLiveScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_live_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixMerchantLiveScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_merchant_live_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRelationScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_relation_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRecoPhotoScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_reco_photo_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixRecoPhotoScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_reco_photo_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixExploreAdScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_explore_ad_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixAdScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_ad_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixLiveScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_live_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixMerchantLiveScoreEnricher(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_merchant_live_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixAuctionSeqRetrieval(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_auction_seq_retrieval"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixAuctionSeqEvaluator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_auction_seq_evaluator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixMultiResultsSort(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_multi_results_sort"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixAuctionSeqEvaluator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_auction_seq_evaluator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixAuctionSeqRetrieval(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_auction_seq_retrieval"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotSingleRecoPhotoBSGenerator(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_single_reco_photo_bs_generator"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankEnsembleSortProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_ensemble_sort_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotExploreLiveResortProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_explore_live_resort_processor"

  @strict_types
  def is_async(self) -> bool:
    return False
class HotMixRankResortByRuleProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_resort_by_rule_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankMultiQueueGeneratorProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_multi_queue_generator_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankModelRank(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_model_rank"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankPostInjectProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_post_inject_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankAddAttrToKafkaProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_add_attr_to_kafka_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankSendAbtestMetricsProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_send_abtest_metrics_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankWriteInfoToRedisProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_write_info_to_redis_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankPostProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_post_process"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_resp_attr"))
    return attrs

class MixRankHotPreProcessRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_hot_pre_process"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_req_attr"))
    return attrs

class FountainMixRankPreProcessRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_rank_pre_process"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mix_rank_req_attr"))
    return attrs

class FountainMixRankByRuleProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_rank_by_rule"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideUniverseAuctionProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_mix_universe_auction_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideLiveStreamProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_live_stream_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideMerchantCardProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_merchant_card_processor"

class SlideFissionCardProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_fission_card_processor"

class SlideRecoPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_reco_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideRandomPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_random_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideLocalLifePhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_local_life_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideMarketingSurveyProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_marketing_survey_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideInterestPhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "slide_interest_photo_processor"

  @strict_types
  def is_async(self) -> bool:
      return False

class SlideNrExplorePhotoProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_nr_explore_photo_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideRecoUserCardProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_reco_user_card_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideRecoPhotoCardProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_reco_photo_card_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideRecoMaterialCardProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_reco_material_card_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class MixRankLeafShowObserver(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mix_rank_leaf_show"

  @strict_types
  def is_async(self) -> bool:
    return False

class HotMixRankLeafShowObserver(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_mix_rank_leaf_show"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixRankPostInjectProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_rank_post_inject_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class FountainMixRankResortByRuleProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_mix_rank_resort_by_rule_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class SlideMixMemoryDataProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_mix_memory_data_processor"

  @strict_types
  def is_async(self) -> bool:
    return False