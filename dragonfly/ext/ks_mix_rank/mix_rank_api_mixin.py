#!/usr/bin/env python3
# coding=utf-8
"""
filename: mix_rank_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,mix rank api mixin
author: ch<PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-03-03 16:54:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .mix_rank_retriever import *
from .mix_rank_enricher import *

class MixRankApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 mix_rank 相关的 Processor 接口
  """

  def mix_rank_pre_process(self, **kwargs):
    """
    MixRankPreProcessRetriever
    ------
    完成混排模块中不同混排策略共同的数据预处理工作

    参数配置
    ------
    `mix_rank_req_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::MixRankRequest 的 common_attr

    调用示例
    ------
    ``` python
    .mix_rank_pre_process(
      mix_rank_req_attr="mix_rank_request"
    )
    ```
    """
    self._add_processor(MixRankPreProcessRetriever(kwargs))
    return self

  def mix_rank_hot_pre_process(self, **kwargs):
    """
    MixRankHotPreProcessRetriever
    ------
    完成混排模块中不同混排策略共同的数据预处理工作，兼容单双列请求

    参数配置
    ------
    `mix_rank_req_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::MixRankRequest 的 common_attr

    调用示例
    ------
    ``` python
    .mix_rank_hot_pre_process(
      mix_rank_req_attr="mix_rank_request"
    )
    ```
    """
    self._add_processor(MixRankHotPreProcessRetriever(kwargs))
    return self

  def mix_rank_post_process(self, **kwargs):
    """
    MixRankPostProcessEnricher
    ------
    完成混排模块后处理、响应填充等过程

    参数配置
    ------
    `mix_rank_resp_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::MixRankResponse 的 common_attr

    调用示例
    ------
    ``` python
    .mix_rank_post_process(
      mix_rank_resp_attr="mix_rank_response_bytes"
    )
    ```
    """
    self._add_processor(MixRankPostProcessor(kwargs))
    return self

  def mix_rank_leaf_show(self, **kwargs):
    """
    MixRankLeafShowObserver
    ------
    完成混排定制的特征抽取及样本发送

    参数配置
    ------
    `enable_send_sample`: [bool] 动态参数，选配，默认 false 不发送
    `biz_name`: [string] 必配.业务名，需要和 其他发送接口 和 SampleJoin 保持一致
    `bt_queue_name`: [string] 必配. 发送的 btq topic_name 由 bt_queue_name + shard_id 构成（shard_id = 0,1,2,...,shard_num)
    `shard_num_`: [int] 选配. btq 单个 topic 容量有限，有时需要分为多个 shard。默认 shard_num = 1
    `common_key_choice`: [string] 选配，仅支持 "request_id", "device_id", "user_id" 字符串中一个，默认"user_id"
    `as_extra_attrs`: [bool] 选配，是否将 item 的所有 `attrs` 特征值作为样本拼接的 extra_attrs 发送, 默认 false
    `user_extractors`: [list] 必配. 抽取 User 级别特征, 依次调用对应名字类的 extract() 函数, 需要在 mix_rank_feature_extractor.cc 进行注册, 参考 SlideMixRankUserExtractorDemo
    `item_extractors`: [list] 必配. 抽取 Item 级别特征, 依次调用对应名字类的 extract() 函数, 需要在 mix_rank_feature_extractor.cc 进行注册, 参考 SlideMixRankItemExtractorDemo
    `user_merge_cached_feature_names`: [list] 选配. 配置时，用户可以在任何位置自定义抽取 User 级别特征，并将 User 侧 PredictItem 和字符串关联起来.
                                      在这里进行配置的字符串, 会以此为 key 去取对应的之前抽取好的 PredictItem，并 merge 到最后的 CommonAttr, 参考 MockCachePredictItem
    `item_merge_cached_feature_names`: [list] 选配. 配置时，用户可以在任何位置抽取一组 Item 级别的特征，并将这组 Item 侧 PredictItem 和字符串关联，
                                      在这里进行配置的字符串, 会以此为 key 去取对应的之前抽取好一组 PredictItem，并再以最后结果的 PhotoId() 为 key 查对应的 PredictItem
                                      最后, merge 到发送的 ItemAttrs, 参考 MockCachePredictItem

    调用示例
    ------
    ``` python
    .mix_rank_leaf_show(
      enable_send_sample=true,
      biz_name="slide_hot_mix",
      bt_queue_name="slide_hot_mix_attr",
      shard_num=16,
      common_key_choice="user_id"
      user_extractors=["SlideMixRankUserExtractorDemo"],
      item_extractors=["SlideMixRankItemExtractorDemo"],
      user_merge_cached_feature_names=["UserTest"],
      item_merge_cached_feature_names=["Test"]
    )
    ```
    """
    self._add_processor(MixRankLeafShowObserver(kwargs))
    return self

  def hot_mix_rank_leaf_show(self, **kwargs):
    """
    HotMixRankLeafShowObserver
    ------
    完成混排定制的特征抽取及样本发送

    参数配置
    ------
    `enable_send_sample`: [bool] 动态参数，选配，默认 false 不发送
    `biz_name`: [string] 必配.业务名，需要和 其他发送接口 和 SampleJoin 保持一致
    `bt_queue_name`: [string] 必配. 发送的 btq topic_name 由 bt_queue_name + shard_id 构成（shard_id = 0,1,2,...,shard_num)
    `shard_num_`: [int] 选配. btq 单个 topic 容量有限，有时需要分为多个 shard。默认 shard_num = 1
    `common_key_choice`: [string] 选配，仅支持 "request_id", "device_id", "user_id" 字符串中一个，默认"user_id"
    `as_extra_attrs`: [bool] 选配，是否将 item 的所有 `attrs` 特征值作为样本拼接的 extra_attrs 发送, 默认 false
    `user_extractors`: [list] 必配. 抽取 User 级别特征, 依次调用对应名字类的 extract() 函数, 需要在 mix_rank_feature_extractor.cc 进行注册, 参考 SlideMixRankUserExtractorDemo
    `item_extractors`: [list] 必配. 抽取 Item 级别特征, 依次调用对应名字类的 extract() 函数, 需要在 mix_rank_feature_extractor.cc 进行注册, 参考 SlideMixRankItemExtractorDemo
    `user_merge_cached_feature_names`: [list] 选配. 配置时，用户可以在任何位置自定义抽取 User 级别特征，并将 User 侧 PredictItem 和字符串关联起来.
                                      在这里进行配置的字符串, 会以此为 key 去取对应的之前抽取好的 PredictItem，并 merge 到最后的 CommonAttr, 参考 MockCachePredictItem
    `item_merge_cached_feature_names`: [list] 选配. 配置时，用户可以在任何位置抽取一组 Item 级别的特征，并将这组 Item 侧 PredictItem 和字符串关联，
                                      在这里进行配置的字符串, 会以此为 key 去取对应的之前抽取好一组 PredictItem，并再以最后结果的 PhotoId() 为 key 查对应的 PredictItem
                                      最后, merge 到发送的 ItemAttrs, 参考 MockCachePredictItem

    调用示例
    ------
    ``` python
    .hot_mix_rank_leaf_show(
      enable_send_sample=true,
      biz_name="slide_hot_mix",
      bt_queue_name="slide_hot_mix_attr",
      shard_num=16,
      common_key_choice="user_id"
      user_extractors=["SlideMixRankUserExtractorDemo"],
      item_extractors=["SlideMixRankItemExtractorDemo"],
      user_merge_cached_feature_names=["UserTest"],
      item_merge_cached_feature_names=["Test"]
    )
    ```
    """
    self._add_processor(HotMixRankLeafShowObserver(kwargs))
    return self

  def slide_commercial_default_mix_rank_by_code(self, **kwargs):
    """
    SlideCommercialDefaultMixRankByCodeProcessor
    ------
    重构自 getMixResultsByCode 相关逻辑

    参数配置
    ------

    调用示例
    ------
    ``` python
    .slide_commercial_default_mix_rank_by_code()
    ```
    """
    self._add_processor(SlideCommercialDefaultMixRankByCodeProcessor(kwargs))
    return self


  def nebula_mix_rank_list_process(self, **kwargs):
    """
    NebulaMixRankListProcessor
    ------
    极速版混排V1，根据策略进行listwise排序

    参数配置
    ------

    调用示例
    ------
    ``` python
    .nebula_mix_rank_list_process()
    ```
    """
    self._add_processor(NebulaMixRankListProcessor(kwargs))
    return self

  def nebula_listwise_rerank_processor(self, **kwargs):
    """
    NebulaListwiseRerankProcessor
    ------
    极速版混排V2，根据模型进行listwise排序

    参数配置
    ------

    调用示例
    ------
    ``` python
    .nebula_listwise_rerank_processor()
    ```
    """
    self._add_processor(NebulaListwiseRerankProcessor(kwargs))
    return self

  def nebula_reco_ensemble_sort_rerank(self, **kwargs):
    """
    NebulaRecoEnsembleSortEnricher
    ------
    极速版混排，根据ensemble score进行排序

    参数配置
    ------

    调用示例
    ------
    ``` python
    .nebula_reco_ensemble_sort_rerank()
    ```
    """
    self._add_processor(NebulaRecoEnsembleSortEnricher(kwargs))
    return self

  def pack_ad_info(self, **kwargs):
    """
    AdPackEnricher
    ------
    打包广告信息 & 发送计费、servershow 等数据流

    参数配置
    ------
    `kess_service`: [string] 必配. 下游 ad 打包服务的 kess service name
    `timeout_ms`: [int] 必配. 调用 grpc_adPackService 的超时时间设置

    调用示例
    ------
    ``` python
    .pack_ad_info(
      kess_service="grpc_adPackService",
      timeout_ms=5
    )

    ```
    """
    self._add_processor(AdPackEnricher(kwargs))
    return self

  def slide_last_judge(self, **kwargs):
    """
    SlideMixRankLastJudgeEnricher
    ------
    单列兜底 Processor, Server 强插, 位置检查，频率兜底控制

    参数配置
    ------

    调用示例
    ------
    ``` python
    .slide_last_judge()

    ```
    """
    self._add_processor(SlideMixRankLastJudgeEnricher(kwargs))
    return self

  def fetch_mix_history_from_redis(self, **kwargs):
    """
    MixRankFetchHistoryFromRedisEnricher
    ------
    读取历史 history_max_size 次混排结果做频控依据，或写入当前混排结果到 redis. 以 did 为 key

    参数配置
    ------
    `is_read_mode`: [bool] 必配. true 代表读取历史并保存到 context，false 表示写入当前的结果到 redis
    `cluster_name`: [string] 必配. redis 集群的名字
    `timeout_ms`: [int] 选配. 超时时间，默认为 10
    `history_max_size`: [int] [动态参数] redis 维护的历史结果最大长度, 默认为 10
    `history_max_seconds`: [int] [动态参数] redis 维护的过期时间, 单位为秒, 默认为 3600 s

    调用示例
    ------
    ``` python
    .fetch_mix_history_from_redis(
      is_read_mode=False,
      cluster_name="recoMixRankResultList",
      history_max_size=10,
      history_max_seconds=3600
    )

    ```
    """
    self._add_processor(MixRankFetchHistoryFromRedisEnricher(kwargs))
    return self

  def fetch_mix_subsidy_from_redis(self, **kwargs):
    """
    MixRankFetchSubsidyFromRedisEnricher
    ------
    读取用户补贴信息，“ad_” + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_mix_subsidy_from_redis(
       cluster_name="recoMixRankResultList",
    )
    ```
    """
    self._add_processor(MixRankFetchSubsidyFromRedisEnricher(kwargs))
    return self

  def fetch_sen_plugin_from_redis(self, **kwargs):
    """
    MixRankFetchSenPluginFromRedisEnricher
    ------
    读取敏感用户信息，业务 + product + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_sen_plugin_from_redis(
       cluster_name="mixUePortraitModel",
    )
    ```
    """
    self._add_processor(MixRankFetchSenPluginFromRedisEnricher(kwargs))
    return self

  def fetch_mix_sensitivity_from_redis(self, **kwargs):
    """
    MixRankFetchSensitivityFromRedisEnricher
    ------
    读取敏感用户信息，did + “_” + product 为 key

    调用示例
    ------
    ``` python
    .fetch_mix_sensitivity_from_redis(
       cluster_name="recoMixRankResultList",
    )
    ```
    """
    self._add_processor(MixRankFetchSensitivityFromRedisEnricher(kwargs))
    return self

  def fetch_big_r_mark_from_redis(self, **kwargs):
    """
    MixRankFetchBigRMarkFromRedisEnricher
    ------
    读取用户信息，“bigr_” + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_big_r_mark_from_redis(
       cluster_name="recoPushLiveAuthorCache",
    )
    ```
    """
    self._add_processor(MixRankFetchBigRMarkFromRedisEnricher(kwargs))
    return self

  def fetch_produce_hot_author_from_redis(self, **kwargs):
    """
    MixRankFetchProduceHotAuthorFromRedisEnricher
    ------
    读取用户信息，“produce_hot_” + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_produce_hot_author_from_redis(
       cluster_name="recoPromoteProduction",
    )
    ```
    """
    self._add_processor(MixRankFetchProduceHotAuthorFromRedisEnricher(kwargs))
    return self

  def fetch_merchant_sensitivity_from_redis(self, **kwargs):
    """
    MixRankFetchMerchantSensitivityEnricher
    ------
    读取电商敏感用户信息，"mersens__" + did + “_” + product 为 key

    调用示例
    ------
    ``` python
    .fetch_merchant_sensitivity_from_redis(
       cluster_name="recoMixRankResultList",
    )
    ```
    """
    self._add_processor(MixRankFetchMerchantSensitivityEnricher(kwargs))
    return self

  def slide_mix_universe_auction_processor(self, **kwargs):
    """
    SlideUniverseAuctionProcessor
    ------
    统一竞价

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_mix_universe_auction_processor()
    ```
    """
    self._add_processor(SlideUniverseAuctionProcessor(kwargs))
    return self

  def fetch_mix_roi_history_from_redis(self, **kwargs):
    """
    MixRankFetchRoiHistoryFromRedisEnricher
    ------
    读取用户历史预期花费信息，“ ur_” + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_mix_roi_history_from_redis(
       cluster_name="recoLongTermRewardCache",
    )
    ```
    """
    self._add_processor(MixRankFetchRoiHistoryFromRedisEnricher(kwargs))
    return self

  def fetch_mix_roi_history_v2_from_redis(self, **kwargs):
    """
    MixRankFetchRoiHistoryV2FromRedisEnricher
    ------
    读取用户历史预期花费信息，“ ui_” + uid 为 key

    调用示例
    ------
    ``` python
    .fetch_mix_roi_history_v2_from_redis(
       cluster_name="recoLongTermRewardCache",
    )
    ```
    """
    self._add_processor(MixRankFetchRoiHistoryV2FromRedisEnricher(kwargs))
    return self

  def fetch_mix_coeffect_data_from_redis(self, **kwargs):
      """
      MixRankFetchCoeffectFromRedisEnricher
      -----
      读取历史item共现信息， “ih_” + code为key
      调用示例
      ``` python
      .fetch_mix_coeffect_data_from_redis(
        cluster_name="recoMixRankIndustryData",
      )
      ```
      """
      self._add_processor(MixRankFetchCoeffectFromRedisEnricher(kwargs))
      return self

  def fetch_mix_live_revenue_from_redis(self, **kwargs):
    """
    MixRankFetchLiveRevenueFromRedisEnricher
    ------
    读取用户直播营收价值

    调用示例
    ------
    ``` python
    .fetch_mix_live_revenue_from_redis(
       cluster_name="dpUserDigAuthorInfo",
    )
    ```
    """
    self._add_processor(MixRankFetchLiveRevenueFromRedisEnricher(kwargs))
    return self

  def fetch_follow_live_mix_score_from_redis(self, **kwargs):
    """
    MixRankFetchFollowLiveMixScoreFromRedisEnricher
    ------
    读取已关leaf直播出价分校准map

    调用示例
    ------
    ``` python
    .fetch_follow_live_mix_score_from_redis(
       cluster_name="merchantVideoItemCf",
    )
    ```
    """
    self._add_processor(MixRankFetchFollowLiveMixScoreFromRedisEnricher(kwargs))
    return self

#===================================================== Slide Procssor Split Start =====================================================
  def slide_reco_photo_processor(self, **kwargs):
    """
    SlideRecoPhotoProcessor
    ------
    主站单列混排，base RecoPhoto 结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_reco_photo_processor()
    ```
    """
    self._add_processor(SlideRecoPhotoProcessor(kwargs))
    return self

  def slide_random_photo_processor(self, **kwargs):
    """
    SlideRandomPhotoProcessor
    ------
    主站单列混排，RandomPhoto 结果相关插入逻辑

    参数配置
    `random_insert_lower_bound`: [int] 选配. 随机插入 index 的下界，默认为 0, 意为允许随机插入到第 0 位
    ------

    调用示例
    ------
    ```python
    .slide_random_photo_processor(
     random_insert_lower_bound=1
    )
    ```
    """
    self._add_processor(SlideRandomPhotoProcessor(kwargs))
    return self

  def slide_local_life_photo_processor(self, **kwargs):
    """
    SlideLocalLifePhotoProcessor
    ------
    主站单列混排，本地生活结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_local_life_photo_processor()
    ```
    """
    self._add_processor(SlideLocalLifePhotoProcessor(kwargs))
    return self

  def slide_marketing_survey_processor(self, **kwargs):
    """
    SlideMarketingSurveyProcessor
    ------
    主站单列混排，对营销感短视频挂载问卷标识

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_marketing_survey_processor()
    ```
    """
    self._add_processor(SlideMarketingSurveyProcessor(kwargs))
    return self

  def slide_nr_explore_photo_processor(self, **kwargs):
    """
    SlideNrExplorePhotoProcessor
    ------
    主站单列混排，NrExplorePhoto 结果相关插入逻辑

    参数配置
    `random_insert_lower_bound`: [int] 选配. 随机插入 index 的下界，默认为 0, 意为允许随机插入到第 0 位
    ------

    调用示例
    ------
    ```python
    .slide_nr_explore_photo_processor(
     random_insert_lower_bound=1
    )
    ```
    """
    self._add_processor(SlideNrExplorePhotoProcessor(kwargs))
    return self

  def slide_interest_photo_processor(self, **kwargs):
    """
    SlideInterestPhotoProcessor
    ------
    主站单列混排，用户兴趣探索链路结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_interest_photo_processor(
     enable_mix_interest_photo_independent_insert=True
    )
    ```
    """
    self._add_processor(SlideInterestPhotoProcessor(kwargs))
    return self

  def slide_reco_user_card_processor(self, **kwargs):
    """
    SlideRecoUserCardProcessor
    ------
    主站单列混排，UserCard 结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_reco_user_card_processor()
    ```
    """
    self._add_processor(SlideRecoUserCardProcessor(kwargs))
    return self

  def slide_reco_photo_card_processor(self, **kwargs):
    """
    SlideRecoPhotoCardProcessor
    ------
    主站单列混排，PhotoCard 结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_reco_photo_card_processor()
    ```
    """
    self._add_processor(SlideRecoPhotoCardProcessor(kwargs))
    return self

  def slide_reco_material_card_processor(self, **kwargs):
    """
    SlideRecoMaterialCardProcessor
    ------
    主站单列混排，MaterialCard 结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_reco_material_card_processor()
    ```
    """
    self._add_processor(SlideRecoMaterialCardProcessor(kwargs))
    return self

  def slide_live_stream_processor(self, **kwargs):
    """
    SlideMixRankLiveStreamProcessor
    ------
    主站单列混排，直播结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_live_stream_processor()
    ```
    """
    self._add_processor(SlideLiveStreamProcessor(kwargs))
    return self

  def slide_merchant_card_processor(self, **kwargs):
    """
    SlideMerchantCardProcessor
    ------
    主站单列混排，电商卡片结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_merchant_card_processor()
    ```
    """
    self._add_processor(SlideMerchantCardProcessor(kwargs))
    return self

  def slide_fission_card_processor(self, **kwargs):
    """
    SlideFissionCardProcessor
    ------
    单列混排，增长裂变卡片结果相关插入逻辑

    参数配置
    ------

    调用示例
    ------
    ```python
    .slide_fission_card_processor()
    ```
    """
    self._add_processor(SlideFissionCardProcessor(kwargs))
    return self

  def nebula_mix_rank_sample(self, **kwargs):
    """
    NebulaMixRankSampleProcessor
    ------
    agent log 发送到 btq，用于样本拼接

    调用示例
    ------
    ``` python
    .nebula_mix_rank_sample()
    ```
    """
    self._add_processor(NebulaMixRankSampleProcessor(kwargs))
    return self

#===================================================== Slide Procssor Split End   =====================================================
  def hot_mix_rank_resort_by_rule_processor(self, **kwargs):
    """
    HotMixRankResortByRuleProcessor

    ------
    主站双列发现页混排人工调整结果

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankResortByRuleProcessor(kwargs))
    return self

  def fountain_mix_rank_resort_by_rule_processor(self, **kwargs):
    """
    FountainMixRankResortByRuleProcessor

    ------
    主站双列发现页混排人工调整结果

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .fountain_mix_rank_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(FountainMixRankResortByRuleProcessor(kwargs))
    return self

  def hot_mix_rank_reco_photo_processor(self, **kwargs):
    """
    HotMixRankRecoPhotoProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankRecoPhotoProcessor(kwargs))
    return self

  def hot_single_reco_photo_processor(self, **kwargs):
    """
    HotSingleRecoPhotoProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoProcessor(kwargs))
    return self

  def hot_single_random_reco_photo_processor(self, **kwargs):
    """
    HotSingleRandomRecoPhotoProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_random_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRandomRecoPhotoProcessor(kwargs))
    return self

  def hot_single_relation_reco_photo_processor(self, **kwargs):
    """
    HotSingleRelationRecoPhotoProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_relation_reco_photo_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRelationRecoPhotoProcessor(kwargs))
    return self

  def hot_single_reco_live_processor(self, **kwargs):
    """
    HotSingleRecoLiveProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_live_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoLiveProcessor(kwargs))
    return self

  def hot_single_reco_photo_gen_es_processor(self, **kwargs):
    """
    HotSingleRecoPhotoGenESProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_gen_es_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoGenESProcessor(kwargs))
    return self

  def hot_single_reco_photo_gen_model(self, **kwargs):
    """
    HotSingleRecoPhotoGenModel
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_gen_model(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoGenModel(kwargs))
    return self

  def hot_single_reco_photo_pre_processor(self, **kwargs):
    """
    HotSingleRecoPhotoPreProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_pre_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoPreProcessor(kwargs))
    return self

  def hot_single_reco_photo_post_processor(self, **kwargs):
    """
    HotSingleRecoPhotoPostProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_post_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoPostProcessor(kwargs))
    return self

  def fountain_mix_meta_log_observer(self, **kwargs):
    """
    FountainMixMetaLogObserver
    ------
    主站双列发现页内流混排落表

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .fountain_mix_meta_log_observer(**ab_parameters)
    ```
    """
    self._add_processor(FountainMixMetaLogObserver(kwargs))
    return self

  def fountain_mix_post_process(self, **kwargs):
    """
    FountainMixPostProcessor
    ------
    主站双列发现页内流混排后处理

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .fountain_mix_post_process(**ab_parameters)
    ```
    """
    self._add_processor(FountainMixPostProcessor(kwargs))
    return self

  def hot_single_reco_photo_rule_generator(self, **kwargs):
    """
    HotSingleRecoPhotoRuleGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_rule_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoRuleGenerator(kwargs))
    return self

  def hot_single_reco_photo_model_generator(self, **kwargs):
    """
    HotSingleRecoPhotoModelGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_model_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoModelGenerator(kwargs))
    return self

  def hot_single_reco_photo_async_model_generator(self, **kwargs):
    """
    HotSingleRecoPhotoAsyncModelGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_async_model_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoAsyncModelGenerator(kwargs))
    return self

  def hot_single_reco_photo_model_generator2(self, **kwargs):
    """
    HotSingleRecoPhotoModelGenerator2
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_model_generator2(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoModelGenerator2(kwargs))
    return self

  def hot_single_reco_photo_evaluator(self, **kwargs):
    """
    HotSingleRecoPhotoEvaluator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_evaluator(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoEvaluator(kwargs))
    return self

  def hot_live_seq_generator(self, **kwargs):
    """
    HotLiveSeqGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_live_seq_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotLiveSeqGenerator(kwargs))
    return self

  def hot_rela_seq_generator(self, **kwargs):
    """
    HotRelaSeqGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_rela_seq_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotRelaSeqGenerator(kwargs))
    return self

  def hot_multi_queue_generator(self, **kwargs):
    """
    HotMultiQueueGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_multi_queue_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotMultiQueueGenerator(kwargs))
    return self

  def hot_mix_live_score_enricher(self, **kwargs):
    """
    HotMixLiveScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_live_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixLiveScoreEnricher(kwargs))
    return self

  def hot_mix_merchant_live_score_enricher(self, **kwargs):
    """
    HotMixMerchantLiveScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_merchant_live_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixMerchantLiveScoreEnricher(kwargs))
    return self

  def hot_mix_relation_score_enricher(self, **kwargs):
    """
    HotMixRelationScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_relation_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRelationScoreEnricher(kwargs))
    return self

  def hot_mix_reco_photo_score_enricher(self, **kwargs):
    """
    HotMixRecoPhotoScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_relation_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRecoPhotoScoreEnricher(kwargs))
    return self

  def fountain_mix_reco_photo_score_enricher(self, **kwargs):
    """
    FountainMixRecoPhotoScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .fountain_mix_reco_photo_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(FountainMixRecoPhotoScoreEnricher(kwargs))
    return self

  def hot_mix_explore_ad_score_enricher(self, **kwargs):
    """
    HotMixExploreAdScoreEnricher
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_explore_ad_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixExploreAdScoreEnricher(kwargs))
    return self

  def hot_mix_multi_results_sort(self, **kwargs):
    """
    HotMixMultiResultsSort
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_relation_score_enricher(**ab_parameters)
    ```
    """
    self._add_processor(HotMixMultiResultsSort(kwargs))
    return self

  def hot_mix_auction_seq_evaluator(self, **kwargs):
    """
    HotMixAuctionSeqEvaluator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_auction_seq_evaluator(**ab_parameters)
    ```
    """
    self._add_processor(HotMixAuctionSeqEvaluator(kwargs))
    return self

  def hot_mix_auction_seq_retrieval(self, **kwargs):
    """
    HotMixAuctionSeqRetrieval
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_auction_seq_retrieval(**ab_parameters)
    ```
    """
    self._add_processor(HotMixAuctionSeqRetrieval(kwargs))
    return self

  def hot_single_reco_photo_bs_generator(self, **kwargs):
    """
    HotSingleRecoPhotoBSGenerator
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_single_reco_photo_bs_generator(**ab_parameters)
    ```
    """
    self._add_processor(HotSingleRecoPhotoBSGenerator(kwargs))
    return self

  def hot_mix_rank_ensemble_sort_processor(self, **kwargs):
    """
    HotMixRankEnsembleSortProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_ensemble_sort_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankEnsembleSortProcessor(kwargs))
    return self

  def hot_explore_live_resort_processor(self, **kwargs):
    """
    HotExploreLiveResortProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_explore_live_resort_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotExploreLiveResortProcessor(kwargs))
    return self

  def hot_mix_rank_model_rank(self, **kwargs):
    """
    HotMixRankModelRank
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_model_rank(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankModelRank(kwargs))
    return self

  def hot_mix_rank_multi_queue_generator_processor(self, **kwargs):
    """
    HotMixRankMultiQueueGeneratorProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_multi_queue_generator_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankMultiQueueGeneratorProcessor(kwargs))
    return self

  def hot_mix_rank_post_inject_processor(self, **kwargs):
    """
    HotMixRankPostInjectProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_post_inject_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankPostInjectProcessor(kwargs))
    return self

  def hot_mix_rank_add_attr_to_kafka_processor(self, **kwargs):
    """
    HotMixRankAddAttrToKafkaProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_add_attr_to_kafka_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankAddAttrToKafkaProcessor(kwargs))
    return self

  def hot_mix_rank_send_abtest_metrics_processor(self, **kwargs):
    """
    HotMixRankSendAbtestMetricsProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `abtest_prefix`: [string],ab指标前缀

    调用示例
    ------
    ```python
    .hot_mix_rank_send_abtest_metrics_processor(
      abtest_prefix = "default",
    )
    ```
    """
    self._add_processor(HotMixRankSendAbtestMetricsProcessor(kwargs))
    return self

  def hot_mix_rank_write_info_to_redis_processor(self, **kwargs):
    """
    HotMixRankWriteInfoToRedisProcessor
    ------
    主站双列发现页混排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_mix_rank_write_info_to_redis_processor(**ab_parameters)
    ```
    """
    self._add_processor(HotMixRankWriteInfoToRedisProcessor(kwargs))
    return self

  def mix_rank_update_ad_price(self, **kwargs):
    """
    MixRankUpdateAdPriceEnricher
    ------
    精选页和极速版根据曝光位置对应的 sctr 修改计费逻辑

    调用示例
    ------
    ``` python
    .mix_rank_update_ad_price()
    ```
    """
    self._add_processor(MixRankUpdateAdPriceEnricher(kwargs))
    return self

  def mix_rank_post_update_ad_price(self, **kwargs):
    """
    MixRankPostUpdateAdPriceEnricher
    ------
    精选页和极速版根据曝光位置对应的 sctr 修改重构计费逻辑

    调用示例
    ------
    ``` python
    .mix_rank_post_update_ad_price()
    ```
    """
    self._add_processor(MixRankPostUpdateAdPriceEnricher(kwargs))
    return self

  def mix_rank_write_ad_sctr_enricher(self, **kwargs):
    """
    MixRankWriteAdSctrEnricher
    ------
    精选页和极速版预估的曝光率 sctr 写入 redis

    调用示例
    ------
    ``` python
    .mix_rank_write_ad_sctr_enricher()
    ```
    """
    self._add_processor(MixRankWriteAdSctrEnricher(kwargs))
    return self

  def write_user_ad_value_to_redis_enricher(self, **kwargs):
    """
    WriteUserAdValueToRedisEnricher
    ------
    精选页和极速版用户历史商业化消费价值写入 redis

    调用示例
    ------
    ``` python
    .write_user_ad_value_to_redis_enricher()
    ```
    """
    self._add_processor(WriteUserAdValueToRedisEnricher(kwargs))
    return self

  def write_batch_ad_value_for_reco_rl_enricher(self, **kwargs):
    """
    WriteBatchAdValueForRecoRLEnricher
    ------
    精选页和极速版用户当前刷商业化价值写入 redis

    调用示例
    ------
    ``` python
    .write_batch_ad_value_for_reco_rl_enricher()
    ```
    """
    self._add_processor(WriteBatchAdValueForRecoRLEnricher(kwargs))
    return self

  def mix_item_show_count(self, **kwargs):
    """
    MerchantRecoItemShowCountEnricher
    ------
    精选页和极速版电商pid controller的计数器

    调用示例
    ------
    ``` python
    .merchant_item_show_count_enricher()
    ```
    """
    self._add_processor(MixItemShowCountEnricher(kwargs))
    return self

  def fetch_user_commercial_value_from_redis(self, **kwargs):
    """
    MixRankFetchUserCommercialValueFromRedisEnricher
    ------
    读取用户商业化累积价值信息，“ ur_” + uid 为 key
    调用示例
    ------
    ``` python
    .fetch_user_commercial_value_from_redis(
       cluster_name="recoSidebarPhotoScore",
    )
    ```
    """
    self._add_processor(MixRankFetchUserCommercialValueFromRedisEnricher(kwargs))
    return self

  def fetch_mix_ad_item_attr_from_redis(self, **kwargs):
    """
    MixRankFetchAdItemAttrFromRedisEnricher
    ------
    极速版读取广告索引 redis 为抽取特征填充模型请求
    调用示例
    ------
    ``` python
    .fetch_mix_ad_item_attr_from_redis(
       cluster_name="adDspItemIndex",
    )
    ```
    """
    self._add_processor(MixRankFetchAdItemAttrFromRedisEnricher(kwargs))
    return self

  def fountain_mix_rank_pre_process(self, **kwargs):
    """
    FountainMixRankPreProcessRetriever
    ------
    双列内流混排预处理逻辑
    调用示例
    ------
    ``` python
    .fountain_mix_rank_pre_process()
    ```
    """
    self._add_processor(FountainMixRankPreProcessRetriever(kwargs))
    return self

  def fountain_mix_rank_by_rule(self, **kwargs):
    """
    FountainMixRankByRuleProcessor
    ------
    双列内流混排 base 处理逻辑

    调用示例
    ------
    ``` python
    .fountain_mix_rank_by_rule()
    ```
    """
    self._add_processor(FountainMixRankByRuleProcessor(kwargs))
    return self

  def fountain_mix_rank_post_inject_process(self, **kwargs):
    """
    FountainMixRankPostInjectProcessor
    ------
    双列内流混排 base 处理逻辑

    调用示例
    ------
    ``` python
    .fountain_mix_rank_post_inject_process()
    ```
    """
    self._add_processor(FountainMixRankPostInjectProcessor(kwargs))
    return self

  def fountain_mix_list_rerank(self, **kwargs):
    """
    FountainListRerankProcessor
    ------
    精选版混排，根据策略进行排序(list rerank 版本)

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_list_rerank()
    ```
    """
    self._add_processor(FountainListRerankProcessor(kwargs))
    return self

  def fountain_mix_ad_score_enricher(self, **kwargs):
    """
    FountainMixAdScoreEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_ad_score_enricher()
    ```
    """
    self._add_processor(FountainMixAdScoreEnricher(kwargs))
    return self

  def fountain_mix_live_score_enricher(self, **kwargs):
    """
    FountainMixLiveScoreEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_live_score_enricher()
    ```
    """
    self._add_processor(FountainMixLiveScoreEnricher(kwargs))
    return self

  def fountain_mix_merchant_live_score_enricher(self, **kwargs):
    """
    FountainMixLiveScoreEnricher
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_merchant_live_score_enricher()
    ```
    """
    self._add_processor(FountainMixMerchantLiveScoreEnricher(kwargs))
    return self

  def fountain_mix_auction_seq_retrieval(self, **kwargs):
    """
    FountainMixAuctionSeqRetrieval
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_auction_seq_retrieval()
    ```
    """
    self._add_processor(FountainMixAuctionSeqRetrieval(kwargs))
    return self

  def fountain_mix_auction_seq_evaluator(self, **kwargs):
    """
    FountainMixAuctionSeqEvaluator
    ------

    参数配置
    ------

    调用示例
    ------
    ``` python
    .fountain_mix_auction_seq_evaluator()
    ```
    """
    self._add_processor(FountainMixAuctionSeqEvaluator(kwargs))
    return self

  def mix_rank_ad_auction_processor(self, **kwargs):
    """
    MixRankAdAuctionEnricher
    ------
    精选页和极速版根据计算的分数修改广告计费的逻辑

    调用示例
    ------
    ``` python
    .mix_rank_ad_auction_processor()
    ```
    """
    self._add_processor(MixRankAdAuctionEnricher(kwargs))
    return self

  def mix_rank_fetch_load_redis_key(self, **kwargs):
    """
    MixRankFetchLoadRedisKeyEnricher
    ------
    获取 redis 拼接需要的 key

    调用示例
    ------
    ``` python
    .mix_rank_fetch_load_redis_key()
    ```
    """
    self._add_processor(MixRankFetchLoadRedisKeyEnricher(kwargs))
    return self

  def slide_mix_memory_data_processor(self, **kwargs):
    """
    SlideMixGlobalDataProcessor
    -----
    单列混排 memory data

    调用示例
    -----
    ```python
      .slide_mix_memory_data_processor(
        data_key="realtime_frac_redis_nebula",
        data_type="string_double_vector_map",
        saved_ptr_name_="realtime_frac_memory_data"
      )
    ```
    """
    self._add_processor(SlideMixMemoryDataProcessor(kwargs))
    return self

  def get_abtest_params_parallel(self, **kwargs):
        """
    ParallelAbtestCommonAttrEnricher
    ------
    从 abtest 系统中获得参数值并作为 CommonAttr 填入 Context 中（千分世界和百分世界均可）

    并行执行，get_abtest_param_thread_num设置线程池大小，默认32

    参数配置
    ------
    `biz_name`: [string] [动态参数] ab 参数所属业务，即 abtest 网站上全大写字母的“所属业务”（ab1.0）或“关联 App 组”（ab2.0）名称

    `ab_params`: [list]
      - `param_name`: [string|dict] ab 参数的名称
      - `default_value`: [int|double|string|bool] ab 参数获取失败时取用的默认值
      - `param_type`: [string] 可缺省，ab 参数的类型，缺省将由 `default_value` 类型自动推断，可选值："int", "double", "string", "bool"（bool 将被转换为 0 / 1 int 值存储到 int CommonAttr 中）
      - `attr_name`: [string] 可缺省，要写入的 CommonAttr 名称，若缺省或为空将直接取 `param_name` 的值
      - `report_ab_hit`: [bool] [动态参数] 可缺省，是否将 ab 命中结果做上报，默认为 False 不上报

    `prioritized_suffix`: [list] [动态参数] 可缺省，为所有 `param_name` 定义一组优先尝试的参数名规则。
      - 若 `param_name` 配置为 string 类型将使用后缀拼接模式生成参数名，例如 ["_A", "_B"] 将优先使用 `param_name + "_A"` 参数值，次优使用 `param_name + "_B"` 参数值，最后兜底使用 `param_name` 参数值
      - 若 `param_name` 配置为 dict<str, str> 类型将使用 map 映射查询模式生成参数名，例如 ["C", "D"] 将优先使用 `param_name["C"]` 的参数名取值，次优使用 `param_name["D"]` 的参数名取值，若查询不到将直接取默认值

    `user_id`: [int] [动态参数] 选配项，指定获取 ab 参数时使用的 user_id ，缺省时，使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，指定获取 ab 参数时使用的 device_id ，缺省时，使用当前请求的 device_id

    `session_id`: [string] [动态参数] 选配项，ab 平台用于分流的 sid 值，原始含义是 session id，ab 平台的后续规划是用于自定义扩展分流 id，默认为 ""

    `product`: [string] [动态参数] 选配项，对于配置了 product 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 product ，例如 "KUAISHOU" 或 "ACFUN"

    `platform`: [string] [动态参数] 选配项，对于配置了 platform 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 platform ，例如 "ANDROID_PHONE" 或 "IPHONE"

    `app_version`: [string] [动态参数] 选配项，对于配置了 app_version 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 app_version

    `concurrency`: [int] 并发度，即每次访问分多少个batch

    调用示例
    ------
    ``` python
    # 完整格式
    .get_abtest_params_parallel(
      concurrency = 4,
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      ab_params = [{
        "param_name": "mmu_cover_erotic_prob_threshold",
        "param_type": "double",
        "default_value": 0.0
      }]
    )
    # 简写格式
    .get_abtest_params_parallel(
      concurrency = 4,
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      # 简写格式下每个 ab_param 为二元组 (param_name, default_value) 或三元组 (param_name, default_value, attr_name)
      ab_params = [
        ("mmu_cover_erotic_prob_threshold", 0.0),
        ("use_new_predict", False),
      ]
    )
    ```
    """
        keys = ("param_name", "default_value", "attr_name", "param_type")
        func = lambda x: dict(zip(keys, x)) if isinstance(x, tuple) else x
        kwargs["ab_params"] = list(map(func, kwargs["ab_params"]))
        self._add_processor(ParallelAbtestCommonAttrEnricher(kwargs))
        return self

  def mix_rank_enrich_abtest_user_tag(self, **kwargs):
    """
    MixRankAbtestTagEnricher
    ------
    设置 abtest sdk 可能用到的一些用户标志
    详细背景信息见文档：
    https://docs.corp.kuaishou.com/d/home/<USER>
    https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------
    `mix_rank_req_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::MixRankRequest 的 common_attr

    调用示例
    ------
    ``` python
    .mix_rank_enrich_abtest_user_tag(
      mix_rank_req_attr="mix_rank_request"
    )
    ```
    """
    self._add_processor(MixRankAbtestTagEnricher(kwargs))
    return self
