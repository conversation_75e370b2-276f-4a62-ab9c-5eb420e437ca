#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class PerRandomRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_per_random"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["user_info_attr"])
    attrs.add(self._config["per_random_request_num"])
    for name in ["service_shard", "timeout_ms"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class AuthorGrowthRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_author_growth"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["user_info_attr"])
    attrs.add(self._config["request_num"])
    for name in ["service_shard", "timeout_ms"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True