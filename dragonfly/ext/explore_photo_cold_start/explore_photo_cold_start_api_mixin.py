#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .explore_photo_cold_start_retriever import *

class ExplorePhotoColdStartApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 ExplorePhotoColdStartApiMixin 相关的 Processor 接口
  - PerRandomRemoteRetriever
  """

  def retrieve_by_per_random(self, **kwargs):
    """
    PerRandomRemoteRetriever
    ------
    从 RecoPersonalizedRandom 服务召回个性化随机视频

    参数配置
    ------
    `kess_service`: [string] [动态参数] RecoPersonalizedRandom 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `reason`: [int] 召回原因

    `per_random_request_num`: [string] 从 RecoPersonalizedRandom 召回个数, 通过 Request 获取

    `service_shard`: [string] kess 服务的 shard，默认值是 s0

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `user_info_attr`: [string] 通过 Request 获取 user info

    调用示例
    ------
    ``` python
    .retrieve_by_per_random(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      service_shard = "s0",
      service_group = "PRODUCTION",
      reason = 999,
      per_random_request_num = "randomCount",
      user_info_attr = "userInfo",
    )
    ```
    """
    self._add_processor(PerRandomRemoteRetriever(kwargs))
    return self

  def retrieve_by_author_growth(self, **kwargs):
    """
    AuthorGrowthRemoteRetriever
    ------
    从 AuthorGrowth 服务召回优质作者视频

    参数配置
    ------
    `kess_service`: [string] [动态参数] AuthorGrowthRemoteRetriever 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `reason`: [int] 召回原因

    `request_num`: [string] 从 AuthorGrowthRemoteRetriever 召回个数, 通过 Request 获取

    `service_shard`: [string] kess 服务的 shard，默认值是 s0

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `user_info_attr`: [string] 通过 Request 获取 user info

    调用示例
    ------
    ``` python
    .retrieve_by_author_growth(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      service_shard = "s0",
      service_group = "PRODUCTION",
      reason = 999,
      request_num = "randomCount",
      user_info_attr = "userInfo",
    )
    ```
    """
    self._add_processor(AuthorGrowthRemoteRetriever(kwargs))
    return self