#!/usr/bin/env python3
# coding=utf-8
"""
filename: eyeshot_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kap
author: lih<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-11-25 00:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class EyeshotPersonalizedHetuVariantArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "eyeshot_personalized_hetu_variant"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["debug","size_limit", "topk_num", "max_candidate_pos", "hetu_tag_attr_name",
                    "skip_null_uid", "uid_attr", "user_hetu_interest_idx_attr", "user_hetu_interest_prob_attr"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["hetu_tag"]:
            attrs.add(name)
        for name in ["hetu_tag_attr_name"]:
            attrs.add(self._config.get(name))
        return attrs

class EyeshotAttrTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_truncate_by_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for queue in self._config.get("queues"):
        attrs.add(queue.get("attr_name"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["sorted", "truncate_neg_item"]:
      if self._config.get(name):
        attrs.add(name)
    for name in ["size_limit", "allow_overflow"]:
        if name in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))

    for queue in self._config.get("queues", []):
        attrs.update(self.extract_dynamic_params(queue.get("attr_value_list")))
        attrs.update(self.extract_dynamic_params(queue.get("ratio")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_cluster_id_to_attr"]:
      if self._config.get(key, ""):
        attrs.add(self._config.get(key, ""))
    return attrs

