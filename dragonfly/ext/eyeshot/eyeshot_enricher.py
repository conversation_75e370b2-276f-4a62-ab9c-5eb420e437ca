#!/usr/bin/env python3
# coding=utf-8
"""
filename: eyeshot_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kap
author: li<PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-11-19 00:00:00
"""


from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel, check_arg
from ...common_leaf_processor import LeafEnricher

class GsuWithSameAuthorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_same_author"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "colossus_result_as_list" in self._config and self._config["colossus_result_as_list"]:
      if "colossus_result_list_attr" not in self._config:
        raise ArgumentError("colossus_result_as_list 为 True 时，必须提供非空的 colossus_result_list_attr 属性")
      ret.add(gen_attr_name_with_common_attr_channel(self._config["colossus_result_list_attr"], self._config["colossus_pid_attr"]))
      return ret
    for key in ["author_id_attr", "tag_attr", "play_time_attr",
                "duration_attr", "timestamp_attr", "channel_attr", "label_attr"]:
      if key in self._config:
        ret.add(gen_attr_name_with_common_attr_channel(self._config[key], self._config["colossus_pid_attr"]))
    for key in ["item_author_id_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class EyeshotRealtimeTagProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_realtime_tag_profile"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["user_info_attr_name"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_tag_list_attr_name", "output_tag_xtr_list_attr_name", "output_tag_xtr_list_score_name"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class EyeshotUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_user_info_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_info_attr_name") or "user_info")
    ret.add(self._config.get("window_size_hour_attr"))
    ret.add(self._config.get("non_effview_playtime_thresh_attr"))
    ret.add(self._config.get("non_effview_times_thresh_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("pic_non_effview_hetul1_list"))
    ret.add(self._config.get("pic_non_effview_hetul2_list"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret


class EyeshotTailNumberCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_tail_number_common_attr_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["tail_number_kconf_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for name in ["user_id"]:
        if name in self._config:
            ret.update(self.extract_dynamic_params(self._config.get(name)))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["save_output_to"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class EyeshotAbtestGroupIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_abtest_group_id_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["world_name", "exp_name"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["save_output_to"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class EyeshotColossusV2AuthorTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_colossus_v2_author_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    for conf in ["colossus_label_weight_attr","colossus_channel_select_attr",
                  "colossus_time_s_min_max_attr", "colossus_duration_s_min_max_attr",
                "is_select_picture", "trigger_num", "min_trigger_weight", "is_debug"]:
      if conf in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(conf)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_colossus_trigger_id_attr"))
    attrs.add(self._config.get("output_colossus_trigger_weight_attr"))
    return attrs

class EyeshotColossusV2PhotoTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_colossus_v2_photo_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    for conf in ["trigger_id_attr", "trigger_ts_attr", "trigger_label_attr",
                "is_select_picture", "trigger_num", "is_debug", "begin_time_ms", "end_time_ms", "shift_right_num"]:
      if conf in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(conf)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("trigger_pid_attr"))
    attrs.add(self._config.get("trigger_aid_attr"))
    attrs.add(self._config.get("trigger_tag_attr"))
    attrs.add(self._config.get("trigger_ts_attr"))
    attrs.add(self._config.get("trigger_label_attr"))
    attrs.add(self._config.get("trigger_duration_attr"))
    attrs.add(self._config.get("trigger_channel_attr"))
    attrs.add(self._config.get("trigger_playtime_attr"))
    attrs.add(self._config.get("trigger_size_attr"))
    return attrs

class EyeshotMioUpdateMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_generate_update_message"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for input_config in self._config["inputs"]:
      source_type = input_config.get("sign_source_type", "item_attr")
      if source_type == "item_attr":
        ret.add(input_config["embedding_attr"])
        ret.add(input_config["sign_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
      elif source_type == "item_id":
        ret.add(input_config["embedding_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for input_config in self._config["inputs"]:
      source_type = input_config.get("sign_source_type", "item_attr")
      if source_type == "common_attr":
        ret.add(input_config["embedding_attr"])
        ret.add(input_config["sign_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
      elif source_type in ["user_id", "device_id", "user_id_or_device_id"]:
        ret.add(input_config["embedding_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config["save_result_to_common_attr"] + str(shard) for shard in range(self._config["shards"]))

class EyeshotRecoRemoteEmbeddingAttrRawLiteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_get_remote_embedding_lite_v2"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for conf in self._config.get("inputs", []):
      if conf.get("query_source_type", "item_key") in ["user_id", "device_id", "common_attr"]:
        attrs.add(conf["output_attr_name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for conf in self._config.get("inputs", []):
      if conf.get("query_source_type", "item_key") in ["item_key", "item_id", "item_attr"]:
        attrs.add(conf["output_attr_name"])
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    for conf in self._config.get("inputs", []):
      if conf.get("query_source_type", "item_key") in ["common_attr"]:
        attrs.add(conf.get("input_attr_name"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for conf in self._config.get("inputs", []):
      if conf.get("query_source_type", "item_key") in ["common_attr"]:
        attrs.add(conf.get("input_attr_name"))
    return attrs

class EyeshotDiversityUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_diversity_update_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_info_ptr_attr", "prev_items_from_attr"]:
      attrs.add(self._config.get(name))
    for name in ["embedding_format", "embedding_service_name",
                  "dpp_diversity_shard_num", "embedding_slot_id", "enable_mgs_his_cnt_less_topk",
                  "embedding_sign_format", "embedding_timeout_ms", "dpp_diversity_mgs_topk", "enable_dpp_diversity_mgs_ratio",
                  "dpp_diversity_mgs_ratio", "enable_his_div_score_mgs", "longview_items_from_attr", "dpp_diversity_longview_topk",
                  "enable_his_sim_score"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr", ""))
    attrs.add(self._config.get("export_longview_score_attr", ""))
    return attrs

class EyeshotPicFeasuryHistoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_pic_feasury_history_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["pic_real_show_list_attr", "pic_click_list_attr", "pic_like_list_attr",
                 "pic_follow_list_attr", "pic_collect_list_attr", "pic_long_view_list_attr"]:
      attrs.add(self._config.get(name))
    for name in ["pic_history_max_num"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_pic_history_list_attr"))
    return attrs

class EyeshotEmbeddingDistanceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_embedding_distance_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["src_emb_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_emb_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_similarity_attr"))
    return attrs

class EyeshotEmbJointEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_emb_joint_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["src_emb_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class EyeshotItemAttrStatCacheEnricher(LeafEnricher):
    name_space_cache_size_map = {}
    @strict_types
    def _check_config(self) -> None:
        # check namespace
        check_arg("namespace" in self._config,
                  f"{self.get_type_alias()} 缺少 namespace")
        namespace = self._config.get("namespace")
        # check cache_size
        if "cache_size" in self._config:
            check_arg(namespace not in self.name_space_cache_size_map or self.name_space_cache_size_map[
                      namespace] == self._config["cache_size"], f"{self.get_type_alias()} 在 namespace {namespace} 配置了冲突的 cache_size")
            self.name_space_cache_size_map[namespace] = self._config["cache_size"]
            check_arg(self.name_space_cache_size_map[namespace] > 0,
                      f"{self.get_type_alias()} 配置的 cache_size {self.name_space_cache_size_map[namespace]} <= 0")
        elif self._config.get("mode", "") != "read":
            check_arg(namespace in self.name_space_cache_size_map,
                      f"{self.get_type_alias()} 缺少 cache_size")
        # check update_param
        if self._config.get("mode", "") != "read":
            update_param = self._config.get("update_param", [])
            check_arg(isinstance(update_param, list) and len(update_param) > 0,
                      f"{self.get_type_alias()} 缺少 update_param，或 update_param 不为 list")
            for _update_param in update_param:
                check_arg("update_method" in _update_param,
                          f"{self.get_type_alias()} update_param 缺少 update_method 配置")
                check_arg(_update_param["update_method"] in {
                          "replace", "max", "min", "moving_avg"}, f"{self.get_type_alias()} update_method 可选值为 replace/max/min/moving_avg，当前为 {_update_param['update_method']}")

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "eyeshot_item_attr_stat_cache_enricher"
    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      for attr in self._config.get("item_value_attrs"):
        attrs.add(self._config.get(attr))
      attrs.add(self._config.get("item_key_attr"))

      for attr in self._config.get("item_value_attrs"):
          attrs.add(self._config.get(attr))
      return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        if "group_prefix" in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get("group_prefix")))
        if "mode" in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get("mode", "")))
        if self._config.get("mode", "") != "read":
            attrs.update(self._config.get("common_attr", []))
        return attrs
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        if 'output_item_attrs' in self._config:
          for attr in self._config.get('output_item_attrs'):
            attrs.add(self._config.get(attr, ""))
        else:
          for attr in self._config.get('item_value_attrs'):
            attrs.add(self._config[attr])
        return attrs

