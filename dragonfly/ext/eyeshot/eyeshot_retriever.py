#!/usr/bin/env python3
# coding=utf-8
"""
filename: eyeshot_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kap
author: lih<PERSON>cha<PERSON>@kuaishou.com
date: 2021-11-25 00:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class CommonRecoEyeshotRedisIdPoolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_id_by_eyeshot_redis"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    retrieval_type = self._config.get("retrieval_type", -1)
    if retrieval_type == 0:
      ret.add("_USER_ID_")
    elif retrieval_type == 1:
      if "output_attr" in self._config:
        ret.add(self._config.get("output_attr"))
    return ret

class EyeshotColossusRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "eyeshot_colossus_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["colossus_resp_attr"]}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set()
    return set(self._config["item_fields"].values())

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set(self._config["item_fields"].values())
    return set()




