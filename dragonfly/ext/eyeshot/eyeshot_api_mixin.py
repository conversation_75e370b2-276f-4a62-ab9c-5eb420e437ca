#!/usr/bin/env python3
# coding=utf-8
"""
filename: eyeshot_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kkd api mixin
author: lih<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-11-25 00:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .eyeshot_retriever import *
from .eyeshot_enricher import *
from .eyeshot_arranger import *

class EyeshotApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 eyeshot 相关的 Processor 接口
  """

  def retrieve_id_by_eyeshot_redis(self, **kwargs):
    """
    CommonRecoEyeshotRedisIdPoolRetriever
    ------

    在 CommonRecoRedisIdPoolRetriever功能基础上添加retrieval_type=3的召回方式，每隔固定时间从redis加载一次全量id，
    每次retrieve从中随机选 retrieval_num 个

    参数配置
    ------
    `retrieval_type` : [int] 遍历方式，0 : 获取一个 id 并设置为 context 的 user id (实际是 enricher)
                                       1 : 获取一个 id 并设置为IntCommonAttr, 字段名为 output_attr 指定 (实际是 enricher)
                                       2 : 获取多个 id 多为 photo retrieval 的结果列表 (retriever)
                                       3 : 每隔固定时间从redis加载一次全量id，每次retrieve从中随机选 retrieval_num 个

    `retrieval_num` : [int] 默认的 retrieval_type 时，该值指定要获取的 id 数量

    `retrieval_reason` : [int] 默认的 retrieval_type 时，该值指定 item reason

    `output_attr` : [string] retrieval_type=1 时，该值指定 id 存 common attr 的字段名

    `redis_key_prefix` : [string] 需要遍历的 redis key 前缀

    `redis_key_num` : [int] 需要遍历的 redis key 数量

    `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

    `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

    `redis_cluster` : [int] 指定 redis 集群名

    `redis_timeout_ms` : [int] 访问 redis 的超时阈值 ，默认 200 ms

    `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

    `redis_biz_name` : [int] 访问 redis 集群所属业务，可以不填写, 默认为空

    `redis_replica` : [int] redis 集群是否开启双集群同步，一般无需修改，默认为 true

    `update_cycel_sec` : [int]  每隔 `update_cycel_sec` 重新加载redis

    调用示例
    ------
    ``` python
    .retrieve_id_by_eyeshot_redis(
      retrieval_type = 0,
      redis_key_prefix="user_ids",
      redis_key_num=50,
      redis_value_get_type=0,
      redis_value_parse_type=0,
      redis_cluster="recoNewUserOffline",
      redis_timeout_ms=1000)
      ```
      """
    self._add_processor(CommonRecoEyeshotRedisIdPoolRetriever(kwargs))
    return self

  def gsu_with_same_author(self, **kwargs):
    """
    GsuWithSameAuthorEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的tag attr name

    `play_time_attr` : [string] pid对应的play time attr name

    `duration_attr` : [string] pid对应的duration attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `item_author_id_attr` : [string] target对应的 author id attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    示例
    ------
    ``` python
    .gsu_with_same_author(colossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    item_author_id_attr="item_author_id_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithSameAuthorEnricher(kwargs))
    return self


  def eyeshot_personalized_hetu_variant(self, **kwargs):
    """
    EyeshotPersonalizedHetuVariantArranger
    ------

    eyeshot个性化河图打散策略

    参数配置
    ------
    `debug` : [bool] 是否开启debug模式，默认false

    `size_limit` : [int]

    `topk_num` : [int] [动态参数]

    `max_candidate_pos` : [int] [动态参数]

    `hetu_tag_attr_name` : [string] [动态参数]

    `skip_null_uid` : [bool] [动态参数]

    `uid_attr` : [string][动态参数]

    `user_hetu_interest_idx_attr` : [int_list] [动态参数] 期望河图1的index list

    `user_hetu_interest_prob_attr` : [double_list] [动态参数] 期望河图1的prob list

    调用示例
    ------
    ``` python
    .eyeshot_personalized_hetu_variant(
      size_limit = 650,
      topk_num=200,
      hetu_tag_attr_name="hetu_tag",
      skip_null_uid=True,
      uid_attr="featureUId",
      user_hetu_interest_idx_attr="user_hetu_interest_idx",
      user_hetu_interest_idx_attr="user_hetu_interest_prob")
    ```
    """
    self._add_processor(EyeshotPersonalizedHetuVariantArranger(kwargs))
    return self


  def eyeshot_realtime_tag_profile(self, **kwargs):
    """
    EyeshotRealtimeTagProfileEnricher
    ------


    参数配置
    ------


    调用示例
    ------

      """
    self._add_processor(EyeshotRealtimeTagProfileEnricher(kwargs))
    return self
  
  def eyeshot_user_info_enricher(self, **kwargs):
    """
    EyeshotUserInfoEnricher
    ------


    参数配置
    ------


    调用示例
    ------

      """
    self._add_processor(EyeshotUserInfoEnricher(kwargs))
    return self


  def eyeshot_truncate_by_attr(self, **kwargs):
    """
    EyeshotAttrTruncateArranger
    ------

    功能参考了MerchantRecoBucketTruncateArranger, 代码实现参考CommonRecoTruncateArranger
    整体上实现了分桶、截断、排序；
      1 分桶，按priority从大到小，以attr_name是否命中attr_value_list进行分桶；
      2 桶内截断，桶内按ratio进行截断，可以实现分桶保量或分桶打压
      3 按priority排序，未命中分桶视频priority=0， 被截断的视频priority=-1，整体按priority进行稳定排序;

    参数配置
    ------
    `sorted` : [bool] 是否按priority排序, 默认True

    `queues`: [list] 每个桶的保量信息
      - `priority`: [int] 分桶优先级，值越大优先级越高，需 大于 0
      - `attr_name`: [string] 该桶的 itemAttr name，支持int或int_list
      - `attr_value_list`: [int_list] [动态参数] 该桶需要匹配itemAttr的int_list
      - `ratio`: [double] [动态参数] 该桶截断比例，大于1时全保量、小于0是全扔掉
      - `truncate_neg_item`: [truncate_neg_item] [动态参数] 是否打压被截断的item, 默认false


    调用示例
    ------
    ``` python
    .eyeshot_truncate_by_attr(
        size_limit = "{{size_limit}}",
      queues = [
          dict(
              attr_name = "hetu_level2_tags",
              priority = 2,
              attr_value_list = "{{short_term_hetu_level2_list}}",
              ratio = "{{short_term_hetu_level2_cut_ratio}}",
              ),
          dict(
              attr_name = "hetu_level1_tags",
              priority = 1,
              attr_value_list = "{{long_term_hetu_level1_list}}",
              ratio = "{{long_term_hetu_level1_list_cnt_ratio}}",
              )
      ]
    )
    ```
    """
    self._add_processor(EyeshotAttrTruncateArranger(kwargs))
    return self

  def eyeshot_tail_number_common_attr_enrich(self, **kwargs):
    """
    EyeshotTailNumberCommonAttrEnricher
    ------

    ** 建议使用 common 模块下的 check_tail_number() **

    一个用于读取tail number kconf的enricher;

    参数配置
    ------
    `tail_number_kconf_attr` : [string] 非空字符串
    `save_output_to` : [string] 默认为 tail_number_output，1为满足，0为不满足
    `user_id` : [int] [动态参数] 用于判断tail number的user_id, 为空是默认从context里读user_id

    调用示例
    ------
    ``` python
    .eyeshot_tail_number_common_attr_enrich(
        tail_number_kconf_attr = 'reco.eyeshot.eyeshotDryrun',
        save_output_to = 'is_sampled_user',
        user_id = '{{user_id}}'
    )
    ```
    """
    self._add_processor(EyeshotTailNumberCommonAttrEnricher(kwargs))
    return self

  def eyeshot_abtest_group_id_enrich(self, **kwargs):
    """
    EyeshotAbtestGroupIdEnricher
    ------

    一个判断用户分组的enricher

    参数配置
    ------
    `world_name` : [string] 非空字符串
    `exp_name` : [string] 非空字符串
    `save_output_to` : [string] 默认为 group_name

    调用示例
    ------
    ``` python
    .eyeshot_abtest_group_id_enrich(
        world_name = 'mille_eyeshot_did_29',
        exp_name = 'eyeshot_cold_start_retr_opt',
        save_output_to = 'group_name'
    )
    ```
    """
    self._add_processor(EyeshotAbtestGroupIdEnricher(kwargs))
    return self

  def explore_target_cluster_sort(self, **kwargs):
    """
    ExploreTargetClusterSortArranger
    """
    self._add_processor(ExploreTargetClusterSortArranger(kwargs))
    return self

  def eyeshot_colossus_v2_author_trigger_enrich(self, **kwargs):
    """
    EyeshotColossusV2AuthorTriggerEnricher
    ------
    召回阶段请求 colossusv2 并预处理获得 colossus author trigger

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp 指针的 attr

    `output_colossus_trigger_id_attr`: [string] 用于保存trgger id list 的 attr

    `output_colossus_trigger_weight_attr`: [string] 用于保存trgger weight list 的 attr

    `is_select_picture`: [bool] [动态参数] 是否使用label中图文标记过滤视频, 默认false

    `trigger_num`: [int] [动态参数] 返回trigger的最大数目, 默认100

    `min_trigger_weight`: [int] [动态参数] 过滤掉一些不置信的trigger, 返回trigger的最小weight, 默认2

    `colossus_label_weight_attr`: [string] [动态参数] 统计reward使用的label weight, 格式为click:like:follow:forward:hate:comment,
                                  默认 1:2:5:2:-10:2

    `colossus_channel_select_attr`: [string] [动态参数] 统计reward只使用哪些channel,
                                  默认 1

    `colossus_time_s_min_max_attr`: [string] [动态参数] 统计reward使用的时间范围, 格式为为min:max,单位为s,默认0:604800,

    `colossus_duration_s_min_max_attr`: [string] [动态参数] 统计reward使用的duration范围, 格式为为min:max,单位为s,默认-1:100000

    示例
    ------
    ``` python
    .eyeshot_colossus_v2_author_trigger_enrich(
                    colossus_resp_attr="colossus_resp",
                    output_colossus_trigger_id_attr="colossus_author_trigger_list",
                    output_colossus_trigger_weight_attr="colossus_author_weight_list",
                    is_select_picture=False,
                    trigger_num=100,
                    min_trigger_weight=2,
                    colossus_label_weight_attr="1:2:5:2:-10:2",
                    colossus_time_s_min_max_attr="0:604800",
                    colossus_duration_s_min_max_attr="-1:0"
                    )
    ```

    """
    self._add_processor(EyeshotColossusV2AuthorTriggerEnricher(kwargs))
    return self

  def eyeshot_colossus_v2_photo_trigger_enrich(self, **kwargs):
    """
    EyeshotColossusV2PhotoTriggerEnricher
    ------
    召回阶段请求 colossusv2 并预处理获得 colossus photo trigger

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp 指针的 attr

    `trigger_pid_attr`: [string] 用于保存trgger pid list 的 attr

    `trigger_ts_attr`: [string] 用于保存trgger ts list 的 attr

    `trigger_aid_attr`: [string] 用于保存trgger aid list 的 attr

    `trigger_tag_attr`: [string] 用于保存trgger tag list 的 attr

    `trigger_label_attr`: [string] 用于保存trgger label list 的 attr

    `trigger_duration_attr`: [string] 用于保存trgger duration list 的 attr

    `trigger_playtime_attr`: [string] 用于保存trgger playtime list 的 attr

    `trigger_channel_attr`: [string] 用于保存trgger channel list 的 attr

    `trigger_size_attr`: [string] 用于保存trgger size 的 attr

    `is_select_picture`: [bool] [动态参数] 是否使用label中图文标记过滤视频, 默认false

    `trigger_num`: [int] [动态参数] 返回trigger的最大数目, 默认100

    `begin_time_ms`: [int] [动态参数] 过滤掉ts小于 begin_time_ms 的样本, 默认-1, 即不过滤

    `end_time_ms`: [int] [动态参数] 过滤掉ts大于 end_time_ms 的样本, 默认-1, 即不过滤

    `shift_right_num`: [int] [动态参数] trigger前N个使用0填充,用于线上预测, 默认0, 即不填充

    `colossus_channel_select_attr`: [string] [动态参数] 统计reward只使用哪些channel,
                                  默认 1
    示例
    ------
    ``` python
    .eyeshot_colossus_v2_author_trigger_enrich(
                    colossus_resp_attr="colossus_resp",
                    trigger_pid_attr="trigger_pid",
                    trigger_ts_attr="trigger_ts",
                    trigger_aid_attr="trigger_aid",
                    trigger_tag_attr="trigger_tag",
                    trigger_label_attr="trigger_label",
                    trigger_duration_attr="trigger_duration",
                    trigger_channel_attr="trigger_channel",
                    trigger_playtime_attr="trigger_playtime",
                    trigger_size_attr="trigger_size",
                    is_select_picture=True,
                    colossus_channel_select_attr="1",
                    trigger_num=100
                    )
    ```

    """
    self._add_processor(EyeshotColossusV2PhotoTriggerEnricher(kwargs))
    return self

  def eyeshot_generate_update_message(self, **kwargs):
    """
    EyeshotMioUpdateMessageEnricher
    ------
    按 shard 生成 ks::model::ModelUpdateMessage, 类似 MioUpdateMessageEnricher, 删除 is_raw_embedding_list, 发送时不区分单值和list;

    参数配置
    ------
    `inputs`: [list] 配置输入项，成员为一个 dict，包含一个 sign_source_type字段，用于指定 sign 的来源，支持 user_id，device_id，user_id_or_device_id，common_attr，item_attr，默认为 item_attr。当 sign_source_type 为 item_attr/common_attr 时，需要给出 embedding_attr 和 sign_attr，否则仅需要给出 embedding_attr。除此之外，还可以指定 slot 或者 slot_attr，当 slot_attr 为空时使用 slot，slot 通常仅用于 Embedding Server 打点，当 id_converter 不为 plainIdConverter 时也用于对 sign 做处理，注意默认的 slot 为 -1，是一个非法值，当指定了 id_converter 时必须通过 slot 或 slot_attr 来指定 slot 的值。

    `shards`: [int] shard 数量。

    `max_items_per_update_message`: [int] 单个 ModelUpdateMessage 中最大的 item 数量。

    `min_items_per_update_message_batch_buffer`: [int] 单个 ModelUpdateMessage 中最最小的 item 数量。 当 [min_items_per_update_message_batch_buffer, max_items_per_update_message] 区间合法时，开启buffer模式 ， 一个message 至少攒 min_items_per_update_message_batch_buffer 个item才发送

    `save_result_to_common_attr`: [string] 将生成的 ModelUpdateMessage 存到指定的 Common Attr，实际存入时加上 shard 作为后缀。结果会以 String List 的格式存储，每个 String 是一个序列化的 ModelUpdateMessage。

    `expire_seconds`: [int] embedding 存储逐出时间，以秒为单位，默认为 30 小时。

    `id_converter`: [object] 需要包含一个 type_name 字段，支持 plainIdConverter, kuibaEmbeddingIdConverter, mioEmbeddingIdConverter，默认为 plainIdConverter。

    `use_raw_embedding`: [bool] 是否用 embedding 原样输出至 btq , 默认 false 、此时会进行 WeightToShort 转换

    `raw_embedding_type`: [string] 当 use_raw_embedding 时用于指定 attr 的类型。支持 float32, uint16, uint32, uint64, string。默认为 float32。

    `compress_type`: [string] 当 use_raw_embedding 为 false 用于指定 embedding 压缩的方式，支持 mio_int16, scale_int8, scale_int16，默认为 mio_int16。

    调用示例
    ------
    ``` python

    .eyeshot_generate_update_message(
        inputs = [dict(
            embedding_attr="embedding",
            sign_attr="sign",
            slot_attr="slot",
        )],
        save_result_to_common_attr = "update_message")
    ```
    """
    self._add_processor(EyeshotMioUpdateMessageEnricher(kwargs))
    return self

  def eyeshot_get_remote_embedding_lite_v2(self, **kwargs):
    """
    EyeshotRecoRemoteEmbeddingAttrRawLiteEnricher
    ------
    支持 配置多个slot 的 get_remote_embedding_lite_v2 版本，请求远程 embedding 服务获取 embedding。

    参数配置
    ------
    `kess_service`: [string][动态参数] embedding server 服务的 kess 服务名 [必填]

    `kess_cluster`: [string] embedding server 服务的 kess 服务组，默认值为 "PRODUCTION"

    `shard_num`: [int] embedding server 的 shard num, 默认值为 1

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `timeout_ms`: [int] 请求 embedding server 服务的超时时间，默认值为 10

    `id_converter`: [dict] sign -> id 生成器的相关配置 [必填]
      - `type_name`: [string] 根据 sign_generator 格式可选值："mioEmbeddingIdConverter" 或 "kuibaEmbeddingIdConverter" 或 "plainIdConverter"

    `is_raw_data`: [bool] 启动 raw data 数据类型，即非 Embedding 的数据。

    `client_side_shard`: [bool] 是否根据 shard 发送请求，默认为 False，当 embedding 按 shard 存储时建议打开。

    `is_pad_size`: [bool] 时候开启padding，开启时 不满足size的embedding或raw data 不会被过滤，而是pad 0。

    `inputs`: [list] 支持配置的多个slot配置，配置项如下,

      - `slot`: [int] 填写需要与 item_key 拼接的 slot 值, 默认值为 0

      - `input_attr_name`: [string] 输入的 ItemAttr name，当 query_source_type 为 item_attr 或 common_attr 时生效。

      - `output_attr_name`: [string] 输出的 ItemAttr name

      - `query_source_type`: [string] 请求 key 来源，支持 item_key，item_id，user_id，device_id，item_attr 和 common_attr。默认为 item_key。

      - `dim`: [int] embedding 维度，需要指定，当结果的 size 不符合时会被过滤掉。

      - `raw_data_type`: [string] raw data 的数据类型，支持 int8，int16，int32，int64，uint8，uint16，uint32，uint64，float32，默认值为 uint16。

    调用示例
    ------
    ``` python
    .eyeshot_get_remote_embedding_lite_v2(
      kess_service = "grpc_xxx",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      client_side_shard = True,
      is_raw_data = True,
      is_pad_size = True,
      inputs = [
        dict(
          slot = 100,
          dim = 1,
          input_attr_name = "photo_id",
          query_source_type = "item_attr",
          output_attr_name = "click_count",
          raw_data_type = "uint64",
        ),
        dict(
          slot = 101,
          dim = 1,
          input_attr_name = "user_id",
          query_source_type = "common_attr",
          output_attr_name = "gender",
          raw_data_type = "uint64",
        ),
      ]
    )
    ```
    """
    self._add_processor(EyeshotRecoRemoteEmbeddingAttrRawLiteEnricher(kwargs))
    return self

  def eyeshot_diversity_update_enricher(self, **kwargs):
    """
    ExploreTargetClusterSortArranger
    """
    self._add_processor(EyeshotDiversityUpdateEnricher(kwargs))
    return self

  def eyeshot_pic_feasury_history_enricher(self, **kwargs):
    """
    EyeshotPicFeasuryHistoryEnricher
    """
    self._add_processor(EyeshotPicFeasuryHistoryEnricher(kwargs))
    return self

  def eyeshot_colossus_retriever(self, **kwargs):
      """
      EyeshotColossusRetriever
      """
      self._add_processor(EyeshotColossusRetriever(kwargs))
      return self

  def eyeshot_embedding_distance_enricher(self, **kwargs):
      """
      EyeshotEmbeddingDistanceEnricher
      """
      self._add_processor(EyeshotEmbeddingDistanceEnricher(kwargs))
      return self

  def eyeshot_emb_joint_enricher(self, **kwargs):
      """
      EyeshotEmbJointEnricher
      """
      self._add_processor(EyeshotEmbJointEnricher(kwargs))
      return self

  def eyeshot_item_attr_stat_cache_enricher(self, **kwargs):
    """
    EyeshotItemAttrStatCacheEnricher
    ------
    eyeshot 在本地统计 item_attr 并存入 cache，若要统计 common_attr 请参考 oversea_fetch_global_varaible_cache()，会以 namespace、cache_size 初始化一个[string, double] map，其 key = {group_prefix}_{item_key_attr}_{output_item_attr_name}, value = 统计的值

    参数配置
    ------
    `namespace`: [string] 本地 cache 存储的命名空间，使不同任务 cache 之间硬分隔管理

    `group_prefix`: [string] [动态参数] 选配项，cache 分组 (key prefix)

    `mode`: [string] [动态参数] 选配项，'read'/'write'/'' 不配或为空时写入本地 cache，并读取最新 cache 填充到 common_attr

    `use_local`: [bool] 选配项，默认值 true，true 时各线程 processor 内部独立存取 cache，false 时每台机器共同存取一份 cache

    `cache_size`: [int] 选配项，namespace 存储的 cache 数，不配置时该算子不会初始化 cache；多个同 namespace 的 cache 配置的 cache_size 需相同

    `item_key_attr`: [list] [int, string] 该 item 分群的 key

    `item_value_attrs`: [list] [double] 该 item 要拿来被统计的值，与 `item_key_attrs`

    `output_item_attrs`: [list] [double] 选配项，存储的 item_attr name, 默认为 item_value_attrs, 若要配置的话数量需与 item_value_attrs 一致

    `update_param`: [list] 选配项
      - `update_method`: [string] 更新方式，可选值：replace/max/min/moving_avg
      - `moving_decay`: [double] 选配项，使用 moving_avg 时的衰减因子，默认值0.999
    调用示例
    ------
    ``` python
    .eyeshot_item_attr_stat_cache_enricher(
      namespace="model_output_cluster_avg_v1",
      group_prefix="cluster_avg_v1",
      use_local=True,
      item_key_attr="user_group",
      item_value_attrs=["pltr", "plvtr"],
      output_item_attrs="["pltr_avg", "plvtr_avg"],
      cache_size=10000,
      update_param=[{"moving_decay": 0.99, "update_method": "moving_avg"}]
    )
    ```
    """
    self._add_processor(EyeshotItemAttrStatCacheEnricher(kwargs))
    return self