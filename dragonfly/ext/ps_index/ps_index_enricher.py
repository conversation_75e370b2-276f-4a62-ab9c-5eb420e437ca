#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class PsIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_ps_index"

  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    r = set()
    if "item_attr" in self.keys:
      for attr in self.attrs:
        r.add(attr)
    if self.output_item_attr:
      r.add(self.output_item_attr)
    return r
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self.output_common_attr:
      return set([self.output_common_attr])
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if "item_attr" in self.keys and self.keys["item_attr"] != "item_key":
      return set([self.keys["item_attr"]])
    return set()
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if "common_attr" in self.keys:
      if isinstance(self.keys["common_attr"], list):
        return set(self.keys["common_attr"])
      else:
        return set([self.keys["common_attr"]])
    return set()
  
  @property
  @strict_types
  def kconf(self) -> str:
    return self._config.get("kconf", "")

  @property
  @strict_types
  def attrs(self) -> list:
    return self._config.get("attrs", [])
  
  @property
  @strict_types
  def keys(self) -> dict:
    return self._config.get("keys", { "item_attr": "item_key" })
  
  @property
  @strict_types
  def output_item_attr(self) -> str:
    return self._config.get("output_item_attr", "")
  
  @property
  @strict_types
  def output_common_attr(self) -> str:
    return self._config.get("output_common_attr", "")

  @strict_types
  def _check_config(self) -> None:
    if self.kconf == "":
      raise ArgumentError("must configure 'kconf'")
