#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafRetriever

class PsIndexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_ps_index_output"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self.input_common_attr])
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    result = set()
    for rule in self.rules:
      if "attr_map" in rule:
        for key, value in rule["attr_map"].items():
          result.add(value)
    return result
  
  @property
  @strict_types
  def input_common_attr(self) -> str:
    return self._config.get("input_common_attr", "")

  @property
  @strict_types
  def rules(self) -> list:
    return self._config.get("rules", [])

  @strict_types
  def _check_config(self) -> None:
    if self.input_common_attr == "":
      raise ArgumentError("must configure 'input_common_attr'")
    if len(self.rules) == 0:
      raise ArgumentError("must configure 'rules'")
    for rule in self.rules:
      if "item_key" not in rule:
        raise ArgumentError("every rule must has 'item_key'")
