#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ps_index_enricher import *
from .ps_index_retriever import *
from .label_list_proto_enricher import *

class PsIndexMixin(CommonLeafBaseMixin):
  """
  PsIndex API 接口的 Mixin 实现

  colossusdb_partition_server 服务的 index 业务，简称为 ps_index 。
  是一种在线特征存储服务，目前存储 feasury 用户侧画像数据与 Item 侧索引数据。
  此 Mixin 包含了从 ps_index 服务中获取用户侧与 Item 侧数据的接口。

  维护团队： xinchenghua, zhoujindong, lixutong03
  """

  def enrich_by_ps_index(self, **kwargs):
    """
    PsIndexEnricher
    ----
    从 colossusdb_partition_server 服务获取数据。
    
    有两种用法，一种是通过请求用户侧数据保存到 common_attr 中，另一种是请求 Item
    侧数据直接 enrich item attr

    参数配置
    ------
    `kconf`: [string] 必配项， kconf 配置路径

    kconf 配置内容参考 https://docs.corp.kuaishou.com/d/home/<USER>
    ```
    {
      "table_kconf": "aaa.bbb.ccc",
      "timeout_ms": 100,
      "max_signs_per_request": 100,
      "request_format": "FlexBuffers"
    }
    ```

    其中 aaa.bbb.ccc 需要由被调负责人提供

    `attrs`: [list] 选配项，请求的特征名称列表

    `keys`: [dict] 选配项，表示输入的 key 的从哪个 dragon attr 来。默认认为是请求 Item 侧数据，将所有 item 的 item_key 作为请求 keys

    在请求 Item 侧数据服务时
    - dict key 为 "item_attr": value 为 string ，表示从对应 item attr 中获取 keys ，对应 item attr 类型必须是 int

    在请求用户侧数据服务时
    - dict key 为 "common_attr": value 为 string 或 string array 表示从对应的 common attr 中获取 keys ，对应 common attr 的值类型必须是 int 或 int list
    - dict key 为 "uid_or_did_hash": value 为 true 表示使用当前用户的 uid 或者用 device id 的 city hash 结果

    `output_item_attr`: [string] 选配项，只支持非 FlexBuffers 的请求格式，将结果 Document 内 value 当成 bytes 存储到对应 item attr 中，不做进一步解析

    `output_common_attr`: [string] 选配项，将结果存储到指定 common attr 中，用于后续其它 processor 解析，请求用户侧数据时必须配置
    - 如果是 FlexBuffers 格式，存储的是结果中的 batch_data 字段
    - 如果是其它格式，将结果中 founds 字段的 Document 序列化后存

    `version` : [int] 选配项，如果请求的服务存储格式为 MultiVersionBytes，需要填写此字段，默认为 INT64_MAX

    `calcu_cityhash_for_keys` : [bool] 选配项，True 表示对输入的 key 进行 CityHash64 计算

    调用示例
    ------
    ``` python

    # 请求 Item 侧数据，请求的 keys 就是所有 item 的 item_key
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      attrs = [
        "attr1",
        "attr2",
      ],
    )

    # 用 uid 或 did hash 作为 key
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      attrs = [
        "attr1",
        "attr2",
      ],
      keys = { "uid_or_did_hash": True, },
      output_common_attr = "ps_index_output",
    )

    # 用 common attr 作为 key 
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      attrs = [
        "attr1",
        "attr2",
      ],
      keys = { "common_attr": "uid_like_common_attr", },
      output_common_attr = "ps_index_output",
    )

    # 请求 MultiVersionBytes 格式数据
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      output_item_attr = "store_data_compress_bytes",
      version = 1693377592283,
      calcu_cityhash_for_keys = True
    )

    ```
    """
    self._add_processor(PsIndexEnricher(kwargs))
    return self
  
  def retrieve_by_ps_index_output(self, **kwargs):
    """
    PsIndexRetriever
    -----
    读取 enrich_by_ps_index 的输出结果，解析并召回 items

    参数
    -----
    `input_common_attr`: [string] 必配项，是 enrich_by_ps_index 的结果保存到的 common attr

    `rules`: [list] 必配项，表示转换规则，每条转换规则里必须包含 "item_key"

    - "item_key": 必配项，要作为 dragon item_key 的请求特征名称
    - "attr_map": 选配项，与 item_key 同源的请求特征名称转换成 dragon item_attr 名称的映射关系
                  注意必须是同一个 fea 中衍生下来的，长度是一致的
    - "reason": 选配项，默认为 0

    调用示例
    -----
    ``` python
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      attrs = [
        "fea1PidList",
        "fea1TimeList",
        "fea2PidList",
        "fea2TimeList",
      ],
      keys = { "uid_or_did_hash": True, },
      output_common_attr = "ps_index_output",
    ).retrieve_by_ps_index_output(
      input_common_attr = "ps_index_output",
      rules = [
        {
          "item_key": "fea1PidList",
          "reason": 1,
          "attr_map": {
            "fea1PidList": "photo_id",
            "fea1TimeList": "timestamp",
            ...
          },
        },
        {
          "item_key": "fea2PidList",
          "attr_map": {
            "fea2PidList": "photo_id",
            "fea2TimeList": "timestamp",
            ...
          },
        },
        ...
      ]
    )
    ```

    """
    self._add_processor(PsIndexRetriever(kwargs))
    return self

  def enrich_by_label_list_proto(self, **kwargs):
    """
    LabelListProtoEnricher
    -----
    读取 Labellist proto，解析出 label 并 enrich 到 item_attr 中
    output attr 为固定的 "str_key"、"label_cnt" 和 "label_list"

    参数
    -----
    `from_extra_var`: [string] 从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 Message，一般可指定为 parse_protobuf_from_string 接口的 output_attr 值

    `output_str_key`: [string] 必配项， 存储 str keys 的 item attr name

    `output_label_cnt`: [string] 必配项， 存储 label 数量的 item attr name

    `output_label_list`: [string] 必配项， 存储 label list 的 item attr name

    调用示例
    -----
    ``` python
    .enrich_by_ps_index(
      kconf = "colossus.ps.testPsClientXXXXXXXXX",
      output_item_attr = "label_list_bytes",
    ).parse_protobuf_from_string(
      input_attr = "label_list_bytes",
      output_attr = "label_list_pb",
      class_name = "colossusdb.proto.LabelList",
      is_common_attr = False
    ).enrich_by_label_list_proto(
      from_extra_var = "label_list_pb",
      output_str_key = "str_key",
      output_label_cnt = "label_cnt",
      output_label_list = "label_list"
    )

    # 打印 enrich 后的结果
    print(f"str_key: {item['str_key']}")
    print(f"label_cnt: {item['label_cnt']}")
    print("label_list:")
    if item['label_list'] is not None:
      for label in item['label_list']:
        print(label)
    ```

    """
    self._add_processor(LabelListProtoEnricher(kwargs))
    return self
