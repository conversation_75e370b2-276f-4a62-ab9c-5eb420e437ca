#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class LabelListProtoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_label_list_proto"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set((self._config["output_str_key"], self._config["output_label_cnt"], self._config["output_label_list"]))
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set([self._config["from_extra_var"]])
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()
