#!/usr/bin/env python3
"""
filename: mio_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for mio
author: <EMAIL>
date: 2020-01-16 18:34:00
"""

import itertools

from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel, extract_attr_names
from ...common_leaf_processor import LeafEnricher

class MioEmbeddingAttrLiteEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mio_embedding_lite"

  @staticmethod
  @strict_types
  def _parse_slots(slots) -> set:
    if isinstance(slots, str):
      return set(map(int, slots.split()))
    elif isinstance(slots, int):
      return {slots}
    elif isinstance(slots, list):
      return set(slots)
    else:
      raise ValueError(f"Invalid slots: {slots}({type(slots)})")

  @strict_types
  def _all_slots(self, is_common = None) -> set:
    slots_config = self._config.get("slots_config", [])
    return set(itertools.chain.from_iterable(self._parse_slots(sc["slots"]) for sc in slots_config
                                             if is_common is None or sc.get("common", False) == is_common))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    return set(sc["input_name"] for sc in slots_config if not sc.get("common", False))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    ret = set(sc["input_name"] for sc in slots_config if sc.get("common", False))
    if len(self._config.get("save_final_status_to_attr", "")) > 0:
      ret.add(self._config["save_final_status_to_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    attrs = set()
    if self._config.get("slot_as_attr_name", False):
      for slot in self._all_slots():
        attrs.add(self._config.get("slot_as_attr_name_prefix", "") + str(slot))
    else:
      for key in ["slots_inputs", "parameters_inputs"]:
        attrs.update(self._config.get(key, []))

    for sc in slots_config:
      if sc.get("common", False):
        continue
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    attrs = set()
    if self._config.get("slot_as_attr_name", False):
      for slot in self._all_slots(is_common=True):
        attrs.add(self._config.get("slot_as_attr_name_prefix", "") + str(slot))
    else:
      for key in ["common_slots_inputs", "common_parameters_inputs"]:
        attrs.update(self._config.get(key, []))

    if self._config.get("model_version_attr"):
      attrs.add(self._config["model_version_attr"])

    for sc in slots_config:
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)

    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MioEmbeddingAttrEnricher(MioEmbeddingAttrLiteEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mio_embedding"

class MioLocalEmbeddingAttrEnricher(MioEmbeddingAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_mio_embedding"

  @strict_types
  def is_async(self) -> bool:
    return False

class MioExternalEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mio_external_embedding"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    return set(sc["input_name"] for sc in slots_config if not sc.get("common", False))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    return set(sc["input_name"] for sc in slots_config if sc.get("common", False))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["slots_inputs", "parameters_inputs"]:
      attrs.update(self._config.get(key, []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_slots_inputs", "common_parameters_inputs"]:
      attrs.update(self._config.get(key, []))
    return attrs

class MioPredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_predict"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("output_common_attr", False):
      return set(c["attr_name"] for c in self._config["outputs"])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("output_common_attr", False):
      return set(c["attr_name"] for c in self._config["outputs"])
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(c["attr_name"] for c in self._config["inputs"] if c.get("common", False))
    # 下面的实现必须耦合 fetch_mio_embedding 的 save_result_as_tensor_output
    #if not self._config.get("read_input_from_extra_var", False):
    #  return set(c["attr_name"] for c in self._config["inputs"] if c.get("common", False))
    #else:
    #  return set(c["attr_name"] for c in self._config["inputs"])

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(c["attr_name"] for c in self._config["inputs"] if not c.get("common", False))


class TensorrtPredictItemAttrEnricher(MioPredictItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tensorrt_predict"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("output_common_attr", False):
      ret = set(c["attr_name"] for c in self._config["outputs"])
    debug_suffix = self._config.get("log_input_tensor_attr_suffix", "")
    if len(debug_suffix) > 0:
      for c in self._config["inputs"]:
        if c.get("common", False):
          ret.add(c["attr_name"] + debug_suffix)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config.get("output_common_attr", False):
      ret = set(c["attr_name"] for c in self._config["outputs"])
    debug_suffix = self._config.get("log_input_tensor_attr_suffix", "")
    if len(debug_suffix) > 0:
      for c in self._config["inputs"]:
        if not c.get("common", False):
          ret.add(c["attr_name"] + debug_suffix)
    return set()


class KaiPredictItemAttrEnricher(MioPredictItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kai_predict"

class MioKsSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_ks_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["user_info_attr", "click_pids_attr", "like_pids_attr", "follow_ids_attr", "tab_id_attr", "source_photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    if "caller_model" in self._config.keys() :
      ret.update(self.extract_dynamic_params(self._config.get("caller_model")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_id_attr", "photo_info_attr", "context_info_attr", "retrieve_info_attr", "reason_attr", "is_living_attr", "pctr_attr", "pltr_attr", "rank_attr", "page_attr", "cascade_pctr_attr", "cascade_plvtr_attr", "cascade_psvr_attr", "cascade_pltr_attr", "cascade_pwtr_attr", "cascade_pftr_attr", "cas_xtr_pos_ptr_attr", "cas_final_pos_ptr_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MioOverseaSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_oversea_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["reader_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["common_sample_attrs"]:
      if key in self._config:
        ret.update(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr", "context_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["item_sample_attrs"]:
      if key in self._config:
        ret.update(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MioDenseFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_dense_feature"

class MioKsibSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_ksib_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["reader_info_attr", "request_info_attr", "colossus_sim_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["common_sample_attrs", "user_attrs"]:
      if key in self._config:
        ret.update(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr", "context_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["item_sample_attrs", "photo_attrs", "context_attrs"]:
      if key in self._config:
        ret.update(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["common_slots_output", "common_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_slots_output", "item_parameters_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MioKsibRedisFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_ksib_redis_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["reader_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr", "context_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["common_redis_feature_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_redis_feature_output"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class MioKsibRewriteSlotIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_rewrite_slot_in_sign"


class MioUpdateMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_update_message"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for input_config in self._config["inputs"]:
      source_type = input_config.get("sign_source_type", "item_attr")
      if source_type == "item_attr":
        ret.add(input_config["embedding_attr"])
        ret.add(input_config["sign_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
      elif source_type == "item_id":
        ret.add(input_config["embedding_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for input_config in self._config["inputs"]:
      source_type = input_config.get("sign_source_type", "item_attr")
      if source_type == "common_attr":
        ret.add(input_config["embedding_attr"])
        ret.add(input_config["sign_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
      elif source_type in ["user_id", "device_id", "user_id_or_device_id"]:
        ret.add(input_config["embedding_attr"])
        if "slot_attr" in input_config:
          ret.add(input_config["slot_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config["save_result_to_common_attr"] + str(shard) for shard in range(self._config["shards"]))

class MioUpdateCommonSpecEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_commonspec"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(filter(lambda x : x is not None, itertools.chain.from_iterable(map(lambda x: (x.get("embedding_attr"), x.get("sign_attr"), x.get("slot_attr")), self._config["inputs"]))))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to_common_attr"]}

class MioRemotePredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_remote_predict"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { self._config["reco_photo_attr"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["user_info_attr"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["pctr_attr", "pltr_attr", "pwtr_attr", "pftr_attr", "phtr_attr", "pvtr_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    ret.update(self._config.get("extend_rate_attrs", []))

    for c in self._config.get("preds_attrs", []):
      if type(c) is str:
        ret.add(c)
      else:
        ret.add(c["pred_name"])

    if self._config.get("debug", False):
      for key in ["dnn_input_attr", "feature_info_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class MioGeneralSearchUnitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_enrich_from_gsu"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("send_common_attrs", []), "name")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("send_item_attrs", []), "name")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "save_item_list_to_item_attr" in self._config:
      ret.add(self._config["save_item_list_to_item_attr"])
    ret |= extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    ret |= extract_attr_names(self._config.get("recv_common_attrs", []), "as")
    return ret

class MioFeatureEmbeddingTensorAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_enrich_feature_embedding_tensor"

  @strict_types
  def get_tensor_config(self):
    return self._config["tensors"] if "tensors" in self._config else list()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for c in self.get_tensor_config():
      if c.get("common", False):
        ret.add(c["attr_name"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for c in self.get_tensor_config():
      if not c.get("common", False):
        ret.add(c["attr_name"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for c in self.get_tensor_config():
      if c.get("common", False):
        ret.add(c["tensor_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for c in self.get_tensor_config():
      if not c.get("common", False):
        ret.add(c["tensor_name"])
    return ret

class MioLoadEmbFromFileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "load_emb_from_file"

class RemoteModelAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "remote_model_enrich"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return_attrs = self._config.get("return_attrs", [])
    return set(return_attrs)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["slots_inputs", "parameters_inputs"]:
      attrs.update(self._config.get(key, []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["common_slots_inputs", "common_parameters_inputs"]:
      attrs.update(self._config.get(key, []))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MioDumpTensorAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_mio_tensor"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("tensors", list()):
      attrs.add(u["dump_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("tensors", list()):
      attrs.add(u["tensor_attr"])
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False

class MioUserInfoHotFixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_fix_user_info"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["user_info_attr"]}

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["user_info_attr"]}

class MioLongTermSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_long_term_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    # long term item feature sign
    channel=self._config.get("long_term_items_attr", "")
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_slot_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_sign_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_fake_sign_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_fake_slot_attr"], channel))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["long_term_items_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MioEmbeddingAttrOptEnricher(MioEmbeddingAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_mio_embedding_opt"

class MioLocalEmbeddingAttrOptEnricher(MioEmbeddingAttrOptEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_mio_embedding_opt"

  @strict_types
  def is_async(self) -> bool:
    return False 

class MioPredictItemAttrOptEnricher(MioPredictItemAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_predict_opt"

class MioTensorOpEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_tensor_op"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "sl_config" in self._config:
      for c in self._config["sl_config"]:
        ret.add(c["input_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret = set()
    if "sl_config" in self._config:
      for c in self._config["sl_config"]:
        ret.add(c["output_attr"])
    return ret

class GenerateItemSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_item_sign"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for c in self._config["output_slot_attrs"]:
      ret.add(c)
    #for c in self._config["output_slots"]:
    #  ret.add(c)
    ret.add(self._config["output_sign_attr"])
    return ret

class MioLongTermSignFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_long_term_sign_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    # long term item feature sign
    channel=self._config.get("long_term_items_attr", "")
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_slot_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_sign_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_fake_sign_attr"], channel))
    ret.add(gen_attr_name_with_common_attr_channel(self._config["output_fake_slot_attr"], channel))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["long_term_items_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class MioKsibLiveDeepHashEncodeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_live_deep_hash_encode"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_item_attr_name", ""))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_item_attr_name", ""))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_common_list_attr_name", ""))
    ret.add(self._config.get("embedding_size", 512))
    ret.add(self._config.get("prime_number", 1000000000000037))
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False
  

class DecompressEmbeddingFromStringEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "decompress_embedding_from_string"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for c in self._config["decompress_configs"]:
      if c["is_common"]:
        ret.add(c["output_attr"])
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for c in self._config["decompress_configs"]:
      if c["is_common"]:
        ret.add(c["input_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for c in self._config["decompress_configs"]:
      if not c["is_common"]:
        ret.add(c["output_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for c in self._config["decompress_configs"]:
      if not c["is_common"]:
        ret.add(c["input_attr"])
    return ret
  
  @strict_types
  def is_async(self) -> bool:
    return False


class CommonAttrFromRedisByLrangeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_common_attr_from_redis_by_lrange"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["output_common_attr"]}
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "redis_key" in self._config.keys() :
      ret.update(self.extract_dynamic_params(self._config.get("redis_key")))
    return ret

class MioUniqueAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "mio_unique"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {*self._config["output_item_index_attrs"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["output_attr"], *self._config["output_common_index_attrs"]}

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {*self._config["input_item_attrs"]}

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {*self._config["input_common_attrs"]}

class FilterSlotSignAttrBySlotIdListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_slot_sign_attr_by_slotid_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "common_slots_attr" in self._config or "common_signs_attr" in self._config:
      assert "common_slots_attr" in self._config and "common_signs_attr" in self._config, \
      f"common_slots_attr & common_signs_attr 应同时配置"
      ret.add(self._config["common_slots_attr"])
      ret.add(self._config["common_signs_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "item_slots_attr" in self._config or "item_signs_attr" in self._config:
      assert "item_slots_attr" in self._config and "item_signs_attr" in self._config, \
      f"item_slots_attr & item_signs_attr 应同时配置"
      ret.add(self._config["item_slots_attr"])
      ret.add(self._config["item_signs_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attr_suffix = self._config["export_attr_name_suffix"]
    for attr in ["common_slots_attr", "common_signs_attr"]:
      if attr in self._config:
        ret.add(self._config[attr] + attr_suffix)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attr_suffix = self._config["export_attr_name_suffix"]
    for attr in ["item_slots_attr", "item_signs_attr"]:
      if attr in self._config:
        ret.add(self._config[attr] + attr_suffix)
    return ret
