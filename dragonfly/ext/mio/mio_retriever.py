#!/usr/bin/env python3
"""
filename: mio_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for mio
author: <EMAIL>
date: 2020-09-24 18:34:00
"""

import operator
import itertools

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class MioUicServerRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_uic_server"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["aid_attr", "play_attr", "time_attr", "pid_attr", "tab_attr", "slot_attr", "click_attr"
                "like_attr", "follow_attr", "forward_attr", "cmef_attr", "download_attr", "long_view_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MioGpuLiveRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gpu_live_retrieval"

  def run_type(self):
    return self._config.get("run_type", -1)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self.run_type() in [1, 2]:
      for key in ["item_no_attr", "item_embed_table_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self.run_type() == 0:
      for key in ["item_embed_attr", "item_embed_table_attr", "cluster_tensor_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class NextInterestSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "next_interest_predict_samples_generate_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["avg_photo_embedding_attr", "avg_watch_time_attr", "cluster_ids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["input_photo_embedding_attr", "input_watch_time_attr", "label_weight_attr", "input_cluster_id_attr", "mask_cluster_id_attr", "label_watch_time"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret