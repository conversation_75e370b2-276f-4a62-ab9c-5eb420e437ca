#!/usr/bin/env python3
"""
filename: mio_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for mio
author: <EMAIL>
date: 2020-01-16 18:34:00
"""

import operator

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class MioRecordChannelObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_to_mio_learner"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["attrs", "slots_attrs", "signs_attrs", "eval_attrs"]:
      attrs.update(self._config.get(key, []))

    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("slots", [])))

    for key in ["pid_attr"]:
      if key in self._config:
        attrs.add(self._config[key])

    attrs.add(self._config["label_attr"])
    return attrs;

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["attrs", "slots_attrs", "signs_attrs", "eval_attrs"]:
      attrs.update(self._config.get(key, []))

    if self._config.get("slot_as_attr_name", False):
      attrs.update(map(str, self._config.get("slots", [])))

    for key in ["lineid_attr", "time_ms_attr", "user_hash_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs;

class MioStringChannelObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_to_simple_mio"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["input_attr"] }
