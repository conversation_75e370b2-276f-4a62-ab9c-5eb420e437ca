#!/usr/bin/env python3
"""
filename: kuiba_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for kuiba
author: <EMAIL>
date: 2020-01-16 18:34:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class KuibaRawSamplePackageRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_raw_sample_package"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for save_attr in ["save_common_attr_names_to", "save_next_common_attr_names_to", "save_item_attr_names_to"]:
      if save_attr in self._config:
        attrs.add(self._config[save_attr])
    attrs.add(self._config.get("save_locale_to", "rLocale"))
    attrs.add(self._config.get("save_channel_to", "rChannel"))
    return attrs

class KuibaPredictRequestRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_kuiba_predict_request"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "attr_list" in self._config.keys():
      return set(self._config["attr_list"])
    return set()

class KuibaCompressSampleRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_kuiba_compress_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["item_signs"], self._config["item_slots"]])

