#!/usr/bin/env python3
"""
filename: kuiba_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kuiba
author: <EMAIL>
date: 2020-01-16 18:34:00
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher

class KaiKuibaFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kai_extract_kuiba_feature"

  @strict_types
  def _get_input_attrs(self) -> set:
    attrs = set()
    for slots_config in self._config["config"].values():
      if not isinstance(slots_config, dict):
        continue
      extrators = slots_config['attrs']
      for extrator in extrators:
        if extrator["converter"] == "combine":
          converter_args = extrator["converter_args"]
          attrs.update(converter_args["left"].keys())
          attrs.update(converter_args["right"].keys())
        else:
          attrs.update(extrator["attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["item_slots"])
    attrs.add(self._config["item_signs"])
    for name, slot_id in self._config["slots_mapping"].items():
      if slot_id == -1:
        attrs.add(name)
    return attrs

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["common_slots"])
    attrs.add(self._config["common_signs"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(filter(lambda x: x in self.preceding_output_item_attrs or \
                                x not in self.preceding_output_common_attrs, self._get_input_attrs()))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(filter(lambda x: x in self.preceding_output_common_attrs or \
                                x not in self.preceding_output_item_attrs, self._get_input_attrs()))

class KuibaParameterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_kuiba_parameter"

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    return True

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    if self._config.get("slot_as_attr_name", False):
      prefix = self._config.get("slot_as_attr_name_prefix", "")
      for conf in self._config["config"].values():
        attr_conf = conf["attrs"][0]
        slot = attr_conf.get("mio_slot_key_type", attr_conf["key_type"])
        attrs.add(prefix + str(slot))
    else:
      attrs.add(self._config["slots_output"])
      attrs.add(self._config["parameters_output"])
    return attrs

  @strict_types
  def _get_input_attrs(self, common) -> set:
    ret = set()
    for conf in self._config["config"].values():
      attrs = list()
      for attr in conf["attrs"]:
        if attr["converter"] == "combine":
          converter_args = attr["converter_args"]
          attrs += list(converter_args["left"].keys())
          attrs += list(converter_args["right"].keys())
        else:
          attrs += attr["attr"]

      if common is None:
        ret.update(attrs)
      else:
        for attr in attrs:
          if attr in self.preceding_output_item_attrs:
            if not common:
              ret.add(attr)
          elif attr in self.preceding_output_common_attrs:
            if common:
              ret.add(attr)
          else:
            ret.add(attr)

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if not self._config.get("is_common_attr", False):
      # item part
      return self._get_input_attrs(common=False)
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      # all input is common attr
      return self._get_input_attrs(common=None)
    else:
      # common part
      return self._get_input_attrs(common=True)

class KuibaPredictItemIndexItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_kuiba_predict_item_index"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("attr_prefix", "") + attr + self._config.get("attr_postfix", "")
               for attr in self._config["attrs"])

class KuibaCalcUserEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kuiba_tower"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attr = self._config.get("save_user_embedding_to_attr", "")
    if attr:
      return set([attr])
    return set()

class KuibaPredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kuiba_predict_item"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set: 
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("output_prefix", "") + attr
               for attr in self._config["loss"])

class KuibaPhotoStoreItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_kuiba_photo_store"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_key_from_attr")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("attr_prefix", "") + attr + self._config.get("attr_postfix", "")
               for attr in self._config["attrs"])

class KuibaThirdPartyDataAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_kuiba_third_party_data"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {self._config["save_data_to_attr"]}

class KuibaRawSamplePackageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_raw_sample_package"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("common_attrs", [])))
    attrs.update(self.extract_dynamic_params(self._config.get("item_attrs", [])))
    attrs.add(self._config.get("timestamp_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if isinstance(self._config.get("item_attrs", []), list):
      attrs.update(self._config.get("item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}
  
  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    """ 是否依赖当前目标 item_table 中的所有 attr 数据 """
    if len(self.extract_dynamic_params(self._config.get("item_attrs", []))) > 0:
      return True
    else:
      return False
    

  @property
  @strict_types
  def enable_auto_merge(self) -> bool:
    return self._config.get("enable_auto_merge", False)
  
  @strict_types
  def auto_merge_config(self, other_config: dict) -> bool:
    for field in ["save_result_to", "timestamp_attr"]:
      if self._config.get(field, None) != other_config.get(field, None): 
        return False
    
    for merge_key in ["common_attrs", "item_attrs"]:
      merge_item = self._config.get(merge_key, [])
      merge_item.extend(other_config.get(merge_key, []))
      self._config[merge_key] = merge_item
    return True

class KuibaSampleAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_kuiba_sample_attr"

  def is_common(self):
    return self._config.get("is_common_attr", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self.is_common():
      for key in ["predict_item"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self.is_common():
      for key in ["predict_item"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self.is_common():
      for key in ["output_attrs"]:
        if key in self._config:
          for e in self._config[key]:
            ret.add(e)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self.is_common():
      for key in ["output_attrs"]:
        if key in self._config:
          for e in self._config[key]:
            ret.add(e)
    return ret

class KuibaAttrHitCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calculate_attr_hit_count"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      attrs.add(attr["hit_list"])
      if "hit_score_list" in attr:
        attrs.add(attr["hit_score_list"])
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      for target_list in attr["target_list"]:
        attrs.add(target_list)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    prefix = ""
    if "output_prefix" in self._config:
      prefix = self._config["output_prefix"]
    for attr in self._config.get("attrs", []):
      for target_list in attr["target_list"]:
        attrs.add(prefix + target_list + "_hit")
        attrs.add(prefix + target_list + "_hit_count")
    return attrs

class KuibaDrmTowerAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_drm_tower_attr"

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    # NOTE(wenjianfeng) 每次修改processsor的C++抽取common attr后，需要修改这里
    implemented_attrs = ['uHhEffectiveViewList', 'uId', 'uTrueGender', 'uHetuLevelOneDistList', 'uRealtimeLikeList',
     'dId', 'uVisitMod', 'uForwardList', 'uAppList', 'uGenderRaw', 'uFollowListGlobal', 'uRealtimeClickList', 'uIp', 'uInferGender', 'uHhLongViewList']
    attrs.update(implemented_attrs)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    res_attr = set()
    if self._config.get("is_common_attr", False):
      res_attr.add(self._config.get("user_info_attr", 'miss_user_info_attr'))
    return res_attr

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    res_item_attr = set()
    if self._config.get("is_common_attr", False):
      return res_item_attr
    else:
      res_item_attr.add(self._config.get("photo_info_attr", 'miss_photo_info_attr'))
    return res_item_attr

# TODO 修改这里
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("is_common_attr", False):
      return self._get_output_attrs()
    else:
      return set()

  # @property
  # @strict_types
  # def output_item_attrs(self) -> set:
  #   if not self._config.get("is_common_attr", False):
  #     return self._get_output_attrs()
  #   else:
  #     return set()

class KuibaParameterFusedItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_kuiba_item_parameter_by_fused_converter"

  @strict_types
  def _check_config(self) -> None:
    # 检查 slots_output, parameters_output 都有
    check_arg(self._config.get("slots_output"), "`slots_output` 是必选项")
    check_arg(self._config.get("parameters_output"), "`parameters_output` 是必选项")
    check_arg(not self._config.get("slot_as_attr_name", False), "`slot_as_attr_name` 在这个 processor 里不被支持")

    # 检查支持的 converter 类型
    supported_converter_types = {"fused_match"}
    for conf in self._config["config"].values():
      check_arg(len(conf.get("attrs")) == 1, "每个 parameter config 只支持一个 attrs")
      attr = conf["attrs"][0]
      converter_type = attr.get("converter")
      check_arg(converter_type and converter_type in supported_converter_types,
        "`converter` 必须选自 {\"fused_match\"}")
      if converter_type == "fused_match":
        converter_attrs = attr.get("attr")
        check_arg(len(converter_attrs) == 4,
          "used_match converter 必须有 4 个 attr, 分别是 [left, l_common?, right, r_common?]")
      # implement other fused_converters

  @strict_types
  def _get_all_type_input_attrs(self) -> tuple:
    item_attrs = set()
    common_attrs = set()
    for conf in self._config["config"].values():
      for attr in conf["attrs"]:
        if attr["converter"] == "fused_match": # [left_attr, left_common?, right_attr, right_common?]
          left_attr = attr["attr"][0]
          if attr["attr"][1] == "common":
            common_attrs.add(left_attr)
          else:
            item_attrs.add(left_attr)
          right_attr = attr["attr"][2]
          if attr["attr"][3] == "common":
            common_attrs.add(right_attr)
          else:
            item_attrs.add(right_attr)
        # implement other fused_converters

    return (common_attrs, item_attrs)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["slots_output"])
    attrs.add(self._config["parameters_output"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_all_type_input_attrs()[1]

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_all_type_input_attrs()[0]
