#!/usr/bin/env python3
# coding=utf-8
"""
filename: kuiba_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kuiba api mixin
author: <EMAIL>
date: 2020-01-19 16:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kuiba_enricher import *
from .kuiba_retriever import *
from .kuiba_observer import *

class KuibaApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 kuiba 相关的 Processor 接口:
  - KaiKuibaFeatureEnricher
  - KuibaParameterAttrEnricher
  - KuibaPredictItemIndexItemAttrEnricher
  - KuibaPhotoStoreItemAttrEnricher
  - KuibaRawSamplePackageRetriever
  - KuibaThirdPartyDataAttrEnricher
  - KuibaExtractedSampleObserver
  - KuibaPredictItemAttrEnricher
  - kuibaDrmTowerAttrEnricher
  - KuibaParameterFusedItemAttrEnricher
  """

  def kai_extract_kuiba_feature(self, **kwargs):
    """
    KaiKuibaFeatureEnricher
    ------
    使用 ParameterConfig 抽取 Parameter 放到 attr 里（如果是 common 则放到 common attr, 否则放到 item attr）

    参数配置
    ------
    `common_slots`: [string] 输出 common slots 的位置, 默认为 common_slots

    `common_signs`: [string] 输出 common signs 的位置, 默认为 common_signs

    `item_slots`: [string] 输出 item slots 的位置, 默认为 slots

    `item_signs`: [string] 输出 item signs 的位置, 默认为 signs

    `config`: [dict] 传给 ParameterConfig 的配置

    `slots_mapping`: [dict] 传给 kuiba feature name 到 slot id 的映射

    调用示例
    ------
    ``` python
    import json
    .extract_kuiba_parameter(
        common_slots="common_slots",
        common_signs="common_signs",
        item_slots="slots",
        item_signs="signs",
        slots_mapping=json.load(open("./kai_kuiba_config.json"))["sign_feature_slot"],
        config=json.load(open("./kuiba_config.json"))["krp_ps_server"]["network"]["loss_functions"])
    ```
    """
    self._add_processor(KaiKuibaFeatureEnricher(kwargs))
    return self

  def extract_kuiba_parameter(self, **kwargs):
    """
    KuibaParameterAttrEnricher
    ------
    使用 ParameterConfig 抽取 Parameter 放到 attr 里（如果是 common 则放到 common attr, 否则放到 item attr）

    参数配置
    ------
    `slots_output`: [string] 输出 slots 的位置，仅当 slot_as_attr_name 为 False 时生效。

    `parameters_output`: [string] 输出 parameters 的位置，仅当 slot_as_attr_name 为 False 时生效。

    `config`: [dict] 传给 ParameterConfig 的配置，说明文档见 [document](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB75t1xAsVFb9H7f6peiUHI)

    `is_common_attr`: [bool] 是否仅从 common attr 中抽取，默认为 false。

    `slot_as_attr_name`: [bool] 用 slot 作为 key，signs 作为 int list 存储。默认为 False。

    `slot_as_attr_name_prefix`: [string] 用 slot 作为 key 时的前缀，默认无前缀。

    `fused_slot_sign_remap`: [bool] 是否在该 processor 中融入 remap_slot_sign_feature 逻辑，默认为 False。

    `item_map_list`: [list] fused_slot_sign_remap 为 True 时配置，list of [slot, map_slot, map_prefix, size] ，其中 slot[string | int] 代表 attr name 或者 slot ，map_slot[int] 代表映射后的 attr name 或者 slot ， map_prefix[int] 代表映射后 sign 的前缀， size[int] 代表映射后 sign 的桶数（即 value %= size ）。其中 size 可以省略，表示不作分桶。

    `sign_prefix_bit_num_input`: [int] fused_slot_sign_remap 为 True 时配置，需要映射的 sign 的前缀位数，有效范围 (0, 16]

    `sign_prefix_bit_num_output`: [int] fused_slot_sign_remap 为 True 时配置， 输出 sign 的前缀位数，有效范围 (0, 16]
    调用示例
    ------
    ``` python
    import json
    .extract_kuiba_parameter(
        slots_output="slots",
        parameters_output="parameters",
        config=json.load(open("./parameter_config.json")))
    ```
    """
    self._add_processor(KuibaParameterAttrEnricher(kwargs))
    return self

  def get_item_attr_by_kuiba_predict_item_index(self, **kwargs):
    """
    KuibaPredictItemIndexItemAttrEnricher
    ------
    从 PredictItemIndex 获取 item_attr

    参数配置
    ------
    `attrs`: [list] 将要获取的 item attr 列表

    `queue_name`: [string] 索引的 btqueue name，默认为 kuiba_predict_index_doc

    `reader_name`: [string] reader 的标识，默认为 kuiba_predict_service

    `index_dir`: [string] 存储索引的位置，默认为 `/dev/shm/kuiba-predict-item`

    `memory`: [int] 索引的 value 部分占用内存大小，默认为 (1L << 30) * 100

    `capacity`: [int] 索引所能容纳的最大 key 数量，默认为 (1L << 30)

    `expire_seconds`: [int] 索引中 value 的过期时间，默认为 4 * 24 * 3600

    `item_filter`: [string] item 过滤器，默认为 `default`

    `use_mem_kv`: [bool] 是否使用 mem_kv 取代 shm_kv，默认为 False

    `update_thread_num`: [int] 索引更新任务线程数，默认为 4

    `max_delay_seconds`: [int] 索引启动时允许的最长 lag 时间，默认为 -1（不检查 lag）

    `item_key_from_attr`: [string] 从给定的 attr 读取 item_key，默认为空，使用 RecoResult 里的 item_key。

    `attr_prefix`：[string] 存入 item_attr 时增加前缀，默认为空。

    `attr_prefix`：[string] 存入 item_attr 时增加后缀，默认为空。

    `no_overwrite`: [bool] 仅当 attr 不存在时写入，默认为 false。

    调用示例
    ------
    ``` python
    .get_item_attr_by_kuiba_predict_item_index(attrs=["pId", "aId"])
    ```
    """
    self._add_processor(KuibaPredictItemIndexItemAttrEnricher(kwargs))
    return self

  def get_item_attr_by_kuiba_photo_store(self, **kwargs):
    """
    KuibaPhotoStoreItemAttrEnricher
    ------
    从 photo_store 获取 item_attr

    参数配置
    ------
    `attrs`: [list] 将要获取的 item attr 列表。

    `photo_store_kconf_key`: [string] 指定 photo_store 相关配置的 kconf_key (具体选用哪个 kconf 配置请先咨询 @林鹏鹏)

    `item_type`: [string] 请求分布式索引时的 item type，默认为 ITEM_TYPE_PHOTO，设为 AUTO 则使用 leaf 中的 item_type。

    `page_type`: [string] 请求分布式索引时的 page type，默认为 PAGE_TYPE_KUIBA_PREDICT。

    `item_key_from_attr`: [string] 从给定的 attr 读取 item_key，默认为空，使用 RecoResult 里的 item_key。

    `attr_prefix`：[string] 存入 item_attr 时增加前缀，默认为空。

    `attr_postfix`：[string] 存入 item_attr 时增加后缀，默认为空。

    `no_overwrite`: [bool] 仅当 attr 不存在时写入，默认为 false。

    调用示例
    ------
    ``` python
    .get_item_attr_by_kuiba_photo_store(attrs=["pId", "aId"])
    ```
    """
    self._add_processor(KuibaPhotoStoreItemAttrEnricher(kwargs))
    return self
  
  def kuiba_tower(self, **kwargs):
    """
    KuibaCalcUserEmbeddingAttrEnricher
    -----
    通过 kuiba-tf 计算 user embedding

    在非 common infer 服务中配置 kuiba_tower processor, 需要在编译脚本配置环境变量 KRP_INFER_KUIBA_USE_RECO_MODEL=true 
    
    参数配置
    -----
    `common_attrs`: [list] 额外提供给 kuiba tower 使用的 common_attr, 默认为空
    
    `use_common_attr_in_request`: [bool] 是否使用 request 带的全部 common_attr, 默认为 false

    `sample_list_common_attr_key`: [string] sample_list 的 common_attr key
    
    `exclude_common_attrs`: [list] 需要过滤的 common_attr, 默认为空.
  
    `attr_rename_map`: [map[string, string]] 支持在把 common_attr 发送到 kuiba_tower 的时候进行重命名, 不改变 context 内部 common_attr; 默认为空.
    
    `save_user_embedding_to_attr`: [string] 将 user embedding 保存到 common attr

    `layer`: [string] 获取哪一层的 user embedding

    `tower_conf`: [object] tower 配置, 相当于原 kuiba tower 服务中 dynamic_json 配置, [kuiba tower wiki](https://docs.corp.kuaishou.com/k/home/<USER>/fcACBNZD5sqwhJXY55FfFy0Pj)
      - `model_queue`: [object]
          - `shard_num`: [int]
          - `queue_name`: [string] btq queue name
      - `fast_tower`: [object]
          - `loss_kconf_path`: [string] 填写 tower layer 相关配置的 kconf 路径
      - `ps_memory`: [int]
      - `ps_capacity`: [int]
      - `empirical_key_num`: [int]
      - `empirical_wait_seconds`: [int] 

    调用示例
    -----
    ``` python
    .kuiba_tower(
      common_attrs=["device_id", "is_new_user"],
      use_common_attr_in_request=true,
      attr_rename_map={"user_id" : "userId"},
      save_user_embedding_attr_name = "kuiba_user_embedding",
      layer = "source_embedding",
      tower_conf=dict(
        fast_tower=dict(
          "loss_kconf_path": "reco.offline.fountainPairTowerConfig"
        ),
        model_queue=dict(
          queue_name="krp_fountain_pair-tower-v1_20200413",
          shard_num=1)))
    ```
    """
    self._add_processor(KuibaCalcUserEmbeddingAttrEnricher(kwargs))
    return self

  def kuiba_predict_item(self, **kwargs):
    """
    KuibaPredictItemAttrEnricher
    -----
    通过 kuiba-tf 模型进行预估
    
    参数配置
    -----
    `common_attrs`: [string array] 额外提供给 kuiba-tf 预估使用的 common_attr, 默认为空
    
    `use_common_attrs_in_request` : [bool] 是否使用 request 带的全部 common_attr, 默认为 false

    `exclude_common_attrs` : [string array] 预估时需要过滤的 common_attr, 默认为空.
  
    `item_attrs`: [string array] 提供给 kuiba-tf 预估使用的 item_attr， 必须设置.

    `use_item_attrs_in_request`: [bool] 是否使用 request 带的全部 item_attr, 默认为 false

    `loss`: [string array] 提供给 kuiba-tf 预估的 loss 数组，必须设置.

    `loss_default_value`: [float array|float] 每个 loss 对应的默认值，必须设置. 支持两种方式设置，配置成与 loss 个数对应的 list; 或者配置成单值，所有 loss 共享默认值.

    `attr_rename_map` : [map[string,string]] 支持在把 common_attr 发送到 kuiba-tf 预估的时候进行重命名, 不改变 context 内部 common_attr, 只影响发送到 kuiba-tf; 默认为空.

    `output_prefix` : [string] 保存的预估结果 xtr 的作为 item_attr 的前缀, 默认值为"". 最终输出的 item_attr 为 $(ouput_prefix)$loss, 例如 "mix_ctr".
    
    `common_predict_conf` : [object] common_predict_service 配置, 
      -`predictor_type` [string]: 使用的预估服务类型
      -`remote_ps` [object] : 远程 PS 的配置。目前只能单独配置远程 PS, 后续支持配置单独的 embedding processor
          -`service_name`: [string] 远程 PS 的 kess name.
          -`shard_num`: [int] 远程 PS 的 shard num.

    调用示例
    -----
    ``` python
    .kuiba_predict_item(
      common_attrs=["device_id", "is_new_user"],
      use_common_attrs_in_request=true,
      attr_rename_map={"user_id" : "userId"},
      item_attrs=["pid", "sample_weight", "pctr"],
      loss=["ctr", "cvr"],
      loss_default_value=[0.0, 0.0],
      output_prefix="mix_",
      common_predict_conf=dict(
        predictor_type="TFPredictor",
        remote_ps=dict(
          service_name="grpc_MerchantLiveGoodsPs",
          shard_num=8)))
    ```
    """
    self._add_processor(KuibaPredictItemAttrEnricher(kwargs))
    return self

  def send_to_kuiba_learner(self, **kwargs):
    """
    KuibaExtractedSampleObserver
    -----
    从 context 提取特征 转为 kuiba::ExtractedSamplePackage 结构，通过 blocking_queue 给 kuiba learner

    注意事项
    ------
    - 该 processor 仅允许使用在 kuiba-learner 的离线训练配置里，禁止用到线上，会导致阻塞引发严重的线上故障。

    参数配置
    ------
    `attrs`: [list] 从给定的 common attr 或 item attr 获取 double (list) 或 int64 （list) 填到 ExtractedSamplePackage->sample->attr

    `parameter_attr`: [string] 从给定 common attr 或 item attr 获取 sign，填到 ExtractedSamplePackage->sample(or common_paramter) 的 ExtractedParameter。

    `time_ms_attr`: string 从 item attr 中提取样本的时间戳

    `labels`: [string] 填充需要发送给 kuiba_learner 的 label names

    调用示例
    ------
    ``` python
    .send_to_kuiba_learner(
        parameter_attr = "parameters",
        attrs = ["click", "follow", "like", "long_view", "finish", "play"],
        time_attr_name = "_REQ_TIME_",
        labels = ["live_watch_live_time", "live_comment"])
    ```
    """
    self._add_processor(KuibaExtractedSampleObserver(kwargs))
    return self

  def retrieve_from_raw_sample_package(self, **kwargs):
    """
    KuibaRawSamplePackageRetriever
    ------
    从 RawSamplePackage 触发出样本。注意这个 processor 会修改 user id、device id、requeset time。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 RawSamplePackage。

    `labels`: [list] label 到 attr 的映射关系，用 dict 存储。

    `pid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 photo id，抽取后将作为该样本的 item_id，默认为 pId。

    `uid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 UserId 值，默认为 uId。

    `device_id_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 DeviceId 值，默认为 dId。

    `save_locale_to`: [string] 用于存储 locale 的 attr 的名字，默认为 rLocale。

    `save_channel_to`: [string] 用于存储 channel 的 attr 的名字，默认为 rChannel。

    `item_type`: [int] 触发的 item type，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `skip_sample_without_labels`: [bool] 是否跳过没有label（或所有 label 都不在 labels 里）的样本，默认为 true。

    `save_common_attr_names_to`: [string] 存储 common_attr 名字列表的 common attr。

    `save_next_common_attr_names_to`: [string] 存储 next_common_attr 名字列表的 common attr。

    `save_item_attr_names_to`: [string] 存储 item_attr 名字列表的 common attr。

    `compatible_labels`: [list] 如果老数据流中正样本的 label value 是 0，那么为了使老数据能正常训练，这里需要把这些正样本写进去；后续还是建议把 label value 改正确。

    `time_unit`: [string] RawSamplePackage 的 timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    `use_sub_biz`: [bool] label定义中是否包含sub biz，默认为 false。

    调用示例
    ------
    ``` python
    .retrieve_from_raw_sample_package(
      from_extra_var="raw_sample_package",
      labels=[
        dict(label="China,nearby,realshow", attr="realshow"),
        dict(label="China,nearby,click", attr="click"),
      ],
      save_common_attr_names_to="common_attrs"
      save_next_common_attr_names_to="next_common_attrs"
      save_item_attr_names_to="item_attrs")
    ```
    """
    self._add_processor(KuibaRawSamplePackageRetriever(kwargs))
    return self

  def retrieve_from_kuiba_compress_sample(self, **kwargs):
    """
    KuibaCompressSampleRetriever
    ------
    从 kuiba 压缩样本抽特征, 用于训练

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 CompressCommonPredictRequest

    `item_type`: [int] 触发的 item type，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `item_slots`: [string] 输出 item slots 的位置, 默认为 slots

    `item_signs`: [string] 输出 item signs 的位置, 默认为 signs

    调用示例(一般为自动脚本填写)
    ------
    ``` python
    .retrieve_from_kuiba_compress_sample(
      from_extra_var="kuiba_compress_sample_str",
      kuiba_sample_filter=xxx,
      kuiba_loss_function_sample_filter=xxx,
      kuiba_loss_function=xxx,
      slots_mapping=xxx,
      config=xxx,
      item_slots="item_slots",
      item_signs="item_signs"
      )
    ```
    """
    self._add_processor(KuibaCompressSampleRetriever(kwargs))
    return self
  
  def retrieve_from_kuiba_predict_request(self, **kwargs):
    """
    KuibaPredictRequestRetriever
    ------
    从 CompressCommonPredictRequest 触发出样本。填写 common attr 和 item attr 用于特征抽取

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 CompressCommonPredictRequest

    `reason`: [int] 触发的 reason，默认为 0。

    `attr_list`: [string] 存储需要存储 common_attr 和 item_attr 的属性名

    调用示例
    ------
    ``` python
    .retrieve_from_kuiba_predict_request(
      from_extra_var="compress_common_predict_request",
      attr_list=["uId", "pId", "aId"])
    ```
    """
    self._add_processor(KuibaPredictRequestRetriever(kwargs))
    return self

  def fetch_kuiba_third_party_data(self, **kwargs):
    """
    KuibaThirdPartyDataAttrEnricher
    ------
    读取 kuiba_third_party 支持的外部数据

    参数配置
    ------
    `config`: [dict] kuiba_third_party 的配置。

    `save_data_to_attr`: [string] 存储结果的 item_attr

    调用示例
    ------
    ``` python
    .fetch_kuiba_third_party_data(
      config={
        "decoder": "embedding",
        "key_prefix": "ce_",
        "storage": "memcache",
        "zk_conn_strs": [
          "config.zk.cluster.zw:2181",
          "config.zk.cluster.zw:2181",
        ],
        "zk_full_path": [
          "/ks2/memcached/mmuFeature4WatchTime1/_ZW",
          "/ks2/memcached/mmuFeature4WatchTime2/_ZW",
        ],
      },
      save_data_to_attr="mmu_embedding")
    ```
    """
    self._add_processor(KuibaThirdPartyDataAttrEnricher(kwargs))
    return self

  def build_raw_sample_package(self, **kwargs):
    """
    KuibaRawSamplePackageEnricher
    -----
    从 context 提取特征 转为 kuiba::RawSamplePackage 结构，保存在 common attr 中

    参数配置
    ------
    `common_attrs`: [list][动态参数] 从给定的 common attr 填充入 RawSamplePackage 的 common_attr

    `item_attrs`: [list][动态参数] 从给定的 item attr 填充入 RawSamplePackage 中每个 sample 的 attr

    `timestamp_attr`: [string] 从给定的 common attr 填充入 RawSamplePackage 的 timestamp 默认为当前时间

    `save_result_to`: [string] 将构造出来的 RawSamplePackage 存入指定的 common attr
    
    `enable_auto_merge`: [bool] 是否开启算子合并, 如果开启会将 `save_result_to`、`timestamp_attr` 配置相同的算子合并为一个算子，合并项为 `common_attrs` 以及 `item_attrs`。此模式需要 service.AUTO_INJECT_META_DATA = True 才能生效

    调用示例
    ------
    ``` python
    .build_raw_sample_package(
        common_attrs = ["uId"],
        item_attrs = ["pId"],
        timestamp_attr = "requst_time",
        save_result_to = "raw_sample_package")
    ```
    """
    self._add_processor(KuibaRawSamplePackageEnricher(kwargs))
    return self

  def extract_kuiba_sample_attr(self, **kwargs):
    """
    KuibaSampleAttrEnricher
    -----
    从 context 提取 kuiba::PredictItem , 并将其中的 SampleAttr 存入 context 中

    参数配置
    ------
    `is_common_attr`: [bool] 是否是 common attr 操作

    `predict_item`: [string] kuiba::PredictItem 获取字段名

    `output_attrs`: [list[string]] 需要从 PredictItem 中抽取的 attr，为空则抽取全部的 attr

    调用示例
    ------
    ``` python
    .extract_kuiba_sample_attr(
        output_attrs = ["uId", "dId"],
        predict_item = "kuiba_user_attr",
        is_common_attr = True)
    ```
    """
    self._add_processor(KuibaSampleAttrEnricher(kwargs))
    return self

  def calculate_attr_hit_count(self, **kwargs):
    """
    KuibaAttrHitCountEnricher
    -----
    计算 hit count，将结果分 id list 和 count list 分别写入 item attr 中

    参数配置
    -----
    `attrs`: [list[dict]] 输入的 attr 配置，list 中的每个 dict 包括:
    - `hit_list` [string] 必配，common attr 名，类型为 int list
    - `hit_score_list` [string] 选配，common attr 名，类型为 double list。若配置则 count list 结果来自这个 attr
    - `target_list` [list[string]] 必配，item attr 名，类型为 int list

    `output_prefix`: [string] 输出的 item attr 前缀，结果以 {output_prefix}+{target_list}+"_hit" 和 {output_prefix}+{target_list}+"_hit_count" 作为 attr name 写入 item attr 中，默认为空

    调用示例
    -----
    ```python
    .calculate_attr_hit_count(
        attrs = [
          {
            "hit_list": "hit_list1",
            "target_list": ["target_list1", "target_list2"]
          },
          {
            "hit_list": "hit_list2",
            "hit_score_list": "hit_score_list1",
            "target_list": ["target_list3"]
          }
        ],
        output_prefix: "KuibaAttrHitCountEnricher_")
    ```
    """
    self._add_processor(KuibaAttrHitCountEnricher(kwargs))
    return self

  def extract_drm_tower_attr(self, **kwargs):
    """
    kuibaDrmTowerAttrEnricher 
    ---- 
    抽取 drm tower需要的attr，hard code 写死，drm专用
    参数配置
    ----
    - `is_common_attr` [bool] 为true时抽取user侧特征，为false时抽取item侧特征
    - `user_info_attr` [string]  存储user info的common attr name
    - `photo_info_attr` [string] 存储photo info的item attr name
    """
    self._add_processor(KuibaDrmTowerAttrEnricher(kwargs))
    return self

  def extract_kuiba_item_parameter_by_fused_converter(self, **kwargs):
    """
    KuibaParameterFusedItemAttrEnricher
    ------
    类似 KuibaParameterAttrEnricher, 但是使用高性能的 FusedConverters 抽取 Parameter 放到 item attr 里

    参数配置
    ------
    `slots_output`: [string] 输出 slots 的位置.

    `parameters_output`: [string] 输出 parameters 的位置.

    `config`: [dict] 传给 ParameterConfig 的配置

    调用示例
    ------
    ``` python
    import json
    .extract_kuiba_item_parameter_by_fused_converter(
        slots_output="slots",
        parameters_output="parameters",
        config=available_fused_converter(json.load(open("./parameter_config.json"))))
    ```
    """
    self._add_processor(KuibaParameterFusedItemAttrEnricher(kwargs))
    return self
