#!/usr/bin/env python3
# coding=utf-8

"""
filename: kuiba_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for kuiba
author: <EMAIL>
date: 2020-08-26 17:34:00
"""
import operator

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class KuibaExtractedSampleObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "send_to_kuiba_learner"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", False):
      if "attrs" in self._config:
        attrs.update(self._config["attrs"])
      if "parameter_attr" in self._config:
        attrs.add(self._config["parameter_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", False):
      if "parameter_attr" in self._config:
        attrs.add(self._config["parameter_attr"])
    return attrs
