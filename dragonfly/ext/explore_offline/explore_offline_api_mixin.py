#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_offline_api_mixin.py
description:
author: t<PERSON><PERSON><PERSON>@kuaishou.com
date: 2022-06-15 15:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .explore_offline_enricher import *

class ExploreOfflineApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含双列发现页内外流离线接口，如模型特征及训练等业务相关的 Processor API接口

  背景：目前主要为 双列发现页内外流 场景提供召回及排序服务所需 dragon 插件，如 排序特征抽取，样本生成等
  
  现有维护人：tianshiqi zhangxin25
  """
  def explore_reco_log_kuiba_sample_enricher(self, **kwargs):
    """
    ExploreRecoLogKuibaSampleOfflineEnricher
    ------
    离线抽取特征

    参数配置
    ------
    `from_extra_var`: [string] reco log pb

    `sample_kconf_name`: [string] 特征kconf文件

    `save_result_to`: [string] 保存的common attr

    调用示例
    ------
    ``` python
    .explore_reco_log_kuiba_sample_enricher(
      from_extra_var="reco_log_package",
      sample_kconf_name="reco.fountain.fountainMetaRetainConfig",
      save_result_to="raw_sample_package"
    )
    ```
    """
    self._add_processor(ExploreRecoLogKuibaSampleOfflineEnricher(kwargs))
    return self

  def explore_fetch_dynamic_cluster_centers(self, **kwargs):
    """
    ExploreFetchDynamicClusterCentersEnricher
    """
    self._add_processor(ExploreFetchDynamicClusterCentersEnricher(kwargs))
    return self

  def explore_cluster_via_dynamic_centers(self, **kwargs):
    """
    ExploreClusterViaDynamicCentersEnricher
    """
    self._add_processor(ExploreClusterViaDynamicCentersEnricher(kwargs))
    return self
  
  def explore_extend_item_feature_enricher(self, **kwargs):
    """
    ExploreExtendItemFeatureEnricher
    """
    self._add_processor(ExploreExtendItemFeatureEnricher(kwargs))
    return self
