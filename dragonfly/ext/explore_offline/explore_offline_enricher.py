#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_offline_enricher.py
description:
author: <PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2022-06-15 15:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class ExploreRecoLogKuibaSampleOfflineEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_reco_log_kuiba_sample_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("sample_kconf_name"))
    attrs.add(self._config.get("from_extra_var"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_result_to"))
    return attrs

class ExploreFetchDynamicClusterCentersEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_fetch_dynamic_cluster_centers"

  @strict_types
  def depend_on_items(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_type", "cluster_num", "item_emb_dim", "iteration_num"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("common_embs_attr"))
    attrs.add(self._config.get("pid_list_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_attr"))
    return attrs

class ExploreClusterViaDynamicCentersEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_via_dynamic_centers"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_num", "item_emb_dim"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("cluster_centers_attr"))
    attrs.add(self._config.get("duration_attr"))
    attrs.add(self._config.get("living_attr"))
    attrs.add(self._config.get("picture_attr"))
    attrs.add(self._config.get("check_point"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_emb_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["export_item_attr"]:
        attrs.add(self._config.get(name))
    return attrs

class ExploreExtendItemFeatureEnricher(LeafEnricher):
  """
  ExploreExtendItemFeatureEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_extend_item_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("pxtr_percentile_queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("percentile_lits", ""))
      attrs.add(cfg.get("pxtr_percentile", ""))
    return attrs