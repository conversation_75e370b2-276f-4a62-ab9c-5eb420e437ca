#!/usr/bin/env python3
# coding=utf-8
"""
filename: uni_predict_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, uni_predict api mixin
author: <EMAIL>
date: 2021-09-03 16:34:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .uni_predict_enricher import *
from .uni_predict_gpu_live_retriever import *

class UniPredictV2ApiMixin(CommonLeafBaseMixin):
  """
  - 模块介绍: UniPredictV2是预估服务，底层使用TensorRT或者TensorFlow进行计算，支持GPU和CPU, 是在UniPrdict上的一次不完全兼容的重构。
    特别是使用GPU预估时，经过batching system的batching处理后，把多个请求batching起来再进行计算，可以充分发挥GPU设备大吞吐的优势，提高qps.
    - 重构已经完成部分：
      TensorRT dynamic shape的支持;
      使用TensorRT时支持在Batch维度做reduce操作;
      MemoryContext和Infer Context比例由1:1改成N:1;
      模型参数更新机制的升级;
    - 重构进行中部分：
      弹性的调度策略,包括batching_size的动态调度、ctx_per_device的动态调度;
      使用TensorRT 9进行infer, 目前正在测试TensorRT 9的性能;
      GPU embedding cache的支持;
      numa aware的改造;
    - UniPredictV2和UniPredict不兼容部分：
      使用TensorRT infer时，不支持implicit batch;
      使用TensorFlow infer时， implicit_batch = True 需要改为 dynamic_shape = True;

  - 使用场景:
  如果有以下需求可以使用UniPredictV2:
  需要获得比UniPredict更高的qps和更低的延时，需要在拥有多个numa node的CPU上利用numa aware来充分发挥硬件性能，需要使用TensorRT的dynamic shape, 需要在Batch维度做reduce操作,
  需要使用TensorRT 9进行infer, 需要使用GPU embedding cache, 需要使用新的模型参数更新机制.
  相比于Mio模块下的Predict, 该模块使用uni-predict-v2模块，可以使用explicit batch模式构建计算图, 能够兼容大部分的模型. 优化了batching和调度代码, 有相对更好的性能表现.
  
  - 维护人: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;
  <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;
  <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>

  - 维护团队: 算法引擎部/计算引擎架构组
  """

  def uni_predict_fused(self, **kwargs):
    """
    UniPredictFusedItemAttrEnricher
    ------
    通过 RPC 访问 EmbeddingServer 拿到 sign 对应的 embedding 参数,
    然后按照指定 slot sign 配置填入 tensor 中, 最后调用 uni-predict 做预估,
    将结果回填 Attr. 功能类似于 mio::fetch_mio_embedding + mio::mio_predict,
    性能更佳

    参数配置
    ------
    `embedding_fetchers`: [list] of [dict] embedding 拉取的配置, 目前支持从 BtEmbeddingServer 和 ColossusdbEmbeddingServer 拉取. 没有拉取到的 tensor 会 fallback 到 common/item attr 中去寻找, 都没有则填 0

    `embedding_fetchers::fetcher_type`: [string] embedding 拉取的类型. 目前支持 {"BrpcBtEmbeddingServerFetcher", "BtEmbeddingServerFetcher", "ColossusdbEmbeddingServerFetcher", "RdmaColossusdbEmbeddingServerFetcher", "LocalEmbeddingFetcher"}

    fetcher_type == "LocalEmbeddingFetcher" 没有 processor 特化配置 (embedding table 被置于 json 顶层了)

    以下为 fetcher_type == "ColossusdbEmbeddingServerFetcher" 时的对应配置，下游服务信息会从自定义的路由表中获取，请求使用统一存储封装好的 client

    `embedding_fetchers::colossusdb_embd_model_name`: [string] 下游 colossusdb embedding server 的模型名，一般是: {模型名称}.
      获取方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH).
      之前又名为 colossusdb_embd_service_name, 配置 model_name 和 service_name 两者等价, 任选其一即可。

    `embedding_fetchers::colossusdb_embd_table_name`: [string] 下游 colossusdb embedding server 的表名，如 test-table

    `embedding_fetchers::use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_model_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    以下为 fetcher_type == "BtEmbeddingServerFetcher" 时的对应配置，请求使用 kess client

    `embedding_fetchers::kess_service`: [string] 下游 bt embedding server 的 kess name

    `embedding_fetchers::kess_cluster`: [string] 下游 bt embedding server 的 kess 集群名, 默认 "PRODUCTION"

    `embedding_fetchers::thread_num`: [int] 获取 embedding 的 kess client 线程数, 默认 1

    `embedding_fetchers::shards`: [int] 下游 bt embedding server 的 shard 数量

    `embedding_fetchers::client_side_shard`: [bool] 开启后根据 sign 的数值发到指定 bt embedding server 的 shard, 否则会把所有 sign 发到所有 shard

    `embedding_fetchers::hash_input_attr`: [string] 根据指定 common attr 值计算 hash 决定请求发往哪个副本，某个确定的值总是会请求到固定的某一个副本。
      默认为空，此时仍然是随机选择副本发送请求。

    以下为所有 fetcher_type 的通用配置，包括 input 配置、共用的请求配置、cache 配置等等

    `embedding_fetchers::slots_inputs`: [list] 从哪些 item attr 读取 slots 信息

    `embedding_fetchers::parameters_inputs`: [list] 从哪些 item attr 读取 parameters 信息

    `embedding_fetchers::common_slots_inputs`: [list] 从哪些 common attr 读取 slots 信息

    `embedding_fetchers::common_parameters_inputs`: [list] 从哪些 common attr 读取 parameters 信息

    `embedding_fetchers::timeout_ms`: [int] 从下游 embedding server 拉 embedding 的超时时间设置, 默认 10

    `embedding_fetchers::slots_config`: [list of dict] 后续 predict 时各个 attr input 需要的 slot 配置. 和 mio::fetch_mio_embedding 基本相同, 但是取消了 sentry, 增加了 compress_group，拓展了 dtype。dict 中各个字段的含义为:
          `input_name`: [str] tensor 的 name;
          `dim`: [int] 每个 slot 的 dim;
          `dtype`: [str] embedding 的数据类型，支持mio_int16/scale_int8/scale_int16/float16/float32，默认为 mio_int16;
          `common`: [bool] 是否为 user 侧特征, 启用 batching 的时候不要使用 `common` 为 true，使用`compress_group="USER"`替代;
          `compress_group`: [str] 是否该 tensor 可以根据 compress_group 来复用，例如 common 就是 compress_group 根据 "USER" 来复用，也就是请求里所有 item 都可以复用这个 tensor, 目前只实现 compress_group = "USER" 这个规则;
          `expand`: [int] 该 slot 的 dim 是否要拓展为 expand 倍;
          `sized`: [int] 每个 item/user 的 tensor 最后加一位, 表示这个 item tensor 的所有 slot 抽取到 sign 的个数;
          `weights`: [string or array of string] 每个 slot 对应的权重配置;
          `slots`: [int or str or int list] 该 tensor 配置的所有 slot.

    `embedding_fetchers::max_signs_per_request`: [int] 向 embedding server 发的 request 中每个 request sign 个数限制, 默认 0 代表没有限制
    `embedding_fetchers::dynamic_timeout`: [bool] 暂时支持 colossusdb emb server. 打开动态超时，目前仅支持 P999 截断
    
    `embedding_fetchers::sample_duration`: [int] 暂时支持 colossusdb emb server. 动态超时采样窗口时间，单位 s

    `embedding_fetchers::cache_config`: [dict] 针对下游 embedding server 建立的本地 cache. cache config 里包含一系列配置, 不填则会 fallback 到默认值

    `embedding_manager_type`: [string] embedding 处理方式，支持 "parallel_fetch" 和 "post_parallel_fetch" 两种，默认为 "parallel_fetch"。"parallel_fetch" 的处理流程是: Collect Signs -> 并行获取 embedding -> batching -> sum_pooling; "post_parallel_fetch" 的处理流程是： batching —> 并行( collect_signs + 获取 embedding + sum_pooling)
                               
    `graph`: [string] 计算图 uri, 只支持 base64:// 这种 schema

    `key`: [string] 模型唯一标识符，如果有多个模型需要指定不同的 key

    `inputs`: [list] of [dict] 模型输入配置，包括 `attr_name` [string], `tensor_name` [string], `common` [bool] (optional 默认为 False), `dim` [int]. 将根据 `common` 从上述的 slots_config (如果没有则从 common attr 或 item attr) 的 `attr_name` 取出参数，构造成宽度为 `dim` 的矩阵放到计算图的 `tensor_name` 中执行模型。
              inputs::dtype [string], 指定输入模型的 input tensor 的数据类型，支持 float32 和 float16 ， 默认为 float32, enable_fp16 为true时会覆盖本配置，input 将强制为 float16

    `outputs`: [list] of [dict] 模型输出配置，包括:
                `attr_name` [string],
                `tensor_name` [string]，将计算图中的 `tensor_name` 放到 item attr 的 `attr_name` 中,
                `common` [bool], 结果 tensor 是否输出至 common attr
                `dim` [int], 结果 tensor 的维度
                `type` [int], 结果类型，
                      0: 正常结果，第 0 维限制为common/item batch, 维度必须为 2，
                      1: 用于 dump 模型内部 tensor，tensor shape 不限，只能 dump 至 common attr，dump 时视 tensor 为 dim 长的一维数组（因为 tensor 第 0 维不一定是 common/item batch）

    `param`: [list] of [dict] 计算图的 dense 参数配置, 主要需要 `name` [string] 指定 dense 参数名字, `rown`, `coln`, `dim` 指定参数 shape. list 中参数顺序需要和 btq 一致.
             param::dtype [string], 指定模型 dense param 的数据类型，支持 float32 和 float16 ， 默认为 float32, enable_fp16 为true时会覆盖本配置，param 将强制为 float16 (非开发者确认时、param.dtype 建议统一)

    `queue_prefix`: [string] 接收模型的 BTQueue topic 前缀.

    `debug_tensor`: [bool] 把 input tensor 写进 attr 中. 因为拉取 embedding 和 predict 在同一 processor 并做 0 拷贝处理, 为了方便 debug attr 设置的此选项. 注意, 假如当前 processor 在 subflow 内部, 尽可能在同样 subflow 内部打印/导出这个 attr. 默认 false

    `keep_invalid_value`: [bool] 如果预估过程中出现了 invalid value (nan / inf) 如何处理。如果为 True, 则将预估值原样返回; 如果为 false, 则舍弃该 item 的预估值. 默认为 True

    `using_onnx_style_name`: [bool] 默认 false，onnx model 内部的 inputs / params name 是否保留 onnx 风格，false 的话将 name 的 ":0" 部分删除，true 则强制加上

    `model_loader_config`: [dict] 使用哪种类型的 model_loader, 及其参数. 决定了怎么读取 btq 参数和使用哪种 executor 执行计算图. 具体配置在下列出

    `model_loader_config::rowmajor`: [bool]  BTQueue 传输过来的参数是否是 Row-Major 的，默认为 False, 即 Column-Major. (当前 KAI 默认 Row-Major 的参数

    `model_loader_config::type`: [string] 使用哪种 model_loader. 目前支持:
        1. "MioTFExecutedByTensorRTModelLoader", 意思是 mio-tf 格式的 dense 参数(kai c++ 默认格式) 读取, 使用 TensorRT 作为 executor 执行计算图.
        2. "MioTFExecutedByTensorFlowModelLoader", 使用 Tensorflow 作为 executor 执行计算图, 其余同前者. (下文 batch_config 中的 BatchTensorflowTask 仅在MioTFExecutedByTensorFlowModelLoader 下生效)
        3. "MioTFExecutedByTVMModelLoader", 使用 TVM 执行计算图，其余同 1. （下文 batch_config 中的 BatchTVMTask 仅在 MioTFExecutedByTVMModelLoader 下生效）
        4. "MioTFExecutedByOnnxTRTModelLoader", 使用 onnx 格式的 model build engine 执行计算图，其余同 1

    `model_loader_config::executor_batchsizes`: [list] of [int] Executor 需要固定几个 batchsize 运行. 如果不指定, 则系统会根据 batching 参数等配置使用默认 policy 生成.

    `model_loader_config::implicit_batch`: [bool][deprecated] implicit batch 模式下不需要上述明确的 batchsize, 老 TensorRT 版本只支持 implicit_batch. 默认 false

    `model_loader_config::dynamic_shape`: [bool] 运行时动态 batch size, 默认 false

    `model_loader_config::enable_fp16`: [bool] 使用 FLOAT16 来做预估, 会导致 input tensor 格式变成 FLOAT16, 速度会快但是可能结果有误差甚至 NAN. 默认 false
    
    `model_loader_config::enable_bf16`: [bool] 使用 BFLOAT16 来做预估, 会导致 input tensor 格式变成 BFLOAT16, 速度会快但是可能结果有误差. 默认 false, 该选项仅对 `model_loader_type` 为 `MioTFExecutedByOnnxTRTModelLoader` 有效
    
    `model_loader_config::strongly_typed_network`: [bool] 是否开启强类型网络, 默认为 false. 开启将完全依照计算图精度进行计算, 不会额外加入 cast, 适合混合精度, 注意此配置与 enable_fp16 / enable_bf16 冲突. 该选项仅对 `model_loader_type` 为 `MioTFExecutedByOnnxTRTModelLoader` 有效

    `model_loader_config::force_input_tensor_fp32`: [bool] 强制 input tensor 为 FLOAT32. 即使 enable_fp16 开启, 该选项也会强制 input tensor 为 FLOAT32 格式, 方便调用. 默认 false

    `model_loader_config::receive_dnn_model_as_macro_block`: [bool] 按 MacroBlock 从 BTQueue 接收模型数据. 默认为 false

    `model_loader_config::workspace_size`: [float] 构建预估 engine 时使用的最大 workspace size，单位 GB，若不配置默认使用 4。仅对 `model_loader_type` 为 `MioTFExecutedByTensorRTModelLoader`, `MioTFExecutedByOnnxTRTModelLoader` 有效

    `model_loader_config::enable_xla`: [bool] 是否启用 XLA，仅对 `model_loader_type` 为 `MioTFExecutedByTensorFlowModelLoader` 且为 GPU 预估有效，开启该选项需要 `model_loader_config::implicit_batch` 为 false

    `model_loader_config::reset_subnormal_to_zero`: [bool] 是否将 subnormal dense param 设置为 0, 默认为 false; 目前仅当 enable_fp16 为 true 时对 dense param 转 float16 格式的过程进行调整

    `model_loader_config::promote_compute_precision`: [bool] 针对 enable_fp16 为 True 的情况，是否采用 float 进行计算，并在 cast 时进行饱和处理。仅对 MioTFExecutedByTVMModelLoader 生效, 默认为 False
    
    `model_loader_config::use_cutlass`: [bool] 是否使用 cutlass, 仅对 MioTFExecutedByTVMModelLoader 生效, 默认为 False

    `model_loader_config::skip_watch_update`: [bool] 是否跳过从 btq 获取模型 dense 参数, 仅适用于 ad 场景
    
    `batching_config`: [dict] batching 系统的一系列配置. batching 系统会把并行的请求收集起来后, 统一给到 Executor 执行, 以提高 GPU 利用率. 具体配置会在下列出

    `batching_config::alignment`: [int] batching 后每个 tensor 需要的 alignment 字节数. 和使用的 Executor 和是否使用 GPU 有关. 如果不设置则会读 FLAGS_batching_option_alignment, 默认 256

    `batching_config::batch_timeout_micros` [int] batching 收集请求的等待时间, 也就是每个请求在 batching 系统中最大的等待时间. 如果不设置则会读 FLAGS_batching_option_batch_timeout_micros, 默认 0, 意思是不使用 batching

    `batching_config::max_batch_size`: [int] batching 时最大的 batchsize 设置. 如果不设置则会读 FLAGS_batching_option_max_batch_size, 默认 512

    `batching_config::max_enqueued_batches`: [int] batching 时最大收集的请求数. 如果不设置则会读 FLAGS_batching_option_max_enqueued_batches, 默认 16

    `batching_config::batch_task_type`: [string] batching 时使用的 task type, 不同 task type 对应的 batching kernel 不一样. 目前支持 {"BasicBatchingTask"(CPU SumPooling, only TRT or TF on GPU), "BatchGPUMergeTask"(GPU SumPooling, only TRT), "BatchTensorflowTask" (TF Inferring, only TF on CPU)}, 默认 "BasicBatchingTask". 如果使用 Tensorflow CPU 部署则默认不可行，需要显式指定为 BatchTensorflowTask

    `batching_config::process_thread_num`: [int] 处理 batching task 的线程数量，默认为 2 * CPU 核数。建议在 MioTFExecutedByTVMModelLoader 设置为 2 * context_per_device.

    `executor_config`: [dict] 底层 engine 执行时的一系列配置。

    `executor_config::intra_op_parallelism_threads_num`: [int] 底层 engine 在执行时单个 OP 内部最大并行加速设置，仅对 `model_loader_config::type` 为 `MioTFExecutedByTensorFlowModelLoader` 有效，对于 CPU 预估推荐 32 或 64，对于 GPU 预估推荐 1 或 4。若不配置则从 `FLAGS_tensorflow_intra_op_parallelism_threads` 中读取，默认为 1。

    `executor_config::inter_op_parallelism_threads_num`: [int] 底层 engine 在执行多个 OP 时最大的并行加速设置，仅对 `model_loader_config::type` 为 `MioTFExecutedByTensorFlowModelLoader` 有效，对于 CPU 预估推荐 32 或 64，对于 GPU 预估推荐 1 或 4。若不配置则从 `FLAGS_tensorflow_inter_op_parallelism_threads` 读取，默认为 1。

    `executor_config::context_per_device`: [int] engine 在执行时每个 device 最大的 context 数。若不配置, CPU 预估从`tensorflow_executor_session_per_cpu_device` 读取，默认值为 0，当为 0 时会使用 CPU 核数作为该值；GPU 预估从 `FLAGS_tensorflow_executor_session_per_gpu_device` 读取，默认值为 4；TensorRT 预估会从`FLAGS_tensorrt_executor_ctx_per_gpu_device` 读取，默认值为 12。

    `executor_config::executor_per_flavor`: [int] 每个 batch tvm executor 的数量，仅对 MioTFExecutedByTVMModelLoader 生效，默认为 2.

    `executor_config::memory_size_per_context`: [int] engine 在执行时每个 device 使用的最大内存数，单位为`MB`。仅对 `model_loader_config::type` 为 `MioTFExecutedByTensorFlowModelLoader` 且为 GPU 预估时有效。若没有配置，则从`FLAGS_tensorflow_gpu_device_memory_mb` 中读取，默认为 200MB.

    `executor_config::enable_dynamic_context_num`: [bool] 是否开启动态分配每个 device 上 context 的数目，仅对 TF-GPU 生效，默认为 false，即不开启。开启时 context_per_device 无效，每个 context 占用显存仍由 memory_size_per_context 指定

    `executor_config::reserve_memory_per_device_mb`: [int] 开启动态分配 context 的数目时，需要用该配置指定非 context 占用的显存大小, 单位为 MB，默认为 0

    `executor_config::dynamic_targets`: [list of dict]  支持动态选择的预估目标的相关配置，默认为空、为空则保持全量目标预估模式; 
                                        dict 包含以下配置：
                                        targets: [list of int] 需要支持动态选择的预估目标在 outputs 中的序号列表, 
                                        weight: [float] 需要支持动态选择的预估目标的算力在整个预估服务中的占比，用于分配 GPU 算力；
                                                默认为 1.0，如果所有 weight 都为空，此时形同平均分配 GPU 算力

    `dynamic_targets_attr`: [string] 用于指定要预估的目标在 outputs 中的序号列表,  必须 为 executor_config::dynamic_targets 中配置的目标列表

    调用示例
    ------
    ``` python
    import yaml
    .uni_predict_fused(
      embedding_fetchers = [
        dict(
          fetcher_type="BtEmbeddingServerFetcher", 
          kess_service="grpc_someBtEmbeddingServer",
          shards=8,
          client_side_shard=True,
          slots_inputs=["item_slots"],
          parameters_inputs=["item_parameters"],
          common_slots_inputs=["common_slots"],
          common_parameters_inputs=["common_parameters"],
          slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"])
          max_signs_per_request=500,
          timeout_ms=50,
        )
      ],
      "input"=[
        {
          "attr_name": "user_input",
          "tensor_name": "user_input",
          "common": True,
          "dim": 128
        },
        {
          "attr_name": "photo_input",
          "tensor_name": "photo_input",
          "dim": 128
        }
      ],
      "outputs"=[
        {
          "attr_name": "ctr",
          "tensor_name": "mul:0"
        },
        {
          "attr_name": "ltr",
          "tensor_name": "Sigmoid:0"
        }
      ]
      "param"=[
        {
          "name": "user_param",
          "rown": 128,
          "coln": 256
        }
      ],
      "queue_prefix"="my_btq",
      "model_loader_config"={
        "type": "MioTFExecutedByTensorRTModelLoader",
        "rowmajor": True,
        "executor_batchsizes": [256, 128, 64],
        "implicit_batch": False,
        "receive_dnn_model_as_macro_block": True
      },
      "batching_config"={
        "batch_timeout_micros"=10000,
        "max_batch_size": 256,
        "max_enqueued_batches": 10
      },
      "debug_tensor"=False,
    )

    .uni_predict_fused(
      embedding_fetchers = [
        dict(
          fetcher_type="ColossusdbEmbeddingServerFetcher",
          colossusdb_embd_model_name="reco-arch-test-model",
          colossusdb_embd_table_name="test-table",
          slots_inputs=["item_slots"],
          parameters_inputs=["item_parameters"],
          common_slots_inputs=["common_slots"],
          common_parameters_inputs=["common_parameters"],
          slots_config=yaml.load(open("dnn_model.yaml"))["embedding"]["slots_config"]),
          max_signs_per_request=500,
          timeout_ms=50,
        )
      ], # 其他参数参考上面
    )
    ```
    """
    self._add_processor(UniPredictFusedItemAttrEnricher(kwargs))
    return self

  def uni_predict(self, **kwargs):
    """
    UniPredictItemAttrEnricher
    ------
    和上边 uni_predict_fused 参数几乎一样, 本 processor 只做 predict, 不拉取 embedding 和做 batching.
    由于没有 embedding 拉取功能, 需要保证 graph 的所有 inputs tensor 都存在了指定的 device 上, 具体:
    1. 需要保证 common_attr 中提前设置了一个名为 "uni_predict_set_device" 的 Device 指针.
    2. 所有 input tensor 的 buffer 都从 common_attr["uni_predict_tensor_cache"] 中读取,
       里面存了一个 map[input_name, device buffer]. 需要保证 device_buffer 和上述 Device 一致.

    开发本 processor 的目的是方便在高度定制化的服务中使用, 如 TDM, 部分粗排服务等.
    一般配合下边的 uni_predict_fetch_embedding 使用.

    注意: 普通场景使用依旧 uni_predict_fused, 更省资源.

    参数配置
    ------
    同 uni_predict_fused, 不需要配置 `embedding_fetchers` 和 `embedding_manager_type`

    `uni_predict_tensor_cache_attr`: [string] 预估时, 每个 input tensor 可以从一个 tensor_cache 中获得, 这里指定这个 tensor_cache 的 common_attr 名字, 默认 `uni_predict_tensor_cache`.

    `uni_predict_device_attr`: [string] 预估时, 需要指定每个 input tensor 位于哪个 device, 以及使用哪个 device 做预估, 这里指定这个 device 的 common_attr 名字, 默认 `uni_predict_set_device`.

    `save_outputs_to_common_attr': [bool] 是否将输出结果以 float list 存到 common_attr 里.

    """

    self._add_processor(UniPredictItemAttrEnricher(kwargs))
    return self

  def uni_predict_fetch_embedding(self, **kwargs):
    """
    UniPredictFetchEmbeddingAttrEnricher
    ------
    从远端 btEmbeddingServer, colossusDbEmbeddingServer 或者本地 local embedding server 拉取 embedding 并聚合成一个 tensor.
    聚合成的 tensor 存储在指定 device 上, 方便后续 predict 使用.
    假如没有指定 device_id, 会随机选一个可用的 device 做存储. 比如 `save_to_device` = "GPU", 此时如果上游没有指定具体哪张
    GPU 卡, 则会随机选择一张 GPU 卡. 随机选择 Device 后, 会记录到 "uni_predict_set_device" 这个 common_attr 中.

    参数配置
    ------
    需要填充上述 uni_predict_fused 参数里的 `embedding_fetchers` 和 `embedding_manager_type`.

    `max_batch_size`: [int] 每次请求的最大 item 数. 如果该 processor 后调用了 uni_predict, 需要和后续 uni_predict::batching_config::max_batch_size 一致, 必填.

    `save_to_device`: [string] 从 {"CPU", "GPU"} 中选择. 指定聚合好的 tensor 存在 CPU 或者 GPU 上, 必填.

    `enable_fp16`: [bool] 是否使用 fp16 存储聚合好的 tensor. 默认是 false, 使用 fp32 存储.

    `write_to_attr`: [bool] 是否额外把聚合好的 embedding 存到 item/common attr 里, 默认值 false.

    `key`: [string] 独一无二的标志, 方便监控, 必填.

    `uni_predict_device_attr`: [string] embedding 拉取后需要存到某个 device 上的内存里. 这个 attr 指定了从哪个 common_attr 拿这个 device 的设定. 默认值 `uni_predict_set_device`.

    `uni_predict_tensor_cache_attr`: [string] embedding 拉取后需要存到某个 tensor cache 结构上. 这个 attr 指定了该 tensor cache 指针在哪个 common_attr. 默认值 `uni_predict_tensor_cache`.

    `random_device_if_not_set`: [bool] 上述 device 假如没有设置, 是否随机选择一个 device_id. device_type 由 `save_to_device` 指定. 默认 true.

    `random_device_set_to_attr` [bool] 上述随机 device_id 选择后, 是否保存到 common_attr 里供后续使用. 具体哪个 common_attr 由 `uni_predict_device_attr` 指定. 默认 true.

    """
    self._add_processor(UniPredictFetchEmbeddingAttrEnricher(kwargs))
    return self

  def uni_gpu_live_retrieval(self, **kwargs):
    """
    UniGpuLiveRetriever
    ------
    直播全量召回 embedding 填充模式:
    从多张 GPU 卡获取已缓存全量 live embedding 的首地址、数据长度、dim 信息，并将 embeding 地址存放在 context 中。

    直播召回指定 item 模式:
    从 common attr 获取 item no list，查询 live embedding 缓存获取相应的 embedding key 作为 item key 进行召回 

    直播召回全部item模式:
    将 GPU 中缓存的 embedding 对应的 embedding key 作为 item key 全部召回

    参数配置
    ------
    `run_type` : [int] 0: embedding 填充模式 ，
                       1: 召回指定 item ,
                       2: 召回全部 item ,
                       other ：未定义, 无默认值

    `gpu_live_embed` : [dict] gpu live embedding 双 buffer 更新的相关配置

                       - `embedding_queue` : [list[string]] live embedding 更新流 btqueue

                       - `remove_queue` : [list[string]] 关播信号所在的直播索引 btqueue

                       - `embedding_queue_thread_num` : [int] live embedding 更新线程数, 默认 4

                       - `remove_queue_thread_num` : [int] 删除关播 item 的线程数, 默认 4

                       - `memkv_shm_path` : [string] live embedding 存储的共享内存路径，默认 /dev/shm/live_embed

                       - `memkv_part_num` : [int] live embedding 内部空间分 part ，默认 16

                       - `memkv_expire_sec` : [int] live embedding 不更新时的强制过期时间，默认 3600 秒

                       - `memkv_capacity` : [int] live embedding 最大 kv 数量，默认 200000

                       - `memkv_mem_limit` : [int] live embedding 最大内存占用，默认 2G 

                       - `update_interval_sec` : [int] live embedding 往 gpu copy 的间隔时间, 默认 60 秒

                       - `emb_dim` : [int] live embedding 单目标维数

                       - `emb_target_num` : [int] live embedding 多目标数量

                       - `min_emb_num` : [int] live embedding 最少积攒多少有效 item 才认为加载成功

                       - `rpc_service` : [string] 请求 service 获取当前所有在播 live id，用于过滤

                       - `rpc_cluster` : [string] rpc_service 对应的 cluster，默认 PRODUCTION

                       - `rpc_timeout_ms` : [int] rpc_service 对应的超时阈值

                       - `redis_cluster` : [string] 请求的 redis 集群名, 获取当前 live id 白名单，用于过滤，不在内的抛弃之，不设置时过滤功能关闭

                       - `redis_keys` : [list[string]] 请求 redis 的 key list， 不设置时过滤功能关闭

                       - `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

                       - `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

                       - `redis_biz` : [string] 访问 redis 集群所属业务，可以不填写, 默认为空

                       - `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

                       - `redis_replica` : [bool] redis 集群是否开启双集群同步，一般无需修改，默认为 true

                       - `use_fp16`: [bool] 是否使用 fp16 模式，将 embedding 以 fp16 的格式缓存在 GPU 中，默认为 false

    embedding填充模式:

    `item_embed_table_attr` : [string] live embedding 缓存 table 存入 common attr 的字段名

    `item_embed_attr` : [string] 构造的 TensorOutput 在 common context 中的 attr name

    `uni_predict_save_format`: [bool] 是否将 item_embeddding 存入 uni_predict 格式的 tensor_cache 中, 默认 false.

    `uni_predict_tensor_cache_attr`: [string] 存入 uni_predict 格式的 tensor_cache 所在的 common_attr, 默认 `uni_predict_tensor_cache`.

    `uni_predict_device_attr` [string] 存入 uni_predict 格式的 tensor_cache 对应的 device 所在的 common_attr, 默认 `uni_predict_set_device`.  

    直播召回模式

    `item_embedding_table_attr` : [string] 获取 common attr 中 live embedding 缓存 table 指针

    `item_no_attr` : [string] (部分召回时) 获取 common attr 中 item no list

    `reason` : [int] 召回 reason , 默认 0

    `cluster_tensor_attr` : [string] 不为空时，填充 item 的 cluster tensor 到该字段指定的 common attr

    `default_cluster_id` : [int] item cluster id 缺失时，补充默认的 cluster id，默认 1000

    调用示例
    ------
    .uni_gpu_live_retrieval(
      run_type=0,
      gpu_live_embed=dict(
        embedding_queue=["xxxx_live_emb"],
        remove_queue=["reco_index_builder_kuiba_live"],
        embedding_queue_thread_num=4,
        remove_queue_thread_num=2,
        update_interval_sec=120,
        emb_dim=128,
        emb_target_num=4,
        min_emb_num=80000,
        rpc_service="grpc_liveExposeAllLivingRpcService",
        rpc_timeout_ms=500,
        redis_cluster="recoExploreLiveDebugInfo",
        redis_keys=["LIVE_LEAF_ANN_LIVEID_WHITE_V4"],
        redis_value_get_type=0,
        redis_value_parse_type=0,
        redis_timeout_ms=1000,
        use_fp16=True,
        ),
      item_embed_attr="all_live_embed",
      item_embed_table_attr="live_embed_table",
    )\
    .blabla(....)\
    .uni_gpu_live_retrieval(
      run_type=1,
      gpu_live_embed=dict(
        embedding_queue=["xxxx_live_emb"],
        remove_queue=["reco_index_builder_kuiba_live"],
        embedding_queue_thread_num=4,
        remove_queue_thread_num=2,
        update_interval_sec=120,
        emb_dim=128,
        emb_target_num=4,
        min_emb_num=80000,
        use_fp16=True,
        ),
      item_no_attr="item_nos",
      item_embed_table_attr="live_embed_table",
    )\

    """
    self._add_processor(UniGpuLiveRetriever(kwargs))
    return self

  def live_retrieve_predict(self, **kwargs):
    """
    LiveRetrievePredictItemAttrEnricher
    ------
    通过 Mio 模型进行预估（仅支持通过 TensorRT 新架构进行预估），使用该 processor 需要确保
    non-common 的 attr 已经存在 GPU cache 中。

    参数配置
    ------
    `graph`: [string] 计算图 uri，仅支持 `base64://`` 这种 schema

    `inputs`: [list] of [dict] 模型输入配置，包括 `attr_name` [string]，`tensor_name` [string], `common` [bool] (optional 默认为 False), `dim` [int]. 将根据 `common` 从上述的 slots_config (如果没有则从 common attr 或 item attr) 的 `attr_name` 取出参数，构造成宽度为 `dim` 的矩阵放到计算图的 `tensor_name` 中执行模型。

    `outputs`: [list] of [dict] 模型输出配置，包括 `attr_name` [string]，`tensor_name` [string]，将计算图中的 `tensor_name` 放到 item attr 的 `attr_name` 中

    `param`: [list] of [dict] 计算图的 dense 参数配置, 主要需要 `name` [string] 指定 dense 参数名字, `rown`, `coln`, `dim` 指定参数 shape. list 中参数顺序需要和 btq 一致.

    `queue_prefix`: [string] 接收模型的 BTQ topic 前缀

    `key`: [string] 模型唯一标识符，如果有多个模型需要指定不同的 key

    `read_input_from_extra_var`: [bool] 是否从额外的地方读取 input, 默认为 false，配合 fetch_mio_embedding 中的 save_result_as_tensor_output和 direct_write 一起使用

    `optimizers`: [list] of [string] 对图做优化的策略，目前仅支持 "MatmulBiasaddReluFusion", 表示对图中的`tf.relu(tf.bias_add(tf.matmul(input, weight), bias)) 做融合操作`

    `model_loader_config`: [dict] 使用哪种类型的 model_loader, 及其参数. 决定了怎么读取 btq 参数和使用哪种 executor 执行计算图. 具体配置在下列出

    `model_loader_config::type`: [string] 使用哪种 model_loader，目前仅支持"MioTFExecutedByTensorRTModelLoader"，意思是 mio-tf 格式的 dense 参数(kai c++ 默认格式) 读取，使用 TensorRT 作为 executor 执行计算图

    `model_loader_config::rowmajor`: [bool] BTQ 传输过来的参数是否是 row-major 的，默认为 false，即 column-major，（当前 kai 默认 row-major 的参数）

    `model_loader_config::enable_fp16`: [bool] 使用 fp16 做预估，预估速度会快，但是有可能会有误差甚至 NAN，默认 false

    `model_loader_config::executor_batchsizes`: [list] of [int] Executor 需要固定几个 batchsize 运行. 如果不指定, 则系统会根据 batching 参数等配置使用默认 policy 生成

    `model_loader_config::implicit_batch`: [bool] implicit batch 模式下不需要上述明确的 batchsize, 老 TensorRT 版本只支持 implicit_batch. 默认 false

    `model_loader_config::force_input_tensor_fp32`: [bool] 强制 input tensor 为 FLOAT32. 即使 enable_fp16 开启, 该选项也会强制 input tensor 为 FLOAT32 格式, 方便调用. 默认 false

    `model_loader_config::receive_dnn_model_as_macro_block`: [bool] 按 MacroBlock 从 BTQ 接收模型数据. 默认为 false



    调用示例
    ------

    """
    self._add_processor(LiveRetrievePredictItemAttrEnricher(kwargs))
    return self

  def uni_predict_tensor_cache_manipulate(self, **kwargs):
    """
    UniPredictTensorCacheManipulateAttrEnricher
    ------
    uni_predict 格式的 tensor_cache, 存储在 common_attr 中, 其中的各个 tensor 的指针等需要做简单操作时, 使用这个 processor 做调整.

    参数配置
    ------

    `mappings`: [list of dict] 一系列需要做的操作, 目前支持 {"copy", "offset"}, 必填.

                - `operation`: [string] 指定操作的类型, 必填.

                - `from`: [list of dict] 指定操作的 inputs.

                - `to`: [list of dict] 指定操作的 outputs.

    不同的 operation 有不同的配置方法:

    `copy`: 把一个 common_attr 里的 tensor_cache 浅拷贝到另一个 common_attr 的 tensor_cache.

            - `from`: [list of dict] 配置一个 source 的 tensor_cache, 限定 list 里只有一个.

            - `from`[0]: source 的 tensor_cache, 要被拷贝.

                    - `container`: [string] 必须填 `tensor_cache`.

                    - `cache_in_common_attr`: [string] 这个 tensor_cache 所在的 common_attr, 必填.

            - 'to': [list of dict] 配置一个或者多个 destination 的 tensor_cache, 限定 list 里必须有 >= 1 个.

                    - `container`: [string] 必须填 `tensor_cache`.

                    - `cache_in_common_attr`: [string] 这个 destination 的 tensor_cache 所在的 common_attr, 必填.

    `offset`: 把某个 tensor_cache 中的 tensor 头指针做一个 bytes 的偏移. 这个操作不会有真的内存操作, 只会有指针的偏移.

              - `from`: [list of dict] 配置 source 的 tensor_cache 和 offset 来源, list 里只有两个.

              - `from`[0]: source 的 tensor_cache 中的某个具体 tensor.

                          - `container`: [string] 必须填 `tensor_cache`.

                          - `cache_in_common_attr`: [string] 这个 tensor_cache 所在的 common_attr, 必填.

                          - `key`: [string] 在 tensor_cache 中某个具体的 tensor, 必填.

              - `from`[1]: offset 值.

                          - `container`: [string] 必须填 `common_attr`.

                          - `key`: [string] 存在哪个 common_attr 中, 会去找 common int attr, 必填.

              - `to`: [list of dict] 配置偏移后的 tensor 存在哪里. list 里只有 1 个.

              - `to`[0]: destination 的 tensor_cache, 要被存入.

                        - `container`: [string] 必须填 `tensor_cache`.

                        - `cache_in_common_attr`: [string] 这个 tensor_cache 所在的 common_attr, 必填.

                        - key`: [string] 存在 tensor_cache 中某个具体的 tensor, 必填.



    调用示例
    ------
    ``` python
    .uni_predict_tensor_cache_manipulate(
      mappings = [
        dict(
          operation="copy", 
          from=[
            dict(
              container="tensor_cache",
              cache_in_common_attr="uni_predict_tensor_cache"
            )
          ],
          to=[
            dict(
              container="tensor_cache",
              cache_in_common_attr="parallel_tensor_cache"
            )
          ]
        ),
        dict(
          operation="offset",
          from=[
            dict(
              container="tensor_cache",
              cache_in_common_attr="parallel_tensor_cache",
              key="no_common_inputs"
            ),
            dict(
              container="common_attr",
              key="offsets"
            )
          ],
          to=[
            dict(
              container="tensor_cache",
              cache_in_common_attr="parallel_tensor_cache",
              key="no_common_inputs"
            )
          ]
        )
      ],
    )
    """
    self._add_processor(UniPredictTensorCacheManipulateAttrEnricher(kwargs))
    return self
