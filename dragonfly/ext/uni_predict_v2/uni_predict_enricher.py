#!/usr/bin/env python3
"""
filename: uni_predict_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for uni_predict
author: <EMAIL>
date: 2021-09-03 18:31:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class UniPredictFusedItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_predict_fused"

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    return True

  @strict_types
  def _is_item_fallback_to_common(self, config)->bool:
    attr_name = config["attr_name"]
    return attr_name not in self.preceding_output_item_attrs and attr_name in self.preceding_output_common_attrs

  @staticmethod
  @strict_types
  def _is_common(config)->bool:
    if config.get("common", False):
      return True
    elif config.get("compress_group", "") == "USER":
      return True
    else:
      return False

  @strict_types
  def _check_config(self) -> None:
    # 检查 embedding fetcher type
    if "embedding_fetchers" in self._config:
      for embedding_fetcher_config in self._config.get("embedding_fetchers"):
        if "fetcher_type" in embedding_fetcher_config:
          fetcher_type = embedding_fetcher_config.get("fetcher_type")
          check_arg(fetcher_type and fetcher_type in {"BtEmbeddingServerFetcher", "RdmaColossusdbEmbeddingServerFetcher", "ColossusdbEmbeddingServerFetcher", "LocalEmbeddingFetcher"},
            '`embedding_fetchers::fetcher_type` 必须选自 {"BtEmbeddingServerFetcher", "RdmaColossusdbEmbeddingServerFetcher", "ColossusdbEmbeddingServerFetcher", "LocalEmbeddingFetcher"}')

    # 检查 batch task type
    if "batching_config" in self._config:
      batch_task_type = self._config.get("batching_config").get("batch_task_type")
      if batch_task_type is not None:
        check_arg(batch_task_type in {"BasicBatchingTask", "BatchGPUMergeTask", "BatchTensorflowTask", "BatchTVMTask"},
          '`model_loader_config::batch_task_type` 必须选自 {"BasicBatchingTask", "BatchGPUMergeTask", "BatchTensorflowTask", "BatchTVMTask"}')
        # 检查 task type 和 embedding_manager_type 的兼容性
        check_arg(self._config.get("embedding_manager_type", "") != "post_parallel_fetch" \
                  or batch_task_type in set(["BasicBatchingTask", "BatchTensorflowTask", "BatchTVMTask"]),
                  'embedding_manager_type 参数的 post_parallel_fetch 模式仅支持 {"BasicBatchingTask", "BatchTensorflowTask", "BatchTVMTask"}')

    # 检查 model_loader_config
    if "model_loader_config" in self._config:
      model_loader_type = self._config.get("model_loader_config").get("type")
      if model_loader_type is not None:
        check_arg(model_loader_type in {"MioTFExecutedByTensorFlowModelLoader", "MioTFExecutedByTensorRTModelLoader", "MioTFExecutedByTVMModelLoader", "MioTFExecutedByOnnxTRTModelLoader"},
            '`model_loader_type::type` 必须选自 {"MioTFExecutedByTensorFlowModelLoader", "MioTFExecutedByTensorRTModelLoader", "MioTFExecutedByTVMLoader", "MioTFExecutedByOnnxTRTModelLoader"}')
        if model_loader_type is "MioTFExecutedByTensorFlowModelLoader":
          dynamic_shape = self._config.get("model_loader_config").get("dynamic_shape")
          if dynamic_shape is None:
            dynamic_shape = self._config.get("model_loader_config").get("implicit_batch")
          enable_xla = self._config.get("model_loader_config").get("enable_xla")
          check_arg(not (dynamic_shape and enable_xla), "使用 TF 预估 dynamic_shape 模式下不支持 xla")

    # 检查 implicit 和 compress_group 冲突
    check_arg(self._config.get("inputs"), "`inputs` 是必选项")
    inputs_common_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if self._is_common(c))
    if len(inputs_common_attr):
      implicit = self._config.get("model_loader_config").get("implicit_batch", False)
      valid = model_loader_type == "MioTFExecutedByTensorFlowModelLoader" or not implicit
      check_arg(valid, "有 `inputs` 是 USER 侧特征: " + str(inputs_common_attr) + ", 必须设置 model_loader_config::implicit 为 False, 或者使用 TF 预估")

    # 检查 max(executor_bs) == batching:max_bs
    batching_max_bs = self._config.get("batching_config").get("max_batch_size", 512)
    executor_batchsizes = self._config.get("model_loader_config").get("executor_batchsizes", [batching_max_bs])
    check_arg(max(executor_batchsizes) == batching_max_bs,
      "batching_config::max_batch_size=" + str(batching_max_bs) + " 必须和 max(model_loader_config::executor_batchsizes)=" + str(executor_batchsizes) + " 一致. ")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for c in self._config["outputs"]:
      if self._is_common(c): attrs.add(c["attr_name"])

    if self._config.get("debug_tensor", False):
      # debug tensor, inputs as outputs
      inputs_common_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if self._is_common(c))
      attrs |= inputs_common_attr
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for c in self._config["outputs"]:
      if not self._is_common(c): attrs.add(c["attr_name"])

    if self._config.get("debug_tensor", False):
      # debug tensor, inputs as outputs
      input_item_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if not self._is_common(c))
      attrs |= input_item_attr
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    slot_config = self._config.get("slots_config", [])
    attrs = set()
    for key in ["common_slots_inputs", "common_parameters_inputs"]:
      attrs.update(self._config.get(key, []))

    slots_config_attrs = set()
    for sc in slot_config:
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)
      # collect slots_config attrs
      slots_config_attrs |= set(c["input_name"] for c in sc.get("slots_config", []))

    # embedding fetcher config
    fetcher_configs = self._config.get("embedding_fetchers", [])
    for fetcher_config in fetcher_configs:
      this_fetcher_common_attrs = set()
      for key in ["common_slots_inputs", "common_parameters_inputs"]:
        this_fetcher_common_attrs.update(fetcher_config.get(key, []))
      attrs |= this_fetcher_common_attrs
      # collect slots_config attrs
      slots_config_attrs |= set(c["input_name"] for c in fetcher_config.get("slots_config", []))

    # no slot config, inputs as attr input
    inputs_common_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if self._is_common(c) or self._is_item_fallback_to_common(c))
    attrs |= (inputs_common_attr - slots_config_attrs)

    for key in ["dynamic_targets_attr"]:
      if key in self._config: attrs.add(self._config[key])

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    attrs = set()
    for key in ["slots_inputs", "parameters_inputs"]:
      attrs.update(self._config.get(key, []))

    slots_config_attrs = set()
    for sc in slots_config:
      if sc.get("common", False):
        continue
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)
      # collect slots_config attrs
    slots_config_attrs |= set(c["input_name"] for c in slots_config)

    # embedding fetcher config
    fetcher_configs = self._config.get("embedding_fetchers", [])
    for fetcher_config in fetcher_configs:
      this_fetcher_item_attrs = set()
      for key in ["slots_inputs", "parameters_inputs"]:
        this_fetcher_item_attrs.update(fetcher_config.get(key, []))
      attrs |= this_fetcher_item_attrs
      # collect slots_config attrs
      slots_config_attrs |= set(c["input_name"] for c in fetcher_config.get("slots_config", []))

    # no slot config, inputs as attr input
    input_item_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if not (self._is_common(c) or self._is_item_fallback_to_common(c)))
    attrs |= (input_item_attr - slots_config_attrs)

    return attrs

class UniPredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_predict"

  @classmethod
  @strict_types
  def _is_common(cls, config)->bool:
    if config.get("common", False):
      return True
    elif config.get("compress_group", "") == "USER":
      return True
    else:
      return False

  @strict_types
  def _check_config(self) -> None:
    # 检查 batch task type
    if "batching_config" in self._config:
      batch_task_type = self._config.get("batching_config").get("batch_task_type")
      if batch_task_type is not None:
        check_arg(batch_task_type in {"BasicBatchingTask", "BatchGPUMergeTask", "BatchTensorflowTask"},
          '`model_loader_config::batch_task_type` 必须选自 {"BasicBatchingTask", "BatchGPUMergeTask", "BatchTensorflowTask"}')
      check_arg(self._config.get("batching_config").get("max_enqueued_batches") == 1,
        '非 fused 模式下 batching_config:max_enqueued_batches 必须等于 1')

    # 检查 model_loader_config
    if "model_loader_config" in self._config:
      model_loader_type = self._config.get("model_loader_config").get("type")
      if model_loader_type is not None:
        check_arg(model_loader_type in {"MioTFExecutedByTensorFlowModelLoader", "MioTFExecutedByTensorRTModelLoader"},
            '`model_loader_type::type` 必须选自 {"MioTFExecutedByTensorFlowModelLoader", "MioTFExecutedByTensorRTModelLoader"}')
        if model_loader_type is "MioTFExecutedByTensorFlowModelLoader":
          implicit_batch = self._config.get("model_loader_config").get("implicit_batch")
          enable_xla = self._config.get("model_loader_config").get("enable_xla")
          check_arg(not (implicit_batch and enable_xla), "使用 TF 预估 implicit batch 模式下不支持 xla")

    # 检查 implicit 和 compress_group 冲突
    check_arg(self._config.get("inputs"), "`inputs` 是必选项")
    inputs_common_attr = set(c["attr_name"] for c in self._config.get("inputs", []) if self._is_common(c))
    if len(inputs_common_attr):
      implicit = self._config.get("model_loader_config").get("implicit_batch", False)
      valid = model_loader_type == "MioTFExecutedByTensorFlowModelLoader" or not implicit
      check_arg(valid, "有 `inputs` 是 USER 侧特征: " + str(inputs_common_attr) + ", 必须设置 model_loader_config::implicit 为 False, 或者使用 TF 预估")

    # 检查 max(executor_bs) == batching:max_bs
    batching_max_bs = self._config.get("batching_config").get("max_batch_size", 512)
    executor_batchsizes = self._config.get("model_loader_config").get("executor_batchsizes", [batching_max_bs])
    check_arg(max(executor_batchsizes) == batching_max_bs,
      "batching_config::max_batch_size=" + str(batching_max_bs) + " 必须和 max(model_loader_config::executor_batchsizes)=" + str(executor_batchsizes) + " 一致. ")

    # 检查 key 和 queue_prefix 必须设置
    check_arg(self._config.get("key"), 'key 必须设置')
    check_arg(self._config.get("queue_prefix"), 'queue_prefix 必须设置')

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    output_common_attr = set(c["attr_name"] for c in self._config.get(
        "outputs", []) if self._is_common(c) or self._config.get("save_outputs_to_common_attr", False))
    attrs |= output_common_attr
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(c["attr_name"] for c in self._config["outputs"] if not self._is_common(
        c) and not self._config.get("save_outputs_to_common_attr", False))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    device_attr = self._config.get("uni_predict_device_attr", "uni_predict_set_device")
    tensor_cache_attr = self._config.get("uni_predict_tensor_cache_attr", "uni_predict_tensor_cache")
    ret = {device_attr, tensor_cache_attr}
    if "num_items_attr" in self._config:
      ret.add(self._config["num_items_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # cannot determine statically
    return set()

class UniPredictFetchEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_predict_fetch_embedding"

  @classmethod
  @strict_types
  def _is_common(cls, config)->bool:
    if config.get("common", False):
      return True
    elif config.get("compress_group", "") == "USER":
      return True
    else:
      return False

  @strict_types
  def _check_config(self) -> None:
    # 检查 embedding fetcher type
    if "embedding_fetchers" in self._config:
      for embedding_fetcher_config in self._config.get("embedding_fetchers"):
        if "fetcher_type" in embedding_fetcher_config:
          fetcher_type = embedding_fetcher_config.get("fetcher_type")
          check_arg(fetcher_type and fetcher_type in {"BtEmbeddingServerFetcher", "RdmaColossusdbEmbeddingServerFetcher", "ColossusdbEmbeddingServerFetcher", "LocalEmbeddingFetcher"},
            '`embedding_fetchers::fetcher_type` 必须选自 {"BtEmbeddingServerFetcher", "RdmaColossusdbEmbeddingServerFetcher", "ColossusdbEmbeddingServerFetcher", "LocalEmbeddingFetcher"}')

    # 检查 max_batch_size 必须设置
    check_arg(self._config.get("max_batch_size"), 'max_batch_size 必须设置')

    # key 必须设置
    check_arg(self._config.get("key"), 'key 必须设置')

    # save_to_device 必须从 {CPU, GPU} 中选择
    check_arg(self._config.get("save_to_device") is not None, 'save_to_device 必须设置')
    check_arg(self._config.get("save_to_device") in {"GPU", "CPU"}, 'save_to_device 必须从 `CPU` 和 `GPU` 中选择')

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    tensor_cache_attr = self._config.get("uni_predict_tensor_cache_attr", "uni_predict_tensor_cache")
    attrs.add(tensor_cache_attr)
    if self._config.get("random_device_set_to_attr", True):
      device_attr = self._config.get("uni_predict_device_attr", "uni_predict_set_device")
      attrs.add(device_attr)

    if self._config.get("write_to_attr", False):
      for fetcher in self._config.get("embedding_fetchers"):
        slots_configs = fetcher.get("slots_config", [])
        common_slots_attr = set(c["input_name"] for c in slots_configs if self._is_common(c))
        attrs | common_slots_attr

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("write_to_attr", False):
      for fetcher in self._config.get("embedding_fetchers", []):
        slots_configs = fetcher.get("slots_config", [])
        item_slots_attr = set(c["input_name"] for c in slots_configs if not self._is_common(c))
        attrs | item_slots_attr

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    slot_config = self._config.get("slots_config", [])
    for key in ["common_slots_inputs", "common_parameters_inputs"]:
      attrs.update(self._config.get(key, []))

    for sc in slot_config:
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)

    # embedding fetcher config
    fetcher_configs = self._config.get("embedding_fetchers", [])
    for fetcher_config in fetcher_configs:
      this_fetcher_common_attrs = set()
      for key in ["common_slots_inputs", "common_parameters_inputs"]:
        this_fetcher_common_attrs.update(fetcher_config.get(key, []))
      attrs |= this_fetcher_common_attrs

    # device and tensor_cache cannot be determined
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    slots_config = self._config.get("slots_config", [])
    attrs = set()
    for key in ["slots_inputs", "parameters_inputs"]:
      attrs.update(self._config.get(key, []))

    for sc in slots_config:
      if sc.get("common", False):
        continue
      weights = sc.get("weights", [])
      if isinstance(weights, str):
        weights = weights.strip().split()
      attrs |= set(weights)


    # embedding fetcher config
    fetcher_configs = self._config.get("embedding_fetchers", [])
    for fetcher_config in fetcher_configs:
      this_fetcher_item_attrs = set()
      for key in ["slots_inputs", "parameters_inputs"]:
        this_fetcher_item_attrs.update(fetcher_config.get(key, []))
      attrs |= this_fetcher_item_attrs

    return attrs

class UniPredictTensorCacheManipulateAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_predict_tensor_cache_manipulate"
  
  @classmethod
  @strict_types
  def parse_copy_operation(self, copy_conf) -> dict:
    ret = {"input_item": set(), "input_common": set(), "output_item": set(), "output_common": set()}
    from_list = copy_conf.get("from", [])
    check_arg(len(from_list) == 1, "copy operation 的 from 必须只有一个")
    check_arg(from_list[0].get("container") == "tensor_cache", "copy operation 的 from[0].container 必须是 tensor_cache")
    check_arg(from_list[0].get("cache_in_common_attr"), "copy operation 的 from[0].cache_in_common_attr 必须设置")
    ret["input_common"].add(from_list[0].get("cache_in_common_attr"))

    to_list = copy_conf.get("to", [])
    check_arg(len(to_list) >= 1, "copy opration 的 to 必须 >= 1 个")
    for to_conf in to_list:
      check_arg(to_conf.get("container") == "tensor_cache", "copy operation 的 to[i].container 必须是 tensor_cache")
      check_arg(to_conf.get("cache_in_common_attr"), "copy operation 的 to[i].cache_in_common_attr 必须设置")
      ret["output_common"].add(to_conf.get("cache_in_common_attr"))
    return ret

  @classmethod
  @strict_types
  def parse_offset_operation(self, offset_conf) -> dict:
    ret = {"input_item": set(), "input_common": set(), "output_item": set(), "output_common": set()}
    from_list = offset_conf.get("from", [])
    check_arg(len(from_list) == 2, "offset operation 的 from 必须有 2 个")
    check_arg(from_list[0].get("container") == "tensor_cache", "offset operation 的 from[0].container 必须是 tensor_cache")
    check_arg(from_list[0].get("cache_in_common_attr"), "offset operation 的 from[0].cache_in_common_attr 必须设置")
    check_arg(from_list[0].get("key"), "offset operation 的 from[0].key 必须设置")
    ret["input_common"].add(from_list[0].get("cache_in_common_attr"))
    check_arg(from_list[1].get("container") == "common_attr", "offset operation 的 from[1].container 必须是 common_attr")
    check_arg(from_list[1].get("key"), "offset operation 的 from[1].key 必须设置")
    ret["input_common"].add(from_list[1].get("key"))

    to_list = offset_conf.get("to", [])
    check_arg(len(to_list) >= 1, "offset opration 的 to 必须 >= 1 个")
    for to_conf in to_list:
      check_arg(to_conf.get("container") == "tensor_cache", "offset operation 的 to[i].container 必须是 tensor_cache")
      check_arg(to_conf.get("cache_in_common_attr"), "offset operation 的 to[i].cache_in_common_attr 必须设置")
      check_arg(to_conf.get("key"), "offset operation 的 to[i].key 必须设置")
      ret["output_common"].add(to_conf.get("cache_in_common_attr"))

    return ret

  @classmethod
  @strict_types
  def parse_dispatch(self, conf) -> dict:
    dispatch = {
      "copy": self.parse_copy_operation,
      "offset": self.parse_offset_operation
    }
    return dispatch[conf.get("operation")](conf)

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mappings"), 'mappings 必须设置')
    mappings = self._config.get("mappings", [])
    for op in mappings:
      check_arg(op.get("operation") in {"copy", "offset"}, "operation 必须在 [`copy`, `offset`] 中选择")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for op in self._config.get("mappings", []):
      ret = ret | self.parse_dispatch(op)["output_common"]
    return ret
    
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for op in self._config.get("mappings", []):
      ret = ret | self.parse_dispatch(op)["output_item"]
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    output_common = set()
    ret = set()
    for op in self._config.get("mappings", []):
      attrs = self.parse_dispatch(op)
      # 防止上游的 operation 产出的 common_attr 被判定为需要的 input common_attr
      new_input_common = attrs["input_common"] - output_common
      ret = ret | new_input_common
      output_common = output_common | attrs["output_common"]
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    output_item = set()
    ret = set()
    for op in self._config.get("mappings", []):
      attrs = self.parse_dispatch(op)
      # 防止上游的 operation 产出的 item_attr 被判定为需要的 input item_attr
      new_input_item = attrs["input_item"] - output_item
      ret = ret | new_input_item
      output_item = output_item | attrs["output_item"]
    return ret


class LiveRetrievePredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_retrieve_predict"

  @classmethod
  @strict_types
  def _is_common(cls, config)->bool:
    if config.get("common", False):
      return True
    elif config.get("compress_group", "") == "USER":
      return True
    else:
      return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("output_common_attr", False):
      return set(c["attr_name"] for c in self._config.get("outputs", []))
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if not self._config.get("output_common_attr", False):
      return set(c["attr_name"] for c in self._config.get("outputs", []))
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(c["attr_name"] for c in self._config.get("inputs", []) if self._is_common(c))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(c["attr_name"] for c in self._config.get("inputs", []) if not self._is_common(c))
