#!/usr/bin/env python3
"""
filename: uni_live_retriver.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for uni_predict live recall.
author: <EMAIL>
date: 2021-12-13 14:32:39
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class UniGpuLiveRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_gpu_live_retrieval"

  def run_type(self):
    return self._config.get("run_type", -1)
  
  def uni_predict_format_save(self) -> bool:
    return self._config.get("uni_predict_save_format", False) and self.run_type() == 0

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self.run_type() in [1, 2, 3]:
      for key in ["item_no_attr", "item_embed_table_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    
    # uni_predict format save
    if self.uni_predict_format_save():
      ret.add(self._config.get("uni_predict_device_attr", "uni_predict_set_device"))

    if "retrieve_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["retrieve_num"]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self.run_type() == 0:
      if self.uni_predict_format_save():
        ret.add(self._config.get("uni_predict_tensor_cache_attr", "uni_predict_tensor_cache"))
      else:
        for key in ["item_embed_attr", "cluster_tensor_attr"]:
          if key in self._config:
            ret.add(self._config[key])
      if "item_embed_table_attr" in self._config:
        ret.add(self._config["item_embed_table_attr"])
      if "num_items_attr" in self._config:
        ret.add(self._config["num_items_attr"])
    return ret
