#!/usr/bin/env python3
# coding=utf-8
"""
filename: snack_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,snack api mixin
author: <EMAIL>
date: 2020-08-18 16:54:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .snack_retriever import *
from .snack_enricher import *
from .snack_observer import *

class snackApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 snack 相关的 Processor 接口
  - SnackPerRandomRetriever
  - SnackValidOperationTriggerEnricher
  - SnackLookUpMapStringKconfEnricher
  - SnackVideoPlayTimeEnricher
  - SnackI2ILeftKeyEnricher
  - SnackI2IRetriever
  - SnackRegionHashCodeEnricher
  - SnackRedisIncrbyObserver
  - SnackPatchRecoResultEnricher
  - SnackPatchPhotoInfoEnricher
  - SnackTransformItemKeyEnricher
  - SnackIntSetKconfCommonAttrEnricher
  - SnackIntSetKconfSetCommonAttrEnricher
  - SnackMapKconfRetriever
  - SnackFishPredictItemAttrEnricher
  - SnackSetKconfRetriever
  - SnackUserRedisRetriever
  - SnackUserReprPhotoEnricher
  - SnackGraphEnricher
  - SnackFetchUserInfoEnricher
  - SnackKconfItemAttrEnricher
  """

  def snack_get_kconf_item_attr(self, **kwargs):
    """
    SnackKconfItemAttrEnricher
    ------
    snack 用 map_int64 类型的 kconf 获取 item attr

    参数配置
    ------
    `kconf_prefix`: [string] kconf key 的前缀

    `partition_size`: [int] kconf 分片个数

    `output_item_attr`: [string] 输出的 item attr

    调用示例
    ------
    ``` python
    .snack_get_kconf_item_attr(
      kconf_prefix="prefix",
      partition_size=10,
      output_item_attr="replace_id"
    )
    ```
    """
    self._add_processor(SnackKconfItemAttrEnricher(kwargs))
    return self

  def snack_backup_result(self, **kwargs):
    """
    SnackBackupResultObserver
    ------
    snack 存入 backup 集群

    参数配置
    ------
    `redis_cluster`: [string] 访问的 redis 集群

    `redis_key_attr`: [string] 读取和写入的 redis key 的 common attr

    `timeout_ms`: [int] 选配项 redis 请求超时时间，默认为 20 ms

    `backup_interval_time_ms`: [int] zadd 最小时间间隔, 单位 ms

    `backup_expire_time_ms`: [int] redis 设置的过期时间, 单位 ms

    `time_attr`: [string] 代表当前时间的 common attr，单位 ms

    调用示例
    ------
    ``` python
    .snack_backup_result(
      redis_cluster="snackBackUp",
      redis_key_attr="key1",
      timeout_ms=20,
      backup_interval_time_ms=24 * 3600 * 1000,
      backup_expire_time_ms=30 * 24 * 3600 * 1000,
    )
    ```
    """
    self._add_processor(SnackBackupResultObserver(kwargs))
    return self

  def snack_tensor_predict(self, **kwargs):
    """
    SnackTensorPredictEnricher
    ------
    snack 调用 CommonPredictServer 的 GetTensor 接口获取 item attr

    参数配置
    ------
    `kess_service`: [string] 动态参数 请求的 CommonPredictServer 的服务名

    `timeout_ms`: [int] 选配项 请求 CommonPredictServer 超时时间, 单位是毫秒，默认为 200 ms

    `layer`: [string] 动态参数 设置 GetTensorRequest 中 layer

    `enable_layer_suffix`: [bool] 是否启用 layer 的 后缀，默认为 true

    `common_attr_rename_map`: [dict] 设置请求 CommonPredictServer 的 common attrs, 映射关系的 key 值为 CommonPredict 服务中的特征名称, value 值为 leaf 里的 attr 名称

    `item_attr_rename_map`: [dict] 设置请求 CommonPredictServer 的 predict item 的 attr, 映射关系的 key 值为 CommonPredict 服务中的特征名称, value 值为 leaf 里的 attr 名称

    `output_item_attr`: [string] 将获取的 GetTensorResponse 存储到对应的 item attr

    调用示例
    ------
    ``` python
    .snack_tensor_predict(
      common_attr_rename_map={
        "uId": "user_id",
        "dId": "device_id",
      },
      item_attr_rename_map={
        "pId": "photo_id",
        "aId": "author_id",
      },
      layer="l1",
      output_item_attr="pctr",
      timeout_ms=100,
      kess_service="grpc_PredictServer",
      )
    ```
    """
    self._add_processor(SnackTensorPredictEnricher(kwargs))
    return self

  def snack_per_random_retrieve(self, **kwargs):
    """
    SnackPerRandomRetriever
    ------
    snack 调用 PerRandomServer 获取 item

    参数配置
    ------
    `oversea_user_pb_attr`: [string] 存储用户 ReaderInfo 的 pb 的 string CommonAttr

    `bucket_id`: [string] 存储用户 BucketId 的 int CommonAttr

    `num_limit`: [int] 动态参数 从 PerRandomServer 获取的 item 数量

    `timeout_ms`: [int] 选配项 请求 PerRandomServer 超时时间, 单位是毫秒，默认为 50 ms

    `kess_service`: [string] 动态参数 请求的 PerRandomServer 的服务名

    `service_kess_shard`: [string] 动态参数 请求的 PerRandomServer 的 shard

    调用示例
    ------
    ``` python
    .snack_per_random_retrieve(
      oversea_user_pb_attr="oversea_user_pb",
      bucket_id="bucket_id_attr",
      num_limit=10,
      timeout_ms=100,
      kess_service="grpc_PerRandomServer",
      service_kess_shard="s0"
      )
    ```
    """
    self._add_processor(SnackPerRandomRetriever(kwargs))
    return self

  def snack_gen_validate_trigger(self, **kwargs):
    """
    SnackValidOperationTriggerEnricher
    ------
    判断 operation trigger 是否符合要求，将所有符合要求的 trigger 存入 output_common_attr

    参数配置
    ------
    `operation_triggers`: [string] 存储 trigger 的 CommonAttr

    `content_language`: [string] 存储 user content_language 的 CommonAttr

    `time_now`: [string] 选配项 存储 当前时间戳的 CommonAttr, 默认为 _REQ_TIME_

    `time_range`: [int] 有效时间范围，单位分钟，默认为 60

    `user_location_region`: [string] 存储 user user_region 的 CommonAttr

    `output_common_attr`: [string] 用来存储 valid trigger 的 CommonAttr

    调用示例
    ------
    ``` python
    .snack_gen_validate_trigger(
      operation_triggers="operation_triggers",
      content_language="language",
      user_location_region="region",
      output_common_attr="valid_triggers"
      )
    ```
    """
    self._add_processor(SnackValidOperationTriggerEnricher(kwargs))
    return self

  def snack_look_up_map_kconf(self, **kwargs):
    """
    SnackLookUpMapStringKconfEnricher
    ------
    在 Kconf 中 获取 map<string, string>, 然后用 look_up_key 进行查找

    参数配置
    ------
    `kconf_key`: [string] 动态参数 kconf key，仅支持 string CommonAttr

    `look_up_key`: [string] 动态参数 在 map 中查找的 key, 仅支持 string CommonAttr

    `default_key`: [string] 动态参数 在 map 中查找的 default key, 仅支持 string CommonAttr

    `default_value`: [string] 动态参数 查找结果的默认值, 仅支持 string CommonAttr

    `output_common_attr`: [string] 存储查找结果的 CommonAttr

    调用示例
    ------
    ``` python
    .snack_look_up_map_kconf(
      kconf_key="kconf_key",
      look_up_key="{{region}}",
      default_key="default",
      default_value="",
      output_common_attr="region_key"
      )
    ```
    """
    self._add_processor(SnackLookUpMapStringKconfEnricher(kwargs))
    return self

  def snack_video_play_time_enrich(self, **kwargs):
    """
    SnackVideoPlayTimeEnricher
    ------
    snack user info 中的 video 播放时长是否满足一定的条件，将结果存入 ItemAttr

    参数配置
    ------
    `video_attr`: [string] 存储 video 的 item key 的 int list CommonAttr

    `video_playing_time_attr`: [string] 存储 video 的 play_time 的 int list CommonAttr

    `video_duration_attr`: [string] 存储 video 的 video_duration key 的 int list CommonAttr

    `enable_play_time_threshold`: [bool] 动态参数 可缺省 是否启用 play_time 的 threshold 默认不启用

    `play_time_threshold`: [double] 动态参数 可缺省 play_time 的 threshold 默认 0

    `duration_ms_threshold`: [double] 动态参数 可缺省 duration 的 threshold 默认 0

    `output_item_attr`: [string] 存储判断 video 是否满足条件的 int ItemAttr

    调用示例
    ------
    ``` python
    .snack_video_play_time_enrich(
      video_attr="video_item_keys",
      video_playing_time_attr="video_playing_times",
      video_duration_attr="video_durations",
      enable_play_time_threshold="{{ab_params_enable_play_time_threshold}}",
      play_time_threshold="{{ab_params_play_time_threshold}}",
      duration_ms_threshold="{{ab_params_duration_ms_threshold}}",
      output_item_attr="PlayTimeMeetThreshold"
      )
    ```
    """
    self._add_processor(SnackVideoPlaytimeEnricher(kwargs))
    return self

  def snack_i2i_left_key_enricher(self, **kwargs):
    """
    SnackI2ILeftKeyEnricher
    ------
    从生成 i2i 的 leaf key 及 weight

    参数配置
    ------
    `bucket_id_attr`: [string] 存储用户 BucketId 的 int CommonAttr

    `left_key_max_num`: [string] 存储 left key 的 max num CommonAttr

    `output_left_terms`: [string] 存储 输出 的 left terms 的 int list CommonAttr

    `output_left_weights`: [string] 存储 输出 的 left weights 的 double list CommonAttr

    `strategies`: [list] 触发的策略
      - `source_item`: [string] 存储 i2i 查询 item 的 int|int list CommonAttr
      - `num_limit`: [int] 动态参数 可缺省 查询符合条件的前 num_limit 项目,
      - `item_benefit`: [string] 可缺省 i2i 查询 item benefit 分数的 double ItemAttr
      - `common_benefit`: [string] 存储 i2i 查询 common benefit 分数的 double CommonAttr，最终 benefit 是 common_benefit * item_benefit
      - `additional_conditions`: [list] 存储 用来判断是否符合条件 的 ItemAttr，仅支持 int ItemAttr;

    调用示例
    ------
    ``` python
    .snack_i2i_left_key_enricher(
      bucket_id_attr="bucket_id_attr",
      left_key_max_num="left_key_max_num",
      strategies=[{
        "source_item": "promotion_action_item",
        "common_benefit": "bulldog_i2i_promotion_benefit"
      }, {
        "source_item": "follow_list",
        "num_limit": "{{bulldog_i2i_follow_list_length}}",
        "common_benefit": "bulldog_i2i_follow_benefit"
      }, {
        "source_item": "like_list",
        "num_limit": "{{bulldog_i2i_like_list_length}}",
        "common_benefit": "bulldog_i2i_like_benefit"
      }, {
        "source_item": "forward_list",
        "num_limit": "{{bulldog_i2i_forward_list_length}}",
        "common_benefit": "bulldog_i2i_forward_benefit"
      }, {
        "source_item":"video_playing",
        "num_limit": "{{bulldog_i2i_finish_play_list_length}}",
        "common_benefit": "bulldog_i2i_finish_play_benefit",
        "additional_conditions": ["playing_time_meet_threshold"]
      }],
      output_left_terms="left_keys",
      output_left_weights="weight"
      )
    ```
    """
    self._add_processor(SnackI2ILeftKeyEnricher(kwargs))
    return self

  def snack_i2i_retrieve(self, **kwargs):
    """
    SnackI2IRetriever
    ------
    访问 redis进行 i2i 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `redis_cluster`: [string] 动态参数 访问的 redis 集群

    `bucket_name_attr`: [string] 存储用户 BucketName 的 string CommonAttr

    `bucket_id_attr`: [string] 存储用户 BucketId 的 int CommonAttr

    `redis_prefix_attr`: [string] 存储 redis_prefix 的 string CommonAttr

    `left_key_max_num`: [string] 存储 left key 的 max num CommonAttr

    `save_source_item_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 source_item_id 存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `save_distance_to_attr`: [string] 如果不为空，则将各个召回 item 与其 source_item_id 的 distance 值存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `strategies`: [list] 触发的策略
      - `source_item`: [string] 存储 i2i 查询 item 的 int|int list CommonAttr
      - `num_limit`: [int] 动态参数 可缺省 查询符合条件的前 num_limit 项目,
      - `item_benefit`: [string] 可缺省 i2i 查询 item benefit 分数的 double ItemAttr
      - `common_benefit`: [string] 存储 i2i 查询 common benefit 分数的 double CommonAttr，最终 benefit 是 common_benefit * item_benefit
      - `additional_conditions`: [list] 存储 用来判断是否符合条件 的 ItemAttr，仅支持 int ItemAttr;

    调用示例
    ------
    ``` python
    .snack_i2i_retrieve(
      redis_cluster="kSnackI2ICluster",
      bucket_name_attr="bucket_name_attr",
      bucket_id_attr="bucket_id_attr",
      redis_prefix_attr="pbg_i2i_redis_key_prefix",
      left_key_max_num="pbg_i2i_left_key_max_num",
      strategies=[{
        "source_item": "promotion_action_item",
        "common_benefit": "bulldog_i2i_promotion_benefit"
      }, {
        "source_item": "follow_list",
        "num_limit": "{{bulldog_i2i_follow_list_length}}",
        "common_benefit": "bulldog_i2i_follow_benefit"
      }, {
        "source_item": "like_list",
        "num_limit": "{{bulldog_i2i_like_list_length}}",
        "common_benefit": "bulldog_i2i_like_benefit"
      }, {
        "source_item": "forward_list",
        "num_limit": "{{bulldog_i2i_forward_list_length}}",
        "common_benefit": "bulldog_i2i_forward_benefit"
      }, {
        "source_item":"video_playing",
        "num_limit": "{{bulldog_i2i_finish_play_list_length}}",
        "common_benefit": "bulldog_i2i_finish_play_benefit",
        "additional_conditions": ["playing_time_meet_threshold"]
      }]
      )
    ```
    """
    self._add_processor(SnackI2IRetriever(kwargs))
    return self

  def snack_region_hash_code(self, **kwargs):
    """
    SnackRegionHashCodeEnricher
    ------
    将 snack 的 region 信息 取 hashcode

    参数配置
    ------
    `input_common_attr`: [string] 存储输入 region 的 string CommonAttr

    `output_common_attr`: [string] 存储输出 item_key 的 int CommonAttr

    调用示例
    ------
    ``` python
    .snack_region_hash_code(
      input_common_attr="region",
      output_common_attr="region_hash_code"
    )
    ```
    """
    self._add_processor(SnackRegionHashCodeEnricher(kwargs))
    return self

  def snack_redis_incrby(self, **kwargs):
    """
    SnackRedisIncrbyObserver
    ------
    调用 redis Incr 命令

    参数配置
    ------
    `redis_cluster`: [string] 访问的 redis 集群

    `redis_keys`: [string] 动态参数 被调用 Incr 的 redis keys, 仅支持 string list common attr

    `expire_s`: [int] 动态参数 选配项 redis key 的过期时间，单位 s, 默认不改变过期时间

    `num`: [int] 动态参数 增加的数目，可缺省，默认为1

    调用示例
    ------
    ``` python
    .snack_region_hash_code(
      redis_cluster="redisCluster"
      redis_keys="{{keys}}",
      num=1
    )
    ```
    """
    self._add_processor(SnackRedisIncrbyObserver(kwargs))
    return self

  def snack_patch_reco_result(self, **kwargs):
    """
    SnackPatchRecoResultEnricher
    ------
    由于 build_protobuf 暂时不支持 repeated_message
    所以手动将 photo 信息 patch 到 rank result 的 protobuf 上，并将 SerializeString 存储到 context 中

    参数配置
    ------
    `reco_result`: [string] RealTimeOverseaRecoResult 的 PtrItemAttr

    `a_admin_mark`: [string] a_admin_mark 的 int list ItemAttr

    `a_admin_type`: [string] a_admin_type 的 int list ItemAttr

    `p_multi_tag_mapping_id`: [string] 选配项 p_multi_tag_mapping_id 的 int list ItemAttr

    `p_tag_id`: [string] 选配项 p_tag_id 的 int list ItemAttr

    `p_tag_level_union_id`: [string] 选配项 p_tag_level_union_id 的 int list ItemAttr

    `rank_result_sample_attrs` [object] 将 ItemAttr 转存到 sample attr 中, key 为 sample attr 名字, value 为 ItemAttr

    `serialize_reco_result`: [string] 存储 reco_result SerializeAsString 的 string ItemAttr

    调用示例
    ------
    ``` python
    .snack_patch_reco_result(
      reco_result="RankResult",
      a_admin_mark="a_admin_mark",
      a_admin_type="a_admin_type",
      p_multi_tag_mapping_id="p_multi_tag_mapping_id",
      p_tag_id="p_tag_id",
      rank_result_sample_attrs={
        "l2r_score": "Fm_pl2r_nnctr",
        "ensemble_score": "FineTuningScore",
        "host_name": "HostName",
      },
      serialize_reco_result="SerializedRankResult"
    )
    ```
    """
    self._add_processor(SnackPatchRecoResultEnricher(kwargs))
    return self

  def snack_patch_photo_info(self, **kwargs):
    """
    SnackPatchPhotoInfoEnricher
    ------
    由于 build_protobuf 暂时不支持 repeated_message
    所以手动将 photo 信息 patch 到 photo info 的 protobuf 上

    参数配置
    ------
    `photo_info`: [string] PhotoInfo 的 PtrItemAttr

    `a_admin_mark`: [string] a_admin_mark 的 int list ItemAttr

    `a_admin_type`: [string] a_admin_type 的 int list ItemAttr

    `p_multi_tag_mapping_id`: [string] 选配项 p_multi_tag_mapping_id 的 int list ItemAttr

    `p_tag_id`: [string] 选配项 p_tag_id 的 int list ItemAttr

    `p_tag_level_union_id`: [string] 选配项 p_tag_level_union_id 的 int list ItemAttr

    调用示例
    ------
    ``` python
    .snack_patch_photo_info(
      photo_info="PhotoInfo",
      p_multi_tag_mapping_id="p_multi_tag_mapping_id",
      p_tag_id="p_tag_id"
    )
    ```
    """
    self._add_processor(SnackPatchPhotoInfoEnricher(kwargs))
    return self

  def snack_transform_item_key(self, **kwargs):
    """
    SnackTransformItemKeyEnricher
    ------
    将 photo id 的 int list CommonAttr 转化成 item_key 的 int list CommonAttr

    参数配置
    ------
    `input_common_attr`: [string] 存储输入 photo_id 的 int list CommonAttr

    `output_common_attr`: [string] 存储输出 item_key 的 int list CommonAttr

    `bucket_id_attr`: [string] 存储用户 BucketId 的 int CommonAttr

    调用示例
    ------
    ``` python
    .snack_transform_item_key(
      input_common_attr="force_insert_photo_ids",
      output_common_attr="force_insert_item_key",
      bucket_id_attr="bucket_id_attr"
    )
    ```
    """
    self._add_processor(SnackTransformItemKeyEnricher(kwargs))
    return self

  def snack_int_set_kconf_common_attr(self, **kwargs):
    """
    SnackIntSetKconfCommonAttrEnricher
    ------
    将 kconf 的 set int64 转化成 int list CommonAttr

    参数配置
    ------
    `kconf_configs`: [list] 转换的列表
      - `kconf_key`: [string] 动态参数 kconf key，仅支持 string CommonAttr
      - `bucket_name_attr`: [string] 存储 bucket name 的 CommonAttr name
      - `export_common_attr`: [string] 存储输出 int list CommonAttr name

    调用示例
    ------
    ``` python
    .snack_int_set_kconf_common_attr(
      kconf_configs=[{
        "kconf_key": "{{selected_author_kconf_key}}",
        "bucket_name_attr": "bucket_name_attr",
        "export_common_attr": "selected_author_list"
      }, {
        "kconf_key": "snackReco.hot.selectedAuthor",
        "bucket_name_attr": "bucket_name_attr",
        "export_common_attr": "selected_author_list"
      }]
    )
    ```
    """
    self._add_processor(SnackIntSetKconfCommonAttrEnricher(kwargs))
    return self

  def snack_int_set_kconf_set_common_attr(self, **kwargs):
    """
    SnackIntSetKconfSetCommonAttrEnricher
    ------
    将 kconf 的 set int64 转化成 int list CommonAttr

    参数配置
    ------
    `kconf_key`: [string] 动态参数 kconf key，仅支持 string list CommonAttr

    `limit_num`: [int] 动态参数 可缺省 每个 kconf_key 取前 limit_num 个，缺省为无限制

    `random`: [bool] 动态参数 可缺省 是否 shuffle 取出来的 结果，默认不 shuffle

    `output_common_attr`: [string] set int64 存储的 CommonAttr

    调用示例
    ------
    ``` python
    .snack_int_set_kconf_set_common_attr(
      kconf_key="{{selected_authors_kconf_key}}",
      limit_num=10,
      random=false,
      output_common_attr="selected_photos"
    )
    ```
    """
    self._add_processor(SnackIntSetKconfSetCommonAttrEnricher(kwargs))
    return self

  def snack_map_kconf_retrieve(self, **kwargs):
    """
    SnackMapKconfRetriever
    ------
    kconf map 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `kconf_key`: [string] 动态参数 kconf key

    `retrieval_num`: [int] 动态参数 召回数量

    `bucket_name_attr`: [string] 存储用户 BucketName 的 string CommonAttr

    `bucket_id_attr`: [string] 存储用户 BucketId 的 int CommonAttr

    `pool_weight`: [string] 存储 redis_prefix 的 string CommonAttr

    调用示例
    ------
    ``` python
    .snack_map_kconf_retrieve(
      kconf_key="{{selected_author_kconf_key}}",
      bucket_name_attr="bucket_name_attr",
      reason=306,
      retrieval_num="{{retrieval_num}}",
      bucket_id_attr="bucket_id_attr",
      pool_weight="{{timely_pool_weights}}"
    )
    .snack_map_kconf_retrieve(
      kconf_key="snackReco.boutique.snackTimely",
      bucket_name_attr="bucket_name_attr",
      reason=306,
      retrieval_num=200,
      bucket_id_attr="bucket_id_attr",
      pool_weight="0.35,0.35,0.15,0.15,0.0"
    )
    ```
    """
    self._add_processor(SnackMapKconfRetriever(kwargs))
    return self

  def snack_fish_predict(self, **kwargs):
    """
    SnackFishPredictItemAttrEnricher
    ------
    kconf map 召回

    参数配置
    ------
    `fish_config`: [array] 转换的列表
      - `kess_service`: [string] 动态参数 访问的 kess service
      - `output_prefix`: [string] 输出 item attr 的前缀
      - `enable`: [bool][array] 动态参数 是否请求这个 fish service，默认为 false.
      - `pred`: [string] 要哪些 predict 参数 ctr, ftr....

    request_attr_path: [object] 从 FishPredictRequest 的 msg path 到 CommonAttr name 的映射

    time_out: [int] 选配项 调用 predict 服务的超时时间，单位 ms, 默认 为 350 ms

    item_instance_path: [object] 从 FishInstanceList 的 msg path 到 ItemAttr name 的映射

    `oversea_user_pb_attr`: [string] 存储用户 ReaderInfo 的 pb 的 string CommonAttr

    `mapping_tag_id_attr`: [string] 选配项 存储 photo 的 mapping_tag_id 的 int list ItemAttr

    `p_tag_id_attr`: [string] 选配项 存储 photo 的 tag_id 的 int list ItemAttr

    `p_tag_level_union_id_attr`: [string] 选配项 存储 photo 的 tag_level_union_id 的 int list ItemAttr

    调用示例
    ------
    ``` python
    .snack_fish_predict(
        fish_config=[{
          "kess_service": "grpc_overseaFishIESnackL2RV1IND",
          "output_prefix": "Fm_pl2r_nn",
          "enable": True,
          "pred": ["ctr"] # ctr
        }],
        oversea_user_pb_attr="oversea_user_pb",
        mapping_tag_id_attr="p_multi_tag_mapping_id",
        request_attr_path = {
          "device_id": "_DEVICE_ID_",
          "user_id": "_USER_ID_",
          "bucket": "bucket_id_attr",
          "region": "user_location_region",
          "city": "user_location_city",
        },
        item_instance_path = {
          "author_id": "a_id",
          "explore_forward": "p_explore_forward_count",
          "explore_like": "p_explore_like_count",
          "reco_photo_info.photo_info.author.common.user_id": "a_id",
          "reco_photo_info.photo_info.meta_data.explore_count.follow_count": "p_explore_follow_count",
          "reco_photo_info.photo_info.meta_data.explore_count.forward_count": "p_explore_forward_count",
          "reco_photo_info.photo_info.meta_data.explore_count.like_count": "p_explore_like_count",
          "reco_photo_info.photo_info.meta_data.explore_count.complete_view_count": "p_explore_complete_view_count",
          "reco_photo_info.photo_info.meta_data.explore_count.short_view_count": "p_explore_short_view_count",
          "reco_photo_info.photo_info.meta_data.explore_count.effective_view_count": "p_explore_effective_view_count",
          "reco_photo_info.photo_info.upload_info.duration_ms": "p_duration_ms",
          "reco_photo_info.photo_info.gender": "p_gender",
          "reco_photo_info.photo_info.upload_info.upload_time_ms": "p_upload_time",
          "reco_photo_info.photo_info.build_time_ms": "p_build_time_ms",
          "reco_photo_info.photo_info.meta_data.explore_count.show_count": "p_explore_show_count",
          "reco_photo_info.photo_info.meta_data.unlogin_explore_count.not_login_show_count": "p_explore_not_login_show_count",
          "reco_photo_info.photo_info.doc_info.mmu_tag_result.definition_level": "p_mmu_tag_definition_level",
          "reco_photo_info.photo_info.doc_info.mmu_tag_result.definition_prob": "p_mmu_tag_definition_prob",
          "reco_photo_info.context_info.fm_predict_rate.ltr": "Fm_pltr",
          "reco_photo_info.context_info.fm_predict_rate.ftr": "Fm_pftr",
          "reco_photo_info.context_info.fm_predict_rate.wtr": "Fm_pwtr",
          "reco_photo_info.context_info.fm_predict_rate.fpr": "Fm_pfpr",
          "reco_photo_info.context_info.fm_predict_rate.vtr": "Fm_pvtr",
          "reco_photo_info.context_info.fm_predict_rate.epr": "Fm_pepr",
          "reco_photo_info.context_info.fm_predict_rate.cmr": "Fm_pcmr",
          "reco_photo_info.context_info.fm_predict_rate.dtr": "Fm_pdtr",
          "reco_photo_info.context_info.fm_predict_rate.lvr": "Fm_plvr",
          "reco_photo_info.context_info.fm_predict_rate.ulr": "Fm_pulr",
          "reco_photo_info.context_info.fm_predict_rate.ufr": "Fm_pufr",
          "reco_photo_info.context_info.fm_predict_rate.svr": "Fm_psvr",
          "reco_photo_info.context_info.fm_predict_rate.fnr": "Fm_pfnr",
          "reco_photo_info.context_info.fm_predict_rate.watch": "Fm_pwatch",
        }
      )
    ```
    """
    self._add_processor(SnackFishPredictItemAttrEnricher(kwargs))
    return self

  def snack_set_kconf_retrieve(self, **kwargs):
    """
    SnackSetKconfRetriever
    ------
    kconf set 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `kconf_key`: [string] [动态参数] kconf key

    `retrieval_num`: [int] [动态参数] 召回数量

    `random`: [int] 是否随机召回, 默认不随机

    `browsed_item_count`: [int] 请求携带的 browsed item 个数，用于过滤， 正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个,  请求携带的 browsed item 是来自 browse set

    调用示例
    ------
    ``` python
    .snack_set_kconf_retrieve(
      kconf_key = "{{selected_author_kconf_key}}",
      reason = 306,
      random = 0,
      retrieval_num = "{{retrieval_num}}"
    )
    .snack_set_kconf_retrieve(
      kconf_key = "snackReco.boutique.snackTimely",
      reason = 306,
      random = 1,
      retrieval_num = 200,
      browsed_item_count = 0
    )
    ```
    """
    self._add_processor(SnackSetKconfRetriever(kwargs))
    return self

  def snack_user_redis_retrieve(self, **kwargs):
    """
    SnackUserRedisRetriever
    ------
    **该接口已进入 DEPRECATE 状态，不再进行功能更新，建议改用 retrieve_by_redis 方法**
    访问 redis 进行 u2i 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `profile_user_attr`: [string] 用于查询 redis 的 user_id, 优先级 profile_user_id > user_id > device_id

    `redis_cluster`: [string] 访问的 redis 集群

    `timeout_ms`: [int] 请求 redis 超时时间

    `prefix`: [string] [动态参数] redis 中存储的 prefix

    `is_only_prefix`: [int] 是否只用 prefix 做 redis_key

    `retrieval_num`: [int] [动态参数] 可缺省 redis 触发返回个数,

    `browsed_item_count`: [int] 请求携带的 browsed item 个数，用于过滤， 正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个,  请求携带的 browsed item 是来自 browse set

    调用示例
    ------
    ``` python
    .snack_user_redis_retrieve(
      redis_cluster = "snackUserRecoCache",
      prefix: "ar",
      num_limit_attr: 100,
    )
    .snack_user_redis_retrieve(
      redis_cluster = "snackUserRecoCache",
      timeout_ms = 20,
      profile_user_attr = "{{profile_user_id}}",
      prefix: "ar",
      is_only_prefix: 1,
      retrieval_num: "{{retrieval_num}}",
      )
    ```
    """
    self._add_processor(SnackUserRedisRetriever(kwargs))
    return self

  def snack_get_user_repr_photo(self, **kwargs):
    """
    SnackUserReprPhotoEnricher
    ------
    通过 user_id 取用户的代表作, 转化成 item_key 的 int list CommonAttr

    参数配置
    ------
    `repr_photo_attr`: [string] 存储输出 item_key 的代表作 int list CommonAttr

    `repr_photo_num_attr`: [string] 存储输出 item_key 的代表作个数 int CommonAttr

    `kess_name`: [string] 调用取用户作品的 rpc 服务 kess_name

    `repr_photo_num`: [int] 获取代表作的个数

    调用示例
    ------
    ``` python
    .snack_get_user_repr_photo(
      repr_photo_attr = "repr_photo",
      repr_photo_num_attr = "repr_photo_num",
      kess_name = "grpc_SnackPhotoAuthorService",
      repr_photo_num = 4,
      browsed_item_count = 0
    )
    ```
    """
    self._add_processor(SnackUserReprPhotoEnricher(kwargs))
    return self

  def snack_get_graph_data(self, **kwargs):
    """
    SnackGraphEnricher 
    ------
    图服务检索，获取 src_attr 为起始节点的 1-3 度关系，存入 dst_attr 的 CommonAttr 中

    参数配置
    ------
    `kess_name`: [string] 调用 graph service rpc 服务的 kess_name

    `timeout_ms`: [int] 请求 graph service 的超时时间 

    `db_name`: [string] GraphDB的名字 

    `src_attr`: [string] 图服务其实检索节点，可以是 int 或者 int list 类型的 CommonAttr

    `depth`: [list] 每一层的查询参数, depth.size() <= 3, 分别对应 graph service 中的 1-3 度关系. 
      - `type`: [int] 边类型 
      - `direction`: [int] 查询方向, 0: out; 1: in.
      - `limit`: [int] 扩散限制. 0: 无限制; >0: 只取前limit个结果.
      - `topn`: [int] topn. 0: 不取topn, 返回所有结果; >0: 对结果进行聚合, 返回前topn个结果.
      - `dst_attr`: [string] 返回结果存入名为 dst_attr 的 CommonAttr 中

    调用示例
    ------
    ``` python
    .snack_get_graph_data(
      kess_name = "grpc_kgraph_SnackPymkFollowGraph",
      db_name = "snack_pymk_follow_graph",
      src_attr = "user_list",
      depth = [
        {"type": 0, "direction": 0, "limit": 50, "topn": 100, "dst_attr": "relation1"}, 
        {"type": 0, "direction": 0, "limit": 100, "topn": 300, "dst_attr": "relation2"}
      ]
    )
    ```
    """
    self._add_processor(SnackGraphEnricher(kwargs))
    return self

  def snack_fetch_user_info(self, **kwargs):
    """
    SnackFetchUserInfoEnricher 
    ------
    从 snack user info service 获取 user profile v1, 并将 protobuf 存入 ItemAttr

    参数配置
    ------
    `kess_service`: [string] 调用 snack user info service rpc 服务的 kess_name

    `timeout_ms`: [string] 请求服务的超时时间，默认为 200 ms

    `bucket_id`: [string] 存储 bucket_id 的 CommonAttr

    `user_id`: [string] 存储 user_id 的 int|int list CommonAttr

    `device_id`: [string] 选配项 存储 device_id 的 string|string list CommonAttr

    `output_reader_info`: [string] 存储 输出 的 user profile v1 的 ItemAttr, item_key 为 user_id

    调用示例
    ------
    ``` python
    .snack_fetch_user_info(
      kess_service = "grpc_snackUserInfoService",
      user_id = "_USER_ID_",
      device_id = "_DEVICE_ID_",
      bucket_id = "bucket_id",
      output_reader_info = "oversea_user_info_pb"
    )
    ```
    """
    self._add_processor(SnackFetchUserInfoEnricher(kwargs))
    return self

  def snack_delegate_enrich(self, **kwargs):
    """
    SnackDelegateEnricher
    ------
    Snack 定制调用另一个基于 CommonLeaf 协议的远程服务进行计算，并填充返回的属性

    参数配置
    ------
    `kess_service`: [string] [动态参数] 调用的 CommonLeaf 的 kess service

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `shard_num`: [int] 被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并。

    `shard_id_offset`: [int] 被调服务的 shard_id 起始偏移量，默认为 0，即 shard 号从 s0 开始

    `shard_by_item_attr`: [string] 按给定的 item attr 将 item list 拆分成不同的 shard 去请求下游服务，默认为空，此时对于各个 shard 的请求完全相同。

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `use_item_id_in_attr`: [string] 选填项，若设置则使用指定 ItemAttr 作为发送的 item_id, 否则使用当前 Item 的 item_id
    
    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`：[list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `for_predict`: [bool] 选填项，标记是否是为预估服务请求，若为 true 则会对接收的 double 类型 item_attr 按 pxtr 进行监控上报，默认为 true

    `use_packed_item_attr`: [bool] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，默认为 false

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，sample_list 的 common_attr key

    `use_sample_list_attr_flatten`: [bool] 选填项，是否**不使用**压缩格式发送 sample_list 服务获取的 common attrs，默认为 true.

    调用示例
    ------
    ``` python
    .snack_delegate_enrichk(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
    )
    # 如果有 attr 需要改名
    .snack_delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      recv_item_attrs = [{"name": "ctr", "as": "pctr"}],
      request_type = "default",
    )
    # 访问 krp-infer-server 可能需要配置从 sample_list, request 获取发送的 common_attr
    .snack_delegate_enrich(
      kess_service = "grpc_KrpCommonLeafTest",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = [{"name": "user_id", "as": "uId"}],
      send_common_attrs_in_request = True,
      exclude_common_attrs = ["userFollowList"],
      recv_item_attrs = ["ctr"],
      use_sample_list_attr_flag = True,
      sample_list_common_attr_key = "kuiba_user_attr_names",
      request_type = "default",
    )
    ```
    """
    self._add_processor(SnackDelegateEnricher(kwargs))
    return self
