#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class SnackPerRandomRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_per_random_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["oversea_user_pb_attr", "bucket_id"]:
      if key in self._config:
        attrs.add(self._config[key])
    for key in ["num_limit", "kess_service", "service_kess_shard"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs


class SnackI2IRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_i2i_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("bucket_name_attr"))
    attrs.add(self._config.get("bucket_id_attr"))
    attrs.add(self._config.get("redis_prefix_attr"))
    attrs.add(self._config.get("left_key_max_num"))
    attrs.update(self.extract_dynamic_params(self._config.get("redis_cluster")))
    for cfg in self._config["strategies"]:
      for key in ["source_item", "common_benefit"]:
        if key in cfg:
          attrs.add(cfg[key])
      if "num_limit" in cfg:
        attrs.update(self.extract_dynamic_params(cfg["num_limit"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["strategies"]:
      if "item_benefit" in cfg:
        attrs.add(cfg["item_benefit"])
      if "additional_conditions" in cfg:
        attrs.update(cfg.get("additional_conditions", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_source_item_to_attr", "save_distance_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("bucket_name_attr"), str) and self._config["bucket_name_attr"], "bucket_name_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("bucket_id_attr"), str) and self._config["bucket_id_attr"], "bucket_id_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("redis_prefix_attr"), str) and self._config["redis_prefix_attr"], "redis_prefix_attr 需为非空字符串")


class SnackMapKconfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_map_kconf_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kconf_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieval_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("pool_weight")))
    attrs.add(self._config.get("bucket_id_attr"))
    attrs.add(self._config.get("bucket_name_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kconf_key"), str) and self._config["kconf_key"], "kconf_key 需为非空字符串")
    check_arg((isinstance(self._config.get("retrieval_num"), str) and self._config["retrieval_num"]) or
        (isinstance(self._config.get("retrieval_num"), int) and self._config["retrieval_num"]), "retrieval_num 需为非空字符串 或者 int")
    check_arg(isinstance(self._config.get("pool_weight"), str) and self._config["pool_weight"], "pool_weight 需为非空字符串")
    check_arg(isinstance(self._config.get("bucket_id_attr"), str) and self._config["bucket_id_attr"], "bucket_id_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("bucket_name_attr"), str) and self._config["bucket_name_attr"], "bucket_name_attr 需为非空字符串")


class SnackSetKconfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_set_kconf_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kconf_key"]))
    attrs.update(self.extract_dynamic_params(self._config["retrieval_num"]))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kconf_key"), str) and self._config["kconf_key"], "kconf_key 需为非空字符串")
    check_arg((isinstance(self._config.get("retrieval_num"), str) and self._config["retrieval_num"]) or
        (isinstance(self._config.get("retrieval_num"), int) and self._config["retrieval_num"]), "retrieval_num 需为非空字符串 或者 int")
    check_arg(isinstance(self._config.get("browsed_item_count", 0), int), "browsed_item_count 需为整数")


class SnackUserRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_user_redis_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("profile_user_attr"))
    attrs.update(self.extract_dynamic_params(self._config["retrieval_num"]))
    attrs.update(self.extract_dynamic_params(self._config["prefix"]))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("redis_cluster"), str) and self._config["redis_cluster"], "redis_cluster 需为非空字符串")
    check_arg(isinstance(self._config.get("prefix"), str) and self._config["prefix"], "redis_cluster 需为非空字符串")
    check_arg((isinstance(self._config.get("retrieval_num"), str) and self._config["retrieval_num"]) or
        (isinstance(self._config.get("retrieval_num"), int) and self._config["retrieval_num"]), "retrieval_num 需为非空字符串 或者 int")
    check_arg(isinstance(self._config.get("browsed_item_count", 0), int), "browsed_item_count 需为整数")
