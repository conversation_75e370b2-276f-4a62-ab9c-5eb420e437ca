#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class SnackRedisIncrbyObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_redis_incrby"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["redis_keys"]))
    attrs.update(self.extract_dynamic_params(self._config.get("expire_s")))
    if "num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["num"]))
    return attrs

class SnackBackupResultObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_backup_result"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["redis_key_attr", "time_attr"]:
      attrs.add(self._config[key])
    return attrs
