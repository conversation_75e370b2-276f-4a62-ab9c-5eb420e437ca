#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafEnricher

class SnackKconfItemAttr<PERSON><PERSON><PERSON>er(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_get_kconf_item_attr"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_item_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

class SnackTensorPredictEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_tensor_predict"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["layer", "kess_service"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    if "common_attr_rename_map" in self._config:
      for _, v in self._config["common_attr_rename_map"].items():
        attrs.add(v)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attr_rename_map" in self._config:
      for _, v in self._config["item_attr_rename_map"].items():
        attrs.add(v)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_item_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

class SnackValidOperationTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_gen_validate_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["operation_triggers", "content_language", "time_now", "user_location_region"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class SnackLookUpMapStringKconfEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_look_up_map_kconf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kconf_key", "look_up_key", "default_key", "default_value"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class SnackI2ILeftKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_i2i_leaf_key_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("bucket_id_attr"))
    attrs.add(self._config.get("left_key_max_num"))
    for cfg in self._config["strategies"]:
      for key in ["source_item", "common_benefit"]:
        if key in cfg:
          attrs.add(cfg[key])
      if "num_limit" in cfg:
        attrs.update(self.extract_dynamic_params(cfg["num_limit"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["strategies"]:
      if "item_benefit" in cfg:
        attrs.add(cfg["item_benefit"])
      if "additional_conditions" in cfg:
        attrs.update(cfg.get("additional_conditions", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_left_terms"))
    attrs.add(self._config.get("output_left_weights"))
    return attrs

class SnackRegionHashCodeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_region_hash_code"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class SnackPatchRecoResultEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_patch_reco_result"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reco_result", "a_admin_mark", "a_admin_type", "p_multi_tag_mapping_id",
                "p_tag_id", "p_tag_level_union_id"]:
      attrs.add(self._config.get(key))
    if "rank_result_sample_attrs" in self._config:
      attrs.update(self._config.get("rank_result_sample_attrs").values())
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("serialize_reco_result"))
    return attrs

class SnackPatchPhotoInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_patch_photo_info"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["photo_info", "p_multi_tag_mapping_id", "p_tag_id",
                "a_admin_mark", "a_admin_type",
                "p_tag_level_union_id"]:
      attrs.add(self._config.get(key))
    return attrs

class SnackTransformItemKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_transform_item_key"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("input_common_attr"))
    attrs.add(self._config.get("bucket_id_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class SnackRedisSetStringEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_redis_set_string"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("redis_keys"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr"))
    return attrs

class SnackIntSetKconfCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_int_set_kconf_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["kconf_configs"]:
      attrs.update(self.extract_dynamic_params(cfg["kconf_key"]))
      attrs.add(cfg["bucket_name_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config["kconf_configs"]:
        attrs.add(cfg["export_common_attr"])
    return attrs

class SnackIntSetKconfSetCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_int_set_kconf_set_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kconf_key", "limit_num", "random"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_common_attr"])
    return attrs

class SnackVideoPlaytimeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_video_play_time_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["video_attr"])
    attrs.add(self._config["video_playing_time_attr"])
    attrs.add(self._config["video_duration_attr"])
    for key in ["enable_play_time_threshold", "play_time_threshold", "duration_ms_threshold"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_item_attr"])
    return attrs

class SnackFishPredictItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_fish_predict"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in self._config["request_attr_path"]:
      attrs.add(self._config["request_attr_path"][key])
    for key in ["oversea_user_pb_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    if "fish_config" in self._config:
      for fish_config in self._config["fish_config"]:
        if "kess_service" in fish_config:
          attrs.update(self.extract_dynamic_params(fish_config["kess_service"]))
        if "enable" in fish_config:
          if type(fish_config["enable"]) == list:
            for enable_config in fish_config["enable"]:
              attrs.update(self.extract_dynamic_params(enable_config))
          else:
            attrs.update(self.extract_dynamic_params(fish_config["enable"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in self._config["item_instance_path"]:
      attrs.add(self._config["item_instance_path"][key])
    for key in ["mapping_tag_id_attr", "p_tag_id_attr",
                "p_tag_level_union_id_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "fish_config" in self._config:
      for fish_config in self._config["fish_config"]:
        for key in fish_config["pred"]:
          if key in ["ctr", "ltr", "ftr", "wtr", "fpr",
                     "evr", "vtr", "epr", "htr", "svr",
                     "lvr", "dtr", "cmr", "watch", "fnr",
                     "ufr", "ulr", "tensor_result", "vtr_v2",
                     "cst", "pst", "wcst", "wpst", "fpr_twice",
                     "watch_v2", "ll2r", "gl2r"]:
            attrs.add(fish_config["output_prefix"] + key)
    return attrs

class SnackAbtestCommonAttrEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    TYPE_NAME_MAP = {
      "int": "int",
      "float": "double",
      "str": "string",
      "bool": "bool",
    }
    for param in config["ab_params"]:
      check_arg("param_name" in param, "ab_params 不能缺少 param_name")
      check_arg(isinstance(param["param_name"], str), "ab_params 里的 param_name 值必须为字符串")
      check_arg(len(param["param_name"]) > 0, "ab_params 里的 param_name 不可为空")
      if "param_type" not in param:
        type_name = type(param["default_value"]).__name__
        check_arg(type_name in TYPE_NAME_MAP, f"非法的 abtest param default_value: {param['default_value']}")
        param["param_type"] = TYPE_NAME_MAP[type_name]
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_get_abtest_params"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return { ab.get("attr_name", ab["param_name"]) for ab in self._config["ab_params"] }

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("bucket_name_attr"))
    return attrs

class SnackUserReprPhotoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_get_user_repr_photo"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["repr_photo_attr"])
    attrs.add(self._config["repr_photo_num_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("repr_photo_attr"), str) and self._config["repr_photo_attr"], "repr_photo_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("repr_photo_num_attr"), str) and self._config["repr_photo_num_attr"], "repr_photo_num_attr 需为非空字符串")

class SnackGraphEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_get_graph_data"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["src_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for d in self._config["depth"]: 
      attrs.add(d["dst_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("src_attr"), str) and self._config["src_attr"], "src_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("depth"), list) and self._config["depth"], "depth 需为非空参数数组")
    for d in self._config["depth"]:
      check_arg("type" in d and isinstance(d["type"], int), "非法的 depth 配置 type error.")
      check_arg("direction" in d and isinstance(d["direction"], int), "非法的 depth 配置 direction error.")
      check_arg("limit" in d and isinstance(d["limit"], int), "非法的 depth 配置 limit error.")
      check_arg("topn" in d and isinstance(d["topn"], int), "非法的 depth 配置 topn error.")
      check_arg("dst_attr" in d and isinstance(d["dst_attr"], str) and len(d["dst_attr"]) > 0, 
        "非法的 depth 配置, dst_attr 需为非空字符串")

class SnackFetchUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_fetch_user_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["device_id", "user_id", "bucket_id"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_reader_info"])
    return attrs

class SnackDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "snack_delegate_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("send_item_attrs", []), "name")
    use_item_id_in_attr = self._config.get("use_item_id_in_attr")
    if use_item_id_in_attr:
      attrs.add(use_item_id_in_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_item_attrs", []), "as")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")
