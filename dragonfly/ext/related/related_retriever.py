#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever


class RelatedRedisIndexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_redis_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["key"]))
    attrs.update(self.extract_dynamic_params(self._config.get("prefix")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],
              "cluster_name 需为非空字符串")

class RelatedRedisIndexTypeRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_redis_index_with_type"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["key"]))
    attrs.update(self.extract_dynamic_params(self._config.get("prefix")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if "save_author_id_to_attr" in self._config:
      attrs.add(self._config["save_author_id_to_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],
              "cluster_name 需为非空字符串")


class RelatedSearchRpcRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_search_rpc"
  
  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("query")))
    attrs.update(self.extract_dynamic_params(self._config.get("page_size")))
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需要非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("page_size"), (int, str)), "page_size 需为整数或字符串类型")
    if isinstance(self._config["page_size"], int):
      check_arg(self._config["page_size"] > 0, "page_size 为整数时需大于 0")
    if isinstance(self._config["page_size"], str):
      check_arg(self._config["page_size"].startswith("{{") and self._config["page_size"].endswith("}}"), "page_size 为字符串时需满足动态参数 {{}} 格式")


