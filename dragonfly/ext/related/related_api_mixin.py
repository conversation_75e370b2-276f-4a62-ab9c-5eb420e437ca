#!/usr/bin/env python3
# coding=utf-8
"""
filename: related_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,related api mixin
author: xing<PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-06-15 20:58:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .related_enricher import *
from .related_retriever import *

class RelatedApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 related 相关的 Processor 接口
  - RelatedFullRankPredictItemAttrEnricher
  - RelatedRedisIndexRetriever
  - RelatedRedisIndexTypeRetriever
  - RelatedRedisEntityEnricher
  """

  def get_full_rank_by_predict_fetcher(self, **kwargs):
    """
    RelatedFullRankPredictItemAttrEnricher
    ------
    通过 PredictKessFetcher 接口访问 GetPredictResult，并获取如下 item_attr:
    - pctr
    - pltr
    - pwtr
    - pftr (如果存在)
    - plvtr
    - psvtr

    参数配置
    ------
    `kess_service`: [string] [动态参数] 预估服务的 kess 服务名

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 80

    `user_info_attr`: [string] user 侧信息通过 `ks::reco::UserInfo` 的序列化字符串传入，存储在该 common_attr 中

    `input_field`: [dict]: 设置输入 protobuf field 和 item attr 的映射关系
      - `pctr`: [string] 输入侧 pctr field 对应的 item attr，可缺省
      - `cascade_pctr`: [string] 输入侧 cascade_pctr field 对应的 item attr，可缺省
      - `cascade_pltr`: [string] 输入侧 cascade_pltr field 对应的 item attr，可缺省
      - `cascade_pwtr`: [string] 输入侧 cascade_pwtr field 对应的 item attr，可缺省
      - `cascade_plvtr`: [string] 输入侧 cascade_plvtr field 对应的 item attr，可缺省
      - `cascade_psvr`: [string] 输入侧 cascade_psvr field 对应的 item attr，可缺省
      - `living`: [string] 输入侧 living field 对应的 item attr，可缺省
      - `user_term_type`: [string] 输入侧 user_term_type field 对应的 item attr，可缺省
      - `photo_term_type`: [string] 输入侧 photo_term_type field 对应的 item attr，可缺省
      - `benifit`: [string] 输入侧 benifit field 对应的 item attr，可缺省
      - `benifit_rank`: [string] 输入侧 benifit_rank field 对应的 item attr，可缺省

    `output_prefix`: [string] 写入 ItemAttr 的 attr_name 前缀，默认值为 ""

    `output_field`: [dict] 设置输出侧 protobuf field 和 item attr 的映射关系
      - `pctr`: [string] 输出侧 pctr field 对应的 item attr，可缺省
      - `pltr`: [string] 输出侧 pltr field 对应的 item attr，可缺省
      - `pwtr`: [string] 输出侧 pwtr field 对应的 item attr，可缺省
      - `pftr`: [string] 输出侧 pftr field 对应的 item attr，可缺省
      - `extend_rate`: [string list] 输出侧 extend_rate field 依次对位对应的 item attr
      - `preds`: [dict] 输出侧 preds map_field 下 protobuf key 与 item attr 的映射关系

    调用示例
    ------
    ``` python
    .get_full_rank_by_predict_fetcher(
      kess_service = "grpc_towerPredictServer",
      timeout_ms = 80,
      user_info_attr = "user_info_str",
      input_field = {
        "cascade_pctr": "item_cascade_pctr",
        "cascade_pltr": "item_cascade_pltr",
        "cascade_pwtr": "item_cascade_pwtr"
      }
      # 为了区别不同输出需求，定义的output前缀
      output_prefix = "full_rank_",
      output_field = {
        "pctr": "pctr",
        "pltr": "pltr",
        "extend_rate": ["psvr", "plvtr", "pvtr"]
      },
    )
    ```
    """
    self._add_processor(RelatedFullRankPredictItemAttrEnricher(kwargs))
    return self

  def filter_item_attr_by_browse_set(self, **kwargs):
    """
    RelatedBrowseSetItemAttrFilterEnricher
    ------
    使用 browse_set 过滤结果集 item 的 ItemAttr

    参数配置
    ------
    `item_attrs`: [list] 待过滤的 ItemAttr 列表

    调用示例
    ------
    ``` python
    .filter_item_attr_by_browse_set(
      item_attrs = ["tag", "cat"]
    )
    ```
    """
    self._add_processor(RelatedBrowseSetItemAttrFilterEnricher(kwargs))
    return self

  def retrieve_by_redis_index(self, **kwargs):
    """
    RelatedRedisIndexRetriever
    ------
    从 redis 召回 index results

    参数配置
    ------
    `cluster_name`: [string] redis cluster name

    `timeout_ms`: [int] 选配项，redis client 超时时间，默认为 10ms

    `reason`: [int] 召回原因，默认为 0

    `key`: [string] [动态参数] redis key

    `prefix`: [string] [动态参数] 选配项，redis key 的前缀

    调用示例
    ------
    ``` python
    .retrieve_by_redis_index(
      cluster_name = "recoRealTimeCF",
      key = "{{redis_key}}",
      prefix = "idx_"
    )
    ```
    """
    self._add_processor(RelatedRedisIndexRetriever(kwargs))
    return self

  def retrieve_by_redis_index_with_type(self, **kwargs):
    """
    RelatedRedisIndexTypeRetriever
    ------
    从 redis 召回带 type 类型的 index results

    参数配置
    ------
    `cluster_name`: [string] redis cluster name

    `timeout_ms`: [int] 选配项，redis client 超时时间，默认为 10ms

    `reason`: [int] 召回原因，默认为 0

    `key`: [string] [动态参数] redis key

    `prefix`: [string] [动态参数] 选配项，redis key 的前缀

    `save_author_id_to_attr`: [string] 选配项，将 type=author_id 的结果存入 common_attr，默认不存

    调用示例
    ------
    ``` python
    .retrieve_by_redis_index_with_type(
      cluster_name = "recoRealTimeCF",
      key = "{{redis_key}}",
      prefix = "idx_"
    )
    ```
    """
    self._add_processor(RelatedRedisIndexTypeRetriever(kwargs))
    return self

  def get_entity_name(self, **kwargs):
    """
    RelatedRedisEntityEnricher
    ------
    从 redis 获取 item 的 entity name

    参数配置
    ------
    `cluster_name`: [string] redis cluster name

    `timeout_ms`: [int] 选配项，redis client 超时时间，默认为 10ms

    `item_prefix`: [string] [动态参数] 选配项，item key 的前缀

    `entity_prefix`: [string] [动态参数] 选配项，entity key 的前缀

    `min_valid_score`: [double] [动态参数] 选配项，entity 相关分数过滤，默认为0

    `get_entity_name`: [bool] 选配项，是否继续从 redis 获取其他类型的 entity name，默认为 true

    `save_entity_name_to_attr`: [string] 将 entity name 存入 item_attr

    调用示例
    ------
    ``` python
    .get_entity_name(
      cluster_name = "recoRealTimeCF",
      item_prefix = " photo2entity:",
      entity_prefix = "entity-info:",
      save_entity_name_to_attr = "entityName"
    )
    ```
    """
    self._add_processor(RelatedRedisEntityEnricher(kwargs))
    return self

  def get_follow_info(self, **kwargs):
    """
    RelatedRedisFollowInfoEnricher
    ------
    从 redis 获取 item 的续集信息

    参数配置
    ------
    `cluster_name`: [string] redis cluster name

    `timeout_ms`: [int] 选配项，redis client 超时时间，默认为 10ms

    `prefix`: [string] [动态参数] 选配项，redis key 的前缀

    `save_follow_info_to_attr`: [string] 选配项，将后续剧集是否存在存入 item_attr

    调用示例
    ------
    ``` python
    .get_follow_info(
      cluster_name = "recoRealTimeCF",
      save_follow_info_to_attr = "hasFollow"
    )
    ```
    """
    self._add_processor(RelatedRedisFollowInfoEnricher(kwargs))
    return self

  def retrieve_from_search(self, **kwargs):
    """
    RelatedSearchRpcRetriever
    ------
    根据指定的 query，从搜索接口获取结果 

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] 搜索 RPC 服务的 kess 服务名

    `service_group`: [string] 搜索 RPC 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 请求搜索 RPC 服务的超时时间，建议最少 500，默认值为 1000

    `query`: [string] [动态参数] 搜索查询词

    `page_size`: [int] [动态参数] 召回的结果数，需要大于 0

    调用示例
    ------
    ``` python
    .retrieve_from_search(
      reason = 100,
      kess_service = "grpc_xxx",
      timout_ms = 500,
      query = "斗罗大陆",
      page_size = 300,
    )
    ```
    """
    self._add_processor(RelatedSearchRpcRetriever(kwargs))
    return self

  def related_layer_tensor(self, **kwargs):
    """
    RelatedLayerTensorEnricher GetTensorResponseBatch请求 支持一次性获取多个layer tensor

    参数配置
    ------
    `kess_service`: [string] 动态参数 请求的 CommonPredictServer 的服务名

    `timeout_ms`: [int] 选配项 请求 CommonPredictServer 超时时间, 单位是毫秒，默认为 200 ms

    `layers`: [list] 设置 GetTensorRequest 中 layers

    `dims`: [list] 请求layers tensor的维度

    `common_attr_rename_map`: [dict] 设置请求 CommonPredictServer 的 common attrs, 映射关系的 key 值为 CommonPredict 服务中的特征名称, value 值为 leaf 里的 attr 名称

    `item_attr_rename_map`: [dict] 设置请求 CommonPredictServer 的 predict item 的 attr, 映射关系的 key 值为 CommonPredict 服务中的特征名称, value 值为 leaf 里的 attr 名称

    `output_item_attrs`: [List] 将获取的 tensor 按 dim拆分后存储到对应的 item attr, size要和dim维度一致

    调用示例
    ------
    ``` python
    .related_layer_tensor(
      common_attr_rename_map={
        "uId": "user_id",
        "dId": "device_id",
      },
      item_attr_rename_map={
        "pId": "photo_id",
        "aId": "author_id",
      },
      layer=["l1", "2"],
      dim=[2, 3],
      output_item_attr=["out1", "out2"],
      timeout_ms=100,
      kess_service="grpc_PredictServer",
      )
    ```
    """
    self._add_processor(RelatedLayerTensorEnricher(kwargs))
    return self