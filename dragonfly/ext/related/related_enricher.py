import base64
import inspect

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class RelatedFullRankPredictItemAttr<PERSON>nricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_full_rank_by_predict_fetcher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["user_info_attr"] }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("input_field", {}).values())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    defined_attrs = ("pctr", "pltr", "pwtr", "pftr")
    output_fields_map = self._config.get("output_field", {})
    attrs = set()
    for attr in defined_attrs:
      if attr in output_fields_map:
        attrs.add(self._config.get("output_prefix", "") + output_fields_map[attr])
    attrs.update(self._config.get("output_prefix", "") + v for v in output_fields_map.get("extend_rate", []))
    attrs.update(self._config.get("output_prefix", "") + v for v in output_fields_map.get("preds", {}).values())
    return attrs

class RelatedBrowseSetItemAttrFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_item_attr_by_browse_set"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config["item_attrs"])

class RelatedRedisEntityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_entity_name"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set();
    attrs.add(self._config["save_entity_name_to_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("item_prefix")))
    attrs.update(self.extract_dynamic_params(self._config.get("entity_prefix")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"], "cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("save_entity_name_to_attr"), str) and self._config["save_entity_name_to_attr"], "save_entity_name_to_attr 需为非空字符串")

class RelatedRedisFollowInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_follow_info"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set();
    attrs.add(self._config["save_follow_info_to_attr"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("prefix")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"], "cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("save_follow_info_to_attr"), str) and self._config["save_follow_info_to_attr"], "save_follow_info_to_attr 需为非空字符串")

class RelatedLayerTensorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "related_layer_tensor"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kess_service"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config[key]))
    if "common_attr_rename_map" in self._config:
      for _, v in self._config["common_attr_rename_map"].items():
        attrs.add(v)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attr_rename_map" in self._config:
      for _, v in self._config["item_attr_rename_map"].items():
        attrs.add(v)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for v in self._config.get("output_item_attrs", []):
      if type(v) is str:
        attrs.add(v)
    return attrs