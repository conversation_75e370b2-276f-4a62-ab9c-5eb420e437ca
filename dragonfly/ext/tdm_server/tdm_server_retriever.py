import operator
import itertools
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class LocalTdmBaseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_tdm"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["top_k", "tree_name", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["tree_level_common_attr_name", "tdm_context_common_attr_name", "user_id_input_common_attr_name", \
        "device_id_input_common_attr_name", "current_candidates_id_input_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["tree_level_common_attr_name", "tdm_context_common_attr_name", \
        "layer_top_k_output_common_attr_name", "is_keep_search_output_common_attr_name", "labels_output_common_attr_name"]) \
        | set(k for k in self._config.get("output_score_param_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["real_seq_output_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    if self._config.get("duration_ms_output_item_attr_name", "") != "":
      attrs.add(self._config.get("duration_ms_output_item_attr_name", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")


class LocalTdmRedisCacheRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_tdm_item_from_redis_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["top_k", "tree_name", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["user_id_attr_name", "device_id_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_hit_attr_name", "tdm_context_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for key in ["tdm_context_common_attr_name", "user_id_attr_name", "device_id_attr_name"]:
      check_arg(isinstance(self._config.get(key), str), key + " 需为 string")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")


class RecoLocalAttrIndexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_attr_index"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class TdmTreeCalcMemKvKeysRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_tree_calc_mem_kv_keys_retriever"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tree_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs


class TdmTreeCalcNodesRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_tree_calc_nodes_retriever"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set([self._config[x] for x in ["output_node_attr",
                "output_left_child_attr", "output_right_child_attr"] if x in self._config])
    return attrs

class TdmTreeCalcNodesByLevelRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_tdm_tree_calc_nodes_by_level"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["current_level"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["children_node_id_output_item_attr_name", "need_update_feature_output_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class TdmTreeCalcFromHdfsRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_tree_calc_retrieve_by_hdfs"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_real_show_count_to_item_attr", "save_click_count_to_item_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs
  
class HdfsTextFileRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "read_hdfs_text_file"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("hdfs_path")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set([self._config[x] for x in ["output_attr"] if x in self._config])
    return attrs

class LocalTdmAllCandidatesRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_all_candidates_by_local_tdm"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["top_k", "tree_name", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["user_id_input_common_attr_name", "device_id_input_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["candidate_size_attr_name"]:
      attrs.add(self._config.get(key, ""))
    attrs.add(self._config.get("frame_version_attr_name", "frame_version"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["real_seq_item_attr_name"]:
      attrs.add(self._config.get(key, "real_seq"))
    for key in ["item_slots_attr_name", "item_signs_attr_name"]:
      attrs.add(self._config.get(key, ""))
    attrs.add(self._config.get("output_node_info_item_attr_name", ""))
    attrs.add(self._config.get("output_level_item_attr_name", ""))
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    for key in ["item_slots_attr_name", "item_signs_attr_name"]:
      check_arg(isinstance(self._config.get(key), str) and len(self._config.get(key)) > 0, key + " 为 string 且不能为空")

class TdmTreeCalcNodesDeltaRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_tdm_tree_calc_delta_nodes"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["node_info_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs