from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class LocalTdmRedisCacheObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_tdm_result_to_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tdm_context_common_attr_name", "user_id_attr_name", "device_id_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for key in ["tdm_context_common_attr_name", "user_id_attr_name"]:
      check_arg(isinstance(self._config.get(key), str), key + " 需为 string")
    if self._config.get("enable_did_key", False):
      check_arg(isinstance(self._config.get("device_id_attr_name"), str), "device_id_attr_name 需为 string")

class LocalTdmEvalRecallRateObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_eval_recall_rate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("candidate_sample_common_attr_name", []))
    attrs.update(self._config.get("pos_sample_common_attr_name", []))
    for key in ["tdm_context_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class TdmSetSelectedCntObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_set_selected_cnt"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tdm_context_common_attr_name", "candidates_id_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class LocalTdmPerfDistObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_perf_dist"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tdm_context_common_attr_name", "tree_level_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for key in ["tdm_context_common_attr_name", "tree_level_common_attr_name"]:
      check_arg(isinstance(self._config.get(key), str), key + " 需为 string")

class TdmCreateTreeObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "create_tdm_tree"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["create_tree_type_input_common_attr_name", "current_tree_version_input_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["leaf_level_output_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("item_features_input_item_attr_name", []))
    for key in ["item_emb_input_item_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class TdmTreeCalcUpdateNodeInfoObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_tree_calc_update_node_info_observer"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([self._config[x] for x in ["node_attr", "probability_attr", "slots_attr", "signs_attr"] if x in self._config]) 
    if "node_attrs" in self._config: # node_attrs 会触发 `TypeError: unhashable type: 'list'` 这个错误
      attrs.update(self._config["node_attrs"])
    return attrs

class TdmTreeMergeNodeInfoObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tdm_tree_merge_node_info_observer"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([self._config[x] for x in ["merge_from_node_info_item_attr_name"] if x in self._config])
    return attrs