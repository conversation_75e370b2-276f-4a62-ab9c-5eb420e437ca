from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class LocalTdmFinalScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tdm_final_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tree_level_common_attr_name", "tdm_context_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("multi_target_input_attr_names", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["output_score_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("multi_target_input_attr_names"), list), "multi_target_input_attr_names 需为 list")
    for key in ["tree_level_common_attr_name", "tdm_context_common_attr_name", "output_score_attr_name"]:
      check_arg(isinstance(self._config.get(key), str), key + " 需为 string")

class FetchRecoUserInfoFromRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_reco_user_info_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["user_id_input_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["user_info_output_common_attr_name"])

class FetchNodeAncestorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_node_ancestor"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("import_common_attr", []))
    for key in ["tree_name", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["level_num_attr_name", "tdm_context_common_attr_name", "user_id_input_common_attr_name", \
        "device_id_input_common_attr_name", "current_pos_sample_id_input_common_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("each_level_ancestor_common_attr_name", []))

class FetchEachLevelCandidateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_each_level_candidate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tree_level"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class FetchNodeSearchedCntEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_node_searched_cnt"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tree_name", "kconf_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ["tdm_context_common_attr_name", "user_id_input_common_attr_name", "device_id_input_common_attr_name", \
      "sample_start_node_seq_id_attr_name", "sample_end_node_seq_id_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["total_node_num_output_attr_name", "node_id_output_common_attr_name", "searched_cnt_output_common_attr_name", "selected_cnt_output_common_attr_name"])

class PrepareItemEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_tensor_from_cache"
  # @property
  # @strict_types
  # def input_common_attrs(self) -> set:
  #   attrs = set()
  #   for key in ["tree_name", "attr_name", "queue_name"]:
  #     attrs.add(self._config.get(key, ""))
  #   for key in ["dim", "gpu_reserve_buffer_max_item_num"]:
  #     attrs.add(self._config.get(key, 0))
  #   for key in ["enable_fp16"]:
  #     attrs.add(self._config.get(key, False))
  #   return attrs

class TdmStartCreateTreeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_start_create_tdm_tree"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["is_create_tree_output_common_attr_name", "create_tree_type_output_common_attr_name", "current_tree_version_output_common_attr_name"])

class TdmFetchItemEmbEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tdm_item_emb"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([self._config[x] for x in ["item_id_attr"] if x in self._config])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in ["item_emb_output_item_attr_name"])

class TdmTreeCalcNodesFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_tdm_tree_calc_nodes_feature"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config[k] for k in ["need_update_feature_item_attr_name", "children_node_id_input_item_attr_name", "total_leaf_num_attr_name"])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in ["need_update_feature_item_attr_name"])

class TdmTreeCalcMetaInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_tdm_tree_calc_meta_info"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config[k] for k in ["is_create_success_output_common_attr_name", "leaf_level_output_common_attr_name"])

class TdmTreeCalcPhotoMapItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_photo_info_from_photo_map"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in ["output_photo_info_attr_name"])

class TdmSoftMaxEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tdm_soft_max"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config[k] for k in ["parent_item_list_attr_name",
                                         "parent_item_embedding_attr_name",
                                         "children_item_list_attr_name",
                                         "dim"])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in ["item_path_score_attr_name",
                                         "item_score_attr_name"])

class LocalTdmSendingMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tdm_sending_message"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["range_start_common_attr_name", "range_end_common_attr_name", "candidate_size_attr_name"]:
      attrs.add(self._config.get(key, ""))
    for key in ["tdm_partition_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    attrs.add(self._config.get("frame_index_attr_name", "frame_index"))
    attrs.add(self._config.get("frame_version_attr_name", "frame_version"))
    attrs.add(self._config.get("frame_num_attr_name", "frame_num"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_top_embedding_attr_name"]:
      attrs.add(self._config.get(key, ""))
    attrs.add(self._config.get("item_real_seq_attr_name", "real_seq"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["is_keep_sending_attr_name", "output_message_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    for key in ["output_message_attr_name", "range_start_common_attr_name", "range_end_common_attr_name", "candidate_size_attr_name",\
      "item_top_embedding_attr_name", "is_keep_sending_attr_name", "embedding_type"]:
      check_arg(isinstance(self._config.get(key), str), key + " 需为 string")
  
class LocalTdmItemInputEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tdm_item_input_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tree_name"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_slots_attr_name", "item_parameters_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_input_embedding_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class FetchLocalNodeEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_node_embedding"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["node_embedding_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class TdmTreeGatAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tdm_tree_gat_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config[k] for k in ["children_node_id_input_item_attr_name",
                                         "items_from_kgnn", "weights_from_kgnn",
                                         "items_number_from_kgnn"])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config[k] for k in ["items_from_kgnn", "weights_from_kgnn"])

class TdmRecoWeightedMultiplyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_weighted_mul"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      for key in ["alpha", "gama", "beta"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }