#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .tdm_server_retriever import *
from .tdm_server_enricher import *
from .tdm_server_observer import *

class TdmServerApiMixin(CommonLeafBaseMixin):
  """
  Tdm Server Processor API 接口的 Mixin 实现
  """

  def retrieve_by_local_tdm(self, **kwargs):
    """
    LocalTdmBaseRetriever
    ------
    本地 TDM 召回
    
    参数
    ------
    `tree_name`: [string] [动态参数], 请求的 tdm tree name

    `kconf_name`: [string] [动态参数], tdm server 使用的 kconf key

    `top_k`: [int] [动态参数]，tdm 召回数量

    `tree_level_common_attr_name`: [string], 从哪个 common attr 获取上一次处理的 tree level / 下次要处理的 tree level 写入哪个 common attr

    `tdm_context_common_attr_name`: [string] 从哪个 common attr 获取 tdm context / 更新后的 tdm context 写入哪个 common attr

    `user_id_input_common_attr_name`: [string] 从哪个 common attr 获取 user_id

    `device_id_input_common_attr_name`: [string] 从哪个 common attr 获取 device_id

    `current_candidates_id_input_common_attr_name`: [string] 从哪个 common_attr 获取上一次处理的候选集

    `layer_top_k_output_common_attr_name`: [string] 下次要处理层的 top_k 写入哪个 common attr

    `is_keep_search_output_common_attr_name`: [string] 是否继续检索下一层写入哪个 common attr

    `labels_output_common_attr_name`: [string] 需要预估的 label index 写入哪个 common attr

    `output_score_param_attrs`: [list] of [string] 多目标预估时用来存储各个预估值权重的 common attr name

    `real_seq_output_item_attr_name`: [string] real_seq 写入哪个 item attr

    `enable_real_seq_double_list`: [bool] real_seq 是否以 double_list 类型输出，默认会以 int 类型输出

    `is_retrieve_current_id`: [bool] [动态参数] 此开关打开会召回current_candidates_id并进行打分，用于tdm指定pid打分

    `duration_ms_output_item_attr_name`: [string] duration_ms 写入哪个 item attr

    `reason`: [int] 召回原因
    
    `tdm_config`: [dict] tdm server 配置 
    
    调用示例
    ------
    ``` python
    .retrieve_by_local_tdm(
      reason=100,
      tree_name="longview",
      kconf_name="reco.model.TDMTreeServer",
      top_k=400,
      tree_level_common_attr_name="tree_level",
      tdm_context_common_attr_name="tdm_context",
      user_id_input_common_attr_name="user_id",
      device_id_input_common_attr_name="device_id",
      current_candidates_id_input_common_attr_name="current_candidates",
      layer_top_k_output_common_attr_name="layer_top_k",
      is_keep_search_output_common_attr_name="is_keep_search",
      labels_output_common_attr_name="labels",
      output_score_param_attrs=["evtr_w", "lvtr_w", "wtr_w"],
      real_seq_output_item_attr_name="real_seq",
      tdm_config={xxx}
    )
    ```
    """
    self._add_processor(LocalTdmBaseRetriever(kwargs))
    return self
  
  def enrich_tdm_final_score(self, **kwargs):
    """
    LocalTdmFinalScoreEnricher
    ------
    本地 TDM 计算多目标最终分数
    
    参数
    ------
    `tree_level_common_attr_name`: [string], 从哪个 common attr 获取本次处理的 tree level

    `tdm_context_common_attr_name`: [string] 从哪个 common attr 获取 tdm context

    `output_score_attr_name`: [string] 计算的分数写入哪个 item attr

    `multi_target_input_attr_names`: [list] of [string] 多目标预估时用来存储各个预估值的 item attr name
    
    调用示例
    ------
    ``` python
    .enrich_tdm_final_score(
      tree_level_common_attr_name="tree_level",
      tdm_context_common_attr_name="tdm_context",
      output_score_attr_name="tdm_score",
      multi_target_input_attr_names=["evtr", "lvtr", "wtr"]
    )
    ```
    """
    self._add_processor(LocalTdmFinalScoreEnricher(kwargs))
    return self

  def fetch_reco_user_info_from_redis(self, **kwargs):
    """
    FetchRecoUserInfoFromRedisEnricher
    ------
    从 redis 获取 reco user info，用于 demo 请求
    
    参数
    ------
    `user_id_input_common_attr_name`: [string] 从哪个 common attr 获取 user_id

    `user_info_output_common_attr_name`: [string] 将查到的 user_info 写入哪个 common attr

    `redis_cluster`: [string] redis cluster name，默认值为 recoNNUserCF
    
    调用示例
    ------
    ``` python
    .fetch_reco_user_info_from_redis(
      user_id_input_common_attr_name="user_id",
      user_info_output_common_attr_name="user_info"
    )
    ```
    """
    self._add_processor(FetchRecoUserInfoFromRedisEnricher(kwargs))
    return self
  
  def enrich_item_tensor_from_cache(self, **kwargs):
    """
    PrepareItemEmbeddingEnricher
    ------
    离线提前把 item embedding copy to gpu, 此 processor 负责从 gpu 读出 emb 构造 tensor
    
    参数
    ------
    `tree_name`: [string] tdm 的树名称，应该与离线计算的统一

    `dim`: [int] 需要提前 cache 的 emb dim, 必须大于 0

    `attr_name`: [string] 需要提前 cache 的 input name

    `enable_scale_int8`: [bool] 是否使用 scale_int8 存储, 默认为 false，优先级高于 enable_fp16

    `enable_fp16`: [bool] 是否使用 fp16 存储, 默认为 false

    `gpu_reserve_buffer_max_item_num`:[int] 需要提前 cache 的 item 的最大数量, 必须大于 0

    `queue_name`: [string] 离线计算的 item emb 产出的 queue

    `index_attr`: [string] 在线计算出需要预估的 item 在 cache 中的位置

    `max_batch_size`: [int] 从 cache 中选出的 item embedding 存储 buffer 的 max batch size 大小, 和后边预估需要保持一致即可

    调用示例
    ------
    ``` python
    .enrich_item_tensor_from_cache(
      tree_name="tdm_fr_one",
      dim=528,
      attr_name="photo_all_embedding",
      gpu_reserve_buffer_max_item_num=5000000,
      queue_name="reco_jingxuan_tdm_uni_predict_item0",
      index_attr="photo_cache_index",
      max_batch_size=8192
    )
    ```
    """
    self._add_processor(PrepareItemEmbeddingEnricher(kwargs))
    return self

  def write_tdm_result_to_redis(self, **kwargs):
    """
    LocalTdmRedisCacheObserver
    ------
    实时计算好结果后将结果写入 redis 缓存, 格式是kv, key 为 key_prefix + uid (+did) , value 为 pb parse 的 string 
    
    参数
    ------
    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context, 不能对 tdm context 进行更新和 dragon context 重写

    `user_id_attr_name`: [string] 必填, 存储 user id 的 common attr name, 不能为空

    `redis_cluster`: [string] 必填, redis 集群名称

    `redis_key_prefix`: [string] key 统一前缀, 默认为 ""

    `enable_did_key`:[bool] 是否加入 device id 作为 redis key 组成部分

    `device_id_attr_name`: [string] 存储 device id 的 common attr name, enable_did_key 为 True 时不能为空

    调用示例
    ------
    ``` python
    .write_tdm_result_to_redis(
      tdm_context_common_attr_name="tdm_context",
      user_id_attr_name="user_id",
      redis_key_prefix="test_",
      redis_cluster="XXXXX"
    )
    ```
    """
    self._add_processor(LocalTdmRedisCacheObserver(kwargs))
    return self

  def retrieve_tdm_item_from_redis_cache(self, **kwargs):
    """
    LocalTdmRedisCacheRetriever
    ------
    把 tdm 结果写到 redis 里缓存
    与 write_tdm_result_to_redis 联合使用
    
    参数
    ------
    `tree_name`: [string] [动态参数] 必填, 请求的 tdm tree name

    `kconf_name`: [string] [动态参数] 必填, tdm server 使用的 kconf key

    `top_k`: [int] [动态参数] 必填, tdm 召回数量

    `user_id_attr_name`:[string] 必填, 存储 user id 的 common attr name

    `device_id_attr_name`: [string] 必填, 存储 device id 的 common attr name

    `reason`: [int] 必填, 召回 reason, 必须 > 0

    `output_hit_attr_name`: [string] 必填, 存储表示'用户请求是否命中缓存'的 int 值(0表示没有命中, 1表示命中) 的 attr_name

    `tdm_context_common_attr_name`: [string] 必填, 用于存储 tdm_context 的 common attr name

    `redis_cluster`: [string] 必填, redis 集群名称, 需要与 write_tdm_result_to_redis 保持一致

    `redis_timeout_ms`: [int] 请求 redis 的超时时间, 默认 20

    `redis_key_prefix`: [string] 必填, key 统一前缀, 默认为 ""

    `enable_did_key`: [bool] 是否加入 device id 作为 redis key 组成部分, 默认 False

    调用示例
    ------
    ``` python
    .retrieve_tdm_item_from_redis_cache(
      redis_cluster="XXXXX",
      reason=1,
      output_hit_attr_name="hit_cache",
      tdm_context_common_attr_name="tdm_context",
      user_id_attr_name="user_id",
      device_id_attr_name="device_id",
      tree_name="{{gamora_tdm_tree_name}}",
      kconf_name="{{gamora_tdm_abtest_kconf_key}}",
      top_k="{{request_num}}"
    )
    ```
    """
    self._add_processor(LocalTdmRedisCacheRetriever(kwargs))
    return self

  def tdm_perf_dist(self, **kwargs):
    """
    LocalTdmPerfDistObserver
    ------
    为某层的结果集作 dist 打点, 目前只实现了 photo duration dist 打点 
    
    参数
    ------
    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context, 不能对 tdm context 进行更新和 dragon context 重写

    `tree_level_common_attr_name`: [string] 必填, 存储 current tree level 的 common attr name, 不能为空

    调用示例
    ------
    ``` python
    .write_tdm_result_to_redis(
      tdm_context_common_attr_name="tdm_context",
      tree_level_common_attr_name="cur_level"
    )
    ```
    """
    self._add_processor(LocalTdmPerfDistObserver(kwargs))
    return self

  def tdm_eval_recall_rate(self, **kwargs):
    """
    LocalTdmEvalRecallRateObserver
    ------
    计算各层召回率
    ------

    参数
    ------
    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context, 不能对 tdm context 进行更新和 dragon context 重写
    `candidate_sample_common_attr_name`: [string_list] 必填, 候选集的 common attr name
    `pos_sample_common_attr_name`: [string_list] 必填, 正样本的 common attr name
    `each_level_topk`: [int_list] 必填, 每一层 topk 的数值, 是一个 int 类型的列表
    `level_num`: [int] 必填, tdm 树的检索层数
    调用示例
    -------
    ``` python
    .tdm_eval_recall_rate(
        tdm_context_common_attr_name="tdm_context",
        candidate_sample_common_attr_name=["candidate_22", "candidate_19", "candidate_16", "candidate_13"],
        pos_sample_common_attr_name=["ancestor_1", "ancestor_2", "ancestor_3", "ancestor_4"],
        each_level_topk=[800, 800, 800, 800],
        level_num=4
    )
    ```
    """
    self._add_processor(LocalTdmEvalRecallRateObserver(kwargs))
    return self

  def tdm_set_selected_cnt(self, **kwargs):
    """
    TdmSetSelectedCntObserver
    ------
    记录每次检索的 topk 计数
    ------

    参数
    ------
    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context, 不能对 tdm context 进行更新和 dragon context 重写
    `candidates_id_common_attr_name`: [string] 必填, 候选集的 common attr name
    `topk`: [int] 必填, tdm 检索的 topk
    调用示例
    -------
    ``` python
    .tdm_set_selected_cnt(
        tdm_context_common_attr_name="tdm_context",
        candidates_id_common_attr_name="candidate_id",
        topk=800
    )
    ```
    """
    self._add_processor(TdmSetSelectedCntObserver(kwargs))
    return self

  def fetch_node_ancestor(self, **kwargs):
    """
    FetchNodeAncestorEnricher
    ------
    上溯结点的祖先结点，并输出到指定的 common attr 里
    ------

    参数
    ------
    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context, 不能对 tdm context 进行更新和 dragon context 重写
    `level_num`: [int] 必填, tdm 树的检索层数
    `user_id_input_common_attr_name`: [string] 从哪个 common attr 获取 user_id
    `device_id_input_common_attr_name`: [string] 从哪个 common attr 获取 device_id
    `current_pos_sample_id_input_common_attr_name`: [string] 从哪个 common attr 获取用户正样本
    `each_level_ancestor_common_attr_name`: [string_list] 各层祖先正样本的输出 common attr, 按顺序依次为 [ 叶子层正样本, 倒数第二层正样本, 倒数第三层正样本, ... ]
    `tdm_config`: [dict] tdm server 配置
    调用示例
    -------
    ``` python
    .fetch_node_ancestor(
        tdm_context_common_attr_name="tdm_context",
        level_num=4,
        user_id_input_common_attr_name="user_id",
        device_id_input_common_attr_name="device_id",
        current_pos_sample_id_input_common_attr_name="user_pos_action",
        each_level_ancestor_common_attr_name=["ancestor_1", "ancestor_2", "ancestor_3", "ancestor_4"],
        tdm_config=tdm_config
    )
    ```
    """
    self._add_processor(FetchNodeAncestorEnricher(kwargs))
    return self
  
  def fetch_node_searched_cnt(self, **kwargs):
    """
    FetchNodeSearchedCntEnricher
    ------
    返回请求结点的searched_cnt，用于热度 bsat 采样
    ------

    参数
    ------
    `tree_name`: [string] [动态参数], 请求的 tdm tree name
    `sample_start_node_seq_id_attr_name`: [string] 必填，请求的起始 node seq id
    `sample_end_node_seq_id_attr_name`: [string] 必填，请求的末尾 node seq id
    `node_id_output_common_attr_name`: [string] 必填，请求的 node id 输出 common attr
    `searched_cnt_output_common_attr_name`: [string] 必填，请求的 searched_cnt 输出 common attr
    `selected_cnt_output_common_attr_name`: [string] 必填，请求的 searched_cnt 输出 common attr
    `tdm_config`: [dict] tdm server 配置
    调用示例
    -------
    ``` python
    .fetch_node_searched_cnt(tree_name="gamora_tdm_tree_name",
                             sample_start_node_seq_id_attr_name="sample_start_seq_id",
                             sample_end_node_seq_id_attr_name="sample_end_seq_id",
                             total_node_num_output_attr_name="sample_total_node_num",
                             node_id_output_common_attr_name="photo_id_list",
                             searched_cnt_output_common_attr_name="searched_cnt_list",
                             selected_cnt_output_common_attr_name="selected_cnt_list",
                             tdm_config=tdm_config
    )
    ```
    """
    self._add_processor(FetchNodeSearchedCntEnricher(kwargs))
    return self

  def fetch_each_level_candidate(self, **kwargs):
    """
    FetchEachLevelCandidateEnricher
    ------
    记录每次打分排序后的候选集, 输出到指定 common attr 里
    ------

    参数
    ------
    `each_level_candidate_common_attr_name_prefix`: [string] 必填, 输出 common attr 的前缀, 最终输出 common attr name 由 each_level_candidate_common_attr_name_prefix + "_" + tree_level 组成
    `tree_level`: [string] 必填, tdm 的层数
    调用示例
    -------
    ``` python
    .fetch_each_level_candidate(
      each_level_candidate_common_attr_name_prefix="candidate",
      tree_level="tree_level")
    ```
    """
    self._add_processor(FetchEachLevelCandidateEnricher(kwargs))
    return self

  def enrich_start_create_tdm_tree(self, **kwargs):
    """
    TdmStartCreateTreeEnricher
    ------
    判断是否开始新一轮的建树流程

    参数
    ------
    `is_create_tree_output_common_attr_name`: [string] 将是否开始新一轮的建树流程写入哪个 common attr

    `create_tree_type_output_common_attr_name`: [string] 将建树类型写入哪个 common attr

    `current_tree_version_output_common_attr_name`: [string] 新一轮建好的树使用的 tree_version 写入哪个 common attr

    `tdm_tree_calc_server_config`: [dict] tdm tree calc server 配置

    调用示例
    ------
    ```python
    .enrich_start_create_tdm_tree(
      is_create_tree_output_common_attr_name="is_create_tree",
      create_tree_type_output_common_attr_name="create_tree_type",
      current_tree_version_output_common_attr_name="current_tree_version",
      tdm_tree_calc_server_config={xxx}
    )
    ```
    """
    self._add_processor(TdmStartCreateTreeEnricher(kwargs))
    return self

  def retrieve_by_local_attr_index(self, **kwargs):
    """
    RecoLocalAttrIndexRetriever
    ------
    从本地 attr index 召回
    
    参数
    ------
    `reason`: [int] 召回原因

    调用示例
    ------
    ``` python
    .retrieve_by_local_attr_index(
      reason=100
    )
    ```
    """
    self._add_processor(RecoLocalAttrIndexRetriever(kwargs))
    return self

  def fetch_tdm_item_emb(self, **kwargs):
    """
    TdmFetchItemEmbEnricher
    ------
    从 tree calc server 本地 kv 获取 item embedding

    参数
    ------
    `item_emb_output_item_attr_name`: [string] 将 item embedding 写入哪个 item attr

    `item_id_attr`: [string] 从哪个 attr 获取 item_id, 默认为空使用 item_key

    调用示例
    ------
    ```python
    .fetch_tdm_item_emb(
      item_emb_output_item_attr_name="item_emb"
    )
    ```
    """
    self._add_processor(TdmFetchItemEmbEnricher(kwargs))
    return self

  def create_tdm_tree(self, **kwargs):
    """
    TdmCreateTreeObserver
    ------
    创建 tdm tree

    参数
    ------
    `create_tree_type_input_common_attr_name`: [string] 从哪个 common attr 获取 create_tree_type

    `current_tree_version_input_common_attr_name`: [string] 从哪个 common attr 获取 tree_version

    `item_emb_input_item_attr_name`: [string] 从哪个 item attr 获取 item_embedding

    `node_info_input_item_attr_name`: [string] 从哪个 item attr 获取 node_info

    `item_features_input_item_attr_name`: [string list] 从哪些 item attr 获取 item_features

    `create_tree`: [bool] 是否建树

    `dump`: [bool] 是否 dump tree

    `send_to_btq`: [bool] 是否 send to btq，注意如果 send_to_btq 设置为 true， 则 dump 也必须设置为 true

    `node_id_attr_name`: [string] (可选) 指定建树结点中id的attr来源，若不指定，默认从 `key_sign` 中提取
    
    调用示例
    ------
    ```python
    .create_tdm_tree(
      create_tree_type_input_common_attr_name="create_tree_type",
      current_tree_version_input_common_attr_name="tree_version",
      item_emb_input_item_attr_name="item_emb",
      node_info_input_item_attr_name="reco_node_info",
      item_features_input_item_attr_name=["pId", "aId"]
    )
    ```
    """
    self._add_processor(TdmCreateTreeObserver(kwargs))
    return self

  def tdm_tree_calc_mem_kv_keys_retriever(self, **kwargs):
    """
    TdmTreeCalcMemKvKeysRetriever
    -----
    提取 TDM Mem KV Keys
    -----
    `reason`: [int] 召回原因 

    调用示例
    -----
    ```python
    .retrieve_tdm_memkv_keys(
      reason=100,
    )
    ```
    """
    self._add_processor(TdmTreeCalcMemKvKeysRetriever(kwargs))
    return self
  
  def tdm_tree_calc_nodes_retriever(self, **kwargs):
    """
    TdmTreeCalcNodesRetriever
    -----
    retrieve tdm nodes
    -----
    `reason`: [int] 召回原因

    `output_node_attr`: [string] 输出 node attr

    `output_left_child_attr`: [string] 输出节点左儿子 attr

    `output_right_child_attr`: [string] 输出节点右儿子 attr

    调用示例
    -----
    ```python
    .tdm_tree_calc_ndoes_retriever(
      reason=100,
      output_node_attr="tdm_node",
      output_left_child_attr="left_child",
      output_right_child_attr="right_child",
    )
    ```
    """
    self._add_processor(TdmTreeCalcNodesRetriever(kwargs))
    return self

  def tdm_tree_calc_update_node_info_observer(self, **kwargs):
    """
    TdmTreeCalcUpdateNodeInfoObserver
    -----
    更新树节点的 attrs
    -----
    `node_attr`: [string] node attr 
    
    `probability_attr`: [string] 采样概率 attr

    `node_attrs`: [list] 写入 node info attrs 里的特征

    `enable_update_feature_sign`: [bool] 是否更新 slots 和 signs 到 node_info，默认 false

    `slots_attr`: [string] 从哪个 item attr 读取 slots

    `signs_attr`: [string] 从哪个 item attr 读取 signs

    调用示例
    -----
    ```python
    .tdm_tree_calc_update_node_info_observer(
      node_attr="tdm_node",
      probability_attr="probability",
      node_attrs=["aId", "age"],
    )
    ```
    """
    self._add_processor(TdmTreeCalcUpdateNodeInfoObserver(kwargs))
    return self

  def retrieve_tdm_tree_calc_nodes_by_level(self, **kwargs):
    """
    TdmTreeCalcNodesByLevelRetriever
    ------
    从 tdm tree calc server 获取指定 level 的 node_id

    参数
    ------
    `reason`: [int] 召回原因

    `current_level`: [int] [动态参数] 获取哪个 level 的 node_id

    `children_node_id_output_item_attr_name`: [string] 将 node 的儿子节点 id 写入哪个 item attr

    `need_update_feature_output_item_attr_name`: [string] 将后续是否需要更新节点的特征值标记写入哪个 item attr

    调用示例
    ------
    ```python
    .retrieve_tdm_tree_calc_nodes_by_level(
      reason=100,
      current_level=20,
      children_node_id_output_item_attr_name="children_node_id",
      need_update_feature_output_item_attr_name="need_update_feature"
    )
    ```
    """
    self._add_processor(TdmTreeCalcNodesByLevelRetriever(kwargs))
    return self
  
  def update_tdm_tree_calc_nodes_feature(self, **kwargs):
    """
    TdmTreeCalcNodesFeatureEnricher
    ------
    根据子节点的特征值更新父节点的特征值

    参数
    ------
    `need_update_feature_item_attr_name`: [string] 从哪个 item attr 读取是否需要更新该节点的特征值标记

    `children_node_id_input_item_attr_name`: [string] 从哪个 item attr 读取该节点的字节点 node_id

    `discrete_attrs`: [string_list] 离散类型的特征 attr_name，会生成 fake_id 作为该种类型特征的值

    `continuous_attrs`: [string_list] 连续类型的特征 attr_name，会通过计算子节点的平均值作为该种类型特征的值

    `id_offset`: [int] 默认值为 100000000000000L，用于生成 fake_id，fake_id = item_key + id_offset

    `attr_offset`: [list] attr offset 个性化, 支持对不同的 attr 配置不同的 offset, 没配置则使用上面的 id_offset

    `total_leaf_num_attr_name`: [int] 对连续类型的特征计算均值, 不配置则不计算均值

    `is_root_node_attr_name`: [string] 从哪个 item attr 读取该节点是否为 root 的标识, 不配置则不读取

    调用示例
    ------
    ```python
    .update_tdm_tree_calc_nodes_feature(
      need_update_feature_item_attr_name="need_update_feature",
      children_node_id_input_item_attr_name="children_node_id",
      discrete_attrs=["author_id", "tag"],
      continuous_attrs=["click_num", "like_num"],
      total_leaf_num_attr_name="total_leaf_num",
      is_root_node_attr_name="is_root_node",
      attr_offset=[{"author_id_offset": 50000000000}, {"hetu_tag_id_offset": 100000000000000}]
    )```
    """
    self._add_processor(TdmTreeCalcNodesFeatureEnricher(kwargs))
    return self

  def get_tdm_tree_calc_meta_info(self, **kwargs):
    """
    TdmTreeCalcMetaInfoEnricher
    ------
    从 tree calc server 获取树相关的信息

    参数
    ------
    `is_create_success_output_common_attr_name`: [string] 将建树是否成功标记写入哪个 common attr

    `leaf_level_output_common_attr_name`: [string] 将 leaf_level 写入哪个 common attr

    调用示例
    ------
    ```python
    .get_tdm_tree_calc_meta_info(
      is_create_success_output_common_attr_name="is_create_success",
      leaf_level_output_common_attr_name="leaf_level"  
    )```
    """
    self._add_processor(TdmTreeCalcMetaInfoEnricher(kwargs))
    return self

  def tdm_tree_calc_retrieve_by_hdfs(self, **kwargs):
    """
    TdmTreeCalcFromHdfsRetriever
    ------
    从 HDFS 获取建树的 item 候选集，HDFS 文件格式如下：
    item_id \t xxx \t real_show_count \t click_count
    
    参数
    ------
    `reason`: [int] 召回原因

    `hdfs_user_name`: [string] HDFS 的用户名，默认值 "reco_proinc"

    `hdfs_file_path_prefix`: [string] HDFS 文件路径前缀，默认值 "viewfs:///home/<USER>/mpi/chenguozhang/photo_show_cnt/"

    `save_real_show_count_to_item_attr`: [string] 从 HDFS 文件读取的 real_show_count 写入哪个 item attr

    `save_click_count_to_item_attr`: [string] 从 HDFS 文件读取的 click_count 写入哪个 item attr 

    调用示例
    ------
    ```python
    .tdm_tree_calc_retrieve_by_hdfs(
      reason=100,
      hdfs_user_name="reco_proinc",
      hdfs_file_path_prefix="viewfs:///home/<USER>/mpi/chenguozhang/photo_show_cnt/",
      save_real_show_count_to_item_attr="real_show_count",
      save_click_count_to_item_attr="click_count"
    ) ```
    """
    self._add_processor(TdmTreeCalcFromHdfsRetriever(kwargs))
    return self
  
  def read_hdfs_text_file(self, **kwargs):
    """
    ReadHdfsTextFileRetriever
    -----
    通过 hdfsBuilder 读取 hdfs 格式的 text 文件，请注意该 Processor 特别耗时，仅限于用作离线任务。
    
    参数
    -----
    `reason`: [int] 召回原因

    `hdfs_user`: [string] hdfs 用户名，默认为 mpi

    `hdfs_path`: [string] [动态参数] hdfs 路径
    
    `matching_pattern`: [int] (即将支持) 0表示精确名字，1表示自动搜索最近的文件
    -----
    ```python
    .read_hdfs_text_file(
      hdfs_path="viewfs://path/to/your/file",
    )```
    """
    self._add_processor(HdfsTextFileRetriever(kwargs))
    return self

  def enrich_photo_info_from_photo_map(self, **kwargs):
    """
    TdmTreeCalcPhotoMapItemAttrEnricher
    ------
    从 PhotoMap 获取 Photo Info 

    参数
    ------
    `output_photo_info_attr_name`: [string] 将 photo info 写入哪个 item attr

    调用示例
    ------
    ```python
    .enrich_photo_info_from_photo_map(
      output_photo_info_attr_name="photo_info" 
    )```
    """
    self._add_processor(TdmTreeCalcPhotoMapItemAttrEnricher(kwargs))
    return self

  def enrich_tdm_soft_max(self, **kwargs):
    """
    TdmSoftMaxEnricher
    ------
    根据父子节点的embedding计算子节点的softmax

    参数
    ------
    `parent_item_list_attr_name`: [string] 从哪个 common attr 读取父节点 list, 计算第一层节点时只填一个

    `parent_item_embedding_attr_name`: [string] 从哪个 item attr 读取父节点的 embedding

    `children_item_list_attr_name`: [string] 从哪个 item attr 读取子节点 list

    `dim`: [int] 默认值为 512，用于校验 embedding 维度

    `item_path_score_attr_name`: [string] item path score 输出到哪个 item attr

    `item_score_attr_name`: [string] item score 输出到哪个 item attr

    调用示例
    ------
    ```python
    .enrich_tdm_soft_max(
      parent_item_list_attr_name="parent_item_list",
      parent_item_embedding_attr_name="parent_item_embedding",
      children_item_list_attr_name="children_item_list",
      dim=512,
      item_path_score_attr_name="item_path_score_attr"
      item_score_attr_name="item_score_attr"
    )```
    """
    self._add_processor(TdmSoftMaxEnricher(kwargs))
    return self

  def retrieve_all_candidates_by_local_tdm(self, **kwargs):
    """
    LocalTdmAllCandidatesRetriever
    ------
    本地 TDM 召回树上所有参与 top embedding 计算的节点
    
    参数
    ------
    `tree_name`: [string] [动态参数] 必填, 请求的 tdm tree name

    `kconf_name`: [string] [动态参数] 必填, tdm server 使用的 kconf key

    `top_k`: [int] [动态参数] 必填, tdm 召回数量

    `tdm_context_common_attr_name`: [string] 必填, 从哪个 common attr 获取 tdm context / 更新后的 tdm context 写入哪个 common attr

    `user_id_input_common_attr_name`: [string]  从哪个 common attr 获取 user_id, 默认为空

    `device_id_input_common_attr_name`: [string] 从哪个 common attr 获取 device_id, 默认为空

    `candidate_size_attr_name`: [string] 必填, 存储候选集大小的 common attr name

    `frame_version_attr_name`: [string] 候选集的版本需要写入的 common_attr name, 默认为 "frame_version"

    `item_real_seq_attr_name`: [string] 候选集每个 node 的 real_seq 需要写入的 item attr name, 默认为 "real_seq"

    `use_slot_signs`: [bool] 是否直接使用 pb 自带的 item slot 和 signs, 免去抽 sign 的流程, 默认是 True

    `item_slots_attr_name`: [string] use_slot_signs=True 时需要配置, 从 pb 中取出的 slots 存放的 item attr name

    `item_signs_attr_name`: [string] use_slot_signs=True 时需要配置, 从 pb 中取出的 signs 存放的 item attr name

    `use_photo_info`: [bool] 是否填充每个 item 的 photo_info, 默认 false

    `output_photo_into_item_attr_name`: [string] 输出 photo_info ptr 的 item attr name,
      use_photo_info 为 true 时需要填, 默认是 "photo_info"

    `reason`: [int] 召回原因
    
    `tdm_config`: [dict] tdm server 配置
    
    调用示例
    ------
    ``` python
    .retrieve_all_candidates_by_local_tdm(
      reason=100,
      tree_name="longview",
      kconf_name="reco.model.TDMTreeServer",
      top_k=400,
      tdm_context_common_attr_name="tdm_context",
      user_id_input_common_attr_name="user_id",
      device_id_input_common_attr_name="device_id",
      candidate_size_attr_name="nodes_size",
      item_slots_attr_name="item_slots",
      item_signs_attr_name="item_signs",
      tdm_config={xxx}
    )
    ```
    """
    self._add_processor(LocalTdmAllCandidatesRetriever(kwargs))
    return self

  def enrich_tdm_sending_message(self, **kwargs):
    """
    LocalTdmSendingMessageEnricher
    ------
    将算好的 node top embedding 分批次组成 frame, 本 processor 封装一个 frame 的生成, 供后续发送消息队列
    
    参数
    ------
    `output_message_attr_name`: [string] 必填, 存放序列化后用来发送的 string message 的 common attr name

    `frame_index_attr_name`: [string] 存放第几个 frame 标号的 common_attr name, 默认为 "frame_index"

    `frame_version_attr_name`: [string] 候选集的版本需要写入的 common_attr name, 默认为 "frame_version"

    `frame_num_attr_name`: [string] 存放总共 frame 数量的 common_attr name, 默认为 "frame_num"

    `candidate_size_attr_name`: [string] 必填, 存放候选集总数的 common_attr name

    `item_real_seq_attr_name`: [string] 候选集每个 node 的 real_seq 需要写入的 item attr name, 默认为 "real_seq"

    `range_start_common_attr_name`: [string] 必填, 存放此 frame 起始的 node vec 下标的 common_attr name

    `range_end_common_attr_name`: [string] 必填, 存放此 frame 结束的 node vec 下标的 common_attr name
 
    `is_keep_sending_attr_name`: [string] 必填, 此 processor 用来处理 tdm 候选集合，一般用于循环类型的 flow,
        这个变量存储 int 值，用于标示是否继续循环，生成下一个 frame, 需要上游 processor 置初始值 1

    `embedding_type`: [string] 上游预估输出的 embedding 元素类型, 默认为 float32, 支持 float32 | int32 | float16
    
    调用示例
    ------
    ``` python
    .enrich_tdm_sending_message(
      output_message_attr_name="output_message",
      item_top_embedding_attr_name="photo_top_layer_int32",
      embedding_type="int32",
      tdm_partition_size=20000,
      candidate_size_attr_name="nodes_size",
      range_start="{{range_start}}",
      range_end="{{range_end}}",
      range_start_common_attr_name="frame_start",
      range_end_common_attr_name="frame_end",
      is_keep_sending_attr_name="is_keep_sending",
    )
    ```
    """
    self._add_processor(LocalTdmSendingMessageEnricher(kwargs))
    return self
  
  def enrich_tdm_item_input_embedding(self, **kwargs):
    """
    LocalTdmItemInputEmbeddingEnricher
    ------
    将算好的 node top embedding 分批次组成 frame, 本 processor 封装一个 frame 的生成, 供后续发送消息队列
    
    参数
    ------
    `item_slots_attr_name`: [string] 必填, 存放 item slots 的 item_attr_name

    `item_parameters_attr_name`: [string] 必填, 存放 item parameterss 的 item_attr_name

    `item_input_embedding_attr_name`: [string] 必填, 拼接好的 input_embedding 存放的 item_attr_name

    `slots_config`: [json] 必填, 需要拼接的原始 input slot 配置, 为 json array 格式
      `common`: [bool] 是否为 user 特征, 默认 false
      `input_name`: [string] 必填, 特征名字
      `dtype`: [string] 支持 float | float32 | float16 | double | float64 等所有 tensorflow 的基本输入type
      `dim`: [int] 必填, 每个 slot 对应 embedding 的维度
      `slots`: [int | string] 必填, 只有一个 slot 时填 slot 值, 有多个 slot 时, 填按空格划分的 slot 组成的string
      `expand`: [int] expand 个数, 默认为 1, 不 expand
      `sized`: [bool] 默认 false, 不支持 true
      `sentry`: [bool] 默认 false, 不支持 true

    `kess_service`: [string] 必填, item embedding server kess name

    `service_group`: [string] item embedding server group, 默认 "PRODUCTION"

    `shard_num`: [int] item embedding server shard 总数, 默认 1

    `timeout_ms`: [int] 请求 item embedding server 超时时间, 默认 20000 (ms)

    `max_signs_per_request`: [int] 请求 item embedding server 每个 request 承载的 sign 数量, 默认 1000

    `thread_num`: [int] 同时向 item embedding server 发送请求的并发度, 默认 32, 不推荐过大
    
    调用示例
    ------
    ``` python
    .enrich_tdm_item_input_embedding(
      item_slots_attr_name="item_slots",
      item_parameters_attr_name="item_parameters",
      item_input_embedding_attr_name="item_input_embedding",
      slots_config=[{
            "dim": 16,
            "dtype": "float16",
            "input_name": "photo_all_id_cache",
            "slots": "807 817 808 818 810 820 811 821 812 822 813 823 814 824 517 537 518 538 519 539",
            "trainable": false,
            "use_v2": true
          }, {
            "dim": 8,
            "dtype": "float16",
            "input_name": "photo_mix_id_cache",
            "slots": "540 541 542 543 544 545 546 547 548 549",
            "trainable": false,
            "use_v2": true
          }],
      kess_service="XXXXX",
      shard_num=8,
      thread_num=16,
      timeout_ms=40000
    )
    ```
    """
    self._add_processor(LocalTdmItemInputEmbeddingEnricher(kwargs))

  def fetch_local_node_embedding(self, **kwargs):
    """
    FetchLocalNodeEmbeddingEnricher
    ------
    从本地树取节点上的 embedding, NodeInfo 的 data 值

    参数
    ------
    `node_embedding_attr`: [string] 必填, 存储 node embedding 的 item attr name
    
    调用示例
    ------
    ``` python
    .fetch_local_node_embedding(
      node_embedding_attr="node_embedding",
    )
    ```
    """
    self._add_processor(FetchLocalNodeEmbeddingEnricher(kwargs))
    return self
  
  class LocalTdmSampleRetriever(LeafRetriever):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
      return "local_tdm_base_sample"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      for key in ["tdm_context_common_attr_name", "user_id_input_common_attr_name", \
          "device_id_input_common_attr_name", "sample_response_attr_name"]:
        attrs.add(self._config.get(key, ""))
      return attrs

  def retrieve_tdm_tree_calc_delta_nodes(self, **kwargs):
    """
    TdmTreeCalcNodesDeltaRetriever
    ------
    获取建树完成后的 delta 节点, 并填充 node info

    参数
    ------
    `reason`: [int] 召回原因

    `node_info_item_attr_name`: [string] 将 node info 写入哪个 item attr

    调用示例
    ------
    ```python
    .retrieve_tdm_tree_calc_delta_nodes(
      node_info_item_attr_name="reco_node_info"
    )```
    """
    self._add_processor(TdmTreeCalcNodesDeltaRetriever(kwargs))
    return self

  def tdm_tree_merge_node_info_observer(self, **kwargs):
    """
    TdmTreeMergeNodeInfoObserver
    -----
    回填树节点的 node info
    -----
    `merge_from_node_info_item_attr_name`: [string] 从哪个 item attr 读取 node info 做 merge from 操作

    调用示例
    -----
    ```python
    .tdm_tree_merge_node_info_observer(
      merge_from_node_info_item_attr_name="reco_node_info",
    )
    ```
    """
    self._add_processor(TdmTreeMergeNodeInfoObserver(kwargs))
    return self

  def enrich_tdm_tree_gat_attr(self, **kwargs):
    """
    TdmTreeGatAttrEnricher
    ------
    根据子节点的 GAT 特征值更新父节点的 GAT 特征值

    参数
    ------
    `children_node_id_input_item_attr_name`: [string] 从哪个 item attr 读取该节点的字节点 node_id

    `items_from_kgnn`: [int_list] GAT kgnn item list 特征

    `weights_from_kgnn`: [list] GAT kgnn item 权重特征

    `items_number_from_kgnn`: [int] GAT kgnn item 数量

    调用示例
    ------
    ```python
    .enrich_tdm_tree_gat_attr(
      children_node_id_input_item_attr_name="children_node_id",
      items_from_kgnn="items_from_kgnn",
      weights_from_kgnn="weights_from_kgnn",
      items_number_from_kgnn=50,
    )```
    """
    self._add_processor(TdmTreeGatAttrEnricher(kwargs))
    return self

  def calc_weighted_mul(self, **kwargs):
    self._add_processor(TdmRecoWeightedMultiplyEnricher(kwargs))
    return self
