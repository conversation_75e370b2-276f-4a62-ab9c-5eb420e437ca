#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class LocalLifePrepareItemEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_prepare_item_embedding"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["poi_id_input_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["item_index_output_attr_name", "poi_index_output_attr_name"]:
      attrs.add(self._config.get(key, ""))
    return attrs

class LocalLifeCommonUserFeatureProcessEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_common_user_feature_process"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for each_op in self._config.get("ops", []):
      attrs.add(each_op.get("target_attr", ""))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for each_op in self._config.get("ops", []):
      attrs.add(each_op.get("match_list_attr", ""))
      attrs.update(each_op.get("input_list_attr", []))
      for each_filter in each_op.get("filter", []):
        attrs.add(each_filter.get("input_list_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for each_op in self._config.get("ops", []):
      if each_op.get("op", "") == "filter_list":
        attrs.update(each_op.get("output_list_attr", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for each_op in self._config.get("ops", []):
      if each_op.get("op", "") == "match_list":
        attrs.update(each_op.get("output_list_attr", []))
    return attrs

class LocalLifePxtrQuantileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_enrich_pxtr_quantile"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs_hint", []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs_hint", []))
    attrs.update(self.extract_dynamic_params(self._config.get("kconf_key", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_clip_quantile", "")))
    for q_config in self._config.get("quantiles", []):
      attrs.add(q_config["config_name_attr"])
      attrs.add(q_config["quantile_attr"])
      attrs.update(self.extract_dynamic_params(q_config.get("enabled", "")))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for q_config in self._config.get("quantiles", []):
      if q_config.get("output_thresh_attr"):
        attrs.add(q_config.get("output_thresh_attr"))
    return attrs
  
class LocalLifePxtrCacheFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_pxtr_cache_fetch_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in self._config.get("pxtr_attrs", []):
      attrs.add(key)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["redis_cluster", "redis_timeout", "redis_valid_seconds", "redis_part_num"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key, "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class LocalLifePxtrCachePushEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_pxtr_cache_push_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in self._config.get("pxtr_attrs", []):
      attrs.add(key)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["redis_cluster", "redis_timeout", "redis_valid_seconds", "redis_pack_size", "redis_part_num"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key, "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class LocalLifeColossusV2BatchHandleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_colossus2_batch_handle_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "input_int_attr" in self._config:
      attrs.add(self._config["input_int_attr"])
    for key in ["filter_label", "output_aid_list_size", "sort_type", "min_timestamp_sec", "max_timestamp_sec"]:
      if key in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(key, "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for key in ["output_aid_list_attr", "output_ts_list_attr", "output_pay_order_amt_list_attr",
                "output_live_id_list_attr", "output_page_module_id_list_attr", "output_label_list_attr",
                "output_play_duration_list_attr", "output_auto_play_duration_list_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs