#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .l2model_mixer import *
from .l2model_enricher import *


class LocalLifeModelApiMixin(CommonLeafBaseMixin):
  def local_life_model_mix_photo2goods_4goods_model(self, **kwargs):
    """
    LocalLifeGoodsModelPhoto2GoodsMixer
    ------
    根据 Photo 表创建 Goods 表并填充 Item 属性

    参数配置
    ------
    `input_table`: [string] 选配项，默认为主表，长度为 0 的字符串
    `copy_item_attrs`: [list] 选配项，直接复制的 Item 属性
    `extra_item_attrs`: [list] 选配项，依赖的其他 Item 属性
    `output_table`: [string] 选配项，派生表的名称，默认为 goods
    `output_item_attrs`: [list] 选配项，派生表的属性

    调用示例
    ------
    ``` python
    .local_life_model_mix_photo2goods_4goods_model(
      input_table = "",
      copy_item_attrs = [],
      extra_item_attrs = [],
      output_table = "goods",
      output_item_attrs = [])
    ```
    """
    self._add_processor(LocalLifeGoodsModelPhoto2GoodsMixer(kwargs))
    return self
  
  def local_life_model_mix_goods2photo_4goods_model(self, **kwargs):
    """
    LocalLifeGoodsModelGoods2PhotoMixer
    ------
    根据 Goods 表填充 Photo 表 Item 属性

    参数配置
    ------
    `input_table`: [string] 选配项，默认为 goods
    `copy_item_attrs`: [list] 选配项，直接复制的 Item 属性
    `extra_item_attrs`: [list] 选配项，依赖的其他 Item 属性
    `output_table`: [string] 选配项，派生表的名称，长度为 0 的字符串
    `output_item_attrs`: [list] 选配项，派生表的属性

    调用示例
    ------
    ``` python
    .local_life_model_mix_goods2photo_4goods_model(
      input_table = "goods",
      copy_item_attrs = [],
      extra_item_attrs = [],
      output_table = "",
      output_item_attrs = [])
    ```
    """
    self._add_processor(LocalLifeGoodsModelGoods2PhotoMixer(kwargs))
    return self

  def local_life_prepare_item_embedding(self, **kwargs):
    """
    LocalLifePrepareItemEmbeddingEnricher 
    ------
    本地生活全量召回 embedding 填充模式：
    从多张 GPU 卡获取已缓存全量 item embedding 和 poi embedding 的首地址、数据长度、dim 信息，并将 embeding 地址存放在 context 中。

    同时可以根据当前候选集的 item_id 和 poi_id 查询其在全量 item embedding 和 poi embedding 中的 offset，用于后续预估时 gather 使用

    参数配置
    ------
    `gpu_local_life_embed`: [dict] gpu local life embedding 双 buffer 更新的相关配置

                       - `embedding_queue` : [list[string]] embedding 更新流 btqueue

                       - `embedding_queue_thread_num` : [int] embedding 更新线程数, 默认 1 

                       - `update_interval_sec` : [int] embedding 往 gpu copy 的间隔时间, 默认 60 秒

                       - `use_fp16`: [bool] 是否使用 fp16 模式，将 embedding 以 fp16 的格式缓存在 GPU 中，默认为 false

                       - `item_config` : [dict] item embedding 相关配置
                             
                              - `slot`: [int] 用于从 btqueue 消费到的 embedding 中过滤出来 embedding

                              - `memkv_part_num` : [int] embedding 内部空间分 part ，默认 16

                              - `memkv_expire_sec` : [int] embedding 不更新时的强制过期时间，默认 0 秒

                              - `memkv_capacity` : [int] embedding 最大 kv 数量，默认 0 

                              - `memkv_mem_limit` : [int] embedding 最大内存占用，默认 0G 

                              - `memkv_shm_path` : [string] embedding 存储的共享内存路径

                              - `emb_target_num` : [int] embedding 多目标数量

                              - `emb_dim` : [int] embedding 单目标维数

                              - `min_emb_num` : [int] embedding 最少积攒多少有效 item 才认为加载成功

                              - `id_filter` : [dict]
                                   
                                  - `type_name` : [string] 白名单类型，现在都用 "redisIndexFilter"

                                  - `redis_key` : [string] 每个业务自己的白名单 redis key, 对应 java sdk saveItemIds 接口的参数 subKey

                       - `poi_config` : [dict] poi embedding 相关配置
                             
                              - `slot`: [int] 用于从 btqueue 消费到的 embedding 中过滤出来 embedding

                              - `memkv_part_num` : [int] embedding 内部空间分 part ，默认 16

                              - `memkv_expire_sec` : [int] embedding 不更新时的强制过期时间，默认 0 秒

                              - `memkv_capacity` : [int] embedding 最大 kv 数量，默认 0 

                              - `memkv_mem_limit` : [int] embedding 最大内存占用，默认 0G 

                              - `memkv_shm_path` : [string] embedding 存储的共享内存路径

                              - `emb_target_num` : [int] embedding 多目标数量

                              - `emb_dim` : [int] embedding 单目标维数

                              - `min_emb_num` : [int] embedding 最少积攒多少有效 item 才认为加载成功

                              - `id_filter` : [dict]
                                   
                                  - `type_name` : [string] 白名单类型，现在都用 "redisIndexFilter"

                                  - `redis_key` : [string] 每个业务自己的白名单 redis key, 对应 java sdk saveItemIds 接口的参数 subKey

    `item_embed_attr` : [string] 构造的 item embedding TensorOutput 在 common context 中的 attr name, 默认值 "all_item_embed"

    `poi_embed_attr` : [string] 构造的 poi embedding TensorOutput 在 common context 中的 attr name, 默认值 "all_poi_embed"

    `poi_id_input_attr_name` : [string] 从哪个 item attr 获取候选集对应的 poi_id

    `item_index_output_attr_name` : [string] 候选集在 item embedding 的 offset 输出到哪个 item attr, 默认值 "item_cache_index"

    `poi_index_output_attr_name` : [string] 候选集在 poi embedding 的 offset 输出到哪个 item attr, 默认值 "poi_cache_index"

    调用示例
    ------
    .local_life_prepare_item_embedding(
      gpu_local_life_embed=dict(
        embedding_queue=["xxxx_emb"],
        embedding_queue_thread_num=4,
        update_interval_sec=60,
        use_fp16=True,
        item_config=dict(
          slot=1,
          memkv_part_num=16,
          memkv_expire_sec=86400,
          memkv_capacity=10000000,
          memkv_mem_limit=2147483648,
          memkv_shm_path="/dev/shm/item",
          emb_target_num=1,
          emb_dim=128,
          min_emb_num=80000,
          id_filter=dict(
            type_name="redisIndexFilter",
            redis_key="local_life_item_wl",
          )
        ),
        poi_config=dict(
          slot=2,
          memkv_part_num=16,
          memkv_expire_sec=86400,
          memkv_capacity=10000000,
          memkv_mem_limit=2147483648,
          memkv_shm_path="/dev/shm/poi",
          emb_target_num=1,
          emb_dim=128,
          min_emb_num=80000,
          id_filter=dict(
            type_name="redisIndexFilter",
            redis_key="local_life_poi_wl",
          )
        ),
      ),
      item_embed_attr="all_item_embed",
      poi_embed_attr="all_poi_embed",
      poi_id_input_attr_name="poi_id",
      item_index_output_attr_name="item_cache_index",
      poi_index_output_attr_name="poi_cache_index",
    )
    """
    self._add_processor(LocalLifePrepareItemEmbeddingEnricher(kwargs))
    return self

  def local_life_common_user_feature_process(self, **kwargs):
    """
    LocalLifeCommonUserFeatureProcessEnricher 
    ------
    本地生活通用用户行为序列处理逻辑

    参数配置
    ------
    `ops`: [list[dict]] 多个处理算子配置

          - `op`: [string] 算子类型："match_list", "filter_list"

          - `filter`: [list[dict]] 多个过滤用户序列的配置，各个 filter 之间是 and 操作

                       - `input_list_attr` : [string] 按用户哪个序列进行过滤 

                       - `type`: [string] 过滤方式："and", "eq"

                       - `value` : [list[int]] 对用户序列与 value list 做计算作为是否过滤的依据，各个 value 之间是 or 操作

          - `target_attr`: [string] 使用 target item 的哪个 attr 与 match_list_attr 指定的用户的行为序列做匹配计算，可以是 int 类型，也可以是 int list 类型

          - `match_list_attr`: [string] 见上

          - `output_list_size`: [int] 输出的 output_list_attr 中各个序列的长度上限，会优先输出最新的 item，默认值 0，表示不做截断

          - `input_list_attr`: [list[string]] 指定将哪些用户的行为序列输出到 output_list_attr 

          - `output_list_attr`: [list[string]] 见上, input_list_attr 与 output_list_attr 的 list size 必须相同

    调用示例
    ------
    .local_life_common_user_feature_process(
      ops=[dict(
        op="filter_list",
        filter=[dict(
          input_list_attr="label_list",
          type="and",
          value=[4]
      ), dict(
          input_list_attr="page_module_id_list",
          type="eq",
          value=[0, 8]
      )],
        output_list_size=200,
        input_list_attr=["timestamp_list", "photo_id_list", "poi_id_list", "author_id_list", "duration_list", "brand_name_hash_list", "poi_cate_3_name_hash_list", "page_module_id_list", "label_list", "play_duration_list", "pay_order_amt_list", "poi_page_stay_time_list"],
        output_list_attr=["filter_timestamp_list", "filter_photo_id_list", "filter_poi_id_list", "filter_author_id_list", "filter_duration_list", "filter_brand_name_hash_list", "filter_poi_cate_3_name_hash_list", "filter_page_module_id_list", "filter_label_list", "filter_play_duration_list", "filter_pay_order_amt_list", "filter_poi_page_stay_time_list"]
      ),
      dict(
        op="match_list",
        filter=[dict(
          input_list_attr="label_list",
          type="and",
          value=[4]
      ), dict(
          input_list_attr="page_module_id_list",
          type="eq",
          value=[0, 8]
      )],
        target_attr="author_id",
        match_list_attr="author_id_list",
        output_list_size=200,
        input_list_attr=["timestamp_list", "photo_id_list", "poi_id_list", "author_id_list", "duration_list", "brand_name_hash_list", "poi_cate_3_name_hash_list", "page_module_id_list", "label_list", "play_duration_list", "pay_order_amt_list", "poi_page_stay_time_list"],
        output_list_attr=["match_timestamp_list", "match_photo_id_list", "match_poi_id_list", "match_author_id_list", "match_duration_list", "match_brand_name_hash_list", "match_poi_cate_3_name_hash_list", "match_page_module_id_list", "match_label_list", "match_play_duration_list", "match_pay_order_amt_list", "match_poi_page_stay_time_list"]
      )])
  """
    self._add_processor(LocalLifeCommonUserFeatureProcessEnricher(kwargs))
    return self

  def local_life_enrich_pxtr_quantile(self, **kwargs):
    """
    LocalLifePxtrQuantileEnricher 
    ------
    本地生活分位数获取 client

    参数配置
    ------
    `kconf_key`: [string][动态参数] 和分位点计算服务保持一致

    `quantiles`: [list[dict]]
                - `config_name_attr`: [string] 必填，需要取分位点的配置名 attr name, attr value 是 "config_name"
                - `quantile_attr`: [string] 必填，需要取的分位点 attr name
                - `output_thresh_attr`: [string] 输出阈值的 attr name
                - `enabled`: [bool][动态参数] 默认 true

    `enable_clip_quantile`: [bool][动态参数], 是否根据 kconf clip 输入的分位点, 默认 false

    `input_common_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    `input_item_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    `hack_item_attr_as_common`: [bool] 兼容直播 model kess 的补丁, 迁移后不使用, 默认 false

    调用示例
    ------
    .local_life_enrich_pxtr_quantile(
      kconf_key="reco.localLife.LocalLifePxtrPercentileDimensions",
      quantiles=[
        dict(config_name_attr="ctcvr_quantile_config", quantile_attr="ctcvr_quantile", output_thresh_attr="thresh_ctcvr")
      ]
    )

    """
    self._add_processor(LocalLifePxtrQuantileEnricher(kwargs))
    return self

  def local_life_pxtr_cache_fetch_enricher(self, **kwargs):
    """
    LocalLifePxtrCacheFetchEnricher 
    ------
    本地生活模型缓存获取

    参数配置
    ------
    `model_name`: [string] 模型名

    `pxtr_attrs`: [list[string]] 需要缓存的 pxtr 名，对应 item_attr

    `redis_cluster_name`: [string] redis 集群名称

    `redis_timeout`: [int] redis 超时时间，默认20ms

    `redis_valid_seconds`: [int][动态参数] redis 缓存有效时间，默认900s

    `redis_part_num`: [int][动态参数] redis 分片数，默认为1

    调用示例
    ------
    ``` python
    .local_life_pxtr_cache_fetch_enricher(
      redis_cluster_name="recoLocalLifeModelPxtrCache",
      pxtr_attrs=['pxtr_ctr', 'pxtr_in_lvtr'],
      redis_timeout=20,
      redis_valid_seconds=900,
      redis_part_num=1,
    )
    ```

    """
    self._add_processor(LocalLifePxtrCacheFetchEnricher(kwargs))
    return self

  def local_life_pxtr_cache_push_enricher(self, **kwargs):
    """
    LocalLifePxtrCachePushEnricher 
    ------
    本地生活模型预估值写入缓存

    参数配置
    ------
    `model_name`: [string] 模型名

    `pxtr_attrs`: [list[string]] 需要缓存的 pxtr 名，对应 item_attr

    `redis_cluster_name`: [string] redis 集群名称

    `redis_timeout`: [int] redis 超时时间，默认20ms

    `redis_valid_seconds`: [int][动态参数] redis 缓存有效时间，默认900s

    `redis_pack_size`: [int][动态参数] redis 单包中的 item 数，默认30

    `redis_part_num`: [int][动态参数] redis 分片数，默认1

    调用示例
    ------
    ``` python
    .local_life_pxtr_cache_fetch_enricher(
      redis_cluster_name="recoLocalLifeModelPxtrCache",
      pxtr_attrs='pxtr_ctr', 'pxtr_in_lvtr'],
      redis_timeout=20,
      redis_valid_seconds=900,
      redis_pack_size=30,
      redis_part_num=1
    )
    ```

    """
    self._add_processor(LocalLifePxtrCachePushEnricher(kwargs))
    return self

  def local_life_colossus2_batch_handle_enricher(self, **kwargs):
    """
    LocalLifeColossusV2BatchHandleEnricher 
    ------
    本地生活处理 colossus v2 版本批量请求返回的结果

    参数配置
    ------
    `input_int_attr`: [string] 批量的 int 数据输入源

    `output_aid_list_attr`: [string] 输出 aid list 的 attr name

    `output_ts_list_attr`: [string] 输出 timestamp list 的 attr name

    `output_pay_order_amt_list_attr`: [string] 输出 pay_order_amt list 的 attr name

    `output_live_id_list_attr`: [string] 输出 live_id list 的 attr name

    `output_page_module_id_list_attr`: [string] 输出 page_module_id list 的 attr name

    `output_label_list_attr`: [string]  输出 label list 的 attr name

    `output_play_duration_list_attr`: [string] 输出 play_duration list 的 attr name

    `output_auto_play_duration_list_attr`: [string] 输出 auto_play_duration list 的 attr name 

    `filter_label`: [int][动态参数] 按 label 过滤时需要保留的 label 值, 值为 0 则不过滤

    `output_aid_list_size`: [int][动态参数] 输出的 aid list size

    `sort_type`: [int][动态参数] aid list 排序方式，0 为按时间戳由大到小排序，1 为按支付金额由大到小排序

    `min_timestamp_sec`: [int][动态参数] 过滤掉 timestamp 小于该值的 item, 值为 0 则不过滤

    `max_timestamp_sec`: [int][动态参数] 过滤掉 timestamp 大于等于该值的 item, 值为 0 则不过滤

    调用示例
    ------
    ``` python
    .local_life_colossus2_batch_handle_enricher(
      input_int_attr="colossus_int_batch_output",
      output_aid_list_attr="output_aid_list",
      output_ts_list_attr="output_ts_list",
      output_pay_order_amt_list_attr="output_pay_order_amt_list",
      output_live_id_list_attr="output_live_id_list",
      output_page_module_id_list_attr="output_page_module_id_list",
      output_label_list_attr="output_label_list",
      output_play_duration_list_attr="output_play_duration_list",
      output_auto_play_duration_list_attr="output_auto_play_duration_list",
      min_timestamp_sec=,
      max_timestamp_sec=,
      filter_label=0,
      output_aid_list_size=200,
      sort_type=0
    )
    ```

    """
    self._add_processor(LocalLifeColossusV2BatchHandleEnricher(kwargs))
    return self