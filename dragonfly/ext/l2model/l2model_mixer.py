#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafMixer, try_add_table_name


class LocalLifeGoodsModelPhoto2GoodsMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_model_mix_photo2goods_4goods_model"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    input_table = self._config.get("input_table", "")
    input_attrs = self._config.get("copy_item_attrs", []) + self._config.get("extra_item_attrs", [])
    attrs.update(try_add_table_name(input_table, input_attrs))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_table = self._config.get("output_table", "goods")
    output_attrs = self._config.get("output_item_attrs", [])
    output_attrs.append("goods_id")
    attrs.update(try_add_table_name(output_table, output_attrs))
    return attrs


class LocalLifeGoodsModelGoods2PhotoMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_model_mix_goods2photo_4goods_model"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    input_table = self._config.get("input_table", "goods")
    input_attrs = self._config.get("copy_item_attrs", []) + self._config.get("extra_item_attrs", [])
    attrs.update(try_add_table_name(input_table, input_attrs))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    output_attrs = set()
    output_table = self._config.get("output_table", "")
    output_attrs.update(self._config.get("output_item_attrs", []))
    input_attrs = self._config.get("copy_item_attrs", [])
    output_attrs.update([attr + "_list" for attr in input_attrs])
    attrs.update(try_add_table_name(output_table, output_attrs))
    return attrs