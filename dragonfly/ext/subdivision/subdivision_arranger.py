#!/usr/bin/env python3
# coding=utf-8
"""
filename: subdivision_arranger.py
description:
author: <EMAIL>
date: 2020-09-01 14:40:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class CommonRecoEnsembleScoreCalcArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_calc_ensemble_score"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["rank_smooth", "rank_cliff", "rank_height", "cliff_ratio", "user_new_proportion", "user_power_calc", "use_dist_calc",
                     "user_power_calc_v2", "use_reciprocal", "duration_min", "duration_max", "enable_time_cost_opt", "dis_factor",
                     "enable_perf_pxtr_pic", "perf_pxtr_pic_num", "duration_add", "action_day", "use_xtr_raw_score"
                     ,"use_queue_smooth_as_rank_smooth", "rank_score_calculate_method", "hyperbolic_scale", "hyperbolic_alpha", "hyperbolic_beta"
                     , "min_rank_weight", "fractile_smooth","queue_head_boost_index","queue_tail_discount_index","use_absolute_score_queue_power_weight"
                     ,"queue_head_boost_threshold","queue_tail_discount_threshold","ensemble_score_head_coef","ensemble_score_tail_coef","use_xtr_weight_raw_score"
                     ,"use_rank_with_absolute_score"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add(self._config.get("user_info_ptr_attr"))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("enable", ""))
            attrs.add(cfg.get("weight_attr", ""))
            attrs.add(cfg.get("power_weight_attr", ""))
            attrs.add(cfg.get("variant_weight", ""))
            attrs.add(cfg.get("temperature_attr", ""))
            attrs.add(cfg.get("smooth_attr", ""))
            attrs.add(cfg.get("raw_weight_attr", ""))
            attrs.add(cfg.get("raw_pow_weight_attr", ""))
            attrs.add(cfg.get("hyperbolic_raw_pow_weight_attr", ""))
            attrs.add(cfg.get("raw_value_fusion_type", ""))
            attrs.add(cfg.get("raw_diff_threshold_attr", ""))
            attrs.add(cfg.get("raw_score_alpha_attr", ""))
            attrs.add(cfg.get("que_cliff_ratio_attr", ""))
            attrs.add(cfg.get("score_threshold", ""))
            attrs.add(cfg.get("fractile_weight_attr", ""))
            attrs.add(cfg.get("fractile_pow_weight_attr", ""))
            attrs.add(cfg.get("queue_head_boost_coef", ""))
            attrs.add(cfg.get("queue_tail_discount_coef", ""))
            attrs.add(cfg.get("enable_queue_head_boost", ""))
            attrs.add(cfg.get("enable_queue_tail_discount", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
            attrs.add(cfg.get("fractile_value_attr", ""))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        attrs.add(self._config.get("save_ori_ensemble_score_to_attr"))
        attrs.add(self._config.get("save_absolute_score_to_attr"))
        attrs.add(self._config.get("save_fractile_score_to_attr"))
        return attrs

class CommonRecoEnsembleScoreCalcV2Arranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_calc_ensemble_score_v2"
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["ensemble_type", "merge_type", "rank_merge_weight", "orig_merge_weight", "rank_smooth", "rank_height", "cliff_ratio",
                     "interactive_reweight_factor", "interactive_reweight", "interactive_duration_upper_bound", "interactive_duration_lower_bound"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr", ""))
        return attrs
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs

class CommonRecoListTypeAttrFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_dedup_content_ids_in_pagesize"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["need_filter_types", "version", "item_type_of_checked_id", "use_old_mmu_content_id", 
            "enable_skip_filter_hot_high_photo", "enable_skip_filter_hot_high_photo_in_bs", "hot_high_photo_skip_filter_types",
            "cid_browse_set_hetu_list"]:
          if name in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["audit_hot_high_tag_level_attr", "dedup_item_attr", "hetu_level_one_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        if "mmu_content_attrs" in self._config:
            for _, attr in self._config.get("mmu_content_attrs").items():
                attrs.add(attr)
        return attrs

class CommonRecoListTypeBrowseSetFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_filter_content_ids_by_browse_set"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["enable_skip_filter_hot_high_photo", "need_filter_types"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["audit_hot_high_tag_level_attr", "check_id_in_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_filtered_items_to_common_attr"))
        return attrs

class FountainRelatedCalcScoreArranger(LeafArranger):
    # FIXME (huzengyi) 这其实是一个 enricher
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_calc_related_score"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["diversity_dim_weight"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.update(self._config.get("int_source_attrs"))
        attrs.update(self._config.get("int_list_source_attrs"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.update(self._config.get("int_item_attrs"))
        attrs.update(self._config.get("int_list_item_attrs"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs

class FountainRelatedCalcScoreV2Arranger(LeafArranger):
    # FIXME (huzengyi) 这其实是一个 enricher
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_calc_related_score_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["diversity_dim_weight", "enable_cal_photo_sim_by_intersect"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.update(self._config.get("int_source_attrs"))
        attrs.update(self._config.get("int_list_source_attrs"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.update(self._config.get("int_item_attrs"))
        attrs.update(self._config.get("int_list_item_attrs"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs

class CommonRecoXtrPreFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_ensemble_pre_filter"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["guarantee_num", "keep_photo_size", "buckets_configs"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["save_score_to_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

class FountainVariantClusterSortArranger(LeafArranger):
        @classmethod
        @strict_types
        def get_type_alias(cls) -> str:
            return "fountain_variant_cluster_sort"

        @property
        @strict_types
        def input_common_attrs(self) -> set:
            attrs = set()
            for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                         "use_reciprocal_v2", "check_point", "enable_user_profile_config", "cut_ratio_decay", "cut_ratio_boost",
                         "user_profile_hetu_count_threshold", "neg_time_limit_second", "neg_play_sec_limit", "pos_time_limit_second", "pos_play_sec_limit"]:
                if name in self._config:
                    attrs.update(self.extract_dynamic_params(self._config.get(name)))
            for cfg in self._config.get("queues"):
                attrs.add(cfg.get("weight_attr", ""))
                attrs.add(cfg.get("power_weight_attr", ""))
                attrs.add(cfg.get("temperature_attr", ""))
                attrs.add(cfg.get("cutoff_ratio", ""))
            return attrs

        @property
        @strict_types
        def input_item_attrs(self) -> set:
            attrs = set()
            for name in ["cluster_sort_list_attr_name"]:
                if name in self._config:
                    attrs.add(self._config.get(name))
            for cfg in self._config.get("queues"):
                attrs.add(cfg.get("name", ""))
            return attrs

class FountainVariantClusterSortV2Arranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_variant_cluster_sort_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                     "use_reciprocal_v2", "check_point", "enable_user_profile_config", "cut_ratio_decay", "cut_ratio_boost",
                     "user_profile_hetu_count_threshold", "neg_time_limit_second", "neg_play_sec_limit", "pos_time_limit_second", "pos_play_sec_limit"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr", ""))
            attrs.add(cfg.get("power_weight_attr", ""))
            attrs.add(cfg.get("temperature_attr", ""))
            attrs.add(cfg.get("cutoff_ratio", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["cluster_sort_list_attr_name"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs

class FountainVariantEnsembleArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_variant_ensemble_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["variant_num", "variant_ratio", "size_limit", "truncate_end", "variant_item_attr",
                "global_cut_ratio", "min_survival", "enable_proportional", "use_power_calc",
                "enable_boost", "boost_from_attr", "enable_special_queues", "enable_use_click_list",
                "enable_user_info", "total_keep_num", "use_reciprocal", "enable_variant"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr", ""))
            attrs.add(cfg.get("power_weight_attr", ""))
            attrs.add(cfg.get("temperature_attr", ""))
            attrs.add(cfg.get("cutoff_ratio", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("save_score_to_attr", ""))
      return attrs

class RelatedVariantMultiClusterSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_related_cluster_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["global_cut_ratio", "min_survival"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr"))
            attrs.add(cfg.get("power_weight_attr"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        for cfg in self._config.get("cluster_config"):
            attrs.add(cfg.get("item_attr"))
        return attrs

class FountainMmrVariantArrange(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_mmr_variant"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["mmr_need_diversity_num", "mmr_cal_candidate_num",
                "item_ensemble_score_attr", "mmr_lambda", "mmr_gamma", "mmr_no_max_weight_use",
                "enable_mmr_his_feedback_diversity", "mmr_no_his_max_weight_use", "mmr_diversity_dim_weight",
                "mmr_history_diversity_dim_weight", "mmr_cal_noclick_num", "mmr_real_show_expired_gap",
                "mmr_page_num_limit", "mmr_time_weight_param", "mmr_time_interval_param",
                "fountain_enable_mmr_photo_embedding",]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add(self._config.get("user_info_attr"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.update(self._config.get("item_attrs", []))
        attrs.update(self._config.get("item_list_attrs", []))
        attrs.update(self._config.get("item_count_attrs", []))
        attrs.update(self._config.get("his_item_attrs", []))
        return attrs

class FountainRelatedPromotionArrange(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_related_force_insert"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["out_page_promotion", "out_page_promotion_num_page2", "out_page_promotion_num_page3",
                "source_related_attr", "promotion_count"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add("page_size")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["item_related_attr"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class FountainNegFeedbackDiscountArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_negative_feedback_discount"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["user_info_attr", "discount_score", "min_neg_feedback", "time_limit_second"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["hetu_level_one", "hetu_level_two", "photo_dnn_cluster_id", "mmu_img_cluster_v3", "tag"])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["save_score_to_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
                #attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class FountainMultiPageVariantArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_variant_multi_page"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for key in ["window_limit_size", "candidate_limit_size", "item_score_from_attr"]:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
        attrs.add(self._config.get("user_info_attr"))
        for v in self._config["variant_config"].values():
            if isinstance(v, dict):
                for val in v.values():
                    attrs.update(self.extract_dynamic_params(val))

        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = { k for k, v in self._config["variant_config"].items() if isinstance(v, dict) }
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attr = self._config.get("save_decay_score_to_attr", "")
        return { attr } if attr else set()

class ListVariantDiversityArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_variant_diversity"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for key in ["page_size", "use_type"]:
            attrs.add(self._config.get(key))
        for key in ["enable_skip_diversity", "enable_fix_variant_attr"]:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
        for v in self._config["variant_config"].values():
            if isinstance(v, dict):
                for val in v.values():
                    attrs.update(self.extract_dynamic_params(val))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        #attrs = { k for k, v in self._config["variant_config"].items() if isinstance(v, dict) }
        attrs = set()
        for name in ["seq_item_attr_name", "init_decay_score_from_attr",
                    "prev_items_from_attr"]:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("retrieval_list_keys_2")
        return attrs

class ListVariantFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_variant_filter"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for v in self._config["variant_config"].values():
            if isinstance(v, dict):
                for val in v.values():
                    attrs.update(self.extract_dynamic_params(val))
        for name in ["max_retrieval_num"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class FountainListEnsembleFilterArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_ensemble_filter"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
        for name in ["use_proportion", "use_pow_rank", "fountain_enable_list_filter",
                    "max_retrieval_num"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class FountainVariantClusterSubdivisionSortArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_variant_cluster_subdivision_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                     "subdivision_cluster_weight", "subdivision_cluster_weight_postfix",
                     "use_reciprocal_v2", "cluster_sort_list_attr_name", "check_point"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("weight_attr", ""))
            attrs.add(cfg.get("power_weight_attr", ""))
            attrs.add(cfg.get("temperature_attr", ""))
            attrs.add(cfg.get("cutoff_ratio", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs
class SubdivisionFlowControlWithVariantArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "flow_control_with_variant_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["need_diversity_num", "window_size", "variant_count", "variant_name", "flow_control_configs","flow_control_attr","ab_test_group"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("flow_control_configs"):
            attrs.add(cfg.get("name", ""))
            attrs.add(cfg.get("percentage_attr", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("variant_name")))
        for cfg in self._config.get("flow_control_configs"):
            attrs.add(cfg.get("name", ""))
        return attrs

class SubdivisionFlowControlWithVariantV2Arranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "flow_control_with_variant_v2_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["need_diversity_num", "window_size", "variant_count", "variant_name", "flow_control_configs","flow_control_attr","ab_test_group"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for cfg in self._config.get("flow_control_configs"):
            attrs.add(cfg.get("name", ""))
            attrs.add(cfg.get("percentage_attr", ""))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("variant_name")))
        for cfg in self._config.get("flow_control_configs"):
            attrs.add(cfg.get("name", ""))
        return attrs
