#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class CommonRecoSimilarKessI2iRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_common_reco_similar"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["featureSourcePId", "sourcePidAuthorId"])
    for name in ["total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True


class CommonRecoFriendCollectPhotoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_friend_collect_photo"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["kcc_cluster", "friend_collect_prefix"])
    for name in ["total_limit", "remote_recall_limit", "kess_service"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class RecoPhotoClusterRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_reco_photo_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs


class RecoKessI2iItemcfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_reco_kess_i2i_itemcf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_source"))
    attrs.update(self.extract_dynamic_params(self._config["bound_type"]["total_limit"]))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True


class MmuEmbeddingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    for name in ["total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MmuEmbeddingSubdivisionRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_embedding_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    attrs.add(self._config.get("bucket_id"))
    for name in ["total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True


class RecoRelevanceTransferProbRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_transfer_prob"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_source"))
    attrs.update(self.extract_dynamic_params(self._config.get("version")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("attrs"):
      if "save_score_to_attr" in cfg:
        attrs.add(cfg.get("save_score_to_attr",""))
      if "save_count_to_attr" in cfg:
        attrs.add(cfg.get("save_count_to_attr",""))

    return attrs


class CommonAttrWithReasonAndScoreRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_reason_and_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("attrs"):
      if "reasons_from_attr" in cfg:
        attrs.add(cfg.get("reasons_from_attr",""))
      if "name" in cfg:
        attrs.add(cfg.get("name",""))
      if "scores_from_attr" in cfg:
        attrs.add(cfg.get("scores_from_attr",""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("attrs"):
      if "save_score_to_attr" in cfg:
        attrs.add(cfg.get("save_score_to_attr",""))

    return attrs


class ExploreLrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_Explore_lr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config._get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config["bound_type"].get("total_limit")))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("attrs"):
      if "save_score_to_attr" in cfg:
        attrs.add(cfg.get("save_score_to_attr",""))

    return attrs


class RecoMindRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mind"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))

    return attrs


class MmuEmbeddingGasRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_mmu_embedding_gas"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit")))

    return attrs


class RelevanceI2iRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_relevance_i2i_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_source"))
    attrs.update(self.extract_dynamic_params(self._config["bound_type"].get("total_limit")))

    return attrs


class RecoRelevanceTransferRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_relevance_transfer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_source"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("version")))
    attrs.update(self.extract_dynamic_params(self._config.get("prefix")))

    return attrs


class RecoRelevanceMmuCategoryContentTagRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_relevance_mmu_category_content_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_source"))
    attrs.update(self.extract_dynamic_params(self._config.get("version")))
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit")))

    return attrs


class CommonRecoFountainKessModelRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_model_retrieve"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_simple_user_info")))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class RecoItemCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_fountain_itemcf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit", 2000)))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms", 300)))
    attrs.update(self.extract_dynamic_params(self._config.get("only_fountain_behavior", 0)))
    return attrs
    
  @strict_types
  def is_async(self) -> bool:
    return True
