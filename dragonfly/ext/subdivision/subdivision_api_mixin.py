#!/usr/bin/env python3
# coding=utf-8
"""
filename: subdivision_api_mixin.py
description:
author: <EMAIL>
date: 2020-08-31 18:08:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .subdivision_retriever import *
from .subdivision_arranger import *
from .subdivision_enricher import *
from .subdivision_observer import *

class subdivisionApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 subdivision 相关的 Processor 接口
  - CommonRecoSimilarKessI2iRetriever
  - RecoPhotoClusterRetriever
  - RecoKessI2iItemcfRetriever
  - MmuEmbeddingRetriever
  - MmuEmbeddingSubdivisionRetriever
  - RecoRelevanceTransferProbRetriever
  - CommonAttrWithReasonAndScoreRetriever
  - CommonRecoEnsembleScoreCalcArranger
  - ExploreFullRankPredictItemAttrEnricher
  - ExploreLrRetriever
  - RecoItemCfRetriever
  """

  def retrieve_by_common_reco_similar(self, **kwargs):
    """
    CommonRecoSimilarKessI2iRetriever
    """
    self._add_processor(CommonRecoSimilarKessI2iRetriever(kwargs))
    return self

  def retrieve_by_friend_collect_photo(self, **kwargs):
    """
    CommonRecoFriendCollectPhotoRetriever
    """
    self._add_processor(CommonRecoFriendCollectPhotoRetriever(kwargs))
    return self

  def retrieve_by_reco_photo_cluster(self, **kwargs):
    """
    RecoPhotoClusterRetriever
    """
    self._add_processor(RecoPhotoClusterRetriever(kwargs))
    return self

  def retrieve_by_reco_kess_i2i_itemcf(self, **kwargs):
    """
    RecoKessI2iItemcfRetriever
    """
    self._add_processor(RecoKessI2iItemcfRetriever(kwargs))
    return self

  def retrieve_by_mmu_embedding(self, **kwargs):
    """
    MmuEmbeddingRetriever
    """
    self._add_processor(MmuEmbeddingRetriever(kwargs))
    return self

  def retrieve_by_mmu_embedding_v2(self, **kwargs):
    """
    MmuEmbeddingSubdivisionRetriever
    -----
    繁星向量检索平台 i2i 召回服务，该插件功能不全，推荐用 retrieve_by_mmu_embedding_gas 替换
    参数配置
    ------
    `kess_service`: [string] [动态参数] grpc_se_gasForReco 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] 召回原因

    `item_type`: [int] item 类型

    `source`: [string] 区分调用方

    `vertical_id`: [string] 业务 ID

    `bucket_id`: [int] [动态参数] 分桶 ID

    `bucket_string`: [string] [动态参数] [可选] 分桶 ID String 类型，配置后会覆盖 bucket_id 的配置

    `source_photo_attr`: [List] i2i trigger

    `total_limit`: [int] [动态参数] 召回数

    调用示例
    ------
    ``` python
    .retrieve_by_mmu_embedding_v2(
      "kess_service": "grpc_se_gasForReco",
      "timeout_ms": 200,
      "reason": 29,
      "item_type": 1,
      "vertical_id": "recoDiscoverTabVertical",
      "bucket_id": "tabId",
      "source": "test",
      "source_photo_attr": "commonRetrievalPhotos",
      "total_limit": "200",
    )
    ```
    """
    self._add_processor(MmuEmbeddingSubdivisionRetriever(kwargs))
    return self

  def starry_embedding_enrich(self, **kwargs):
    """
    StarryEmbeddingEnricher
    -----
    搜索 Starry 向量检索服务 Enrich
    参数配置
    ------
    `kess_service`: [string]  服务的 kess 服务名 [必填]

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 [必填]

    `vertical_id`: [string] 业务 ID，一般是是索引名 [必填]

    `source`: [string] 主调方来源标识，用于下游区分流量来源，一般用用用途标识即可 [必填]

    `total_limit`: [int] [动态参数] 召回总数，每个请求 top_k = `total_limit` / batch_size

    `top_k`: [int] [动态参数] 单个召回数控制， 与 `total_limit` 二选一配置，这个优先级高

    `query_source_type`: [string], 请求来源，支持: "common_attr","item_attr","item_id","item_key" [必填]

    `query_source_attr_name`: [string] 当 `query_source_type` 设置为: "common_attr","item_attr" 时生效

    `query_source_attr_type`: [string] 当 `query_source_attr_name` 设置时生效， 支持 "int","string"

    `output_attr_name` : [string] 输出 attr 的名字 [必填]

    `filter_item_attr_name` : [string]  当 `query_source_type` 设置为: "item_attr" 生效, 若设置：取此 item_attr 存在且 >0 才会下发请求，默认 不设置，则全部下发

    `batch_request_upper_limit` : [int] 下发 batch 请求的最大个数 , 默认不限制 [选填]

    `dedup_for_common_batch_results` : [bool] 当 `query_source_type` 设置为: "common_attr" 生效, 且 batch 请求生效， batch 之间进行滤重 , 默认不滤重 [选填]

    `bucket_id`: [string] [可选] 当托管服务有多个 bucket 时才需要设置，默认不设置，请求唯一一个 桶

    调用示例
    ------
    ``` python
    .starry_embedding_enrich(
      "kess_service":"",
      "timeout_ms":300,
      "vertical_id":"hetuEmbedding",
      "source":"hetu_single_list_lite",
      "total_limit":600,
      "query_source_type":"item_attr",
      "query_source_attr_name":"photo_id",
      "query_source_attr_type":"int",
      "output_attr_name":"starry_i2i_result",
      "batch_request_upper_limit":100,
      "filter_item_attr_name":"query_starry"
    )
    ```
    """
    self._add_processor(StarryEmbeddingEnricher(kwargs))
    return self

  def retrieve_by_transfer_prob(self, **kwargs):
    """
    RecoRelevanceTransferProbRetriever
    """
    self._add_processor(RecoRelevanceTransferProbRetriever(kwargs))
    return self

  def retrieve_by_reason_and_score(self, **kwargs):
    """
    CommonAttrWithReasonAndScoreRetriever
    """
    self._add_processor(CommonAttrWithReasonAndScoreRetriever(kwargs))
    return self

  def common_attr_enrich_from_redis_json(self, **kwargs):
    """
    CommonAttrEnrichFromRedisJson
    """
    self._add_processor(CommonAttrEnrichFromRedisJson(kwargs))
    return self

  def item_attr_enrich_from_redis_json(self, **kwargs):
    """
    ItemAttrEnrichFromRedisJson
    """
    self._add_processor(ItemAttrEnrichFromRedisJson(kwargs))
    return self

  def item_predict_by_xgb(self, **kwargs):
    """
    ItemPredictByXgb
    """
    self._add_processor(ItemPredictByXgb(kwargs))
    return self

  def item_xgb_kml_predict_enrich(self, **kwargs):
    """
    ItemXgbKmlPredictEnrich
    """
    self._add_processor(ItemXgbKmlPredictEnrich(kwargs))
    return self

  def fountain_calc_ensemble_score(self, **kwargs):
    """
    CommonRecoEnsembleScoreCalcArranger
    """
    self._add_processor(CommonRecoEnsembleScoreCalcArranger(kwargs))
    return self

  def fountain_calc_ensemble_score_v2(self, **kwargs):
    """
    CommonRecoEnsembleScoreCalcV2Arranger
    -----
    Ensemble Score 计算，引入原始分，duration 衰减。支持原始分与rank分使用 + 或 * 融合，支持不同队列分采用 + 或 * 融合。

    参数配置
    ------
    `ensemble_type`: [string] "plus" 或 "multiply", Ensemble Score 计算方式。

    `merge_type`: [string] "plus" 或 "multiply", 队列内部候选分计算方式。

    `rank_merge_weight`: [double] 计算队列候选分时rank分的通用权重。

    `orig_merge_weight`: [double] 计算队列候选分时原始分的通用权重。

    `orig_score_max_norm`: [bool] 是否对原始分进行 max norm。

    `interactive_reweight`: [bool] 是否在特定 duration 区间内对互动队列权重采用线性加权。

    `interactive_reweight_factor`: [double] 对互动队列加权系数，采用线性衰减策略，区间下边界加权系数最大，区间上边界加权系数衰减为1。

    `interactive_duration_upper_bound`: [double] duration 加权区间上边界。

    `interactive_duration_lower_bound`: [double] duration 加权区间下边界。

    `rank_smooth`: [double] Rank 分平滑系数。

    `queues`: [list[dict]] Ensemble Sort 队列。

    队列设置示例
    ------
    ```python
    queues = 
    [
        {
            "name": "fullrank_click",                           # 队列名称
            "weight": 0.0,                                      # [double] 默认权重
            "rank_weight_attr": "fullrank_click_rank_weight",   # [double] [可选] 计算队列内部候选分时rank分权重，若不设置则使用通用权重。
            "orig_weight_attr": "fullrank_click_orig_weight",   # [double] [可选] 计算队列内部候选分时原始分权重，若不设置则使用通用权重。
            "is_interactive": False,                            # [bool] 是否做 duration 加权，默认 False，当且仅当 duration 加权生效时有效。
            "weight_attr": "fullrank_click_weight",             # [double] 队列权重设置，如不设置则使用默认权重，权重绝对值小于 1e-3 时队列不生效。
        },
    ]
    ```
    """
    self._add_processor(CommonRecoEnsembleScoreCalcV2Arranger(kwargs))
    return self

  def fountain_filter_content_ids_by_browse_set(self, **kwargs):
    """
    CommonRecoListTypeBrowseSetFilterArranger
    """
    self._add_processor(CommonRecoListTypeBrowseSetFilterArranger(kwargs))
    return self

  def fountain_dedup_content_ids_in_pagesize(self, **kwargs):
    """
    CommonRecoListTypeAttrFilterArranger
    """
    self._add_processor(CommonRecoListTypeAttrFilterArranger(kwargs))
    return self


  def fountain_calc_related_score(self, **kwargs):
    """
    FountainRelatedCalcScoreArranger
    """
    self._add_processor(FountainRelatedCalcScoreArranger(kwargs))
    return self

  def fountain_calc_related_score_v2(self, **kwargs):
    """
    FountainRelatedCalcScoreV2Arranger
    """
    self._add_processor(FountainRelatedCalcScoreV2Arranger(kwargs))
    return self

  def fountain_ensemble_pre_filter(self, **kwargs):
    """
    CommonRecoXtrPreFilterArranger
    """
    self._add_processor(CommonRecoXtrPreFilterArranger(kwargs))
    return self

  def fountain_variant_cluster_sort(self, **kwargs):
    """
    FountainVariantClusterSortArranger
    """
    self._add_processor(FountainVariantClusterSortArranger(kwargs))
    return self

  def fountain_variant_cluster_sort_v2(self, **kwargs):
    """
    FountainVariantClusterSortV2Arranger
    """
    self._add_processor(FountainVariantClusterSortV2Arranger(kwargs))
    return self

  def fountain_variant_ensemble_sort(self, **kwargs):
    """
    FountainVariantEnsembleArranger
    """
    self._add_processor(FountainVariantEnsembleArranger(kwargs))
    return self

  def fountain_related_cluster_sort(self, **kwargs):
    """
    RelatedVariantMultiClusterSortArranger
    """
    self._add_processor(RelatedVariantMultiClusterSortArranger(kwargs))
    return self

  def fountain_related_force_insert(self, **kwargs):
    """
    FountainRelatedPromotionArrange
    """
    self._add_processor(FountainRelatedPromotionArrange(kwargs))
    return self

  def predict_by_item_attr(self, **kwargs):
    """
    ExploreFullRankPredictItemAttrEnricher
    """
    self._add_processor(ExploreFullRankPredictItemAttrEnricher(kwargs))
    return self

  def retrieve_by_mind(self, **kwargs):
    """
    RecoMindRetriever
    """
    self._add_processor(RecoMindRetriever(kwargs))
    return self

  def retrieve_by_mmu_embedding_gas(self, **kwargs):
    """
    MmuEmbeddingGasRetriever
    -----
    搜索繁星 starry 检索服务插件：实现 i2i,q2i,emb2i，有问题 可咨询 zhangcunyi
    参数配置
    ------
    `kess_service`: [string] [动态参数] grpc_se_gasForReco 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 200

    `reason`: [int] 召回原因

    `item_type`: [int] item 类型

    `source`: [string] 区分调用方

    `vertical_id`: [string] 业务 ID 或 索引 id

    `input_type`: [string], 'org_text':q2i StringCommonAttr, 'feature':emb2i DoubleListCommonAttr, 'text': i2i IntListCommonAttr 或 IntCommonAttr

    `source_photo_attr`: [string] CommonAttr Name for `input_type`

    `total_limit`: [int] [动态参数] 召回数, batch 总召回数；batch 内每个 item 的召回个数 = (batch + item_size - 1) / item_size

    `top_k`: [int] [动态参数] 不管 batch 内有多少个 item, 每个 item 均取 top_k 个，优先级 低于 `total_limit`

    调用示例
    ------
    ``` python
    .retrieve_by_mmu_embedding_gas(
      "kess_service": "grpc_se_gasForReco",
      "timeout_ms": 200,
      "reason": 29,
      "item_type": 0,
      "vertical_id": "recoDiscoverTabVertical",
      "source_photo_attr": "commonRetrievalPhotos",
      "total_limit": "200",
    )
    ```
    """
    self._add_processor(MmuEmbeddingGasRetriever(kwargs))
    return self

  def retrieve_by_relevance_i2i_redis(self, **kwargs):
    """
    RelevanceI2iRedisRetriever
    """
    self._add_processor(RelevanceI2iRedisRetriever(kwargs))
    return self

  def retrieve_by_relevance_transfer(self, **kwargs):
    """
    RecoRelevanceTransferRetriever
    """
    self._add_processor(RecoRelevanceTransferRetriever(kwargs))
    return self

  def fountain_related_sccore_enricher(self, **kwargs):
    """
    FountainRelatedScoreEnricher
    """
    self._add_processor(FountainRelatedScoreEnricher(kwargs))
    return self

  def fountain_related_sccore_v2_enricher(self, **kwargs):
    """
    FountainRelatedScoreV2Enricher
    """
    self._add_processor(FountainRelatedScoreV2Enricher(kwargs))
    return self

  def fountain_related_tag_common_attr_enricher(self, **kwargs):
    """
    FountainRelatedTagCommonAttrEnricher
    """
    self._add_processor(FountainRelatedTagCommonAttrEnricher(kwargs))
    return self

  def retrieve_by_relevance_mmu_category_content_tag(self, **kwargs):
    """
    RecoRelevanceMmuCategoryContentTagRetriever
    """
    self._add_processor(RecoRelevanceMmuCategoryContentTagRetriever(kwargs))
    return self

  def retrieve_by_explore_lr(self, **kwargs):
    """
    ExploreLrRetriever
    """
    self._add_processor(ExploreLrRetriever(kwargs))
    return self

  def fountain_fetch_nn_user_embedding(self, **kwargs):
    """
    NnUserEmbeddingCommonAttrEnricher
    """
    self._add_processor(NnUserEmbeddingCommonAttrEnricher(kwargs))
    return self

  def fountain_enrich_cascade_score(self, **kwargs):
    """
    FountainCascadeScoreEnricher
    """
    self._add_processor(FountainCascadeScoreEnricher(kwargs))
    return self

  def fountain_enrich_sample_package(self, **kwargs):
    """
    FountainSamplePackageEnricher
    """
    self._add_processor(FountainSamplePackageEnricher(kwargs))
    return self

  def fountain_negative_feedback_discount(self, **kwargs):
    """
    FountainNegFeedbackDiscountArranger
    """
    self._add_processor(FountainNegFeedbackDiscountArranger(kwargs))
    return self

  def fountain_mmr_variant(self, **kwargs):
    """
    FountainMmrVariantArrange
    """
    self._add_processor(FountainMmrVariantArrange(kwargs))
    return self

  def fountain_variant_multi_page(self, **kwargs):
    """
    FountainMultiPageVariantArranger
    """
    self._add_processor(FountainMultiPageVariantArranger(kwargs))
    return self

  def fountain_model_retrieve(self, **kwargs):
    """
    CommonRecoFountainKessModelRetriever
    """
    self._add_processor(CommonRecoFountainKessModelRetriever(kwargs))
    return self

  def fountain_enrich_cascade_cluster_id(self, **kwargs):
    """
    FountainCascadeClusterIdEnricher
    """
    self._add_processor(FountainCascadeClusterIdEnricher(kwargs))
    return self

  def fountain_environment_perf_log(self, **kwargs):
    """
    FountainEnvironmentPerflogObserver
    """
    self._add_processor(FountainEnvironmentPerflogObserver(kwargs))
    return self

  def fountain_perflog_attr_fractile(self, **kwargs):
    """
    FountainAttrFractilePerflogObserver
    -----
    统计 attr 的分位数监控

    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `item_attrs`: [list] 需要统计的 item_attr 名称列表，支持 int 和 double 类型

    `common_attrs`: [list] 需要统计的 common_attr 名称列表，支持 int_list 和 double_list 类型

    `fractile_list`: [list] 需要统计的分位数列表

    `local`: [bool] 选配项，统计结果直接写到本地 perflog.log 文件而不是上传到 clickhouse，默认为 False。

    `perf_base`: [int] 打点时的放大倍数，用于保留小数点，默认为 1000000L，注意修改这个值会导致 Grafana 上打点显示异常，使用时需要注意。

    调用示例
    ------
    ``` python
    # 统计 default 流程中排序阶段的各个 pxtr 分位数
    .fountain_perflog_attr_fractile(
      check_point="default.cascade",
      item_attrs=["pctr", "pltr", "pftr"],
      common_attrs=["pctr_list"],
      fractile_list=[0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1.0]
    )
    ```
    """
    self._add_processor(FountainAttrFractilePerflogObserver(kwargs))
    return self

  def gen_ensemble_second_sequence(self, **kwargs):
    """
    GenEnsembleSecondSequenceEnricher
    """
    self._add_processor(GenEnsembleSecondSequenceEnricher(kwargs))
    return self

  def dpp_gen_sequence(self, **kwargs):
    """
    DppGenSequenceEnricher
    """
    self._add_processor(DppGenSequenceEnricher(kwargs))
    return self

  def gen_original_sequence(self, **kwargs):
    """
    GenOriginalSequenceEnricher
    """
    self._add_processor(GenOriginalSequenceEnricher(kwargs))
    return self

  def gen_beamsearch_sequence(self, **kwargs):
    """
    GenFountainBeamSearchListEnricher
    """
    self._add_processor(GenFountainBeamSearchListEnricher(kwargs))
    return self

  def list_wise_seq_attr(self, **kwargs):
    """
    ListwiseFountainSeqAttrEnricher
    """
    self._add_processor(ListwiseFountainSeqAttrEnricher(kwargs))
    return self

  def list_item_predict(self, **kwargs):
    """
    ListItemPredictEnricher
    """
    self._add_processor(ListItemPredictEnricher(kwargs))
    return self

  def list_ensemble_sort(self, **kwargs):
    """
    ListEnsembleSortEnricher
    """
    self._add_processor(ListEnsembleSortEnricher(kwargs))
    return self

  def list_wise_item_attr(self, **kwargs):
    """
    ListwiseItemAttrEnricher
    """
    self._add_processor(ListwiseItemAttrEnricher(kwargs))
    return self

  def list_variant_diversity(self, **kwargs):
    """
    ListVariantDiversityArranger
    """
    self._add_processor(ListVariantDiversityArranger(kwargs))
    return self

  def list_variant_filter(self, **kwargs):
    """
    ListVariantFilterArranger
    """
    self._add_processor(ListVariantFilterArranger(kwargs))
    return self

  def list_ensemble_filter(self, **kwargs):
    """
    FountainListEnsembleFilterArranger
    """
    self._add_processor(FountainListEnsembleFilterArranger(kwargs))
    return self

  def retrieve_by_fountain_itemcf(self, **kwargs):
    """
    RecoItemCfRetriever
    """
    self._add_processor(RecoItemCfRetriever(kwargs))
    return self

  def fountain_variant_cluster_subdivision_sort_arranger(self, **kwargs):
    """
    FountainVariantClusterSubdivisionSortArranger
    """
    self._add_processor(FountainVariantClusterSubdivisionSortArranger(kwargs))
    return self

  def flow_control_with_variant_sort(self, **kwargs):
    """
    SubdivisionFlowControlWithVariantArranger
        "flow_control": {
          "type_name": "SubdivisionFlowControlWithVariantArranger",
          "skip": "{{skip_flow_control}}",
          "need_diversity_num" : 16, #流控头部流量范围，TOP N
          "window_size" : 8,  #打散规则的窗口长度
          "variant_count" : 2, #打散窗口内最大出现次数
          "variant_name" : "hetu_level2_tags", #需要打散字段name
          "flow_control_configs" : [  #需要流控的比例
            {
              "name": "is_nice_photo",
              "percentage": 0.3,
            },
            {
              "name": "is_bili_photo",
              "percentage": 0.1,
            },
            {
              "name": "is_follow_photo",
              "percentage": 0.1,
            },
            {
              "name": "is_clodstart_photo",
              "percentage": 0.05,
            }
          ],
          "$output_item_attrs": []
        }
    """
    self._add_processor(SubdivisionFlowControlWithVariantArranger(kwargs))
    return self

  def flow_control_with_variant_v2_sort(self, **kwargs):
    """
    SubdivisionFlowControlWithVariantV2Arranger
        "flow_control": {
          "type_name": "SubdivisionFlowControlWithVariantV2Arranger",
          "skip": "{{skip_flow_control}}",
          "need_diversity_num" : 16, #流控头部流量范围，TOP N
          "window_size" : 8,  #打散规则的窗口长度
          "variant_count" : 2, #打散窗口内最大出现次数
          "variant_name" : "hetu_level2_tags", #需要打散字段name
          "flow_control_configs" : [  #需要流控的比例
            {
              "name": "is_nice_photo",
              "percentage": 0.3,
            },
            {
              "name": "is_bili_photo",
              "percentage": 0.1,
            },
            {
              "name": "is_follow_photo",
              "percentage": 0.1,
            },
            {
              "name": "is_clodstart_photo",
              "percentage": 0.05,
            }
          ],
          "$output_item_attrs": []
        }
    """
    self._add_processor(SubdivisionFlowControlWithVariantV2Arranger(kwargs))
    return self
