#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class FountainAttrFractilePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_perflog_attr_fractile"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("item_attrs", []))
    return attrs

class FountainEnvironmentPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_environment_perf_log"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if 'enable_pxtr_miss_perf' in self._config:
      attrs.add(self._config["enable_pxtr_miss_perf"])
    if 'enable_upload_day_perf' in self._config:
      attrs.add(self._config["enable_upload_day_perf"])
    if 'upload_day_divide' in self._config:
      attrs.add(self._config["upload_day_divide"])
    attrs.add(self._config.get('check_point'))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("item_pxtrs", []))
    if 'upload_time_attr' in self._config:
      attrs.add(self._config['upload_time_attr'])
    return attrs
