#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class ExploreFullRankPredictItemAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "predict_by_item_attr"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_attr"))
        attrs.update(self._config.get("common_attrs"))
        for name in ["kess_service"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("content_safety_level_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        pxtr=[
            "pctr",
            "pltr",
            "pwtr",
            "pftr",
            "psvr",
            "plvtr",
            "pvtr",
            "pcmtr",
            "pptr",
            "pevr",
            "ptag_click",
            "pmf_ctr",
            "pjoin_topic",
            "ptag_ctr",
            "pliving_click",
            "plive_wtr",
            "pdctr",
            "ppeef",
            "pcmef",
            "pwatch_time",
            "pfvtr",
            "phtr",
            "pdtr",
            "pfr_score1",
            "pfr_score2",
            "eyeshot_pevtr",
            "eyeshot_pltr",
            "eyeshot_pwtr",
            "eyeshot_pftr",
            "eyeshot_plvtr",
            "eyeshot_pfvtr",
            "eyeshot_pvtr",
            "eyeshot_cmef",
            "pfr_score",
            "pwtd",
            ]
        return { self._config.get("output_prefix", "") + v for v in pxtr }

class FountainCascadeScoreEnricher(LeafEnricher):
    """
    FountainCascadeScoreEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_enrich_cascade_score"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["fountain_cascade_ctr_param", "fountain_cascade_ctr_power", "fountain_cascade_ltr_param",
        "fountain_cascade_wtr_param", "fountain_cascade_ftr_param", "fountain_cascade_lvtr_param",
        "fountain_cascade_picture_discount", "fountain_cascade_time_param",
        "fountain_cascade_ensemble_use_ctr", "fountain_cascade_ensemble_lvtr_use_watch_time", "fountain_cascade_upload_time_slope",
        "fountain_cascade_enable_lvtr_sigmoid_bias_fix", "fountain_cascade_lvtr_sigmoid_bias", "cascade_linear_score_weights",
        ])
        for key in ["svtr_coeff", "svtr_power", "short_play_discount_value", "lvtr_use_predict_watch_time", "mid_photo_boost_coeff"]:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["upload_time", "picture_variant_attr", "view_length_sum", "click_count", "duration_ms",
                "cascade_pctr", "cascade_pltr", "cascade_pwtr", "cascade_plvtr", "cascade_psvtr", "cascade_pftr",
                ])
        attrs.add(self._config.get("pwatch_time_attr"))
        attrs.add(self._config.get("pptr_attr"))
        attrs.add(self._config.get("pepstr_attr"))
        attrs.add(self._config.get("pcestr_attr"))
        attrs.add(self._config.get("pcmtr_attr"))
        attrs.add(self._config.get("pwtd_attr"))
        attrs.add(self._config.get("pslide_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set(["cascade_score", "cascade_shortview_score2", "cascade_longview_score"])
        return attrs


class CommonAttrEnrichFromRedisJson(LeafEnricher):
    """
    CommonAttrEnrichFromRedisJson
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "common_attr_enrich_from_redis_json"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("json_queues"):
            attrs.add(cfg.get("output_attr", ""))
        return attrs


class ItemAttrEnrichFromRedisJson(LeafEnricher):
    """
    ItemAttrEnrichFromRedisJson
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "item_attr_enrich_from_redis_json"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("json_queues"):
            attrs.add(cfg.get("output_attr", ""))
        return attrs

class ItemPredictByXgb(LeafEnricher):
    """
    ItemPredictByXgb
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "item_predict_by_xgb"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["model_kconf_key"])
        for fea in self._config.get("common_feature_attrs"):
            attrs.add(fea)
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for fea in self._config.get("item_feature_attrs"):
            attrs.add(fea)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("comment_ltr")
        return attrs

class ItemXgbKmlPredictEnrich(LeafEnricher):
    """
    ItemXgbKmlPredictEnrich
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "item_xgb_kml_predict_enrich"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.update(self.extract_dynamic_params(self._config.get("model_name")))
        attrs.update(self.extract_dynamic_params(self._config.get("kml_service_name")))
        attrs.update(self.extract_dynamic_params(self._config.get("common_attrs_oredr_kconf")))
        attrs.update(self.extract_dynamic_params(self._config.get("item_attrs_oredr_kconf")))
        for fea in self._config.get("common_feature_attrs"):
            attrs.add(fea)
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for fea in self._config.get("item_feature_attrs"):
            attrs.add(fea)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("comment_ltr")
        attrs.add(self._config.get("output_attr"))
        return attrs

class FountainRelatedScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_related_sccore_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["sourcePidHetuLevelFourList", "sourcePidHetuLevelThreeList", "sourcePidHetuLevelTwoList",
                    "sourcePidHetuLevelOneList", "sourcePidAuthorId", "sourcePidThirdLevelCategory",
                    "sourcePidSecondLevelCategory", "sourcePidFirstLevelCategory", "sourcePidHetuFaceIdList"])
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["hetu_tag_level_info.hetu_level_four", "hetu_tag_level_info.hetu_level_three",
                    "hetu_tag_level_info.hetu_level_two", "hetu_tag_level_info.hetu_level_one",
                    "author.id", "author.category_detail.third_level_id",
                    "author.category_detail.second_level_id", "author.category_detail.first_level_id",
                    "hetu_tag_level_info.hetu_face_id"])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs


class FountainRelatedScoreV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_related_sccore_v2_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["source_hetu_level_four_v2", "source_hetu_level_three_v2", "source_hetu_level_two_v2",
                    "source_hetu_level_one_v2", "sourcePidFourthLevelCategory", "sourcePidThirdLevelCategory",
                    "source_hetu_face_id_v2", "source_hetu_tag_v2", "source_hetu_cluster_id_v2"])
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["hetu_level_four_v2", "hetu_level_three_v2", "hetu_level_two_v2", "hetu_level_one_v2",
                    "author.category_detail.fourth_level_id", "author.category_detail.third_level_id",
                    "hetu_face_id_v2", "hetu_tag_v2", "hetu_tag_level_info_v2.hetu_cluster_id"])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_score_to_attr"))
        return attrs


class FountainRelatedTagCommonAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_related_tag_common_attr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["mmu_hetu_related_tag1", "mmu_hetu_related_tag2", "mmu_hetu_related_tag3"])
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for name in ["save_tag_list1_to_attr", "save_tag_list1_to_attr", "save_tag_list3_to_attr", "save_tag_list4_to_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs


class NnUserEmbeddingCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fountain_fetch_nn_user_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.add(self._config.get("user_info_attr"))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_embedding_attr"))

    return attrs


class FountainSamplePackageEnricher(LeafEnricher):
    """
    FountainSamplePackageEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_enrich_sample_package"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["load_attr", "size_limit"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        for name in ["common_attrs"]:
            if name in self._config:
                attrs.update(self._config.get(name))
        for name in ["common_attrs_from_kconf", "item_attrs_from_kconf", "sample_ratio"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        for v in self._config["sample_config"]:
            if isinstance(v, dict):
                for val in v.keys():
                    if val in ["sample_begin","sample_end","sample_num"]:
                        attrs.add(v[val])
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["item_attrs"]:
            if name in self._config:
                attrs.update(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for name in ["output_attr"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class FountainCascadeClusterIdEnricher(LeafEnricher):
    """
    FountainCascadeClusterIdEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_enrich_cascade_cluster_id"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["enable_user_profile_top_hetu_level_one_cluster", "enable_user_profile_top_hetu_level_two_cluster",
                "user_profile_tag_score_limit", "user_profile_limit_num", "enable_frequency_duration_cluster",
                "enable_colossus_top_hetu_one_cluster", "enable_colossus_top_hetu_two_cluster",
                "colossus_top_hetu_level_two_attr", "colossus_top_hetu_level_one_attr", "variant_queue_num",
                "enable_use_real_show_list", "enable_use_click_list", "enable_use_like_list", "enable_use_follow_list",
                "enable_use_forward_list", "real_show_weight", "click_weight", "like_weight", "follow_weight", "forward_weight",
                "enable_use_fountain_real_show_list", "enable_use_fountain_click_list", "enable_use_fountain_like_list",
                "enable_use_fountain_follow_list", "enable_use_fountain_forward_list", "fountain_real_show_weight",
                "fountain_click_weight", "fountain_like_weight", "fountain_follow_weight", "fountain_forward_weight",]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add("userInfo")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["hetu_tag_level_info.hetu_level_one", "hetu_tag_level_info.hetu_level_two", "duration_ms"])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["save_cluster_id_to_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

class GenEnsembleSecondSequenceEnricher(LeafEnricher):
    """
    GenEnsembleSecondSequenceEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "gen_ensemble_second_sequence"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("bias_range", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("weight_lower_bound", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("enable_use_proportion", "")))
        for name in ["max_sequence_num", "origin_seq_range", "sequence_max_size",
                     "proportion_temperature", "use_proportion", "use_pow_rank", "discount_map",
                     "enable_gen_ensemble_use_order_13", "enable_gen_ensemble_use_order_23",
                     "gen_final_seq_max_size"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        for name in ["hetu_level_one_attr"]:
            attrs.add(self._config.get(name))
        return attrs

class GenOriginalSequenceEnricher(LeafEnricher):
    """
    GenOriginalSequenceEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "gen_original_sequence"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["enable_random_gen_list", "random_gen_list_topk", "random_gen_list_nums"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for name in ["reason"]:
            if name in self._config:
                attrs.add("retrieval_list_keys_" + str((self._config.get(name))))
        return attrs

class GenFountainBeamSearchListEnricher(LeafEnricher):
    """
    GenFountainBeamSearchListEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "gen_beamsearch_sequence"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("bs_search_num_attr"))
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
        for name in ["enable_fountain_rerank_use_beamsearch", "fountain_rerank_beamsearch_size",
                    "fountain_rerank_beamsearch_rate_type", "fountain_rerank_beamsearch_max_len",
                    "use_proportion", "use_pow_rank", "rate", "smooth", "fountain_enable_list_ensemble_filter",
                    "const_for_linear", "weight_for_linear", "bs_fix_top1_method"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["rerank_expected_value_score_splash_attr"]:
            attrs.add(self._config.get(name))
        return attrs

class ListwiseFountainSeqAttrEnricher(LeafEnricher):
    """
    ListwiseFountainSeqAttrEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_wise_seq_attr"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["pre_request_photo_key", "recent_hetu_level_one_key", "recent_hetu_level_two_key"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        # FIXME (huzengyi) 由于内流 leaf dsl 中实现的不规范，这里暂时不能添加正确的 item attr
        return set()

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        return set(self._config.get("output_attrs", []))

class ListItemPredictEnricher(LeafEnricher):
    """
    ListItemPredictEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_item_predict"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["attr_name_transform_map"]:
            for key in self._config.get(name):
                attrs.add(key)
        for name in ["extra_common_attrs", "rr_wtd_attrs"]:
            if name in self._config:
                for common_attr in self._config.get(name):
                    attrs.add(common_attr)
        for name in ["kess_service", "origin_seq_reason", "timeout_ms",
                    "layer_name", "shard_num", "use_odd_score", "enable_use_kai", "loss_name",
                    "use_dev_score", "dev_alpha", "dev_beta", "use_product_score", "use_pos_discount",
                    "pos_reward_str", "rerank_list_next_weight", "enable_use_list_score", "item_next_attr",
                    "enable_use_pos_context_score", "last_coeff", "enable_use_pos_next_multiply", "mul_target_weight_pos",
                    "mul_target_weight_next", "mul_target_weight_play", "mul_target_weight_raw_pos", "mul_target_weight_raw_next",
                    "mul_target_weight_raw_play", "mul_target_weight_raw_pow_pos", "mul_target_weight_raw_pow_next", "mul_target_weight_raw_pow_play",
                    "enable_user_list_score", "list_score_weight_pos", "list_score_weight_next", "list_score_weight_play",
                    "enable_use_pos_next_add", "pos_next_cut", "pos_next_weight", "enable_use_bellman_equation","enable_play_time_reverse",
                    "enable_use_mul_target", "enable_use_ensemble_sort", "rerank_list_ltr_weight"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(self._config.get("item_attrs", []))
        for name in ["rr_duration_attrs"]:
            if name in self._config:
                for item_attr in self._config.get(name):
                    attrs.add(item_attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["output_attr", "output_pred_val_attr"]:
            attrs.add(self._config.get(name))
        for name in ["output_pointwise_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

class ListEnsembleSortEnricher(LeafEnricher):
    """
    ListEnsembleSortEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_ensemble_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
        for name in ["use_proportion", "use_pow_rank", "fountain_enable_list_ensemble_sort", "enable_use_debias_score",
                    "fountain_rerank_ensemble_list_weight"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class ListwiseItemAttrEnricher(LeafEnricher):
    """
    ListwiseItemAttrEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_wise_item_attr"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["seq_score_attr_name"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        for name in ["list_reason", "reason_set"]:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["seq_score_attr_name"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        for name in ["seq_item_attr_to_item_attr", "input_seq_predict_name"]:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["prm_score_attr_name"]:
            if name in self._config:
                attrs.update(self._config.get(name))
        attrs.add("virtual_rerank_score")
        for name in ["seq_item_attr_to_item_attr", "output_item_predict_name"]:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        for name in ["fountain_rr_list_reason_0_count","fountain_rr_list_reason_2_count", "fountain_rr_list_reason_4_count",
                     "fountain_rr_list_reason_10_count","fountain_rr_list_reason_666_count", "ssd_div_score_attr",
                     "pic_insert_flag_attr"]:
            attrs.add(self._config.get(name))
        return attrs

class DppGenSequenceEnricher(LeafEnricher):
    """
    DppGenSequenceEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "dpp_gen_sequence"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("bias_range", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_weight", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_pow_weight", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_weight_addAndMul", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_pow_weight_addAndMul", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_weight_multiply", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_pow_weight_multiply", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("que_discount_coef", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("weight_lower_bound", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("enable_use_proportion", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("variant_weight", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("avg_xtr", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("dynamic_weight", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("min_ratio", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("max_ratio", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("user_xtr", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_score_alpha", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("score_norm", 0.0)))
            attrs.update(self.extract_dynamic_params(cfg.get("weight_addAndMul", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_weight_fractile", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("raw_pow_weight_fractile", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("boost_topk_coef", "")))
            attrs.update(self.extract_dynamic_params(cfg.get("boost_topk_threshold", "")))
        if "sim_matrix_pxtr_emb" in self._config:
            for cfg in self._config.get("sim_matrix_pxtr_emb"):
              attrs.add(cfg.get("weight_attr", ""))
        if "dpp_core_attr_except_embedding" in self._config:
            for cfg in self._config.get("dpp_core_attr_except_embedding"):
                attrs.add(cfg.get("weight_attr", ""))
                attrs.add(cfg.get("max_value_attr", ""))
                attrs.add(cfg.get("min_value_attr", ""))
        if "hetu_emsemble_attr" in self._config:
            for cfg in self._config.get("hetu_emsemble_attr"):
                attrs.add(cfg.get("weight_attr", ""))
                attrs.add(cfg.get("avg_attr", ""))
        if "cid_emsemble_attr" in self._config:
            for cfg in self._config.get("cid_emsemble_attr"):
                attrs.add(cfg.get("weight_attr", ""))
                attrs.add(cfg.get("avg_attr", ""))
        if self._config.__contains__("rules"):
            for r in self._config.get("rules"):
                for key in ["priority", "window_size", "max_num", "min_num", "enabled", "consider_prev_items", "window_type"]:
                    attrs.update(self.extract_dynamic_params(r.get(key)))
        for name in ["fix_diversity_score", "max_sequence_num", "origin_seq_range", "enable_only_max_dpp_score_list", "raw_score_pow_type", "raw_fractile_score_pow_type",
                     "embedding_service_name", "dpp_diversity_shard_num", "embedding_slot_id",
                     "embedding_sign_format", "embedding_timeout_ms", "keep_pre_size",
                     "the_temperature", "use_power_rank", "use_proportion", "use_neg_weight",
                     "use_sigmoid", "use_combine", "sigmoid_beta", "sigmoid_gamma", "combine_smooth", "combine_alpha",
                     "diversity_list_size", "rank_theta", "enable_dpp", "embedding_format",
                     "dm_epsilon", "filter_red_vertical_num", "final_cnt",
                     "enable_ssd_filter_skip_sin", "enable_skip_sin_que", "enable_dpp_use_ssd",
                     "enable_dpp_diversity_div_que", "diversity_history_size", "enable_slot_num_minus_offset",
                     "enable_new_variety_engineer", "max_satisfied_pick",
                     "is_explore", "action_day", "enable_relate_score_org","enable_gen_distill_model","enable_gen_next_model","enable_plus_cal_method","next_model_param_a","next_model_param_b",
                     "weight_adjust_param_a","weight_adjust_param_b","weight_adjust_param_c","enable_ensemble_hetu_cal","enable_relate_score_ensemble","enable_switch_method","ltr_model_param_a","ltr_model_param_b",
                     "enable_relate_score_ltr", "enable_ensemble_hetu_score","enable_point_model_ltr_score","ensemble_hetu_score_method","top_rank_threshold","duration_threshold",
                     "use_div_prefer", "div_lower_bound", "div_upper_bound", "div_bias", "smooth_num", "top_slot", "min_gap",
                     "enable_pic_mix_generator", "use_set_filter", "set_max_filter_cnt", "related_score_power_weight","rr_origin_num", "enable_ee_list",
                     "enable_dynamic_pic_min_gap", "enable_new_single_queues", "dpp_beam_size", "relevant_score_type",
                     "rank_score_type", "embedding_orthogonal_method", "embedding_orthogonal_bias",
                     "enable_sim_matrix_combo", "sim_matrix_alpha", "enale_dpp_sim_matrix_norm", "dpp_emb_dim",
                     "enable_pxtr_based_sim_matrix", "pxtr_sim_matrix_alpha", "pxtr_matrix_row_norm", "pxtr_matrix_col_norm",
                     "the_temperature_addAndMul", "use_power_rank_addAndMul", "use_proportion_addAndMul", "sequence_num_multiple",
                     "enable_theta_random", "theta_bias_range", "personalized_param_type", "personalized_base", "personalized_min", "personalized_max", "use_rank_div", "related_score_smooth",
                     "multiply_use_power_rank", "sequence_num_for_multiply", "enable_max_hetu1_dpp_regular", "max_hetu1_dpp_regular_num", "personalized_timely_param_type", "personalized_hetu_top1_param_type",
                     'enable_pic_fixed_slots', 'enable_que_tail_discount', 'que_tail_discount_threshold', 'que_tail_discount_coef','que_tail_discount_min',
                     "que_tail_boost_threshold", "enable_que_tail_adaptive_discount", "enable_que_tail_adaptive_boost",
                     'pxtr_matrix_col_zero_centered', "hetu_history_size", "enable_session_hetu1_dpp_regular", "session_hetu1_dpp_regular_num",
                     "time_gap_limit", "enable_cal_kl_score", "enable_kl_fusion_real_show_hetu_cnt", "kl_max_threshold",
                     "hetu_rate_min_threshold", "kl_fusion_real_show_size_max_threshold", "enable_get_user_longterm_interest", "enable_es_score_hetu_cal","enable_es_score_hetu_not_pow_cal", "enable_es_score_cid_cal",
                     "enable_fpsa_recall", "fpsa_diversity_wt", "fpsa_beam_size","fpsa_result_size", "fpsa_fintr_wt", "fpsa_next_wt", "fpsa_diversity_num", "fpsa_cid_max_cnt", "enable_fpsa_diversity", "enable_single_recall_list",
                     "kl_score_smooth_alpha", "real_show_history_size_min_threshold", "enable_get_session_hetu1", "div_recall_wt", "div_rel_cnt", "enable_diversity_recall", "div_mode_type",
                     "real_show_unique_hetu_min_threshold", "kl_score_power_weight", "enabl_kl_fusion_add_sigmod",
                     "enable_use_rerank_distill_ori", "enable_use_rerank_distill", "rerank_distill_weight", "enable_dpp_gen_model", "rerank_gen_model_beam_size", "enable_fresh_request_dynamic_config",
                     "pic_fix_slot_skip_variety", "enable_random_replace_topk", "random_replace_topk",
                     "enable_raw_weight_random", "enable_raw_pow_weight_random", "enable_sim_matrix_exp", "matrix_exp_param", "use_multiply", "enable_sim_matrix_sigmoid", "sim_matrix_sigmoid_scale_param","sim_matrix_sigmoid_move_param",
                     "dpp_dynamic_action_space", "enable_discrete_action_space", "enable_pic_mix_insertion", "mix_insert_num_limit",
                     "mix_insert_range_end", "mix_insert_pic_boost_coef", "dpp_dynamic_noise_weight", "pic_mix_insertion_skip_single_pic", "pics_to_insert_after_dpp",
                     "fixed_slots_after_dpp", "enable_pic_explore", "item_key_for_pic_explore", "pic_explore_flag", "pic_explore_insert_pos_min", "pic_explore_insert_pos_max", "enable_pic_explore_pos_move_forward",
                     "enable_pic_interest_explore", "item_key_for_pic_interest_explore", "pic_interest_explore_cluster_id_list_attr",
                     "enable_pxtr_matrix_cliff", "enable_dpp_gen_list", "enable_ssd_gen_list", "enable_ori_gen_list", "ssd_diversity_factor", "enable_hetu1_filter", "hetu1_filter_num",
                     "enable_sim_matrix_changed_by_hetu", "interest_discount_type", "interest_hetu1_alpha", "interest_hetu1_smooth", "enable_get_user_shortterm_interest",
                     "ssd_score_fusion_type", "ssd_rank_score_weight", "enable_big_circle_method", "enable_increasing_mmu_emb", "multiple_emb_weight",
                     "enable_use_actual_reward", "hetu_adjust_coef", "hetu_adjust_min_value", "hetu_adjust_max_value", "enable_hetu_adjust_in_rank", "enable_actual_interest_in_es",
                     "enable_use_session_ssd", "enable_get_session_item", "enable_marketing_compensation_photo_discount", "dbrs_rank_score_weight", "enable_cal_diversity_dprs", "dbrs_random_param",
                     "marketing_compensation_photo_discount_coeff", "marketing_compensation_photo_adjust_version", "marketing_compensation_photo_scale_factor", "enable_pic_interest_expand", "item_key_for_pic_interest_expand",
                     "enable_replace_pic_es_score_by_pic_rank"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add(self._config.get("prev_items_from_attr"))
        attrs.add(self._config.get("pic_quota_attr"))
        attrs.add(self._config.get("pic_fixed_slot_conf_attr"))
        attrs.add(self._config.get("fresh_request_pic_fixed_slot_conf_attr"))
        attrs.add(self._config.get("is_fresh_request_attr"))
        attrs.add(self._config.get("user_hetu_stat_attr"))
        attrs.add(self._config.get("user_info_ptr_attr"))
        attrs.add(self._config.get("actual_hetu_stat_attr"))
        attrs.add(self._config.get("actual_cid_stat_attr"))
        attrs.add(self._config.get("personalized_score_attr"))
        attrs.add(self._config.get("personalized_timely_score_attr"))
        attrs.add(self._config.get("personalized_hetu_top1_attr"))
        attrs.add(self._config.get("cluster_idsource_attr"))
        attrs.add(self._config.get("fpsa_next_score_attr"))
        attrs.add(self._config.get("fpsa_fintr_score_attr"))
        attrs.add(self._config.get("div_relate_score_attr"))
        attrs.add(self._config.get("pid_embedding_common_attr"))
        attrs.add(self._config.get("user_hetu1_distribution_attr"))
        for r in self._config.get("single_queues", []):
            for key in ["enabled"]:
                attrs.update(self.extract_dynamic_params(r.get(key)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set() 
        attrs.add(self._config.get("cluster_id862_attr"))
        attrs.add(self._config.get("cluster_id_attr"))
        attrs.add(self._config.get("duration_attr"))
        attrs.add(self._config.get("predict_play_time_attr"))
        attrs.add(self._config.get("predict_vv_attr"))
        attrs.add(self._config.get("predict_action_once_attr"))
        attrs.add(self._config.get("gen_distill_model_score_attr"))
        attrs.add(self._config.get("gen_pos_score_attr"))
        attrs.add(self._config.get("gen_next_score_attr"))
        attrs.add(self._config.get("picture_attr"))
        attrs.add(self._config.get("mix_score_attr"))
        attrs.add(self._config.get("pic_score_attr"))
        attrs.add(self._config.get("dpp_emb_attr_name"))
        attrs.add(self._config.get("rerank_distill_attr"))
        attrs.add(self._config.get("rerank_gen_model_attr"))
        attrs.add(self._config.get("mix_insert_score_attr"))
        attrs.add(self._config.get("picture_type_attr"))
        attrs.add(self._config.get("pic_interest_explore_cluster_id_attr"))
        attrs.add(self._config.get("marketing_compensation_photo_attr"))
        attrs.add(self._config.get("marketing_compensation_photo_reward_coeff_attr"))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
            attrs.add(cfg.get("fractile_name", ""))
        for cfg in self._config.get("single_queues", []):
            attrs.add(cfg.get("name", ""))
        if self._config.__contains__("rules"):
            for r in self._config.get("rules"):
                attrs.add(r.get("attr_name"))
        if "dpp_core_attr_except_embedding" in self._config:
            for cfg in self._config.get("dpp_core_attr_except_embedding"):
                attrs.add(cfg.get("name_attr", ""))
        if "sim_matrix_pxtr_emb" in self._config:
            for cfg in self._config.get("sim_matrix_pxtr_emb"):
                attrs.add(cfg.get("pxtr_attr", ""))
        if "hetu_emsemble_attr" in self._config:
            for cfg in self._config.get("hetu_emsemble_attr"):
                attrs.add(cfg.get("pxtr_attr", ""))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("retrieval_list_keys_6")
        return attrs
class StarryEmbeddingEnricher(LeafEnricher):
    """
    StarryEmbeddingEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "starry_embedding_enrich"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        if self._config.get("query_source_type", "") in ["common_attr"] and "query_source_attr_name" in self._config:
            attrs.add(self._config.get("query_source_attr_name"))
        for name in ["total_limit","top_k"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        if self._config.get("query_source_type", "") in ["common_attr"]:
            if "output_attr_name" in self._config:
                attrs.add(self._config.get("output_attr_name"))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        if self._config.get("query_source_type", "") in ["item_attr"] and "query_source_attr_name" in self._config:
            attrs.add(self._config.get("query_source_attr_name"))
        if self._config.get("query_source_type", "") not in ["common_attr"]:
            if self._config.get("filter_item_attr_name", "") not in [""]:
                attrs.add(self._config.get("filter_item_attr_name"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        if self._config.get("query_source_type", "") not in ["common_attr"]:
            if "output_attr_name" in self._config:
                attrs.add(self._config.get("output_attr_name"))
        return attrs

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True
