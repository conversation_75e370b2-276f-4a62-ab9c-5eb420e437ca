#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class RecoPushKeepOrderQuantityArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "keep_order_and_quantity"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["total_num", "keep_topk_ratio", "attr_name", "queue"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class RecoPushTruncateByAttrArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "push_truncate_by_attr"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["attr_name", "size_limit", "min_survival_ratio", "attr_truncate_config"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs


class PushChannelSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "push_channel_sort"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_queue_names")))
    attrs.update(self.extract_dynamic_params(self._config.get("force_last_queue_name")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_quota' in value.keys():
              attrs.update(self.extract_dynamic_params(value.get("sub_quota")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    if 'sub_queue_config' in self._config.keys():
      sub_queue_configs = self._config.get("sub_queue_config")
      if isinstance(sub_queue_configs, dict):
        for value in sub_queue_configs.values():
          if isinstance(value, dict):
            if 'sub_queue_score_attrs' in value.keys():
              attrs.update(value.get("sub_queue_score_attrs"))
            if 'sub_queue_flag_attrs' in value.keys():
              attrs.update(value.get("sub_queue_flag_attrs"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["mc_channel_origin_rank", "mc_channel_origin_name", "sub_mc_channel_origin_name", "sub_mc_channel_origin_rank"])
    return attrs

