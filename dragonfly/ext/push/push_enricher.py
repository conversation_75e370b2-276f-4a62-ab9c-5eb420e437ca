import base64
import inspect

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class CommonRecoPushPredictEnricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    config["kuiba_user_attrs"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_push_xtr_by_predict"

  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    return True

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["kuiba_user_attrs"] }
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
     attrs = ("pctr", "pltr", "pwtr", "pftr", "phtr", "pvtr")
     return { self._config.get("output_prefix", "") + v for v in attrs } 
  

class RecoPushFixPctrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fix_model_pctr_by_event_type"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")      

class RecoPushFixPctrV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fix_model_pctr_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["hourOfDay"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["event_type"])
    attrs.add(self._config["mix_ctr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_item_attr"])
    return attrs

class RecoPushFixPctrV5Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fix_model_pctr_v5"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["hourOfDay"])
    attrs.add(self._config["uProvince"])
    attrs.add(self._config["app"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["event_type"])
    attrs.add(self._config["mix_ctr"])
    attrs.add(self._config["infra_terminal"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_item_attr"])
    return attrs

class RecoPushPhotoInfoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_push_photo_info_attrs"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(self._config.get("photo_info_attrs", []))
    return attrs

class RecoPushSelectKeyAndValueAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "select_key_and_value"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_key_attr", 0))
    attrs.add(self._config.get("output_value_attr", 0.0))
    return attrs

class RecoPushParseJsonStrByListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_json_str_by_list"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr", 0.0))
    return attrs

class RecoPushEnrichJsonByItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_json_by_item_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("key_attr", ""))
    ret.add(self._config.get("value_attr", ""))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_json_to"])
    return attrs

class DenseMatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dense_match"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["keys"])
        ret.add(config["scores"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("to_match_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["output"])
    return ret

class DenseMatchV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dense_match_v2"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["keys"])
        ret.add(config["scores"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("to_match_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["output"])
    return ret

class DenseMatchV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dense_match_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["keys"])
        ret.add(config["scores"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("to_match_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["output"])
    return ret

class RawSamplePackageLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "modify_raw_sample_package_label"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("pid_attr_name"), "`pid_attr_name` 是必选项")
    check_arg(self._config.get("label_config"), "`label_config` 是必选项")
    check_arg(self._config.get("rsp_pointer_common_attr"), "`rsp_pointer_common_attr` 是必选项")
    for cfg in self._config.get("label_config"):
      check_arg(cfg.get("label_name"), "`label_name` 是必选项")
      check_arg(cfg.get("modify_pid_list_common_attr"), "`modify_pid_list_common_attr` 是必选项")
      check_arg(cfg.get("label_value_list_common_attr"), "`label_value_list_common_attr` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    input = set()
    input.update([self._config.get("rsp_pointer_common_attr"),self._config.get("pid_attr_name")])
    for cfg in self._config.get("label_config"):
      input.update([cfg.get("modify_pid_list_common_attr"),cfg.get("label_value_list_common_attr")])
    return input

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()
  
  @strict_types
  def is_async(self) -> bool:
    return True


class RecoPushHashGetAllEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hash_get_all_from_redis"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("is_async", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("key", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("field_attr"))
    attrs.add(self._config.get("value_attr"))
    return attrs



class RecoPushHashGetAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hash_get_attr_from_redis"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("is_async", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("key", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("value_attr"))
    return attrs


class RecoPushNullItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reco_push_null_item_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { v["check_attr_name"] for v in self._config["mappings"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { v["output_attr_name"] for v in self._config["mappings"] }

class RecoPushParseTimingSeqFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reco_push_parse_timing_seq_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {"last_time_int", "new_day", "reflux_day", "l30", "ack_l30", "click_l30", "duration_cur_l30", "duration_l30", "vv_l30"}

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {"arrive_time"}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { "l30", "ack_l30", "click_l30", "duration_cur_l30", "duration_l30", "vv_l30", "new_day", "reflux_day", "last_active_day", "minute_of_day", "no_arrive_duration", "no_arrive_duration"}

class PushColossusV2ItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "push_colossusv2_item_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {self._config.get("item_key_attr")}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {i for i in self._config.get("output_attrs")}