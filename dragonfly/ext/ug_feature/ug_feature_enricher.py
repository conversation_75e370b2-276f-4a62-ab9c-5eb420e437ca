#!/usr/bin/env python3
"""
filename: ug_feature_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for ad
author: <EMAIL>
date: 2024-04-01 00:00:00
"""

from ...common_leaf_util import strict_types, check_arg, ArgumentError,extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher


class UgFeatureGetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("feature_params", []):
      attrs.update(self.extract_dynamic_params(string_config.get("feature_name", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("feature_params", []):
      attrs.add(string_config["output_attr_name"])
    return attrs
  


class UgFeatureGetByRpcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_feature_by_rpc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("id")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    for string_config in self._config.get("feature_params", []):
      attrs.update(self.extract_dynamic_params(string_config.get("feature_name", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for string_config in self._config.get("feature_params", []):
      attrs.add(string_config["output_attr_name"])
    return attrs

class UgFeatureClothoCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_common_attr_by_clotho"
  
  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("key"), "`key` 是必选项")
    check_arg(self._config.get("client_kess_service"), "`client_kess_service` 是必选项")
    check_arg(self._config.get("clotho_kconf_key"), "`clotho_kconf_key` 是必选项")
    if self._config.get("is_dynamic_output", False) == False:
      for string_config in self._config.get("include_attrs", []):
        check_arg(string_config.get("field_name"), "`include_attrs: field_name` 是必选项")
    
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("client_kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("key")))
    attrs.update(self.extract_dynamic_params(self._config.get("columns")))
    attrs.add(self._config.get("timeout_ms"))
    attrs.add(self._config.get("clotho_kconf_key"))
    attrs.add(self._config.get("biz_name"))
    attrs.add(self._config.get("is_dynamic_output"))
    if self._config.get("is_dynamic_output", False) == False:
      for string_config in self._config.get("include_attrs", []):
        attrs.add(string_config.get("field_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_dynamic_output", False):
      attrs.add(self._config.get("include_attrs", []))
    else:
      for string_config in self._config.get("include_attrs", []):
        attrs.add(string_config["output_name"])
    return attrs

class UgFeatureFeasuryCommonAttrV1Enricher(LeafEnricher):
  @strict_types
  def __init__(self, config: dict):
    if "save_attr_names_to_attr" not in config:
      config["save_attr_names_to_attr"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_common_attr_by_feasury_v1"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("include_attrs", []))
    attr = self._config.get("save_attr_names_to_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

class UgFeatureProtobufAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_enrich_with_protobuf"

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("save_all_fields", False):
      check_arg(not self._config.get("attrs", []), "save_all_fields=True 时禁止设置 attrs")
    else:
      check_arg(self._config.get("attrs", []), f"{self.get_type_alias()} 的 attrs 配置不可为空")

  @property
  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif "name" in attr:
        attrs.add(attr["name"])
      elif "sample_attr_name" in attr:
        attrs.add(attr["sample_attr_name"])
      elif "sample_attr_name_value" in attr:
        attrs.add(str(attr["sample_attr_name_value"]))
      else:
        attrs.add(attr["path"])
    serialize_to_attr = self._config.get("serialize_to_attr")
    if serialize_to_attr:
      attrs.add(serialize_to_attr)
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._get_input_attrs() if self._is_common_attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._get_input_attrs() if not self._is_common_attr else set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    output_attrs = self._get_output_attrs() if self._is_common_attr else set()
    if self._config.get("save_all_fields", False):
      save_attr_names_to_attr = self._config.get("save_attr_names_to_attr")
      if save_attr_names_to_attr:
        output_attrs.add(save_attr_names_to_attr)
    return output_attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self._is_common_attr else self._get_output_attrs()

class UgFeatureOutputFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_feature_output_feature"

  @strict_types
  def _get_input_attrs(self) -> set:
    pass

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("output_attr", []):
      if isinstance(attr, str):
        attrs.add(attr)
      else:
        raise ArgumentError(f"{attr} only support string")
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs()

class UgFeatureReadQuotaLimitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_ug_feature_read_quota"

  @strict_types
  def _get_input_attrs(self) -> set:
    pass

  @strict_types
  def _get_output_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_attr")
    if isinstance(attr, str):
      attrs.add(attr)
    else:
      raise ArgumentError(f"{attr} only support string")
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._get_output_attrs()