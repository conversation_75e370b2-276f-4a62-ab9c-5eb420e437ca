#!/usr/bin/env python3
# coding=utf-8
"""
filename: ug_feature_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, ad api mixin
author: <EMAIL>
date: 2024-04-01 00:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ug_feature_enricher import *
from .ug_feature_observer import *

class UgFeatureApiMixin(CommonLeafBaseMixin):
  """
  增长特征服务Processor API 接口的 Mixin 实现。
  背景：统一特征服务，服务于增长各个业务，提升增长效率。
  维护人/团队：增长引擎(<EMAIL>、<EMAIL>)
  """

  def get_ug_feature(self, **kwargs):
    """
    UgFeatureGetEnricher
    ------
    传入特征名获取特征值

    参数配置
    ------
    `id`: [string] 必配项，id值，动态参数

    `id_type`: [string] 必配项，id类型，支持oaid、imei、idfa、device_id、user_id

    `product_type`: [string] 选配项，仅当id_type = device_id可用，支持kwai、nebula

   `feature_params`: [list] feature的相关配置
      - `feature_name`:  [string] 必配项，同增长特征注册的name
      - `feature_source_type`: [string] 选配项，实时/离线特征 online/offline，不指定则同时取实时和离线，并优先返回实时特征，不需要都查询的情况下尽量指定
      - `output_attr_name`: [string] 必配项，value 写入的 commonAttr 名
    
    `timeout_ms`: [int] 选配项，存储为redis时client 的超时时间，默认为 10，动态参数


    调用示例
    ------
    ``` python
    .get_ug_feature(
            id="xxxxxxxx",
            id_type = "oaid",
            product_type =
            feature_params=[
                {
                    "feature_name": "alliance_gender",
                    "feature_source_type": "offline",
                    "output_attr_name": "alliance_gender"
                }
            ]
        )
    ```
    """
    self._add_processor(UgFeatureGetEnricher(kwargs))
    return self



  def get_ug_feature_by_rpc(self, **kwargs):
    """
    UgFeatureGetByRpcEnricher
    ------
    传入特征名获取特征值

    参数配置
    ------
    `id`: [string] 必配项，id值，动态参数

    `id_type`: [string] 必配项，id类型，支持oaid、imei、idfa、device_id、user_id

    `product_type`: [string] 选配项，仅当id_type = device_id可用，支持kwai、nebula

   `feature_params`: [list] feature的相关配置
      - `feature_name`:  [string] 必配项，同增长特征注册的name
      - `output_attr_name`: [string] 必配项，value 写入的 commonAttr 名
    
    `timeout_ms`: [int] 选配项，rpc的超时时间，默认为 20，动态参数


    调用示例
    ------
    ``` python
    .get_ug_feature_by_rpc(
            id="xxxxxxxx",
            id_type = "oaid",
            product_type = "", 
            feature_params=[
                {
                    "feature_name": "alliance_gender",
                    "output_attr_name": "alliance_gender"
                }
            ]
        )
    ```
    """
    self._add_processor(UgFeatureGetByRpcEnricher(kwargs))
    return self

  def get_ug_common_attr_by_clotho(self, **kwargs):
    """
    UgFeatureClothoCommonAttrEnricher
    ------
    传入特征名获取特征值

    参数配置
    ------
   `client_kess_service`: [string] 必配项，主调服务名，与主调服务保持一致，动态参数
   `biz_name`: [string] 必配项，特征所属业务，如：push
   `key`: [string|string_list] 必配项，clotho中存储key，一般是id，支持传入列表，特征按第一个id读取的值进行填充，动态参数
   `columns`: [string_list] 选配项，clotho列名，默认全部读取，动态参数
   `timeout_ms`: [int] 选配项，rpc的超时时间，默认为 200ms
   `clotho_kconf_key`: [string] 必配项，clotho kconf名称，参考：userGrowth.matrix.growthFeatureMetaConfig
   `is_dynamic_output`: [bool] 选配项，include_attrs是否支持动态参数，默认不支持，为动态参数时只支持string_list
   `include_attrs`: [list] 特征的相关配置
      - `field_name`:  [string] 必配项，同增长特征注册的特征名
      - `output_name`: [string] 选配项，value 写入的 commonAttr 名
    

    调用示例
    ------
    ``` python
    .get_ug_common_attr_by_clotho(
            client_kess_service="grpc_xxxxxxxx",
            biz_name="PUSH_LEAF",
            key=["xxx", "rrr"],
            columns=[yyy, zzz],
            timeout_ms = 200,
            clotho_kconf_key="kconf_xxx",
            include_attrs=[
                {
                    "field_name": "xxx",
                    "output_name": "xxx"
                },
                "xxx"
            ]
        )
    ```
    """
    self._add_processor(UgFeatureClothoCommonAttrEnricher(kwargs))
    return self

  def get_ug_common_attr_by_feasury_v1(self, **kwargs):
    """
    UgFeatureFeasuryCommonAttrV1Enricher
    ------
    从 SampleList 服务中获取用户特征（SampleAttr 格式）作为 CommonAttr 内容填入 Context 中，include_attrs 只支持动态参数

    参数配置
    ------
    `kess_service`: [string] [动态参数] SampleList 服务的 kess 服务名

    `service_group`: [string] 选配项，SampleList 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] SampleList 服务的超时时间，必须大于 0，默认值 200

    `include_attrs`: [list] 选配项，显式指定需要的 attr，为空不返回。

    `user_id`: [int] [动态参数] 选配项，手动指定要获取特征的 user_id，缺省则使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，手动指定要获取特征的 device_id，缺省则使用当前请求的 device_id

    `attr_config`: [string] 业务token用来动态配置需要请求的attr

    `no_overwrite`: [bool] 选配项，仅当 attr 不存在时写入，默认为 false。

    `save_attr_names_to_attr`: [string] 选配项，(**更建议使用 include_attrs 配置项显式写明属性列表！**) 把从样本拼接服务中获取的 UserAttr 名称列表作为值，save_attr_names_to_attr 值作为 AttrName 存入 CommonAttr 中，以方便后续 Processor 可以知道 CommonAttr 中哪些 Attr 是从样本拼接服务获取的。为空或不设置将不会存储 UserAttr 的名称列表。

    调用示例
    ------
    ``` python
    .get_ug_common_attr_by_feasury_v1(
      kess_service = "grpc_xxx",
      include_attrs = "{{xxx}}"
      attr_config = "Live"
    )
    ```
    """
    self._add_processor(UgFeatureFeasuryCommonAttrV1Enricher(kwargs))
    return self

  def ug_enrich_with_protobuf(self, **kwargs):
    """
    UgFeatureProtobufAttrEnricher
    ------
    从 extra attr 里读取一个 protobuf Message，将指定 field的结果写回到 context，仅支持动态attrs 输入

    注意：不支持set全量field。

    SampleAttr-like 类型
    -----
    该 Processor 给像 SampleAttr 这样的 message 提供了额外的支持，只要 message 符合如下定义，
    都称为 SampleAttr-like 的 message：

    ```protobuf
        // 下面所有等号后的 number 都可以任意指定，不限制。

        enum AttrType { // 任意 enum，名字不限
            // 枚举类型的名字，当该枚举存在时，SampleAttr 里必须有对应的 *_value 字段。
            // AttrType 里至少需要有一个枚举值的名字匹配下面的名字。
            BOOL_ATTR = 0;
            INT_ATTR = 1;
            FLOAT_ATTR = 2;
            STRING_ATTR = 3;
            INT_LIST_ATTR = 4;
            FLOAT_LIST_ATTR = 5;
            STRING_LIST_ATTR = 6;
            // 可以有其他枚举值，会忽略
        };

        // 自定义 kv 属性值
        message SampleAttrLike { // 任意 message，名字不限
            AttrType type = 1; // 必须有 type 字段，是一个枚举类型，约束见 AttrType 的注释

            // bytes/string 类型的 name，int32 类型的 id，二者至少定义一个，用于匹配。
            bytes name = 2;
            int32 id = 3;

            // 下面的是所有支持的类型，后续可以进一步添加更多支持，需要对应 AttrType 里对应的枚举类型存在。
            bool bool_value = 5;  // bool 会以 0/1 int 值存入 context
            int64 int_value = 6;
            float float_value = 7;
            bytes string_value = 8;
            repeated int64 int_list_value = 9;
            repeated float float_list_value = 10;
        }
    ```

    当前系统里满足 SampleAttr-like 的 message 类型包括并不限于：

    * kuiba::SampleAttr
    * kuaishou::log::LabelAttr
    * mix.kuaishou.ad::LabelAttr

    参数配置
    ------
    `from_extra_var`: [string] 从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 Message，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值

    `attrs`: [string_list] 动态参数，需要抽取的属性列表，每项值为字符串，用增专用，pb结构为ks/algo-engine-proto/common_sample_log.proto 的 PredictItem

    `is_common_attr`: [bool] 选配项，是否为 common attr，为 True 时从 common attr 读取 Message，结果存到 common attr，否则从 item attr 读取 Message，结果存到 item attr，默认为 True。

    `pb_msg_max_depth`: [int] 选配项，限制检索深度，当且仅当 attrs 为空时生效，默认不限制，线上服务慎用！

    `serialize_to_attr`: [string] 选配项，若配置则会将 `from_extra_var` 中的 pb message 序列化成 string 存入指定的 common/item attr 中。

    `save_attr_names_to_attr`: [string] 选配项，仅当 save_all_fields=True 时生效，存储所有 attr name 到指定 common attr，默认不存。

    `output_attr_prefix`: [string] 选配项，仅当 save_all_fields=True 时生效，用于给保存的 attr 名增加一个前缀，默认无前缀。

    调用示例
    ------
    ``` python
    .enrich_with_protobuf(
      from_extra_var = "user_info",
      attrs = "{{xxx}}"
    )
    ```
    """
    self._add_processor(UgFeatureProtobufAttrEnricher(kwargs))
    return self
  
  def get_ug_feature_output_feature(self, **kwargs):
    """
    UgFeatureOutputFeatureEnricher
    ------
    获取需要返回的特征列表，按业务(PUSH_LEAF/PUSH_JAVA)+存储引擎(clotho/feasury/redis)区分，以及返回clotho需要读取的列

    参数配置
    ------

    `output_attr`: [string_list] 输出的 attr name，暂时用不到，以防万一

    调用示例
    ------
    ``` python
    .get_ug_feature_output_feature(
    
      output_attr=["PUSH_LEAF_clotho", "PUSH_LEAF_feasury"]
      
    )
    ```
    """
    self._add_processor(UgFeatureOutputFeatureEnricher(kwargs))
    return self

  def get_ug_feature_read_quota(self, **kwargs):
      """
      UgFeatureReadQuotaLimitEnricher
      ------
      判断上游服务是否达到限速条件

      参数配置
      ------
      `output_attr`: [string] 用来存放输出结果的 common attr 名称，false 表示被限速

      调用示例
      ------
      ``` python
      .get_ug_feature_read_quota(

        output_attr="has_quota"
        
      )
      ```
      """
      self._add_processor(UgFeatureReadQuotaLimitEnricher(kwargs))
      return self

  def ug_feature_perf_attr(self, **kwargs):
        """
        UgFeatureAttrPerfObserver
        ------
        打印kconf中attr, number打印数值，其他打印size

        参数配置
        ------
        `kconf_path`: [string] kconf路径，kconf中填写需要监控的attr

        调用示例
        ------
        ``` python
        .ug_feature_perf_attr(

          kconf_path="userGrowth.matrix.importantFeatureInfo"
          
        )
        ```
        """
        self._add_processor(UgFeatureAttrPerfObserver(kwargs))
        return self
