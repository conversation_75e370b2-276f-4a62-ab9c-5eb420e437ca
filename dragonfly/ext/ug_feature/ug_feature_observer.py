#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver


class UgFeatureAttrPerfObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ug_feature_perf_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("kconf_path"))
    return attrs