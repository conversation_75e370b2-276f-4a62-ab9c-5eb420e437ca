#!/usr/bin/env python3
"""
filename: offline_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for offline
author: <EMAIL>
date: 2022-03-15 20:34:00
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafObserver

class OfflineLagClockObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lag_set_clock"

class OfflineLagWatcherObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lag_watch_clock"

class TrafficKafkaFetchObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "emb_fetch_from_kafka"

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True

class UpdateLocalSamplePoolObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_local_sample_pool"

  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update([self._config[x] for x in ["id_from_common",
               "value_from_common"] if x in self._config])
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.update([self._config[x]
               for x in ["id_from_item", "value_from_item"] if x in self._config])
    return ret

class UpdateLocalSamplePoolByRedisObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_local_sample_pool_by_redis"

  @property 
  @strict_types
  def input_common_attrs(self) -> set:
    return set()
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

class OfflineHdfsObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_to_hdfs"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common", True):
      attrs.add(self._config.get("source_attr", ""))
    attrs.update(self.extract_dynamic_params(self._config.get("hdfs_path_prefix")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common", True):
      attrs.add(self._config.get("source_attr", ""))
    return attrs
