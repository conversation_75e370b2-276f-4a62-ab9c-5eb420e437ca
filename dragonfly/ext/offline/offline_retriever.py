#!/usr/bin/env python3
"""
filename: kuiba_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for offline
author: <EMAIL>
date: 2020-03-11 20:34:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class OfflineSessionBasedRLRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_session_based_rl"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineFulldataSessionBasedRLRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fulldata_retrieve_from_session_based_rl"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineFulldataSessionBasedRLWatchTiredRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fulldata_retrieve_from_session_based_rl_watch_tired"
  

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineKsRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_ks_reco_log"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "save_reco_photo_to" in self._config:
      return set([self._config["save_reco_photo_to"]])
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])


class OfflineFastKsRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_ks_reco_log_fastly"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

class OfflineOverseaRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_oversea_reco_log"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_user_attrs", [])))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # to saved photo attrs
    attrs1 = set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_photo_attrs", [])))
    # to saved context attrs
    attrs2 = set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_context_attrs", [])))
    attrs = attrs1.union(attrs2)

    if "save_reco_photo_to" in self._config:
      attrs.add(self._config["save_reco_photo_to"])

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

class OfflineKsibRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_ksib_reco_log"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # to saved photo attrs
    attrs1 = set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_photo_attrs", [])))
    # to saved context attrs
    attrs2 = set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_context_attrs", [])))
    attrs = attrs1.union(attrs2)

    if "save_reco_photo_to" in self._config:
      attrs.add(self._config["save_reco_photo_to"])

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(map(lambda x: x if isinstance(x, str) else x.get("to"), self._config.get("save_user_attrs", [])))

class OfflineFastOverseaRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_oversea_reco_log_fastly"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

class OfflineBTShmKVRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_bt_shm_kv"

class OfflineSimpleOverseaRecoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "simple_retrieve_from_oversea_reco_log"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("save_oversea_photo_info_str_to", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("save_oversea_reader_info_str_to", []))

class OfflineOverseaSequenceExampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_oversea_sequence_example"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("save_sequence_example_str_to", []))

class OfflineTensorflowSequenceExampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_tf_sequence_example"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

class OfflineBTQPhotoInfoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_photo_info_from_btq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "delete_status_attr" in self._config:
      ret.add(self._config["delete_status_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["save_item_info_to_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OfflineBTQModelUpdateRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_photo_from_model_update_btq"

class OfflineBTQIdRetriever(OfflineBTQModelUpdateRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_item_from_id_btq"

class OfflineLitePhotoMapRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_photo_from_lite_photo_map"

class OfflineBTQKuibaAttrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_kuiba_attr_from_btq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "delete_status_attr" in self._config:
      ret.add(self._config["delete_status_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(attr for attr in self._config["item_features"])

class OfflineBTQCommonIndexDocRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_common_doc_from_btq"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["save_item_info_to_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["item_info_bytes_name"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OfflineOverseaBTQModelUpdateRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_photo_from_oversea_model_btq"


class RandomNegativeSamplingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_random_negative_sampling"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["filter_pids_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    ret.update(self.extract_dynamic_params(self._config.get("sampling_rate")))
    pick_items = self._config.get("pick_item")
    if isinstance(pick_items, dict):
      for value in pick_items.values():
        if isinstance(value, list):
          for list_value in value:
            ret.update(self.extract_dynamic_params(list_value))
        else:
          ret.update(self.extract_dynamic_params(value))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_pids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["extra_int_attrs", "extra_float_attrs", "extra_string_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    for key in self._config.get("pick_item", {}).keys():
      ret.add(key)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["is_negative_sampling_attr", "photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class RandomNegativeSamplingRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_random_negative_sampling_v2"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["filter_pids_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    ret.update(self.extract_dynamic_params(self._config.get("sampling_rate")))
    pick_items = self._config.get("pick_item")
    if isinstance(pick_items, dict):
      for value in pick_items.values():
        if isinstance(value, list):
          for list_value in value:
            ret.update(self.extract_dynamic_params(list_value))
        else:
          ret.update(self.extract_dynamic_params(value))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_pids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for key in ["extra_int_attrs", "extra_float_attrs", "extra_string_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    for key in self._config.get("pick_item", {}).keys():
      ret.add(key)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["is_negative_sampling_attr", "photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class RandomTableSamplingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_random_table_sampling"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_author_id_to_attr"))
    return ret

class OfflineItemCombineRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_combine_item"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    item_map = self._config["combine_item_map"]
    ret.add(item_map.get("map_value_attr"))
    ret.add(item_map.get("map_size_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in self._config["combine_attr"]:
      ret.add(key["attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in self._config["combine_attr"]:
      ret.add(key["output_attr"])
      ret.add(key.get("output_split_attr"))
    ret.add(self._config.get("save_combine_flag_to"))
    return ret


class OfflineLiveColossusCkptSeqModelSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_colossus_ckpt_seq_model_sample"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("max_timestamp_ms")
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output_prefix = self._config["output_prefix"]
    ret = set(["sample_type", "user_id", "aId", "gift_amt", "gift_count", "play_time", "auto_play_time",
               "comment_count", "like_count", "follow_count"])
    list_attrs = ["author_id", "record_count", "play_time_sum", "reward_sum",
                  "reward_count", "comment_count", "like_count", "follow_count", "avg_acu_count"]
    ret.update([output_prefix + x for x in list_attrs])
    percentiles = self._config.get("percentiles", [25, 50, 75])
    ret.update(["u_gift_{}".format(x) for x in percentiles])
    ret.update(["u_play_time_{}".format(x) for x in percentiles])
    return ret

class OfflineLocalSamplePoolRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_local_sample_pool"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_pool_size_attr" in self._config:
      ret.add(self._config["output_pool_size_attr"])
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.update([self._config[x]
               for x in ["output_prob_attr"] if x in self._config])
    return ret

class OfflineRandomRetrieveFromCommonAttrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "random_retrieve_from_common_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    ret.update(self._config[x] for x in ["prob_attr"] if x in self._config)
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "output_index_attr" in self._config:
      ret.add(self._config["output_index_attr"])
    return ret

class OfflineRerankRecoRLRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rerank_reco_rl"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])
  
class OfflineRerankRecoPRMRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rerank_reco_prm"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineRankRecoRLRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rank_reco_rl"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineContextRankRLRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rank_reco_rl_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineRankRecoRLLongRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rank_reco_rl_long"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineRerankPluginInfoLogRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rerank_plugin_info_log"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in ["save_plugin_reco_photo_to", "save_mix_redis_photo_to"]:
      attrs.add(self._config.get(attr))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["from_extra_var"])
    return attrs
  
class OfflineRankRecoPageRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rank_reco_page"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["sample_from_attr"]])

class OfflineBTQMarmEmbRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_photo_with_emb_from_model_update_btq"

