#!/usr/bin/env python3
"""
filename: kuiba_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for offline
author: <EMAIL>
date: 2020-03-11 20:34:00
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher

class OfflineFeatureConvertEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "convert_to_tf_sequence_example"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["common_attrs", "common_slots_from", "common_signs_from"]:
      ret.update(self._config.get(field, []))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for field in ["item_attrs", "item_slots_from", "item_signs_from"]:
      ret.update(self._config.get(field, []))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["save_result_to"]])

class OfflineCsvToTfSequenceExampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "convert_csv_to_tf_sequence_example"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    from_extra_var = self._config.get("from_extra_var", "")
    check_arg(from_extra_var, "from_extra_var can not be empty")
    ret.add(from_extra_var)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    save_result_to = self._config.get("save_result_to", "")
    check_arg(save_result_to, "save_result_to can not be empty")
    ret.add(save_result_to)
    return ret

class OfflineMessageFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_message"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    for key in ["save_begin_time_ms_to_common_attr", "save_end_time_ms_to_common_attr"]:
      if key in self._config and self._config[key]:
        ret.add(self._config[key])
    return ret

class FetchSynchronizeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_synchronize"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    time_ms_attr = self._config.get("time_ms_attr", "")
    if time_ms_attr:
      ret.add(time_ms_attr)
    return ret

class OfflineRandomContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "random_init_context"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config.get("tf_kuiba_update_model_key", "PULL_MODEL_VARIABLE")])
    return ret

class OfflineResetTimeStampEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reset_timestamp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["timestamp"])
    return ret

class OfflineResetUserMetaInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reset_user_meta_info"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set([self._config.get("timestamp_attr", ""), self._config.get("user_id_attr", ""), self._config.get("divice_id_attr", "")])
    return ret

class OfflineModelUpdateMessageReshardEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reshard_model_update_message"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    input_attr = self._config.get("input_attr", "")
    if input_attr:
      ret.add(input_attr)
    return ret

class OfflineKsibRecoLogConvertEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_reco_log_convert"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["reader_info_attr", "new_user_threshold_attr", "convert_cluster_id"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for field in ["photo_info_attr", "context_info_attr"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for k in ["reader_info_attr"]:
      if k in self._config:
        ret.add(self._config[k])
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for k in ["photo_info_attr", "context_info_attr"]:
      if k in self._config:
        ret.add(self._config[k])
    return ret

class OfflineStatisticDenseFeature(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_with_statistic_dense_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["reader_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for config in self._config.get("match_common_attrs", []):
      ret.add(config)
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr", "context_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    for config in self._config.get("match_item_attrs", []):
      ret.add(config)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["item_statistic_dense_feature_output"]:
      if key in self._config:
        ret.update(self._config.get(key, []))
    return ret
class OfflineCommonCrossFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_cross_dense_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("cross_feature_config", []):
      id = config.get("action_id_list_attr", None)
      ts = config.get("action_time_list_attr", None)
      if id:
        ret.add(id)
      if ts:
        ret.add(ts)
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("cross_feature_config", []):
      id = config.get("item_id_attr", None)
      if id:
        ret.add(id)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("cross_feature_config", []):
      prefix = config.get("feature_save_attr", None)
      if id:
        for win in ['index_window_config', 'time_window_config']:
          for win_config in self._config.get(win, []):
            suffix = win_config.get("suffix", None)
            if suffix:
              ret.add(prefix + suffix)
    return ret

class OverseaFetchRedisPhotoProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_redis_photo_profile"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("export_item_attr", []))

class OfflineKsibDenseMatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_dense_match"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["keys"])
        ret.add(config["scores"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("to_match_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("match_configs", []):
        ret.add(config["output"])
    return ret

class OfflineClientFeatureDispatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dispatch_client_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["input_json_attr"]}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {attr["item_attr"] for attr in self._config["attrs"]}

class OfflineKsibReshapeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ksib_reshape_attrs"

  @strict_types
  def _is_common_attr(self) -> bool:
    return self._config.get("is_common_attr", True)

  @strict_types
  def _get_input_attrs(self) -> set:
    ret = set()
    for config in self._config.get("reshape_attrs", []):
      ret.add(config["input"])
    return ret

  @strict_types
  def _get_output_attrs(self) -> set:
    ret = set()
    for config in self._config.get("reshape_attrs", []):
      ret.add(config["output"])
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return not self._is_common_attr()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._is_common_attr():
      ret = self._get_input_attrs()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._is_common_attr():
      ret = self._get_output_attrs()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self._is_common_attr():
      ret = self._get_input_attrs()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._is_common_attr():
      ret = self._get_output_attrs()
    return ret


class OfflineColossusCkptReaderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_ckpt_reader"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_attr"])
    ret.update([self._config[x] for x in ["user_id_attr"] if x in self._config])
    return ret

class GroupListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "group_list_attr"
  
  def get_configs(self, is_common):
    configs = self._config["configs"]
    return [config for config in configs if config.get("is_common", True) == is_common]

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(False)
    for config in configs:
      ret.add(config["key_attr"])
      agg_configs = config["agg_configs"]
      ret.update([c["src_attr"] for c in agg_configs])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(False)
    for config in configs:
      agg_configs = config["agg_configs"]
      ret.update([c["dst_attr"] for c in agg_configs])
    return ret


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(True)
    for config in configs:
      ret.add(config["key_attr"])
      agg_configs = config["agg_configs"]
      ret.update([c["src_attr"] for c in agg_configs])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret =set()
    configs = self.get_configs(True)
    for config in configs:
      agg_configs = config["agg_configs"]
      ret.update([c["dst_attr"] for c in agg_configs])
    return ret

class TransformListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "transform_list_attr"

  def input_attrs(self, is_common):
    ret = set()
    configs = self._config["configs"]
    for config in configs:
      if config.get("is_sort_attr_common", True) == is_common:
        if "sort_attr" in config:
          ret.add(config["sort_attr"])
      if "filter_configs" in config:
        for c in config["filter_configs"]:
          if c.get("is_attr_comon", True) == is_common:
            ret.add(c["attr"])
          if c.get("is_bound_attr_common", True) == is_common:
            if "lower_bound_attr" in c:
              ret.add(c["lower_bound_attr"])
            if "upper_bound_attr" in c:
              ret.add(c["upper_bound_attr"])
          if c.get("is_not_equal_to_attr_common", True) == is_common:
            if "not_equal_to_attr" in c:
              ret.add(c["not_equal_to_attr"])
      if "attr_configs" in config:
        ret.update([c["src_attr"] for c in config["attr_configs"]
                 if c.get("is_src_attr_common", True) == is_common])
    return ret
  
  def output_attrs(self, is_common):
    ret = set()
    configs = [config for config in self._config["configs"]
               if config.get("is_dst_common", True) == is_common]
    for config in configs:
      if "attr_configs" in config:
        for c in config["attr_configs"]:
          ret.add(c["dst_attr"])
      if "output_sort_indices_attr" in config:
        ret.add(config["output_sort_indices_attr"])
      if "output_mask_flags_attr" in config:
        ret.add(config["output_mask_flags_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.input_attrs(False)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.output_attrs(False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.input_attrs(True)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.output_attrs(True)

class JointRecoLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "joint_reco_log"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["reco_log_attr"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for field in ["label_llsid_pid_str", "label_uid_pid_str", "label_did_pid_str", "label_uid_aid_str"]:
      if field in self._config:
        ret.add(self._config[field])
    attr_names = self._config.get("ad_label_attr_names", [])
    for name in attr_names:
      ret.add(name)
    return ret

class RecoLogJointFullLinkEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reco_log_joint_full_link"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for field in ["joint_reco_log_attr", "full_link_attr"]:
      if field in self._config:
        ret.add(self._config[field])
    return ret

class RecoLogPackedItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_reco_log_packed_item_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("reco_log_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    item_attr_list_attr = self._config.get("packed_item_attr_list_attr", "")
    if len(item_attr_list_attr) > 0:
      attrs.add(item_attr_list_attr)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("expected_output_item_attr_list"):
      attrs = {name for name in self._config["expected_output_item_attr_list"]}
    return attrs

class OfflineKafkaBatchConsumeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_consume_kafka"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set([self._config["output_attr"]])
    return ret
