#!/usr/bin/env python3
# coding=utf-8
"""
filename: offline_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, offline api mixin
author: <EMAIL>
date: 2020-03-11 20:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .offline_enricher import *
from .offline_observer import *
from .offline_retriever import *

class OfflineApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 offline 相关的 Processor 接口:
  - OfflineKsRecoLogRetriever
  - OfflineSimpleOverseaRecoLogRetriever
  - OfflineOverseaSequenceExampleRetriever
  - OfflineMessageFetchEnricher
  - OfflineBTShmKVRetriever
  - OfflineFeatureConvertEnricher
  - OfflineResetTimeStampEnricher
  """
  def simple_retrieve_from_oversea_reco_log(self, **kwargs):
    """
    OfflineSimpleOverseaRecoLogRetriever
    ------
    从 context 的 kuaishou::oversea::reco::FullRecoLog PB 中触发
    可选地将 ReaderInfo 和 PhotoInfo 序列化为字符串写入 common attr / item attr
    此方法可以用于调试，验证线上线下的预估一致性，用法如下：
    1. 先离线预估出一批 FullRecoLog 样本的分数
    2. 使用 fetch_message + parse_protobuf_from_string 导入第一步中的样本，然后使用本函数构造线上请求，发送给线上 infer server
    3. 对比上述两步的预估结果

    注意事项：
    - 这里的 uid、did 和 pid 是原始的 id，跟训练/预估时的不一样

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 kuaishou::oversea::reco::FullRecoLog

    `save_oversea_reader_info_str_to`: [string] 存储序列化后 ReaderInfo 的 common attr name，默认不存

    `save_oversea_photo_info_str_to`: [string] 存储序列化后 PhotoInfo 的 item attr name，默认不存

    `save_uid_to`: [string] 存储 uid 的 common attr name，默认不存

    `save_did_to`: [string] 存储 pid 的 common attr name，默认不存

    调用示例
    ------
    ``` python
    .simple_retrieve_from_oversea_reco_log(
      from_extra_var="ks_reco_log",
      save_oversea_reader_info_str_to="oversea_reader_info_str",
      save_oversea_photo_info_str_to="oversea_photo_info_str",
      save_uid_to="uid",
      save_did_to="did")
    ```
    """
    self._add_processor(OfflineSimpleOverseaRecoLogRetriever(kwargs))
    return self

  def retrieve_from_oversea_sequence_example(self, **kwargs):
    """
    OfflineSimpleOverseaRecoLogRetriever
    ------
    从 context 的 ks::reco::feature_pipe::SequenceExample PB 中触发
    可选地将 SequenceExample 序列化为字符串写入 common attr
    此方法可以用于调试，验证线上线下的预估一致性，用法如下：
    1. 先离线预估出一批 SequenceExample 样本的分数
    2. 使用 fetch_message + parse_protobuf_from_string 导入第一步中的样本，然后使用本函数构造线上请求，发送给线上 infer server
    3. 对比上述两步的预估结果

    注意事项：
    - 这里的 uid、did 和 pid 通常是经过特征抽取后的 id，不是原始 id

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 kuaishou::oversea::reco::FullRecoLog

    `uid_key`: [string] 存储 user id 的 feature key，默认为 "38"
    
    `did_key`: [string] 存储 device id 的 feature key，默认为 "34"

    `pid_key`: [string] 存储 photo id 的 feature key，默认为 "123"

    `save_sequence_example_str_to`: [string] 存储序列化后 SequenceExample 的 common attr name，默认不存

    `save_uid_to`: [string] 存储 uid 的 common attr name，默认不存

    `save_did_to`: [string] 存储 pid 的 common attr name，默认不存

    调用示例
    ------
    ``` python
    .retrieve_from_oversea_sequence_example(
      from_extra_var="tf_sequence_example",
      save_sequence_example_str_to="tf_sequence_example_str",
      save_uid_to="uid",
      save_did_to="did")
    ```
    """
    self._add_processor(OfflineOverseaSequenceExampleRetriever(kwargs))
    return self

  def retrieve_from_ks_reco_log(self, **kwargs):
    """
    OfflineKsRecoLogRetriever
    ------
    从 ks::reco::RecoLog 触发

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::RecoLog。

    `reason`: [int] 触发的 reason，默认为 0。

    `save_reco_photo_to`: [string] 存储 RecoPhotoInfo 的 item attr，默认不存。

    调用示例
    ------
    ``` python
    .retrieve_from_ks_reco_log(from_extra_var="ks_reco_log")
    ```
    """
    self._add_processor(OfflineKsRecoLogRetriever(kwargs))
    return self

  def retrieve_from_ks_reco_log_fastly(self, **kwargs):
    """
    OfflineFastKsRecoLogRetriever
    ------
    OfflineKsRecoLogRetriever 的高性能不灵活版本，作为一种短期过度方案存在，后续可能会删除。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::RecoLog。

    `reason`: [int] 触发的 reason，默认为 0。

    `common_attrs`: [list] 取出的 common attr 列表。

    `item_attrs`: [list] 取出的 item attr 列表。

    `save_user_sign_to`: [string] 存储 user 侧 sign 的 common attr name，默认不存。

    `save_user_slot_to`: [string] 存储 user 侧 slot 的 common attr name，默认不存。

    `save_item_sign_to`: [string] 存储 item 侧 sign 的 item attr name，默认不存。

    `save_item_slot_to`: [string] 存储 item 侧 slot 的 item attr name，默认不存。

    调用示例
    ------
    ``` python
    .retrieve_from_ks_reco_log(
      from_extra_var="ks_reco_log",
      common_attrs=["lineid", "user_hash", "time_ms", "user_type", "gender", "client_id", "tab_id"],
      item_attrs=[
        "pid", "show", "realshow", "clk", "like", "follow", "forward", "comment", "hate", "profile",
        "ineffective_view", "effective_view", "duration", "emp_watch_time", "long_view", "cover_show_time",
        "slide_enter", "click_non_short_view", "watch_live", "live_follow", "living", "has_origin_music",
        "join_topic", "click_origin_music", "click_magic_face", "has_magic_face", "click_detail_tag",
        "has_same_frame", "show_tube_entrance", "click_tube_entrance", "click_kuaishan_tag", "kuaishan_photo", "effective_view2", "ineffective_view2", "long_view2", "duplicate_click", "comment_effective_stay", "down_load", "photo_type", "watch_time", "scale_watch_time", "pctr", "pltr", "pwtr", "pftr", "pvtr", "plvtr", "emp_ctr", "emp_ltr", "emp_wtr", "emp_ftr", "emp_htr", "emp_lvtr", "emp_svtr", "emp_evtr", "emp_mctr", "score", "debug_ctr", "debug_ltr", "debug_wtr", "debug_ftr"],
      save_item_slot_to="item_slot",
      save_item_sign_to="item_sign",
      save_user_slot_to="common_slot",
      save_user_sign_to="common_sign")
    ```
    """
    self._add_processor(OfflineFastKsRecoLogRetriever(kwargs))
    return self

  def retrieve_from_oversea_reco_log_fastly(self, **kwargs):
    """
    OfflineFastOverseaRecoLogRetriever
    ------
    OfflineRecoLogRetriever 的高性能不灵活版本，作为一种短期过度方案存在，后续可能会删除。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::RecoLog。

    `reason`: [int] 触发的 reason，默认为 0。

    `common_attrs`: [list] 取出的 common attr 列表。

    `item_attrs`: [list] 取出的 item attr 列表。

    `save_user_sign_to`: [string] 存储 user 侧 sign 的 common attr name，默认不存。

    `save_user_slot_to`: [string] 存储 user 侧 slot 的 common attr name，默认不存。

    `save_item_sign_to`: [string] 存储 item 侧 sign 的 item attr name，默认不存。

    `save_item_slot_to`: [string] 存储 item 侧 slot 的 item attr name，默认不存。

    调用示例
    ------
    ``` python
    .retrieve_from_oversea_reco_log(
      from_extra_var="ks_reco_log",
      common_attrs=["lineid", "user_hash", "time_ms", "user_type", "gender", "client_id", "tab_id"],
      item_attrs=[
        "pid", "show", "realshow", "clk", "like", "follow", "forward", "comment", "hate", "profile",
        "ineffective_view", "effective_view", "duration", "emp_watch_time", "long_view", "cover_show_time",
        "slide_enter", "click_non_short_view", "watch_live", "live_follow", "living", "has_origin_music",
        "join_topic", "click_origin_music", "click_magic_face", "has_magic_face", "click_detail_tag",
        "has_same_frame", "show_tube_entrance", "click_tube_entrance", "click_kuaishan_tag", "kuaishan_photo", "effective_view2", "ineffective_view2", "long_view2", "duplicate_click", "comment_effective_stay", "down_load", "photo_type", "watch_time", "scale_watch_time", "pctr", "pltr", "pwtr", "pftr", "pvtr", "plvtr", "emp_ctr", "emp_ltr", "emp_wtr", "emp_ftr", "emp_htr", "emp_lvtr", "emp_svtr", "emp_evtr", "emp_mctr", "score", "debug_ctr", "debug_ltr", "debug_wtr", "debug_ftr"],
      save_item_slot_to="item_slot",
      save_item_sign_to="item_sign",
      save_user_slot_to="common_slot",
      save_user_sign_to="common_sign")
    ```
    """
    self._add_processor(OfflineFastOverseaRecoLogRetriever(kwargs))
    return self

  def retrieve_from_oversea_reco_log(self, **kwargs):
    """
    OfflineOverseaRecoLogRetriever
    ------
    从::kuaishou::oversea::reco::FullRecoLog中触发

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 kuaishou::oversea::reco::FullRecoLog。

    `reason`: [int] 触发的 reason，默认为 0。

    `save_reco_photo_to`: [string] 存储 RecoPhotoInfo 的 item attr，默认不存。

    `save_user_attrs`: [list] 从 FullRecoLog 的 ReaderInfo 中抽取 common_attrs 的部分字段到 common attr

    `save_photo_attrs`: [list] 从 FullRecoLog 的 PhotoInfo 中抽取 item_attrs 的部分字段到 item attr

    `save_context_attrs`: [list] 从 FullRecoLog 的 ContextInfo 中抽取 context_attrs 的部分字段到 item attr

    调用示例
    ------
    ``` python
    .retrieve_from_oversea_reco_log(from_extra_var="oversea_reco_log",
      save_photo_atts=[
          dict(from="p_photo_in_whitelist", to="photo_in_whitelist"),
          "p_op_tag_id"],
      save_context_attrs=["exit_slide"])
    ```
    """
    self._add_processor(OfflineOverseaRecoLogRetriever(kwargs))
    return self

  def retrieve_from_ksib_reco_log(self, **kwargs):
    """
    OfflineKsibRecoLogRetriever
    ------
    从::ksib::reco::FullRecoLog中触发

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ksib::reco::FullRecoLog。

    `reason`: [int] 触发的 reason，默认为 0。

    `save_reco_photo_to`: [string] 存储 RecoPhotoInfo 的 item attr，默认不存。

    `save_user_attrs`: [list] 从 FullRecoLog 的 ReaderInfo 中抽取 common_attrs 的部分字段到 common attr

    `save_photo_attrs`: [list] 从 FullRecoLog 的 PhotoInfo 中抽取 item_attrs 的部分字段到 item attr

    `save_context_attrs`: [list] 从 FullRecoLog 的 ContextInfo 中抽取 context_attrs 的部分字段到 item attr

    `save_all_proto_attr`: [bool] 是否从 FullRecoLog 的 ReaderInfo PhotoInfo ContextInfo 抽取所有 attr 分别到 common attr 和 item attr, 默认为 True

    调用示例
    ------
    ``` python
    .retrieve_from_ksib_reco_log(from_extra_var="ksib_reco_log",
      save_photo_atts=[
          dict(from="p_photo_in_whitelist", to="photo_in_whitelist"),
          "p_op_tag_id"],
      save_context_attrs=["exit_slide"])
    ```
    """
    self._add_processor(OfflineKsibRecoLogRetriever(kwargs))
    return self

  def ksib_reco_log_convert(self, **kwargs):
    """
    OfflineKsibRecoLogConvertEnricher
    ------
    海外 RecoLog 转换，方便特征抽取（当前仅支持 Snack 向 Kwai 兼容）
    包括
    1. page_type: snack 的 page_type 为 EXPLORE, 兼容kwai需要改为 SELECT
    2. tag 位置不一样，填充 kwai 中对应位置的值
    3. tower_pxtr 填充位置不一样
    4. is_new_user_v2 未填充

    参数配置
    ------
    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `new_user_threshold_attr`: [string] 从给定 common attr 获取 new_user_thr，默认留空。

    `browse_set_count_attr`: [string] 从给定的 common attr 获取 browse_set_count，默认留空。

    `convert_cluster_id`: [bool] 是否将 snack cluster_it 写到 Kwai 位置，默认 true。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    调用示例
    ------
    ``` python
    .ksib_reco_log_convert(
      reader_info_attr="reader_info",
      photo_info_attr="photo_info",
      context_info_attr="context_info",
      new_user_threshold_attr="new_user_threshold"
    )
    ```
    """
    self._add_processor(OfflineKsibRecoLogConvertEnricher(kwargs))
    return self

  def extract_with_statistic_dense_feature(self, **kwargs):
    """
    OfflineStatisticDenseFeature
    ------
    国际化基于统计的 dense 特征抽取

    参数配置
    ------

    `item_statistic_dense_feature_output`: [list] Item 侧统计 dense 特征输出到给定 item attr。

    `reader_info_attr`: [string] 从给定 common attr 获取 ReaderInfo，默认留空。

    `photo_info_attr`: [string] 从给定 item attr 获取 PhotoInfo，默认留空。

    `context_info_attr`: [string] 从给定 item attr 获取 ContextInfo，默认留空。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，线上一定不要开启，默认 False。

    `match_common_attrs`: [list] 统计匹配时的用户侧 attr。

    `match_item_attrs`: [list] 统计匹配是用到的photo侧 attr。

    `max_time_gap`: [int] 统计最近时间内的匹配计数的最大时间窗口，单位为秒，默认为 7776000 (90天)。

    `max_last_size`: [int] 统计最近N个数据的匹配计数的最大数据个数，单位为个，默认为 200。

    `match_fields`: [list] 需要匹配的字段, 支持以下类型:
      - authorid
      - mmuclusterid
      - mmuclusterv2id
      - mmuprimarytagid
      - trinitym32
      - trinitym128
      - trinitym1000
      - trinitym4000
      - trinityprimarytag
      - trinitysecondtag

    调用示例
    ------
    ``` python
    .extract_with_statistic_dense_feature(
        item_statistic_dense_feature_output=[
          "ds_upm_click_authorid_1d",
          "ds_upm_click_authorid_1h",
        ],
        reader_info_attr='reader_info',
        photo_info_attr='photo_info')
    ```
    """
    self._add_processor(OfflineStatisticDenseFeature(kwargs))
    return self

  def extract_cross_dense_feature(self, **kwargs):
    """
    OfflineCommonCrossFeatureEnricher
    ------
    通用的统计交叉特征抽取

    参数配置
    ------

    `cross_feature_config`: [list] 统计特征抽取配置。
    - `action_id_list_attr`: [list] 存储用户侧行为 id 列表的 common attr。
    - `action_time_list_attr`: [list] 存储用户侧行为时间戳列表的 common attr。
    - `item_id_attr`: [string] 存储 item 侧交叉匹配的 id 的 item attr。
    - `feature_save_attr`: [string] 存储输出 dense 特征的 item attr。

    `time_window_config`: [list] 时间窗口配置。
    - `time_ms`: [int] 时间，单位为毫秒。
    - `suffix`: [string] 特征存储 item attr 的后缀

    `index_window_config`: [list] 索引窗口配置。
    - `index`: [int] 索引。
    - `suffix`: [string] 特征存储 item attr 的后缀

    `match_function_name`: [string] 交叉匹配时的函数名。

    调用示例
    ------
    ``` python
    .extract_cross_dense_feature(
        cross_feature_config=[
          dict(
            action_id_list_attr="like_author_id_value_list",
            action_time_list_attr="like_author_id_timestamp_list",
            item_id_attr="author_id",
            feature_save_attr="ds_like_author_id"
          )
        ],
        time_window_config=[
          dict(time_ms=1*3600*1000, suffix="_1h");
          dict(time_ms=24*3600*1000, suffix="_1d");
        ],
        time_window_config=[
          dict(index=1, suffix="_last1");
          dict(index=100, suffix="_last100");
        ])
    ```
    """
    self._add_processor(OfflineCommonCrossFeatureEnricher(kwargs))
    return self

  def convert_to_tf_sequence_example(self, **kwargs):

    """
    OfflineFeatureConvertEnricher
    ------
    mio 的样本转换成 tf_sequence_example (ks::reco::feature_pipe::SequenceExample)

    参数配置
    ------
    `common_attrs`: [list] 取出的 common attr 列表。

    `item_attrs`: [list] 取出的 item attr 列表。

    `common_slots_from`: [list] 从 context 的哪个属性读出common_slots 列表

    `common_signs_from`: [list] 从 context 的哪个属性读出common_signs 列表

    `item_slots_from`: [list] 从 context 的哪个属性读出item_slots 列表

    `item_signs_from`: [list] 从 context 的哪个属性读出item_slots 列表

    `save_result_to`: [string] 是否存 exmaple 到 context， 默认为空，表示不存

    调用示例
    ------
    ``` python
    .convert_to_tf_sequence_example(
      common_attrs=["lineid", "user_hash", "time_ms", "user_type", "gender", "client_id", "tab_id"],
      item_attrs=[
        "pid", "show", "realshow", "clk", "like", "follow", "forward", "comment", "hate", "profile",
        "ineffective_view", "effective_view", "duration", "emp_watch_time", "long_view", "cover_show_time",
        "slide_enter", "click_non_short_view", "watch_live", "live_follow", "living", "has_origin_music",
        "join_topic", "click_origin_music", "click_magic_face", "has_magic_face", "click_detail_tag",
        "has_same_frame", "show_tube_entrance", "click_tube_entrance", "click_kuaishan_tag", "kuaishan_photo", "effective_view2", "ineffective_view2", "long_view2", "duplicate_click", "comment_effective_stay", "down_load", "photo_type", "watch_time", "scale_watch_time", "pctr", "pltr", "pwtr", "pftr", "pvtr", "plvtr", "emp_ctr", "emp_ltr", "emp_wtr", "emp_ftr", "emp_htr", "emp_lvtr", "emp_svtr", "emp_evtr", "emp_mctr", "score", "debug_ctr", "debug_ltr", "debug_wtr", "debug_ftr"],
      common_slots_from=["common_slot"],
      common_signs_from=["common_sign"],
      item_slots_from=["item_slot]",
      item_signs_from=["item_sign"],
      save_result_to="tf_sequence_example")
    ```
    """
    self._add_processor(OfflineFeatureConvertEnricher(kwargs))
    return self
  
  def convert_csv_to_tf_sequence_example(self, **kwargs):

    """
    OfflineCsvToTfSequenceExampleEnricher
    ------
    csv 格式的 string 转换成 tf_sequence_example (ks::reco::feature_pipe::SequenceExample)

    `common_attrs` 和 `item_attrs` 需要指定每个列的 schema:
      - column_index: 列索引
      - column_name: 转成 tf_sequence_example 中的特征名
      - type: 列数据类型，仅支持 int/float/string/int_list/float_list/string_list

    注意：为了最终 SequenceExample 的 item 侧特征能够对齐，csv 中所有 item 侧特征的 item 个数需要相等，
      如果某个 item 存在某个特征的缺失，需要填充默认值。

    参数配置
    ------
    `from_extra_var`: [string] 必配项，从给定的 common attr 获取 csv

    `common_attrs`: [list] 选配项，取出的 common attr 列表，作为 SequenceExample.context 侧的特征

    `item_attrs`: [list] 选配项，取出的 item attr 列表，作为 SequenceExample.feature_lists 侧的特征

    `column_separator`: [string] 必配项，分隔符，用于分隔列

    `item_separator`: [string] 必配项，分隔符，对于 item_attr 的列，用于分隔不同的 item

    `list_separator`: [string] 必配项，分隔符，对于 list 类型的特征，用于分隔 list 内的 value

    `line_separator`: [string] 选配项，行分隔符，默认为 "\n" ，如果不存在行分隔符，可以设置为空字符串 ""

    `save_result_to`: [string] 必配项，将 exmaple 保存到 context

    调用示例
    ------
    ``` python
    # common attr 的 csv_str 内容为 "2174161363|28|0.33234468|1,2|100 101,200 201|watch like,click"
    # 其中最后 3 列为 item 侧特征，item 个数为 2
    .convert_csv_to_tf_sequence_example(
      from_extra_var="csv_str",
      common_attrs=[
        {"column_index": 0, "column_name": "user_id", "type": "int"},
        {"column_index": 1, "column_name": "tab_id", "type": "int"},
        {"column_index": 2, "column_name": "click", "type": "float"},
      ],
      item_attrs=[
        {"column_index": 3, "column_name": "photo_id", "type": "int"},
        {"column_index": 4, "column_name": "tags", "type": "int_list"},
        {"column_index": 5, "column_name": "labels", "type": "string_list"},
      ],
      column_separator="|",
      item_separator=",",
      list_separator=" ",
      save_result_to="tf_sequence_example"
    )
    ```
    """
    self._add_processor(OfflineCsvToTfSequenceExampleEnricher(kwargs))
    return self

  def retrieve_from_bt_shm_kv(self, **kwargs):
    """
    OfflineBTShmKVRetriever
    ------
    从 kuiba predict index 的 BTShmKV 服务获取 item key 用于触发

    参数配置
    ------
    `kess_service`: [string] bt shm kv 注册的 kess name。

    `auto_num_shards`: [bool] 是否在每次请求从 kess 拿 num_shards，会与 shard_name 前缀匹配，会覆盖 num_shards 的配置，默认为 false。

    `shard_name`: [string] bt shm kv 注册的 shard name （如果不分 shard）或 shard name 的前缀（如果分 shard）。
    
    `num_shards`: [int] 如果 bt shm kv 分 shard 的话，shard 的数量，默认不分 shard。
        
    `client_kconf`: [string] 统一存储版本 colossusdb 服务的遍历，请求的客户端配置，配置该项后不再需要配置 kess_service， shard_name 和 num_shards

    `num_parts`: [int] 分 part 读取 key，注意如果 part 太少会读取失败。

    `thread_num`: [int] rpc 线程数，默认为 shard 数量。

    `reason`: [int] 触发的 reason，默认为 0。

    `use_raw_key`: [bool] 是否使用 BTShmKV 原始的 key 作为 item key，默认为 false。使用中台 CommonIndex 格式的分布式索引需设成 true。
    
    `min_round_second`: [int] 仅统一存储版本支持，限制遍历一轮的最少耗时，默认为 600 秒。
    
    调用示例
    ------
    ``` python
    #统一存储新服务
    .retr_from_bt_shm_kv(
      client_kconf="reco.colossusdb.clientConfig",
      num_parts=102400,
      min_round_second=1800)
    
    # 老服务
    .retrieve_from_bt_shm_kv(
      kess_service="grpc_nearbyBTShmKV",
      shard_name="grpc_nearbyPhotoInfoService",
      num_shards=4,
      num_parts=102400)
    ```
    """
    self._add_processor(OfflineBTShmKVRetriever(kwargs))
    return self

  def fetch_message(self, **kwargs):
    """
    OfflineMessageFetchEnricher
    ------
    从外部读取 string 存入 common attr

    参数配置
    ------
    `group_id`: [string] 用于区分不同的 group，对 hdfs 和 kafka 有效，相同 group 的 runner 会互斥地消费数据。

    `begin_time_ms`: [string or int] 开始时间，支持 UNIX 时间戳或字符串（格式见serving_base/utility/time_helper.h），支持 hdfs 和 kafka，默认不指定开始时间。

    `end_time_ms`: [string or int] 结束时间，支持 UNIX 时间戳或字符串（格式见serving_base/utility/time_helper.h），支持 hdfs，默认不指定开始时间。

    `kafka_topic`: [string] 逻辑 kafka topic，需要找 @dongliu 配置和物理 kafka topic 的关系。

    `kafka_consume_mode`: [string] 机房选择，可以是 currentAz otherAzOfCurrentRegion currentRegion AllOtherRegion AllRegion，默认是 AllRegion

    `kafka_timeout_ms`: [int] 单次Poll超时时间，默认为 10。

    `kafka_log_level`: [int] kafka 日志等级，默认为 5。

    `kafka_prefix_group_id_with_topic`: [bool] 在 kafka group_id 前加上 topic 作为前缀，解决 kafka 同一集群的不同 topic 的相同 group_id 会一起 rebalance 的问题。

    `kafka_consume_delay_ms`: [int] kafka 延迟消费时间，单位毫秒，默认值 0。

    `share_kafka_consumer`: [bool] 每个线程是否共享 kafka consumer，默认值为 True

    `btq_prefix`: [string] btq prefix 或 queue name。

    `btq_with_shard`: [bool] btq queue name 是否使用 prefix + 后缀的形式，默认为 False。

    `btq_shard_num`: [int] btq 分 shard 数量，默认为 0。

    `btq_batch_size`: [int] 从 btq 中一次性最多读的数据数量，默认为 1024。

    `hdfs_path`: [string] 需要访问的 HDFS 路径。

    `hdfs_user_name`: [string] 访问 HDFS 时的用户名，默认为 mpi。

    `hdfs_read_thread_num`: [int] 后台 HDFS 访问的线程数，默认为 12。

    `hdfs_format`: [string] HDFS 数据的格式，默认是 text。

    `hdfs_max_buffer_size`: [int] hdfs buffer byte 数，默认为 (1 << 30) * 8，即 8GB。

    `hdfs_loop_read`: [bool] 不断探测 HDFS 目录下的新文件，永不退出，默认为 false。

    `local_path`: [string] 本地 base64 文件的路径，注意：当这个配置设置时上述所有配置都会失效，强制读这个文件的内容，且多线程会读多次，当前只用于调试，慎用。

    `from_stdin`: [bool] 从 stdin 数据，注意：该配置优先级最高，多线程不保证可用。

    `from_stdin_base64_encode`: [bool] 仅 `from_stdin` 为 True 时生效，从 stdin 读取的数据是否为 base64 编码，默认为 True 。

    `save_begin_time_ms_to_common_attr`: [string] 将 begin_time_ms （考虑 gflag 之后的最终结果）存到指定的 common attr，默认不存。

    `save_end_time_ms_to_common_attr`: [string] 将 end_time_ms （考虑 gflag 之后的最终结果）存到指定的 common attr，默认不存。

    `output_attr`: [string] 用于存储的 common attr。

    `kafka_filter_tags`: [string] kafka需要过滤的tags，例如"tag1,tag2"，字符串以英文逗号分隔，字符串内部内部禁止有空格和逗号

    `kafka_partition_assign_strategy`: [string] 选配项，kafka 分区分配策略，可以解决 kafka partition 在线程中分配不均导致个别 partition lag 的问题，如需修改请先联系 kafka oncall

    `kafka_user_extra_params`: [string] 选配项，用于手动指定 kafka consumer 的参数，默认为空。格式为 KEY=VALUE,多个参数可以使用;分隔。注意可能会覆盖原有的 fetch.message.max.bytes 等参数，使用前请咨询 kafka oncall

    `message_type`: [string] 消息类型, 默认 "STRING", 仅支持如下几类: {"STRING", "PARQUET_RECORD_BATCH", "ARROW_RECORD_BATCH"}。当类型为 {"PARQUET_RECORD_BATCH", "ARROW_RECORD_BATCH"} 之一时，存到 `output_attr` 里的类型为 arrow::RecordBatch 指针

    `column_names`: [list] 从消息中解析的字段列表， 默认为空，读取所有字段。仅对如下 `message_type` 生效: {"PARQUET_RECORD_BATCH", "ARROW_RECORD_BATCH"}

    `record_batch_context_key`: [string] 将 record_batch 的哪一列作为行切分依据。仅对如下 `message_type` 生效: {"PARQUET_RECORD_BATCH", "ARROW_RECORD_BATCH"}。默认不设置，每行作为一个子 record_batch。如果设置，需要保证非空，连续相同的值会被合并为一个子 record_batch

    GFLAGS 配置
    ------
    该 processor 带一些 gflags 配置项，用于外部框架改变这个 processor 的行为，也可以用于定制一些行为，默认都不生效。
    使用前需要确认你知道自己在做什么。

    common_leaf_offline_fetcher_begin_time_ms：修改 begin_time_ms 的默认值。
    common_leaf_offline_fetcher_begin_time_str：修改 begin_time_ms 的默认值。
    common_leaf_offline_fetcher_end_time_ms：修改 end_time_ms 的默认值。
    common_leaf_offline_fetcher_end_time_str：修改 end_time_ms 的默认值。
    common_leaf_offline_fetcher_group_id_postfix：在 group_id 后加一个后缀，避免冲突。
    common_leaf_offline_fetcher_kafka_user_extra_params：类似算子参数中的kafka_user_extra_params，全局生效, 但优先级更低。

    MioLearner 中的用法
    ------

    如果是在 MioLearner 中使用这个 Processor，不要设置 begin_time_ms 和 end_time_ms，由 MioLearner 帮你指定，除非你不想由 MioLearner 替你指定。
    另外 MioLearner 会帮你指定一个合适的 postfix，避免有人复制了你的训练直接启动会竞争样本。

    调用示例
    ------
    ``` python
    .fetch_message(
      group_id="common_leaf_test",
      kafka_topic="explore_photo_training_sample_realtime",
      output_attr="raw_sample_package_str")
    ```
    """
    self._add_processor(OfflineMessageFetchEnricher(kwargs))
    return self

  def fetch_synchronize(self, **kwargs):
    """
    FetchSynchronizeEnricher
    ------
    多 pipeline 读取多数据源时，进行时间戳的同步

    参数配置
    ------
    `max_diff_second`: [int] 多数据源之间最大的时间戳 diff，单位 s。

    `min_wait_millsecond`: [int] 最小 sleep 时间，单位 ms。

    `max_wait_millsecond`: [int] 最小 sleep 时间，单位 ms。

    `fetch_offset_second`: [int] 延迟消费功能，保持读取时间戳与当前时间戳有一定 diff，单位 s。

    `time_ms_attr`: [string] 从给定的 common attr 获取样本时间戳，单位 ms。默认为空，表示使用 context 自带的 RequestTime。

    GFLAGS 配置
    ------

    调用示例
    ------
    ``` python
    .fetch_synchronize(
      max_diff_second=3600,
      min_wait_millsecond=5,
      max_wait_millsecond=100,
      time_ms_attr='')
    ```
    """
    self._add_processor(FetchSynchronizeEnricher(kwargs))
    return self

  def retrieve_from_tf_sequence_example(self, **kwargs):
    """
    OfflineTensorflowSequenceExampleRetriever
    ------
    从 SequenceExample 中获取 item 列表，并填充 attr。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 common attr 获取 SequenceExample。

    `item_key_attr`: [string] 从给定的 field 获取 item key，默认为空，用序列号作为 item key。

    `user_id_attr`: [string] 从给定的 field 获取 user id，默认为空，不设置 user id。

    `device_id_attr`: [string] 从给定的 field 获取 device id，默认为空，不设置 device id。

    `time_ms_attr`: [string] 从给定的 field 获取 request time，默认为空，不设置 request time。

    `reason`: [int] 触发的 reason，默认为 0。

    调用示例
    ------
    ``` python
    .retrieve_from_tf_sequence_example(
      from_extra_var="tf_seq_example",
      item_key_attr="item_key")
    ```
    """
    self._add_processor(OfflineTensorflowSequenceExampleRetriever(kwargs))
    return self

  def random_init_context(self, **kwargs):
    """
    OfflineRandomContextEnricher
    ------
    随机初始化当前 context，包括 uid、did、tf-kuiba 更新参数的 flag 等等，供离线 runner 使用

    参数配置
    ------
    `random_init_uid`: [bool] 随机初始化 uid，默认为 true

    `random_init_did`: [bool] 随机初始化 did，默认为 true

    `tf_kuiba_update_model_prob`: [float] tf-kuiba 拉取最新网络参数的概率，默认为 0.1。配合 enrich_attr_by_tf_local_predict 使用

    `tf_kuiba_update_model_key`: [str] 用于控制拉取最新网络参数的 key，默认值为 PULL_MODEL_VARIABLE。如果触发了网络参数更新，则会在 common attr 中添加一个值为 1 的 int value。

    调用示例
    ------
    ``` python
    .random_init_context()
    ```
    """
    self._add_processor(OfflineRandomContextEnricher(kwargs))
    return self

  def reset_timestamp(self, **kwargs):
    """
    OfflineResetTimeStampEnricher
    ------
    使用 common attr 覆盖原有请求时间

    参数配置
    ------
    `timestamp`: [str] 时间戳 common attr

    `time_unit`: [string] timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    调用示例
    ------
    ``` python
    .reset_timestamp(timestamp= 'uTimeStamp')
    ```
    """
    self._add_processor(OfflineResetTimeStampEnricher(kwargs))
    return self

  def reset_user_meta_info(self, **kwargs):
    """
    OfflineResetUserMetaInfoEnricher
    ------
    使用 common attr 覆盖原有请求时间,请求user_id,请求device_id

    参数配置
    ------
    `timestamp_attr`: [str] 时间戳 common attr

    `time_unit`: [string] timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    `user_id_attr`: [str] user_id common attr

    `device_id_attr`: [string] device_id common attr

    调用示例
    ------
    ``` python
    .reset_user_meta_info(timestamp_attr= 'uTimeStamp', user_id_attr= 'user_id', device_id_attr= 'device_id')
    ```
    """
    self._add_processor(OfflineResetUserMetaInfoEnricher(kwargs))
    return self


  def retrieve_photo_info_from_btq(self, **kwargs):
    """
    OfflineBTQPhotoInfoRetriever
    ------
    读取 btq message , 反解为 ItemDoc , 根据 ItemDoc::status() 过滤待删除的 item ，然后反解 ItemDoc::item_info 为 PhotoInfo , 并存入 context

    参数配置
    ------
    `reason`: [int] 默认为 1 , retrieve 标记 

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `delete_status_attr`: [list[string]] [dynamic_parameter] 需要删除的 ItemDoc status 列表, 一般由 get_kconf_params processors 生成

    `save_item_info_to_attr`: [string] photo info 存入的 ItemAttr 字段 

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    `enable_lite_photo_map`: [bool] 是否将特定的 photo 存入本地的 LitePhotoMap ，默认 False

    `photo_map_capacity`: [int] 本地的 LitePhotoMap 的最大可存入 photo 数量，默认 10000000

    `photo_upload_expire_sec`: [int] 存入本地的 LitePhotoMap 的 photo 的上传时间不可大于该值，默认 30 分钟

    `just_run_at_shard0`: [bool] 仅 shard 0 的 runner 实例读取 btqueue , 默认为 true

    调用示例
    ------
    ``` python
    .retrieve_photo_info_from_btq(batch_size=1024,
      queue_name='exp_realtime_item_doc',
      delete_status_attr='delete_status',
      save_item_info_to_attr='photo_info'
    )
    ```
    """
    self._add_processor(OfflineBTQPhotoInfoRetriever(kwargs))
    return self
  
  def retrieve_photo_from_model_update_btq(self, **kwargs):
    """
    OfflineBTQModelUpdateRetriever
    ------
    读取 btq message , 反解为 ModelUpdateMessage, 从 item 中提取 photo_id ，作为召回结果

    参数配置
    ------
    `reason`: [int] 默认为 2 , retrieve 标记 

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `queue_no`: [string] btq queue no. , 即 queue_name 为所有要读取的 queue 中的第几个 queue

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    `kuiba_photo_slots`: [string] 可选，kuiba sign 特征需要标注哪些 slot/key_type 是 item id 特征, 
                          格式为kuiba_photo_slots = "slot1,slot2,...",
                          同时需要设置 --use_kuiba_sign_slot=true 

    调用示例
    ------
    ``` python
    .retrieve_photo_from_model_update_btq(batch_size=1024,
                                          queue_name='exp_realtime_item_doc',
    )
    ```
    """
    self._add_processor(OfflineBTQModelUpdateRetriever(kwargs))
    return self

  def retrieve_item_from_id_btq(self, **kwargs):
    """
    OfflineBTQIdRetriever
    ------
    读取 btq message , 将 message 转成 photo_id ，作为召回结果

    参数配置
    ------
    等同于 retrieve_photo_from_model_update_btq

    调用示例
    ------
    ``` python
    .retrieve_item_from_id_btq(
      queue_name='photo_id_queue0',
      queue_no=0,
      batch_size=1024,
      sleep_for_wait_btq_ms=50,
    )
    ```
    """
    self._add_processor(OfflineBTQIdRetriever(kwargs))
    return self

  def retrieve_photo_from_lite_photo_map(self, **kwargs):
    """
    OfflineLitePhotoMapRetriever 
    ------
    扫描本地的 LitePhotoMap ，获取一定数量的 photo

    参数配置
    ------
    `reason`: [int] 默认为 3 , retrieve 标记 

    `batch_size`: [int] 计算 photo embedding 的 batch size , 默认 128

    `photo_map_capacity`: [int] 本地的 LitePhotoMap 的最大可存入 photo 数量，默认 10000000

    `photo_upload_expire_sec`: [int] 存入本地的 LitePhotoMap 的 photo 的上传时间不可大于该值，默认 30 秒

    `update_interval_sec`: [int] 扫描一次 LitePhotoMap 后 sleep 多少秒 ，默认 30 秒

    调用示例
    ------
    ``` python
    .retrieve_photo_info_from_btq(batch_size=1024,
      queue_name='exp_realtime_item_doc',
      delete_status_attr='delete_status',
      save_item_info_to_attr='photo_info'
    )
    ```
    """
    self._add_processor(OfflineLitePhotoMapRetriever(kwargs))
    return self
  
  def retrieve_kuiba_attr_from_btq(self, **kwargs):
    """
    OfflineBTQKuibaAttrRetriever
    ------
    读取 btq message , 反解为 ItemDoc , 根据 ItemDoc::status() 过滤待删除的 item ，然后反解 ItemDoc::item_info 为 kuiba::PredictItem, 并从中提取指定的或全部的 attr、以 attr name 为 key 存入 item attr

    参数配置
    ------
    `reason`: [int] 默认为 1 , retrieve 标记 

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `delete_status_attr`: [list[string]] 需要删除的 ItemDoc status 列表, 一般由 get_kconf_params processors 生成

    `item_features`: [list[string]] 需要抽取的 attr 字段 

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    `enable_lite_photo_map`: [bool] 是否将特定的 photo 存入本地的 LitePhotoMap ，默认 False

    `photo_map_capacity`: [int] 本地的 LitePhotoMap 的最大可存入 photo 数量，默认 10000000

    `photo_upload_expire_sec`: [int] 存入本地的 LitePhotoMap 的 photo 的上传时间不可大于该值，默认 30 分钟

    `just_run_at_shard0`: [bool] 仅 shard 0 的 runner 实例读取 btqueue , 默认为 true

    调用示例
    ------
    ``` python
    .retrieve_photo_info_from_btq(batch_size=1024,
      queue_name='exp_realtime_item_doc',
      delete_status_attr='delete_status',
      item_features=["pId"],
    )
    ```
    """
    self._add_processor(OfflineBTQKuibaAttrRetriever(kwargs))
    return self
  
  def retrieve_common_doc_from_btq(self, **kwargs):
    """
    OfflineBTQCommonIndexDocRetriever
    ------
    读取 btq message , 反解为 CommonIndexDoc . 如果定义了 photoInfo 的序列化结果，则将序列化结果存入 context; 如果定义了 upload_time , 则根据开关存入 litePhotoMap

    参数配置
    ------
    `reason`: [int] 默认为 1 , retrieve 标记 

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `save_item_info_to_attr`: [string] photo info bytes 存入的 ItemAttr 字段 

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    `enable_lite_photo_map`: [bool] 是否将特定的 photo 存入本地的 LitePhotoMap ，默认 False

    `photo_map_capacity`: [int] 本地的 LitePhotoMap 的最大可存入 photo 数量，默认 10000000

    `photo_upload_expire_sec`: [int] 存入本地的 LitePhotoMap 的 photo 的上传时间不可大于该值，默认 30 分钟

    `item_info_bytes_name`: [string] common doc 中存储序列化 photoInfo 的字段名，为空则不获取

    `upload_time_name`: [string] common doc 中存储上传时间的字段名，为空则相当于 enable_lite_photo_map = False

    调用示例
    ------
    ``` python
    .retrieve_common_doc_from_btq(batch_size=1024,
      queue_name='exp_realtime_item_doc',
      item_info_bytes_name='photo_info_str',
      save_item_info_to_attr='photo_info_str',
      upload_time_name='upload_time_ms',
      enable_lite_photo_map=True
    )
    ```
    """
    self._add_processor(OfflineBTQCommonIndexDocRetriever(kwargs))
    return self

  def retrieve_photo_from_oversea_model_btq(self, **kwargs):
    """
    OfflineOverseaBTQModelUpdateRetriever
    ------
    读取 btq message , 反解为 ModelUpdateMessage, 从 item 中提取 photo_id ，作为召回结果

    参数配置
    ------
    `reason`: [int] 默认为 2 , retrieve 标记 

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    调用示例
    ------
    ``` python
    .retrieve_photo_from_oversea_model_btq(batch_size=1024,
                                           queue_name='exp_realtime_item_doc',
    )
    ```
    """
    self._add_processor(OfflineOverseaBTQModelUpdateRetriever(kwargs))
    return self

  def reshard_model_update_message_to_btq(self, **kwargs):
    """
    OfflineModelUpdateMessageReshardEnricher
    ------
    从 common attr 中读取 ks::model::ModelUpdateMessage，根据每个 ModelItem 中的 sign 按照取模的方式进行 reshard，重新组装后发送到下游 btq

    参数配置
    ------
    `input_attr`: [string] 存放 ks::model::ModelUpdateMessage 指针的 common attr

    `batch_size`: [int] 发送到下游 btq 时，每条 message 的 ModelItem 数量, 默认 1024

    `queue_name`: [string] 下游 btq queue name

    `queue_shard_num`: [int] 下游 btq 的 shard 数，默认为 1024

    `is_logic_topic`: [bool] 是否使用逻辑 topic，默认为 True

    调用示例
    ------
    ``` python
    .reshard_model_update_message_to_btq(
      input_attr='model_update_message',
      batch_size=1024,
      queue_name='colossusdb_embd_test_v0',
      queue_shard_num=1024,
      is_logic_topic=True
    )
    ```
    """
    self._add_processor(OfflineModelUpdateMessageReshardEnricher(kwargs))
    return self
  
  def retrieve_by_random_negative_sampling_v2(self, **kwargs):
    """
    RandomNegativeSamplingRetrieverV2
    ------
    对样本进行全局随机负采样, 与RandomNegativeSamplingRetriever的不同是会在buffer中去除重复Photo

    参数配置
    ------
    `sampling_rate`: [double][动态参数] 负采样率，默认为0.01。

    `output_pids_attr`: [string] 存储负采样的 pid list 的 common attr。

    `buffer_size`: [string] 存储 photo info 的 buffer 的 大小，默认为10000。

    `sampling_cnt_per_item`: [int] 每条样本负采样数量，默认为1.

    `filter_pids_attrs`: [list] 用于过滤 pid 的 common attr list。

    `photo_info_attr`: [string] 存储 PhotoInfo 的item attr。

    `is_negative_sampling_attr`: [string] 存储是否为负采样样本的item attr。

    `extra_int_attrs`: [list] buffer 中存储额外的 int 类型 item attr。

    `extra_float_attrs`: [list] buffer 中存储额外的 double 类型 item attr。

    `extra_string_attrs`: [list] buffer 中存储额外的 string 类型 item attr。
    
    `pick_item`: [dict] 用来指定触发采样以及更新buffer的条件。

    `debug`: [bool] debug模式，即把buffer内容输出到common_attr，默认为False, 并且记录重复率相关attr。

    `debug_attr`: [string] debug模式下存储buffer内容的common_attr名称，默认为buffer。

    `debug_repeat_nums`: [string] debug模式下存储入buffer时重复次数的common_attr， 默认为buffer_repeat_nums。

    `debug_all_nums`: [string] debug模式下存储入buffer操作的总次数的common_attr，用来计算重复率，默认为buffer_all_nums。buff_repeat_nums/buff_all_nums即重复率。

    调用示例
    ------
    ``` python
    .retrieve_by_random_negative_sampling_v2(
      sampling_rate=0.01,
      output_pids_attr="pids_attr",
      buffer_size=10000,
      sampling_cnt_per_item=1,
      filter_pids_attrs=["colossus_pids"],
      photo_info_attr="photo_info",
      extra_string_attrs=["reason"],
      extra_float_attrs=["pctr", "pltr"],
      is_negative_sampling_attr="is_negative_sampling",
      pick_item={ "tag": 1 }
    )
    ```
    """
    self._add_processor(RandomNegativeSamplingRetrieverV2(kwargs))
    return self

  def retrieve_by_random_negative_sampling(self, **kwargs):
    """
    RandomNegativeSamplingRetriever
    ------
    对样本进行全局随机负采样

    参数配置
    ------
    `sampling_rate`: [double][动态参数] 负采样率，默认为0.01。

    `output_pids_attr`: [string] 存储负采样的 pid list 的 common attr。

    `buffer_size`: [string] 存储 photo info 的 buffer 的 大小，默认为10000。

    `sampling_cnt_per_item`: [int] 每条样本负采样数量，默认为1.

    `filter_pids_attrs`: [list] 用于过滤 pid 的 common attr list。

    `photo_info_attr`: [string] 存储 PhotoInfo 的item attr。

    `is_negative_sampling_attr`: [string] 存储是否为负采样样本的item attr。

    `extra_int_attrs`: [list] buffer 中存储额外的 int 类型 item attr。

    `extra_float_attrs`: [list] buffer 中存储额外的 double 类型 item attr。

    `extra_string_attrs`: [list] buffer 中存储额外的 string 类型 item attr。
    
    `pick_item`: [dict] 用来指定触发采样以及更新buffer的条件

    调用示例
    ------
    ``` python
    .retrieve_by_random_negative_sampling(
      sampling_rate=0.01,
      output_pids_attr="pids_attr",
      buffer_size=10000,
      sampling_cnt_per_item=1,
      filter_pids_attrs=["colossus_pids"],
      photo_info_attr="photo_info",
      extra_string_attrs=["reason"],
      extra_float_attrs=["pctr", "pltr"],
      is_negative_sampling_attr="is_negative_sampling",
      pick_item={ "tag": 1 }
    )
    ```
    """
    self._add_processor(RandomNegativeSamplingRetriever(kwargs))
    return self

  def retrieve_by_random_table_sampling(self, **kwargs):
    """
    RandomTableSamplingRetriever
    ------
    利用 RandomTable 服务对样本进行全局随机负采样

    参数配置
    ------
    `remote_sampler`: [dict] 必配项，请求 RandomTable 服务的相关配置。
      - `service`: [string] 必配项，RandomTable 服务的 kess name
      - `power`: [double] 选配项，采样率调节，是否需依据该样本出现次数的 power 次方进行不同概率采样，默认为 0 即等概率采样。
      - `update_interval_seconds`: [int] 选配项，更新 RandomTable 服务的间隔，默认为 10 秒。

    `sampling_num`: [int][动态参数] 必配项，负采样数目，默认为 0。

    `save_author_id_to_attr`: [string] 选配项，将采样样本的 author_id 存入指定的 item attr。

    调用示例
    ------
    ``` python
    .retrieve_by_random_table_sampling(
      remote_sampler={
        "service": "grpc_xxxRandomTable"
      },
      sampling_num=100,
      save_author_id_to_attr="author_id"
    )
    
    ```
    """
    self._add_processor(RandomTableSamplingRetriever(kwargs))
    return self

  def fetch_redis_photo_profile(self, **kwargs):
    """
    OverseaFetchRedisPhotoProfileEnricher
    ------
    从 Redis 获取 Photo 的 Action device List (如 effective view device list, 前缀为1时返回值为playtime, 不返回device id)
    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name。
    
    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 20。
    
    `queue_size`: [int] 获取 device list 最大长度。
    
    `export_item_attr`: [string] 写入的 item attr name。
    
    `key_suffix`: [string] 用以区分不同的 action device list, 具体 enum 见 com.kuaishou.ksib.reco.proto.log.ActionType。
    
    `limit_timestamp`: [int][动态参数]可缺省。 限制获取的 action 在此时间戳之前, 不填默认不生效。
    
    `cache_bits`: [int]选配项, cache大小, 即最多存2^cache_bits个kv值(LRU)删除, 默认为0(无cache)
    
    `cache_expire_second`:  [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒
    
    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒
    
    `cache_name`:  [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同
    
    调用示例
    ------
    ``` python
    .fetch_redis_photo_profile(
      cluster_name = "kwai_redis_cluster_name",
      queue_size = 200,
      export_item_attr = "like_device_list",
      key_suffix = "10",
      limit_timestamp = "{{log_timestamp}}"
    )
    ```
    """
    self._add_processor(OverseaFetchRedisPhotoProfileEnricher(kwargs))
    return self


  def retrieve_by_combine_item(self, **kwargs):
    """
    OfflineItemCombineRetriever
    ------
    把结果集中的 item 按照指定分组进行组装，组装后的 item_attr 会拼接成 list

    参数配置
    ------
    `combine_item_map`: [dict] 组装的 item key map
      - `map_value_attr`: [string] int list 类型的 common attr，存储 n 组 item key 的列表
      - `map_size_attr`: [string] int list 类型的 common attr，存储 n 组 item key 的列表的长度

    `combine_attr` : [list] 组装的 item attr
      - `attr` : [string] 要组装的 item attr name
      - `output_attr` : [string] 组装后输出的 item attr name
      - `output_split_attr` : [string] 分隔的 size attr

    `save_combine_flag_to` : [string] 标记是否为组合 item 的 attr


    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(OfflineItemCombineRetriever(kwargs))
    return self     
  
  def ksib_dense_match(self, **kwargs):
    """
    OfflineKsibDenseMatchEnricher
    ------
    海外dense特征匹配

    参数配置
    ------
    `to_match_attr`: [int] 用于匹配时key值比较的 item attr name
    `match_configs` : [list]
      - `keys` : [int_list] 待匹配的key值list item attr name
      - `scores` : [double_list] 待匹配的score值list item attr name
      - `output` : [double] 存入匹配结果 item attr name

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(OfflineKsibDenseMatchEnricher(kwargs))
    return self

  def lag_set_clock(self, **kwargs):
    """
    OfflineLagClockObserver
    ------
    与 lag_watch_clock 一起使用，用当前样本时间尝试去更新最大时间，以便释放由于 lag_watch_clock 阻塞的 pipeline。

    调用示例
    ------
    ``` python
    .lag_set_clock()
    ```
    """
    self._add_processor(OfflineLagClockObserver(kwargs))
    return self

  def emb_fetch_from_kafka(self, **kwargs):
    """
    TrafficKafkaFetchObserver
    ------
    配合 kstable 的流量转发至 kafka 功能，从 Kafka 读取流量并发送请求至指定 embedding server 服务。
    当 protocol = 0 时（压 bt emb ）有如下限制：
      - 要求被转发的请求类型是 GetKuibaEmbeddingRequest。

    当 protocol = 1 时（压 colossusDB emb ）有如下限制：
      - 总是会根据指定的 shard_num 只回放一个实例的 key

    参数配置
    ------
    `protocol`: [int] (必填) 访问 embedding 服务所使用的协议，默认为 0, 协议的支持情况见 协议支持 部分。

    `topic_name`: [string] (必填) 读取的 kafka topic

    `group_id`: [string] (必填) 读取使用的 group name

    `unique_group_per_host`: [bool] 默认为 true, 此时每个实例实际使用的 group name 是 group_id + hostname, 来确保使用的 group_id 唯一

    `repeat`: [int] 每个读到的请求会触发 n 次请求的发送用于加压，默认为 1。

    `max_qps_kconf_key`: [string] 从指定的 kconf key 获取 qps 限制的配置。即使 repeat 次数再大，发送的 qps 也不会超过此数值。默认为空字符串。
      注意这个限制是进程级别的。每个线程会阻塞式地不断尝试获取 repeat 个配额，如果此数值小于 repeat 会导致所有线程都被一直阻塞。
      可能需要配合 FLAGS_dragon_grpc_use_multi_eventloop 使用。

    `max_qps`: [int] 当上述 kconf key 获取失败时，使用一个固定数值作为 qps 限制。默认为 1000。

    `timeout_ms`: [int] 超时时间 ms, 默认为 200

    `max_signs_per_request`: [int] 默认为 0, 当大于 0 时会限制每个请求包含的 key 个数最多为该值, 并在需要时将请求进行拆分;
      另外, 读取到的请求如果 size 等于该值, 并且进行了拆包（由 shard_num 增加导致的）, 则不会立即发送请求, 被缓存的请求会等待被拼接为更大的请求。

    以下为 protocol == 0 时的对应配置

    `kess_service`: [string] (必填) 要请求的 embedding 服务名

    `shard_num`: [int] embedding 服务 shard 数，默认为 1

    `kess_cluster`: [string] 默认为 PRODUCTION

    `send_shard_num`: [int] 默认为 1, 此时只发送前1个 shard 也就是 shard0 的请求（会根据请求中解析的 sign 判断其所属的 shard_id ），其余的请求直接丢弃

    `repack`: [bool] 默认为 true, 此时对于从 kafka 读到的每个请求, 都会逐个检测其中每个 key 应属于哪个 shard_id, 并重新生成请求

    `min_signs_per_request`: [int] 默认为 0, 当请求里面的 key 个数小于该值时不进行发送, 等待后续请求填充到足够数目之后再一起发送

    `use_brpc_client`: [bool] 默认为 false, 此时使用 grpc client。设为 true 时使用 brpc client

    以下为 protocol == 1 时的对应配置

    `colossusdb_embd_service_name`: [string] (必填) 下游 colossusdb embedding server 的服务名，一般是: grpc_clsdb_ps-{模型名称}.
      获取服务名的方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH)

    `colossusdb_embd_table_name`: [string] (必填) 下游 colossusdb embedding server 的表名，如 test-table

    `request_type`: [int] 转发请求类型: 0 对应 BatchListGet, 1 对应 BatchMapGet; 默认为 0.

    `shard_num`: [int] embedding 服务单副本实例数，默认为 1

    `shard_id`: [int] 发压到指定的一个 shard_id ，默认为 0

    `partition_num`: [int] embedding 服务 partition 数，默认为 1

    `partition_less_than`: [int] 只发送 partition_id 小于该值的数据，默认等于 partition_num

    协议支持
    ------

    当前共实现了2种协议，对应的服务器端实现情况分别为：

    | protocol \ server       | bt_embedding_server | colossusdb_embedding_server |
    |:------------------------|:--------------------|:----------------------------|
    | 0 (GetKuibaEmbedding)   | YES                 | NO                          |
    | 1 (统一存储)             | NO                  | YES                         |

    调用示例
    ------
    ``` python
    .emb_fetch_from_kafka(
      topic_name="colossus_sim_requests",
      group_id="test",
      kess_service="kws-kuaishou-platform-embedding-zxx-new-base-ps-test",
      shard_num=8
    )

    .emb_fetch_from_kafka(
      topic_name="colossus_living_photo_item",
      group_id="grpc_colossusLivingPhotoItem",
      protocol=1,
      colossusdb_embd_service_name="grpc_clsdb_ps-reco-arch-test",
      colossusdb_embd_table_name="test_table",
      shard_num=8
    )
    ```
    """
    self._add_processor(TrafficKafkaFetchObserver(kwargs))
    return self

  def lag_watch_clock(self, **kwargs):
    """
    OfflineLagWatcherObserver
    ------
    与 lag_set_clock 一起使用，当样本时间晚于调用 lag_set_clock 所指定的最大时间时，阻塞 pipeline。
    等 lag_set_clock 所指定的时间大于当前样本的时间（也可以增加一个间隔）时，继续当前 pipeline。

    参数配置
    ------
    `lag_seconds`: [int] lag 的时间，单位是秒，默认为 0。

    `interval_seconds`: [int] 检查时间的间隔，单位是秒，默认为 30。

    调用示例
    ------
    ``` python
    .lag_watch_clock()
    ```
    """
    self._add_processor(OfflineLagWatcherObserver(kwargs))
    return self

  def dispatch_client_feature(self, **kwargs):
    """
    OfflineClientFeatureDispatchEnricher
    -----
    客户端上报的特征是以 json 的格式列存储的，该 processor 将客户端上报的 json 解析为 item attr。

    参数配置
    ------
    `input_json_attr`: [string] 输入的 json 的 common attr。

    `attrs`: [list] 输出的 attr 列表，需要包含 item_attr，json_field, type 三个字段，其中 type 支持 int，float，string，int_list，float_list，string_list。

    调用示例
    ------
    ``` python
    .dispatch_client_feature(
      input_json_attr="client_feature",
      attrs=[
        dict(item_attr="pctr", json_field="p_ctr", type="float"),
      ]
    )
    ```
    """
    self._add_processor(OfflineClientFeatureDispatchEnricher(kwargs))
    return self

  def ksib_reshape_attrs(self, **kwargs):
    """
    OfflineKsibReshapeEnricher
    ------
    对attr做类型转换

    参数配置
    ------
    `is_common_attr`: [bool] 选配项，是否对 common 侧转换，默认为 True

    `fill_default_value`: [bool] 选配项，是否填充默认值，默认为 True

    `reshape_type`: [int] 类型转换的方式, 默认为 1
     - 1: int 转换为 int_list
     - 2: float 转换为 float_list
     - 3: int 转换为 float_list

    `reshape_attrs` : [list]
      - `input` : [string] 待转换的 attr name
      - `output` : [string] 转换后的 attr name

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(OfflineKsibReshapeEnricher(kwargs))
    return self

  def colossus_ckpt_reader(self, **kwargs):
    """
    OfflineColossusCkptReaderEnricher
    -----
    Colossus Checkpoint Reader
  
    参数配置
    -----
    `service_name`: [string] colossus service name
  
    `output_attr`: [string] 输出的 common attr 

    `output_epoch_attr`: [string] 输出 epoch number 的 common attr
  
    `read_thread_num`: [int] 读取 hdfs 线程数, default:8
  
    `buffer_size`: [int] buffer size, default: 10000
  
    `batch_size`: [int] 一次读取到 dragon 的条数, default: 512

    `use_common_colossus_resp`: [bool] 是否使用和 colossus processor 同样格式的返回结果

    `user_id_attr`: [string] 开启 use_common_colossus_resp 时，输出 user_id 
  
    调用示例
    -----
    ```python
    .colossus_ckpt_reader(service_name="grpc_colossusLiveItemV4",
                          output_attr="colossus_ckpt_out")
    ```
    """
    self._add_processor(OfflineColossusCkptReaderEnricher(kwargs))
    return self
  
  def live_colossus_ckpt_seq_model_sample_retriever(self, **kwargs):
    """
    OfflineLiveColossusCkptSeqModelSampleRetriever
    -----
    从 colossus checkpoint 生成直播离线序列模型样本
  
    参数配置
    -----
    `input_attr`: [string] 对应 colossus_ckpt_reader output_attr

    `output_prefix`: [string] list 序列特征 attr 前缀

    `live_item_version`: [int] LiveItem Proto version

    `max_list_size`: [int] 序列特征最大长度

    `reason`: [int] reason

    `reward_weight`: [float] 对序列排序时 reward 的权重

    `play_time_weight`: [float] 对序列排序时 play_time 的权重
  
    调用示例
    -----
    ```python
    .live_colossus_ckpt_seq_model_sample_retriever(input_attr="colossus_ckpt_out",
                                                   output_prefix="live_author_",
                                                   reward_weight=1000.0,
                                                   play_time_weight=1.0)
    ```
    """
    self._add_processor(OfflineLiveColossusCkptSeqModelSampleRetriever(kwargs))
    return self
  
  def random_retrieve_from_common_attr(self, **kwargs):
    """
    OfflineRandomRetriveFromCommonAttrRetriever
    -----
    随机从 Common Attr 里采样一部分，可配置采样概率 

    参数
    -----
    `reason`: [int] 召回原因

    `id_attr`: [string] 待召回 int_list common attr

    `retrieve_num`: [int] 动态参数, 召回数量 
    
    `prob_attr`: [string] 选配项, 采样概率 double_list common attr

    `output_index_attr`: [string] 选配项, 输出召回 id 在 list attr 中的 index

    调用示例
    -----
    ```python
    .random_retrieve_from_common_attr(
      id_attr="author_id_list",
      prob_attr="sample_prob",
      output_index_attr="sample_index",
      retrieve_num={{retrieve_num}},
    )
    ```
    """
    self._add_processor(OfflineRandomRetrieveFromCommonAttrRetriever(kwargs))
    return self

  def retrieve_from_local_sample_pool(self, **kwargs):
    """
    OfflineLocalSamplePoolRetriever
    -----
    从本地 sample pool 里采样。
    sample pool 采用滑动窗口设计，统计一段时间内出现过的 item id, 并按照概率采样。

    参数
    -----
    `reason`: [int] 召回原因

    `retrieve_num`: [int] 动态参数, retrieve 数量

    `output_prob_attr`: [string] 选配项，输出 prob 的 attr

    `output_pool_size_attr`: [string] 选配项，输出 local sample pool size 的 attr

    `sample_pool_config`: [dict] sample pool config
      - `key`: [string] 标识全局唯一 sample pool
      - `power`: [double] 选配项，采样率调节，是否需依据该样本出现次数的 power 次方进行不同概率采样，默认为 0 即等概率采样。
      - `update_interval_seconds`: 选配项，更新采样概率时间间隔, 默认为 10 秒
      - `window_period_seconds`: 选配项, 一个窗口的统计周期, 默认为 3600 秒
      - `window_number`: 选配项, 窗口数量, 默认为 12
      - `window_capacity`: 选配项，窗口最大容纳 key 数量，默认为 1e8
      - `maximum_wait_udpate_cnt`: 选配项，最大更新队列长度，默认为 1

    调用示例
    -----
    ```python
    .retrieve_from_local_sample_pool(
      reason=100,
      sample_pool_config=dict(key="author_id_sample_pool"),
    )
    ```
    """
    self._add_processor(OfflineLocalSamplePoolRetriever(kwargs))
    return self
  
  def update_local_sample_pool(self, **kwargs):
    """
    UpdateLocalSamplePoolObserver
    -----
    更新本地 sample pool

    参数
    -----
    `id_from_common`: [string] 更新 id, int_list common attr

    `value_from_common`: [string] 更新 id 对应的 value, double_list common attr

    `id_from_item`: [string] 更新 id, int item attr, id_from_common 和 id_from_item 必须配置一个

    `value_from_item`: [string] 更新 id 对应的 value, double item_attr

    `sample_pool_config`: [dict] sample pool config
      - `key`: [string] 标识全局唯一 sample pool
      - `power`: [double] 选配项，采样率调节，是否需依据该样本出现次数的 power 次方进行不同概率采样，默认为 0 即等概率采样。
      - `update_interval_seconds`: 选配项，更新采样概率时间间隔, 默认为 10 秒
      - `window_period_seconds`: 选配项, 一个窗口的统计周期, 默认为 3600 秒
      - `window_number`: 选配项, 窗口数量, 默认为 10
    -----
    """
    self._add_processor(UpdateLocalSamplePoolObserver(kwargs))
    return self
  
  def update_local_sample_pool_by_redis(self, **kwargs):
    """
    UpdateLocalSamplePoolByRedisObserver
    -----
    通过redis 白名单（和 ANN IdFilter 兼容）更新本地 sample pool

    参数
    -----
    `redis_cluster`: [string] redis cluster name    
    `redis_key`: [string] redis key
    `redis_timeout_ms`: [int] redis timeout ms
    `update_batch_interval_ms`: [int] update batch interval ms

    `sample_pool_config`: [dict] sample pool config
      - `key`: [string] 标识全局唯一 sample pool
      - `power`: [double] 选配项，采样率调节，是否需依据该样本出现次数的 power 次方进行不同概率采样，默认为 0 即等概率采样。
      - `update_interval_seconds`: 选配项，更新采样概率时间间隔, 默认为 10 秒
      - `window_period_seconds`: 选配项, 一个窗口的统计周期, 默认为 3600 秒
      - `window_number`: 选配项, 窗口数量, 默认为 10
    -----
    """
    self._add_processor(UpdateLocalSamplePoolByRedisObserver(kwargs))
    return self

  def group_list_attr(self, **kwargs):
    """
    GroupListAttrEnricher
    -----
    将 list attr 按照 key group, 并应用 min, max, sum, avg 操作

    参数
    -----
    `configs`: [list] 配置
      - `is_common`: [bool] 是否是 common attr, 默认为 true
      - `key_attr`: [string] 用户 group 的 key
      - `out_key_attr`: [string] 输出 key attr
      - `out_count_attr`: [string] 输出 count attr
      - `agg_configs`: [list] 聚合设置
        - `src_attr`: [string] 输入 attr name
        - `op`: [string] 可选择 (min, max, sum, avg)
        - `dst_attr`: [string] 输出 attr name
        - `dst_type`: [string] 输出 attr type, 可选择 (int_list, float_list) 默认和 src_attr 保持一致

    调用示例
    -----
    ```python
    .group_list_attr(
      configs=[
        dict(
          key_attr="author_id",
          out_key_attr="unique_author_id",
          out_count_attr="author_count",
          agg_configs=[
            dict(
              src_attr="gift_amt",
              op="sum",
              dst_attr="gift_amt_sum",
              dst_type="int_list",
            ),
          ],
        ),
      ],
    )
    ```
    """
    self._add_processor(GroupListAttrEnricher(kwargs))
    return self

  def transform_list_attr(self, **kwargs):
    """
    TransformListAttrEnricher
    -----
    对 common list attr 进行排序、过滤后生成新的 common list attr

    参数
    -----
    `configs`: [list] transform 配置
      - `is_dst_common`: [bool] 输出是否是 common attr, 默认为 true

      - `sort_attr`: [string] 用于排序的 attr

      - `is_sort_attr_common`: [bool] 用于排序的 attr 是否是 common attr, 默认为 true

      - `sort_desc`: [bool] 是否降序排序，默认为 true

      - `limit_size`: [int] 新 list attr 最大长度, 默认为 -1 表示没有限制

      - `output_sort_indices_attr`: [string] 输出排序后的 indices attr, 默认为 None

      - `output_mask_flags_attr`: [string] 输出 mask flags attr, 默认为 None

      - `use_double_mask`: [bool] 是否使用 double mask, 默认为 false

      - `filter_configs`: [list] filter 配置
        - `attr`: [string] 用于 filter 的 attr
        - `is_attr_common`: [bool] 用于 filter 的 attr 是否是 common attr, 默认为 true
        - `lower_bound_attr`: [string] 用于 filter 的 attr 的 lower bound, 默认为 None
        - `upper_bound_attr`: [string] 用于 filter 的 attr 的 upper bound, 默认为 None
        - `not_equal_to_attr`: [string] 不等于 attr, 默认为 None
        - `is_not_equal_to_attr_common`: [bool] 不等于 attr 是否是 common attr, 默认为 true

      - `attr_configs`: [list] attr 配置
       - `src_attr`: [string] 输入 attr name
       - `dst_attr`: [string] 输出 attr name
       - `is_src_attr_common`: [bool] 输入 attr 是否是 common attr, 默认为 true

    调用示例
    -----
    .transform_list_attr(
      configs=[
        dict(
          is_dst_common=True
          sort_attr="timestamp",
          sort_desc=True,
          filter_configs=[
            dict(attr="watch_time", min_val=60),
          ]
          limit_size=50,
          attr_configs=[
            dict(src_attr="author_id", dst_attr="new_author_id"),
            dict(src_attr="watch_time", dst_attr="new_watch_time"),
          ]
        ),
      ]
    )
    """
    self._add_processor(TransformListAttrEnricher(kwargs))
    return self
  
  def joint_reco_log(self, **kwargs):
    """
    JointRecoLogEnricher
    ------
    recoLog 拼接 label, item_key必须是photo_id

    参数配置
    ------

    `reco_log_attr`: [string] 从给定 common attr 获取要拼接的 recoLog。

    `label_llsid_pid_str`: [string] 从给定 item attr 获取 label string。

    `label_uid_pid_str`: [string] 从给定 item attr 获取 label string。

    `label_did_pid_str`: [string] 从给定 item attr 获取 label string。

    `label_uid_aid_str`: [string] 从给定 item attr 获取 label string。

    `ad_label_attr_names`: [list] 从给定 item attr 获取 ad label string, 默认为空。

    `kconf_path_of_action_to_label_map`: [string] kconf path，指定 label 填充 contextInfo 的映射表，默认是 reco.feedStream.recoLogAllActionLabelMap。

    调用示例
    ------
    ``` python
    .joint_reco_log(
        reco_log_attr='reco_log',
        label_llsid_pid_str='reco_label_llsid_pid',
        label_uid_pid_str='reco_label_uid_pid',
        label_did_pid_str='reco_label_did_pid',
        label_uid_aid_str='reco_label_uid_aid',
        ad_label_attr_names=['reco_plc_label_uid_pid', 'reco_plc_ad_label_uid_creative', 'reco_plc_label_llsid_pid'],
        kconf_path_of_action_to_label_map='reco.feedStream.recoLogAllActionLabelMap')
    ```
    """
    self._add_processor(JointRecoLogEnricher(kwargs))
    return self

  def retrieve_from_session_based_rl(self, **kwargs):
    """
    OfflineSessionBasedRLRetriever
    ------
    从::ks::reco::FullLinkSession中触发

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取 ks::reco::FullLinkSession。

    `reason`: [int] 触发的 reason，默认为 0。

    `xxx_weight`: [double] xxx 信号的reward权重，默认为0.0。

    `gamma`: [double] future_reward的discount factor，默认为0。

    调用示例
    ------
    ``` python
    .retrieve_from_session_based_rl(sample_from_attr="session_based_rl",
      like_weight=1.0,
      comment_weight=2.0,
      gamma=0.6)
    ```
    """
    self._add_processor(OfflineSessionBasedRLRetriever(kwargs))
    return self
  
  def fulldata_retrieve_from_session_based_rl(self, **kwargs):
    """
    OfflineFulldataSessionBasedRLRetriever
    ------
    从::ks::reco::FullLinkSession中触发

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取 ks::reco::FullLinkSession。

    `reason`: [int] 触发的 reason，默认为 0。

    `xxx_weight`: [double] xxx 信号的reward权重，默认为0.0。

    `gamma`: [double] future_reward的discount factor，默认为0。

    调用示例
    ------
    ``` python
    .fulldata_retrieve_from_session_based_rl(sample_from_attr="session_based_rl",
      like_weight=1.0,
      comment_weight=2.0,
      gamma=0.6)
    ```
    """
    self._add_processor(OfflineFulldataSessionBasedRLRetriever(kwargs))
    return self
  
  def fulldata_retrieve_from_session_based_rl_watch_tired(self, **kwargs):
    """
    OfflineFulldataSessionBasedRLWatchTiredRetriever
    ------
    从::ks::reco::FullLinkSession中触发

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取 ks::reco::FullLinkSession。

    `reason`: [int] 触发的 reason，默认为 0。

    `xxx_weight`: [double] xxx 信号的reward权重，默认为0.0。

    `gamma`: [double] future_reward的discount factor，默认为0。

    调用示例
    ------
    ``` python
    .fulldata_retrieve_from_session_based_rl_watch_tired(sample_from_attr="session_based_rl_watch_tired",
      like_weight=1.0,
      comment_weight=2.0,
      gamma=0.6)
    ```
    """
    self._add_processor(OfflineFulldataSessionBasedRLWatchTiredRetriever(kwargs))
    return self
  
  def reco_log_joint_full_link(self, **kwargs):
    """
    RecoLogJointFullLinkEnricher
    ------
    recoLog 拼接 full_link_sample

    参数配置
    ------

    `joint_reco_log_attr`: [string] 从给定 common attr 获取要拼接的 recoLog, 拼接结果写回给定的 attr。

    `full_link_attr`: [string] 从给定 common attr 获取 fullLinkSample。

    `user_full_link_photo_info`: [bool] 是否用 fullLinkSample 样本的 photoInfo 覆盖 recoLog 的, 默认是 true。

    调用示例
    ------
    ``` python
    .reco_log_joint_full_link(
        joint_reco_log_attr='reco_log',
        full_link_attr='full_link',
        user_full_link_photo_info=true)
    ```
    """
    self._add_processor(RecoLogJointFullLinkEnricher(kwargs))
    return self

  def enrich_with_reco_log_packed_item_attr(self, **kwargs):
    """
    RecoLogPackedItemAttrEnricher
    ------
    从 RecoLog full_link_packed_item_attr 字段 (ks.platform.PackedItemAttr 类型) 扩展其所包含的全部 item attr, 当前支持 ValueType: FLOAT32/FLOAT64/INT32/INT64

    参数配置
    ------

    `reco_log_attr`: [string] 从哪个 common_attr（具体为 RecoLog pb 指针类型）获取 Message

    `packed_item_attr_list_attr`: [string] 选配项. 如配置非空值，作为 string_list common_attr，存放 PackedItemAttr 实时解析得到的全部 item_attr name

    `expected_output_item_attr_list`: [string list] 预期从 PackedItemAttr 获取的 item_attr 列表. 注意:
    (1) PackedItemAttr 数据所包含的全部 item_attr 无法提前获知. 避免 dsl 检测无上游产出 item_attr，请将 "预期" 从该数据获取的 item_attr 列出. 即：不保证所填 attr 可以获取得到
    (2) 该参数仅用于 dsl output_item_attrs 进行依赖检测与 downstream 推导, 不参与 C++ 代码中 item_attr 过滤

    调用示例
    ------
    ``` python
    .enrich_with_reco_log_packed_item_attr(
        reco_log_attr='reco_log',
        expected_output_item_attr_list=['patr', 'pbtr']
    )
    ```
    """
    self._add_processor(RecoLogPackedItemAttrEnricher(kwargs))
    return self
  
  def write_to_hdfs(self, **kwargs):
    """
    OfflineHdfsObserver
    ------
    将 Context 写入 hdfs 文件。

    参数配置
    ------
    `source_attr`: [string] 存到 hdfs 的 common or items attr,  bytes级别的写入
    
    `is_common`: [bool] 默认 False. 是否是common attr

    `line_seperate`: [bool] 默认 False. 是否写换行符
    
    `to_stdout`: [bool] 默认 False. 设置 True 输出到 stdout, `*_path_prefix` 会不起作用, 只允许单线程

    `disk_path_prefix`: [string] 写入本地文件的前缀

    `hdfs_path_prefix`: [string] [动态参数] 写入hdfs文件的路径前缀


    调用示例
    ------
    ``` python
    .write_to_hdfs(
      source_attr="bin_data",
      is_common=False,
      hdfs_path_prefix="viewfs:///home/<USER>/data",
    )
    ```
    """
    self._add_processor(OfflineHdfsObserver(kwargs))
    return self
  
  def retrieve_from_rerank_reco_rl(self, **kwargs):
    """
    OfflineRerankRecoRLRetriever
    ------
    处理 session rl 数据流重排使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取

    `reason`: [int] 触发的 reason，默认为 0

    调用示例
    ------
    ``` python
    .retrieve_from_rerank_reco_rl(
      sample_from_attr="reco_rl")
    ```
    """
    self._add_processor(OfflineRerankRecoRLRetriever(kwargs))
    return self

  def retrieve_from_rerank_reco_prm(self, **kwargs):
    """
    OfflineRerankRecoPRMRetriever
    ------
    处理 session rl 数据流重排使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取

    `reason`: [int] 触发的 reason，默认为 0

    调用示例
    ------
    ``` python
    .retrieve_from_rerank_reco_prm(
      sample_from_attr="reco_rl")
    ```
    """
    self._add_processor(OfflineRerankRecoPRMRetriever(kwargs))
    return self
  
  def retrieve_from_rank_reco_rl_v2(self, **kwargs):
    """
    OfflineContextRankRLRetriever
    ------
    处理 session rl 数据流重排使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取

    `reason`: [int] 触发的 reason，默认为 0

    调用示例
    ------
    ``` python
    .retrieve_from_rank_reco_rl_v2(
      sample_from_attr="reco_rl")
    ```
    """
    self._add_processor(OfflineContextRankRLRetriever(kwargs))
    return self
  
  def retrieve_from_rank_reco_rl(self, **kwargs):
    """
    OfflineRankRecoRLRetriever
    ------
    从::ks::reco::FullLinkSession中触发, RL 精排模型使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取 ks::reco::FullLinkSession。

    `reason`: [int] 触发的 reason，默认为 0。

    `xxx_weight`: [double] xxx 信号的reward权重，默认为0.0。

    `gamma`: [double] future_reward的discount factor，默认为0。

    调用示例
    ------
    ``` python
    .retrieve_from_rank_reco_rl(sample_from_attr="session_based_rl",
      like_weight=1.0,
      comment_weight=2.0,
      gamma=0.6)
    ```
    """
    self._add_processor(OfflineRankRecoRLRetriever(kwargs))
    return self

  def retrieve_from_rank_reco_rl_long(self, **kwargs):
    """
    OfflineRankRecoRLLongRetriever
    ------
    从::ks::reco::FullLinkSession中触发, RL 精排模型使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取 ks::reco::FullLinkSession。

    `reason`: [int] 触发的 reason，默认为 0。

    `xxx_weight`: [double] xxx 信号的reward权重，默认为0.0。

    `gamma`: [double] future_reward的discount factor，默认为0。

    调用示例
    ------
    ``` python
    .retrieve_from_rank_reco_rl_long(sample_from_attr="session_based_rl",
      like_weight=1.0,
      comment_weight=2.0,
      gamma=0.6)
    ```
    """
    self._add_processor(OfflineRankRecoRLLongRetriever(kwargs))
    return self
  
  def retrieve_from_rerank_plugin_info_log(self, **kwargs):
      """
      OfflineRerankPluginInfoLogRetriever
      ------
      从 ks::reco::PluginItemInfo 中触发重排异构物料内容

      参数配置
      ------
      `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::PluginItemInfo。

      `item_type`: [int] 触发的 item_type， 默认为 0。

      `reason`: [int] 触发的 reason，默认为 0。

      `save_plugin_reco_photo_to`: [string] 存储 RecoPhotoInfo 格式的 item attr，默认不存。

      调用示例
      ------
      ``` python
      .retrieve_from_rerank_plugin_info_log(
        from_extra_var="plugin_info_log",
        item_type=1,
        mixredis_item_type=2,
        save_mix_redis_photo_to="mix_redis_info",
        save_plugin_reco_photo_to="plugin_info")
      ```
      """
      self._add_processor(OfflineRerankPluginInfoLogRetriever(kwargs))
      return self
  
  def retrieve_from_rank_reco_page(self, **kwargs):
    """
    OfflineRankRecoPageRetriever
    ------
    处理 session rl 数据流精排使用

    参数配置
    ------
    `sample_from_attr`: [string] 从给定的 sample_from_attr 读取

    `reason`: [int] 触发的 reason，默认为 0

    调用示例
    ------
    ``` python
    .retrieve_from_rank_reco_page(
      sample_from_attr="reco_rl")
    ```
    """
    self._add_processor(OfflineRankRecoPageRetriever(kwargs))
    return self

  def batch_consume_kafka(self, **kwargs):
    """
    OfflineKafkaBatchConsumeEnricher
    ------
    批量消费 kafka, 将 message 的 payload 存储到 list 类型的 common attr 中。基于 ::ks::infra::kfk::SimpleConsumer 实现，需要单进程消费全量数据。
    
    官方文档：

    - https://halo.corp.kuaishou.com/help/docs/a02b4cb0baa2cf62a668f47532ebee1c#simple-consumer

    - https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------
    `topic_id`: [string] 必填，kafka topic id

    `group_id`: [string] 必填，kafka group id

    `output_attr`: [string] 必填，用于存储的 common attr。

    `timeout_ms`: [int] 选填，超时时间，默认 100 ms

    `msg_num`: [int] 选填，批量获取数量，默认 100

    `user_extra_params`: [string] 选填，用于手动指定 kafka consumer 的参数，默认为空。格式为 KEY=VALUE,多个参数可以使用;分隔。注意可能会覆盖原有的 fetch.message.max.bytes 等参数，使用前请咨询 kafka oncall

    调用示例
    ------
    ``` python
    .batch_consume_kafka(
      topic_id="kafka_topic_id",
      group_id="kafka_group_id",
      output_attr="msgs",
      timeout_ms=200,
      msg_num=10
    )
    ```
    """
    self._add_processor(OfflineKafkaBatchConsumeEnricher(kwargs))
    return self

  def retrieve_photo_with_emb_from_model_update_btq(self, **kwargs):
    """
    OfflineBTQModelUpdateRetriever
    ------
    读取 btq message , 反解为 ModelUpdateMessage, 从 item 中提取 sign_id 和 embedding 存入召回结果

    参数配置
    ------
    `reason`: [int] 默认为 2 , retrieve 标记

    `batch_size`: [int] btq client 一次从 queue 中读取的 message 数量, 默认 512

    `queue_name`: [string] btq queue name

    `queue_no`: [string] btq queue no. , 即 queue_name 为所有要读取的 queue 中的第几个 queue

    `sleep_for_wait_btq_ms`: [int] 当 btqueue 没有数据时 sleep 多少毫秒，默认 0

    `save_sign_id_to_attr`: [string] sign_id 存入的attr name

    `save_emb_to_attr`: [string] emb 存入的attr name

    调用示例
    ------
    ``` python
    .retrieve_photo_with_emb_from_model_update_btq(batch_size=1024,
                                          queue_name='exp_realtime_item_doc',
                                          save_sign_id_to_attr='u_pid',
                                          save_emb_to_attr='emb'
    )
    ```
    """
    self._add_processor(OfflineBTQMarmEmbRetriever(kwargs))
    return self

