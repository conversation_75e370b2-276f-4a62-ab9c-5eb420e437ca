#!/usr/bin/env python3
# coding=utf-8
"""
filename: user_reco_api_mixin.py
description: user_reco_common_leaf dynamic_json_config DSL intelligent builder,user_reco api mixin
author: x<PERSON>od<PERSON><EMAIL>
date: 2022-10-17 10:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .user_reco_retriever import *
from .user_reco_enricher import *

class UserRecoApiMixin(CommonLeafBaseMixin):
  """
  该 MiXin 包含用户推荐业务相关的 Processor
  - 维护人 xiaodongdong
  - 团队 社交挖掘组
  """

  def user_reco_retrieve_by_need_field_num_list(self, **kwargs):
    """
    UserRecoRemoteRetriever
    ------
    从 关系链中台服务 召回各类关系类型

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 100ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .user_reco_retrieve_by_need_field_num_list(
      need_field_num_list = [1, 19],
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 250,
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "u_friend_list_size", 
        "u_friend_list", 
        "u_newest_contact_list_size",
        "u_relation_score_list_size",
        "u_fof_list_size",
        "u_user_searched_list_size",
        "u_wechat_friend_list_size",
        "u_care_you_score_list_size",
        "u_contact_bidirection_list_size",
      ],
      output_item_attrs = [
        "item_type",
      	"f_intimate_score",
        "f_be_intimate_score",
        "common_friend_list",
        "common_friend_list_size",
        "max_common_friend_score",
        "sum_common_friennd_score",
        "max_score_common_friend",
      ],
    )
    ```
    """
    self._add_processor(UserRecoRemoteRetriever(kwargs))
    return self
  
  def user_reco_retrieve2_by_need_field_num_list(self, **kwargs):
    """
    UserRecoRemote2Retriever
    ------
    从 关系链中台服务 召回各类关系类型

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 100ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .user_reco_retrieve2_by_need_field_num_list(
      need_field_num_list = [1, 19],
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 250,
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "u_friend_list_size", 
        "u_friend_list", 
        "u_newest_contact_list_size",
        "u_relation_score_list_size",
        "u_fof_list_size",
        "u_user_searched_list_size",
        "u_wechat_friend_list_size",
        "u_care_you_score_list_size",
        "u_contact_bidirection_list_size",
      ],
      output_item_attrs = [
        "item_type",
      	"f_intimate_score",
        "f_be_intimate_score",
        "common_friend_list",
        "common_friend_list_size",
        "max_common_friend_score",
        "sum_common_friennd_score",
        "max_score_common_friend",
      ],
    )
    ```
    """
    self._add_processor(UserRecoRemote2Retriever(kwargs))
    return self

  def user_reco_retrieve3_by_need_field_num_list(self, **kwargs):
    """
    UserRecoRemote3Retriever
    ------
    从 关系链中台服务 召回各类关系类型

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 100ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .user_reco_retrieve3_by_need_field_num_list(
      need_field_num_list = [1, 19],
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 250,
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "u_friend_list_size", 
        "u_friend_list", 
        "u_newest_contact_list_size",
        "u_relation_score_list_size",
        "u_fof_list_size",
        "u_user_searched_list_size",
        "u_wechat_friend_list_size",
        "u_care_you_score_list_size",
        "u_contact_bidirection_list_size",
      ],
      output_item_attrs = [
        "item_type",
      	"f_intimate_score",
        "f_be_intimate_score",
        "common_friend_list",
        "common_friend_list_size",
        "max_common_friend_score",
        "sum_common_friennd_score",
        "max_score_common_friend",
      ],
    )
    ```
    """
    self._add_processor(UserRecoRemote3Retriever(kwargs))
    return self

  def user_reco_another_retrieve_by_need_field_num_list(self, **kwargs):
    """
    UserRecoAnotherRemoteRetriever
    ------
    根据请求用户的关键关系节点从 关系链中台服务 召回各类关系类型

    参数配置
    ------
    `reason_prefix`: [int] 必配项，召回原因前缀，该值加上中台关系编号得到最后的召回原因

    `need_field_num_list`: [list] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 100ms

    `another_user_id_name`: [string] 必配项，从本 common 属性中取请求用户的关键关系节点 uid 进行关系召回

    `another_user_recall_attr_prefix`: [string] 必配项，本路召回产生的 common 和 item 维度特征名的前缀
    
    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .user_reco_another_retrieve_by_need_field_num_list(
      need_field_num_list = [1, 19],
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 250,
      reason_prefix = 2000,
      another_user_id_name = "xxx",
      another_user_recall_attr_prefix = "yyy_", 
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "u_friend_list_size", 
        "u_friend_list", 
        "u_newest_contact_list_size",
        "u_relation_score_list_size",
        "u_fof_list_size",
        "u_user_searched_list_size",
        "u_wechat_friend_list_size",
        "u_care_you_score_list_size",
        "u_contact_bidirection_list_size",
      ],
      output_item_attrs = [
        "item_type",
      	"f_intimate_score",
        "f_be_intimate_score",
        "common_friend_list",
        "common_friend_list_size",
        "max_common_friend_score",
        "sum_common_friennd_score",
        "max_score_common_friend",
      ],
    )
    ```
    """
    self._add_processor(UserRecoAnotherRemoteRetriever(kwargs))
    return self

  def user_reco_retrieve_by_user_reco_api_grpc(self, **kwargs):
    """
    UserRecoApiRemoteRetriever
    ------
    从 用户推荐召回服务 召回各类关系类型

    参数配置
    ------
    `is_low_bifollow_request`: [int] 必配项，是否低双关请求，默认 -1 即采用上游传过来的值

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 15000

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，默认值 200ms

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .user_reco_retrieve_by_user_reco_api_grpc(
      remote_recall_limit = 15000,
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      is_low_bifollow_request = -1,
      input_common_attrs = [
        "user_reco_request",
      ], 
      output_common_attrs = [
        "user_reco_api_retriever_size", 
      ],
      output_item_attrs = [
        "item_type",
      ],
    )
    ```
    """
    self._add_processor(UserRecoApiRemoteRetriever(kwargs))
    return self

  def enrich_user_reco_profile_info_attr_by_kcc(self, **kwargs):
    """
    UserRecoAuthorInfoAttrEnricher
    ------
    用户推荐 用户和好友的用户画像基础特征 抽取。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_user_profile_attr_v2_by_kcc(
      input_common_attrs=["user_id"],
      output_common_attrs=[
        "u_province",
        "u_province_origin",
      ], 
      input_item_attrs=["item_type"], 
      output_item_attrs=[
        "is_same_province",
        "is_same_province_origin",
        "f_province",
        "f_province_origin",
      ],
    )
    ```
    """
    self._add_processor(UserRecoAuthorInfoAttrEnricher(kwargs))
    return self


  def enrich_item_xtr_by_user_reco_predict_service(self, **kwargs):
    """
    UserRecoPredictItemXtrEnricher
    ------
    调用用户推荐预测服务拿到各类 xtr

    参数配置
    ------
    `kess_service`: [string] 必配项，用户推荐预测服务的 kess 名称

    `model_version`: [string] 必配项，用户推荐预测服务的版本号，默认值 base

    `timeout_ms`: [int] 必配项，调用户推荐过滤服务的超时时间，默认值 200ms
  
    `is_low_bifollow_request`: [int] 必配项，是否低双关请求，默认值 -1， 即采用上游传过来的值

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_item_xtr_by_user_reco_predict_service(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      is_low_bifollow_request = -1,
      input_common_attrs = [
        "protal_int",
        "user_reco_request",
      ], 
      output_common_attrs = [
      ],
      output_item_attrs = [
        "ftr",
        "mtr",
        "bftr",
        "fcvr",
        "is_get_xtr_success",
      ],
    )
    ```
    """
    self._add_processor(UserRecoPredictItemXtrEnricher(kwargs))
    return self

  def enrich_mc_model_xtr_by_user_reco_api_service(self, **kwargs):
    """
    UserRecoMcModelXtrEnricher
    ------
    调用用户推荐粗排服务拿到各类 xtr

    参数配置
    ------
    `kess_service`: [string] 必配项，用户推荐预测服务的 kess 名称

    `model_version`: [string] 必配项，用户推荐预测服务的版本号，默认值 base

    `timeout_ms`: [int] 必配项，调用户推荐过滤服务的超时时间，默认值 200ms
  
    `is_low_bifollow_request`: [int] 必配项，是否低双关请求，默认值 -1， 即采用上游传过来的值

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_mc_model_xtr_by_user_reco_api_service(
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      is_low_bifollow_request = -1,
      param_for_common_leaf = 3,
      input_common_attrs = [
        "protal_int",
        "user_reco_request",
      ],
      input_item_attrs = [
        "all_reason_list",
      ],  
      output_common_attrs = [
      ],
      output_item_attrs = [
        "mc_model_score",
        "is_get_mc_xtr_success",
      ],
    )
    ```
    """
    self._add_processor(UserRecoMcModelXtrEnricher(kwargs))
    return self

  def enrich_user_profile_info_by_user_reco_request_pb(self, **kwargs):
    """
    UserRecoUserprofileAttrEnricher
    ------
    解析用户画像拿到各类特征

    参数配置
    ------
    `is_open_enricher_item_attr`: [int] 必配项， 1 表示开启解析 item 维度特征，默认值 0 不开启解析
  
    `is_open_enricher_common_attr`: [int] 必配项， 1 表示开启解析 common 维度特征，默认值 0 表示不开启解析

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_user_profile_info_by_user_reco_request_pb(
      is_open_enricher_item_attr = 1,
      is_open_enricher_common_attr = 1,
      input_common_attrs = [
        "protal_int",
        "user_reco_request",
      ],
      input_item_attrs = [
      ],  
      output_common_attrs = [
        "u_follow_farmer_tag",
      ],
      output_item_attrs = [
        "all_portal_reco_history_show_count",
        "current_portal_reco_history_show_count",
        "last_reco_history_show_ts",
        "last_reco_history_show_index",
      ],
    )
    ```
    """
    self._add_processor(UserRecoUserprofileAttrEnricher(kwargs))
    return self

  def enrich_outside_push_user_profile_info_by_krc_relation_info_pb(self, **kwargs):
    """
    OutsidePushUserprofileAttrEnricher
    ------
    解析用户画像拿到各类特征

    参数配置
    ------
    `is_open_enricher_photo_item_attr`: [int] 必配项， 1 表示开启解析 item 视频维度特征，默认值 0 不开启解析

    `is_open_enricher_user_item_attr`: [int] 必配项， 1 表示开启解析 item 用户维度特征，默认值 0 不开启解析

    `is_open_enricher_common_attr`: [int] 必配项， 1 表示开启解析 common 维度特征，默认值 0 表示不开启解析

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_outside_push_user_profile_info_by_krc_relation_info_pb(
      is_open_enricher_user_item_attr = 1,
      is_open_enricher_photo_item_attr = 0,
      is_open_enricher_common_attr = 0,
      input_common_attrs = [
      ],
      input_item_attrs = [
        "p_like_max_score_action_user_id",
        "p_follow_max_score_action_user_id",
      ],  
      output_common_attrs = [
      ],
      output_item_attrs = [
        "friend_moment_outside_push_ack_last_timestamp_31d",
        "friend_moment_outside_push_ack_count_31d",
        "friend_moment_outside_push_ack_click_last_timestamp_31d",
        "friend_moment_outside_push_ack_click_count_31d",
        "friend_moment_outside_push_ack_reverse_last_timestamp_31d",
        "friend_moment_outside_push_ack_reverse_count_31d",
        "friend_moment_outside_push_ack_click_reverse_last_timestamp_31d",
        "friend_moment_outside_push_ack_click_reverse_count_31d",
        "friend_moment_outside_push_server_show_last_timestamp_31d",
        "friend_moment_outside_push_server_show_count_31d",
      ],
    )
    ```
    """
    self._add_processor(OutsidePushUserprofileAttrEnricher(kwargs))
    return self


  def enrich_item_attr_by_user_reco_filter_service(self, **kwargs):
    """
    UserRecoFilterServiceItemAttrEnricher
    ------
    调用用户推荐过滤服务进行通用过滤

    参数配置
    ------
    `kess_service`: [string] 必配项，用户推荐过滤服务的 kess 名称

    `timeout_ms`: [int] 必配项，调用户推荐过滤服务的超时时间，默认值 40ms

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_item_attr_by_user_reco_filter_service(
      kess_service = "grpc_XXX",
      timeout_ms = 40,
      input_common_attrs = [
        "protal_int",
        "u_friend_list_size",
      ], 
      output_common_attrs = [
        "u_filter_service_response_candidate_size", 
      ],
      output_item_attrs = [
        "is_filtered_by_filter_service",
      ],
    )
    ```
    """
    self._add_processor(UserRecoFilterServiceItemAttrEnricher(kwargs))
    return self

  def enrich_com_attr_by_global_pymk_hate_filter_service(self, **kwargs):
    """
    GlobalPymkHateFilterServiceComAttrEnricher
    ------
    调用全局的 pymk 负反馈服务进行过滤

    参数配置
    ------
    `kess_service`: [string] 必配项，负反馈服务的 kess 名称

    `timeout_ms`: [int] 必配项，调负反馈服务的超时时间，默认值 60ms

    `input_common_attrs`: [list] 必配项，string list, 需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_com_attr_by_global_pymk_hate_filter_service(
      kess_service = "grpc_XXX",
      timeout_ms = 60,
      input_common_attrs = [
        "protal_int",
      ], 
      output_common_attrs = [
        "u_protal_global_pymk_hate_max_ts",
        "u_global_pymk_hate_author_ids_list_size",
        "u_global_pymk_hate_author_ids_list",
      ],
    )
    ```
    """
    self._add_processor(GlobalPymkHateFilterServiceComAttrEnricher(kwargs))
    return self

  def enrich_author_last_active_timestamp(self, **kwargs):
    """
    UserRecoLastActiveTimeEnricher
    ------
    获取recouid最近一次活跃的时间（userid维度）

    参数配置
    ------
  
    `output_item_attrs`: [list] 必配项，string list, 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_author_last_active_timestamp(
      output_item_attrs = [
        "last_app_start_timestamp",
        "is_active_today"
      ],
    )
    ```
    """
    self._add_processor(UserRecoLastActiveTimeEnricher(kwargs))
    return self

  def enrich_author_last_followed_timestamp(self, **kwargs):
    """
    UserRecoFollowedTodayEnricher
    ------
    获取uid最近一次被关注的时间（userid维度）

    参数配置
    ------
  
    `output_item_attrs`: [list] 必配项，string list, 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_author_last_followed_timestamp(
      output_item_attrs = [
        "last_followed_timestamp",
        "is_followed_today"
      ],
    )
    ```
    """
    self._add_processor(UserRecoFollowedTodayEnricher(kwargs))
    return self
  
  def enrich_user_reco_reason_ftr(self, **kwargs):
    """
    UserRecoReasonFtrEnricher
    ------
    基于reason获取对应的关注率ftr

    参数配置
    ------
  
    `input_item_attrs`: [list] 必配项，string list, 输入 common_attrs 列表
    `output_item_attrs`: [list] 必配项，string list, 输出 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_user_reco_reason_ftr(
      input_item_attrs = [
        "all_reason_list"
      ],
      output_item_attrs = [
        "reason_sum_ftr"
      ],
    )
    ```
    """
    self._add_processor(UserRecoReasonFtrEnricher(kwargs))
    return self
  
  def enrich_user_reco_author_info_attr_by_graph(self, **kwargs):
    """
    UserRecoGraphAuthorInfoAttrEnricher
    ------
    用户推荐 用户和好友的用户画像基础特征 基于图服务抽取。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_user_reco_author_info_attr_by_graph(
      input_common_attrs=["user_id"],
      output_common_attrs=[
        "u_province",
        "u_province_origin",
      ], 
      input_item_attrs=["item_type"], 
      output_item_attrs=[
        "is_same_province",
        "is_same_province_origin",
        "f_province",
        "f_province_origin",
      ],
    )
    ```
    """
    self._add_processor(UserRecoGraphAuthorInfoAttrEnricher(kwargs))
    return self
  
  def enrich_user_reco_cross_feature(self, **kwargs):
    """
    UserRecoCrossFeatureAttrEnricher
    ------
    用户推荐 共同好友交叉特征 获取。

    参数配置
    ------
    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `feature_name`: [string] 必配项，string, 本 processor 获取的特征名称

    `timeout_ms`: [int] 必配项，int, 本 processor 超时配置

    调用示例
    ------
    ``` python
    .enrich_user_reco_cross_feature(

      output_common_attrs=[], 
      output_item_attrs=[
        "friend_common_cnt_int"
      ],
      feature_name="friend_common_cnt_int",
      timeout_ms = 80
    )
    ```
    """
    self._add_processor(UserRecoCrossFeatureAttrEnricher(kwargs))
    return self
  
  def enrich_user_reco_fans_graph_feature(self, **kwargs):
    """
    UserRecoFansGraphEnricher
    ------
    用户推荐 共同好友交叉特征 获取。

    参数配置
    ------
    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `timeout_ms`: [int] 必配项，int, 本 processor 超时配置

    调用示例
    ------
    ``` python
    .enrich_user_reco_fans_graph_feature(
      output_common_attrs=[], 
      output_item_attrs=[
        "author_fan_follow_stack"
      ],
      timeout_ms=100
    )
    ```
    """
    self._add_processor(UserRecoFansGraphEnricher(kwargs))
    return self