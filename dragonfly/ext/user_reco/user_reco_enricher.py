#!/usr/bin/env python3
# coding=utf-8
"""
filename: user_reco_enricher.py
description: user_reco_common_leaf dynamic_json_config DSL intelligent builder, enricher module
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2022-10-31 10:15:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class UserRecoAuthorInfoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_reco_profile_info_attr_by_kcc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class UserRecoPredictItemXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_xtr_by_user_reco_predict_service"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("model_version"), str) and self._config["model_version"],
              "model_version 需为非空字符串")
    check_arg(isinstance(self._config.get("is_low_bifollow_request"), int) and self._config["is_low_bifollow_request"] >= -1,
              "is_low_bifollow_request 需为大于等于 -1 的整数")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class UserRecoMcModelXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_mc_model_xtr_by_user_reco_api_service"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("param_for_common_leaf"), int) and self._config["param_for_common_leaf"] > 0,
              "param_for_common_leaf 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("is_low_bifollow_request"), int) and self._config["is_low_bifollow_request"] >= -1,
              "is_low_bifollow_request 需为大于等于 -1 的整数")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class UserRecoUserprofileAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_profile_info_by_user_reco_request_pb"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("is_open_enricher_item_attr"), int) and self._config["is_open_enricher_item_attr"] >= 0,
              "is_open_enricher_item_attr 需为>= 0 的整数")
    check_arg(isinstance(self._config.get("is_open_enricher_common_attr"), int) and self._config["is_open_enricher_common_attr"] >= 0,
              "is_open_enricher_common_attr 需为>= 0 的整数")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class OutsidePushUserprofileAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_outside_push_user_profile_info_by_krc_relation_info_pb"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("is_open_enricher_photo_item_attr"), int) and self._config["is_open_enricher_photo_item_attr"] >= 0,
              "is_open_enricher_photo_item_attr 需为>= 0 的整数")
    check_arg(isinstance(self._config.get("is_open_enricher_user_item_attr"), int) and self._config["is_open_enricher_user_item_attr"] >= 0,
              "is_open_enricher_user_item_attr 需为>= 0 的整数")
    check_arg(isinstance(self._config.get("is_open_enricher_common_attr"), int) and self._config["is_open_enricher_common_attr"] >= 0,
              "is_open_enricher_common_attr 需为>= 0 的整数")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class UserRecoFilterServiceItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_attr_by_user_reco_filter_service"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class GlobalPymkHateFilterServiceComAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_com_attr_by_global_pymk_hate_filter_service"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    if self._config.get("output_item_attrs"):
      check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")


class UserRecoLastActiveTimeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_author_last_active_timestamp"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    if self._config.get("output_item_attrs"):
      check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserRecoFollowedTodayEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_author_last_followed_timestamp"
  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    if self._config.get("output_item_attrs"):
      check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserRecoReasonFtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_reco_reason_ftr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    if self._config.get("output_common_attrs"):
      check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("input_item_attrs"):
      check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    if self._config.get("output_item_attrs"):
      check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserRecoGraphAuthorInfoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_reco_author_info_attr_by_graph"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class UserRecoCrossFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_reco_cross_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs


  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("feature_name"), str) and self._config["feature_name"],
              "feature_name 需为 string 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    
class UserRecoFansGraphEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_user_reco_fans_graph_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs


  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    

    