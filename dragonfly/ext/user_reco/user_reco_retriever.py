#!/usr/bin/env python3
# coding=utf-8
"""
filename: user_reco_retriever.py
description: user_reco_common_leaf dynamic_json_config DSL intelligent builder, retriever module
author: <PERSON><PERSON>od<PERSON><PERSON>@kuaishou.com
date: 2022-10-17 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_common_attrs
from ...common_leaf_processor import LeafRetriever

class UserRecoRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_reco_retrieve_by_need_field_num_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("remote_recall_limit"), int) and self._config["remote_recall_limit"] > 0,
              "remote_recall_limit 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("request_biz_type_for_relation_api"), str) and self._config["request_biz_type_for_relation_api"],
              "request_biz_type_for_relation_api 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("need_field_num_list"), list), "need_field_num_list 需为 list 类型")

class UserRecoAnotherRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_reco_another_retrieve_by_need_field_num_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("remote_recall_limit"), int) and self._config["remote_recall_limit"] > 0,
              "remote_recall_limit 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("request_biz_type_for_relation_api"), str) and self._config["request_biz_type_for_relation_api"],
              "request_biz_type_for_relation_api 需为非空字符串")
    check_arg(isinstance(self._config.get("another_user_id_name"), str) and self._config["another_user_id_name"],
              "another_user_id_name 需为非空字符串")
    check_arg(isinstance(self._config.get("another_user_recall_attr_prefix"), str) and self._config["another_user_recall_attr_prefix"],
              "another_user_recall_attr_prefix 需为非空字符串")  
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason_prefix"), int) and self._config["reason_prefix"] > 0,
              "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("need_field_num_list"), list), "need_field_num_list 需为 list 类型")

class UserRecoApiRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_reco_retrieve_by_user_reco_api_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("remote_recall_limit"), int) and self._config["remote_recall_limit"] > 0,
              "remote_recall_limit 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("is_low_bifollow_request"), int) and self._config["is_low_bifollow_request"] >= -1,
              "is_low_bifollow_request 需为大于等于 -1 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class UserRecoRemote2Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_reco_retrieve2_by_need_field_num_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("remote_recall_limit"), int) and self._config["remote_recall_limit"] > 0,
              "remote_recall_limit 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("request_biz_type_for_relation_api"), str) and self._config["request_biz_type_for_relation_api"],
              "request_biz_type_for_relation_api 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("need_field_num_list"), list), "need_field_num_list 需为 list 类型")

class UserRecoRemote3Retriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_reco_retrieve3_by_need_field_num_list"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("remote_recall_limit"), int) and self._config["remote_recall_limit"] > 0,
              "remote_recall_limit 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("request_biz_type_for_relation_api"), str) and self._config["request_biz_type_for_relation_api"],
              "request_biz_type_for_relation_api 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("need_field_num_list"), list), "need_field_num_list 需为 list 类型")
