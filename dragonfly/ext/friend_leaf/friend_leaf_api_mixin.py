#!/usr/bin/env python3
# coding=utf-8
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .friend_leaf_enricher import *

class FriendLeafApiMixin(CommonLeafBaseMixin):
    def friend_parse_user_info_enricher(self, **kwargs):
        """
        FriendParseUserInfoEnricher
        ------
        将 user_info 解析到 common attr

        参数配置
        ------
        `user_info_path` : [string] 从 parse_protobuf_from_string() 得到的pb字符串
        `is_prepare` : [boolean] 为 True 则只解析基本的 common attr，用于召回。默认为 False，将 Author 和 UA 交叉特征解析为 item attr。

        调用示例
        ------
        ```
        flow.friend_parse_user_info_enricher(
            user_info_path = "user_info_pb",
            is_prepare = True
        )
        ```
        """
        self._add_processor(FriendParseUserInfoEnricher(kwargs))

        return self

    def friend_rank_feature_enricher(self, **kwargs):
        """
        FriendRankFeatureEnricher
        ------
        将 user_info 解析到 item attr

        参数配置
        ------

        调用示例
        ------
        ```
        flow.friend_rank_feature_enricher(
            user_info_path = "user_info_pb",
        )
        ```
        """
        self._add_processor(FriendRankFeatureEnricher(kwargs))

        return self
