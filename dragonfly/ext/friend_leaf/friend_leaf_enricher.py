#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import LeafEnricher
from ...common_leaf_util import strict_types

class FriendParseUserInfoEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "friend_parse_user_info_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = {
            "item_type",
            "timestamp",
            "photo_id",
            "author_id",
        }
        return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add("user_id")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = {
            "llsid",
            "userActivity",
            "uFollowPageProductType",
            "device_id_hash",
            "leaf_request_time",
            "leaf_time_ms",
            "uIsTrueNewUser",
            "uIsRefluxUser",
            "dMod",
            "requestId",
            "uFollowCount",
            "uFollowAuthorList",
            "uHour",
            "uWeekday",
            "uIsCommonLeaf",
            "social_degree",
            "channel_id",
            "friend_cnt",
            "friend_tab_author_list",
            "friend_list",
            "friend_related_pids",
            "friend_related_types",
            "today_interact_cnt_follow",
            "today_interact_cnt_friend",
            "today_interact_cnt_pymk",
            "today_interact_cnt_related",
            "interact_cnt_follow",
            "interact_cnt_friend",
            "interact_cnt_pymk",
            "interact_cnt_related",
        }
        return attrs
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = {
            "pFriendRelatedInfoStr",
            "is_friend_related_photo",
            "is_pymk_author",
            "is_friend_tab_pymk",
            "is_follow_author",
            "is_single_follow_friend_author",
            "is_friend_author",
            "is_high_quality_author",
            "is_favorite_friend_author",
            "is_read_filter",

            "photo_age_minute",
            "allpage_ua_like_cnt_28d",
            "allpage_ua_comment_cnt_28d",
            "allpage_ua_complete_cnt_28d",
            "allpage_ua_emp_ltr_28d",
            "allpage_ua_emp_lvtr_28d",
            "allpage_ua_emp_cmtr_28d",
            "colossus_ua_positive_interact_cnt",
            "colossus_ua_today_interact_cnt",
            "colossus_ua_emp_ltr",
            "colossus_ua_emp_cmtr",
            "colossus_ua_emp_ltr_14d",
            "colossus_ua_emp_cmtr_14d",
            "colossus_ua_emp_pymk_ptr",
            "colossus_ua_emp_pymk_wtr",
            "colossus_ua_emp_pymk_ptr_14d",
            "colossus_ua_emp_pymk_wtr_14d",
        }
        return attrs

class FriendRankFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "friend_rank_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = {
            "timestamp_ms",
            "city_id",
            "visit_net",
            "visit_channel",
            "gender",
            "age_segment",
            "is_douyin",
            "mod_price",
            "u_app_list",
            "life_time_status",
            "upload_in_recent_hours",
            "author_list",
            "kgnn_samples_1st",
            "relation_colossus_list",
        }
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = {
            "item_type",
            "photo_type",
            "author_id",
            "author_age",
            "reco_gender_first",
            "city_id",
            "device_id",
            "follow_count",
            "fans_count",
            "friends_count",
            "music_id",
            "magic_face_id",
            "photo_cover_cluster_id",
            "duration_ms",
            "timestamp",
            "photo_like_count",
            "photo_neg_count",
        }
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = {
            "a_id",
            "a_age_segment",
            "a_gender",
            "a_city",
            "a_did",
            "a_follow_cnt",
            "a_fans_cnt",
            "a_friend_cnt",
            "a_publish_cnt",
            # "a_hetu_tag",
            # "relation_author_stats",
            # "recommender_user_id",
            # "r_follow_day",

            "p_exp_tag",
            "p_id",
            "p_type",
            "p_music_id",
            "p_magic_face_id",
            "p_photo_cover_cluster_id",
            "p_duration_ms",
            "p_upload_ms",
            "p_all_page_like_cnt",
            "p_all_page_neg_cnt",
            "ua_weighted_intimated_score",
            "author_intimate_score",
            "common_friend_count",
            # "p_hetu_tag",
            # "p_quality_score",
            # "up_photo_status",
        }
        return attrs
