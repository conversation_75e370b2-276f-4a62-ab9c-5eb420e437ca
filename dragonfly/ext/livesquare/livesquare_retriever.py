#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class LivesquareFmRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livesquare_fm_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("src_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("dest_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    attrs.update(self.extract_dynamic_params(self._config.get("relevance_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("retr_max_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_aid")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_dist_control")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config.get("output_attr"))
    attrs.add(self._config["user_emb_config"].get("kuiba_user_attrs_from_attr"))
    attrs.update(self._config["user_emb_config"].get("user_emb_service_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("service_name"), str) and self._config["service_name"], "service_name 需为非空字符串")
    check_arg(isinstance(self._config.get("src_bucket"), str) and self._config["src_bucket"], "src_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("dest_bucket"), str) and self._config["dest_bucket"], "dest_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("top_k"), int) or isinstance(self._config.get("top_k"), str), "top_k 格式不正确")
    check_arg(isinstance(self._config.get("relevance_threshold"), float) or isinstance(self._config.get("relevance_threshold"), str), "relevance_threshold 格式不正确")
    check_arg(isinstance(self._config.get("retr_max_num"), int) or isinstance(self._config.get("retr_max_num"), str), "app_nn_retr_max_num 格式不正确")
    check_arg(isinstance(self._config.get("use_aid"), int) or isinstance(self._config.get("use_aid"), str), "use_aid 格式不正确")
    check_arg(isinstance(self._config.get("enable_dist_control"), int) or isinstance(self._config.get("enable_dist_control"), str), "enable_dist_control 格式不正确")
    check_arg(isinstance(self._config.get("timeout_ms"), int) or isinstance(self._config.get("timeout_ms"), str), "timeout_ms 格式不正确")
    check_arg(isinstance(self._config.get("output_attr"), str) and self._config["output_attr"], "output_attr 需为非空字符串")
    if (self._config.get("need_fetch_user_emb") != None):
      check_arg(isinstance(self._config.get("user_emb_config"), dict), "user_emb_config 需为 dict 类型")


class LivesquareAppNNRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livesquare_app_nn_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("src_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("dest_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    attrs.update(self.extract_dynamic_params(self._config.get("relevance_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("retr_max_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("retr_use_aid")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_dist_control")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config.get("output_attr"))
    attrs.add(self._config["user_emb_config"].get("kuiba_user_attrs_from_attr"))
    attrs.update(self._config["user_emb_config"].get("user_emb_layer_name"))
    attrs.update(self._config["user_emb_config"].get("user_emb_service_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("service_name"), str) and self._config["service_name"], "service_name 需为非空字符串")
    check_arg(isinstance(self._config.get("src_bucket"), str) and self._config["src_bucket"], "src_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("dest_bucket"), str) and self._config["dest_bucket"], "dest_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("top_k"), int) or isinstance(self._config.get("top_k"), str), "top_k 格式不正确")
    check_arg(isinstance(self._config.get("relevance_threshold"), float) or isinstance(self._config.get("relevance_threshold"), str), "relevance_threshold 格式不正确")
    check_arg(isinstance(self._config.get("retr_max_num"), int) or isinstance(self._config.get("retr_max_num"), str), "app_nn_retr_max_num 格式不正确")
    check_arg(isinstance(self._config.get("retr_use_aid"), int) or isinstance(self._config.get("retr_use_aid"), str), "app_nn_retr_use_aid 格式不正确")
    check_arg(isinstance(self._config.get("enable_dist_control"), int) or isinstance(self._config.get("enable_dist_control"), str), "enable_dist_control 格式不正确")
    check_arg(isinstance(self._config.get("timeout_ms"), int) or isinstance(self._config.get("timeout_ms"), str), "timeout_ms 格式不正确")
    check_arg(isinstance(self._config.get("output_attr"), str) and self._config["output_attr"], "output_attr 需为非空字符串")
    if (self._config.get("need_fetch_user_emb") != None):
      check_arg(isinstance(self._config.get("user_emb_config"), dict), "user_emb_config 需为 dict 类型")


class LivesquarePhotoNNRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livesquare_photo_nn_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("src_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("dest_bucket"), check_format=False))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    attrs.update(self.extract_dynamic_params(self._config.get("relevance_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("retr_max_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_dist_control")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config.get("output_attr"))
    attrs.add(self._config["user_emb_config"].get("kuiba_user_attrs_from_attr"))
    attrs.update(self._config["user_emb_config"].get("user_emb_layer_name"))
    attrs.update(self._config["user_emb_config"].get("user_emb_service_name"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("service_name"), str) and self._config["service_name"], "service_name 需为非空字符串")
    check_arg(isinstance(self._config.get("src_bucket"), str) and self._config["src_bucket"], "src_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("dest_bucket"), str) and self._config["dest_bucket"], "dest_bucket 需为非空字符串")
    check_arg(isinstance(self._config.get("top_k"), int) or isinstance(self._config.get("top_k"), str), "top_k 格式不正确")
    check_arg(isinstance(self._config.get("relevance_threshold"), float) or isinstance(self._config.get("relevance_threshold"), str), "relevance_threshold 格式不正确")
    check_arg(isinstance(self._config.get("retr_max_num"), int) or isinstance(self._config.get("retr_max_num"), str), "app_nn_retr_max_num 格式不正确")
    check_arg(isinstance(self._config.get("enable_dist_control"), int) or isinstance(self._config.get("enable_dist_control"), str), "enable_dist_control 格式不正确")
    check_arg(isinstance(self._config.get("timeout_ms"), int) or isinstance(self._config.get("timeout_ms"), str), "timeout_ms 格式不正确")
    check_arg(isinstance(self._config.get("output_attr"), str) and self._config["output_attr"], "output_attr 需为非空字符串")
    if (self._config.get("need_fetch_user_emb") != None):
      check_arg(isinstance(self._config.get("user_emb_config"), dict), "user_emb_config 需为 dict 类型")

