#!/usr/bin/env python3
# coding=utf-8
"""
filename: livesquare_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,livesquare api mixin
author: <EMAIL>
date: 2021-03-18 16:54:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .livesquare_retriever import *

class livesquareApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 livesquare 相关的 Processor 接口
  """

  def livesquare_fm_retrieve(self, **kwargs):
    """
    LiveStreamFmRetriever
    ------
    直播 leaf 中的 fm 召回

    参数配置
    ------
    `user_emb_service_name`: [string] [动态参数] 必配. 计算 user embedding 的 service
    `fetch_user_emb_timeout_ms`: [int] [动态参数] 必配. 获取 user embedding 的 超时时间
    `service_name`: [string] [动态参数] 必配. 召回服务的 service name
    `src_bucket`: [string] [动态参数] 必配. 召回使用的 embedding 的来源
    `dest_bucket`: [string] [动态参数] 必配. 做 embedding 匹配时匹配内容所在的桶
    `top_k`: [int] [动态参数] 必配. 限制下游召回服务的单次召回数量
    `relevance_threshold`: [float] [动态参数] 必配. 距离当前 embedding 距离小于这个阈值的 item 会被淘汰. 与 enable_dist_control 配合使用
    `retr_max_num`: [int] [动态参数] 必配. 限制返回 item 的数量
    `use_aid`: [int] [动态参数] 必配. 是否使用 live_id 
    `enable_dist_control`: [int]. 必配. 是否淘汰和当前 item 距离过近的召回结果
    `timeout_ms`: [int] 必配. 调用召回服务时的超时时间
    `output_attr`: [string] 必配. 召回的 author_id 输出到的 int list common attr

    调用示例
    ------
    ``` python
    self.livesquare_fm_retrieve(
      user_emb_config = {
        "user_emb_service_name": "{{fm_retr_service_name_nowl}}",
        "fetch_user_emb_timeout_ms": "{{fm_retr_time_out_ms}}",
      },
      service_name = "{{fm_retr_service_name_nowl}}",
      src_bucket = "{{fm_retr_live_bucket_name}}",
      dest_bucket = "{{fm_retr_live_bucket_name}}",
      top_k = "{{fm_retr_search_num}}",
      relevance_threshold="{{fm_retr_distance_threshold}}",
      retr_max_num="{{fm_retr_max_num}}",
      use_aid="{{fm_retr_use_aid}}",
      enable_dist_control = "{{sidebar_enable_embedding_retr_distance_threshold}}",
      timeout_ms = "{{fm_retr_time_out_ms}}",
      output_attr = "author_list",
    )
    ```
    """
    self._add_processor(LivesquareFmRetriever(kwargs))
    return self

  def livesquare_app_nn_retrieve(self, **kwargs):
    """
    LivesquareAppNNRetriever
    ------
    直播 leaf 中使用 ItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `user_emb_layer_name`: [string] 必配. 获取 user embedding 的层
    `user_emb_service_name`: [string] [动态参数] 必配. 计算 user embedding 的 service
    `kuiba_user_attrs_from_attr`: [string] 必配. 存储kuiba user 属性的 common attr
    `fetch_user_emb_timeout_ms`: [int] [动态参数] 必配. 获取 user embedding 的 超时时间
    `service_name`: [string] [动态参数] 必配. 召回服务的 service name
    `src_bucket`: [string] [动态参数] 必配. 召回使用的 embedding 的来源
    `dest_bucket`: [string] [动态参数] 必配. 做 embedding 匹配时匹配内容所在的桶
    `top_k`: [int] [动态参数] 必配. 限制下游召回服务的单次召回数量
    `relevance_threshold`: [float] [动态参数] 必配. 距离当前 embedding 距离小于这个阈值的 item 会被淘汰. 与 enable_dist_control 配合使用
    `retr_max_num`: [int] [动态参数] 必配. 限制返回 item 的数量
    `use_aid`: [int] [动态参数] 必配. 是否使用 live_id 
    `enable_dist_control`: [int]. 必配. 是否淘汰和当前 item 距离过近的召回结果
    `timeout_ms`: [int] 必配. 调用召回服务时的超时时间
    `output_attr`: [string] 必配. 召回的 author_id 输出到的 int list common attr

    调用示例
    ------
    ``` python
    self.livesquare_app_nn_retrieve(
      user_emb_config = {
        "user_emb_layer_name": "user_embedding",
        "user_emb_service_name": "{{app_nn_retr_pred_service_name}}",
        "kuiba_user_attrs_from_attr": "kuiba_user_attr_names",
        "fetch_user_emb_timeout_ms" : 50
      },
      service_name = "{{app_nn_retr_ann_service_name}}",
      src_bucket = "{{app_nn_retr_live_bucket_name}}",
      dest_bucket = "{{app_nn_retr_live_bucket_name}}",
      top_k = "{{app_nn_retr_search_num}}",
      relevance_threshold="{{app_nn_retr_distance_threshold}}",
      retr_max_num="{{app_nn_retr_max_num}}",
      use_aid="{{app_nn_retr_use_aid}}",
      enable_dist_control = "{{sidebar_enable_embedding_retr_distance_threshold}}",
      timeout_ms = "{{app_nn_retr_time_out_ms}}",
      output_attr = "author_list",
    )
    ```
    """
    self._add_processor(LivesquareAppNNRetriever(kwargs))
    return self

  def livesquare_photo_nn_retrieve(self, **kwargs):
    """
    LivesquarePhotoNNRetriever
    ------
    直播 leaf 中使用 ItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `user_emb_layer_name`: [string] 必配. 获取 user embedding 的层
    `user_emb_service_name`: [string] [动态参数] 必配. 计算 user embedding 的 service
    `kuiba_user_attrs_from_attr`: [string] 必配. 存储kuiba user 属性的 common attr
    `fetch_user_emb_timeout_ms`: [int] [动态参数] 必配. 获取 user embedding 的 超时时间
    `service_name`: [string] [动态参数] 必配. 召回服务的 service name
    `src_bucket`: [string] [动态参数] 必配. 召回使用的 embedding 的来源
    `dest_bucket`: [string] [动态参数] 必配. 做 embedding 匹配时匹配内容所在的桶
    `top_k`: [int] [动态参数] 必配. 限制下游召回服务的单次召回数量
    `relevance_threshold`: [float] [动态参数] 必配. 距离当前 embedding 距离小于这个阈值的 item 会被淘汰. 与 enable_dist_control 配合使用
    `retr_max_num`: [int] [动态参数] 必配. 限制返回 item 的数量
    `enable_dist_control`: [int]. 必配. 是否淘汰和当前 item 距离过近的召回结果
    `timeout_ms`: [int] 必配. 调用召回服务时的超时时间
    `output_attr`: [string] 必配. 召回的 author_id 输出到的 int list common attr

    调用示例
    ------
    ``` python
    self.livesquare_photo_nn_retrieve(
      user_emb_config = {
        "user_emb_layer_name": "nn_user_embedding",
        "user_emb_service_name": "{{photo_nn_retr_pred_service_name}}",
        "kuiba_user_attrs_from_attr": "kuiba_user_attr_names",
        "fetch_user_emb_timeout_ms" : 50
      },
      service_name = "{{photo_nn_retr_ann_service_name_nowl}}",
      src_bucket = "nn_item",
      dest_bucket = "nn_item",
      top_k = "{{photo_nn_retr_ann_search_num}}",
      relevance_threshold="{{photo_nn_retr_distance_threshold}}",
      retr_max_num="{{photo_nn_retr_final_num}}",
      enable_dist_control = "{{sidebar_enable_embedding_retr_distance_threshold}}",
      timeout_ms = "{{photo_nn_retr_ann_timeout_ms}}",
      output_attr = "author_list",
    )
    ```
    """
    self._add_processor(LivesquarePhotoNNRetriever(kwargs))
    return self

