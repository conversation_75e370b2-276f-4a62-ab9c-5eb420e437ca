#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, check_arg, extract_attr_names, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher

class CommonRecoColossusInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_interest"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_interest_attr"])
    ret.add(self._config["output_explore_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["candidate_uid_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonRecoColossusInterestOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_interest_opt"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_exploit_attr"])
    ret.add(self._config["output_explore_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["candidate_ids_size_attr"])
    for key in ['top_hetu_cnt_attr', 'num_per_tag_attr', 'num_per_top_tag_attr', 'ensmeble_weight_attr']:
      if self._config.get(key) is not None:
        ret.add(self._config.get(key))
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonRecoLongtermInterestTriggerGenerateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "longterm_interest_trigger_generate"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_trigger_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True


class NrGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_gsu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["target_tag_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["output_sign_attr"])
    return ret

class CommonKgnnSamplePairEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kgnn_sample_pair"

  @strict_types
  def is_async(self) -> bool:
    return True

class CommonRecoCandidateUserCrossFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "candidate_user_cross_feature"

class CommonRecoRefluxEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_reflux"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["slots_attr"])
    ret.add(self._config["signs_attr"])
    ret.add(self._config["filter_slots_attr"])
    ret.add(self._config["user_info_str"])
    return ret
  
  @strict_types
  def is_async(self) -> bool:
    return True

class CommonLinearEsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "linear_es"
  
  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    check_arg(formula_version in (0, 1, 2), f"{self.get_type_alias()} 的 formula_version 配置只能为 0 或 1 或 2")
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["regulator", "smooth", "cliff_ratio", "cliff_height"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      save_score_to = channel.get("save_score_to")
      if save_score_to:
        attrs.add(save_score_to)
    attrs.add(self._config["output_attr"])
    return attrs

class TowerPredictFloatDotProductXtrAttrEnricherCross(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_dot_product_xtr_cross"

  @strict_types
  def load_from_item_context(self) -> bool:
    return self._config.get("load_from_item_context", False)

  @strict_types
  def save_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False) or self._config.get("output_type", 0) in [1,3]

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["common_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["miss_embedding_mark"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.save_to_item_context():
      for key in ["pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.save_to_item_context():
      if self._config["output_type"] == 1:
        for key in ["pxtr_value_attr"]:
          if key in self._config:
            ret.add(self._config[key])
      elif self._config["output_type"] == 3:
        for key in self._config["predict_labels"]:
          ret.add(key)
    return ret

class CausalRandomEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "causal_random_enricher"
    
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['rand_attr'])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["param_attrs"])
    return ret

class BuildProtoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_proto_enricher"
    
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['output_common_attr'])
    return ret

class CommonMerchantSliceFeatureColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_slice_feature_colossus_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr", ""))
    ret.add(self._config.get("aid_list_attr", ""))
    ret.add(self._config.get("pid_list_attr", ""))
    ret.add(self._config.get("request_time_attr", ""))
    ret.add(self._config.get("enable_filter_request_time_attr", ""))
    ret.add(self._config.get("filter_offset_ms_attr", ""))
    return ret

class CommonMerchantSliceFeatureColossusOptEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_slice_feature_colossus_opt_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr", ""))
    ret.add(self._config.get("aid_list_attr", ""))
    ret.add(self._config.get("pid_list_attr", ""))
    ret.add(self._config.get("request_time_attr", ""))
    ret.add(self._config.get("category_click_attr", ""))
    ret.add(self._config.get("timestamp_click_attr", ""))
    ret.add(self._config.get("category_order_attr", ""))
    ret.add(self._config.get("timestamp_order_attr", ""))
    ret.add(self._config.get("enable_filter_request_time_attr", ""))
    ret.add(self._config.get("filter_offset_ms_attr", ""))
    ret.add(self._config.get("slice_feature_limit_size_attr", ""))
    return ret

class CommonMerchantSliceFeatureColossusOptV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_slice_feature_colossus_opt_v2_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr", ""))
    ret.add(self._config.get("aid_list_attr", ""))
    ret.add(self._config.get("pid_list_attr", ""))
    ret.add(self._config.get("request_time_attr", ""))
    ret.add(self._config.get("category_click_attr", ""))
    ret.add(self._config.get("timestamp_click_attr", ""))
    ret.add(self._config.get("category_order_attr", ""))
    ret.add(self._config.get("timestamp_order_attr", ""))
    ret.add(self._config.get("cart_cate1_list_attr", ""))
    ret.add(self._config.get("cart_cate2_list_attr", ""))
    ret.add(self._config.get("cart_cate3_list_attr", ""))
    ret.add(self._config.get("enable_filter_request_time_attr", ""))
    ret.add(self._config.get("filter_offset_ms_attr", ""))
    ret.add(self._config.get("slice_feature_limit_size_attr", ""))
    return ret

class CommonMerchantUserPhotoFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_user_photo_feature_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr", ""))
    ret.add(self._config.get("leaf_request_time_attr", ""))
    ret.add(self._config.get("common_prefix_attr", ""))
    return ret

class CausalLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "causal_log_enricher"
    
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['slots_attr'])
    ret.add(self._config['signs_attr'])
    ret.add(self._config['param_attr'])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['log_attr'])
    return ret

class NrPushEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_push_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_slot_attr"])
    ret.add(self._config["output_sign_attr"])
    return ret

class NrRecoWeightedSumEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_nr_weighted_sum"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      for key in ["weight"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
      if channel.get("pow", None):
        attrs.update(self.extract_dynamic_params(channel.get("pow")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }


class NrRecoListToStringEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "list_to_string"

  @strict_types
  def _check_config(self) -> None:
    if "attrs" not in self._config:
      raise ValueError("缺少 attrs 配置")
    if "output_attr" not in self._config:
      raise ValueError("缺少 output_attr 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("attrs", []))

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr"))
    return attrs

class NrRecoReplaceMockUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "replace_mock_user_info"

  @strict_types
  def _check_config(self) -> None:
    if "input_user_info_pb" not in self._config:
      raise ValueError("缺少 input_user_info_pb 配置")
    if "replace_user_id_attr" not in self._config:
      raise ValueError("缺少 replace_user_id_attr 配置")
    if "replace_device_id_attr" not in self._config:
      raise ValueError("缺少 replace_device_id_attr 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "input_user_info_pb" in self._config:
      attrs.add(self._config["input_user_info_pb"])
    if "replace_user_id_attr" in self._config:
      attrs.add(self._config["replace_user_id_attr"])
    if "replace_device_id_attr" in self._config:
      attrs.add(self._config["replace_device_id_attr"])
    return attrs

class NrVectorOperationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "vector_operation"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    check_arg(self._config.get("vector_a"), f"{self.get_type_alias()} 缺少 vector_a 配置")
    ret.add(self._config["vector_a"])
    if "scalar_a" in self._config:
      ret.update(self.extract_dynamic_params(self._config.get("scalar_a")))
    if "vector_b" in self._config:
      ret.add(self._config["vector_b"])
    if "scalar_b" in self._config:
      ret.update(self.extract_dynamic_params(self._config.get("scalar_b")))
    check_arg(self._config.get("operator"), f"{self.get_type_alias()} 缺少 operator 配置")
    if self._config.get("operator") not in ["+", "-", "*", "/", "&", "|", "^", "pow", "<<", ">>"]:
      raise ValueError('operator 仅支持四则运算（"+", "-", "*", "/"）, 位运算（"&", "|", "^", "<<", ">>"），指数运算（"pow"）')
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_vector"), f"{self.get_type_alias()} 缺少 output_vector 配置")
    ret.add(self._config["output_vector"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class NrCalcFractileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_calc_fractile"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    check_arg(self._config.get("common_attrs"), f"{self.get_type_alias()} 缺少 common_attrs 配置")
    common_attrs = self._config.get("common_attrs", [])
    if len(common_attrs) <= 0:
      raise ValueError("common_attrs 不能为空")
    attrs = set(common_attrs)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_fractile_prefix"), f"{self.get_type_alias()} 缺少 output_fractile_prefix 配置")
    prefix = self._config.get("output_fractile_prefix")
    for attr in self._config.get("common_attrs", []):
      ret.add(prefix + attr)
    return ret
  
  @strict_types
  def is_async(self) -> bool:
    return False


class NrHetuCostDistributeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "max_cost_distribute"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("max_cost_quota"), f"{self.get_type_alias()} 缺少 max_cost_quota 配置")
    check_arg(self._config.get("photo_cost_list"), f"{self.get_type_alias()} 缺少 photo_cost_list 配置")
    check_arg(self._config.get("photo_score_list"), f"{self.get_type_alias()} 缺少 photo_score_list 配置")
    ret.add(self._config["max_cost_quota"])
    ret.update(self.extract_dynamic_params(self._config.get("photo_cost_list")))
    ret.update(self.extract_dynamic_params(self._config.get("photo_score_list")))
    return ret
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_common_attr"), f"{self.get_type_alias()} 缺少 output_common_attr 配置")
    ret.add(self._config["output_common_attr"])
    return ret
  @strict_types
  def is_async(self) -> bool:
    return False

class NrDebiasScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_debias_score"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for dimension in self._config["dimensions"]:
      check_arg(dimension.get("item_attr"), f"{self.get_type_alias()} item_attr 配置为空")
      attrs.add(dimension.get("item_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for dimension in self._config["dimensions"]:
      check_arg(dimension.get("confidence"), f"{self.get_type_alias()} confidence 配置为空")
      attrs.update(self.extract_dynamic_params(dimension.get("confidence")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("debiased_score"), f"{self.get_type_alias()} 缺少 debiased_score 配置")
    ret.add(self._config["debiased_score"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class NrParseColossusItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_parse_colossus_item_enricher"

  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("colossus_item_attr"), f"{self.get_type_alias()} 缺少 colossus_item_attr 配置")
    ret.add(self._config["colossus_item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_pid_list_attr"), f"{self.get_type_alias()} 缺少 output_pid_list_attr 配置")
    ret.add(self._config["output_pid_list_attr"])
    if ("output_aid_list_attr" in self._config):
        attr_name = self._config["output_aid_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_ts_list_attr" in self._config):
        attr_name = self._config["output_ts_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_play_time_list_attr" in self._config):
        attr_name = self._config["output_play_time_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_duration_list_attr" in self._config):
        attr_name = self._config["output_duration_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

    
class NrColossusInterestV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_colossus_interest_v2"

  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("colossus_item_attr"), f"{self.get_type_alias()} 缺少 colossus_item_attr 配置")
    ret.add(self._config["colossus_item_attr"])
    check_arg(self._config.get("candidate_uids_attr"), f"{self.get_type_alias()} 缺少 candidate_uids_attr 配置")
    ret.add(self._config["candidate_uids_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    check_arg(self._config.get("output_pid_list_attr"), f"{self.get_type_alias()} 缺少 output_pid_list_attr 配置")
    ret.add(self._config["output_pid_list_attr"])
    if ("output_aid_list_attr" in self._config):
        attr_name = self._config["output_aid_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_ts_list_attr" in self._config):
        attr_name = self._config["output_ts_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_play_time_list_attr" in self._config):
        attr_name = self._config["output_play_time_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    if ("output_duration_list_attr" in self._config):
        attr_name = self._config["output_duration_list_attr"];
        if isinstance(attr_name, str) and len(attr_name) > 0:
            ret.add(attr_name)
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False
  
class NrGlobalDynamicFractileScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_global_dynamic_fratile_score"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    #attrs.update(self._config.get("input_common_attrs"))
    # for attr in self._config.get("input_common_attrs", ["p_ctr"]):
    #   attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    #attrs = extract_attr_names(self._config.get("output_item_attrs", []), "as")
    attrs.add(self._config.get("fr_ncee_score", "fr_ncee_score"))
    attrs.add(self._config.get("fr_ncee_gender_score", "fr_ncee_gender_score"))
    return attrs

class SlideLeafStringListSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "split_string_list"

  @strict_types
  def _check_config(self) -> None:
    input_common_attr = self._config.get("input_common_attr")
    output_common_attr = self._config.get("output_common_attr")

    check_arg(output_common_attr, "`output_common_attr` 参数缺失")
    check_arg(isinstance(input_common_attr, str), "`input_common_attr` 配置需为 string 类型")
    check_arg(isinstance(output_common_attr, str), "`output_common_attr` 配置需为 string 类型")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attr"):
      attrs.add(self._config["input_common_attr"])
    for name in ["max_num_per_str"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attr"):
      attrs.add(self._config["output_common_attr"])
    return attrs

class NrFrCommonLeafEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_fr_common_leaf_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr_name") or "user_info")
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_mc_result_set_attr") or "is_mc_result")
    attrs.add("live_photo_info_is_living")
    attrs.add("photo_dnn_cluster_id")
    attrs.add('mmu_hot_face_cluster_id')
    attrs.add('mmu_img_cluster_v3')
    attrs.add('photo_dnn_cluster_id')
    attrs.update(['hetu_tag_v1_level_one_list','hetu_tag_v1_level_two_list','hetu_tag_v1_level_three_list',
    'duration_ms'])
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    output = set()
    output.add(self._config.get("fr_result_count_attr") or "fr_result_count")
    return output

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    output.add(self._config.get("is_fr_result_set_attr") or "is_fr_result")
    output.add(self._config.get("fr_result_order_atrr") or "fr_result_order")
    output.add(self._config.get("fr_reason_attr") or "fr_reason")
    return output
  
  @strict_types
  def is_async(self) -> bool:
    return False

class NrMcCommonLeafEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_mc_common_leaf_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr_name") or "user_info")
    attrs.update(self.extract_dynamic_params(self._config.get("request_type")))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["max_request_num"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    output = set()
    output.add(self._config.get("mc_result_count_attr") or "mc_result_count")
    return output

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    output = extract_attr_names(self._config.get("recv_item_attrs", []), "as")
    output.add(self._config.get("is_mc_result_set_attr") or "is_mc_result")
    if "mc_result_order_attr" in self._config and self._config.get("mc_result_order_attr"):
      output.add(self._config.get("mc_result_order_attr"))
    return output
  
  @strict_types
  def is_async(self) -> bool:
    return False

class NrMockUserInfoPostProcessingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_mock_user_info_post_processing_enricher"

  @strict_types
  def _check_config(self) -> None:
    user_info_attr = self._config.get("user_info_attr")
    check_arg(isinstance(user_info_attr, str), "user_info_attr 配置需为字符串")

    mocked_user_info_str = self._config.get("mocked_user_info_str")
    check_arg(isinstance(mocked_user_info_str, str), "mocked_user_info_str 配置需为字符串")

    check_arg(isinstance(self._config.get("is_open_common_mock_merge"), bool), f"{self.get_type_alias()} 缺少 is_open_common_mock_merge 配置")
    check_arg(isinstance(self._config.get("is_open_local_mock_merge"), bool), f"{self.get_type_alias()} 缺少 is_open_local_mock_merge 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr") or "user_info")
    if "ug_user_profile_attr" in self._config:
      attrs.add(self._config.get("ug_user_profile_attr"))
    attrs.update(self.extract_dynamic_params("enable_ug_user_profile_merge"))
    attrs.update(self.extract_dynamic_params("enable_ug_user_profile_app_merge"))
    if self._config.get("is_open_common_mock_merge"):
      attrs.add("user_id")
      attrs.add("device_id")
      attrs.add("app_info_list")
      attrs.add("click_list")
      attrs.add("like_list")
      attrs.add("follow_list")
      attrs.add("forward_list")
      attrs.add("comment_list")
      attrs.add("user_location_str")
      attrs.add("gender")
      attrs.add("year")
      attrs.add("age_segment")
    if self._config.get("is_open_local_mock_merge"): 
      attrs.add(self._config.get("user_type_ab_param_attr", "user_type_ab_param"))
      attrs.add(self._config.get("enable_unlogin_mock_actionlist_attr") or "enable_unlogin_mock_actionlist")
      attrs.add(self._config.get("enable_nr_user_profile_reset_using_negative_history_attr") or "enable_nr_user_profile_reset_using_negative_history")
      if self._config.get("is_open_kuiba_attr_modify"): 
        attrs.add(self._config.get("kuiba_user_attr_str_attr", "kuiba_user_attr_str"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("mocked_user_info_str") == None:
        attrs.add("mocked_user_info_str")
    else:
        attrs.add(self._config.get("mocked_user_info_str"))
    return attrs

class NrMockLowVVUserActionInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_mock_low_vv_user_action_info_enricher"

  @strict_types
  def _check_config(self) -> None:
    user_info_attr = self._config.get("user_info_attr")
    check_arg(isinstance(user_info_attr, str), "user_info_attr 配置需为字符串")

    mocked_user_info_str = self._config.get("mocked_user_info_str")
    check_arg(isinstance(mocked_user_info_str, str), "mocked_user_info_str 配置需为字符串")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr") or "user_info")
    for key in ['src_item_list', 'sim_pid_list', 'author_id_list', 'duration_ms_list',
                'hetu_one_list', 'hetu_two_list', 'min_action_size'
                'upv1_long_view_pid_list', 'upv1_long_view_ts_list']:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("mocked_user_info_str") == None:
        attrs.add("mocked_user_info_str")
    else:
        attrs.add(self._config.get("mocked_user_info_str"))
    return attrs

class NrEmpiricalXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_empirical_xtr_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_mc_result_set_attr", "is_mc_result"));
    attrs.add("thanos_stats_click_count")
    attrs.add("nebula_stats_click_count")
    attrs.add("thanos_stats_real_show_count")
    attrs.add("nebula_stats_real_show_count")
    attrs.add("thanos_stats_forward_count")
    attrs.add("nebula_stats_forward_count")
    attrs.add("thanos_stats_follow_count")
    attrs.add("nebula_stats_follow_count")
    attrs.add("thanos_stats_like_count")
    attrs.add("nebula_stats_like_count")
    attrs.add("thanos_stats_profile_enter_count")
    attrs.add("nebula_stats_profile_enter_count")
    attrs.add("thanos_stats_negative_count")
    attrs.add("nebula_stats_negative_count")
    attrs.add("thanos_stats_short_play_count")
    attrs.add("nebula_stats_short_play_count")
    attrs.add("thanos_stats_comment_count")
    attrs.add("nebula_stats_comment_count")
    attrs.add("thanos_stats_long_play_count")
    attrs.add("nebula_stats_long_play_count")
    attrs.add("tag_detail_music_tag_click")
    attrs.add("explore_stat_click_count")
    attrs.add("ads_explore_stat_click_count")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("emp_ctr", "emp_ctr"))
    attrs.add(self._config.get("emp_ftr", "emp_ftr"))
    attrs.add(self._config.get("emp_wtr", "emp_wtr"))
    attrs.add(self._config.get("emp_ltr", "emp_ltr"))
    attrs.add(self._config.get("emp_ptr", "emp_ptr"))
    attrs.add(self._config.get("emp_htr", "emp_htr"))
    attrs.add(self._config.get("emp_cmtr", "emp_cmtr"))
    attrs.add(self._config.get("emp_svtr", "emp_svtr"))
    attrs.add(self._config.get("emp_tag_ctr", "emp_tag_ctr"))
    return attrs

class NrInitCommonAttrFromUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_init_common_attr_from_user_info_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("user_info_attr") == None:
        attrs.add("user_info")
    else:
        attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("user_type_ab_param_attr", "user_type_ab_param"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("is_login_user")
    attrs.add("is_unlogin_user")
    attrs.add("is_nebula_user")
    attrs.add("is_gamora_user")
    attrs.add("is_new_device")
    attrs.add("is_reflux_device")
    attrs.add("is_tnu_user")
    attrs.add("is_new_device_old_user")
    attrs.add("is_new_device_reflux_user")
    attrs.add("is_fragile_user")
    attrs.add("is_14d_rd")
    attrs.add("is_14d_nd")
    attrs.add("sem_info_timestamp")
    attrs.add("sem_info_keyword")
    return attrs

class NrUserTypeABParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_user_type_ab_param_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("enable_new_nr_user_check_method")
    attrs.add("nr_nebula_login_enable_long_period_new_device")
    attrs.add("nr_nebula_unlogin_enable_long_period_new_device")
    attrs.add("nr_gamora_login_enable_long_period_new_device")
    attrs.add("nr_gamora_unlogin_enable_long_period_new_device")
    attrs.add("nr_nebula_login_enable_long_period_reflux_device")
    attrs.add("nr_nebula_unlogin_enable_long_period_reflux_device")
    attrs.add("nr_gamora_login_enable_long_period_reflux_device")
    attrs.add("nr_gamora_unlogin_enable_long_period_reflux_device")
    attrs.add("nr_nebula_tnu_enable_long_period_new_device")
    attrs.add("nr_gamora_tnu_enable_long_period_new_device")
    attrs.add("nr_nebula_tnu_enable_long_period_reflux_device")
    attrs.add("nr_gamora_tnu_enable_long_period_reflux_device")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_type_ab_param_out_attr", "user_type_ab_param"))
    return attrs
class NrInitClearSetUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_init_clear_set_userinfo_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("user_info_attr") == None:
        attrs.add("user_info")
    else:
        attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("user_type_ab_param_attr", "user_type_ab_param"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("user_info_attr") == None:
        attrs.add("user_info")
    else:
        attrs.add(self._config.get("user_info_attr"))
    return attrs


class NrSeqNumOverAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_gen_seq_num_over_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "attr_name" not in self._config:
      raise ValueError("缺少 attr_name 配置")
    ret.add(self._config["attr_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "output_seq_attr" not in self._config:
      raise ValueError("缺少 output_seq_attr 配置")
    return { self._config["output_seq_attr"] }


class NrFeedbackScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_calc_feedback_score"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "browsed_items_from_attr" not in self._config:
      raise ValueError("缺少 browsed_items_from_attr 配置")
    ret.add(self._config["browsed_items_from_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "item_embedding_attr" not in self._config:
      raise ValueError("缺少 item_embedding_attr 配置")
    ret.add(self._config["item_embedding_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "export_item_attr" not in self._config:
      raise ValueError("缺少 export_item_attr 配置")
    return { self._config["export_item_attr"] }

class NrRecoWeightedCumprodV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_weighted_cumprod_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      for key in ["weight"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class NrUserInfoMergeBrowsedIdsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_user_info_merge_browsed_ids_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("user_info_attr") == None:
        attrs.add("user_info")
    else:
        attrs.add(self._config.get("user_info_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("user_info_attr") == None:
        attrs.add("user_info")
    else:
        attrs.add(self._config.get("user_info_attr"))
    return attrs
  
class NrRequestAlignRelatedUidListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_request_align_related_uid_list_enricher"

  @strict_types
  def _check_config(self) -> None:
    if "aid_list_attr" not in self._config:
      raise ValueError("缺少 aid_list_attr 配置")
    if "align_related_uid_attr" not in self._config:
      raise ValueError("缺少 align_related_uid_attr 配置")
    if "kess_service" not in self._config:
      raise ValueError("缺少 kess_service 配置")
    if "timeout_ms" not in self._config:
      raise ValueError("缺少 timeout_ms 配置")
    if "request_num_per_uid" not in self._config:
      raise ValueError("缺少 request_num_per_uid 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ['aid_list_attr', 'kess_service', 'timeout_ms', 'request_num_per_uid']:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("align_related_uid_attr"))
    return attrs
  
class UgConditionCommonAttrValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_condition_set_common_attr_value"

  @strict_types
  def _check_config(self) -> None:
    kconf = self._config.get("kconf_path", "")
    save_common_attrs = self._config.get("save_common_attrs", []) 
    if not isinstance(kconf,str) or len(kconf) ==0:
      raise ValueError("缺少 kconf_path 配置")
    if not isinstance(save_common_attrs,list) or not save_common_attrs:
      raise ValueError("缺少 save_common_attrs 配置")
    for save_common_attr in save_common_attrs: 
      if not isinstance(save_common_attr,dict) or "name" not in save_common_attr or "default_value" not in save_common_attr:
        raise ValueError("缺少 name/default_value 配置")
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("save_common_attrs", []), "as")
  
class NrCommonAttrFromRedisByLrangeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_get_common_attr_from_redis_by_lrange"
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_common_attr" in self._config.keys() :
      ret.update(self.extract_dynamic_params(self._config.get("output_common_attr")))
    return ret
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "redis_key" in self._config.keys() :
      ret.update(self.extract_dynamic_params(self._config.get("redis_key")))
    return ret