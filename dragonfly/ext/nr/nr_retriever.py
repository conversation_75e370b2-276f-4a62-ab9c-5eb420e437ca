#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>, LeafRetriever

class TnuRedisSamplingRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_redis_sampling_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class TnuHetuRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_hetu_uid"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_str"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class TnuHetuRetrieverDirect(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_hetu_uid_direct"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_str"])
    ret.update(self.extract_dynamic_params(self._config.get("enable_hetu_tag_two")))
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class TnuSamplingPoolRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_sampling_pool_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["common_pid"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class NrGsuRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_gsu_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class TnuCausalRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_causal_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_hetu"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class TnuCausalAuthorRetriever(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tnu_causal_author_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_author"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False


class NrGraphCollabRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_graph_collab_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_str_attr"])
    for key in ['retrieve_num', 'redis_key_prefix', 'timeout_ms', 'reason', 'kess_service', 'trigger_res_num']:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    for key in ['max_click_size', 'max_like_size', 'max_follow_size', 'max_forward_size',
                'max_longview_size', 'max_profile_enter_size', 'trigger_type', 'trigger_num', 'oaid']:
      if self._config.get(key) is not None:
        ret.update(self.extract_dynamic_params(self._config.get(key)))
    return ret


  @strict_types
  def is_async(self) -> bool:
    return False

class NrLongtermItemcfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_longterm_itemcf_retriever"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["user_info_attr"])
    for key in ['expected_trigger_count', 'timeout_ms', 'reason', 'kess_service', 'prefix', 'long_term_triggers']:
      ret.update(self.extract_dynamic_params(self._config.get(key)))
    return ret


  @strict_types
  def is_async(self) -> bool:
    return False

class NrCommonAttrWithReasonRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_common_attr_with_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["attr"])
    ret.add(self._config["reason_attr"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return False

class RecoPandoraRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_pandora"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    for name in ["app_name", "extra_args", "retrieve_num", "kess_service", "service_group", "timeout_ms"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))

    return attrs
