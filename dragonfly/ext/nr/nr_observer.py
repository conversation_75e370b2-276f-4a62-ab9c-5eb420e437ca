#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class NrABTestObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_abtest_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr") or "user_info");
    attrs.add(self._config.get("fr_result_count") or "fr_result_count");
    attrs.add(self._config.get("mc_result_count") or "mc_result_count");
    attrs.add(self._config.get("mock_user_info_start_ts") or "mock_user_info_start_ts");
    attrs.add(self._config.get("mock_user_info_end_ts") or "mock_user_info_end_ts");
    attrs.add(self._config.get("cascading_start_ts") or "cascading_start_ts");
    attrs.add(self._config.get("cascading_end_ts") or "cascading_end_ts");
    attrs.add(self._config.get("fullrank_start_ts") or "fullrank_start_ts");
    attrs.add(self._config.get("fullrank_end_ts") or "fullrank_end_ts");
    return attrs

class NrSampleObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_sample_observer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr") or "user_info")
    attrs.add("llsid")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_mc_result") or "is_mc_result")
    attrs.add(self._config.get("is_sample_mc_result") or "is_sample_mc_result")
    attrs.add(self._config.get("is_fr_result") or "is_fr_result")
    attrs.add("like_count")
    attrs.add("show_count")
    attrs.add("unlike_count")
    attrs.add("emp_ctr");
    attrs.add("emp_ftr");
    attrs.add("emp_wtr");
    attrs.add("emp_ltr");
    attrs.add("emp_ptr");
    attrs.add("emp_htr");
    attrs.add("emp_cmtr");
    attrs.add("emp_svtr");
    attrs.add("emp_tag_ctr");
    attrs.add("mc_ctr");
    attrs.add("mc_ltr");
    attrs.add("mc_wtr");
    attrs.add("mc_ftr");
    attrs.add("mc_lvtr");
    attrs.add("mc_svtr");
    attrs.add("mc_vtr");
    attrs.add("mc_ptr");
    attrs.add("mc_epstr");
    attrs.add("mc_cmtr");
    attrs.add("mc_htr");
    attrs.add("fr_ctr");
    attrs.add("fr_ltr");
    attrs.add("fr_wtr");
    attrs.add("fr_ftr");
    attrs.add("fr_htr");
    attrs.add("fr_lvtr");
    attrs.add("fr_svtr");
    attrs.add("fr_vtr");
    attrs.add("fr_ptr");
    attrs.add("fr_cmtr");
    attrs.add("fr_epstr");
    attrs.add("fr_tag_ctr");
    attrs.add("fr_cmef");
    attrs.add('fr_cltr');
    attrs.add('fr_etcm');
    attrs.add('fr_evtr');
    attrs.add('fr_leaf_score');
    attrs.add('fr_lsst');
    attrs.add('fr_lstr');
    attrs.add('fr_setr');
    attrs.add('fr_swpac');
    attrs.add('fr_swppc');
    attrs.add('fr_swpst');
    attrs.add('fr_swptr');
    attrs.add('rerank_score_array_str');
    attrs.add('channel_ensemble_sort_scores');
    attrs.add('mc_ecstr');
    attrs.add('mc_leaf_score');
    attrs.add('mc_livingtr');
    attrs.add('mc_lvtr2');
    attrs.add('mc_wtl1');
    attrs.add('mc_wtl2');
    attrs.add('queue_seq');
    attrs.add('variant_es_score');
    attrs.add('duration_ms');
    attrs.add('upload_time');
    attrs.add('click_count');
    attrs.add('like_count');
    # attrs.add('forward_count');
    # attrs.add('report_count');
    attrs.add('upload_type');
    # attrs.add('is_mid_video_photo');
    return attrs
  
class NrCommonAttrToRedisByLpushObserver(LeafObserver):
  @strict_types
  def is_async(self) -> bool:
    return True

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_common_attr_to_redis_by_lpush"


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["expire_second", "key", "value"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
