#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafArranger

class SlideFullRankMmrDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "slide_diversify_by_mmr"

  @strict_types
  def __init__(self, config: dict):
    TYPE_NAME_MAP = {
      "float": "double",
    }
    check_arg("mmr_score_name" in config, "mmr_configs 不能缺少 mmr_score_name")
    for mmr_config in config["mmr_configs"]:
      check_arg("attr_name" in mmr_config, "mmr_configs 不能缺少 attr_name")
      check_arg(isinstance(mmr_config["attr_name"], str), "mmr_configs 里的 attr_name 值必须为字符串")
      check_arg(len(mmr_config["attr_name"]) > 0, "mmr_configs 里的 attr_name 不可为空")
      # type_name = type(mmr_config["attr_weight"]).__name__
      # check_arg(type_name in TYPE_NAME_MAP, f"非法的 mmr configs attr_weight: {mmr_config['attr_weight']}")
    super().__init__(config)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for r in self._config.get("mmr_configs"):
      attrs.add(r.get("attr_name"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmr_lambda")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmr_score_name")))
    for config in self._config.get("mmr_configs"):
      attrs.update(self.extract_dynamic_params(config["attr_weight"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["mmr_score_name"])
    return attrs