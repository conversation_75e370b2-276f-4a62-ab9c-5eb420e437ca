#!/usr/bin/env python3
# coding=utf-8
"""
filename: slide_api_mixin.py
description: 
author: lin<PERSON><EMAIL>
date: 2021-03-04 12:15:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .slide_arranger import *
from .slide_enricher import *


class SlideApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 单列slide 相关的 Processor 接口
  - SlideFullRankMmrDiversityArranger 
  """

  def slide_diversify_by_mmr(self, **kwargs):
    """
    SlideFullRankMmrDiversityArranger 
    ------
    计算视频的MMR分数并排序

    参数配置
    ------
    `mmr_configs`: [list of dict] 必填项，配置计算相似度的 item_attr（目前仅支持int类型的attr）

    `attr_name`: [string] 必填项，计算相似度的 item_attr（目前仅支持int类型的attr）

    `attr_weight`: [double] 必填项，item_attr的相似度权重

    `mmr_score_name`: [string] 必填项，mmr分数的item_attr名称
    """
    self._add_processor(SlideFullRankMmrDiversityArranger(kwargs))
    return self
  def tag_list_dist_feature_enricher(self, **kwargs):
    self._add_processor(TagListDistFeatureEnricher(kwargs))
    return self
  def slide_nr_user_interest_representation(self, **kwargs):
    """
    NrUserInterestRepresentationEnricher
    ------
    根据 user info 中的 video playing state 计算用户的兴趣表达相关字段，将结果存入 CommonAttr

    参数配置
    ------
    `user_info_attr`: [string] 必配. user profile 信息，从common attr中反序列化得到

    `export_short_play_hetu_level_one_tag_ids_attr_name`: [string] 必配.存储 video playing state 中短播的 hetu level one int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应

    `export_short_play_hetu_level_two_tag_ids_attr_name`: [string] 必配.存储 video playing state 中短播的 hetu level two int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应

    `export_long_play_hetu_level_one_tag_ids_attr_name`: [string] 必配.存储 video playing state 中长播的 hetu level one int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应

    `export_long_play_hetu_level_two_tag_ids_attr_name`: [string] 必配.存储 video playing state 中长播的 hetu level two int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应

    `export_ie_play_hetu_level_one_tag_ids_attr_name`: [string] 必配.存储 video playing state 中 兴趣探索流量的 hetu level one int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应

    `export_ie_play_hetu_level_two_tag_ids_attr_name`: [string] 必配.存储 video playing state 中 兴趣探索流量的 hetu level two int list CommonAttr. 备注： 非去重，与 video playing state 的 photo id 长度不对应


    调用示例
    ------
    ``` python
    .slide_nr_user_interest_representation(
      user_info_attr="user_info_str",
      export_short_play_hetu_level_one_tag_ids_attr_name="export_short_play_hetu_level_one_tag_ids",
      export_short_play_hetu_level_two_tag_ids_attr_name="export_short_play_hetu_level_two_tag_ids",
      export_long_play_hetu_level_one_tag_ids_attr_name="export_long_play_hetu_level_one_tag_ids",
      export_long_play_hetu_level_two_tag_ids_attr_name="export_long_play_hetu_level_two_tag_ids_attr_name",
      export_ie_play_hetu_level_two_tag_ids_attr_name="export_ie_play_hetu_level_two_tag_ids_attr_name",
      export_ie_play_hetu_level_one_tag_ids_attr_name="export_ie_play_hetu_level_one_tag_ids_attr_name",
      )
    ```
    """
    self._add_processor(NrUserInterestRepresentationEnricher(kwargs))
    return self
  
  def slide_nr_user_interest_search(self, **kwargs):
    """
    NrUserInterestSearchEnricher
    ------
    根据 hetu tag tree 以及 传入的正反馈 / 负反馈 hetu 节点信息，检索出适合探索的 hetu tag ids  以及 权重

    参数配置
    ------
    `hetu_index_tree_json_kconf_name`: [string] 必配. hetuIndex 索引树 kconf json name。具体格式可参考 reco.slide.nrIEHetuIndex

    `positive_hetu_tag_id_list_attr_name`: [string] 必配.探索初始化的正反馈 hetu 节点 id, int list CommonAttr. 备注：一般是正反馈 hetu tag

    `positive_hetu_tag_weight_list_attr_name`: [string] 选配.探索初始化的正反馈 hetu 节点 权重, int list CommonAttr. 备注：如果该字段配置了，则必须与 positive_hetu_tag_id_list_attr_name 对应的 list 长度相同

    `negative_hetu_tag_id_list_attr_name`: [string] 必配.探索初始化的负反馈 hetu 节点 id, int list CommonAttr. 备注：一般是负反馈 hetu tag

    `export_search_hetu_tag_ids_attr_name`: [string] 必配.存储 搜索出来的探索 hetu tag id 列表,  int list CommonAttr. 

    `export_search_hetu_tag_weights_attr_name`: [string] 必配.存储 搜索出来的探索 hetu tag 权重列表 list,  int list CommonAttr.. 备注：与 export_search_hetu_tag_ids_attr_name 对应的 list 长度相同

    调用示例
    ------
    ``` python
    .slide_nr_user_interest_search(
      hetu_index_tree_json_kconf_name="reco.slide.nrIEHetuIndex",
      positive_hetu_tag_id_list_attr_name="positive_hetu_tag_id_list",
      export_search_hetu_tag_ids_attr_name="export_search_hetu_tag_ids",
      export_search_hetu_tag_weights_attr_name="export_search_hetu_tag_weights",
      )
    ```
    """
    self._add_processor(NrUserInterestSearchEnricher(kwargs))
    return self

  def reco_gsu_v2_author_reward_cal(self, **kwargs):
    """
    RecoGsuV2AuthorRewardCal 
    ------
    长期价值模型抽取author reward

    参数
    ------
    ``` python
    reco_gsu_v2_author_reward_cal(
         colossus_resp_attr='colossus_output',
         dag_lag = 7,
         kconf_name = "reco.thanos.kconf_name",
       )
    ```
    """
    self._add_processor(RecoGsuV2AuthorRewardCal(kwargs))
    return self
  def calc_nr_ensemble_score(self, **kwargs):
    """
    NrRecoEnsembleScoreEnricher
    ------
    对指定的 N 个队列计算用于 ensemble sort 的综合分，计算方式:

    每个 item 在各个队列 channels[i] 的单项分公式: `Si = func(formula_version)`

    每个 item 最后输出到 `output_attr` 的综合分数值为: `S1 + S2 +...+ Sn` (`n` 为 channels 个数)

    注意：该方法只生成 ensemble score, 如需根据该综合分进行排序, 请在之后自行调用 `.sort(score_from_attr="${output_attr}")`

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (SEQ_ON_ATTR(channels[i].name) ^ regulator + smooth)`
      - 1: `Si = channels[i].weight * (1 - min(1, SEQ_ON_ATTR(channels[i].name) / item_num) ^ regulator)`
      - 2: `Si = channels[i].weight * (e ^ (channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)) - e ^ (-channels[i].hyper_scala * (2.0 * (SEQ_ON_ATTR(channels[i].name) / item_num) - 1.0)))`
      - 3: 分桶归一化（以后将废弃）
      - 100: 分桶归一化
      - 101: 对头部视频序的反比例函数映射用sigmoid函数替代计算，打压平滑分数
      - 102: 将 rank 均匀映射到 [0, 1] 区间，并计算分数
      - 103: ensemble sort filter
      - 105: ensemble sort filter with channel weight
      - 106: 102 升级版本，支持队列配置不同的 regulator

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `enabled`: [bool] [动态参数] 该队列开关，默认值是 true
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `hyper_scala`: [double] [动态参数] 该队列的双曲调节因子
      - `alpha`: [double] [动态参数] 排序公式超参（具体功能见代码），默认值为0.0
      - `beta`: [double] [动态参数] 排序公式超参（具体功能见代码），默认值为0.0
      - `save_score_to`: [string] 选配项，将该 channel 的 `Si = func(formula_version)` 分值存入指定的 item_attr

    `regulator`: [double] [动态参数] ensemble sort 各队列的调节因子

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `bucket_num`: [int] [动态参数] ensemble sort 各队列分桶数

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `start_seq`: [int] 选配项，序号的起始值，默认为 0

    `allow_stair_seq`: [bool] [动态参数] 选配项，是否允许按照间隔产生序号值; 默认值 False

    `allow_tied_seq`: [bool] 选配项，是否允许并列的序号值，例如可能按排名产出如下值：0, 1, 1, 3, 4, 4, 4, 7; 默认值 False

    `continuous_tied_seq`: [bool] 选配项，在允许并列序号的情况下，是否产出连续的序号，例如为 True 时将按排名产出如下值：0, 1, 1, 2, 3, 3, 3, 4; 默认值 False

    `cliff_ratio`: [double] [动态参数] 选配项，ensemble 队列排序序号开始降权 position 的比例点，即对排名 `队列长度 * cliff_ratio` 之后的 item 进行打压

    `cliff_height`: [int] [动态参数] 选配项，ensemble 队列降权系数，即对排名 `队列长度 * cliff_ratio` 之后的 item 序号额外加上该值作为惩罚

    `epsilon`: [double] 选配项，在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    `stable_sort`: [bool] 选配项，是否使用稳定排序来计算各 item 序号，默认值 False

    `bucket_num`: [int] [动态参数] 选配项，ensemble sort 各队列分桶数

    `sigmoid_critical_point`: [int] [动态参数] 选配项，调整sigmoid头部打压平滑算分方法的打压范围，序值小于该值的头部视频将采用sigmoid平滑的方式算分

    `sigmoid_scale`: [double] [动态参数] 选配项，调整sigmoid头部打压平滑算分方法的打压程度

    `queue_size`: [double] [动态参数] 选配项，视频所在队列的长度

    `cliff_threshold`: [double] [动态参数] 选配项， rank 超过 cliff_threshold 的视频将被打压

    `cliff_param`: [double] [动态参数] 选配项，打压系数

    `filter_soft_cnt`: [int] [动态参数] 选配项，ensemble sort filter 的起始位置

    `desc_sort`: [bool] [动态参数] 选配项，各 channel 排序时是否降序排列，默认为 True, 即降序排列

    调用示例
    ------
    ``` python
    .calc_ensemble_score(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      regulator = "{{ensemble_regulator}}",
      smooth = "{{ensemble_smooth}}",
      bucket_num = "{{bucket_num}}",
      output_attr = "ensemble_score",
    )
    ```
    """
    self._add_processor(NrRecoEnsembleScoreEnricher(kwargs))
    return self

  def calc_weighted_cumprod(self, **kwargs):
    """
    NrRecoWeightedCumprodEnricher
    ------
    对多个 item attr 进行累乘计算。
    参数配置
    ------
    `formula_version`: [int] 选配项，单项分计算公式, 默认为 0
      - 0: `Si = TT(pow(ATTR_VALUE(channels[i].name), channels[i].weight))`
      - 1: `1 - TT(1 - pow(ATTR_VALUE(channels[i].name), channels[i].weight))`
      
    `channels`: [list] 需要用于计算累乘的队列配置，每个队列包含以下两个配置项：
      - `name`: [string] 队列名，将从同名 double 或 int 类型 item_attr 获取值进行计算
      - `weight`: [double] [动态参数] 该队列的参数
      
    `regulator`: [double] [动态参数] 每次累乘时，通过 score / regulator 调节分数的取值范围。默认值 1.0

    `output_item_attr`: [string] 将最后计算出累乘分数存入指定的 double item_attr
    
    调用示例
    ------
    ``` python
    .calc_weighted_cumprod(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      output_item_attr = "final_score",
    )
    ```
    """
    self._add_processor(NrRecoWeightedCumprodEnricher(kwargs))
    return self

  def parse_hetu_tag(self, **kwargs):
    """
    HetuTagEnricher
    ------
    将 item 的【河图标签列表】解析为【河图标签】，优化 Lua 脚本解析存在的耗时问题

    参数配置
    ------
    `configs`: [list] 需要解析的河图列表，每个列表包含以下配置项：
      - `hetu_list_attr`: [string] 变量名，表示待解析的河图标签列表
      - `hetu_attr`: [string] 变量名，解析后的河图标签

    `hetu_v2`: [bool] 选配项，是否按照河图标签v2的方式解析; 默认值 True

    `ignore_tags`: [int_list|动态参数] 选配项，是否忽略特定的河图类目（如：合集-64）

    调用示例
    ------
    ``` python
    .parse_hetu_tag(
      configs = [
        { "hetu_list_attr": "hetu_tag_level_one_list", "hetu_attr": "hetu_tag_level_one" },
        { "hetu_list_attr": "hetu_tag_level_two_list", "hetu_attr": "hetu_tag_level_two" },
        { "hetu_list_attr": "hetu_tag_level_three_list", "hetu_attr": "hetu_tag_level_three" },
      ],
      hetu_v2 = True,
      ignore_tags = [64]
    )
    ```
    """
    self._add_processor(HetuTagEnricher(kwargs))
    return self

  def calc_nr_ensemble_fit_score(self, **kwargs):
    self._add_processor(NrRecoEnsembleFitEnricher(kwargs))
    return self

  def calc_remap_sroce(self, **kwargs):
    """
    RecoScoreRemapEnricher
    ------
    采样计算xtr全局分位数
    参数配置
    ------
    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(RecoScoreRemapEnricher(kwargs))
    return self

  def pack_item_attr_to_item_attr(self, **kwargs):
    """
    已迁移至 common 模块
    """
    self.pack_item_attr_to_item_attr(**kwargs)
    return self

  def enrich_json_string(self, **kwargs):
    """
    NrRecoEnrichJsonStringEnricher
    ------
    将指定的 CommonAttr 转换为 plain json string 的格式

    参数配置
    ------
    `common_attrs`: [list] 需要抽取的各个 CommonAttr 名称

    `json_attr`: [string] 待写入的 CommonAttr 名称，被抽取的各个 CommonAttr 组装成 plain json string 并写入该 CommonAttr 中

    调用示例
    ------
    🎯 [Try in Playground](http://ksurl.cn/jGhEIktH)
    ``` python
    .enrich_json_string(
      common_attrs = ["user_id", "device_id"],
      json_attr = "str_json"
    )
    ```
    """
    self._add_processor(NrRecoEnrichJsonStringEnricher(kwargs))
    return self

  def parse_json_string(self, **kwargs):
    """
    已迁移至 common 模块并重命名为 enrich_attr_by_json
    """
    self.enrich_attr_by_json(**kwargs)
    return self

  def backplay_page_reward(self, **kwargs):
    """
    RecoPageRewardEnricher
    ------
    回放 video_play_stat的样本，并计算 page base reward
    参数配置
    ------
    调用示例
    ------
    ``` python
    .backplay_page_reward(
      user_info_attr = "user_info",
      playback_photo_pid = "pb_pid",
      playback_feature_pids = "pb_fea_pids",
    )
    ```
    """
    self._add_processor(RecoPageRewardEnricher(kwargs))
    return self

  def interest_calibrated_weight_enricher(self, **kwargs):
    """
    GetInterestCalibratedWeightEnricher
    ------

    参数
    ------
    ``` python
    interest_calibrated_weight_enricher(
         get_topk_from = 'effective', #["effective", "longview", 'follow', 'forward', "like", "positive"],
         get_topk_hetu = 5,
         topk_save_attr = 'top_hetu',
         just_get_top_hetu = False,
         colossus_resp_attr='colossus_output',
         item_tag_name_attr = 'hetu_tag_level1',
         statistic_time_range_day_ = 60,
         calibrate_weight_postfix = '_cal_weight',
         calibrate_types = ["effective", "longview", "like", "positive"],
         weight_coef = 1.0,
       )
    ```
    """
    self._add_processor(GetInterestCalibratedWeightEnricher(kwargs))
    return self

  def random_negative_sample_enricher(self, **kwargs):
    """
    RandomNegativeSampleEnricher
    ------

    参数
    ------
    ``` python
    .random_negative_sample_enricher(
    slot_as_attr_name=True,
    sampling_cnt_per_item=10,
    buffer_size=500000,
    filter_pids_attrs=[],
    author_attr_name = "author_id",
    sampled_pid_slot = 1546,
    sampled_pid_sign = 1546,
    sampled_aid_slot = 1547,
    sampled_aid_sign = 1547,
    )
    ```
    """
    self._add_processor(RandomNegativeSampleEnricher(kwargs))
    return self

  def search_insert(self, **kwargs):
    """
    SearchInsertEnricher
    ------
    给定一个排序数组 nums 和一个目标值 target，返回 target 将会被按顺序插入的位置。

    参数配置
    ------
    `target_attr`: [string] 目标值 (item_attr)，支持 int/double

    `nums_attr`: [string] 排序数组 (common_attr)，支持 int_list/double_list

    `is_common_nums_attr`: [bool] nums_attr 是否为 common_attr，默认值为 True

    `output_attr`: [string] 插入位置 (item_attr)

    调用示例
    ------
    ``` python
    .search_insert(
      target_attr = "ltr",
      nums_attr = "ltr_dist",
      output_attr = "ltr_fragile",
    )
    ```
    """
    self._add_processor(SearchInsertEnricher(kwargs))
    return self

  def enrich_with_map_ptr(self, **kwargs):
    """
    GetMapValueEnricher
    ------
    将 map value 写入 common attr

    参数配置
    ------
    `map_ptr_attr`: [string] 存储 map 指针的 common attr

    `key_str`: [string] map key

    `value_type`: [string] map key 的数据类型，目前支持 double_list

    `output_attr`: [string] 输出的 common attr

    调用示例
    ------
    ``` python
    .enrich_with_map_ptr(
      map_ptr_attr = "test_map",
      key_str = "test_key",
      value_type = "double_list",
      output_attr = "key_value",
    )
    ```
    """
    self._add_processor(GetMapValueEnricher(kwargs))
    return self

  def xtr_to_quantile(self, **kwargs):
    """
    XtrToQuantileEnricher
    ------
    根据 xtr 分为数 map ，将 xtr 转换为分位数

    参数配置
    ------
    `xtr_quantile_map_ptr`: [string] 存储 map 指针的 common attr。指针可以通过 explore_memory_data_enrich() 获取。

    `configs`: [list]
      - `xtr_attr`: [string] 输入 xtr 的 item attr
      - `quantile_key_attr`: [string] 存储 map key 的 item attr
      - `output_attr`: [string] 输出分位数的 item attr

    调用示例
    ------
    ``` python
    .xtr_to_quantile(
      xtr_quantile_map_ptr = "xtr_quantile_map",
      configs = [
        dict(xtr_attr="ctr", quantile_key_attr="ctr_quantile_key", output_attr="ctr_quantile_value"),
        dict(xtr_attr="ltr", quantile_key_attr="ltr_quantile_key", output_attr="ltr_quantile_value"),
      ],
    )
    ```
    """
    self._add_processor(XtrToQuantileEnricher(kwargs))
    return self

  def fetch_degrade_strategy_type(self, **kwargs):
    """
    FetchDegradeStrategyTypeEnricher
    ------
    根据请求的业务分数，获取降级类型: 0 不做判断；1 降级；2 非降级

    参数配置
    ------
    `request_scores`: [list] 输入参数
      - `weight`: [string] 该业务的权重 common_attr
      - `score`: [string] 该业务的 score common_attr

    最终计算的分数为 score = w_1 * score_1 + w_2 * score_2 + ...

    `skip_degrade_rate`: [double] [动态参数] 默认为 0.0。防止系统对低 score 用户持续降级，例如可以配置为 0.2，表示有 20% 的随机流量不做降级判断，由上游链路决定是否降级

    `using_dynamic_strategy`: [bool] [动态参数] 默认为 true, 动态搜寻最佳 threshold，忽略 `request_score_threshold_attr`

    `degrade_threshold_adjust_rate`: [double] [动态参数] 默认值 1.0。由于 RPC 真实降级比例和 DegradeLeaf 返回的降级比例存在偏差，可借此参数修正。如果 RPC 的实时流量偏高，可调高该参数，反之调低。

    `dynamic_adjust_degrade_threshold_alpha`: [double] [动态参数] 只有当 `using_dynamic_strategy` 为 true 时才生效。
    默认为 0.01，在 Dryrun 环境下，threshold 会在 5 分钟收敛.
    一般来说 `dynamic_adjust_degrade_threshold_alpha` 越小，最终的 threshold 收敛的速度会越慢。
    如果 `dynamic_adjust_degrade_threshold_alpha` 取值过大，可能导致最终的 threshold 波动剧烈，甚至无法收敛。

    `using_random_threshold`: [bool] [动态参数] 默认为 true，将使用随机阈值，避免低 score 用户被持续降级。

     `threshold_tag_attr`: [string] 对于同一组 ab 实验，应该使用同一个 threshold_tag。由于不同分桶的实验参数可能不同，可以使用 `threshold_tag_attr` 区分，防止阈值混乱。

    `request_score_threshold_attr`: [string] 输入参数，分数的阈值 common_attr。只有当配置 `using_dynamic_strategy` 为 false 才会生效

    `output_strategy_attr`: [string] 输出参数，最终降级的类型：
    1. 如果参数非法，或者中途出现异常情况，返回 0
    2. 如果最终的 score 小于阈值，返回 1，即降级
    3. 如果最终的 score 大于等于阈值，返回 2，即非降级

    `only_perf`: [bool] [动态参数] 默认为 false。如果配置为 true，将只 perf 打点儿，不会设置 output_strategy_attr

    调用示例
    ------
    ``` python
    .fetch_degrade_strategy_type(
      output_strategy_attr = "strategy"
      request_scores = [
        dict(weight="weight_1", score="score_1"),
        dict(weight="weight_2", score="score_2"),
      ],
    )
    ```
    """
    self._add_processor(FetchDegradeStrategyTypeEnricher(kwargs))
    return self
