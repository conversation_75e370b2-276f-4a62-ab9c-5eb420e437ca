#!/usr/bin/env python3
# coding=utf-8

import base64
import inspect
import logging
from ...common_leaf_util import strict_types, check_arg, ArgumentError, \
    gen_attr_name_with_item_attr_channel
from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class NrUserInterestRepresentationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_user_interest_representation"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_short_play_hetu_level_one_tag_ids_attr_name"))
    attrs.add(self._config.get("export_short_play_hetu_level_two_tag_ids_attr_name"))
    attrs.add(self._config.get("export_long_play_hetu_level_one_tag_ids_attr_name"))
    attrs.add(self._config.get("export_long_play_hetu_level_two_tag_ids_attr_name"))
    return attrs


class NrUserInterestSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nr_user_interest_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("hetu_index_tree_json_kconf_name")))
    attrs.add(self._config.get("positive_hetu_tag_id_list_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_search_hetu_tag_ids_attr_name"))
    attrs.add(self._config.get("export_search_hetu_tag_weights_attr_name"))
    return attrs



class RecoGsuV2AuthorRewardCal(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reco_gsu_v2_author_reward_cal"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["colossus_resp_attr"])
    attrs.add(self._config["dag_lag"])
    attrs.add(self._config["kconf_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("photo_id")
    attrs.add("author_id")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_reward")
    return attrs

class TagListDistFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tag_list_dist_feature_enricher"

  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("user_info_attr"):
      raise ArgumentError("user_info_attr 未配置")
    if not self._config.get("output_key_attr"):
      raise ArgumentError("output_key_attr 未配置")
    if (not self._config.get("output_cnt_attr")) \
          and (not self._config.get("output_sum_attr")) \
          and (not self._config.get("output_avg_attr")) :
      raise ArgumentError("output_cnt_attr|output_sum_attr|output_avg_attr 不能都未配置")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr", ""))
    attrs = set([ attr for attr in attrs if len(attr) > 0 ])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_key_attr", ""))
    attrs.add(self._config.get("output_cnt_attr", ""))
    attrs.add(self._config.get("output_sum_attr", ""))
    attrs.add(self._config.get("output_avg_attr", ""))
    attrs = set([ attr for attr in attrs if len(attr) > 0 ])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("export_item_attr", []))

class NrRecoEnsembleScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_nr_ensemble_score"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    all_formula_versions = {0, 1, 2, 3, 100, 101, 102, 103, 104, 105, 106}
    check_arg(
      formula_version in all_formula_versions,
      f"{self.get_type_alias()} 的 formula_version 配置只能为 {all_formula_versions}",
    )
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 1:
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
    if formula_version == 2:
      for channel in self._config["channels"]:
        check_arg(channel.get("hyper_scala"), f"{self.get_type_alias()} 缺少 hyper_scala 配置")
    if formula_version == 3:
      check_arg(self._config.get("bucket_num"), f"{self.get_type_alias()} 缺少 bucket_num 配置")
    if formula_version == 100:
      check_arg(self._config.get("bucket_num"), f"{self.get_type_alias()} 缺少 bucket_num 配置")
    if formula_version == 101:
      check_arg(self._config.get("sigmoid_critical_point"), f"{self.get_type_alias()} 缺少 sigmoid_critical_point 配置")
      check_arg(self._config.get("sigmoid_scale"), f"{self.get_type_alias()} 缺少 sigmoid_scale 配置")
    if formula_version == 102:
      check_arg(self._config.get("queue_size"), f"{self.get_type_alias()} 缺少 queue_size 配置")
      check_arg(self._config.get("cliff_threshold"), f"{self.get_type_alias()} 缺少 cliff_threshold 配置")
      check_arg(self._config.get("cliff_param"), f"{self.get_type_alias()} 缺少 cliff_param 配置")
    if formula_version == 103:
      check_arg(self._config.get("filter_soft_cnt"), f"{self.get_type_alias()} 缺少 filter_soft_cnt 配置")
    if formula_version == 105:
      check_arg(self._config.get("filter_soft_cnt"), f"{self.get_type_alias()} 缺少 filter_soft_cnt 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    configs = [
      "regulator",
      "smooth",
      "cliff_ratio",
      "cliff_height",
      "allow_stair_seq",
      # formula 100
      "bucket_num",
      # formula 101
      "sigmoid_critical_point",
      "sigmoid_scale",
      # formula 102
      "queue_size",
      "cliff_threshold",
      "cliff_param",
      # formula 103 & formula 105
      "filter_soft_cnt",
      "desc_sort",
    ]
    for key in configs:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight", "hyper_scala"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for channel in self._config["channels"]:
      save_score_to = channel.get("save_score_to")
      if save_score_to:
        attrs.add(save_score_to)
    attrs.add(self._config["output_attr"])
    return attrs

class NrRecoWeightedCumprodEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_weighted_cumprod"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["regulator"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["weight"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { self._config["output_item_attr"] }

class HetuTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_hetu_tag"

  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("configs"):
      raise ArgumentError("configs 未配置")
    for x in self._config["configs"]:
      for key in ["hetu_list_attr", "hetu_attr"]:
        if key not in x:
          raise ArgumentError(f"configs 未配置 {key} ")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = { x["hetu_list_attr"] for x in self._config["configs"] }
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["ignore_tags"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return { x["hetu_attr"] for x in self._config["configs"] }

class NrRecoEnsembleFitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_nr_ensemble_fit_score"

  @strict_types
  def _check_config(self) -> None:
    formula_version = self._config.get("formula_version", 0)
    all_formula_versions = {0}
    check_arg(
      formula_version in all_formula_versions,
      f"{self.get_type_alias()} 的 formula_version 配置只能为 {all_formula_versions}",
    )
    if formula_version == 0:
      check_arg(self._config.get("smooth"), f"{self.get_type_alias()} 缺少 smooth 配置")
      check_arg(self._config.get("regulator"), f"{self.get_type_alias()} 缺少 regulator 配置")
      check_arg(self._config.get("bucket_num"), f"{self.get_type_alias()} 缺少 bucket_num 配置")
      check_arg(self._config.get("ensemble_fit_name"), f"{self.get_type_alias()} 缺少 ensemble_fit_name 配置")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    configs = [
      "regulator",
      "smooth",
      "bucket_num",
      "ensemble_fit_name",
    ]
    for key in configs:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["enabled", "weight"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class RecoScoreRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "remap_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    con_attrs_ = self._config["convert_item_attrs"]
    if (len(con_attrs_) > 0):
      [ret.add(x) for x in con_attrs_]
    ret.add(self._config["random_num_attr"])
    ret.add(self._config["temperature_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["duration_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attrs_ = self._config["converted_attrs"]
    if (self._config["save_ensemble_score"] and len(attrs_) > 0):
      [ret.add(x) for x in attrs_]
    ret.add(self._config["merge_score_attr"])
    return ret

class NrRecoEnrichJsonStringEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_json_string"

  @strict_types
  def _check_config(self) -> None:
    if "common_attrs" not in self._config:
      raise ArgumentError("必须配置 common_attrs")
    if not isinstance(self._config["common_attrs"], list):
      raise ArgumentError("common_attrs 必须为 string list")

    if "json_attr" not in self._config:
      raise ArgumentError("必须配置 json_attr")
    if not isinstance(self._config["json_attr"], str):
      raise ArgumentError("json_attr 必须为 string")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config["common_attrs"]:
      attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["json_attr"])
    return attrs

class RecoPageRewardEnricher(LeafEnricher):
  """doc for RecoPageRewardEnricher"""
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "backplay_page_reward"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if ( "user_info_attr" in self._config):
      ret.add(self._config["user_info_attr"])
    else:
      ret.add(user_info)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attr_kv = {"playback_photo_pid": "pb_pid",  "playback_photo_aid" : "pb_aid",
              "playback_photo_tag" : "pb_tag",  "playback_photo_dura": "pb_dura",
              "playback_photo_play": "pb_play", "playback_photo_type": "pb_type",
              "playback_photo_reward": "pb_reward",
              "playback_feature_pids": "pb_fea_pids",
              "playback_feature_aids": "pb_fea_aids",
              "playback_feature_tags": "pb_fea_tags",
              "playback_feature_page": "pb_fea_page",
              "playback_feature_play": "pb_fea_play",
              "playback_feature_type": "pb_fea_type"}
    for key, val in attr_kv.items():
      if (key in self._config):
        ret.add(self._config[key])
      else:
        ret.add(val)
    return ret

class GetInterestCalibratedWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "interest_calibrated_weight_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["topk_save_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_tag_name_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    postfix = self._config.get("calibrate_weight_postfix", "_postfix")
    for name in self._config["calibrate_types"]:
        ret.add(name + postfix)
    return ret

class RandomNegativeSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "random_negative_sample_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for att_ in self._config["filter_pids_attrs"]:
      ret.add(att_)
    ret.add(self._config["author_attr_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add("rnd_early_return")
    if self._config.get("slot_as_attr_name", False):
        prefix = self._config.get("slot_as_attr_name_prefix", "")
        pid_slot = self._config.get("sampled_pid_slot")
        aid_slot = self._config.get("sampled_aid_slot")
        ret.add(prefix + str(pid_slot))
        ret.add(prefix + str(aid_slot))
    else:
        ret.add(self._config["sampled_signs_name"])
        ret.add(self._config["sampled_slots_name"])
    return ret

class SearchInsertEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_insert"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()

    if "target_attr" not in self._config:
      raise ArgumentError("必须配置 target_attr")
    ret.add(self._config["target_attr"])

    if not self._config.get("is_common_nums_attr", True):
      if "nums_attr" not in self._config:
        raise ArgumentError("必须配置 nums_attr")
        
      ret.add(self._config["nums_attr"])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    if self._config.get("is_common_nums_attr", True):
      if "nums_attr" not in self._config:
        raise ArgumentError("必须配置 nums_attr")
        
      ret.add(self._config["nums_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "output_attr" not in self._config:
      raise ArgumentError("必须配置 output_attr")
    ret.add(self._config["output_attr"])
    return ret


class GetMapValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_with_map_ptr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "map_ptr_attr" not in self._config:
      raise ArgumentError("必须配置 map_ptr_attr")
    ret.add(self._config["map_ptr_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_attr" not in self._config:
      raise ArgumentError("必须配置 output_attr")
    ret.add(self._config["output_attr"])
    return ret


class XtrToQuantileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "xtr_to_quantile"
  
  @strict_types
  def _check_config(self) -> None:
    for config in self._config["configs"]:
      check_arg("xtr_attr" in config, "一个 config 里必须配置 xtr_attr")
      check_arg("quantile_key_attr" in config, "一个 config 里必须配置 quantile_key_attr")
      check_arg("output_attr" in config, "一个 config 里必须配置 output_attr")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "xtr_quantile_map_ptr" not in self._config:
      raise ArgumentError("必须配置 xtr_quantile_map_ptr")
    ret.add(self._config["xtr_quantile_map_ptr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for config in self._config["configs"]:
      if "xtr_attr" in config:
        ret.add(config.get("xtr_attr"))
      if "quantile_key_attr" in config:
        ret.add(config.get("quantile_key_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for config in self._config["configs"]:
      if "output_attr" in config:
        ret.add(config.get("output_attr"))
    return ret

class FetchDegradeStrategyTypeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_degrade_strategy_type"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "only_perf" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("only_perf")))
    if "using_dynamic_threshold" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("using_dynamic_threshold")))
    if "dynamic_adjust_degrade_threshold_alpha" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("dynamic_adjust_degrade_threshold_alpha")))
    if "degrade_threshold_adjust_rate" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("degrade_threshold_adjust_rate")))
    if "skip_degrade_rate" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("skip_degrade_rate")))
    if "using_random_threshold" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("using_random_threshold")))
    if "request_score_threshold_attr" in self._config:
      attrs.add(self._config["request_score_threshold_attr"])
    if "threshold_tag_attr" in self._config:
      attrs.add(self._config["threshold_tag_attr"])
    for scores_attrs in self._config["request_scores"]:
      attrs.add(scores_attrs["weight"])
      attrs.add(scores_attrs["score"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_strategy_attr"])
    return attrs
