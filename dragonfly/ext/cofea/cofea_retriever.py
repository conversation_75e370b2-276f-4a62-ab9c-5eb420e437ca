#!/usr/bin/env python3
# coding=utf-8
"""
filename: cofea_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, cofea retriever module
author: <EMAIL>
date: 2020-11-13 19:59:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class CofeaFeatureSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "read_cofea_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("sample_from_attr"):
      attrs.add(self._config.get("sample_from_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    key_score_attrs = set(self._config.get("key_score_features", []))
    common_features = set(self._config.get("extract_common_features", [])) - key_score_attrs
    attrs = set()
    attrs.update(f"{name}_key" for name in key_score_attrs)
    attrs.update(f"{name}_score" for name in key_score_attrs)
    attrs.update(common_features)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    key_score_attrs = set(self._config.get("key_score_features", []))
    item_features = set(self._config.get("extract_item_features", [])) - key_score_attrs
    attrs = set()
    attrs.update(f"{name}_key" for name in key_score_attrs)
    attrs.update(f"{name}_score" for name in key_score_attrs)
    attrs.update(item_features)
    return attrs


class CofeaPairItemCombineRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cofea_sample_combine_pair"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    # check_arg 这一方法类似于 assert，判断结果是否为真，为假的话会报错，报错内容为第二个参数的字符串
    check_arg(self._config.get("slot_id_list"), "`slot_id_list` 是必选项")
    check_arg(self._config.get("label_attr_list"), "`label_attr_list` 是必选项")
    check_arg(len(self._config.get("slot_offset_list", []))==self._config.get("pair_size", 2), "`slot_offset_list`的长度 和`pair_size` 需相同")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return False
