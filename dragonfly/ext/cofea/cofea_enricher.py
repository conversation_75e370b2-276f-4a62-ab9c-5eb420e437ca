#!/usr/bin/env python3
"""
filename: cofea_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for cofea
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-11-09 11:34:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class CofeaFeatureJointExampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cofea_feature_joint_example"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("from_extra_var"):
      attrs.add(self._config.get("from_extra_var"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["btq_hash_attr"]])

class CofeaFeatureSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_cofea_sample"

  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("merge_sample_from"):
      check_arg(self._config.get("timestamp_from"), "merge_sample_from 未配的情况下必须配置 timestamp_from")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["merge_sample_from", "timestamp_from", "dynamic_common_attrs_names", "dynamic_item_attrs_names"]:
      value = self._config.get(key, "")
      if value:
        attrs.add(value)

    for key in ["dump_common_attrs", "common_slots_from", "common_signs_from"]:
      values = self._config.get(key, [])
      if values:
        attrs.update(values)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config["dump_to_attr"]])

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["dump_item_attrs", "item_slots_from", "item_signs_from"]:
      values = self._config.get(key, [])
      if values:
        attrs.update(values)
    return attrs

class CofeaFeatureFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cofea_feature_fetcher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("primary_key_from"):
      attrs.add(self._config.get("primary_key_from"))
    if self._config.get("timestamp_from"):
      attrs.add(self._config.get("timestamp_from"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr_features = set(self._config.get("attr_features", []))
    attrs.update(attr_features)
    score_features = set(self._config.get("score_features", []))
    attrs.update(score_features)
    return attrs

class ColossusFeatureFetchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_feature_fetcher"
  
  def collect_all_feature_names(self):
    attrs = set()
    table_name_map = self._config.get("table_name_map", dict())
    for name_space, table in self._config.get("attr_features", {}).items():
      for table_name, feature_names in table.items():
        attrs.update({"{}.{}.{}".format(name_space, table_name, feature_name) for feature_name in feature_names})
    for name_space, table in self._config.get("score_features", {}).items():
      for table_name, feature_names in table.items():
        attrs.update({"{}.{}.{}".format(name_space, table_name, feature_name) for feature_name in feature_names})
    attrs_new = set()
    for attr in attrs:
      for k, v in table_name_map.items():
        if attr.startswith(k):
          attrs_new.add(v + attr[len(k):])
          break
      else:
        attrs_new.add(attr)
    return attrs_new

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr") and self._config.get("primary_key_from"):
      attrs.add(self._config.get("primary_key_from"))
    if self._config.get("timestamp_from"):
      attrs.add(self._config.get("timestamp_from"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr"):
      attrs.update(self.collect_all_feature_names())
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr") and self._config.get("primary_key_from"):
      attrs.add(self._config.get("primary_key_from"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr"):
      attrs.update(self.collect_all_feature_names())
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True
