#!/usr/bin/env python3
# coding=utf-8
"""
filename: cofea_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, cofea api mixin
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-11-09 11:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .cofea_enricher import *
from .cofea_retriever import *
from .cofea_observer import *

class CofeaApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 cofea 相关的 Processor 接口:
  - JointCofeaEnricher
  """
  def cofea_feature_joint_example(self, **kwargs):
    """
    CofeaFeatureJointExampleEnricher
    ------
    从 context 的 ks::reco::feature_pipe::SequenceExample PB 中触发

    从 SequenceExample.context 中取出 user_id 和 time_ms，在 cofea server 中查出对应的 feature 填充回 SequenceExample.context

    注意事项：
    - 处理过程中会从 context 中取出 const pb pointer，并转成 pb pointer，更新里面的字段

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 ks::reco::feature_pipe::SequenceExample

    `btq_hash_attr`: [string] SequenceExample 中的一个字段，send_with_btq 的时候用来计算 btq topic shard

    `service_name`: [string] Cofea Server 注册的 kess service name

    调用示例
    ------
    ``` python
    .cofea_feature_joint_example(
      from_extra_var="tf_sequence_example",
      save_result_to="tf_sequence_example",
      btq_hash_attr="device_id",
      service_name="grpc_cofeaTest")
    ```
    """
    self._add_processor(CofeaFeatureJointExampleEnricher(kwargs))
    return self

  def read_cofea_sample(self, **kwargs):
    """
    CofeaFeatureSampleRetriever
    ------
    从 BatchedSamples 结构的 flatbuffers 序列化字符串中反解出样本和特征数据，样本作为 item 加入结果集，特征作为 attr 写入 context

    参数配置
    ------
    `sample_from_attr`: [string] 从指定的 string common_attr 中获取 BatchedSamples 的 flatbuffers 序列化字符串

    `save_primary_key_to`: [string] 将 primary_key 保存到 context 的哪个属性上

    `id_feature`: [string] 用哪个 feature 值作为样本 item 的 item_id (同 item_key)

    `user_id_feature`: [string] 用某个 feature 值作为 user_id 覆盖当前的 user_id，默认不覆盖。

    `device_id_feature`: [string] 用某个 feature 值作为 device_id 覆盖当前的 device_id，默认不覆盖。

    `extract_common_features`: [list] 需要从 BatchedSamples.context 中抽取哪些特征写入 common_attr

    `extract_item_features`: [list] 需要从 BatchedSamples.sub_context 和 BatchedSamples.samples 中抽取哪些特征写入 item_attr

    `key_score_features`: [list] 选配项，如果 `extract_common_features` 和 `extract_item_features` 中有 SimpleKeyScoreList 类型的 feature，需在该配置中列举出来（仅用于帮助 Dragonfly 进行属性依赖分析）

    `reason`: [int] 选配项，召回原因，默认为 0

    `time_unit`: [string] RawSamplePackage 的 timestamp 字段的单位，默认为 ms，也支持 us 和 s。

    调用示例
    ------
    ``` python
    .read_cofea_sample(
      sample_from_attr="batched_sample",
      id_feature="pid",
      extract_common_features=["click_list_30D"],
      extract_item_features=["click_user_list_30D"],
    )
    ```
    """
    self._add_processor(CofeaFeatureSampleRetriever(kwargs))
    return self

  def dump_cofea_sample(self, **kwargs):
    """
    CofeaFeatureSampleEnricher
    ------
    将 context 和 item 的 attr 写入 BatchedSamples 结构的 flatbuffers 并序列化为字符串

    BatchedSamples 的定义请参考 svn 的 ks/cofea/proto/samples.proto

    注意事项：
    - 如果输出的结果希望被 `read_cofea_sample` 解析，一定要将代表 item 个数的特殊字段 `__inner_sample_length__` 放入 `dump_common_attrs`

    参数配置
    ------
    `dump_to_attr`: [string] 把新生成的 BatchedSamples 结构用 flatbuffers 序列化至指定的 string common_attr 中

    `dump_common_attrs`: [list] 选配项，需要从 context 中 dump 哪些 common_attr 进入新的 BatchedSamples 结构

    `dynamic_common_attrs_names`: [string] 选配项, 指定从哪个common attrs中获取动态增加的common attrs

    `dynamic_item_attrs_names`: [string] 选配项, 指定从哪个common attrs中获取动态增加的item attrs

    `dump_item_attrs`: [list] 选配项，需要从 context 中 dump 哪些 item_attr 进入新的 BatchedSamples 结构

    `merge_sample_from`: [string] 选配项，从指定的 string common_attr 中获取 BatchedSamples 进行合并

    `primary_key_from`: [string] 选配项，用指定的 common_attr 值作为新 BatchedSamples 的 primary_key. 注意该配置没有生效，如果需要使用该功能，请使用 `pk_from`

    `pk_from`: [string] 选配项，用指定的 common_attr 值作为新 BatchedSamples 的 primary_key. 注意本来该功能应当由 `primary_key_from` 实现，但是实际上 `primary_key_from` 并没有生效, 为了保证兼容性, 新增了该字段。primary_key 的优先级如下: `pk_from` 最高; `merge_sample_from` 其次; 默认值(computed from user_id and device_id) 最低

    `timestamp_from`: [string] 选配项（但 merge_sample_from_attr 若为空则必配），用指定的 common_attr 值作为新 BatchedSamples 的 timestamp

    `common_slots_from`: [list] 选配项，从哪个 common_attr 中读出 common_slots 列表

    `common_signs_from`: [list] 选配项，从哪个 common_attr 中读出 common_signs 列表

    `item_slots_from`: [list] 选配项，从哪个 item_attr 中读出 item_slots 列表

    `item_signs_from`: [list] 选配项，从哪个 item_attr 中读出 item_signs 列表


    调用示例
    ------
    ``` python
    .copy_user_meta_info(save_result_size_to_attr="__inner_sample_length__")\
    .dump_cofea_sample(
      dump_to_attr="new_batched_samples",
      dump_common_attrs=["click_list_60D", "__inner_sample_length__"],
      dump_item_attrs=["click_user_list_60D"],
      merge_sample_from="batched_sample",
    )
    ```
    """
    self._add_processor(CofeaFeatureSampleEnricher(kwargs))
    return self

  def cofea_feature_fetcher(self, **kwargs):
    """
    CofeaFeatureFetchEnricher
    ------

    从 context 中取出 cofea_key 和 cofea_timestamp ，在 cofea server
    
    中查出对应的 feature 填充回 context

    参数配置
    ------
    `attr_features`: [list[string]] 必配项，用于过滤 cofea server 返回的 feature，只有出现在 attr_features 中的 feature 才会写入到 context 中

    `score_features`: [list[string]] 必配项，用于过滤 cofea server 返回的 score feature，只有出现在 score_features 中的  score feature 才会写入到 context 中

    `service_name`: [string] 必配项，Cofea Server 注册的 kess service name

    `primary_key_from`: [string] 选配项，用指定的 common_attr 值作为查询 cofea server 的 primary_key 。若未配，则尝试取 context->GetUserId() 或者 CityHash(context->GetDeviceId()），

    `timestamp_from`: [string] 选配项，用指定的 common_attr 值作为查询 cofea server 的 timestamp 。若未配，则尝试取 context->GetRequestTime()

    `max_retry`: [string] 选配项，首次查询 cofea server 失败后最大重试次数，默认不配就不重试

    调用示例
    ------
    ``` python
    .cofea_feature_fetcher(
      attr_features=["last60DayXX"],
      score_features=["last60DayXX_score", "last60DayXX_key"],
      service_name="grpc_cofea",
      primary_key_from="user_id",
      timestamp_from="time_ms",
      max_retry=1
    )
    ```
    """
    self._add_processor(CofeaFeatureFetchEnricher(kwargs))
    return self

  def fetch_colossus_feature(self, **kwargs):
    """
    ColossusFeatureFetchEnricher
    ------

    从 context/item 中取出 primary_key ，在 colossus server

    中查出对应的 feature 填充回 context/item

    参数配置
    ------
    `is_common_attr`: [bool] 是否查询 common 属性，该字段与 primary_key_from 配合使用，指定了 primary_key_from 来自 context 侧还是 item 侧

    `attr_features`: [dict] 用于指定查询的 feature，需要指定特征的 name_space 和 table_name ，格式为 {"name_space1": {"table_name1": ["feature_name1", "feature_name_2"], ...}, ...}

    `score_features`: [dict] 用于指定查询的 score feature，格式和 `attr_features` 相同，与 `attr_features` 至少配一个。

    `primary_key_from`: [string] 选配项，用指定的 common_attr/item_attr 值作为查询 colossus server 的 primary_key。若未配，则尝试取 context->GetUserId() 或者 CityHash(context->GetDeviceId()）

    `max_retry`: [int] 选配项，首次查询失败后最大重试次数，默认不配就不重试

    `async_mode`: [int] 选配项，异步模式，默认 0，支持如下模式
      - 0 同步模式
      - 1 异步模式

    `timestamp_from`: [string] 选配项，用指定的 common_attr 值作为查询时间戳 (unix timestamp ，单位 second) ，可以防止特征穿越，仅对实时特征 (feart) 有效。如果不设置，使用查询时的时间戳

    `table_name_map`: [dict] 选配项，特征查询回来后，将特征名的前缀部分（表名）做映射，默认不做映射。key 必须满足 `<name_space>.<table_name>.` 的格式, value 为任意字符串

    `timeout_ms`: [int] 选配项，rpc请求的超时时间，单位 ms. 默认值由 gflag 控制 (colossus_feature_client_timeout_ms, 默认 5000)

    调用示例
    ------
    ``` python
    .fetch_colossus_feature(
      is_common_attr=True,
      attr_features={"ns_test": {"test_table": [
        "playActionList60D_photo_id",
        "__VERSION__",  # ns_test.test_table 的数据版本（一般表示生产日期）
        ]}},
      score_features={"ns_test": {"test_table": ["topClickPid1Day_key", "topClickPid1Day_score", "topLikeHetu3D_key", "topLikeHetu3D_score"]}},
      primary_key_from="user_id",
      max_retry=1
    )

    .fetch_colossus_feature(
      is_common_attr=True,
      attr_features={"ns_test": {"test_table": ["playActionList60D_photo_id", "__VERSION__"]}},
      primary_key_from="user_id",
      max_retry=1,
      async_mode=1,  # 异步 processor
      table_name_map={
        "ns_test.test_table.": "",  # 查询回来后，将 ns_test.test_table 表的所有特征名称的表名部分去除
      }
    )

    .fetch_colossus_feature(
      is_common_attr=True,
      attr_features={"ns_test": {"test_realtime": ["__VERSION__"]}},
      score_features={"ns_test": {"test_realtime": ["UserAidPlay30m_key", "UserAidPlay30m_score", "topLikeHetu3D_key", "topLikeHetu3D_score"]}},
      primary_key_from="primary_key",
      max_retry=1,
      timestamp_from="time_s"
    )
    ```
    """
    self._add_processor(ColossusFeatureFetchEnricher(kwargs))
    return self

  def print_cofea_sample(self, **kwargs):
    """
    CofeaFeatureSampleObserver
    ------
    读取 BatchedSamples 结构的 flatbuffers 序列化字符串，将结果打印到标准输出

    参数配置
    ------
    `sample_from_attr`: [string] 从指定的 string common_attr 中获取 BatchedSamples 的 flatbuffers 序列化字符串

    `to`: [string] 输出目标，支持 glog，stdout，stderr。默认为 glog。

    调用示例
    ------
    ``` python
    .print_cofea_sample(
      sample_from_attr="batched_sample",
    )
    ```
    """
    self._add_processor(CofeaFeatureSampleObserver(kwargs))
    return self

  def perflog_cofea_sample(self, **kwargs):
    """
    CofeaFeatureSamplePerflogObserver
    ------
    读取 BatchedSamples 结构的 flatbuffers 序列化字符串，上报统计信息

    参数配置
    ------
    `sample_from_attr`: [string] 必配项，从指定的 string common_attr 中获取 BatchedSamples 的 flatbuffers 序列化字符串

    `check_point`: [string] [动态参数] 必配项，自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `perf_base`: [int] 选配项，打点时的放大倍数，用于保留小数点，默认为 1000000L，注意修改这个值会导致 Grafana 上打点显示异常，使用时需要注意
  
    调用示例
    ------
    ``` python
    .perflog_cofea_sample(
      sample_from_attr="batched_sample",
      check_point="default.sample"
    )
    ```
    """
    self._add_processor(CofeaFeatureSamplePerflogObserver(kwargs))
    return self
  
  def cofea_sample_combine_pair(self, **kwargs):
    """
    CofeaPairItemCombineRetriever
    ------
    在send_to_mio_learner前已经处理好的样本item 组成pair，slot特征加偏置，label加suffix 作为 attr 写入 context

    参数配置
    ------
    `slot_id_list`: [list]必配项 需要保留哪些slots 偏置后存入attr

    `label_attr_list`: [list]必配项 需要保留哪些label 会在加suffix后存入attr, suffix为 `_idx0, _idx1`..

    `slot_offset_list`: [list] 必配项 每个idx对应的slot的偏置值 e.g. slot_offset_list=[0,10000],combine后为[item1,item2] 则在combine_item中 item1的所有slot偏置0 item2的所有slot偏置10000

    `pair_size`: [int] 选配项， 从现有item中 组合成pair_size大小 的pair 默认2

    `filter_same_item_pair`: [bool] 选配项， 是否过滤掉相同item的组合，比如[item1, item1] 默认为false

    `save_combine_item_flag_to`: [string] 选配项， 标记是否为组合 item 的 attr

    调用示例
    ------
    ``` python
    .cofea_sample_combine_pair(
      slot_id_list=[1,7,9,51,720],
      label_attr_list=["is_click"],
      slot_offset_list=[0,10000],
      filter_same_item_pair=True,
      save_combine_item_flag_to="is_combine_result"
    )
    ```
    """
    self._add_processor(CofeaPairItemCombineRetriever(kwargs))
    return self
