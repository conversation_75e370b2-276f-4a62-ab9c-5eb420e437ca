#!/usr/bin/env python3
# coding=utf-8
"""
filename: cofea_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, cofea observer module
author: <EMAIL>
date: 2020-11-13 19:59:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class CofeaFeatureSampleObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "print_cofea_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("sample_from_attr"):
      attrs.add(self._config.get("sample_from_attr"))
    return attrs

class CofeaFeatureSamplePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "perflog_cofea_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("sample_from_attr"):
      attrs.add(self._config.get("sample_from_attr"))
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    """ 检查配置是否合法 """
    check_arg("sample_from_attr" in self._config, f"{self.get_type_alias()} 缺少 \"sample_from_attr\" 配置")
    check_arg("check_point" in self._config, f"{self.get_type_alias()} 缺少 \"check_point\" 配置")
