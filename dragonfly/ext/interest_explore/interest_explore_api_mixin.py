#!/usr/bin/env python3
# coding=utf-8
"""
filename: interest_explore_api_mixin.py
description:
author: fengqi<PERSON><PERSON>@kuaishou.com
date: 2022-09-22 10:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .interest_explore_enricher import *

class InterestExploreApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 interest explore / slide window 独立链路 相关的 Processor 接口

  背景: 兴趣丢失/侧滑小窗独立链路，调用各种模型和后处理 

  场景: 精选和极速版

  维护人: fengqinlin

  """

  def interest_next_reward_model(self, **kwargs):
    """
    InterestNextRewardModelEnricher

    未来收益最大化模型
    """
    self._add_processor(InterestNextRewardModelEnricher(kwargs))
    return self
  
  def interest_enhance_reward_seq_from_redis(self, **kwargs):
    """
    InterestEnhanceRewardSeqFromRedisEnricher
    从redis获取用户兴趣探索正反馈序列
    """
    self._add_processor(InterestEnhanceRewardSeqFromRedisEnricher(kwargs))
    return self

  def interest_cluster_distance_model(self, **kwargs):
    """
    InterestClusterDistanceModelEnricher
    从redis获取用户河图距离，计算当前视频的历史新颖性
    """
    self._add_processor(InterestClusterDistanceModelEnricher(kwargs))
    return self

  def slide_window_author_photo_server(self, **kwargs):
    """
    SlideWindowAuthorPhotoServerEnricher
    获取用户去重后的作品列表
    """
    self._add_processor(SlideWindowAuthorPhotoServerEnricher(kwargs))
    return self

  def slide_window_photo_info_server(self, **kwargs):
    """
    SlideWindowPhotoInfoServerEnricher
    获取生产侧的作品信息
    """
    self._add_processor(SlideWindowPhotoInfoServerEnricher(kwargs))
    return self

  def slide_window_serial_photo_server(self, **kwargs):
    """
    SlideWindowSerialPhotoServerEnricher
    根据视频id获取当前视频的合集视频
    """
    self._add_processor(SlideWindowSerialPhotoServerEnricher(kwargs))
    return self

  def slide_window_realshow_photo_server(self, **kwargs):
    """
    SlideWindowRealshowPhotoListEnricher
    获取侧滑realshow的 realshow_photo_id
    """
    self._add_processor(SlideWindowRealshowPhotoServerEnricher(kwargs))
    return self

  def slide_window_author_photo_down_server(self, **kwargs):
    """
    SlideWindowAuthorPhotoDownServerEnricher
    获取作者下滑【老的】作品列表，【传入 realshow_photo_list 去重】
    """
    self._add_processor(SlideWindowAuthorPhotoDownServerEnricher(kwargs))
    return self

  def slide_window_author_photo_up_server(self, **kwargs):
    """
    SlideWindowAuthorPhotoUpServerEnricher
    获取作者上滑[新的]的作品列表，[传入 realshow_photo_list 去重]
    """
    self._add_processor(SlideWindowAuthorPhotoUpServerEnricher(kwargs))
    return self

  def slide_window_result_serial_protect_server(self, **kwargs):
    """
    侧滑排序结果的合集保护
    获取最终的排序结果，以及对用视频是否为合集
    """
    self._add_processor(SlideWindowResultSerialProtectServerEnricher(kwargs))
    return self
