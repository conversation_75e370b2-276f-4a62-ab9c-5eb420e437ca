#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class InterestNextRewardModelEnricher(LeafEnricher):
  """
  精选页重排 model rerank
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "interest_next_reward_model"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_loss_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_reward_weight")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_maxsize")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_usertype")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_nextreward_model_fill_fullrankpxtr")))
    attrs.add(self._config.get("send_user_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs
  
class InterestEnhanceRewardSeqFromRedisEnricher(LeafEnricher):
  """
    探索兴趣巩固，获取用户正反馈序列
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "interest_enhance_reward_seq_from_redis"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("redis_cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("redis_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_enahnce_time_limit_second")))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("interest_explore_enhance_aid_list_attr_name"))
    attrs.add(self._config.get("interest_explore_enhance_pid_list_attr_name"))
    attrs.add(self._config.get("interest_explore_enhance_timestamp_list_attr_name"))
    return attrs

class InterestClusterDistanceModelEnricher(LeafEnricher):
  """
  河图clusterId 距离计算
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "interest_cluster_distance_model"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("redis_cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("redis_prefix")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_cluster_num_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_cluster_time_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_cluster_use_click")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_cluster_time_segment_weight")))
    attrs.add(self._config.get("send_user_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class SlideWindowAuthorPhotoServerEnricher(LeafEnricher):
  """
  获取作者的作品列表，[通过redis 或者 传入 realshow_photo_list 去重]
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_author_photo_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("redis_cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_timeout_ms")))
    attrs.add(self._config.get("slide_window_author_id"))
    attrs.add(self._config.get("redis_key"))
    attrs.add(self._config.get("use_redis_for_dup"))
    attrs.add(self._config.get("slide_window_limit_maxsize"))
    attrs.add(self._config.get("realshow_photo_list"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_browset_output_attr_name"))
    attrs.add(self._config.get("slide_window_author_photo_list_output_attr_name"))
    attrs.add(self._config.get("slide_window_photo_upload_time_output_attr_name"))
    return attrs

class SlideWindowPhotoInfoServerEnricher(LeafEnricher):
  """
  获取生产侧的作品信息
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_photo_info_server"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_photo_info_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_photo_info_service_timeout_ms")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class SlideWindowSerialPhotoServerEnricher(LeafEnricher):
  """
  获取视频的合集视频
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_serial_photo_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_serial_photo_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_serial_photo_service_timeout_ms")))
    attrs.add(self._config.get("realshow_photo_list"))
    attrs.add(self._config.get("slide_window_serial_photo_req_index_name"))
    attrs.add(self._config.get("slide_window_serial_photo_req_num_name"))
    attrs.add(self._config.get("slide_window_entrance_photo_id"))
    attrs.add(self._config.get("slide_window_author_id"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_serial_photo_index_output_attr_name"))
    attrs.add(self._config.get("slide_window_serial_photo_list_output_attr_name"))
    attrs.add(self._config.get("slide_window_serial_all_photos_output_attr_name"))
    return attrs

class SlideWindowRealshowPhotoServerEnricher(LeafEnricher):
  """
  获取侧滑realshow的realshow_photo_id
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_realshow_photo_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("redis_cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("redis_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.add(self._config.get("use_redis_for_dup"))
    attrs.add(self._config.get("realshow_photo_list"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_browset_output_attr_name"))
    return attrs

class SlideWindowAuthorPhotoDownServerEnricher(LeafEnricher):
  """
  获取作者下滑[老的]的作品列表，[传入 realshow_photo_list 去重]
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_author_photo_down_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_timeout_ms")))
    attrs.add(self._config.get("slide_window_author_id"))
    attrs.add(self._config.get("slide_window_photo_timestamp"))
    attrs.add(self._config.get("slide_window_limit_maxsize"))
    attrs.add(self._config.get("realshow_photo_list"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_author_photo_list_output_attr_name"))
    attrs.add(self._config.get("slide_window_photo_upload_time_output_attr_name"))
    return attrs

class SlideWindowAuthorPhotoUpServerEnricher(LeafEnricher):
  """
  获取作者下滑[新的]的作品列表，[传入 realshow_photo_list 去重]
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_author_photo_up_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_author_photo_service_timeout_ms")))
    attrs.add(self._config.get("slide_window_author_id"))
    attrs.add(self._config.get("slide_window_photo_timestamp"))
    attrs.add(self._config.get("slide_window_limit_maxsize"))
    attrs.add(self._config.get("realshow_photo_list"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_author_photo_list_output_attr_name"))
    attrs.add(self._config.get("slide_window_photo_upload_time_output_attr_name"))
    return attrs

class SlideWindowResultSerialProtectServerEnricher(LeafEnricher):
  """
  侧滑排序结果的合集保护
  input: photo_ids [xx,xx,xx], [1,2,3],slide_author_id
  其中1：上滑合集，2代表合集视频, 3代表下滑非合集
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_window_result_serial_protect_server"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_serial_photo_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("slide_window_serial_photo_service_timeout_ms")))
    # 暂时使用 entrance_photo_id 作为时间标记，大于这个 pid 认为是最新视频 
    attrs.add(self._config.get("slide_window_entrance_photo_id"))
    attrs.add(self._config.get("realshow_photo_list"))
    attrs.add(self._config.get("slide_window_select_photo_list_name"))
    attrs.add(self._config.get("slide_window_select_photo_sign_name"))
    attrs.add(self._config.get("slide_window_author_id"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("slide_window_final_photo_list_output_attr_name"))
    attrs.add(self._config.get("slide_window_final_photo_sign_output_attr_name"))
    return attrs
