#!/usr/bin/env python3
# coding=utf-8
"""
filename: livestream_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,livestream api mixin
author: ch<PERSON><PERSON><PERSON>@kuaishou.com
date: 2020-12-14 16:54:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .livestream_retriever import *
from .livestream_enricher import *
from .livestream_arranger import *
from .livestream_observer import *
from .livestream_mixer import *

class LivestreamApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 livestream 相关的 Processor 接口
  """

  def livestream_embedding_retrieve(self, **kwargs):
    """
    LivestreamEmbeddingRetriever 
    ------
    直播 leaf 中使用 CommonEmbeddingKessClient 进行 ann 召回

    参数配置
    ------
    `need_fetch_user_emb`: [bool] [动态参数] 必配. 是否需要从predict服务获取user embedding用于u2i触发

    `only_fetch_user_emb`: [bool] 是否只获取user embedding

    `export_user_emb_to_attr`: [string] user embedding 导出到的 CommonAttr name

    `extra_user_attr`: [string] 增加额外的 user attr 的 StringList

    `user_emb_config`: [dict]  user embedding 服务相关配置

      - `user_emb_layer_name`: [string] [动态参数] 必配.获取哪一层的 user embedding

      - `user_emb_service_name`: [string] [动态参数] 必配.服务 kess 名字

      - `kuiba_user_attrs_from_attr`: [string] 从哪个 CommonAttr 获取 SampleAttr 的 CommonAttr names

      - `fetch_user_emb_timeout_ms`: [int] 服务超时时间，默认值 10

    `top_k`: [int] [动态参数] 必配. 召回阶段最大召回的数目

    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `relevance_threshold`: [double] [动态参数], 召回 item 的相关性过滤阈值, 默认 2.5

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    调用示例
    ------
    ``` python
    .livestream_embedding_retrieve(
      need_fetch_user_emb = true,
      only_fetch_user_emb = false,
      extra_user_attr = ['name'],
      export_user_emb_to_attr = "name"
      user_emb_config = {
        "user_emb_layer_name": "weight",
        "user_emb_service_name": "{{ab_livestream_u2a_retr_predict_service}}",
        "kuiba_user_attrs_from_attr": "kuiba_user_attr_names",
        "fetch_user_emb_timeout_ms" : 10
      },
      top_k = 100,
      relevance_threshold = 2.5,
      kess_service = "{{ab_livestream_embedding_u2a_service}}",
      timeout_ms = 100,
      reason = Reason_u2a_embedding,
    )
    ```
    """
    self._add_processor(LivestreamEmbeddingRetriever(kwargs))
    return self


  def livestream_cf_retrieve(self, **kwargs):
    """
    LiveStreamCommonCfRetriever
    ------
    直播 leaf 中使用 ItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `enable_live_icf`: [bool] 是否icf小流量实验

    `config_key`: [string] [动态参数] 必配.召回服务的 config key name

    `save_left_trigger_id_to`: [动态参数] 必配.召回服务的 left trigger

    `enable_icf_follow_list`: [bool] [动态参数] 必配. 小流量实验开关

    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    `user_info_attr`: [string] 必配. user profile 信息，从common attr中反序列化得到

    调用示例
    ------
    ``` python
    .livestream_cf_retrieve(
        reason = Reason_follow_cf_retrieval,
        enable_live_icf = False,
        user_info_attr = "user_info_attr",
        kess_service = "livestream_follow_cf_retr_service",
        config_key = "livestream_follow_cf_config_key",
        save_left_trigger_id_to = "left_trigger",
        enable_icf_follow_list = False,
        timeout_ms = 100
    ) 
    ```
    """
    self._add_processor(LiveStreamCommonCfRetriever(kwargs))
    return self

  def livestream_live_itemcf_retrieve(self, **kwargs):
    """
    LiveStreamLiveItemCfRetriever
    ------
    直播 leaf 中使用 LiveItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `enable_live_icf`: [bool] 是否icf小流量实验

    `config_key`: [string] [动态参数] 必配.召回服务的 config key name

    `save_left_trigger_id_to`: [动态参数] 必配.召回服务的 left trigger

    `enable_icf_follow_list`: [bool] [动态参数] 必配. 小流量实验开关

    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    `user_info_attr`: [string] 必配. user profile 信息，从common attr中反序列化得到

    调用示例
    ------
    ``` python
    .livestream_live_itemcf_retrieve(
        reason = Reason_follow_cf_retrieval,
        enable_live_icf = False,
        user_info_attr = "user_info_attr",
        kess_service = "livestream_follow_cf_retr_service",
        config_key = "livestream_follow_cf_config_key",
        save_left_trigger_id_to = "left_trigger",
        enable_icf_follow_list = False,
        timeout_ms = 100
    )
    ```
    """
    self._add_processor(LiveStreamLiveItemCfRetriever(kwargs))
    return self

  def livestream_single_cf_retrieve(self, **kwargs):
    """
    LiveStreamCommonSingleCfRetriever
    ------
    直播 leaf 中使用 ItemCFKessClient 进行 item cf 召回

    参数配置
    ------
    `config_key`: [string] [动态参数] 必配.召回服务的 config key name

    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    `trigger_type_attr`: [string] 必配

    调用示例
    ------
    ``` python
    .livestream_single_cf_retrieve(
        reason = Reason_livecard_icf_retrieval,
        trigger_type_attr = "trigger_type",
        kess_service = "livestream_follow_cf_retr_service",
        config_key = "livestream_follow_cf_config_key",
        timeout_ms = 100
    ) 
    ```
    """
    self._add_processor(LiveStreamCommonSingleCfRetriever(kwargs))
    return self

  def livestream_fm_retrieve(self, **kwargs):
    """
    LiveStreamCommonFmRetriever
    ------
    直播 leaf 中使用 FmKessClient 进行 fm 召回

    参数配置
    ------
    `kess_service`: [string] [动态参数] 必配.召回服务的 kess service name

    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 100

    `reason`: [int] 召回原因，默认为 0

    `user_info_attr`: [string] 必配. user profile 信息, 从common attr中反序列化得到

    `retr_num`: [string] 必配. 请求返回的召回 item 数量

    `search_num`: [string] 必配. fm召回需要搜索的结果集范围

    调用示例
    ------
    ``` python
    .livestream_fm_retrieve(
        reason = Reason_cotrain_fm,
        user_info_attr = "user_info_attr",
        kess_service = "livestream_cotrain_fm_retr_service",
        retr_num = 20,
        search_num = 1000,
        timeout_ms = 100
    ) 
    ```
    """
    self._add_processor(LiveStreamCommonFmRetriever(kwargs))
    return self

  def livestream_follow_retrieve(self, **kwargs):
    """
    LiveStreamFollowRetriever 
    ------
    直播 leaf 中通过 user_info 中携带的关注信息召回

    参数配置
    ------
    `reason`: [int] 召回原因，默认为 0

    `user_info_attr`: [string] 必配. user profile 信息, 存储序列化后的 user_info 的 common_attr

    调用示例
    ------
    ``` python
    .livestream_follow_retrieve(
        reason = Reason_follow_retrieval,
        user_info_attr = "user_info_attr",
    ) 
    ```
    """
    self._add_processor(LiveStreamFollowRetriever(kwargs))
    return self

  def livestream_common_calc_score(self, **kwargs):
    """
    LiveStreamCommonCalcScoreEnricher
    ------
    包含单列直播 leaf 召回阶段后各种特化业务逻辑, 主要分俩部分功能
    1.解析 user_info 和 redis 数据，初始化后面过滤、排序等业务逻辑所必须用到的 CommonAttr & ItemAttr
    2.使用用户历史行为和各路召回结果，结合权重计算 item 的得分

    参数配置
    ------
    `livestream_score_save_to`: [string] [动态参数] 必配.存储召回阶段最终分数的 item_attr

    `user_info_attr`: [string] 必配. 存储序列化后的 user_info 的 common_attr

    `feed_livestream_realshow_live_size_min_cnt`: [int] [动态参数] 必配. 用于控制判断 user 是否是 live_hate_user

    `feed_livestream_preview_playing_time_threshold`: [int] [动态参数] 必配. 用于控制判断 user 是否是 live_hate_user

    `author_time_bound_hour`: [int] [动态参数] 必配. 用于控制主播直播推送给用户的时间间隔(小时)

    `enable_slide_live_room_browse_use_realshow`: [bool] [动态参数] 必配.控制直播间透出是否使用 realshow 做 browseset 过滤

    `merchant_page_bound`: [int] [动态参数] 必配.电商直播的推送相关阈值
    
    `live_profile_effective_watch_time_thres`: [int] [动态参数] 必配.电商直播的推送相关阈值

    `live_profile_short_watch_time_thres`: [int] [动态参数] 必配.判断 user_info.live_profile 中观看时长相关阈值
    
    `merchant_count_bound`: [int] [动态参数] 必配.判断 user_info.live_profile 中观看时长相关阈值

    `enable_userinfo_calc_decay`: [bool] [动态参数] 必配.是否让用户行为的权重随时间按指数衰减，如打开则每种行为每过两天权重衰减至两天前权重的 1/e

    `liveroom_action_list_truncate_day_gap`: [int] [动态参数] 必配.发生时间距今超过 day_gap 的行为将被忽略.需要大于0

    `thanos_live_trigger_weights`: [string] [动态参数] 必配.各个用户行为和各路召回占最终得分的权重。格式较复杂，修改请务必再三确认

    调用示例
    ------
    ``` python
    .livestream_common_calc_score(
        livestream_score_save_to = "livestream_score",
        user_info_attr = "user_info_attr",
        // 以下为数据初始化过程使用的ab参数
        feed_livestream_realshow_live_size_min_cnt = "{{feed_livestream_realshow_live_size_min_cnt}}",
        feed_livestream_preview_playing_time_threshold = "{{feed_livestream_preview_playing_time_threshold}}",
        author_time_bound_hour = "{{author_time_bound_hour}}",
        enable_slide_live_room_browse_use_realshow = "{{enable_slide_live_room_browse_use_realshow}}",
        merchant_page_bound = "{{merchant_page_bound}}",
        merchant_count_bound = "{{merchant_count_bound}}",
        live_profile_effective_watch_time_thres = 60000,
        live_profile_short_watch_time_thres = 3000,
        // 以下为CalcScore过程使用的 ab 参数
        enable_userinfo_calc_decay = "{{enable_userinfo_calc_decay}}",
        liveroom_action_list_truncate_day_gap = "{{liveroom_action_list_truncate_day_gap}}",
        thanos_live_trigger_weights = "{{thanos_live_trigger_weights}}"
    ) 
    ```
    """
    self._add_processor(LiveStreamCommonCalcScoreEnricher(kwargs))
    return self

  def livestream_ensemble_sort_arranger(self, **kwargs):
    """
    LivestreamEnsSortArranger 
    ------
    单列直播请求各个预估模型后的排序逻辑

    参数配置
    ------
    `user_info_attr`: [string] 必配. user profile 信息, 存储序列化后的 user_info 的 common_attr

    调用示例
    ------
    ``` python
    .livestream_ensemble_sort_arranger(
        user_info_attr = "user_info_attr",
    ) 
    ```
    """
    self._add_processor(LivestreamEnsSortArranger(kwargs))
    return self

  def livestream_weight_sample_arranger(self, **kwargs):
    """
    LivestreamWeightSampleArranger
    ------
    直播 leaf 中对主播池根据扶植流量做加权采样
    参数配置
    ------
    `size_limit`: [int] 必配. 加权采样的个数
    `speed_control`: [bool]. 是否做下发速率控制
    `completion_threshold`:[double]. 下发完成度
    `decelerate_rate`:[double]. 下发减速比例
    调用示例
    ------
    ``` python
    .livestream_ensemble_sort_arranger(
        size_limit = 20,
        speed_control = true,
        completion_threshold = 0.9,
        decelerate_rate = 0.5
    )
    ```
    """
    self._add_processor(LivestreamWeightSampleArranger(kwargs))
    return self

  def livestream_off_filters_enrich(self, **kwargs):
    """
    LiveStreamRetrievalOffFiltersEnricher
    ------
    根据 reason_list 对召回的直播结果添加过滤豁免列表 默认为 item_off_filter_list

    参数配置
    ------
    `reason_off_filter_map`: [list] 必配. 配置各个 reason 的 off_filters, off_filters 支持动态参数
    `off_filter_list_name`: [string] 必配.
    调用示例
    ------
    ``` python
    .livestream_off_filters_enrich(
        reason_off_filter_map = [
          {"reason": 100, "off_filters": "40,41"},
          {"reason": 200, "off_filters": "{{200_off_filters}}"},
          {"reason": 300, "off_filters": "43"},
        ],
        off_filter_list_name = "item_off_filter_list"
    )
    ```
    """
    self._add_processor(LiveStreamRetrievalOffFiltersEnricher(kwargs))
    return self

  def livestream_enrich_retrieval_filter_flag(self, **kwargs):
    """
    LiveStreamRetrievalFilterEnricher
    ------
    对召回的直播结果添加是否应该被召回过滤过滤掉的标志

    参数配置
    ------
    `unified_post_filter_mode`: [int] 选配. 默认为 0. 是否启用统一后置过滤模式. 启用后 reason/on_filters/off_filters 配置失效 并读取 item_off_filter_list 作为过滤豁免列表
    `off_filter_list_name`: [string] 随 unified_post_filter_mode 启用.
    `reason`: [int] 必配. 当前召回的 reason
    `on_filters`: [string] [动态参数] 可选. 在默认过滤基础上要增加的过滤集合
    `off_filters`: [string] [动态参数] 可选. 在默认过滤上要关闭的过滤集合
    `filter_flag_save_to`: [string] 必配. 结果作为 Item Attr 的属性名称
    调用示例
    ------
    ``` python
    .livestream_enrich_retrieval_filter_flag(
        reason = 80,
        on_filters = "0,1",
        off_filters = "3,5",
        filter_flag_save_to = "80_filter_flag"
    )
    ```
    ```
    .livestream_enrich_retrieval_filter_flag(
        unified_post_filter_mode = 1,
        off_filter_list_name = "item_off_filter_list",
        filter_flag_save_to = "80_filter_flag"
    )
    ```
    """
    self._add_processor(LiveStreamRetrievalFilterEnricher(kwargs))
    return self

  def livestream_model_retrieve(self, **kwargs):
    """
    LivestreamModelRetriever
    ------
    直播 leaf 中使用 PredictKessFetcher 进行 model 召回

    参数配置
    ------
    `kess_service`: [string] [动态参数] 必配项，召回服务的 kess service name
    `user_info_attr`: [string] 必配项，存储 user info 的 common attr
    `reason`: [int] 必配项，召回标识，添加到结果集的每个数据的 reason 属性取值
    `service_group`: [string] 选配项，默认为 "PRODUCTION"
    `timeout_ms`: [int] [动态参数] 选配项，默认为 300

    调用示例
    ------
    ``` python
    .livestream_model_retrieve(
      kess_service = "grpc_JinxuanLiveLrRetrievalServerExp1",
      user_info_attr = "user_info",
      reason = 1
    )
    ```
    """
    self._add_processor(LivestreamModelRetriever(kwargs))
    return self

  def livestream_common_kv_user_embedding(self, **kwargs):
    """
    LiveStreamCommonKVUserEmbeddingEnricher
    ------
    直播 leaf 中使用 PredictKessFetcher 进行 model 召回

    参数配置
    ------
    `user_id_attr`: [string] 必配项，存储 user id 的 common attr
    `kess_service`: [string] [动态参数] 必配项，服务的 kess service name
    `timeout_ms`: [string] [动态参数] 必配项，服务的 timeout limitation
    `bucket`: [string] [动态参数] 必配项，服务的 bucket name
    `service_group`: [string] 选配项，默认为 "PRODUCTION"

    调用示例
    ------
    ``` python
    .livestream_common_kv_user_embedding(
        user_id_attr = "_USER_ID_",
        kess_service = "grpc_liveFmAnnNoWlV2",
        timeout_ms = 50,
        bucket = "user",
        output_tensor_attr = "user_embedding")
    ```
    """
    self._add_processor(LiveStreamCommonKVUserEmbeddingEnricher(kwargs))
    return self

  def livestream_author_gnn_enrich(self, **kwargs):
    """
    LiveStreamAuthorGnnEnricher
    ------
    直播召回中心 author_gnn 召回使用的数据预处理

    参数配置
    ------
    `user_info_attr`: [string] 必配项，存储 user_info 的 common attr
    `export_common_attr`: [string] 必配项，预处理的数据输出的 common attr

    调用示例
    ------
    ``` python
    .livestream_author_gnn_enrich(
      user_info_attr = "user_info_attr",
      export_common_attr = "author_gnn_item_list")
    ```
    """
    self._add_processor(LiveStreamAuthorGnnEnricher(kwargs))
    return self

  def livestream_realtime_livereco_retrieve(self, **kwargs):
    """
    LiveStreamRealTimeLiveRecoRetriever
    ------
    本 Retriever 将自动解析所有召回中心的 reason_{}，并对命中老 Leaf 的 reason 请求
    RealTimeLiveRecoService 并解析返回的 RealTimeLiveRecoResponse，是一个多路召回。
    用于召回中心兼容老 Leaf 的请求， 召回结果已按照老 Leaf 的策略进行过滤。

    参数配置
    ------
    `kess_service`: [string] [动态参数] 必配项，召回服务的 kess service name
    `service_group`: [string] 选配项，默认为 "PRODUCTION"
    `real_time_live_reco_request`: [string] 必配项，RealTimeLiveRecoRequest格式的请求序列化成 string
    `real_time_live_reco_reasons`: [string] 必配项，多路召回的原因聚合成 string，如 "1,4,5,6"
    `timeout_ms`: [int]. 召回服务超时时间, 默认值为 300
    `recv_aid`:[string] 接收 author_id 的 item_attr 名称

    调用示例
    ------
    ``` python
    .livestream_realtime_livereco_retrieve(
      kess_service = "grpc_dryrunLiveRecommendRecoLeafV2",
      real_time_live_reco_request = "{{real_time_live_reco_request}}",
      recv_aid = "author_id",
    )
    ```
    """
    self._add_processor(LiveStreamRealTimeLiveRecoRetriever(kwargs))
    return self
  
  def livestream_redis_zrange_retrieve(self, **kwargs):
    """
    LiveStreamRedisZrangeRetriever
    ------
    本 Retriever 将redis中的zset结构的结果取出来，并根据score由低到高排序，根据召回的数量从zset结构中将一定数量的member召回回来

    参数配置
    ------
    `cluster_name`: [string] [动态参数] 必配项，redis集群名
    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限
    `use_bplus`: [bool] 动态参数，是否只召回b+主播
    `reason`: [int] 召回原因，默认为 0
    `key_from_attr`: [string] 从指定的string common_attr 中获取动态的redis key
  
    调用示例
    ------
    ``` python
    .livestream_redis_zrange_retrieve(
      cluster_name = "{{redis_cluster_name}}",
      retrieve_num = "{{}}",
      reason = self.reason,
      key_from_attr = "u_uid"
    )
    ```
    """
    self._add_processor(LiveStreamRedisZrangeRetriever(kwargs))
    return self

  def livestream_redis_lrange_enrich(self, **kwargs):
    """
    LiveStreamRedisLrangeEnricher
    ------
    本 Enricher 将redis中的list结构的结果取出来，将满足指定数量的主播以list形式返回。

    参数配置
    ------
    `cluster_name`: [string] [动态参数] 必配项，redis集群名
    `use_two_key`: [bool] [动态参数]，是否使用两个key进行两次查询
    `time_mins`: [int] [动态参数]，分钟
    `first_value_limit`: [int] [动态参数] 必配项，first value的限定数目
    `second_value_limit`: [int] [动态参数] 必配项，second value的限定数目
    `key_from_first_attr`: [string] [动态参数] 从指定的string common_attr 中获取动态的redis key
    `key_from_second_attr`: [string] [动态参数] 从指定的string common_attr 中获取动态的redis key
    `output_attr_name`: [string] 输出的int_list类型的common attr
  
    调用示例
    ------
    ``` python
    .livestream_redis_lrange_enrich(
      cluster_name = "{{redis_cluster_name}}",
      use_two_key = False,
      time_mins = 5,
      first_value_limit = 5,
      second_value_limit = 1,
      key_from_first_attr = "{{key_from_first_attr}}",
      key_from_second_attr = "{{key_from_second_attr}}",
      output_attr_name = "aid_list"
    )
    ```
    """
    self._add_processor(LiveStreamRedisLrangeEnricher(kwargs))
    return self

  def livestream_redis_range_pk_history_enricher(self, **kwargs):
      """
      LiveStreamRedisRangePKHistoryEnricher
      ------
      本 Enricher 将redis中的 pk history 结构的结果取出来，将满足指定数量的主播和 pk 时长以list形式返回。

      参数配置
      ------
      `main_author_id`: [int] [动态参数]，必配项, main_author_id
      `cluster_name`: [string] [动态参数] 选配项，redis集群名
      `pk_history_last_n_days`: [int] [动态参数] 选配项，最近 N 天的 pk 记录
      `pk_history_limit`: [int] [动态参数] 选配项, 限定数目
      `output_pk_author_list`: [string] 输出的int_list类型的common attr
      `output_pk_duration_list`: [string] 输出的double_list类型的common attr

      调用示例
      ------
      ``` python
      .livestream_redis_range_pk_history_enricher(
        main_author_id = "{{main_author_id}}",
        cluster_name = "{{cluster_name}}",
        pk_history_last_n_days = 30,
        pk_history_limit = 100,
        output_pk_author_list = "output_pk_author_list",
        output_pk_duration_list = "output_pk_duration_list"
      )
      ```
      """
      self._add_processor(LiveStreamRedisRangePKHistoryEnricher(kwargs))
      return self

  def slide_live_pre_process_enrich(self, **kwargs):
    """
    SlideLiveStreamPreProcessEnricher 
    ------
    单列直播特殊的召回预处理 enricher, 主要处理 user_profile 等准备召回使用的数据

    参数配置
    ------

    调用示例
    ------
    ``` python
    .slide_live_pre_process_enrich(
    )
    ```
    """
    self._add_processor(SlideLiveStreamPreProcessEnricher(kwargs))
    return self

  def livestream_user_live_exit_time_enricher(self, **kwargs):
    """
    LiveStreamUserLiveExitTimeEnricher 
    ------
    单列直播特殊的召回预处理 enricher, 主要处理 user_profile 等准备召回使用的数据

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_user_live_exit_time_enricher(
    )
    ```
    """
    self._add_processor(LiveStreamUserLiveExitTimeEnricher(kwargs))
    return self

  def livestream_climb_retrieve(self, **kwargs):
    """
    LiveStreamClimbRetriever 
    ------
    直播 leaf 中召回爬坡直播

    参数配置
    ------
    `reason`: [int] 召回原因，默认为 0

    `cluster_name`: [string] 必配. redis 集群名

    `key_prefix`: [string] 必配. 内容源的 shard redis key 前缀

    `shard_num`: [int] 必配. 一共使用了多少个 shard，将获取所有 shard 的内容

    `total_limit`: [int][动态参数] 选配. 设置最大的召回量，通过此参数可以控制探索流量占比
    
    `source_list`: [string][动态参数] 必配. 设置本次请求允许的召回源名称列表，以逗号分隔

    `enable_filter_same_city`: [int][动态参数] 选配，是否采用同城过滤
    `enable_filter_same_province`: [int][动态参数] 选配，是否用同省过滤
    `enable_filter_same_city_level`: [int][动态参数] 选配，是否用城市等级过滤
    `enable_filter_same_gender`: [int][动态参数] 选配，是否用性别过滤
    `enable_filter_same_age_seg`: [int][动态参数] 选配，是否用年龄段过滤
    `enable_filter_same_ns_region`: [int][动态参数] 选配，是否用南北方过滤
    
    `enable_match_city`: [int][动态参数] 选配，是否匹配城市人群包
    `enable_match_province`: [int][动态参数] 选配，是否匹配省份人群包
    `enable_match_city_level`: [int][动态参数] 选配，是否匹配城市等级人群包
    `enable_match_gender`: [int][动态参数] 选配，是否匹配性别人群包
    `enable_match_age_seg`: [int][动态参数] 选配，是否匹配年龄人群包
    `enable_match_ns_region`: [int][动态参数] 选配，是否匹配南北方人群包

    `user_city`: [string][动态参数] 选配，请求用户所在的城市名
    `user_province`: [string][动态参数] 选配，请求用户所在的省份名称
    `user_city_level`: [string][动态参数] 选配，请求用户所在的城市等级
    `user_gender`: [string][动态参数] 选配，请求用户性别
    `user_age_seg`: [string][动态参数] 选配，请求用户年龄段
    `user_ns_region`: [string][动态参数] 选配，请求用户属于南北方

    调用示例
    ------
    ``` python
    .livestream_climb_retrieve(
        reason = Reason_climb_retrieval,
        cluster_name = "recoLiveClimbInfo",
        key_prefix = "lclmb",
        shard_num = 20,
        source_list = "{{livestream_rocket_normal_retrievial_source_list}}"
    ) 
    ```
    """
    self._add_processor(LiveStreamClimbRetriever(kwargs))
    return self

  def retr_center_control_enrich(self, **kwargs):
    """
    RetrCenterControlEnricher
    ------
    直播召回中心控制参数处理 enricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .retr_center_control_enrich(
    )
    ```
    """
    self._add_processor(RetrCenterControlEnricher(kwargs))
    return self

  def retr_center_params_enrich(self, **kwargs):
    """
    RetrCenterParamsEnricher
    ------
    直播召回中心参数处理 enricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .retr_center_params_enrich(
    )
    ```
    """
    self._add_processor(RetrCenterParamsEnricher(kwargs))
    return self

  def recruit_realtime_params_enrich(self, **kwargs):
    """
    RecruitRealtimeParamsEnricher
    ------
    直播招聘实时行为相关参数 enricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .recruit_realtime_params_enrich(
    )
    ```
    """
    self._add_processor(RecruitRealtimeParamsEnricher(kwargs))
    return self

  def retr_client_control_enrich(self, **kwargs):
    """
    RetrClientControlEnricher
    ------
    召回中心客户点控制参数预处理 enricher

    参数配置
    ------

    调用示例
    ------
    ``` python
    .retr_client_control_enrich(
    )
    ```
    """
    self._add_processor(RetrClientControlEnricher(kwargs))
    return self

  def livestream_mc_predict_xtr_enrich(self, **kwargs):
    """
    LiveStreamMcPredictXtrEnricher
    ------
    双列直播mc_mode >= 3排序后处理
    将排序预测的结果分 xtr 依次写入 ItemAttr
    参考ks/realtime_reco/live_recommend/ranking_base/ranking_util.cc

    参数配置
    ------
    `pxtr_label`: [string] 必配. 存储预测的 pxtr label 的 StringList

    `pxtr_value`: [string] 必配. 存储预测的 pxtr 值的 DoubleList
    ------
    ``` python
    .livestream_mc_predict_xtr_enrich(
        pxtr_label = "mc_pxtr_label",
        pxtr_value = "mc_pxtr_value"
    )
    ```
    """
    self._add_processor(LiveStreamMcPredictXtrEnricher(kwargs))
    return self

  def livestream_mc_bypass_predict_xtr_enrich(self, **kwargs):
    """
    LiveStreamMcBypassPredictXtrEnricher
    ------
    仅供粗排旁路预估结果解析使用

    参数配置
    ------
    `pxtr_label`: [string] 必配. 存储预测的 pxtr label 的 StringList

    `pxtr_value`: [string] 必配. 存储预测的 pxtr 值的 DoubleList
    ------
    ``` python
    .livestream_mc_bypass_predict_xtr_enrich(
        pxtr_label = "mc_pxtr_label",
        pxtr_value = "mc_pxtr_value"
    )
    ```
    """
    self._add_processor(LiveStreamMcBypassPredictXtrEnricher(kwargs))
    return self

  def livestream_get_random_index_enrich(self, **kwargs):
    """
    LiveStreamGetRandomIndexEnricher
    ------
    从 candidate 中随机抽样 sample_num 个数据，写入 output_common_attr

    参数配置
    ------
    `candidate`: [list] 必配. 存储预测的 pxtr label 的 IntList
    `sample_num`: [int] 选配. 抽样数量，默认为 1
    `left_percent`: [double] 选配. 采样左起的百分比，默认为 0
    `right_percent`: [double] 选配. 采样右至的百分比，默认为 1 (0 < left_percent < right_percent < 1)
    `output_common_attr`: [list] 必配. 输出的IntList 类型的 common_attr
    ------
    ``` python
    .livestream_get_random_index_enrich(
      candidate = "global_sample_redis_value_int_list",
      sample_num = "{{global_sample_num}}",
      left_percent = 0,
      right_percent = 1,
      output_common_attr = "global_samp_v1" #输出全局样本
    )
    ```
    """
    self._add_processor(LiveStreamGetRandomIndexEnricher(kwargs))
    return self

  def livestream_explore_mc_rank_arranger(self, **kwargs):
    """
    LiveStreamExploreMcRankArranger
    ------
    直播粗排

    参数配置
    ------
    `input_count_threshold`: [string] [动态参数] 必配，运行 mc_rank 的初始结果数限制

    `output_count`: [string] [动态参数] 必配，mc_rank 输出结果数

    `channel_sort_param`: [string] [动态参数] 必配

    `mc_engage_param`: [string] [动态参数] 必配

    `mc_engage_cost_param`: [string] [动态参数] 必配

    `mc_variant_config`: [string] [动态参数] 必配

    `reason_list_attr`: [string] [动态参数] 必配。每个 item 的召回源 list

    `is_low_active_user`: [int] [动态参数] 选配，默认为 0

    `is_gift_user`: [int] [动态参数] 选配，默认为 0

    `is_high_gift_user`: [int] [动态参数] 选配，默认为 0

    `is_pref_new_author`: [int] [动态参数] 选配，默认为 0

    `cs_custom_mode`: [int] 选配，是否采用变量（channel_sort_param）人为指定通道优先级，默认为 0

    `info_output_attr`: [string] 选配，输出 debug 信息（各通道数量）到 attr

    调用示例
    ------
    ``` python
    .livestream_explore_mc_rank_arranger(
      input_count_threshold = 200,
      output_count = 200,
      channel_sort_param = "exploitation:0.95,high_show:0.05",
      mc_engage_param = "svtr:1.0,ctr:1.0",
      mc_engage_cost_param = "svtr:1.0,ctr:1.0",
      mc_variant_config = "new_tag:4,4,0.1",
      reason_list_attr = "reason_list")
    ```
    """
    self._add_processor(LiveStreamExploreMcRankArranger(kwargs))
    return self

  def livestream_live_recommend_init_context_enrich(self, **kwargs):
    """
    LiveStreamLiveRecommendInitContextEnricher
    ------
    读取 ks::reco::UserInfo 进行参数初始化, 逻辑参照 live_recommend/retrieval_processor/init_retrieval_context_processor.cc

    参数配置
    ------
    `item_attrs`: [list] 选配，这里填充需要输入的 item_attr 用于下游填充交叉特征
    `from_user_info`: [string] 必配. 从哪个 extra var 获取 UserInfo，一般可指定为 `parse_protobuf_from_string` 接口的 `output_attr` 值
    ------
    ``` python
    .livestream_live_recommend_init_context_enrich(
      item_attrs = ["author_id", "live_author_full_tag_path", "leaf_retr_reason"],
      from_user_info = "user_info_attr"
    )
    ```
    """
    self._add_processor(LiveStreamLiveRecommendInitContextEnricher(kwargs))
    return self

  def liverecommend_retr_params_enrich(self, **kwargs):
    """
    LiveRecommendCommonLeafParamsEnricher
    ------
    侧边栏直播（或者其他直播场景）调用召回中心参数初始化

    参数配置
    ------
    `request_type_attr`: [string] 必配, 包含调用端传过来的 request_type 的 common_attr
    ------
    ``` python
    .liverecommend_retr_params_enrich(
      request_type_attr = "request_type"
    )
    ```
    """
    self._add_processor(LiveRecommendCommonLeafParamsEnricher(kwargs))
    return self

  def before_tide(self, **kwargs):
    """
    LiveStreamBeforeTideEnricher

    ------

    一般该 processor 用作自动降级，自动调整参数。能够根据设定的目标，自动化的调整变量，使得影响的值尽量趋近于目标值。

    潮汐自动化调参 Processor，该 Processor 需要配合 CommonRecoAfterTideEnricher 使用。

    有任何疑问可以联系 @卿俊，相关文档：https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------

    `save_name`: [string] [必填] 获取的值的 common attr 名字，其存储的格式为 IntAttr。

    `default_value`: [int] [动态参数] [必填] 初始的默认值

    `group_name`: [string] 每对 tide processor 的对应名字，需要一一对应。如果忽略，则默认表示代码中仅有一组 Tide Processor。

    调用示例
    ------
    ``` python
    .before_tide(
      default_value=200,
      group_name="sort",
      save_name="limit_num"
    )
    ```
    """
    self._add_processor(LiveStreamBeforeTideEnricher(kwargs))
    return self
  
  def after_tide(self, **kwargs):
    """
    LiveStreamAfterTideEnricher
    ------

    一般该 processor 用作自动降级，自动调整参数。能够根据设定的目标，自动化的调整变量，使得影响的值尽量趋近于目标值。

    潮汐自动化调参 Processor，该 Processor 需要配合 CommonRecoBeforeTideEnricher 使用。

    有任何疑问可以联系 @卿俊，相关文档：https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------

    `save_name`: [string] [必填] 获取的值的 common attr 名字，格式为 Int Attr。

    `adjust_function`: [string] [必填] 潮汐策略调节方式，有 easy 和 pid 两种方式

    如果在 adjust_function = "pid" 的情况下，你需要填写以下三个参数, kp, kd, ki.
      
    其 pid 公式为 output = -kp * (measures[n - 1] - set_point) - ki * sum(measures[i] - set_point) - kd * (measures[n - 1] - measures[n - 2])：
      - `pid_kp`: [double] [动态参数] 可缺省。 pid 调节的比例项，默认值 0.1。
      - `pid_ki`: [double] [动态参数] 可缺省。 pid 调节的积分项，默认值 0.01。
      - `pid_kd`: [double] [动态参数] 可缺省。 pid 调节的微分项，默认值 0.02。

    如果在 adjust_function = "easy" 的情况下，你需要填写以下两个参数，step, window_size:
      - `step`: [int] [动态参数] 可缺省，表示单次变换的步长，默认值为 20
      - `window_size`: [int] [动态参数] 可缺省，表示多少次 QPS 之后进行一次统计，默认值为 20

    `max_value`: [int] [动态参数] 存储的 common attr 值的上界，默认 10000

    `min_value`: [int] [动态参数] 存储的 common attr 值的下界，默认 10
    
    `default_value`: [int] [动态参数] 初始的默认值， 默认 1000

    `target`: [int] [动态参数] 追踪的目标值，默认 200

    `from_common_attr`: [string] [动态参数] 表示用来追踪调节目标的 CommonAttr 名字, 仅支持 int 或者 float 类型

    `group_name`: [string] 可缺省，每对 tide processor 的对应名字，需要一一对应。如果忽略，则默认表示代码中仅有一组 Tide Processor。

    调用示例
    ------
    ``` python
    .after_tide(
      window_size=10,
      target=300,
      step=10,
      default_value=1000,
      adjust_function="easy",
      group_name="sort",
      save_name="limit_num",
      from_common_attr="test"
    )
    ```
    """
    self._add_processor(LiveStreamAfterTideEnricher(kwargs))
    return self

  def livestream_explore_variant(self, **kwargs):
    """
    LiveStreamExploreVariantArranger
    ------
    双列直播打散

    参数配置
    ------
    `reason_list_attr`: [string] 必配

    `is_high_active_user`: [int] [动态参数] 选配

    `mmu_tag_merchant_param`: [string] [动态参数] 选配 mmutag1=1001电商打散

    `mmu_tag_game_param`: [string] [动态参数] 选配 mmutag1=1006游戏打散

    调用示例
    ------
    ``` python
    .livestream_explore_variant(
      reason_list_attr = "reason_list")
    ```
    """
    self._add_processor(LiveStreamExploreVariantArranger(kwargs))
    return self

  def livestream_explore_moments(self, **kwargs):
    """
    LiveStreamExploreMomentsArranger
    ------
    直播精彩时刻

    参数配置
    ------
    `tag_param`: [string] [动态参数] 必配

    调用示例
    ------
    ``` python
    .livestream_explore_moments(
      tag_param = "1452-1620,2000,10,0.35,1;1336-,1000,5,0.4,1;1453-,1000,5,0.5,1;-,1000,5,0.4,1",
    ```
    """
    self._add_processor(LiveStreamExploreMomentsArranger(kwargs))
    return self

  def livestream_fr_predict_enrich(self, **kwargs):
    """
    LiveStreamLiveRecommendFrPredictEnricher
    ------
    侧边栏直播（或者其他直播场景）精排预测阶段， 适用于 fr_mode = 3

    参数配置
    ------
    `kess_service`: [string] 请求的 predict server 的 kess name。

    `kess_cluster`: [string] 请求的 predict server 的 cluster，默认为 PRODUCTION。

    `shard_num`: [int] predict server 的 shard num，默认为 1，一次请求会分别请求所有 shard，每个 shard 返回部分结果。

    `timeout_ms`: [int] 超时时间，单位为毫秒，默认为 300。

    `thread_num`: [int] 被调方线程数，默认为 1

    `user_info_attr`: [string] 必配. 从哪个 extra var 获取 UserInfo，一般可指定为
    `parse_protobuf_from_string` 接口的 `output_attr` 值

    `kuiba_user_attrs`: [string] 必配. string 类型的 kuiba_user_attrs

    `cross_attrs`: [list] 将写入 request 的 cross_attrs 字段，支持 name/as

    `receive_item_attrs`: [list] 将返回 resp 中的 pxtr 保存到指定 item attr，支持 name/as
    ------
    ``` python
    .livestream_live_recommend_fr_predict_enrich(
      kess_service = "grpc_kuibaShmPredictServerJxLiveSlideExp4",
      shard_num = 1,
      timeout_ms = 200,
      thread_num = 8,
      user_info_attr = "user_info_attr",
      kuiba_user_attrs = "kuiba_user_attrs_string",
      cross_attrs = [
          "author_id",
          "fans_count",
          "gender",
          "{"name":"tag", "as":"live_tag"}",
      ],
      receive_item_attrs = [
        {"name": "ctr", "as": "fr_pctr"}
      ],
    )
    ```
    """
    self._add_processor(LiveStreamLiveRecommendFrPredictEnricher(kwargs))
    return self
  
  def live_calc_retr_precision_recall(self, **kwargs):
    """
    LiveStreamCalcRetrPrecisionRecallObserver
    ------
    @dengzhikang

    将candidate set(集合A)与groud truth set(集合B)对比，计算召回率、准确率，
    结果存放在common attr中。
    召回率: A ∩ B / B
    准确率: A ∩ B / A

    参数配置
    ------
    `candidate_set_name`: [string] 必配项，作为 candidate_set 的 common attr

    `candidate_set_top_k`: [int] 使用 candidate_set 的前k个结果，默认100

    `gt_set_name`: [string] 必配项，作为 gt_set 的 common attr

    `gt_set_top_k`: [int] 使用 gt_set 的前k个结果，默认100

    `export_precision_name`: [string] 必配项，存放准确率的 common attr

    `export_recall_name`: [string] 必配项，存放召回率的 common attr
    ------
    ``` python
    .live_calc_retr_precision_recall(
      candidate_set_name = "recall_authorids",
      candidate_set_top_k = 100,
      gt_set_name = "reco.live_user_feature.live_top_author_valid_play_cnt_28d_key",
      gt_set_top_k = 100,
      export_precision_name = "history_authors_precision_" + str(reason),
      export_recall_name = "history_authors_recall_" + str(reason),
    )
    ```
    """
    self._add_processor(LiveStreamCalcRetrPrecisionRecallEnricher(kwargs))
    return self

  def livestream_explore_fr_rank_enricher(self, **kwargs):
    self._add_processor(LiveStreamExploreFrRankEnricher(kwargs))
    return self

  def livestream_neg_similarity_enricher(self, **kwargs):
    self._add_processor(LiveStreamNegSimilarityEnricher(kwargs))
    return self

  def live_group_by_item_attr(self, **kwargs):
    """
    LiveStreamGroupByItemAttrEnricher 
    ------
    按照特定的分组逻辑，将 ItemAttr 值映射到 1~n 个分组中
    将所有 item 按照该分组逻辑分组后，对所有组进行 perf 上报。

    参数配置
    ------
    `group_by_rules`: [list] 必配
      - `rule_name`: [string] 必配，分组名字，映射到一个具体规则, 将一个 int 类型的 ItemAttr 映射到一个 vector<int>，用户可以实现子类继承 GroupByRule 实现自定义分组规则，目前支持以下 rule:
                     'group_by_bit': 将 ItemAttr 对应的整数值, 映射到一个最多长度最多为 64 的数组，数组中第 i 位表示整数值对应的二进制的第 i 位不为 0 的位数。eg. 11 -> 1011 -> [0,1,3]
                     'group_by_int_value': 将 ItemAttr 对应的整数值, 直接映射一个只有一个元素且元素值相等的数组。eg. 11 -> [11]
      - `item_attr_name`: [string] 必配，需要分组的 item_attr 名字
      - `perf_key_name`: [string] 选配，对应字符串值为 extra4。比如需要在两个位置 perf 某种类型 item, 分别设置为 xx_before_ranking xx_after_ranking, 默认为 "{rule_name}:{item_attr_name}"
      - `extra_6_attr`: [string] 选配，从对应字符串 CommonAttr 动态取值作为 extra6
      - `group_name_from_kconf_key`: [string] 选配。可以指定一个 kconf_key 解析为 <int64, string> 的 map, 作为每个分组的分组名用作 perf 用, 默认为 item_attr_name + 对应分组 int 值, 最后的分组名会作为 extra5

    调用示例
    ------
    ``` python
    .live_group_by_item_attr(
      group_by_rules = [{
        "rule_name" : "group_by_bit",
        "item_attr_name" : "author_info.high_gift_author_type",
        "perf_key_name" : "high_gift_author_type_before_ranking",
        "group_name_from_kconf_key" : "reco.live.high_gift_author_type_bit_name"
      }]
    ) \
    .ranking(...) \
    .live_group_by_item_attr(
      group_by_rules = [{
        "rule_name" : "group_by_int_value",
        "item_attr_name" : "is_shop_live",
        "perf_key_name" : "is_shop_live_after_ranking",
        "extra_6_attr" : "ab_revenue_follow_perf_exp_tag"
      }]
    )
    ```
    """
    self._add_processor(LiveStreamGroupByItemAttrEnricher(kwargs))
    return self

  def grade_distribute_filter_enrich(self, **kwargs):
    """
    LiveStreamGradeDistributeFilterEnricher 
    ------
    分级分发直播间标签过滤，要求索引接分级分发直播间标签信号

    参数配置
    ------
      - `user_info_attr`: [string] 必配，UserInfo attr 的名字
      - `filter_flag_save_to`: [string] 选配. 结果作为 Item Attr 的属性名称

    调用示例
    ------
    ``` python
      .grade_distribute_filter_enrich(
        user_info_attr = USER_INFO_ATTR,
        filter_flag_save_to = "grade_distribute_filter_flag",
      )
    ```
    """
    self._add_processor(LiveStreamGradeDistributeFilterEnricher(kwargs))
    return self

  def item_attr_convert_arranger(self, **kwargs):
    """
    ItemAttrConvertArranger
    ------

    参数配置
    ------
      - `copy_item_attrs`: [string] 必配，需要 transfer key 的 item attr 名字
      - `origin_id_list`: [string] 必配，convert 前 item key 存放的 common attr 名字
      - `old_key`: [string] 必配. 旧结果集 item_key 对应的 Item Attr 属性名称

    调用示例
    ------
    ``` python
    .item_attr_convert_arranger(
      copy_item_attrs = ["a_raw_score", "left_trigger"],
      origin_id_list = "author_id_list",
      old_key = "author_id")
    ```
    """
    self._add_processor(ItemAttrConvertArranger(kwargs))
    return self

  def xtr_calibration_enrich(self, **kwargs):
    """
    LiveStreamXtrCalibrationEnricher
    ------
    单列直播对 sim xtr 进行 Calibration
    参数配置
    ------
      - `redis_name`: [string] 必配，储存 calibration 结果的 Redis 集群名
      - `request_type`: [int] 必配，当前请求的类型，目前支持 1, 5
      - `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 10
      - `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）
      - `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒
      - `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 60 秒
      - `redis_key_prefix`: [string] 必配，slide live 的 redis key 前缀，redis key 为前缀加上 calibrat_xtr
      - `calibrate_xtrs`: [string] 必配，需要执行 calibration 操作的 xtr 列表
      - `xtrs`: [list] 必配，上游传进来的 xtrs
    调用示例
    ------
    ``` python
      .xtr_calibration_enrich(
        redis_name = "redis_name",
        request_type = 1,
        redis_key_prefix = "slide_live_",
        calibrate_xtrs = "sim_wtr,sim_weight",
        xtrs = ["sim_wtr", "sim_weight"],
      )
    ```
    """
    self._add_processor(LiveStreamXtrCalibrationEnricher(kwargs))
    return self

  def liverecommend_reason_map(self, **kwargs):
    """
    LiveRecommendReasonMapArranger
    ------
    召回中心 reason 对应老召回 reason

    调用示例
    ------
    ``` python
    .liverecommend_reason_map()
    ```
    """
    self._add_processor(LiveRecommendReasonMapArranger(kwargs))
    return self

  def livestream_negative_feedback_enricher(self, **kwargs):
    """
    LiveStreamNegativeFeedbackEnricher
    ------
    从 redis 和 live_profile 中读取用户的短播历史和 hate 历史，输出 mmutag 粒度的负反馈信息

    参数配置
    ------
      - `user_info_attr`: [string] 必配，存储 userinfo 的 attr 名字
      - `time_range_sec`: [int] [动态参数] 必配. 取用户多久以前的历史作为负向过滤
      - `filter_thred`: [int] [动态参数] 必配. 用户在此类目负反馈多少次后会被记录过滤
      - `output_mmu_neg_name`: [string] 必配，需要过滤的 mmutag 输出到哪个特征

    调用示例
    ------
    ``` python
    .livestream_negative_feedback_enricher(
      user_info_attr = "user_info",
      time_range_sec = "{{ab_mmu_neg_filter_time_sec}}",
      filter_thred = "{{ab_mmu_neg_filter_cnt_thred}}",
      output_mmu_neg_name = "neg_mmu_tags"
    )
    ```
    """
    self._add_processor(LiveStreamNegativeFeedbackEnricher(kwargs))
    return self

  def livestream_counter_control_arranger(self, **kwargs):
    """
    LivestreamCounterControlArranger
    ------
    通过入参实现以下功能之一：
    (1) 从 redis 获取一定时间窗口内的 server show 计数（author_id 维度），并按照指定的阈值过滤
    (2) 从 redis 获取 server show 总计数（live_id 维度）

    参数配置
    ------
      - `max_count`: [int] [动态参数] 必配. 计数超过该数时被过滤，`counter_dimension_name_attr=author_id`时生效
      - `time_unit_munutes`: [int] [动态参数] 选配. 最小时间单位的分钟数，默认为60，即同一个小时的数据都写在同一个缓存key里，`counter_dimension_name_attr=author_id`时生效
      - `window_size`: [int] [动态参数] 选配. 默认为24，即统计24个 `time_unit_munutes` 时间内的计数总和，`counter_dimension_name_attr=author_id`时生效
      - `cluster_name`: [string] 必配. 用于存储计数的 redis 集群名
      - `key_prefix`: [string] 必配. redis key 前缀，格式为 key_predix + item_key
      - `counter_dimension_name_attr`: [string] 选配. 默认为`author_id`，取值为`author_id`时得到author_id维度的时间窗口server show计数，取值为`live_id`时得到live_id维度的server show总计数
      - `cache_bits`: [int] 选配. 用于缓存计数的空间大小，默认为 0
      - `timeout_ms`: [int] 选配. 调用 redis 延时，默认为 30
      - `export_count_attr`: [string] 选配，若配置则输出当前计数到指定的 item attr

    调用示例
    ------
    ``` python
    .livestream_negative_feedback_enricher(
      max_count = "{{cold_start_max_count}}",
      time_unit_munutes = "{{cold_start_time_unit_munutes}}",
      window_size = "{{cold_start_window_size}}",
      cluster_name = "recoLiveClimbInfo",
      key_prefix = "1295",
      cache_bits = 24,
      export_count_attr = "cold_start_show_count"
    )
    ```
    """
    self._add_processor(LivestreamCounterControlArranger(kwargs))
    return self

  def livestream_sample_send(self, **kwargs):
    """
    LiveStreamSampleObserver
    ------
    发送样本流

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_sample_send(
    )
    ```
    """
    self._add_processor(LiveStreamSampleObserver(kwargs))
    return self

  def livestream_sample_gen(self, **kwargs):
    """
    LiveStreamSampleEnricher
    ------
    双列直播正样本生成

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livesreeam_sample_gen(
    )
    ```
    """
    self._add_processor(LiveStreamSampleEnricher(kwargs))
    return self

  def livestream_explore_author_protect_insert(self, **kwargs):
    """
    LiveStreamExploreAuthorProtectInsertArranger
    ------
    保量主播强插

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_explore_author_protect_insert(
    )
    ```
    """
    self._add_processor(LiveStreamExploreAuthorProtectInsertArranger(kwargs))
    return self

  def grade_index_term_enrich(self, **kwargs):
    """
    LiveStreamGradeIndexTermEnricher
    ------
    分级倒排索引召回 根据 kconf 配置 reco.live.GradeLocalIndexRetrRules 决定召回的 term
    并写入 save_term_to_attr

    参数配置
    ------
      - `user_info_attr`: [string] 必配
      - `label_list_str`: [string] [动态参数] 必配 需要采用的规则标签
      - `rule_version`: [string] [动态参数] 默认为 "grade_index_term_default"
      - `save_term_to_attr`: [string] 必配. 倒排 term 记录在此 common attr 中
    调用示例
    ------
    ``` python
    .grade_index_term_enrich(
      user_info_attr = "user_info_attr",
      save_term_to_attr = "test_attr",
      label_list_str = "1,2,3,5",
      rule_version = "grade_index_term_default",
    )
    ```
    """
    self._add_processor(LiveStreamGradeIndexTermEnricher(kwargs))
    return self

  def livestream_feature_gen(self, **kwargs):
    """
    LiveStreamFeatureEnricher
    ------
    双列直播精排特征生成

    参数配置
    ------
      - `user_info_attr`: [string] 必配
    调用示例
    ------
    ``` python
    .liverecommend_fr_feature_gen(
      user_info_attr = "user_info_attr",
      redis_show_attr = "redis_show_list",
      redis_click_attr = "redis_click_list"
    )
    ```
    """
    self._add_processor(LiveStreamFeatureEnricher(kwargs))
    return self

  def liverecommend_user_attr(self, **kwargs):
    """
    LiveRecommendUserAttrEnricher
    ------
    双列直播填充额外用户特征
    参数配置
    ------
      - `user_info_attr`: [string] 必配
      - `sample_attr_name_list_attr`: [string] 必配
    调用示例
    ------
    ``` python
    .liverecommend_user_attr(
      user_info_attr = "user_info_attr",
      sample_attr_name_list_attr = "user_attr_names",
    )
    ```
    """
    self._add_processor(LiveRecommendUserAttrEnricher(kwargs))
    return self

  def unified_strategy_fr_arranger(self, **kwargs):
    """
    统一策略配置面板
    参数配置
    ------
      - `strategy`: [string][动态参数] 选用的单条策略 将逐步被 strategy_config_attrs 取代
      - `strategy_config_ab_params`: [string list] 配置了策略的多个 ab 参数 每个 ab 参数又可以`,`分割多个策略
    """
    self._add_processor(LiveStreamUnifiedStrategyFrArranger(kwargs))
    return self

  def livestream_enrich_channel_sort_queue_param(self, **kwargs):
    """
    LiveStreamChannelSortQueueParamEnricher
    处理粗排阶段多通道排序各队列参数相关信息
    参数配置
    ------
      - `channel_sort_queue_param`: [string][动态参数] 比配
      - `pass_queue_names`: [string list] 保送队列
      - `queue_weight_attrs`: [string list] 表示全部队列的权重的 Common Attr 名字
      - `queue_score_attrs`: [string list] 表示全部队列的Item分数的 Common Attr 名字
      - `queue_flag_attrs`: [string list] 表示全部队列的Item标志的 Common Attr 名字
    调用示例
    ------
    ``` python
    .livestream_enrich_mc_channel_sort_queue_param(channel_sort_queue_param="{{channel_sort_param}}",
    pass_queue_names=["pass"],
    queue_weight_attrs=["mc_csqw_follow, "mc_csqw_hot"],
    queue_score_attrs=["mc_csqs_follow, "mc_csqs_hot"],
    queue_flag_attrs=["mc_csqf_follow, "mc_csqf_hot"])
    ```
    """
    self._add_processor(LiveStreamChannelSortQueueParamEnricher(kwargs))
    return self

  def livestream_channel_sort(self, **kwargs):
    """
    LiveStreamChannelSortArranger
    多通道排序
    参数配置
    ------
      - `channel_queue_names`: [string list][动态参数] 实际队列的名字
      - `input_count_threshold`: [int][动态参数] 进行多通道排序最小输入 Item 数量
      - `output_count`: [int][动态参数] 进行多通道排序输出 Item 数量
      - `mc_variant_config`: [string] [动态参数] 多样性参数
      - `queue_weight_attrs`: [string list] 表示各队列的权重的 Common Attr 名字
      - `queue_score_attrs`: [string list] 表示各队列的分数的 Item Attr 名字
      - `queue_flag_attrs`: [string list] 标志是否属于某队列的 Item Attr 名字
      - `enable_dryrun_mode`: [bool] 标志是否进行粗排精排重复率的衡量
      - `dryrun_mode_result_attr`: [string] dryrun 模式结果保存的 Item Attr 名字
    ------
    ``` python
    .livestream_channel_sort(
    channel_queue_names="{{mc_channel_queue_names}}"
    input_count_threshold=200,
    output_count=200,
    mc_variant_config = "new_tag:4,4,0.1"
    queue_score_attrs=["mc_csqw_follow, "mc_csqw_hot"],
    queue_score_attrs=["mc_csqs_follow", "mc_csqs_hot"],
    queue_flag_attrs=["mc_csqf_follow", "mc_csqf_hot"]),
    enable_dryrun_mode=False,
    dryrun_mode_result_attr = "live_stream_dryrun_mode_result",
    additional_pass_queue=[
      {'name': 'addi_q1', 'weight': 0.1},
      {'name': 'addi_q2', 'weight': "{{addi_q2_weight}}"}
    ]
    ```
    """
    self._add_processor(LiveStreamChannelSortArranger(kwargs))
    return self

  def livestream_enrich_kv_param(self, **kwargs):
    """
    LiveStreamKvParamEnricher
    分割类似 sim_weight:2.0;sim_gift:1.8;sim_lvtr:1.2;sim_ctr:0.6 字符串参数到 Common Attrs
    参数配置
    ------
      - `origin_param`: [string][动态参数] 原始参数，一般来源于 AB
      - `param_attr_prefix`: [string] 参数解析后成 Common Attr 的前缀
      - `param_separator`: [string][可选] 默认值为英文分号
      - `kv_separator`: [string][可选] 默认值为英文冒号
      - `param_name_list_attr`: [string][可选] 参数解析后所有参数名的 Common Attr
    调用示例
    ------
    ``` python
    .livestream_enrich_kv_param(origin_param="{{mc_queue_weight_param}}",
    param_attr_prefix="mc_cs_weight_",
    param_separator=";",
    kv_separator=":"
    param_name_list_attr="mc_weight_param_names"
    ```
    """
    self._add_processor(LiveStreamKvParamEnricher(kwargs))
    return self
    
  def livestream_revenue_redis_retrieve(self, **kwargs):
    """
    LiveStreamRevenueRedisRetriever 
    ------
    直播 leaf 中通过 redis集群：dpUserDigAuthorInfo 中营收相关信号召回aid
    参数配置
    ------
    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限
    `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 10
    `key_from_attr`: [string] 从指定的 common_attr 中获取动态的 redis key
    `key_name_list`: [string] redis value中需要召回的key, 名字拼接成字符串
    `attr_name_list`: [string] redis value中需要召回的key对应的reason, 名字拼接成字符串,与key_name_list对齐
    调用示例
    ------
    ``` python
    .livestream_revenue_redis_retrieve(
        key_from_attr = "u_uid",
        key_name_list = "{{ab_revenue_author_retriever_key_name_list}}",
        attr_name_list = "{{ab_revenue_author_retriever_attr_name_list}}"
    ) 
    ```
    """
    self._add_processor(LiveStreamRevenueRedisRetriever(kwargs))
    return self

  def livestream_redis_record_and_get_avg(self, **kwargs):
    """
    LiveStreamRedisRecordAndGetAvgEnricher
    ------
    多个通道的原始数据会统计到 redis 中 并求过去一段时间内的均值
    不同通道 不同实验组的数据会打到不同的 key 上
    
    参数配置
    ------
      `cluster_name`: [string][必配] redis 集群名
      `cluster_prefix`: [string] redis 前缀 默认为 default
      `enable_send_to_redis`: [int][动态参数] 写 redis 开关 默认为 true
      `enable_get_avg_from_redis`: [int][动态参数] 读 redis 开关 默认为 true
      `enable_random_get`: [bool][动态参数] 是否随机读取 redis 开关 默认为 false
      `random_get_key_copies`: [int][动态参数] 是否随机读取 redis key 拷贝数量，默认值 2
      `sample_rate`: [double][动态参数] 写 redis 采样率 [0,1] 默认为 1.0
      `queue_name_sample_rate`: [double][动态参数] 队列名采样率 默认为 0.1 (实际采样率 = sample_rate * queue_name_sample_rate)
      `params`: [list] 通道相关配置
      - `queue_name`: [string][必配] 当前通道名称
      - `extra_info`: [string][动态参数] 当前通道的 ab 实验组信息 默认为 base
      - `raw_attr`: [string][必配] 用于统计的原始数据的 CommonAttr 名
      - `output_attr`: [string] 输出均值的 CommonAttr 名
    调用示例
    ------
    ``` python
    .livestream_redis_record_and_get_avg(
      cluster_name='还没申请',
      cluster_prefix = "live_reco_leaf",
      params = [
        {
          "queue_name" : "time",
          "extra_info" : "base",
          "raw_attr" : "raw_weight_time",
          "output_attr" : "avg_weight_time"
        }
      ]
    )
    ```
    """
    self._add_processor(LiveStreamRedisRecordAndGetAvgEnricher(kwargs))
    return self

  def livestream_revenue_model_score_enrich(self, **kwargs):
    """
    LiveStreamRevenueModelScoreEnricher
    ------
    从 redis 获得用户-主播营收分
    
    调用示例
    ------
    ``` python
    .livestream_revenue_model_score_enrich(
      output_attr="follow_revenue_model_score",
      use_item_id_in_attr="author_id"
    )
    ```
    """
    self._add_processor(LiveStreamRevenueModelScoreEnricher(kwargs))
    return self  

  def retr_center_filter_enrich(self, **kwargs):
    """
    RetrCenterFilterEnricher
    ------
    召回中心 Filter 处理，区分旧filters 和 新增 filters

    参数配置
    ------
    `filter_list_name`: [string] filter list 的common attr name
    `filter_edge`: [int] 新旧filters 分界线，默认 100
    ------
    ``` python
    .retr_center_filter_enrich(
      filter_list_name: "common_filter_list",
      filter_edge: 100
    )
    ```
    """
    self._add_processor(RetrCenterFilterEnricher(kwargs))
    return self

  def livestream_calc_ensemble_score(self, **kwargs):
    """
    LiveStreamEnsembleScoreEnricher
    ------
    对指定的 N 个队列计算用于 ensemble sort 的综合分，计算方式:

    注意：该方法只生成 ensemble score, 如需根据该综合分进行排序, 请在之后自行调用 `.sort(score_from_attr="${output_attr}")`

    参数配置
    ------
    `xtrs`: [string list] ensemble 计算依赖的 xtrs。这个参数是为了强制表达 processor 的依赖输入

    `channels`: [string][动态参数] 类似 pctr:1.0;pvtr:2.0;plvtr:0.5 的字符串，英文分号分隔的每一项表示一个队列名字和权重

    `start_seq`: [int] 选配项，序号的起始值，默认为 0

    `stable_sort`: [bool] 选配项，是否使用稳定排序来计算各 item 序号，默认值 False

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `allow_tied_seq`: [bool] 选配项，是否允许并列的序号值，例如可能按排名产出如下值：0, 1, 1, 3, 4, 4, 4, 7; 默认值 False

    `continuous_tied_seq`: [bool] 选配项，在允许并列序号的情况下，是否产出连续的序号，例如为 True 时将按排名产出如下值：0, 1, 1, 2, 3, 3, 3, 4; 默认值 False

    `epsilon`: [double] 选配项，在允许并列序号的情况下，比较是否相等时的精度要求，默认为 1e-9

    `formula_version`: [int][动态参数] 选配项，channel item 打分公式版本，默认值 0

    `aggregator`: [string][动态参数] 选配项，聚合队列分数的方式，支持 sum/multi，默认值 sum

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    """
    raise Exception('不要直接调用该 Processor，请使用它的子类 Processors！')

  def livestream_mc_ensemble_score(self, **kwargs):
    """
    LiveStreamMcEnsembleScoreEnricher
    请先查看  livestream_calc_ensemble_score 的说明，这儿只有和打分相关的参数说明
    ------
    参数配置
    ------
    `regulator`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的调节因子，默认值 1.0

    `smooth`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的平滑因子，默认值 0.0

    调用示例
    ------
    ``` python
    .livestream_mc_ensemble_score(
      xtrs = ["mc_pctr", "mc_pvtr"],
      channels = "{{mc_channels}}",
      regulator = "{{ensemble_regulator}}",
      smooth = "{{ensemble_smooth}}",
      output_attr = "ensemble_score")
    ```
    """
    self._add_processor(LiveStreamMcEnsembleScoreEnricher(kwargs))
    return self

  def livestream_follow_ensemble_score(self, **kwargs):
    """
    LiveStreamFollowEnsembleScoreEnricher
    请先查看  livestream_calc_ensemble_score 的说明，这儿只有和打分相关的参数说明
    ------
    参数配置
    ------
    `regulator`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的调节因子，默认值 1.0

    `smooth`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的平滑因子，默认值 0.0
    """
    self._add_processor(LiveStreamFollowEnsembleScoreEnricher(kwargs))
    return self

  def livestream_follow_reward_ensemble_score(self, **kwargs):
    """
    LiveStreamFollowRewardEnsembleScoreEnricher
    请先查看  livestream_calc_ensemble_score 的说明，这儿只有和打分相关的参数说明
    ------
    对关注作者候选计算打赏ensemble分
    调用示例
    """
    self._add_processor(LiveStreamFollowRewardEnsembleScoreEnricher(kwargs))
    return self

  def livestream_follow_merchant_ensemble_score(self, **kwargs):
    """
    LiveStreamFollowMerchantEnsembleScoreEnricher
    请先查看  livestream_calc_ensemble_score 的说明，这儿只有和打分相关的参数说明
    ------
    对关注作者候选计算电商ensemble分
    参数配置
    ------
    `regulator`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的调节因子，默认值 1.0

    `smooth`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的平滑因子，默认值 0.0
    """
    self._add_processor(LiveStreamFollowMerchantEnsembleScoreEnricher(kwargs))
    return self

  def livestream_norm_score(self, **kwargs):
    """
    LiveStreamNormScoreEnricher
    ------
    对 Item 的某个属性做归一化
    参数配置
    ------
    `input_attr`: [string][必配] 要做归一化的 item_attr

    `output_attr`: [string][必配] 归一化结果的 item_attr

    `top_ratio`: [double][动态参数] 区间 [0.0, 1.0] 的数，按 input_attr 排序，前 top_ratio 的最归一，之后的都归零，默认值 1.0

    `stable_sort`: [bool] 选配项，top_ratio < 1.0 时是否稳定排序，默认值 False

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与计算，默认值为 0

    调用示例
    ------
    ``` python
    .livestream_norm_score(
      input_attr = "raw_score",
      output_attr = "norm_score")
    ```
    """
    self._add_processor(LiveStreamNormScoreEnricher(kwargs))
    return self

  def livestream_calc_engagement_score(self, **kwargs):
    """
    LiveStreamEngagementScoreEnricher
    ------
    就算 engagement score

    参数配置
    ------

    `engage_param`: [string] [动态参数] 必配

    `engage_cost_param`: [string] [动态参数] 必配

    `engage_score_type`: [int] [动态参数] 必配，可配 2/3/6/7/8

    `is_gift_user`: [int] [动态参数] 选配，默认为 0

    `output_attr`: [string] 必配，计算结果的 attr

    调用示例
    ------
    ``` python
    .livestream_explore_mc_rank_arranger(
      engage_param = "svtr:1.0,ctr:1.0",
      engage_cost_param = "svtr:1.0,ctr:1.0",
      engage_score_type = 3,
      output_attr = "engagement_score")
    ```
    """
    self._add_processor(LiveStreamEngagementScoreEnricher(kwargs))
    return self

  def livestream_revenue_ensemble_score(self, **kwargs):
      """
      LiveStreamRevenueEnsembleScoreEnricher

      `protect`: [bool][动态参数] 选配项，formula_version 为 0 是的参数，输入 xtr 队列的值相同时 输出为 0.0

      `formula_version`: [int][动态参数] 选配项，channel item 打分公式版本，默认值 0

      其他同 livestream_calc_ensemble_score
      
      调用示例
      ------
      ``` python
      .livestream_revenue_calc_ensemble_score(
        xtrs = ["sim_lvtr", "sim_ctr"],
        channels = "{{mc_channels}}",
        regulator = "{{ensemble_regulator}}",
        smooth = "{{ensemble_smooth}}",
        protect= "{{ensemble_protect}}",
        output_attr = "ensemble_score",
        start_seq = 1)
      ```
      """
      self._add_processor(LiveStreamRevenueEnsembleScoreEnricher(kwargs))
      return self

  def livestream_time_ensemble_score(self, **kwargs):
      """
      LiveStreamTimeEnsembleScoreEnricher
      请先查看  livestream_time_calc_ensemble_score 的说明，这儿只有和打分相关的参数说明
      ------
      参数配置
      ------
      `regulator`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的调节因子，默认值 1.0

      `smooth`: [double][动态参数] 选配项，formula_version 为 0 是的参数，ensemble 各队列的平滑因子，默认值 0.0

      `protect`: [bool][动态参数] 选配项，formula_version 为 0 是的参数，输入 xtr 队列的值相同时 输出为 0.0

      调用示例
      ------
      ``` python
      .livestream_time_calc_ensemble_score(
        xtrs = ["sim_lvtr", "sim_ctr"],
        channels = "{{mc_channels}}",
        regulator = "{{ensemble_regulator}}",
        smooth = "{{ensemble_smooth}}",
        protect= "{{ensemble_protect}}",
        output_attr = "ensemble_score",
        start_seq = 1)
      ```
      """
      self._add_processor(LiveStreamTimeEnsembleScoreEnricher(kwargs))
      return self

  def livestream_recruit_regression_score(self, **kwargs):
      """
      LiveStreamRecruitRegressionScoreEnricher
      ------
      参数配置
      ------
      `user_info_attr`: [string][动态参数] 必配项
      `model_weight`: [string][动态参数] 必配项，且默认形式为"0.1;0.1",长度需为14
      `output_attr`: [string][动态参数] 必配项
      `user_recruit_job_province`: [string][动态参数] 选配项
      `user_recruit_job_category`: [string][动态参数] 选配项
      `model_type`: [int][动态参数] 选配项

      调用示例
      ------
      ``` python
      .livestream_recruit_regression_score(
        user_info_attr='user_info_attr',
        model_weight = "{{recruit_model_processor_weight}}",
        output_attr = "recruit_regression_score"
        )
      ```
      """
      self._add_processor(LiveStreamRecruitRegressionScoreEnricher(kwargs))
      return self

  def livestream_recruit_redis_score(self, **kwargs):
      """
      LiveStreamRecruitRedisScoreEnricher
      ------
      参数配置
      ------
      `redis_key_type`: [int][动态参数] 必配项, 默认为1,
                        1：过去30天投递次数，
                        2：过去30天曝光投递率（千分比）
                        3：过去30天平滑曝光投递率（千分比）
      `output_attr`: [string][动态参数] 必配项

      调用示例
      ------
      ``` python
      .livestream_recruit_redis_score(
        )
      ```
      """
      self._add_processor(LiveStreamRecruitRedisScoreEnricher(kwargs))
      return self

  def livestream_recruit_fm_score(self, **kwargs):
      """
      LiveStreamRecruitFMScoreEnricher
      ------
      参数配置
      ------
      `user_info_attr`: [string][动态参数] 必配项
      `model_weight`: [string][动态参数] 必配项，且默认形式为"0.1;0.1",长度需为14
      `output_attr`: [string][动态参数] 必配项
      `user_recruit_job_province`: [string][动态参数] 选配项
      `user_recruit_job_category`: [string][动态参数] 选配项
      `model_type`: [int][动态参数] 选配项

      调用示例
      ------
      ``` python
      .livestream_recruit_fm_score(
        user_info_attr='user_info_attr',
        output_attr = "recruit_fm_score",
        model_type = 0,
        user_recruit_job_category="reco.live_user_feature.u_recruit_category_id_list_v2",
        user_recruit_job_province="reco.live_user_feature.u_recruit_province_prefer_list_v2"
        )
      ```
      """
      self._add_processor(LiveStreamRecruitFMScoreEnricher(kwargs))
      return self

  def livestream_transform_score(self, **kwargs):
      """
      LiveStreamTransformScoreEnricher
      ------
      参数配置
      ------
      `input_attr`: [string] 需要进行映射变换的 item_attr，仅支持 FLOAT 和 INT 类型

      `output_attr`: [string] 将最后计算出的变换分存入指定的 item_attr
      
      `transform_param`: [string] 映射变换涉及的相关参数

      调用示例
      ------
      ``` python
      .livestream_transform_score(
        input_attr = "origin_score",
        output_attr = "trans_score",
        transform_param = "mode:1;alpha:4.0;smooth:0.0;regulator:1.0;enable_rank:0"
        )
      ```
      """
      self._add_processor(LiveStreamTransformScoreEnricher(kwargs))
      return self

  def livestream_common_attr_by_sample_list(self, **kwargs):
      """
      LiveStreamSampleListCommonAttrEnricher
      ------
      参数配置
      ------
      `sample_list_proto`: [string] 必配项，sample list 反序列化后的 attr

      `kuiba_attr_names`: [string] 必配项，sample list 的各个 attr 名字写到的 attr

      调用示例
      ------
      ``` python
      .livestream_common_attr_by_sample_list(
        sample_list_ptr_attr = "sample_list_proto",
        save_attr_names_to_attr = "kuiba_attr_names")
      ```
      """
      self._add_processor(LiveStreamSampleListCommonAttrEnricher(kwargs))
      return self

  def livestream_calc_fractile_score(self, **kwargs):
    """
    LiveStreamFractileScoreEnricher
    ------


    参数配置
    ------
    `fractile`: [string] 需要计算的数值全局分数位

    `input_attr`: [string] 需要计算的 item_attr

    `output_attr`: [string] 将最后计算出的 fractile 分存入指定的 item_attr

    """
    self._add_processor(LiveStreamFractileScoreEnricher(kwargs))
    return self

  def livestream_merchant_redis_retrieve(self, **kwargs):
    """
    LiveStreamMerchantRedisRetriever
    ------
    直播 leaf 中通过请求 redis数据，并解析json数据以召回结果
    参数配置
    ------
    `cluster_name`: [string] 必配项，redis集群
    `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 10
    `reason`: [int] 必配项，召回源
    `key_from_attr`: [string] 从指定的 common_attr 中获取动态的 redis key
    `output_item_attr`: [string] 必配项，新增一个item attr字段，用于保存召回的初始score
    `json_key`: [string] 必配项，redis结果是个map，json_key其实就是sub_key,
                用于指定使用map中的哪一项
    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限
    调用示例
    ------
    ``` python
    .livestream_merchant_redis_retrieve(
        cluster_name = "CLUSTER_NAME",
        timeout_ms = 20,
        reason = Reason_follow_top_history_merchant,
        key_prefix = "BIZ_PREFIX:",
        key_from_attr = "uid",
        output_item_attr = "merchant_re_raw_pscore",
        json_key = "THE_SUB_KEY",
        item_seperator = ",",
        attr_seperator = "_",
        retrieve_num = 5
    ) 
    ```
    """
    self._add_processor(LiveStreamMerchantRedisRetriever(kwargs))
    return self

  def livestream_redis_show_click(self, **kwargs):
    """
    LiveStreamRedisShowClickEnricher
    ------
    从 Redis thanosLivestreamUserList 中获取用户 Show 和 Click 的列表
    参数配置
    ------
    `user_id`: [int][动态参数] 必配项，用户 ID
    `redis_show_attr`: [string] 必配项，输出 Show List 属性名
    `redis_click_attr`: [string] 必配项，输出 Click List 属性名
    调用示例
    ------
    ``` python
    .livestream_redis_show_click(
        user_id = "{{uid}}",
        redis_show_attr = "redis_show_list",
        redis_click_attr = "redis_click_list"
    ) 
    ```
    """
    self._add_processor(LiveStreamRedisShowClickEnricher(kwargs))
    return self

  def livestream_enrich_user_attr(self, **kwargs):
    """
    LiveStreamUserAttrEnricher
    ------
    丰富用户属性
    参数配置
    ------
    `redis_show_attr`: [string] 必配项，输出 Show List 属性名
    `redis_click_attr`: [string] 必配项，输出 Click List 属性名
    `is_hate_live_attr`: [string] 必配项，输出是否不喜欢直播属性名
    `show_count_threshold`: [int] 选配项，计算 is_hate_live_attr 的参数，默认值 4
    `show_time_threshold`: [int] 选配项，计算 is_hate_live_attr 的参数，默认值 20000
    调用示例
    ------
    ``` python
    .livestream_enrich_user_attr(
        redis_show_attr = "redis_show_list",
        redis_click_attr = "redis_click_list",
        is_hate_live_attr= "is_live_hate_user"
    ) 
    ```
    """
    self._add_processor(LiveStreamUserAttrEnricher(kwargs))
    return self

  def livestream_extract_from_user_info(self, **kwargs):
    """
    LiveStreamUserInfoEnricher
    ------
    用来从 User Info 中提取不能用 enrich_with_protobuf 提取的字段
    参数配置
    ------
    `user_info_attr`: [string] 必配项，输出 Show List 属性名
    `merchant_browse_bound`: [int][动态参数] 选配项，计算 merchant_browse_count_attr 的参数，默认值 3
    `merchant_browse_count_attr`: [string] 选配项，计算最近浏览的电商直播数
    调用示例
    ------
    ``` python
    .livestream_extract_from_user_info(
        user_info_attr = "user_info_attr",
        merchant_browse_count_attr = "merchant_browse_count"
    ) 
    ```
    """
    self._add_processor(LiveStreamUserInfoEnricher(kwargs))
    return self
    
  def livestream_retrival_follow_negative_system_filter(self, **kwargs):
    """
    LiveStreamRetrievalNegativeSystemFilter
    ------
    已关电商/打赏召回特殊负向系统过滤逻辑，豁免部分非安全的过滤

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_retrival_follow_negative_system_filter(
        valid_tag_id="{{valid_tag_id}}",
        enable_filter_flag="{{enable_filter_flag}}"
    )
    ```
    """
    self._add_processor(LiveStreamRetrievalNegativeSystemFilter(kwargs))
    return self

  def livestream_enrich_item_attr_by_mapping(self, **kwargs):
    """
    LiveStreamItemAttrMappingEnricher
    ------
    dragonfly中不支持map类型，本算子提供从map中寻找target_key的方法，并将
    target_key在map中对应的value作为新的item attr。也可以当作判断target_key
    是否在set中使用，此种情况下，返回0或者1作为新的item attr。

    参数配置
    ------
    `map_keys_list`: [intlist] 必配项，map的keys
    `map_values_list`: [intlist] 选配项，如果不填或为空，则会将keys当作set
    `target_item_attr`: [int] 必配项，item属性的target key
    `output_item_attr`: [int] 必配项，输出的item属性名称

    调用示例
    ------
    ``` python
    .livestream_enrich_item_attr_by_mapping(
      map_keys_list="map_keys_list",
      map_values_list="map_values_list",
      target_item_attr="author_id",
      output_item_attr="follow_reason"
    )
    ```
    """
    self._add_processor(LiveStreamItemAttrMappingEnricher(kwargs))
    return self
  
  def livestream_enrich_embedding_similarity(self, **kwargs):
    """
    LiveStreamEmbeddingSimilarityEnricher
    ------
    计算两个向量的相似性
    参数配置
    ------
    `seed_embedding_attr`: [string] 必配项，种子 Embedding Common Attr 名字
    `item_embedding_attr`: [string] 必配项，Item Embedding Attr 名字
    `similarity_attr`: [string] 必配项，输出相似性 Item Attr 名字
    `norm_similarity_attr`: [string] 必配项，输出归一化相似性 Item Attr 名字

    调用示例
    ------
    ``` python
    .livestream_enrich_embedding_similarity(
      seed_embedding_attr="seed_embedding",
      item_embedding_attr="item_embedding",
      similarity_attr="similarity",
      norm_similarity_attr="norm_similarity"
    )
    ```
    """
    self._add_processor(LiveStreamEmbeddingSimilarityEnricher(kwargs))
    return self

  def livestream_retrieval_filter(self, **kwargs):
    """
    LiveStreamRetrievalFilterArranger
    ------
    对召回的直播结果添加是否应该被召回过滤过滤掉的标志

    参数配置
    ------
    `unified_post_filter_mode`: [int] 选配. 默认为 0. 是否启用统一后置过滤模式. 启用后 reason/on_filters/off_filters 配置失效 并读取 item_off_filter_list 作为过滤豁免列表
    `off_filter_list_name`: [string] 随 unified_post_filter_mode 启用.
    `on_filters`: [string] [动态参数] 可选. 在默认过滤基础上要增加的过滤集合
    `off_filters`: [string] [动态参数] 可选. 在默认过滤上要关闭的过滤集合
    调用示例
    ------
    ``` python
    .livestream_retrieval_filter(
        reason = 80,
        on_filters = "0,1",
        off_filters = "3,5",
    )
    ```
    ```
    .livestream_retrieval_filter(
        unified_post_filter_mode = 1,
        off_filter_list_name = "item_off_filter_list",
    )
    ```
    """
    self._add_processor(LiveStreamRetrievalFilterArranger(kwargs))
    return self
  
  def livestream_enrich_count_by_item_attr(self, **kwargs):
    """
    LiveStreamCountByItemAttrEnricher
    ------
    统计某个整数型 Item Attr 不同取值的 Item 数量
    参数配置
    ------
    `item_attr`: [string] 必配项，要统计的 Item Attr 名字，该 Item Attr 必须是整数类型
    `target_values`: [int list] 必配项，上述 Item Attr 的目标值
    `group_output_attr`: [string] 必配项，输出统计个数的 Common Attr 名字，输出类型是 int list
    `remind_output_attr`: [string] 必配项，输出不在target_values中统计个数的 Common Attr 名字，输出类型是 int

    调用示例
    ------
    ``` python
    .livestream_enrich_count_by_item_attr(
      item_attr="author_type",
      target_values=[0, 1, 2],
      group_output_attr="author_group_count",
      remind_output_attr="remind_count"
    )
    ```
    """
    self._add_processor(LiveStreamCountByItemAttrEnricher(kwargs))
    return self
  
  def livestream_enrich_follow(self, **kwargs):
    """
    LiveStreamFollowEnricher
    ------
    判断主播是否在用户的关注和取关列表中
    参数配置
    ------
    `user_info_attr`: [string] 必配项，User Info Attr 名
    `author_id_attr`: [string] 必配项，主播 Item Attr 名
    `follow_attr`: [string] 必配项，输出是否为关注 Item Attr 名字，输出类型是 int 0/1
    `unfollow_attr`: [string] 必配项，输出是否为关注 Item Attr 名字，输出类型是 int 0/1

    调用示例
    ------
    ``` python
    .livestream_enrich_follow(
      user_info_attr="user_info",
      author_id_attr="author_id",
      follow_attr="is_follow_author",
      unfollow_attr="is_unfollow_author"
    )
    ```
    """
    self._add_processor(LiveStreamFollowEnricher(kwargs))
    return self

  def livestream_ltv_by_colossus(self, **kwargs):
    """
    LiveStreamLtvByColossusEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus LiveItemV4 用户历史打赏列表
    `author_id_attr` : [string] 进直播间时对应的aid attr name

    示例
    ------
    ``` python
    livestream_ltv_by_colossus(
       colossus_resp_attr="colossus_resp",
       author_id_attr="author_id"
    )
    ```
    """
    self._add_processor(LiveStreamLtvByColossusEnricher(kwargs))
    return self    

  def enrich_common_attr_by_kconf(self, **kwargs):
    """
    LiveStreamKconfAttrEnricher

    ------
    将 kconf 中的值添加到 common attr 中，并添加统一的前缀，后续用于 override AB 参数

    参数配置
    ------
    `kconf_key`: [string] 必配项，kconf 的地址
    `common_attr_prefix`: [string] 选配项，给 common attr 增加的前缀

    调用示例
    ------
    ``` python
    .enrich_common_attr_by_kconf(
      kconf_key = "reco.live.test",
      common_attr_prefix = "kconf_"
    ```
    """
    self._add_processor(LiveStreamKconfAttrEnricher(kwargs))
    return self

  def enrich_ua_pid_list_by_candidate(self, **kwargs):
    """
    LivingPhotoUAPidEnricher

    ------
    将候选集中的pid list作为attr 加入到UA对的item attr中

    参数配置
    ------
    `living_photo_aid_attr`: [string] living_photo aid的attr name，
    `pid_list_attr`: [string] 必配项，生成的pid_list_attr
    `living_photo_reason_attr` :[int] living photo的reason
    `revenue_ua_reason_attr` :[int] ua对的reason
    调用示例
    ------
    ``` python
    .enrich_ua_pid_list_by_candidate(
      living_photo_aid_attr = "",
      pid_list_attr = "",
      living_photo_reason_attr = 2333
      revenue_ua_reason_attr = 1569
    )
    ```
    """
    self._add_processor(LivingPhotoUAPidEnricher(kwargs))
    return self

  def enrich_ua_live_photo_by_browse_ts(self, **kwargs):
      """
      UALivePhotoAidFreqEnricher

      ------
      检查高价值UA的aid是否需要被频控
      
      参数配置
      ------
      `freq_control_threshold`: [int] 频控阈值
      `freq_control_attr`: [int] 判断频控, 1:需要, 0:不需要

      调用示例
      ------
      ``` python
      .enrich_ua_live_photo_by_browse_ts(
        freq_control_threshold = 30,
        freq_control_attr = "should_be_filtered_by_freq"
      )
      ```
      """
      self._add_processor(UALivePhotoAidFreqEnricher(kwargs))
      return self

  def livestream_funnel_count(self, **kwargs):
      """
      LiveStreamFunnelCountObserver
      ------
      对当前阶段的 item 进行计数，缓存在内存中，定时打包发送至 kafka

      参数配置
      ------
      `type`: [string] [动态参数] 标记类型，一般可以穿用户页面类型或后缀即可

      `stage`: [string] [动态参数] 当前排序阶段

      `flush_interval_ms`: [int] 发送消息的时间间隔，默认为 300000（5分钟）

      `batch_size`: [int] 单条消息中包含的 item 个数，默认为 4096

      `kafka_topic`: [string] 发送至的 kafka topic, 默认为 reco_live_rocket_funnel_log

      `sample_ratio`: [float] 采样率（0 ~ 1），默认为 1.0

      调用示例
      ------
      ``` python
      .livestream_funnel_count(
          type = "{{ab_suffix_l2}}",
          stage="prerank")
      ```
      """
      self._add_processor(LiveStreamFunnelCountObserver(kwargs))
      return self

  def livestream_tag_count_perflog(self, **kwargs):
      """
      LiveStreamTagCountPerflogObserver
      ------
      tag/count 对 perflog 上报

      数据上报到 perflog-clickhouse

      namespace 和 talbe 表的关系参考 https://docs.corp.kuaishou.com/k/home/<USER>/fcADbZWCVFUyTmuAUzcFpbKcO

      参数配置
      ------
      `tag_list`: [int_list/string_list] [动态参数] 要上报的 tag list。

      `count_list`: [int_list] [动态参数] 与 tag_list 对应 count list。

      `namespace`: [string] [动态参数] 设置 perf 上报的 namespace。

      `subtag`: [string] [动态参数] 设置 perf 上报的 sub_tag。

      `extra3`: [string] [动态参数] 选配项 设置 perf 上报的 ext3，默认为空字符串。

      `extra5`: [string] [动态参数] 选配项 设置 perf 上报的 ext5，默认为空字符串。

      `extra6`: [string] [动态参数] 选配项 设置 perf 上报的 ext6，默认为空字符串。

      调用示例
      ------
      ``` python
      .perflog(tag_list = [1, 2, 3],
              count_list= [100, 200, 300],
              namespace = 'reco.live.leaf',
              subtag = 'retr_cnt')
      ```
      """
      self._add_processor(LiveStreamTagCountPerflogObserver(kwargs))
      return self

  def livestream_pack_topk_item(self, **kwargs):
    """
    ------
      - `item_attrs`: [string] 需要计算top item的属性名, 最终将对应的item_id list存到对应attr
      - `topk_num`: [int] 需要计算top item的数目,  将对应topk的item_id list存到对应attr
      - `out_attr_suffix`: [string] 输出属性名的后缀
    调用示例
    ------
    ``` python
    .livestream_pack_topk_item(
      item_attrs= source_attrs + target_attrs,
      topk_num=10,
      out_attr_suffix="_top_item"
    )
    ```
    """
    self._add_processor(LiveStreamTopKItemEnricher(kwargs))
    return self

  def livestream_intersection_count(self, **kwargs):
    """
    ------
      - `source_attrs`: [string] 需要对比交集的 source attrs, attr 存的是id list
      - `target_attrs`: [string] 需要对比交集的 target attrs, attr 存的是id list
      - `in_source_suffix`: [string][可选] 对 attrs 加前缀获取对应真实的 attr_name
      - `in_target_suffix`: [string][可选] 对 attrs 加前缀获取对应真实的 attr_name
      - `out_attr_suffix`: [string][可选] 对数据结果 attr name添加后缀
      - `sep`: [string][可选] 对数据结果 attr name, 拼接的分割符号
      - `topk_num`: [string][可选] 参数解析后所有参数名的 Common Attr
      存id list的attr_name = source_attr/target_attr + in_attr_suffix_
      输出属性名 attr_name = source_attr + sep + target_attr [+ sep + out_attr_suffix]
    调用示例
    ------
    ``` python
    .livestream_intersection_count(
      source_attrs = ["ctr"],
      target_attrs = ["ensemble_score"],
      in_source_suffix = "_fr_top_item",
      in_target_suffix = "_fr_top_item",
      out_attr_suffix = "top10",
      sep=":",
      topk_num=10
    )
    ```
    """
    self._add_processor(LiveStreamIntersectionCountEnricher(kwargs))
    return self

  def livestream_retrieval_distribution_calc(self, **kwargs):
      """
      LiveStreamRetrievalDistributionCalcEnricher
      ------
      统计各召回的分布，即CommonRecoResult中各路召回所包含的item数量
      利用各个item的reason_list计算，为不带遮挡的结果

      参数配置
      ------
      `export_retrieval_list_name`: [int_list] 必配项，存储各召回reason的common attr名称，按reason顺序存储。

      `export_count_list_name`: [int_list]  必配项，存储各召回分布结果的common attr名称，与export_retrieval_list中各reason一一对应。

      `top_k`: [int] [动态参数] 选配项，取CommonRecoResult的前k个结果统计，缺省时默认取100。


      调用示例
      ------
      ``` python
      .livestream_retrieval_distribution_calc(
        export_retrieval_list_name = 'match_reason_id_list',
        export_count_list_name = 'match_count_list',
        top_k = 100)
      ```
      """
      self._add_processor(LiveStreamRetrievalDistributionCalcEnricher(kwargs))
      return self

  def livestream_elementwise_division(self, **kwargs):
      """
      LiveStreamElementWiseDivisionEnricher
      ------
      两个int list中各元素按位做除法
      例：粗排后召回一致率统计
         过滤后召回分布：
             reason: [1,2,3,4,5,6]
             count: [100,100,100,100,100,100]
         粗排后召回分布：
             reason: [1,2,6]
             count: [50,10,60]
         粗排后召回一致率:
             reason: [1,2,3,4,5,6]
             consitency ratio: [0.5,0.1,0.0,0.0,0.0,0.6]

      参数配置
      ------
      `denominator_tag_list_name`: [int_list] 必配项，存储分母各元素tag id的common attr名称，需按tag id顺序存储。

      `denominator_element_list_name`: [int_list] 必配项，存储分母各元素的common attr名称，应与denominator_tag_list中的tag id一一对应。

      `numerator_tag_list_name`: [int_list] 必配项，存储分子各元素tag id的common attr名称，需按tag id顺序存储，且为分母的子集。

      `numerator_element_list_name`: [int_list] 必配项，存储分子各元素的common attr名称，应与numerator_tag_list中的tag id一一对应。

      `export_quotient_list_name`: [double_list]  必配项，存储计算结果的common attr名称，与numerator_tag_list中的tag id一一对应。

      调用示例
      ------
      ``` python
      .livestream_elementwise_division(
        denominator_tag_list_name = 'match_reason_id_list',
        denominator_element_list_name = 'match_count_list',
        numerator_tag_list_name = 'cascade_reason_id_list',
        numerator_element_list_name = 'cascade_count_list',
        export_quotient_list_name = 'cascade_agreement_rate_list')
      ```
      """
      self._add_processor(LiveStreamElementWiseDivisionEnricher(kwargs))
      return self

  def livestream_tag_value_perflog(self, **kwargs):
      """
      LiveStreamTagValuePerflogObserver
      ------
      tag/value 对 perflog 上报

      数据上报到 perflog-clickhouse

      namespace 和 talbe 表的关系参考 https://docs.corp.kuaishou.com/k/home/<USER>/fcADbZWCVFUyTmuAUzcFpbKcO

      参数配置
      ------
      `tag_list`: [int_list/string_list] [动态参数] 要上报的 tag list。

      `value_list`: [int_list/double_list] [动态参数] 与 tag_list 对应 value list。

      `namespace`: [string] [动态参数] 设置 perf 上报的 namespace。

      `subtag`: [string] [动态参数] 设置 perf 上报的 sub_tag。

      `extra3`: [string] [动态参数] 选配项 设置 perf 上报的 ext3，默认为空字符串。

      `extra5`: [string] [动态参数] 选配项 设置 perf 上报的 ext5，默认为空字符串。

      `extra6`: [string] [动态参数] 选配项 设置 perf 上报的 ext6，默认为空字符串。

      `perf_base`: [int] 打点时的放大倍数，当value list中元素为小数时使用，用于保留小数点，默认为 1000000L。

      调用示例
      ------
      ``` python
      .livestream_tag_value_perflog(tag_list = [1, 2, 3],
        value_list= [100, 200, 300],
        namespace = 'reco.live.leaf',
        subtag = 'retr_cnt')
      ```
      """
      self._add_processor(LiveStreamTagValuePerflogObserver(kwargs))
      return self

  def livestream_module_duration(self, **kwargs):
      """
      LiveStreamModuleDurationEnricher 
      ------
      直播 Module 用来统计一段逻辑的绝对时间、cpu 时间、Item 数量变化等指标并打 Perf
      主要弥补 Processor 粒度太细、Pipeline 粒度太粗的痛点。额外地，只对长尾耗时请求单独 Perf，用于分析长尾请求瓶颈
      参数配置
      ------

      `module_name`: [string] 必须设置，监控的 module 名字
      `is_module_start`: [bool] 是否是 module 开始，默认 False
      `duration_names_attr`: [string] 将输出的 durations 的名字存储到指定 attr，默认 duration_names_attr
      `extra3`: [string] 动态参数，Perf 时指定的 extra3, 比如作为不同类型 module 的区分

      `is_request_end`: [bool] 是否请求结束，默认 False
      `long_tail_request_thres_ms`: [int] 动态参数，大于此值才会当作长尾 perf
      ------
      .livestream_module_duration(
        module_name = self._name,
        extra3 = "retr_module",
        is_module_start = True,
      ) \
      .do_retrieve() \
      .livestream_module_duration(
        module_name = self._name,
        extra3 = "retr_module",
      )
      """
      self._add_processor(LiveStreamModuleDurationEnricher(kwargs))
      return self

  def livestream_ltv_by_rodis(self, **kwargs):
    """
    LiveStreamLtvByRodisEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `rodis_resp_attr`: [string] rodis 用户历史打赏列表
    `author_id_attr` : [string] 进直播间时对应的aid attr name

    示例
    ------
    ``` python
    livestream_ltv_by_rodis(
       rodis_resp_attr="rodis_resp",
       author_id_attr="aId"
    )
    ```
    """
    self._add_processor(LiveStreamLtvByRodisEnricher(kwargs))
    return self

  def live_pairwise_retrieve_from_raw_sample_package(self, **kwargs):
    """
    LiveKuibaRawSamplePackagePairwiseRetriever
    ------
    从 RawSamplePackage 触发出 pairwise 样本。注意这个 processor 会修改 user id、device id、requeset time。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 RawSamplePackage。

    `labels`: [list] label 到 attr 的映射关系，用 dict 存储。

    `pair_labels`: 按优先级从高到低排列，命中相邻优先级的样本构建 pair ，注意 pair_labels 必需是 labels 的子集

    `negative_prefix`: 负样本的特征前缀，默认是 NEG_

    `pid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 photo id，抽取后将作为该样本的 item_id，默认为 pId。

    `uid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 UserId 值，默认为 uId。

    `device_id_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 DeviceId 值，默认为 dId。

    `save_locale_to`: [string] 用于存储 locale 的 attr 的名字，默认为 rLocale。

    `save_channel_to`: [string] 用于存储 channel 的 attr 的名字，默认为 rChannel。

    `item_type`: [int] 触发的 item type，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `skip_sample_without_labels`: [bool] 是否跳过没有label（或所有 label 都不在 labels 里）的样本，默认为 true。

    `save_common_attr_names_to`: [string] 存储 common_attr 名字列表的 common attr。

    `save_next_common_attr_names_to`: [string] 存储 next_common_attr 名字列表的 common attr。

    `save_item_attr_names_to`: [string] 存储 item_attr 名字列表的 common attr。

    `compatible_labels`: [list] 如果老数据流中正样本的 label value 是 0，那么为了使老数据能正常训练，这里需要把这些正样本写进去；后续还是建议把 label value 改正确。

    `time_unit`: [string] RawSamplePackage 的 timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    `use_sub_biz`: [bool] label定义中是否包含sub biz，默认为 false。

    调用示例
    ------
    ``` python
    .live_pairwise_retrieve_from_raw_sample_package(
      from_extra_var="raw_sample_package",
      labels=[
        dict(label="China,nearby,realshow", attr="realshow"),
        dict(label="China,nearby,fr_neg_1", attr="fr_neg_1"),
        dict(label="China,nearby,fr_neg_2", attr="fr_neg_2"),
      ],
      pair_labels=["realshow", "fr_neg_1", "fr_neg_2"],
      save_common_attr_names_to="common_attrs"
      save_next_common_attr_names_to="next_common_attrs"
      save_item_attr_names_to="item_attrs")
    ```
    """
    self._add_processor(LiveKuibaRawSamplePackagePairwiseRetriever(kwargs))
    return self

  def live_pairwise_retrieve_from_kuiba(self, **kwargs):
    """
    LiveKuibaPairwiseRetriever
    ------
    从 RawSamplePackage 触发出 pairwise 样本。注意这个 processor 会修改 user id、device id、requeset time。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 RawSamplePackage。

    `labels`: [list] label 到 attr 的映射关系，用 dict 存储。

    `pair_labels`: 按优先级从高到低排列，命中相邻优先级的样本构建 pair ，注意 pair_labels 必需是 labels 的子集

    `negative_prefix`: 负样本的特征前缀，默认是 NEG_

    `pid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 photo id，抽取后将作为该样本的 item_id，默认为 pId。

    `uid_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 UserId 值，默认为 uId。

    `device_id_attr_name`: [string] RawSamplePackage 中哪个 attr 是记录的 user id，抽取后将作为 context 中的 DeviceId 值，默认为 dId。

    `save_locale_to`: [string] 用于存储 locale 的 attr 的名字，默认为 rLocale。

    `save_channel_to`: [string] 用于存储 channel 的 attr 的名字，默认为 rChannel。

    `item_type`: [int] 触发的 item type，默认为 0。

    `reason`: [int] 触发的 reason，默认为 0。

    `skip_sample_without_labels`: [bool] 是否跳过没有label（或所有 label 都不在 labels 里）的样本，默认为 true。

    `save_common_attr_names_to`: [string] 存储 common_attr 名字列表的 common attr。

    `save_next_common_attr_names_to`: [string] 存储 next_common_attr 名字列表的 common attr。

    `save_item_attr_names_to`: [string] 存储 item_attr 名字列表的 common attr。

    `compatible_labels`: [list] 如果老数据流中正样本的 label value 是 0，那么为了使老数据能正常训练，这里需要把这些正样本写进去；后续还是建议把 label value 改正确。

    `time_unit`: [string] RawSamplePackage 的 timestamp 字段的单位，默认为 us，也支持 ms 和 s。

    `use_sub_biz`: [bool] label定义中是否包含sub biz，默认为 false。

    调用示例
    ------
    ``` python
    .live_pairwise_retrieve_from_raw_sample_package(
      from_extra_var="raw_sample_package",
      labels=[
        dict(label="China,nearby,realshow", attr="realshow"),
        dict(label="China,nearby,fr_neg_1", attr="fr_neg_1"),
        dict(label="China,nearby,fr_neg_2", attr="fr_neg_2"),
      ],
      pair_labels=["realshow", "fr_neg_1", "fr_neg_2"],
      save_common_attr_names_to="common_attrs"
      save_next_common_attr_names_to="next_common_attrs"
      save_item_attr_names_to="item_attrs")
    ```
    """
    self._add_processor(LiveKuibaPairwiseRetriever(kwargs))
    return self


  def livestream_dcaf_control_enrich(self, **kwargs):
    """
    LivestreamDcafControlEnricher
    ------
    直播获取 DCAF 计算档位

    参数
    ------
    `kconf_key`: [string] Kconf 地址 默认为 reco.live2.DcafTaskConfig
    `json_path`: [string] [动态参数] [必填] json 路径配置
    `user_value_attr`: [string] [必填] 存放 user_value 的 attr 名字
    `lambda_value_attr`: [string] [必填] 存放 lambda_value 的 attr 名字
      约定 lambda_value 格式为 【lambda更新的毫秒时间戳_lambda值】(string) 前者用于监控
    `output_attr` : [string] 输出挡位的 attr 名字 默认为 dcaf_output_attr
    `kafka_topic` : [string] 输出 UserValue 的 topic 默认为 slide_live_dcaf_online_log

    示例
    ------
    ``` python
    livestream_dcaf_control_enrich(
        kconf_key="reco.live2.DcafTaskConfig",
        json_path="{{rc_arch_dcaf_json_path}}",
        user_value_attr="dcaf_user_value",
        lambda_value_attr="dcaf_lambda_val",
        output_attr="dcaf_output_attr"
    )
    ```
    """
    self._add_processor(LivestreamDcafControlEnricher(kwargs))
    return self

  def livestream_fnv_hash(self, **kwargs):
    """
    LiveStreamFNVHashEnricher
    -----
    对字符串使用 fnv_hash 生成 uint64 hash

    参数配置
    -----
    `input_item_attrs`: [list] 输入 item attr list
    
    `output_item_attrs`: [list] 输出 item attr list, 长度必须与 input_item_attrs 相等

    `input_common_attrs`: [list] 输入 common attr list

    `output_common_attrs`: [list] 输出 common attr list, 初度必须与 output_common_attrs 相等

    调用示例
    -----
    ```python
    .livestream_fnv_hash(
      input_item_attrs=['unit_str'],
      output_item_attrs=['unit_hash']
    )
    ```
    """
    self._add_processor(LiveStreamFNVHashEnricher(kwargs))
    return self

  def livestream_ltv3_by_rodis(self, **kwargs):
    """
    LiveStreamLtv3ByRodisEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `rodis_resp_attr`: [string] rodis 用户历史打赏列表
    `author_id_attr` : [string] 进直播间时对应的aid attr name

    示例
    ------
    ``` python
    livestream_ltv3_by_rodis(
       rodis_resp_attr="rodis_resp",
       author_id_attr="aId"
    )
    ```
    """
    self._add_processor(LiveStreamLtv3ByRodisEnricher(kwargs))
    return self
  
  def livestream_ltvn_by_rodis(self, **kwargs):
    """
    LiveStreamLtvnByRodisEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `rodis_resp_attr`: [string] rodis 用户历史打赏列表
    `author_id_attr` : [string] 进直播间时对应的aid attr name
    `count_days`: [int] 计算ltv的周期

    示例
    ------
    ``` python
    livestream_ltvn_by_rodis(
       rodis_resp_attr="rodis_resp",
       author_id_attr="aId",
       count_days=7
    )
    ```
    """
    self._add_processor(LiveStreamLtvnByRodisEnricher(kwargs))
    return self
  
  def livestream_minute_ltv_by_rodis(self, **kwargs):
    """
    LiveStreamMinLtvByRodisEnricher
    ------
    获取 user-author 的统计特征

    参数
    ------
    `rodis_resp_attr`: [string] rodis 用户历史打赏列表
    `author_id_attr` : [string] 进直播间时对应的aid attr name
    `count_minutes`: [int] 计算ltv的周期

    示例
    ------
    ``` python
    livestream_minutue_ltv_by_rodis(
       rodis_resp_attr="rodis_resp",
       author_id_attr="aId",
       count_minutes=60
    )
    ```
    """
    self._add_processor(LiveStreamMinLtvByRodisEnricher(kwargs))
    return self

  def livestream_gift_user_type_parser(self, **kwargs):
    """
    LiveStreamGiftUserTypeParserEnricher
    ------
    解析直播礼物场景中的用户类别和相关属性

    参数
    ------
    `user_type_list_str`: [string] [动态参数] [必填] 打开开关的用户类型列表，最前的优先级最高
    `user_type_parser_kconf` : [string] [必填] 解析用到的 kconf key
    `default_user_type`: [string] 默认用户类型， 不填时默认为 known
    `default_user_property` : [string] 默认用户属性，以;分割的int值， 不填时默认为 空字符串
    `save_user_type_to`: [string] 保存用户类型到common attr 默认 gift_user_type
    `save_user_property_to` : [string] 保存用户属性到common attr 默认 gift_user_property
    `redis_timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10
    `redis_cache_bit`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）
    `redis_cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒
    `redis_cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒
    
    示例
    ------
    ``` python
    livestream_gift_user_type_parser(
       user_type_list_str="user_type_list",
       user_type_parser_kconf="reco.live2.liveGiftUserTypeParserConfig",
       default_user_type="slient",
       default_user_property="0;0;0",
       save_user_type_to="user_type",
       save_user_property_to="user_property",
       redis_timeout_ms=10,
       redis_cache_bit=0,
       redis_cache_delay_delete_ms=100000,
       redis_cache_expire_second=3600,
    )
    ```
    """
    self._add_processor(LiveStreamGiftUserTypeParserEnricher(kwargs))
    return self

  def get_live_abtest_params(self, **kwargs):
    """
    LiveStreamAbtestCommonAttrEnricher
    ------
    get_abtest_params 的拷贝 用于支持直播特殊后缀功能，后续请 ab 系统原生支持此功能
    从 abtest 系统中获得参数值并作为 CommonAttr 填入 Context 中（千分世界和百分世界均可）

    参数配置
    ------
    `biz_name`: [string] [动态参数] ab 参数所属业务（即 abtest 网站上全大写字母的“所属业务”名称）

    `ab_params`: [list]
      - `param_name`: [string|dict] ab 参数的名称
      - `default_value`: [int|double|string|bool] ab 参数获取失败时取用的默认值
      - `param_type`: [string] 可缺省，ab 参数的类型，缺省将由 `default_value` 类型自动推断，可选值："int", "double", "string", "bool"（bool 将被转换为 0 / 1 int 值存储到 int CommonAttr 中）
      - `attr_name`: [string] 可缺省，要写入的 CommonAttr 名称，若缺省或为空将直接取 `param_name` 的值
      - `report_ab_hit`: [bool] [动态参数] 可缺省，是否将 ab 命中结果做上报，默认为 False 不上报

    `prioritized_suffix`: [list] [动态参数] 可缺省，为所有 `param_name` 定义一组优先尝试的参数名规则。
      - 若 `param_name` 配置为 string 类型将使用后缀拼接模式生成参数名，例如 ["_A", "_B"] 将优先使用 `param_name + "_A"` 参数值，次优使用 `param_name + "_B"` 参数值，最后兜底使用 `param_name` 参数值
      - 若 `param_name` 配置为 dict<str, str> 类型将使用 map 映射查询模式生成参数名，例如 ["C", "D"] 将优先使用 `param_name["C"]` 的参数名取值，次优使用 `param_name["D"]` 的参数名取值，若查询不到将直接取默认值

    `skip_special_param_during_suffix_check`: [dict] 在配置 prioritized_suffix 后，如果读到某个后缀的 ab 参数是“特殊设定”的值，则视作该参数未配置并跳过读取，继续尝试适配更低优先级的后缀。
      用于在 ab 平台新增一个更高优先级的后缀时，将默认值配置为此特殊值，使得原先更低优先级的参数的生效范围不受影响。
      以下特殊值未设置则该型参数不生效此功能，bool 参数无特殊值
      - `int`: [int] int 型参数的特殊值
      - `double`: [double] double 型参数的特殊值
      - `string`: [string] string 型参数的特殊值

    `user_id`: [int] [动态参数] 选配项，指定获取 ab 参数时使用的 user_id ，缺省时，使用当前请求的 user_id

    `device_id`: [string] [动态参数] 选配项，指定获取 ab 参数时使用的 device_id ，缺省时，使用当前请求的 device_id

    `product`: [string] [动态参数] 选配项，对于配置了 product 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 product ，例如 "KUAISHOU" 或 "ACFUN"

    `platform`: [string] [动态参数] 选配项，对于配置了 platform 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 platform ，例如 "ANDROID_PHONE" 或 "IPHONE"

    `app_version`: [string] [动态参数] 选配项，对于配置了 app_version 筛选的 ab 受众人群实验，指定获取 ab 参数时使用的 app_version

    `for_item_level`: [bool] [动态参数] 选配项，是否切换到 item 侧的 ab 参数获取，遍历每个 item 获取 ab 参数。默认值 false，仅获取 user 侧 ab 参数

    `item_level_user_id_attr`: [string] 选配项，指定从哪个 item attr 下获取 item 侧 ab 参数所使用的 user_id ，缺省时将使用 0 作为 user_id 默认值

    `item_level_device_id_attr`: [string] 选配项，指定从哪个 item attr 下获取 item 侧 ab 参数所使用的 device_id ，缺省时将使用 "" 作为 device_id 默认值

    `deduplicate`: [bool] 选配项, 为 True 时会对 ab 参数去重(保留第一个)，缺省时默认为 False

    调用示例
    ------
    ``` python
    # 完整格式
    .get_live_abtest_params(
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      ab_params = [{
        "param_name": "mmu_cover_erotic_prob_threshold",
        "param_type": "double",
        "default_value": 0.0
      }],
      prioritized_suffix=[
            "_high",
            "_mid",
            "_low"
      ],
      skip_special_param_during_suffix_check={
          "double": -666.0,
          "int": -666,
          "string": "nodata"
      }
    )
    # 简写格式
    .get_abtest_params(
      biz_name = "YOUR_ABTEST_BIZ_NAME",
      # 简写格式下每个 ab_param 为二元组 (param_name, default_value) 或三元组 (param_name, default_value, attr_name)
      ab_params = [
        ("mmu_cover_erotic_prob_threshold", 0.0),
        ("use_new_predict", False),
      ]
    )
    ```
    """
    keys = ("param_name", "default_value", "attr_name", "param_type")
    func = lambda x: dict(zip(keys, x)) if isinstance(x, tuple) else x
    kwargs["ab_params"] = list(map(func, kwargs["ab_params"]))
    self._add_processor(LiveStreamAbtestCommonAttrEnricher(kwargs))
    return self

  def livestream_new_retrieval_filter(self, **kwargs):
    """
    LiveStreamNewRetrievalFilterArranger
    ------
    直播召回过滤

    参数配置
    ------
    `item_attr_map` : [dict] 传入的 item attr 
    `filters` : [list] 传入过滤算子的相关配置
    `truncation_map` : [list] 传入分 reason 截断数， default 为默认截断数
    `debug_mode` : [bool] 是否开启 debug 模式，如果为 true ，则导出 item 会被哪些 filter 过滤，但并未实际过滤，也不截断，默认为false。
    `export_item_attr` : [string] 指定将 filter list 导入到哪个 item attr ， debug_mode 为 true 时必须配置该项。

    调用示例
    ------
    ``` python
    ITEM_ATTR_MAP = {
      "photo_id_attr": "photo_id"
    }
    FILTERS = [
      {
        "name": "demo",
        "enable": True,
        "filter_flag": 1000,
        "bad_photo_id_list_attr": "bad_photo_id_list"
      }
    ]
    self.livestream_new_retrieval_filter(
      name = "demo_retrieval_filter",
      traceback = True,
      item_attr_map = ITEM_ATTR_MAP,
      filters = FILTERS,
      truncation_map = {
        "default": 6
      }
    )
    ```
    """

    self._add_processor(LiveStreamNewRetrievalFilterArranger(kwargs))
    return self;

  def enrich_by_live_label_proto(self, **kwargs):
    """
    LiveLabelEnricher
    -----
    读取 Labellist proto，解析出 label 并 enrich 到 item_attr 中
    output attr 

    参数
    -----
    `live_label_attr`: [string] 从哪个 extra 类型（具体为 pb 指针类型）的 attr 中获取 Message，一般可指定为 parse_protobuf_from_string 接口的 output_attr 值
    `labels`: [list] 需要抽取的label，每项值为 {"name"="xxx", "attr"="yyy"} 的 dict 格式。
    `live_label_kconf_path`: [string] kconf 路径，定义 label 聚合方式，默认使用 SUM 聚合

    调用示例
    -----
    ``` python
    .parse_protobuf_from_string(
      input_attr = "live_label_bytes",
      output_attr = "live_label",
      class_name = "kuaishou::reco::big_river::ListOfBytes",
      is_common_attr = False,
    )
    .enrich_by_live_label_proto(
      live_label_attr="live_label",
      labels = [
        "live_inside_click_count",
        dict(label="click", attr="live_click"),
        dict(label="finish", attr="live_finish"),
      ],
      live_label_kconf_path = "KAIWorks.stream.recoLiveLabelConf",
    )
    .log_debug_info(
      for_debug_request_only=False,
      item_attrs = ["live_click", "live_finish",]
    )
    ```
    """
    self._add_processor(LiveLabelEnricher(kwargs))
    return self

  def enrich_by_raw_sample_package(self, **kwargs):
    """
    RawSamplePackageAttrEnricher
    -----
    读取common特征中的RawSamplePackage，解析其中的common和item特征，enrich到对应的attr中

    参数
    -----
    `from_extra_var`: [string] 从指定的extra var读取RawSamplePackage，必填项，一般指定为parse_protobuf_from_string算子输出的output_attr。
    `item_id_attr_name`: [string] RawSamplePackage中哪个attr是记录的item_id，抽取后将用该字段匹配context中的id，必填项。
    `save_common_attr_names_to`: [string] 存储common_attr名字列表的common attr。
    `save_item_attr_names_to`: [string] 存储item_attr名字列表的item attr。

    调用示例
    -----
    ``` python
    .parse_protobuf_from_string(
      input_attr="feature_bytes",
      output_attr="raw_sample_common",
      class_name="kuiba::RawSamplePackage"
    ) \
    .enrich_by_raw_sample_package(
      from_extra_var="raw_sample_common",
      item_id_attr_name="iItemId",
      save_common_attr_names_to="common_attrs",
      save_item_attr_names_to="item_attrs"
    )
    ```
    """
    self._add_processor(RawSamplePackageAttrEnricher(kwargs))
    return self

  def livestream_dump_context(self, **kwargs):
    """
    LivestreamDumpContextEnricher
    ------
    将当前 context 中的 topX 个 item 的 attr 和 common attr 数据快照保存到指定的 string 类型 common_attr 中，后续可发送给 redis 等外部存储供其它使用。

    参数配置
    ------

    `common_attrs`: [list] [动态参数] 用以填写存储的 common attr list 的 attr

    `item_attrs`: [list] [动态参数] 填写存储的 item attr list 的 attr

    `dump_to_attr`: [string] 必配项，将 attr 和 item 数据 dump 后的 string 内容存储到指定 common_attr 下

    `tag_name`: [string] [动态参数] 选配项，标识当前的分组, 默认为空字符串

    调用示例
    ------
    ``` python
    .livestream_dump_context(
      common_attrs="{{livestream_need_dumped_common_attr}}",
      item_attrs="{{livestream_need_dumped_item_attr}}",
      dump_to_attr="dump_livestream_context",
      tag_name="{{dump_tag}}"
    )
    ```
    """
    self._add_processor(LivestreamDumpContextEnricher(kwargs))
    return self

  def livestream_generate_sequence(self, **kwargs):
    """
    LivestreamGenerateSequenceMixer
    ------
    对现有 item 进行序列生成，生成的每个 item 均为一个序列，item key 从 0 开始。生成的序列会有 item attr: item_key_list, item_index_list, 为各个 item 的 key 和在 item_table 的顺序拼接而得。
    得到序列中每个 type 的 item 均为保序，即一个序列中如果出现 3 个 type1 的 item，这 3 个 item 的顺序与传入顺序一致。

    参数配置
    ------
    `item_table`: [string] 选配项，要生成序列的 item 所在 table，默认为全局

    `sequence_table`: [string] 选配项，生成的序列放置到哪个 table，默认为全局

    `type_num`: [int] [动态参数] 选配项，共有多少个 type 的 item，不填时为 1

    `type_attr`: [string] 选配项，指定哪个 item attr 表示 type，type 请从 1 开始枚举，不填或没有 attr 时均使用 type 1，

    `seq_len`: [int] [动态参数] 必配项，每个序列的长度，即一个序列多少个 item

    `gap_table_string`: [string] [动态参数] 选配项，序列化的 gap table，格式为：type1_type2_gap1,type1_type3_gap2,type2_type3_gap3  不写默认为 0 ,如 2_3_2,3_3_5,3_4_2,3_5_2,3_6_2

    `type_min_pos_list`: [int_list] [动态参数] 选配项，每个业务类型最高位置限制，如 6 个业务，为 7 个元素的数组 [0, 0, 0, 1, 0, 0, 0]，表示 type3 只能出现在最小为 1 （第二个）的位置，即不能为第一个

    `historical_item_type_list`: [int_list] [动态参数] 选配项，最近一刷每个 item 所属的 type，如 [1, 1, 3, 1, 1] 表示最近一刷有 5 个，第三个是 type3

    `reason`: [int] 选配项，生成序列的召回原因，默认为 0

    `item_attrs` [list] 选配项，需要生成序列特征的 item attr，生成后的序列特征为 list

    调用示例
    ------
    ``` python
    .livestream_generate_sequence(
      sequence_table = "seq_table",
      type_num = "{{business_type_num}}",
      type_attr = "inner_business_type",
      seq_len = "{{sequence_len}}",
      gap_table_string = "{{inner_mix_gap_table}}",
      type_min_pos_list = "{{inner_mix_min_pos}}",
      historical_item_type_list = "{{historical_item_business_type_list}}",
      item_attrs = ["inner_business_type", "auction_score"]
    )
    ```
    """
    self._add_processor(LivestreamGenerateSequenceMixer(kwargs))
    return self

  def livestream_athena_results_request(self, **kwargs):
    """
    livestream_athena_results_request
    ------
    发送得到 ad results 的请求

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_ad_results_request(
      athena_explore_request_bytes_attr = 'athena_explore_request_bytes',
      kess_service = 'ad-athena',
      timeout_ms = 400
    )
    ```
    """
    self._add_processor(LivestreamAthenaResultsRequestEnricher(kwargs))

  def livestream_athena_results_parse_retrieve(self, **kwargs):
    """
    livestream_athena_results_parse_retrieve
    ------
    解析请求 ad athena response 并召回

    参数配置
    ------

    调用示例
    ------
    ``` python
    .livestream_ad_results_parse(
      ad_tag_attr = 'is_ad',
      dsp_ad_info_bytes_attr = 'fans_top_ad_info_v2'
    )
    ```
    """
    self._add_processor(LivestreamAthenaResultsParseRetriever(kwargs))

  
  def livestream_select_retrieval_items_arranger(self, **kwargs):
    """
    LivestreamSelectRetrievalItemsArranger
    ------
    按比例合并召回队列，截断并同时去重。

    参数配置
    ------

    `total_limit_num`: [int][动态参数] 必配项，总截断数

    `channel2reason_list`: [dict][动态参数] 必配项，channel -> reason_list 映射

    `channel_weight`: [dict][动态参数] 必配项，channel -> weight 映射

    `reason_weight_in_channel`: [dict][动态参数] 必配项，channel 内部各个 reason 的权重

    `item_order`: [string] 必配项，item 顺序的 attr 名，与 reason_list 一一对应

    `reason_list`: [string] 必配项，去重 reason_list 的 attr 名

    调用示例
    ------
    ``` python
    .livestream_select_retrieval_items_arranger(
      total_limit_num=8000,
      reason_list = "reason_list",
      item_order = "item_order",
      channel2reason_list = {
        "channel1": [100, 200],
        "channel2": [300],
        "channel3": [400],
      }
      channel_weight = {
        "channel1": 3.0,
        "channel2": 5.0,
      }
      reason_weight_in_channel = {
        "100": 3.0,
        "200": 5.0,
        "300": 2.0,
      }
    )
    ```
    """
    self._add_processor(LivestreamSelectRetrievalItemsArranger(kwargs))
    return self
  
  def livestream_retrieval_channel_merge_arranger(self, **kwargs):
    """
    LivestreamRetrievalChannelMergeArranger
    ------
    按比例合并召回队列，截断并同时去重。

    参数配置
    ------

    `total_limit_num`: [int][动态参数] 必配项，总截断数

    `channel2reason_list`: [dict][动态参数] 必配项，channel -> reason_list 映射

    `channel_weight`: [dict][动态参数] 必配项，channel -> weight 映射

    `reason_list`: [string][动态参数] 必配项

    调用示例
    ------
    ``` python
    .livestream_retrieval_channel_merge_arranger(
      total_limit_num=8000,
      channel2reason_list = {
        "channel1": [100, 200],
        "channel2": [300],
        "channel3": [400],
      }
      channel_weight = {
        "channel1": 3.0,
        "channel2": 5.0,
      }
      reason_list = 'reason_list'
    )
    ```
    """
    self._add_processor(LivestreamRetrievalChannelMergeArranger(kwargs))
    return self
  
  def livestream_merchant_show_case_results_request(self, **kwargs):
    """
    livestream_merchant_show_case_results_request
    ------
    异步调用电商独立leaf得到请求，对 delegate_retrieve 算子进行拆分，实现跨 subflow 并行

    参数配置
    ------

    同 delegate_retrieve

    调用示例
    ------
    ``` python
    .livestream_merchant_show_case_results_request(
      kess_service = "{{request_merchant_show_case_kess_service_name}}",
      request_type = "slide_inner_amu",
      request_num = "{{request_merchant_show_case_request_num}}",
      timeout_ms = "{{request_merchant_show_case_time_out}}",
      send_common_attrs=['cover_author_id'],
    )
    ```
    """
    self._add_processor(LivestreamMerchantShowCaseResultsRequestEnricher(kwargs))
  
  def livestream_merchant_show_case_results_parse_retrieve(self, **kwargs):
    """
    livestream_merchant_show_case_results_parse_retrieve
    ------
    解析电商独立leaf请求并召回，需要和 livestream_merchant_show_case_results_request 搭配使用

    参数配置
    ------

    `merchant_tag_attr`: [string][动态参数] 必配项，召回结果打tag

    调用示例
    ------
    ``` python
    .livestream_merchant_show_case_results_parse_retrieve(
      merchant_tag_attr = 'seller_author_onsale2',
      recv_item_attrs = [{ "name": pred, "as": "slide_" + pred} for pred in ["sc_live_ctr"]],
      recv_common_attrs = ['mc_channel_sort_output_count'],
    )
    ```
    """
    self._add_processor(LivestreamMerchantShowCaseResultsParseRetriever(kwargs))

  def livestream_rocket_leaf_request(self, **kwargs):
    """
    livestream_rocket_leaf_request
    ------
    异步调用火箭rocket rpc得到请求，对 delegate_retrieve 算子进行拆分，实现跨 subflow 并行

    参数配置
    ------

    同 delegate_retrieve

    调用示例
    ------
    ``` python
    .livestream_rocket_leaf_request(
      kess_service = "{{request_rocket_leaf_kess_service_name}}",
      request_type = "slide_live_inner",
      request_num = "{{request_rocket_leaf_request_num}}",
      timeout_ms = "{{request_rocket_leaf_time_out}}",
      send_common_attrs=['entry_type', 'page'],
    )
    ```
    """
    self._add_processor(LivestreamRocketLeafRequestEnricher(kwargs))
  
  def livestream_rocket_leaf_results_parse_retrieve(self, **kwargs):
    """
    livestream_rocket_leaf_results_parse_retrieve
    ------
    解析火箭rocket rpc请求并召回，需要和 livestream_rocket_leaf_results_request 搭配使用

    参数配置
    ------

    `rocket_tag_attr`: [string][动态参数] 必配项，召回结果打tag

    调用示例
    ------
    ``` python
    .livestream_rocket_leaf_results_parse_retrieve(
      rocket_tag_attr = 'inside_cold_item_tag',
      recv_item_attrs = [
        {'name': 'inside_bonus', 'as': 'rocket_inside_bonus'}
      ],
      recv_common_attrs = ['entry_type'],
    )
    ```
    """
    self._add_processor(LivestreamRocketLeafResultsParseRetriever(kwargs))

