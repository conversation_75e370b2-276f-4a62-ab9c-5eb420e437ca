#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafRetriever

class LivestreamEmbeddingRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_embedding_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("need_fetch_user_emb")))
    for key in ["user_emb_layer_name", "user_emb_service_name"]:
      attrs.update(self.extract_dynamic_params(self._config["user_emb_config"].get(key)))
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("relevance_threshold")))
    attrs.update(self._config.get("extra_user_attr", []))
    attrs.update(self._config["user_emb_config"].get("kuiba_user_attrs_from_attr", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_user_emb_to_attr"))
    return attrs
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")
    if (self._config.get("need_fetch_user_emb") != None):
      check_arg(isinstance(self._config.get("user_emb_config"), dict), "user_emb_config 需为 dict 类型")



class LiveStreamCommonSingleCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_single_cf_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("config_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_type_attr")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class LiveStreamCommonCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_cf_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("config_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_icf_follow_list")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_left_trigger_id_to"))
    attrs.add(self._config.get("save_score_to_item_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class LiveStreamLiveItemCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_live_itemcf_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("config_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_icf_follow_list")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_item_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")


class LiveStreamCommonFmRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_fm_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("retr_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("search_num")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class LiveStreamFollowRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_follow_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_full_follow")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("user_info_attr"), str) and self._config["user_info_attr"], "user_info_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("enable_full_follow"), str) or isinstance(self._config.get("enable_full_follow"), int) ,"enable_full_follow 需设置")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class LivestreamModelRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_model_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["timeout_ms"]))

    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")


class LiveStreamRealTimeLiveRecoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_realtime_livereco_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("real_time_live_reco_request")))
    attrs.update(self.extract_dynamic_params(self._config.get("real_time_live_reco_reasons")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("recv_aid"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("real_time_live_reco_request"), str) and self._config["real_time_live_reco_request"], "real_time_live_reco_request 需为非空字符串")
    check_arg(isinstance(self._config.get("real_time_live_reco_reasons"), str) and self._config["real_time_live_reco_reasons"], "real_time_live_reco_reasons 需为非空字符串")

class LiveStreamRedisZrangeRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_redis_zrange_retrieve"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_bplus")))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"], "cluster_name 需为非空字符串")

class LiveStreamClimbRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_climb_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],"cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("key_prefix"), str) and self._config["key_prefix"],"key_prefix 需为非空字符串")
    check_arg(isinstance(self._config.get("source_list"), str) and self._config["source_list"],"key_prefix 需为非空字符串")
    check_arg(isinstance(self._config.get("shard_num"), int) and self._config["shard_num"] > 0,"shard_num 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

class LiveStreamRevenueRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_revenue_redis_retrieve"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("revenue_amount_1d")
    attrs.add("revenue_amount_3d")
    attrs.add("revenue_amount_7d")
    attrs.add("revenue_amount_14d")
    attrs.add("revenue_amount_30d")
    attrs.add("revenue_amount_60d")
    attrs.add("is_new_follow_from_find")
    attrs.add("is_180d_reward_author")
    attrs.add("is_realtime_reward_author")    
    attrs.add("revenue_amount_180d")
    attrs.add("new_follow_date")
    attrs.add("new_follow_from_source")
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_name_list"))
    attrs.add(self._config.get("attr_name_list"))
    attrs.add(self._config.get("perf_exp_tag"))
    attrs.add(self._config.get("enable_follow_revenue_strategy"))
    attrs.add("enable_user_active_flag_valid")
    attrs.add("revenue_follow_perf_exp_tag")
    attrs.add("user_attr_names")
    for key in ["key_from_attr"]:
      if self._config.get(key):
        attrs.add(self._config.get(key))
    for key in ["enable_add_realtime_reward_author", "enable_add_realtime_reward_author_for_public", "enable_add_180d_reward_author"]:
      if key in self._config.keys():
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if "cache_bits" in self._config:
      check_arg(isinstance(self._config.get("cache_bits"), int) and self._config["cache_bits"] >= 0,
                "cache_bits 需为大于等于 0 的整数")
    if "cache_delay_delete_ms" in self._config:
      check_arg(isinstance(self._config.get("cache_delay_delete_ms"), int) and self._config["cache_delay_delete_ms"] > 0,
                "cache_delay_delete_ms 需为大于 0 的整数")
    if "cache_expire_second" in self._config:
      check_arg(isinstance(self._config.get("cache_expire_second"), int) and self._config["cache_expire_second"] > 0,
                "cache_expire_second 需为大于 0 的整数")

class LiveStreamMerchantRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_merchant_redis_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    # attrs.add("merchant_re_raw_pscore")
    attrs.add(self._config.get("output_item_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("json_key")))
    # 以下这些都不是 CommonAttr !!!
    # attrs.add(self._config.get("cluster_name"))
    # attrs.add(self._config.get("timeout_ms"))
    # attrs.add(self._config.get("key_prefix"))
    # attrs.add(self._config.get("key_suffix"))
    # attrs.add(self._config.get("output_item_attr"))
    # attrs.add(self._config.get("reason"))
    # attrs.add(self._config.get("item_seperator"))
    # attrs.add(self._config.get("attr_seperator"))
    attrs.add(self._config.get("key_from_attr"))
    attrs.add(self._config.get("enable_top_history_follow_merchant"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    if "cluster_name" in self._config:
      check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],
              "cluster_name 需为非空字符串")
    if "reason" in self._config:
      check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")

class LiveKuibaRawSamplePackagePairwiseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_pairwise_retrieve_from_raw_sample_package"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for save_attr in ["save_common_attr_names_to", "save_next_common_attr_names_to", "save_item_attr_names_to"]:
      if save_attr in self._config:
        attrs.add(self._config[save_attr])
    attrs.add(self._config.get("save_locale_to", "rLocale"))
    attrs.add(self._config.get("save_channel_to", "rChannel"))
    return attrs

class LiveKuibaPairwiseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_pairwise_retrieve_from_kuiba"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for save_attr in ["save_common_attr_names_to", "save_next_common_attr_names_to", "save_item_attr_names_to"]:
      if save_attr in self._config:
        attrs.add(self._config[save_attr])
    attrs.add(self._config.get("save_locale_to", "rLocale"))
    attrs.add(self._config.get("save_channel_to", "rChannel"))
    return attrs    

class LivestreamAthenaResultsParseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_athena_results_parse_retrieve"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 获取 livestream_athena_results_request 的结果
    attrs = set()
    attrs.add('athena_explore_response_ptr')
    attrs.add('athena_explore_response_bytes')
    attrs.add('athena_explore_response_waiter')
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['dsp_ad_info_bytes_attr'])
    attrs.add(self._config['ad_tag_attr'])
    attrs.add('ad_inner_live_mix_cpm')
    attrs.add('ad_inner_live_mix_rb')
    return attrs

class LivestreamMerchantShowCaseResultsParseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_merchant_show_case_results_parse_retrieve"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add('merchant_show_case_response_ptr')
    attrs.add('merchant_show_case_response_bytes')
    attrs.add('merchant_show_case_response_waiter')
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['merchant_tag_attr'])
    attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

class LivestreamRocketLeafResultsParseRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_rocket_leaf_results_parse_retrieve"
  
  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add('rocket_leaf_response_ptr')
    attrs.add('rocket_leaf_response_bytes')
    attrs.add('rocket_leaf_response_waiter')
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['rocket_tag_attr'])
    attrs.update(extract_attr_names(self._config.get("recv_item_attrs", []), "as"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return extract_attr_names(self._config.get("recv_common_attrs", []), "as")

