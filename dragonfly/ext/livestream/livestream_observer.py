#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafObserver

class LiveStreamSampleObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_sample_send"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("enable_services")))
    attrs.add(self._config.get("kuiba_user_attrs_string_attr"))
    attrs.add(self._config.get("pos_sample_list_attr"))
    attrs.update(self._config.get("neg_sample_list_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("send_neg_sample")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_sample_channel_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_action_log_btq_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("leaf_show_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("leaf_show_biz_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("leaf_show_shard_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("leaf_show_queue_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_sample_queue_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_sample_shard_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("send_one_request")))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set([ "author_id" ])
    for attr in self._config.get("neg_sample_list_attrs", []):
      attrs.add(gen_attr_name_with_common_attr_channel("author_id", attr))
    attrs.add(gen_attr_name_with_common_attr_channel("extra_sample_attrs", self._config.get("pos_sample_list_attr")))

    return attrs

class LiveStreamFunnelCountObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_funnel_count"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("type"), "缺少 type 配置")
    check_arg(self._config.get("stage"), "缺少 stage 配置")

class LiveStreamTagCountPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_tag_count_perflog"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tag_list", "count_list", "namespace", "subtag", "extra3", "extra5", "extra6"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("namespace"), "缺少 namespace 配置")
    check_arg(self._config.get("subtag"), "缺少 subtag 配置")

class LiveStreamTagValuePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_tag_value_perflog"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["tag_list", "value_list", "namespace", "subtag", "extra3", "extra5", "extra6"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("namespace"), "缺少 namespace 配置")
    check_arg(self._config.get("subtag"), "缺少 subtag 配置")