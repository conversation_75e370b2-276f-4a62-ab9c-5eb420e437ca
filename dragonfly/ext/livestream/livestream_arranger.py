#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, ArgumentError, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class LivestreamEnsSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_ensemble_sort_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["exp_tag", "livestream_score", "livestream_retrieval_score", "a_raw_score", "livestream_pctr", "pctr_v2", "pwtr",
        "pgtr", "ptime", "pevtr", "plvtr10", "plvtr30", "plvtr60",
        "pltv", "pltv7tr", "pltv7tr_p80", "pltv7tr_p90", "pltv7" , "author_info.empirical_ltv7",
        "pweight", "b_type_author_boost","author_info.high_gift_author_type", 
        "sim_ctr", "sim_wtr", "sim_lvtr", "sim_gift", "sim_rtr", "sim_weight", "mc_pctr", "mc_pwtr",
        "mc_plvtr", "mc_pgtr", "mc_pltr", 
        "bypass_sim_ctr", "bypass_sim_wtr", "bypass_sim_lvtr", "bypass_sim_gift", "bypass_sim_rtr",
        "bypass_sim_weight", "bypass_sim_cmtr", "bypass_sim_ptr", "bypass_sim_invtr", "bypass_sim_outvtr",
        "bypass_sim_wt", "bypass_sim_benenfit", "bypass_sim_svr", "bypass_sim_etr", "bypass_sim_out_etr",
        "bypass_sim_in_etr", "bypass_sim_in_lvtr", "bypass_sim_gtr", "bypass_sim_gmtr", "bypass_sim_flvtr",
        "bypass_sim_fgtr", "bypass_sim_benefit", "bypass_mc_pctr", "bypass_mc_pwtr", "bypass_mc_plvtr",
        "bypass_mc_pevtr", "bypass_mc_pievr", "bypass_mc_pgtr", "bypass_mc_pnctr", "bypass_mc_pweight",
        "sim_ltr_out_gift", "sim_ltr_in_time", "sim_ltr_out_time", "sim_ltr_in_gift", "target_revenue_score", "is_follow_author",
        "sim_ctr_realtime", "is_top_revenue_author", "target_revenue_score_rank", "is_revenue_score_author",
        ])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("ensemble_sort_score")
    attrs.add("revenue_score")
    attrs.add("revenue_all_score")
    attrs.add("extra_score_str")
    attrs.add("extra_predict_score_str")
    attrs.add("follow_score")
    attrs.add("is_follow_force_insert")
    attrs.add("is_cover_sim_force_insert")
    attrs.add("extra_bypass_predict_score_str")
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

class LiveStreamExploreMcRankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_explore_mc_rank_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(["is_live_revenue_user", "game_revenue_user_type"])
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_sort_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_engage_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_engage_cost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_variant_config")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_low_active_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_gift_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_high_gift_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_pref_new_author")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_engage_score_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_force_empty")))
    attrs.update(self.extract_dynamic_params(self._config.get("force_need_leaf_result")))
    attrs.update(self.extract_dynamic_params(self._config.get("static_store_redis_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("static_read_redis_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_online_count_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_cover_merchant_punish")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_author_protect_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_author_category_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_author_category_punish")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_gift_author_category_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_nearby_retr_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_high_gift_author_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_gift_low_follow_bplus_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_protect_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_protect_pref_new_author_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_protect_climb_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_punish_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("gift_author_category_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("nearby_retr_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_category_punish_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("gift_author_category_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("high_gift_author_boost_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("user_info_follow_author_b_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("low_follow_author_b_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("cs_custom_mode")))
    if "user_info_attr" in self._config:
      attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_mmu_glad_author_mc_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_glad_author_mc_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_same_city_recruit_live_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("same_city_recruit_live_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_channel_recruit_live_boost")))
    attrs.update(self.extract_dynamic_params(self._config.get("same_city_recruit_level_boost_coeff")))
    if 'strategy_config_ab_params' in self._config.keys():
      attrs.update(self._config['strategy_config_ab_params'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("force_empty_response")
    if "info_output_attr" in self._config:
      attrs.add(self._config.get("info_output_attr"))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["mc_pctr", "mc_plvtr", "mc_plvtr2", "mc_psvtr", "mc_pltr", "mc_pwtr", "mc_pgtr",
        "mc_pwatchtime", "mc_pinwatchtime", "mc_gift_value_1", "mc_gift_value_2", "mc_pievr",
        "mc_pnctr", "mc_pevtr", "mc_pctr_enter", "mc_pwt1", "mc_preal_wt1", "mc_pweight", "mc_pgpr",
        "live_author_full_tag_path", "count.live_recommend_click", "online_count",
        "author_info.category_type", "author_info.high_gift_category_type", "is_cover_merchant_live",
        "mmu_tag_info.tag_list_lv1", "mmu_tag_info.tag_list_lv2", "mmu_tag_info.tag_list_lv3",
        "mmu_tag_info.tag_list_lv4", "mmu_tag_info.tag_list_lv5", "retr_score_225",
        "author_info.is_seller_author", "ip_region", "recruit_priority_attr", "livestream_follow_score",
        "target_revenue_score", "target_revenue_score_rank",])
    attrs.add(self._config.get("reason_list_attr"))
    if "author_id_attr" in self._config:
      attrs.add(self._config.get("author_id_attr"))
    if self._config.get("mc_engage_score_type", 6) == 8:
      attrs.add(self._config.get("external_score_attr", "mc_score"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("mc_engage_score")
    attrs.add("mc_score")

    return attrs

class LiveStreamExploreVariantArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_explore_variant_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("history_show_list")
    attrs.update(self.extract_dynamic_params(self._config.get("is_high_active_user")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_history")))
    attrs.update(self.extract_dynamic_params(self._config.get("tag_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("tag1_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("tag2_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("tag3_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("tag4_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag1_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag2_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag3_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag4_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag5_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("author_protect_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag_merchant_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_tag_game_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("recruit_job_category_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("recruit_live_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_live_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_live_target")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_movie_tag_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_movie_target_tag")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_group_tag_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_group_target_tag")))        
    attrs.update(self.extract_dynamic_params(self._config.get("neg_game_live_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("neg_game_live_target")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_id_32_tag_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_id_128_tag_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_id_512_tag_param")))
    
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update([
      "tag",
      "live_author_full_tag_path",
      "relay_live_id",
      "author_id",
      "mmu_tag_info.tag_list_lv1",
      "mmu_tag_info.tag_list_lv2",
      "mmu_tag_info.tag_list_lv3",
      "mmu_tag_info.tag_list_lv4",
      "mmu_tag_info.tag_list_lv5",
      "grade_label",
      "is_movie_tag",
      "aIsGroupLive",
      "llm_cluster_id_32",
      "llm_cluster_id_128",
      "llm_cluster_id_512"
      ])
    attrs.add(self._config.get("reason_list_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("variant_score")

    return attrs

class LiveStreamExploreMomentsArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_explore_moments_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("tag_param")))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["author_info.acu_7d", "live_author_full_tag_path", "live_entry_count", "exit_count", "online_count"])

    return attrs

class LivestreamWeightSampleArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_weight_sample_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["author_id_attr", "rocket_budget", "rocket_balance"])

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("size_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("speed_control")))
    attrs.update(self.extract_dynamic_params(self._config.get("completion_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("decelerate_rate")))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("live_weight_sample_score")

    return attrs

class ItemAttrConvertArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_attr_convert_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()

    attrs.add(self._config.get("old_key"))
    for r in self._config.get("copy_item_attrs"):
      prev_attrs.add(r)

    prev_items_from_attr = self._config.get("origin_id_list")
    if prev_items_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_items_from_attr) for v in prev_attrs)
      attrs.update(t)

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for r in self._config.get("copy_item_attrs"):
      attrs.add(r)
    return attrs

class LiveRecommendReasonMapArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "liverecommend_reason_map"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("reason_list_attr")))

    return attrs

class LivestreamCounterControlArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_counter_control_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(["author_id", "live_id"])

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("max_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("time_unit_munutes")))
    attrs.update(self.extract_dynamic_params(self._config.get("window_size")))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("live_server_show_cnt")
    attrs.add("live_pid_score")

    return attrs

class LiveStreamExploreAuthorProtectInsertArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_explore_author_protect_insert"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("reason_list_attr")))

    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("insert_position_list_str")))

    return attrs

class LiveStreamUnifiedStrategyFrArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unified_strategy_fr_arranger"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("strategy")))
    if 'strategy_config_ab_params' in self._config.keys():
      attrs.update(self._config['strategy_config_ab_params'])
    return attrs

class LiveStreamChannelSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_channel_sort_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_queue_names")))
    attrs.update(self.extract_dynamic_params(self._config.get("mc_variant_config")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("queue_score_attrs"))
    attrs.update(self._config.get("queue_flag_attrs"))
    variants = ["live_author_full_tag_path", "is_cover_merchant_live", "mmu_tag_info.tag_list_lv1",
                "mmu_tag_info.tag_list_lv2", "mmu_tag_info.tag_list_lv3", "mmu_tag_info.tag_list_lv4",
                "mmu_tag_info.tag_list_lv5"]
    attrs.update(variants)
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class LiveStreamRetrievalFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_retrieval_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["acu_7d_filter_thred", "online_count_range_list", "online_count_thred_list", "kconf_live_black_author_list",
                 "punish_live_scene_id", "enable_scene_punish_effect", "kconf_live_gr_level", "multi_white_list_version",
                 "multi_big_v_white_list", "grade_distribute_rule_version", "grade_distribute_item_label_version", "neg_mmu_tags",
                 "diversity_tag_request_source_tag", "common_filter_list", "rc_not_skip_any_old_filter", "enable_teampk_exempt"])
    for item in ["common_filter_list", "on_filters", "off_filters", "unified_post_filter_mode", "truncation_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(item)))
    if self._config.get("old_default_browset_filter_name"):
      attrs.add(self._config["old_default_browset_filter_name"])
    if self._config.get("old_default_item_info_filter_name"):
      attrs.add(self._config["old_default_item_info_filter_name"])
    attrs.add(self._config.get("user_info_attr", "user_info_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["has_item_info", "has_dynamic_item_info", "fans_count", "mmu_info.mmu_impre_score", "is_cover_merchant_live",
                 "is_merchant_live", "author_info.acu_7d", "online_count", "fans_count", "is_voice_party", "is_theater", "is_ktv",
                 "is_teampk", "author_id", "scene_punish_info.key_list", "scene_punish_info.effect_list", "safe_level",
                 "count.follow_click", "count.follow_show", "count.nearby_show", "count.follow_report", "count.nearby_report",
                 "count.follow_negative", "count.nearby_negative", "audit_author_white_info.explore_author_version",
                 "audit_author_big_v_info.big_v_type", "grade_label", "timestamp", "audit_author_white_info.slide_author_version",
                 "author_mmuCategory_lv3", "live_all_tags", "author_info.is_seller_author"])
    attrs.add(self._config.get("off_filter_list_name", "item_off_filter_list"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("filter_flag_save_to"):
      attrs.add(self._config["filter_flag_save_to"])
    return attrs
  
class LiveStreamNewRetrievalFilterArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_new_retrieval_filter_arranger"
  
  @strict_types
  def _check_config(self) -> None:
    if self._config.get("debug_mode", False):
      if "export_item_attr" not in self._config:
        raise ArgumentError(" debug_mode 为 true ，但未配置 export_item_attr ")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "filters" in self._config:
      for filter in self._config.get("filters"):
        attrs.update(self.extract_dynamic_params(filter.get("enable")))
        for key, value in filter.items():
          if key!= "enable" and key!= "name" and key!= "filter_flag":
            attrs.add(value)
    if "truncation_map" in self._config:
      for num in self._config.get("truncation_map").values():
        attrs.update(self.extract_dynamic_params(num))

    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attr_map").values():
      attrs.add(attr)

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr"))
    return attrs
class LivestreamSelectRetrievalItemsArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_select_retrieval_items_arranger"

  @strict_types
  def _check_config(self) -> None:
    required_attrs = ['total_limit_num', 'reason_list', 'item_order', 'reason_weight_in_channel', 'channel2reason_list', 'channel_weight']
    for attr_name in required_attrs:
      if attr_name not in self._config:
        raise ArgumentError(f"{attr_name} 必须配置！保存去重 reason list!")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    dynamic_attrs = ['total_limit_num','reason_list', 'item_order']
    for attr_name in dynamic_attrs:
      attrs.update(self.extract_dynamic_params(self._config[attr_name]))
      
    if "reason_weight_in_channel" in self._config:
      for reason in self._config["reason_weight_in_channel"]:
        if isinstance(self._config["reason_weight_in_channel"][reason], str):
          attrs.update(self.extract_dynamic_params(self._config["reason_weight_in_channel"][reason]))
    if "channel_weight" in self._config:
      for channel in self._config["channel_weight"]:
        if isinstance(self._config["channel_weight"][channel], str):
          attrs.update(self.extract_dynamic_params(self._config["channel_weight"][channel]))
    if "channel2reason_list" in self._config:
      for channel in self._config["channel2reason_list"]:
        if isinstance(self._config["channel2reason_list"][channel], str):
          attrs.update(self.extract_dynamic_params(self._config["channel2reason_list"][channel]))
    return attrs
  # 
  # @property
  # @strict_types
  # def output_item_attrs(self) -> set:
  #   attrs = set()
  #   attrs.add(self._config.get("dedup_reason_attr_name"))
  #   return attrs

class LivestreamRetrievalChannelMergeArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_retrieval_channel_merge_arranger"

  @strict_types
  def _check_config(self) -> None:
    required_attrs = ['total_limit_num', 'channel2reason_list', 'channel_weight', 'reason_list']
    for attr_name in required_attrs:
      if attr_name not in self._config:
        raise ArgumentError(f"{attr_name} 必须配置！保存去重 reason list!")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    dynamic_attrs = ['total_limit_num', 'reason_list']
    for attr_name in dynamic_attrs:
      attrs.update(self.extract_dynamic_params(self._config[attr_name]))
      
    if "channel_weight" in self._config:
      for channel in self._config["channel_weight"]:
        if isinstance(self._config["channel_weight"][channel], str):
          attrs.update(self.extract_dynamic_params(self._config["channel_weight"][channel]))
    if "channel2reason_list" in self._config:
      for channel in self._config["channel2reason_list"]:
        if isinstance(self._config["channel2reason_list"][channel], str):
          attrs.update(self.extract_dynamic_params(self._config["channel2reason_list"][channel]))
    return attrs
