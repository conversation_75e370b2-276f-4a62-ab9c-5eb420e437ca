#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_processor import LeafMixer
from ...common_leaf_util import try_add_table_name, strict_types

class LivestreamGenerateSequenceMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "livestream_generate_sequence"
  
  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if self._config.get("type_attr"):
      attrs.add(self._config.get("type_attr"))
    attrs.update(self._config.get("item_attrs", []))
    attrs = try_add_table_name(self._config.get("item_table", ""), attrs)

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("item_key_list")
    attrs.add("item_index_list")
    seq_attrs = []
    for attr in self.gen_listwise_item_attr_list(self._config.get("item_attrs", [])):
      seq_attrs.append(attr)
      attrs.add(attr)
    
    self._config["seq_attrs"] = seq_attrs
    attrs = try_add_table_name(self._config.get("sequence_table", ""), attrs)
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("gap_table_string", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("type_min_pos_list", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("historical_item_type_list", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("type_num", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("seq_len", "")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self.item_table)
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("sequence_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    tables = self.output_item_tables
    return tables
  
  def gen_listwise_item_attr_list(self, item_attr_list) -> list:
    res = list()
    for attr in item_attr_list:
      if isinstance(attr, str):
        res.append(attr + "_list")
      else:
        raise TypeError(f"该 list 中存在不支持的配置类型 {type(attr)}: {item_attr_list}")

    return res

