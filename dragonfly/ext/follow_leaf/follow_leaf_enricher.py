#!/usr/bin/env python3
# coding=utf-8
from ...common_leaf_processor import <PERSON><PERSON>nricher
from ...common_leaf_util import strict_types

class FollowLeafAfterRetrieval<PERSON>n<PERSON>er(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_after_enricher"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add("FollowContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        attrs.add("is_broadcast_whitelist_author")
        attrs.add("broadcast_livestream_id")
        attrs.add("is_broadcast_original_list_author")
        attrs.add("percent_punish_list")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        return {"source", "percent_punish_ptr"}

class FollowLeafPredictEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_predict_enricher"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("kess_service"))
        attrs.add(self._config.get("request_type"))
        attrs.add(self._config.get("timeout_ms"))
        attrs.add(self._config.get("request_num_limit"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for filter in self._config.get("item_attr_name_list"):
            for item in filter.items():
                if item[0] == "name":
                    attrs.add(item[1])
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("pxtr_name_list"):
            attrs.add(self._config.get("output_prefix") + attr)
        return attrs

class FollowLeafInvertedItemAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_inverted_item_attr_enricher"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("user_info_path"))
        attrs.add("FollowContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("pId")
        attrs.add("pUploadTimeGapHour")
        attrs.add("aId")
        attrs.add("pIsHighValuePhoto")
        attrs.add("pIsHighValueAuthorPhoto")
        return attrs

class FollowLeafRetrievalModelFinalScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_retrieval_model_final_score_enricher"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("user_info_path"))
        attrs.update(self.extract_dynamic_params(self._config.get("retrieval_model_pxtr_alpha_weight_str")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_actioned_photo_coeff_in_mc")))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_follow_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_global_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_follow_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_recent_showed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_recent_showed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_recent_showed_photo_time_gap_hours")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_boost_high_value_author_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_high_value_author_photo_power")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_high_value_in_days_outside_common_leaf")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_keep_newfollow_ua_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("newfollow_author_timegap")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_recall_newfollow_author_photo_power")))
        attrs.add("FollowContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        attrs.add("recall_tower_plttr")
        attrs.add("recall_tower_pctr")
        attrs.add("recall_tower_pltr")
        attrs.add("recall_tower_psvtr")
        attrs.add("recall_tower_plvtr")
        attrs.add("recall_tower_pftr")
        attrs.add("recall_tower_pvtr")
        attrs.add("recall_tower_pevtr")
        attrs.add("recall_tower_psutr")
        attrs.add("recall_tower_pdist")
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("recall_final_score")
        return attrs

class FollowLeafV5RetrievalModelFinalScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_v5_retrieval_model_final_score_enricher"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("user_info_path"))
        attrs.update(self.extract_dynamic_params(self._config.get("v5_retrieval_model_pxtr_alpha_weight_str")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_boost_topk_pxtr")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_topk_pltr_ratio")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_topk_pcmtr_ratio")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_topk_pfinish_ratio")))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_follow_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_global_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_follow_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_browsed_photo_coeff_in_remote")))        
        attrs.update(self.extract_dynamic_params(self._config.get("enable_boost_high_value_author_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_high_value_author_photo_power")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_high_value_in_days_inner_common_leaf")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_keep_newfollow_ua_retrieval")))
        attrs.update(self.extract_dynamic_params(self._config.get("newfollow_author_timegap")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_recall_newfollow_author_photo_power")))
        attrs.add("FollowContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        attrs.add("v5_recall_tower_pl2r")
        attrs.add("v5_recall_tower_pltr")
        attrs.add("v5_recall_tower_pcmtr")
        attrs.add("v5_recall_tower_pnext")
        attrs.add("v5_recall_tower_pwt")
        attrs.add("v5_recall_tower_pfinish")
        attrs.add("v5_recall_tower_pshort_play")
        attrs.add("v5_recall_tower_plong_play")
        attrs.add("v5_recall_tower_ua_pl2r")
        attrs.add("v5_recall_tower_ua_pltr")
        attrs.add("v5_recall_tower_ua_pcmtr")
        attrs.add("v5_recall_tower_ua_pnext")
        attrs.add("v5_recall_tower_ua_pwt")
        attrs.add("v5_recall_tower_ua_pfinish")
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("v5_recall_final_score")
        return attrs

class FollowLeafV4BoostInnerRetrievalModelFinalScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_v4_boost_inner_retrieval_model_final_score_enricher"

    @strict_types
    def is_async(self) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("user_info_path"))
        attrs.update(self.extract_dynamic_params(self._config.get("v5_retrieval_model_pxtr_alpha_weight_str")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_actioned_photo_coeff_in_mc")))
        attrs.update(self.extract_dynamic_params(self._config.get("browse_photo_timestamp_gap")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_follow_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_global_browsed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_follow_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_global_browsed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_discount_recent_showed_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_recent_showed_photo_coeff_in_remote")))
        attrs.update(self.extract_dynamic_params(self._config.get("discount_recent_showed_photo_time_gap_hours")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_boost_high_value_author_photo")))
        attrs.update(self.extract_dynamic_params(self._config.get("boost_high_value_author_photo_power")))
        attrs.add("FollowContextAttrKey")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("query_index")
        attrs.add("score_index")
        attrs.add("v5_recall_tower_pl2r")
        attrs.add("v5_recall_tower_pltr")
        attrs.add("v5_recall_tower_pcmtr")
        attrs.add("v5_recall_tower_pnext")
        attrs.add("v5_recall_tower_ua_pl2r")
        attrs.add("v5_recall_tower_ua_pltr")
        attrs.add("v5_recall_tower_ua_pcmtr")
        attrs.add("v5_recall_tower_ua_pnext")
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("v5_recall_final_score")
        return attrs

class FollowLeafLocalCacheEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_local_cache_enricher"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("miss_cache_follow_list_attr"))
        attrs.add(self._config.get("cluster_index_to_attr"))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_retrieval_cache")))
        attrs.update(self.extract_dynamic_params(self._config.get("enable_retrieval_cache_empty")))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_query_index_to_attr"))
        attrs.add(self._config.get("query_index_to_attr"))
        attrs.add(self._config.get("score_attr_name"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("query_index_to_attr"))
        return attrs
    
class SplitFollowListEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "split_follow_list"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("each_shard_max_num_attr"))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_shard_num_attr"))
        for i in range(self._config.get("max_shard_num")):
            attrs.add(self._config.get("output_follow_list_attr") + "_" + str(i))
        return attrs

class FollowLeafAuthorFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_author_feature_enricher"

    @strict_types
    def is_async(self) -> bool:
        return False

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("aid_show_cnt")
        attrs.add("aid_view_cnt")
        attrs.add("is_friend")
        attrs.add("common_friend_count")
        attrs.add("follow_age_hour")
        return attrs

class FollowLeafCommonAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_common_attr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add("is_nebula_user")
        attrs.add("FollowContextAttrKey")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("follow_list_attr"))
        attrs.add(self._config.get("unfollow_list_attr"))
        return attrs

class FollowLeafUnfollowListLiveEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_leaf_unfollow_list_live_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("unfollow_list_attr"))
        attrs.add(self._config.get("unfollow_live_cursor_threshold_attr"))
        attrs.add("is_nebula_user")
        attrs.add("FollowContextAttrKey")
        attrs.add("extra_add_mmu_revenue_author_ids")
        attrs.add("extra_add_bigr_pk_author_ids")
        attrs.add("extra_add_gnn_revenue_author_ids")
        attrs.add("extra_add_long_gift_model_author_ids")
        attrs.add("extra_aids")
        attrs.add("extra_aids_reason")
        attrs.add("slide_mode_cursor_index")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("unfollow_list_live_attr"))
        return attrs


class FollowWholeStoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_whole_store_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_whole_store_merchant_cart")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("has_bid_info")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attr_table = self._config.get("attr_table")
        if attr_table != "only_live":
            attrs.add(self._config.get("photo_ad_trans_info", "iGoodsAdOwnerAdTransInfo"))
            attrs.add("merchant_item_id")
        if attr_table != "only_photo":
            attrs.add("ad_budget_put_status")
            attrs.add("ad_owner_put_status")
            attrs.add("l2_live_qzt_info__budge_put_status")
            attrs.add("l2_live_qzt_info__material_put_status")
            attrs.add("ad_trans_info")
            attrs.add("l2_live_qzt_info__ad_trans_info")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("liveId")
        attrs.add("productID")
        attrs.add("itemType")
        attrs.add("adTransInfo")
        attrs.add("need_call_bid")
        attrs.add("is_live_valid")
        attrs.add('is_product_photo')
        attrs.add("is_l2_live_valid")
        return attrs
    

class FollowRankFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_rank_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add("uid")
        attrs.add("pxtr_rank_pxtrs_feature_str")
        attrs.add("uUserClassLivePayingTypeNebula")
        attrs.add("uUserClassLivePayingTypeMainApp")
        attrs.add("uMerchantUserQuantileScore")
        attrs.add("uBuyerEffectiveType")
        attrs.add("uClickListFollow")
        attrs.add("uClickListFollowLive")
        attrs.add("uLikePhotoAuthorListFollow")
        attrs.add("uLikeLiveAuthorList")
        attrs.add("uDislikePhotoAuthorList")
        attrs.add("uDislikeLiveAuthorList")
        attrs.add("uHatePhotoAuthorListFollow")
        attrs.add("uHateLiveAuthorListFollow")
        
        attrs.add("uAllLikeLIST")
        attrs.add("uLikeListPhotoSlideFollow")
        attrs.add("uLikeListFollow")
        attrs.add('uLikeListFollowLive')
        attrs.add("uLikeListHot")
        attrs.add("uLikeListNear")
        attrs.add("uLikeListNearInside")
        attrs.add("uLikeListBlHot")
        attrs.add("uLikeListBlNear")
        attrs.add("uLikeListBlFollow")
        attrs.add("uLikeListSlHot")
        attrs.add("uLikeListSlNear")
        attrs.add("uLikeListSlFollow")
        attrs.add("uCommentPhotoList")
        attrs.add("uCommentPhotoListFollow")
        attrs.add("uCommentPhotoListHot")
        attrs.add("uCommentPhotoListNear")
        attrs.add("uCommentPhotoListSlHot")
        attrs.add("uCommentPhotoListSlNear")
        attrs.add("uCommentListPhotoSlideFollow")
        attrs.add("uCommentListPhotoBlSlideFollow")
        attrs.add("uForwardList")
        attrs.add("uForwardListHot")
        attrs.add("uForwardListNear")
        attrs.add("uForwardListFollow")
        attrs.add("uHateListHot")
        attrs.add("uHateListNear")
        attrs.add("uHateListFollow")
        attrs.add("uHateListSlHot")
        attrs.add("uHateListSlNear")
        attrs.add("uHateListSlFollow")
        attrs.add("uHateListPhotoSlideFollow")
        attrs.add("uHateListPhotoBlSlideFollow")
        attrs.add("uFollowLiveTextCmt14DAuthorIdsList")
        attrs.add("uFollowLiveTextCmt14DCmtCntList")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        attrs.add("live_room_enter_exit_count_str_list")
        attrs.add("follow_live_quality_result_ptr")
        attrs.add("timestamp")
        attrs.add("author_ds_info__p50_watched_live_duration")
        attrs.add("author_ds_info__p50_watched_photo_duration")
        attrs.add("author_ds_info__avg_watched_live_duration")
        attrs.add("author_ds_info__avg_watched_photo_duration")
        attrs.add("author_ds_info__avg_photo_fctr")
        attrs.add("author_ds_info__avg_live_fctr")
        attrs.add("fans_count")
        attrs.add("duration_ms")
        attrs.add("photo_type")
        attrs.add('count__type')
        attrs.add("count__value")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_cnt")
        attrs.add("lCurrentOnlineCount")
        attrs.add("lMinuteEnterCount")
        attrs.add("lMinuteExitCount")
        attrs.add("lMinuteExitRate")
        attrs.add("pIsFollow7Day")
        attrs.add("pIsFollow14Day")
        attrs.add("pIsFollow30Day")
        attrs.add("pIsFavoriateAuthorItem")
        attrs.add("uFollowAuthorPhotoAggrWatchTimePercent")
        attrs.add("uFollowAuthorPhotoAggrWatchTime")
        attrs.add("aPokeTimes")
        attrs.add("uAuthorCrossTodayEmpFtrFollowPhoto")
        attrs.add("uAuthorCrossTodayEmpCmtrFollowPhoto")
        attrs.add("uAuthorCrossTodayEmpCtrFollowPhoto")
        attrs.add("uAuthorCrossTodayEmpLtrFollowPhoto")
        attrs.add("uAuthorCrossTodayAvgTimeFollowPhoto")
        attrs.add("uAuthorCrossRecentAvgTimeFollowPhoto")
        attrs.add("uAuthorCrossRecentEmpFtrFollowPhoto")
        attrs.add("uAuthorCrossRecentEmpCtrFollowPhoto")
        attrs.add("uAuthorCrossRecentEmpLtrFollowPhoto")
        attrs.add("uAuthorCrossRecentEmpCmtrFollowPhoto")
        attrs.add("aFollowSource")
        attrs.add("uAuthorAvgTimeProfile")
        attrs.add("uAuthorEmpCtrProfile")
        attrs.add("uAuthorAvgTimeFollowLive")
        attrs.add("uAuthorEmpCtrFollowLive")
        attrs.add("uAuthorEmpLtrProfile")
        attrs.add("uAuthorIsFriend")
        attrs.add("max_live_view_count_ma")
        attrs.add("max_live_comment_count_ma")
        attrs.add("max_live_like_count_ma")
        attrs.add("max_history_live_view_count_40min_ma")
        attrs.add("max_history_live_view_count_10min_ma")
        attrs.add("max_history_live_comment_count_40min_ma")
        attrs.add("max_history_live_comment_count_10min_ma")
        attrs.add("max_history_live_like_count_40min_ma")
        attrs.add("max_history_live_like_count_10min_ma")
        attrs.add("live_quality_view_count")
        attrs.add("live_quality_comment_count")
        attrs.add("live_quality_like_count")
        attrs.add("live_max_time_per_person")
        attrs.add("live_quality_time_per_person")
        attrs.add("live_exit_conn_count_10min")
        attrs.add("live_exit_conn_count_1min")
        attrs.add("live_entry_conn_count_10min")
        attrs.add("live_entry_conn_count_1min")
        attrs.add("live_online_person_count_all")
        attrs.add("pUserAuthorPhotoEmpCtr")
        attrs.add("pUserAuthorPhotoEmpTPC")
        attrs.add("pUserAuthorPhotoEmpCmtr")
        attrs.add("pUserAuthorPhotoEmpLtr")
        attrs.add("live_quality_score_v1")
        attrs.add("live_current_time_per_person")
        attrs.add("live_item_detail_buy_count_1min")
        attrs.add("live_forward_count_10min")
        attrs.add("live_forward_count_5min")
        attrs.add("live_forward_count_1min")
        attrs.add("live_unfollow_count_10min")
        attrs.add("live_unfollow_count_5min")
        attrs.add("is_shop_negative_live")
        attrs.add("is_pk")
        attrs.add("is_chat")
        attrs.add("is_voice_party")
        attrs.add("has_red_pack")
        attrs.add("live_open_ten_minutes")
        attrs.add("min_live_exit_rate")
        attrs.add("max_time_per_show")
        attrs.add("min_live_exit_rate_ma")
        attrs.add("max_live_watch_time_per_person_ma")
        attrs.add("pIsShown")
        attrs.add("pIsClick")
        attrs.add("uIsShowCnt")
        attrs.add("uIsClickCnt")
        attrs.add("pLastShownTimeLagMinute")
        attrs.add("pLastShownTimeLagSecond")
        attrs.add("uMergeShowClick")
        attrs.add("AidIsShownCnt")
        attrs.add("AidIsViewCnt")
        attrs.add("uSearchClickPhoto")
        attrs.add("uIsItemShown")
        attrs.add("uIsItemClicked")
        attrs.add("uIsItemLiked")
        attrs.add("min_history_live_exit_rate_40min_ma")
        attrs.add("min_history_live_exit_rate_30min_ma")
        attrs.add("min_history_live_exit_rate_20min_ma")
        attrs.add("min_history_live_exit_rate_10min_ma")
        attrs.add("max_history_live_watch_time_per_person_40min_ma")
        attrs.add("max_history_live_watch_time_per_person_30min_ma")
        attrs.add("max_history_live_watch_time_per_person_20min_ma")
        attrs.add("max_history_live_watch_time_per_person_10min_ma")
        attrs.add("live_follow_count_10min")
        attrs.add("live_follow_count_1min")
        attrs.add("live_shop_button_click_count_10min")
        attrs.add("live_shop_button_click_count_5min")
        attrs.add("live_shop_button_click_count_1min")
        attrs.add("live_shop_button_show_count_10min")
        attrs.add("live_shop_button_show_count_5min")
        attrs.add("live_shop_button_show_count_1min")
        attrs.add("live_quality_exit_rate")
        attrs.add("live_current_exit_rate")
        attrs.add("live_max_time_per_show")
        attrs.add("live_current_time_per_show")
        attrs.add("live_exit_count_10min")
        attrs.add("live_exit_count_5min")
        attrs.add("live_entry_count_10min")
        attrs.add("live_entry_count_5min")
        attrs.add("live_gift_count_5min")
        attrs.add("live_gift_count_1min")
        attrs.add("live_follow_count_5min")
        attrs.add("live_min_exit_rate")
        attrs.add("live_comment_count_10min")
        attrs.add("live_comment_count_5min")
        attrs.add("live_comment_count_1min")
        attrs.add("live_like_count_10min")
        attrs.add("live_like_count_5min")
        attrs.add("live_like_count_1min")
        attrs.add("live_exit_conn_count_5min")
        attrs.add("live_entry_conn_count_5min")
        attrs.add("live_watch_time_10min")
        attrs.add("live_watch_time_5min")
        attrs.add("live_watch_time_1min")
        attrs.add("live_realshow_count_10min")
        attrs.add("live_realshow_count_5min")
        attrs.add("live_realshow_count_1min")
        attrs.add("max_live_view_rate_ma")
        attrs.add("max_live_comment_rate_ma")
        attrs.add("max_live_like_rate_ma")
        attrs.add("max_history_live_view_rate_40min_ma")
        attrs.add("max_history_live_view_rate_10min_ma")
        attrs.add("max_history_live_comment_rate_40min_ma")
        attrs.add("max_history_live_comment_rate_10min_ma")
        attrs.add("max_history_live_like_rate_40min_ma")
        attrs.add("max_history_live_like_rate_10min_ma")
        attrs.add("live_quality_view_rate")
        attrs.add("live_quality_comment_rate")
        attrs.add("live_quality_like_rate")
        attrs.add("live_max_view_rate")
        attrs.add("live_max_comment_rate")
        attrs.add("live_max_like_rate")
        attrs.add("live_current_like_rate")
        attrs.add("live_current_comment_rate")
        attrs.add("live_current_view_rate")
        attrs.add("pUploadGapMinute")
        attrs.add("is_global_view")
        attrs.add("is_global_all_view")
        attrs.add("is_global_browse_final")
        attrs.add("is_follow_browse_final")
        attrs.add("is_follow_browse")
        attrs.add("item_key_4_ltr")
        attrs.add("crossCommonFriendList")
        attrs.add("crossCommonFriendCount")
        attrs.add("UADislikeLiveAuthorRank")
        attrs.add("UALikeLiveAuthorRank")
        attrs.add("UADislikePhotoAuthorRank")
        attrs.add("UALikePhotoAuthorRank")
        attrs.add("UAValidLiveCtr7d")
        attrs.add("UAPhotoLtr7d")
        attrs.add("UAPhotoCtr7d")
        attrs.add("UAValidPhotoCtr7d")
        attrs.add("UAValidPlayPhotoCnt7d")
        attrs.add("UAShowPhotoCnt7d")
        attrs.add("UAPlayPhotoCnt7d")
        attrs.add("UALikePhotoCnt7d")
        attrs.add("UAShowLiveCnt7d")
        attrs.add("UAValidPlayLiveCnt7d")
        attrs.add("UAProfilePageEnterCnt14d")
        attrs.add("pIsLive")
        attrs.add("aId")
        attrs.add("pId")
        attrs.add("pP50WatchedLiveDuration")
        attrs.add("pP50WatchedPhotoDuration")
        attrs.add("pAvgWatchedLiveDuration")
        attrs.add("pAvgWatchedPhotoDuration")
        attrs.add("pAvgPhotoFctr")
        attrs.add("pAvgLiveFctr")
        attrs.add("photo_author_fans_cnt")
        attrs.add("is_live_on_sale")
        attrs.add("aPlayDurationPhoto")
        attrs.add("aNegPerThousandPhoto")
        attrs.add("uAuthorIntimacyScore")
        attrs.add("pUserAuthorLiveEmpTPC")
        attrs.add("pUserAuthorLiveEmpCtr")
        attrs.add("pUserAuthorLiveEmpLtr")
        attrs.add("pUserAuthorLiveEmpCmtr")
        attrs.add("uAuthorEmpCtrFollowPhoto")
        attrs.add("uAuthorEmpCmtrFollowPhoto")
        attrs.add("pIsSelfItem")
        attrs.add("pIsPictureType")
        attrs.add("pPhotoType")
        attrs.add("pVideoDurationMs")
        attrs.add("lUploadGapMinute")
        attrs.add("uFollowAuthorTimeDay")
        attrs.add("uFollowAuthorTimeDayClass")
        attrs.add("uAuthorIntimacyOfRelationScore")
        attrs.add("aShowCntPhoto")
        attrs.add("aValidCtrPhoto")
        attrs.add("aLtrPhoto")
        attrs.add("aPlayDurationPerPhoto")
        attrs.add("aShowCntLive")
        attrs.add("aCtrLive")
        attrs.add("hs_l_pctr")
        attrs.add("hs_l_pltr")
        attrs.add("hs_l_lvtr")
        attrs.add("hs_l_psvr")
        attrs.add("hs_l_pgtr")
        attrs.add("hs_l_pwatch_time")
        attrs.add("hs_pctr")
        attrs.add("hs_pltr")
        attrs.add("hs_lvtr")
        attrs.add("hs_psvr")
        attrs.add("hs_pwatch_time")
        attrs.add("aLikePhotoAuthorRank")
        attrs.add("aLikeLiveAuthorRank")
        attrs.add("aDislikePhotoAuthorRank")
        attrs.add("aDislikeLiveAuthorRank")
        attrs.add("pUserRealshowPhotoCnt")
        attrs.add("pUserClickPhotoCnt")
        attrs.add("uFollowAuthorOrder")
        attrs.add("uFollowAuthorLive7dWatchTimeOrder")
        attrs.add("live_unfollow_count_1min")
        attrs.add("UaFollowRealshowTimes")
        attrs.add("UaLastShowGapSecond")
        attrs.add("should_punish_live")
        attrs.add("uHateAuthor")
        attrs.add("uIsPymkFollowReason")
        attrs.add("is_author_chat")
        attrs.add("pFollowPhotoEmpTPC")
        attrs.add('pFollowLiveEmpTPC')
        attrs.add('last_show_cnts_with_limit')
        attrs.add('last_show_s_with_limit')
        attrs.add('live_cmt_cnt')
        attrs.add('follow_time_s')
        attrs.add('ua_last_show_time_s_v2')
        attrs.add('aIsIntimateAuthor')
        attrs.add('uAuthorFollowGapHour')
        attrs.add('uAuthorEmpLtrFollowPhoto')
        attrs.add('uAuthorEmpFtrFollowPhoto')
        attrs.add('uAuthorAvgTimeFollowPhoto')
        attrs.add('author_key')
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_user_type")
        attrs.add("merchant_user_level")
        attrs.add("merchant_user_quantile")
        attrs.add("paying_type")
        attrs.add("active_refresh_times")
        attrs.add("passive_refresh_times")
        attrs.add("refresh_type")
        attrs.add("is_down_fresh")
        attrs.add("last_feed_list_cursor")
        return attrs

class FollowRecallFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_recall_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_time_ms")
        attrs.add("follow_day_gap")      
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        return attrs
    
class FollowRecallSlideFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_recall_slide_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_time_ms")
        attrs.add("follow_day_gap")        
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        return attrs

class FollowCalcBoostEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_boost_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        calculators = self._config.get("boost_calculators")
        for calculator in calculators:
            attrs.update(self.extract_dynamic_params(calculator.get("enable")))
            attrs.add(calculator.get("boost_value_attr"))
            if calculator.get("class_name") == "HighvalueUaBoostScoreCalculator":
                attrs.add("highvalue_author_v2_ids")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        calculators = self._config.get("boost_calculators")
        for calculator in calculators:
            attrs.add(calculator.get("is_work_attr"))
            if calculator.get("class_name") == "HighvalueUaBoostScoreCalculator":
                attrs.add("is_global_browse_final")
                attrs.add("is_follow_browse_final")
                attrs.add("is_follow_browse")
                attrs.add("is_global_all_view")
                attrs.add("author_id")
                attrs.add("timestamp")
        attrs.add(self._config.get("save_score_name"))
        return attrs

class FollowMemoryDataEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_memory_data_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("data_key")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_data_ptr_to_attr"))
    return attrs

class FollowLocalLifeLiveScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_locallife_live_store_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_follow_whole_store_l2_live")
        attrs.add("locallife_qzt_norm_coeff")
        attrs.add("enable_locallife_core_ua_boost")
        attrs.add("locallife_core_ua_boost_weight")
        attrs.add("locallife_core_ua_initial_score")
        attrs.add("locallife_live_sort_param")
        attrs.add('locallife_live_filter_param_v2')
        attrs.add("locallife_live_cpm_max_threshold")
        attrs.add("enable_locallife_flow_control_deboost")
        attrs.add("locallife_flow_control_use_monetization")
        attrs.add("locallife_flow_control_deboost_ratio")
        attrs.add("locallife_core_authors")
        if not self._config.get("is_slide", False):
            attrs.add("locallife_low_gpm_author_deboost_score")
            attrs.add("locallife_live_high_gpm_authors")
        attrs.update(self.extract_dynamic_params(self._config.get("enable_locallife_core_ua_filter")))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("author_id")
        attrs.add("l2_ad_monetization_control_deboost_ratio")
        attrs.add("l2_ad_flow_control_deboost_ratio")
        attrs.add("autoRoi")
        attrs.add("is_l2_live_valid")
        attrs.add("adTransInfo")
        attrs.add("l2_live_qzt_info__material_roi_ratio")
        attrs.update(self._config.get("xtr_list"), [])
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("has_locallife_live_score")
        attrs.add('locallife_live_score')
        attrs.add('is_l2_qzt_item')
        attrs.add('l2_qzt_ad_trans_info')
        attrs.add('l2_qzt_auto_roi')
        attrs.add('l2_qzt_roi_ratio')
        attrs.add('l2_qzt_gpm')
        attrs.add('l2_qzt_price')
        return attrs

class FollowKuibaUserToStrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_kuiba_user_to_str_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        user_attr_names = self._config.get("user_attr_names")
        for user_attr_name in user_attr_names:
            attrs.add(user_attr_name)
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("kuiba_user_attr_name"))
        return attrs

class FollowKuibaUserEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_kuiba_user_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("kuiba_user_attr_name"))
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        if self._config.get("feasury_attrs"):
            for attr in self._config.get("feasury_attrs"):
                attrs.add(attr)
        if self._config.get("common_attrs"):
            for attr in self._config.get("common_attrs"):
                attrs.add(attr)
        attrs.add("uLikeLiveAuthorList")
        attrs.add("userActivity")
        attrs.add("uWithFollowReason")
        attrs.add("uWeekday")
        attrs.add("uShowTimeMilliSecond")
        attrs.add("uShortTermInterestTagIdList")
        attrs.add("uRealtimeFriendListCnt")
        attrs.add("uRealtimeFriendList")
        attrs.add("uRealtimeFollowListWithoutFriendCnt")
        attrs.add("uRealtimeFollowListWithoutFriend")
        attrs.add("uRealtimeFollowListCnt")
        attrs.add("uRealtimeFollowList")
        attrs.add("uPredictZeroStatus")
        attrs.add("uPlayStatus")
        attrs.add("uPhotoUnClickList10")
        attrs.add("uPhotoShowTimeList10")
        attrs.add("uPhotoShowCntList10")
        attrs.add("uPhotoRealShowList10")
        attrs.add("uPhotoFullRankService")
        attrs.add("uPhotoDupUnClickList10")
        attrs.add("uPhotoClickCntList10")
        attrs.add("uPhoto7Day4RealShowTimesList")
        attrs.add("uPhoto7Day1RealShowTimesList")
        attrs.add("uPhoto7Day10RealShowTimesList")
        attrs.add("uPhoto4Hour4RealShowTimesList")
        attrs.add("uPhoto4Hour1RealShowTimesList")
        attrs.add("uPhoto4Hour10RealShowTimesList")
        attrs.add("uPhoto1Day4RealShowTimesList")
        attrs.add("uPhoto1Day1RealShowTimesList")
        attrs.add("uPhoto1Day10RealShowTimesList")
        attrs.add("uNotifyType")
        attrs.add("uModelTag")
        attrs.add("uMerchantUserQuantileScore")
        attrs.add("uLongTermUaModelTag")
        attrs.add("uLongTermInterestTagIdList")
        attrs.add("uLiveUnClickList10")
        attrs.add("uLiveShowTimeList10")
        attrs.add("uLiveShowCntList10")
        attrs.add("uLiveRetrFollow7DayCnt")
        attrs.add("uLiveRetrFollow30DayCnt")
        attrs.add("uLiveRetrFollow14DayCnt")
        attrs.add("uLiveRetrBClassCnt")
        attrs.add("uLiveRetrAClassCnt")
        attrs.add("uLiveRealShowList10")
        attrs.add("uLivePreferScore")
        attrs.add("uLiveNearbyWtr")
        attrs.add("uLiveNearbyLtr")
        attrs.add("uLiveNearbyHtr")
        attrs.add("uLiveNearbyAvgWatchTime")
        attrs.add("uLiveFullRankService")
        attrs.add("uLiveDupUnClickList10")
        attrs.add("uLiveClickCntList10")
        attrs.add("uLikePhotoAuthorList")
        attrs.add("uLastFeedCursor")
        attrs.add("uIsZeroPlay")
        attrs.add("uIsUsingV4Leaf")
        attrs.add("uIsTrueNewUser")
        attrs.add("uIsTopFresh")
        attrs.add("uIsTodayZeroPlay")
        attrs.add("uIsRefluxUser")
        attrs.add("uIsPredictZeroPlay")
        attrs.add("uIsNotify")
        attrs.add("uIsImportantGiftUserMix")
        attrs.add("uIsImportantGiftUser")
        attrs.add("uIsHateMerchantLiveUser")
        attrs.add("uIsGiftUserMix")
        attrs.add("uIsGiftUser")
        attrs.add("uIsEshopPlay")
        attrs.add("uIsBigUser")
        attrs.add("uHourOfDay")
        attrs.add("uHour")
        attrs.add("uGiftUserPayingType")
        attrs.add("uFreqLiveAuthorList")
        attrs.add("uFollowRetrievalPhotoAuthorList")
        attrs.add("uFollowRetrievalLiveAuthorList")
        attrs.add("uFollowPageProductType")
        attrs.add("uFollowEnableUniverseMixRank")
        attrs.add("uFollowCount")
        attrs.add("uFollowCostCnt7d")
        attrs.add("uFollowCostCnt30d")
        attrs.add("uFollowCostCnt1d")
        attrs.add("uFollowCostAmt7d")
        attrs.add("uFollowCostAmt30d")
        attrs.add("uFollowCostAmt1d")
        attrs.add("uFollowAuthorList")
        attrs.add("uFavoriteLiveType")
        attrs.add("uDislikePhotoAuthorList")
        attrs.add("uDislikeLiveAuthorList")
        attrs.add("uDayOfWeek")
        attrs.add("uCostCntAccu")
        attrs.add("uCostCnt7d")
        attrs.add("uCostCnt30d")
        attrs.add("uCostCnt1d")
        attrs.add("uCostAmtAccu")
        attrs.add("uCostAmt7d")
        attrs.add("uCostAmt30d")
        attrs.add("uCostAmt1d")
        attrs.add("uConsumeShopLiveLevel")
        attrs.add("uConsumeOtherLiveLevel")
        attrs.add("uConsumeLiveLevel")
        attrs.add("uConsumeGameLiveLevel")
        attrs.add("uCloseGiftRrankBeforeMix")
        attrs.add("uClickStatus")
        attrs.add("uActiveDegreeDetailType")
        attrs.add("requestId")
        attrs.add("leaf_time_ms")
        attrs.add("leaf_request_time")
        attrs.add("follow_photo_play_time_7d")
        attrs.add("follow_photo_play_time_28d")
        attrs.add("follow_photo_play_time_1d")
        attrs.add("follow_photo_play_time_14d")
        attrs.add("sim_user_follow_photo_list_lately200")
        attrs.add("sim_user_follow_photo_list_effectplay_lately200")
        attrs.add("sim_user_follow_photo_list_longplay_lately200")
        attrs.add("sim_user_follow_photo_tag_list_play_top50")
        attrs.add("sim_user_photo_tag_list_play_top50")
        attrs.add("sim_user_follow_author_list_play_top50")
        attrs.add("sim_user_follow_author_list_effectplay_top50")
        attrs.add("device_id_hash")
        attrs.add("dWifi")
        attrs.add("dMod")
        attrs.add("uNearbyNotActiveUser")
        attrs.add("uId")
        attrs.add("uGender")
        attrs.add("uFriendListNew")
        attrs.add("uDpGender")
        attrs.add("uAgeSeg")
        attrs.add("dId")
        attrs.add("uIp")
        attrs.add("uFollowList")
        attrs.add("uFollowCountFromFollowList")
        attrs.add("uCountry")
        attrs.add("uCity")
        attrs.add("uVideoPlayStatList")
        attrs.add("uShortPlayList")
        attrs.add("uProvince")
        attrs.add("uLongPlayList")
        attrs.add("uIsSouth")
        attrs.add("uClickList")
        attrs.add("uAllClickAuthorLIST")
        attrs.add("uUserLiveActiveType")
        attrs.add("uLocalLifeUserLayerU1")
        attrs.add("uLiveIsMagicGift")
        attrs.add("uLiveIsInvestGift")
        attrs.add("uLiveIsCharged")
        attrs.add("uWatchAuthorClusterList")
        attrs.add("uUserActiveDegreeJingxuan")
        attrs.add("uLiveWatchTimeList")
        attrs.add("uLiveWatchStatList")
        attrs.add("uLiveWatchStartTimeList")
        attrs.add("uLiveWatchList")
        attrs.add("uLikeList")
        attrs.add("uFollowActionStatList")
        attrs.add("uConsumePreferType")
        attrs.add("uAllLikeLIST")
        attrs.add("uActiveDegree30d")
        attrs.add("uActive7dCntJingxuan")
        attrs.add("uClickPhotoLatestTimeMsFollow")
        attrs.add("uClickPhotoAuthorListFollow")
        attrs.add("uClickListFollow")
        attrs.add("uClickFollowPhotoTimeGap")
        attrs.add("uLiveTrueClickList")
        attrs.add("uRSNCLT")
        attrs.add("uRSNCLDNN")
        attrs.add("uRSNCL")
        attrs.add("uHotClickTsLIST")
        attrs.add("uHotClickAuthorLIST")
        attrs.add("uHotClickAuthorDnnLIST")
        attrs.add("uClickListPhotoSlideFollow")
        attrs.add("uClickListPhotoAuthorSlideFollow")
        attrs.add("uClickListHot")
        attrs.add("uForwardList")
        attrs.add("uShortPlayLiveList")
        attrs.add("uShortPlayLiveAuthorList")
        attrs.add("uShortPlayListPhotoSlideFollow")
        attrs.add("uShortPlayListPhotoAuthorSlideFollow")
        attrs.add("uRecentClickListHot")
        attrs.add("user_recent_7d_play_top_time_list")
        attrs.add("user_recent_7d_play_top_author_list")
        attrs.add("user_recent_14d_play_top_time_list")
        attrs.add("user_recent_14d_play_top_author_list")
        attrs.add("uLongPlayListFollow")
        attrs.add("uClickAuthorClusterList")
        attrs.add("follow_watch_live_times_7d")
        attrs.add("follow_watch_live_times_28d")
        attrs.add("follow_watch_live_times_1d")
        attrs.add("follow_watch_live_times_14d")
        attrs.add("uStandardFollowRealshowTimestampList")
        attrs.add("uStandardFollowRealshowPlayTimeList")
        attrs.add("uStandardFollowRealshowPhotoIdList")
        attrs.add("uStandardFollowRealshowLabelList")
        attrs.add("uStandardFollowRealshowChannelList")
        attrs.add("uStandardFollowRealshowAuthorIdList")
        attrs.add("uMediumPlayLiveList")
        attrs.add("uMediumPlayLiveAuthorList")
        attrs.add("uShortPlayListFollow")
        attrs.add("uLongPlayLiveList")
        attrs.add("uLongPlayLiveAuthorList")
        attrs.add("uLongPlayListPhotoSlideFollow")
        attrs.add("uLongPlayListPhotoAuthorSlideFollow")
        attrs.add("uLiveHeadClickAuthorIdList")
        attrs.add("uClickLiveAuthorListBs")
        attrs.add("uClickListBsLive")
        attrs.add("uLong")
        attrs.add("uLat")
        attrs.add("uClickLiveAuthorList")
        attrs.add("uClickListLive")
        attrs.add("uLongPlayListHot")
        attrs.add("uLikePhotoAuthorListFollow")
        attrs.add("uLikeListFollow")
        attrs.add("uClickListNear")
        attrs.add("uShortPlayListHot")
        attrs.add("uNebulaMerchantPayFreqTag30D")
        attrs.add("uNebulaMerchantPayAmtTag30D")
        attrs.add("uNebulaLivePayTag14D")
        attrs.add("uLastFollowActiveTime")
        attrs.add("uKuaishouMerchantPayFreqTag30D")
        attrs.add("uKuaishouMerchantPayAmtTag30D")
        attrs.add("uKuaishouLivePayTag14D")
        attrs.add("uForwardPhotoAuthorList")
        attrs.add("uClickLiveAuthorListFollow")
        attrs.add("uClickListFollowLive")
        attrs.add("uClickAuthorClusterListFollow")
        attrs.add("uClickLiveLatestTimeMsFollow")
        attrs.add("uClickFollowLiveTimeGap")
        attrs.add("uLiveFollowStatList")
        attrs.add("uLikeListPhotoSlideFollow")
        attrs.add("uLikeListPhotoAuthorSlideFollow")
        attrs.add("uFollowLiveAuthorList")
        attrs.add("uFollowListLive")
        attrs.add("uShortPlayListLiveSlideFollow")
        attrs.add("uShortPlayListLiveAuthorSlideFollow")
        attrs.add("uLikeListHot")
        attrs.add("uStandardClickLiveFollowTimeList")
        attrs.add("uStandardClickLiveFollowAuthorIdList")
        attrs.add("uHighvalueAuthorListV1")
        attrs.add("uStandardAllPlayInsideAndOutSideLiveFollowValueList")
        attrs.add("uStandardAllPlayInsideAndOutSideLiveFollowTimeList")
        attrs.add("uStandardAllPlayInsideAndOutSideLiveFollowIdList")
        attrs.add("uStandardAllPlayInsideAndOutSideLiveFollowAuthorIdList")
        attrs.add("uStandardRealShowPhotoFollowTimeList")
        attrs.add("uStandardRealShowPhotoFollowIdList")
        attrs.add("uStandardRealShowPhotoFollowAuthorIdList")
        attrs.add("uEffectPlayListNear")
        attrs.add("uClickLiveAuthorListSlHot")
        attrs.add("uClickListSlHotLive")
        attrs.add("uProfileListHot")
        attrs.add("uClickLiveAuthorListNear")
        attrs.add("uClickListNearLive")
        attrs.add("uClickAuthorClusterListNear")
        attrs.add("dAppList")
        attrs.add("uLongPlayListNear")
        attrs.add("uShortPlayListNearInside")
        attrs.add("uLiveNearbyCtr")
        attrs.add("uClickLiveAuthorListFollowOut")
        attrs.add("uClickListFollowOutLive")
        attrs.add("uClickListNearInside")
        attrs.add("uEffectPlayListNearInside")
        attrs.add("uMerchantClickCartSellerIdList")
        attrs.add("uLongPlayListNearInside")
        attrs.add("uShortPlayListNear")
        attrs.add("uClickLiveAuthorListHot")
        attrs.add("uClickListHotLive")
        attrs.add("uClickAuthorClusterListHot")
        attrs.add("uLiveForwardStatList")
        attrs.add("uFreqVisitAuthorClickList")
        attrs.add("uLikeListNear")
        attrs.add("user_layer")
        attrs.add("uKsCoin")
        attrs.add("uLiveCommentStatList")
        attrs.add("uCommentLiveAuthorList")
        attrs.add("uCommentListLive")
        attrs.add("uLiveHateStatList")
        attrs.add("uBuyerEffectiveType")
        attrs.add("uMerchantBuyItemSellerIdList")
        attrs.add("uLikeListNearInside")
        attrs.add("uMerchantLastPurTimeSub")
        attrs.add("uMerchantLast30DOrderCnt")
        attrs.add("uMerchantIsPhotoAuthor")
        attrs.add("uMerchantFreCityLevel")
        attrs.add("uEshopLast30dLivePlayDur")
        attrs.add("uForwardPhotoAuthorListFollow")
        attrs.add("uForwardListFollow")
        attrs.add("uBlackAuthorList")
        attrs.add("uForwardLiveAuthorList")
        attrs.add("uForwardListLive")
        attrs.add("uTaskFollowList")
        attrs.add("uHatePhotoAuthorListHot")
        attrs.add("uSearchClickAuthorIdList")
        attrs.add("uCommentPhotoListFollow")
        attrs.add("uCommentPhotoAuthorListFollow")
        attrs.add("uForwardPhotoAuthorListHot")
        attrs.add("uForwardListHot")
        attrs.add("uHateListHot")
        attrs.add("uFrequentWatchAuthorList")
        attrs.add("uHatePhotoAuthorListFollow")
        attrs.add("uHateListFollow")
        attrs.add("uPhotoBuyItemSellerIdList")
        attrs.add("uLocalLifeClickList")
        attrs.add("uHateLiveAuthorList")
        attrs.add("uHateListLive")
        attrs.add("uEshopBuyItemSellerIdList")
        attrs.add("uCommentPhotoList")
        attrs.add("uCommentPhotoAuthorList")
        attrs.add("uUserClassLivePayingTypeMainApp")
        attrs.add("uLongPlayListLiveSlideFollow")
        attrs.add("uLongPlayListLiveAuthorSlideFollow")
        attrs.add("uLiveGiftWorthListAll")
        attrs.add("uLiveGiftTimeListAll")
        attrs.add("uLiveGiftStatList")
        attrs.add("uLiveGiftAuthorListAll")
        attrs.add("uGiftLiveAuthorList")
        attrs.add("uGiftListLive")
        attrs.add("uPaidTag90d")
        attrs.add("uSearchClickMerchantAuthorIdList")
        attrs.add("uEshopClickSearchSellerIdList")
        attrs.add("uUnFrequentWatchAuthorList")
        attrs.add("uClickListBlHot")
        attrs.add("uForwardPhotoAuthorListNear")
        attrs.add("uForwardListNear")
        attrs.add("uCommentListPhotoSlideFollow")
        attrs.add("uCommentListPhotoAuthorSlideFollow")
        attrs.add("uHatePhotoAuthorList")
        attrs.add("uForwardLiveAuthorListFollow")
        attrs.add("uForwardListFollowLive")
        attrs.add("uHatePhotoAuthorListNear")
        attrs.add("uFowardListPhotoSlideFollow")
        attrs.add("uFowardListPhotoAuthorSlideFollow")
        attrs.add("uHateListNear")
        attrs.add("uCommentPhotoListHot")
        attrs.add("uCommentPhotoAuthorListHot")
        attrs.add("uShortPlayListPhotoBlSlideFollow")
        attrs.add("uShortPlayListPhotoAuthorBlSlideFollow")
        attrs.add("uLikeListBlHot")
        attrs.add("uHateLiveAuthorListHot")
        attrs.add("uHateListHotLive")
        attrs.add("uClickListBl")
        attrs.add("uForwardLiveAuthorListHot")
        attrs.add("uForwardListHotLive")
        attrs.add("uClickListPhotoBlSlideFollow")
        attrs.add("uClickListPhotoAuthorBlSlideFollow")
        attrs.add("uFriendReverseRemoveUserList")
        attrs.add("uLiveKuaishouMonthlyGiftGvalueWeightList")
        attrs.add("uLiveKuaishouMonthlyGiftGtrWeightList")
        attrs.add("uLiveKuaishouMonthlyGiftCtrWeightList")
        attrs.add("uLiveKuaishouMonthlyGiftAuthorLst")
        attrs.add("uLiveKuaishouMonthlyGiftAmtList")
        attrs.add("uLongPlayListPhotoBlSlideFollow")
        attrs.add("uLongPlayListPhotoAuthorBlSlideFollow")
        attrs.add("uHateListPhotoSlideFollow")
        attrs.add("uHateListPhotoAuthorSlideFollow")
        attrs.add("uHateListSlHot")
        attrs.add("uHateLiveAuthorListBs")
        attrs.add("uHateListBsLive")
        attrs.add("uCommentPhotoListNear")
        attrs.add("uCommentPhotoAuthorListNear")
        attrs.add("uClickListBlNear")
        attrs.add("uFollowLiveAuthorListNear")
        attrs.add("uFollowListNearLive")
        attrs.add("uClickListLiveBlSlideFollow")
        attrs.add("uClickListLiveAuthorBlSlideFollow")
        attrs.add("uLiveLikeStatList")
        attrs.add("uLikeListPhotoBlSlideFollow")
        attrs.add("uLikeListPhotoAuthorBlSlideFollow")
        attrs.add("uHateLiveAuthorListFollow")
        attrs.add("uHateListFollowLive")
        attrs.add("uLikeLiveAuthorListFollow")
        attrs.add("uLikeListFollowLive")
        attrs.add("uStandardGiftThanksLiveAllValueList")
        attrs.add("uStandardGiftThanksLiveAllTimeList")
        attrs.add("uStandardGiftThanksLiveAllIdList")
        attrs.add("uStandardGiftThanksLiveAllAuthorIdList")
        attrs.add("uUserClassLivePayingTypeNebula")
        attrs.add("uHateLiveAuthorListNear")
        attrs.add("uHateListNearLive")
        attrs.add("uClickListBlFollow")
        attrs.add("uLikeListBlNear")
        attrs.add("uShortPlayListLiveBlSlideFollow")
        attrs.add("uShortPlayListLiveAuthorBlSlideFollow")
        attrs.add("uFollowLiveAuthorListHot")
        attrs.add("uFollowListHotLive")
        attrs.add("uLikeListLive")
        attrs.add("uFollowLiveAuthorListBs")
        attrs.add("uFollowListBsLive")
        attrs.add("uLikeListBlFollow")
        attrs.add("uFriendRemoveUserList")
        attrs.add("uFollowLiveAuthorListSlHot")
        attrs.add("uFollowListSlHotLive")
        attrs.add("uForwardLiveAuthorListNear")
        attrs.add("uForwardListNearLive")
        attrs.add("uLikeListSlHot")
        attrs.add("uForwardLiveAuthorListSlFollow")
        attrs.add("uForwardListSlFollowLive")
        attrs.add("uReportAuthorList")
        attrs.add("uHatePhotoAuthorListSlHot")
        attrs.add("uHatePhotoAuthorListSlFollow")
        attrs.add("uShortPlayListSlHot")
        attrs.add("uHateLiveAuthorListSlHot")
        attrs.add("uHateListSlHotLive")
        attrs.add("uFowardListPhotoBlSlideFollow")
        attrs.add("uFowardListPhotoAuthorBlSlideFollow")
        attrs.add("uLikeListSlFollow")
        attrs.add("uLocalLifeCreateOrderList")
        attrs.add("uHateListSlFollow")
        attrs.add("uLikeListSlNear")
        attrs.add("uLocalLifePayOrderList")
        attrs.add("uFollowLiveTextCmt14DCmtCntList")
        attrs.add("uFollowLiveTextCmt14DAuthorIdsList")
        attrs.add("uShortPlayListSlNear")
        attrs.add("uHateLiveAuthorListSlFollow")
        attrs.add("uHateLiveAuthorListFollowOut")
        attrs.add("uHateListSlFollowLive")
        attrs.add("uHateListFollowOutLive")
        attrs.add("uLongPlayListLiveBlSlideFollow")
        attrs.add("uLongPlayListLiveAuthorBlSlideFollow")
        attrs.add("uLongPlayListSlHot")
        attrs.add("uLikeLiveAuthorListNear")
        attrs.add("uLikeListNearLive")
        attrs.add("uFollowLiveAuthorListSlNear")
        attrs.add("uFollowListSlNearLive")
        attrs.add("uForwardLiveAuthorListSlNear")
        attrs.add("uForwardListSlNearLive")
        attrs.add("uLikeLiveAuthorListBs")
        attrs.add("uLikeListBsLive")
        attrs.add("uHatePhotoAuthorListSlNear")
        attrs.add("uHateListPhotoBlSlideFollow")
        attrs.add("uHateListPhotoAuthorBlSlideFollow")
        attrs.add("uCommentListPhotoBlSlideFollow")
        attrs.add("uCommentListPhotoAuthorBlSlideFollow")
        attrs.add("uHateListSlNear")
        attrs.add("uForwardLiveAuthorListBs")
        attrs.add("uForwardListBsLive")
        attrs.add("uFollowLiveAuthorListFollow")
        attrs.add("uFollowListFollowLive")
        attrs.add("uClickListSlNear")
        attrs.add("uClickListSlHot")
        attrs.add("uForwardLiveAuthorListSlHot")
        attrs.add("uForwardListSlHotLive")
        attrs.add("uLikeLiveAuthorListSlHot")
        attrs.add("uLikeListSlHotLive")
        attrs.add("uHateLiveAuthorListSlNear")
        attrs.add("uHateListSlNearLive")
        attrs.add("uLocalLifeClickGoodList")
        attrs.add("uLikeLiveAuthorListSlFollow")
        attrs.add("uLikeListSlFollowLive")
        attrs.add("uLikeLiveAuthorListHot")
        attrs.add("uLikeListHotLive")
        attrs.add("uHighvalueLiveAuthorListV2")
        attrs.add("uLongPlayListSlNear")
        attrs.add("uIsBigGForFollowKV")
        attrs.add("uLikeLiveAuthorListFollowOut")
        attrs.add("uLikeListFollowOutLive")
        attrs.add("uForwardLiveAuthorListFollowOut")
        attrs.add("uForwardListFollowOutLive")
        attrs.add("uShortPlayListSlFollow")
        attrs.add("uLongPlayListSlFollow")
        attrs.add("uForwardLiveAuthorListJXLiveTabSingle")
        attrs.add("uForwardListJXLiveTabSingleLive")
        attrs.add("uClickListSlFollow")
        attrs.add("uCommentPhotoListSlHot")
        attrs.add("uYear")
        attrs.add("uHateLiveAuthorListJXLiveTabSingle")
        attrs.add("uHateListJXLiveTabSingleLive")
        attrs.add("uLikeLiveAuthorListSlNear")
        attrs.add("uLikeListSlNearLive")
        attrs.add("uFollowListJXLiveTabSingleLive")
        attrs.add("uFollowListAuthorListJXLiveTabSingle")
        attrs.add("uGiftLiveAuthorListFollow")
        attrs.add("uGiftListFollowLive")
        attrs.add("uLikeListLiveBlSlideFollow")
        attrs.add("uLikeListLiveAuthorBlSlideFollow")
        attrs.add("uHateListLiveBlSlideFollow")
        attrs.add("uHateListLiveAuthorBlSlideFollow")
        attrs.add("uCommentPhotoListSlNear")
        attrs.add("uFollowLiveAuthorListSlFollow")
        attrs.add("uFollowListSlFollowLive")
        attrs.add("uHateLiveAuthorListJXLiveTabDouble")
        attrs.add("uHateListJXLiveTabDoubleLive")
        attrs.add("uIsBigRForFollowKV")
        attrs.add("uCommentLiveAuthorListNear")
        attrs.add("uCommentListNearLive")
        attrs.add("uLikeListJXLiveTabSingleLive")
        attrs.add("uLikeListJXLiveTabDoubleLive")
        attrs.add("uLikeListAuthorListJXLiveTabSingle")
        attrs.add("uLikeListAuthorListJXLiveTabDouble")
        attrs.add("uCommentLiveAuthorListSlHot")
        attrs.add("uCommentLiveAuthorListHot")
        attrs.add("uCommentLiveAuthorListBs")
        attrs.add("uCommentListSlHotLive")
        attrs.add("uCommentListHotLive")
        attrs.add("uCommentListBsLive")
        attrs.add("llsid")
        attrs.add("uFollowListJXLiveTabDoubleLive")
        attrs.add("uFollowListAuthorListJXLiveTabDouble")
        attrs.add("uCommentLiveAuthorListFollow")
        attrs.add("uCommentListFollowLive")
        attrs.add("uFowardListLiveBlSlideFollow")
        attrs.add("uFowardListLiveAuthorBlSlideFollow")
        attrs.add("uCommentLiveAuthorListFollowOut")
        attrs.add("uCommentListFollowOutLive")
        attrs.add("uIsCommonLeaf")
        attrs.add("uBeKickedBlackAidList")
        attrs.add("uStandardUnfollowAllIdListV0")
        attrs.add("uPropertyUserCluster")
        attrs.add("uClickListSlFollowLive")
        attrs.add('uCommentPhotoListSlFollow')
        return attrs

class FollowAdDspEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_ad_dsp_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("ad_dsp")
        attrs.add("ad_exp_tag")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add('ad_cpm')
        attrs.add('ad_bonus_ecpm')
        attrs.add('ad_reco_rank_benefit')
        attrs.add('author_id')
        attrs.add('is_ad_dsp')
        attrs.add('is_ad_fans_top')
        return attrs

class FollowMixCalSeqScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_cal_seq_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        calculators = self._config.get("calculators")
        for calculator in calculators:
            attrs.update(self.extract_dynamic_params(calculator.get("enable")))
            for common_attr in calculator.get("common_attr"):
                attrs.add(common_attr)
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        calculators = self._config.get("calculators")
        for calculator in calculators:
            for item_attr in calculator.get("item_attr"):
                attrs.add(item_attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        calculators = self._config.get("calculators")
        for calculator in calculators:
            attrs.add(calculator.get("cal_value"))
        return attrs

class FollowBizTypeEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_biz_type_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_ad_dsp")
        attrs.add("is_ad_fans_top")
        attrs.add("is_merchant_live")
        attrs.add("is_merchant_cart")
        attrs.add("is_natural")
        attrs.add(self._config.get("is_recruit_photo_attr"))
        attrs.add(self._config.get("is_recruit_live_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("biz_type_list")
        attrs.add('is_ad')
        attrs.add('is_merchant')
        attrs.add('is_gift')
        attrs.add('is_boost')
        attrs.add('is_store_wide')
        attrs.add('is_local_life')
        attrs.add('is_merchant_cart')
        return attrs
    

class FollowCommonScoreCalculator(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_common_score_calculator"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("copy_score_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        return attrs

class FollowGiftLiveScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_gift_live_score_calculator"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("gift_pxtrs"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("uIsGiftUser")
        attrs.add("uIsImportantGiftUser")
        attrs.add("uIsBigUser")
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        attrs.add("gift_cpm")
        attrs.add("gift_bonus")
        return attrs

class FollowMerchantLiveScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_live_score_calculator"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_merchant_bonus")
        attrs.add("enable_recover_high_level_merchant_seller")
        attrs.add("enable_recover_high_value_merchant_seller")
        attrs.add("enable_live_showcase_add_bonus")
        attrs.add("enable_live_new_goods_add_bonus")
        attrs.add("live_showcase_bonus_show_cnt_thres")
        attrs.add("live_showcase_days_thres_follow_not_buy")
        attrs.add("live_showcase_bonus_weight_redirect")
        attrs.add("live_showcase_bonus_weight_common")
        attrs.add("live_showcase_bonus_weight_recent_follow_not_buy")
        attrs.add("live_showcase_bonus_weight_decay_factor")
        attrs.add("new_goods_bonus_adjust")
        attrs.add("enable_adjust_new_goods_bonus_by_u_type")
        attrs.add("new_goods_bonus_weight_u2")
        attrs.add("new_goods_bonus_weight_u3")
        attrs.add("new_goods_bonus_weight_u4")
        attrs.add("skip_merchant_strategy_toB_new_goods_follow")
        attrs.add("enable_live_new_goods_bonus_self_weight")
        attrs.add("merchant_cpm_score_threshold")
        attrs.add("merchant_coldstart_showcase_seller_set")
        attrs.add("merchant_new_goods_aid_info")
        merchant_common_attrs = self._config.get("merchant_common_attr")
        for common_attr_name in merchant_common_attrs:
            attrs.add(common_attr_name)
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("merchant_pxtrs"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        attrs.add("merchant_cpm")
        attrs.add("merchant_bonus")
        attrs.add("merchant_cart_score")
        attrs.add("merchant_cart_cpm")
        attrs.add("merchant_bonus_toB")
        attrs.add("merchant_bonus_toB_new_goods")
        return attrs

class FollowMerchantLiveScoreCalculatorV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_live_score_calculator_v2"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_merchant_bonus")
        attrs.add("enable_recover_high_level_merchant_seller")
        attrs.add("enable_recover_high_value_merchant_seller")
        attrs.add("enable_live_showcase_add_bonus")
        attrs.add("enable_live_new_goods_add_bonus")
        attrs.add("live_showcase_bonus_show_cnt_thres")
        attrs.add("live_showcase_days_thres_follow_not_buy")
        attrs.add("live_showcase_bonus_weight_redirect")
        attrs.add("live_showcase_bonus_weight_common")
        attrs.add("live_showcase_bonus_weight_recent_follow_not_buy")
        attrs.add("live_showcase_bonus_weight_decay_factor")
        attrs.add("new_goods_bonus_adjust")
        attrs.add("enable_adjust_new_goods_bonus_by_u_type")
        attrs.add("new_goods_bonus_weight_u2")
        attrs.add("new_goods_bonus_weight_u3")
        attrs.add("new_goods_bonus_weight_u4")
        attrs.add("skip_merchant_strategy_toB_new_goods_follow")
        attrs.add("enable_live_new_goods_bonus_self_weight")
        attrs.add("merchant_cpm_score_threshold")
        attrs.add("merchant_coldstart_showcase_seller_set")
        attrs.add("merchant_new_goods_aid_info")
        merchant_common_attrs = self._config.get("merchant_common_attr")
        for common_attr_name in merchant_common_attrs:
            attrs.add(common_attr_name)
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("merchant_pxtrs"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        attrs.add("merchant_cpm")
        attrs.add("merchant_bonus")
        attrs.add("merchant_cart_score")
        attrs.add("merchant_cart_cpm")
        attrs.add("merchant_bonus_toB")
        attrs.add("merchant_bonus_toB_new_goods")
        return attrs

class FollowSlideMerchantLiveScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_merchant_live_score_calculator"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_follow_ltv_gmv_weight")
        attrs.add("merchant_cpm_score_threshold")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_ctr")
        attrs.add("merchant_inner_cvr")
        attrs.add("merchant_price")
        attrs.add("merchant_bonus")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add("merchant_cpm")
        attrs.add("merchant_score")
        return attrs

class FollowSlideMerchantCartScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_merchant_cart_score_calculator"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_cart_gmv")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add("merchant_cart_bonus")
        attrs.add("merchant_cart_cpm")
        attrs.add("merchant_cart_score")
        return attrs

class FollowSlideMerchantLiveheadScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_merchant_livehead_score_calculator"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_living_photo_gmv")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add("merchant_livehead_bonus")
        attrs.add("merchant_livehead_cpm")
        attrs.add("merchant_livehead_score")
        return attrs

class FollowSlideGiftLiveScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_gift_live_score_calculator"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_pb"))
        attrs.add("uIsGiftUser")
        attrs.add("uIsImportantGiftUser")
        attrs.add("uIsBigUser")
        attrs.add("enable_certain_ua_protect_all_request_big_g")
        attrs.add("enable_certain_ua_protect_big_g")
        attrs.add("enable_certain_ua_protect_all_request_important_gift_user")
        attrs.add("enable_certain_ua_protect_important_gift_user")
        attrs.add("enable_certain_ua_protect_all_request_gift_user")
        attrs.add("enable_certain_ua_protect_gift_user")
        attrs.add("enable_certain_ua_protect_all_request_normal_user")
        attrs.add("enable_certain_ua_protect_normal_user")
        attrs.add("enable_inner_gift_cpm")
        attrs.add("enable_use_simple_gift_cpm")
        attrs.add("enable_use_simple_gift_cpm_v3")
        attrs.add("certain_ua_protect_coeff_big_g")
        attrs.add("high_value_ua_protect_coeff_big_g")
        attrs.add("certain_ua_protect_coeff_important_gift_user")
        attrs.add("high_value_ua_protect_coeff_important_gift_user")
        attrs.add("certain_ua_protect_coeff_gift_user")
        attrs.add("high_value_ua_protect_coeff_gift_user")
        attrs.add("certain_ua_protect_coeff_normal_user")
        attrs.add("high_value_ua_protect_coeff_normal_user")
        attrs.add("gift_cpm_ctr_simple_bias")
        attrs.add("gift_cpm_ctr_simple_alpha")
        attrs.add("gift_cpm_ctr_simple_beta")
        attrs.add("gift_cpm_gtr_bias")
        attrs.add("gift_cpm_gtr_alpha")
        attrs.add("gift_cpm_gtr_beta")
        attrs.add("gift_cpm_gtr80_bias")
        attrs.add("gift_cpm_gtr80_alpha")
        attrs.add("gift_cpm_gtr80_beta")
        attrs.add("gift_cpm_gtr90_bias")
        attrs.add("gift_cpm_gtr90_alpha")
        attrs.add("gift_cpm_gtr90_beta")
        attrs.add("gift_cpm_ua_gtr_bias")
        attrs.add("gift_cpm_ua_gtr_alpha")
        attrs.add("gift_cpm_ua_gtr_beta")
        attrs.add("gift_cpm_gcount_bias")
        attrs.add("gift_cpm_gcount_alpha")
        attrs.add("gift_cpm_gcount_beta")
        attrs.add("gift_gtr_a")
        attrs.add("gift_gtr_w")
        attrs.add("gift_gtr_b")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("author_id")
        attrs.add("fr_pgtr")
        attrs.add("fr_pgtr80")
        attrs.add("fr_pgtr_price2")
        attrs.add("fr_pctr_simple")
        attrs.add("fr_pgtr90")
        attrs.add("fr_pua_gtr")
        attrs.add("fr_pgtr_count")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add("gift_cpm")
        attrs.add("gift_bonus")
        attrs.add("gift_score")
        return attrs

class FollowAdScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_ad_score_calculator_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("ad_attrs"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        return attrs

class FollowStoreWideScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_store_wide_score_calculator_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("store_wide_attrs"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add(self._config.get("score_name_attr"))
        attrs.add("ad_boost_cpm_score")
        attrs.add("roi_ratio")
        attrs.add("store_wide_cpm")
        attrs.add("has_store_wide")
        return attrs


class FollowSlideStoreWideScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_store_wide_score_calculator_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_storewide_live_es_gpm_calibration")
        attrs.add("ad_roi_ratio_alpha")
        attrs.add("follow_storewide_live_es_cali_ratio")
        attrs.add("enable_storewide_live_cali_cpm")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("merchant_inner_cvr")
        attrs.add("merchant_ctr")
        attrs.add("merchant_price")
        attrs.add("ad_roi_ratio_score")
        attrs.add("autoRoi")
        attrs.add("is_merchant_living_photo")
        attrs.add("is_merchant_cart")
        attrs.add("merchant_cart_gmv")
        attrs.add("merchant_living_photo_gmv")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("biz_index_attr"))
        attrs.add("ad_gpm_score")
        attrs.add("ad_boost_cpm_score")
        attrs.add("ad_auto_roi_score")
        attrs.add("store_wide_cpm")
        attrs.add("photo_store_wide_type")
        return attrs

class FollowSlideNaturalScoreCalculatorEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_slide_natural_score_calculator"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("l2r_f1_score")
        attrs.add("l2r_score")
        attrs.add("duration_ms")
        attrs.add("fr_pfinish")
        attrs.add("fr_pwt")
        return attrs
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("enable_f1_score_replace_ltr_score_photo")
        attrs.add("enable_f1_score_replace_ltr_score_live")
        attrs.add("enable_follow_natural_score_by_mutli_pxtr")
        attrs.add("photo_f1_score_w")
        attrs.add("photo_f1_score_a")
        attrs.add("photo_f1_score_b")
        attrs.add("photo_pfinish_score_w")
        attrs.add("photo_pfinish_score_a")
        attrs.add("photo_pfinish_score_b")
        attrs.add("photo_pwt_score_w")
        attrs.add("photo_pwt_score_a")
        attrs.add("photo_pwt_score_b")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("natural_score")
        attrs.add("natural_index")
        return attrs

class FollowFillAdResponseEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_fill_ad_response_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("natural_index")
        attrs.add("biz_type")
        attrs.add("ad_dsp")
        attrs.add("ad_exp_tag")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("ad_dsp_str")
        attrs.add("ad_fix_position")
        attrs.add("ad_natural_position")
        return attrs

class FollowMixLogEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_mix_log_enricher"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("page_size")
        attrs.add("mix_log_max_trace_photo_size")
        attrs.add("mix_log_max_trace_live_size")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("log_item_attr"):
            attrs.add(attr)
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("mix_rank_log_attr"))
        for attr in self._config.get("log_common_attr"):
            attrs.add(attr)
        return attrs
    

class FollowFeatureDebugEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_feature_debug_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("predict_item")
        for attr in self._config.get("debug_features"):
            attrs.add(attr)
        return attrs

class FollowMixListwiseModelSeqFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_listwise_model_seq_feature_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("seq_len")
        for attr in self._config.get("item_int_attrs"):
            attrs.add(attr + '_list')
        for attr in self._config.get("item_float_attrs"):
            attrs.add(attr + '_list')
        for attr in self._config.get("item_string_attrs"):
            attrs.add(attr + '_list')
        return attrs

class FollowMerchantStrToPxtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_str_to_pxtr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("merchant_pxtrs_cache_valid_second")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_value_name"))
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_merchant_live_hit_cache")
        attrs.add("eshop_live_ctr")
        attrs.add("eshop_live_cvr")
        attrs.add("eshop_live_price")
        attrs.add("eshop_live_bonus")
        attrs.add("eshop_live_l2r")
        attrs.add("eshop_live_rpr")
        attrs.add("eshop_live_pflag")
        attrs.add("eshop_live_ston_bigv")
        attrs.add("eshop_live_ston_item")
        attrs.add("eshop_live_ston_path")
        attrs.add("eshop_live_gctr")
        return attrs


class FollowMerchantPxtrToStrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_pxtr_to_str_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_merchant_live_hit_cache")
        attrs.add("eshop_live_ctr")
        attrs.add("eshop_live_cvr")
        attrs.add("eshop_live_price")
        attrs.add("eshop_live_bonus")
        attrs.add("eshop_live_l2r")
        attrs.add("eshop_live_rpr")
        attrs.add("eshop_live_pflag")
        attrs.add("eshop_live_ston_bigv")
        attrs.add("eshop_live_ston_item")
        attrs.add("eshop_live_ston_path")
        attrs.add("eshop_live_gctr")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_value_name"))
        return attrs

class FollowFillWholeStoreRoiPhotoEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fill_whole_store_roi_photo_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("follow_store_wide_photo_live_limit")
        attrs.add("follow_store_wide_merchant_cart_limit")
        attrs.add("enable_whole_store_live_photo")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_living_photo")
        attrs.add("living_photo_live_key")
        attrs.add("is_product_photo")
        attrs.add("is_store_wide")
        attrs.add("merchant_cart_pgmv")
        attrs.add("merchant_living_photo_pgmv")
        attrs.add("autoRoi")
        attrs.add("iGoodsAdOwnerRoiRatio")
        attrs.add("iGoodsAdOwnerAdTransInfo")
        attrs.add("iGoodsAdOwnerCreativeId")
        attrs.add("merchant_item_id")
        attrs.add("ad_roi_ratio_score")
        attrs.add("ad_trans_info")
        attrs.add("ad_creative_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("resp_ad_gpm_score")
        attrs.add("resp_ad_boost_cpm_score")
        attrs.add("resp_store_wide_cpm")
        attrs.add("resp_ad_auto_roi_score")
        attrs.add("resp_ad_roi_ratio_score")
        attrs.add("resp_ad_trans_info")
        attrs.add("resp_ad_creative_id")
        attrs.add("resp_photo_store_wide_type")
        attrs.add("resp_has_store_wide")
        attrs.add("resp_photo_store_wide_live_id")
        attrs.add("resp_merchant_product_id")
        return attrs

class FollowFillWholeStoreRoiLiveEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fill_whole_store_roi_live_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("ad_roi_ratio_alpha")
        attrs.add("enable_store_wide_in_mix")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("fr_pctr")
        attrs.add("eshop_cart_cvr")
        attrs.add("eshop_live_price")
        attrs.add("autoRoi")
        attrs.add("ad_roi_ratio_score")
        attrs.add("ad_trans_info")
        attrs.add("ad_creative_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("resp_ad_gpm_score")
        attrs.add("resp_ad_boost_cpm_score")
        attrs.add("resp_store_wide_cpm")
        attrs.add("resp_ad_auto_roi_score")
        attrs.add("resp_ad_roi_ratio_score")
        attrs.add("resp_ad_trans_info")
        attrs.add("resp_ad_creative_id")
        attrs.add("resp_has_store_wide")
        attrs.add("resp_is_whole_store_roi_ad")
        return attrs


class FollowParseUserInfoEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_pasre_user_info_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_path"))
        attrs.add(self._config.get("notify_response_path"))
        attrs.add("follow_model_tag")
        attrs.add("use_long_term_ua_model_tag")
        attrs.add("enable_get_follow_reason_list")
        attrs.add("followEnableUniverseMixRank")
        attrs.add("enable_close_gift_rank_before_mix")
        attrs.add("follow_quantile_merchant_user_factor")
        attrs.add("follow_live_common_predict_server_kess_name")
        attrs.add("follow_live_common_predict_server_request_type")
        attrs.add("follow_common_predict_server_kess_name")
        attrs.add("follow_common_predict_server_request_type")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = {
            "llsid",
            "WIFI",
            "wifi",
            "dWifi",
            "userActivity",
            "uFollowPageProductType",
            "device_id_hash",
            "leaf_request_time",
            "leaf_time_ms",
            "uIsTrueNewUser",
            "uIsRefluxUser",
            "uModelTag",
            "dMod",
            "uIsHateMerchantLiveUser",
            "uIsTopFresh",
            "uLastFeedCursor",
            "uLongTermUaModelTag",
            "uWithFollowReason",
            "uIsZeroPlay",
            "uIsPredictZeroPlay",
            "uPredictZeroStatus",
            "uClickStatus",
            "uPlayStatus",
            "uIsTodayZeroPlay",
            "uCostAmt1d",
            "uFollowCostAmt1d",
            "uCostAmt7d",
            "uFollowCostAmt7d",
            "uCostAmt30d",
            "uFollowCostAmt30d",
            "uCostAmtAccu",
            "uCostCnt1d",
            "uFollowCostCnt1d",
            "uCostCnt7d",
            "uFollowCostCnt7d",
            "uCostCnt30d",
            "uFollowCostCnt30d",
            "uCostCntAccu",
            "uFollowCount",
            "uLiveFullRankService",
            "uPhotoFullRankService",
            "uGiftUserPayingType",
            "uIsGiftUser",
            "uIsImportantGiftUser",
            "uIsGiftUserMix",
            "uIsImportantGiftUserMix",
            "uIsBigUser",
            "requestId",
            "uFollowAuthorList",
            "uHour",
            "uWeekday",
            "uFollowEnableUniverseMixRank",
            "uIsCommonLeaf",
            "uCloseGiftRrankBeforeMix",
            "uIsNotify",
            "uNotifyType",
            "uNotifyType",
            "uIsEshopPlay",
            "uPhoto4Hour1RealShowTimesList",
            "uPhoto4Hour4RealShowTimesList",
            "uPhoto4Hour10RealShowTimesList",
            "uPhoto1Day1RealShowTimesList",
            "uPhoto1Day4RealShowTimesList",
            "uPhoto1Day10RealShowTimesList",
            "uPhoto7Day1RealShowTimesList",
            "uPhoto7Day4RealShowTimesList",
            "uPhoto7Day10RealShowTimesList",
            "uUserClassLivePayingTypeNebula",
            "uUserClassLivePayingTypeMainApp",
            "uRealtimeFollowList",
            "uRealtimeFollowListCnt",
            "uRealtimeFollowListWithoutFriend",
            "uRealtimeFollowListWithoutFriendCnt",
            "uRealtimeFriendList",
            "uRealtimeFriendListCnt",
            "uLivePreferScore",
            "uPhotoRealShowList10",
            "uPhotoShowCntList10",
            "uPhotoClickCntList10",
            "uPhotoShowTimeList10",
            "uPhotoUnClickList10",
            "uPhotoDupUnClickList10",
            "uLiveRealShowList10",
            "uLiveShowCntList10",
            "uLiveClickCntList10",
            "uLiveShowTimeList10",
            "uLiveUnClickList10",
            "uLiveDupUnClickList10",
            "uFreqLiveAuthorList",
            "session_pre_items",
        }        
        for attr in self._config.get("feasury_attrs"):
            attrs.add(attr)
        return attrs

class FollowRetrievalFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_retrieval_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_pb"))
        attrs.add("fill_feasury_global_action_strs_outside")
        attrs.add("uClickListFollow")
        attrs.add("uLikeListFollow")
        attrs.add("uCommentPhotoListFollow")
        attrs.add("uShortPlayListFollow")
        attrs.add("uClickListSlFollow")
        attrs.add("uLikeListSlFollow")
        attrs.add("uCommentPhotoListSlFollow")
        attrs.add("uShortPlayListSlFollow")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("is_nebula_user")
        attrs.add("retrieval_user_info")
        attrs.add("retrieval_follow_list")
        attrs.add("retrieval_action_list_set")
        attrs.add("retrieval_fine_browsed_set")
        attrs.add("retrieval_global_action_list_set")
        return attrs

class FollowItemToCommonFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_item_to_common_feature_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("timestamp")
        attrs.add("author_id")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = {"uFollowRetrievalPhotoAuthorList",
                 "uFollowRetrievalLiveAuthorList",
                 "uLiveRetrAClassCnt",
                 "uLiveRetrBClassCnt",
                 "uLiveRetrFollow7DayCnt",
                 "uLiveRetrFollow14DayCnt",
                 "uLiveRetrFollow30DayCnt"}
        return attrs

class FollowLeafAdAthenaSendEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_leaf_ad_athena_send_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ad_request_for_leaf_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = {"ad_athena_batch_waiter", "ad_response_ptr", "ad_request_ptr"}
    return attrs


class FollowLeafRedisCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_leaf_redis_cache_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("use_cache_attr"))
    attrs.add(self._config.get("last_follow_cache_path"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attr_names = self._config.get("attr_names")
    for attr_name in attr_names:
        attrs.add(attr_name)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("follow_cache_str"))
    return attrs


class FollowParseDynamicParameterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_parse_dynamic_parameter_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("count__type")
    attrs.add("count__value")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("follow_real_show")
    attrs.add("follow_click")
    attrs.add("follow_like")
    attrs.add("follow_comment")
    attrs.add("follow_forward")
    attrs.add("follow_effective_view")
    attrs.add("follow_view_time")
    attrs.add("follow_watch_live_time")
    attrs.add("follow_negative")
    attrs.add("follow_out_click")
    attrs.add("follow_out_like")
    attrs.add("follow_out_comment")
    attrs.add("follow_out_forward")
    attrs.add("follow_out_effective_view")
    attrs.add("follow_out_view_time")
    attrs.add("follow_out_watch_live_time")
    attrs.add("follow_out_negative")
    attrs.add("follow_photo_emp_tpc")
    return attrs

class FollowMcSlideFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_mc_slide_feature_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("timestamp")
    attrs.add("author_id")
    attrs.add("item_type")
    attrs.add("photo_id")
    attrs.add("photo_like_count")
    attrs.add("photo_neg_count")
    attrs.add("duration_ms")
    attrs.add("fans_count")
    attrs.add("nebula_stats_real_show_count")
    attrs.add("thanos_stats_real_show_count")
    attrs.add("photo_cold_start_garantee")
    attrs.add("video_cold_start_author_tails_json")
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_pb"))
    attrs.add("uHatePhotoAuthorListFollow")
    attrs.add("uHatePhotoAuthorListSlFollow")
    attrs.add("highvalue_author_v2_ids_list")
    attrs.add("v5_highvalue_ua_in_days_boost")
    attrs.add("v5_highvalue_ua_photo_in_days_boost")
    attrs.add("v5_highvalue_ua_photo_in_days_boost_v2")
    attrs.add("follow_shield_vv_threshold")
    attrs.add("follow_guarantee_rank_threshold")
    attrs.add("enable_follow_vcs_shield_exp_refactor")
    attrs.add("follow_vcs_shield_exp_rule_kconf_key")
    attrs.add("live_author_list")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("timestamp_ms")
    attrs.add("visit_net")
    attrs.add("visit_channel")
    attrs.add("is_douyin")
    attrs.add("mod_price")
    attrs.add("life_time_status")
    attrs.add("upload_in_recent_hours")
    attrs.add("author_list")
    attrs.add("kgnn_samples_1st")
    attrs.add("relation_colossus_list")
    attrs.add("social_channel_id")
    attrs.add("social_degree")
    attrs.add("user_publish_cnt_map_ptr")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("is_hate_photo_author")
    attrs.add("photo_age_minute")
    attrs.add("photo_age_minute_int")
    attrs.add("photo_age_hour")
    attrs.add("is_global_play_photo")
    attrs.add("is_global_browse")
    attrs.add("is_realshow")
    attrs.add("aIsHighValueAuthor")
    attrs.add("aIsNoshowedHighValueAuthor7d7")
    attrs.add("aIsNoshowedHighValueAuthor7d1")
    attrs.add("aIsNoshowedHighValueAuthor1d")
    attrs.add("enable_high_ua_boost")
    attrs.add("enable_highvalue_ua_boost")
    attrs.add("is_discount_by_cold_start")
    attrs.add("vcs_shield_should_filter")
    attrs.add("user_author_follow_photo_emp_tpc")
    attrs.add("pos_rate")
    attrs.add("is_friend")
    attrs.add("is_global_browse")
    attrs.add("is_follow_browse")
    attrs.add("duration_ms_double")
    attrs.add("is_living_photo")
    attrs.add("high_quality_author")
    attrs.add("is_intimate_relation")
    attrs.add("uDislikePhotoAuthorList")
    attrs.add("uLikePhotoAuthorList")
    attrs.add("uLikeLiveAuthorList")
    attrs.add("uDislikeLiveAuthorList")
    return attrs

class FollowPercentPunishItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_percent_punish_item_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("percent_punish_list")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("percent_punish_ptr")
    return attrs

class FollowParsePhotoColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_parse_photo_colossus_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_info_path"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    attrs.add("photo_hetu_tag_info__hetu_level_one")
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add("sim_like_ratio_30d")
    attrs.add("sim_comment_ratio_30d")
    attrs.add("sim_play_cnt_30d")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("sim_user_author_playtime_7d")
    attrs.add("sim_user_author_effectcnt_7d")
    attrs.add("sim_interact_tag_cnt_1d")
    attrs.add("sim_interact_tag_cnt_3d")
    attrs.add("sim_interact_tag_cnt_7d")
    attrs.add("sim_interact_tag_cnt_14d")
    attrs.add("sim_interact_tag_cnt_30d")
    attrs.add("sim_interact_tag_last_lag_30d")
    attrs.add("sim_short_view_tag_cnt_1d")
    attrs.add("sim_short_view_tag_cnt_3d")
    attrs.add("sim_short_view_tag_cnt_7d")
    attrs.add("sim_short_view_tag_cnt_14d")
    attrs.add("sim_short_view_tag_cnt_30d")
    attrs.add("sim_short_view_tag_last_lag_30d")
    attrs.add("sim_effective_view_tag_cnt_1d")
    attrs.add("sim_effective_view_tag_play_1d")
    attrs.add("sim_effective_view_tag_cnt_3d")
    attrs.add("sim_effective_view_tag_play_3d")
    attrs.add("sim_effective_view_tag_cnt_7d")
    attrs.add("sim_effective_view_tag_play_7d")
    attrs.add("sim_effective_view_tag_cnt_14d")
    attrs.add("sim_effective_view_tag_play_14d")
    attrs.add("sim_effective_view_tag_cnt_30d")
    attrs.add("sim_effective_view_tag_last_lag_30d")
    attrs.add("sim_effective_view_tag_play_30d")
    attrs.add("sim_long_view_tag_cnt_1d")
    attrs.add("sim_long_view_tag_cnt_3d")
    attrs.add("sim_long_view_tag_cnt_7d")
    attrs.add("sim_long_view_tag_cnt_14d")
    attrs.add("sim_long_view_tag_cnt_30d")
    attrs.add("sim_long_view_tag_last_lag_30d")
    attrs.add("sim_negative_tag_cnt_1d")
    attrs.add("sim_negative_tag_cnt_3d")
    attrs.add("sim_negative_tag_cnt_7d")
    attrs.add("sim_negative_tag_cnt_14d")
    attrs.add("sim_negative_tag_cnt_30d")
    attrs.add("sim_negative_tag_last_lag_30d")
    attrs.add("sim_interact_author_cnt_1d")
    attrs.add("sim_interact_author_cnt_3d")
    attrs.add("sim_interact_author_cnt_7d")
    attrs.add("sim_interact_author_cnt_14d")
    attrs.add("sim_interact_author_cnt_30d")
    attrs.add("sim_interact_author_last_lag_30d")
    attrs.add("sim_short_view_author_cnt_1d")
    attrs.add("sim_short_view_author_cnt_3d")
    attrs.add("sim_short_view_author_cnt_7d")
    attrs.add("sim_short_view_author_cnt_14d")
    attrs.add("sim_short_view_author_cnt_30d")
    attrs.add("sim_short_view_author_last_lag_30d")
    attrs.add("sim_effective_view_author_cnt_1d")
    attrs.add("sim_effective_view_author_play_1d")
    attrs.add("sim_effective_view_author_cnt_3d")
    attrs.add("sim_effective_view_author_play_3d")
    attrs.add("sim_effective_view_author_cnt_7d")
    attrs.add("sim_effective_view_author_play_7d")
    attrs.add("sim_effective_view_author_cnt_14d")
    attrs.add("sim_effective_view_author_play_14d")
    attrs.add("sim_effective_view_author_cnt_30d")
    attrs.add("sim_effective_view_author_last_lag_30d")
    attrs.add("sim_effective_view_author_play_30d")
    attrs.add("sim_long_view_author_cnt_1d")
    attrs.add("sim_long_view_author_cnt_3d")
    attrs.add("sim_long_view_author_cnt_7d")
    attrs.add("sim_long_view_author_cnt_14d")
    attrs.add("sim_long_view_author_cnt_30d")
    attrs.add("sim_long_view_author_last_lag_30d")
    attrs.add("sim_negative_author_cnt_1d")
    attrs.add("sim_negative_author_cnt_3d")
    attrs.add("sim_negative_author_cnt_7d")
    attrs.add("sim_negative_author_cnt_14d")
    attrs.add("sim_negative_author_cnt_30d")
    attrs.add("sim_negative_author_last_lag_30d")
    attrs.add("sim_user_author_playcnt_14d")
    attrs.add("sim_user_author_playcnt_14d_v2")
    attrs.add("sim_user_author_playtime_14d")
    attrs.add("sim_user_author_playtime_7d")
    attrs.add("sim_user_author_playtime_7d_v2")
    attrs.add("sim_user_author_playcnt_7d")
    attrs.add("sim_user_author_effectcnt_14d")
    attrs.add("sim_user_author_effectcnt_7d")
    attrs.add("sim_user_author_likecnt_14d")
    attrs.add("sim_user_author_likecnt_7d")
    attrs.add("sim_user_author_commentcnt_14d")
    attrs.add("sim_user_author_commentcnt_7d")
    attrs.add("sim_user_author_playtime_list_7d")
    attrs.add("sim_user_author_duration_list_7d")
    attrs.add("sim_user_author_follow_playtime_list_7d")
    attrs.add("sim_user_author_follow_duration_list_7d")
    return attrs

class FollowPackLiveGuestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_pack_live_guest_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_path"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("wire_guest_list")
    attrs.add("item_type")
    attrs.add("author_id")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("live_stream_author_guest_info_str")
    attrs.add("exp_tag")
    return attrs

class FollowV5McHighvalueUABoostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_v5_mc_highvalue_ua_boost_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    attrs.add("photo_id")
    attrs.add("mc_final_score")
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("follow_v5_highvalue_author_boost_user_activity")
    attrs.add("userActivity")
    attrs.add("follow_v5_highvalue_author_boost_cursor")
    attrs.add("cursor_index")
    attrs.add("follow_v5_mc_highvalue_author_boost_author_num")
    attrs.add("follow_v5_mc_highvalue_author_boost_author_photo_topk")
    attrs.add("mc_highvalue_ua_low_activity_boost_value")
    attrs.add("mc_highvalue_ua_high_activity_boost_value")
    attrs.add("mc_highvalue_ua_high_activity_other_cursor_boost_value")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("mc_final_score")
    return attrs

class FollowSupportLiveAidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_support_live_aid_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("enable_random_support_live_tagging")
    attrs.add("enable_merchant_live_filter")
    attrs.add("enable_random_support_live_tagging_v0")
    attrs.add("enable_merchant_live_filter_v0")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    attrs.add("is_pro_live_author")
    attrs.add("is_pro_live_author_or_big_v")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add("is_support_live")
    attrs.add("is_random_support")
    return attrs

class FollowRankSlideFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_rank_slide_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_pb"))
    attrs.add("uClickListFollow")
    attrs.add("uClickListSlFollow")
    attrs.add("uClickListBlFollow")
    attrs.add("uClickListFollowLive")
    attrs.add("uClickListSlFollowLive")
    attrs.add("uLikeListFollow")
    attrs.add("uLikeListBlFollow")
    attrs.add("uLikeListSlFollow")
    attrs.add("uLikeListFollowLive")
    attrs.add("uLikeListSlFollowLive")
    attrs.add("uCommentPhotoListFollow")
    attrs.add("uCommentListFollowLive")
    attrs.add("uCommentPhotoListSlFollow")
    attrs.add("uForwardList")
    attrs.add("uForwardListFollowLive")
    attrs.add("uForwardListSlFollowLive")
    attrs.add("uHatePhotoAuthorListFollow")
    attrs.add("uHatePhotoAuthorListSlFollow")
    attrs.add("feasury_neg_author_action_strs")
    attrs.add("uid")
    attrs.add("rank_pxtrs_feature_str")
    attrs.add("slide_mix_l2r_rerank_show_times_weight")
    attrs.add("user_publish_cnt_map_ptr")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add("author_id")
    attrs.add("item_type")
    attrs.add("photo_id")
    attrs.add("reco_gender_first")
    attrs.add("follow_click")
    attrs.add("follow_comment")
    attrs.add("follow_real_show")
    attrs.add("follow_effective_view")
    attrs.add("follow_forward")
    attrs.add("follow_watch_live_time")
    attrs.add("follow_out_watch_live_time")
    attrs.add("follow_out_click")
    attrs.add("follow_out_effective_view")
    attrs.add("follow_like")
    attrs.add("follow_out_comment")
    attrs.add("follow_out_forward")
    attrs.add("follow_out_like")
    attrs.add("follow_out_view_time")
    attrs.add("follow_view_time")
    attrs.add("high_gift_author_type")
    attrs.add("is_realshow")
    attrs.add("timestamp")
    if not self._config.get("is_live"):
        attrs.add("photo_hetu_tag_info__hetu_level_one")
    else:
        attrs.add("live_room_enter_exit_count_str_list")
        attrs.add("follow_live_quality_result")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = {
        "aFollowSource",
        "pIsPictureType",
        "pUserAuthorPhotoEmpCmtr",
        "pUserAuthorPhotoEmpCmtr7d",
        "pUserAuthorPhotoEmpCtr",
        "pUserAuthorPhotoEmpCtr14d",
        "pUserAuthorPhotoEmpCtr1d",
        "pUserAuthorPhotoEmpCtr7d",
        "pUserAuthorPhotoEmpLtr",
        "pUserAuthorPhotoEmpLtr7d",
        "pUserAuthorPhotoEmpTPC",
        "pVideoDurationMs",
        "uAuthorAvgTimeFollowPhoto",
        "uAuthorEmpCmtrFollowPhoto",
        "uAuthorEmpCtrFollowPhoto",
        "uAuthorEmpFtrFollowPhoto",
        "uAuthorEmpLtrFollowPhoto",
        "uAuthorAvgTimeProfile",
        "uAuthorEmpCtrProfile",
        "u_author_recent_show_cnt",
        "u_author_recent_play_cnt",
        "u_author_last_show_lag_m",
        "u_author_last_show_lag_h",
        "u_author_last_view_lag_m",
        "u_author_last_view_lag_h",
        "uIsItemClicked",
        "lCurrentOnlineCount",
        "lMinuteEnterCount",
        "lMinuteExitCount",
        "lMinuteExitRate",
        "uShowTimeMilliSecond",
        "uFollowAuthorTimeDay",
        "uFollowAuthorTimeDayNew",
        "uIsPymkFollowReason",
        "pIsFavoriateAuthorItem",
        "uHateAuthor",
        "uAuthorEmpCtrFollowLive",
        "uAuthorAvgTimeFollowLive",
        "aPokeTimes",
        "uFollowAuthorPhotoAggrWatchTime",
        "uFollowAuthorPhotoAggrWatchTimePercent",
        "uFollowAuthorLiveAggrWatchTime",
        "uFollowAuthorLiveAggrWatchTimePercent",
        "live_realshow_count_1min",
        "live_realshow_count_5min",
        "live_realshow_count_10min",
        "live_watch_time_1min",
        "live_watch_time_5min",
        "live_watch_time_10min",
        "live_entry_count_1min",
        "live_entry_count_5min",
        "live_entry_count_10min",
        "is_live_on_sale",
        "live_entry_conn_count_1min",
        "live_entry_conn_count_5min",
        "live_entry_conn_count_10min",
        "live_exit_count_1min",
        "live_exit_count_5min",
        "live_exit_count_10min",
        "live_exit_conn_count_1min",
        "live_exit_conn_count_5min",
        "live_exit_conn_count_10min",
        "live_negative_1min",
        "live_negative_5min",
        "live_negative_10min",
        "live_like_count_1min",
        "live_like_count_5min",
        "live_like_count_10min",
        "live_comment_count_1min",
        "live_comment_count_5min",
        "live_comment_count_10min",
        "live_view_count_1min",
        "live_view_count_5min",
        "live_view_count_10min",
        "live_report_1min",
        "live_report_5min",
        "live_report_10min",
        "live_current_time_per_show",
        "live_max_time_per_show",
        "live_quality",
        "live_current_exit_rate",
        "live_min_exit_rate",
        "live_quality_exit_rate",
        "live_online_person_count",
        "live_shop_button_show_count_1min",
        "live_shop_button_show_count_5min",
        "live_shop_button_show_count_10min",
        "live_shop_button_click_count_1min",
        "live_shop_button_click_count_5min",
        "live_shop_button_click_count_10min",
        "should_punish_live",
        "live_follow_count_1min",
        "live_follow_count_5min",
        "live_follow_count_10min",
        "live_unfollow_count_1min",
        "live_unfollow_count_5min",
        "live_unfollow_count_10min",
        "live_forward_count_1min",
        "live_forward_count_5min",
        "live_forward_count_10min",
        "live_gift_count_1min",
        "live_gift_count_5min",
        "live_gift_count_10min",
        "live_item_detail_buy_count_1min",
        "live_item_detail_buy_count_5min",
        "live_item_detail_buy_count_10min",
        "live_current_time_per_person",
        "live_online_person_count_all",
        "live_max_time_per_person",
        "live_quality_time_per_person",
        "live_quality_like_count",
        "live_quality_view_count",
        "live_quality_comment_count",
        "live_quality_score_v1",
        "live_quality_score_v2",
        "live_quality_score_v3",
        "live_quality_score_v4",
        "has_shop",
        "is_voice_party",
        "has_red_pack",
        "is_author_chat",
        "is_chat",
        "is_pk",
        "is_shop_negative_live",
        "max_history_live_like_count_10min_ma",
        "max_history_live_like_count_40min_ma",
        "max_history_live_view_count_10min_ma",
        "max_history_live_view_count_40min_ma",
        "max_history_live_comment_count_10min_ma",
        "max_history_live_comment_count_40min_ma",
        "max_history_live_watch_time_per_person_10min_ma",
        "max_history_live_watch_time_per_person_20min_ma",
        "max_history_live_watch_time_per_person_30min_ma",
        "max_history_live_watch_time_per_person_40min_ma",
        "min_history_live_exit_rate_10min_ma",
        "min_history_live_exit_rate_20min_ma",
        "min_history_live_exit_rate_30min_ma",
        "min_history_live_exit_rate_40min_ma",
        "live_open_ten_minutes",
        "max_live_watch_time_per_person_ma",
        "min_live_exit_rate_ma",
        "max_time_per_show",
        "min_live_exit_rate",
        "max_live_like_count_ma",
        "max_live_view_count_ma",
        "max_live_comment_count_ma",
        "live_current_view_rate",
        "live_current_like_rate",
        "live_current_comment_rate",
        "live_max_view_rate",
        "live_max_like_rate",
        "live_max_comment_rate",
        "live_quality_view_rate",
        "live_quality_like_rate",
        "live_quality_comment_rate",
        "max_live_view_rate_ma",
        "max_live_like_rate_ma",
        "max_live_comment_rate_ma",
        "aGender",
        "uAuthorFollowTimeMs",
        "uAuthorFollowGapHour",
        "photo_click_times",
        "photo_show_times",
        "photo_last_show_time",
        "ua_photo_play_rate_30d",
        "hetu_l1_tags_double",
        "high_potential_ua_score",
        "hv_ua_score",
        "browsed_author_times_in_session",
        "is_negative_author",
        "aLiveCount",
        "aLiveCount7D",
        "aPhotoClickCount7D",
        "aPhotoCount",
        "aPhotoLikeCount7D",
        "aPhotoShowCount7D",
        "author_live_follow_realshow_recently_time",
        "author_live_follow_realshow_times",
        "follow_cmtr",
        "follow_ctr",
        "follow_effective_vtr",
        "follow_ftr",
        "follow_live_emp_tpc",
        "follow_live_tpc",
        "follow_ltr",
        "follow_out_cmtr",
        "follow_out_ftr",
        "follow_out_live_tpc",
        "follow_out_ltr",
        "follow_out_photo_tpc",
        "follow_photo_emp_cmtr",
        "follow_photo_emp_ctr",
        "follow_photo_emp_ftr",
        "follow_photo_emp_ltr",
        "follow_photo_tpc",
        "intimacy_daily_score",
        "live_revenue_1d",
        "live_revenue_3d",
        "live_revenue_7d",
        "live_revenue_14d",
        "live_revenue_30d",
        "live_revenue_60d",
        "merchant_high_loyalty_90d",
        "pIsBplusAuthor",
        "pIsLive",
        "pIsSelfItem",
        "pUserAuthorLiveAggrWatchtimePercent7d",
        "pUserAuthorLiveEmpCmtr",
        "pUserAuthorLiveEmpCtr7d",
        "pUserAuthorLiveEmpTPC",
        "pUserAuthorLiveEmpTpc7d",
        "prob_buy_rate",
        "revenue_score",
        "uAuthorEmpLtrProfile",
        "uGlobalUALiveGiftCnt28D",
        "uGlobalUALivePlayCnt28D",
        "uGlobalUALiveTime28D",
        "uGlobalUAPhotoCmtr28D",
        "uGlobalUAPhotoCollectCnt28D",
        "uGlobalUAPhotoCommentCnt28D",
        "uGlobalUAPhotoCompleteCnt28D",
        "uGlobalUAPhotoCpltr28D",
        "uGlobalUAPhotoEvtr28D",
        "uGlobalUAPhotoLikeCnt28D",
        "uGlobalUAPhotoLongViewCnt28D",
        "uGlobalUAPhotoLtr28D",
        "uGlobalUAPhotoLvtr28D",
        "uGlobalUAPhotoTime28D",
        "uGlobalUAPhotoValidCnt28D",
        "uGlobalUAPhotoWT28D",
        "hs_l_score",
        "hs_l_pctr",
        "hs_l_pltr",
        "hs_l_pftr",
        "hs_l_lvtr",
        "hs_l_psvr",
        "hs_l_pgtr",
        "hs_l_pwatch_time",
        "hs_l_gift_value",
        "hs_score",
        "hs_pctr",
        "hs_pltr",
        "hs_pftr",
        "hs_lvtr",
        "hs_psvr",
        "hs_pgtr",
        "hs_pwatch_time",
        "hs_gift_value",
        "show_times_score",
        "up_photo_status",
        "common_friend_count",
        "a_publish_cnt",
        "author_intimate_score",
        "p_upload_ms",
        "ua_weighted_intimate_score",
    }
    return attrs

class FollowMerchantSlideStrToPxtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_slide_str_to_pxtr_enricher"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_value_name"))
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_merchant_live_hit_cache")
        attrs.add("merchant_ctr")
        attrs.add("merchant_cvr")
        attrs.add("merchant_price")
        attrs.add("merchant_bonus")
        attrs.add("merchant_l2r")
        attrs.add("merchant_gctr")
        attrs.add("merchant_pflag")
        attrs.add("merchant_rpr")
        attrs.add("merchant_type")
        return attrs

class FollowMerchantSlidePxtrToStrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_merchant_slide_pxtr_to_str_enricher"
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("is_merchant_live_hit_cache")
        attrs.add("merchant_ctr")
        attrs.add("merchant_cvr")
        attrs.add("merchant_price")
        attrs.add("merchant_bonus")
        attrs.add("merchant_l2r")
        attrs.add("merchant_gctr")
        attrs.add("merchant_pflag")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_value_name"))
        return attrs

class FollowParseUserInfoSlideEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_pasre_user_info_slide_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_pb"))
        attrs.add("uBuyerEffectiveType")
        attrs.add("uUserClassLivePayingTypeNebula")
        attrs.add("uUserClassLivePayingTypeMainApp")
        return attrs
    
    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("llsid_str")
        attrs.add("cursor_index")
        attrs.add("userActivity")
        attrs.add("refresh_type")
        attrs.add("is_down_fresh")
        attrs.add("merchant_user_type")
        attrs.add("product_type")
        attrs.add("uIsBigUser")
        attrs.add("uIsImportantGiftUser")
        attrs.add("uIsGiftUser")
        attrs.add("ubuyEffectiveTypeValue")
        attrs.add("session_pre_items")
        return attrs


class FollowCalcEmbeddingScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_calc_embedding_score_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("show_pid_list")
        attrs.add("show_pid_emb_list")
        attrs.add("enable_use_min_max_norm")
        attrs.add("enable_use_circle_distance")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_embeddings")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("diversity_score")
        attrs.add("fr_diversity_score")
        return attrs
    
class FollowDurationDiversityEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_duration_diversity_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add("duration_and_timestamp_diversity_slide_window")
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("duration_ms_double")
        attrs.add("photo_age_minute")
        attrs.add("fr_pwt")
        attrs.add("fr_plvtr")
        attrs.add("fr_pfinish")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("pwt_diversity")
        attrs.add("plvtr_diversity")
        attrs.add("pfinish_diversity")
        attrs.add("wt_diversity")
        attrs.add("pwt_diversity_time")
        attrs.add("plvtr_diversity_time")
        attrs.add("pfinish_diversity_time")
        attrs.add("wt_diversity_time")
        return attrs
    
class FollowSocialCacheResultToPxtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_social_cache_result_to_pxtr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_pb_name"))
        attrs.add("social_data_mix_model_rank_cache_miss_ratio_limit")
        attrs.add("social_data_mix_model_rank_cache_author_miss_ratio_limit")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("author_id")
        attrs.add("photo_id")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("social_mix_score")
        attrs.add("mix_play")
        attrs.add("mix_like")
        attrs.add("mix_comment")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("skip_social_data_predict_model_attr"))
        return attrs

class FollowSocialResultToStrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_social_result_to_str_enricher"
    
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_pb_name"))
        attrs.add("enable_live_boost_in_mix_model")
        attrs.add("live_boost_coeff_in_mix_model")
        attrs.add("social_data_mix_model_rank_cache_num")
        attrs.add("social_data_mix_model_rank_cache_num_per_author")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("item_type")
        attrs.add("author_id")
        attrs.add("photo_id")
        attrs.add("social_mix_score")
        attrs.add("mix_play")
        attrs.add("mix_like")
        attrs.add("mix_comment")
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("cache_value_name"))
        return attrs

class FollowSocialEnsembleSortEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_social_ensemble_sort_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("user_info_pb"))
        attrs.add("live_ensemble_sort_weights")
        attrs.add("social_photo_ensemble_sort_weights")
        attrs.add("live_ensemble_sort_rank_score_weight")
        attrs.add("live_ensemble_sort_ctr_weight")
        attrs.add("live_ensemble_sort_ltr_weight")
        attrs.add("live_ensemble_sort_ftr_weight")
        attrs.add("live_ensemble_sort_cmtr_weight")
        attrs.add("live_ensemble_sort_dctr_weight")
        attrs.add("live_ensemble_sort_svr_weight")
        attrs.add("live_ensemble_sort_lvtr_weight")
        attrs.add("live_ensemble_sort_dtr_weight")
        attrs.add("live_ensemble_sort_wt_weight")
        attrs.add("live_ensemble_sort_evtr_weight")
        attrs.add("live_ensemble_sort_llvtr_weight")
        attrs.add("live_ensemble_sort_ptr_weight")
        attrs.add("live_ensemble_sort_vtr_weight")
        attrs.add("social_photo_ensemble_sort_rank_score_weight")
        attrs.add("social_photo_ensemble_sort_ctr_weight")
        attrs.add("social_photo_ensemble_sort_ltr_weight")
        attrs.add("social_photo_ensemble_sort_ftr_weight")
        attrs.add("social_photo_ensemble_sort_cmtr_weight")
        attrs.add("social_photo_ensemble_sort_dctr_weight")
        attrs.add("social_photo_ensemble_sort_svr_weight")
        attrs.add("social_photo_ensemble_sort_lvtr_weight")
        attrs.add("social_photo_ensemble_sort_dtr_weight")
        attrs.add("social_photo_ensemble_sort_wt_weight")
        attrs.add("social_photo_ensemble_sort_benefit_weight")
        attrs.add("social_photo_ensemble_sort_cmef_weight")
        attrs.add("social_photo_ensemble_sort_ptr_weight")
        attrs.add("social_photo_ensemble_sort_vtr_weight")
        attrs.add("social_photo_ensemble_sort_rer_mio_score_weight")
        attrs.add("social_photo_ensemble_sort_column_memcache_score_weight")
        attrs.add("social_photo_ensemble_sort_ua_score_weight")
        attrs.add("social_photo_ensemble_sort_author_out_score_weight")
        attrs.add("social_photo_ensemble_sort_author_relation_score_weight")
        attrs.add("social_photo_ensemble_sort_lsst_weight")
        attrs.add("social_photo_ensemble_sort_negative_rate_weight")
        attrs.add("social_photo_ensemble_sort_now_living_weight")
        attrs.add("social_photo_ensemble_sort_wtr_weight")
        attrs.add("social_photo_ensemble_sort_wtd_weight")
        attrs.add("social_photo_ensemble_sort_se_ctr_weight")
        attrs.add("social_photo_ensemble_sort_se_ltr_weight")
        attrs.add("social_photo_ensemble_sort_se_wtr_weight")
        attrs.add("social_photo_ensemble_sort_se_ftr_weight")
        attrs.add("social_photo_ensemble_sort_se_cmtr_weight")
        attrs.add("social_photo_ensemble_sort_se_lvtr_weight")
        attrs.add("social_photo_ensemble_sort_se_svr_weight")
        attrs.add("social_photo_ensemble_sort_se_ptr_weight")
        attrs.add("social_photo_ensemble_sort_se_vtr_weight")
        attrs.add("social_photo_ensemble_sort_mix_model_like_weight")
        attrs.add("social_photo_ensemble_sort_mix_model_play_weight")
        attrs.add("social_photo_ensemble_sort_mix_model_mix_weight")
        attrs.add("social_photo_ensemble_sort_mix_model_comment_weight")
        attrs.add("enable_live_boost_in_mix_model")
        attrs.add("live_boost_coeff_in_mix_model")
        attrs.add("live_ensemble_svtr_coeff")
        attrs.add("photo_ensemble_svtr_coeff")
        attrs.add("live_ensemble_svtr_power")
        attrs.add("photo_ensemble_svtr_power")
        attrs.add("friend_photo_es_score")
        attrs.add("frivate_photo_es_score")
        attrs.add("inner_slide_ensemble_negative_rate_denominator_smooth")
        attrs.add("live_ensemble_sort_smooth_arg")
        attrs.add("photo_ensemble_sort_smooth_arg")
        attrs.add("live_ensemble_sort_temperature")
        attrs.add("photo_ensemble_sort_temperature")
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add("photo_id")
        attrs.add("author_id")
        attrs.add("social_mix_score")
        attrs.add("fr_pctr")
        attrs.add("fr_pltr")
        attrs.add("fr_pftr")
        attrs.add("fr_pcmtr")
        attrs.add("fr_pdctr")
        attrs.add("fr_psvr")
        attrs.add("fr_plvtr")
        attrs.add("fr_pdtr")
        attrs.add("fr_pwt")
        attrs.add("fr_pptr")
        attrs.add("fr_pvtr")
        attrs.add("hs_score")
        attrs.add("pIsFavoriateAuthorItem")
        attrs.add("follow_negative")
        attrs.add("follow_out_negative")
        attrs.add("follow_real_show")
        attrs.add("is_living_photo")
        attrs.add("fr_pwtr")
        attrs.add("fr_pwtd")
        attrs.add("uIsItemClicked")
        attrs.add("is_global_play_photo")
        attrs.add("mix_like")
        attrs.add("mix_play")
        attrs.add("mix_comment")
        if self._config.get("is_live"):
            attrs.add("fr_pevtr")
            attrs.add("fr_pllvtr")
            attrs.add("fr_pctr_simple")
        else:
            attrs.add("fr_pbenefit")
            attrs.add("fr_pcmef")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add("fr_final_score")
        attrs.add("es_score")
        return attrs

class FollowItemExtraAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "follow_item_extra_attr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("is_perf_attr"))
        return attrs
    
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for attr in self._config.get("attrs"):
            if isinstance(attr, str):
                attrs.add(attr)
            elif isinstance(attr, dict):
                if ("name" in attr):
                    attrs.add(attr["name"])
                else:
                    raise ArgumentError(f"该 list 中存在不支持的配置类型没有name: {config}")
            else:
                raise ArgumentError(f"该 list 中存在不支持的配置类型 {type(attr)}: {config}")
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("save_attr_name"))
        return attrs
    