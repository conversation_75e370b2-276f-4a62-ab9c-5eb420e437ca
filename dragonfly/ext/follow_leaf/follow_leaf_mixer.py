#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_mixer.py
description: common_leaf dynamic_json_config DSL intelligent builder, mixer module
author: qian<PERSON><EMAIL>
date: 2022-12-28
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafMixer, try_add_table_name

class FollowTableUnionMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_union_table"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    append_attrs = set()
    for attr in self._config.get("if_not_copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      else:
        check_arg(False, "if_not_copy_attrs 的元素必须是 string")
    for attr in self._config.get("overwrite_copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      else:
        check_arg(False, "overwrite_copy_attrs 的元素必须是 string")

    attrs = try_add_table_name(self._config.get("from_table", ""), attrs)
    append_attrs = try_add_table_name(self._config.get("to_table", ""), append_attrs)
    attrs.update(append_attrs)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("if_not_copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      else:
        check_arg(False, "if_not_copy_attrs 的元素必须是 string")
    for attr in self._config.get("overwrite_copy_attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      else:
        check_arg(False, "overwrite_copy_attrs 的元素必须是 string")
    if "dup_mark_attr" in self._config:
      attrs.add(self._config.get("dup_mark_attr"))
    return try_add_table_name(self._config.get("to_table", ""), attrs)

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    tables.add(self._config.get("from_table", ""))
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("to_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables

class FollowBidInfoFilleMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "follow_bid_info_fill"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    live_attrs = {
      "author_id",
      "is_live_valid",
      "ad_roi_ratio_score",
      "ad_creative_id",
      "ad_trans_info",
      "ad_budget_put_status",
      "autoRoi",
      "budget_status",
      "is_store_wide",
    }
    photo_attrs = {
      "author_id",
      "ad_roi_ratio_score",
    }

    attrs = try_add_table_name(self._config.get("live_table", ""), live_attrs)
    attrs.update(try_add_table_name(self._config.get("photo_table", ""), photo_attrs))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = {
      "ad_roi_ratio_score",
      "ad_creative_id",
      "ad_trans_info",
      "put_status",
      "autoRoi",
      "budget_status",
      "is_live_valid"
      "is_store_wide",
    }
    return try_add_table_name(self._config.get("photo_table", ""), attrs)

  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("live_table", ""))
    tables.add(self._config.get("photo_table", ""))
    return tables

  @property
  @strict_types
  def output_item_tables(self) -> set:
    tables = set()
    tables.add(self._config.get("photo_table", ""))
    return tables

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    return self.output_item_tables
