#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class LocalLifePreProcessRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_pre_process_retriever"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hot_local_life_req_attr"))
    return attrs

class LocalLifePostProcessRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_post_process_retriever"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hot_local_life_resp_attr"))
    return attrs

class LocalLifeCascadeRankProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_cascade_rank_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class LocalLifeFullRankProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_full_rank_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class LocalLifeResponseProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_response_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class LocalLifeInitContextProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_init_context_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class LocalLifeRequestGateProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_request_gate_processor"

  @strict_types
  def is_async(self) -> bool:
    return False

class LocalLifeInitCascadingContextProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_init_cascading_context_processor"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

class LocalLifeInitRankingContextProcessor(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hot_local_life_init_ranking_context_processor"

  @strict_types
  def is_async(self) -> bool:
    return False
