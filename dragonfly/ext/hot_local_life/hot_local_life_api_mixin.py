#!/usr/bin/env python3
# coding=utf-8
"""
filename: hot_local_life_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, hot local life api mixin
author: <EMAIL>
date: 2022-01-04 16:07:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .hot_local_life_retriever import *

class HotLocalLifeApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 hot_local_life 相关的 Processor 接口

  本页所述 api 主要用于本地生活模块

  负责人：libingchen, wangziqi05, zhaojingjing
  """
  def hot_local_life_pre_process_retriever(self, **kwargs):
    """
    LocalLifePreProcessRetriever
    ------
    完成本地生活模块中不同策略共同的数据预处理工作，兼容单双列请求

    参数配置
    ------
    `hot_local_life_req_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::HotLocalLifeRequest 的 common_attr

    调用示例
    ------
    ``` python
    .hot_local_life_pre_process_retriever(
      hot_local_life_req_attr="hot_local_life_request"
    )
    ```
    """
    self._add_processor(LocalLifePreProcessRetriever(kwargs))
    return self

  def hot_local_life_post_process_retriever(self, **kwargs):
    """
    LocalLifePostProcessRetriever
    ------
    完成本地生活模块中不同策略共同的数据预处理工作，兼容单双列请求

    参数配置
    ------
    `hot_local_life_resp_attr`: [string] 必配. 存储序列化后的 kuaishou::reco::HotLocalLifeResponse 的 common_attr

    调用示例
    ------
    ``` python
    .hot_local_life_post_process_retriever(
      hot_local_life_resp_attr="hot_local_life_response"
    )
    ```
    """
    self._add_processor(LocalLifePostProcessRetriever(kwargs))
    return self

  def hot_local_life_cascade_rank_processor(self, **kwargs):
    """
    LocalLifeCascadeRankProcessor
    ------
    主站双列发现页本地粗排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_cascade_rank_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeCascadeRankProcessor(kwargs))
    return self

  def hot_local_life_full_rank_processor(self, **kwargs):
    """
    LocalLifeFullRankProcessor
    ------
    主站双列发现页本地精排逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_full_rank_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeFullRankProcessor(kwargs))
    return self

  def hot_local_life_response_processor(self, **kwargs):
    """
    LocalLifeResponseProcessor
    ------
    主站双列发现页本地接受回复逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_response_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeResponseProcessor(kwargs))
    return self

  def hot_local_life_init_context_processor(self, **kwargs):
    """
    LocalLifeInitContextProcessor
    ------
    主站双列发现页本地初始化上下文逻辑

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_init_context_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeInitContextProcessor(kwargs))
    return self

  def hot_local_life_request_gate_processor(self, **kwargs):
    """
    LocalLifeRequestGateProcessor
    ------
    主站双列发现页本地生活是否出本地生活结果

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .local_life_request_gate_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeRequestGateProcessor(kwargs))
    return self

  def hot_local_life_init_cascading_context_processor(self, **kwargs):
    """
    LocalLifeInitCascadingContextProcessor
    ------
    主站双列发现页本地生活初始化粗排相关参数

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_init_cascading_context_processor(
      input_item_attrs = [
        "plc_type_attr",
        "plc_text_attr",
        "poi_id_attr",
        "poi_city_attr",
        "app_ids_attr",
        "product_price_attr",
        "is_promotion_attr",
      ],
      **ab_parameters
    )
    ```
    """
    self._add_processor(LocalLifeInitCascadingContextProcessor(kwargs))
    return self

  def hot_local_life_init_ranking_context_processor(self, **kwargs):
    """
    LocalLifeInitRankingContextProcessor
    ------
    主站双列发现页本地生活初始化精排相关参数

    参数配置
    ------
    `kwargs`: [dict],ab参数

    调用示例
    ------
    ```python
    .hot_local_life_init_ranking_context_processor(**ab_parameters)
    ```
    """
    self._add_processor(LocalLifeInitRankingContextProcessor(kwargs))
    return self
