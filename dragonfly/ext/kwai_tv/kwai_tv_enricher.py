#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwai_tv_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for kap
author: <EMAIL>
date: 2020-08-18 00:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class KwaiTvColossusPlayEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_long_term_play_list"

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("rodis_kess"), str) and self._config["rodis_kess"], "rodis_kess 需为非空字符串")
    check_arg(isinstance(self._config.get("rodis_domain"), str) and self._config["rodis_domain"], "rodis_domain 需为非空字符串")
    check_arg(isinstance(self._config.get("rodis_payload_id"), int) and self._config["rodis_payload_id"] > 0, "rodis_payload_id 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("device_id"), str) and self._config["device_id"], "device_id 需为非空字符串")
    check_arg(isinstance(self._config.get("action_list_attr"), str) and self._config["action_list_attr"], "action_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("author_list_attr"), str) and self._config["author_list_attr"], "author_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("tag_list_attr"), str) and self._config["tag_list_attr"], "tag_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("duration_list_attr"), str) and self._config["duration_list_attr"], "duration_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("play_time_list_attr"), str) and self._config["play_time_list_attr"], "play_time_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("timestamp_list_attr"), str) and self._config["timestamp_list_attr"], "timestamp_list_attr 需为非空字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("device_id", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    if self._config.get("max_timestamp_ms", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("max_timestamp_ms")))
    if self._config.get("deduplicate_item", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("deduplicate_item")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("action_list_attr", ""):
      attrs.add(self._config.get("action_list_attr", ""))
    if self._config.get("author_list_attr", ""):
      attrs.add(self._config.get("author_list_attr", ""))
    if self._config.get("tag_list_attr", ""):
      attrs.add(self._config.get("tag_list_attr", ""))
    if self._config.get("duration_list_attr", ""):
      attrs.add(self._config.get("duration_list_attr", ""))
    if self._config.get("play_time_list_attr", ""):
      attrs.add(self._config.get("play_time_list_attr", ""))
    if self._config.get("timestamp_list_attr", ""):
      attrs.add(self._config.get("timestamp_list_attr", ""))
    return attrs

class KwaiTvColossusPlayKiwiEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_long_term_play_list_kiwi"

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kiwi_cluster"), str) and self._config["kiwi_cluster"], "kiwi_cluster 需为非空字符串")
    check_arg(isinstance(self._config.get("device_id"), str) and self._config["device_id"], "device_id 需为非空字符串")
    check_arg(isinstance(self._config.get("action_list_attr"), str) and self._config["action_list_attr"], "action_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("author_list_attr"), str) and self._config["author_list_attr"], "author_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("tag_list_attr"), str) and self._config["tag_list_attr"], "tag_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("duration_list_attr"), str) and self._config["duration_list_attr"], "duration_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("play_time_list_attr"), str) and self._config["play_time_list_attr"], "play_time_list_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("timestamp_list_attr"), str) and self._config["timestamp_list_attr"], "timestamp_list_attr 需为非空字符串")

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("device_id", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    if self._config.get("max_timestamp_ms", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("max_timestamp_ms")))
    if self._config.get("deduplicate_item", ""):
      attrs.update(self.extract_dynamic_params(self._config.get("deduplicate_item")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("action_list_attr", ""):
      attrs.add(self._config.get("action_list_attr", ""))
    if self._config.get("author_list_attr", ""):
      attrs.add(self._config.get("author_list_attr", ""))
    if self._config.get("tag_list_attr", ""):
      attrs.add(self._config.get("tag_list_attr", ""))
    if self._config.get("duration_list_attr", ""):
      attrs.add(self._config.get("duration_list_attr", ""))
    if self._config.get("play_time_list_attr", ""):
      attrs.add(self._config.get("play_time_list_attr", ""))
    if self._config.get("timestamp_list_attr", ""):
      attrs.add(self._config.get("timestamp_list_attr", ""))
    return attrs

class OttScoreRemapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_calc_remap_sroce"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["duration_attr"])
    con_attrs_ = self._config["convert_item_attrs"]
    if (len(con_attrs_) > 0):
      [ret.add(x) for x in con_attrs_]
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attrs_ = self._config["converted_attrs"]
    if (self._config["save_converted_score"] and len(attrs_) > 0):
      [ret.add(x) for x in attrs_]
    if self._config.get("merge_score_attr", ""):
      ret.add(self._config["merge_score_attr"])
    return ret


class OttGsuWithIndexEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_gsu_with_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_pid_attr", "author_id_attr", "tag_attr", "play_time_attr", "duration_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["topk_indices_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret


class OttColossusExtractFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_colossus_extract_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["colossus_pid_attr", "play_time_attr", "duration_attr", "timestamp_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OttGsuWithMatrixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_gsu_with_matrix"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "colossus_pid_attr", "author_id_attr",
                "tag_attr", "play_time_attr", "duration_attr", "timestamp_attr", "continue_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_sign_attr", "output_slot_attr", "output_item_colossus_pid_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OttCopyItemSlotSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_copy_item_slot_sign"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_slot_attr"))
    ret.add(self._config.get("in_sign_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("out_slot_attr"))
    ret.add(self._config.get("out_sign_attr"))
    return ret

class OttSampleWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_calc_sample_weight"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["duration_attr", "weight_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_weight_attr", "output_avg_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class OttItemDualAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_item_dual_attr_enrich"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("duration_attr"))
    ret.add(self._config.get("item_dual_did_attr"))
    ret.add(self._config.get("item_dual_play_time_attr"))
    ret.add(self._config.get("item_dual_timestamp_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_dual_len_attr"))
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class OttSystemInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ott_copy_system_info"

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "windows_in_sec" in self._config:
      attrs.update(self.extract_dynamic_params(self._config["windows_in_sec"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    [
      attrs.update(self.extract_dynamic_params(self._config[out_attr]))
      for out_attr in ["save_cpu_busy_to_attr", "save_load_to_attr"] if out_attr in self._config
    ]
        
    return attrs
