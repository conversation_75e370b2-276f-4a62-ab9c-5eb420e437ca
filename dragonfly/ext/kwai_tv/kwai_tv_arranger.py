#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwai_tv_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module for ott
author: <EMAIL>
date: 2023-08-14 00:00:00
"""

from ...common_leaf_util import (
    strict_types,
    check_arg,
    gen_attr_name_with_common_attr_channel,
)
from ...common_leaf_processor import LeafArranger


class OttSsdVariantArranger(LeafArranger):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "ott_ssd_variant"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        for key in [
            "limit",
            "window_size",
            "local_size",
            "cross_screen_variant",
        ]:
            if key in self._config:
                ret.update(self.extract_dynamic_params(self._config[key]))
        if "cross_screen_items_from_attr" in self._config:
            ret.add(self._config["cross_screen_items_from_attr"])
        return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        ret = set()
        for key in ["item_embedding_attr"]:
            if key in self._config:
                ret.add(self._config[key])
        if (
            "cross_screen_variant" in self._config
            and "cross_screen_item_embedding_attr" in self._config
            and "cross_screen_items_from_attr" in self._config
        ):
            ret.add(
                gen_attr_name_with_common_attr_channel(
                    self._config["cross_screen_item_embedding_attr"],
                    self._config["cross_screen_items_from_attr"],
                )
            )
        return ret
