#!/usr/bin/env python3
# coding=utf-8
"""
filename: kwai_tv_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, api mixin
author: <EMAIL>
date: 2020-08-18 00:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kwai_tv_enricher import *
from .kwai_tv_arranger import *

class KwaiTvApiMixin(CommonLeafBaseMixin):
  """
  背景：为 快手 TV 推荐场景提供特征抽取，召回，排序等业务所需 dragon 插件

  现有维护人：lianxin03, liyishen, chenchen03
  """

  def ott_long_term_play_list(self, **kwargs):
    """
    KwaiTvColossusPlayEnricher
    ------

    获取用户播放序列，返回内容：
    - action_list_attr
      播放时间长于 1s 的主站 photo_id，按时间顺序从近到远排列
    - author_list_attr
      作者 id，取不到则为 0
    - tag_list_attr
      河图类目，优先获取第一个 level2，否则取第一个 level1，否则为 0
    - duration_list_attr
      视频长度，单位：秒，最大 1800s (30min)，取不到则为 0
    - play_time_list_attr
      视频播放时长，单位：秒，最大 1800s (30min)，取不到则为 0
    - timestamp_list_attr
      行为发生时间戳，单位：秒，取不到则为 0

    参数配置
    ------
    `rodis_kess`: [string] 必填项，数据源 rodis kess

    `rodis_domain`: [string] 必填项，数据源 rodis domain

    `rodis_payload_id`: [int] 必填项，数据源 rodis payload id

    `rodis_timeout_ms`: [int] 选填项，请求超时, 默认 1000

    `device_id`: [string] [动态参数] 必填项，设备 ID

    `action_list_attr`: [string] 必填项，主站 id 列表对应名称

    `author_list_attr`: [string] 必填项，作者 id 列表对应名称

    `tag_list_attr`: [string] 必填项，河图标签列表对应名称

    `duration_list_attr`: [string] 必填项，视频时长列表对应名称

    `play_time_list_attr`: [string] 必填项，视频播放时长列表对应名称

    `timestamp_list_attr`: [string] 必填项，行为发生时间戳列表对应名称

    `max_timestamp_ms` : [string] [动态参数] 选填项，根据行为发生时间过滤

    `max_play_size` : [int] 选填项，默认 10000

    `deduplicate_item` : [int] [动态参数] 选填项，取值 0 或 1，默认 0, 是否对 item_id 去重，保留最大 play_time 项

    调用示例
    ------
    ``` python
    .ott_long_term_play_list(
      rodis_kess = "grpc_rodisRecoNewProduct",
      rodis_domain = "RECO_BP_COMMON",
      rodis_payload_id = 36,
      device_id = "{{device_id}}",
      action_list_attr = "ott_action_list",
      author_list_attr = "ott_author_list",
      tag_list_attr = "ott_tag_list",
      duration_list_attr = "ott_duration_list",
      play_time_list_attr = "ott_play_time_list",
      timestamp_list_attr = "ott_timestamp_list",
      max_play_size = 10000,
      max_timestamp_ms = "{{max_timestamp_ms}}",
      deduplicate_item = 0,
    )
    ```
    """
    self._add_processor(KwaiTvColossusPlayEnricher(kwargs))
    return self
  
  def ott_long_term_play_list_kiwi(self, **kwargs):
    """
    KwaiTvColossusPlayEnricherKiwi
    ------

    从kiwi中获取用户播放序列
    - action_list_attr
      播放时间长于 1s 的主站 photo_id，按时间顺序从近到远排列
    - author_list_attr
      作者 id，取不到则为 0
    - tag_list_attr
      河图类目，优先获取第一个 level2，否则取第一个 level1，否则为 0
    - duration_list_attr
      视频长度，单位：秒，最大 1800s (30min)，取不到则为 0
    - play_time_list_attr
      视频播放时长，单位：秒，最大 1800s (30min)，取不到则为 0
    - timestamp_list_attr
      行为发生时间戳，单位：秒，取不到则为 0

    参数配置
    ------
    `kiwi_cluster`: [string] 必填项，数据源 kiwi 集群名
  
    `field`: [string] 选填项，kiwi 中colossus对应field名称

    `timeout_ms`: [int] 选填项，请求超时, 默认 1000

    `io_thread`: [int] 选填项，kiwi线程配置, 默认 2

    `device_id`: [string] [动态参数] 必填项，设备 ID

    `action_list_attr`: [string] 必填项，主站 id 列表对应名称

    `author_list_attr`: [string] 必填项，作者 id 列表对应名称

    `tag_list_attr`: [string] 必填项，河图标签列表对应名称

    `duration_list_attr`: [string] 必填项，视频时长列表对应名称

    `play_time_list_attr`: [string] 必填项，视频播放时长列表对应名称

    `timestamp_list_attr`: [string] 必填项，行为发生时间戳列表对应名称

    `max_timestamp_ms` : [string] [动态参数] 选填项，根据行为发生时间过滤

    `max_play_size` : [int] 选填项，默认 10000

    `deduplicate_item` : [int] [动态参数] 选填项，取值 0 或 1，默认 0, 是否对 item_id 去重，保留最大 play_time 项

    调用示例
    ------
    ``` python
    .ott_long_term_play_list_kiwi(
      kiwi_cluster = "ottUserDataKiwi",
      field = "COLOSSUS_PLAY_LIST_FIELD",
      device_id = "{{device_id}}",
      action_list_attr = "ott_action_list",
      author_list_attr = "ott_author_list",
      tag_list_attr = "ott_tag_list",
      duration_list_attr = "ott_duration_list",
      play_time_list_attr = "ott_play_time_list",
      timestamp_list_attr = "ott_timestamp_list",
      max_play_size = 10000,
      max_timestamp_ms = "{{max_timestamp_ms}}",
      deduplicate_item = 0,
    )
    ```
    """
    self._add_processor(KwaiTvColossusPlayKiwiEnricher(kwargs))
    return self

  def ott_calc_remap_sroce(self, **kwargs):
    """
    OttScoreRemapEnricher
    ------
    采样计算xtr全局分位数
    参数配置
    ------
    调用示例
    ------
    ``` python
    .ott_calc_remap_sroce(
        duration_attr="duration_attr",
        random_num_attr=1.0,
        convert_item_attrs=["ctr"],
        save_converted_score=True,
        converted_attrs=["glocdf_ctr"],
        save_ensemble_score=True,
        temperature_attr=1.0,
        merge_weightes=[1.0],
        merge_score_attr="glocdf_merge_score",
    )
    ```
    """
    self._add_processor(OttScoreRemapEnricher(kwargs))
    return self


  def ott_gsu_with_index(self, **kwargs):
    """
    OttGsuWithIndexEnricher
    ------
    根据 item_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots
  
    示例
    ------
    ``` python
    .ott_gsu_with_index(
        topk_indices_attr="topk_indices_attr",
        colossus_pid_attr="colossus_pid_attr",
        author_id_attr="author_id_attr",
        tag_attr="tag_attr",
        play_time_attr="play_time_attr",
        duration_attr="duration_attr",
        timestamp_attr="timestamp_attr",
        output_sign_attr="output_sign_attr",
        output_slot_attr="output_slot_attr",
        output_item_colossus_pid_attr="output_pid_attr",
        top_n=50
    )
    ```
    """
    self._add_processor(OttGsuWithIndexEnricher(kwargs))
    return self


  def ott_colossus_extract_feature(self, **kwargs):
    """
    OttColossusExtractFeatureEnricher
    ------
    从 `colossus_pid_attr` 计算全部 sign, 返回 output_sign 和 output_slots
  
    示例
    ------
    ``` python
    .ott_colossus_extract_feature(
        colossus_pid_attr="colossus_pid_attr",
        play_time_attr="play_time_attr",
        duration_attr="duration_attr",
        timestamp_attr="timestamp_attr",
        output_sign_attr="output_sign_attr",
        output_slot_attr="output_slot_attr",
    )
    ```
    """
    self._add_processor(OttColossusExtractFeatureEnricher(kwargs))
    return self


  def ott_gsu_with_matrix(self, **kwargs):
    """
    OttGsuWithMatrixEnricher
    ------
    根据 common_attr 中的 `sorted_item_idx_attr`, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots
  
    示例
    ------
    ``` python
    .ott_gsu_with_matrix(
        sorted_item_idx_attr="sorted_item_index",
        colossus_pid_attr="colossus_pid_attr",
        author_id_attr="author_id_attr",
        tag_attr="tag_attr",
        play_time_attr="play_time_attr",
        duration_attr="duration_attr",
        timestamp_attr="timestamp_attr",
        output_sign_attr="output_sign_attr",
        output_slot_attr="output_slot_attr",
        output_item_colossus_pid_attr="output_pid_attr",
        top_n=50
    )
    ```
    """
    self._add_processor(OttGsuWithMatrixEnricher(kwargs))
    return self


  def ott_copy_item_slot_sign(self, **kwargs):
    """
    OttCopyItemSlotSignEnricher
    ------
    拷贝已有的item slot/sign, 构成新的mio特征

    调用示例
    ------
    ``` python
    .ott_copy_item_slot_sign(
      src_mio_slot = [100],
      dst_mio_slot = [200],
      in_slot_attr = "item_slot",
      in_sign_attr = "item_sign",
      out_slot_attr = "copy_item_slot",
      out_sign_attr = "copy_item_sign"
    )
    ```
    """
    self._add_processor(OttCopyItemSlotSignEnricher(kwargs))
    return self

  def ott_calc_sample_weight(self, **kwargs):
    """
    OttSampleWeightEnricher
    ------
    计算时长分桶平均值
    参数配置
    ------
    调用示例
    ------
    ``` python
    .ott_calc_sample_weight(
        duration_attr="duration_attr",
        weight_attr="weight_attr",
        output_weight_attr="output_weight_attr",
        output_avg_attr="output_avg_attr",
    )
    ```
    """
    self._add_processor(OttSampleWeightEnricher(kwargs))
    return self

  def ott_item_dual_attr_enrich(self, **kwargs):
    """
    OttItemDualAttrEnricher
    ------
    调用示例
    ------
    ``` python
    .ott_item_dual_attr_enrich(
      duration_attr = "duration_attr",
      item_dual_did_attr = "item_dual_did_attr",
      item_dual_play_time_attr = "item_dual_play_time_attr",
      item_dual_timestamp_attr = "item_dual_timestamp_attr",
      item_dual_len_attr = "item_dual_len_attr",
      output_slot_attr = "output_slots",
      output_sign_attr = "output_signs",
    )
    ```
    """
    self._add_processor(OttItemDualAttrEnricher(kwargs))
    return self

  def ott_ssd_variant(self, **kwargs):
    """
    OttSsdVariantArranger
    ------
    ssd 算法打散, 支持跨屏打散. 使用前请校验 embedding 的覆盖率以及返回值, 防止出现大量空或为0向量的情况.

    参数配置
    ------

    `limit`: [int] 动态参数,需要返回的计算个数

    `window_size`: [int] 动态参数, 跨度
    
    `local_size`: [int] 动态参数, 局部 ssd

    `item_embedding_attr`: [string] 候选集 item embedding

    `cross_screen_variant`: [bool] 动态参数,是否开启跨屏打散, 默认为 false

    `cross_screen_items_from_attr`: [string] 跨屏打散时需要输入的 跨屏 item 集合, common attr

    `cross_screen_item_embedding_attr`: [string] 跨屏 item 集合 对应的 item embedding attr

    调用示例
    ------
    ``` python
    .ott_ssd_variant(
      limit=10,
      window_size=5,
      item_embedding_attr="mmu_hetu_embedding",
    )
    ```
    """

    self._add_processor(OttSsdVariantArranger(kwargs))
    return self

  def ott_copy_system_info(self, **kwargs):
    """
    OttSystemInfoEnricher
    ------
    获取当前系统的运行信息，并填充到指定的 CommonAttr 中

    参数配置
    ------
    `windows_in_sec`: [int_list] [动态参数] 指定希望查询的时间窗口，合法范围由 system_monitor_stat_windows(GFLAG) 定义，启动期间。返回值进行多窗口加权平均: 多窗口统计值 = SUM(window_i / (i + 1)) / SUM(1 / (i + i))。

    - GFLAG(--system_monitor_stat_windows=5,10,30,60,300,900): 指定要统计的时间窗口（以秒为单位）。默认统计过去5秒、10秒、30秒、1分钟、5分钟、15分钟的数据。**注: &emsp;对于1分钟、5分钟和15分钟的窗口的load average数据, 将直接使用更准确的系统值；对于其他非标准时间窗口，统计值将基于动态窗口进行累计。**
    - 如果系统的运行时间不足以支持指定的窗口大小（例如，系统运行了23秒，不足30秒），则返回值将基于实际运行时间的累计平均值。如果没有可用的窗口（例如，程序刚启动，还没有累积任何统计数据），则返回值为0.0，表示目前没有足够的数据来提供有意义的统计信息。

    `save_cpu_busy_to_attr`: [double] [动态参数] 将当前 CPU Busy Rate 另存到指定的 common_attr 中，取值范围[0.0, 1.0]，缺省则不存

    `save_load_to_attr`: [double] [动态参数] 将当前 Load Avg Rate (load_avg / core_num) 另存到指定的 common_attr 中，取值范围[0.0, 1.0]，缺省则不存

    调用示例
    ------
    ``` python
    .ott_copy_system_info(
    	save_cpu_busy_to_attr="current_cpu_busy",
    	save_load_to_attr="{{current_load_attr_name}}",
    )
    ```
    ``` python
    .ott_copy_system_info(
    	windows_in_sec=[60, 300, 900],
    	save_cpu_busy_to_attr="current_cpu_busy",
    	save_load_to_attr="{{current_load_attr_name}}",
    )
    ```
    """
    self._add_processor(OttSystemInfoEnricher(kwargs))
    return self
