#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
filename: qiliang_enricher.py
date: 2023/09/27 11:32:41
author: wang<PERSON>an24 
description: 
'''


from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class CalcLocationToLatLanAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_location_adcode_attr"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in [ "common_country_attr", "common_province_attr", "common_city_attr", "common_county_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_common_adcode_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret