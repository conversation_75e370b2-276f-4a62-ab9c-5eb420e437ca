#!/usr/bin/env python
# -*- coding: utf-8 -*-

'''
filename: qiliang_api_mixin.py
date: 2023/09/27 11:30:12
author: wangjian24 
description:   
'''

from typing import Optional, Union
from itertools import chain
from collections import OrderedDict

from ...common_leaf_util import ArgumentError, extract_attrs_from_expr, check_parenthesis_closed, check_arg
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .qiliang_retriever import *
from .qiliang_enricher import *
from .qiliang_arranger import *
from .qiliang_observer import *
from .qiliang_mixer import *

class QiliangApiMixin(CommonLeafBaseMixin):
  """
  - 模块介绍：该 Mixin 包含 QiLiang 内粉相关的 Processor 接口
  - 使用场景：新内粉推荐系统“启量”Leaf
  - 维护人：李永昌、谢文涛、王健
  - 维护团队：内容增长算法部/画风生态组/启量推荐小组
  """
  def slide_merge_order_key_by_query_index(self, **kwargs):
    """
    SlideMergeOrderKeyByQueryIndexArranger
    ------
    适用于这种情况，比如根据pid批量请求倒排获取order_key，但是默认order_key会以独立item存在，无法直接和原始的item_key合并。新增这个merge处理，根据查询倒排结果填充的query_index，按照顺序合并order_key并填充到指定的item_attr

    参数配置
    ------
    `query_index_attr_name`: [string] 必填项，查询倒排时候对应在query中的顺序

    `merge_order_key_attr_name`: [string] 必填项，合并的结果存入的item_attr字段名
    """
    self._add_processor(SlideMergeOrderKeyByQueryIndexArranger(kwargs))
    return self

  def calc_adcode_attr(self, **kwargs):
    """
    calc_adcode_attr
    -------------
    地址到adcode的转换，返回adcode的common attr, 类型int_list

    参数配置
    -------------
    `common_country_attr`: [string] 必填项，地址国家名

    `common_province_attr`: [string] 必填项，地址省名

    `common_city_attr`: [string] 必填项，地址城市名

    `common_county_attr`: [string] 必填项，地址区县名

    `output_common_adcode_attr`: [string] 必填项，输出adcode列表变量名
    """
    self._add_processor(CalcLocationToLatLanAttrEnricher(kwargs))
    return self