#!/usr/bin/env python
# -*- coding: utf-8 -*-

'''
filename: qiliang_api_mixin.py
date: 2023/09/27 11:30:12
author: wangjian24 
description:   
'''

from typing import Optional, Union
from itertools import chain
from collections import OrderedDict

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>

from ...common_leaf_util import ArgumentError, extract_attrs_from_expr, check_parenthesis_closed, check_arg
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .qiliang_retriever import *
from .qiliang_enricher import *
from .qiliang_arranger import *
from .qiliang_observer import *
from .qiliang_mixer import *

class SlideMergeOrderKeyByQueryIndexArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "slide_merge_order_key_by_query_index"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("query_index_attr_name"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["merge_order_key_attr_name"])
    return attrs