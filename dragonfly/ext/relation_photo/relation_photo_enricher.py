import base64
import inspect

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .util import *

class RelationBaseFeatureAttr<PERSON>n<PERSON>er(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_base_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cut_length")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_base_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")

class RelationCounterFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_counter_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cut_length")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_counter_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")

class RelationInfoFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_info_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cut_length")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_info_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")

class RelationU2AFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_u2a_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_u2a_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("redis_cluster_name"), str) and self._config["redis_cluster_name"], "redis_cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")

class RelationPhotoDistributedIndexItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_item_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_item_attr_list())

class RelationUARelFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_ua_rel_feature"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_ua_rel_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("redis_cluster_name"), str) and self._config["redis_cluster_name"], "redis_cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")

class RelationUACrossFeatureAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_ua_cross_feature"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(get_relation_ua_cross_common_attr_list())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_ua_cross_item_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("redis_cluster_name"), str) and self._config["redis_cluster_name"], "redis_cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")


class RelationInfoFeatureV2AttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_relation_info_feature_v2"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("cut_length")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(get_relation_info_v2_attr_list())

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"], "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0, "timeout_ms 需要大于0")
