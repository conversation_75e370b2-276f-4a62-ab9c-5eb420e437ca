#!/usr/bin/env python3
# coding=utf-8
"""
filename: relation_photo_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder,relation_photo api mixin
author: <EMAIL>
date: 2021-05-11 18:50:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .relation_photo_enricher import *
from .relation_photo_retriever import *

class RelationPhotoApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 relation_photo 相关的 Processor 接口
  - RelationBaseFeatureAttrEnricher
  - RelationCounterFeatureAttrEnricher
  - RelationInfoFeatureAttrEnricher
  - RelationPhotoDistributedIndexItemAttrEnricher
  - AuthorPhotoQueryRemoteRetriever
  """

  def get_relation_base_feature(self, **kwargs):
    """
    RelationBaseFeatureAttrEnricher
    ------
    通过 kess_service 设定的关系中台服务取得用户与作者侧的基本特征并设为 common 和 item attr

    参数配置
    ------
    `kess_service`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `cut_length`: [int] [动态参数] 请求作者截断数，默认值为 50

    调用示例
    ------
    ``` python
    .get_relation_base_feature(
      kess_service = "grpc_ccRelationFeedService",
      timeout_ms = 100,
      cut_length = 50
    )
    ```
    """
    self._add_processor(RelationBaseFeatureAttrEnricher(kwargs))
    return self

  def get_relation_counter_feature(self, **kwargs):
    """
    RelationCounterFeatureAttrEnricher
    ------
    通过 kess_service 设定的关系中台服务取得行为统计特征并设为 item attr

    参数配置
    ------
    `kess_service`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `cut_length`: [int] [动态参数] 请求作者截断数，默认值为 50

    调用示例
    ------
    ``` python
    .get_relation_counter_feature(
      kess_service = "grpc_ccRelationFeedService",
      timeout_ms = 100,
      cut_length = 50
    )
    ```
    """
    self._add_processor(RelationCounterFeatureAttrEnricher(kwargs))
    return self

  def get_relation_info_feature(self, **kwargs):
    """
    RelationInfoFeatureAttrEnricher
    ------
    通过 kess_service 设定的关系中台服务取得关系特征并设为 item attr

    参数配置
    ------
    `kess_service`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `cut_length`: [int] [动态参数] 请求作者截断数，默认值为 50

    调用示例
    ------
    ``` python
    .get_relation_info_feature(
      kess_service = "grpc_ccRelationFeedService",
      timeout_ms = 100,
      cut_length = 50
    )
    ```
    """
    self._add_processor(RelationInfoFeatureAttrEnricher(kwargs))
    return self

  def get_relation_u2a_feature(self, **kwargs):
    """
    RelationU2AFeatureAttrEnricher
    ------
    通过 redis_cluster_name 设定的redis服务取得U2A关系特征并设为 item attr

    参数配置
    ------
    `redis_cluster_name`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 10

    调用示例
    ------
    ``` python
    .get_relation_u2a_feature(
      redis_cluster_name = "redis_recoRelationUserEmbedding",
      timeout_ms = 10
    )
    ```
    """
    self._add_processor(RelationU2AFeatureAttrEnricher(kwargs))
    return self

  def get_relation_ua_rel_feature(self, **kwargs):
    """
    RelationUARelFeatureAttrEnricher
    ------
    通过 redis_cluster_name 设定的redis服务取得UA共同关系特征并设为 item attr

    参数配置
    ------
    `redis_cluster_name`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 10

    调用示例
    ------
    ``` python
    .get_relation_ua_rel_feature(
      redis_cluster_name = "redis_recoRelationUserEmbedding",
      timeout_ms = 10
    )
    ```
    """
    self._add_processor(RelationUARelFeatureAttrEnricher(kwargs))
    return self

  def get_relation_ua_cross_feature(self, **kwargs):
    """
    RelationUACrossFeatureAttrEnricher
    ------
    通过 redis_cluster_name 设定的redis服务取得统计的交叉特征，包含user侧和item侧

    参数配置
    ------
    `redis_cluster_name`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 10

    调用示例
    ------
    ``` python
    .get_relation_ua_cross_feature(
      redis_cluster_name = "redis_recoRelationUserEmbedding",
      timeout_ms = 10
    )
    ```
    """
    self._add_processor(RelationUACrossFeatureAttrEnricher(kwargs))
    return self

  def get_relation_item_feature(self, **kwargs):
    """
    RelationPhotoDistributedIndexItemAttrEnricher
    ------
    通过倒排索引取得 item 侧的相关特征

    参数配置
    ------

    `additional_item_source`: [dict] 增加额外的 common attr 对应的 item 列表

    调用示例
    ------
    ``` python
    .get_relation_item_feature(
      additional_item_source = {
        "common_attr": ["browseAuthorPhotos"]
      }
    )
    ```
    """
    self._add_processor(RelationPhotoDistributedIndexItemAttrEnricher(kwargs))
    return self

  def retrieve_by_author_remote_query_index(self, **kwargs):
    """
    AuthorPhotoQueryRemoteRetriever
    ------
    根据 common attr 里的作者列表从远程倒排索引服务中取得对应 photo 列表

    参数配置
    ------
    `kess_service`: [string] author 倒排索引服务的 kess 服务名

    `service_group`: [string] 倒排索引服务的 kess 服务组，默认值为 "PRODUCTION"

    `service_shard`: [string] shard 分组名

    `shard_num`: [int] 服务的 shard 个数，默认为1

    `shard_predix`: [string] shard_num 不为1时的 shard 分组名前缀，默认为空

    `timeout_ms`: [int] 请求倒排索引 RPC 服务的超时时间，默认为300

    `default_search_num`: [int] 单作者搜索个数，默认为10000

    `default_random_search`: [int] 是否采用随机搜索，默认为0

    `default_expire_second`: [int]  索引端查询结果 cache 过期时间（仅对 random search 有效）

    `default_total_request_num`: [int] 总查询个数，代码中未使用

    `author_source`: [string] 作者列表对应的 common attr，默认为 bid_follow

    `common_query`: [string] 通用查询 query 设置，默认为空

    `consider_browse_set`: [bool] 是否考虑过滤已浏览 item，目前未使用

    `reason`: [int] 召回原因，默认为0

    `retrieval_recent_hours`: [int] [动态参数] 默认168

    调用示例
    ------
    ``` python
    .retrieve_by_author_remote_query_index(
      kess_service = "grpc_xxxAuthorCommonIndexServer",
      shard_num = 1,
      timout_ms = 100,
      default_search_num = 200,
      default_random_search = 1,
      default_expire_second = 0,
      author_source = "relationV2_contact_fof_list",
      consider_browse_set = False,
      reason = 1482
    )
    ```
    """
    self._add_processor(AuthorPhotoQueryRemoteRetriever(kwargs))
    return self

  def get_relation_info_feature_v2(self, **kwargs):
    """
    RelationInfoFeatureV2AttrEnricher
    ------
    通过 kess_service 设定的关系中台服务取得关系特征并设为 item attr

    参数配置
    ------
    `kess_service`: [string] 关系中台服务名称

    `timeout_ms`: [int] 请求预估服务的超时时间，默认值为 300

    `cut_length`: [int] [动态参数] 请求作者截断数，默认值为 50

    调用示例
    ------
    ``` python
    .get_relation_info_feature_v2(
      kess_service = "grpc_ccRelationFeedService",
      timeout_ms = 100,
      cut_length = 50
    )
    ```
    """
    self._add_processor(RelationInfoFeatureV2AttrEnricher(kwargs))
    return self
