#!/usr/bin/env python3
# coding=utf-8
"""
filename: retrieval_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for retrieval server
author: <EMAIL>
date: 2020-08-24 14:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class LocalInvertedListRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_inverted_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["search_list_attr"] }


class RetrievalRpcRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_rpc_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("key")))
    ret.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    ret.update(self.extract_dynamic_params(self._config.get("retrieve_num_per_key")))
    ret.update(self.extract_dynamic_params(self._config.get("key_prefix")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_rpc_save_server_show_with_timestamp")))

    return ret

