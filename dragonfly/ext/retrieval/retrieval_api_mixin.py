#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, retrieval api mixin
author: hongx<PERSON><EMAIL>
date: 2020-08-24 14:00:00
"""

from ...common_leaf_util import ArgumentError
from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .retrieval_arranger import *
from .retrieval_retriever import *
from .retrieval_enricher import *


class RetrievalApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含（定制的） retrieval 相关的 Processor 接口:
  - GcseRetrievalPostProcessArranger
  - LocalInvertedListRetriever
  - get_kuiba_user_embedding
  """
  
  def gcse_retrieval_post_process(self, **kwargs):
    """
    GcseRetrievalPostProcessArranger  
    ------
    Gcse 触发服务使用。针对 ANN 触发结果进行过滤、混排、截断

    参数配置
    ------
    `limit`: [int] [动态参数] 结果截断参数，程序处理达到 `limit` 个数据即退出，可缺省则对所有结果集进行处理
    `user_level`: [int] [动态参数] 请求用户的 risk_level_test 取值，content_safety_level_with_namespace__level_hot_online 取值低于该值的 photo 将被过滤
    `browsed_attr`: [string] 指定哪个 common_attr 的数据内容作为过滤集，pid 命中过滤集则被过滤，可缺省则不设置过滤集
    `mix_pattern`: [list] [动态参数] 指定的 _REASON_ 交错规则，数组中的各项为具体的 _REASON_ （正整数），请填写一个完整周期的排列。示例：`[1, 2, 3, 1, 2, 4]` 表示让结果集中的 _REASON_ 顺序循环满足 `1, 2, 3, 1, 2, 4, ...` 的排列，规则为尽力满足

    调用示例
    ------
    ``` python
    .gcse_retrieval_post_process(
      limit = 100,
      user_level = 2,
      browsed_attr = "browsed_pids",
      mix_pattern = [1, 2, 3, 1, 2, 4],
    )
    ```
    """
    self._add_processor(GcseRetrievalPostProcessArranger(kwargs))
    return self
  
  def retrieval_low_pass_diversity(self, **kwargs):
    """
    RetrievalLowPassDiversityArranger 
    ------
    对召回结果的 item attr 的取值数量进行截断过滤，确保多样性

    参数配置
    ------
    `is_debug`: [bool] [动态参数]  Debug 模式，增加一些参数输出

    `default_limit`: [int] [动态参数] 按照数量截断的默认值，缺省 -1 代表不截断

    `default_ratio`: [double] [动态参数] 按照比例截断的默认值，缺省 1.0 不截断

    `config`: [list] 指定需要进行截断的每个 attr 对应的截断参数设置
      - `attr`: [string] item attr
      - `limit`: [int] [动态参数] attr 取值数量限制，默认 `default_limit`
      - `ratio`: [int] [动态参数] attr 取值比例限制，默认 `default_ratio`
      - `match_any`: [bool] [动态参数] attr 取值模糊匹配，等价于按取值数量, 默认 false
      - `limit_map`: [dict(string, int)] attr 取值数量限制，针对具体 `limit_map` 的每个取值设置不同的截断限制

    调用示例
    ------
    ``` python
    .retrieval_low_pass_diversity(
      is_debug = false,
      default_limit = -1,
      default_ratio = 1.0,
      config = [
        {"attr": "exptag", "limit_map": {"1": 200, "2": 100}},
        {"attr": "hetu_level_one", "limit": 20},
        {"attr": "hetu_level_two", "limit": "{{limit_level_two}}"},
      ],
    )
    ```
    """
    self._add_processor(RetrievalLowPassDiversityArranger(kwargs))
    return self


  def pandora_multi_interest_retrieval_post_process(self, **kwargs):
    """
    PandoraMultiInterestRetrievalPostProcessArranger
    ------
    pandora multi_interest 触发服务使用。针对 ANN 触发结果进行排序，多元化打散

    参数配置
    ------
    `limit`: [int] [动态参数] 结果截断参数，程序处理达到 limit 个数据即退出，缺省则对所有结果集进行处理

    `duration_diversity_limit`: [int] [动态参数] 结果中包含duration_diversity_limit条duration分桶后的photo，默认值为0, 可选

    `duration_s_bucket_flag`: [str] [动态参数] duration分桶的分桶边界，默认值"0, 30, 59, 90, 120", 可选

    `diversity_boost`: [double] [动态参数] 多元化打散加权分的权重，缺省则取前 limit 个
    
    `cluster_num`: [int] [动态参数] photo 的 cluster 个数，多元化打散时 photo 按照不同 cluster 计算加权分，若 photo 的 cluster id 为空或者不在 [0, cluster_num - 1] 范围内，则会在 [0, cluster_num - 1] 之间取一个随机值作为其 cluster， 默认值 10000

    调用示例
    ------
    ``` python
    .pandora_multi_interest_retrieval_post_process(
      limit = 100,
      diversity_boost = 0.4,
      cluster_num = 10000,
    )
    ```
    """
    self._add_processor(PandoraMultiInterestRetrievalPostProcessArranger(kwargs))
    return self


  def comirec_retrieval_diversity_post_process(self, **kwargs):
    """
    ComirecRetrievalDiversityPostProcessArranger
    ------
    comirec multi_interest 触发服务使用。针对 ANN 触发结果进行排序，根据多个兴趣进行打散

    参数配置
    ------
    `browsed_attr`: [string] 指定哪个 common_attr 的数据内容作为过滤集，pid 命中过滤集则被过滤，可缺省则不设置过滤集

    `limit`: [int] [动态参数] 结果截断参数，程序处理达到 limit 个数据即退出，缺省则对所有结果集进行处理

    `each_interest_force_keep_size`: [int] [动态参数] 单个兴趣点优先保留视频个数，加入到优先保留集合中

    `all_interest_force_keep_size`: [int] [动态参数] 优先集合中视频最多保留个数

    `duration_diversity_keep_size`: [int] [动态参数] 除去每个兴趣头部视频外，其他所有视频经过时长多样性分桶截断，该参数为时长分桶集合保留个数

    `bucket_size`: [int] [动态参数] 时长分桶个数，默认20

    调用示例
    ------
    ``` python
    .comirec_retrieval_diversity_post_process(
      browsed_attr = "browsed_pids",
      limit = 1000,
      each_interest_force_keep_size = 40,
      all_interest_force_keep_size = 240,
      duration_diversity_keep_size = 800,
      bucket_size = 20
    )
    ```
    """
    self._add_processor(ComirecRetrievalDiversityPostProcessArranger(kwargs))
    return self


  def retrieve_by_local_inverted_list(self, **kwargs):
    """
    LocalInvertedListRetriever  
    ------
    触发服务使用。接收 BTQ 消息并在内存维护倒排数据: int64 -> []int64；业务给定 []int64，顺序查找倒排数据，将结果写到结果集

    参数配置
    ------
    `reason`: [int] 召回标识，添加到结果集的每个数据的 reason 属性取值

    `search_list_attr`: [string] 指定哪个 common_attr 的数据内容作为待查找的数据列表，将顺序查找倒排数据获取结果并补充到结果集

    `data_map_key`: [string] 指定倒排数据内容，在线请求接口需要传递该参数内容获取指定倒排数据，可选值：kconf 对应配置中 return_type_name = UINT64_VECTOR_LRU_MAP 的配置项的一级配置名称，比如：pid_list，kconf 配置由 flag 参数：reco_memory_data_map_kconf_path 指定，参见：ks/reco/memory_data_map/util.cc

    调用示例
    ------
    ``` python
    .retrieve_by_local_inverted_list(
      reason = 1,
      search_list_attr = "uid_list",
      data_map_key = "pid_list",
    )
    ```
    """
    self._add_processor(LocalInvertedListRetriever(kwargs))
    return self

  def get_kuiba_user_embedding(self, **kwargs):
    """
    KuibaUserEmbeddingAttrEnricher
    ------
    请求 kuiba 模型的 fast tower 获取 user embedding

    参数配置
    ------
    `tensor_request_layer`: [string] [动态参数] request layer 名称，与模型配置有关

    `kess_service`: [string] [动态参数] 请求的 fast tower 服务名

    `timeout_ms` : [int] 超时时间 默认 50ms

    `input_common_attr`: [list] [动态参数] 要添加的 common attr 名称列表（user 特征）

    `output_dim_attr`: [string] 选配项，response dim 属性名称

    `output_tensor_attr`: [string] response tensor 属性名称

    调用示例
    ------
    ``` python
    .get_kuiba_user_embedding(
      tensor_request_layer='user_layer',
      kess_service='grpc_fmActionFastTower',
      timeout_ms=20,
      input_common_attr=['uId', 'clickPid', 'clickAid'],
      output_dim_attr='user_embedding_dim',
      output_tensor_attr='user_embedding',
    )
    ```
    """
    self._add_processor(KuibaUserEmbeddingAttrEnricher(kwargs))
    return self

  def retrieve_from_rpc_redis(self, **kwargs):
    """
    RetrievalRpcRedisRetriever  
    ------
    召回使用。从远程存储的 redis 中取 rpc 缓存的 server show 结果，将结果写到结果集，
    其中 redis 部分参考 [retrieve_by_redis](https://dragonfly.corp.kuaishou.com/#/api/common?id=retrieve_by_redis)
    key 直接请求（优先） / key_prefix + key_from_attr 拼接 key

    参数配置
    ------
    `reason`: [int] 召回标识，添加到结果集的每个数据的 reason 属性取值
    
    `retrieve_num`: [int] [动态参数] 召回总量
    
    `retrieve_num_per_key`: [int] [动态参数] 单 key 召回量

    `key`: [string] [动态参数] redis key 

    `key_prefix`: [string] [动态参数] redis key 前缀 

    `key_from_attr`: [string] redis key 取值 common attr

    `enable_rpc_save_server_show_with_timestamp`: [bool] [动态参数] 按时间戳拆分key，默认 False

    `cluster_name`: [string] [动态参数] 集群名称 
    
    `save_src_key_to_attr`: [string] 选配项，将 key 保存到结果集的 item attr 中

    `save_rpc_cache_timestamp_to_attr`: [string] 选配项，将 rpc 缓存时间戳保存到结果集的 item attr 中

    调用示例
    ------
    ``` python
    .retrieve_from_rpc_redis(
      reason = 1,
      retrieve_num = "{{retrieve_num}}",
      retrieve_num_per_key = "{{retrieve_num_per_key}}",
      key = "{{}}",
      enable_rpc_save_server_show_with_timestamp = True,
      cluster_name = "",
      timeout_ms = 10,
      key_from_attr = "user_ids",
      item_separator = ";",
      save_src_key_to_attr = "src_key",
      save_rpc_cache_timestamp_to_attr = "rpc_timestamp",
      save_err_code_to = "error_code",
    )
    ```
    """
    self._add_processor(RetrievalRpcRedisRetriever(kwargs))
    return self

  def enrich_high_value_author_from_colossus(self, **kwargs):
    """
    RetrievalColossusHighValueAuthorEnricher  
    ------
    召回使用。从 colossus 中获取高价值作者信息到 common attr

    参数配置
    ------
    `export_high_value_aid_list`: [string] 指定输出高价值作者列表的 common attr name

    `revisit_author_num`: [int] [动态参数] 复访作者数量，默认 30

    `revisit_author_ratio`: [int] [动态参数] 复访作者比例，默认 0.1

    `revisit_author_time_lower_thresh`: [int] [动态参数] 复访作者次数阈值下限，默认 3

    `revisit_author_time_upper_thresh`: [int] [动态参数] 复访作者次数阈值上限，默认 5

    `revisit_session_interval_s`: [int] [动态参数] session 间隔，单位秒，默认 3600

    `revisit_session_vv`: [int] [动态参数] session 曝光次数，默认 3

    `consume_author_num`: [int] [动态参数] 高消费作者数量，默认 20

    `consume_author_ratio`: [int] [动态参数] 高消费作者比例，默认 0.1

    `consume_author_play_time_lower_thresh`: [int] [动态参数] 高消费作者播放时长阈值下限，默认 300 秒

    `consume_author_play_time_upper_thresh`: [int] [动态参数] 高消费作者播放时长阈值上限，默认 900 秒

    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    调用示例
    ------
    ``` python
    .enrich_high_value_author_from_colossus(
      colossus_resp_attr="colossus_resp",
      export_high_value_aid_list="high_value_aid_list",
    )
    """
    self._add_processor(RetrievalColossusHighValueAuthorEnricher(kwargs))
    return self
  
  def gsu_single_edge_feature_v1_enricher(self, **kwargs):
    self._add_processor(GsuSingleEdgeFeatureV1Enricher(kwargs))
    return self

  def zigzag_arranger(self, **kwargs):
    """
    ZigZagArranger

    分桶蛇形 zigzag 排序
    参数配置
    ------
      - `zigzag_arranger`: [string] [非动态参数] 聚类属性名
    ------
    ``` python
    .explore_snake_merge(
      cluster_attr_name="cluster_attr_name"
    )
    ```
    """
    self._add_processor(ZigZagArranger(kwargs))
    return self
  

  def zigzag_v2_arranger(self, **kwargs):
    """
    ZigZagV2Arranger

    分桶蛇形 zigzag 排序
    参数配置
    ------
      - `cluster_attr_name`: [string] [非动态参数] 聚类属性名
      - `bucket_ratio`: [string] [动态参数] 每个bucket 保留的比例

    ------
    ``` python
    .zigzag_arranger(
      cluster_attr_name="cluster_attr_name",
      bucket_ratio="1.0,1.0,1.0",
    )
    ```
    """
    self._add_processor(ZigZagV2Arranger(kwargs))
    return self

  def retrieval_bucket_enricher(self, **kwargs):
    """
    RetrievalBucketEnricher
    -----
    从 src_item_attr 中，分桶排序

    参数配置
    ------
    `src_item_attr`: [string] 分桶的源 src item attr

    `bucket_attr`: [string] 分桶后 item attr

    `split_bucket_num`: [int] [动态参数] bucket 数量

    `bucket_desc`: [bool] [动态参数] 是否降序排 bucket


    调用示例
    ------
    ``` python
    .retrieval_bucket_enricher(
        src_item_attr = "duration_ms",
        bucket_attr = "duration_bucket",
        split_bucket_num=12,
        bucket_desc=False,
    )
    ```
    """
    self._add_processor(RetrievalBucketEnricher(kwargs))
    return self
