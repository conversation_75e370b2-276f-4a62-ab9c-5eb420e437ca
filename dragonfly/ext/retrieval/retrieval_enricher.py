#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher


class KuibaUserEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_kuiba_user_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("tensor_request_layer")))
    input_attr = self._config.get("input_common_attr")
    if isinstance(input_attr, list):
      attrs.update(input_attr)
    elif isinstance(input_attr, str):
      attrs.update(self.extract_dynamic_params(input_attr))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ['output_tensor_attr', 'output_dim_attr']:
      if self._config.get(key):
        ret.add(self._config.get(key))
    return ret

class RetrievalColossusHighValueAuthorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_high_value_author_from_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_author_ratio")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_author_time_lower_thresh")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_author_time_upper_thresh")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_session_interval_s")))
    ret.update(self.extract_dynamic_params(self._config.get("revisit_session_vv")))
    ret.update(self.extract_dynamic_params(self._config.get("consume_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("consume_author_ratio")))
    ret.update(self.extract_dynamic_params(self._config.get("consume_author_play_time_lower_thresh")))
    ret.update(self.extract_dynamic_params(self._config.get("consume_author_play_time_upper_thresh")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["export_high_value_aid_list"]:
      if self._config.get(key):
        ret.add(self._config.get(key))
    return ret
  
class GsuSingleEdgeFeatureV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "SingleEdgeFeatureV1Enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_keys_from"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config["item_fields"].values())

class RetrievalBucketEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieval_bucket_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("split_bucket_num"):
      ret.update(self.extract_dynamic_params(self._config.get("split_bucket_num")))
    if self._config.get("bucket_desc"):
      ret.update(self.extract_dynamic_params(self._config.get("bucket_desc")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["src_item_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("bucket_attr"))
    return attrs
