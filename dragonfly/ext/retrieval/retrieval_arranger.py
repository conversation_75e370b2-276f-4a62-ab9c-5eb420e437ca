#!/usr/bin/env python3
# coding=utf-8
"""
filename: retrieval_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module for retrieval server
author: <EMAIL>
date: 2020-08-24 14:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class GcseRetrievalPostProcessArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gcse_retrieval_post_process"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["limit", "user_level", "browsed_attr", "mix_pattern"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class PandoraMultiInterestRetrievalPostProcessArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pandora_multi_interest_retrieval_post_process"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["limit", "duration_diversity_limit", "duration_s_bucket_flag", "diversity_boost", "cluster_num"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class ComirecRetrievalDiversityPostProcessArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "comirec_retrieval_diversity_post_process"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["browsed_attr", "limit", "each_interest_force_keep_size",
        "all_interest_force_keep_size", "duration_diversity_keep_size", "bucket_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class RetrievalLowPassDiversityArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieval_low_pass_diversity"


class ZigZagArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "zigzag_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ZigZagV2Arranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "zigzag_v2_arranger"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self._config.get("bucket_ratio"):
      ret.update(self.extract_dynamic_params(self._config.get("bucket_ratio")))
    return ret