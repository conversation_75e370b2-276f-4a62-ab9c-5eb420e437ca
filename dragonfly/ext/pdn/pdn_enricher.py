import operator
import itertools

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class EffectiveViewEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_effective_view_flag"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_item_duration"))
    ret.add(self._config.get("import_item_play_time"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_effective_view"))
    return ret

class GenerateItemTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_custom_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("pro_evtr_percentile")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    return ret

class TransformListAttrPdnEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "transform_list_attr"

  def input_attrs(self, is_common):
    ret = set()
    configs = self._config["configs"]
    for config in configs:
      if config.get("is_sort_attr_common", True) == is_common:
        if "sort_attr" in config:
          ret.add(config["sort_attr"])
      if "filter_configs" in config:
        for c in config["filter_configs"]:
          if c.get("is_attr_comon", True) == is_common:
            ret.add(c["attr"])
          if c.get("is_bound_attr_common", True) == is_common:
            if "lower_bound_attr" in c:
              ret.add(c["lower_bound_attr"])
            if "upper_bound_attr" in c:
              ret.add(c["upper_bound_attr"])
          if c.get("is_not_equal_to_attr_common", True) == is_common:
            if "not_equal_to_attr" in c:
              ret.add(c["not_equal_to_attr"])
      if "attr_configs" in config:
        ret.update([c["src_attr"] for c in config["attr_configs"]
                 if c.get("is_src_attr_common", True) == is_common])
    return ret
  
  def output_attrs(self, is_common):
    ret = set()
    configs = [config for config in self._config["configs"]
               if config.get("is_dst_common", True) == is_common]
    for config in configs:
      if "attr_configs" in config:
        for c in config["attr_configs"]:
          ret.add(c["dst_attr"])
      if "output_sort_indices_attr" in config:
        ret.add(config["output_sort_indices_attr"])
      if "output_mask_flags_attr" in config:
        ret.add(config["output_mask_flags_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self.input_attrs(False)

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self.output_attrs(False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self.input_attrs(True)

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self.output_attrs(True)

class GenerateInterestClockItemTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_interest_clock_item_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_hyperparameter")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sigma")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sort_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_is_incremental")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_exclude_pdn_tag_num")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_critical_value")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_interest_clock_trigger_list"))
    # ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    return ret

class GenerateItemTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    return ret

class TriggerItemEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_trigger_item"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    ret.add(self._config.get("tag_from"))
    ret.add(self._config.get("comment_stay_time_from"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_custom_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("pro_evtr_percentile")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_excluded_from_existing_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_longview_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playtime_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_cst_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playcomplete_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_tag_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_enabled")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    ret.add(self._config.get("export_cst_sort_by_longview_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playtime_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_cst_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playcomplete_trigger_attr_name"))
    ret.add(self._config.get("export_cst_tag_trigger_attr_name"))
    return ret

class TriggerItemV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_trigger_item_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    ret.add(self._config.get("tag_from"))
    ret.add(self._config.get("comment_stay_time_from"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_hyperparameter")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sigma")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sort_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_is_incremental")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_exclude_pdn_tag_num")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_critical_value")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_excluded_from_existing_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_longview_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playtime_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_cst_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playcomplete_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_tag_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_enabled")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_interest_clock_trigger_list"))
    # ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    ret.add(self._config.get("export_cst_sort_by_longview_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playtime_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_cst_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playcomplete_trigger_attr_name"))
    ret.add(self._config.get("export_cst_tag_trigger_attr_name"))
    return ret


class TriggerItemV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_trigger_item_sid"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    ret.add(self._config.get("sid_tag_from"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("hot_sid_tag_rank_ratio")))
    ret.update(self.extract_dynamic_params(self._config.get("rare_sid_tag_rank_ratio")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    return ret

class TriggerItemClockEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_trigger_item_clock"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    ret.add(self._config.get("tag_from"))
    ret.add(self._config.get("comment_stay_time_from"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_hyperparameter")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sigma")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sort_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_is_incremental")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_exclude_pdn_tag_num")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_critical_value")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_excluded_from_existing_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_longview_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playtime_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_cst_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_playcomplete_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_tag_trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("cst_is_enabled")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_interest_clock_trigger_list"))
    # ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    ret.add(self._config.get("export_cst_sort_by_longview_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playtime_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_cst_trigger_attr_name"))
    ret.add(self._config.get("export_cst_sort_by_playcomplete_trigger_attr_name"))
    ret.add(self._config.get("export_cst_tag_trigger_attr_name"))
    return ret

class TriggerAuthorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_trigger_author"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    # ret.add(self._config.get("tag_from"))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_custom_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("pro_evtr_percentile")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_type")))
    ret.update(self.extract_dynamic_params(self._config.get("split_window_num")))
    ret.update(self.extract_dynamic_params(self._config.get("pick_window_num")))
    ret.update(self.extract_dynamic_params(self._config.get("window_top_author_num")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_aid_list"))
    ret.add(self._config.get("export_positive_aid_list"))
    return ret

class TriggerAuthorV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_colossus_author_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    # ret.add(self._config.get("tag_from"))
    ret.update(self.extract_dynamic_params(self._config.get("select_recent_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_hate_author")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_type")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_aid_list"))
    ret.update(self.extract_dynamic_params(self._config.get("export_like_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_follow_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_forward_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_comment_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_hate_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_enter_profile_aid_list")))
    return ret

class GenerateIndividualItemTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_individual_item_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("eval_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_max_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("select_min_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("skip_latest_items_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("all_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("pdn_trigger_random_rates")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_range")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_trigger_sample_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("swing_add_positive_feedback")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_aid_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_dist")))
    ret.update(self.extract_dynamic_params(self._config.get("interact_trigger_sample_rates")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_trigger_type")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_past_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_window_future_s")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_max_interest_number")))
    # ret.update(self.extract_dynamic_params(self._config.get("time_interest_is_use_weekend")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_hyperparameter")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sigma")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_sort_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_is_incremental")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_exclude_pdn_tag_num")))
    ret.update(self.extract_dynamic_params(self._config.get("interest_clock_critical_value")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_missing_memory_trigger")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_top_author_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("mm_photo_num_per_author_cluster")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_all_trigger_list"))
    ret.add(self._config.get("export_pdn_trigger_list"))
    ret.add(self._config.get("export_interact_trigger_list"))
    ret.add(self._config.get("export_swing_trigger_list"))
    ret.add(self._config.get("export_ltv_trigger_list"))
    ret.add(self._config.get("export_interest_clock_trigger_list"))
    # ret.add(self._config.get("export_time_interest_trigger_list"))
    ret.add(self._config.get("export_latest_timestamp"))
    ret.add(self._config.get("export_oldest_timestamp"))
    ret.add(self._config.get("export_mm_cluster_photo_id"))
    ret.add(self._config.get("export_mm_author_photo_id"))
    ret.add(self._config.get("export_mm_author_cluster_photo_id"))
    ret.add(self._config.get("export_mm_missing_author_id"))
    ret.add(self._config.get("export_mm_missing_author_cluster_aid"))
    ret.add(self._config.get("export_mm_recent_photo_cluster"))
    ret.add(self._config.get("export_mm_recent_author"))
    ret.add(self._config.get("export_mm_recent_author_cluster"))
    return ret

class GeneralColossusFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_general_colossus_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("keep_past_range_second")))
    ret.update(self.extract_dynamic_params(self._config.get("keep_future_range_second")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_channels")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_pos")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("extract_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("sort_by_timestamp")))
    ret.update(self.extract_dynamic_params(self._config.get("sort_desc")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_item_num_attr"))
    ret.add(self._config.get("output_pids_attr"))
    ret.add(self._config.get("output_channels_attr"))
    return ret
 
class GenerateTagItemTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_tag_item_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("target_tag_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_sample_rates")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_tag_trigger_list"))
    return ret


class ColossusAuthorTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_colossus_author_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_custom_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("pro_evtr_percentile")))    
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_type")))
    ret.update(self.extract_dynamic_params(self._config.get("split_window_num")))
    ret.update(self.extract_dynamic_params(self._config.get("pick_window_num")))
    ret.update(self.extract_dynamic_params(self._config.get("window_top_author_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_aid_list"))
    ret.add(self._config.get("export_positive_aid_list"))
    return ret

class ColossusAuthorTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_colossus_author_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("select_recent_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_author_num")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_hate_author")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_ev")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_lv")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_type")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_aid_list"))
    ret.update(self.extract_dynamic_params(self._config.get("export_like_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_follow_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_forward_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_comment_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_hate_aid_list")))
    ret.update(self.extract_dynamic_params(self._config.get("export_enter_profile_aid_list")))
    return ret
 
class BatchSampleListCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_common_attr_by_sample_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("query_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config["query_user_ids"]))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("output_attrs", []))
    return attrs

class RecoItemMultiAttrExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_item_multi_attr_from_common"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("label_attr"))
    ret.add(self._config.get("value_attr"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    output_attr_list = self._config.get("output_attr_list")
    for output_attr in output_attr_list:
      ret.add(output_attr)
    return ret

class GenerateTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_pdn_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_num_per_tag")))
    ret.add(self._config.get("import_pid_list"))
    ret.add(self._config.get("import_tag_list"))
    ret.add(self._config.get("import_aid_list"))
    ret.add(self._config.get("import_play_time_list"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_trigger_list"))
    return ret

class GenerateTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_pdn_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_num_per_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("ev")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_trigger_list"))
    return ret

class GenerateLtvTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_ltv_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("total_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_type")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("aid_trigger_threshold")))
    ret.add(self._config.get("import_pid_list"))
    ret.add(self._config.get("import_tag_list"))
    ret.add(self._config.get("import_aid_list"))
    ret.add(self._config.get("import_play_time_list"))
    ret.add(self._config.get("import_duration_list"))
    ret.add(self._config.get("import_label_list"))
    ret.add(self._config.get("import_timestamp_list"))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_trigger_list"))
    ret.add(self._config.get("export_ltv_aid_list"))
    ret.add(self._config.get("export_tag_list"))
    ret.add(self._config.get("export_duration_list"))
    ret.add(self._config.get("export_timestamp_list"))
    ret.add(self._config.get("export_all_ltv_tag_trigger_size"))
    return ret

class GenerateLtvTriggerV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_ltv_trigger_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("total_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_type")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("aid_trigger_threshold")))
    ret.add(self._config.get("colossus_resp_attr"))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_trigger_list"))
    ret.add(self._config.get("export_ltv_aid_list"))
    ret.add(self._config.get("export_tag_list"))
    ret.add(self._config.get("export_duration_list"))
    ret.add(self._config.get("export_timestamp_list"))
    ret.add(self._config.get("export_all_ltv_tag_trigger_size"))
    return ret

class GenerateLtvTriggerColossusV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_ltv_trigger_colossus_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("total_trigger_number")))
    ret.update(self.extract_dynamic_params(self._config.get("ltv_type")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_type")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_diversity")))
    ret.update(self.extract_dynamic_params(self._config.get("single_tag_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_past_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_future_time_range")))
    ret.update(self.extract_dynamic_params(self._config.get("tag_trigger_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("aid_trigger_threshold")))
    ret.add(self._config.get("colossus_resp_attr"))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_trigger_list"))
    ret.add(self._config.get("export_ltv_aid_list"))
    ret.add(self._config.get("export_tag_list"))
    ret.add(self._config.get("export_duration_list"))
    ret.add(self._config.get("export_timestamp_list"))
    ret.add(self._config.get("export_all_ltv_tag_trigger_size"))
    return ret

class TagSelectionAlgorithmV3Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tag_selection_colossus_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("learning_rate")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_size")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_type")))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_tag_list"))
    ret.add(self._config.get("export_tag_dist_list"))
    ret.add(self._config.get("export_tag_size"))
    ret.add(self._config.get("export_trigger_list"))
    ret.add(self._config.get("export_trigger_tag_list"))
    # ret.add(self._config.get("export_tag_list_recent"))
    return ret

class GsuWithAverageTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_average_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_num_per_tag")))
    ret.add(self._config.get("use_padding"))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_photo_id_to_attr"))
    ret.add(self._config.get("save_author_id_to_attr"))
    ret.add(self._config.get("save_duration_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_tag_to_attr"))
    return ret

class CopyCommonSlotSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_common_slot_sign"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_slot_attr"))
    ret.add(self._config.get("in_sign_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("out_slot_attr"))
    ret.add(self._config.get("out_sign_attr"))
    return ret

class CopyItemSlotSignEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "copy_item_slot_sign"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_slot_attr"))
    ret.add(self._config.get("in_sign_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("out_slot_attr"))
    ret.add(self._config.get("out_sign_attr"))
    return ret

class GsuWithSameTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_same_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_item_pid_attr"))
    ret.add(self._config.get("import_item_tag_list_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) ->set:
    ret = set()
    ret.add(self._config.get("export_same_tag_item_pid_list_attr"))
    return ret

class GetConcurrenceKvEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_concurrence_kv"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("kess_service")))
    ret.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    ret.update(self.extract_dynamic_params(self._config.get("bucket")))
    ret.add(self._config.get("import_common_pid_list"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_item_pid"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_concurrence_weight"))
    return ret

class PDNMerchantLiveGsuWithTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pdn_merchant_live_gsu_with_tag"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_live_id_to_attr"))
    ret.add(self._config.get("save_author_id_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_tag_to_attr"))
    ret.add(self._config.get("save_complete_tag_to_attr"))
    return ret

class PDNMerchantLiveAverageTagTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_pdn_merchant_live_average_tag_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("trigger_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ev_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("max_trigger_num_per_tag")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_trigger_to_attr"))
    return ret

class PDNLiveGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pdn_live_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("hetu_cluster_config_key_name"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("target_cluster_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_cluster_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_history_live_id_attr"))
    ret.add(self._config.get("output_history_author_id_attr"))
    ret.add(self._config.get("output_history_cluster_id_attr"))
    ret.add(self._config.get("output_history_play_time_attr"))
    return ret

class PDNLiveGetTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pdn_live_get_trigger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ev_playtime")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_live_id_to_attr"))
    ret.add(self._config.get("save_author_id_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_cluster_id_to_attr"))
    return ret

class PDNLiveGetTriggerV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pdn_live_get_trigger_v1"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("ev_playtime")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_live_id_to_attr"))
    ret.add(self._config.get("save_author_id_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_cluster_id_to_attr"))
    return ret

class SessionTemporalInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_session_temporal_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("deduplicate")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("split_size"))
    ret.add(self._config.get("split_sections"))
    ret.add(self._config.get("ev"))
    ret.add(self._config.get("lv"))
    ret.add(self._config.get("mio_slot_id_start_num"))
    ret.add(self._config.get("slot_offset"))
    ret.add(self._config.get("is_split_by_time"))
    ret.add(self._config.get("is_split_by_num"))
    ret.add(self._config.get("split_by_hour"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_stats_attr"))
    return ret

class SessionTagInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_session_tag_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("deduplicate")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("ev"))
    ret.add(self._config.get("lv"))
    ret.add(self._config.get("mio_slot_id_start_num"))
    ret.add(self._config.get("slot_offset"))    
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_ltr_attr"))
    ret.add(self._config.get("output_wtr_attr"))
    ret.add(self._config.get("output_htr_attr"))
    ret.add(self._config.get("output_ftr_attr"))
    ret.add(self._config.get("output_cmtr_attr"))
    ret.add(self._config.get("output_eptr_attr"))
    ret.add(self._config.get("output_comintr_attr"))
    ret.add(self._config.get("output_commntr_attr"))
    ret.add(self._config.get("output_avg_playtime_attr"))
    ret.add(self._config.get("output_pos_act_dist_attr"))
    ret.add(self._config.get("output_tag_num_dist_attr"))
    return ret

class RetrievalPostRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_post_rank_pxtr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in self._config["all_pxtr_labels"]:
        ret.add(key)
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("mix_format")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_labels_format")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_labels_seperator")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_labels_str")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_weights_seperator")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_weights_str")))    
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_labels")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_weights")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_alpha")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_beta")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_bias")))
    ret.update(self.extract_dynamic_params(self._config.get("pxtr_norm")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("pxtr_attr"))
    return ret


class DecideDynamicDowngradeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "decide_dynamic_downgrade"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_downgrade_prob_double_list_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_is_downgraded_attr"))
    return ret


class GenerateRandomUnifromAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_random_uniform_attr"

  @property
  @strict_types
  def ouput_common_attrs(self) -> set:
    ret = set()
    if self._config["is_common_attr"]:
      ret.add(self._config.get("export_random_uniform_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self._config["is_common_attr"]:
      ret.add(self._config.get("export_random_uniform_attr"))
    return ret


class RetrievalNewReasonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_new_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("reason_list")))
    ret.update(self.extract_dynamic_params(self._config.get("reason_nums")))
    ret.update(self.extract_dynamic_params(self._config.get("reset_strategy")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("new_reason_attr"))
    return ret

class LifeLongTemporalInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_life_long_temporal_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("deduplicate")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("split_size"))
    ret.add(self._config.get("split_sections"))
    ret.add(self._config.get("ev"))
    ret.add(self._config.get("lv"))
    ret.add(self._config.get("mio_slot_id_start_num"))
    ret.add(self._config.get("slot_offset"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_stats_attr"))
    return ret


class LifeLongTagInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_life_long_tag_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("deduplicate")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("ev"))
    ret.add(self._config.get("lv"))    
    ret.add(self._config.get("mio_slot_id_start_num"))
    ret.add(self._config.get("slot_offset"))    
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("output_ltr_attr"))
    ret.add(self._config.get("output_wtr_attr"))
    ret.add(self._config.get("output_htr_attr"))
    ret.add(self._config.get("output_ftr_attr"))
    ret.add(self._config.get("output_cmtr_attr"))
    ret.add(self._config.get("output_eptr_attr"))
    ret.add(self._config.get("output_comintr_attr"))
    ret.add(self._config.get("output_commntr_attr"))
    ret.add(self._config.get("output_tag_num_dist_attr"))
    return ret


class LifeLongU2UInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_life_long_u2u_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class LifeLongU2UInterestEvaluateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "evaluate_life_long_u2u_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("similar_user_colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

class LifeLongU2ULiveInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_life_long_u2u_live_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class LifeLongU2ULiveInterestEvaluateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "evaluate_life_long_u2u_live_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("similar_user_colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret

class LifeLongU2UCommonInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_life_long_u2u_common_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.update(self.extract_dynamic_params(self._config.get("history_start_from_recent")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_ts_filter")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class LiveUserTypeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_live_user_type"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_user_type_attr"))
    return ret

class LongTermInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_long_term_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class ShortTermInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_short_term_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("user_info_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class UnionInterestEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_union_interest"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("user_info_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class TargetItemTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "target_item_tag_extract"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("hetu_level_one_attr"))
    ret.add(self._config.get("hetu_level_two_attr"))
    ret.add(self._config.get("hetu_level_three_attr"))
    return ret
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class SearchIntentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_search_intent"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("query")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_preds_attrs"))
    ret.add(self._config.get("output_probs_attrs"))
    return ret

class PhotoHetuTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_photo_hetu_tag"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_photo_ids"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_hetu_face_ids"))
    ret.add(self._config.get("output_hetu_tag_one_ids"))
    ret.add(self._config.get("output_hetu_tag_two_ids"))
    ret.add(self._config.get("output_hetu_tag_three_ids"))
    return ret

class SIMLRFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_sim_lr_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("topk")))
    ret.update(self.extract_dynamic_params(self._config.get("photo_num_per_tag")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_trigger_to"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("right_id_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class SimilarPhotoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_similar_photo"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("source_item_attr"))
    ret.add(self._config.get("distance_attr"))
    ret.add(self._config.get("ann_result_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("top_k")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_similar_photo_attr"))
    ret.add(self._config.get("output_similar_photo_distance_attr"))
    return ret

class LiveSIMLRFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_live_sim_lr_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("limit_number")))
    ret.update(self.extract_dynamic_params(self._config.get("play_time_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("n_minute_ago")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_trigger_to"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("right_id_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class ColossusBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_colossus_batch_trigger"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr",""))
    ret.add(self._config.get("uid_list_attr",""))
    return ret

class PositiveSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pos_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("sample_type")))
    ret.update(self.extract_dynamic_params(self._config.get("sample_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("filter_ids"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_photo_ids_attr"))
    ret.add(self._config.get("export_author_ids_attr"))
    ret.add(self._config.get("export_tags_attr"))
    return ret

class ItemDualSimHardSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_dual_sim_hard_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("item_sim_topk")))
    ret.update(self.extract_dynamic_params(self._config.get("dedup_did")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_sim_cluster_did_attr"))
    ret.add(self._config.get("user_cluster_attr"))
    ret.add(self._config.get("item_sim_play_time_attr"))
    ret.add(self._config.get("item_sim_ts_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_sim_did_gsu_attr"))
    ret.add(self._config.get("item_sim_play_time_gsu_attr"))
    ret.add(self._config.get("item_sim_ts_gsu_attr"))
    ret.add(self._config.get("item_sim_ori_len_attr"))
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class ItemDualSimMultiClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_item_dual_sim_multi_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("item_sim_topk_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_rank_type")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_max_num")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_cluster_min_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_min_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("dedup_did")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_dura_attr"))
    ret.add(self._config.get("item_sim_cluster_did_attr"))
    ret.add(self._config.get("item_sim_play_time_attr"))
    ret.add(self._config.get("item_sim_ts_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_topk_cluster_attr"))
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    return ret

class UnionHardSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "union_hard_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("src_num")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_src_photo_id_attr"))
    ret.add(self._config.get("save_dst_photo_id_attr"))
    ret.add(self._config.get("save_src_photo_tag_attr"))
    ret.add(self._config.get("save_dst_photo_tag_attr"))
    return ret

class UnionLostMemoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "union_lost_memory"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("n_day")))
    ret.update(self.extract_dynamic_params(self._config.get("n_vv")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_photo_id_attr"))
    ret.add(self._config.get("save_photo_tag_attr"))
    ret.add(self._config.get("save_author_id_attr"))
    ret.add(self._config.get("save_play_time_attr"))
    ret.add(self._config.get("save_duration_attr"))
    return ret

class UnionUniformEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "union_uniform"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("long_size")))
    ret.update(self.extract_dynamic_params(self._config.get("short_size")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("effective_view"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_short_photo_id_attr"))
    ret.add(self._config.get("save_long_photo_id_attr"))
    ret.add(self._config.get("save_actual_short_size_attr"))
    ret.add(self._config.get("save_actual_long_size_attr"))
    return ret

class ExtractMillionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_million"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("filter_pids"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_tag_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("use_cluster_id")))
    ret.update(self.extract_dynamic_params(self._config.get("effective_view")))
    ret.update(self.extract_dynamic_params(self._config.get("top_tag_number")))
    ret.update(self.extract_dynamic_params(self._config.get("photo_num_per_tag")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_photo_id_to_attr"))
    ret.add(self._config.get("save_author_id_to_attr"))
    ret.add(self._config.get("save_duration_to_attr"))
    ret.add(self._config.get("save_play_time_to_attr"))
    ret.add(self._config.get("save_tag_to_attr"))
    ret.add(self._config.get("save_actual_size_attr"))
    return ret

class ExtractMissingMemoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_missing_memory"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_photo_id"))
    ret.add(self._config.get("colossus_author_id"))
    ret.add(self._config.get("colossus_cluster_id"))
    ret.add(self._config.get("colossus_tag"))
    ret.add(self._config.get("colossus_duration"))
    ret.add(self._config.get("colossus_playing_time"))
    ret.add(self._config.get("colossus_timestamp"))
    ret.add(self._config.get("colossus_label"))
    ret.add(self._config.get("colossus_author_cluster_id"))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("top_cluster_number")))
    ret.update(self.extract_dynamic_params(self._config.get("photo_num_per_cluster")))
    ret.update(self.extract_dynamic_params(self._config.get("top_author_number")))
    ret.update(self.extract_dynamic_params(self._config.get("photo_num_per_author")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_cluster_photo_id"))
    ret.add(self._config.get("save_author_photo_id"))
    ret.add(self._config.get("save_missing_author_id"))
    ret.add(self._config.get("save_missing_author_cluster_aid"))
    return ret

class ExtractSearchUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_search_user_info"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("session_number")))
    ret.update(self.extract_dynamic_params(self._config.get("random_prob")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_query_optimazation")))
    ret.update(self.extract_dynamic_params(self._config.get("time_decay_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("random_weight")))
    ret.update(self.extract_dynamic_params(self._config.get("enable_trigger_filter")))
    ret.update(self.extract_dynamic_params(self._config.get("play_dur_threshold")))
    ret.update(self.extract_dynamic_params(self._config.get("trigger_num_limit")))
    ret.add(self._config.get("user_info_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("search_play_photo_attr"))
    ret.add(self._config.get("search_query_attr"))
    return ret

class GeneralSearchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "general_search"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("gsu_search_num")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_item_hetu_tag_attr"))
    ret.add(self._config.get("export_slot_attr"))
    ret.add(self._config.get("export_sign_attr"))
    ret.add(self._config.get("export_photo_id_result_attr"))
    return ret

class GeneralSearchInferEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "general_search_infer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("custom_tag_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("gsu_search_num")))
    ret.update(self.extract_dynamic_params(self._config.get("bucket_num")))
    ret.update(self.extract_dynamic_params(self._config.get("gsu_method")))
    ret.update(self.extract_dynamic_params(self._config.get("mixture_rate")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_slot_attr"))
    ret.add(self._config.get("export_sign_attr"))
    ret.add(self._config.get("export_photo_id_result_attr"))
    ret.add(self._config.get("export_bucket_num_attr"))
    ret.add(self._config.get("export_bucket_name_attr"))
    return ret

class UnionUniformV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "union_uniform_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("long_size")))
    ret.update(self.extract_dynamic_params(self._config.get("short_size")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("effective_view"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_short_photo_id_attr"))
    ret.add(self._config.get("save_long_photo_id_attr"))
    ret.add(self._config.get("save_actual_short_size_attr"))
    ret.add(self._config.get("save_actual_long_size_attr"))
    return ret

class RandomSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sum_random_split"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("sum_size")))
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_p1_photo_id_attr"))
    ret.add(self._config.get("save_p2_photo_id_attr"))
    return ret

class GenerateUserHistoryFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "generate_user_history_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("item_num_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("is_reverse")))
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("is_effective_view"))
    ret.add(self._config.get("is_long_view"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_slot_attr"))
    ret.add(self._config.get("output_sign_attr"))
    ret.add(self._config.get("save_photo_id_list_attr"))
    ret.add(self._config.get("save_photo_tag_list_attr"))
    ret.add(self._config.get("save_photo_size_attr"))
    ret.add(self._config.get("save_span_day_attr"))
    return ret

class  CalcRetrieveScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calculate_retrieve_score"
  
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("retrieve_score_attr_list"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("item_reason_list"))
    ret.add(self._config.get("item_score"))
    return ret

class SftDataEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sft_data_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("photo_id_from"))
    ret.add(self._config.get("timestamp_from"))
    ret.add(self._config.get("author_id_from"))
    ret.add(self._config.get("play_time_from"))
    ret.add(self._config.get("duration_from"))
    ret.add(self._config.get("label_from"))
    ret.add(self._config.get("tag_from"))
    ret.add(self._config.get("channel_from"))
    ret.update(self.extract_dynamic_params(self._config.get("filter_channels")))
    ret.update(self.extract_dynamic_params(self._config.get("extract_item_limit")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("export_colossus_photo_id_attr"))
    ret.add(self._config.get("export_colossus_timestamp_attr"))
    ret.add(self._config.get("export_colossus_play_time_attr"))
    ret.add(self._config.get("export_colossus_interact_mask_attr"))
    ret.add(self._config.get("export_colossus_channel_attr"))
    ret.add(self._config.get("export_colossus_longview_mask_attr"))
    return ret
