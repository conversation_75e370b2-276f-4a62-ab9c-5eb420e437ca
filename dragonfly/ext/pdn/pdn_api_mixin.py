#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .pdn_enricher import *
from .pdn_observer import *
from .pdn_retriever import *
from .pdn_arranger import *

class PDNApiMixin(CommonLeafBaseMixin):
  """
  PDN Processor API 接口的 Mixin 实现
  """

  def enrich_effective_view_flag(self, **kwargs):
    """
    EffectiveViewEnricher
    ------
    根据 item 的 duration 和 play_time 计算是否是 effective_view 

    参数
    ------
    `import_item_duration`: [string] 指定输入 item duration 的 attr name
    `import_item_play_time`: [string] 指定输入 item play_time 的 attr name
    `export_effective_view`: [string] 指定输出 effective_view flag 的 attr name 

    调用示例
    ------
    ``` python
    .enrich_effective_view_flag(
      import_item_duration="item_duration",
      import_item_play_time="item_play_time",
      export_effective_view="item_effective_view"
    )
    ```
    """
    self._add_processor(EffectiveViewEnricher(kwargs))
    return self

  def retrieve_general_colossus(self, **kwargs):
    """
    GeneralColossusRetriever
    ------
    召回通用 colossus

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `reason`: [int] 指定召回 reason

    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_cluster_id_to_attr`: [string]

    `save_profile_stay_time_to_attr`: [string]

    `save_comment_stay_time_to_attr`: [string]

    `filter_future_ts` : [boolean] [动态参数] 是否只取时间在request time之前的colossus pid, 默认为false

    `keep_past_range_second`: [int] [动态参数] 只取时间在request time之前keep_past_range_second秒的colossus pid, 默认为0不限制

    `keep_future_range_second`: [int] [动态参数] 只取时间在request time之后keep_future_range_second秒的colossus pid, 默认为0不限制

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    调用示例
    ------
    ``` python
    .retrieve_general_colossus(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      colossus_service_name="grpc_colossusSimV2",
      colossus_item_limit=10000
    )
    ```
    """
    self._add_processor(GeneralColossusRetriever(kwargs))
    return self
    
  def enrich_item_trigger(self, **kwargs):
    """
    GenerateItemTriggerEnricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 4 种方式：

    （1）PDN Trigger: 从历史行为中选择 photo 数量最多的 `pdn_top_tag_number` 个 tag，每个 tag 下选择 `pdn_trigger_num_per_tag` 个 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_custom_ev`: [bool] [动态参数] 是否过滤自定义有效播放观看视频， 1: wtd_evtr, 2: pro_evtr, 3: wtd_evtr or pro_evtr, 0: 不过滤

    `pro_evtr_percentile`: [double] [动态参数] pro_evtr 的分位数，默认 0.50    

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `pdn_top_tag_number`: [int] [动态参数] 指定 pdn 最优 tag 数量

    `pdn_trigger_num_per_tag`: [int] [动态参数] 指定 pdn 每个 tag 下视频数量

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    调用示例
    ------
    ``` python
    .enrich_item_trigger(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      pdn_top_tag_number=5,
      pdn_trigger_num_per_tag=10,
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0]
    )
    ```
    """
    self._add_processor(GenerateItemTriggerEnricher(kwargs))
    return self

  def transform_list_attr_pdn(self, **kwargs):
    """
    TransformListPdnAttrEnricher
    -----
    对 common list attr 进行排序、过滤后生成新的 common list attr

    参数
    -----
    `configs`: [list] transform 配置
      - `is_dst_common`: [bool] 输出是否是 common attr, 默认为 true

      - `sort_attr`: [string] 用于排序的 attr

      - `is_sort_attr_common`: [bool] 用于排序的 attr 是否是 common attr, 默认为 true

      - `sort_desc`: [bool] 是否降序排序，默认为 true

      - `limit_size`: [int] 新 list attr 最大长度, 默认为 -1 表示没有限制

      - `output_sort_indices_attr`: [string] 输出排序后的 indices attr, 默认为 None

      - `output_mask_flags_attr`: [string] 输出 mask flags attr, 默认为 None

      - `use_double_mask`: [bool] 是否使用 double mask, 默认为 false

      - `filter_configs`: [list] filter 配置
        - `attr`: [string] 用于 filter 的 attr
        - `is_attr_common`: [bool] 用于 filter 的 attr 是否是 common attr, 默认为 true
        - `lower_bound_attr`: [string] 用于 filter 的 attr 的 lower bound, 默认为 None
        - `upper_bound_attr`: [string] 用于 filter 的 attr 的 upper bound, 默认为 None
        - `not_equal_to_attr`: [string] 不等于 attr, 默认为 None
        - `is_not_equal_to_attr_common`: [bool] 不等于 attr 是否是 common attr, 默认为 true

      - `attr_configs`: [list] attr 配置
       - `src_attr`: [string] 输入 attr name
       - `dst_attr`: [string] 输出 attr name
       - `is_src_attr_common`: [bool] 输入 attr 是否是 common attr, 默认为 true

    调用示例
    -----
    .transform_list_attr(
      configs=[
        dict(
          is_dst_common=True
          sort_attr="timestamp",
          sort_desc=True,
          filter_configs=[
            dict(attr="watch_time", min_val=60),
          ]
          limit_size=50,
          attr_configs=[
            dict(src_attr="author_id", dst_attr="new_author_id"),
            dict(src_attr="watch_time", dst_attr="new_watch_time"),
          ]
        ),
      ]
    )
    """
    self._add_processor(TransformListAttrPdnEnricher(kwargs))
    return self
  
  def enrich_interest_clock_item_trigger_v2(self, **kwargs):
    """
    GenerateInterestClockItemTriggerV2Enricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：

    （1）PDN Trigger: 从历史行为中选择 photo 数量最多的 `pdn_top_tag_number` 个 tag，每个 tag 下选择 `pdn_trigger_num_per_tag` 个 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `pdn_top_tag_number`: [int] [动态参数] 指定 pdn 最优 tag 数量

    `pdn_trigger_num_per_tag`: [int] [动态参数] 指定 pdn 每个 tag 下视频数量

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    .enrich_interest_clock_item_trigger_v2(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      pdn_top_tag_number=5,
      pdn_trigger_num_per_tag=10,
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """
    self._add_processor(GenerateInterestClockItemTriggerV2Enricher(kwargs))
    return self

  def enrich_item_trigger_v2(self, **kwargs):
    """
    GenerateItemTriggerV2Enricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：

    （1）PDN Trigger: 从历史行为中选择 photo 数量最多的 `pdn_top_tag_number` 个 tag，每个 tag 下选择 `pdn_trigger_num_per_tag` 个 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `pdn_top_tag_number`: [int] [动态参数] 指定 pdn 最优 tag 数量

    `pdn_trigger_num_per_tag`: [int] [动态参数] 指定 pdn 每个 tag 下视频数量

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    .enrich_item_trigger_v2(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      pdn_top_tag_number=5,
      pdn_trigger_num_per_tag=10,
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """
    self._add_processor(GenerateItemTriggerV2Enricher(kwargs))
    return self
  
  def enrich_individual_item_trigger_v2(self, **kwargs):
    """
    GenerateIndividualItemTriggerV2Enricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：

    （1）PDN Trigger: 从历史行为中对所有 tag 下 photo 数量进行取对数和归一化处理，降低头部 tag 热度，丢弃冷门 tag，按取对数和归一化后的比例值选取 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    .enrich_item_trigger_v2(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """
    self._add_processor(GenerateIndividualItemTriggerV2Enricher(kwargs))
    return self

  def enrich_trigger_item(self, **kwargs):
    """
    TriggerItemEnricher
    ------
    从 colossus_v2 行为历史中选择 item trigger, 支持以下 5 种方式：
    ( 与 enrich_item_trigger_v2 功能相同, colossus 来源不同 )

    （1）PDN Trigger: 从历史行为中选择 photo 数量最多的 `pdn_top_tag_number` 个 tag，每个 tag 下选择 `pdn_trigger_num_per_tag` 个 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------

    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `tag_from` : [string] tag 从那个 CommonAttr 获取

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_custom_ev`: [bool] [动态参数] 是否过滤自定义有效播放观看视频， 1: wtd_evtr, 2: pro_evtr, 3: wtd_evtr or pro_evtr, 0: 不过滤

    `pro_evtr_percentile`: [double] [动态参数] pro_evtr 的分位数，默认 0.50

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `pdn_top_tag_number`: [int] [动态参数] 指定 pdn 最优 tag 数量

    `pdn_trigger_num_per_tag`: [int] [动态参数] 指定 pdn 每个 tag 下视频数量

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    # 数据来自 v2 colossus
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                                item_fields=dict(photo_id="photo_id_list",
                                                 timestamp="timestamp_list",
                                                 author_id="author_id_list",
                                                 play_time="play_time_list",
                                                 duration="duration_list",
                                                 label="label_list",
                                                 tag="tag_list",
                                                 )) \  
    .enrich_trigger_item(
      photo_id_from="photo_id_list",
      timestamp_from="timestamp_list",
      author_id_from="author_id_list",
      play_time_from="play_time_list",
      duration_from="duration_list",
      label_from="label_list",
      tag_from="tag_list",
      use_cluster_id=False,
      colossus_item_limit=12000,
      filter_ev=True,
      filter_lv=False,
      filter_custom_ev=1,
      pro_evtr_percentile=0.5,      
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      pdn_top_tag_number=5,
      pdn_trigger_num_per_tag=10,
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """
    self._add_processor(TriggerItemEnricher(kwargs))
    return self

  def enrich_trigger_item_v2(self, **kwargs):
    """
    TriggerItemV2Enricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：
    (功能同 enrich_individual_item_trigger_v2， colossus 来源不同)

    （1）PDN Trigger: 从历史行为中对所有 tag 下 photo 数量进行取对数和归一化处理，降低头部 tag 热度，丢弃冷门 tag，按取对数和归一化后的比例值选取 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------
    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `tag_from` : [string] tag 从那个 CommonAttr 获取

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    # 数据来自 v2 colossus
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                                item_fields=dict(photo_id="photo_id_list",
                                                 timestamp="timestamp_list",
                                                 author_id="author_id_list",
                                                 play_time="play_time_list",
                                                 duration="duration_list",
                                                 label="label_list",
                                                 tag="tag_list",
                                                 )) \  
    .enrich_trigger_item_v2(
      photo_id_from="photo_id_list",
      timestamp_from="timestamp_list",
      author_id_from="author_id_list",
      play_time_from="play_time_list",
      duration_from="duration_list",
      label_from="label_list",
      tag_from="tag_list",
      colossus_item_limit=12000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """    
    self._add_processor(TriggerItemV2Enricher(kwargs))
    return self
  
  def enrich_trigger_item_sid(self, **kwargs):
    """
    TriggerItemV3Enricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：
    
    （1）PDN Trigger: 从历史行为中对所有 sid_tag 下 photo 数量进行取对数和归一化处理，降低头部 tag 热度，丢弃冷门 tag，按取对数和归一化后的比例值选取 photo；

    参数
    ------
    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `sid_tag_from` : [string] sid_tag 从那个 CommonAttr 获取

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `hot_sid_tag_rank_ratio`: [double] [动态参数] 删除热门sid_tag的比例，默认 0.1
    
    `rare_sid_tag_rank_ratio`: [double] [动态参数] 删除冷门sid_tag的比例，默认 0.1

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值
    """

    self._add_processor(TriggerItemV3Enricher(kwargs))
    return self

  def enrich_trigger_item_clock(self, **kwargs):
    """
    TriggerItemClockEnricher
    ------
    从 colossus 行为历史中选择 item trigger， 支持以下 5 种方式：
    (功能同 enrich_interest_clock_item_trigger_v2， colossus_v2 数据源)

    （1）PDN Trigger: 从历史行为中选择 photo 数量最多的 `pdn_top_tag_number` 个 tag，每个 tag 下选择 `pdn_trigger_num_per_tag` 个 photo；

    （2）Swing Trigger: 从历史行为中考虑最近的 `swing_trigger_sample_range` 个 longview photo，以 `swing_trigger_sample_rate` 的概率采样；

    （3）LTV Trigger: 从历史行为中考虑过去的 `ltv_trigger_past_time_range` 和未来的 `ltv_trigger_future_time_range` 范围内在 tag 或 aid 超过指定阈值的 photo 

    （4）Interact Trigger: 从历史行为中选择产生互动行为 (like, follow, forward, comment, enter_profile) 的 photo 
    
    （5）Missing_Memory Trigger: 从历史行为中选择遗忘 tag 的 photo

    参数
    ------
    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `tag_from` : [string] tag 从那个 CommonAttr 获取

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag

    `eval_timestamp`: [int] [动态参数] 指定评估 timestamp(s)，统计比 timestamp 更老和更新的视频数量

    `select_max_timestamp`: [int] [动态参数] 指定选取最大 timestamp(s)，超过该 timestamp 将丢弃，默认当前请求时刻

    `select_min_timestamp`: [int] [动态参数] 指定选取最小 timestamp(s)，小于该 timestamp 将丢弃，默认 0 不丢弃
    
    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频

    `skip_latest_items_seconds`: [int] [动态参数] 制定过滤新视频的时间间隔（秒），默认 60

    `export_all_trigger_list`: [string] 指定输出融合 trigger list 的 common attr name

    `export_pdn_trigger_list`: [string] 指定输出 pdn trigger list 的 common attr name

    `export_swing_trigger_list`: [string] 指定输出 swing trigger list 的 common attr name

    `export_ltv_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `export_interact_trigger_list`: [string] 指定输出 interact trigger list 的 common attr name

    `export_latest_timestamp`: [string] 指定输出 colossus list 最新 photo 的 timestamp

    `export_oldest_timestamp`: [string] 指定输出 colossus list 最老 photo 的 timestamp

    `all_trigger_number`: [int] [动态参数] 指定融合 trigger 数量

    `pdn_trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `pdn_trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `pdn_trigger_random_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    `pdn_top_tag_number`: [int] [动态参数] 指定 pdn 最优 tag 数量

    `pdn_trigger_num_per_tag`: [int] [动态参数] 指定 pdn 每个 tag 下视频数量

    `swing_trigger_number`: [int] [动态参数] 指定 swing trigger 数量

    `swing_trigger_sample_range`: [int] [动态参数] 指定 swing trigger 采样的范围

    `swing_trigger_sample_rate`: [int] [动态参数] 指定 swing trigger 的采样概率

    `swing_add_positive_feedback`: [bool] [动态参数] swing trigger 增加 positive_feedback

    `ltv_trigger_number`: [int] [动态参数] 指定 ltv trigger 数量

    `ltv_trigger_type`: [int] [动态参数] 指定 ltv trigger 选取类型，支持 0（按 tag）, 1（按 aid）, 2（混合）

    `ltv_enable_diversity`: [int] [动态参数] 指定 ltv trigger 是否考虑 tag 多样性

    `ltv_single_tag_max_num`: [int] [动态参数] 指定 ltv trigger 单个 tag 下的 photo 限制阈值

    `ltv_trigger_past_time_range`: [int] [动态参数] 指定 ltv trigger 的过去偏好计算范围

    `ltv_trigger_future_time_range`: [int] [动态参数] 指定 ltv trigger 的未来偏好计算范围

    `ltv_tag_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 tag 选择阈值

    `ltv_aid_trigger_threshold`: [double] [动态参数] 指定 ltv trigger 的 aid 选择阈值

    `interact_trigger_number`: [double] [动态参数] 指定 interact trigger 数量

    `interact_trigger_sample_dist`: [string] [动态参数] 指定 interact trigger 采样分布，可选 `history`（colossus 历史分布), `custom`（自定义），默认 `history`

    `interact_trigger_sample_rates`: [list[double]] [动态参数] 指定 interact trigger 采样概率，仅当 `custom`  生效

    `enable_missing_memory_trigger`: [int] [动态参数] 打开 missing_memory 开关

    `export_mm_cluster_photo_id`: [string] 输出 missing_memory 特征

    `export_mm_author_photo_id`: [string]输出 missing_memory 特征

    `export_mm_author_cluster_photo_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_id`: [string]输出 missing_memory 特征

    `export_mm_missing_author_cluster_aid`: [string]输出 missing_memory 特征

    `export_mm_recent_photo_cluster`: [string]输出 missing_memory 特征

    `export_mm_recent_author`: [string]输出 missing_memory 特征

    `export_mm_recent_author_cluster `: [string]输出 missing_memory 特征

    `mm_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_missing_day_num`: [int] [动态参数] missing_memory 输入参数

    `mm_author_cluster_valid_view_num`: [int] [动态参数] missing_memory 输入参数

    `mm_top_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_cluster`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author`: [int] [动态参数] missing_memory 输入参数

    `mm_top_author_cluster_number`: [int] [动态参数] missing_memory 输入参数

    `mm_photo_num_per_author_cluster`: [int] [动态参数] missing_memory 输入参数 

    调用示例
    ------
    ``` python
    # 数据来自 v2 colossus
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                                item_fields=dict(photo_id="photo_id_list",
                                                 timestamp="timestamp_list",
                                                 author_id="author_id_list",
                                                 play_time="play_time_list",
                                                 duration="duration_list",
                                                 label="label_list",
                                                 tag="tag_list",
                                                 )) \  
    .enrich_trigger_item_clock(
      photo_id_from="photo_id_list",
      timestamp_from="timestamp_list",
      author_id_from="author_id_list",
      play_time_from="play_time_list",
      duration_from="duration_list",
      label_from="label_list",
      tag_from="tag_list",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_all_trigger_list="all_item_trigger",
      export_pdn_trigger_list="pdn_item_trigger",
      export_swing_trigger_list="swing_item_trigger",
      export_ltv_trigger_list="ltv_item_trigger",
      export_interact_trigger_list="interact_item_trigger",
      export_latest_timestamp="latest_timestamp",
      export_oldest_timestamp="oldest_timestamp",
      all_trigger_number=150,
      pdn_trigger_number=50,
      pdn_trigger_type=3,
      pdn_trigger_random_rates=[1.0, 0.0, 0.0],
      pdn_top_tag_number=5,
      pdn_trigger_num_per_tag=10,
      swing_trigger_number=50,
      swing_trigger_sample_range=100,
      swing_trigger_sample_rate=0.5,
      swing_add_positive_feedback=True,
      ltv_trigger_number=50,
      ltv_trigger_type=2,
      ltv_enable_diversity=1,
      ltv_single_tag_max_num=30,
      ltv_trigger_past_time_range=500,
      ltv_trigger_future_time_range=100,
      ltv_tag_trigger_threshold=5.0,
      ltv_aid_trigger_threshold=5.0,
      interact_trigger_number=30,
      interact_trigger_sample_dist="history",
      interact_trigger_sample_rates=[1.0, 0.0, 0.0, 0.0, 0.0],
      # missing_memory trigger
      export_mm_cluster_photo_id="missing_cluster_photo_id",
      export_mm_author_photo_id="missing_author_photo_id",
      export_mm_author_cluster_photo_id="missing_author_cluster_photo_id",
      export_mm_missing_author_id="missing_author_id",
      export_mm_missing_author_cluster_aid="missing_author_cluster_aid",
      export_mm_recent_photo_cluster="recent_photo_cluster",
      export_mm_recent_author="recent_author",
      export_mm_recent_author_cluster = "recent_author_cluster",
      mm_cluster_missing_day_num="{{cluster_missing_day_num}}",
      mm_cluster_valid_view_num="{{cluster_valid_view_num}}",
      mm_author_missing_day_num="{{author_missing_day_num}}",
      mm_author_valid_view_num="{{author_valid_view_num}}",
      mm_author_cluster_missing_day_num="{{author_cluster_missing_day_num}}",
      mm_author_cluster_valid_view_num="{{author_cluster_valid_view_num}}",
      mm_top_cluster_number="{{top_cluster_number}}",
      mm_photo_num_per_cluster="{{photo_num_per_cluster}}",
      mm_top_author_number="{{top_author_number}}",
      mm_photo_num_per_author="{{photo_num_per_author}}",
      mm_top_author_cluster_number="{{top_author_cluster_number}}",
      mm_photo_num_per_author_cluster="{{photo_num_per_author_cluster}}",
    )
    ```
    """
    self._add_processor(TriggerItemClockEnricher(kwargs))
    return self

  def enrich_trigger_author(self, **kwargs):
    """
    TriggerAuthorEnricher
    ------
    从 colossus 行为历史中选择 author id：
    （1）将行为历史划分成 `split_window_num` 个等分窗口，并随机挑选 `pick_window_num` 个窗口进行 trigger 生成;

    （2）在每个挑选的窗口中，分别按 avg_play_time 和 photo 数量由大到小排序，取前 `window_top_author_num` 个 author_id 作为 trigger

    参数
    ------
    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `export_aid_list`: [string] 指定输出 author id list 的 common attr name

    `export_positive_aid_list`: [string] 指定输出正反馈 author id list 的 common attr name

    `split_window_num`: [int] [动态参数] 指定划分的窗口数量

    `pick_window_num`: [int] [动态参数] 指定选择生成 trigger 的窗口数量

    `window_top_author_num`: [int] [动态参数] 指定每个窗口内选择的 author id 数量

    `sample_type`: [int][动态参数] 指定 Top 选择序: 0 -> Aid 视频个数 , 1 -> Aid 累计 Play Time, 2 ->  Aid 平均 Play Complete Rate, 默认 0

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长播观看视频

    `filter_custom_ev`: [bool] [动态参数] 是否过滤自定义有效播放观看视频， 1: wtd_evtr, 2: pro_evtr, 3: wtd_evtr or pro_evtr, 0: 不过滤

    `pro_evtr_percentile`: [double] [动态参数] pro_evtr 的分位数，默认 0.50
    
    `filter_future_ts`: [bool] [动态参数] 是否过滤穿越的行为历史

    调用示例
    ------
    ``` python
    # 数据来自 v2 colossus
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                  item_fields=dict(photo_id="photo_id_list",
                                  timestamp="timestamp_list",
                                  author_id="author_id_list",
                                  play_time="play_time_list",
                                  duration="duration_list",
                                  label="label_list",
                                  tag="tag_list",
                                  )) \
    .enrich_trigger_author(
      photo_id_from="photo_id_list",
      timestamp_from="timestamp_list",
      author_id_from="author_id_list",
      play_time_from="play_time_list",
      duration_from="duration_list",
      label_from="label_list",
      export_aid_list="aid_list",
      export_positive_aid_list="positive_aid_list",
      split_window_num=5,
      pick_window_num=2,
      window_top_author_num=100,
      sample_type=1,
      filter_lv=True,
      filter_custom_ev=0,
      pro_evtr_percentile=0.5,
    )
    ```
    """
    self._add_processor(TriggerAuthorEnricher(kwargs))
    return self

  def enrich_trigger_author_v2(self, **kwargs):
    """
    TriggerAuthorV2Enricher
    ------
    从 colossus 行为历史中选择 author id：
    (1) 选取最近观看的 `select_recent_author_num` 个 aid;
    
    (2) 此外随机采样 `sample_author_num` 个 aid;
    
    (3) 可选地选择 prefer(like、follow、forward、comment 和 has_enter_profile) 的 aid; 
    
    (4) 可选地丢弃 hate 的 aid.
    参数
    ------
    `photo_id_from` : [string] photo_id 从那个 CommonAttr 获取

    `timestamp_from` : [string] timestamp 从那个 CommonAttr 获取

    `author_id_from` : [string] author_id 从那个 CommonAttr 获取

    `play_time_from` : [string] play_time 从那个 CommonAttr 获取

    `duration_from` : [string] duration 从那个 CommonAttr 获取

    `label_from` : [string] label 从那个 CommonAttr 获取

    `export_aid_list`: [string] 指定输出 author id list 的 common attr name

    `select_recent_author_num`: [int][动态参数] 指定输出最近的 aid 数量，默认 50

    `sample_author_num`: [int][动态参数] 指定随机采样的 aid 数量，默认 50

    `sample_type`: [int][动态参数] 指定采样的权重: 0 -> Uniform, 1 -> Play Time, 2 -> Play Complete Rate, 默认 0

    `filter_hate_author`: [bool][动态参数] 是否过滤 hate 的 aid，默认 True

    `filter_future_ts`: [bool] [动态参数] 是否过滤穿越的行为历史

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长播观看视频

    `export_like_aid_list`: [string] [动态参数] 指定输出 like aid list 的 common attr name

    `export_follow_aid_list`: [string] [动态参数] 指定输出 follow aid list 的 common attr name

    `export_forward_aid_list`: [string] [动态参数] 指定输出 forward aid list 的 common attr name

    `export_comment_aid_list`: [string] [动态参数] 指定输出 comment aid list 的 common attr name

    `export_enter_profile_aid_list`: [string] [动态参数] 指定输出 has_enter_profile aid list 的 common attr name

    `export_hate_profile_aid_list`: [string] [动态参数] 指定输出 hate aid list 的 common attr name

    调用示例
    ------
    ``` python
    .gsu_common_colossusv2_enricher(kconf="colossus.kconf_client.hot_video_pxtr_item",
                                    item_fields=dict(photo_id="photo_id_list",
                                                      timestamp="timestamp_list",
                                                      author_id="author_id_list",
                                                      play_time="play_time_list",
                                                      duration="duration_list",
                                                      label="label_list",
                                                      tag="tag_list",
                                                      )) \
    .enrich_trigger_author_v2(
      photo_id_from="photo_id_list",
      timestamp_from="timestamp_list",
      author_id_from="author_id_list",
      play_time_from="play_time_list",
      duration_from="duration_list",
      label_from="label_list",
      export_aid_list="aid_list",
      select_recent_author_num=50,
      sample_author_num=100,
      sample_type=1,
      filter_hate_author=True,
      filter_lv=True,
      export_like_aid_list="like_aid_list",
      export_follow_aid_list="follow_aid_list",
      export_forward_aid_list="forward_aid_list",
      export_comment_aid_list="comment_aid_list",
      export_enter_profile_aid_list="enter_profile_aid_list",
      export_hate_aid_list="hate_aid_list"
    )
    ```
    """
    self._add_processor(TriggerAuthorV2Enricher(kwargs))
    return self

  def enrich_general_colossus_feature(self, **kwargs):
    """
    GeneralColossusFeatureEnricher 
    ------
    从 colossus 行为历史中抽取特征，支持通用化的 Colossus 格式，当前抽取 pid, aid, tag/cluster, play_dur, timestamp, label

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `colossus_output_type`: [string] [动态参数] colossus item 类型，支持 "sim_item", "common_item", "colossus_item", 默认 "common_item"

    `output_slot_attr`: [string] 指定 slot 输出的 common attr name

    `output_sign_attr`: [string] 指定 sign 输出的 common attr name

    `output_item_num_attr`: [string] 指定抽取 colossus item 数量输出的 common attr name

    `output_pids_attr`: [string] 指定抽取 colossus pids 输出的 common attr name

    `output_channels_attr`: [string] 指定抽取 colossus channels 输出的 common attr name

    `colossus_service_name`: [string] [动态参数] colossus服务的kess_name，默认为 "grpc_colossusSimV2"

    `colossus_photo_id_field_name`: [string] [动态参数] colossus服务中photo_id的域名, 默认为 photo_id

    `colossus_author_id_field_name`: [string] [动态参数] colossus服务中author_id的域名，默认为 author_id

    `colossus_play_time_field_name`: [string] [动态参数] colossus服务中play_time的域名，默认为 play_time

    `colossus_duration_field_name`: [string] [动态参数] colossus服务中duration的域名，默认为 duration

    `colossus_timestamp_field_name`: [string] [动态参数] colossus服务中timestamp的域名，默认为 timestamp

    `colossus_channel_field_name`: [string] [动态参数] colossus服务中channel的域名，默认为 channel

    `colossus_label_field_name`: [string] [动态参数] colossus 服务中 label 的域名，默认为 label

    `colossus_profile_stay_time_field_name`: [string] [动态参数] colossus服务中colossus_profile_stay_time的域名，默认为空

    `colossus_comment_stay_time_field_name`: [string] [动态参数] colossus服务中comment_stay_time的域名，默认为空

    `colossus_cluster_id_field_name`: [string] [动态参数] colossus服务中cluster_id的域名，默认为空

    `colossus_item_limit`: [int] [动态参数] 指定最大 item 数量限制

    `keep_past_range_second`: [int] [动态参数] 指定以当前请求时间最老的 timstamp，单位秒，默认 0

    `keep_future_range_second`: [int] [动态参数] 指定以当前请求时间最新的 timstamp，单位秒，默认 0

    `filter_channels`: [int_list] [动态参数] 要过滤 channel list，默认 [] 

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_pos`: [bool] [动态参数] 是否过滤非互动正反馈视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤比请求时刻更新的视频，训练防止穿越

    `extract_item_limit`: [int] [动态参数] 限制最大抽取 item 数量，<=0 代表不限制

    `use_cluster_id`: [bool] [动态参数] 指定是否用 cluster_id 代替 tag，对于百万序列应设置为 True

    `sort_by_timstamp`: [bool] [动态参数] 指定是否用 timestamp 对 item 进行排序，防止百万序列乱序

    `sort_desc`: [bool] [动态参数] 指定是否按照 timestamp 从高到低排序

    调用示例
    ------
    ``` python
    .enrich_general_colossus_feature(
      colossus_resp_attr="colossus_resp",
      colossus_output_type="common_item",
      output_slot_attr="colossus_slots",
      output_sign_attr="colossus_signs",
      output_item_num_attr="item_num",
      output_pids_attr="colossus_pids",
      output_channels_attr="colossus_channels",
      colossus_item_limit=10000,
      filter_ev=True,
      filter_future_ts=False,
      extract_item_limit=1000,
    )
    ```
    """
    self._add_processor(GeneralColossusFeatureEnricher(kwargs))
    return self

  def enrich_tag_item_trigger(self, **kwargs):
    """
    GenerateTagItemTriggerEnricher
    ------
    从 colossus 行为历史中选择与 target item tag 相同的 item trigger， 支持以下 PDN 的方式从每个 tag 下选择 `trigger_number` 个 photo；

    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name
    
    `target_tag_attr`: [string] 指定 target item 的 tag attr

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长观看视频

    `filter_future_ts`: [bool] [动态参数] 是否过滤穿越视频

    `export_tag_trigger_list`: [string] 指定输出 author id list 的 common attr name

    `trigger_number`: [int] [动态参数] 指定 pdn trigger 数量

    `trigger_type`: [int] [动态参数] 指定 pdn trigger 选取类型，支持 0（按 play_time），1（按完播率），2（随机），3（按概率选前三种）

    `trigger_sample_rates`: [list[double]] [动态参数] 仅当 type 3 生效，指定 type 0, type 1, type 2 的选取概率，默认 [1.0, 0.0, 0.0]

    调用示例
    ------
    ``` python
    .enrich_tag_item_trigger(
      colossus_resp_attr="colossus_resp",
      target_tag_attr="tag",
      filter_ev=True,
      filter_lv=False,
      filter_future_ts=False,
      export_tag_trigger_list="tag_item_trigger",
      trigger_number=50,
      trigger_type=3,
      trigger_sample_rates=[1.0, 0.0, 0.0],
    )
    ```
    """
    self._add_processor(GenerateTagItemTriggerEnricher(kwargs))
    return self

  def enrich_colossus_author_trigger(self, **kwargs):
    """
    ColossusAuthorTriggerEnricher
    ------
    从 colossus 行为历史中选择 author id：
    （1）将行为历史划分成 `split_window_num` 个等分窗口，并随机挑选 `pick_window_num` 个窗口进行 trigger 生成;

    （2）在每个挑选的窗口中，分别按 avg_play_time 和 photo 数量由大到小排序，取前 `window_top_author_num` 个 author_id 作为 trigger

    参数
    ------

    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `export_aid_list`: [string] 指定输出 author id list 的 common attr name

    `export_positive_aid_list`: [string] 指定输出正反馈 author id list 的 common attr name

    `split_window_num`: [int] [动态参数] 指定划分的窗口数量

    `pick_window_num`: [int] [动态参数] 指定选择生成 trigger 的窗口数量

    `window_top_author_num`: [int] [动态参数] 指定每个窗口内选择的 author id 数量

    `sample_type`: [int][动态参数] 指定 Top 选择序: 0 -> Aid 视频个数 , 1 -> Aid 累计 Play Time, 2 ->  Aid 平均 Play Complete Rate, 默认 0

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长播观看视频

    `filter_custom_ev`: [bool] [动态参数] 是否过滤自定义有效播放观看视频， 1: wtd_evtr, 2: pro_evtr, 3: wtd_evtr or pro_evtr, 0: 不过滤

    `pro_evtr_percentile`: [double] [动态参数] pro_evtr 的分位数，默认 0.50    

    `filter_future_ts`: [bool] [动态参数] 是否过滤穿越的行为历史

    调用示例
    ------
    ``` python
    .enrich_colossus_author_trigger(
      colossus_resp_attr="colossus_resp",
      export_aid_list="aid_list",
      export_positive_aid_list="positive_aid_list",
      split_window_num=5,
      pick_window_num=2,
      window_top_author_num=100,
      sample_type=1,
      filter_lv=True
    )
    ```
    """
    self._add_processor(ColossusAuthorTriggerEnricher(kwargs))
    return self

  def enrich_colossus_author_trigger_v2(self, **kwargs):
    """
    ColossusAuthorTriggerV2Enricher
    ------
    从 colossus 行为历史中选择 author id：
    (1) 选取最近观看的 `select_recent_author_num` 个 aid;
    
    (2) 此外随机采样 `sample_author_num` 个 aid;
    
    (3) 可选地选择 prefer(like、follow、forward、comment 和 has_enter_profile) 的 aid; 
    
    (4) 可选地丢弃 hate 的 aid.
    参数
    ------

    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `export_aid_list`: [string] 指定输出 author id list 的 common attr name

    `select_recent_author_num`: [int][动态参数] 指定输出最近的 aid 数量，默认 50

    `sample_author_num`: [int][动态参数] 指定随机采样的 aid 数量，默认 50

    `sample_type`: [int][动态参数] 指定采样的权重: 0 -> Uniform, 1 -> Play Time, 2 -> Play Complete Rate, 默认 0

    `filter_hate_author`: [bool][动态参数] 是否过滤 hate 的 aid，默认 True

    `filter_future_ts`: [bool] [动态参数] 是否过滤穿越的行为历史

    `filter_ev`: [bool] [动态参数] 是否过滤非有效观看视频

    `filter_lv`: [bool] [动态参数] 是否过滤非长播观看视频

    `export_like_aid_list`: [string] [动态参数] 指定输出 like aid list 的 common attr name

    `export_follow_aid_list`: [string] [动态参数] 指定输出 follow aid list 的 common attr name

    `export_forward_aid_list`: [string] [动态参数] 指定输出 forward aid list 的 common attr name

    `export_comment_aid_list`: [string] [动态参数] 指定输出 comment aid list 的 common attr name

    `export_enter_profile_aid_list`: [string] [动态参数] 指定输出 has_enter_profile aid list 的 common attr name

    `export_hate_profile_aid_list`: [string] [动态参数] 指定输出 hate aid list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_colossus_author_trigger_v2(
      colossus_resp_attr="colossus_resp",
      export_aid_list="aid_list",
      select_recent_author_num=50,
      sample_author_num=100,
      sample_type=1,
      filter_hate_author=True,
      filter_lv=True,
      export_like_aid_list="like_aid_list",
      export_follow_aid_list="follow_aid_list",
      export_forward_aid_list="forward_aid_list",
      export_comment_aid_list="comment_aid_list",
      export_enter_profile_aid_list="enter_profile_aid_list",
      export_hate_aid_list="hate_aid_list"
    )
    ```
    """
    self._add_processor(ColossusAuthorTriggerV2Enricher(kwargs))
    return self

  def batch_common_attr_by_sample_list(self, **kwargs):
    """
    BatchSampleListCommonAttrEnricher
    ------
    自定义从输入的 user_id list 中从 SampleList 服务同步查询获取用户特征（SampleAttr 格式）作为 CommonAttr 内容填入 Context 中，只适用于单值 Attr (IntAttr, DoubleAttr, StringAttr)

    参数配置
    ------
    `kess_service`: [string] [动态参数] SampleList 服务的 kess 服务名

    `query_user_ids`: [list] [动态参数] 需要查询的 user_id 列表

    `service_group`: [string] SampleList 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] SampleList 服务的超时时间，允许的设置范围为 [150, 400]，默认值 200

    `query_attrs`: [list] 显式指定需要查询的 attr。

    `output_attrs`: [list] 显式指定需要输出的 attr，默认使用 query 的 attr。

    `attr_config`: [string] 业务token用来动态配置需要请求的attr


    调用示例
    ------
    ``` python
    .custom_common_attr_by_sample_list(
      kess_service = "grpc_feasuryProxy",
      query_attrs = ["aFansCount"],
      output_attrs = ["fans_count"],
      attr_config = "AuthorRetrieve",
      query_user_ids = [2829511473,1338073757]
    )
    ```
    """
    self._add_processor(BatchSampleListCommonAttrEnricher(kwargs))
    return self

  def extract_item_multi_attr_from_common(self, **kwargs):
    """
    RecoItemMultiAttrExtractEnricher
    ------
    解析上游传递的 label 和 value 到 item attr

    参数
    ------

    `label_attr`: [string] 指定输入 label_list 的 common attr name

    `value_attr`: [string] 指定输入 value_list 的 common attr name

    `output_attr_list`: [string list] 指定输出的 item attr， 只用于 dragonfly 生成 json 时计算依赖关系

    调用示例
    ------
    ``` python
    .extract_item_multi_attr_from_common(
      label_attr="label_attr",
      value_attr="value_attr",
      output_attr_list=["pctr", "pltr", "pwtr"],
    )
    ```
    """
    self._add_processor(RecoItemMultiAttrExtractEnricher(kwargs))
    return self

  def enrich_pdn_trigger(self, **kwargs):
    """
    GenerateTriggerEnricher
    ------
    从超长 play photo 中选择 trigger：
    （1）按照 tag 分类，按 tag 内的 photo 数量从大到小排序，取前 top_tag_number 个 tag
    （2）从选中的 tag 里分别按 play_time 有大到小排序，取前 trigger_num_per_tag 个 photo 作为 trigger，同一个 tag 中不会选择 author_id 重复的 photo

    参数
    ------
    `top_tag_number`: [int] [动态参数]

    `trigger_num_per_tag`: [int] [动态参数]

    `import_pid_list`: [string] 指定输入 pid_list 的 common attr name

    `import_tag_list`: [string] 指定输入 tag_list 的 common attr name

    `import_aid_list`: [string] 指定输入 aid_list 的 common attr name

    `import_play_time_list`: [string] 指定输入 play_time_list 的 common attr name

    `export_trigger_list`: [string] 指定输出 trigger_list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_pdn_trigger(
      top_tag_number=25,
      trigger_num_per_tag=5,
      import_pid_list="pid_list",
      import_tag_list="tag_list",
      import_aid_list="aid_list",
      import_play_time_list="play_time_list",
      export_trigger_list="trigger_list"
    )
    ```
    """
    self._add_processor(GenerateTriggerEnricher(kwargs))
    return self

  def enrich_pdn_trigger_v2(self, **kwargs):
    """
    GenerateTriggerV2Enricher
    ------
    从超长 play photo 中选择 trigger：
    （1）按照 tag 分类，按 tag 内的 photo 数量从大到小排序，取前 top_tag_number 个 tag
    （2）从选中的 tag 里分别按 play_time 有大到小排序，取前 trigger_num_per_tag 个 photo 作为 trigger，同一个 tag 中不会选择 author_id 重复的 photo

    参数
    ------
    `top_tag_number`: [int] [动态参数]

    `trigger_num_per_tag`: [int] [动态参数]

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `ev`: [bool] 是否使用 effectview 过滤

    调用示例
    ------
    ``` python
    .enrich_pdn_trigger(
      top_tag_number=25,
      trigger_num_per_tag=5,
      colossus_resp_attr="colossus_resp",
      export_trigger_list="trigger_list"
    )
    ```
    """
    self._add_processor(GenerateTriggerV2Enricher(kwargs))
    return self

  def enrich_ltv_trigger(self, **kwargs):
    """
    GenerateLtvTriggerEnricher
    ------
    从超长 play photo 中选择 ltv trigger：

    参数
    ------
    `total_trigger_number`: [int] [动态参数]

    `ltv_type`: [int] [动态参数]

    `trigger_type`: [int] [动态参数]

    `trigger_past_time_range`: [int] [动态参数]

    `trigger_future_time_range`: [int] [动态参数]

    `trigger_threshold`: [float] [动态参数]

    `import_pid_list`: [string] 指定输入 pid_list 的 common attr name

    `import_tag_list`: [string] 指定输入 tag_list 的 common attr name

    `import_aid_list`: [string] 指定输入 aid_list 的 common attr name

    `import_play_time_list`: [string] 指定输入 play_time_list 的 common attr name

    `import_duration_list`: [string] 指定输入 duration_list 的 common attr name

    `import_label_list`: [string] 指定输入 label_list 的 common attr name

    `export_trigger_list`: [string] 指定输出 trigger_list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_pdn_trigger(
      top_tag_number=25,
      trigger_num_per_tag=5,
      import_pid_list="pid_list",
      import_tag_list="tag_list",
      import_aid_list="aid_list",
      import_play_time_list="play_time_list",
      export_trigger_list="trigger_list"
    )
    ```
    """
    self._add_processor(GenerateLtvTriggerEnricher(kwargs))
    return self

  def enrich_ltv_trigger_v2(self, **kwargs):
    """
    GenerateLtvTriggerV2Enricher
    ------
    从超长 play photo 中选择 ltv trigger：

    参数
    ------
    `total_trigger_number`: [int] [动态参数]

    `ltv_type`: [int] [动态参数]

    `trigger_type`: [int] [动态参数]

    `trigger_past_time_range`: [int] [动态参数]

    `trigger_future_time_range`: [int] [动态参数]

    `trigger_threshold`: [float] [动态参数]

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `export_trigger_list`: [string] 指定输出 trigger_list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_pdn_trigger(
      top_tag_number=25,
      trigger_num_per_tag=5,
      colossus_resp_attr="colossus_resp",
      export_trigger_list="trigger_list"
   V2
    ```
    """
    self._add_processor(GenerateLtvTriggerV2Enricher(kwargs))
    return self

  def enrich_ltv_trigger_colossus_v2(self, **kwargs):
    """
    GenerateLtvTriggerColossusV2Enricher
    ------
    从超长 play photo 中选择 ltv trigger：

    参数
    ------
    `total_trigger_number`: [int] [动态参数]

    `ltv_type`: [int] [动态参数]

    `trigger_type`: [int] [动态参数]

    `trigger_past_time_range`: [int] [动态参数]

    `trigger_future_time_range`: [int] [动态参数]

    `trigger_threshold`: [float] [动态参数]

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `export_trigger_list`: [string] 指定输出 trigger_list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_pdn_trigger(
      top_tag_number=25,
      trigger_num_per_tag=5,
      colossus_resp_attr="colossus_resp",
      export_trigger_list="trigger_list"
   V2
    ```
    """
    self._add_processor(GenerateLtvTriggerColossusV2Enricher(kwargs))
    return self

  def tag_selection_colossus_v3(self, **kwargs):
    """
    TagSelectionAlgorithmEnricherV3
    ------
    从play photo 历史信息中生成tag分布，输出trigger_list

    参数
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `export_tag_list`: [string] 指定输出 tag_list 的 common attr name, 默认为colossus_tag_list

    `export_tag_dist_list`: [string] 指定输出 tag_dist_list 的 common attr name, 默认为colossus_tag_dist_list

    `export_tag_size`: [string] 指定输出 tag_size 的 common attr name, 默认为colossus_tag_size

    `export_trigger_list`:[string] 指定trigger_name,

    'trigger_size':[int][动态参数] trigger num, 默认 100,

    'learning_rate':[double][动态参数] learning_rate, 默认 1.0,

    'trigger_type':[int][动态参数] trigger_type, 默认 0,
        
    调用示例
    ------
    ``` python
    .tag_selection_colossus_v3(
      colossus_resp_attr="colossus_resp",
      export_tag_list="colossus_tag_list",
      export_tag_dist_list="colossus_tag_list",
      export_tag_size="colossus_tag_size",
      trigger_size=100,
      learning_rate=0.1,
      trigger_type=0,
    )
    ```
    """
    self._add_processor(TagSelectionAlgorithmV3Enricher(kwargs))
    return self
  
  def gsu_with_average_tag(self, **kwargs):
    """
    GsuWithAverageTagEnricher
    ------
    根据(1)有效观看(2)平均tag(3)优先play_time的规则进行gsu搜索并填充到 sign/slot 特征
    参数
    ------
    `top_tag_number`: [int] [动态参数]

    `trigger_num_per_tag`: [int] [动态参数]

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `use_padding`: [bool] 当搜索结果长度不足时, 是否进行padding, 默认为False

    `save_photo_id_to_attr` [string] 填充 photo_id 的 common attr

    `save_author_id_to_attr` [string] 填充 author_id 的 common attr

    `save_duration_to_attr` [string] 填充 duration 的 common attr

    `save_play_time_to_attr` [string] 填充 play_time 的 common attr

    `save_tag_to_attr` [string] 填充 play_time 的 common attr

    调用示例
    ------
    ``` python
    .gsu_with_average_tag(
      top_tag_number=25,
      trigger_num_per_tag=5,
      colossus_resp_attr = "colossus_output",
      output_slot_attr = "gsu_slot",
      output_sign_attr = "gsu_sign",
      save_photo_id_to_attr = "pid"
    )
    ```
    """
    self._add_processor(GsuWithAverageTagEnricher(kwargs))
    return self

  def copy_common_slot_sign(self, **kwargs):
    """
    CopyCommonSlotSignEnricher
    ------
    拷贝已有的common slot/sign,构成新的mio特征(支持slot>16的feature)
    ------
    `src_mio_slot` [list] 源mio slot

    `dst_mio_slot` [list] copy后设置的新的mio slot,与mio_slot长度相同

    `slot_mapping` [dict of int strings] slot 映射关系

    `in_slot_attr` [string] 输入slot common attr

    `in_sign_attr` [string] 输入sign common attr

    `out_slot_attr` [string] copy的slot common attr

    `out_sign_attr` [string] copy的sign common attr

    调用示例
    ------
    ``` python
    .copy_common_slot_sign(
      src_mio_slot = [326, 327, 328],
      dst_mio_slot = [1001, 1002, 1003],
      slot_mapping = {
        "1001":"26"
      },
      in_slot_attr = "common_slot",
      in_sign_attr = "common_sign",
      out_slot_attr = "copy_common_slot",
      out_sign_attr = "copy_common_sign"
    )
    ```
    """
    self._add_processor(CopyCommonSlotSignEnricher(kwargs))
    return self

  def copy_item_slot_sign(self, **kwargs):
    """
    CopyItemSlotSignEnricher
    ------
    拷贝已有的item slot/sign,构成新的mio特征(支持slot>16的feature)
    ------
    `src_mio_slot` [list] 源mio slot

    `dst_mio_slot` [list] copy后设置的新的mio slot,与mio_slot长度相同

    `slot_mapping` [dict of int strings] slot 映射关系

    `in_slot_attr` [string] 输入slot item attr

    `in_sign_attr` [string] 输入sign item attr

    `out_slot_attr` [string] copy的slot item attr

    `out_sign_attr` [string] copy的sign item attr

    调用示例
    ------
    ``` python
    .copy_item_slot_sign(
      src_mio_slot = [26],
      dst_mio_slot = [88],
      slot_mapping = {
        "88":"36"
      },
      in_slot_attr = "item_slot",
      in_sign_attr = "item_sign",
      out_slot_attr = "copy_item_slot",
      out_sign_attr = "copy_item_sign"
    )
    ```
    """
    self._add_processor(CopyItemSlotSignEnricher(kwargs))
    return self

  def update_concurrence_kv(self, **kwargs):
    """
    UpdateConcurrenceKvObserver
    ------
    从 kv 服务读取 photo pair 的共现权重，计算新的权重，然后写入 btq
    ------
    `import_common_action_pid_list` [string] 指定输入 action pid list 的 common attr name

    `import_item_action_pid_list` [string] 指定输入 action pid list 的 item attr name, 如果同时配置，common action pid 优先

    `import_item_pid` [string] 指定输入 item pid 的 attr name 

    `import_item_weight` [string] 指定输入 item weight 的 attr name

    `kess_service`: [string] [动态参数] KV 服务的 kess 服务名

    `shard_prefix`: [string] KV 服务的 shard 前缀，默认值为 "s"

    `shard_num`: [int] 如果 KV 服务端是分片服务，通过该项指定分片数，默认值为 1（未分片）

    `service_group`: [string] KV 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 KV 服务的超时时间，默认值为 300
    
    `queue_name_prefix`: [string] 输出权重数据的 btq topic 前缀

    `bucket`: [int] [动态参数] 业务所属 bucket，默认值为 0

    `concurrence_pair_decay`: [double] [动态参数] 更新共现权重时使用的衰减参数，默认值为 1.0

    `concurrence_pair_step`: [double] [动态参数] 更新共现权重时使用的衰减参数，默认值为 1.0

    调用示例
    ------
    ``` python
    .update_concurrence_kv(
      import_common_action_pid_list="action_pid_list",
      import_item_pid="pid",
      import_item_weight="weight",
      kess_service="grpc_xxx",
      shard_num=2,
      timeout_ms=50,
      queue_name_prefix="concurrence_btq",
      concurrence_pair_decay=1.0,
      concurrence_pair_step=1.0 
    )
    ```
    """
    self._add_processor(UpdateConcurrenceKvObserver(kwargs))
    return self

  def gsu_with_same_tag(self, **kwargs):
    """
    GsuWithSameTagEnricher
    ------
    从 colossus 行为中找到与 target item 相同河图标签的 photo ，填充到 item attr 中
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `import_item_pid_attr`: [string] 输入 target photo pid item attr

    `import_item_tag_list_attr`: [string] 输入 target photo 的 hetu tag list item attr

    `export_same_tag_item_pid_list_attr`: [string] 输出相同tag的检索结果，item attr

    `limit_num`: [int] 动态参数，检索长度

    调用示例
    ------
    ``` python
    .gsu_with_same_tag(
      colossus_resp_attr="colossus_output",
      import_item_pid_attr="item_pid",
      import_item_tag_list_attr="item_tag_list",
      export_same_tag_item_pid_list_attr="same_tag_pid_list",
      limit_num=20
    )
    ```
    """
    self._add_processor(GsuWithSameTagEnricher(kwargs))
    return self

  def get_concurrence_kv(self, **kwargs):
    """
    GetConcurrenceKvEnricher
    ------
    sync方式从 kv 服务读取 photo pair 的共现权重
    ------
    `kess_service`: [string] [动态参数] KV 服务的 kess 服务名

    `shard_prefix`: [string] KV 服务的 shard 前缀，默认值为 "s"

    `shard_num`: [int] 如果 KV 服务端是分片服务，通过该项指定分片数，默认值为 1（未分片）

    `service_group`: [string] KV 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 KV 服务的超时时间，默认值为 300

    `bucket`: [int] [动态参数] 业务所属 bucket，默认值为 0

    `import_common_pid_list` [string] 指定输入 action pid list 的 common attr name

    `import_item_pid` [string] 指定输入 item pid 的 attr name

    `export_concurrence_weight` [string] 指定输出查询结果的 item attr name

    调用示例
    ------
    ``` python
    .get_concurrence_kv(
      kess_service="grpc_xxx",
      shard_num=2,
      timeout_ms=50,
      import_common_pid_list="action_pid_list",
      import_item_pid="pid",
      export_concurrence_weight="weight"
    )
    ```
    """
    self._add_processor(GetConcurrenceKvEnricher(kwargs))
    return self

  def pdn_merchant_live_gsu_with_tag(self, **kwargs):
    """
    PDNMerchantLiveGsuWithTagEnricher
    ------
    从colossus中提取用户的历史电商直播观看行为，并拼接tag，生成slot/sign/tag，用于PDN模型训练和offline runner
    ------
    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [int] [动态参数]

    `kess_service`: [string] 电商 tag embedding server 服务名称

    `shards`: [int] 电商 tag embedding server 分片数目

    `save_live_id_to_attr` [string] 填充 photo_id 的 common attr

    `save_author_id_to_attr` [string] 填充 author_id 的 common attr

    `save_tag_to_attr` [string] 填充 tag 的 common attr

    `save_play_time_to_attr` [string] 填充 play_time 的 common attr

    `save_complete_tag_to_attr` [string] 填充一二三集完整 tag 的 common attr

    调用示例
    ------
    ```python
    .colossus(
      service_name='grpc_colossusLiveItemV3',
      output_attr='colossus_resp',
      client_type='common_item_client') \
    .pdn_merchant_live_gsu_with_tag(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=50,
      kess_service='MerchantLiveModelFeatureV1',
      shards=2,
      save_author_id_to_attr="colossus_author_id",
      save_tag_to_attr="colossus_tag")
    ```
    """
    self._add_processor(PDNMerchantLiveGsuWithTagEnricher(kwargs))
    return self

  def get_pdn_merchant_live_average_tag_trigger(self, **kwargs):
    """
    PDNMerchantLiveAverageTagTriggerEnricher
    """
    self._add_processor(PDNMerchantLiveAverageTagTriggerEnricher(kwargs))
    return self

  def pdn_live_gsu_with_cluster(self, **kwargs):
    """
    PDNLiveGsuWithClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `hetu_cluster_config_key_name`: [string] cluster kconf config key name

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [int] [动态参数] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `output_cluster_attr`: [string] 填充 item 的 cluster 属性

    `output_history_live_id_attr` : [string] 填充 colossus photo_id 的 item attr

    `output_history_author_id_attr` : [string] 填充 colossus author_id 的 item attr

    `output_history_cluster_id_attr` : [string] 填充 colossus cluster_id 的 item attr

    `output_history_play_time_attr` : [string] 填充 colossus play_time 的 item attr

    示例
    ------
    ``` python
    .pdn_live_gsu_with_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      output_history_live_id_attr = 'history_live_id',
                      output_history_author_id_attr = 'history_author_id',
                      output_history_cluster_id_attr = 'history_cluster_id',
                      output_history_play_time_attr = 'history_play_time'
                      )
    ```
    """
    self._add_processor(PDNLiveGsuWithClusterEnricher(kwargs))
    return self

  def retrieve_from_sample_join_api_request(self, **kwargs):
    """
    SampleJoinApiRequestRetriever
    ------
    从 SampleJoinApiRequest 触发出样本。注意这个 processor 会修改 user id、request time。

    参数配置
    ------
    `from_extra_var`: [string] 从给定的 Extra Var 读取 SampleJoinApiRequest 

    `reason`: [int] 触发的 reason，默认为 0

    `output_request_timestamp_ms`: [int] 提取 request timestamp

    `extract_item_attrs`: [list] 提取指定的 item attrs，如果列表为空默认不提取任何 item attr

    调用示例
    ------
    ``` python
    .retrieve_from_sample_join_api_request(
      from_extra_var="sample_join_api_request",
      output_request_timestamp_ms="output_request_timestamp_ms",
      extract_item_attrs=["pId", "aId"])
    ```
    """
    self._add_processor(SampleJoinApiRequestRetriever(kwargs))
    return self


  def pdn_live_get_trigger(self, **kwargs):
    """
    PDNLiveGetTriggerEnricher
    ------
    从colossus中提取用户的历史直播观看行为，生成slot/sign/tag
    ------
    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [int] [动态参数]

    `ev_playtime`: [int] [动态参数] 有效观看的最低时长

    `save_live_id_to_attr` [string] 填充 photo_id 的 common attr

    `save_author_id_to_attr` [string] 填充 author_id 的 common attr

    `save_cluster_id_to_attr` [string] 填充 cluster_id 的 common attr

    `save_play_time_to_attr` [string] 填充 play_time 的 common attr

    调用示例
    ------
    ```python
    .pdn_live_get_trigger(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=50,
      save_author_id_to_attr="colossus_author_id")
    ```
    """
    self._add_processor(PDNLiveGetTriggerEnricher(kwargs))
    return self

  def pdn_live_get_trigger_v1(self, **kwargs):
    """
    PDNLiveGetTriggerEnricherV1
    ------
    从colossus中提取用户的历史直播观看行为，生成slot/sign/tag
    ------
    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num`: [int] [动态参数]

    `ev_playtime`: [int] [动态参数] 有效观看的最低时长

    `save_live_id_to_attr` [string] 填充 photo_id 的 common attr

    `save_author_id_to_attr` [string] 填充 author_id 的 common attr

    `save_cluster_id_to_attr` [string] 填充 cluster_id 的 common attr

    `save_play_time_to_attr` [string] 填充 play_time 的 common attr

    调用示例
    ------
    ```python
    .pdn_live_get_trigger_v1(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=50,
      save_author_id_to_attr="colossus_author_id")
    ```
    """
    self._add_processor(PDNLiveGetTriggerV1Enricher(kwargs))
    return self

  def fetch_session_temporal_interest(self, **kwargs):
    """
    SessionTemporalInterestEnricher
    ------
    从 colossus 提取长期兴趣，并按照时序划分成窗口，并对每个窗口内的 photo list 抽取 pid，aid，tag 等特征，并对每个窗口内的 photo 按照
    1级 HetuTag 统计互动统计特征（like、follow 等)
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_stats_attr`: [string] 分窗口 stats 统计信息输出 attr name. common 类型

    `split_size`: [int] 均等划分的窗口个数（大于 0）

    `split_sections`: [double list] 按照比例划分窗口，输入为 list 类型，不要求比例之和为 1.0 (不可与 `split_size` 同时设置)

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `mio_slot_id_start_num`: [int] 输出特征的 mio_slot_id 开始数值, 按 slot_offset 累加, 必须>1000, 默认为2001

    `slot_offset`: [int] 输出特征的 mio_slot_id 开始数值, 累计数值, 必须>=1, 默认为100

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越, playground中测试必须为False, 否则取不到数据

    `deduplicate`: [bool] [动态参数] 是否过滤重复 photo，默认为 False

    `ev`: [bool] 是否使用 effectview 过滤，默认为 False

    `lv`: [bool] 是否使用 longview 过滤，默认为 False，与 `ev` 不可同时为 True

    `is_split_by_num`: [bool] 是否按数量切分，默认为True

    `is_split_by_time`: [bool] 是否按时间段切分，默认为 False，与 `is_split_by_num` 不可同时为True

    `split_by_hour`: [int] 按时间段切分时，几个小时为一段，默认为 1，必须能被 24 整除


    调用示例
    ------
    ```python
    .fetch_session_temporal_interest(
      colossus_resp_attr='colossus_resp',
      output_slot_attr='sess_slot',
      output_sign_attr='sess_sign',
      output_stats_attr='stats',
      split_size=10,
      limit_num=5000,
      mio_slot_id_start_num=2001,
      slot_offset=1,      
      history_start_from_recent=False,
      enable_ts_filter=False, # always false in dragonfly playground
      ev=False,
      lv=False,
      deduplicate=False,
      is_split_by_num=False,
      is_split_by_time=True,
      split_by_hour=True,
      split_hour=1,
      )
    ```
    """
    self._add_processor(SessionTemporalInterestEnricher(kwargs))
    return self
  
  def fetch_post_rank_pxtr(self, **kwargs):
    """
    RetrievalPostRankEnricher
    ------
    对已有的多个预估值计算给定权重下的加权 pxtr 得分，用于 i2i/u2u2i 召回的双塔打分
    ------
    `all_pxtr_labels`: [list[string]] 所有可能依赖的 pxtr, 必须有且仅包含 all_pxtr_labels 中的 label

    `mix_format`: [int][动态参数] 计算 pxtr 的公式； 0: 加法, 1: 乘法, 默认为 0
    
    `pxtr_labels_format`: [int] [动态参数] pxtr_labels 格式： 默认 0: 从 pxtr_labels 中获取; 1: 从 pxtr_labels_str 中获取

    `pxtr_labels_seperator`: [string] [动态参数] 只有当 pxtr_labels_format 为 1 时成立，默认为 「,」, 按此分隔 pxtr_labels_str

    `pxtr_labels_str`: [string][动态参数] pxtr_labels 对应 string 格式, 按 pxtr_labels_seperator 拼接

    `pxtr_weights_seperator`: [string][动态参数] 只有当 pxtr_labels_format 为 1 时成立，默认为 「,」, 按此分隔 pxtr_weights_str

    `pxtr_weights_str`: [string][动态参数] pxtr weights 对应 weights 格式，按 pxtr_labels_seperator 拼接

    `pxtr_labels`: [list[string]][动态参数] 计算 pxtr 的 item attr 列表， 使用 pxtr_labels_str 时候, 置空即可

    `pxtr_weights`: [list[double]][动态参数] item attr 对应权重


    `pxtr_attr`: [string] pxtr 输出特征的名称

    调用示例
    ------
    ```python
    .fetch_post_rank_pxtr(
      all_pxtr_labels=["ctr", "lvr", "vtr", "u2a_ctr", "abs_lvtr",],
      mix_format=0,
      pxtr_labels_format=0,
      pxtr_labels=["ctr", "lvr", "vtr", "u2a_ctr", "abs_lvtr",],
      pxtr_weights='{{post_rank__pxtr_weights}}',
      pxtr_attr='pxtr',
      )
    ```
    """
    self._add_processor(RetrievalPostRankEnricher(kwargs))
    return self


  def generate_random_uniform_attr(self, **kwargs):
    """
    GenerateRandomUnifromAttrEnricher
    ------
    生成均匀分布随机数
    ------
    `begin`: [double] 最小值

    `end`: [double] 最大值

    `is_common_attr`: [bool] 是否 common

    `export_random_uniform_attr`: [string] 输出特征的名称

    """
    self._add_processor(GenerateRandomUnifromAttrEnricher(kwargs))
    return self


  def decide_dynamic_downgrade(self, **kwargs):
    """
    DecideDynamicDowngradeEnricher
    ------
    根据降级概率和当前小时决定是否降级
    ------
    `import_downgrade_prob_double_list_attr`: [list[double]] 概率列表

    `export_is_downgraded_attr`: [string] 输出特征的名称

    """
    self._add_processor(DecideDynamicDowngradeEnricher(kwargs))
    return self

  def fetch_new_reason(self, **kwargs):
    """
    RetrievalNewReasonEnricher
    ------
    动态权重赋值新reason
    ------
    `reason_list`: [list[int]][动态参数] 计算pxtr的item attr 列表

    `reason_nums`: [list[int]][动态参数] 空list默认为等权重生成, 长度需要和 reason_list 一致, 对应每个 reason 的 quota 数量

    `reset_strategy`: [int][动态参数] 默认 1，全局 reset; 2 为 inplace reset, 只把多召回的 reason reset 到召不满的 reason 上; 3 为 zigzag reset

    `new_reason_attr`: [string] 新reason对应attr name
    
    调用示例
    ------
    ```python
    .fetch_new_reason(
      reason_list=[],
      reason_nums=[],
      reset_strategy=2,
      new_reason_attr='final_reason',
      )
    ```
    """
    self._add_processor(RetrievalNewReasonEnricher(kwargs))
    return self


  def fetch_session_tag_interest(self, **kwargs):
    """
    SessionTagInterestEnricher
    ------
    从 colossus 提取长期兴趣，并按照 1 级 HetuTag (40 个）进行切分填充 pid, aid 等特征, 并统计每个 Tag 对应的 
    photo list 的互动统计特征填充到对应的 output_XXX_attr 中
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_ltr_attr`: [string] ltr list 分 tag 输出 attr name. common 类型

    `output_wtr_attr`: [string] wtr list 分 tag 输出 attr name. common 类型

    `output_htr_attr`: [string] htr list 分 tag 输出 attr name. common 类型

    `output_ftr_attr`: [string] ftr list 分 tag 输出 attr name. common 类型

    `output_cmtr_attr`: [string] cmtr list 分 tag 输出 attr name. common 类型

    `output_eptr_attr`: [string] eptr list 分 tag 输出 attr name. common 类型

    `output_comintr_attr`: [string] comintr list 分 tag 输出 attr name. common 类型

    `output_commntr_attr`: [string] commntr list 分 tag 输出 attr name. common 类型

    `output_avg_playtime_attr`: [string] 数量分布 list 分 tag  输出 attr name. common 类型    

    `output_pos_act_dist_attr`: [string] 数量分布 list 分 tag  输出 attr name. common 类型    

    `output_tag_num_dist_attr`: [string] 数量分布 list 分 tag  输出 attr name. common 类型    

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `mio_slot_id_start_num`: [int] 输出特征的 mio_slot_id 开始数值, 按 slot_offset 累加, 必须>1000, 默认为2001

    `slot_offset`: [int] 输出特征的 mio_slot_id 开始数值, 累计数值, 必须>=1, 默认为100    

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越, playground中测试必须为False, 否则取不到数据

    `deduplicate`: [bool] [动态参数] 是否过滤重复 photo，默认为 False
    
    `ev`: [bool] 是否使用 effectview 过滤，默认为 False

    `lv`: [bool] 是否使用 longview 过滤，默认为 False，与 `ev` 不可同时为 True


    调用示例
    ------
    ```python
    .fetch_session_tag_interest(
      colossus_resp_attr='colossus_resp',
      output_slot_attr='tag_slot',
      output_sign_attr='tag_sign',
      output_ltr_attr='ltr',
      output_wtr_attr='wtr',
      output_htr_attr='htr',
      output_ftr_attr='ftr',
      output_cmtr_attr='cmtr',
      output_eptr_attr='eptr',
      output_comintr_attr='comintr',
      output_avg_playtime_attr='avg_playtime',
      output_pos_act_dist_attr='pos_act',
      output_tag_num_dist_attr='tag_num_dist',
      limit_num=100,
      mio_slot_id_start_num=2001,
      slot_offset=100,
      history_start_from_recent=False,
      enable_ts_filter=False, # always false in dragonfly playground
      ev=True,
      lv=False,
    )
    ```
    """
    self._add_processor(SessionTagInterestEnricher(kwargs))
    return self
  
  def fetch_life_long_temporal_interest(self, **kwargs):
    """
    LifeLongTemporalInterestEnricher
    ------
    从 colossus 提取长期兴趣，并按照时序划分成窗口，并对每个窗口内的 photo list 抽取 pid，aid，tag 等特征，并对每个窗口内的 photo 按照
    1级 HetuTag 统计互动统计特征（like、follow 等)
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_stats_attr`: [string] 分窗口 stats 统计信息输出 attr name. common 类型

    `split_size`: [int] 均等划分的窗口个数（大于 0）

    `split_sections`: [double list] 按照比例划分窗口，输入为 list 类型，不要求比例之和为 1.0 (不可与 `split_size` 同时设置)

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `mio_slot_id_start_num`: [int] 输出特征的 mio_slot_id 开始数值, 按 slot_offset 累加, 必须>1000, 默认为2001

    `slot_offset`: [int] 输出特征的 mio_slot_id 开始数值, 累计数值, 必须>=1, 默认为100

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越

    `deduplicate`: [bool] [动态参数] 是否过滤重复 photo，默认为 False

    `ev`: [bool] 是否使用 effectview 过滤，默认为 False

    `lv`: [bool] 是否使用 longview 过滤，默认为 False，与 `ev` 不可同时为 True


    调用示例
    ------
    ```python
    .fetch_life_long_temporal_interest(
      colossus_resp_attr='colossus_resp',
      output_slot_attr='slot',
      output_sign_attr='sign',
      output_stats_attr='stats',
      split_size=3,
      limit_num=5000,
      history_start_from_recent=False,
      enable_ts_filter=True,
      mio_slot_id_start_num=2001,
      slot_offset=1,
      ev=False,
      lv=False,
      deduplicate=False,
      )
    ```
    """
    self._add_processor(LifeLongTemporalInterestEnricher(kwargs))
    return self

  def fetch_life_long_tag_interest(self, **kwargs):
    """
    LifeLongTagInterestEnricher
    ------
    从 colossus 提取长期兴趣，并按照 1 级 HetuTag (40 个）进行切分填充 pid, aid 等特征, 并统计每个 Tag 对应的 
    photo list 的互动统计特征填充到对应的 output_XXX_attr 中
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `output_ltr_attr`: [string] ltr list 分 tag 输出 attr name. common 类型

    `output_wtr_attr`: [string] wtr list 分 tag 输出 attr name. common 类型

    `output_htr_attr`: [string] htr list 分 tag 输出 attr name. common 类型

    `output_ftr_attr`: [string] ftr list 分 tag 输出 attr name. common 类型

    `output_cmtr_attr`: [string] cmtr list 分 tag 输出 attr name. common 类型

    `output_eptr_attr`: [string] eptr list 分 tag 输出 attr name. common 类型

    `output_comintr_attr`: [string] comintr list 分 tag 输出 attr name. common 类型

    `output_commntr_attr`: [string] commntr list 分 tag 输出 attr name. common 类型

    `output_tag_num_dist_attr`: [string] 数量分布 list 分 tag  输出 attr name. common 类型

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越

    `deduplicate`: [bool] [动态参数] 是否过滤重复 photo，默认为 False

    `mio_slot_id_start_num`: [int] 输出特征的 mio_slot_id 开始数值, 按 slot_offset 累加, 必须>1000, 默认为2001

    `slot_offset`: [int] 输出特征的 mio_slot_id 开始数值, 累计数值, 必须>=1, 默认为100
    
    `ev`: [bool] 是否使用 effectview 过滤，默认为 False

    `lv`: [bool] 是否使用 longview 过滤，默认为 False，与 `ev` 不可同时为 True


    调用示例
    ------
    ```python
    .fetch_life_long_tag_interest(
      colossus_resp_attr='colossus_resp',
      output_slot_attr='slot',
      output_sign_attr='sign',
      output_ltr_attr='ltr',
      output_wtr_attr='wtr',
      output_htr_attr='htr',
      output_ftr_attr='ftr',
      output_cmtr_attr='cmtr',
      output_eptr_attr='eptr',
      output_comintr_attr='comintr',
      output_tag_num_dist_attr='tag_num_dist',
      limit_num=5000,
      history_start_from_recent=False,
      enable_ts_filter=True,
      ev=False,
      lv=False,
      mio_slot_id_start_num=2001,
      slot_offset=1,
      deduplicate=False,
      )
    ```
    """
    self._add_processor(LifeLongTagInterestEnricher(kwargs))
    return self

  def fetch_life_long_u2u_interest(self, **kwargs):
    """
    LifeLongU2UInterestEnricher
    ------
    从 colossus 提取长期兴趣
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越

    `ev`: [bool] 是否使用 effectview 过滤

    `is_pred`: [bool] true 为预估模式，只 parse 预估塔的特征

    `extract_aid`: [bool] 是否抽取 aid 特征，默认为 false

    `extract_tag`: [bool] 是否抽取 tag 特征，默认为 false

    `slot_split_num`: [int] pid 特征 slot 分裂段数

    `slot_split_offset`: [int] pid 特征 slot 分裂 offset

    调用示例
    ------
    ```python
    .fetch_life_long_u2u_interest(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      history_start_from_recent=True,
      enable_ts_filter=True,
      ev=True,
      extract_aid=True,
      extract_tag=True,
      slot_split_num=4,
      slot_split_offset=100,
      is_pred=False)
    ```
    """
    self._add_processor(LifeLongU2UInterestEnricher(kwargs))
    return self

  def evaluate_life_long_u2u_interest(self, **kwargs):
    """
    LifeLongU2UInterestEvaluateEnricher
    ------
    评估 U2U 中间效果指标
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `similar_user_colossus_resp_attr`: [string] similar_user_colossus_resp 输入的 attr

    `output_attr`: [string] 评估输出 attr name

    `ev`: [bool] 是否使用 effectview 过滤

    调用示例
    ------
    ```python
    .evaluate_life_long_u2u_interest(
      colossus_resp_attr='colossus_resp',
      similar_user_colossus_resp_attr='colossus_resp',
      output_attr='evaluate_results',
      ev=True,
      )
    ```
    """
    self._add_processor(LifeLongU2UInterestEvaluateEnricher(kwargs))
    return self

  def fetch_life_long_u2u_live_interest(self, **kwargs):
    """
    LifeLongU2ULiveInterestEnricher
    ------
    从 colossus 提取长期兴趣
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越

    `effective_playtime`: [bool] playtime > effective_playtime 判定为正行为

    `is_pred`: [bool] true 为预估模式，只 parse 预估塔的特征

    `extract_aid`: [bool] 是否抽取 aid 特征，默认为 false

    `extract_tag`: [bool] 是否抽取 tag 特征，默认为 false

    `slot_split_num`: [int] pid 特征 slot 分裂段数

    `slot_split_offset`: [int] pid 特征 slot 分裂 offset

    调用示例
    ------
    ```python
    .fetch_life_long_u2u_live_interest(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      history_start_from_recent=True,
      enable_ts_filter=True,
      effective_playtime=60,
      extract_aid=True,
      extract_tag=True,
      slot_split_num=4,
      slot_split_offset=100,
      is_pred=False)
    ```
    """
    self._add_processor(LifeLongU2ULiveInterestEnricher(kwargs))
    return self

  def evaluate_life_long_u2u_live_interest(self, **kwargs):
    """
    LifeLongU2ULiveInterestEvaluateEnricher
    ------
    评估 Live U2U 中间效果指标
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `similar_user_colossus_resp_attr`: [string] similar_user_colossus_resp 输入的 attr

    `output_attr`: [string] 评估输出 attr name，当 enable_string_result_building 为 True 时才输出

    `output_query_user_action_len_attr`: [string] 用于 perf 的 query user 正行为个数的输出 attr name

    `output_similar_user_action_len_attr`: [string] 用于 perf 的 similar users 正行为平均个数的输出 attr name

    `output_lid_hit_attr`: [string] 用于 perf 的 lid 重合数的输出 attr name

    `output_lid_js_attr`: [string] 用于 perf 的 lid jaccard similarity 的输出 attr name

    `output_aid_hit_attr`: [string] 用于 perf 的 aid 重合数的输出 attr name

    `output_aid_js_attr`: [string] 用于 perf 的 aid jaccard similarity 的输出 attr name

    `effective_playtime`: [int] playtime > effective_playtime 判定为正行为

    `enable_filter_merchant_item`: [bool] 是否过滤掉电商直播，依赖 colossus_v4=True

    `click_as_positive`: [bool] 使用click 而不是 lv 作为正行为判定，依赖 colossus_v4=True

    `colossus_v4`: [bool] 是否是 colossus v4 格式数据

    `enable_string_result_building`: [bool] 是否输出长字符串结果到 output_attr 用于 print，关闭不影响 perf

    调用示例
    ------
    ```python
    .evaluate_life_long_u2u_live_interest(
      colossus_resp_attr="colossus_resp",
      similar_user_colossus_resp_attr="similar_user_colossus_resp",
      output_attr="uu_eval",
      output_query_user_action_len_attr="query_user_action_len",
      output_similar_user_action_len_attr="similar_user_action_len",
      output_lid_hit_attr="lid_hit",
      output_lid_js_attr="lid_js",
      output_aid_hit_attr="aid_hit",
      output_aid_js_attr="aid_js",
      effective_playtime=60,
      enable_filter_merchant_item=True,  # only work when colossus_v4=True
      click_as_positive=True,  # only work when colossus_v4=True
      colossus_v4=False,
      enable_string_result_building=False,
      )
    ```
    """
    self._add_processor(LifeLongU2ULiveInterestEvaluateEnricher(kwargs))
    return self

  def fetch_life_long_u2u_common_interest(self, **kwargs):
    """
    LifeLongU2UCommonInterestEnricher
    ------
    从 colossus 提取长期兴趣，通用算子
    ------

    `item_list_from_attr`: [string] 从哪个 common attr 里获取 item key list

    `aid_attr`: [string] author id item attr name

    `tag_attr`: [string] tag item attr name

    `ts_attr`: [string] timestamp item attr name

    `play_time_attr`: [string] 播放时长 item attr name

    `auto_play_time_attr`: [string] 直播间外播放时长 item attr name

    `label_attr`: [string] label item attr name，目前用于判断是否是电商直播

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数] （正）行为序列使用数量

    `history_start_from_recent`: [bool] [动态参数] 是否从用户近期行为开始计数

    `enable_ts_filter`: [bool] [动态参数] 是否过滤掉请求时刻以后的用户行为，防止特征穿越

    `enable_filter_merchant_item`: [bool] [动态参数] 是否过滤掉直播中的电商直播

    `effective_playtime`: [bool] playtime > effective_playtime 判定为正行为

    `is_pred`: [bool] true 为预估模式，只 parse 预估塔的特征

    `extract_aid`: [bool] 是否抽取 aid 特征，默认为 false

    `extract_tag`: [bool] 是否抽取 tag 特征，默认为 false

    `click_as_positive`: [bool] 是否用外流 click 作为正行为判定，默认用长播判定

    `slot_split_num`: [int] pid 特征 slot 分裂段数

    `slot_split_offset`: [int] pid 特征 slot 分裂 offset

    调用示例
    ------
    ```python
    .fetch_life_long_u2u_common_interest(
      item_list_from_attr='colossus_iid_common',
      aid_attr='colossus_aid_attr',
      tag_attr='colossus_tag_attr',
      ts_attr='colossus_ts_attr',
      play_time_attr='colossus_play_time_attr',
      auto_play_time_attr='colossus_auto_play_time_attr',
      label_attr='colossus_label_attr',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      history_start_from_recent=True,
      enable_ts_filter=True,
      enable_filter_merchant_item=True,
      effective_playtime=60,
      extract_aid=True,
      extract_tag=True,
      click_as_positive=False,
      slot_split_num=4,
      slot_split_offset=100,
      is_pred=False)
    ```
    """
    self._add_processor(LifeLongU2UCommonInterestEnricher(kwargs))
    return self

  def get_live_user_type(self, **kwargs):
    """
    LiveUserTypeEnricher
    ------
    从直播 colossus v4 的数据判断直播用户类型
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_user_type_attr`: [string] live_user_type 输出 attr name

    调用示例
    ------
    ```python
    .get_live_user_type(
      colossus_resp_attr='colossus_resp',
      output_user_type_attr='live_user_type',
    )
    ```
    """
    self._add_processor(LiveUserTypeEnricher(kwargs))
    return self

  def fetch_long_term_interest(self, **kwargs):
    """
    LongTermInterestEnricher
    ------
    从 colossus 中提取长期兴趣
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数]

    `ev`: [bool] 是否使用 effectview 过滤

    调用示例
    ------
    ```python
    .fetch_long_term_interest(
      colossus_resp_attr='colossus_resp',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      ev = True)
    ```
    """
    self._add_processor(LongTermInterestEnricher(kwargs))
    return self

  def fetch_short_term_interest(self, **kwargs):
    """
    ShortTermInterestEnricher
    ------
    从 user_info 中提取短期兴趣
    ------
    `user_info_attr`: [string] user_info 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数]

    `ev`: [bool] 是否使用 effectview 过滤

    调用示例
    ------
    ```python
    .fetch_short_term_interest(
      user_info_attr='user_info',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      ev = True)
    ```
    """
    self._add_processor(ShortTermInterestEnricher(kwargs))
    return self

  def fetch_union_interest(self, **kwargs):
    """
    UnionInterestEnricher
    ------
    从 colossus、user_info 中提取所有兴趣
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `user_info_attr`: [string] user_info 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数]

    `ev`: [bool] 是否使用 effectview 过滤

    调用示例
    ------
    ```python
    .fetch_union_interest(
      colossus_resp_attr='colossus_resp',
      user_info_attr='user_info',
      output_sign_attr='sign',
      output_slot_attr='slot',
      limit_num=5000,
      ev = True)
    ```
    """
    self._add_processor(UnionInterestEnricher(kwargs))
    return self

  def target_item_tag_extract(self, **kwargs):
    """
    TargetItemTagEnricher
    ------
    将 target_item 侧的河图 tag 进行特征抽取
    ------
    `hetu_level_one_attr`: [string] hetu_level_one 输入的 attr

    `hetu_level_two_attr`: [string] hetu_level_two 输入的 attr

    `hetu_level_three_attr`: [string] hetu_level_three 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. item 类型

    `output_sign_attr`: [string] sign 输出 attr name. item 类型

    调用示例
    ------
    ```python
    .target_item_tag_extract(
      hetu_level_one_attr='hetu_level_one',
      hetu_level_two_attr='hetu_level_two',
      hetu_level_three_attr='hetu_level_three',
      output_sign_attr='target_item_tag_sign',
      output_slot_attr='target_item_tag_slot'
    )
    ```
    """
    self._add_processor(TargetItemTagEnricher(kwargs))
    return self

  def lr_model_retrieve(self, **kwargs):
    """
    LrModelRetriever
    ------
    LR 模型召回
    ------
    `kess_service`: [string] [dynamic]

    `result_num`: [int] [dynamic]

    `timeout_ms`: [int] [dynamic] default = 100

    `service_group`: [string] default = "PRODUCTION"

    `reason`: [int] default = 0

    `user_info_attr_name`: [string]

    调用示例
    ------
    .lr_model_retrieve(
      kess_service = 'grpc_xxx',
      result_num = 1500,
      reason = 191,
      user_info_attr_name = 'user_info',
    )
    """
    self._add_processor(LrModelRetriever(kwargs))
    return self

  def get_search_intent(self, **kwargs):
    """
    SearchIntentEnricher
    ------
    请求搜索意图识别模型
    ------
    调用示例
    ------
    .get_search_intent(
      kess_service = 'xxx',
      query = 'xxx',
      output_preds_attrs = 'xxx',
      output_probs_attrs = 'xxx',
      top_k = 'xxx'
    )
    """
    self._add_processor(SearchIntentEnricher(kwargs))
    return self

  def get_photo_hetu_tag(self, **kwargs):
    """
    PhotoHetuTagEnricher
    ------
    请求PhotoHetuTag服务
    ------
    调用示例
    ------
    .get_photo_hetu_tag(
      kess_service = 'xxx',
      input_photo_ids = 'xxx',
      output_hetu_tag_one_ids = 'xxx',
      output_hetu_tag_two_ids = 'xxx',
      output_hetu_tag_three_ids = 'xxx',
      output_hetu_face_ids = 'xxx'
    )
    """
    self._add_processor(PhotoHetuTagEnricher(kwargs))
    return self

  def extract_sim_lr_feature(self, **kwargs):
    self._add_processor(SIMLRFeatureEnricher(kwargs))
    return self

  def enrich_similar_photo(self, **kwargs):
    self._add_processor(SimilarPhotoEnricher(kwargs))
    return self

  def pdn_retrieve_by_ann_embedding(self, **kwargs):
    """
    PdnEmbeddingRetriever
    ------
    从通用 Embedding 服务进行相似 item 召回

    参数配置
    ------
    `reason`: [int] 召回原因

    `kess_service`: [string] [动态参数] Embedding 服务的 kess 服务名

    `shard_num`: [int] 如果 ann 服务端是分片服务，通过该项指定分片数，默认为 1（未分片）

    `space`: [string] 如果 ann 服务端是分片服务，space 需要和 server 端做相同配置，默认为 cosine

    `service_group`: [string] Embedding 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] [动态参数] 请求 embedding 服务的超时时间，默认值为 300

    `items_from_attr`: [list] 从哪些 common_attr 选取目标 item 发送给 embedding 服务，支持 int/int_list attr，
      NN retr 流程需要每个 src embedding 都有对应 id, items_from_attr 长度必须与 embeddings_from_attr 内 embedding 数量一致，为对应的 ID 列表

    `user_key_for_embedding`: [string] 选配项，使用 user key 作为 src_item_id 从 src_bucket 获取 embedding，用于 u2i 和 u2u 召回，可选值：
      1. "uid_only": 使用 uid 作为 user key；
      2. "did_only": 使用 did 作为 user key；
      3. "uid_or_did"：登录用户（uid > 0）使用 uid，否则使用 did。

    `embeddings_from_attr`: [list] 跟 items_from_attr 类似，从对应的 common_attr 中直接获取 embedding 数据（注意：common_attr 数目需与 items_from_attr 保持一致，且均为 double_list 类型，多个 item 的 embedding 按 items_from_attr 中的顺序拼接在一起）

    `attr_single_limit`: [int] 为 int_list 类型的 common_attr 限制最大选取数目，不限制可缺省该项

    `browsed_item_count`: [int] [动态参数] 请求携带的 browsed item 个数，用于在召回服务端进行前置过滤，正数为最近，0 为全部，负数为取最远的 abs(browsed_item_count) 个，缺省则不过滤！browsed item 来自请求中的 browse set

    `bound_type`: [dict] embedding 召回的边界类型，以下可选类型只可配置一项！
      - `top_k`: [int] [动态参数] 按距离最近的 K 个筛选
      - `total_limit`: [int] [动态参数] 根据 items_from_attr 配置中传入的 item 数量动态调整 top_k 的值，使召回的总数目控制在 total_limit 以下

    `algo_type`: [dict] 算法类型
      - `annoy`: [dict] annoy 算法参数配置
        - `search_k`: [int] [动态参数] 启发式的获取 search_k 个节点, 最后暴力找 top n 个结果
      - `hnsw`: [dict] hnsw 算法参数配置
        - `ef`: [int] [动态参数] 算法动态 list 大小，阈值越大耗时越长但结果更准确，同组数据调参时通常考虑和 m 成反比
      - `faiss`: [dict] faiss 算法参数配置
        - `nprobe`: [int] [动态参数] 从 nlist 个分区的 nprobe 中查找结果，越大越耗时，但结果越精确，可缺省不配置

    `src_data_type`: [string] [动态参数] 目标 item 的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `src_bucket`: [string] [动态参数] [DEPRECATED] 目标 item 的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `dest_bucket`: [string] [动态参数] 待召回候选集的 embedding 数据桶名称，也可通过 "{{}}" 格式从 CommonAttr 中获取

    `dest_bucket_item_type`: [int] [动态参数] dest_bucket 中各个 item 的 item_type（该值一般需要从 embedding 服务的数据提供方获取），默认值为 0

    `save_source_item_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 source_item_key 存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `save_embs_to_attr`: [string] 如果不为空，则将各个召回 item 对应的 embeddings 存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `save_distance_to_attr`: [string] 如果不为空，则将各个召回 item 与其 source_item 的 distance 值存入该 item_attr 中（类型为 double_list, 对应多个 source item），可缺省

    `save_seq_num_to_attr`: [string] 如果不为空，则将各个召回 item 在其 source_item 所触发的结果队列顺序值（从 0 开始）存入该 item_attr 中（类型为 int_list, 对应多个 source item），可缺省

    `fetch_user_embedding`: [dict] 已废弃，请使用 [get_kuiba_user_embedding](https://dragonfly.corp.kuaishou.com/#/api/retrieval?id=get_kuiba_user_embedding) 获取 kuiba tower 顶层embedding

    `perturb`: [dict] 如果需要对 user embedding 做随机扰动，以避免对相同用户重复触发相同的 item
      - `perturb_norm_stddev`: [double] 随机扰动的 norm_stddev
      - `perturb_prob`: [double] user embedding 随机扰动的概率
      - `embedding_bit_perturb_prob`: [double] user embedding 每一位扰动的概率

    调用示例
    ------
    ``` python
    .PdnEmbeddingRetriever(
      kess_service = "grpc_acfunI2iEmbeddingRetrieveServer",
      timeout_ms = 300,
      reason = 1000,
      items_from_attr = ["play_pid_list"],
      attr_single_limit = 10,
      bound_type = {
        "top_k": 10,
        # "total_limit": 100,
      },
      algo_type = {
        "faiss": {},
      },
      # item 对应的数据桶，如 i2i 这里是 "item", u2i 这里是 "user"
      src_data_type = "ac_mmu",
      # 目标桶
      dest_bucket = "ac_mmu"
    )
    ```
    """
    self._add_processor(PdnEmbeddingRetriever(kwargs))
    return self

  def parse_model_update_message(self, **kwargs):
    self._add_processor(ModelUpdateMessageRetriever(kwargs))
    return self

  def extract_live_sim_lr_feature(self, **kwargs):
    self._add_processor(LiveSIMLRFeatureEnricher(kwargs))
    return self

  def retrieve_neg_sample(self, **kwargs):
    """
    NegSampleRetriever
    ------
    从负样本中心进行负采样
    ------
    `kess_service`: [string] 不可缺省, 负采样中心服务名

    `sampling_type`: [int] 不可缺省, 负采样类型, 需要是正整数

    `sampling_cnt_per_item`: [int] 不可缺省, 每条样本负采样数量, 需要是正整数
    
    `filter_pids_attrs`: [list] 可缺省, 用于过滤 pid 的 common attr list
    
    `save_result_to_common_attr`: [string] 不可缺省, 存储负采样的 pid list 的 common attr

    调用示例
    ------
    .retrieve_neg_sample(
      kess_service = 'grpc_xxx',
      sampling_type = 1,
      sampling_cnt_per_item = 20,
      filter_pids_attrs = ['pos_ids'],
      save_result_to_common_attr = 'neg_sample_result_ids'
    )
    """
    self._add_processor(NegSampleRetriever(kwargs))
    return self

  def enrich_colossus_batch_trigger(self, **kwargs):
    """
    ColossusBatchEnricher
    ------
    参数
    ------

    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name

    `uid_list_attr`: [string] 指定输入 user id list 的 common attr name

    调用示例
    ------
    ``` python
    .enrich_colossus_batch_trigger(
      colossus_resp_attr="colossus_resp",
      uid_list_attr="uid_list"
    )
    ```
    """
    self._add_processor(ColossusBatchEnricher(kwargs))
    return self

  def pos_sample(self, **kwargs):
    self._add_processor(PositiveSampleEnricher(kwargs))
    return self
  
  def enrich_item_dual_sim_hard_search(self, **kwargs):
    """
    ItemDualSimHardSearchEnricher
    ------
    参数
    ------

    `user_cluster_attr`: [string] 输入 user cluster 的 item attr

    `item_sim_cluster_did_attr`: [string] 输入 item sim cluster_did 的 item attr

    `item_sim_play_time_attr`: [string] 输入 item sim play_time 的 item attr

    `item_sim_ts_attr`: [string] 输入 item sim timestamp 的 item attr

    `item_sim_did_gsu_attr`: [string] 输出 item sim did 经过 hard search 之后的 item attr

    `item_sim_play_time_gsu_attr`: [string] 输出 item sim play_time 经过 hard search 之后的 item attr

    `item_sim_ts_gsu_attr`: [string] 输出 item sim timestamp 经过 hard search 之后的 item attr

    `item_sim_ori_len_attr`: [string] 输出 item dual sim 原始的 did 数量的 item attr

    `item_sim_topk`: [int] [动态参数] item dual sim 经过 hard search 之后保留的数量

    `dedup_did`: [bool] [动态参数] 是否对相同 did 去重，默认 false

    `output_slot_attr`: [string] 输出 item dual sim hard search 处理之后的 slot 对应的 item attr

    `output_sign_attr`: [string] 输出 item dual sim hard search 处理之后的 sign 对应的 item attr

    调用示例
    ------
    ``` python
    .enrich_item_dual_sim_hard_search(
      user_cluster_attr = "user_cluster_id",
      item_sim_cluster_did_attr = "item_sim_cluster_did",
      item_sim_play_time_attr = "item_sim_play_time",
      item_sim_ts_attr = "item_sim_ts",
      item_sim_did_gsu_attr = "item_sim_did_gsu",
      item_sim_play_time_gsu_attr = "item_sim_play_time_gsu",
      item_sim_ts_gsu_attr = "item_sim_ts_gsu",
      item_sim_ori_len_attr = "item_sim_did_len",
      item_sim_topk = 50,
      output_slot_attr = "output_slots",
      output_sign_attr = "output_signs",
    )
    ```
    """
    self._add_processor(ItemDualSimHardSearchEnricher(kwargs))
    return self

  def enrich_item_dual_sim_multi_cluster(self, **kwargs):
    """
    ItemDualSimMultiClusterEnricher
    ------
    参数
    ------

    `item_dura_attr`: [string] 输入 item duration 的 item attr

    `item_sim_cluster_did_attr`: [string] 输入 item sim cluster_did 的 item attr

    `item_sim_play_time_attr`: [string] 输入 item sim play_time 的 item attr

    `item_sim_ts_attr`: [string] 输入 item sim timestamp 的 item attr

    `slots_id`: [string] 进行 kuiba 编码时, did、playtime_dur、td 对应的 slot

    `mio_slots_id`: [string] 进行 kuiba 编码时, did、playtime_dur、td 对应的 mio_slot

    `item_sim_topk_cluster`: [int] [动态参数] item dual sim 取数量最多的 topk 个 user_cluster

    `cluster_rank_type`: [int] [动态参数] topk cluster 排序规则：0->用户数量, 1->平均 play_time, 2->平均完播率, 3->平均 evtr, 4->平均 lvtr，默认 0

    `enable_cluster_min_threshold`: [bool] [动态参数] 是否需要对每个 cluster 的最小数量进行过滤

    `cluster_min_threshold`: [int] [动态参数] 每个 cluster 进行过滤的最小数量

    `cluster_max_num`: [int] [动态参数] 每个 cluster 最多获取的 user 数量

    `dedup_did`: [bool] [动态参数] 是否对相同 did 去重，默认 false

    `output_topk_cluster_attr`: [string] 输出 item dual sim 的 topk 个 cluster 对应的 item attr

    `output_slot_attr`: [string] 输出 item dual sim topk 个 cluster 经过处理之后的 slot 对应的 item attr

    `output_sign_attr`: [string] 输出 item dual sim topk 个 cluster 经过处理之后的 sign 对应的 item attr

    调用示例
    ------
    ``` python
    .enrich_item_dual_sim_multi_cluster(
      item_dura_attr = "duration",
      item_sim_cluster_did_attr = "item_sim_cluster_did",
      item_sim_play_time_attr = "item_sim_play_time",
      item_sim_ts_attr = "item_sim_ts",
      slots_id = [23, 25, 26],
      mio_slots_id = [117, 118, 119],
      output_topk_cluster_attr = "topk_cluster",
      output_slot_attr = "multi_cluster_output_slots",
      output_sign_attr = "multi_cluster_output_signs",
      item_sim_topk_cluster = 5,
      cluster_rank_type = 1,
      enable_cluster_min_threshold = False,
      cluster_min_threshold = 10,
      cluster_max_num = 100
    )
    ```
    """
    self._add_processor(ItemDualSimMultiClusterEnricher(kwargs))
    return self

  def union_hard_search(self, **kwargs):
    self._add_processor(UnionHardSearchEnricher(kwargs))
    return self

  def union_lost_memory(self, **kwargs):
    self._add_processor(UnionLostMemoryEnricher(kwargs))
    return self

  def union_uniform(self, **kwargs):
    self._add_processor(UnionUniformEnricher(kwargs))
    return self

  def observe_multi_interest(self, **kwargs):
    self._add_processor(MultiInterestObserver(kwargs))
    return self

  def observe_interest_hit_rate(self, **kwargs):
    self._add_processor(InterestHitRateObserver(kwargs))
    return self

  def observe_full_link_retrieval_evaluate(self, **kwargs):
    self._add_processor(FullLinkRetrievalEvaluateObserver(kwargs))
    return self

  def observe_missing_interest(self, **kwargs):
    self._add_processor(MissingInterestObserver(kwargs))
    return self

  def extract_million(self, **kwargs):
    self._add_processor(ExtractMillionEnricher(kwargs))
    return self

  def extract_missing_memory(self, **kwargs):
    self._add_processor(ExtractMissingMemoryEnricher(kwargs))
    return self

  def extract_search_user_info(self, **kwargs):
    self._add_processor(ExtractSearchUserInfoEnricher(kwargs))
    return self

  def general_search(self, **kwargs):
    self._add_processor(GeneralSearchEnricher(kwargs))
    return self

  def general_search_infer(self, **kwargs):
    self._add_processor(GeneralSearchInferEnricher(kwargs))
    return self

  def set_reason_by_item_attr(self, **kwargs):
    """
    SetReasonArranger
    ------
    重设 reason
  
    参数
    ------
    `reason_attr`: [string] reason item attr 字段
    
    `default_reason`: [int] reason attr 取值为空时的回落值

    调用示例
    ------
    ``` python
    .set_reason_by_item_attr(
      reason_attr='final_reason',
      default_reason=633,
    )
    ```
    """
    self._add_processor(SetReasonArranger(kwargs))
    return self

  def union_uniform_v2(self, **kwargs):
    self._add_processor(UnionUniformV2Enricher(kwargs))
    return self

  def sum_random_split(self, **kwargs):
    self._add_processor(RandomSplitEnricher(kwargs))
    return self

  def generate_user_history_feature(self, **kwargs):
    """
    GenerateUserHistoryFeatureEnricher
    ------
    生成式召回抽取序列特征专用
  
    参数
    ------
    `colossus_resp_attr`: [string] 指定输入 `colossus_resp` 的 common attr name
    
    `output_slot_attr`: [string] 输出的 slot common attr name

    `output_sign_attr`: [string] 输出的 sign common attr name

    `slots_ids`: [list] slots_ids, 默认值 [26, 128, 1003, 1004, 1005, 1006]

    `mio_slots_ids`: [list] mio_slots_ids, 默认值 [1001, 1002, 1003, 1004, 1005, 1006]

    `is_effective_view`: [bool] 抽取有效观看序列

    `is_long_view`: [bool] 抽取长播序列

    `save_photo_id_list_attr`: [string] 保存 pid 序列的 common attr name

    `save_photo_size_attr`: [string] 保存序列长度

    `item_num_limit`: [int] [动态参数] 序列长度上限，默认 500 长度

    `is_reverse`: [bool] [动态参数] 默认是最新的视频排在最前，reverse 后最老的视频排在最前

    调用示例
    ------
    ``` python
    .generate_user_history_feature(
      colossus_resp_attr='colossus_resp_attr',
      output_slot_attr='sim_slots',
      output_sign_attr='sim_signs',
      is_effective_view=True,
      save_photo_id_list_attr="sim_photo_id_list",
      item_num_limit=500,
      is_reverse=True,
    )
    ```
    """
    self._add_processor(GenerateUserHistoryFeatureEnricher(kwargs))
    return self

  def calculate_retrieve_score(self, **kwargs):
    self._add_processor(CalcRetrieveScoreEnricher(kwargs))
    return self

  def sft_data_enrich(self, **kwargs):
    """
    SftDataEnricher
    ------
    pretrain/sft 训练数据
    按时间戳从大到小排列

    调用示例
    ------
    ``` python
    .gsu_common_colossusv2_enricher(
      kconf="colossus.kconf_client.video_item",
      limit=100,
      item_fields=dict(photo_id="pid_list",
                      timestamp="timems_list",
                      author_id="aid_list",
                      play_time="playtime_list",
                      duration="duration_list",
                      tag="tag_list",
                      channel="channel_list",
                      label="label_list"),
    )
    .sft_data_enrich(
      photo_id_from="pid_list",
      timestamp_from="timems_list",
      author_id_from="aid_list",
      play_time_from="playtime_list",
      duration_from="duration_list",
      label_from="label_list",
      tag_from="tag_list",
      channel_from="channel_list",
      filter_channels=[66,67,68],
      export_colossus_photo_id_attr="sft_pid_list",
      export_colossus_timestamp_attr="sft_time_list",
      export_colossus_play_time_attr="sft_playtime_list",
      export_colossus_interact_mask_attr="sft_inter_list",
      export_colossus_channel_attr="sft_channel_list",
      export_colossus_longview_mask_attr="sft_lv_mask_list",
      extract_item_limit=128
    )
    ```
    """
    self._add_processor(SftDataEnricher(kwargs))
    return self
  