import operator
import itertools

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger

class SetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_reason_by_item_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_reason"]:
      attrs.add(self._config.get(key, ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for key in ["reason_attr"]:
      attrs.add(self._config.get(key, ""))
    return attrs