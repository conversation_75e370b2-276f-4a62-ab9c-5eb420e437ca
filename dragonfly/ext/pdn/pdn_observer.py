from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class UpdateConcurrenceKvObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_concurrence_kv"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("kess_service")))
    ret.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    ret.update(self.extract_dynamic_params(self._config.get("bucket")))
    ret.update(self.extract_dynamic_params(self._config.get("concurrence_pair_decay")))
    ret.update(self.extract_dynamic_params(self._config.get("concurrence_pair_step")))
    ret.add(self._config.get("import_common_action_pid_list"))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("import_item_pid"))
    ret.add(self._config.get("import_item_weight"))
    ret.add(self._config.get("import_item_action_pid_list"))
    return ret

class MultiInterestObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "observe_multi_interest"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_cluster_id"))
    ret.add(self._config.get("in_hetu_level_one"))
    ret.add(self._config.get("in_hetu_level_two"))
    ret.add(self._config.get("in_hetu_level_three"))
    ret.add(self._config.get("in_reason"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("user_type")))
    ret.update(self.extract_dynamic_params(self._config.get("name_space")))
    ret.update(self.extract_dynamic_params(self._config.get("sub_tag")))
    return ret

class InterestHitRateObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "observe_interest_hit_rate"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_cluster_id"))
    ret.add(self._config.get("in_author_id"))
    ret.add(self._config.get("in_reason"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("user_type")))
    ret.update(self.extract_dynamic_params(self._config.get("name_space")))
    ret.add(self._config.get("colossus_photo_id"))
    ret.add(self._config.get("colossus_author_id"))
    ret.add(self._config.get("colossus_duration"))
    ret.add(self._config.get("colossus_playing_time"))
    ret.add(self._config.get("colossus_label"))
    ret.add(self._config.get("colossus_timestamp"))
    ret.add(self._config.get("colossus_cluster_id"))
    return ret

class MissingInterestObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "observe_missing_interest"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("in_cluster_id"))
    ret.add(self._config.get("in_author_id"))
    ret.add(self._config.get("in_author_cluster_id"))
    ret.add(self._config.get("in_reason"))
    ret.add(self._config.get("in_photo_id"))
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("user_type")))
    ret.update(self.extract_dynamic_params(self._config.get("name_space")))
    ret.update(self.extract_dynamic_params(self._config.get("sub_tag")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("cluster_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_missing_day_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_cluster_valid_view_num")))
    ret.update(self.extract_dynamic_params(self._config.get("author_cluster_missing_day_num")))
    ret.add(self._config.get("colossus_photo_id"))
    ret.add(self._config.get("colossus_author_id"))
    ret.add(self._config.get("colossus_duration"))
    ret.add(self._config.get("colossus_playing_time"))
    ret.add(self._config.get("colossus_label"))
    ret.add(self._config.get("colossus_timestamp"))
    ret.add(self._config.get("colossus_cluster_id"))
    ret.add(self._config.get("colossus_author_cluster_id"))
    ret.add(self._config.get("perf_retr_overlap"))
    return ret

class FullLinkRetrievalEvaluateObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "observe_full_link_retrieval_evaluate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("user_type")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["exptag_list_attr", "extra_float_attrs", "extra_string_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    return ret