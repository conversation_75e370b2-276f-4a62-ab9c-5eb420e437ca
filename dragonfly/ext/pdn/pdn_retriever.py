#!/usr/bin/env python3
"""
filename: pdn_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for pdn 
author: <EMAIL>
date: 2021-11-05
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class SampleJoinApiRequestRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_sample_join_api_request"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_request_timestamp_ms"))
    return ret 

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("extract_item_attrs", []))

class GeneralColossusRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_general_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    ret.add(self._config.get("reason"))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_output_type")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_service_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_photo_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_author_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_play_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_duration_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_timestamp_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_channel_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_label_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_tag_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_profile_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_comment_stay_time_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_cluster_id_field_name")))
    ret.update(self.extract_dynamic_params(self._config.get("colossus_item_limit")))
    ret.update(self.extract_dynamic_params(self._config.get("filter_future_ts")))
    ret.update(self.extract_dynamic_params(self._config.get("keep_past_range_second")))
    ret.update(self.extract_dynamic_params(self._config.get("keep_future_range_second")))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_photo_id_to_attr", "save_author_id_to_attr", "save_duration_to_attr", "save_play_time_to_attr", 
        "save_tag_to_attr", "save_channel_to_attr", "save_label_to_attr", "save_timestamp_to_attr", 
        "save_cluster_id_to_attr", "save_profile_stay_time_to_attr", "save_comment_stay_time_to_attr"
      ] if key in self._config 
    } 

class LrModelRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "lr_model_retrieve"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("user_info_attr_name"))
    ret.update(self.extract_dynamic_params(self._config.get("kess_service")))
    ret.update(self.extract_dynamic_params(self._config.get("result_num")))
    ret.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    return ret

class PdnEmbeddingRetriever(LeafRetriever):
  @strict_types
  def __init__(self, config: dict):
    if "fetch_user_embedding" in config and \
        config["fetch_user_embedding"].get("include_sample_list_user_info", False):
      config["fetch_user_embedding"]["use_sample_list_attr_flag"] = True
      config["fetch_user_embedding"]["sample_list_common_attr_key"] = self._SAMPLE_LIST_COMMON_ATTR_KEY
      if "use_sample_list_attr_flatten" not in config["fetch_user_embedding"]:
        config["fetch_user_embedding"]["use_sample_list_attr_flatten"] = True
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "pdn_retrieve_by_ann_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    user_embedding_config = self._config.get("fetch_user_embedding", {})
    return user_embedding_config.get("use_sample_list_attr_flag", True) and bool(user_embedding_config.get("sample_list_common_attr_key"))

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.update(self._config.get("items_from_attr", []))
    attrs.update(self._config.get("embeddings_from_attr", []))
    for key in ["kess_service", "timeout_ms", "src_bucket", "dest_bucket", "dest_bucket_item_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key, ""), check_format=False))
    for key in ["top_k", "total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config["bound_type"].get(key, "")))
    attrs.update(self.extract_dynamic_params(self._config["algo_type"].get("annoy", {}).get("search_k", "")))
    attrs.update(self.extract_dynamic_params(self._config["algo_type"].get("hnsw", {}).get("ef", "")))
    embedding_config = self._config.get("fetch_user_embedding", {})
    attrs.update(embedding_config.get("attr_name_transform_map", {}).keys())
    if embedding_config.get("sample_list_common_attr_key", ""):
      attrs.add(embedding_config.get("sample_list_common_attr_key", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_source_item_to_attr", "save_distance_to_attr", "save_seq_num_to_attr", "save_embs_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

class ModelUpdateMessageRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_model_update_message"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set([self._config["from_extra_var"]])

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("save_sign_to_attr"))
    ret.add(self._config.get("save_left_id_to_attr"))
    ret.add(self._config.get("save_right_id_to_attr"))
    ret.add(self._config.get("save_slot_id_to_attr"))
    ret.add(self._config.get("save_weights_to_attr"))
    ret.add(self._config.get("save_feature_score_to_attr"))
    return ret
  
class NegSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_neg_sample"

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("kess_service")))
    ret.update(self.extract_dynamic_params(self._config.get("sampling_type")))
    ret.update(self.extract_dynamic_params(self._config.get("sampling_cnt_per_item")))
    for key in ["filter_pids_attrs"]:
      if key in self._config:
        for value in self._config[key]:
          ret.add(value)
    return ret
