#!/usr/bin/env python3
# coding=utf-8
"""
filename: yuzhou_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, yuzhou arranger
author: <EMAIL>
date: 2021-01-27 14:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafArranger


class YuzhouFlowControlFilter(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "flow_control_by_config"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["filter"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class YuzhouFlowControlArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "flow_control_random_filter"

class YuzhouRetrFilter(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "yuzhou_retrievals_filter"
  
class YuzhouSelectWithDiversityConstraintsArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "yuzhou_select_with_embedding_diversity_constraints"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["need_num", "aggregation_degree"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

class YuzhouLowPassFilter(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "yuzhou_low_pass_filter"

class YuzhouLowPassFilterV2Arranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "yuzhou_low_pass_filter_v2"

