#!/usr/bin/env python3
# coding=utf-8
"""
filename: yuzhou_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, yuzhou enricher
author: <EMAIL>
date: 2021-01-27 14:00:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class YuzhouCountEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_yuzhou_count_from_redis"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["app_id"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update({
      'showCnt', 'fvCnt', 'evCnt', 'lvCnt', 'svCnt', 'empFvtr', 'empEvtr',
      'empLvtr', 'empSvtr'
    })
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config["cluster_name"] and isinstance(self._config.get("cluster_name"), str),
              "cluster_name 需为非空字符串")
    check_arg(self._config["timeout_ms"] and isinstance(self._config.get("timeout_ms"), int),
              "timeout_ms 需为 > 0 的 int 数值")


class YuzhouBucketItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_bucket"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["buckets"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    out_attr_prefix = self._config.get("out_attr_prefix", "")
    # 临时应对 wifi dragonfly 检测报错：无法找到以下 item_attr 来源
    attrs.add(out_attr_prefix + str(0))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("buckets"), str),
              "buckets 需为非空字符串")
    check_arg(self._config.get("perflog_subtag", "") or self._config.get("out_attr_prefix", ""),
              "perflog_subtag 或者 out_attr_prefix 不能同时为空")


class YuzhouPhotoAgeDistributionTrimEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trim_photo_age_distribution"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in ["buckets", "window_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(attr)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    bucket_attr_prefix = self._config.get("bucket_attr_prefix", "")
    # 临时应对 wifi dragonfly 检测报错：无法找到以下 item_attr 来源
    attrs.add(bucket_attr_prefix + str(0))
    attrs.add(bucket_attr_prefix + str(30))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("buckets"), str),
              "buckets 需为非空字符串")
    check_arg(self._config.get("bucket_attr_prefix", "") and self._config.get("bucket_num_attr_prefix", ""),
              "bucket_attr_prefix 和 bucket_num_attr_prefix 不能为空")

class YuzhouCityHashWithSeedEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "city_hash_with_seed"

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config["input_common_attr_name"] and isinstance(self._config.get("input_common_attr_name"), str),
              "input_common_attr_name 需为非空字符串")
    check_arg(self._config["output_common_attr_name"] and isinstance(self._config.get("output_common_attr_name"), str),
              "output_common_attr_name 需为 > 0 的 int 数值")


class YuzhouPhotoInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_attrs_of_photo_info"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["photo_info_attr"])
    ret.add(self._config["user_info_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update({
      'audit_hot_high_tag_level',
      'author.category_detail.first_level_id',
      'author.category_detail.fourth_level_id',
      'author.category_detail.second_level_id',
      'author.category_detail.third_level_id',
      'author.id',
      'author.is_gr_account',
      'author.is_pr_account',
      'author.recent_explore_stat.click_count',
      'caption',
      'caption_segment',
      'click_count',
      'content_safety_level',
      'content_safety_level_with_namespace.level_hot_online',
      'duration_ms',
      'explore_stat.click_count',
      'explore_stat.comment_count',
      'explore_stat.comment_stay_time_sum_ms',
      'explore_stat.follow_count',
      'explore_stat.forward_count',
      'explore_stat.like_count',
      'explore_stat.negative_count',
      'explore_stat.not_login_click_count',
      'explore_stat.profile_enter_count',
      'explore_stat.report_detail.total_report_count',
      'explore_stat.show_count',
      'explore_stat.view_length_sum',
      'hetu_tag_level_info.hetu_level_one',
      'hetu_tag_level_info.hetu_level_three',
      'hetu_tag_level_info.hetu_level_two',
      'mmu_content_id',
      'mmu_cover_gender',
      'mmu_img_cluster_v3',
      'mmu_img_cluster_v4',
      'mmu_photo_score.click_bait_score',
      'mmu_photo_score.click_bait_score_v2',
      'mmu_photo_score.disgust_score',
      'mmu_photo_score.ds_cover_title_score',
      'mmu_photo_score.ds_disgust_score',
      'mmu_photo_score.ds_like_bait_score',
      'mmu_photo_score.erotic_score',
      'mmu_photo_score.low_score',
      'mmu_photo_score.miserable_score',
      'mmu_photo_tag.vertical_tag_id_list',
      'photo_disability_score_gray',
      'photo_dnn_cluster_id',
      'photo_id',
      'tag',
      'upload_time',
      'upload_type',
      'view_length_sum',
      'hetu_level_one_for_prophet'
    })
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config["photo_info_attr"] and isinstance(self._config.get("photo_info_attr"), str),
              "photo_info_attr 需为非空字符串")
              
class KuibaRawSamplePackageLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_raw_sample_package"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    input_attrs = set()
    input_attrs.add(self._config["timestamp"])
    input_attrs.update(self._config["labels"])
    input_attrs.update(self._config["item_attrs"])
    return input_attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}

class AppListFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_app_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["user_info_attr"])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}

class AppCategoryListFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_app_cate_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["user_info_attr"])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}

class FreqWifiListFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_freq_wifi_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["user_info_attr"])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}

class FreqLocationFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_freq_location_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config["user_info_attr"])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["save_result_to"]}

class YuzhouPxtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_pxtr_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    cfg_keys = ["prefix", "pxtr_label_attr", "pxtr_value_attr", "labels"]
    return set([self._config.get(k) for k in cfg_keys])

class WenyuanItemInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_wenyuan_item_info_by_redis"

  @strict_types
  def _check_config(self) -> None:
    check_arg("cluster_name" in self._config, f"{self.get_type_alias()} 缺少 cluster_name 配置")
    check_arg("save_item_info_to_attr" in self._config, f"{self.get_type_alias()} 缺少 save_item_info_to_attr 配置")
    check_arg("class_name" in self._config, f"{self.get_type_alias()} 缺少 class_name 配置")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {self._config.get("save_item_info_to_attr")}

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

class YuzhouLastAccessTimeEnricher(LeafEnricher): 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_last_request_time"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    cfg_keys = ["user_info_attr", "page", "duration_limit"]
    return set([self._config.get(k) for k in cfg_keys])

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["to_attr"]}


