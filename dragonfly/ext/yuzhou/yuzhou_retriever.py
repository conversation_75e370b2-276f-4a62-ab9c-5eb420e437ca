#!/usr/bin/env python3
# coding=utf-8
"""
filename: yuzhou_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module
author: <EMAIL>
date: 2021-02-03 19:05:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever


class YuzhouTopPhotoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_yuzhou_top_photos"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("cache"), dict) and self._config["cache"],
              "cache 需要为非空 dict")
    check_arg(isinstance(self._config.get("strategy"), dict) and self._config["strategy"],
              "strategy 需要为非空 dict")


class RaveRecoColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "yuzhou_rave_retriever_with_colossus_resp"

  @strict_types
  def is_async(self) -> bool:
    return True

  