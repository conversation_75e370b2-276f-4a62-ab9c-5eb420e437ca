#!/usr/bin/env python3
# coding=utf-8
"""
filename: yuzhou_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, yuzhou api mixin
author: <EMAIL>
date: 2021-01-27 14:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .yuzhou_arranger import *
from .yuzhou_enricher import *
from .yuzhou_retriever import *


class YuzhouApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 yuzhou 相关的 Processor 接口
  - YuzhouCountEnricher
  - YuzhouFlowControlFilter
  - YuzhouFlowControlArranger
  - YuzhouRetrFilter
  - YuzhouTopPhotoRetriever
  - YuzhouBucketItemAttrEnricher
  - YuzhouPhotoAgeDistributionTrimEnricher
  """

  def get_yuzhou_count_from_redis(self, **kwargs):
    """
    YuzhouCountEnricher
    ------
    从 Counter Server 获取 item 的多种 count 信息和 through rate 信息
    包括 'showCnt', 'fvCnt', 'evCnt', 'lvCnt', 'svCnt', 'empFvtr', 'empEvtr',
    'empLvtr', 'empSvtr'

    参数配置
    ------
    `cluster_name`: [string] 所用的 Counter Server 的名字，不包含 namespace

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 50

    调用示例
    ------
    ``` python
    .get_yuzhou_count_from_redis(
      cluster_name = "recoCoopCounter",
      timeout_ms = 50
    )
    ```
    """
    self._add_processor(YuzhouCountEnricher(kwargs))
    return self

  def flow_control_by_config(self, **kwargs):
    """
    YuzhouFlowControlFilter
    ------
    使用 kconf 获取的 config common attr 来进行流量控制

    参数配置
    ------
    `filter`: [dict] 所用的进行流量控制的 config

    调用示例
    ------
    ``` python
    .flow_control_by_config(
      filter = {"506400001": [[1000, 0.1], [10000, 0.5]]}
    )
    ```
    """
    self._add_processor(YuzhouFlowControlFilter(kwargs))
    return self

  def flow_control_random_filter(self, **kwargs):
    """
    YuzhouFlowControlArranger
    ------
    流量控制，根据类-SQL 的策略进行 item 的加权/打压

    参数配置
    ------
    `strategy`: [json] 所用的进行流量控制的 config

    调用示例
    ------
    ``` python
    .flow_control_random_filter(
      strategy = {
        "test_strategy_1" : [
            {"expression": "pShowCnt > 1000 and app_id = 532800001", "weight": 0.7}
          ],
        "test_strategy_2":[
            {"expression": "pShowCnt > 1000 and pFinishViewCnt < 2000", "weight": 0.4}, 
            {"expression": "pShowCnt > 1000 and ads_content_sdk_like_count_attr <= 4000", "weight": 0.9}
          ],
        "test_strategy_3":[
            {"expression": "(content_safety_level_attr = 3 or content_safety_level_attr = 1) and pShowCnt > 100", "weight": 0.7}, 
            {"expression": "is_from_shennong_attr = 1 and is_in_ks_index_attr = 1", "weight": 1.1}, 
            {"expression": "pShowCnt > 1000 and app_id_long = 532800001", "weight": 1.4}
          ]
      }
    )
    ```
    """
    self._add_processor(YuzhouFlowControlArranger(kwargs))
    return self

  def yuzhou_retrievals_filter(self, **kwargs):
    """
    YuzhouRetrFilter
    ------
    对召回的物料进行自定义策略过滤
    参数配置
    ------
    `filter`: [dict] 所用的进行过滤控制的 config
    调用示例
    ------
    ``` python
    .yuzhou_retrievals_filter(
      filter = {"empty_photo_id_filter": {"type_name":"YuzhouCommonFilter", "filter_path":"photo_id", "remove_if": "<=", "compare_to": 0}}
    )
    ```
    """
    self._add_processor(YuzhouRetrFilter(kwargs))
    return self

  def retrieve_yuzhou_top_photos(self, **kwargs):
    """
    YuzhouTopPhotoRetriever
    ------
    通过配置从 redis 根据某些策略来获取 photo_ids
    这些 id 是普通的，没有 keysign 处理或者截断
    cluster_kconf_key 中包含的是 list_string 类型的 group_names，对于 group_names 中单个 group
    redis_cluster 中有 key-value pair (cluster_key_prefix + group : id)
    redis_cluster 中有 key-value pair (id_key_prefix + group + '_' + id : photo_ids)

    参数配置
    ------
    `cache`: [dict] 所读取的 redis 集群配置，具体参数如下
      - `cluster_kconf_key`: [str] kconf 的 key 值，一级.二级.三级
      - `cluster_key_prefix`: [str] redis cluster 中类别的前缀
      - `expire`: [int] cache 过期时间
      - `id_key_prefix`: [str] redis cluster 中获取 photo_ids 时的前缀
      - `redis_cluster`: [str] redis 集群 cluster name
      - `redis_timeout`: [int] redis 获取结果超时时间
    `strategy`: [dict] 所使用的具体筛选策略，可以定义多个，具体参数如下
      - `random`: [dict] 单个策略的参数设置
        - `count`: [int] 召回返回个数
        - `group`: [dict] 选取 group_set，为所有 group_names 的子集
          - exp_rule_27: [set] 
        - `reason`: [int] 返回的 reason 类别
        - `type_name`: [str] "TopPhotoRandomRetrievalStrategy" 唯一值，不要改动

    调用示例
    ------
    ``` python
    .retrieve_yuzhou_top_photos(
      cache = {
        "cluster_kconf_key": "recoNewProducts.commonReco.shennong_admin_pool_config",
        "cluster_key_prefix": "shennong_admin_pool_cluster_",
        "expire": 600000,
        "id_key_prefix": "shennong_admin_pool_id_",
        "redis_cluster": "yuzhouIndexPool",
        "redis_timeout": 5000
      },
      strategy = {
        "random": {
          "count": 500,
          "group": {
            "exp_rule_80": []
          },
          "reason": 20080,
          "type_name": "TopPhotoRandomRetrievalStrategy"
        }
      }
    )
    ```
    """
    self._add_processor(YuzhouTopPhotoRetriever(kwargs))
    return self
  
  def yuzhou_rave_retriever_with_colossus_resp(self, **kwargs):
    """
    RaveRecoColossusRespRetriever
    
    """
    self._add_processor(RaveRecoColossusRespRetriever(kwargs))
    return self

  def get_item_attr_bucket(self, **kwargs):
      """
      YuzhouBucketItemAttrEnricher
      ------
      针对数值型 item_attr，依据分桶配置，实现：
      1. 生成名称为 out_attr_prefix_xx 的 item_attr （xx表示桶名，桶外默认为0）
      2. perflog监控

      参数配置
      ------
      `attr_name`: [string] 要操作的 item_attr 名称
      `buckets`: [string][动态参数] 分桶配置，以逗号分隔，如 "14,30"
      `perflog_subtag`: [string] perflog 的 subtag 名称，当不为空时，输出perflog
      `check_point`: [string] perflog 的 extra3 名称
      `out_attr_prefix`: [string] 衍生分桶 item_attr 名称前缀，值为1。默认为空，表示不输出
      调用示例
      ------
      ``` python
      .get_item_attr_bucket(
        attr_name = "freshness_attr",
        buckets = "14,30",
        perflog_subtag = "mc_photo_age",
        check_point = "mc.variant",
        out_attr_prefix = "photo_age_bucket_"
      )
      ```
      """
      self._add_processor(YuzhouBucketItemAttrEnricher(kwargs))
      return self


  def trim_photo_age_distribution(self, **kwargs):
      """
      YuzhouPhotoAgeDistributionTrimEnricher
      ------
      针对数值型 item_attr，依据分桶配置，按比例和窗口计算N：生成属性 bucket_attr_prefix_xx -> mc_age_bucket_num_N 

      参数配置
      ------
      `buckets`: [string][动态参数] 分桶配置，格式如 "1:0.8,7:0.1,30:0.05"
      `window_size`: [int][动态参数] 计算窗口大小
      `bucket_attr_prefix`: [string] 桶名称前缀
      `bucket_num_attr_prefix`: [string] 桶名称值，按照占比计算窗口内个数
      调用示例
      ------
      ``` python
      .trim_photo_age_distribution(
        buckets = "1:0.8,7:0.1,30:0.05",
        window_size = "mc_photo_age",
        bucket_attr_prefix = "mc_age_bucket_",
        bucket_num_attr_prefix = "mc_age_bucket_num_"
      )
      ```
      """
      self._add_processor(YuzhouPhotoAgeDistributionTrimEnricher(kwargs))
      return self

  def city_hash_with_seed(self, **kwargs):
      '''
      '''
      self._add_processor(YuzhouCityHashWithSeedEnricher(kwargs))
      return self

  def enrich_attrs_of_photo_info(self, **kwargs):
      """
      YuzhouPhotoInfoEnricher
      ------
      从 photo info 获取各种 item attr 并且填入
      上游是一个 CommonRecoDistributedIndexItemAttrEnricher，并且需要设置起参数
      "save_item_info_to_attr": "photo_info"

      参数配置
      ------
      `photo_info_attr`: [string] 从哪个参数读取 photo_info，支持动态参数

      `user_info_attr`: Optional[string] 从哪个参数读取 user_info，支持动态参数

      调用示例
      ------
      ``` python
      .enrich_attrs_of_photo_info(
        photo_info_attr = "photo_info",
        user_info_attr = "user_info"
      )
      ```
      """
      self._add_processor(YuzhouPhotoInfoEnricher(kwargs))
      return self

  def build_raw_sample_package(self, **kwargs):
    """
    KuibaRawSamplePackageLabelEnricher
    -----
    从 context 提取特征 转为 kuiba::RawSamplePackage 结构，保存在 common attr 中

    参数配置
    ------
    `common_attrs`: [list] 从给定的 common attr 填充入 RawSamplePackage 的 common_attr

    `item_attrs`: [list] 从给定的 item attr 填充入 RawSamplePackage 中每个 sample 的 attr

    `save_result_to`: [string] 将构造出来的 RawSamplePackage 存入指定的 common attr

    调用示例
    ------
    ``` python
    .build_raw_sample_package(
        labels = ["click", "like", "watch_time"],
        common_attrs = ["user_id", "device_id", "client_id"],
        item_attrs = ["photo_id", "author_id"],
        timestamp = "time_ms",
        save_result_to = "raw_sample_package")
    ```
    """
    self._add_processor(KuibaRawSamplePackageLabelEnricher(kwargs))
    return self

  def enrich_app_list(self, **kwargs):
    """
    AppListFeatureEnricher
    -----
    从 context 的 user_info 提取 applist，保存在 $save_result_to 中

    参数配置
    ------
    `user_info_attr`: [string] 存储 user_info 的属性名
    `save_result_to`: [string] 将构造出来的特征存入指定的 common attr

    调用示例
    ------
    ``` python
    .enrich_app_list(user_info_attr = "user", save_result_to = "app_list")
    ```
    """
    self._add_processor(AppListFeatureEnricher(kwargs))
    return self


  def enrich_app_cate_list(self, **kwargs):
    """
    AppCategoryListFeatureEnricher
    -----
    从 context 的 user_info 提取 applist，保存在 $save_result_to 中

    参数配置
    ------
    `user_info_attr`: [string] 存储 user_info 的属性名
    `save_result_to`: [string] 将构造出来的特征存入指定的 common attr

    调用示例
    ------
    ``` python
    .enrich_app_cate_list(user_info_attr = "user", save_result_to = "app_cate_list")
    ```
    """
    self._add_processor(AppCategoryListFeatureEnricher(kwargs))
    return self


  def enrich_freq_wifi_list(self, **kwargs):
    """
    FreqWifiListFeatureEnricher
    -----
    从 context 的 user_info 提取 applist，保存在 $save_result_to 中

    参数配置
    ------
    `user_info_attr`: [string] 存储 user_info 的属性名 
    `save_result_to`: [string] 将构造出来的特征存入指定的 common attr

    调用示例
    ------
    ``` python
    .enrich_freq_wifi_list(user_info_attr = "user", save_result_to = "freq_wifi_list")
    ```
    """
    self._add_processor(FreqWifiListFeatureEnricher(kwargs))
    return self


  def enrich_freq_location_list(self, **kwargs):
    """
    FreqLocationFeatureEnricher
    -----
    从 context 的 user_info 提取 applist，保存在 $save_result_to 中

    参数配置
    ------
    `user_info_attr`: [string] 存储 user_info 的属性名 
    `save_result_to`: [string] 将构造出来的特征存入指定的 common attr

    调用示例
    ------
    ``` python
    .enrich_freq_location_list(user_info_attr = "user", save_result_to = "freq_location_list")
    ```
    """
    self._add_processor(FreqLocationFeatureEnricher(kwargs))
    return self


  def get_wenyuan_item_info_by_redis(self, **kwargs):
    """
    WenyuanItemInfoEnricher
    ------
    从 redis 获取 item_info 的序列化 string 值, 转成 Protobuf 存回 context， 下游一般调用 CommonRecoProtobufParseAttrEnricher 获取路径下的值写入 item_attr 中

    参数配置
    ------
    `cluster_name`: [string] 必配项，redis 的 kcc cluster name

    `timeout_ms`: [int] 选配项，获取 redis client 的超时时间，默认为 200

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）
    
    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 grafana 监控 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `item_id_prefix`: [string] 选配项，为 item_id_attr 中每个 redis key 的值添加一个前缀，默认为空

    `save_item_info_to_attr`: [string] 必配项，可将解析出的 protobuf 直接存到指定的 extra 类型 ItemAttr 中

    `additional_item_source`: [dict] 选配项，若配置则为下列数据源里的 item 获取 ItemAttr，忽略当前结果集里的 item
      - `common_attr`: [list] 需要从哪些 common_attr 的值中获取 item_key，仅支持 int/int_list 类型的 common_attr

    `class_name`: [string] 必配项，转换的 protobuf 的类型，当前支持所有链接的 Message 类型。
    
    `ttl_seconds`: [int] 选配项，默认底层会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间

    调用示例
    ------
    ``` python
    .get_wenyuan_item_info_by_redis(
      cluster_name="recoCacheCluster",
      timeout_ms=100,
      cache_bits=20,
      cache_expire_second=1800,
      item_id_prefix="index:",
      save_item_info_to_attr="photo_info",
      class_name="kuaishou.reco.wenyuan.RecoItemInfo",
      ttl_seconds=86400
    )
    ```
    """
    self._add_processor(WenyuanItemInfoEnricher(kwargs))
    return self

  def yuzhou_select_with_embedding_diversity_constraints(self, **kwargs):
      """
      YuzhouSelectWithDiversityConstraintsArranger
      -----
      Select from reco results a subset of size need_num, with similarity between items embedding considered.
      -----
      
      参数配置      
      -----
      `aggregation_degree`: [double] [动态参数] ratio of aggregation, [0, 1], the larger the less diverse, default=1.0
      
      `item_embedding_attr_name`: [string] attr_name of item_embedding, default=""
      
      `score_attr_name`: [string] attr_name of pxtr scores, default="tower_evtr"
      
      `need_num`: [int] [动态参数] size of expected result set, default=200
      
      调用示例
      -----
      ``` python
      .yuzhou_select_with_embedding_diversity_constraints(
        aggregation_degree="{{selection_aggregation_alpha}}",
        item_embedding_attr_name="tower_item_evtr_embedding",
        score_attr_name="tower_evtr"
        need_num="{{selection_with_diversity_limit}}"
      )
      ```
      
      """
      self._add_processor(YuzhouSelectWithDiversityConstraintsArranger(kwargs))
      return self

  def set_item_pxtr_from_common_attr(self, **kwargs):
      """
      YuzhouPxtrEnricher
      -----
      把 infer server返回的pxtr从common attr里填充到item中
      -----
      参数配置
      -----
      `prefix`: [string] 输出pxtr的前缀
      `labels`: [string] 要输出的pxtr label，逗号分割
      `pxtr_label_attr`: [string] infer server 返回的 label 属性
      `pxtr_value_attr`: [string] infer server 返回的 pxtr value 属性
      """
      self._add_processor(YuzhouPxtrEnricher(kwargs))
      return self

  def set_last_request_time(self, **kwargs):
      """
      YuzhouLastAccessTimeEnricher
      -----
      设置上次访问当前页面的至今的时间差(ms)
      -----
      参数配置
      -----
      `page`: [int] 页面id，参考埋点
      `duration_limit`: [int] 最小的播放时长
      `user_info_attr`: [string] user info属性名称
      `to_attr`: [string] 输出的属性名
      """
      self._add_processor(YuzhouLastAccessTimeEnricher(kwargs))
      return self
    
  def yuzhou_low_pass_filter(self, **kwargs):
      """
      YuzhouLowPassFilter
      -----
      结果集低通过滤
      -----
      
      参数配置
      -----
      `default_max_freq`: [int] 默认达到该阈值开始过滤
      `channel`: [list] 根据哪些 item 特征进行过滤
      
      调用示例
      -----
      ``` python
      .yuzhou_low_pass_filter(
        default_max_freq = 10,
        channel = [
          {"attr": "author.id", "default_max_freq": 2},
          {"attr": "hetu_tag_level_info.hetu_level_two","default_max_freq": 45},
          {"attr": "photo_dnn_cluster_id","default_max_freq": 35},
          {"attr": "author.category_detail.third_level_id","default_max_freq": 45},
          {"attr": "hetu_tag_level_info.hetu_level_one","default_max_freq": 100},
          {"attr": "author.category_detail.second_level_id","default_max_freq": 200},
          {"attr": "author.category_detail.first_level_id","default_max_freq": 300}
        ]
      )
      ```
      """
      self._add_processor(YuzhouLowPassFilter(kwargs))
      return self

  def yuzhou_low_pass_filter_v2(self, **kwargs):
      """
      YuzhouLowPassFilterV2
      -----
      结果集低通过滤
      -----

      参数配置
      -----
      `channel_config`: [list] 根据哪些 item attr 进行过滤, 其中每个 item attr 对应一个字典，当前仅支持 attr 类型为 int ，格式如下:
      + `attr`: [str] 必配项，item attr 标识
      + `limit`: [int][动态参数] 选配项，该 attr 内相同特征最多保留数目，默认值为-1（全部保留）
      + `ratio`: [double][动态参数] 选配项，该 attr 内相同特征最多保留比例，默认值为1（全部保留）
      最终取 `ratio` 和 `limit` 中更严格的条件执行过滤截断

      调用示例
      -----
      ``` python
      .yuzhou_low_pass_filter_v2(
        channel_config = [
          {"attr": "author.id", "limit": 10},
          {"attr": "hetu_tag_level_info.hetu_level_one", "ratio": 0.1},
          {"attr": "author.category_detail_first_level_id", "limit": "{{the_limit}}""}
        ]
      )
      ```
      """
      self._add_processor(YuzhouLowPassFilterV2Arranger(kwargs))
      return self
