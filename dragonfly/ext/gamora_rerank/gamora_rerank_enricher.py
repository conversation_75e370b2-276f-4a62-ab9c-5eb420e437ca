#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class GamoraRpcRerankEnsembleSortEnricher(LeafEnricher):
  """
  精选页重排 ensemble sort
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_rerank_ensemble_sort"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_ensemble_sort_candidate_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_ensemble_sort_linear_func")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_ensemble_sort_multiply_func")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_ensemble_sort_pure_score_func")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_ensemble_weight_str")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    attrs.add(self._config["generator_output_attr"])
    return attrs


class GamoraRpcRerankModelEnricher(LeafEnricher):
  """
  精选页重排 model rerank
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_rerank_model"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_predict_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_loss_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_rerank_smooth")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_rerank_temperature")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_rerank_weights")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_enable_with_kgnn_fea")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_kgnn_res_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_enable_rpc_rerank_kai")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_model_kai_shard_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_enable_rpc_rerank_graph_fea")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_graph_feature_prefix")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_graph_feature_cluster_name")))
    attrs.add(self._config.get("send_common_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    for cpxtr in self._config["context_pxtrs"]:
      attrs.add(cpxtr)
    return attrs

class GamoraRpcCrolossLtrModelEnricher(LeafEnricher):
  """
  精选页 croloss ltr
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_croloss_ltr_model"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_croloss_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_croloss_timeout_ms")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_croloss_request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("gamora_rpc_rerank_croloss_loss_name")))
    attrs.add(self._config.get("send_common_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class RerankOptimusRlEnricher(LeafEnricher):
  """
  精选页重排 ensemble sort 序列召回超参搜索
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rerank_optimus_rl_param_predict"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["session_id"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["rl_params_name_attr", "rl_params_value_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class GamoraRpcDegradeLtrModelEnricher(LeafEnricher):
  """
  精选页降级 LTR model
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_degrade_ltr_model"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_common_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class GamoraRpcRerankMulFuncEnricher(LeafEnricher):
  """
  精选页重排乘法公式 Processor
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_rerank_mul_func_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class GamoraRpcRerankGateScoreEnricher(LeafEnricher):
  """
  精选页重排GateScore计算 Processor
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_rerank_mul_func_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs

class GamoraRpcRerankRussianDollsEnricher(LeafEnricher):
  """
  精选页重排俄罗斯套娃探索 Processor
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gamora_rpc_rerank_russian_dolls_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs
  
class GamoraRpcListWiseLtrModelEnricher(LeafEnricher):
  """
  精选页级联listwise ltr model
  """
  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "gamora_listwise_ltr_model"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_common_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("send_item_attr"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_attr"])
    return attrs
