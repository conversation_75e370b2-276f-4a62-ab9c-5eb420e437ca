#!/usr/bin/env python3
# coding=utf-8
"""
filename: gamora_rerank_api_mixin.py
description:
author: <EMAIL>
date: 2021-08-29 21:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .gamora_rerank_enricher import *

class GamoraRerankApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 gamora_rerank 相关的 Processor 接口

  背景: 以前精选页重排是在混排阶段实现的，容易影响其他业务，现在准备迁移到 RPC 独立服务

  场景: 精选页重排模块排序专用 Processor

  维护人: huxiaolong

  """

  def gamora_rpc_rerank_ensemble_sort(self, **kwargs):
    """
    GamoraRpcRerankEnsembleSortEnricher

    重排 ensemble sort
    
    """
    self._add_processor(GamoraRpcRerankEnsembleSortEnricher(kwargs))
    return self

  def gamora_rpc_rerank_model(self, **kwargs):
    """
    GamoraRpcRerankModelEnricher

    重排 model rerank
    """
    self._add_processor(GamoraRpcRerankModelEnricher(kwargs))
    return self

  def gamora_rpc_croloss_ltr_model(self, **kwargs):
    """
    GamoraRpcCrolossLtrModelEnricher

    rpc croloss ltr model
    """
    self._add_processor(GamoraRpcCrolossLtrModelEnricher(kwargs))
    return self
  
  def gamora_rpc_rerank_rl_model(self, **kwargs):
    """
    RerankOptimusRlEnricher

    重排 rl model rerank
    """
    self._add_processor(RerankOptimusRlEnricher(kwargs))
    return self

  def gamora_rpc_degrade_ltr_model(self, **kwargs):
    """
    GamoraRpcDegradeLtrModelEnricher

    降级 LTR model
    """
    self._add_processor(GamoraRpcDegradeLtrModelEnricher(kwargs))
    return self

  def gamora_rpc_rerank_mul_func(self, **kwargs):
    """
    GamoraRpcRerankMulFuncEnricher

    重排
    """
    self._add_processor(GamoraRpcRerankMulFuncEnricher(kwargs))
    return self
  
  def gamora_rpc_rerank_gate_score(self, **kwargs):
    """
    GamoraRpcRerankGateScoreEnricher

    重排
    """
    self._add_processor(GamoraRpcRerankGateScoreEnricher(kwargs))
    return self

  def gamora_rpc_rerank_russian_dolls(self, **kwargs):
    """
    GamoraRpcRerankRussianDollsEnricher

    重排
    """
    self._add_processor(GamoraRpcRerankRussianDollsEnricher(kwargs))
    return self
  
  def gamora_rpc_listwise_ltr_model(self, **kwargs):
    """
    GamoraRpcListWiseLtrModelEnricher
    重排级联 LTR
    """
    self._add_processor(GamoraRpcListWiseLtrModelEnricher(kwargs))
    return self
