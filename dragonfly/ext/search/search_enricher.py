#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types,check_arg
from ...common_leaf_processor import LeafEnricher

class SearchProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_search_profile"

  @strict_types
  def _check_config(self) -> None:
    input_source_type = self._config.get("input_source_type")
    input_attr_name = self._config.get("input_attr_name") 
    check_arg(input_source_type, "`input_source_type` 是必选项")
    if input_source_type and input_source_type in ('common_attr','item_attr'):
      check_arg(input_attr_name, '`input_source_type` in {"common_attr", "item_attr"} 时， `input_attr_name` 是必选项')
    check_arg(self._config.get("timeout_ms"), "`timeout_ms` 是必选项")
    check_arg(self._config.get("biz"), "`biz` 是必选项")
    check_arg(self._config.get("profile_type"), "`profile_type` 是必选项")
    check_arg(self._config.get("features"), "`features` 是必选项")


  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config['input_source_type'] == 'common_attr':
      return set([self._config['input_attr_name']])
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config['input_source_type'] == 'item_attr':
      return set([self._config['input_attr_name']])
    else:
      return set()
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config['input_source_type'] == 'common_attr':
      return set([f['output_attr_name'] for f in self._config['features']])
    else:
      return set()


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config['input_source_type'] != 'common_attr':
      return set([f['output_attr_name'] for f in self._config['features']])
    else:
      return set()
  
  @strict_types
  def is_async(self) -> bool:
    return True

class SearchStarryEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_embeddings_by_search_starry"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kess_service"), "`kess_service` 是必选项")
    check_arg(self._config.get("source"), "`source` 是必选项")
    check_arg(self._config.get("timeout_ms"), "`timeout_ms` 是必选项")
    check_arg(self._config.get("vertical_id"), "`vertical_id` 是必选项")
    check_arg(self._config.get("source_photo_attr"), "`source_photo_attr` 是必选项")
    check_arg(self._config.get("dim"), "`dim` 是必选项")
    check_arg(self._config.get("output_photo_attr"), "`output_photo_attr` 是必选项")
    check_arg(self._config.get("output_embeddings_attr"), "`output_embeddings_attr` 是必选项")

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_embeddings_attr"))
    attrs.add(self._config.get("output_photo_attr"))
    return attrs
class StreamAnnGsuEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_embeddings_by_kai_stream_ann"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kess_service"), "`kess_service` 是必选项")
    check_arg(self._config.get("timeout_ms"), "`timeout_ms` 是必选项")
    check_arg(self._config.get("output_embeddings_key"), "`output_embeddings_key` 是必选项")

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("source_photo_attr"))
    attrs.add(self._config.get("user_id_attr"))
    attrs.add(self._config.get("se_q_pv_1d_pv"))
    attrs.add(self._config.get("se_q_pv_7d_pv"))
    attrs.add(self._config.get("q_user_score"))
    attrs.add(self._config.get("se_q_1d_click_cnt"))
    attrs.add(self._config.get("se_q_1d_play_cnt"))
    attrs.add(self._config.get("se_q_1d_long_view_cnt"))
    attrs.add(self._config.get("se_q_1d_follow_cnt"))
    attrs.add(self._config.get("se_q_1d_like_cnt"))
    attrs.add(self._config.get("se_q_7d_play_cnt"))
    attrs.add(self._config.get("se_q_7d_long_view_cnt"))
    attrs.add(self._config.get("se_q_7d_follow_cnt"))
    attrs.add(self._config.get("se_q_7d_like_cnt"))
    attrs.add(self._config.get("se_q_1d_ctr"))
    attrs.add(self._config.get("se_q_1d_lvtr"))
    attrs.add(self._config.get("se_q_7d_click_cnt"))
    attrs.add(self._config.get("se_q_7d_ctr"))
    attrs.add(self._config.get("se_q_7d_lvtr"))
    attrs.add(self._config.get("city_name"))
    attrs.add(self._config.get("city_level"))
    attrs.add(self._config.get("gender"))
    attrs.add(self._config.get("age_segment"))
    attrs.add(self._config.get("normal_mod"))
    attrs.add(self._config.get("query"))
    attrs.add(self._config.get("query_emb_ebr"))
    attrs.add(self._config.get("photo_high_consume"))
    attrs.add(self._config.get("photo_high_consume_mask"))
    attrs.add(self._config.get("refer_photo_id"))
    attrs.add(self._config.get("refer_photo_token_id"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_embeddings_key"))
    return attrs

class StreamAnnSugPrefixEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_prefix_embeddings_by_kai_stream_ann_sug"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kess_service"), "`kess_service` 是必选项")
    check_arg(self._config.get("timeout_ms"), "`timeout_ms` 是必选项")
    check_arg(self._config.get("output_embeddings_key"), "`output_embeddings_key` 是必选项")

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("prefix"))
    attrs.add(self._config.get("user_id_attr"))
    attrs.add(self._config.get("prefix_click_cnt_d1_exp"))
    attrs.add(self._config.get("prefix_ctr_d1_exp"))
    attrs.add(self._config.get("prefix_click_cnt_exp"))
    attrs.add(self._config.get("prefix_ctr_d3_exp"))
    attrs.add(self._config.get("prefix_click_cnt_d7"))
    attrs.add(self._config.get("prefix_ctr_d7"))
    attrs.add(self._config.get("u_gender"))
    attrs.add(self._config.get("u_country_hash"))
    attrs.add(self._config.get("u_city_id"))
    attrs.add(self._config.get("u_city_hash"))
    attrs.add(self._config.get("u_age_segment"))
    attrs.add(self._config.get("prefix_embedding"))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_embeddings_key"))
    return attrs

class SearchGsuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_search_gsu"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config['input_source_type'] == 'common_attr':
      attrs.update(set(self._config['input_attr_name']))
      attrs.add(self._config['top_k_attr'])
      attrs.add(self._config['is_train'])
      attrs.add(self._config['user_embedding_attr'])
      if self._config.get("filter_time_stamp_attr") is not None:
        attrs.add(self._config['filter_time_stamp_attr'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config['input_source_type'] == 'common_attr':
      attrs.update(set([f['output_attr_name'] for f in self._config['features']]))
      attrs.add("filter_time_input")
      attrs.add("filter_time_stamp")
      attrs.add("colossus_filter_size")
      attrs.add("colossus_second_size")
      attrs.add("colossus_first_size")
      attrs.add("real_nearby_num")
    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config['input_source_type'] != 'common_attr':
      return set([f['output_attr_name'] for f in self._config['features']])
    else:
      return set()
  
  @strict_types
  def is_async(self) -> bool:
    return False


class SearchLiveMerchantEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_live_merchant_feat"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("input_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("attrs", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif "name" in attr:
        attrs.add(attr["name"])
      else:
        attrs.add(attr["path"])
    return attrs
  
  @strict_types
  def is_async(self) -> bool:
    return True

class LiveSeAuthorListFromColossusEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_se_author_list_from_colossus"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    prefix = self._config.get("output_prefix", "colossus_author_")
    ret = ["author_id", "play_time_sum", "reward_sum", "reward_count", "unseen_days"]
    ret = set([prefix + x for x in ret])
    return ret

class SearchQueryRewriterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_query_rewritter"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("export_common_attr"), "`export_common_attr` 是必选项")
    check_arg(self._config.get("cmd_list"), "`cmd_list` 是必选项")
    check_arg(self._config.get("params"), "`params` 是必选项")
    check_arg(self._config.get("service_name"), "`service_name` 是必选项")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("log_param")
    ret.add("new_log_level")
    ret.add("debug_logger")
    for value in self._config.get("params", {}).values():
      ret.update(self.extract_dynamic_params(value))
    ret.update(self.extract_dynamic_params(self._config.get("timeout")))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["export_common_attr"])
    for export_config in self._config.get("export_config", []):
      ret.add(export_config["attr_name"])
    return ret

  @strict_types
  def is_async(self) -> bool:
    return True

class SearchQueryInitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_query_init"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("log_param")
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("debug_logger")
    ret.add("new_log_level")
    ret.add("pb_arena")
    return ret

  
class SearchQueryEndEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_query_end"

class SearchRankFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_rank_feature"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("business_name"), "`business_name` 是必选项")
    check_arg(self._config.get("service_name"), "`service_name` 是必选项")
    check_arg(self._config.get("range_end"), "`range_end` 是必选项")
    check_arg(self._config.get("partition_size"), "`partition_size` 是必选项")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("query")

    for value in self._config.get("request_config", {}).values():
      if "{{" in value and "}}" in value:
        # 去除 {{ 和 }} 并输出结果
        result = value.replace("{{", "").replace("}}", "")
        ret.add(result)

    for value in self._config.get("send_common_attrs", []):
      ret.add(value)
    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for value in self._config.get("send_item_attrs", []):
      ret.add(value)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for value in self._config.get("recv_common_attrs", []):
      ret.add(value)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()

    for value in self._config.get("recv_item_attrs", []):
      ret.add(value)

    config = self._config.get("export_config", {})
    values = config.values()
    for value in values:
      ret.add(value)
    return ret
  
class SearchDebugEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_debug"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("debug_config", []):
      if config["is_common"] == True:
        ret.update(config.get("attr_name", []))
    return ret
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for config in self._config.get("debug_config", []):
      if config["is_common"] == False:
        ret.update(config.get("attr_name", []))
    return ret

class PoiUnimatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "poi_unimatch_enricher"

class PoiQuerymatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "poi_querymatch_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {
      "poi_querymatch_version",
      "querymatch_query_embedding"
    }

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {
      "querymatch_doc_embedding"
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      "querymatch_score"
    }

class PoiPhotoUnimatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "poi_photo_unimatch_enricher"

class SearchRawSamplePackageLabelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_raw_sample_package_label"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add("search_play_input_attr")
    return ret

class SeRecoColossusRespFlattenEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
     return "se_reco_colossus_resp_flatten"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if 'timestamp' in self._config:
      ret.add(self._config["timestamp"])
    if 'photo_id' in self._config:
      ret.add(self._config["photo_id"])
    if 'query' in self._config:
      ret.add(self._config["query"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set(self._config["item_fields"].values())
    ret.add(self._config["query_feature_idx"])
    return ret

class SeMerchantAuthorListExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_merchant_author_list_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["valid_play_threshold"])
    ret.add(self._config["lasted_filter_time"])
    ret.add(self._config["only_merchant"])
    ret.add(self._config["only_merchant_invalid"])
    ret.add(self._config["lasted_num"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_invalid_play_pid_list",
      "u_invalid_play_aid_list",
      "u_invalid_play_hetu_tag_list",
      "u_invalid_play_channel_list",
      "u_invalid_play_cluster_id_list",
      "u_invalid_play_timediff_list",
      "u_invalid_play_timestamp_list",
     
      "u_dislike_pid_list",
      "u_dislike_aid_list",
      "u_dislike_hetu_tag_list",
      "u_dislike_channel_list",
      "u_dislike_cluster_id_list",
      "u_dislike_type_list",
      "u_dislike_timediff_list",
      "u_dislike_timestamp_list",

      "u_action_pid_list",
      "u_action_aid_list",
      "u_action_play_time_list",
      "u_action_auto_play_time_list",
      "u_action_hetu_tag_list",
      "u_action_channel_list",
      "u_action_cluster_id_list",
      "u_action_audience_count_list",
      "u_action_type_list",
      "u_action_timediff_list",
      "u_action_label_list"
    }

class SeMerchantLiveFeatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_merchant_author_list_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("from_colossus_sim_v2", False):
      ret.add(self._config["colossus_resp_attr"])
    else:
      for key in ["live_id_from", "author_id_from", "auto_play_time_from",
                  "play_time_from", "hetu_tag_channel_from", "label_from",
                  "timestamp_from", "photo_id_from", "duration_from", "from_colossus_sim_v2"
                  ]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["valid_play_threshold"])
    ret.add(self._config["lasted_filter_time"])
    ret.add(self._config["only_merchant"])
    ret.add(self._config["colossus_type"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_hour_live_play_cnt_list",
      "u_hour_live_vaild_play_cnt_list",
      "u_hour_live_play_time_list",
      "u_hour_live_se_show_cnt_list",
      "u_hour_live_action_cnt_list",

      "u_week_live_play_cnt_list",
      "u_week_live_vaild_play_cnt_list",
      "u_week_live_play_time_list",
      "u_week_live_se_show_cnt_list",
      "u_week_live_action_cnt_list",

      "u_hour_live_all_play_cnt_list",
      "u_hour_live_all_vaild_play_cnt_list",
      "u_hour_live_all_play_time_list",
      "u_hour_live_all_se_show_cnt_list",
      "u_hour_live_all_action_cnt_list",

      "u_week_live_all_play_cnt_list",
      "u_week_live_all_vaild_play_cnt_list",
      "u_week_live_all_play_time_list",
      "u_week_live_all_se_show_cnt_list",
      "u_week_live_all_action_cnt_list",

      "u_hour_photo_play_cnt_list",
      "u_hour_photo_vaild_play_cnt_list",
      "u_hour_photo_play_time_list",
      "u_hour_photo_action_cnt_list",

      "u_week_photo_play_cnt_list",
      "u_week_photo_vaild_play_cnt_list",
      "u_week_photo_play_time_list",
      "u_week_photo_action_cnt_list"
    }

class SearchTextSegmentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_text_segment"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("input_source_type"), "`input_source_type` 是必选项")
    check_arg(self._config.get("input_attr_name") , "`input_attr_name` 是必选项")
    check_arg(self._config.get("output_segment_text_attr"), "`output_segment_text_attr` 是必选项")
    check_arg(self._config.get("output_token_id_attr"), "`output_token_id_attr` 是必选项")
    check_arg(self._config.get("output_type_id_attr"), "`output_type_id_attr` 是必选项")
    check_arg(self._config.get("output_pos_id_attr"), "`output_pos_id_attr` 是必选项")
    check_arg(self._config.get("output_mask_id_attr"), "`output_mask_id_attr` 是必选项")

  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    if self._config['input_source_type'] == 'common_attr':
      return set([self._config['input_attr_name']])
    else:
      return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config['input_source_type'] != 'common_attr':
      return set([self._config['input_attr_name']])
    else:
      return set()
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config['input_source_type'] == 'common_attr':
      ret = set()
      ret.add(self._config['output_segment_text_attr'])
      ret.add(self._config['output_token_id_attr'])
      ret.add(self._config['output_type_id_attr'])
      ret.add(self._config['output_pos_id_attr'])
      ret.add(self._config['output_mask_id_attr'])
      return ret
    else:
      return set()


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config['input_source_type'] != 'common_attr':
      ret = set()
      ret.add(self._config['output_segment_text_attr'])
      ret.add(self._config['output_token_id_attr'])
      ret.add(self._config['output_type_id_attr'])
      ret.add(self._config['output_pos_id_attr'])
      ret.add(self._config['output_mask_id_attr'])
      return ret
    else:
      return set()

  @strict_types
  def is_async(self) -> bool:
    return True
  
class PushTextSegmentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "push_text_segment_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config["sentence_len_limit"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["sentence_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_token_attr", "output_id_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
    
class SeQ2ICateClickV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_merchant_author_list_extract"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["limit_num"])
    ret.add(self._config["query_attr_cate1"])
    ret.add(self._config["query_attr_cate2"])
    ret.add(self._config["query_attr_cate3"])
    ret.add(self._config["query_attr_cate_prob"])
    ret.add(self._config["filter_future_attr"])
    ret.add(self._config["filter_low_prob_attr"])
    ret.add(self._config["user_cate3"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "query_cate_click_match_item_id",
      "query_cate_click_match_timestamp_list",
      "query_cate_click_match_cate1_list",
      "query_cate_click_match_cate2_list",
      "query_cate_click_match_cate3_list",
      "query_cate_click_match_flow_type_list",
      "query_cate_click_match_from_list",
      "query_cate_click_match_price_list",
    }
  
class SearchLiveGoodsFeatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_se_goods_feat_enricher"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_attr_name"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    config = self._config.get("export_config", {})
    values = config.values()
    for value in values:
      ret.add(value)
    return ret
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
"""
  异步版本
"""
class UnifiedLocalRankIndexV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_unified_local_rank_index_v2"
    
  @strict_types
  def _check_config(self) -> None:
    table_name = self._config.get("table_name")
    check_arg(table_name is not None and table_name != "", "`table_name` must be set a non-empty value")
    check_arg(self._config.get("output_attr_names"), "`output_attr_names` must be set")
    primary_key = self._config.get("primary_key")
    check_arg(primary_key is not None and primary_key != "", "`primary_key` must be set a non-empty value")
    output_fields = self._config.get("output_attr_names")
    check_arg(output_fields is not None and len(output_fields) > 0, "`output_attr_names` must be set and not empty")

  def is_common(self):
    return self._config.get("is_common_attr", False)
  
  @strict_types
  def depend_on_items(self) -> bool:
    return not self.is_common()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      attrs.add(self._config["primary_key"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self.is_common():
      for kv in self._config["output_attr_names"]:
        attrs.add(kv['export_name'])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      attrs.add(self._config["primary_key"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self.is_common():
      for kv in self._config["output_attr_names"]:
        attrs.add(kv['export_name'])
    return attrs

class SearchRspDumpEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_rsp_dump_enricher"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for value in self._config.get("dump_common_attrs", []):
      ret.add(value)
    ret.update(self.extract_dynamic_params(self._config.get("business")))
    return ret


class SearchGoodsAgeIntentTransformEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_goods_age_intent_transform_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("input_age_segment_attr", ""))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_age_attr", ""))
    return ret


class SearchGoodsGenderIntentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_goods_gender_intent_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("query_attr", ""))
    ret.add(self._config.get("input_gender_attr", ""))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("output_gender_attr", ""))
    return ret


class SearchGoodsTextSegmentEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_goods_text_segment_enrich"
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("input_attr_name"), "`input_attr_name` 是必选项")
    check_arg(self._config.get("token_ids_attr_name") , "`token_ids_attr_name` 是必选项")
    check_arg(self._config.get("token_type_attr_name"), "`token_type_attr_name` 是必选项")
    check_arg(self._config.get("token_pos_attr_name"), "`token_pos_attr_name` 是必选项")
    check_arg(self._config.get("token_mask_attr_name"), "`token_mask_attr_name` 是必选项")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", False):
      attrs.add(self._config["input_attr_name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if not self._config.get("is_common_attr", False):
      attrs.add(self._config["token_ids_attr_name"])
      attrs.add(self._config["token_type_attr_name"]) 
      attrs.add(self._config["token_pos_attr_name"]) 
      attrs.add(self._config["token_mask_attr_name"]) 
    return attrs
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", False):
      attrs.add(self._config["input_attr_name"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("is_common_attr", False):
      attrs.add(self._config["token_ids_attr_name"])
      attrs.add(self._config["token_type_attr_name"]) 
      attrs.add(self._config["token_pos_attr_name"]) 
      attrs.add(self._config["token_mask_attr_name"]) 
    return attrs

class SearchGoodsQueryDealPriceSegEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "search_goods_query_deal_price_seg_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        ret.update(
            {
                "query_avg_price_limit",
                "q_m_q_deal_price",
            }
        )
        return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        ret = set()
        ret.update(
            {
                "is_top_query",
                "min_limit",
                "max_limit",
            }
        )
        return ret

class SearchAbtestSessionEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_search_abtest_session"
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("abtest_list"), "`abtest_list` 是必选项")
    check_arg(self._config.get("distribute_abtest_list"), "`distribute_abtest_list` 是必选项")
    check_arg(self._config.get("abtest_config"), "`abtest_config` 是必选项")
    check_arg(self._config.get("vertical_source"), "`vertical_source` 是必选项")
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["vertical_source"])
    ret.add(self._config["abtest_list"])
    return ret
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for config in self._config.get("abtest_config", []):
      ret.add(config["key"])
    ret.add(self._config["distribute_abtest_list"])
    return ret

class SearchFetchTowerTopNDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_fetch_tower_topn_dot_product_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    ret.update({"user_id"})
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_hetu_tag"]:
      if key in self._config:
        ret.add(self._config[key])
    if not self._config.get("use_item_key_as_embed_key", True):
      ret.add(self._config["item_embedding_key_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    ret.add(self._config.get("item_pxtr_label_attr", "return_pxtr_label"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class SearchDistributionCalibEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_distribution_calib_enricher"
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("ctr_attr_name"), "`ctr_attr_name` 是必选项")
    check_arg(self._config.get("cvr_attr_name"), "`cvr_attr_name` 是必选项")
    check_arg(self._config.get("pos_attr_name"), "`pos_attr_name` 是必选项")

    check_arg(self._config.get("clk_label_attr_name"), "`clk_label_attr_name` 是必选项")
    check_arg(self._config.get("order_label_attr_name"), "`order_label_attr_name` 是必选项")
    check_arg(self._config.get("output_click_attr_name"), "`output_click_attr_name` 是必选项")
    check_arg(self._config.get("output_order_attr_name"), "`output_order_attr_name` 是必选项")

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["ctr_attr_name"])
    attrs.add(self._config["cvr_attr_name"])
    attrs.add(self._config["pos_attr_name"])
    attrs.add(self._config["clk_label_attr_name"])
    attrs.add(self._config["order_label_attr_name"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["output_click_attr_name"])
    attrs.add(self._config["output_order_attr_name"]) 
    return attrs
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs


class SearchGetCallerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_get_caller_enricher"
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("save_service_name"), "`save_service_name` 是必选项")
    check_arg(self._config.get("save_caller_name"), "`save_caller_name` 是必选项")

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["save_service_name"])
    attrs.add(self._config["save_caller_name"])
    return attrs
  
class SearchGoodsPriceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_goods_price_enricher"
    
  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("price_attr"), "`price_attr` 是必选项")
    check_arg(self._config.get("item_id_attr"), "`item_id_attr` 是必选项")
  

  @property
  @strict_types
  def intput_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("timeout")))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_id_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["price_attr"])
    return attrs


class SearchVisionLogCollectorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_vision_log_collector_enricher"
  

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("common_attrs", []):
      attrs.add(attr)
    return attrs
