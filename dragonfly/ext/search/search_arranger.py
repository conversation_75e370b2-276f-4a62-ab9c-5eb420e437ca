#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafArranger

class VgsGoodsNewBreakerArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "vgs_goods_new_breaker_arranger"
    
  # break_item_topN: 多少以内打散； ori_page_no：当前为第几页；break_window_size：打散窗口大小；merchant_break_brand_name：品牌打散；
  # merchant_break_brand_author_id：店铺打散；root_merchant_break_duplicate_num：商品最多可以与前面重复多少次；root_merchant_break_duplicate_topn：
  # root_merchant_break_duplicate_topn：冲突降级
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = super().input_common_attrs
    ret.update({ "break_item_topN", "ori_page_no", "break_window_size", "merchant_break_brand_name", 
                "merchant_break_brand_author_id", "root_merchant_break_duplicate_num", "root_merchant_break_duplicate_topn" 
                })
    return ret
  
  # item_type：商品类型；relevance_score：相关性档位；owner_id：店铺id；spu_id：spu id；暂未使用 == mmu_brand_score 的 ：品牌店铺分；mmu_brand_name：品牌店铺名
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = super().input_item_attrs
    ret.update({"item_type", "relevance_score", "owner_id", "spu_id", "mmu_brand_score", "mmu_brand_name"})
    return ret