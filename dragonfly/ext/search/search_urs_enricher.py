#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher


class SearchGoodsFeatureDumpEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "search_goods_feature_dump_enrich"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        common_dump_configs = self._config["common_dump_configs"]
        for config in common_dump_configs:
            if "is_dump" in config and not config["is_dump"]:
                continue
            if "is_convert_list" in config and config["is_convert_list"]:
                ret.add(config["name"] + "_list")
            else:
                ret.add(config["name"])
        return ret

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        ret = set()
        item_dump_configs = self._config["item_dump_configs"]
        for config in item_dump_configs:
            if "is_dump" in config and not config["is_dump"]:
                continue
            if "is_convert_list" in config and config["is_convert_list"]:
                ret.add(config["name"] + "_list")
            else:
                ret.add(config["name"])
        return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        attrs.add("feature_dump_request")
        return attrs
