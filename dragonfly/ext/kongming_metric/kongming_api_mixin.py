#!/usr/bin/env python3
# coding=utf-8
"""
filename: kongming_api_mixin.py
description: kongming metric dynamic_json_config DSL intelligent builder, common api mixin
author: <EMAIL>
date: 2024-03-15 23:03:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kongming_metric_observer import *
from .kongming_metric_enricher import *

class KongmingMetricApiMixin(CommonLeafBaseMixin):
  """
  KongmingMetric 孔明白盒化 Processor API 接口的 Mixin 实现
  """
  def enrich_by_ep_center(self, **kwargs):
    """
    EpCenterEnricher
    ------
    参数配置
    ------
    `kconf_key`: [string] 必配项（与 kconf_str 二选一），指定配置的 kconf_key。Kconf 必须以 Json 形式配置

    `kconf_str`: [string] 必配项（与 kconf_key 二选一），指定配置的 kconf_str。用于 dragonfly playground 快速 debug 验证
    
    `abtest_biz`: [str] 必配项，获取 abtest 参数时使用的 abtest biz_name

    `import_common`: [list] 选配项，需要导入到 Ep_Center 的 common_attr 名称列表（只支持 int/int_list 类型的 attr），支持 name/as 重命名保存,

    `import_item`: [list] 选配项，需要导入到 Ep_Center 的 item_attr 名称列表（只支持  int/int_list 类型的 attr），支持 name/as 重命名保存。

    `export_flag`: [list] 必配项，将 Ep_Center计算结果导出到同名的 item_attr 中（将以int 类型保存），支持 name/as 重命名保存

    `user_id`: [int] [动态参数] 选配项，用指定的 user_id 值获取 abtest 参数，默认为当前请求所用的 user_id

    `device_id`: [string] [动态参数] 选配项，用指定的 device_id 值获取 abtest 参数，默认为当前请求所用的 device_id
    
    ``` python
    .enrich_by_ep_center(
      kconf_key = merchant_center_key_conf,
      abtest_biz = "THANOS_RECO",
      export_flag = [
        "fc_stragegy_flag"
      ]
    )
    ```
    """
    self._add_processor(EpCenterEnricher(kwargs))
    return self
  
  def gen_kongming_hit_flag(self, **kwargs):
      """
      KongmingMetricHitReportEnricher
      -------
      获取是否命中孔明采样的标记，并存入 common attr
      
      参数配置 
      ------

      `metric_kconf_path`: [string] 业务对应的 kconf 配置，不要直接拷贝其他业务的配置
      
      `service_type`: [string] 服务阶段，默认值是 LEAF [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L22)
      
      `sub_stage`: [string] 服务子阶段，必须填写，例如 LEAF_RETRIEVE; [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L23)

      `hit_flag_attr`: [string] 输出字段，是否命中该阶段的孔明采样，必须填写，例如 hit_kongming_retrive_report

      调用示例
      ------
      ``` python
      .gen_kongming_hit_flag(
          metric_kconf_path="reco.merchantMetric.merchantLiveMetric",
          service_type="LEAF",
          sub_stage="LEAF_RETRIEVE",
          hit_flag_attr="hit_kongming_retrive_report",
      )
      ```
      """
      self._add_processor(KongmingMetricHitReportEnricher(kwargs))
      return self

  def report_kongming_metric(self, **kwargs):
      """
      CommonRecoKongmingMetricObserver
      -------
      发送白盒化指标到 kafka，取代report_metric_to_kafka，目前使用 [MetricLog](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L94)格式，使用前请先联系作者。
      
      参数配置 
      ------
      `item_type`: [string] 每个 item 的类型，例如: VIDEO(视频), LIVE, GOOD;[代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L14)

      `metric_kconf_path`: [string] 业务对应的 kconf 配置，不要直接拷贝其他业务的配置

      `owner_id_attr_name`: [string] 类似 authord Id 的 item attr name
      
      `common_attr_names`: [list] 需要的 commonAttr 的指标名, 暂不支持 list 类型的特征, 需要在白名单中配置才能生效。
      
      `long_metric_attr_names`: [list] long/int 类型的指标名
      
      `double_metric_attr_names`: [list] double/float 类型的指标名

      `item_attr_names`: [list] 需要的 itemAttr 的指标名, 如果是list类型默认长度为3, 或在白名单中配置

      `item_must_sample_names`: [list] 采集阶段有此 itemAttr 的请求必采集
      
      `service_type`: [string] 服务阶段，默认值是 LEAF [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L22)
      
      `sub_stage`: [string] 服务子阶段，必须填写，例如 LEAF_RETRIEVE; [代码位置](https://git.corp.kuaishou.com/reco/kuaishou-reco-biz-split-proto/-/blob/master/kuaishou-reco-biz-split-log-base-proto/src/main/proto/kuaishou/reco/log/metric/reco_metric_log.proto#L23)
      
      `product_common_attr_name`: [string] request 请求中的 product_name 字段名

      `sample_by_common_attr_name`: [string] common_attr 名， 如果此 attr 值 为 1, 则本次请求根据 kconf 中配置的 sampleByCommonAttrRatio 采样率进行采样

      调用示例
      ------
      ``` python
      .report_kongming_metric(
          item_type="LIVE",
          metric_kconf_path="reco.merchantMetric.merchantLiveMetric",
          owner_id_attr_name="author_id",
          common_attr_names=["common_attr1", "common_attr2"]
          long_metric_attr_names=[
            "long_metric_name1",
            "long_metric_name2"
          ],
          double_metric_attr_names=[
            "double_metric_name1",
            "double_metric_name2"
          ],
          item_attr_names=[
            "item_attr_name1",
            "item_attr_name2"
          ],
          service_type="LEAF",
          sub_stage="LEAF_RETRIEVE",
          product_common_attr_name="app_source_type"
      )
      ```
      """
      self._add_processor(KongmingMetricObserver(kwargs))
      return self
  
  def report_kongming_metric_v2(self, **kwargs):
      """
      KongmingMetricV2Observer
      -------
      发送白盒化指标到 kafka, 使用前请先联系作者。
      
      参数配置 
      ------
      `service_type`: [必填][string] 服务阶段，默认值是 LEAF
      
      `sub_stage`: [必填][string] 服务子阶段
      
      `item_type`: [string] item 的类型，例如: VIDEO, LIVE, GOOD

      `metric_kconf_path`: [string] 业务 kconf 配置 禁止直接拷贝其他业务的配置!!!

      `owner_id_attr_name`: [string] author_id/seller_id

      `product_common_attr_name`: [string] request 请求中的 product_name 字段名
      
      `common_attr_names`: [list] 上报的 common_attr

      `item_attr_names`: [list] 上报的 item_attr, 如果是 list 类型默认截断长度3, 或在 metric_kconf 中配置

      `sample_by_common_attr_name`: [string] common_attr 名, 如果此 attr 值为 1, 则根据 metric_kconf 中配置的 sample_by_common_attr_ratio 采样率进行采样

      调用示例
      ------
      ``` python
      .report_kongming_metric(
          item_type="LIVE",
          metric_kconf_path="reco.merchantMetric.merchantLiveMetric",
          owner_id_attr_name="author_id",
          common_attr_names=["common_attr1", "common_attr2"]
          item_attr_names=["item_attr_name1", "item_attr_name2"],
          service_type="LEAF",
          sub_stage="LEAF_RETRIEVE",
          product_common_attr_name="app_source_type"
      )
      ```
      """
      self._add_processor(KongmingMetricV2Observer(kwargs))
      return self