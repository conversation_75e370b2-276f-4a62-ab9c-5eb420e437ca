#!/usr/bin/env python3
"""
filename: kfs_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for KFS
author: <EMAIL>
date: 2021-07-28
"""
from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafEnricher

class EpCenterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_ep_center"
  
  @strict_types
  def _check_config(self) -> None:
    check_arg(bool("kconf_key" in self._config) or bool("kconf_str" in self._config), "kconf_key / kconf_str 需要配置（二选一）")
    if bool("kconf_key" in self._config) and bool("kconf_str" in self._config):
      check_arg(False, "kconf_key, kconf_str 不能同时配置（二选一）")
    if bool("kconf_key" in self._config) and not self._config.get("kconf_key"):
      check_arg(False, "kconf_key 不能为空字符串")
    if bool("kconf_str" in self._config) and not self._config.get("kconf_str"):
      check_arg(False, "kconf_str 不能为空字符串")
    check_arg("abtest_biz" in self._config, "必须配置 ep center 的 abtest_biz")
    check_arg(len(self._config["export_flag"]) > 0, "ep center 的 export_flag 不可为空")

  @strict_types
  def depend_on_items(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("import_common",[]), "name") 
    attrs.update(self.extract_dynamic_params(self._config.get("user_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("device_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("perf_tag")))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("import_item",[]),"name")
  
  @property
  def output_item_attrs(self) -> set:
    ret = set()
    config = self._config.get("export_flag",[])
    for c in config:
      if isinstance(c, str) :
        ret.add(c)
      else:
        raise ArgumentError(f"list 中配置了不支持的类型 {type(c)}: {config}")
    return ret

class KongmingMetricHitReportEnricher(LeafEnricher): 
 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    # 返回该 processor_api 名的字符串，需与 api_mixin.py 中的 processor_api 保持一致
    return "gen_kongming_hit_flag"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    check_arg(self._config.get("metric_kconf_path"), "`metric_kconf_path` 是必选项")
    check_arg(self._config.get("service_type"), "`service_type` 是必选项")
    check_arg(self._config.get("sub_stage"), "`sub_stage` 是必选项")
    check_arg(self._config.get("hit_flag_attr"), "`hit_flag_attr` 是必选项")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return False
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 输出所存储的 common_attrs 集合
    return set([self._config["hit_flag_attr"]])