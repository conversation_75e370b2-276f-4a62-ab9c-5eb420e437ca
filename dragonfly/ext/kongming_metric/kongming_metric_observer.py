#!/usr/bin/env python3
# coding=utf-8
"""
filename: kongming_metric_processor.py
description: kongming metric dynamic_json_config DSL intelligent builder, observer module
author: <EMAIL>
date: 2024-03-16 00:45:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names
from ...common_leaf_processor import LeafObserver

class KongmingMetricObserver(LeafObserver): 
 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    # 返回该 processor_api 名的字符串，需与 api_mixin.py 中的 processor_api 保持一致
    return "report_kongming_metric"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    check_arg(self._config.get("item_type"), "`item_type` 是必选项")
    check_arg(self._config.get("metric_kconf_path"), "`metric_kconf_path` 是必选项")
    check_arg(self._config.get("owner_id_attr_name"), "`owner_id_attr_name` 是必选项")
    check_arg(isinstance(self._config.get("common_attr_names"), list), "`common_attr_names` 是必选项")
    check_arg(isinstance(self._config.get("long_metric_attr_names"), list), "`long_metric_attr_names` 是必选项")
    check_arg(isinstance(self._config.get("double_metric_attr_names"), list), "`double_metric_attr_names` 是必选项")
    check_arg(self._config.get("service_type"), "`service_type` 是必选项")
    check_arg(self._config.get("sub_stage"), "`sub_stage` 是必选项")
    check_arg(self._config.get("product_common_attr_name"), "`product_common_attr_name` 是必选项")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    attrs = set()
    common_attr_names = self._config.get("common_attr_names", [])
    for name in common_attr_names:
      attrs.add(name)
    attrs.add(self._config["product_common_attr_name"])
    sample_by_attr_name = self._config.get("sample_by_common_attr_name", "")
    if len(sample_by_attr_name) > 0:
      attrs.add(sample_by_attr_name)
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 输出所存储的 common_attrs 集合
    return set()

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    # 表明该 processor 是否是异步的，默认值为 False，当该 processor 为异步时，需要重写这个函数将值设为 True
    # zstd 这一 processor 不是异步的，这里也可以不写这一函数
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 item_attrs
    attrs = set()
    attrs.add(self._config["owner_id_attr_name"])
    long_metric_attr_names = self._config.get("long_metric_attr_names", [])
    for name in long_metric_attr_names:
      attrs.add(name)
    double_metric_attr_names = self._config.get("double_metric_attr_names", [])
    for name in double_metric_attr_names:
      attrs.add(name)
    item_attr_names = self._config.get("item_attr_names", [])
    for name in item_attr_names:
      attrs.add(name)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # 该 processor 输出所存储的 item_attrs
    return set()

class KongmingMetricV2Observer(LeafObserver): 
 
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    # 返回该 processor_api 名的字符串，需与 api_mixin.py 中的 processor_api 保持一致
    return "report_kongming_metric_v2"
  
  @strict_types
  def _check_config(self) -> None:
    # 用于检查 dragonfly 脚本中，对于该 processor 调用的配置是否合法，包括某个参数是否为字符串，一些必填的参数是否都填了等等
    check_arg(self._config.get("item_type"), "`item_type` 是必选项")
    check_arg(self._config.get("metric_kconf_path"), "`metric_kconf_path` 是必选项")
    check_arg(self._config.get("owner_id_attr_name"), "`owner_id_attr_name` 是必选项")
    check_arg(isinstance(self._config.get("common_attr_names"), list), "`common_attr_names` 是必选项")
    check_arg(isinstance(self._config.get("item_attr_names"), list), "`item_attr_names` 是必选项")
    check_arg(self._config.get("service_type"), "`service_type` 是必选项")
    check_arg(self._config.get("sub_stage"), "`sub_stage` 是必选项")
    check_arg(self._config.get("product_common_attr_name"), "`product_common_attr_name` 是必选项")
  
  @strict_types
  def depend_on_items(self) -> bool:
    # 表明该 processor 是否依赖于 items ，绝大多数情况为 True，当该 processor 不依赖于任何 items 时，需要重写这个函数将值设为 False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    attrs = set()
    common_attr_names = self._config.get("common_attr_names", [])
    for name in common_attr_names:
      attrs.add(name)
    attrs.add(self._config["product_common_attr_name"])
    sample_by_attr_name = self._config.get("sample_by_common_attr_name", "")
    if len(sample_by_attr_name) > 0:
      attrs.add(sample_by_attr_name)
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 输出所存储的 common_attrs 集合
    return set()

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
    # 表明该 processor 是否是异步的，默认值为 False，当该 processor 为异步时，需要重写这个函数将值设为 True
    # zstd 这一 processor 不是异步的，这里也可以不写这一函数
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    # 该 processor 依赖的 item_attrs
    attrs = set()
    attrs.add(self._config["owner_id_attr_name"])
    item_attr_names = self._config.get("item_attr_names", [])
    for name in item_attr_names:
      attrs.add(name)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    # 该 processor 输出所存储的 item_attrs
    return set()