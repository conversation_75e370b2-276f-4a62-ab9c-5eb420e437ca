#!/usr/bin/env python3
# coding=utf-8
"""
filename: news_observer.py
description: news_common_leaf dynamic_json_config DSL intelligent builder, observer module
author: <PERSON><EMAIL>
date: 2021-09-15 14:35:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class NewsWaterfallExportObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "export_attr_to_waterfall"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set(self._config.get("common_attrs", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("sub_stage"), str) and self._config["sub_stage"],
              "sub_stage 需为非空字符串")
    check_arg(isinstance(self._config.get("class_name"), str) and self._config["class_name"],
              "class_name 需为非空字符串")
    check_arg(isinstance(self._config.get("env"), str) and self._config["env"],
              "env 需为非空字符串")
    if self._config.get("kafka_topic"):
      check_arg(isinstance(self._config.get("kafka_topic"), str) and self._config["kafka_topic"],
                "kafka_topic 需为非空字符串") 
    if self._config.get("send_common_attrs"):
      check_arg(isinstance(self._config.get("common_attrs"), list), "common_attrs 需为 list 类型")
    if self._config.get("send_item_attrs"):
      check_arg(isinstance(self._config.get("item_attrs"), list), "item_attrs 需为 list 类型")
      
