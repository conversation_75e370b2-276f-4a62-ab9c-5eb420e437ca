#!/usr/bin/env python3
# coding=utf-8
"""
filename: news_api_mixin.py
description: mews_common_leaf dynamic_json_config DSL intelligent builder,news api mixin
author: <PERSON><PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-09-09 16:00:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .news_retriever import *
from .news_observer import *
from .news_enricher import *

class NewsApiMixin(CommonLeafBaseMixin):
  """
  news_common_leaf Processor 相关接口
  """

  def share_chat_retrieve_by_need_field_num(self, **kwargs):
    """
    ShareChatRemoteRetriever
    ------
    从 关系链中台服务 召回各类关系类型

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 100ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .share_chat_retrieve_by_need_field_num(
      # 召回双关列表
      reason = 1,
      need_field_num = 1,
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 100,
      add_reason_to_attr = "all_reason_list",
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "u_friend_list_size", 
        "u_follow_list_size", 
        "u_fans_list_size",
        "u_wechat_friend_list_size",
        "u_wechat_friend_list",
      ],
      output_item_attrs = [
        "item_type",
      	"f_intimate_score",
        "is_friend",
        "is_follow",
        "is_fans",
      ],
    )
    ```
    """
    self._add_processor(ShareChatRemoteRetriever(kwargs))
    return self

  def news_photo_retrieve_by_need_field_num(self, **kwargs):
    """
    NewsPhotoRemoteRetriever
    ------
    从 关系链中台服务 召回朋友动态视频

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 200ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .news_photo_retrieve_by_need_field_num(
      # 召回朋友点赞视频列表
      reason = 52,
      need_field_num = 52,
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      add_reason_to_attr = "all_reason_list",
      input_common_attrs = [
        "news_self_visible_user_list",
        "news_friends_visible_user_list",
        "news_in_banded_user_not_visible_user_list",
        "news_not_open_collect_visible_user_list",
        "news_is_privacy_user_list",
        ], 
      output_common_attrs = ["p_like_photo_retrieve_size",
        "u_first_tag_info_list", 
        "u_second_tag_info_list", 
        "u_third_tag_info_list",
        ],
      output_item_attrs = [
        "item_type",
      	"author_id",
        "p_retriever_photo_age",
        "p_retriever_upload_time_ms",
        "p_retriever_duration_ms",
      	"p_retriever_like_cnt",
      	"p_like_last_user_id",
        "p_like_last_user_action_timestamp",
        "p_like_friend_cnt",
        "p_like_user_id_list",
        "p_like_user_action_timestamp_list",
        "p_is_hit_user_interest_tag",
        "p_hit_user_interest_tag_id",
        "p_mmu_hetu_tag_v2_L1",
        "p_mmu_hetu_tag_v2_L2",
        "p_mmu_hetu_tag_v2_L3",
        "p_mmu_hetu_tag_v2_ip",
        "p_mmu_hetu_tag_v2_content",
        "r_max_friend_relation_score",
        "r_sum_friend_relation_score",
        "r_avg_friend_relation_score"
      ],
    )
    ```
    """
    self._add_processor(NewsPhotoRemoteRetriever(kwargs))
    return self

  def news_photo_local_calculate_retriever_by_need_field_num(self, **kwargs):
    """
    NewsPhotoLocalCalculateRetriever
    ------
    从 分布式图服务获取朋友动态视频

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，与关系链中台的召回数据编号对应，每个编号对应唯一一种召回数据

    `news_pool_max_size`: [int] 必配项，朋友动态视频池计算大小，默认值 15000

    `need_content_relation_type`: [int] 必配项，获取动态视频类型，1 表示取社交视频，2 表示取消费视频，0 表示都取，默认值 0

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .news_photo_local_calculate_retriever_by_need_field_num(
      # 召回朋友点赞的社交视频列表
      reason = 52,
      need_field_num = 52,
      news_pool_max_size = 15000,
      need_content_relation_type = 1,
      input_common_attrs = [
        "news_self_visible_user_list",
        "news_friends_visible_user_list",
        "news_in_banded_user_not_visible_user_list",
        "news_not_open_collect_visible_user_list",
        "news_is_privacy_user_list",
        "u_social_friend_list",
        "request_biz_type",
        "ds_news_common_leaf_user_profile",
        ],
      output_common_attrs = [
        "p_like_photo_retrieve_size",
        ],
      output_item_attrs = [
        "filtered_by_author_id_illegal",
        "filtered_by_photo_private",
        "filtered_by_content_relation_type",
        "filtered_by_author_holdout",
        "is_in_calculate_retriever_final_stage",
        "item_type",
        "author_id",
        "p_score",
        "p_retriever_photo_age",
        "p_retriever_upload_time_ms",
        "p_retriever_duration_ms",
        "p_retriever_like_cnt",
        "p_like_last_user_id",
        "p_like_last_user_action_timestamp",
        "p_like_friend_cnt",
        "p_like_user_id_list",
        "p_like_user_action_timestamp_list",
        "p_like_action_user_max_score",
        "p_like_action_user_sum_score",
        "p_like_action_user_avg_score",
        "p_like_max_score_action_user_id",
        "p_like_max_score_action_user_relation_type",
      ],
    )
    ```
    """
    self._add_processor(NewsPhotoLocalCalculateRetriever(kwargs))
    return self

  def news_livestream_retrieve_by_need_field_num(self, **kwargs):
    """
    NewsLivestreamRemoteRetriever
    ------
    从 关系链中台服务 召回朋友动态直播

    参数配置
    ------
    `reason`: [int] 必配项，召回原因

    `need_field_num`: [int] 必配项，调关系链中台时取的召回数据的编号，每个编号对应唯一一种召回数据

    `remote_recall_limit`: [int] 选配项，调关系链中台时取的召回数据的长度限制，默认值 1500，最大 1500 

    `kess_service`: [string] 必配项，关系链中台服务的 kess 名称

    `timeout_ms`: [int] 必配项，调关系链中台服务的超时时间，建议值 200ms

    `add_reason_to_attr`: [string] 必配项，将召回的所有 item 都追加上当前 Retriever 的 reason 值到指定的 int_list 类型 item_attr 中，这样该配置所指定的 item_attr 中保存了该 item 所有的召回源 reason

    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .news_livestream_retrieve_by_need_field_num(
      # 召回朋友看过直播列表
      reason = 60,
      need_field_num = 60,
      remote_recall_limit = 1500,
      kess_service = "grpc_XXX",
      timeout_ms = 200,
      add_reason_to_attr = "all_reason_list",
      output_common_attrs = ["l_watch_livestream_retrieve_size"],
      output_item_attrs = [
        "item_type",
      	"author_id",
      	"l_start_timestamp",
      	"l_author_fans_num",
      	"l_online_time",
      	"l_watch_last_user_id",
        "l_watch_last_user_action_timestamp",
        "l_watch_friend_cnt",
      	"l_watch_user_id_list",
      	"l_watch_user_action_timestamp_list",
      	"l_author_tag_list",
      	"l_livestream_type_list",
        "l_max_friend_relation_score",
        "l_avg_friend_relation_score",
        "l_sum_friend_relation_score"
      ],
    )
    ```
    """
    self._add_processor(NewsLivestreamRemoteRetriever(kwargs))
    return self

  def news_friends_filming_retrieve_by_grpc(self, **kwargs):
    """
    NewsFriendsFilmingRemoteRetriever
    ------
    从远程服务召回朋友在拍视频

    参数配置
    ------
    `recall_reason`: [int] 选配项，召回原因，默认值 97

    `kess_service`: [string] 选配项，远程服务的 kess 名称

    `timeout_ms`: [int] 选配项，调远程服务的超时时间，默认值 100ms

    `input_common_attrs`: [list] 必配项，string list, 本路召回需要输入的 common_attrs 列表
  
    `output_common_attrs`: [list] 必配项，string list, 本路召回输出的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本路召回输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .news_friends_filming_retrieve_by_grpc(
      # 召回朋友在拍视频
      kess_service = "grpc_XXX",
      timeout_ms = 100,
      input_common_attrs = [
        "user_id",
      ], 
      output_common_attrs = [
        "p_filming_photo_retrieve_size",
      ],
      output_item_attrs = [
        "item_type",
      	"author_id",
        "p_filming_user_id_list",
        "p_filming_friend_cnt",
        "p_filming_last_user_id",
        "p_is_in_filming_recall",
      ],
    )
    ```
    """
    self._add_processor(NewsFriendsFilmingRemoteRetriever(kwargs))
    return self

  def filter_cid_by_browse_set(self, **kwargs):
    """
    NewsBrowseSetFilterByCidListArranger
    ------
    根据 request 中传入的 BrowseSet 内容进行过滤 朋友在看业务自用

    参数配置
    ------
    `check_id_in_attr`: [string] 选配项，从指定的 ItemListAttr 中读取 intlist 值作为当前 item 检查 browse set 过滤用的 item_id（而非该 item 自己的 item_id 值），若 item 无该 ItemAttr 将不会被过滤

    `item_type_of_checked_id`: [int] 选配项，指定 check_id_in_attr 配置中 item_id 对应的 item_type (需大于等于 0)，默认使用与当前 item 一样的 item_type

    `save_filtered_items_to_common_attr`: [string] 选配项，将被 BrowseSet 过滤的 item key 存入指定的 CommonAttr 中，供后续 Processor 取用

    调用示例
    ------
    ``` python
    .filter_cid_by_browse_set()
    # 或者
    .filter_cid_by_browse_set(
      check_id_in_attr="check_id_in_attr_xxx",
    )
    ```
    """
    self._add_processor(NewsBrowseSetFilterByCidListArranger(kwargs))
    return self

  def export_attr_to_waterfall(self, **kwargs):
    """
    NewsWaterfallExportObserver
    ------
    将指定的 CommonAttr 或 ItemAttr 以 waterfall 的格式发送至 Kafka。

    参数配置
    ------
    `kafka_topic`: [string] 选配项，Kafka topic name，默认值 ds_waterfall_log

    `send_common_attrs`: [list] 选配项，需要发送的 CommonAttr 列表

    `send_item_attrs`: [list] 选配项，需要发送的 ItemAttr 列表，注意：若缺省则不会发送 item 的任何数据

    `sub_stage`: [string] 必配项，当前发送日志所属阶段，比如 LEAF_RETRIEVAL、LEAF_ROUGH_RANKING、LEAF_FINE_RANKING、LEAF_ENSEMBLE_SORT、LEAF_VARIANT、LEAF_FINAL_RETURN

    `class_name`: [string] 必配项，当前发送日志时上一个执行的 processor 名

    `env`: [string] 必配项，当前所处环境，比如 online、dryrun、test

    调用示例
    ------
    ``` python
    .export_attr_to_waterfall(
      sub_stage="LEAF_FINAL_RETURN",
      class_name="item_truncate",
      env="online",
      send_common_attrs=["u_browse_set_size", "u_partner_list_size"],
      send_item_attrs=["author_id", "final_reason"],
    )
    ```
    """
    self._add_processor(NewsWaterfallExportObserver(kwargs))
    return self

  def enrich_share_chat_user_profile_attr_by_kcc(self, **kwargs):
    """
    ShareChatUserProfileEnricher
    ------
    私信推荐 用户和好友的用户画像基础特征 抽取。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_user_profile_attr_by_kcc(
      input_common_attrs=["user_id"],
      output_common_attrs=[
        "u_fan_cnt",
        "u_follow_cnt",
        "u_age_num",
        "u_city_origin",
        "u_city",
        "u_products_7days",
        "u_products_30days",
        "u_activates_7days",
        "u_activates_30days",
        "u_active_degree_detail",
      ], 
      input_item_attrs=["item_type"], 
      output_item_attrs=[
        "is_same_gender",
        "is_same_age",
        "is_same_city",
        "is_same_city_origin",
        "is_has_head",
        "is_has_photo",
        "f_fan_cnt",
        "f_age_num",
        "f_city_origin",
        "f_city",
        "f_products_7days",
        "f_products_30days",
        "f_activates_7days",
        "f_activates_30days",
        "f_has_modify_user_name",
        "f_active_degree_detail",
      ],
    )
    ```
    """
    self._add_processor(ShareChatUserProfileEnricher(kwargs))
    return self

  def enrich_share_chat_user_profile_attr_v2_by_kcc(self, **kwargs):
    """
    ShareChatUserProfileV2Enricher
    ------
    私信推荐 用户和好友的用户画像基础特征 抽取。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_user_profile_attr_v2_by_kcc(
      input_common_attrs=["user_id"],
      output_common_attrs=[
        "u_province",
        "u_province_origin",
      ], 
      input_item_attrs=["item_type"], 
      output_item_attrs=[
        "is_same_province",
        "is_same_province_origin",
        "f_province",
        "f_province_origin",
      ],
    )
    ```
    """
    self._add_processor(ShareChatUserProfileV2Enricher(kwargs))
    return self

  def enrich_share_chat_user_action_history_attr_by_kcc(self, **kwargs):
    """
    ShareChatUserActionHistoryEnricher
    ------
    私信推荐 用户历史行为特征 抽取。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_user_action_history_attr_by_kcc(
      input_common_attrs=["user_id"],
      output_common_attrs=[
        "u_share_friend_list",
        "u_like_friend_list",
        "u_comment_at_friend_list",
      ],
    )
    ```
    """
    self._add_processor(ShareChatUserActionHistoryEnricher(kwargs))
    return self

  def enrich_share_chat_social_enricher(self, **kwargs):
    """
    ShareChatSocialEnricher
    ------
    私信推荐 用户和作者、好友和作者的社交关系 抽取。

    参数配置
    ------
    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_social_enricher(
      output_common_attrs=[
        "is_user_follow_author",
        "is_user_bifollow_author",
        "is_friend_follow_author",
        "is_friend_bifollow_author",
      ],
    )
    ```
    """
    self._add_processor(ShareChatSocialEnricher(kwargs))
    return self

  def author_fans_bifollow_list_enricher(self, **kwargs):
    """
    ShareChatAuthorFansBifollowListEnricher
    ------
    私信推荐 作者双关列表、粉丝列表获取

    参数配置
    ------
    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_social_enricher(
      output_common_attrs=[
        "author_fans_list",
        "author_bifollow_list",
      ],
    )
    ```
    """
    self._add_processor(ShareChatAuthorFansBifollowListEnricher(kwargs))
    return self

  def enrich_share_chat_friend_consume_list_attr_by_kgraph(self, **kwargs):
    """
    ShareChatFriendConsumeListEnricher
    ------
    私信推荐 用户历史消费内容列表 抽取。

    参数配置
    ------
    `need_max_outedge_size`: [int] 必配项, 默认值 -20

    `need_field_num`: [int] 必配项, 默认值 52

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 需要输出的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_friend_consume_list_attr_by_kgraph(
      need_max_outedge_size=-20,
      need_field_num=52,
      input_common_attrs=["user_id"],
      output_common_attrs=["user_newest_like_photo_list"], 
      input_item_attrs=["item_type"], 
      output_item_attrs=[
        "friend_newest_like_photo_list",
      ],
    )
    ```
    """
    self._add_processor(ShareChatFriendConsumeListEnricher(kwargs))
    return self

  def enrich_share_chat_item_attr_by_common_attr(self, **kwargs):
    """
    ShareChatItemAttrEnricher
    ------
    私信推荐 item 特征抽取。

    参数配置
    ------
    `is_open_calculate_wechat_list`: [int] 必配项, 默认值 1

    `is_open_parse_dnmfrlp_proto`: [int] 必配项, 默认值 1

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_share_chat_item_attr_by_common_attr(
      is_open_calculate_wechat_list=1,
      is_open_calculate_wechat_list=1,
      input_common_attrs=["ds_news_message_friend_reco_leaf_params","u_wechat_friend_list"],
      output_item_attrs=[
        "is_online", 
        "relation_score_rank",
        "is_same_gender",
        "is_same_age",
        "is_same_city",
        "is_friend_alias",
        "same_hetu_cnt",
        "send_cnt_7_day_rank",
        "user_send_time_rank",
      ],
    )
    ```
    """
    self._add_processor(ShareChatItemAttrEnricher(kwargs))
    return self

  def enrich_by_live_predict_grpc(self, **kwargs):
    """
    LiveRankPredictAttrEnricher
    ------
    从远程直播 predict grpc 服务获取直播的 xtr 预测分。

    参数配置
    ------
    `item_type`: [int] 必配项，为 0 表示直播

    `cluster_name`: [string] 必配项，调用远程直播预测服务的环境，PRODUCTION 表示线上环境

    `server_name`: [string] 必配项，调用远程直播预测服务的 kess 名

    `timeout_ms`: [int] 必配项，调用远程直播预测服务的最大超时时间，默认值 200ms

    `request_type`: [string] 必配项，调用远程直播预测服务的 request_type，建议值 default

    `shard_size`: [int] 必配项，每次调用远程直播预测服务输入的 item 列表大小，默认值 100

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_live_predict_grpc(
      item_type=0,
      cluster_name="PRODUCTION",
      server_name="grpc_GeneralLiveCommonLeafPredictServerSimExp",
      timeout_ms=150,
      request_type="default",
      shard_size=75,
      input_common_attrs=["news_livestream_user_common_attr"],
      output_item_attrs=[
        "fr_pctr", 
        "fr_pltr",
        "fr_pwtr",
        "fr_plvtr",
        "fr_pdctr",
        "fr_phtr",
        "fr_pgtr",
        "fr_pcmtr",
        "fr_pwatchtime",
        "fr_pbenefit",
        "fr_psstr",
        "fr_prtr",
        "fr_pintr",
        "fr_pinwatchtime",
        "fr_poutwatchtime",
      ],
    )
    ```
    """
    self._add_processor(LiveRankPredictAttrEnricher(kwargs))
    return self

  def enrich_by_live_ensemble_sort(self, **kwargs):
    """
    LivestreamEnsembleSortEnricher
    ------
    直播 ensemble sort 多队列打分模块。

    参数配置
    ------
    `timeout_ms`: [int] 选配项，调 redis 超时时间，默认值 100ms

    `redis_cluster_name`: [string] 选配项，该模块取特征的 redis 集群，默认值 dsNewsPushCache

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_live_ensemble_sort(
      timeout_ms=60,
      input_common_attrs=[
        "news_livestream_follow_score_weight",
        "news_livestream_lvtr_score_weight",
        "news_livestream_friend_cnt_score_weight",
        ],
      output_item_attrs=[
        "l_follow_score", 
        "l_lvtr_score",
        "l_friend_cnt_score",
        "l_ensemble_score",
        "l_follow_index",
        "l_lvtr_index",
        "l_friend_cnt_index",
      ],
      output_common_attrs=[
        "l_ensemble_weight", 
      ],      
    )
    ```
    """
    self._add_processor(LivestreamEnsembleSortEnricher(kwargs))
    return self

  def enrich_by_live_samplelist_grpc(self, **kwargs):
    """
    NewsLivestreamUserAttrEnricher
    ------
    从 samplelist 服务获取用户直播画像信息。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_live_samplelist_grpc(
      input_common_attrs=[
        "ds_news_user_profile",
        ],
      output_common_attrs=[
        "news_livestream_user_common_attr", 
      ],      
    )
    ```
    """
    self._add_processor(NewsLivestreamUserAttrEnricher(kwargs))
    return self

  def enrich_by_photo_ensemble_sort(self, **kwargs):
    """
    PhotoEnsembleSortEnricher
    ------
    直播 ensemble sort 多队列打分模块。

    参数配置
    ------
    `timeout_ms`: [int] 选配项，调 redis 超时时间，默认值 100ms

    `redis_cluster`: [string] 选配项，该模块取特征的 redis 集群，默认值 dsNewsPushCache

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_photo_ensemble_sort(
      timeout_ms=60,
      input_common_attrs=[
        "ds_news_common_leaf_user_profile",
      ],
      input_item_attrs=[
        "author_id",
        "p_hetu_tag",
        "p_is_long_term_interest",
        "p_long_term_index",
        "p_is_short_term_interest",
        "p_short_term_index",
        "f_uid_list",
        "ns_playratio",
        "ns_evtr",
        "ns_svtr",
        "ns_ltr",
        "ns_playratio_2",
        "ns_lvtr_2",
        "ns_svtr_2",
        "ns_fvtr_2",
        "ns_ltr_2",
      ],      
      output_item_attrs=[
        "e_author_intimacy_score", 
        "e_friend_intimacy_score",
        "e_interest_score",
        "e_friend_counter_score",
        "e_tag_counter_score",
        "e_friend_intimacy_index",
        "e_friend_counter_index",
        "e_interest_tag_index",
        "e_interest_counter_index",
        "ns_playratio2_index",
        "ns_svtr2_index",
        "ns_ltr2_index",
        "p_ensemble_boost_score",
        "p_ensemble_score",
        "ns_lvtr_2_index",
        "ns_fvtr_2_index",
      ],
      output_common_attrs=[
        "e_ensemble_weight",
        "e_boost_weight",
      ]   
    )
    ```
    """
    self._add_processor(PhotoEnsembleSortEnricher(kwargs))
    return self

  def enrich_by_dynamic_photo_store(self, **kwargs):
    """
    PhotoInfoAttrEnricher
    ------
    视频维度特征抽取模块。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_dynamic_photo_store(
      input_common_attrs=[
        "ds_news_common_leaf_user_profile",
        "u_long_term_interest_list",
        "u_short_term_interest_list",
      ],
      input_item_attrs=[
        "author_id",
      ],      
      output_item_attrs=[
        "p_mmu_resolution_score",
        "p_mmu_art_score",
        "p_mmu_only_text_score",
        "p_mmu_nonsense_score",
        "p_mmu_hetu_tag_v2_L1",
        "p_mmu_hetu_tag_v2_L2",
        "p_mmu_hetu_tag_v2_L3",
        "p_mmu_hetu_tag_v2",
        "p_mmu_hetu_tag_v2_ip",
        "p_mmu_hetu_tag_v2_content",
        "p_user_long_term_intersection_cnt",
        "p_user_short_term_intersection_cnt",
        "fans_cnt",
        "p_is_long_term_interest",
        "p_long_term_index",
        "p_is_short_term_interest",
        "p_short_term_index", 
        "p_photo_age",
        "p_photo_id",
        "p_author_id",
        "p_duration_ms",
        "p_show_cnt",
        "p_click_cnt",
        "p_like_cnt",
        "p_follow_cnt",
        "p_forward_cnt",
        "p_unlike_cnt",
        "p_comment_cnt",
        "p_hetu_tag",
        "p_magic_face_id",
        "p_upload_type",
      ],
    )
    ```
    """
    self._add_processor(PhotoInfoAttrEnricher(kwargs))
    return self


  def enrich_by_dynamic_photo_store_for_share_chat(self, **kwargs):
    """
    ShareChatPhotoInfoAttrEnricher
    ------
    分享私信实时请求的视频维度特征抽取模块。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_dynamic_photo_store_for_share_chat(
      input_common_attrs=[
        "photo_id",
      ],
      output_common_attrs=[
        "s_p_hetu_one",
        "s_p_hetu_one",
        "s_p_hetu_three",
        "s_p_hetu_face_id",
        "s_p_hetu_tag",
        "s_p_fans_cnt",
        "s_p_author_gender",
        "s_p_province",
        "s_p_city",
        "s_p_province_id",
        "s_p_city_id",
        "s_p_photo_id",
        "negative_count",
        "like_cnt",
      ],
    )
    ```
    """
    self._add_processor(ShareChatPhotoInfoAttrEnricher(kwargs))
    return self

  def enrich_by_photo_related_hetu_embedding(self, **kwargs):
    """
    PhotoRelatedHetuEmbeddingAttrEnricher
    ------
    当前候选视频与用户历史点赞视频列表的embedding相似度计算。

    参数配置
    ------
    `item_embedding_name`: [string] 必配项，视频的 embedding 信息存储字段，值为 target_item_embedding

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_photo_related_hetu_embedding(
      item_embedding_name = "target_item_embedding",
      input_common_attrs=[
        "like_photo_hetu_embedding_list",
        "substring_user_like_photo_list",
      ],
      input_item_attrs=[
        "target_item_embedding",
      ],      
      output_item_attrs=[
        "hetu_like_photo_embedding_score",
        "max_similarity_like_photo_pid",
        "max_similarity_like_photo_embedding",
      ],
      output_common_attrs=[
        "filtered_like_photo_list",
      ], 
    )
    ```
    """
    self._add_processor(PhotoRelatedHetuEmbeddingAttrEnricher(kwargs))
    return self

  def enrich_by_red_point_ensemble_sort(self, **kwargs):
    """
    RedPointEnsembleSortEnricher
    ------
    红点 ensemble sort 多队列打分模块。

    参数配置
    ------
    `timeout_ms`: [int] 选配项，调 redis 超时时间，默认值 100ms

    `redis_cluster`: [string] 选配项，该模块取特征的 redis 集群，默认值 dsNewsPushCache

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_red_point_ensemble_sort(
      timeout_ms=60,
      input_item_attrs=[
        "r_user_author_intimacy_score",
        "r_sum_friend_relation_score",
        "p_mmu_nonsense_score",
        "r_user_author_intimacy_score",
        "hetu_like_photo_embedding_score",
        "p_user_match_tag_score",
        "p_final_reason",
        "p_like_user_id_list",
        "p_follow_user_id_list",
        "p_collect_user_id_list",
        ],
      output_item_attrs=[
        "re_author_intimacy_score", 
        "re_friend_intimacy_score",
        "re_nonsense_score",
        "re_like_photo_list_similarity_score",
        "re_friend_cnt_score",
        "re_user_photo_tag_match_score",
        "re_ensemble_score",
        "re_friend_intimacy_index",
        "re_author_intimacy_index",
        "re_nonsense_index",
        "re_like_photo_list_similarity_index",
        "re_user_tag_match_score_index",
        "re_friend_cnt_index",
        "re_ensemble_boost_score",
      ],
      output_common_attrs=[
        "re_ensemble_weight",
        "re_boost_weight",
      ],      
    )
    ```
    """
    self._add_processor(RedPointEnsembleSortEnricher(kwargs))
    return self

  def enrich_by_red_point_user_counter(self, **kwargs):
    """
    RedPointUserCounterAttrEnricher
    ------
    红点用户 counter 特征抽取。

    参数配置
    ------
    `timeout_ms`: [int] 选配项，调 redis 超时时间，默认值 50ms

    `redis_cluster_name`: [string] 选配项，该模块取特征的 redis 集群，默认值 notifyRecoNewsCache

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_red_point_user_counter(
      input_item_attrs=[
        "p_final_reason",
        "p_like_user_id_list",
        "p_follow_user_id_list",
        "p_collect_user_id_list",
        ],
      output_item_attrs=[
        "is_red_point_show_count_gt_threshold",
        "re_p_friend_show_count",
        "re_p_slide_friend_show_count_3days"
      ],     
    )
    ```
    """
    self._add_processor(RedPointUserCounterAttrEnricher(kwargs))
    return self

  def enrich_by_news_producer_relation_score(self, **kwargs):
    """
    RelationScoreItemAttrEnricher
    ------
    用户与作品的动态生产者之间的亲密分计算。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_news_producer_relation_score(
      input_common_attrs=[
        "ds_news_common_leaf_user_profile",
        ], 
      input_item_attrs=[
        "f_uid_list",
        ],
      output_item_attrs=[
        "r_sum_friend_relation_score",
        "r_max_friend_relation_score",
        "r_avg_friend_relation_score",
        "r_sum_friend_relation_rank",
        "r_min_friend_relation_rank",
        "r_avg_friend_relation_rank",
      ],     
    )
    ```
    """
    self._add_processor(RelationScoreItemAttrEnricher(kwargs))
    return self

  def enrich_by_news_author_relation_calculate(self, **kwargs):
    """
    UserAuthorRelationAttrEnricher
    ------
    用户与视频的作者之间的关系特征计算。

    参数配置
    ------
    `common_leaf_user_profile_attr_name`: [string] 必配项，目前值需设置为 ds_news_common_leaf_user_profile

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_news_author_relation_calculate(
      common_leaf_user_profile_attr_name="ds_news_common_leaf_user_profile",
      input_common_attrs=[
        "ds_news_common_leaf_user_profile",
        "u_friend_list",
        "u_follow_list",
        "u_like_author_list_photo",
        ], 
      input_item_attrs=[
        "author_id",
        "f_uid_list",
        ],
      output_item_attrs=[
        "r_user_author_intimacy_score",
        "r_user_author_intimacy_rank",
        "r_author_friend_group_cnt",
        "r_author_friend_group_score",
        "r_author_friend_group_density",
        "r_user_author_is_follow",
        "r_user_author_is_bifollow",
        "r_user_author_in_like_list",
      ],     
    )
    ```
    """
    self._add_processor(UserAuthorRelationAttrEnricher(kwargs))
    return self

  def enrich_by_news_producer_play_counter(self, **kwargs):
    """
    UserFriendCounterAttrEnricher
    ------
    动态场景下用户对视频动态生产者维度的播放 counter 信息统计。

    参数配置
    ------
    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_news_producer_play_counter(
      input_item_attrs=[
        "f_uid_list",
        ],
      output_item_attrs=[
        "f_friend_cnt",
        "f_friend_total_play_cnt",
        "f_friend_mini_play_cnt",
        "f_friend_short_play_cnt",
        "f_friend_valid_play_cnt",
        "f_friend_long_play_cnt",
      ],
      output_common_attrs=[
        "u_overall_total_play_cnt",
        ],  
    )
    ```
    """
    self._add_processor(UserFriendCounterAttrEnricher(kwargs))
    return self

  def enrich_by_news_producer_relation_calculate(self, **kwargs):
    """
    UserFriendRelationAttrEnricher
    ------
    用户与视频的动态生产者之间的关系特征计算。

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_news_producer_relation_calculate(
      input_common_attrs=[
        "ds_news_user_profile",
        ],
      input_item_attrs=[
        "p_play_user_id_list",
        "p_like_user_id_list",
        "p_follow_user_id_list",
        "p_comment_user_id_list",
        "p_collect_user_id_list",
        "p_download_user_id_list",
        "p_reward_user_id_list",
        "p_share_user_id_list",
        ],
      output_item_attrs=[
        "f_uid_list",
        "f_uid_list_size",
        "r_family_cnt",
        "r_contact_cnt",
        "r_contact_reverse_cnt",
        "r_contact_bidirection_cnt",
        "r_wechat_cnt",
        "r_wechat_friend_cnt",
        "r_qq_cnt",
        "r_bifollow_cnt",
        "r_you_care_cnt",
        "r_care_you_cnt",
        "r_common_bifollow_cnt",
        "r_common_follow_cnt",
        "r_common_contact_cnt",
        "r_common_wechat_cnt",
        "r_common_wechat_friend_cnt",
        "r_common_qq_cnt",
        "f_same_gender_cnt",
        "f_same_age_segment_cnt",
      ],
    )
    ```
    """
    self._add_processor(UserFriendRelationAttrEnricher(kwargs))
    return self

  def enrich_by_user_to_news_producer_and_hetu_tag_interest(self, **kwargs):
    """
    UserPhotoInterestScoreAttrEnricher
    ------
    通过用户在动态场景下对动态生产者兴趣和消费内容的河图兴趣标签计算对视频的兴趣分。

    参数配置
    ------
    `timeout_ms`: [int] 选配项，调 redis 超时时间，默认值 50ms

    `redis_cluster_name`: [string] 选配项，数据所存储的 redis 集群，默认值 dsNewsPushCache

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_user_to_news_producer_and_hetu_tag_interest(
      input_item_attrs=[
        "f_uid_list",
        "p_hetu_tag",
        ],
      output_item_attrs=[
        "p_user_friend_interest_score",
        "p_user_tag_interest_score",
        "p_user_interest_score",
      ],
    )
    ```
    """
    self._add_processor(UserPhotoInterestScoreAttrEnricher(kwargs))
    return self

  def enrich_by_user_global_like_photo_tag(self, **kwargs):
    """
    UserLikePhotoTagMatchAttrEnricher
    ------
    通过用户全局点赞消费，统计用户点赞标签特征

    参数配置
    ------
    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_user_global_like_photo_tag(
      input_commen_attrs=[
        "ds_news_user_profile",
      ],
      input_item_attrs=[
        "p_mmu_hetu_tag_v2_L1",
        "p_mmu_hetu_tag_v2_L2",
        "p_mmu_hetu_tag_v2_L3",
        "p_mmu_hetu_tag_v2_content",
        "p_mmu_hetu_tag_v2_ip",
      ],
      output_item_attrs=[
        "p_is_hit_user_like_first_tag",
        "p_hit_user_like_first_tag_list",
        "p_hit_user_like_first_tag_max_counter",
        "p_hit_user_like_first_tag_sum_counter",
        "p_is_hit_user_like_second_tag",
        "p_hit_user_like_second_tag_list",
        "p_hit_user_like_second_tag_max_counter",
        "p_hit_user_like_second_tag_sum_counter",
        "p_is_hit_user_like_third_tag",
        "p_hit_user_like_third_tag_list",
        "p_hit_user_like_third_tag_max_counter",
        "p_hit_user_like_third_tag_sum_counter",
        "p_is_hit_user_like_content_tag",
        "p_hit_user_like_content_tag_list",
        "p_hit_user_like_content_tag_max_counter",
        "p_hit_user_like_content_tag_sum_counter",
        "p_is_hit_user_like_ip_tag",
        "p_hit_user_like_ip_tag_list",
        "p_hit_user_like_ip_tag_max_counter",
        "p_hit_user_like_ip_tag_sum_counter",
        "p_hit_user_first_tag_like_rate",
        "p_hit_user_second_tag_like_rate",
        "p_hit_user_third_tag_like_rate",
        "p_hit_user_content_tag_like_rate",
        "p_hit_user_ip_tag_like_rate"
      ],
      output_common_attrs=[
        "u_like_photo_count",
        "u_like_friend_photo_count",
        "u_like_profession_photo_count",
        "u_like_first_tag_list",
        "u_like_second_tag_list",
        "u_like_third_tag_list",
        "u_like_content_tag_list",
        "u_like_ip_tag_list"
      ]
    )
    ```
    """
    self._add_processor(UserLikePhotoTagMatchAttrEnricher(kwargs))
    return self

  def enrich_by_user_global_hetu_tag_interest(self, **kwargs):
    """
    UserPhotoTagMatchScoreAttrEnricher
    ------
    通过用户在全局的内容消费计算用户对视频的河图兴趣特征。

    参数配置
    ------
    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_user_global_hetu_tag_interest(
      input_item_attrs=[
        "p_mmu_hetu_tag_v2_L1",
        "p_mmu_hetu_tag_v2_L2",
        "p_mmu_hetu_tag_v2_L3",
        "p_mmu_hetu_tag_v2_content",
        "p_mmu_hetu_tag_v2_ip", 
        ],
      output_item_attrs=[
        "p_user_first_level_tag_match_score",
        "p_user_second_level_tag_match_score",
        "p_user_third_level_tag_match_score",
        "p_user_notlike_first_level_tag_match_score",
        "p_user_notlike_second_level_tag_match_score",
        "p_user_notlike_third_level_tag_match_score",
        "p_user_match_tag_score",
        "p_user_match_notlike_tag_score",
        "p_user_first_level_match_tag",
        "p_user_second_level_match_tag",
        "p_user_third_level_match_tag",
        "p_user_notlike_first_level_match_tag",
        "p_user_notlike_second_level_match_tag",
        "p_user_notlike_third_level_match_tag",
        "p_user_content_tag_match_score",
        "p_user_content_match_tag",
        "p_user_notlike_content_tag_match_score",
        "p_user_notlike_content_match_tag",
        "p_user_ip_tag_match_score",
        "p_user_ip_match_tag",
        "p_user_notlike_ip_tag_match_score",
        "p_user_notlike_ip_match_tag",
      ],
      output_common_attrs=[
        "u_first_tag_info_list",
        "u_second_tag_info_list",
        "u_third_tag_info_list",
        "u_not_like_first_tag_info_list",
        "u_not_like_second_tag_info_list",
        "u_not_like_third_tag_info_list",
        "u_content_tag_info_list",
        "u_not_like_content_tag_info_list",
        "u_ip_tag_info_list",
        "u_not_like_ip_tag_info_list",
      ],
    )
    ```
    """
    self._add_processor(UserPhotoTagMatchScoreAttrEnricher(kwargs))
    return self

  def enrich_by_user_news_consumer_photo_hetu_tag_interest(self, **kwargs):
    """
    UserPhotoTagCounterAttrEnricher
    ------
    通过用户在动态场景中最近消费的 100 个视频的河图兴趣标签计算用户对视频的河图标签兴趣分。

    参数配置
    ------
    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_user_news_consumer_photo_hetu_tag_interest(
      input_item_attrs=[
        "p_hetu_tag",
        "p_mmu_hetu_tag_v2_L1",
        ],
      output_item_attrs=[
        "p_tag_counter_score",
        "p_tag_show_count",
        "p_tag_like_count",
        "p_tag_like_rate",
        "p_tag_comment_count",
        "p_tag_comment_rate",
        "p_tag_negative_count",
        "p_tag_negative_rate",
        "p_tag_short_play_count",
        "p_tag_short_play_rate",
        "p_tag_valid_play_count",
        "p_tag_valid_play_rate",
        "p_tag_long_play_count",
        "p_tag_long_play_rate",
        "p_tag_24h_show_count",
        "p_tag_24h_like_count",
        "p_tag_24h_comment_count",
        "p_tag_24h_negative_count",
        "p_tag_24h_short_play_count",
        "p_tag_24h_valid_play_count",
        "p_tag_24h_long_play_count",
      ],
    )
    ```
    """
    self._add_processor(UserPhotoTagCounterAttrEnricher(kwargs))
    return self

  def enrich_by_user_profile_common_attr(self, **kwargs):
    """
    UserProfileCommonAttrExtractEnricher
    ------
    从上游传入的用户画像信息中抽取 common_attr 特征。

    参数配置
    ------
    `raw_user_profile_attr_name`: [string] 必配项，上游用户画像系统以 common_attr pb 形式传入的信息特征字段，目前值需为 ds_news_user_profile

    `common_leaf_user_profile_attr_name`: [string] 必配项，以 pb 形式输出作为 common_attr 特征字段在 leaf 内共享，比如 map 形式的特征数据，目前值需为 ds_news_common_leaf_user_profile

    `user_like_photo_common_attr_name`: [string] 必配项，用户在全局的历史点赞视频列表以 common_attr list 形式存储的字段名，目前值需为 user_like_photo_list

    `user_follow_photo_common_attr_name`: [string] 必配项，用户在全局的历史关注视频列表以 common_attr list 形式存储的字段名，目前值需为 user_follow_photo_list

    `user_comment_photo_common_attr_name`: [string] 必配项，用户在全局的历史评论视频列表以 common_attr list 形式存储的字段名，目前值需为 user_comment_photo_list

    `user_collect_photo_common_attr_name`: [string] 必配项，用户在全局的历史收藏视频列表以 common_attr list 形式存储的字段名，目前值需为 user_collect_photo_list

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 必配项，string list, 本 processor 输出的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_user_profile_common_attr(
      raw_user_profile_attr_name="ds_news_user_profile",
      common_leaf_user_profile_attr_name="ds_news_common_leaf_user_profile",
      user_like_photo_common_attr_name="user_like_photo_list",
      user_follow_photo_common_attr_name="user_follow_photo_list",
      user_comment_photo_common_attr_name="user_comment_photo_list",
      user_collect_photo_common_attr_name="user_collect_photo_list",
      input_common_attrs=[
        "ds_news_user_profile",
        ],
      output_common_attrs=[
        "u_partner_list_size",
        "u_partner_list",
        "u_negative_author_list",
        "u_unlike_live_author_list",
        "news_self_visible_user_list",
        "news_friends_visible_user_list",
        "news_in_banded_user_not_visible_user_list",
        "news_not_open_collect_visible_user_list",
        "news_is_privacy_user_list",
        "u_follow_list_size",
        "u_follow_list",
        "u_friend_list_size",
        "u_friend_list",
        "u_relation_score_list_size",
        "u_relation_score_list",
        "u_hetu_content_tag_info_list",
        "u_favorite_live_type",
        "u_consume_game_live_level",
        "u_consume_shop_live_level",
        "u_like_author_list_photo",
        "u_fans_cnt",
        "u_acquaintaince_prefer_level",
        "u_acquaintaince_click_level",
        "u_acquaintaince_like_level",
        "u_acquaintaince_comment_level",
        "u_work_wifi_ssid",
        "u_home_wifi_ssid",
        "u_colleague_list",
        "u_family_list",
        "u_gender",
        "u_age",
        "u_age_segment",
        "u_long_term_interest_list",
        "u_short_term_interest_list",
        "ds_news_common_leaf_user_profile",
        "user_like_photo_list",
        "user_follow_photo_list",
        "user_comment_photo_list",
        "user_collect_photo_list",
        "u_hetu_content_tag_info_list",
        "u_hetu_ip_tag_info_list",
        "u_first_tag_info_list",
        "u_second_tag_info_list",
        "u_third_tag_info_list"
      ],
    )
    ```
    """
    self._add_processor(UserProfileCommonAttrExtractEnricher(kwargs))
    return self


  def enrich_by_ensemble_sort_model_grpc(self, **kwargs):
    """
    PhotoEnsembleSortModelRankEnricher
    ------
    从远程 ensemble sort model grpc 服务获取排序分。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名，目前值需为 grpc_ensemble_last_v24

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 ensembles-ort-v1

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_ensemble_sort_model_grpc(
      timeout_ms=50,
      kess_service_name="grpc_ensemble_last_v24",
      input_item_attrs=[
        "p_duration_ms",
        "p_show_cnt",
        "p_click_cnt",
        "p_like_cnt",
        "p_follow_cnt",
        "p_forward_cnt",
        "p_comment_cnt",
        "f_same_age_segment_cnt",
        "r_bifollow_cnt",
        "r_common_qq_cnt",
        "p_photo_age",
        "p_hetu_tag",
        "p_user_friend_interest_score",
        "e_author_intimacy_score",
        "e_interest_score",
        "ns_ltr",
        "ns_evtr",
        "ns_svtr",
        "ns_lvtr",
        "ns_playratio",
        "r_user_author_intimacy_score",
        "p_mmu_nonsense_score",
        "f_friend_cnt",
        "f_friend_total_play_cnt",
        "f_friend_mini_play_cnt",
        "f_friend_short_play_cnt",
        "f_friend_mini_play_rate",
        "fans_cnt",
        "p_upload_type",
      ],
      output_item_attrs=[
        "p_ensemble_model_score",
      ]
    )
    ```
    """
    self._add_processor(PhotoEnsembleSortModelRankEnricher(kwargs))
    return self

  def enrich_live_by_ensemble_sort_model_grpc(self, **kwargs):
    """
    LivestreamEnsembleSortModelRankEnricher
    ------
    从远程 ensemble sort model grpc 服务获取排序分。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名，目前值需为 grpc_news_live_ensemble_model_v19

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-livestream-ensemble-sort-rank

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_live_by_ensemble_sort_model_grpc(
      timeout_ms=50,
      kess_service_name="grpc_news_live_ensemble_model_v19",
      input_common_attrs=[
          "u_partner_list_size",
          "u_follow_list_size",
          "avg_friend_long_play_rate",
          "avg_friend_valid_play_rate",
          "avg_friend_extreme_short_play_rate",
      ],
      input_item_attrs=[
        "l_author_fans_num",
        "l_friend_cnt_score",
        "l_online_time",
        "l_follow_score",
        "fr_pctr",
        "fr_pltr",
        "fr_pwtr",
        "fr_plvtr",
        "fr_pwatchtime",
        "fr_pbenefit",
        "l_score",
        "l_online_count",
        "l_author_long_play_rate",
        "l_author_extreme_short_play_rate",
        "f_avg_complete_play_rate",
        "f_avg_long_play_rate",
        "f_avg_extreme_short_play_rate",
        "f_avg_valid_play_rate",
      ],
      output_item_attrs=[
        "l_ensemble_model_score",
      ]
    )
    ```
    """
    self._add_processor(LivestreamEnsembleSortModelRankEnricher(kwargs))
    return self

  def enrich_by_red_point_ctr_model_grpc(self, **kwargs):
    """
    RedPointCtrModelEnricher
    ------
    从远程 red point ctr model grpc 服务获取 pctr。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名，目前值需为 grpc_krp-production_news-entrance-ctr-xgboost

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-entrance-ctr-xgboost

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_red_point_ctr_model_grpc(
      timeout_ms=50,
      kess_service_name="grpc_krp-production_news-entrance-ctr-xgboost",
      input_common_attrs=[
          "u_partner_list_size",
          "u_item_num_after_deduplicate",
          "u_follow_list_size",
          "request_hour"
      ],
      input_item_attrs=[
          "p_like_friend_cnt",
          "r_sum_friend_relation_score",
          "r_avg_friend_relation_score",
          "r_max_friend_relation_score",
      ],
      output_item_attrs=[
          "re_pctr",
      ]
    )
    ```
    """
    self._add_processor(RedPointCtrModelEnricher(kwargs))
    return self

  def enrich_by_red_point_ctr_model_for_message_grpc(self, **kwargs):
    """
    RedPointCtrModelForMessageEnricher
    ------
    从远程 red point ctr model for message grpc 服务获取 pctr。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名，目前值需为 grpc_krp-production_news-entrance-ctr-xgboost

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-entrance-ctr-xgboost

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_red_point_ctr_model_for_message_grpc(
      timeout_ms=50,
      kess_service_name="grpc_news_entrance_ctr_model_for_message_v",
      input_common_attrs=[
          "u_partner_list_size",
          "u_item_num_after_deduplicate",
          "u_follow_list_size",
          "request_hour"
      ],
      input_item_attrs=[
          "p_like_friend_cnt",
          "r_sum_friend_relation_score",
          "r_avg_friend_relation_score",
          "r_max_friend_relation_score",
      ],
      output_item_attrs=[
          "re_pctr",
      ]
    )
    ```
    """
    self._add_processor(RedPointCtrModelForMessageEnricher(kwargs))
    return self

  def enrich_by_red_point_slide_model_grpc(self, **kwargs):
    """
    RedPointSlideModelEnricher
    ------
    从远程 red point slide model grpc 服务获取 pctr。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-entrance-ctr-xgboost

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_red_point_slide_model_grpc(
      timeout_ms=50,
      kess_service_name="grpc_krp-production_news-entrance-ctr-xgboost",
      input_common_attrs=[
          "u_partner_list_size",
          "u_item_num_after_deduplicate",
          "u_follow_list_size",
          "request_hour"
      ],
      input_item_attrs=[
          "p_like_friend_cnt",
          "r_sum_friend_relation_score",
          "r_avg_friend_relation_score",
          "r_max_friend_relation_score",
      ],
      output_item_attrs=[
          "re_pstr",
      ]
    )
    ```
    """
    self._add_processor(RedPointSlideModelEnricher(kwargs))
    return self

  def enrich_by_red_point_ctr_model_v2_for_message_grpc(self, **kwargs):
    """
    RedPointCtrModelForMessageV2Enricher
    ------
    从远程 red point ctr model for message grpc 服务获取 pctr。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名，目前值需为 grpc_krp-production_news-entrance-ctr-xgboost

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-entrance-ctr-xgboost

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_red_point_ctr_model_v2_for_message_grpc(
      timeout_ms=50,
      kess_service_name="grpc_news_entrance_ctr_model_for_message_v",
      input_common_attrs=[
          "u_partner_list_size",
          "u_item_num_after_deduplicate",
          "u_follow_list_size",
          "request_hour"
      ],
      input_item_attrs=[
          "p_like_friend_cnt",
          "r_sum_friend_relation_score",
          "r_avg_friend_relation_score",
          "r_max_friend_relation_score",
      ],
      output_item_attrs=[
          "re_pctr",
      ]
    )
    ```
    """
    self._add_processor(RedPointCtrModelForMessageV2Enricher(kwargs))
    return self

  def enrich_by_red_point_slide_model_v2_grpc(self, **kwargs):
    """
    RedPointSlideModelV2Enricher
    ------
    从远程 red point slide model grpc 服务获取 pctr。

    参数配置
    ------
    `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

    `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 选配项，string list, 本 processor 需要输入的 common_attrs 列表

    `output_common_attrs`: [list] 选配项，string list, 本 processor 输出的 item_attrs 列表

    `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 news-entrance-ctr-xgboost

    `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

    调用示例
    ------
    ``` python
    .enrich_by_red_point_slide_model_v2_grpc(
      timeout_ms=50,
      kess_service_name="grpc_krp-production_news-entrance-ctr-xgboost",
      input_common_attrs=[
          "u_partner_list_size",
          "u_item_num_after_deduplicate",
          "u_follow_list_size",
          "request_hour"
      ],
      input_item_attrs=[
          "p_like_friend_cnt",
          "r_sum_friend_relation_score",
          "r_avg_friend_relation_score",
          "r_max_friend_relation_score",
      ],
      output_item_attrs=[
          "re_pstr",
      ]
    )
    ```
    """
    self._add_processor(RedPointSlideModelV2Enricher(kwargs))
    return self

  def enrich_by_news_user_profile_item_attr(self, **kwargs):
    """
    NewsUserProfileItemAttrEnricher
    ------
    根据离线挖掘的用户画像抽取item特征。

    参数配置

    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    `output_item_attrs`: [list] 必配项，string list, 本 processor 输出的 item_attrs 列表表

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_by_news_user_profile_item_attr(
      input_common_attrs=[
          "user_profile_v1",
          "ds_news_common_leaf_user_profile",
      ],
      input_item_attrs=[
          "author_id",
          "p_play_user_id_list",
          "p_like_user_id_list",
          "p_follow_user_id_list",
          "p_comment_user_id_list",
          "p_collect_user_id_list",
          "p_download_user_id_list",
          "p_reward_user_id_list",
          "p_share_user_id_list",
          "l_watch_user_id_list",
          "l_gift_user_id_list",
          "l_follow_user_id_list"
      ],
      output_item_attrs=[
          "f_uid_list",
          "f_sum_complete_play_rate",
          "f_max_complete_play_rate",
          "f_avg_complete_play_rate",
          "f_sum_long_play_rate",
          "f_max_long_play_rate",
          "f_avg_long_play_rate",
          "f_sum_valid_play_rate",
          "f_max_valid_play_rate",
          "f_avg_valid_play_rate",
          "f_sum_extreme_short_play_rate",
          "f_max_extreme_short_play_rate",
          "f_avg_extreme_short_play_rate",
          "f_sum_like_rate",
          "f_max_like_rate",
          "f_avg_like_rate",
          "l_author_long_play_rate",
          "l_author_extreme_short_play_rate",
          "p_red_point_history_click_rate"
      ]
    )
    ```
    """
    self._add_processor(NewsUserProfileItemAttrEnricher(kwargs))
    return self

  def enrich_update_filter_photo_to_browset(self, **kwargs):
    """
    UpdateFilterPhotoToBrowsetEnricher
    ------
    将过滤视频写入browset。

    参数配置

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_update_filter_photo_to_browset(
      input_common_attrs=[
          "negative_author_filter_item_list"
      ]
    )
    ```
    """
    self._add_processor(UpdateFilterPhotoToBrowsetEnricher(kwargs))
    return self

  def enrich_news_photo_final_generate(self, **kwargs):
    """
    NewsPhotoFinalGenerate
    ------
    生成最终结果

    参数配置

    `input_common_attrs`: [list] 必配项，string list, 本 processor 需要输入的 common_attrs 列表
    `input_item_attrs`: [list] 必配项，string list, 本 processor 需要输入的 item_attrs 列表

    调用示例
    ------
    ``` python
    .enrich_news_photo_final_generate(
      input_common_attrs=[
          "social_photo_insert_cnt"
      ],
      input_item_attrs=[
        "p_author_is_social_friend"
      ]
    )
    ```
    """
    self._add_processor(NewsPhotoFinalGenerateEnricher(kwargs))
    return self