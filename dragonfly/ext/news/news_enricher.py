#!/usr/bin/env python3
# coding=utf-8
"""
filename: news_enricher.py
description: news_common_leaf dynamic_json_config DSL intelligent builder, enricher module
author: <PERSON><PERSON>od<PERSON><PERSON>@kuaishou.com
date: 2021-09-15 15:15:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafEnricher

class ShareChatUserProfileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_user_profile_attr_by_kcc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")


class ShareChatUserProfileV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_user_profile_attr_v2_by_kcc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")


class ShareChatUserActionHistoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_user_action_history_attr_by_kcc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class ShareChatSocialEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_social_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class ShareChatAuthorFansBifollowListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "author_fans_bifollow_list_enricher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")


class ShareChatFriendConsumeListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_friend_consume_list_attr_by_kgraph"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("need_max_outedge_size"), int) and self._config["need_max_outedge_size"] < 0,
              "need_max_outedge_size 需为小于 0 的整数")
    check_arg(isinstance(self._config.get("need_field_num"), int) and self._config["is_open_parse_dnmfrlp_proto"] > 49,
              "need_field_num 需为大于 49 的整数")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")

class ShareChatItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_share_chat_item_attr_by_common_attr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("is_open_calculate_wechat_list"), int) and self._config["is_open_calculate_wechat_list"] >= 0,
              "is_open_calculate_wechat_list 需为大于等于 0 的整数")
    check_arg(isinstance(self._config.get("is_open_parse_dnmfrlp_proto"), int) and self._config["is_open_parse_dnmfrlp_proto"] >= 0,
              "is_open_parse_dnmfrlp_proto 需为大于等于 0 的整数")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class NewsBrowseSetFilterByCidListArranger(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "filter_cid_by_browse_set"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attr = self._config.get("save_filtered_items_to_common_attr", "")
    return { attr } if attr else set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("check_id_in_attr", "")
    return { attr } if attr else set()

class LiveRankPredictAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_live_predict_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("item_type"), int) and self._config["item_type"] >= 0,
              "item_type 需为大于等于 0 的整数")
    check_arg(isinstance(self._config.get("cluster_name"), str) and self._config["cluster_name"],
              "cluster_name 需为非空字符串")
    check_arg(isinstance(self._config.get("server_name"), str) and self._config["server_name"],
              "server_name 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("request_type"), str) and self._config["request_type"],
              "request_type 需为非空字符串")
    check_arg(isinstance(self._config.get("shard_size"), int) and self._config["shard_size"] >= 0,
              "shard_size 需为大于等于 0 的整数")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class LivestreamEnsembleSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_live_ensemble_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class LivestreamEnsembleSortModelRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_live_by_ensemble_sort_model_grpc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class NewsLivestreamUserAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_live_samplelist_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class PhotoEnsembleSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_photo_ensemble_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class PhotoInfoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_dynamic_photo_store"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class ShareChatPhotoInfoAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_dynamic_photo_store_for_share_chat"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class PhotoRelatedHetuEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_photo_related_hetu_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("item_embedding_name"), str) and self._config["item_embedding_name"],
              "item_embedding_name 需为非空字符串")

class RedPointEnsembleSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_ensemble_sort"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    if self._config.get("timeout_ms"):
      check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    if self._config.get("redis_cluster"):
      check_arg(isinstance(self._config.get("redis_cluster"), str) and self._config["redis_cluster"],
                "redis_cluster 需为非空字符串")

class RedPointUserCounterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_user_counter"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    if self._config.get("timeout_ms"):
      check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    if self._config.get("redis_cluster"):
      check_arg(isinstance(self._config.get("redis_cluster_name"), str) and self._config["redis_cluster_name"],
                "redis_cluster_name 需为非空字符串")

class RelationScoreItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_news_producer_relation_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserAuthorRelationAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_news_author_relation_calculate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("common_leaf_user_profile_attr_name"), str) and self._config["common_leaf_user_profile_attr_name"],
                "common_leaf_user_profile_attr_name 需为非空字符串")

class UserFriendCounterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_news_producer_play_counter"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserFriendRelationAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_news_producer_relation_calculate"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserPhotoInterestScoreAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_user_to_news_producer_and_hetu_tag_interest"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    if self._config.get("timeout_ms"):
      check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    if self._config.get("redis_cluster"):
      check_arg(isinstance(self._config.get("redis_cluster_name"), str) and self._config["redis_cluster_name"],
                "redis_cluster_name 需为非空字符串")

class UserLikePhotoTagMatchAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_user_global_like_photo_tag"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")


class UserPhotoTagMatchScoreAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_user_global_hetu_tag_interest"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")

class UserPhotoTagCounterAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_user_news_consumer_photo_hetu_tag_interest"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")

class UserProfileCommonAttrExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_user_profile_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("raw_user_profile_attr_name"), str) and self._config["raw_user_profile_attr_name"],
                "raw_user_profile_attr_name 需为非空字符串")
    check_arg(isinstance(self._config.get("common_leaf_user_profile_attr_name"), str) and self._config["common_leaf_user_profile_attr_name"],
                "common_leaf_user_profile_attr_name 需为非空字符串")
    check_arg(isinstance(self._config.get("user_like_photo_common_attr_name"), str) and self._config["user_like_photo_common_attr_name"],
                "user_like_photo_common_attr_name 需为非空字符串")
    check_arg(isinstance(self._config.get("user_follow_photo_common_attr_name"), str) and self._config["user_follow_photo_common_attr_name"],
                "user_follow_photo_common_attr_name 需为非空字符串")
    check_arg(isinstance(self._config.get("user_comment_photo_common_attr_name"), str) and self._config["user_comment_photo_common_attr_name"],
                "user_comment_photo_common_attr_name 需为非空字符串")
    check_arg(isinstance(self._config.get("user_collect_photo_common_attr_name"), str) and self._config["user_collect_photo_common_attr_name"],
                "user_collect_photo_common_attr_name 需为非空字符串")

class PhotoEnsembleSortModelRankEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_ensemble_sort_model_grpc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"): 
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class RedPointCtrModelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_ctr_model_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"): 
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class RedPointCtrModelForMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_ctr_model_for_message_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class RedPointSlideModelEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_slide_model_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class RedPointCtrModelForMessageV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_ctr_model_v2_for_message_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class RedPointSlideModelV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_red_point_slide_model_v2_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service_name"), str) and self._config["kess_service_name"],
              "kess_service_name 需为非空字符串")
    if self._config.get("model_name"):
      check_arg(isinstance(self._config.get("model_name"), str) and self._config["model_name"],
                "model_name 需为非空字符串")
    if self._config.get("kess_caller_name"):
      check_arg(isinstance(self._config.get("kess_caller_name"), str) and self._config["kess_caller_name"],
                "kess_caller_name 需为非空字符串")
    if self._config.get("input_common_attrs"):
      check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class NewsUserProfileItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_by_news_user_profile_item_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class UpdateFilterPhotoToBrowsetEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_update_filter_photo_to_browset"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")


class NewsPhotoFinalGenerateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "enrich_news_photo_final_generate"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("input_item_attrs"), list), "input_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")