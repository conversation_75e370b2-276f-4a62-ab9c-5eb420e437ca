#!/usr/bin/env python3
# coding=utf-8
"""
filename: news_retriever.py
description: news_common_leaf dynamic_json_config DSL intelligent builder, retriever module
author: <PERSON><PERSON>od<PERSON><PERSON>@kuaishou.com
date: 2021-09-09 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_common_attrs
from ...common_leaf_processor import LeafRetriever

class NewsFriendsFilmingRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "news_friends_filming_retrieve_by_grpc"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class ShareChatRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "share_chat_retrieve_by_need_field_num"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("need_field_num"), int) and self._config["need_field_num"] > 0,
              "need_field_num 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("add_reason_to_attr"), str) and self._config["add_reason_to_attr"],
              "add_reason_to_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class NewsPhotoLocalCalculateRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "news_photo_local_calculate_retriever_by_need_field_num"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("need_field_num"), int) and self._config["need_field_num"] > 49 and self._config["need_field_num"] < 60,
              "need_field_num 需为 [ 50 , 59 ] 的整数")
    check_arg(isinstance(self._config.get("need_content_relation_type"), int) and self._config["need_content_relation_type"] >= 0,
              "need_content_relation_type 需为大于等于 0 的整数")
    check_arg(isinstance(self._config.get("news_pool_max_size"), int) and self._config["news_pool_max_size"] > 0,
              "need_content_relation_type 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class NewsPhotoRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "news_photo_retrieve_by_need_field_num"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("need_field_num"), int) and self._config["need_field_num"] > 0,
              "need_field_num 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("add_reason_to_attr"), str) and self._config["add_reason_to_attr"],
              "add_reason_to_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("input_common_attrs"), list), "input_common_attrs 需为 list 类型")

class NewsLivestreamRemoteRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "news_livestream_retrieve_by_need_field_num"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_common_attrs", []))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("output_item_attrs", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0,
              "reason 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("need_field_num"), int) and self._config["need_field_num"] > 0,
              "need_field_num 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("kess_service"), str) and self._config["kess_service"],
              "kess_service 需为非空字符串")
    check_arg(isinstance(self._config.get("timeout_ms"), int) and self._config["timeout_ms"] > 0,
              "timeout_ms 需为大于 0 的整数")
    check_arg(isinstance(self._config.get("add_reason_to_attr"), str) and self._config["add_reason_to_attr"],
              "add_reason_to_attr 需为非空字符串")
    check_arg(isinstance(self._config.get("output_common_attrs"), list), "output_common_attrs 需为 list 类型")
    check_arg(isinstance(self._config.get("output_item_attrs"), list), "output_item_attrs 需为 list 类型")
