#!/usr/bin/env python3
# coding=utf-8
"""
filename: arrow_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, arrow api mixin
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .arrow_enricher import *
from .arrow_retriever import *
from .arrow_observer import *

class ArrowApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 apache-arrow 相关的 Processor 接口。
  用于 apache-arrow 列存数据结构 (arrow parquet 等) 本身的变化操作，或者与 dragon 内部数据结构之间的转换

  维护人/团队: zhangfangyu/kaiworks
  """
  def build_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchEnricher
    ------
    将 context 的一批 commont attr 和 item attr 转换成 arrow 的 RecordBatch，写入新的 common attr 中

    参数配置
    ------
    `common_attrs_from`: [list][动态参数] 创建 RecordBatch 的 common attr 列表

    `static_common_attrs_from`: [list] 创建 RecordBatch 的 common attr 列表

    `common_slots_from`: [list][动态参数] 从哪些 common_attr 中读出 common_slots 列表

    `common_signs_from`: [list][动态参数] 从哪些 common_attr 中读出 common_signs 列表, 与 common_slots_from 对应

    `merge_record_batch_from`: [list] 选配项，从指定的 common_attr 列表中获取 RecordBatch 进行合并

    `merge_record_batch_field_names`: [list] 选配项，与 `merge_record_batch_from` 对应，表示每个 RecordBatch 需要抽取的列名列表, 默认抽取所有列

    `item_attrs_from`: [list][动态参数] 创建 RecordBatch 的 item attr 列表

    `static_item_attrs_from`: [list] 创建 RecordBatch 的 item attr 列表

    `item_slots_from`: [list][动态参数] 从哪些 item_attr 中读出 item_slots 列表

    `item_signs_from`: [list][动态参数] 从哪些 item_attr 中读出 item_signs 列表, 与 item_slots_from 对应

    `common_attr_to`: [string] 创建的 RecordBatch 保存到 common 的 `common_attr_to` 中

    `utf8_attr_names`: [list][动态参数] 哪些 string/list of string 类型的 common attr 和 item attr 创建为 RecordBatch 的 utf8/list of utf8 类型，默认为空

    `log_record_batch`: [bool] 仅用于调试，是否 pretty print 创建的 RecordBatch 到日志中，默认否

    `fields_to_check_prefix_consistency`: [dict] 对生产的 RecordBatch 的哪些列做前缀一致性检查(出现不同前缀会上报 error), 对性能有影响, 请谨慎添加。默认不做检查。设置示例 `{"static_fields": [{"field_name": "446", "prefix_len": 16}, {"field_name": "447", "prefix_len": 16}, {"field_name": "448", "prefix_len": 16}], "dynamic_fields": {"slots_from": [{"field_name": "query_common_slots", "prefix_len": 16}, {"field_name": "query_item_slots", "prefix_len": 16}, {"field_name": "gsu_slots", "prefix_len": 16}]}}`, 其中 "static_fields" 指定了需要做检查的列名和前缀位数, "dynamic_fields.slots_from" 指定了从哪些 common/item attr 解析 slots 列表, 动态追加到检查的列名集合里, 这些 attr 需要选自 `common_slots_from` 或 `item_slots_from`, "prefix_len" 如果没有设置, 则取默认值(16)

    调用示例
    ------
    ``` python
    .build_arrow_record_batch(
      merge_record_batch_from=["record_batch1", "record_batch2"],
      merge_record_batch_field_names=[["rb1_f1", "rb1_f3"], ["rb2_f2", "rb2_f5"]],
      common_attrs_from=["user_id", "time_ms", "click_pids"],
      item_attrs_from=["photo_id", "author_id", "click"],
      common_attr_to="record_batch",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchEnricher(kwargs))
    return self

  def retrieve_from_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchRetriever
    ------
    从 arrow RecordBatch 结构中反解出样本和特征数据，样本作为 item 加入结果集，特征作为 attr 写入 context

    参数配置
    ------
    `record_batch_from_attr`: [string] 从指定的 common_attr 中获取 RecordBatch

    `id_feature`: [string] 用 RecordBatch 哪个 feature 值作为样本 item 的 item_id (同 item_key)

    `user_id_feature`: [string] 用 RecordBatch 哪个 feature 值作为 user_id 覆盖当前的 user_id，默认不覆盖。

    `device_id_feature`: [string] 用 RecordBatch 哪个 feature 值作为 device_id 覆盖当前的 device_id，默认不覆盖。

    `request_time_feature`: [string] 用 RecordBatch 哪个 feature 值作为 request time，覆盖当前的 request time，默认不覆盖。

    `extract_common_features`: [list][动态参数] 从 RecordBatch 中抽取哪些特征写入 common_attr

    `extract_item_features`: [list][动态参数] 从 RecordBatch 中抽取哪些特征写入 item_attr

    `reason`: [int] 选配项，召回原因，默认为 0

    `prefix`: [string] 选配项，写入到 common 和 item attr 时，增加前缀，默认不加前缀

    调用示例
    ------
    ``` python
    .retrieve_from_arrow_record_batch(
      record_batch_from_attr="record_batch",
      id_feature="photo_id",
      user_id_feature="user_id",
      device_id_feature="device_id",
      request_time_feature="time_ms",
      extract_common_features=["click_list_30D"],
      extract_item_features=["click_user_list_30D"],
    )
    ```
    """
    self._add_processor(ArrowRecordBatchRetriever(kwargs))
    return self

  def export_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchExportEnricher
    ------
    将 context 的 RecordBatch 的 schema 和 array export 成 c struct, 供跨语言使用

    参数配置
    ------
    `record_batch_from`: [string] 必配项，从指定的 common_attr 中获取 RecordBatch 进行 export

    `export_schema_to`: [string] 必配项， 将 export 的 schema 保存到 common 的 `export_schema_to`

    `export_array_to`: [string] 必配项，将 export 的 array 保存到 common 的 `export_array_to`

    `check_schema`: [bool] 选配项，是否检查 schema 合法，默认 true ，例如: 检查 schema 中的重名列

    调用示例
    ------
    ``` python
    .export_arrow_record_batch(
      record_batch_from="record_batch",
      export_schema_to="schema",
      export_array_to="array",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchExportEnricher(kwargs))
    return self

  def import_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchImportEnricher
    ------
    从 context 的 c struct（ schema 和 array）创建 RecordBatch，并保存到 context 的 common attr

    参数配置
    ------
    `import_schema_from`: [string] 必配项，从 common 的 `import_schema_from` 读取 schema 

    `import_array_from`: [string] 必配项，从 common 的 `import_array_from` 读取 array 

    `save_record_batch_to`: [string] 必配项，将生成的 RecordBatch 保存到 common 侧的 `save_record_batch_to`

    调用示例
    ------
    ``` python
    .import_arrow_record_batch(
      import_schema_from="schema",
      import_array_from="array",
      save_record_batch_to="record_batch",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchImportEnricher(kwargs))
    return self

  def log_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchObserver
    ------
    将 context RecordBatch 打印到日志中

    参数配置
    ------
    `record_batch_from_attr`: [string] 从指定的 common_attr 中获取 RecordBatch

    调用示例
    ------
    ``` python
    .log_arrow_record_batch(
      record_batch_from_attr="record_batch",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchObserver(kwargs))
    return self

  def arrow_record_batch_type_cast(self, **kwargs):
    """
    ArrowRecordBatchTypeCastEnricher
    ------
    将 context 侧的 RecordBatch 的某些列转成 `to_type` 类型(如果原始类型是 list 类型，则转为 list(to_type))

    参数配置
    ------
    `record_batch_from_attr`: [string] 必配项，从指定的 common_attr 中获取 RecordBatch

    `column_names`: [list] 必配项，需要转换类型的列名列表

    `to_type`: [int] 选配项，转换成的类型，取值见 (arrow/type_fwd.h) arrow::Type::type 枚举类，默认值 11(FLOAT)。支持的类型列表:

| to_type  |  type_name |
|----------|------------|
| 9        | INT64      |
| 11       | FLOAT      |

    调用示例
    ------
    ``` python
    .arrow_record_batch_type_cast(
      record_batch_from_attr="record_batch",
      column_names=["col_a", "col_b", "col_c"]
    )
    ```
    """
    self._add_processor(ArrowRecordBatchTypeCastEnricher(kwargs))
    return self

  def trim_protobuf_message(self, **kwargs):
    """
    ArrowTrimProtobufMessageEnricher
    ------
    从 common attr 里读取一个 protobuf Message 指针，做字段裁剪，仅保留指定的字段，该操作是 inplace 的

    注意：required 字段一定会保留, 否则后续解析可能报错

    参数配置
    ------
    `from_common_pb`: [string] 必配项, 从哪个 common attr 获取 protobuf Message 的指针

    `paths`: [list] 必配项，保留的字段列表，格式见 google::protobuf::FieldMask

    调用示例
    ------
    ``` python
    .trim_protobuf_message(
      from_common_pb="ks_reco_log",
      paths = [
        "llsid",
        "user.id",
        "recoPhoto.photo"
      ]
    )
    ```
    """
    self._add_processor(ArrowTrimProtobufMessageEnricher(kwargs))
    return self

  def retrieve_from_batch_row(self, **kwargs):
    """
    BatchRowRetriever
    ------
    从 batch row (pb) 结构中反解出样本和特征数据，样本作为 item 加入结果集，特征作为 attr 写入 context

    参数配置
    ------
    `record_batch_from_attr`: [string] 从指定的 common_attr 中获取 BatchRow (bytes)，kaiworks 流引擎中为 serialized_brp

    `id_feature`: [string] 用 BatchRow 哪个 feature 值作为样本 item 的 item_id (同 item_key)

    `user_id_feature`: [string] 用 BatchRow 哪个 feature 值作为 user_id 覆盖当前的 user_id，默认不覆盖。

    `device_id_feature`: [string] 用 BatchRow 哪个 feature 值作为 device_id 覆盖当前的 device_id，默认不覆盖。

    `request_time_feature`: [string] 用 BatchRow 哪个 feature 值作为 request time，覆盖当前的 request time，默认不覆盖。

    `extract_common_features`: [list][动态参数] 从 BatchRow 中抽取哪些特征写入 common_attr

    `extract_item_features`: [list][动态参数] 从 BatchRow 中抽取哪些特征写入 item_attr

    `reason`: [int] 选配项，召回原因，默认为 0

    `prefix`: [string] 选配项，写入到 common 和 item attr 时，增加前缀，默认不加前缀

    `enable_common_attr_backup`: [bool] 选配项，特征从 common attr 中找不到时尝试从 item attr 中获取

    `enable_item_attr_backup`: [bool] 选配项，特征从 item attr 中找不到时尝试从 common attr 中获取

    `use_pb_schema`: [bool] 选配项，开启时从 pb 中获取 schema 信息，忽略 extract_common_features、extract_item_features 配置

    调用示例
    ------
    ``` python
    .retrieve_from_batch_row(
      batch_row_from_attr="batch_row",
      id_feature="photo_id",
      user_id_feature="user_id",
      device_id_feature="device_id",
      request_time_feature="time_ms",
      extract_common_features=["click_list_30D"],
      extract_item_features=["click_user_list_30D"],
    )
    ```
    """
    self._add_processor(BatchRowRetriever(kwargs))
    return self

  def retrieve_from_arrow_record_batch_simple(self, **kwargs):
    """
    BatchRowRetriever
    ------
    从 arrow 中将的列写到 common attr

    参数配置
    ------
    `record_batch_from_attr`: [string] 从指定的 common_attr 中获取 arrow (指针)

    `extract_common_features`: [list][动态参数] 从 arrow 中抽取哪些特征写入 common_attr

    `prefix`: [string] 选配项，写入到 common 和 item attr 时，增加前缀，默认不加前缀

    调用示例
    ------
    ``` python
    .retrieve_from_arrow_record_batch_simple(
      batch_row_from_attr="batch_row",
      extract_common_features=["click_list_30D"],
    )
    ```
    """
    self._add_processor(ArrowRecordBatchSimpleRetriever(kwargs))
    return self

  def serialize_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchSerializeEnricher
    ------
    将 common 侧的 RecordBatch 序列化并保存到 common 侧，结果可以通过 deserialize_arrow_record_batch 反序列化回来

    参数配置
    ------
    `record_batch_from_attr`: [string] 从指定的 common_attr 中获取 RecordBatch

    `common_attr_to`: [string] 序列化后的结果保存到 `common_attr_to`

    调用示例
    ------
    ``` python
    .serialize_arrow_record_batch(
      record_batch_from_attr="record_batch",
      common_attr_to="rb_serialized",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchSerializeEnricher(kwargs))
    return self

  def deserialize_arrow_record_batch(self, **kwargs):
    """
    ArrowRecordBatchDeserializeEnricher
    ------
    将 common 侧的 binary 反序列化成 RecordBatch 并保存到 common 侧，是 serialize_arrow_record_batch 的逆操作

    参数配置
    ------
    `record_batch_serialized_attr_from`: [string] 从指定的 common_attr 中获取 binary，并对其做反序列化操作

    `common_attr_to`: [string] 反序列化的 RecordBatch 保存到 `common_attr_to`

    调用示例
    ------
    ``` python
    .deserialize_arrow_record_batch(
      record_batch_serialized_attr_from="rb_serialized",
      common_attr_to="record_batch",
    )
    ```
    """
    self._add_processor(ArrowRecordBatchDeserializeEnricher(kwargs))
    return self

  def build_batch_row(self, **kwargs):
    """
    BatchRowEnricher
    ------
    将 context 的一批 commont attr 和 item attr 转换成 BatchRow 的 bytes，写入新的 common attr 中

    参数配置
    ------
    `common_attrs_from`: [list][动态参数] 创建 RecordBatch 的 common attr 列表

    `item_attrs_from`: [list][动态参数] 创建 RecordBatch 的 item attr 列表

    `utf8_attr_names`: [list][动态参数] 哪些 string/list of string 类型的 common attr 和 item attr 创建为 batch row 的 utf8/list of utf8 类型，默认为空

    `common_attrs_rename`: [list] 写入 batch_row 时重命名的 common attr 列表

    `item_attrs_rename`: [list] 写入 batch_row 时重命名的 item attr 列表

    `common_attr_to`: [string] 创建的 RecordBatch 保存到 common 的 `common_attr_to` 中

    调用示例
    ------
    ``` python
    .build_batch_row(
      common_attrs_from=["user_id", "time_ms", "click_pids"],
      item_attrs_from=["photo_id", "author_id", "click"],
      common_attrs_rename=[{"name": "user_id", "as": "uId"}],
      item_attrs_rename=[{"name": "photo_id", "as": "pId"}],
      common_attr_to="batch_row_bytes",
    )
    ```
    """
    self._add_processor(BatchRowEnricher(kwargs))
    return self
