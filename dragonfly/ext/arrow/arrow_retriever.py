#!/usr/bin/env python3
"""
filename: kuiba_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for offline
author: <EMAIL>
date: 2020-03-11 20:34:00
"""

from ...common_leaf_util import extract_common_attrs, strict_types
from ...common_leaf_processor import LeafRetriever


class ArrowRecordBatchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_arrow_record_batch"

  def is_dynamic_params(self, expr) -> bool:
    if not isinstance(expr, str):
      return False
    if not expr.startswith("{{") or not expr.endswith("}}"):
      return False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from_attr"]:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      ret.update(self.extract_dynamic_params(self._config.get("extract_common_features", [])))
    if self.is_dynamic_params(self._config.get("extract_item_features", [])):
      ret.update(self.extract_dynamic_params(self._config.get("extract_item_features", [])))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("prefix", "")
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      return ret
    for key in ["extract_common_features"]:
      ret.update([prefix + attr for attr in self._config.get(key, [])])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("prefix", "")
    if self.is_dynamic_params(self._config.get("extract_item_features", [])):
      return ret
    for key in ["extract_item_features"]:
      ret.update([prefix + attr for attr in self._config.get(key, [])])
    return ret

class ArrowRecordBatchSimpleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_arrow_record_batch_simple"

  def is_dynamic_params(self, expr) -> bool:
    if not isinstance(expr, str):
      return False
    if not expr.startswith("{{") or not expr.endswith("}}"):
      return False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from_attr"]:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      ret.update(self.extract_dynamic_params(self._config.get("extract_common_features", [])))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("prefix", "")
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      return ret
    for key in ["extract_common_features"]:
      ret.update([prefix + attr for attr in self._config.get(key, [])])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class BatchRowRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_from_batch_row"

  def is_dynamic_params(self, expr) -> bool:
    if not isinstance(expr, str):
      return False
    if not expr.startswith("{{") or not expr.endswith("}}"):
      return False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["batch_row_from_attr"]:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      ret.update(self.extract_dynamic_params(self._config.get("extract_common_features", [])))
    if self.is_dynamic_params(self._config.get("extract_item_features", [])):
      ret.update(self.extract_dynamic_params(self._config.get("extract_item_features", [])))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("prefix", "")
    if self.is_dynamic_params(self._config.get("extract_common_features", [])):
      return ret
    for key in ["extract_common_features"]:
      ret.update([prefix + attr for attr in self._config.get(key, [])])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    prefix = self._config.get("prefix", "")
    if self.is_dynamic_params(self._config.get("extract_item_features", [])):
      return ret
    for key in ["extract_item_features"]:
      ret.update([prefix + attr for attr in self._config.get(key, [])])
    return ret
