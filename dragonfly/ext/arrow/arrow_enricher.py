#!/usr/bin/env python3
"""
"""

from ...common_leaf_util import check_arg, strict_types
from ...common_leaf_processor import LeafEnricher


class ArrowRecordBatchEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_arrow_record_batch"

  def is_dynamic_params(self, expr) -> bool:
    if not isinstance(expr, str):
      return False
    if not expr.startswith("{{") or not expr.endswith("}}"):
      return False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attrs_from", "common_slots_from", "common_signs_from", "static_common_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        ret.update(self.extract_dynamic_params(self._config.get(attr_name, [])))
      else:
        ret.update(self._config.get(attr_name, []))

    for attr_name in ["merge_record_batch_from"]:
      ret.update(self._config.get(attr_name, []))
    for attr_name in ["item_attrs_from", "item_slots_from", "item_signs_from", "utf8_attr_names"]:
      if not self.is_dynamic_params(self._config.get(attr_name, [])):
        continue
      ret.update(self.extract_dynamic_params(self._config.get(attr_name, [])))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr_name in ["item_attrs_from", "item_slots_from", "item_signs_from", "static_item_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        continue
      ret.update(self._config.get(attr_name, []))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attr_to"]:
      attr = self._config.get(attr_name)
      if attr:
        ret.add(attr)
    return ret

  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    """ 是否依赖当前目标 item_table 中的所有 attr 数据 """
    for attr_name in ["item_attrs_from", "item_slots_from", "item_signs_from", "static_item_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        return True
    return False

  @strict_types
  def depend_on_all_common_attrs(self) -> bool:
    """ 是否依赖所有 common attr 数据 """
    for attr_name in ["common_attrs_from", "common_slots_from", "common_signs_from", "utf8_attr_names", "static_common_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        return True
    return False

class ArrowRecordBatchExportEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "export_arrow_record_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from"]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["export_schema_to", "export_array_to"]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret


class ArrowRecordBatchImportEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "import_arrow_record_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["import_schema_from", "import_array_from"]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["save_record_batch_to"]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret


class ArrowRecordBatchTypeCastEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "arrow_record_batch_type_cast"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from_attr"]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret


class ArrowTrimProtobufMessageEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trim_protobuf_message"

  @strict_types
  def _check_config(self) -> None:
    """ 检查配置是否合法 """
    for attr_name in ["paths", "from_common_pb"]:
      check_arg(self._config.get(attr_name), f"`{attr_name}` is required")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["from_common_pb", ]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret


class ArrowRecordBatchSerializeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "serialize_arrow_record_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from_attr", ]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attr_to", ]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret


class ArrowRecordBatchDeserializeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "deserialize_arrow_record_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_serialized_attr_from", ]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attr_to", ]:
      attr = self._config.get(attr_name)
      check_arg(attr, f"`{attr_name}` is required")
      ret.add(attr)
    return ret

class BatchRowEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_batch_row"

  def is_dynamic_params(self, expr) -> bool:
    if not isinstance(expr, str):
      return False
    if not expr.startswith("{{") or not expr.endswith("}}"):
      return False
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attrs_from", "utf8_attr_names"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        ret.update(self.extract_dynamic_params(self._config.get(attr_name, [])))
      else:
        ret.update(self._config.get(attr_name, []))

    for attr_name in ["item_attrs_from"]:
      if not self.is_dynamic_params(self._config.get(attr_name, [])):
        continue
      ret.update(self.extract_dynamic_params(self._config.get(attr_name, [])))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for attr_name in ["item_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        continue
      ret.update(self._config.get(attr_name, []))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["common_attr_to"]:
      attr = self._config.get(attr_name)
      if attr:
        ret.add(attr)
    return ret

  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    """ 是否依赖当前目标 item_table 中的所有 attr 数据 """
    for attr_name in ["item_attrs_from"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        return True
    return False

  @strict_types
  def depend_on_all_common_attrs(self) -> bool:
    """ 是否依赖所有 common attr 数据 """
    for attr_name in ["common_attrs_from", "utf8_attr_names"]:
      if self.is_dynamic_params(self._config.get(attr_name, [])):
        return True
    return False
