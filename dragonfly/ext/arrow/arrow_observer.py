#!/usr/bin/env python3
"""
filename: arrow_observer.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module for arrow
"""

import operator

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafObserver

class ArrowRecordBatchObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "log_arrow_record_batch"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for attr_name in ["record_batch_from_attr"]:
      attr = self._config.get(attr_name)
      if attr:
        ret.add(attr)
    return ret
