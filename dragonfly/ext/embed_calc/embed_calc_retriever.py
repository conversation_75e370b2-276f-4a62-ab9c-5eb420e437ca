#!/usr/bin/env python3
# coding=utf-8
"""
filename: embed_calc_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, embedding server with calculator retriever 
author: <EMAIL>
date: 2020-09-16 16:45:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class TowerPxtrCacheRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_cache_and_enrich_pxtr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    # load
    if self._config["run_type"] == 1:
      for key in ["cache_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    # save
    if self._config["run_type"] == 0:
      for xtr in self._config["pxtrs"]:
        ret.add(xtr)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    # save
    if self._config["run_type"] == 0:
      for key in ["cache_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    # load
    if self._config["run_type"] == 1:
      for xtr in self._config["pxtrs"]:
        ret.add(xtr)
    return ret
