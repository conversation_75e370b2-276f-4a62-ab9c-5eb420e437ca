#!/usr/bin/env python3
# coding=utf-8
"""
filename: embed_calc_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, embedding server with calculator enricher
author: <EMAIL>
date: 2020-09-16 16:45:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class TowerFetchRemotePxtrAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_remote_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    for key in ["user_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    if "pxtr_timeout_ms" in self._config:
      ret.update(self.extract_dynamic_params(self._config["pxtr_timeout_ms"]))

    extra = self._config.get("pxtr_req_extra_attrs")
    if extra:
      for c in extra:
        if c["common"] : ret.add(c["name"])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    extra = self._config.get("pxtr_req_extra_attrs")
    if extra:
      for c in extra:
        if not c["common"] : ret.add(c["name"])

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self._config.get("output_type", 0) == 0:
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("pxtr_return_value_attr", "return_pxtr_value")))

    if self._config.get("output_type", 0) in [0, 1]:
      ret.add(self._config.get("item_pxtr_label_attr", "return_pxtr_label"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self._config.get("output_type", 0) == 1:
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("pxtr_return_value_attr", "return_pxtr_value")))
    elif self._config.get("output_type", 0) == 2:
      for key in self._config["predict_labels"]:
        ret.add(key)
    return ret


# deprecated
class TowerFetchDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_dot_product_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_hetu_tag"]:
      if key in self._config:
        ret.add(self._config[key])
    if not self._config.get("use_item_key_as_embed_key", True):
      ret.add(self._config["item_embedding_key_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    ret.add(self._config.get("item_pxtr_label_attr", "return_pxtr_label"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class TowerFetchTopNDotProductAttrTwoStepEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_pxtr_two_step"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr", "search_pid_list_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("sorted_item_idx_attr", ""))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class TowerFetchTopNDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "sorted_item_pxtrs_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

# deprecated
class TowerFetchTopNDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_pxtr"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    ret.update(self.extract_dynamic_params(self._config.get("top_n")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_key_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("sorted_item_idx_attr", ""))
    ret.add(self._config.get("sorted_item_pxtrs_attr", "sorted_item_pxtrs_res"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class TowerFetchTopNDotProductAttrDualsimEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_pxtr_for_dualsim"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = []
    if self._config.get("is_target_embedding_common", True):
      check_attrs.append("target_embedding_attr")
    for key in check_attrs:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    check_attrs = ["item_embedding_list_key_attr"]
    if not self._config.get("is_target_embedding_common", True):
      check_attrs.append("target_embedding_attr")
    for key in check_attrs:
      ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "sorted_item_score_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

# deprecated
class TowerFetchGPUIndexAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_gpu_index"

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    if self._config.get("use_item_key_as_item_embed_key", False):
      check_attrs.append("item_embedding_key_attr")

    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["return_gpu_index_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

# deprecated
class TowerConcatEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_concat_tower_embedding"

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["photoid_as_embedkey_attr", "embedding_sign_attr", "common_embedding_len_attr",
                "common_embedding_dim_attr", "compress_zero_embed_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      for key in ["embedding_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      for key in ["embedding_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    if "compress_mark_attr" in self._config:
      ret.add(self._config["compress_mark_attr"])
    return ret

# deprecated
class TowerPredictDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "tower_predict_xtr"

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self._config.get("load_from_item_context", False):
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])

    for key in ["common_embedding_attr", "compress_zero_embed_attr", "common_embedding_dim_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self._config.get("load_from_item_context", False):
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])

    if "compress_mark_attr" in self._config:
      ret.add(self._config["compress_mark_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      for key in ["return_pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      for key in ["return_pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

# deprecated
class DotProductCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "doc_product_pxtr_common_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["gsu_embedding_attr", "target_embedding_attr", "return_pxtr_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["return_pxtr_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

# deprecated
class DotProductItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "doc_product_pxtr_item_attr"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["gsu_embedding_attr", "target_embedding_attr", "return_pxtr_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["return_pxtr_value_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    return ret
  
class TowerFetchLocalEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "V_fetch_local_embedding_V"

  @strict_types
  def use_photoid_as_embedkey(self) -> bool:
    return self._config.get("photoid_as_embedkey", False)

  @strict_types
  def output_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["tower_caller"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self.use_photoid_as_embedkey():
      for key in ["embedding_sign_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.output_to_item_context():
      for key in ["embedding_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      for key in ["embedding_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["miss_embedding_mark", "item_hetu_tag_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerFetchLocalInt16EmbeddingAttrEnricher(TowerFetchLocalEmbeddingAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_int16_embedding"

class TowerFetchLocalScaleInt8EmbeddingAttrEnricher(TowerFetchLocalEmbeddingAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_scale_int8_embedding"

class TowerFetchLocalFloatEmbeddingAttrEnricher(TowerFetchLocalEmbeddingAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_local_float_embedding"

class TowerConvertEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "convert_embedding_format"

  @strict_types
  def is_common_convert(self) -> bool:
    return self._config.get("is_common", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if self.is_common_convert():
      for key in ["input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if not self.is_common_convert():
      for key in ["input_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if self.is_common_convert():
      for key in ["output_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if not self.is_common_convert():
      for key in ["output_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

class TowerLocationDistanceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "location_distance"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_lat_list_attr", "common_lon_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_lat_attr", "item_lon_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_poi_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class NeighbourLocationDistanceEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_item_side_distance"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["distance_limit_common_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_lat_attr", "photo_lon_attr", "neighbour_photos_lat_attr", "neighbour_photos_lon_attr", "neighbour_photos_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_distance_attr", "output_filterd_neighbour_photos_attr", "output_filterd_distance_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerListAddEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "list_add"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_list_attr", "item_add_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerListNormEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "list_norm"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["output_item_list_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerPredictXtrAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "V_predict_pxtr_V"

  @strict_types
  def load_from_item_context(self) -> bool:
    return self._config.get("load_from_item_context", False)

  @strict_types
  def save_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False) or self._config.get("output_type", 0) in [1,3]

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["common_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["miss_embedding_mark"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.save_to_item_context():
      for key in ["pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.save_to_item_context():
      if self._config["output_type"] == 1:
        for key in ["pxtr_value_attr"]:
          if key in self._config:
            ret.add(self._config[key])
      elif self._config["output_type"] == 3:
        for key in self._config["predict_labels"]:
          ret.add(key)
    return ret

class TowerPredictFloatDotProductXtrAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_dot_product_xtr_v_float"

class TowerPredictInt16DotProductXtrAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_dot_product_xtr_v_int16"

class TowerPredictClusterXtrFloatAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_cluster_xtr_v_float"

  @strict_types
  def need_map_cluster(self) -> bool:
    return self._config.get("need_map_cluster", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["common_embedding_attr", "req_common_embedding"]:
      if key in self._config:
        ret.add(self._config[key])
    if self.need_map_cluster():
      ret.add(self._config["common_cluster_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["miss_embedding_mark", "item_cluster_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class FloatEmbeddingXtrAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "float_embedding_dot_product"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["common_embedding_attr","req_common_embedding"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class SimEmbeddingDotProductAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_embedding_dot_product"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    for key in ["common_embedding_attr","req_common_embedding"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
class SimEmbeddingTopNDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_embedding_topn_dot_product"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "common_embedding_attr",
                "req_common_embedding"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["miss_embedding_mark"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_ids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class SimEmbeddingTopNDotProductMultiHeadBiasAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sim_embedding_topn_dot_product_multi_head_bias"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "common_embedding_attr",
                "req_common_embedding", "head_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["miss_embedding_mark", "time_embedding_attr",
                "time_embedding_miss_mark", "play_embedding_attr",
                "play_embedding_miss_mark"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_ids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class ScaleInt8TopNDotProductAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "scale_int8_topn_dot_product"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_attr", "common_embedding_attr",
                "req_common_embedding", "head_slot_attr"]:
      if key in self._config:
        ret.add(self._config[key])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["miss_embedding_mark", "time_embedding_attr",
                "time_embedding_miss_mark", "play_embedding_attr",
                "play_embedding_miss_mark"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_ids_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    return ret

class PredictMVKEPxtrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_mvke_pxtr"

class TowerPredictHetuTagFloatDotProductXtrAttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_dot_product_xtr_v_hetu_tag_float"

# deprecated
class TowerPredictXtrFpgaAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_xtr_by_fpga"

  @strict_types
  def save_to_item_context(self) -> bool:
    return self._config.get("save_to_item_context", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["common_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["fpga_index_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if not self.save_to_item_context():
      for key in ["pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.save_to_item_context():
      for key in ["pxtr_value_attr"]:
        if key in self._config:
          ret.add(self._config[key])
    return ret

# deprecated
class TowerFetchFpgaIndexAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_fpga_index"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["fpga_index_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerTransFloatVector2MatrixAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trans_vec2matrix"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for key in ["vector_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["matrix_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret
 
class TowerFetchSimUserEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_sim_hetu_tag_user_embedding"

  @strict_types
  def get_input_attr_config(self):
    return self._config["inputs"] if "inputs" in self._config else list()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for c in self.get_input_attr_config():
      if c.get("common", False):
        ret.add(c["attr_name"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for c in self.get_input_attr_config():
      if not c.get("common", False):
        ret.add(c["attr_name"])
    for key in ["item_slots", "item_signs"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_user_embedding_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerPhotoInfoItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_sim_feature_with_photo_info"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["photo_info_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set([
      'featurePId',
      'featurePAId',
      'featurePMmuImgClusterV3',
      'featurePSubtitleSegment',
      'featurePThanosPlayStatDuration',
      'featurePThanosPlayStatClick',
      'featurePThanosPlayStat',
      'featurePPlayStatDuration',
      'featurePPlayStat',
      'featurePAuthorExpCtr',
      'featurePAuthorExpLtr',
      'featurePAuthorExpWtr',
      'featurePAuthorExpFtr',
      'featurePAuthorTowerCluster',
      'featurePExploreShortPlayStat',
      'featurePExploreLongPlayStat',
      'featurePGlobalShortPlayStat',
      'featurePGlobalLongPlayStat',
      'featurePUseEmpStat',
      'featurePTag',
      'featurePHetuTagOneList',
      'featurePHetuTagTwoList',
      'featurePThanosPlayRateStat',
      'featurePExploreStatCtr',
      'featurePUploadType',
      'featurePExploreLikeStat',
      'featurePExploreFollowStat',
      'featurePExploreForwardStat',
      'featurePGlobalLikeStat',
      'featurePGlobalFollowStat',
      'featurePGlobalForwardStat',
      'featurePThanosStatCtr',
      'featurePThanosStatCtrRealShow',
      'featurePThanosLikeClickStat',
      'featurePThanosFollowClickStat',
      'featurePThanosForwardClickStat',
      'featurePThanosCommentClickStat',
      'featurePThanosLikeStat',
      'featurePThanosFollowStat',
      'featurePThanosFowardStat',
      'featurePThanosCommentStat',
      'featurePUseThanosEmpStat',
    ])
    return ret

class TowerEmbeddingLocalCacheEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_cache_op"

  @strict_types
  def is_save(self, c):
    return c.get("save", False)

  @strict_types
  def is_common(self, c):
    return c.get("common", False)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    for c in self._config["cache"]:
      if self.is_common(c):
        ret.add(c["key_attr"])
        if self.is_save(c):
          ret.add(c["value_attr"])

    return ret
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for c in self._config["cache"]:
      if not self.is_common(c):
        ret.add(c["key_attr"])
        if self.is_save(c):
          ret.add(c["value_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for c in self._config["cache"]:
      if self.is_common(c) and not self.is_save(c):
        ret.add(c["value_attr"])

    return ret
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for c in self._config["cache"]:
      if not self.is_common(c) and not self.is_save(c):
        ret.add(c["value_attr"])
    return ret


class TowerFetchTopNDotProductMultiHeadBiasEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_tower_topn_dot_product_multi_head_bias"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def output_to_item_context(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    check_attrs = ["user_embedding_attr", "kconf_timeout_ms_attr"]
    for key in check_attrs:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["item_embedding_key_attr", "colossus_time_attr", "colossus_play_attr", "colossus_duration_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["sorted_item_idx_attr", "sorted_item_attention_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if self.output_to_item_context():
      ret.add(self._config.get("item_pxtr_value_attr",
                               self._config.get("return_pxtr_value_attr", "return_pxtr_value")))
    return ret

class TowerDumpAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dump_attrs_package"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("dump_attrs", list()):
      if u.get("is_common", False):
        attrs.add(u["input_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("dump_attrs", list()):
      if not u.get("is_common", False):
        attrs.add(u["input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    for key in ["output_attr"]:
      if key in self._config:
        ret.add(self._config[key])
    return ret

class TowerTransLocalEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trans_local_embedding"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("trans_attrs", list()):
      if u["trans_type"] in ["c2c", "c2i", "m2i"]:
        attrs.add(u["input_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("trans_attrs", list()):
      if u["trans_type"] in ["i2i"]:
        attrs.add(u["input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("trans_attrs", list()):
      if u["trans_type"] in ["c2c"]:
        attrs.add(u["as_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for u in self._config.get("trans_attrs", list()):
      if u["trans_type"] in ["c2i", "i2i", "m2i"]:
        attrs.add(u["as_attr"])
    return attrs

class SimpleRedisIdFilterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "simple_mark_by_redis_ids"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["filter_key_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    for key in ["filter_mark_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret

class EmbMatmulReduceSumInt16AttrEnricher(TowerPredictXtrAttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "emb_matmul_reduce_sum_int16"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if not self.load_from_item_context():
      for key in ["item_embedding_attr"]:
        if key in self._config:
          ret.add(self._config[key])

    for key in ["common_embedding_attr","req_common_embedding"]:
      if key in self._config:
        ret.add(self._config[key])

    for key in ["recent_num", "recent_wei", "far_num", "far_wei", "default_wei"]:
      if key in self._config:
        ret.update(self.extract_dynamic_params(self._config[key]))
    return ret

class EmbMatmulReduceSumFloatAttrEnricher(EmbMatmulReduceSumInt16AttrEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "emb_matmul_reduce_sum_float"

class NormBTQEmbEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "norm_btq_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("from_extra_var", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_result_to_common_attr", ""))
    return attrs
