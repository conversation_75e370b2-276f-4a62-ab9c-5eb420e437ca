#!/usr/bin/env python3
# coding=utf-8
"""
filename: embed_calc_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, embedding server with calculator mixin
author: <EMAIL>
date: 2020-09-16 16:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .embed_calc_enricher import *
from .embed_calc_retriever import *
from .embed_calc_arranger import *

class EmbedCalcApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 双塔相关的 Processor 接口:
    请求远端 pxtr
    - TowerFetchRemotePxtrAttrEnricher

    查本地 photo embedding
    - TowerFetchLocalInt16EmbeddingAttrEnricher
    - TowerFetchLocalFloatEmbeddingAttrEnricher

    转换 embedding 格式
    - TowerConvertEmbeddingAttrEnricher

    pxtr 计算
    - TowerPredictFloatDotProductXtrAttrEnricher (内积 + sigmoid)
    - TowerPredictInt16DotProductXtrAttrEnricher (内积 + sigmoid)
    - FloatEmbeddingXtrAttrEnricher (多user float 向量内积 + sigmoid)
    - SimEmbeddingDotProductAttrEnricher (多user int16 向量内积)
    - TowerPredictHetuTagFloatDotProductXtrAttrEnricher (匹配photo、user的hetu tag再计算内积 + sigmoid)

    response 结果格式转换
    - TowerTransFloatVector2MatrixAttrEnricher

    sim tower 专用
    - TowerFetchSimUserEmbeddingAttrEnricher
  """

  def fetch_tower_remote_pxtr(self, **kwargs):
    """
    TowerFetchRemotePxtrAttrEnricher
    ------
    向远端请求预估多个label (or target) 的多个 user * item 的pxtr

    发item_embedding_key_attr送user embedding 和 item key 列表, 获取各item对应label下的user * item 的pxtr

    user embedding : 每个label都有独有的user embedding，多个label的user embedding拼接成一个更长的embedding , 多个user的embedding 可以继续拼接

    参数
    ------
    `common_embedding_len`: [int] 多个 label/target 下 user embedding 的总长度

    `predict_labels`: [list[string]] 预估目标列表, 如[ctr, ltr, ...] ，必须保持与 graph 的输出顺序一致

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 ItemAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 ItemAttr 获取 embedding key

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr

    `pxtr_kess_service`: [string] pxtr 预估服务kess 服务名

    `pxtr_kess_cluster`: [string] pxtr 预估服务kess 集群名，默认 PRODUCTION

    `pxtr_shards`: [int] pxtr 预估服务shard 数量

    `pxtr_subreq_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `pxtr_timeout_ms`: [动态参数] pxtr 预估服务 rpc 超时，单位毫秒，默认 100 ms

    `pxtr_req_type`: [string] pxtr 预估服务也是dragonfly 实现，需要request_type参数

    `pxtr_req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `pxtr_req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `pxtr_return_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value

    `pxtr_return_type`: [int] pxtr server 返回的 value 的格式, 默认值为 0
                            0 : photos * users * targets
                            1 : photos * targets

    `pxtr_req_extra_attrs`: [list(dict)] 额外的需要从 CommonAttr 或 ItemAttr 中注入 pxtr request 的信息, list 元素配置信息如下
                            "name" : [string] attr name
                            "as" : [string] name 指定的 attr 在 request 里将重命名为 as 的值
                            "type" : [string] attr类型，目前仅支持 "int","double","string","int_list","double_list", "string_list"
                            "common" : [bool] 是否从 CommonAttr 注入
                            "def_int" : [int] type 为 int 时的默认值
                            "def_float" : [float] type 为 float 时的默认值
                            "def_string" : [string] type 为 string 时的默认值
                            type 为各种 list 时，默认值只能为空数组

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `pxtr_return_value_attr`

    示例
    ------
    ``` python
    .fetch_tower_remote_pxtr(common_embedding_len=136*3,
                             predict_labels = [ctr, ltr, svtr],
                             user_embedding_attr = "user_output_embedding",
                             use_item_key_as_item_embed_key = True,
                             output_type=0,
                             pxtr_kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                             pxtr_shards = 8,
                             pxtr_timeout_ms = "{{kconf_pxtr_timeout_ms}}",
                             pxtr_req_type = 'rt_tower_predict_pxtr',
                             pxtr_req_common_embedding_attr='req_common_embedding',
                             pxtr_req_tower_caller_attr='tower_caller',
                             pxtr_return_value_attr='return_pxtr_value_attr',
                             pxtr_req_extra_attrs=[{"name":"xxx", "type":"int_list", "common":true},
                                                   {"name":"yyy", "type":"double", "common":fasle}],
                             )
    ```

    """
    self._add_processor(TowerFetchRemotePxtrAttrEnricher(kwargs))
    return self

  def fetch_tower_dot_product_pxtr(self, **kwargs):
    """
    TowerFetchDotProductAttrEnricher
    ------
    向远端请求预估多个label (or target) 的 user * item 的pxtr

    发item_embedding_key_attr送user embedding 和 item key 列表, 获取各item对应label下的user * item 的pxtr

    user embedding : 每个label都有独有的user embedding，多个label的user embedding拼接成一个更长的embedding

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 ItemAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 ItemAttr 获取 embedding key 列表的key

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_hetu_tag_attr`: [string] item hetu tag 在 context item attr 中的字段名 ， 可选

    `total_hetu_tag_num`: [int] 所有 hetu tag 种类的数量 , 可选

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    示例
    ------
    ``` python
    .fetch_tower_dot_product_pxtr(user_embedding_attr = "user_output_embedding",
                                  use_item_key_as_item_embed_key = True,
                                  predict_labels = [ctr, ltr, svtr],
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  )
    ```

    """
    self._add_processor(TowerFetchDotProductAttrEnricher(kwargs))
    return self

  def fetch_tower_topn_dot_product_pxtr(self, **kwargs):
    """
    TowerFetchTopNDotProductAttrEnricher
    ------
    同 fetch_tower_dot_product_pxtr, 但只返回 user * item topn 的 pxtr 及对应的 index 值。
    目前只支持单目标，且 output_type 必须设置为 4.

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 CommonAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 CommonAttr 获取 embedding key 列表的key

    `non_unique_item_embedding_key`: [bool] 当 item_embedding_key_attr 为非唯一 key 时使用，默认为 false

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `output_type`: [int]  4: 输出 TopN 的 pxtr 及对应的 index。
                          5: 同 4，但是使用 dragon 内置的 int/double list list 而不是 int/double vector 指针，开销会稍微高一些，但是交互性更好。

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    `sorted_item_idx_attr`: [string] 输出排序后 topn item index 的 attr 名

    `sorted_item_pxtrs_attr`: [string] 输出排序后 topn item pxtrs 的 attr 名

    `top_n`: [int][动态参数] 输出的 list 长度，需要与 DPcalc 服务的配置对应

    `pxtr_req_extra_attrs`: [list(dict)] 额外的需要从 CommonAttr 或 ItemAttr 中注入 pxtr request 的信息, list 元素配置信息如下
                            "name" : [string] attr name
                            "as" : [string] name 指定的 attr 在 request 里将重命名为 as 的值
                            "type" : [string] attr类型，目前仅支持 "int","double","string","int_list","double_list", "string_list"
                            "common" : [bool] 是否从 CommonAttr 注入
                            "def_int" : [int] type 为 int 时的默认值
                            "def_float" : [float] type 为 float 时的默认值
                            "def_string" : [string] type 为 string 时的默认值
                            type 为各种 list 时，默认值只能为空数组

    示例
    ------
    ``` python
    .fetch_tower_topn_dot_product_pxtr(user_embedding_attr = "user_output_embedding",
                                  use_item_key_as_item_embed_key = True,
                                  predict_labels = [ctr, ltr, svtr],
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  output_type = 4,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  return_sorted_item_ids_attr='sorted_item_ids_vec",
                                  sorted_item_idx_attr='sorted_item_idx',
                                  top_n = 50,
                                  )
    ```
    """
    self._add_processor(TowerFetchTopNDotProductAttrEnricher(kwargs))
    return self

  def fetch_tower_topn_dot_product_pxtr_for_dualsim(self, **kwargs):
    """
    TowerFetchTopNDotProductAttrDualsimEnricher
    ------
    user embedding 与每个item 的 list 都计算一次距离

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `is_target_embedding_common`: [bool] user embedding 是否为 common embedding，默认 true

    `target_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `item_embedding_list_key_attr`: [string] ItemAttr 获取 embedding key 列表的key

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `sorted_item_idx_attr`: [string] 输出排序后 topn item index 的 ItemAttr 名

    `sorted_item_score_attr`: [string] 输出排序后 topn item score 的 ItemAttr 名

    示例
    ------
    ``` python
    .fetch_tower_topn_dot_product_pxtr_for_dualsim(
                                  target_embedding_attr = "user_output_embedding",
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  return_sorted_item_ids_attr="sorted_item_ids_vec",
                                  sorted_item_idx_attr='sorted_item_idx',
                                  item_embedding_list_key_attr='kgnn_user_id_list',
                                  sorted_item_score_attr='sorted_item_score',
                                  emb_dim=64,
                                  top_n=200
                                  )
    ```
    """
    self._add_processor(TowerFetchTopNDotProductAttrDualsimEnricher(kwargs))
    return self

  def fetch_tower_topn_dot_product_pxtr_two_step(self, **kwargs):
    """
    TowerFetchTopNDotProductAttrTwoStepEnricher
    ------
    同 fetch_tower_topn_dot_product_pxtr, 双阶段检索

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 CommonAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 CommonAttr 获取 embedding key 列表的key

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr
                          4: 输出 TopN 的 pxtr 及对应的 index

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    `sorted_item_idx_attr`: [string] 输出排序后 topn item index 的 attr 名

    `search_pid_list_attr`: [string] search pid list 在 context CommonAttr 中的字段名

    `req_common_search_pid_list_attr`: [string] 从 CommonAttr 中指示 search_pid_list_attr 存放的字段,默认值 req_common_search_pid_list

    示例
    ------
    ``` python
    .fetch_tower_topn_dot_product_pxtr_two_step(user_embedding_attr = "user_output_embedding",
                                  use_item_key_as_item_embed_key = True,
                                  predict_labels = [ctr, ltr, svtr],
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  output_type = 4,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  return_sorted_item_ids_attr='sorted_item_ids_vec",
                                  sorted_item_idx_attr='sorted_item_idx',
                                  search_pid_list_attr='search_pid_list',
                                  search_topn=1000,
                                  req_common_search_pid_list_attr='req_common_search_pid_list',
                                  )
    ```
    """
    self._add_processor(TowerFetchTopNDotProductAttrTwoStepEnricher(kwargs))
    return self

  def local_fetch_tower_embedding(self, **kwargs):
    """
    TowerConcatEmbeddingAttrEnricher
    ------
    从本地的embedding table 查询指定的embedding sign list各sign的embedding，然后存放在vector中

    参数
    ------
    `save_to_item_context`: [string] 是否将 item embedding 结果作为 item attr 写入 context , 默认 False

    `embedding_value_attr`: [string] item embedding matrix 在CommonAttr中的存放字段, 或 item embedding 在 ItemAttr 的存放字段

    `compress_mark_attr`: [string] 如果要压缩全为0的item embedding，则在ItemAttr中为该item添加值为1的属性，存放字段名由该配置指定，默认compress_zero_embed

    `embedding_sign_attr`: [string] 当上游指定 use_item_key_as_embed_key 为 false 时, 该配置指示 CommonAttr 中 embedding sign list的存放字段

    `common_embedding_len_attr`: [string] 从 CommonAttr 中获取 common embedding float 长度存放的字段 ,默认值req_common_embedding_dim

    `common_embedding_dim_attr`: [string] 从 CommonAttr 中获取 common embedding 维度存放的字段,默认值req_common_embedding_len

    `photoid_as_embedkey_attr`: [string] 从 CommonAttr 中获取 request 中的 item_id(photo_id) 是否就是 embedding key的存放字段,默认值req_photoid_as_embedkey

    `compress_zero_embed_attr`: [string] 从 CommonAttr 中获取是否压缩（删除)未查询到 embedding 的 photo 的结果的存放字段，默认值req_compress_zero_embed

    `req_tower_caller_attr`: [string] 从 CommonAttr 中获取调用者身份的存放字段，默认值tower_caller

    示例
    ------
    ``` python
    .local_fetch_tower_embedding(embedding_value_attr="tower_pxtr_value",
                                 compress_mark_attr="compress_zero_embed")
    ```

    """
    self._add_processor(TowerConcatEmbeddingAttrEnricher(kwargs))
    return self

  def tower_predict_pxtr(self, **kwargs):
    """
    TowerPredictDotProductAttrEnricher
    ------
    本地计算 user embedding 和 item embedding 的内积, 可同时计算多个 label (or target) 的内积

    user embedding : 每个label都有独有的user embedding，多个label的user embedding拼接成一个更长的embedding

    item embedding : 每个label都有都有的item embedding，多个label的item embedding拼接成一个更长的embedding
                     多个item embedding组成成一个embedding矩阵

    参数
    ------
    `item_embedding_attr`: [string] 指定如何从context CommonAttr获取 item embedding

    `common_embedding_attr`: [string] 从 CommonAttr 中获取 common embedding 存放的字段,默认值req_common_embedding

    `common_embedding_dim_attr`: [string] 从 CommonAttr 中获取 common embedding 维度存放的字段,默认值req_common_embedding_len

    `compress_zero_embed_attr`: [string] 从 CommonAttr 中获取是否压缩（删除)未查询到 embedding 的 photo 的结果的存放字段，默认值 req_compress_zero_embed

    `compress_mark_attr`: [string] 如果要压缩全为 0 的item embedding，则在 ItemAttr 中为该 item 添加值为 1 的属性，存放字段名由该配置指定，默认 compress_zero_embed

    `pxtr_return_value_attr`: [string] 从 CommonAttr 中获取pxtr 结果存放的字段，必须遵守，默认值return_pxtr_value

    `load_from_item_context`: [bool] 是否从 context common attr 获取已经拼接在一起的 item attr , 默认 False

    `save_to_item_context`: [bool] 是否将 pxtr 结果作为 item attr 写入 context , 默认 False

    `use_cosine`: [bool] embedding 距离计算是否改为 cosine 距离 , 默认 False

    示例
    ------
    ``` python
    .tower_predict_pxtr(item_embedding_attr="tower_pxtr_value")
    ```

    """
    self._add_processor(TowerPredictDotProductAttrEnricher(kwargs))
    return self
  
  def doc_product_pxtr_common_attr(self, **kwargs):
    """
    DotProductCommonAttrEnricher
    ------
    计算 target embedding 和 gsu embedding 的内积, 适用于 target embedding 和 gsu embedding 都是 common attr

    参数
    ------
    `gsu_embedding_attr`: [string] 从 CommonAttr 获取 gsu_embedding 存放的字段

    `target_embedding_attr`: [string] 从 CommonAttr 中获取 target_embedding 存放的字段

    `return_pxtr_value_attr`: [string] 从 CommonAttr 中获取 pxtr 结果存放的字段

    `use_cosine`: [bool] embedding 距离计算是否改为 cosine 距离 , 默认 False

    示例
    ------
    ``` python
    .doc_product_pxtr_common_attr(
      gsu_embedding_attr="gsu_embedding,
      target_embedding_attr='target_embedding',
      return_pxtr_value_attr='return_pxtr_value'
    )
    ```

    """
    self._add_processor(DotProductCommonAttrEnricher(kwargs))
    return self

  def doc_product_pxtr_item_attr(self, **kwargs):
    """
    DotProductItemAttrEnricher
    ------
    计算 target embedding 和 gsu embedding 的内积, 适用于 target embedding 和 gsu embedding 都是 item attr

    参数
    ------
    `gsu_embedding_attr`: [string] 从 ItemAttr 获取 gsu_embedding 存放的字段

    `target_embedding_attr`: [string] 从 ItemAttr 中获取 target_embedding 存放的字段

    `return_pxtr_value_attr`: [string] 从 ItemAttr 中获取 pxtr 结果存放的字段

    `use_cosine`: [bool] embedding 距离计算是否改为 cosine 距离 , 默认 False

    示例
    ------
    ``` python
    .doc_product_pxtr_item_attr(
      gsu_embedding_attr="gsu_embedding,
      target_embedding_attr='target_embedding',
      return_pxtr_value_attr='return_pxtr_value'
    )
    ```

    """
    self._add_processor(DotProductItemAttrEnricher(kwargs))
    return self

  def fetch_local_int16_embedding(self, **kwargs):
    """
    TowerFetchLocalInt16EmbeddingAttrEnricher
    ------
    从本地的embedding table 查询指定的embedding sign list各sign的embedding，然后存放在vector中
    embedding 元素类型为 int16

    参数
    ------
    `photoid_as_embedkey`: [bool] 是否用 item key / photo id 作为查询 embedding 的 key , 默认 True

    `embedding_sign_attr`: [string] photoid_as_embedkey 为 false 时, 该配置指示 CommonAttr 中 embedding sign list的存放字段

    `save_to_item_context`: [bool] 是否将 item embedding 结果作为 item attr 写入 context , 默认 False

    `output_type`: [int] 1:set context common/item int/double attr,
                         2:set context common/item int/double list attr,
                         0: default, set double/int ptr attr
                         other: 未定义

    `save_as_common_embedding`: [bool] output_type=0 && save_to_item_context=false时生效,
                                       是否将 embedding 结果作为 common embedding 写入 context ,
                                       默认 False , True 的话，会将其转成裸指针格式存储

    `embedding_value_attr`: [string] item embedding pointer 在 context 中的存放字段

    `miss_embedding_mark`: [string] 如果 item 缺失 embedding ，则用该字段标记一个 int attr = 1，否则标记 0

    `predict_target_num`: [int] model predict target number

    `common_embedding_len`: [int] 多个 target common embedding 的总长度

    `tower_caller_attr`: [string] 从 CommonAttr 中获取调用者身份的存放字段，默认值tower_caller

    示例
    ------
    ``` python
    .fetch_local_int16_embedding(photoid_as_embedkey=False,
                                 embedding_sign_attr="emb_sign_keys",
                                 save_to_item_context=True,
                                 embedding_value_attr="emb_values",
                                 miss_embedding_mark="miss_embedding",
                                 predict_target_num=14,
                                 common_embedding_len=1360,
                                 tower_caller_attr="tower_caller",
                                 output_type=0,
                                 )
    ```
    """
    self._add_processor(TowerFetchLocalInt16EmbeddingAttrEnricher(kwargs))
    return self

  def fetch_local_scale_int8_embedding(self, **kwargs):
    """
    TowerFetchLocalScaleInt8EmbeddingAttrEnricher
    ------
    从本地的embedding table 查询指定的embedding sign list各sign的embedding，然后存放在vector中
    embedding 元素类型为 int8

    参数
    ------
    `photoid_as_embedkey`: [bool] 是否用 item key / photo id 作为查询 embedding 的 key , 默认 True

    `embedding_sign_attr`: [string] photoid_as_embedkey 为 false 时, 该配置指示 CommonAttr 中 embedding sign list的存放字段

    `save_to_item_context`: [bool] 是否将 item embedding 结果作为 item attr 写入 context , 默认 False

    `output_type`: [int] 1:set context common/item int/double attr,
                         2:set context common/item int/double list attr,
                         0: default, set double/int ptr attr
                         other: 未定义
                         目前只支持 0

    `save_as_common_embedding`: [bool] output_type=0 && save_to_item_context=false时生效,
                                       是否将 embedding 结果作为 common embedding 写入 context ,
                                       默认 False , True 的话，会将其转成裸指针格式存储

    `embedding_value_attr`: [string] item embedding pointer 在 context 中的存放字段

    `miss_embedding_mark`: [string] 如果 item 缺失 embedding ，则用该字段标记一个 int attr = 1，否则标记 0

    `predict_target_num`: [int] model predict target number

    `common_embedding_len`: [int] 多个 target common embedding 的总长度

    `tower_caller_attr`: [string] 从 CommonAttr 中获取调用者身份的存放字段，默认值tower_caller

    示例
    ------
    ``` python
    .fetch_local_int16_embedding(photoid_as_embedkey=False,
                                 embedding_sign_attr="emb_sign_keys",
                                 save_to_item_context=True,
                                 embedding_value_attr="emb_values",
                                 miss_embedding_mark="miss_embedding",
                                 predict_target_num=14,
                                 common_embedding_len=1360,
                                 tower_caller_attr="tower_caller",
                                 output_type=0,
                                 )
    ```
    """
    self._add_processor(TowerFetchLocalScaleInt8EmbeddingAttrEnricher(kwargs))
    return self

  def fetch_local_float_embedding(self, **kwargs):
    """
    TowerFetchLocalFloatEmbeddingAttrEnricher
    ------
    从本地的embedding table 查询指定的embedding sign list各sign的embedding，然后存放在vector中
    embedding 元素类型为 float

    参数
    ------
    同 fetch_local_int16_embedding / TowerFetchLocalInt16EmbeddingAttrEnricher

    示例
    ------
    ``` python
    .fetch_local_float_embedding(photoid_as_embedkey=False,
                                 embedding_sign_attr="emb_sign_keys",
                                 save_to_item_context=True,
                                 embedding_value_attr="emb_values",
                                 miss_embedding_mark="miss_embedding",
                                 predict_target_num=14,
                                 common_embedding_len=1360,
                                 tower_caller_attr="tower_caller",
                                 output_type=0,
                                 )
    ```
    """
    self._add_processor(TowerFetchLocalFloatEmbeddingAttrEnricher(kwargs))
    return self

  def convert_embedding_format(self, **kwargs):
    """
    TowerConvertEmbeddingAttrEnricher
    ------
    特殊需求：将 IntList / DoubleList 的 attr 转成 Int16* / float* / float vector (仅支持common atrr) 的 attr , IntList 可转 float_ptr , DoubleList 也可转 int16_ptr

    参数
    ------
    `is_common`: [bool] 转换的 attr 是否为 CommonAttr ，默认 False

    `input_type`: [string] 转换 attr 的类型，取值： int_list/double_list

    `output_type`: [string] 转换后 attr 类型，取值： int16_ptr/float_ptr

    `input_attr"`: [string] 转换 attr 的字段

    `output_attr"`: [string] 转换后 attr 的字段

    示例
    ------
    ``` python
    .convert_embedding_format(is_common=True,
                              input_type="int_list",
                              output_type="float_ptr",
                              input_attr="src_embedding",
                              output_attr="dst_embedding",
                             )
    ```
    """
    self._add_processor(TowerConvertEmbeddingAttrEnricher(kwargs))
    return self

  def location_distance(self, **kwargs):
    """
    TowerLocationDistanceEnricher
    ------
    计算 common_list 经纬度 与 item 经纬度的距离，单位km.

    参数
    ------
    `common_lat_list_attr` : [double list] list视频的纬度

    `common_lon_list_attr` : [double list] list视频的经度

    `item_lat_attr` : [double] history action list 对应的纬度

    `item_lon_attr` : [double] history action list 对应的经度

    `output_poi_distance_attr` : [double list] common list 与 item poi 距离(km)

    `lat_limit` : [double] abs(lat - item_lat) < lat_limit

    `lon_limit` : [double] abs(lon - item_lon) < lon_limit

    `calculate_limit` : [int] 计算次数限制

    示例
    ------
    ``` python
    .location_distance(common_lat_list_attr="lat_list",
                       common_lon_list_attr="lon_list",
                       item_lat_attr="photo_lat",
                       item_lon_attr="photo_lon",
                       output_poi_distance_attr="poi_distance")
    ```
    """
    self._add_processor(TowerLocationDistanceEnricher(kwargs))
    return self

  def calc_item_side_distance(self, **kwargs):
    """
    NeighbourLocationDistanceEnricher
    ------
    计算 item 经纬度 与 item 邻居经纬度的距离，单位km.

    参数
    ------
    `photo_lat_attr` : [string] target photo的纬度的item attr

    `photo_lon_attr` : [string] target photo的经度的item attr

    `neighbour_photos_lat_attr` : [string] 邻居视频对应的纬度列表attr

    `neighbour_photos_lon_attr` : [string] 邻居视频对应的经度列表attr

    `output_distance_attr` : [string] 输出的距离(km)

    `distance_limit_common_attr` : [string] 距离过滤选项(单位m, 仅输出小于距离限制的结果)

    `neighbour_photos_attr` : [string] 邻居视频pid列表attr

    `output_filterd_neighbour_photos_attr` : [string] 距离过滤后的邻居视频pid列表attr

    `output_filterd_distance_attr` : [string] 距离过滤后的输出的距离(km)

    示例
    ------
    ``` python
    .calc_item_side_distance(photo_lat_attr="pLat",
                       photo_lon_attr="pLong",
                       neighbour_photos_lat_attr='neighbours_pLat',
                       neighbour_photos_lon_attr='neighbours_pLong',
                       output_distance_attr='output_distances_raw',
                       distance_limit_common_attr="distance_limit",
                       neighbour_photos_attr="geo_neighbor_pid_list_raw",
                       output_filterd_neighbour_photos_attr="geo_neighbor_pid_list",
                       output_filterd_distance_attr="output_distances")
    ```
    """
    self._add_processor(NeighbourLocationDistanceEnricher(kwargs))
    return self

  def list_add(self, **kwargs):
    """
    TowerListAddEnricher
    ------
    计算 list + a .

    参数
    ------
    `item_list_attr` : [int list] item list

    `item_add_attr` : [int] item add

    `output_item_list_attr` : [int list] item_list + item_add 结果

    示例
    ------
    ``` python
    .list_add(item_list_attr="item_list",
              item_add_attr="item_add",
              output_item_list_attr="item_add_list")
    ```
    """
    self._add_processor(TowerListAddEnricher(kwargs))
    return self

  def list_norm(self, **kwargs):
    """
    TowerListNormEnricher
    ------
    计算 list L2 norm .

    参数
    ------
    `item_list_attr` : [double list] item list

    `output_item_list_attr` : [double list] item_list L2 norm 结果

    示例
    ------
    ``` python
    .list_norm(item_list_attr="item_list",
              output_item_list_attr="item_norm_list")
    ```
    """
    self._add_processor(TowerListNormEnricher(kwargs))
    return self

  def predict_dot_product_xtr_v_float(self, **kwargs):
    """
    TowerPredictFloatDotProductXtrAttrEnricher
    ------
    计算向量内积、再算 sigmoid

    参数
    ------
    `predict_target_num`: [int] model predict target number

    `common_embedding_dim`: [int] 单个 target common embedding 的维度

    `common_embedding_attr`: [string] common embedding 在 context 中字段名 (common attr)

    `load_from_item_context`: [bool] 是否从 context item attr 中获取 item embedding ，默认 false

    `item_embedding_attr`: [string] item embedding 在 context 中的字段名

    `miss_embedding_mark`: [string] 如果 item 缺失 embedding ，则用该字段标记一个 int attr = 1，否则标记 0

    `predict_labels`: [list] of [string] 预估的 label 名称列表, output_type==3 时必须配置

    `output_type`: [int] 0: 以 `BaseMatrixWrapper` 输出至 common attr （默认）,
                          1: 以 `Doublelist` 输出至 item attr,
                          2: 以 `std::vector<T>*` 输出至 common attr,
                          3: 以 double 输出至 item xtr attr,
                          4: Sim TopN server 的特殊格式

    `pxtr_value_attr`: [string] pxtr 结果保存到 context 中的字段名

    `skip_sigmoid`: [bool] 是否跳过 sigmoid 过程

    `post_process`: [dict] 常规的内积 + sigmoid 的自定义处理配置

    `post_process::downsampled_target_no`: [int] 需要进下采样转换的 xtr 的编号

    `post_process::downsampled_alpha`: [int] 下采样转参数

    示例
    ------
    ``` python
    .predict_dot_product_xtr_v_float(predict_target_num=14,
                                     common_embedding_dim=128,
                                     common_embedding_attr="user_embedding",
                                     load_from_item_context=False,
                                     item_embedding_attr="item_embedding",
                                     miss_embedding_mark="miss_embedding",
                                     output_type=0,
                                     pxtr_value_attr="pxtr_value",
                                    )
    ```
    """
    self._add_processor(TowerPredictFloatDotProductXtrAttrEnricher(kwargs))
    return self

  def predict_dot_product_xtr_v_int16(self, **kwargs):
    """
    TowerPredictInt16DotProductXtrAttrEnricher
    ------
    计算向量内积、再算 sigmoid

    参数
    ------
    同 predict_dot_product_xtr_v_float / TowerPredictFloatDotProductXtrAttrEnricher

    示例
    ------
    ``` python
    .predict_dot_product_xtr_v_int16(predict_target_num=14,
                                     common_embedding_dim=128,
                                     common_embedding_attr="user_embedding",
                                     load_from_item_context=False,
                                     item_embedding_attr="item_embedding",
                                     miss_embedding_mark="miss_embedding",
                                     output_type=0,
                                     pxtr_value_attr="pxtr_value",
                                    )
    ```
    """
    self._add_processor(TowerPredictInt16DotProductXtrAttrEnricher(kwargs))
    return self

  def float_embedding_dot_product_pxtr(self, **kwargs):
    """
    FloatEmbeddingXtrAttrEnricher
    ------
    计算float向量内积, emm …… 有点绕，暂时不想写，大家也别用

    参数
    ------
    同 predict_dot_product_xtr_v_float，额外需要参数
    `req_common_embedding`: [string] 上游传过来的若干 item embedding 组合成的存于 common attr 里的 embedding

    示例
    ------
    ``` python
    .float_embedding_dot_product_pxtr(predict_target_num=13,
                               common_embedding_dim=128,
                               common_embedding_attr="common_embedding",
                               load_from_item_context=False,
                               item_embedding_attr="item_embedding",
                               miss_embedding_mark="miss_embedding",
                               output_type=2,
                               pxtr_value_attr="pxtr_value",
                               req_common_embedding="what_ever_balabala",
                               )
    ```
    """
    self._add_processor(FloatEmbeddingXtrAttrEnricher(kwargs))
    return self

  def sim_embedding_dot_product(self, **kwargs):
    """
    SimEmbeddingDotProductAttrEnricher
    ------
    计算 int 向量内积, emm …… 有点绕，暂时不想写，大家也别用

    参数
    ------
    同 predict_dot_product_xtr_v_int16 ，额外需要参数

    `req_common_embedding`: [string] 上游传过来的若干 item embedding 组合成的存于 common attr 里的 embedding

    `use_cosine`: [bool] 是否使用 cosine 代替 dot_product，默认为 False

    示例
    ------
    ``` python
    .sim_embedding_dot_product(predict_target_num=1,
                               common_embedding_dim=128,
                               common_embedding_attr="common_embedding",
                               load_from_item_context=False,
                               item_embedding_attr="item_embedding",
                               miss_embedding_mark="miss_embedding",
                               output_type=2,
                               pxtr_value_attr="pxtr_value",
                               req_common_embedding="what_ever_balabala",
                               )
    ```
    """
    self._add_processor(SimEmbeddingDotProductAttrEnricher(kwargs))
    return self

  def sim_embedding_topn_dot_product(self, **kwargs):
    """
    SimEmbeddingTopNDotProductAttrEnricher
    ------
    计算 int 向量内积, 和 sim_embedding_dot_product 类似，但只返回 topn 的 index.
    目前只支持单目标，且 output_type 必须设置为 4.

    参数
    ------
    同 sim_embedding_dot_product，额外需要参数

    `sorted_item_ids_attr`: [string] 返回结果中保存排序后 topn item index 的 attr

    示例
    ------
    ``` python
    .sim_embedding_topn_dot_product(predict_target_num=1,
                               common_embedding_dim=128,
                               common_embedding_attr="common_embedding",
                               load_from_item_context=False,
                               item_embedding_attr="item_embedding",
                               miss_embedding_mark="miss_embedding",
                               output_type=4,
                               pxtr_value_attr="pxtr_value",
                               req_common_embedding="what_ever_balabala",
                               sorted_item_ids_attr="sorted_item_ids_attr",
                               )
    ```
    """
    self._add_processor(SimEmbeddingTopNDotProductAttrEnricher(kwargs))
    return self

  def predict_mvke_pxtr(self, **kwargs):
    """
    PredictMVKEPxtrEnricher
    ------
    按照MVKE的方式计算向量内积，user_emb 单目标长度 dim * vk_num， photo_emb 单目标长度 dim + vk_num
    参考： https://arxiv.org/pdf/2106.07356

    参数
    ------
    `pemb_dim`: [int] photo_emb 维度
    `vk_num`: [int] vk的个数
    其他参数同 predict_dot_product_xtr_v_float
    
    示例
    ------
    ``` python
    .predict_mvke_pxtr(predict_target_num=14,
                                 common_embedding_dim=128,
                                 pemb_dim=64,
                                 vk_num=8,
                                 common_embedding_attr="user_embedding",
                                 load_from_item_context=False,
                                 item_embedding_attr="item_embedding",
                                 miss_embedding_mark="miss_embedding",
                                 output_type=0,
                                 pxtr_value_attr="pxtr_value",
                                )
    ```
    """
    self._add_processor(PredictMVKEPxtrEnricher(kwargs))
    return self

  def predict_dot_product_xtr_v_hetu_tag_float(self, **kwargs):
    """
    TowerPredictHetuTagFloatDotProductXtrAttrEnricher
    ------
    计算向量内积、再算 sigmoid , 不同 河图 tag 的photo用的user embedding 是不同的

    参数
    ------
    同 predict_dot_product_xtr_v_float / TowerPredictFloatDotProductXtrAttrEnricher

    `tower_caller_attr`: [string] 从 CommonAttr 中获取调用者身份的存放字段，默认值tower_caller

    `item_hetu_tag_attr`: [string] item hetu tag 在 context item attr 中的字段名 ， 可选

    `reg_hetu_tags`: [list[int]] 期望进行计算的 hetu tag 列表

    示例
    ------
    ``` python
    .predict_dot_product_xtr_v_hetu_tag_float(predict_target_num=14,
                                     common_embedding_dim=128,
                                     common_embedding_attr="user_embedding",
                                     load_from_item_context=False,
                                     item_embedding_attr="item_embedding",
                                     miss_embedding_mark="miss_embedding",
                                     output_type=0,
                                     pxtr_value_attr="pxtr_value",
                                     item_hetu_tag_attr="hetu_tags",
                                     reg_hetu_tags=[1,2,3,4,5],
                                    )
    ```
    """
    self._add_processor(TowerPredictHetuTagFloatDotProductXtrAttrEnricher(kwargs))
    return self

  def predict_cluster_xtr_v_float(self, **kwargs):
    """
    TowerPredictClusterXtrFloatAttrEnricher
    ------
    计算向量内积、再算 sigmoid , 支持 item cluster 和 user cluster 进行匹配操作

    参数
    ------
    同 predict_dot_product_xtr_v_float / TowerPredictFloatDotProductXtrAttrEnricher

    `req_common_embedding`: [string] 上游传过来的若干 cluster user embedding 组合成的存于 common attr 里的 embedding , 用于正确性校验

    `need_map_cluster`: [bool] 是否需要进行 item cluster 和 user cluster 匹配操作

    `common_cluster_attr`: [string] need_map_cluster = True 时，必须设置， 指向 user cluster id list

    `item_cluster_attr`: [string] need_map_cluster = True 时，指向 item cluster id 字段，否则 item cluster 在 req common_emebedding 中的 offset 字段

    示例
    ------
    ``` python
    .predict_cluster_xtr_v_float(predict_target_num=14,
                                 common_embedding_dim=128,
                                 common_embedding_attr="user_embedding",
                                 load_from_item_context=False,
                                 item_embedding_attr="item_embedding",
                                 miss_embedding_mark="miss_embedding",
                                 output_type=0,
                                 pxtr_value_attr="pxtr_value",
                                 need_map_cluster=True,
                                 common_cluster_attr="user_cluster",
                                 item_cluster_attr="item_cluster",
                                 req_common_embedding="req_common_embedding",
                                 )
    ```
    """
    self._add_processor(TowerPredictClusterXtrFloatAttrEnricher(kwargs))
    return self


  def fetch_local_embedding(self, embedding_type, **kwargs):
    """
    整合 TowerFetchLocalInt16EmbeddingAttrEnricher / TowerFetchLocalFloatEmbeddingAttrEnricher

    增加参数
    ------
    embedding_type : [string] embedding 的元素数据类型 int16_ptr / float_ptr

    """
    if embedding_type == "int16_ptr":
      return self.fetch_local_int16_embedding(**kwargs)
    elif embedding_type == "float_ptr":
      return self.fetch_local_float_embedding(**kwargs)
    elif embedding_type == "scale_int8_ptr":
      return self.fetch_local_scale_int8_embedding(**kwargs)
    else:
      raise Exception(f"unexpected embedding type: {embedding_type}")

  def predict_dot_product_xtr(self, embedding_type, **kwargs):
    """
    整合 TowerPredictInt16DotProductXtrAttrEnricher / TowerPredictFloatDotProductXtrAttrEnricher

    增加参数
    ------
    embedding_type : [string] embedding 的元素数据类型 int16_ptr / float_ptr

    """
    if embedding_type == "int16_ptr":
      return self.predict_dot_product_xtr_v_int16(**kwargs)
    elif embedding_type == "float_ptr":
      return self.predict_dot_product_xtr_v_float(**kwargs)
    else:
      raise Exception(f"unexpected embedding type: {embedding_type}")

  def predict_xtr_by_fpga(self, **kwargs):
    """
    TowerPredictXtrFpgaAttrEnricher
    ------
    用 FPGA 板卡计算向量相关性得分

    参数
    ------
    `predict_target_num`: [int] model predict target number

    `common_embedding_attr`: [string] common embedding 在 context 中字段名 (common attr)

    `output_type`: [int] 0: 以 BaseMatrixWrapper 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr

    `pxtr_value_attr`: [string] pxtr 结果保存到 context 中的字段名

    示例
    ------
    ``` python
    .predict_xtr_by_fpga(predict_target_num=14,
                         common_embedding_attr="user_embedding",
                         output_type=0,
                         pxtr_value_attr="pxtr_value",
                         )
    ```
    """
    self._add_processor(TowerPredictXtrFpgaAttrEnricher(kwargs))
    return self

  def fetch_fpga_index(self, **kwargs):
    """
    TowerFetchFpgaIndexAttrEnricher
    ------
    查询photo embedding 在 FPGA 板卡中的偏移位置

    参数
    ------
    `fpga_index_attr`: [string] photo embedding fpga 偏移存在 context ItemAttr中的字段名

    示例
    ------
    ``` python
    .fetch_fpga_index(fpga_index_attr="fpga_index")
    ```
    """
    self._add_processor(TowerFetchFpgaIndexAttrEnricher(kwargs))
    return self

  def trans_vec2matrix(self, **kwargs):
    """
    TowerTransFloatVector2MatrixAttrEnricher
    ------
    将 `std::vector<float>` common attr 转换成 `interop::fbs::BaseMatrixWrapper<float>`

    参数
    ------
    `vector_attr`: [string] 待转换的 common attr

    `matrix_attr`: [string] 转换后的 common attr

    `move_data`: [bool] 是否用 std::move 把数据从 vector move 到 matrix ，默认为 true

    示例
    ------
    ``` python
    .trans_vec2matrix(vector_attr="pxtr_value",
                      matrix_attr="return_pxtr_value",
                      move_data=true,)
    ```
    """
    self._add_processor(TowerTransFloatVector2MatrixAttrEnricher(kwargs))
    return self

  def fetch_sim_hetu_tag_user_embedding(self, **kwargs):
    """
    TowerFetchSimUserEmbeddingAttrEnricher
    ------
    sim hetu tag tower 需要同时计算 39 个 hetu tag 对应的 user embedding，
    需将 sim hetu user embedding 的计算任务发送多个 infer 实例并行计算，然后聚合结果继续下一步的 pxtr 计算

    参数配置
    ------
    `inputs` : [list[json]] 计算 user embedding 所需要的 user feature embedding (DoubleList CommonAttr),
               每个 json 配置项包含以下变量:
                attr_name : user feature embedding 特征名
                common : [bool] 是否为 user 侧特征
                dim : 特征维度
    `item_slots` : [string] item feature slot list 在 ItemAttr 中存储的字段, 非必须，格式与 mio_embedding_attr_enricher 的 slots_inputs 输入保持一致

    `item_signs` : [string] item feature slot list 在 ItemAttr 中存储的字段，非必须，格式与 mio_embedding_attr_enricher 的 parameters_inputs 输入保持一致

    `sub_req_num` : [int] 并行计算的子请求的个数

    `req_idx_attr` : [string] 发送多个请求时，每个子请求有一个 request id ， 该 id 填充在该配置指定的 request common attr 里

    `resp_uemb_attr` : [string] 子请求返回的 user embedding 在 response 中的 common attr

    `uemb_kess_service` : [string] 子请求发往的 kess service name

    `uemb_kess_cluster` : [string] 子请求发往的 kess cluster , 默认 PRODUCTION

    `uemb_request_type` : [string] 子请求发往的服务的 request type (具体参考 common leaf 的 request type 用法)

    `uemb_timeout_ms` : [int] 子请求等待超时阈值

    `output_user_embedding_attr` : [string] 每个 sim hetu tag user embedding 拼接聚合后 embedding 在 common attr 中的字段名

    `uemb_len` : [int] 每个 user embedding 的向量长度

    调用示例
    ------
    ``` python
    .fetch_sim_hetu_tag_user_embedding(
      inputs=[
       {attr_name:"xxx",common=true, dim=128},
       {attr_name:"aaa",common=false, dim=1024},
        ]),
      sub_req_num=8,
      req_idx_attr="request_idx",
      resp_uemb_attr="user_top_layer",  # 一定要与被调的配置保持一致
      uemb_kess_service="grpc_xxxTower",
      uemb_request_type="predict_user_embedding_by_yyy",
      uemb_timeout_ms=100,
      output_user_embedding_attr="user_top_layer",
      uemb_len=2040,
      )
    ```
    """
    self._add_processor(TowerFetchSimUserEmbeddingAttrEnricher(kwargs))
    return self

  def extract_sim_feature_with_photo_info(self, **kwargs):
    """
    TowerPhotoInfoItemAttrEnricher
    ------
    从 PhotoInfo 中抽取 sim tower 所需要的特征值

    参数配置
    ------
    `photo_info_attr` : [string] item attr 字段名，用于获取 PhotoInfo 对象指针

    调用示例
    ------
    ``` python
    .extract_sim_feature_with_photo_info(
      photo_info_attr="photo_info",
    )
    ```
    """
    self._add_processor(TowerPhotoInfoItemAttrEnricher(kwargs))
    return self

  def local_cache_op(self, **kwargs):
    """
    TowerEmbeddingLocalCacheEnricher
    ------
    本地 cache 操作，从 common/item attr 中获取 kv 存入 cache 、 或从 cache 中读取 kv 存入 common/item attr

    参数配置
    ------
    `cache` : [list[json]] cache 存取配置，每个配置包含:
        `save` : 指定是 save 还是 load cache 操作
        `common` : 指定是在 common context 还是 item context 下进行操作
        `key_attr` : 存取所需 key 的 attr name
        `value_attr` : save=true时指 context 要存入 cache 的字段，否则只读取 cache 要写入 context的字段
        `data_type` : 0 表示 value 类型为 doule list，1 表示 value 类型为 int64 list, 其他不支持

    `cache_value_len` : 当存取的 value 值是定长时可以设置该参数，可以优化 cache 内存分配

    `memkv_shm_path` : cache 的共享内存路径，默认 /dev/shm/embed_cache
    `memkv_part_num` : cache 内部空间分 part ，默认 64
    `memkv_expire_sec` : cache kv 过期时间，默认 300 秒
    `memkv_capacity` : cache 最大 kv 数量，默认 300000 (> expire * qps)
    `memkv_mem_limit` : cache 最大内存占用，默认 214748364800 （ 200 G > cap * 500KB）

    调用示例
    ------
    ``` python
    .local_cache_op(
      cache=[
          {"save":true, "common":true, "data_type":0, "key_attr":"llsid", "value_attr":"user_top_layer"},
          {"save":false, "common":true, "data_type":0, "key_attr":"llsid", "value_attr":"user_top_layer"},
          {"save":false, "common":false, "data_type":0, "key_attr":"item_key", "value_attr":"photo_top_layer"},
      ],
    )
    ```
    """
    self._add_processor(TowerEmbeddingLocalCacheEnricher(kwargs))
    return self

  def retrieve_by_cache_and_enrich_pxtr(self, **kwargs):
    """
    TowerPxtrCacheRetriever
    ------
    save 模式：将item key 及其 pxtr 打包写入 common attr (后续由 local_cache_op 存入本地cache)；
    load 模式：从 common attr 获取打包的数据，反解出 item key 及其 pxtr，并进行召回

    参数说明
    ------
    `run_type`: [int] =0, save 模式；
                      =1, load 模式;
                      其他啥也不干

    `reason`: [int] load 模式下召回结果的 reason，默认 0

    `cache_value_attr`: [string] 打包内容在 common attr 中字段名

    `pxtrs` : [list[string]] item xtr 列表

    调用示例
    ------
    ``` python
    .retrieve_by_cache_and_enrich_pxtr(
      run_type=1,
      reason=0,
      cache_value_attr="item_pxtr_cache",
      pxtrs=["ctr","ltr","lvtr"],
    )
    ```
    """
    self._add_processor(TowerPxtrCacheRetriever(kwargs))
    return self

  def fetch_tower_topn_dot_product_multi_head_bias(self, **kwargs):
    """
    TowerFetchTopNDotProductMultiHeadBiasEnricher
    ------
    for sim3.0 适用于多个 head 与 bias 的形式
    同 fetch_tower_dot_product_pxtr, 但返回 user * item topn 的 pxtr 及对应的 index 值和对应的 attention
    需要输入 head number
    目前只支持单目标，且 output_type 必须设置为 4.

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `head_slot`: [int] head number，与 pid 拼接在一起作为 dpcalc 的 key，从1开始

    `colossus_time_attr`: [string] 默认为 colossus_time

    `colossus_play_attr`: [string] 默认为 colossus_play

    `colossus_duration_attr`: [string] 默认为 colossus_duration

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr
                          4: 输出 TopN 的 pxtr 及对应的 index

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    `sorted_item_idx_attr`: [string] 输出排序后 topn item index 的 attr 名

    `sorted_item_attention_attr`: [string] 输出排序后 topn item attention 值的 attr 名

    示例
    ------
    ``` python
    .fetch_tower_topn_dot_product_pxtr(
      user_embedding_attr = "all_sim_query_head1",
      item_list_from_attr="colossus_pid",
      head_slot=1,
      colossus_time_attr="colossus_time",
      colossus_play_attr="colossus_play",
      colossus_duration_attr="colossus_duration",
      predict_labels=["dp"],
      kess_service=dp_kess_name,
      shards=32,
      timeout_ms=500,
      sub_req_num_in_shard=1,
      server_request_type="calc_topn_dot_product_bias",
      req_common_embedding_attr="req_item_emb",
      return_pxtr_value_attr="distance",
      sorted_item_idx_attr="sorted_item_index_head1",
      sorted_item_attention_attr="sorted_item_attention_head1", #增加一个透传topn attention的attr
      pxtr_type=1,
      emb_dim=64,
      output_type=4,
      return_sorted_item_ids_attr="sorted_item_ids_vec",
      top_n=50)
    ```
    """
    self._add_processor(TowerFetchTopNDotProductMultiHeadBiasEnricher(kwargs))
    return self

  def sim_embedding_topn_dot_product_multi_head_bias(self, **kwargs):
    """
    SimEmbeddingTopNDotProductMultiHeadBiasAttrEnricher
    ------
    计算 int 向量内积, 和 sim_embedding_dot_product 类似，但只返回 topn 的 index.
    ，只支持predict_target_num = 1, 且 output_type 必须设置为 4.

    参数
    ------
    同 sim_embedding_dot_product，额外需要参数

    `sorted_item_ids_attr`: [string] 返回结果中保存排序后 topn item index 的 attr
    `bias_attr`: [string] 增加bias item attr

    示例
    ------
    ``` python
    .sim_embedding_topn_dot_product_multi_head_bias(embedding_type=item_embedding_type,
                                predict_target_num=target_num,
                                req_common_embedding=req_common_embedding,
                                common_embedding_dim=dim,
                                common_embedding_attr='common_embedding_value',
                                load_from_item_context=False,
                                item_embedding_attr="item_embedding_value",
                                miss_embedding_mark="miss_embedding",
                                time_embedding_attr="time_bias_embedding_value",
                                time_embedding_miss_mark="time_bias_miss_embedding",
                                play_embedding_attr="play_bias_embedding_value",
                                play_embedding_miss_mark="play_bias_miss_embedding",
                                head_slot_attr="head_slot_attr",
                                save_to_item_context=False,
                                pxtr_value_attr='distance',
                                use_cosine=use_cosine,
                                output_type=4,
                                top_n=50,
                                sorted_item_ids_attr="sorted_item_ids_vec")
    ```
    """
    self._add_processor(SimEmbeddingTopNDotProductMultiHeadBiasAttrEnricher(kwargs))
    return self

  def scale_int8_topn_dot_product(self, **kwargs):
    """
    ScaleInt8TopNDotProductAttrEnricher
    ------
    计算 int 向量内积, 和 sim_embedding_dot_product 类似，但只返回 topn 的 index.
    ，只支持predict_target_num = 1, 且 output_type 必须设置为 4.
    ItemEmbedding 按照 ScaleInt8 的格式存储。

    参数
    ------
    同 sim_embedding_dot_product，额外需要参数

    `sorted_item_ids_attr`: [string] 返回结果中保存排序后 topn item index 的 attr
    `bias_attr`: [string] 增加bias item attr

    示例
    ------
    ``` python
    .scale_int8_topn_dot_product(embedding_type=item_embedding_type,
                                predict_target_num=target_num,
                                req_common_embedding=req_common_embedding,
                                common_embedding_dim=dim,
                                common_embedding_attr='common_embedding_value',
                                load_from_item_context=False,
                                item_embedding_attr="item_embedding_value",
                                miss_embedding_mark="miss_embedding",
                                time_embedding_attr="time_bias_embedding_value",
                                time_embedding_miss_mark="time_bias_miss_embedding",
                                play_embedding_attr="play_bias_embedding_value",
                                play_embedding_miss_mark="play_bias_miss_embedding",
                                head_slot_attr="head_slot_attr",
                                save_to_item_context=False,
                                pxtr_value_attr='distance',
                                use_cosine=use_cosine,
                                output_type=4,
                                top_n=50,
                                sorted_item_ids_attr="sorted_item_ids_vec")
    ```
    """
    self._add_processor(ScaleInt8TopNDotProductAttrEnricher(kwargs))
    return self

  def dump_attrs_package(self, **kwargs):
    """
    TowerDumpAttrEnricher
    ------
    将 context attr 打包成 CommonRecoRequest 并序列化成 string, 会设置 request_id

    参数配置
    ------
    `dump_attrs`: [list] 需要 pack 的 attr 列表，是一个 dict list，每个 dict 包含如下配置:
              "input_attr" : xxx, 需要 pack 的 context attr
              "as_attr" : yyy, dump 后的 attr field name，不配置则同 input_attr
              "is_common" : 是否为 common attr

    `output_attr`: [string] 打包后序列化所得的数据 , common attr

    调用示例
    ------
    ``` python
    .dump_attrs_package(
      "output_attr" = "packed_data",
      "dump_attrs" = [
        dict(
          input_attr = "one_photo_input",
          is_common = True,
        ),
        dict(
          input_attr = "one_photo_input2",
          is_common = False,
        ),
      ]
    )
    ```
    """
    self._add_processor(TowerDumpAttrEnricher(kwargs))
    return self

  def trans_local_embedding(self, **kwargs):
    """
    TowerTransLocalEmbeddingAttrEnricher
    ------
    将存储优化的 local embedding (一般为指针形式) 转化成 common leaf context 内置支持的 int list/ double list 格式

    参数配置
    ------
    `trans_attrs`: [list] 需要转换的 attr 列表，是一个 dict list，每个 dict 包含如下配置:
              "input_attr" : [string] xxx, 需要转换的 context attr,
              "as_attr" : [string] yyy, 转换后的 attr field name,
              "emb_type" : [string] float/double/int16/int/int64
              "dim" : [int] embedding 维度,
              "trans_type" : [string] "c2c", common attr -> common attr,
                             "c2i", common attr -> item attr,
                             "i2i", item attr -> item attr,
                             "m2i", BaseMatrixWrapper -> item attr,

    调用示例
    ------
    ``` python
    .trans_local_embedding(
      trans_attrs = [
        dict(
          input_attr = "one_photo_input",
          as_attr = "one_photo_input_t",
          trans_type = "c2c"
        ),
        dict(
          input_attr = "one_photo_input2",
          as_attr = "one_photo_input2_t",
          trans_type = "c2i",
        ),
      ]
    )
    ```
    """
    self._add_processor(TowerTransLocalEmbeddingAttrEnricher(kwargs))
    return self


  def simple_filter_by_redis_ids(self, **kwargs):
    """
    SimpleRedisIdFilterArranger
    ------
    从 redis 获取 id list，然后判定当前 CommonRecoResult 指定的 filter key 是否在 id list 里，然后根据配置决定是否删除 result

    参数配置
    ------
    `filter_key_attr` : [string] 用于进行过滤判定的 filter key 的字段名

    `filter_type`: [int] =0: 白名单模式，filter key 不在 redis id list 里的 item 将删除;
                         =1: 黑名单模式，filter key 在 redis id list 的将删除;
                         other, 啥也不做

    `redis_keys` : [list[string]] 指定请求 redis 的 key list

    `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

    `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

    `redis_cluster` : [int] 指定 redis 集群名

    `redis_timeout_ms` : [int] 访问 redis 的超时阈值 ，默认 200 ms

    `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

    `redis_biz_name` : [int] 访问 redis 集群所属业务，可以不填写, 默认为空

    `redis_replica` : [int] redis 集群是否开启双集群同步，一般无需修改，默认为 true

    `redis_cycle_sec` : [int] 请求 redis id list 的间隔时间, 单位秒

    调用示例
    ------
    ``` python
    .simple_filter_by_redis_ids(
      filter_key_attr="item_id",
      filter_type=0,
      redis_keys=["XXX_IDS"],
      redis_key_num=50,
      redis_value_get_type=0,
      redis_value_parse_type=0,
      redis_cluster="recoNewUserOffline",
      redis_timeout_ms=1000,
      redis_cycle_sec=60,
      )
    ```
    """
    self._add_processor(SimpleRedisIdFilterArranger(kwargs))
    return self

  def simple_mark_by_redis_ids(self, **kwargs):
    """
    SimpleRedisIdFilterEnricher
    ------
    从 redis 获取 id list，然后判定当前 CommonRecoResult 指定的 filter key 是否在 id list 里，是在标记 1 , 否则标记 0

    参数配置
    ------
    `filter_key_attr` : [string] 用于进行过滤判定的 filter key 的字段名

    `filter_mark_attr` : [string] 用于存储判定成功与否的字段，成功标记 1， 否则标记 0

    `redis_keys` : [list[string]] 指定请求 redis 的 key list

    `redis_value_get_type` : [int] 指定请求 redis 的方式, 目前只支持 = 0 : Get(string key, string &value)

    `redis_value_parse_type` : [int] 指定解析 value 的方式，目前只能支持 = 0 : 解析 num1,num2,...,numN 的数字字符序列序列

    `redis_cluster` : [int] 指定 redis 集群名

    `redis_timeout_ms` : [int] 访问 redis 的超时阈值 ，默认 200 ms

    `redis_io_threads` : [int] 访问 redis 单个 client 的 io 线程数，一般无需修改，默认为 2

    `redis_biz_name` : [int] 访问 redis 集群所属业务，可以不填写, 默认为空

    `redis_replica` : [int] redis 集群是否开启双集群同步，一般无需修改，默认为 true

    `redis_cycle_sec` : [int] 请求 redis id list 的间隔时间, 单位秒

    调用示例
    ------
    ``` python
    .simple_mark_by_redis_ids(
      filter_key_attr="item_id",
      filter_mark_attr="item_filter_mark",
      redis_keys=["XXX_IDS"],
      redis_key_num=50,
      redis_value_get_type=0,
      redis_value_parse_type=0,
      redis_cluster="recoNewUserOffline",
      redis_timeout_ms=1000,
      redis_cycle_sec=60,
      )
    ```
    """
    self._add_processor(SimpleRedisIdFilterEnricher(kwargs))
    return self

  def emb_matmul_reduce_sum_int16(self, **kwargs):
    """
    EmbMatmulReduceSumInt16AttrEnricher
    ------
    计算 M 个 common embedding 和 N 个 item embedding 形成的矩阵乘法，然后将 M * N 的 float 矩阵 reduce_sum 成 N 维向量,
    reduce_sum = sigma{0,recent_num}(recent_wei * element_recent) + sigma{recent_num,far_num}(far_wei * element_far) + sigma{other}(default_wei * element_other)

    参数
    ------
    同 predict_dot_product_xtr_v_int16 ，额外需要参数

    `req_common_embedding`: [string] 上游传过来的若干 item embedding 组合成的存于 common attr 里的 embedding

    `recent_num`: [int 动态参数] reduce sum 参数

    `recent_wei`: [double 动态参数] reduce sum 参数

    `far_num`: [int 动态参数] reduce sum 参数

    `far_wei`: [double 动态参数] reduce sum 参数

    `default_wei`: [double 动态参数] reduce sum 参数

    示例
    ------
    ``` python
    .emb_matmul_reduce_sum_int16(
      predict_target_num=1,
      load_from_item_context=False,
      common_embedding_dim=kwargs["embedding_dim"],
      common_embedding_attr="M_embedding_value",
      item_embedding_attr="N_embedding_value",
      miss_embedding_mark="N_miss",
      output_type=3,
      req_common_embedding="M_embedding_value.placeholder",
      predict_labels=kwargs["tower_labels"],
      recent_num="{{recent_num}}",
      recent_wei="{{recent_wei}}",
      far_num="{{far_num}}",
      far_wei="{{far_wei}}",
      default_wei="{{default_wei}}",
      )
    ```
    """
    self._add_processor(EmbMatmulReduceSumInt16AttrEnricher(kwargs))
    return self

  def emb_matmul_reduce_sum_float(self, **kwargs):
    """
    EmbMatmulReduceSumFloatAttrEnricher
    ------
    计算 M 个 common embedding 和 N 个 item embedding 形成的矩阵乘法，然后将 M * N 的 float 矩阵 reduce_sum 成 N 维向量

    参数
    ------
    同上 emb_matmul_reduce_sum_int16

    示例
    ------
    ``` python
    .emb_matmul_reduce_sum_float(
      predict_target_num=1,
      load_from_item_context=False,
      common_embedding_dim=kwargs["embedding_dim"],
      common_embedding_attr="M_embedding_value",
      item_embedding_attr="N_embedding_value",
      miss_embedding_mark="N_miss",
      output_type=3,
      req_common_embedding="M_embedding_value.placeholder",
      predict_labels=kwargs["tower_labels"],
      recent_num="{{recent_num}}",
      recent_wei="{{recent_wei}}",
      far_num="{{far_num}}",
      far_wei="{{far_wei}}",
      default_wei="{{default_wei}}",
      )
    ```
    """
    self._add_processor(EmbMatmulReduceSumFloatAttrEnricher(kwargs))
    return self

  def norm_btq_emb(self, **kwargs):
    """
    NormBTQEmbEnricher
    ------
    对model sparse embedding进行normalize

    参数
    ------
    from_extra_var
    save_result_to_common_attr

    示例
    ------
    ``` python
    .norm_btq_emb(
      from_extra_var="input_attr",
      save_result_to_common_attr="output_attr",
      )
    ```
    """
    self._add_processor(NormBTQEmbEnricher(kwargs))
    return self

