#!/usr/bin/env python3
# coding=utf-8
"""
filename: embed_calc_arranger.py
description: common_leaf dynamic_json_config DSL intelligent builder, arranger module
author: <EMAIL>
date: 2022-10-18 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg, is_number, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import <PERSON><PERSON><PERSON><PERSON>, LeafRetriever

class SimpleRedisIdFilter<PERSON>rranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "simple_filter_by_redis_ids"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    for key in ["filter_key_attr"]:
      if key in self._config: ret.add(self._config[key])
    return ret
