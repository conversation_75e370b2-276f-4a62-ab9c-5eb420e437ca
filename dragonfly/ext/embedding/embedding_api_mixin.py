#!/usr/bin/env python3
# coding=utf-8
"""
filename: embedding_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, embedding api mixin
author: <EMAIL>
date: 2023-07-26 11:45:14
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .embedding_enricher import *

class EmbeddingApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 embedding server 相关的 Processor 接口

  仍有部分其他 processor 包含访问 embedding server 的功能，详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH)

  维护人/团队: jiangyumeng/架构中心

  """

  def iter_embedding_key(self, **kwargs):
    """
    IterEmbeddingKeyEnricher
    ------
    请求远程 colossusdb embedding 服务，遍历并获取其中所有的 key。
    当一轮遍历完成时，无缝继续下一轮遍历。两轮遍历的分界点可能位于一批返回结果内部。

    使用此 processor 需要注意，一个固定的 table_name 对应一个固定的 iterator, 每个 iterator 都是有状态的。
    如果在 pipeline 中多次调用此 processor 且每次的 table_name 相同，则每次都按照第一个 processor 的参数生效（如 timeout_ms 等）；
    当然，前一次调用的状态 (遍历的 offset) 也会保留到下次调用。

    参数配置
    ------
    `colossusdb_embd_model_name`: [string] 下游 colossusdb embedding server 的模型名，一般是: {模型名称}.
      获取方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH).
      之前又名为 colossusdb_embd_service_name, 配置 model_name 和 service_name 两者等价, 任选其一即可。

    `colossusdb_embd_table_name`: [string] 下游 colossusdb embedding server 的表名，如 test-table

    `colossusdb_use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_model_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    `save_result_to`: [string] 结果写入属性名。总是写入 common_attr。

    `ret_size`: [int] 每次执行该 processor 返回多少个结果，默认 1024

    `part_num`: [int] 底层每次请求时将整个存储分为多少个部分来进行获取，默认 16384 (意味着如果整个 Embedding 服务有 1.6384 亿个 key, 则每次请求获取 1 万个并缓存至本地；
      每次调用此 processor 会从缓存中拿出 1024 个作为结果，不足则再发送一次请求)。
      一般要求这个值大于每个 partition 的 key 数量的 1/1024, 如果它太小会导致请求失败。

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0 即不限制

    `timeout_ms`: [int] RPC 超时，单位毫秒，默认 5000


    调用示例
    ------
    ``` python
    .iter_embedding_key(
      colossusdb_embd_model_name="reco-arch-test-model",
      colossusdb_embd_table_name="test-table",
      save_result_to="common_attr_name",
    )
    ```
    """
    self._add_processor(IterEmbeddingKeyEnricher(kwargs))
    return self

  def fetch_remote_embedding(self, **kwargs):
    """
    FetchRemoteEmbeddingEnricher
    ------
    请求远程 embedding 服务获取 embedding。
    基于 get_remote_embedding_lite 等进行修改，收敛所有获取 embedding 的 processor。

    参数配置
    ------
    `protocol`: [int] 访问 embedding 服务所使用的协议，默认为 0，协议的支持情况见 协议支持 部分。

    `id_converter`: [dict] sign -> id 生成器的相关配置 [必填]
      - `type_name`: [string] 根据 sign_generator 格式可选值："mioEmbeddingIdConverter" 或 "kuibaEmbeddingIdConverter" 或 "plainIdConverter"

    `slot`: [int] 填写需要与 item_key 拼接的 slot 值, 默认值为 0

    `input_attr_name`: [string] 输入的 Attr name, 当 query_source_type 为 item_attr 或 common_attr 时生效。

    `output_attr_name`: [string] 输出的 Attr name, 如果 input_attr 是 item_ 开头则输出是 item attr, 否则输出是 common attr

    `query_source_type`: [string] 请求 key 来源，支持 item_key, item_id, user_id, device_id, item_attr 和 common_attr。默认为 item_key。

    `max_signs_per_request`: [int] 每个 RPC 请求包含的最大 sign 个数，默认为 0，即不限制

    `timeout_ms`: [int][动态参数] 请求 embedding server 服务的超时时间，默认值为 10

    `is_raw_data`: [bool] 启用 raw data 数据类型，即非 Embedding 的数据。默认为 false, 即固定为 mio_int16 格式

    `raw_data_type`: [string] is_raw_data 为 True 时用于指定 raw data 的数据类型，支持 int8, int16, int32, int64, uint8, uint16, uint32, uint64, float32, string, 默认值为 uint16。

    `size`: [int] embedding 维度，如果指定，则当结果的 size 不符合时会被过滤掉。默认为 0，此时不根据返回结果的 size 进行过滤。
    如果 is_raw_data 为 True 且 raw_data_type 为 string 则不生效。

    `enable_smaller_size`: [bool] 开启时，结果的 size 不大于 size 即可不被过滤。默认为 false。
    - 开启的同时使用 embedding cache 可能存在导致 coredump 的 bug, 暂未完成排查

    以下为 protocol == 0 时的对应配置

    `kess_service`: [string][动态参数] embedding server 服务的 kess 服务名 [必填]

    `kess_cluster`: [string] embedding server 服务的 kess 服务组，默认值为 "PRODUCTION"

    `shard_num`: [int] embedding server 的 shard num, 默认值为 1

    `client_side_shard`: [bool] 是否根据 shard 发送请求，默认为 False, 当 embedding 按 shard 存储时建议打开。

    `hash_input`: [string][动态参数] 根据指定值计算 hash 决定请求发往哪个副本，某个确定的值总是会请求到固定的某一个副本。
      默认为空，此时仍然是随机选择副本发送请求。

    以下为 protocol == 1 时的对应配置，下游服务信息会从自定义的路由表中获取

    `colossusdb_embd_model_name`: [string][动态参数] 下游 colossusdb embedding server 的模型名，一般是: {模型名称}.
      获取方法详见文档[获取Embedding](https://docs.corp.kuaishou.com/k/home/<USER>/fcAB1k2CwkZRcxQ09lTBYMRkH).
      之前又名为 colossusdb_embd_service_name, 配置 model_name 和 service_name 两者等价, 任选其一即可。

    `colossusdb_embd_table_name`: [string][动态参数] 下游 colossusdb embedding server 的表名，如 test-table

    `colossusdb_use_kconf_client`: [bool] 是否用 kconf_client 包装在原生 client 外面，默认为 true.
      如果设为 false, 则一般情况下应将上述的 colossusdb_embd_model_name 修改为 grpc_clsdb_ps-{模型名称}, colossusdb_embd_table_name 不变。
      详见文档[统一存储流量治理功能说明](https://docs.corp.kuaishou.com/d/home/<USER>

    调用示例
    ------
    ``` python
    .fetch_remote_embedding(
      protocol=1,
      colossusdb_embd_model_name="reco-arch-test-model",
      colossusdb_embd_table_name="test-table",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      input_attr_name = "signs",
      output_attr_name = "embeddings",
      size = 64,
    )

    .fetch_remote_embedding(
      kess_service = "grpc_xxx",
      id_converter = {"type_name": "mioEmbeddingIdConverter"},
      slot = 38,
      input_attr_name = "signs",
      output_attr_name = "embeddings",
      hash_input = "{{string_attr_name}}",
      size = 64,
    )
    ```
    """
    self._add_processor(FetchRemoteEmbeddingEnricher(kwargs))
    return self
