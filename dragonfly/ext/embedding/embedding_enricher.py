#!/usr/bin/env python3
# coding=utf-8
"""
filename: embedding_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for embedding server
author: <EMAIL>
date: 2023-07-26 11:45:14
"""

from ...common_leaf_util import strict_types, check_arg, ArgumentError
from ...common_leaf_processor import LeafEnricher


class IterEmbeddingKeyEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "iter_embedding_key"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["save_result_to"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @strict_types
  def _check_config(self) -> None:
    essential_args = ["save_result_to", "colossusdb_embd_table_name"]
    if "colossusdb_embd_service_name" not in self._config and "colossusdb_embd_model_name" not in self._config:
      raise ArgumentError(f"both colossusdb_embd_service_name and colossusdb_embd_model_name not defined in: {self._config}")
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in: {self._config}")
    # if use kconf client, model_name usually should not start with "grpc_clsdb_ps_"
    if "colossusdb_use_kconf_client" not in self._config or self._config["colossusdb_use_kconf_client"]:
      if "colossusdb_embd_model_name" in self._config:
        prefix = self._config["colossusdb_embd_model_name"][:14]
        if prefix == "grpc_clsdb_ps_":
          raise ArgumentError(f"config colossusdb_embd_model_name is likely wrong, please confim.")
      else:
        prefix = self._config["colossusdb_embd_service_name"][:14]
        if prefix == "grpc_clsdb_ps_":
          raise ArgumentError(f"config colossusdb_embd_service_name is likely wrong, please confim.")


class FetchRemoteEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_remote_embedding"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["user_id", "device_id", "common_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_key", "item_id", "item_attr"]:
      return {self._config["output_attr_name"]}
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("hash_input")))
    attrs.update(self.extract_dynamic_params(self._config.get("colossusdb_embd_service_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("colossusdb_embd_model_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("colossusdb_embd_table_name")))
    if self._config.get("query_source_type", "item_key") in ["common_attr"]:
      attrs.add(self._config["input_attr_name"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._config.get("query_source_type", "item_key") in ["item_attr"]:
      return {self._config["input_attr_name"]}
    else:
      return set()

  @strict_types
  def _check_config(self) -> None:
    if "protocol" in self._config and self._config["protocol"] == 1:
      essential_args = ["colossusdb_embd_table_name"]
      if "colossusdb_embd_service_name" not in self._config and "colossusdb_embd_model_name" not in self._config:
        raise ArgumentError(f"both colossusdb_embd_service_name and colossusdb_embd_model_name not defined in: {self._config}")
      for item in essential_args:
        if item not in self._config:
          raise ArgumentError(f"{item} not defined in: {self._config}")
      # if use kconf client, model_name usually should not start with "grpc_clsdb_ps_"
      if "colossusdb_use_kconf_client" not in self._config or self._config["colossusdb_use_kconf_client"]:
        if "colossusdb_embd_model_name" in self._config:
          prefix = self._config["colossusdb_embd_model_name"][:14]
          if prefix == "grpc_clsdb_ps_":
            raise ArgumentError(f"config colossusdb_embd_model_name is likely wrong, please confim.")
        else:
          prefix = self._config["colossusdb_embd_service_name"][:14]
          if prefix == "grpc_clsdb_ps_":
            raise ArgumentError(f"config colossusdb_embd_service_name is likely wrong, please confim.")