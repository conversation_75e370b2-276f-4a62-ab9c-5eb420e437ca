#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .reco_feature_server_enricher import *


class RecoFeatureServerMixin(CommonLeafBaseMixin):
  def extract_kuiba_discrete_simplified(self, **kwargs):
    """
    KuibaParameterDiscreteSimplifiedEnricher
    ------
    抽取离散化特征放到 attr 里（如果是 common 则放到 common attr, 否则放到 item attr）, 针对 kuiba discrete 算子简化实现提高抽取效率

    参数配置
    ------
    `slots_output`: [string] 输出 slots 的位置

    `parameters_output`: [string] 输出 parameters 的位置

    `config`: [dict] discrete 抽取配置，详见示例。kuiba discrete 算子，当前仅处理 attrs[0].attr[0] 数据，兼容原配置格式，保留数组配置形式
      - `slotid_999`: 自定义名称，用于去重判断的标识

    `is_common_attr`: [bool] 是否仅从 common attr 中抽取，默认为 false

    调用示例
    ------
    ``` python
    .extract_kuiba_discrete_simplified(
      slots_output="slots",
      parameters_output="parameters",
      is_common_attr=False,
      config={
        "slotid_999": {
          "attrs": [
            {
              "attr": ["pctr"],
              "attr_type": ["float64"],
              "key_type": 999,
              "converter_args": "0.25,0,4,10000,0",
            }
          ]
        }
      }
    )
    ```
    """
    self._add_processor(KuibaParameterDiscreteSimplifiedEnricher(kwargs))
    return self
