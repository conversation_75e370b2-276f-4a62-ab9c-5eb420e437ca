#! /usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafMixer, try_add_table_name

class MerchantMergeTablesMixer(LeafMixer):
    @strict_types
    def __init__(self, config: dict):
        super().__init__(config)

    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "merchant_merge_tables"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for table in self._config.get("input_tables", []):
            table_name = table.get("table_name")
            table_attrs = table.get("attrs", [])
            attrs.update(try_add_table_name(table_name, table_attrs))
        return attrs
    
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        table_name = self._config.get("output_table", "")
        all_table_attrs = set()
        for table in self._config.get("input_tables", []):
            table_attrs = table.get("attrs", [])
            all_table_attrs.update(table_attrs)
        attrs.update(try_add_table_name(table_name, all_table_attrs))
        return attrs
    
    @property
    @strict_types
    def output_item_tables(self) -> set:
        attrs = set()
        table_name = self._config.get("output_table", "") 
        attrs.add(table_name)
        return attrs