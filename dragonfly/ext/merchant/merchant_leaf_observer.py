#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, observer module
author: fang<PERSON><EMAIL>
date: 2020-01-09 10:45:00
"""

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafObserver

class MerchantItemXtrWriteDistributeCacheObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self._item_score_attrs = None

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "write_xtr_score_attr_by_distributed_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    if self._item_score_attrs is not None:
        return self._item_score_attrs
    item_score_attrs = set(self._config.get("item_score_attrs", []))
    extra_attrs = set(self._config.get("extra_attrs", []))
    attrs = item_score_attrs | extra_attrs
    check_arg("cache_ttl" not in item_score_attrs, "item_score_attrs 中不能包含 cache_ttl")
    check_arg(not set(extra_attrs) & set(item_score_attrs), "extra_attrs 和 item_score_attrs 不能有交集")
    check_arg(len(item_score_attrs) > 0, "item_score_attrs 不能为空")
    self._config["item_score_attrs"] = self._config["item_score_attrs"].copy()
    self._config["item_score_attrs"] += self._config.get("extra_attrs", [])
    self._item_score_attrs = attrs
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("enable_async_queue_write"), bool) or isinstance(self._config.get("enable_async_rpc_write"), bool), "需指定 enable_async_queue_write 或 enable_async_rpc_write")
    check_arg(isinstance(self._config.get("biz_name"), str) or isinstance(self._config.get("reco_biz"), str), "需指定 biz_name 或 reco_biz")
    if self._config.get("biz_name"):
      if self._config.get("producer_type","") == "kafka":
        check_arg(self._config.get("kafka_topic"), "producer_type 为 kafka 时，需指定 kafka_topic")
      
class MerchantRecoDuplicateReasonCountLoggerObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "duplicate_reason_count_logger"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["namespace", "subtag", "reason_list_item_attr"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs


class MerchantItemAttrValuePerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_perflog_item_attr_value"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("max_item_limit"))
    ret.add(self._config.get("groupby_item_attr"))
    ret.update(self._config.get("input_white_list_common_attr"))
    ret.update(self.extract_dynamic_params(self._config.get("check_point")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("input_item_attrs", []))

class InferScoreToKafkaObserver(LeafObserver):

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "infer_score_to_kafka"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return True


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("model_family_name") + "_mm_origin_infer_label_and_scores")
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    #attrs.add(self._config.get("model_family_name"))
    #attrs.add(self._config.get('topic_name'))
    attrs.add(self._config.get("model_family_name") + "_infer_meta")
    return attrs

class MerchantPerflogItemAttrObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_perflog_item_attr"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @strict_types
  def depend_on_items(self) -> bool:
    return bool(self.input_item_attrs)

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []) + [self._config.get("value"), self._config.get("extra3"), self._config.get("extra4")])

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["namespace", "subtag", "extra1", "extra2", "extra5", "extra6"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key), check_format=False))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("mode"), "缺少 mode 配置")
    check_arg(self._config.get("namespace"), "缺少 namespace 配置")
    check_arg(self._config.get("subtag"), "缺少 subtag 配置")

class MerchantRecoReasonCountPerflogObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_perflog_reason_count"

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    # perflog 用的比较多，较容易出现 hash 冲突，hash 长度提高到 8
    return 8

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "log_info_from_attr" in self._config:
      attrs.add(self._config["log_info_from_attr"])
    attrs.update(self.extract_dynamic_params(self._config.get("check_point"), check_format=False))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    assert "reason_list_attr" in self._config, "缺少 reason_list_attr 配置"
    attrs.add(self._config["reason_list_attr"])
    return attrs

class MerchantDistributedCacheXtrScoreWriterObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "distributed_cache_xtr_score_writer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "get_table_name_from_kconf" in self._config:
      assert "photo_store_kconf_key" in self._config, "缺少 photo_store_kconf_key 配置"
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["expire_seconds_attr"])
    attrs = set(self._config.get("item_attr_names", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kconf_path"), "缺少kconf_path")
    check_arg(self._config.get("table_name"), "缺少table_name")

class MerchantCommonDistributedCacheWriterObserver(LeafObserver):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "distributed_cache_writer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "get_table_name_from_kconf" in self._config:
      assert "photo_store_kconf_key" in self._config, "缺少 photo_store_kconf_key 配置"
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(self._config.get("item_attr_names", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("kconf_path"), "缺少kconf_path")
    check_arg(self._config.get("table_name"), "缺少table_name")

class MerchantRedisIncrbyObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_redis_incrby"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in self._config.get("keys", []):
      attrs.update(self.extract_dynamic_params(key))
    for val in self._config.get("vals", []):
      attrs.update(self.extract_dynamic_params(val))
    attrs.update(self.extract_dynamic_params(self._config.get("expire_s")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_optimized_write")))
    return attrs

