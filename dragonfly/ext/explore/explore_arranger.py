#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_arranger.py
description:
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class ExploreClusterVariantSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_variant_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
        "use_reciprocal_v2", "check_point", "enable_user_profile_config", "cut_ratio_decay", "cut_ratio_boost",
        "user_profile_hetu_count_threshold", "neg_time_limit_second", "neg_play_sec_limit", "pos_time_limit_second", "pos_play_sec_limit",
        "fixed_final_size", "enable_dynamic_cut_ratio", "enable_kconf_ration", "user_info_ptr_attr", "action_day", "time_cluster_base_id"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("cluster_reweight_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_sort_list_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.add(self._config.get("cluster_sort_list_attr_name"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

class ExploreHierachyClusterVariantSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_hierachy_cluster_variant_sort"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                 "use_reciprocal_v2", "check_point",  "use_power_calc_v2",
                 "fixed_final_size","duration_cluster_new_prefix","duration_cluster_id_prefix",
                 "enable_dynamic_cut_ratio",  "user_info_ptr_attr",  "time_cluster_base_id","default_cluster_id","duration_cluster_cfg_str",
                 "enable_random_retrieve","random_retrieve_threshold","enable_exclude_recent_show","recent_expired_gap_s","recent_show_hetu_use_level_one",
                 "enable_recent_show_thompson_control","recent_show_use_explore_action"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("time_cluster_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("cluster_reweight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_pow_weight_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("duration_attr", "duration_ms"))
    attrs.add(self._config.get("is_picture_attr", "is_picture"))
    attrs.add(self._config.get("is_living_attr", "live_photo_info__is_living"))
    attrs.add(self._config.get("hetu_level_one_attr", "hetu_tag_level_info__hetu_level_one"))
    attrs.add(self._config.get("hetu_level_two_attr", "hetu_tag_level_info__hetu_level_two"))
    for name in ["cluster_sort_list_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

class ExploreClusterVariantSortV3Arranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_variant_sort_v3"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                 "use_reciprocal_v2", "check_point", "enable_user_profile_config", "cut_ratio_decay", "cut_ratio_boost", "use_power_calc_v2",
                 "user_profile_hetu_count_threshold", "neg_time_limit_second", "neg_play_sec_limit", "pos_time_limit_second", "pos_play_sec_limit",
                 "fixed_final_size", "enable_dynamic_cut_ratio", "enable_kconf_ration", "action_day", "time_cluster_base_id",
                 "enable_adjust_time_cluste_cut_ratio_v2", "enable_only_shrink_dislike_time_cluster",
                 "favourite_time_cluster_cut_ratio_coeff", "favourite_time_cluster_min_survival", "enable_adaptive_expand_favourite_cluster",
                 "enable_hetu_cluster_adjust_cut_ratio", "enable_duration_cluster_adjust_hetu_score", "duration_cluster_enable_unknown_hetu_adjust",
                 "rank_smooth", "rank_value_fusion_type", "enable_keep_photos_by_quota_control", "enable_interest_control", "interest_control_coeff",
                 "enable_diversity_control", "hetu1_max_size", "hetu2_max_size", "hetu5_max_size", "interest_control_start", "diversity_control_start",
                 "enable_ceil_keep_size"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("cluster_reweight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_pow_weight_attr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_pow_weight_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("favourite_time_cluster_attr"))
    attrs.add(self._config.get("favourite_time_cluster_score_attr"))
    attrs.add(self._config.get("dislike_time_cluster_attr"))
    attrs.add(self._config.get("candidate_hetu_adjust_coeff_map_attr"))
    attrs.add(self._config.get("hetu_cluster_hetu_adjust_paras_attr"))
    attrs.add(self._config.get("duration_cluster_hetu_adjust_para_attr"))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    attrs.add(self._config.get("hetu_level_two_attr"))
    attrs.add(self._config.get("hetu_level_five_attr"))
    for name in ["cluster_sort_list_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.add(self._config.get("cluster_sort_list_attr_name"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_value_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs


class ExploreClusterVariantSortV2Arranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_variant_sort_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["global_cut_ratio", "min_survival", "enable_proportional",
                "size_limit", "use_power_calc", "use_reciprocal", "use_reciprocal_v2",
                "cluster_sort_list_attr_name", "enable_kconf_ration", "user_info_ptr_attr",
                "action_day", "enable_duration_diversity", "enable_dynamic_weight_by_user_degree"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("avg_xtr", ""))
      attrs.add(cfg.get("min_ratio", ""))
      attrs.add(cfg.get("max_ratio", ""))
      attrs.add(cfg.get("dynamic_weight", ""))
      attrs.add(cfg.get("user_xtr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    attrs.add(self._config.get("hetu_level_one_name", ""))
    attrs.add(self._config.get("cluster_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs

class ExploreClusterEnsembleFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_ensemble_filter_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_emp_xtr_level_one"))
    attrs.add(self._config.get("cluster_sort_list_attr_name"))
    for name in ["keep_photo_size","skip_adjust_weight","global_min_survival","global_guarantee_ratio"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

class ExploreComplexAttrFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_complex_attr_filter_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["photo_life_max_hours_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("photo_info_ptr_attr"))
    return attrs

class ExploreRetrievalFilterArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)
    self.__filter_map = {}
    for filter in self._config.get("filters"):
      self.__filter_map[filter.get("name")] = filter

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieval_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    common_attr_set = set([
        "topk_audit_white_tag_list_attr", "topk_audit_black_tag_list_attr", "high_hot_audit_white_tag_list_attr", "high_hot_audit_black_tag_list_attr",
        "impression_audit_white_tag_list_attr", "impression_audit_black_tag_list_attr", "browse_screen_aid_list_attr", "follow_author_ignore_exptag_list_attr",
        "follow_author_filter_timegap_attr", "valuable_author_type_list_attr", "filter_content_type_list_attr", "filter_content_type_list_for_pic_attr", "magic_kk_id_list_attr",
        "skip_clicked_hate_item_filter_attr", "enable_short_hate_l5_filter_attr", "hetu_tag_l5_minutes_cut_attr", "filtered_hetu_tag_list_attr",
        "enable_long_hate_filter_attr", "hetu_tag_long_term_minutes_cut_attr", "hetu_l2_long_filter_threshold_attr", "hetu_otherl_long_filter_threshold_attr",
        "filter_pic_wallpaper_hetu_tag", "filter_long_pic_picture_type", "filter_long_pic_upload_type", "enable_pic_wallpaper_filter_caption_keep_attr", "pic_wallpaper_caption_keep_thresh_attr",
        "lower_cover_mmu_map_strs_attr", "lower_cover_mmu_map_tnu_reflux_strs_attr", "tmp_be_risk_user_attr", "explore_user_risk_min_attr", "tmp_be_shuffle_user_attr", "low_real_show_threshold",
        "low_fans_threshold", "black_hetu_set_low_real_show_attr", "black_hetu_set_low_fans_attr", "enable_cart_photo_filter_attr", "rate_of_high_explore_show",
        "min_show_of_high_explore_show", "max_show_of_high_explore_show", "black_hetu_set_high_explore_show_attr", "pic_mmu_low_quality_type_map",
        "explore_server_show_threshold", "explore_ctr_threshold", "black_white_change_risk", "black_white_change_shuffle", "explore_zero_play_days_15d_attr",
        "face_90_degree_pids_data_key", "black_photos_attr","support_author_picture_realshow_threshold", "support_author_picture_ctr_threshold", "support_author_filter_memory_data",
        "impression_audit_gray_show_limit_attr", "emp_realshow_show_threshold_attr", "emphtr_filter_threshold_attr", "skip_beauty_photo_filter_attr",
        "cold_start_breakout_score_threshold_attr", "high_fans_threshold_attr", "ctr_threshold_attr", "higher_action_threshold_attr",
        "need_high_quality_mmu_score_attr", "high_quality_mmu_map_strs_attr","questionaire_info_replace_topk_result", "skip_high_hot_quality_pic_attr",
        "questionaire_info_negtive_rate_threhold_attr", "questionaire_info_positive_rate_threhold_attr", "questionaire_info_unsure_rate_threhold_attr",
        "questionaire_info_credible_total_count_attr", "questionaire_thompson_filter_attr", "questionaire_filter_neg_weight_attr", "questionaire_filter_pos_weight_attr",
        "questionaire_filter_unsure_weight_attr", "questionaire_filter_click_weight_attr", "questionaire_filter_unclick_weight_attr",
        "enable_hetu_filter_attr", "enable_audit_tag_filter_attr", "questionaire_use_global_data_attr",
        "questionaire_info_negtive_rate_high_threhold_attr", "questionaire_info_topk_level_threshold_attr", "questionaire_info_audit_level_threshold_attr",
        "hetu_tag_l3_minutes_cut_attr",
        "thompson_filter_enable_fountain_cnt","thompson_filter_enable_thanos_cnt","thompson_filter_enable_nebula_cnt", "thompson_filter_enable_explore_cnt",
        "photo_life_max_hours_attr", "enable_skip_follow_author_attr", "thompson_filter_threshold_attr", "enable_interaction_base_attr",
        "thompson_filter_realshow_divisor_attr", "impression_audit_gray_tag_list_attr",
        "enable_hate_cost_attr", "emphtr_filter_ctr_weight_attr", "emphtr_filter_ltr_weight_attr", "emphtr_filter_wtr_weight_attr",
        "emphtr_filter_ftr_weight_attr", "emphtr_filter_cmtr_weight_attr", "emphtr_filter_time_weight_attr", "emphtr_filter_normal_time_weight_attr", "boost_photo_reason_list_attr",
        "emphtr_filter_report_weight_attr", "enable_hate_author_skip_hetu_filter_attr",
        "thompson_filter_ctr_weight_attr", "thompson_filter_ltr_weight_attr", "thompson_filter_wtr_weight_attr", "thompson_filter_ftr_weight_attr", "thompson_filter_cmtr_weight_attr", "thompson_filter_time_weight_attr", "thompson_filter_report_weight_attr", "thompson_filter_normal_time_weight_attr", "thompson_filter_realshow_weight_attr",
        "ignore_reason_attr", "default_cut_off_ratio_attr", "enable_random_cut_off_attr", "lt_longview_ratio_threshold_attr", "sharp_change_confidence_threshold_attr", "over_days_filter_days_limit_attr",
        "skip_high_xtr_dup_filter_attr", "skip_dup_realshow_threshold_attr", "skip_dup_fvtr_threshold_attr", "skip_dup_ctr_threshold_attr","skip_dup_watchtime_threshold_attr",
        "thompson_filter_enable_skip_low_emphtr_attr", "thompson_filter_no_click_weight_attr", "thompson_filter_low_emphtr_threshold_attr", "thompson_filter_lvtr_weight_attr",
        "skip_not_audit_zero_value_attr","skip_not_audit_follow_author_attr", "only_filter_picture_long_and_set_attr", "enable_adpt_threshold_attr", "emphtr_filter_threshold_list_attr",
        "mmu_enable_follow_author_exemption_attr", "mmu_enable_impression_good_ignore_attr","user_gender_attr","enable_explore_gender_attr",
        "topk_audit_bad_recall_filter_attr",  "topk_audit_bad_recall_filter_use_global_attr",  "topk_audit_bad_recall_filter_credible_ques_cnt_attr",  "topk_audit_bad_recall_filter_pos_threshold_attr",
        "topk_audit_bad_recall_filter_mode_attr", "topk_audit_bad_recall_filter_unsure_threshold_attr", "topk_audit_bad_recall_filter_neg_threshold_attr", "topk_audit_bad_recall_filter_hate_threshold_attr", "only_filter_high_value_pic_attr",
        "user_sexy_interest_score_attr", "user_sexy_interest_score_ignore_threshold_attr", "user_sexy_interest_exemption_tag_list_attr", "user_sexy_interest_extra_filter_tag_list_attr",
        "enable_user_sexy_interest_exemption_high_hot_white_tag_attr", "enable_user_sexy_interest_exemption_hate_rate_attr", "user_sexy_interest_exemption_hate_threshold_attr",
        "user_sexy_interest_exemption_hate_rate_threshold_attr", "user_sexy_interest_exemption_age_list_attr", "user_sexy_interest_exemption_city_level_list_attr",
        "user_age_segment_attr", "user_city_level_attr",
        ])

    attrs = set()
    for _, filter in self.__filter_map.items():
      attrs.update(self.extract_dynamic_params(filter.get("enable")))
      for item in filter.items():
        if item[0] in common_attr_set:
          attrs.add(item[1])

    if "server_show_aid" in self.__filter_map:
      attrs.add(self.__filter_map["server_show_aid"].get("server_show_aid_list_attr"))
    if "upload_type" in self.__filter_map:
      attrs.add(self.__filter_map["upload_type"].get("filter_type_list_attr"))
      attrs.add(self.__filter_map["upload_type"].get("enable_skip_high_value_pic"))
      attrs.add(self.__filter_map["upload_type"].get("enable_skip_climbing_high_value_pic"))
      attrs.add(self.__filter_map["upload_type"].get("enable_skip_useful_high_value_pic"))
    if "picture_type" in self.__filter_map:
      attrs.add(self.__filter_map["picture_type"].get("filter_type_list_attr"))
      attrs.add(self.__filter_map["picture_type"].get("enable_skip_high_value_pic"))
      attrs.add(self.__filter_map["upload_type"].get("enable_skip_climbing_high_value_pic"))
      attrs.add(self.__filter_map["picture_type"].get("enable_skip_useful_high_value_pic"))
    if "source_aid" in  self.__filter_map:
      attrs.add(self.__filter_map["source_aid"].get("source_aid_attr"))
    if "low_fans_lite" in  self.__filter_map:
      attrs.add(self.__filter_map["low_fans_lite"].get("count_threshold_attr"))
    if "low_server_show_lite" in  self.__filter_map:
      attrs.add(self.__filter_map["low_server_show_lite"].get("count_threshold_attr"))
    if "long_term" in  self.__filter_map:
      attrs.add(self.__filter_map["long_term"].get("days_threshold_attr"))
    if "content_dup" in  self.__filter_map:
      attrs.add(self.__filter_map["content_dup"].get("explore_skip_high_hot_quality_attr"))
    if "content_dup_v2" in  self.__filter_map:
      filter = self.__filter_map["content_dup_v2"]
      for attr in ["filter_content_type_list_attr", "filter_content_type_list_for_pic_attr",
        "skip_high_hot_quality_pic_attr", "skip_high_xtr_dup_filter_attr", "skip_dup_realshow_threshold_attr",
        "skip_dup_fvtr_threshold_attr", "skip_dup_ctr_threshold_attr", "skip_dup_watchtime_threshold_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "hate_author" in  self.__filter_map:
      attrs.add(self.__filter_map["hate_author"].get("limit_hate_reason_attr"))
      attrs.add(self.__filter_map["hate_author"].get("enable_short_author_attr"))
      attrs.add(self.__filter_map["hate_author"].get("short_hate_author_minutes_attr"))
    if "short_duration" in self.__filter_map:
       attrs.add(self.__filter_map["short_duration"].get("short_duration_limit_attr"))
    if "emprical_ctr" in  self.__filter_map:
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_filter_threshold_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_threshold_str_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_multi_number_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_sample_base_number_attr"))
      attrs.add(self.__filter_map["emprical_ctr"].get("empctr_filter_realshow_threshold_attr"))
    if "duration_random_filter" in self.__filter_map:
      filter = self.__filter_map["duration_random_filter"]
      for attr in ["ignore_reason_attr", "default_cut_off_ratio_attr", "adjust_cut_off_ratio_attr", "enable_random_cut_off_attr",
        "lt_longview_ratio_threshold_attr", "sharp_change_confidence_threshold_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "duration_emp_watchtime_sample_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_threshold_attr"))
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_base_number_attr"))
      attrs.add(self.__filter_map["duration_emp_watchtime_sample_filter"].get("duration_sample_multi_number_attr"))
    if "pic_exptag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_exptag_filter"].get("pic_exptag_filter_str_attr"))
    if "fresh_request_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["fresh_request_filter"].get("is_fresh_request_attr"))
      attrs.add(self.__filter_map["fresh_request_filter"].get("show_threshold_attr"))
    if "xtab_life_index_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_category_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_category_l2_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_blacklist_category_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("key_hetu_blacklist_category_l2_list_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_category_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_category_l2_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_blacklist_category_filter_attr"))
      attrs.add(self.__filter_map["xtab_life_index_filter"].get("enable_key_hetu_blacklist_category_l2_filter_attr"))
    if "lifecate_pic_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["lifecate_pic_filter"].get("explore_lifecate_hetu1_list_attr"))
    if "over_180_days" in  self.__filter_map:
      attrs.add(self.__filter_map["over_180_days"].get("entertainment_hetu_tags_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("entertainment_hetu_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("enable_filter_low_like"))
      attrs.add(self.__filter_map["over_180_days"].get("low_like_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("low_like_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("topn_screen_filter_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("enable_filter_by_audit"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_not_audit_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_gray_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_normal_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_high_quality_days_limit_attr"))

      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_gray_hours_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_normal_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("high_hot_audit_high_quality_days_limit_attr"))
      attrs.add(self.__filter_map["over_180_days"].get("enable_impression_audit_timeliness_photo_filter"))
      attrs.add(self.__filter_map["over_180_days"].get("impression_audit_timeliness_photo_map_attr"))
    if "multi_audit_gray_filter" in self.__filter_map:
      attrs.add(self.__filter_map["multi_audit_gray_filter"].get("audit_gray_count_threshold_attr"))
      attrs.add(self.__filter_map["multi_audit_gray_filter"].get("multi_audit_gray_days_limit_attr"))
    if "high_hot_audit_gray_show" in self.__filter_map:
      attrs.add(self.__filter_map["high_hot_audit_gray_show"].get("enable_stat_all_page"))
      attrs.add(self.__filter_map["high_hot_audit_gray_show"].get("high_hot_audit_gray_show_threshold"))
    if "audit_gray_cover_level_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("page_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("enable_tnu_and_reflux_not_cover_photo_filter_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("enable_not_cover_photo_filter_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("enable_first_page_not_cover_photo_filter_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("hetu_v3_level_one_white_tag_list_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("hetu_v3_white_tag_fans_threshold_attr"))
      attrs.add(self.__filter_map["audit_gray_cover_level_filter"].get("max_page_threshold_attr"))
    if "audit_rule_adjust_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_rule_adjust_filter"].get("audit_rule_adjust_tags_attr"))
    if "dynamic_xtr_filter" in self.__filter_map:
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_xtrs_threshold_list_attr"))
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_filter_old_photo_days_attr"))
      attrs.add(self.__filter_map["dynamic_xtr_filter"].get("dynamic_filter_save_follow_author_attr"))
    if "empirical_xtr" in self.__filter_map:
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_realshow_threshold_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_upload_date_threshold_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_ctr_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_playtime_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("explore_emp_cross_dropout_rate_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_ctr_threshold_str_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_playtime_threshold_str_attr"))
      attrs.add(self.__filter_map["empirical_xtr"].get("emp_cross_threshold_str_attr"))
    if "merchant_holdout_filter" in self.__filter_map:
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("merchant_author_list_ptr_attr"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("enable_filter_living_merchant_photo"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("enable_filter_living_merchant_author"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("enable_high_negative_feedback_rate_filter"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("photo_real_show_count_thres"))
      attrs.add(self.__filter_map["merchant_holdout_filter"].get("photo_hate_like_rate_thres"))

    if "video_quality_assessment_filter" in self.__filter_map:
      attrs.add(self.__filter_map["video_quality_assessment_filter"].get("skip_video_quality_assessment_follow_author_attr"))
    if "be_black_author_filter" in self.__filter_map:
      attrs.add(self.__filter_map["be_black_author_filter"].get("be_black_list_attr"))
    if "negative_retr_filter" in self.__filter_map:
      attrs.add(self.__filter_map["negative_retr_filter"].get("negative_retr_list_attr"))
    if "short_term_negative_filter" in self.__filter_map:
      attrs.add(self.__filter_map["short_term_negative_filter"].get("short_minutes_cut_attr"))
      attrs.add(self.__filter_map["short_term_negative_filter"].get("long_minutes_cut_attr"))
    if "specified_group_gray_audit_filter" in self.__filter_map:
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_impression_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_high_hot_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("audit_topk_limit_list_attr"))
      attrs.add(self.__filter_map["specified_group_gray_audit_filter"].get("is_satisfy_user"))
    if "second_tab_hetu_filter" in self.__filter_map:
      attrs.add(self.__filter_map["second_tab_hetu_filter"].get("second_tab_category_id"))
    if "audit_user_experiment_level_filter" in self.__filter_map:
       attrs.add(self.__filter_map["audit_user_experiment_level_filter"].get("audit_user_experiment_level_map_attr"))
    if "personified_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["personified_author_filter"].get("personified_author_filter_flag"))
    if "movie_copyright_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["movie_copyright_holdout_filter"].get("filter_bits_list_attr"))
    if "young_inc_tags_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("young_inc_category_list_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("young_inc_category_hetu_list_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_flag_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("filter_prime_attr"))
      attrs.add(self.__filter_map["young_inc_tags_holdout_filter"].get("upload_time_limit_attr"))
    if "fans_count_random_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("filter_prime_attr"))
      attrs.add(self.__filter_map["fans_count_random_holdout_filter"].get("fans_bucket_list_attr"))
    if "low_comment_cnt_filter" in self.__filter_map:
      attrs.add(self.__filter_map["low_comment_cnt_filter"].get("low_comment_cnt_threshold_attr"))
    if "audit_hack_photo_filter" in self.__filter_map:
      filter = self.__filter_map["audit_hack_photo_filter"]
      for attr in ["audit_hack_tag_set_attr", "min_show_attr", "max_ltr_attr", "max_wtr_attr", "max_cmtr_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "audit_cold_review_level_filter" in self.__filter_map:
      filter = self.__filter_map["audit_cold_review_level_filter"]
      for attr in ["audit_cold_review_level_black_tag_set_attr", "audit_cold_review_level_top_list_inferior_tag_set_attr", "audit_cold_review_level_top_list_inferior_vv_limit_attr", "explore_enable_audit_cold_review_level_for_all_user_attr"]:
        if attr in filter:
          attrs.add(filter.get(attr))
    if "user_reco_neg_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("reco_neg_photo_list_attr"))
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("candidate_count_attr"))
      attrs.add(self.__filter_map["user_reco_neg_photo_filter"].get("candidate_count_limit"))
    if "data_set_tags_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["data_set_tags_filter"].get("filter_tags_list_attr"))
    if "data_set_tags_bit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["data_set_tags_bit_filter"].get("filter_bits_list_attr"))
    if "hetu_tag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_whitelist_categories_l1_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_whitelist_categories_l2_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_blacklist_categories_l1_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("hetu_v2_blacklist_categories_l2_list_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("enable_low_vv_filter_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("explore_vv_3d_threshold_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("explore_vv_3d_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("is_zero_play_user_attr"))
      attrs.add(self.__filter_map["hetu_tag_filter"].get("enable_only_zero_play_filter_attr"))
    if "hetu_sim_cluster_id_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["hetu_sim_cluster_id_filter"].get("hetu_sim_cluster_id_blacklist_attr"))
    if "quality_audit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["quality_audit_filter"].get("filter_tags_list_attr"))
    if "quality_control_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_quality_control_threshold_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_audit_gray_weight_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("explore_mmu_score_gray_weight_attr"))
      attrs.add(self.__filter_map["quality_control_filter"].get("impression_audit_gray_tag_list_attr"))
    if "ecom_intent_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["ecom_intent_score_filter"].get("ecom_intent_score_threshold_attr"))
      attrs.add(self.__filter_map["ecom_intent_score_filter"].get("explore_enable_ecom_intent_score_for_all_user_attr"))
    if "hetu_author_category_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["hetu_author_category_holdout_filter"].get("fans_count_limit_attr"))
      attrs.add(self.__filter_map["hetu_author_category_holdout_filter"].get("hetu_author_category_list_attr"))
    if "pic_low_quality_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_quality_filter"].get("pic_low_quality_filter_thresh_attr"))
      attrs.add(self.__filter_map["pic_low_quality_filter"].get("explore_pic_low_quality_tag_list_attr"))
    if "pic_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_author_filter"].get("author_grade_thresh_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_punish_cnt_mode_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_filter_markcode_attr"))
      attrs.add(self.__filter_map["pic_author_filter"].get("author_punish_markcode_attr"))
    if "pic_low_cost_marketing_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_cost_marketing_filter"].get("low_cost_markcode_attr"))
      attrs.add(self.__filter_map["pic_low_cost_marketing_filter"].get("yanghao_markcode_attr"))
    if "pic_mmu_hetu_tag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_prob_str_attr"))
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_skip_hv_str_attr"))
      attrs.add(self.__filter_map["pic_mmu_hetu_tag_filter"].get("mmu_tag_vv_thr_str_attr"))
    if "pic_data_set_tags_bit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("pic_filter_bits_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("pic_punish_bits_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("skip_filter_mark_cod_str_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("punish_vv_thresh_attr"))
      attrs.add(self.__filter_map["pic_data_set_tags_bit_filter"].get("punish_filter_prob_attr"))
    if "pic_secure_grade_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_secure_grade_filter"].get("secure_grade_filter_code_attr"))
      attrs.add(self.__filter_map["pic_secure_grade_filter"].get("secure_grade_punish_code_attr"))
      attrs.add(self.__filter_map["pic_secure_grade_filter"].get("skip_audit_b_second_tags_attr"))
    if "pic_audit_cold_review_level_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_audit_cold_review_level_filter"].get("filter_audit_cold_review_level_str_attr"))
    if "pic_mix_interact_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("base_vv_threshold_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("author_filter_mark_cod_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("interact_rate_thresholds_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("vv_thresholds_str_attr"))
      attrs.add(self.__filter_map["pic_mix_interact_rate_filter"].get("filter_probs_str_attr"))
    if "pic_bad_cover_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_bad_cover_filter"].get("pic_bad_cover_tags_attr"))
    if "pic_low_cost_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_cost_filter"].get("explore_low_cost_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_low_cost_filter"].get("explore_low_cost_pic_cnt_mode_attr"))
    if "pic_hack_act_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_tags_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_types_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_hack_act_filter"].get("explore_hack_act_pic_cnt_mode_attr"))
    if "pic_long_live_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_long_live_photo_filter"].get("pic_long_live_photo_vv_thresh_attr"))
      attrs.add(self.__filter_map["pic_long_live_photo_filter"].get("pic_long_live_photo_duration_thresh_attr"))
    if "pic_low_act_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_low_act_filter"].get("explore_pic_low_act_vv_thres_attr"))
      attrs.add(self.__filter_map["pic_low_act_filter"].get("explore_pic_low_act_rate_thres_attr"))
    if "pic_sexy_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_sexy_filter"].get("sexy_pic_max_cnt_attr"))
      attrs.add(self.__filter_map["pic_sexy_filter"].get("sexy_pic_cnt_mode_attr"))
    if "pic_liezhi_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_liezhi_author_filter"].get("author_liezhi_pic_count_thresh_attr"))
      attrs.add(self.__filter_map["pic_liezhi_author_filter"].get("author_fans_count_thresh_attr"))
    if "high_photo_count_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("high_photo_count_author_map_ptr_attr"))
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("realshow_threshold_attr"))
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("pos_neg_ratio_coeff_attr"))
      attrs.add(self.__filter_map["high_photo_count_author_filter"].get("fans_count_limit_attr"))
    if "douyin_author_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("filter_flag_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("fans_count_limit_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("hetu_author_category_list_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("douyin_10w_author_set_ptr_attr"))
      attrs.add(self.__filter_map["douyin_author_holdout_filter"].get("douyin_100w_author_set_ptr_attr"))
    if "short_play_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["short_play_filter"].get("explore_short_play_threshold_attr"))
      attrs.add(self.__filter_map["short_play_filter"].get("explore_short_play_smooth_attr"))
    if "mid_long_video_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["mid_long_video_holdout_filter"].get("duration_lowerbound_attr"))
      attrs.add(self.__filter_map["mid_long_video_holdout_filter"].get("duration_upperbound_attr"))
      attrs.add(self.__filter_map["mid_long_video_holdout_filter"].get("filter_tags_list_attr"))
    if "produce_type_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["produce_type_filter"].get("produce_magic_type_filter_flag_attr"))
      attrs.add(self.__filter_map["produce_type_filter"].get("produce_need_filter_magic_type_list_attr"))
    if "magic_id_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["magic_id_filter"].get("produce_need_filter_magic_id_set_attr"))
    if "auto_audit_hot_cover_level_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("enable_follow_author_exemption_attr"))
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("enable_impression_good_ignore_attr"))
      attrs.add(self.__filter_map["auto_audit_hot_cover_level_filter"].get("auto_audit_bad_show_limit_attr"))
    if "high_global_emphtr_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["high_global_emphtr_filter"].get("global_emp_realshow_show_threshold_attr"))
      attrs.add(self.__filter_map["high_global_emphtr_filter"].get("global_emphtr_filter_threshold_attr"))
    if "continuous_hitting_filter" in self.__filter_map:
      attrs.add(self.__filter_map["continuous_hitting_filter"].get("explore_hitting_threshold_attr"))
      attrs.add(self.__filter_map["continuous_hitting_filter"].get("enable_hetu_five_white_list"))
      attrs.add(self.__filter_map["continuous_hitting_filter"].get("hetu_five_whitelist_attr"))
      attrs.add(self.__filter_map["continuous_hitting_filter"].get("realshow_unclick_item_cnt_attr"))
      attrs.add(self.__filter_map["continuous_hitting_filter"].get("realshow_unclick_item_id_attr"))
    if "first_slide_impression_audit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["first_slide_impression_audit_filter"].get("page_attr"))
      attrs.add(self.__filter_map["first_slide_impression_audit_filter"].get("enable_only_first_slide_filter_attr"))
      attrs.add(self.__filter_map["first_slide_impression_audit_filter"].get("impression_audit_whitelist_categories_list_attr"))
      attrs.add(self.__filter_map["first_slide_impression_audit_filter"].get("need_filter_photo_type_list_attr"))
    if "minority_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["minority_photo_filter"].get("filter_bits_list_attr"))
      attrs.add(self.__filter_map["minority_photo_filter"].get("pass_num_limit_attr"))
    if "first_refresh_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["first_refresh_filter"].get("filter_list_size_attr"))
      attrs.add(self.__filter_map["first_refresh_filter"].get("enable_global_data_attr"))
      attrs.add(self.__filter_map["first_refresh_filter"].get("page_index_attr"))
      attrs.add(self.__filter_map["first_refresh_filter"].get("refresh_times_attr"))
    if "social_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["social_holdout_filter"].get("friend_id_list_attr"))
      attrs.add(self.__filter_map["social_holdout_filter"].get("global_interact_user_today_cnt_attr"))
    if "sirius_distribution_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["sirius_distribution_photo_filter"].get("filter_tags_list_attr"))
    if "merchant_cart_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["merchant_cart_holdout_filter"].get("enable_first_fresh_merchant_filter_attr"))
      attrs.add(self.__filter_map["merchant_cart_holdout_filter"].get("page_index_attr"))
    if "mmu_merchant_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["mmu_merchant_photo_filter"].get("mmu_merchant_filter_black_list_attr"))
      attrs.add(self.__filter_map["mmu_merchant_photo_filter"].get("page_index_attr"))
    if "first_fresh_ad_impression_audit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["first_fresh_ad_impression_audit_filter"].get("page_attr"))
      attrs.add(self.__filter_map["first_fresh_ad_impression_audit_filter"].get("fresh_ad_impression_audit_blacklist_list_attr"))
    if "proximate_audit_hot_high_bad_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["proximate_audit_hot_high_bad_filter"].get("page_index_attr"))
      attrs.add(self.__filter_map["proximate_audit_hot_high_bad_filter"].get("audit_hot_high_tag_level_attr"))
      attrs.add(self.__filter_map["proximate_audit_hot_high_bad_filter"].get("proximate_hot_high_page_threshold_attr"))
    if "short_term_hate" in  self.__filter_map:
      attrs.add(self.__filter_map["short_term_hate"].get("high_hetu_num_threshold_attr"))
      attrs.add(self.__filter_map["short_term_hate"].get("low_hetu_num_threshold_attr"))
    if "report_author" in  self.__filter_map:
      attrs.add(self.__filter_map["report_author"].get("enable_report_hetu_short_attr"))
      attrs.add(self.__filter_map["report_author"].get("short_report_hetu_minutes_attr"))
      attrs.add(self.__filter_map["report_author"].get("long_report_hetu_minutes_attr"))
    if "high_emp_phtr_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("enable_hate_count_filter_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emp_realshow_show_high_threshold_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emp_hate_cnt_filter_threshold_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("enable_adpt_threshold_by_realshow_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_coeff_max_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_coeff_min_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_alpha_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_beta_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_omega_attr"))
      attrs.add(self.__filter_map["high_emp_phtr_filter"].get("emphtr_filter_adpt_threshold_exp_upper_attr"))
    if "no_cover_audit_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("filter_map_str_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("default_thres_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("realshow_thres_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("enable_dynamic_realshow_thres_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("realshow_per_day_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("enable_protogenetic_advertise_filter_attr"))
      attrs.add(self.__filter_map["no_cover_audit_filter"].get("filter_protogenetic_advertise_list_attr"))
    if "reason_3125_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["reason_3125_filter"].get("hetu_filter_map_str_attr"))
      attrs.add(self.__filter_map["reason_3125_filter"].get("hetu_default_thres_attr"))
      attrs.add(self.__filter_map["reason_3125_filter"].get("marketing_filter_tags_list_attr"))
      attrs.add(self.__filter_map["reason_3125_filter"].get("marketing_default_thres_attr"))
    if "protogenetic_advertise_tags_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["protogenetic_advertise_tags_filter"].get("filter_advertise_list_attr"))
    if "live_photo_flag_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["live_photo_flag_filter"].get("live_photo_flag_thres_attr"))
    if "emp_xtr_decrease_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["emp_xtr_decrease_filter"].get("emp_xtr_decrease_photo_set_ptr_attr"))
      attrs.add(self.__filter_map["emp_xtr_decrease_filter"].get("enable_random_attr"))
      attrs.add(self.__filter_map["emp_xtr_decrease_filter"].get("filter_ratio_attr"))
    if "emp_xtr_decrease_tonpson_filter" in self.__filter_map:
      attrs.add(self.__filter_map["emp_xtr_decrease_tonpson_filter"].get("emp_xtr_decrease_tonpson_photo_map_ptr_attr"))
      attrs.add(self.__filter_map["emp_xtr_decrease_tonpson_filter"].get("filter_ratio_attr"))
    if "tnu_impression_audit_bad_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["tnu_impression_audit_bad_filter"].get("tnu_impression_audit_whitelist_attr"))
    if "negative_aid_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["negative_aid_filter"].get("negative_aid_set_ptr_attr"))
    if "valid_play_ratio_filter" in self.__filter_map:
      attrs.add(self.__filter_map["valid_play_ratio_filter"].get("explore_click_threshold_attr"))
      attrs.add(self.__filter_map["valid_play_ratio_filter"].get("explore_upload_days_threshold_attr"))
      attrs.add(self.__filter_map["valid_play_ratio_filter"].get("explore_valid_play_ratio_threshold_attr"))
      attrs.add(self.__filter_map["valid_play_ratio_filter"].get("explore_valid_play_ratio_dropout_rate_attr"))
    if "over_distribute_filter" in self.__filter_map:
      attrs.add(self.__filter_map["over_distribute_filter"].get("explore_show_limit_attr"))
      attrs.add(self.__filter_map["over_distribute_filter"].get("explore_ctr_limit_attr"))
    if "emotions_pic_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["emotions_pic_filter"].get("emotions_pic_filter_ratio_attr"))
      attrs.add(self.__filter_map["emotions_pic_filter"].get("emotions_pic_show_count_threshold_attr"))
    if "serial_cover_photo_collection_filter" in self.__filter_map:
      attrs.add(self.__filter_map["serial_cover_photo_collection_filter"].get("photo_collection_pids_set_ptr_attr"))
    if "merchant_hetu_tag_photo_filter" in self.__filter_map:
      attrs.add(self.__filter_map["merchant_hetu_tag_photo_filter"].get("show_count_limit_attr"))
      attrs.add(self.__filter_map["merchant_hetu_tag_photo_filter"].get("is_random_filter_attr"))
      attrs.add(self.__filter_map["merchant_hetu_tag_photo_filter"].get("random_filter_percent_attr"))
    if "tnu_content_control_filter" in self.__filter_map:
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("enable_filter_audit_cold_review_attr"))
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("enable_filter_hetu_tags_attr"))
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("enable_filter_mmu_merchant_hetu_photo_attr"))
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("hetu_tags_list_attr"))
      attrs.add(self.__filter_map["tnu_content_control_filter"].get("cold_review_audit_tags_list_attr"))
    if "pic_ecology_high_report_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("explore_pic_ecology_high_report_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("explore_pic_ecology_high_report_count_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_report_filter"].get("pic_ecology_high_report_fans_count_threshold_attr"))
    if "pic_ecology_high_neg_pos_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_neg_pos_rate_filter"].get("explore_pic_ecology_high_neg_pos_rate_threshold_attr"))
    if "pic_ecology_high_short_play_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_short_play_rate_filter"].get("explore_pic_ecology_high_short_play_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_high_short_play_rate_filter"].get("explore_pic_ecology_neg_rate_threshold_attr"))
    if "teenager_author_filter" in self.__filter_map:
      attrs.add(self.__filter_map["teenager_author_filter"].get("show_count_limit_attr"))
    if "pic_ecology_bad_avg_time_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_bad_avg_time_filter"].get("explore_pic_ecology_bad_view_time_for_short_play_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_bad_avg_time_filter"].get("explore_pic_ecology_bad_view_time_threshold_attr"))
    if "high_emp_ntpr_filter" in self.__filter_map:
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_realshow_show_high_threshold_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_filter_threshold_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_filter_report_weight_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_enable_adpt_threshold_by_realshow_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_coeff_max_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_coeff_min_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_alpha_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_beta_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_omega_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_adpt_threshold_exp_upper_attr"))
      attrs.add(self.__filter_map["high_emp_ntpr_filter"].get("emp_ntpr_enable_adpt_fountain_consume_data_attr"))
    if "pic_ecology_high_release_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_release_author_filter"].get("filter_bits_list_attr"))
    if "pic_ecology_high_delete_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_high_delete_author_filter"].get("filter_bits_list_attr"))
    if "pic_ecology_mix_interact_rate_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_rate_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_avg_view_time_threshold_attr"))
      attrs.add(self.__filter_map["pic_ecology_mix_interact_rate_filter"].get("pic_ecology_interact_vv_threshold_attr"))
    if "induced_author_black_list_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["induced_author_black_list_filter"].get("enable_filter_induced_curiosity_author_list_attr"))
      attrs.add(self.__filter_map["induced_author_black_list_filter"].get("induced_curiosity_authorid_blacklist_attr"))
      attrs.add(self.__filter_map["induced_author_black_list_filter"].get("enable_filter_bad_audit_list_attr"))
      attrs.add(self.__filter_map["induced_author_black_list_filter"].get("bad_audit_authorid_blacklist_attr"))
    if "lower_emp_xtr_act_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("enbale_filter_low_like_act_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("enbale_filter_low_follow_act_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("low_real_show_count_threshold_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("low_empltr_act_filter_threshold_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("low_empftr_act_filter_threshold_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("limit_for_like_empctr_threshold_attr"))
      attrs.add(self.__filter_map["lower_emp_xtr_act_filter"].get("limit_for_follow_empctr_threshold_attr"))
    if "llm_negative_photos_filter" in self.__filter_map:
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("teenager_filter_tag_map_str_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("filter_tag_map_str_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("is_teenager_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("show_count_limit_map_str_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("enable_filter_no_impression_audit_result_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("filter_impression_audit_level_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("report_count_coeff_attr"))
      attrs.add(self.__filter_map["llm_negative_photos_filter"].get("report_ratio_coeff_attr"))
    if "hot_point_pid_filter" in self.__filter_map:
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_default_days_attr"))
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_bits_list_attr"))
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_hetu_str_attr"))
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_up_days_attr"))
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_collect_rate_limit_attr"))
      attrs.add(self.__filter_map["hot_point_pid_filter"].get("hot_point_pid_filter_follow_rate_limit_attr"))
    if "author_shop_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["author_shop_score_filter"].get("author_shop_score_limit_attr"))
      attrs.add(self.__filter_map["author_shop_score_filter"].get("author_shop_zero_protect_attr"))
    if "author_goods_score_filter" in self.__filter_map:
      attrs.add(self.__filter_map["author_goods_score_filter"].get("author_goods_score_limit_attr"))
      attrs.add(self.__filter_map["author_goods_score_filter"].get("author_goods_zero_protect_attr"))
    if "audit_overtime_filter" in self.__filter_map:
      attrs.add(self.__filter_map["audit_overtime_filter"].get("upload_days_limit_attr"))
    if "short_term_report_filter" in self.__filter_map:
      attrs.add(self.__filter_map["short_term_report_filter"].get("short_minutes_cut_attr"))
      attrs.add(self.__filter_map["short_term_report_filter"].get("long_minutes_cut_attr"))
    if "source_dup_content_id_filter" in self.__filter_map:
      attrs.add(self.__filter_map["source_dup_content_id_filter"].get("source_content_type_list_attr"))
    if "emp_neg_feedback_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["emp_neg_feedback_filter"].get("emp_neg_feedback_photo_set_ptr_attr"))
      attrs.add(self.__filter_map["emp_neg_feedback_filter"].get("filter_ratio_attr"))
    if "high_report_photo_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["high_report_photo_filter"].get("realshow_threshold_attr"))
      attrs.add(self.__filter_map["high_report_photo_filter"].get("repoprt_ratio_limit_attr"))
    if "marketing_static_video_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("static_video_tag_id_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("static_video_tag_prob_thd_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("marketing_mark_cod_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("base_vv_threshold_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("interact_rate_thresholds_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("vv_thresholds_str_attr"))
      attrs.add(self.__filter_map["marketing_static_video_filter"].get("filter_probs_str_attr"))
    if "coldstart_holdout_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["coldstart_holdout_filter"].get("nebula_thanos_realshow_limit_attr"))
      attrs.add(self.__filter_map["coldstart_holdout_filter"].get("guarantee_rank_limit_attr"))
      attrs.add(self.__filter_map["coldstart_holdout_filter"].get("enable_filter_if_double_shield_attr"))
    if "sexy_induce_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["sexy_induce_author_filter"].get("sexy_induce_photo_set_ptr_attr"))
    if "poor_quality_author_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["poor_quality_author_filter"].get("gaofen_signs_uids_set_ptr_attr"))
      attrs.add(self.__filter_map["poor_quality_author_filter"].get("enable_filter_by_gaofen_signs_uids"))
      attrs.add(self.__filter_map["poor_quality_author_filter"].get("enable_filter_by_hierarchy_label_uids"))
      attrs.add(self.__filter_map["poor_quality_author_filter"].get("hierarchy_label_uids_map_ptr_attr"))
      attrs.add(self.__filter_map["poor_quality_author_filter"].get("hierarchy_label_uids_filter_ratio"))
    if "topn_screen_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["topn_screen_filter"].get("impression_audit_level_limit_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("high_hot_audit_level_limit_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("emp_ntpr_limit_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("explore_today_vv_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("active_days_avg_vv_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("filter_ratio_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("avg_vv_coeff_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("page_index_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("hetu_l1_blacklist_list_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("hetu_l2_blacklist_list_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("hetu_l3_blacklist_list_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("enable_topn_merchant_cart_filter_ratio_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("manjiao_markcode_blacklist_list_attr"))
      attrs.add(self.__filter_map["topn_screen_filter"].get("enable_first_index_control_attr"))
    if "plc_business_type_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["plc_business_type_filter"].get("filter_tags_list_attr"))
    if "valuable_photo_open_filter" in  self.__filter_map:
      attrs.add(self.__filter_map["valuable_photo_open_filter"].get("valuable_rules_kconf_key_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attr_map").values():
      attrs.add(attr)

    if "content_dup" in self.__filter_map:
      attrs.update([
        "mmu_content_ids_3", "mmu_content_ids_8", "mmu_content_ids_9", "mmu_content_ids_10", "mmu_content_ids_14", "mmu_content_ids_15",
        "mmu_content_ids_16", "mmu_content_ids_17", "mmu_content_ids_33"])

    item_attr_set = set([
        "is_break_circle_good_photo_attr", "audit_hot_high_tag_level_attr",
        "explore_operation_c_review_level_attr", "level_hot_online_attr", "audit_b_second_tag_attr", "audit_hot_high_subdivision_level_attr",
        "audit_hot_high_tag_level_attr", "author_id_attr", "is_tnu_extend_index_photo_attr", "auto_audit_black_exempt_level_v1_attr",
        "long_term_high_level_photo_attr", "is_cuckoo_photo_attr", "cuckoo_author_type_attr", "upload_time_attr",
        "explore_operation_c_review_level_attr", "dup_cluster_id_attr", "pic_and_selfdup_id_attr",
        "upload_type_attr", "is_jianguan_risk_photo_attr", "magic_face_id_attr", "kuaishan_id_attr", "outer_material_id_attr",
        "is_eyeshot_longterm_photo_attr", "is_high_other_photo_attr", "show_level_a_attr", "photo_low_report_count_attr",
        "author_low_report_count_attr", "sim_remove_dup_id_attr", "author_fans_count_attr", "explore_real_show_attr", "nebula_real_show_attr",
        "thanos_real_show_attr", "fountain_real_show_attr","mmu_low_quality_model_score_42_attr", "mmu_low_quality_model_score_46_attr",
        "mmu_low_quality_model_score_52_attr", "mmu_low_quality_model_score_63_attr", "mmu_low_quality_model_score_64_attr", "mmu_low_quality_model_score_104_attr",
        "mmu_low_quality_model_score_123_attr", "explore_server_show_attr", "explore_click_attr", "explore_view_length_sum_attr"])

    for filter in self._config.get("filters"):
      for item in filter.items():
        if item[0] == "name" or item[0] == "enable":
          continue

        if item[0] in item_attr_set:
          attrs.add(item[1])

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for _, filter in self.__filter_map.items():
      for item in filter.items():
        if item[0] == "save_filtered_pid_list_to_attr":
          attrs.add(item[1])

    return attrs

class ExploreResetReasonArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_reset_reason"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("discard_photo_list_attr"))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_living_attr"))

    return attrs

class ExplorePiratePhotoReplacementArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_replace_pirate_photo"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("original_photo_id_attr"))
    attrs.add(self._config.get("original_author_id_attr"))
    attrs.add(self._config.get("author_id_attr"))

    return attrs

class ExplorePiratePhotoReplacementArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_replace_pirate_photo"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("original_photo_id_attr"))
    attrs.add(self._config.get("original_author_id_attr"))
    attrs.add(self._config.get("author_id_attr"))

    return attrs

class ExploreReasonPrioritySortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_sort_by_reason_priority"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("reason_priority_list")))

    return attrs

class ExploreEnsembleScoreCalcArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_ensemble_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["rank_smooth","rank_power_weight","rank_cliff", "rank_height", "cliff_ratio", "user_new_proportion", "user_power_calc",
                 "user_power_calc_v2", "use_reciprocal", "duration_min", "duration_max", "enable_time_cost_opt",
                 "enable_perf_pxtr_pic", "perf_pxtr_pic_num", "duration_add", "action_day",
                 "enable_dynamic_weight_by_user_degree", "fr_rank_max_num", "fr_rank_specified_num", "use_absolute_pow",
                 "use_weighed_sum", "fr_rank_has_sec_str", "use_formula_pow_t", "value_seq_fusion_status",
                 "rank_score_calculate_method", "hyperbolic_scale", "hyperbolic_alpha", "hyperbolic_beta", "hyperbolic_min_num",
                 "two_way_sort", "two_way_sort_total_size", "two_way_sort_coeff", "enable_smooth_rank_formula", "smooth_rank_formula_beta", "enable_use_reciprocal_duration_transform",
                 "enable_power_weight_norm", "power_weight_change_coeff", "min_rank_weight", "use_queue_smooth_as_rank_smooth", "use_queue_value_seq_fusion_status",
                 "use_fractile_in_ensemble_sort", "fractile_in_ensemble_sort_type", "queue_head_boost_index","queue_tail_discount_index", "use_rank_with_absolute_score",
                 "use_rank_sort_weight_adjust", "queue_max_raw_score", "queue_min_raw_score", "enable_normalization_item_score"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_power_weight_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("avg_xtr", ""))
      attrs.add(cfg.get("min_ratio", ""))
      attrs.add(cfg.get("max_ratio", ""))
      attrs.add(cfg.get("dynamic_weight", ""))
      attrs.add(cfg.get("user_xtr", ""))
      attrs.add(cfg.get("rank_cliff_attr", ""))
      attrs.add(cfg.get("rank_cliff_ratio_attr", ""))
      attrs.add(cfg.get("rank_cliff_min_attr", ""))
      attrs.add(cfg.get("use_new_pow_func", ""))
      attrs.add(cfg.get("new_pow_func_coeff", ""))
      attrs.add(cfg.get("new_pos_func_bias", ""))
      attrs.add(cfg.get("score_threshold", ""))
      attrs.add(cfg.get("skip_diff_judge", ""))
      attrs.add(cfg.get("queue_pow_t", ""))
      attrs.add(cfg.get("rank_height_attr", ""))
      attrs.add(cfg.get("raw_score_normalize_alpha_attr", ""))
      attrs.add(cfg.get("smooth_attr", ""))
      attrs.add(cfg.get("raw_bias_attr", ""))
      attrs.add(cfg.get("value_seq_fusion_status_attr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_power_weight_attr", ""))
      attrs.add(cfg.get("queue_head_boost_coef", ""))
      attrs.add(cfg.get("queue_tail_discount_coef", ""))
      attrs.add(cfg.get("enable_power_weight_adjust_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_thresh_attr", ""))
      attrs.add(cfg.get("power_weight_adjust_score_thresh_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    attrs.add(self._config.get("save_ori_ensemble_score_to_attr"))
    attrs.add(self._config.get("save_absolute_score_to_attr"))
    return attrs

class ExplorePicRerankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_rerank"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("unify_rank_mode")))
    attrs.update(self.extract_dynamic_params(self._config.get("unify_queue_calc_mode")))
    attrs.update(self.extract_dynamic_params(self._config.get("valid_rank_mode")))
    attrs.update(self.extract_dynamic_params(self._config.get("queue_max_raw_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("queue_min_raw_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("queue_max_rank_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("queue_min_rank_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_normalization_item_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_2sigma_overall_ori_pxtr")))
    attrs.update(self.extract_dynamic_params(self._config.get("valid_rank_exclude_single_pic")))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("raw_score_b_attr", ""))
      attrs.add(cfg.get("raw_score_k_attr", ""))
      attrs.add(cfg.get("raw_score_p_attr", ""))
      attrs.add(cfg.get("pow_alpha_attr", ""))
      attrs.add(cfg.get("pow_beta_attr", ""))
      attrs.add(cfg.get("pow_bias_attr", ""))
      attrs.add(cfg.get("use_exp_base_attr", ""))
      attrs.add(cfg.get("func_mode_attr", ""))
      attrs.add(cfg.get("enable_norm_attr", ""))
      attrs.add(cfg.get("pow_raw_score_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_bias_attr", ""))
      attrs.add(cfg.get("raw_score_min_val_attr", ""))
      attrs.add(cfg.get("raw_score_max_val_attr", ""))
      attrs.add(cfg.get("rank_temperature_attr", ""))
      attrs.add(cfg.get("rank_smooth_attr", ""))
      attrs.add(cfg.get("rank_base_number_attr", ""))
      attrs.add(cfg.get("rank_weight_attr", ""))
      attrs.add(cfg.get("valid_rank_smooth_attr", ""))
      attrs.add(cfg.get("valid_ori_score_attr", ""))
      attrs.add(cfg.get("valid_rank_weight_attr", ""))
      attrs.add(cfg.get("spec_rank_mode_attr", ""))
      attrs.update(self.extract_dynamic_params(cfg.get("reverse_value")))
      attrs.update(self.extract_dynamic_params(cfg.get("reverse_order")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("picture_attr"))
    attrs.add(self._config.get("picture_type_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreChannelSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_channel_sort"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("input_count_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("output_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("channel_queue_names")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_double_lowest_score")))
    if 'queue_weight_attrs' in self._config.keys():
      attrs.update(self._config['queue_weight_attrs'])
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("queue_score_attrs"))
    attrs.update(self._config.get("queue_flag_attrs"))
    if 'queue_score_attrs' in self._config.keys():
      attrs.update(self._config['queue_score_attrs'])
    if 'queue_flag_attrs' in self._config.keys():
      attrs.update(self._config['queue_flag_attrs'])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class ExploreMixRerankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_mix_rerank"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("sim_score_weight")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_sim_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("sim_decay_rate")))
    attrs.update(self.extract_dynamic_params(self._config.get("queue_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("top_slot")))
    attrs.update(self.extract_dynamic_params(self._config.get("min_gap")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_gap_decay")))
    attrs.update(self.extract_dynamic_params(self._config.get("gap_decay_rate")))
    for cfg in self._config.get("sim_configs"):
      attrs.add(cfg.get("weight_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ensemble_score_attr"))
    attrs.add(self._config.get("picture_attr"))
    for cfg in self._config.get("sim_configs"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))

class ExploreUserPersonaSortArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_persona_sorter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_emb_key"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_emb_key"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs


class ExploreClusterTruncateArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_truncate_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["truncate_ratio", "min_reserved_percentile", "truncate_min_size", "cluster_truncate_ratio_map_str", "skip_reserved_clusters_str"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("cluster_name"))
    return attrs

class ExploreDppSetArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_dpp_set_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["dpp_max_num", "beam_size", "rank_theta", "dm_epsilon", "enable_only_max_dpp_score_list",
                "enable_norm_sim_matrix", "sim_matrix_combo_alpha", "sim_matrix_norm_type"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mmu_emb_attr_name"))
    attrs.add(self._config.get("mc_emb_attr_name"))
    return attrs

class ExploreControlHetuCountArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_control_hetu_count_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["keep_size", "enable_hetu_control_interest", "enable_hetu_control_diversity",
                 "enable_duration_control_diversity", "enable_author_control_diversity", "hetu_control_interest_start",
                 "hetu_control_diversity_start", "duration_control_diversity_start", "author_control_diversity_start",
                 "hetu1_max_size", "hetu2_max_size", "hetu5_max_size",
                 "duration_0_7s_max_size", "duration_7_9s_max_size", "duration_9_12s_max_size",
                 "duration_12_17s_max_size", "duration_17_20s_max_size", "duration_20_58s_max_size", "duration_58_120s_max_size", "duration_120s_inf_max_size", "same_author_max_size",
                 "enable_hetu_diversity_control", "diversity_control_smooth", "diversity_control_alpha",
                 "enable_actual_hetu_control", "hetu_adjust_coef", "hetu_adjust_min_value", "hetu_adjust_max_value",
                 "enable_dynamic_hetu_control_diversity", "dynamic_hetu_control_diversity_coeff", "enable_dynamic_hetu_control_diversity_level_one",
                 "enable_dynamic_hetu_control_diversity_level_two", "enable_dynamic_hetu_control_diversity_v2", "enable_adjust_quota_by_avg_reward_coeff",
                 "enable_dynamic_hetu_control_diversity_normal", "enable_cluster_id_control_diversity",
                 "cluster_id_control_diversity_start", "cluster_id_max_size", "enable_hetu_control_diversity_none_hetu",
                 "none_hetu1_max_size", "none_hetu2_max_size", "none_hetu5_max_size"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    attrs.add(self._config.get("user_hetu_distribution_attr"))
    attrs.add(self._config.get("user_actual_distribution_attr"))
    attrs.add(self._config.get("avg_reward_coeff_hetu_stat_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    attrs.add(self._config.get("hetu_level_two_attr"))
    attrs.add(self._config.get("hetu_level_five_attr"))
    attrs.add(self._config.get("duration_ms_attr"))
    attrs.add(self._config.get("author_attr"))
    attrs.add(self._config.get("cluster_id_attr"))
    return attrs

class ExploreControlSimilarityScoreArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_control_similarity_score_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["max_satisfied_pick", "similarity_score_max_threshold"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("common_similarity_pid_list_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_similarity_score_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_similarity_size_attr"))
    return attrs

class ExploreRetrievalReasonLimitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retr_reason_limit"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["size_limit", "default_weight", "reason_weight_str", "skip_personal_weight", "skip_fillback_result", "reason_whitelist_str", "skip_fillback_reason_str"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["personal_weight_map_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreValueAndRankScoreArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_value_and_rank_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["es_rank_power", "es_value_power", "need_rank_queue_names",
                 "need_value_queue_names", "pctr_queue_name", "pfsh_queue_name",
                 "pct_queue_name", "pdlk_queue_name", "multiply_ctr_queue_names",
                 "enable_queue_names", "update_alpha_queue_names_list"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("rank_alpha", ""))
      attrs.add(cfg.get("rank_beta", ""))
      attrs.add(cfg.get("rank_smooth", ""))
      attrs.add(cfg.get("value_beta", ""))
      attrs.add(cfg.get("value_alpha", ""))
      attrs.add(cfg.get("value_omega", ""))
      attrs.add(cfg.get("reverse_order", ""))
      attrs.add(cfg.get("linear_continuous_multiplication", ""))
      attrs.add(cfg.get("expand_alpha_scope", ""))
      attrs.add(cfg.get("es_vrs_alpha", ""))
      attrs.add(cfg.get("es_vrs_beta", ""))
      attrs.add(cfg.get("es_vrs_omega", ""))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_open_update_alpha_handle")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("save_es_queue_score_to_attr", ""))
    return attrs

class ExploreMemoryDataPtrFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_memory_data_ptr_filter"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("memory_data_ptr_attr"))
    return attrs

class ExploreRetrievalQuotaLimitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retr_quota_limit"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("limit_num")))
    for group in self._config.get("quota_groups"):
      attrs.update(self.extract_dynamic_params(group.get("quota_num")))
      attrs.update(self.extract_dynamic_params(group.get("quota_weight")))
    return attrs

class ExploreSnakeMergeArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_snake_merge"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("max_item_num")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs