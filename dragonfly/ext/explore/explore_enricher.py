#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_enricher.py
description:
author: huze<PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ...common_leaf_util import strict_types, check_arg, extract_attr_names, gen_attr_name_with_common_attr_channel, try_add_table_name
from ...common_leaf_processor import LeafEnricher

class ExploreControlHetuCountEnricher(LeafEnricher):
  """
  ExploreControlHetuCountEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_control_hetu_count_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["keep_size", "enable_hetu_control_interest", "enable_hetu_control_diversity", "enable_cluster_id_control_diversity",
                 "enable_minority_control_diversity",
                 "enable_duration_control_diversity", "hetu_control_interest_start", "cluster_id_control_diversity_start",
                 "hetu_control_diversity_start", "duration_control_diversity_start",
                 "hetu1_max_size", "hetu2_max_size", "hetu5_max_size", "cluster_id_max_size",
                 "duration_0s_max_size", "old_cluster_id_interest_list_attr", "old_cluster_id_interest_coef",
                 "duration_0_7s_max_size", "duration_7_9s_max_size", "duration_9_12s_max_size",
                 "duration_12_17s_max_size", "duration_17_20s_max_size", "minority_max_size"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    attrs.add(self._config.get("hetu_level_two_attr"))
    attrs.add(self._config.get("hetu_level_five_attr"))
    attrs.add(self._config.get("duration_ms_attr"))
    attrs.add(self._config.get("cluster_id_attr"))
    attrs.add(self._config.get("is_minority_photo_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_is_degraded_common_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_flag_to_attr"))
    return attrs

class ExplorePhotoDistributionAdjustEnricher(LeafEnricher):
  """
  ExplorePhotoDistributionAdjustEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_photo_distribution_adjust_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_total_count_attr"))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    attrs.add(self._config.get("global_hetu_distribution_use_fountain_flag"))
    attrs.add(self._config.get("latest_global_hetu_distribution_attr"))
    for name in ["colossus_total_count_threshold", "global_fuse_corr", "max_count", "enable_daily_update_global_distribution",
                 "global_hetu_stat_redis_key_prefix"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("candidate_hetu_adjust_coeff_map_attr"))
    return attrs

class ExplorePhotoDistributionColossusStatEnricher(LeafEnricher):
  """
  ExplorePhotoDistributionColossusStatEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_photo_distribution_colossus_stat_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    for name in ["enable_only_hot_stat", "enable_only_fountain_stat", "enable_only_positive_stat", "interest_stat_use_reward",
                 "interest_stat_vv_weight", "interest_stat_reward_weight", "interest_stat_avg_reward_smooth",
                 "enable_interest_stat_avg_reward", "minus_hate_stat_coeff", "minus_sv_stat_coeff", "enable_use_actual_reward", "max_history_size",
                 "enable_interest_stat_use_true_feedback", "enable_user_hetu_entropy", "enable_avg_reward_coeff_hetu_stat", "enable_user_hetu1_distribution",
                 "avg_reward_base_idx_ratio_level1", "avg_reward_base_idx_ratio_level2", "avg_reward_coeff_pow_weight_level1", "avg_reward_coeff_pow_weight_level2",
                 "avg_reward_coeff_upper_bound_level1", "avg_reward_coeff_upper_bound_level2", "avg_reward_coeff_lower_bound_level1", "avg_reward_coeff_lower_bound_level2",
                 "avg_reward_coeff_count_pow_weight_level1", "avg_reward_coeff_count_pow_weight_level2", "avg_reward_coeff_count_thres_level1",
                 "avg_reward_coeff_count_thres_level2"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_total_count"))
    attrs.add(self._config.get("save_user_hetu_stat_attr"))
    attrs.add(self._config.get("save_user_hetu1_distribution_attr"))
    attrs.add(self._config.get("save_user_hetu_entropy_attr"))
    attrs.add(self._config.get("save_actual_hetu_stat_attr"))
    attrs.add(self._config.get("save_system_hetu_stat_attr"))
    attrs.add(self._config.get("save_avg_reward_coeff_hetu_stat_attr"))
    return attrs

class ExploreColossusTopSvtrHetuEnricher(LeafEnricher):
  """
  ExploreColossusTopSvtrHetuEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_top_svtr_hetu_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("short_interest_reward_weights_attr"))
    attrs.add(self._config.get("get_short_interest_attr"))
    attrs.add(self._config.get("long_interest_reward_weights_attr"))
    attrs.add(self._config.get("explore_interest_reward_weights_attr"))
    for name in ["enable_top_sv_hetu2", "enable_stat_top_sv_only_fountain", "top_sv_stat_max_show",
                 "enable_top_sv_stat_use_rate", "top_sv_stat_default_svtr", "top_sv_stat_base_show",
                 "enable_stat_short_interest", "enable_stat_long_interest", "enable_stat_short_interest_only_explore_fountain", "short_interest_max_hours",
                 "enable_interest_use_hetu1", "play_time_slope", "play_time_max", "enable_stat_long_interest_only_explore_fountain",
                 "long_interest_max_days", "long_interest_min_days", "long_interest_min_play_time", "short_interest_reward_lower_bound",
                 "short_interest_num", "long_interest_reward_lower_bound", "long_interest_num", "enable_interest_reward_use_rate",
                 "enable_stat_explore_interest", "explore_interest_num", "explore_interest_reward_lower_bound",
                 ]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_user_top_sv_hetu_attr"))
    attrs.add(self._config.get("save_short_interest_attr"))
    attrs.add(self._config.get("save_long_interest_attr"))
    return attrs

class ExploreMixUserInterestStatEnricher(LeafEnricher):
  """
  ExploreMixUserInterestStatEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_mix_user_interest_stat_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("global_hetu_stat_attr"))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    for name in ["enable_debias_with_global_stat", "enable_debias_multipy_original_stat", "global_hetu_stat_redis_key_prefix", "global_fuse_corr"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_user_mixed_hetu_stat_attr"))
    return attrs

class ExploreXtrDebiasV3Enricher(LeafEnricher):
  """
  ExploreXtrDebiasV3Enricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_xtr_debias_v3_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["dura_bucket_width", "enable_picture_xtr_debias"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("dura_xtr_debias_map_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("debias_type", ""))
      attrs.add(cfg.get("dura_factors", ""))
      attrs.add(cfg.get("freq_factors",""))
      attrs.add(cfg.get("alpha", ""))
      attrs.add(cfg.get("beta", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("input_attr", ""))
    attrs.add(cfg.get("duration_attr"))
    attrs.add(cfg.get("picture_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("output_attr", ""))
    return attrs

class ExploreUserTopWtPidsEnricher(LeafEnricher):
  """
  ExploreUserTopWtPidsEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_gen_user_top_wt_pids"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["colossus_resp_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["top_wt_item_size", "min_dura", "min_wt", "max_seconds_ago", "min_seconds_ago"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
      attrs = set()
      if "output_common_attr" in self._config:
          attrs.add(self._config.get("output_common_attr"))
      return attrs

class CalcLongTermInterstEEScoreEnricher(LeafEnricher):
    """
    CalcLongTermInterstEEScoreEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "calc_long_term_interest_ee_score"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["user_info_pb_name", "hetu_attrs"]:
          if name in self._config:
              attrs.add(self._config.get(name))
        for name in ["boost_threshold", "enable_division_way", "photo_hetu_tag_level_info_type",
          "enable_click_history", "enable_like_history", "enable_follow_history", "enable_long_view_history",
          "long_view_threshold"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @strict_types
    def depend_on_items(self) -> bool:
      return False

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      for name in ["export_item_attr"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs


class GenRealtimeBrowseSetEnricher(LeafEnricher):
    """
    GenRealtimeBrowseSetEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "gen_realtime_browse_set"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["realtime_hot_bs_size", "realtime_fountain_bs_size", "enable_hot_click_list",
          "enable_fountain_browse", "enable_fix_real_show_list", "enable_hot_browse", "profile_time_threshold"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        if "user_info_ptr_attr" in self._config:
            attrs.add(self._config.get("user_info_ptr_attr"))
        return attrs

    @strict_types
    def depend_on_items(self) -> bool:
      return False

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attrs = set()
        if "output_common_attr" in self._config:
            attrs.add(self._config.get("output_common_attr"))
        if "output_click_common_attr" in self._config:
          attrs.add(self._config.get("output_click_common_attr", "explore_click_list"))
        if "output_timestamp_common_attr" in self._config:
          attrs.add(self._config.get("output_timestamp_common_attr", "explore_output_timestamp_list"))
        if "output_hetu_five_common_attr" in self._config:
          attrs.add(self._config.get("output_hetu_five_common_attr", "explore_output_hetu_five_list"))
        return attrs

class ExploreUninterestTagExitEnricher(LeafEnricher):
  """
  ExploreUninterestTagExitEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_uninterest_tag_exit"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      attrs.add(prev_item_from_attr)
    prev_item_timestamp_from_attr = self._config.get("prev_item_from_attr_timestamp")
    if prev_item_timestamp_from_attr:
      attrs.add(prev_item_timestamp_from_attr)
    prev_click_item_from_attr = self._config.get("prev_click_item_from_attr")
    if prev_click_item_from_attr:
      attrs.add(prev_click_item_from_attr)
    interest_list_attr = self._config.get("interest_list_attr")
    if interest_list_attr:
      attrs.add(interest_list_attr)
    for name in ["realshow_num_threshold", "time_window", "calculate_mode", "hetu_classification_mode",
        "discount_coef", "realshow_unclick_num", "personalized_step_size_str",
        "debias_str", "unbias_weight_str", "enable_new_interest_discount",
        "debias_decline_rate_cid862_topclass_str", "debias_debias_ratio_cid862_topclass_str",
        "debias_base_debias_value_cid862_topclass_str"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    attrs.add(self._config.get("cluster_id_attr", "hetu_sim_cluster_id"))
    attrs.add(self._config.get("cluster_cid862_topclass_attr", "hetu_sim_cluster_id862_lv1"))
    attrs.add(self._config.get("hetu_level_five_attr", "hetu_tag_level_info__hetu_level_five"))
    attrs.add(self._config.get("input_pctr_attr", "debias_pctr"))
    prev_attrs.add(self._config.get("cluster_id_attr", "hetu_sim_cluster_id"))
    prev_attrs.add(self._config.get("hetu_level_five_attr", "hetu_tag_level_info__hetu_level_five"))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_pctr_attr", "debias_pctr")
    if attr:
      attrs.add(attr)
    return attrs

class ExploreRealshowEmctrUnbiasEnricher(LeafEnricher):
  """
  ExploreRealshowEmctrUnbiasEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_realshow_emctr_unbias"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      attrs.add(prev_item_from_attr)
    prev_click_item_from_attr = self._config.get("prev_click_item_from_attr")
    if prev_click_item_from_attr:
      attrs.add(prev_click_item_from_attr)
    for name in ["discount_coef", "boost_coef", "realshow_threshold", "click_threshold",
        "cid_click_threshold"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    attrs.add(self._config.get("cluster_id_attr"))
    attrs.add(self._config.get("input_pctr_attr"))
    prev_attrs.add(self._config.get("cluster_id_attr"))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_pctr_attr")
    if attr:
      attrs.add(attr)
    return attrs

class ExploreShortUninterestTaggerEnricher(LeafEnricher):
  """
  ExploreShortUninterestTaggerEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_short_uninterest_tagger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["prev_item_from_attr", "prev_item_from_attr_timestamp", "prev_click_item_from_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["realshow_num_threshold", "time_window", "realshow_no_click_threshold",
                 "enable_cluster_id", "enable_hetu_level_five"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    for name in ["cluster_id_attr", "hetu_level_five_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
        prev_attrs.add(self._config.get(name))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["output_short_uninterest_cid_stat_attr", "output_short_uninterest_hetu5_stat_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("output_short_uninterest_flag_attr")
    if attr:
      attrs.add(attr)
    return attrs

class ExploreGamoraFountainInterestTaggerEnricher(LeafEnricher):
  """
  ExploreGamoraFountainInterestTaggerEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_gamora_fountain_interest_tagger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_info_ptr_attr","filter_hot_hetu2_list_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["gamora_num_threshold", "fountain_num_threshold", "explore_num_threshold",
                 "gamora_realshow_num_threshold","fountain_realshow_num_threshold",
                 "explore_realshow_ratio_threshold",
                 "enable_gamora_interest", "enable_fountain_interest"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["hetu_level_two_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["output_gamora_interest_flag_attr", "output_fountain_interest_flag_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["output_gamora_interest_hetu2_list_attr", "output_fountain_interest_hetu2_list_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs


class ExploreCustomRuleClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_by_custom_rule"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_user_profile_top_hetu_level_one_cluster", "enable_user_profile_top_hetu_level_two_cluster",
        "user_profile_tag_score_limit", "user_profile_limit_num", "enable_frequency_duration_cluster",
        "use_extra_page", "enable_colossus_top_hetu_one_cluster", "enable_colossus_top_hetu_two_cluster",
        "colossus_top_hetu_level_two_attr", "colossus_top_hetu_level_one_attr", "variant_queue_num",
        "enable_use_real_show_list", "enable_use_click_list", "enable_use_like_list", "enable_use_follow_list",
        "enable_use_forward_list", "enable_use_collect_list", "enable_use_comment_list", "enable_user_video_play_stats",
        "comment_weight", "collect_weight", "video_play_weight", "min_effective_play_length",
        "real_show_weight", "click_weight", "like_weight", "follow_weight", "forward_weight",
        "enable_use_fountain_real_show_list", "enable_use_fountain_click_list", "enable_use_fountain_like_list",
        "enable_use_fountain_follow_list", "enable_use_fountain_forward_list", "fountain_real_show_weight",
        "fountain_click_weight", "fountain_like_weight", "fountain_follow_weight", "fountain_forward_weight",
        "enable_colossus_cluster", "enable_white_author_bucket", "enable_mc_explore_cluster", "hetu_level_two_attr",
        "age_cluster_attr", "enable_use_photo_age_cluster", "mc_age_gap_str", "enable_mc_interact_cluster",
        "enable_user_profile_top_hetu_level_three_cluster", "enable_expired_time_on_action_list",
        "expired_gap_second", "enable_mc_use_realshow_no_click", "enable_uninterested_hetu_level_one_cluster",
        "enable_uninterested_hetu_level_two_cluster", "enable_uninterested_hetu_level_three_cluster", "uninterested_tag_score_limit",
        "uninterested_limit_num", "real_show_no_click_weight", "uninterested_expired_gap_second", "enable_mc_use_xhs_tag",
        "enable_rough_default_cluster","enable_mc_follow_author_cluster", "enable_mc_explore_cluster_mix", "enable_get_default_hetu_level_one_cluster",
        "enable_get_default_hetu_level_three_cluster", "enable_get_default_hetu_level_two_cluster", "enable_set_bucket_limit_num_by_ratio",
        "mc_realtime_bucket_limit_num_ratio", "enable_mc_empctr_cluster", "enable_shortterm_interest_cluster_opt",
        "enable_ignore_profile_candidate_limit_cut", "enable_mc_follow_author_cluster_first",
        "enable_mc_cluster_862_cluster", "enable_mc_cluster_862_one_cluster", "mc_user_interest_cluster_862_count",
        "enable_mc_cluster_862_uninterest_cluster", "enable_mc_cluster_862_cluster_ignore_recent_realshow", "mc_user_uninterest_cluster_862_count",
        "enable_mc_cluster_862_uninterest_cluster_by_u2c", "enable_mc_filter_u2c_score_by_cluster_862_lv1",
        "enable_mc_unbias_interest_cluster", "mc_user_unbias_interest_cluster_count","enable_mc_unbias_interest_hetu_cluster",
        "mc_user_unbias_interest_hetu_cluster_count", "enable_mc_cluster_862_uninterest_cluster_impression_filter",
        "enable_mc_cluster_862_uninterest_cluster_cover_filter", "enable_mc_cluster_hot_list_cluster",
        "enable_interest_vary_by_scenario", "gamora_interest_ratio", "enable_mc_all_page_valid_interest_cluster",
        "mc_all_page_valid_interest_cluster_count"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    for name in ["input_colossus_attr_one", "input_colossus_attr_two", "input_colossus_attr_three", "input_colossus_attr_explore",
        "input_colossus_attr_interact", "input_xhs_hetu_tags_attr", "shortterm_hetu_attr", "input_user_interest_cluster_862_attr",
        "input_user_recent_realshow_cluster_862_attr", "input_user_cluster862_sorted_list_attr", "filter_u2c_score_by_cluster_862_lv1_classes_attr",
        "input_user_unbias_interest_cluster_attr","input_user_unbias_interest_hetu_cluster_attr", "input_user_all_page_valid_interest_tags_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["hetu_tag_level_info__hetu_level_one", "hetu_tag_level_info__hetu_level_two", "hetu_tag_level_info__hetu_level_three",
                 "duration_ms", "is_picture", "live_photo_info__is_living"])
    for name in ["white_author_attr", "hetu_level_one_attr", "hetu_level_two_attr", "hetu_level_three_attr", "hetu_level_four_attr",
      "is_follow_author_attr", "empctr_cluster_flag_attr", "cluster_862_attr", "audit_b_second_tag_attr", "audit_hot_cover_level_attr",
      "is_hot_list_flag_attr", "cluster_632_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExplorePicCalcClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_calc_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_multi_hit_independent_bucket")))
    cluster_configs = self._config.get("clusters", [])
    for cluster_config in cluster_configs:
      attrs.update(self.extract_dynamic_params(cluster_config.get("enable")))
      attrs.update(self.extract_dynamic_params(cluster_config.get("priority_num")))

      if cluster_config.get("name") == "long_term":
        attrs.add(cluster_config.get("colossus_hetu_l1_tags_attr"))
        attrs.add(cluster_config.get("enable_user_longterm_hetu_distr_attr"))
        attrs.add(cluster_config.get("user_longterm_hetu_distr_attr"))
        attrs.add(cluster_config.get("limit_num_attr"))
        attrs.add(cluster_config.get("score_thresh_attr"))
      elif cluster_config.get("name") == "privilege_tag":
        attrs.add(cluster_config.get("privilege_tags_attr"))
      elif cluster_config.get("name") == "interest_explore":
        attrs.add(cluster_config.get("interest_explore_hetu_list_attr"))
      elif cluster_config.get("name") == "short_term":
        attrs.add(cluster_config.get("realshow_pic_list_attr"))
        attrs.add(cluster_config.get("valid_play_weight_attr"))
        attrs.add(cluster_config.get("interact_weight_attr"))
        attrs.add(cluster_config.get("time_range_sec_attr"))
        attrs.add(cluster_config.get("only_pic_attr"))
        attrs.add(cluster_config.get("limit_num_attr"))
        attrs.add(cluster_config.get("score_thresh_attr"))
      elif cluster_config.get("name") == "long_interest_hetu":
        attrs.add(cluster_config.get("long_interest_hetu_ids_attr"))
        attrs.add(cluster_config.get("long_interest_hetu_scores_attr"))
        attrs.add(cluster_config.get("limit_num_attr"))
        attrs.add(cluster_config.get("score_thresh_attr"))
      elif cluster_config.get("name") in ["valid_interest_cluster", "long_interest_cluster", "search_interest_cluster"]:
        attrs.add(cluster_config.get("interest_cluster_ids_attr"))
        attrs.add(cluster_config.get("one_bucket_attr"))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()

    cluster_configs = self._config.get("clusters", [])
    for cluster_config in cluster_configs:
      if cluster_config.get("name", "") == "short_term":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "long_term":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "privilege_tag":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "interest_explore":
        attrs.add(cluster_config.get("hetu_level_one_attr"))

      elif cluster_config.get("name", "") == "long_caption":
        attrs.add(cluster_config.get("caption_length_attr"))
        attrs.add(cluster_config.get("is_xhs_type_photo_attr"))

      elif cluster_config.get("name", "") == "follow_author":
        attrs.add(cluster_config.get("is_follow_author_attr"))

      elif cluster_config.get("name", "") == "long_pic_and_pic_set":
        attrs.add(cluster_config.get("upload_type_attr"))
        attrs.add(cluster_config.get("picture_type_attr"))

      elif cluster_config.get("name", "") == "pic_cnt":
        attrs.add(cluster_config.get("picture_count_attr"))

      elif cluster_config.get("name", "") == "pic_default":
        attrs.add(cluster_config.get("upload_type_attr"))
        attrs.add(cluster_config.get("picture_type_attr"))
        attrs.add(cluster_config.get("picture_count_attr"))
      elif cluster_config.get("name", "") == "high_value_pic":
        attrs.add(cluster_config.get("high_value_pic_flag_attr"))
      elif cluster_config.get("name", "") == "long_interest_hetu":
        attrs.add(cluster_config.get("hetu_level_one_attr"))
      elif cluster_config.get("name", "") in ["valid_interest_cluster", "long_interest_cluster", "search_interest_cluster"]:
        attrs.add(cluster_config.get("cluster_id_632_attr"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

class ExploreMcDistillSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_mc_distill_sample_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["candidate_list_attr", "excludes_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["sample_num", "chunk_size", "sample_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_sample_result_to"))
    return attrs

class ExploreFullLinkContextSampleRecoLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_full_link_context_sample_reco_log_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    for name in ["sample_ratio", "load_attr", "rerank_list_item_idx_flat_list_attr", "rerank_list_score_list_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["enable_set_user_info", "size_limit", "send_rerank_eval_list"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["sample_config"]:
      if name in self._config:
        for v in self._config["sample_config"]:
          if isinstance(v, dict):
              for val in v.keys():
                  if val in ["sample_begin","sample_end","sample_num"]:
                      attrs.add(v[val])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["output_attr", "save_result_to"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["rank_index", "final_index", "pctr", "pltr", "pwtr", "pftr", "pcmtr", "pptr", "plvtr", "pvtr",
    "cascade_pctr", "cascade_pltr", "cascade_pwtr", "cascade_pftr", "cascade_pcmtr", "cascade_pptr", "cascade_plvtr", "cascade_pvtr",
    "psvr", "pepstr", "pcltr", "pwtd", "pcpr", "fullrank_ltr_score", "fullrank_act_wtd", "fullrank_ltr_v4_fountain_next", "fountain_related_score_v2"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreFullLinkDistillSampleRecoLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_full_link_distill_sample_reco_log_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    for name in ["sample_list_names"]:
      if name in self._config:
        attrs.update(self._config.get(name))
    for name in ["enable_set_user_info"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["rank_index", "final_index"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreMcDistillSampleRecoLogEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_mc_distill_sample_reco_log_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    for name in ["sample_list_names"]:
      if name in self._config:
        attrs.update(self._config.get(name))
    for name in ["send_photo_optional", "send_user_optional", "tab"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    #for name in ["sample_num", "label_name"]:
    #  attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "attr_map" in self._config:
      for k, v in self._config.get("attr_map").items():
        attrs.add(v)
    attrs.update(["duration_ms", "upload_time", "click_count", "like_count", "follow_count", "forward_count",
      "report_count", "upload_type", "click_upload_rate", "photo_dnn_cluster_id", "mmu_img_cluster_v3",
      "tag", "location", "author", "hetu_tag_level_info", "explore_stat", "content_safety_level_with_namespace",
      "author_age_info", "infer_gender", "infer_year", "mod", "music", "magic_face_id", "view_length_sum",
      "mmu_img_cluster_v1", "show_level_a", "show_level_b", "lda_topic"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_result_to"))
    return attrs

class ExploreCascadeDebiasEnricher(LeafEnricher):
    """
    ItemPredictByXgb
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_cascade_debias_enricher"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("hetu_emp_xtr_level_two"))
        attrs.add(self._config.get("hetu_emp_xtr_level_one"))
        attrs.update(self.extract_dynamic_params(self._config.get("use_hetu_level_one")))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("hetu_level_one_attr"))
        attrs.add(self._config.get("hetu_level_two_attr"))
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("original_name", ""))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("debias_name", ""))
        return attrs

class ExploreInformationRelatedScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_information_related_score_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        if 'source_hetu_attr_list' in self._config:
          attrs.update(self._config.get("source_hetu_attr_list"))
        else:
          attrs.update(["source_hetu_level_three_v2",
                        "source_hetu_level_two_v2"])

        if 'source_author_str_list' in self._config:
          attrs.update(self._config.get("source_author_str_list"))
        else:
          attrs.update(["sourcePidFourthLevelCategory", "sourcePidThirdLevelCategory"])

        attrs.add(self._config.get("source_face_id_attr", "source_hetu_face_id_v2"))
        attrs.add(self._config.get("source_hetu_tag_attr", "source_hetu_tag_v2"))
        attrs.add(self._config.get("source_cluster_id_attr", "source_hetu_cluster_id_v2"))
        attrs.add(self._config.get("information_hetu_tag_id_attr", "information_hetu_tag_id"))

        if "use_hetu_v3" in self._config:
          attrs.update(self.extract_dynamic_params(self._config.get("use_hetu_v3")))

        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        if 'target_hetu_attr_list' in self._config:
          attrs.update(self._config.get("target_hetu_attr_list"))
        else:
          attrs.update(["hetu_tag_level_info_v2.hetu_level_two",
                        "hetu_tag_level_info_v2.hetu_level_three"])

        if 'target_author_str_list' in self._config:
          attrs.update(self._config.get("target_author_str_list"))
        else:
          attrs.update(["author.category_detail.fourth_level_id", "author.category_detail.third_level_id"])

        attrs.add(self._config.get("target_face_id_attr", "hetu_tag_level_info_v2.hetu_face_id"))
        attrs.add(self._config.get("target_hetu_tag_attr", "hetu_tag_level_info_v2.hetu_tag"))
        attrs.add(self._config.get("target_cluster_id_attr", "hetu_tag_level_info_v2.hetu_cluster_id"))

        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("information_related_score"))
        return attrs

class ExploreCustomScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_custom_score_enricher"
    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return False
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("memory_data_name"))
        return attrs
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(["cascade_plvtr", "duration_ms"])
        return attrs
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("export_item_attr"))
        return attrs

class ExplorePdnTriggerWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pdn_trigger_weight_enricher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add("trigger_list")
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("request_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_layer")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_extra_user_attr")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_trigger_weight_attr"))
    return attrs

class ExploreNnUserEmbeddingEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_nn_user_embedding_enricher"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_user_emb_attr"))
    return attrs

class ExploreBrowseScreenEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_browse_screen_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("latest_screen_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("latest_screen_time_ms")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_photo_ids_to_attr"))
    attrs.add(self._config.get("save_author_ids_to_attr"))
    return attrs

class ExploreRerankAttrEnricher(LeafEnricher):
  """
  ExploreRerankAttrEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "explore_rerank_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
      attrs = set(self._config.get("user_info_attr"))
      return attrs

class ExploreUserFeatureCommonAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_feature_common_attr_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_common_attr_list_attr"))
    return attrs

class ExploreRetrUserInfoAdjustEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_copy_adjust_user_info_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_user_info_ptr_attr"))
    attrs.add(self._config.get("output_user_info_attr"))
    return attrs

class ExploreColossusClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_cluster_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "finish_rate_threshold", "colossus_day_upper", "colossus_profile_recent_hours",
      "colossus_click_weight", "colossus_play_time_weight", "tag_limit_str", "max_show_ratio_str",
      "enable_mc_explore_cluster", "mc_explore_cluster_limit", "mc_explore_cluster_score_limit",
      "enable_mc_interact_cluster", "enable_mc_explore_cluster_v2",
      "mc_explore_cluster_recent_click_count_limit", "mc_explore_cluster_click_count_limit",
      "mc_explore_cluster_click_time_limit", "enable_mc_explore_cluster_target",
      "mc_explore_cluster_target_count_limit", "mc_explore_cluster_recent_show_count_limit",
      "mc_explore_cluster_recent_click_top_ratio", "mc_explore_cluster_recent_show_top_ratio",
      "enable_longterm_interest_cluster_opt", "enable_interest_vary_by_scenario",
      "fountain_interest_ratio", "gamora_interest_ratio"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("colossus_v2_attr_name"))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("high_quality_tags_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_colossus_attr_one"))
    attrs.add(self._config.get("export_colossus_attr_two"))
    attrs.add(self._config.get("export_colossus_attr_three"))
    for name in ["export_colossus_attr_explore", "export_colossus_attr_interact"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs


class ExploreListwiseSeqFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_listwise_seq_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["seq_item_attr_name", "list_item_attr_num"]:
      if name in self._config:
        attrs.add(self._config.get(name))

    for name in ["item_attrs_transform_map"]:
      if name in self._config:
        for val in self._config.get(name):
            attrs.add(val.get("name"))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["output_attrs"]:
      if name in self._config:
        for val in self._config.get(name):
            attrs.add(val)

    return attrs

class ExploreInterestMigrationHistoryPrepareEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_interest_migration_history_prepare_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["day_upper_attr", "colossus_num_limit_attr", "realshow_num_limit_attr", "not_effective_view_weight",
                 "playtime_weight", "like_weight_attr", "follow_weight_attr", "forward_weight_attr",
                 "comment_weight_attr", "profile_weight_attr", "collection_weight_attr",
                 "hot_cnt_threshold_attr", "hot_rate_threshold_attr", "long_rate_vv_threshold_attr",
                 "vv_rate_weight_attr", "play_time_rate_weight_attr", "active_rate_weight_attr",
                 "enable_collection_list_attr", "bs_short_view_mins_upper", "bs_short_view_num_upper"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["colossus_v2_attr_name", "user_info_ptr_name", "ignore_channel_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["output_id_list_name", "output_score_list_name", "output_realshow_list_name", "output_is_degraded_flag_name",
                 "output_user_page_prefer_score_name", "bs_short_view_output_id_list_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreInterestMigrationCoefCalculatorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_interest_migration_coef_calculator_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["gamora_score_threshold", "migration_threshold", "migration_coef"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["filter_by_cluster_lv1_classes_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.add(self._config.get("explore_realshow_pids_attr"))
    attrs.add(self._config.get("gamora_play_pids_attr"))
    attrs.add(self._config.get("gamora_play_scores_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    for name in ["cluster_id_lv1_attr", "cluster_id_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ["cluster_id_attr"]:
      if name in self._config:
        prev_attrs.add(self._config.get(name))
    prev_item_from_attr = self._config.get("gamora_play_pids_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    realshow_item_from_attr = self._config.get("explore_realshow_pids_attr")
    if realshow_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, realshow_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_coef_attr"))
    for name in ["output_hot_rate_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreUserInterestCocoonEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_interest_cocoon_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["day_upper", "colossus_num_limit", "realshow_action_limit",
                 "click_action_limit", "vv_in_interest_explore_freq",
                 "real_show_rate_threshold", "click_rate_threshold",
                 "user_vv_calculate_type", "user_cocoon_calculate_type",
                 "consume_concentration_threshold", "show_concentration_threshold",
                 "cocoon_active_day_threshold", "cocoon_consider_show_concentration"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["colossus_v2_attr_name", "user_info_ptr_name", "user_valid_avg_vv_name", "user_cocoon_code_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["output_user_vv_type", "output_user_cocoon_type"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreCalcUserXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_user_xtr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("realtime_ctr_attr"))
    attrs.add(self._config.get("realtime_ltr_attr"))
    attrs.add(self._config.get("realtime_wtr_attr"))
    attrs.add(self._config.get("realtime_ftr_attr"))
    attrs.add(self._config.get("realtime_cltr_attr"))
    return attrs

class ExploreFillAvgXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_fill_avg_xtr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("avg_cltr_attr"))
    return attrs

class ExploreEnvironmentTypeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_environment_type_enrich"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_type_to_attr"))
    return attrs

class ListwiseItemRerankEnricher(LeafEnricher):
    """
    ListwiseItemRerankEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "list_wise_item_rerank"

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in ["seq_score_attr_name", "seq_item_attr_name"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["seq_item_attr_name"]:
            if name in self._config:
                attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attr = set()
        attr.add(self._config.get("seq_reason", "retrieval_list_keys_10"))
        return attr


class ExploreListItemPredictEnricher(LeafEnricher):
    """
    ExploreListItemPredictEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_list_item_predict"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["extra_common_attrs"]:
            if name in self._config:
                for common_attr in self._config.get(name):
                    attrs.add(common_attr)
        for name in ["kess_service", "origin_seq_reason", "timeout_ms",
                    "layer_name", "shard_num", "use_odd_score"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["output_attr", "output_list_attr"]:
            attrs.add(self._config.get(name))
        return attrs

class ExploreListwiseAttrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_listwise_attr"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      name_list = ["seq_item_attr_name", "item_list_from_attr", "hetu_level_one_attr", "hetu_level_two_attr", "duration_attr",
              "hetu_level_one_count", "hetu_level_two_count", "duration_0_9s_num_attr", "duration_9_15s_num_attr",
              "duration_15_20s_num_attr", "duration_20_58s_num_attr", "duration_gt_58s_num_attr", "avg_duration_attr"]
      for name in name_list:
        if name in self._config:
          attrs.add(self._config.get(name))
      for name in ["enable_context_attr", "use_attr_value_type"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

class ExploreListwiseScoreEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_listwise_score_enricher"

    @property
    @strict_types
    def input_common_attrs(self, **kwargs):
      attrs = set()
      for name in ["loss_name", "topk_num", "pxtr_weight"]:
        if name in self._config:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(self._config.get("item_attrs", []))
        for name in ["pxtr_attr", "seq_item_attr_name"]:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["output_attr"]:
            attrs.add(self._config.get(name))
        return attrs

class ExploreMdpGenListEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_mdp_gen_list_enricher"

    @property
    @strict_types
    def input_common_attrs(self, **kwargs):
      attrs = set()
      for name in ["candidate_size", "output_len", "pnext_alpha", "pnext_beta", "beam_size", "value_mean"]:
        if name in self._config:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      for name in ["item_value_attr_name", "item_next_attr_name"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      for name in ["output_attr"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      for name in ["item_value_attr_name", "item_next_attr_name"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

class ExploreModelGreedyGenListEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_model_greedy_gen_list_enricher"

    @property
    @strict_types
    def input_common_attrs(self, **kwargs):
      attrs = set()
      for name in ["use_beamsearch", "pnext_alpha", "pnext_beta", "beam_size", "use_watchtime_predict",
                  "use_watchtime_duration_trans", "source_pid_attr"]:
        if name in self._config:
          attrs.update(self.extract_dynamic_params(self._config.get(name)))
      if "extra_pxtr_attr" in self._config:
        for cfg in self._config.get("extra_pxtr_attr"):
          attrs.add(cfg.get("weight_attr", ""))
      for name in ["seq_item_attr_name", "loss_name", "value_weight", "next_weight", "watchtime_weight"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set(self._config.get("item_attrs", []))
        # for name in ["duration_ms_attr"]:
        #   if name in self._config:
        #     attrs.add(self._config.get(name))
        if "extra_pxtr_attr" in self._config:
          for cfg in self._config.get("extra_pxtr_attr"):
            attrs.add(cfg.get("pxtr_attr", ""))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      for name in ["output_attr"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      for name in ["output_score_attr"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

class ExploreCommonUserFeatureEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_common_user_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set(["user_info_attr", "effect_playtime_thresh_s", "user_ft_realtime_count_time_threshold"])
      attrs.add(self._config.get("global_click_max_len_attr"))
      attrs.add(self._config.get("hot_click_max_len_attr"))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      name_list = ["user_click_pids_attr", "user_app_attr","user_upload_rate_attr", "user_true_new_attr", "user_login_attr", "user_gender_attr",
                  "user_infer_gender_attr", "user_ture_gender_attr", "user_basic_gender_attr","user_infer_year_attr", "user_true_year_attr",
                  "user_basic_age_attr","user_city_level_attr", "user_is_douyin_attr", "user_like_pids_attr", "user_follow_pids_attr",
                  "user_uid_attr", "user_did_attr", "user_province_attr", "user_city_attr", "user_visit_mod_attr","user_visit_net_attr",
                  "user_fans_cnt_attr", "user_follow_cnt_attr", "user_upload_cnt_attr", "user_risk_level_attr","user_click_pids_v1_attr",
                  "user_click_pids_hetu1_attr", "user_click_pids_hetu2_attr", "user_like_pids_hetu1_attr", "user_like_pids_hetu2_attr",
                  "user_follow_pids_hetu1_attr", "user_follow_pids_hetu2_attr", "user_view_pids_attr", "user_view_aids_attr",
                  "user_effective_view_label_attr", "user_long_view_label_attr", "user_short_view_label_attr", "user_view_hetu1_attr",
                  "user_view_hetu2_attr", "user_no_action_pid_attr", "user_no_action_aid_attr", "user_no_action_hetu1_attr", "user_no_action_hetu2_attr",
                  "user_no_action_hetu3_attr", "user_no_action_hetu_tag_attr", "user_app_cate1_orig_attr", "user_pic_click_pids_attr",
                  "user_pic_like_pids_attr", "user_pic_follow_pids_attr", "user_video_play_list_attr", "user_video_play_aid_list_attr",
                  "user_video_play_tag_list_attr", "user_video_play_ts_list_attr", "user_video_play_time_list_attr", "user_pic_play_list_attr",
                  "user_pic_play_aid_list_attr", "user_pic_play_tag_list_attr", "user_pic_play_ts_list_attr", "user_pic_play_time_list_attr",
                  "user_no_action_hetu3_attr", "user_no_action_hetu_tag_attr","user_infer_gender_int_attr", "user_true_gender_int_attr",
                  "user_infer_year_int_attr", "user_true_year_int_attr", "user_client_id_attr", "user_visit_channel_attr", "context_hour_of_day_attr",
                  "context_day_of_week_attr", "user_ori_province_attr", "user_ori_city_attr", "user_freq_province_attr", "user_freq_city_attr",
                  "user_geo_info_attr", "user_request_poi_attr", "user_request_poi_type_attr", "user_request_town_attr", "user_request_city_level_attr",
                  "user_request_community_type_attr", "user_region_attr", "user_freq_region_attr", "user_exp_follow_count_attr", "user_exp_ctr_attr",
                  "user_exp_ltr_attr", "user_exp_wtr_attr", "user_exp_ftr_attr", "user_exp_lvtr_attr", "user_exp_svtr_attr", "user_exp_avg_watchtime_attr",
                  "user_profilev1_click_pids_attr", "user_profilev1_click_aids_attr", "user_profilev1_like_pids_attr", "user_profilev1_like_aids_attr",
                  "user_profilev1_follow_pids_attr", "user_profilev1_follow_aids_attr", "user_profilev1_comment_pids_attr", "user_profilev1_comment_aids_attr",
                  "user_profilev1_profile_enter_pids_attr", "user_profilev1_profile_enter_aids_attr", "user_profilev1_play7s_pids_attr",
                  "user_profilev1_play7s_aids_attr", "user_profilev1_play18s_pids_attr", "user_profilev1_play18s_aids_attr", "user_longterm_hetu_level1_attr",
                  "user_longterm_hetu_level2_attr", "user_app_cate1_attr", "user_app_cate2_attr", "user_app_cate3_attr", "user_app_norm_name_attr",
                  "user_pic_play_count", "user_photo_play_count", "user_pic_eff_play_count", "user_video_eff_play_count",
                  "user_short_view_pids_attr", "user_short_view_aids_attr","user_effective_view_pids_attr", "user_effective_view_aids_attr",
                  "user_long_view_pids_attr", "user_long_view_aids_attr","user_finish_view_hetu1_attr", "user_finish_view_hetu2_attr",
                  "user_finish_view_pids_attr", "user_finish_view_aids_attr", "user_non_finish_view_pids_attr", "user_non_finish_view_aids_attr",
                  "user_click_cnt_attr", "user_like_cnt_attr", "user_forward_cnt_attr", "user_low_active_attr", "user_exp_click_count_attr",
                  "user_exp_like_count_attr", "user_exp_forward_count_attr", "user_exp_hate_count_attr", "user_exp_htr_attr", "hot_click_pids_attr",
                  "hot_click_aids_attr", "hot_click_hetu1_attr", "hot_click_hetu2_attr", "hot_click_len_attr",
                  "hot_like_pids_attr", "hot_like_aids_attr", "hot_follow_pids_attr", "hot_follow_aids_attr",
                  "user_ft_realtime_like_count", "user_ft_realtime_follow_count", "user_ft_realtime_forward_count", "user_ft_realtime_comment_count",
                  "user_ft_realtime_short_view_count", "user_ft_realtime_long_view_count", "user_ft_realtime_effective_view_count", "user_ft_realtime_finish_view_count",
                  "hot_comment_pids_attr", "hot_comment_aids_attr", "hot_forward_pids_attr", "hot_forward_aids_attr", "hot_collect_pids_attr",
                  "hot_collect_aids_attr", "global_click_pids_attr", "global_click_aids_attr", "global_click_hetu1_attr", "global_click_hetu2_attr",
                  "global_click_channels_attr", "global_click_len_attr", "global_like_pids_attr", "global_like_aids_attr",
                  "global_follow_pids_attr", "global_follow_aids_attr", "global_comment_pids_attr", "global_comment_aids_attr", "global_forward_pids_attr",
                  "global_forward_aids_attr", "global_collect_pids_attr", "global_collect_aids_attr", "ft_click_pids_attr", "ft_click_aids_attr",
                  "ft_like_pids_attr", "ft_like_aids_attr", "ft_follow_pids_attr", "ft_follow_aids_attr", "ft_comment_pids_attr", "ft_comment_aids_attr",
                  "ft_forward_pids_attr", "ft_forward_aids_attr", "user_app_package_attr", "user_level_attr", "user_active_days_attr",
                  "ft_sv_pids_attr", "ft_sv_aids_attr", "ft_ev_pids_attr", "ft_ev_aids_attr", "ft_lv_pids_attr", "ft_lv_aids_attr", "hot_sv_pids_attr",
                  "hot_sv_aids_attr", "hot_lv_pids_attr", "hot_lv_aids_attr", "global_pv_pids_attr", "global_pv_aids_attr", "global_pv_duration_attr",
                  "global_pv_time_attr", "global_pv_hetu2_attr", "global_pv_channel_attr",
                  "user_high_potential_attr", "user_hot_low_active_attr", "user_age_gender_city_attr",
                  "hot_hate_pids_attr", "hot_hate_aids_attr", "global_hate_pids_attr", "global_hate_aids_attr", "ft_hate_pids_attr", "ft_hate_aids_attr",
                  "user_find_active_degree_attr", "fountain_session_realshow_attr", "user_profilev1_real_show_pids_attr", "is_xhs_user_attr",
                  "user_apps_hash_attr"]
      for name in name_list:
        if name in self._config:
          attrs.add(self._config.get(name))

      longview_common_list = []
      for i in range(30):
          for suffix in ["", "aid_", "tag_", "play_"]:
              longview_common_list.append("longview_" + suffix + str(i))
      shortview_common_list = []
      for i in range(30):
          for suffix in ["", "aid_", "tag_", "play_"]:
              shortview_common_list.append("shortview_" + suffix + str(i))
      action_cnt_common_list = []
      for key in ["uHotShow", "uHotClick", "uHotLike", "uHotFollow", "uHotHate", "uHotForward", "uHotCollect"]:
          for suffix in ["1m", "5m", "30m", "1h", "1d", "100n", "1000n"]:
              action_cnt_common_list.append(key + suffix)
      aggregation_common_attr_dict = {"user_longview_action_attr": longview_common_list, "user_shortview_action_attr": shortview_common_list,
                                      "user_count_action_attr": action_cnt_common_list}
      for name in aggregation_common_attr_dict.keys():
        if name in self._config:
          attrs.update(aggregation_common_attr_dict[name])
      return attrs

class ExploreCommonItemFeatureEnricher(LeafEnricher):
    """
    ExploreCommonItemFeatureEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_common_item_feature_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set(["user_info_attr"])
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        name_list = ["upload_time_attr", "explore_stat_real_show_count_attr", "explore_stat_click_count_attr",
                    "explore_stat_like_count_attr", "explore_stat_comment_count_attr", "explore_stat_follow_count_attr",
                    "explore_stat_view_length_sum_attr", "hetu_level_one_attr", "hetu_level_two_attr",
                    "hetu_level_three_attr", "hetu_level_four_attr", "hetu_level_five_attr", "hetu_level_tag_attr"]
        for name in name_list:
          if name in self._config:
            attrs.add(self._config.get(name))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        name_list = ["item_age_hour_attr", "item_emp_ctr_hot_attr", "item_emp_ltr_hot_attr", "item_emp_wtr_hot_attr",
                    "item_emp_cmtr_hot_attr", "item_emp_real_show_count_hot_attr", "item_emp_click_count_hot_attr",
                    "item_emp_watch_time_hot_attr", "item_channel_attr"]
        for name in name_list:
          if name in self._config:
            attrs.add(self._config.get(name))

        short_stat_list = []
        for key in ["Hetu1", "Hetu2", "Hetu3", "Hetu4", "Hetu5", "HetuTag"]:
          for suffix in ["100n", "1000n"]:
            short_stat_list.append("pShortStatShow" + key + suffix)
            short_stat_list.append("pShortStatClick" + key + suffix)
            short_stat_list.append("pShortStatClickRate" + key + suffix)
        if "short_stat_list_attr" in self._config:
          attrs.update(short_stat_list)
        return attrs

class ExploreEmbeddingCandidatesAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_embedding_candidates_attr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_realshow", "enable_not_click", "enable_play_stat", "enable_hate", "enable_like", "enable_follow",
                "enable_candidates", "enable_report",
                "enable_explore_not_click", "enable_fix_low_hit_rate", "session_history_max_size", "enable_source_photo"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name in ["user_info_ptr_attr", "source_pid_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_attr"))
    return attrs

class ExploreCustomEmbeddingScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_custom_embedding_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["dim_size", "calc_type", "not_click_weight", "short_view_weight",
                 "hate_weight", "like_weight", "follow_weight", "short_view_threshold",
                 "pos_action_limit_hour", "play_stat_limit_hour", "not_click_limit_hour",
                 "extra_not_click_weight", "enable_fountain_version", "extra_not_click_limit_hour",
                 "explore_diversity_vol_max_num", "diversity_vol_type",
                 "enable_fix_low_hit_rate", "enable_fountain_play_stat", "enable_judge_next_photo_stat",
                 "enable_explore_truncate_topk", "not_click_limit_topk", "play_stat_limit_topk",
                 "enable_explore_hetu_topk", "not_hetu_limit_topk", "not_pid_limit_topk",
                 "enable_extra_no_click_stat", "enable_avg_pooling", "source_photo_id",
                 "hate_limit_hour", "hate_weight", "hate_limit_min", "report_limit_min", "report_weight"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("embedding_list_attr"))
    attrs.add(self._config.get("source_pids_list_attr"))
    attrs.add(self._config.get("target_pids_list_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr"))
    attrs.add(self._config.get("export_item_similarity_score_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_diversity_vol_attr"))
    attrs.add(self._config.get("export_common_similarity_pid_list_attr"))
    return attrs

class ExplorePrerankTrimUserInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_prerank_trim_userinfo"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["output_user_info_ptr_attr", "output_user_info_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExplorePersonallyEnsembleWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_personally_ensemble_weight"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("matrix_weight_attr"))
    attrs.add(self._config.get("feature_vector_attr"))
    for cfg in self._config.get("feature_config"):
      attrs.add(cfg.get("fature_name", ""))
      attrs.add(cfg.get("min_value_attr", ""))
      attrs.add(cfg.get("max_value_attr", ""))
    for cfg in self._config.get("weight_config"):
      attrs.add(cfg.get("weight_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("weight_config"):
      attrs.add(cfg.get("weight_name", ""))
    return attrs

class ExplorePersonalCemWeightEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_personal_cem_weight"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("model_str_attr"))
    for cfg in self._config.get("feature_configs"):
      attrs.add(cfg.get("feature_name", ""))
      attrs.add(cfg.get("feature_attr", ""))
      attrs.add(cfg.get("treat_type", ""))
      attrs.add(cfg.get("value_type", ""))
      attrs.add(cfg.get("min_value_attr", ""))
      attrs.add(cfg.get("max_value_attr", ""))
    for cfg in self._config.get("weight_configs"):
      attrs.add(cfg.get("weight_name", ""))
      attrs.add(cfg.get("enable_activation", ""))
      attrs.add(cfg.get("min_value_attr", ""))
      attrs.add(cfg.get("max_value_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_exp_info_attr"))
    for cfg in self._config.get("weight_configs"):
      attrs.add(cfg.get("output_attr", ""))
    return attrs


class ExploreTrimmedPhotoInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_trimmed_photo_info_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("enable_hot_trend_generalized_info")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_hetu_tag_level_info")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("photo_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_trimmed_photo_info_to_attr"))
    return attrs

class ExploreMcDistillSampleKuibaSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_mc_distill_sample_kuiba_sample_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    for name in ["sample_list_names"]:
      if name in self._config:
        attrs.update(self._config.get(name))
    if "common_attrs" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("common_attrs")))
    if "item_attrs" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("item_attrs")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("label_name_list"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_result_to"))
    return attrs

class ExploreContentDuplicateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_content_duplicate_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("content_type_list")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "photo_info_ptr_attr" in self._config:
      attrs.add(self._config.get("photo_info_ptr_attr"))
    else:
      attrs.update([
          "dup_cluster_id", "pic_and_selfdup_id",
          "mmu_content_ids_3", "mmu_content_ids_8", "mmu_content_ids_9", "mmu_content_ids_10",
          "mmu_content_ids_14", "mmu_content_ids_15", "mmu_content_ids_16", "mmu_content_ids_17"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_content_dup_type_to_attr"))
    return attrs

class ExploreColossusTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("min_days_ago")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_days_ago")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_sample_amplifier")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_min_play_time")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_progressive_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_knowledge_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("knowledge_trigger_play_time_ths")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_interest_explore_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_explore_trigger_play_time_ths")))
    if "trigger_select_alpha" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_alpha")))
    if "trigger_select_base_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_base_num")))
    if "trigger_select_topk" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_topk")))
    if "trigger_select_skip_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_skip_num")))
    if "enable_completion_trigger" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_completion_trigger")))
    if "completion_trigger_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("completion_trigger_num")))
    if "enable_cluster_trigger" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_cluster_trigger")))
    if "cluster_trigger_preserve_ratio" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cluster_trigger_preserve_ratio")))
    if "trigger_select_interval_size" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_interval_size")))
    if "cluster_trigger_play_time_ths" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cluster_trigger_play_time_ths")))
    if "knowledge_trigger_max_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("knowledge_trigger_max_num")))
    attrs.add(self._config.get("knowledge_hetu_set_attr", "knowledge_hetu_set"))
    attrs.add(self._config.get("interest_explore_hetu_set_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_colossus_trigger_attr"))
    attrs.add(self._config.get("output_colossus_trigger_weight_attr"))
    attrs.add(self._config.get("output_colossus_info_attr"))
    attrs.add(self._config.get("output_knowledge_trigger_attr"))
    attrs.add(self._config.get("output_interest_explore_trigger_attr"))
    if "output_colossus_trigger_author_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_author_attr"))
    if "output_colossus_trigger_duration_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_duration_attr"))
    if "output_colossus_trigger_tag_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_tag_attr"))
    return attrs

class ExploreColossusV2TriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_v2_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("min_days_ago")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_days_ago")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_sample_amplifier")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_min_play_time")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_progressive_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_knowledge_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("knowledge_trigger_play_time_ths")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_interest_explore_trigger")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_explore_trigger_play_time_ths")))
    if "trigger_select_alpha" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_alpha")))
    if "trigger_select_base_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_base_num")))
    if "trigger_select_topk" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_topk")))
    if "trigger_select_skip_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_skip_num")))
    if "enable_completion_trigger" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_completion_trigger")))
    if "completion_trigger_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("completion_trigger_num")))
    if "enable_cluster_trigger" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_cluster_trigger")))
    if "cluster_trigger_preserve_ratio" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cluster_trigger_preserve_ratio")))
    if "trigger_select_interval_size" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("trigger_select_interval_size")))
    if "cluster_trigger_play_time_ths" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cluster_trigger_play_time_ths")))
    if "knowledge_trigger_max_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("knowledge_trigger_max_num")))
    if "enable_only_select_fountain" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_select_fountain")))
    if "enable_default_select_triggers" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_default_select_triggers")))
    if "enable_different_signals_triggers" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers")))
    if "enable_different_signals_triggers_action_explore_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers_action_explore_list")))
    if "enable_different_signals_triggers_action_completion_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers_action_completion_list")))
    if "enable_different_signals_triggers_action_hetu_tag_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers_action_hetu_tag_list")))
    if "enable_different_signals_triggers_action_interact_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers_action_interact_list")))
    if "enable_different_signals_triggers_action_timestamp_order" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_different_signals_triggers_action_timestamp_order")))
    if "different_signals_triggers_min_days_ago" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("different_signals_triggers_min_days_ago")))
    if "different_signals_triggers_max_days_ago" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("different_signals_triggers_max_days_ago")))
    if "different_signals_triggers_select_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("different_signals_triggers_select_num")))
    if "different_signals_triggers_min_play_time" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("different_signals_triggers_min_play_time")))
    if "different_signals_triggers_play_time_ratio" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("different_signals_triggers_play_time_ratio")))
    if "enable_not_select_bottom_selection_page" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_not_select_bottom_selection_page")))
    if "enable_only_select_explore_colossus_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_select_explore_colossus_list")))
    if "enable_only_select_high_interest_tab" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_select_high_interest_tab")))
    if "enable_select_high_interest_and_profile_tab" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_high_interest_and_profile_tab")))
    if "enable_negtive_signals_triggers" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_negtive_signals_triggers")))
    if "negtive_signals_triggers_min_days_ago" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("negtive_signals_triggers_min_days_ago")))
    if "negtive_signals_triggers_max_days_ago" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("negtive_signals_triggers_max_days_ago")))
    if "negtive_signals_triggers_select_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("negtive_signals_triggers_select_num")))
    if "negtive_signals_triggers_play_time_thres" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("negtive_signals_triggers_play_time_thres")))
    if "enable_negtive_signals_triggers_short_play" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_negtive_signals_triggers_short_play")))
    if "enable_negtive_signals_triggers_hate" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_negtive_signals_triggers_hate")))
    if "enable_select_bottom_page_negtive_signals" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_bottom_page_negtive_signals")))
    if "enable_select_explore_page_negtive_signals" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_explore_page_negtive_signals")))
    if "enable_only_unselect_fountain_colossus_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_unselect_fountain_colossus_list")))
    if "enable_only_select_fountain_colossus_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_select_fountain_colossus_list")))
    if "enable_only_unselect_explore_colossus_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_unselect_explore_colossus_list")))
    if "enable_get_longview_trigger" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_get_longview_trigger")))
    if "enable_outer_field_interest_triggers" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_outer_field_interest_triggers")))
    if "enable_partial_time_based_interest_triggers" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_partial_time_based_interest_triggers")))
    if "enable_only_explore_data" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_only_explore_data")))
    if "cur_time_mode" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cur_time_mode")))
    if "enable_select_fountain_page_negtive_signals" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_fountain_page_negtive_signals")))
    if "enable_select_unexplore_page_negtive_signals" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_unexplore_page_negtive_signals")))
    if "enable_select_unfountain_page_negtive_signals" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_select_unfountain_page_negtive_signals")))
    attrs.add(self._config.get("knowledge_hetu_set_attr", "knowledge_hetu_set"))
    attrs.add(self._config.get("interest_explore_hetu_set_attr"))
    attrs.add(self._config.get("colossus_resp_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_colossus_trigger_attr"))
    attrs.add(self._config.get("output_colossus_trigger_weight_attr"))
    attrs.add(self._config.get("output_colossus_info_attr"))
    attrs.add(self._config.get("output_knowledge_trigger_attr"))
    attrs.add(self._config.get("output_interest_explore_trigger_attr"))
    if "output_colossus_trigger_author_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_author_attr"))
    if "output_colossus_trigger_duration_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_duration_attr"))
    if "output_colossus_trigger_tag_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_tag_attr"))
    return attrs

class ExploreColossusV2PicTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_v2_pic_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return True

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    if "picture_trigger_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("picture_trigger_num")))
    if "picture_trigger_interact_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("picture_trigger_interact_num")))
    if "colossus_channel_select_str" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("colossus_channel_select_str")))
    if "enable_exclude_single_pic" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_exclude_single_pic")))
    if "seleted_eff_play_thd_sec" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("seleted_eff_play_thd_sec")))
    if "colossus_range_days" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("colossus_range_days")))
    if "colossus_hetu_white_list" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("colossus_hetu_white_list")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_colossus_trigger_attr"))
    if "output_colossus_trigger_weight_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_weight_attr"))
    if "output_colossus_trigger_author_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_author_attr"))
    if "output_colossus_trigger_duration_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_duration_attr"))
    if "output_colossus_trigger_tag_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_tag_attr"))
    if "output_colossus_trigger_label_attr" in self._config:
      attrs.add(self._config.get("output_colossus_trigger_label_attr"))
    return attrs

class ExploreColossusV2PictureTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_colossus_v2_picture_trigger_enrich"

  @strict_types
  def is_async(self) -> bool:
    return False

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("colossus_trigger_type"))
    for conf in ["colossus_label_weight_attr","colossus_channel_select_attr",
                  "colossus_time_s_min_max_attr", "colossus_duration_s_min_max_attr",
                "is_select_picture", "trigger_num", "min_trigger_weight", "is_debug", "trigger_agg_method", "colossus_time_decay_attr"]:
      if conf in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(conf)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_colossus_trigger_id_attr"))
    attrs.add(self._config.get("output_colossus_trigger_weight_attr"))
    return attrs

class ExploreRelatedScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_related_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["source_hetu_level_one_v2", "source_hetu_level_two_v2", "source_hetu_level_three_v2",
                 "source_hetu_level_four_v2", "sourcePidFourthLevelCategory", "sourcePidThirdLevelCategory",
                 "source_hetu_face_id_v2", "source_hetu_tag_v2", "source_hetu_cluster_id_v2"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set(["hetu_tag_level_info_v2.hetu_level_one", "hetu_tag_level_info_v2.hetu_level_two",
                 "hetu_tag_level_info_v2.hetu_level_three", "hetu_tag_level_info_v2.hetu_level_four",
                 "author.category_detail.fourth_level_id", "author.category_detail.third_level_id",
                 "hetu_tag_level_info_v2.hetu_face_id", "hetu_tag_level_info_v2.hetu_tag", "hetu_tag_level_info_v2.hetu_cluster_id"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreRelatedScoreEnricherV2(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_related_score_enricher_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if 'source_hetu_attr_list' in self._config:
      attrs.update(self._config.get("source_hetu_attr_list"))
    else:
      attrs.update(["source_hetu_level_one_v2", "source_hetu_level_two_v2", "source_hetu_level_three_v2",
                    "source_hetu_level_four_v2"])

    if 'source_author_str_list' in self._config:
      attrs.update(self._config.get("source_author_str_list"))
    else:
      attrs.update(["sourcePidFourthLevelCategory", "sourcePidThirdLevelCategory"])

    attrs.add(self._config.get("source_face_id_attr", "source_hetu_face_id_v2"))
    attrs.add(self._config.get("source_hetu_tag_attr", "source_hetu_tag_v2"))
    attrs.add(self._config.get("source_cluster_id_attr", "source_hetu_cluster_id_v2"))
    attrs.add(self._config.get("source_aid_attr"))
    attrs.add(self._config.get("source_hetu_cid_attr"))
    attrs.add(self._config.get("source_user_hash_tag_id_attr"))
    attrs.add(self._config.get("source_author_circle_v2_attr"))

    attrs.add(self._config.get("hetu_conf_score_key_list"))
    attrs.add(self._config.get("hetu_conf_score_value_list"))
    attrs.add(self._config.get("tag_conf_score_list"))
    attrs.add(self._config.get("ip_conf_score"))
    attrs.add(self._config.get("cid_conf_score"))
    attrs.add(self._config.get("aid_conf_score"))
    attrs.add(self._config.get("tag_element_conf_score"))
    attrs.add(self._config.get("tag_content_conf_score"))
    attrs.add(self._config.get("user_hash_tag_conf_score"))
    attrs.add(self._config.get("author_circle_v2_conf_score"))

    attrs.update(self.extract_dynamic_params(self._config.get("enable_use_cluster_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_use_author")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_use_style_tag")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_v3")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_use_hash_tag")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_use_author_circle_v2")))

    attrs.update(["enable_filter_fountain_invalid_hetu_id", "skip_fountain_match_hetu_level_one"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if 'target_hetu_attr_list' in self._config:
      attrs.update(self._config.get("target_hetu_attr_list"))
    else:
      attrs.update(["hetu_tag_level_info_v2.hetu_level_one",
                    "hetu_tag_level_info_v2.hetu_level_two",
                    "hetu_tag_level_info_v2.hetu_level_three",
                    "hetu_tag_level_info_v2.hetu_level_four"])

    if 'target_author_str_list' in self._config:
      attrs.update(self._config.get("target_author_str_list"))
    else:
      attrs.update(["author.category_detail.fourth_level_id", "author.category_detail.third_level_id"])

    attrs.add(self._config.get("target_face_id_attr", "hetu_tag_level_info_v2.hetu_face_id"))
    attrs.add(self._config.get("target_hetu_tag_attr", "hetu_tag_level_info_v2.hetu_tag"))
    attrs.add(self._config.get("target_cluster_id_attr", "hetu_tag_level_info_v2.hetu_cluster_id"))
    attrs.add(self._config.get("target_aid_attr"))
    attrs.add(self._config.get("target_hetu_cid_attr"))
    attrs.add(self._config.get("target_user_hash_tag_id_attr"))
    attrs.add(self._config.get("target_author_circle_v2_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    attrs.add(self._config.get("save_score_detail_to_attr"))
    return attrs

class ExploreSimilarPhotoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_similar_photo_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "enable_filter_invalid_hetu_id" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("enable_filter_invalid_hetu_id")))
    if "skip_match_hetu_level_one" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("skip_match_hetu_level_one")))
    if "related_score_threshold" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("related_score_threshold")))
    attrs.add(self._config.get("fullrank_pid_list_attr", "fullrank_total_photo_id_list"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if 'hetu_attr_list' in self._config:
      attrs.update(self._config.get("hetu_attr_list"))
    else:
      attrs.update(["hetu_tag_level_info_v2.hetu_level_one",
                    "hetu_tag_level_info_v2.hetu_level_two",
                    "hetu_tag_level_info_v2.hetu_level_three",
                    "hetu_tag_level_info_v2.hetu_level_four"])

    if 'author_str_list' in self._config:
      attrs.update(self._config.get("author_str_list"))
    else:
      attrs.update(["author.category_detail.fourth_level_id", "author.category_detail.third_level_id"])

    attrs.add(self._config.get("face_id_attr", "hetu_tag_level_info_v2.hetu_face_id"))
    attrs.add(self._config.get("hetu_tag_attr", "hetu_tag_level_info_v2.hetu_tag"))
    attrs.add(self._config.get("cluster_id_attr", "hetu_tag_level_info_v2.hetu_cluster_id"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_similar_pid_mark_to_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_total_similar_pid_list_to_attr"))
    return attrs

class ExploreCustomRuleClusterV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_by_custom_rule_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_user_profile_top_hetu_level_one_cluster", "enable_user_profile_top_hetu_level_two_cluster",
        "user_profile_tag_score_limit", "user_profile_limit_num", "enable_frequency_duration_cluster",
        "enable_colossus_top_hetu_one_cluster", "enable_colossus_top_hetu_two_cluster",
        "colossus_top_hetu_level_two_attr", "colossus_top_hetu_level_one_attr", "variant_queue_num",
        "enable_use_real_show_list", "enable_use_click_list", "enable_use_like_list", "enable_use_follow_list",
        "enable_use_forward_list", "real_show_weight", "click_weight", "like_weight", "follow_weight", "forward_weight",
        "enable_use_fountain_real_show_list", "enable_use_fountain_click_list", "enable_use_fountain_like_list",
        "enable_use_fountain_follow_list", "enable_use_fountain_forward_list", "fountain_real_show_weight",
        "fountain_click_weight", "fountain_like_weight", "fountain_follow_weight", "fountain_forward_weight",
        "enable_colossus_cluster", "input_colossus_attr_one", "input_colossus_attr_two", "input_colossus_attr_three",
        "enable_user_profile_interact_cluster", "enable_user_profile_interact_hetu_level_one_cluster",
        "enable_user_profile_interact_hetu_level_two_cluster", "fountain_comment_weight",
        "interact_cluster_enable_like_list", "interact_cluster_enable_follow_list", "interact_cluster_enable_comment_list",
        "interact_cluster_enable_fountain_like_list", "interact_cluster_enable_fountain_follow_list",
        "interact_cluster_enable_fountain_comment_list", "enable_hetu_cluster", "enable_merge_interact_cluster", "expired_gap_second"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    for name in ["input_colossus_attr_one", "input_colossus_attr_two", "input_colossus_attr_three"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in ["hetu_level_one_attr", "hetu_level_two_attr", "hetu_level_three_attr", "duration_ms_attr", "is_picture_attr", "is_living_attr"]:
      if attr in self._config:
        attrs.add(self._config.get(attr))

    for channel in ["fountainCommentPidList", "fountainClickPidList", "fountainLikePidList", "fountainFollowPidList"]:
      for attr in ["hetu_level_one_attr", "hetu_level_two_attr"]:
        if attr in self._config:
          attrs.add(self._config.get(attr) + "@" + channel)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreRuleClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rule_cluster_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_user_interest_level_one_cluster","enable_hetu_cluster",
        "enable_merge_interact_cluster","enable_user_explore_interest_cluster",
        "explore_interest_reason","explore_interest_cnt","explore_interest_cnt",
        "explore_interest_limit","enable_explore_use_hetu_level_one","enable_time_cluster", "enable_living_cluster",
        "duration_cluster_cfg_str","enable_user_explore_interest_cluster_from_list",
        "enable_user_follow_author_cluster", "enable_interact_cluster", "enable_hot_content_cluster",
        "enable_user_follow_author_cluster_first", "enable_duration_one_cluster"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))

    for name in ["check_point","input_short_interest_attr",
      "input_action_interest_attr", "input_long_interest_attr",
      "hetu_level_one_attr","hetu_level_two_attr","duration_attr",
      "is_picture_attr","is_living_attr","input_explore_interest_attr",
      "hot_content_exp_tag_attr", "input_random_explore_interest_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["hetu_level_one_attr","hetu_level_two_attr","duration_attr",
      "is_picture_attr","is_living_attr", "is_follow_author_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreExploreSimilarUsersHetuEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_explore_similar_users_hetu_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_user_retrieval_result_explore","explore_interest_cnt",
    "explore_interest_limit","colossuse_min_play_time"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))

    for name in ["check_point","similar_user_colossus_attr","similar_user_list_attr",
    "explore_interest_reason_str_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["hetu_level_one_attr","hetu_level_two_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_to_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs


class ExploreEnsembleScoreCalcEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_ensemble_score_calc_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("adjust_weight_emp_xtr_attr", ""))
    for name in ["save_score_to_attr","skip_adjust_weight","user_new_proportion","user_power_calc","adjust_weight_attr_name",
      "user_power_calc_v2","use_reciprocal","min_factor_weight_ratio","max_factor_weight_ratio","size_limit",
      "rank_smooth","temperature","rank_cliff","rank_height","cliff_ratio","use_division_weight"]:
        if name in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
        attrs.add(cfg.get("weight_attr", ""))
        attrs.add(cfg.get("emp_score_attr", ""))
        attrs.add(cfg.get("skip_adjust_weight", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreMarkovGenSeqEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_markov_gen_seq"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["max_sequence_num", "return_item_type", "filter_red_vertical_num",
                     "tran_hetu_max_count", "hetu_beam_search_top_k", "require_page_size", "each_hetu_max_time",
                     "enable_fountain_stat", "enable_all_stat", "valid_play_sec",
                     "cluster_sort_list_attr_name", "the_temperature", "use_power_rank",
                     "use_proportion", "random_dropout_rate", "dropout_hetu_rate",
                     "play_base_weight", "play_decay_factor",
                     "play_limit_hour", "max_candidate_num"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add(self._config.get("user_info_ptr_attr"))
        return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        attr = set()
        attr.add("retrieval_list_keys_7")
        return attr

class ExploreNegFeedbackDiscountV2Enricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "fountain_negative_feedback_discount_v2"
    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for name in ["discount_score", "min_neg_feedback", "time_limit_second",
                     "neg_feedback_threshold", "period_decay_factor", "no_click_factor", "video_play_stat_factor",
                     "play_time_thresold_0", "play_time_thresold_1", "hate_list_factor", "enable_not_click_list",
                     "enable_play_stat_list", "enable_hate_list", "enable_fountain_user_profile",
                     "enable_hot_user_profile"]:
            if name in self._config:
                attrs.update(self.extract_dynamic_params(self._config.get(name)))
        attrs.add(self._config.get("user_info_attr"))
        return attrs
    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for name in self._config.get("attr_keys"):
          attrs.add(name)
        return attrs
    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for name in ["save_score_to_attr"]:
            if name in self._config:
                attrs.add(self._config.get(name))
                #attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

class ExploreUserInfoCostumTrimEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_custom_trim_user_info"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_trimed_user_info_to_attr"))
    return attrs

class ExploreRankingPartDiversityTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_ranking_gen_part_diversity_tag"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mmu_photo_low_quality_model_attr"))
    attrs.add(self._config.get("hetu_level_one_tag_list_attr"))
    attrs.add(self._config.get("audit_hot_cover_attr"))
    attrs.add(self._config.get("merchant_item_id_list_attr"))
    attrs.add(self._config.get("merchant_photo_cart_relation_attr"))
    attrs.add(self._config.get("hetu_level_two_tag_list_attr"))
    attrs.add(self._config.get("hetu_tag_list_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_soft_porn_cover_attr"))
    attrs.add(self._config.get("is_bad_feeling_cover_attr"))
    attrs.add(self._config.get("is_marketing_cover_attr"))
    attrs.add(self._config.get("is_full_text_cover_attr"))
    attrs.add(self._config.get("is_low_resolution_cover_attr"))
    attrs.add(self._config.get("is_pure_text_cover_attr"))
    attrs.add(self._config.get("is_pure_porn_cover_attr"))
    attrs.add(self._config.get("is_audit_gray_cover_attr"))
    attrs.add(self._config.get("is_audit_gray_porn_cover_attr"))
    attrs.add(self._config.get("is_audit_gray_bad_feeling_cover_attr"))
    attrs.add(self._config.get("is_audit_gray_low_quality_cover_attr"))
    attrs.add(self._config.get("is_audit_gray_sensitive_word_cover_attr"))
    attrs.add(self._config.get("is_new_marketing_sense_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("enable_cart_photo_scatter_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_mmu_score_fix_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("low_resolution_cover_threshold_attr")))
    if "use_hetu_v3" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("use_hetu_v3")))
    return attrs

class ExploreParseLocalLifeInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_parse_local_life_info"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("nearby_feeling_bizname_attr"))
    attrs.add(self._config.get("nearby_feeling_poi_city_attr"))
    attrs.add(self._config.get("nearby_feeling_poi_district_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_city_id_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_local_life_photo_attr"))
    attrs.add(self._config.get("photo_city_id_attr"))
    attrs.add(self._config.get("is_same_city_attr"))
    return attrs

class ExploreInvertedIndexWeightedScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_add_inverted_index_weighted_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("query_index_weight_list")))
    attrs.update(self.extract_dynamic_params(self._config.get("query_index_trigger_list")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("score_attr"))
    attrs.add(self._config.get("query_index_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_weighted_score_to_attr"))
    if "save_trigger_to_attr" in self._config:
      attrs.add(self._config.get("save_trigger_to_attr"))
    return attrs

class ExploreSphinxParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_sphinx_param_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_pb_name"))
    attrs.add(self._config.get("session_id_attr"))
    attrs.add(self._config.get("user_stat_attr"))
    attrs.add(self._config.get("user_app_attr"))
    for name in ["request_based_jarvis_enabled", "jarvis_kess_service",
                 "jarvis_model_name", "app_name", "jarvis_time_out",
                 "action_type", "use_app_cat", "use_expxtr", "use_emp_play"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config:
        cfg = self._config["item_attrs"]
        for attr in [
            "author_id", "pevtr", "plvtr", "psvr", "pvtr",
            "pltr", "phtr", "pwtr", "pftr", "pptr",
            "pcmtr", "pcmef", "pepstr", "duration_ms",
            "hetu_level_one", "hetu_level_two", "hetu_level_three"]:
            attrs.add(cfg.get(attr, ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
        attrs.add(cfg.get("param_attr", ""))
    return attrs

class ExploreListEnsembleSortEnricher(LeafEnricher):
    """
    ExploreListEnsembleSortEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_list_ensemble_sort"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.update(self.extract_dynamic_params(cfg.get("weight_base", "")))
        for name in ["use_proportion", "use_pow_rank",
                     "fountain_rerank_ensemble_list_weight", "seq_item_attr_name", "item_list_from_attr"]:
            attrs.update(self.extract_dynamic_params(self._config.get(name)))
        return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("ltr_score_attr"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        attrs.add(self._config.get("output_attr"))
        return attrs

class ExploreRetrSearchTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retr_search_trigger_enriche"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))

    for name in ["search_emb_explore_retr_max_time_decay", "ht_negative_feedback_timeout_min_in_search_retr",
                "enable_search_retr_walk_off_by_not_click", "search_negative_feedback_filter_threshold",
                "enable_search_retr_only_select_user", "search_retr_high_full_active_or_mid_search",
                "search_retr_high_full_active_and_mid_search", "user_active_degree_down", "user_active_degree_up",
                "search_day_count_down", "search_day_count_up"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_search_retr_on_attr"))
    attrs.add(self._config.get("output_search_author_retr_on_attr"))
    attrs.add(self._config.get("output_search_aid_trigger_attr"))
    return attrs

class ExploreIntrestAdjustEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_intrest_adjust_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("gamora_hetu_adjust_history_list_attr"))
    attrs.add(self._config.get("opt_card_like_list_attr"))
    attrs.add(self._config.get("opt_card_dis_like_list_attr"))

    for name in ["interest_adjust_decay_attr", "interest_adjust_immediate_adjust_thres_attr",
                "interest_adjust_immediate_adjust_weight_attr", "adjust_mode",
                "opt_card_adjust_smooth", "opt_card_adjust_weight", "opt_card_max_score", "opt_card_min_score"]:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_intrest_key_list_attr"))
    attrs.add(self._config.get("output_intrest_value_list_attr"))
    attrs.add(self._config.get("optcard_like_trigger_id_list_attr"))
    attrs.add(self._config.get("optcard_dislike_trigger_id_list_attr"))
    return attrs

class ExploreTransHetuTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_transform_hetu_tag"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("hetu_tag_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set(self._config.get("output_attrs", []))

class ExploreTransFintrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_trans_fintr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("fintr_debias_map_attr"))
    for key in ["get_fintr_quantile_mode", "fintr_redis_key_prefix", "fintr_short_photo_cluster_dist", "fintr_long_photo_threshold", "fintr_long_photo_cluster_dist", "max_fintr_limit", "fintr_dist_reciprocal",
                "enable_multi_duration", "fintr_duration_max_value", "fintr_duration_power_weight", "fintr_duration_offset", "enable_transfer_sigmoid", "enable_map_fintr_positive", "max_dura_limit", "fintr_duration_value_upper_bound",
                "enable_multiply_prob_view"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("fintr_attr"))
    attrs.add(self._config.get("duration_ms_attr"))
    attrs.add(self._config.get("prob_view_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("fintr_attr"))
    attrs.add(self._config.get("save_fintr_duration_to_attr"))
    attrs.add(self._config.get("save_fintr_quantile_to_attr"))
    return attrs

class ExploreKvParamEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_enrich_kv_param"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("origin_param")))
    if "param_separator" in self._config.keys():
      attrs.add("param_separator")
    if "kv_separator" in self._config.keys():
      attrs.add("kv_separator")
    attrs.add("param_attr_prefix")
    if 'import_common_attr' in self._config.keys():
      attrs.update(self._config['import_common_attr'])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config['param_name_list_attr'])
    if 'export_common_attr' in self._config.keys():
      attrs.update(self._config['export_common_attr'])
    return attrs

class ExploreUserEmpXtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_user_emp_xtr_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      for name in ["user_info_ptr_attr"]:
          if name in self._config:
              attrs.add(self._config.get(name))
      attrs.add(self._config.get('colossus_resp_attr'))
      for key in ["enable_colossus_item_limit", "max_colossus_item_num", "save_user_stats_click_count",
                  "user_colossus_min_sec_ago","user_colossus_max_sec_ago", "max_click_cnt", "use_fountain_count_threshold"]:
          if key in self._config:
            attrs.update(self.extract_dynamic_params(self._config.get(key)))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      for name in ["save_user_emp_ltr", "save_user_emp_wtr",
                   "save_user_emp_ftr", "save_user_emp_htr",
                   "save_user_emp_cmtr", "save_user_emp_eptr",
                   "save_user_emp_svtr", "save_user_emp_evtr",
                   "save_user_emp_lvtr", "save_user_emp_fintr",
                   "save_user_emp_watch_time", "save_user_emp_finish_rate",
                   "save_user_emp_watch_time_long_video", "save_user_emp_finish_rate_long_video",
                   "save_user_emp_fountin_time_ratio", "save_user_emp_ctr", "save_user_emp_actiononce_ratio"
                   ]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

class ExploreUserColossusXItemAttrEnricher(LeafEnricher):
    """
    ExploreUserColossusXItemAttrEnricher
    """
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_user_colossus_x_item_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        attrs = set([])
        for key in ["user_colossus_info_min_sec_ago", "user_colossus_info_max_sec_ago",
                    "enable_colossus_v2", "user_colossus_info_service_name_V2",
                    "user_colossus_interact_weight_like", "user_colossus_interact_weight_follow",
                    "user_colossus_interact_weight_forward", "user_colossus_interact_weight_comment",
                    "user_colossus_interact_weight_profile", "user_colossus_item_max_parse_num",
                    "user_colossus_info_min_playtime_sec", "user_colossus_info_service_name",
                    "user_colossus_info_weight_hetu2", "user_colossus_info_weight_hetu3",
                    "user_colossus_info_weight_hetu4", "user_colossus_info_weight_aid",
                    "user_colossus_interact_action_max_bucket",
                    "consume_time_ltr_disable_hetu_relation_update"]:
          attrs.update(self.extract_dynamic_params(self._config.get(key)))

        for name in ["colossus_feature_periods_attr", "colossus_action_names_attrs"]:
          if name in self._config:
            attrs.update(self._config.get(name))

        attrs.add(self._config.get("author_id_attr"))
        attrs.add(self._config.get("hetu_level_one_tag_list_attr"))
        attrs.add(self._config.get("hetu_level_two_tag_list_attr"))
        attrs.add(self._config.get("hetu_level_three_tag_list_attr"))
        attrs.add(self._config.get("hetu_level_four_tag_list_attr"))
        attrs.add(self._config.get("colossus_score_name_attr"))
        attrs.add(self._config.get("colossus_resp_attr"))
        attrs.add(self._config.get("colossus_resp_attr_v2"))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      colos_feat_day_period_strs = ["D7", "D15", "D30", "All"]
      feature_prefix = self._config.get("feature_prefix")
      for action_name in ["Play", "Play0To7", "Play7To20", "Play20To58", "Play58More", "Ev", "Lv", "Interact"]:
        for period in colos_feat_day_period_strs:
          for suffix in ["Hetu1", "Hetu2", "Hetu3"]:
            attrs.add(feature_prefix + action_name + period + suffix)
      attrs.add(self._config.get("colossus_score_name_attr")) # "pColossusScore"
      return attrs

class ExploreUserFeedbackIssueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_feedback_issue_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("contents"):
      attrs.add(cfg.get("coefficient_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("contents"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("feedback_score_attr", ""))
    attrs.add(self._config.get("score_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("score_attr", ""))
    return attrs

class ExploreClusterVariantSortV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_variant_sort_v2_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["global_cut_ratio", "min_survival", "enable_proportional",
                 "size_limit", "use_power_calc", "use_reciprocal", "use_reciprocal_v2",
                 "action_day",
                 "enable_dynamic_weight_by_user_degree",
                 "enable_variant_cut_ratio", "variant_cut_ratio","rank_smooth", "use_rank_as_score",
                 "realshow_no_click_cluster_ratio_adjust", "realshow_no_click_cluster_ratio",
                 "all_page_valid_interest_cluster_ratio_adjust", "all_page_valid_interest_cluster_ratio",
                 "hot_list_cluster_ratio_adjust", "hot_list_cluster_ratio",
                 "interest_explore_cluster_ratio_adjust", "interest_explore_cluster_ratio",
                 "colossus_interest_cluster_ratio_adjust", "colossus_interest_cluster_ratio", "colossus_interest_cluster_min_survival",
                 "explore_pic_user_power_calc_v2", "use_reciprocal_new_value_seq_fusion",
                 "two_times_sort", "first_time_cut_ratio", "use_fractile_in_ensemble_sort", "fractile_in_ensemble_sort_type"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_power_weight_attr", ""))
      attrs.add(cfg.get("raw_score_normalize_alpha_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("variant_weight", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("avg_xtr", ""))
      attrs.add(cfg.get("min_ratio", ""))
      attrs.add(cfg.get("max_ratio", ""))
      attrs.add(cfg.get("dynamic_weight", ""))
      attrs.add(cfg.get("user_xtr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_power_weight_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("explore_pic_es_score_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_common_attr", "save_cluster_cnt_common_attr", "save_cluster_cnt_after_truncaton_common_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_name", ""))
    attrs.add(self._config.get("hetu_level_one_name", ""))
    attrs.add(self._config.get("cluster_attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs

class ExploreMinActRankScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_min_act_rank_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["max_rank_ratio", "rank_smooth"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("enable_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs

class ExploreGetEmbeddingMapEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_get_embedding_map_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["embedding_list_attr", "source_pids_list_attr"]:
      attrs.add(self._config.get(name))
    for name in ["dim_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_common_attr", ""))
    return attrs

class ExploreDiversityUpdateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_diversity_update_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_info_ptr_attr", "pid_embedding_common_attr", "history_feed_back_version"]:
      attrs.add(self._config.get(name))
    for name in ["dpp_diversity_mgs_topk", "enable_dpp_diversity_mgs_ratio", "history_matrix_set_mode",
                  "dpp_diversity_mgs_ratio", "diversity_history_size", "topk_select_mod", "history_valid_timestamp_gap",
                  "dim_size", "expected_score_cand_size", "max_interval_second", "min_duration_threshold",
                  "max_playtime_threshold", "enable_use_weight", "weight_version", "ratio_scale", "ratio_pow_weight",
                  "enable_only_user_explore_hate", "enable_only_user_explore_cover_hate", "min_playtime_threshold", "finish_rate_threshold",
                  "session_interact_history_max_size", "enable_calc_matrix_similarity_score"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      attrs.add(prev_item_from_attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    prev_attrs = set()
    prev_attrs.add(self._config.get("cluster_id_attr", "hetu_sim_cluster_id"))
    prev_item_from_attr = self._config.get("prev_item_from_attr")
    if prev_item_from_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, prev_item_from_attr) for v in prev_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr", ""))
    return attrs

class ExplorePictureDiversityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_picture_diversity_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["history_pic_ids_attr", "pid_embedding_common_attr"]:
      attrs.add(self._config.get(name))
    for name in ["calc_diversity_method", "dpp_diversity_mgs_topk", "enable_mgs_his_cnt_less_topk",
                 "enable_dpp_diversity_mgs_ratio", "dpp_diversity_mgs_ratio", "dim_size", "need_normalize_embed"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr", ""))
    return attrs

class ExplorePicClusterCounterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_cluster_counter_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ['hetu_list_attr']:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["long_term_interest_list_attr", "short_term_interest_list_attr",
                 "explore_interest_list_attr"]:
      attrs.add(self._config.get(name))
    for name in ["candidate_topk"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_attr"]:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_pic_cluster_distr_str_attr", "save_long_term_interest_cnt_attr",
                 "save_short_term_interest_cnt_attr", "save_explore_interest_cnt_attr",
                 "save_unknown_interest_cnt_attr", "save_hetu_cnt_attr", "save_pic_cnt_attr"]:
      attrs.add(self._config.get(name))
    return attrs

class ExplorePicColossusStatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_colossus_stat"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ['colossus_attr_name', 'user_info_ptr_attr', 'effect_playtime_thresh_s']:
      if name in self._config:
        attrs.add(self._config.get(name))

    for name in ['enable_user_sim_hetu_attr', 'pic_sim_hetu_count',
                 'explore_with_photo_thompson_sampling', 'explore_photo_only_key_hetu',
                 'explore_with_pic_thompson_sampling', 'explore_pic_only_key_hetu',
                 'pic_sim_enable_cache_hetu', 'pic_sim_cached_dedup_hetu_list',
                 'enable_user_photo_sim_hetu_attr', 'photo_sim_action_weight_str',
                 'photo_sim_hetu_count', 'photo_sim_exploit_count', 'photo_hetu_pic_pids_prefix',
                 'photo_hetu_pic_aids_prefix', 'photo_hetu_pic_ts_prefix', 'photo_hetu_pic_ptime_prefix',
                 'photo_hetu_pic_action_prefix', 'photo_hetu_pids_prefix', 'photo_hetu_aids_prefix',
                 'photo_hetu_ts_prefix', 'photo_hetu_ptime_prefix', 'photo_hetu_action_prefix',
                 'enable_fountain_splash_sim_hetu', 'fountain_splash_sim_hetu1', 'splash_hetu_pids',
                 'splash_hetu_aids', 'splash_hetu_ts', 'splash_hetu_ptime', 'splash_hetu_action', 'hetu_l1_white_list_str',
                 'enable_fountain_splash_pic_sim_hetu', 'enable_user_pic_hetu_distr_attr',  "interest_distr_use_explore",
                 "interest_distr_use_fountain", "interest_distr_pic_use_global", "interest_distr_video_use_global",
                 "interest_distr_use_hetu_l1_backup", "interest_distr_pic_hate_weight",
                 "interest_distr_pic_action_weight", "interest_distr_video_hate_weight",
                 "interest_distr_video_action_weight", "interest_distr_trans_pic_vv_num_thd", "interest_distr_trans_pic_pic_weight",
                 "interest_distr_trans_video_pic_weight", "short_term_range_ts", "explore_only_hot_tab", "recent_pic_play_thresh_m"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for prefix in ["pHetuL1ActList", "pHetuL1ActPicList"]:
      for fea_type in ["Pids", "Aids", "Ts", "Ptime", "Action"]:
        attr_name = prefix + fea_type + "__Top1_splash"
        attrs.add(self._config.get(attr_name, ""))
        for suffix in range(4):
          attr_name = prefix + fea_type + "__Top" + str(suffix+1)
          attrs.add(self._config.get(attr_name, ""))
          attr_name = prefix + fea_type + "__photo_Top" + str(suffix+1)
          attrs.add(self._config.get(attr_name, ""))

    attrs.add(self._config.get("save_pic_like_cnt", ""))
    attrs.add(self._config.get("save_pic_follow_cnt", ""))
    attrs.add(self._config.get("save_pic_forward_cnt", ""))
    attrs.add(self._config.get("save_pic_comment_cnt", ""))
    attrs.add(self._config.get("save_pic_play_cnt", ""))
    attrs.add(self._config.get("save_pic_recent_play_cnt", ""))
    attrs.add(self._config.get("save_pic_eff_play_cnt", ""))
    attrs.add(self._config.get("save_video_like_cnt", ""))
    attrs.add(self._config.get("save_video_follow_cnt", ""))
    attrs.add(self._config.get("save_video_forward_cnt", ""))
    attrs.add(self._config.get("save_video_comment_cnt", ""))
    attrs.add(self._config.get("save_video_play_cnt", ""))
    attrs.add(self._config.get("save_video_eff_play_cnt", ""))
    attrs.add(self._config.get("save_pic_play_list", ""))
    attrs.add(self._config.get("save_pic_like_list", ""))
    attrs.add(self._config.get("save_pic_follow_list", ""))
    attrs.add(self._config.get("save_pic_comment_list", ""))
    attrs.add(self._config.get("save_pic_comment_aid_list", ""))
    attrs.add(self._config.get("save_pic_hetu_l1_cnt", ""))
    attrs.add(self._config.get("save_pic_hetu_l1_cnt2", ""))
    attrs.add(self._config.get("save_video_hetu_l1_cnt", ""))
    attrs.add(self._config.get("save_pic_hot_outer_cnt", ""))
    attrs.add(self._config.get("save_pic_gamora_slide_cnt", ""))
    attrs.add(self._config.get("save_video_tdiff_action_cnt_x1", ""))
    attrs.add(self._config.get("save_video_tdiff_action_cnt_x2", ""))
    attrs.add(self._config.get("save_video_tdiff_action_cnt_x3", ""))
    attrs.add(self._config.get("save_pic_tdiff_action_cnt_x1", ""))
    attrs.add(self._config.get("save_pic_tdiff_action_cnt_x2", ""))
    attrs.add(self._config.get("save_pic_hetu_l1_cnt_v2", ""))
    attrs.add(self._config.get("selected_sim_hetu_list", ""))
    attrs.add(self._config.get("selected_photo_sim_hetu_list", ""))
    attrs.add(self._config.get("save_photo_hetu_l1_cnt", ""))
    attrs.add(self._config.get("save_photo_like_list", ""))
    attrs.add(self._config.get("save_photo_follow_list", ""))
    attrs.add(self._config.get("save_photo_comment_list", ""))
    attrs.add(self._config.get("save_user_pic_interest_hetu_distr", ""))
    attrs.add(self._config.get("save_user_pic_interest_hetu_distr_str", ""))
    attrs.add(self._config.get("save_short_term_pic_cnt", ""))
    attrs.add(self._config.get("save_short_term_video_cnt", ""))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class ExploreUserDebiasXtrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_debias_xtr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ['memory_data_map_ptr', 'debias_module_prefix', 'gender_attr', 'age_segment_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ['hetu_tag_attr', 'ctr_attr', 'ltr_attr', 'wtr_attr',
                 'ftr_attr', 'cmtr_attr', 'epstr_attr', 'cltr_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ['debias_ctr_attr', 'debias_ltr_attr', 'debias_wtr_attr',
                 'debias_ftr_attr', 'debias_cmtr_attr', 'debas_epstr_attr', 'debias_cltr_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreUserDebiasXtrV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_debias_xtr_v2_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["colossus_v2_attr_name", "user_info_ptr_attr"]:
      attrs.add(self._config.get(name))
    for name in ["xtr_weight_str", "shortterm_stat_show_count", "longterm_stat_click_count",
                 "stat_only_page", "playtime_debias_by_duration", "adjust_playtime_score", "playtime_score_coeff_str",
                 "debias_version", "default_debias_value", "hetu_debias_value_str"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["hetu_tag_attr", "ctr_attr", "ltr_attr", "wtr_attr",
                 "ftr_attr", "cmtr_attr", "pptr_attr", "playtime_attr",
                 "duration_ms_attr"]:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["ctr_debias_attr", "ltr_debias_attr", "wtr_debias_attr",
                 "ftr_debias_attr", "cmtr_debias_attr", "pptr_debias_attr",
                 "playtime_debias_attr", "debias_mix_score_attr"]:
      attrs.add(self._config.get(name))

    return attrs

class ExplorePrerankBucketSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_prerank_bucket_sort_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ['seperate_point_attr', 'seperate_ratio_attr']:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ['cascade_prerank_score_attr', 'duration_ms_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ['cascade_prerank_score_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreTransSimClusterIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_trans_sim_cluster_id_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ['enable_lv1_map_attr', 'enable_threshold_map_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ['cluster_id_input_attr', 'cluster_id_score_attr']:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ['cluster_id862_attr', 'cluster_id862_lv1_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreAbsoluteXtrScoreQueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_absolute_xtr_score_que_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("need_fractile_value_attr", ""))

    for name in ['explore_absolute_xtr_boost_threshold', 'explore_absolute_xtr_boost_weight',
      'enable_explore_absolute_xtr_cliff', 'enable_explore_time_cost_optimal']:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("xtr_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("fractile_value_attr", ""))
    for name in ['absolute_xtr_score_que_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreDurationXtrDebiasEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_duration_xtr_debias_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("alpha_attr", ""))
      attrs.add(cfg.get("bias_attr", ""))
    for name in ["duration_xtr_debias_user_add", "duration_xtr_debias_window_size",
        "enable_use_bidirectional_window", "enable_cid_bias", "enable_sort_by_duration", "que_score_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("xtr_attr", ""))
    for name in ['duration_ms_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))

    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ['duration_xtr_debias_score_que_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreSupportAuthorTgiScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_support_author_tgi_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("memory_data_ptr_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("author_id_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["save_item_tgi_gender_score_attr", "save_item_tgi_age_score_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreUserColossusCascadeClusterEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_user_colossus_cascade_cluster_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get('colossus_resp_attr'))
      for key in ["enable_mc_colossus_favourite_time_cluster", "mc_colossus_favourite_time_cluster_use_rate",
                  "mc_colossus_favourite_time_cluster_num", "mc_colossus_dislike_time_cluster_num",
                  "duration_cluster_cfg_str", "time_cluster_base_id", "mc_favourite_time_cluster_score_power",
                  "mc_favourite_time_cluster_score_offset", "enable_get_relative_duration_cluster_score"]:
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      for name in ["save_user_colossus_favourite_time_cluster", "save_user_colossus_favourite_time_cluster_scores",
                  "save_user_colossus_dislike_time_cluster"]:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

class ExplorePhotoInfoPb2KvEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_photo_info_pb2kv"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("photo_info_attr"))
      return attrs

    @property
    @strict_types
    def output_common_attrs(self) -> set:
      attrs = set()
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      if "save_mmu_content_attrs" in self._config:
        for _, attr in self._config.get("save_mmu_content_attrs").items():
          attrs.add(attr)
      return attrs

class ExploreGlobalQuantileXtrEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "explore_global_quantile_xtr_enricher"

    @classmethod
    @strict_types
    def is_async(cls) -> bool:
        return True

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      for cfg in self._config.get("queues"):
        attrs.add(cfg.get("enable_quantile", ""))
      for name in ['memory_data_map_ptr']:
        if name in self._config:
          attrs.add(self._config.get(name))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("name", ""))
        return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
        attrs = set()
        for cfg in self._config.get("queues"):
            attrs.add(cfg.get("quantile_name", ""))
        return attrs

class ExploreSelectNegativePidsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_select_negative_pids_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "not_click_limit_hour", "play_stat_limit_hour",
      "fountain_short_view_threshold", "short_view_threshold",
      "max_not_click_num", "max_short_view_num", "max_hate_num", "max_click_num", "max_report_num",
      "not_click_weight", "short_view_weight", "hate_weight", "click_weight", "report_weight"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("neg_pid_list_name"))
    attrs.add(self._config.get("weight_list_name"))
    return attrs

class ExploreTopVideoSimScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "explore_top_video_sim_score_enricher"

  @classmethod
  @strict_types
  def is_async(cls) -> bool:
      return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("top_k")))
    for cfg in self._config.get("sim_configs"):
      attrs.add(cfg.get("weight_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("picture_attr"))
    for cfg in self._config.get("sim_configs"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreMerchantGlobalDataEnricher(LeafEnricher):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "explore_merchant_global_data_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("kuiba_user_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "as")
    attrs.add(self._config.get("kuiba_user_ptr_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kuiba_user_attr"), str),
              "kuiba_user_attr 配置项需为 string 类型")


class ExploreExploreHetuTagsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_explore_hetu_tags_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
     "enable_explore_cluster_target", "enable_recent_stat_only_explore",
     "explore_cluster_count_limit", "explore_cluster_target_count_limit", "explore_cluster_recent_play_count_limit",
     "explore_cluster_recent_show_count_limit", "explore_cluster_recent_play_top_ratio",
     "explore_cluster_recent_show_top_ratio", "explore_cluster_play_count_limit",
     "explore_cluster_stat_time_hour_limit", "enable_explore_user_explore_coeff",
     "explore_colossus_min_size", "explore_cluster_interest_score_limit", "explore_cluster_already_interest_score_limit",
     "explore_user_explore_avg_value", "explore_user_explore_coeff_min", "explore_user_explore_coeff_max",
     "interest_score_lv_weight", "interest_score_like_weight", "interest_score_enter_profile_weight",
     "interest_score_comment_weight", "interest_score_follow_weight", "interest_score_forward_weight",
     "enable_explore_cluster_ignore_interest_hetu", "interest_score_click_pow_weight", "interest_score_playtime_weight",
     "explore_cluster_history_ratio_lowerbound", "explore_cluster_history_ratio_upperbound"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("high_quality_tags_attr"))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("colossus_v2_attr_name"))
    attrs.add(self._config.get("shortterm_hetu_attr"))
    attrs.add(self._config.get("longterm_hetu_one_attr"))
    attrs.add(self._config.get("longterm_hetu_two_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_explore_hetu_tags_attr", "explore_hetu_tags"))
    attrs.add(self._config.get("recent_top_show_hetu_attr", "recent_top_show_hetu"))
    return attrs

class ExploreInterestHetuTagsEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_interest_hetu_tags_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "shortterm_interest_count_limit", "shortterm_interest_score_limit", "shortterm_interest_expired_gap_second",
      "shortterm_interest_realshow_weight", "shortterm_interest_click_weight", "shortterm_interest_like_weight",
      "shortterm_interest_follow_weight", "shortterm_interest_forward_weight", "longterm_interest_count_limit_str",
      "longterm_interest_recent_hours", "longterm_interest_finish_rate_threshold", "longterm_interest_stat_day_upper",
      "longterm_interest_stat_day_lower", "longterm_interest_click_weight", "longterm_interest_playtime_weight"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("colossus_v2_attr_name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_short_term_interest_attr"))
    attrs.add(self._config.get("export_long_term_interest_one_attr"))
    attrs.add(self._config.get("export_long_term_interest_two_attr"))
    attrs.add(self._config.get("export_long_term_interest_three_attr"))
    return attrs

class ExploreItemReasonScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_item_reason_score_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集 """
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("smoothing", "")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    mappings = self._config["mappings"]
    for mapping in mappings:
      if "to_item_attr" in mapping:
        attrs.add(mapping["to_item_attr"])
    return attrs

class ExploreEnsembleScoreCalcPureValueEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_ensemble_score_calc_pure_value_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_power_calc", "log_add_alpha"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("use_mapping", ""))
      attrs.add(cfg.get("max_value", ""))
      attrs.add(cfg.get("max_default_value", ""))
      attrs.add(cfg.get("min_value", ""))
      attrs.add(cfg.get("min_default_value", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreAttrQuantileEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_attr_quantile_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("quantile_to_calc")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("quantile_config"):
      attrs.add(cfg.get("attr_name", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("quantile_config"):
      attrs.add(cfg.get("save_quantile_to", ""))
    return attrs

class ExploreAttrsAdjustEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_attrs_adjust_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("item_attr_configs"):
      attrs.add(cfg.get("adjust_cfg", ""))
    for cfg in self._config.get("common_attr_configs"):
      attrs.add(cfg.get("attr_name", ""))
      attrs.add(cfg.get("adjust_cfg", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("item_attr_configs"):
      attrs.add(cfg.get("attr_name", ""))
    return attrs

class ExploreSelectActionListTriggerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_select_action_list_trigger_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "max_trigger_len", "max_candidate_len",
      "enable_click_list"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("trigger_list_name"))
    return attrs

class ForceInsertPositionEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "force_insert_position_enricher"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("user_info_ptr_attr"))
      attrs.update(self.extract_dynamic_params(self._config.get("follow_author_insert_position_limit")))
      attrs.update(self.extract_dynamic_params(self._config.get("top_xtr_insert_position_limit")))
      attrs.update(self.extract_dynamic_params(self._config.get("top_xtr_insert_photo_ratio")))
      return attrs

    @property
    @strict_types
    def input_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("is_follow_author_photo_attr"))
      attrs.add(self._config.get("xtr_score_attr"))
      attrs.add(self._config.get("ctr_score_attr"))
      return attrs

    @property
    @strict_types
    def output_item_attrs(self) -> set:
      attrs = set()
      attrs.add(self._config.get("force_insert_position_attr"))
      return attrs

class ExploreGlobalTriggerSelectEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_global_trigger_select_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "max_candidate_normal_trigger_num", "max_candidate_high_value_trigger_num",
      "max_playstat_trigger_num", "max_colossus_trigger_num",
      "max_actionlist_trigger_num", "high_value_interval_size",
      "normal_interval_size","hetu_low_level_tags",
      "hetu_level2_tags", "enable_click_list","colossus_trigger_play_time_ths",
      "playstat_trigger_play_time_ths",
      "use_hot_playstat", "use_hot_action_list",
      "use_fountain_playstat", "use_fountain_action_list", "useless_trigger_ids",
      "negative_trigger_ids", "negative_trigger_weights", "negative_trigger_min_weight",
      "append_prefer_trigger_num", "prefer_trigger_ids", "prefer_trigger_weights",
      "unbias_trigger_num", "unbias_trigger_ids", "unbias_trigger_weight",
      "enable_weight_adjust", "weight_adjust_coef", "weight_max_value", "colossus_trigger_recent_preserve_num",
      "enable_real_show_list", "enable_hot_list", "enable_colossus_fountain_list", "enable_slide_list", "enable_only_hot_list",
      "append_other_tabs_threshold", "append_play_stat_num", "append_fountain_play_stat_num", "append_only_slide_list",
      "non_hot_trigger_ratio"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("colossus_v2_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("normal_trigger_list_attr"))
    attrs.add(self._config.get("high_value_trigger_list_attr"))
    attrs.add(self._config.get("normal_trigger_weight_list_attr"))
    attrs.add(self._config.get("high_value_trigger_weight_list_attr"))
    return attrs

class ExploreGlobalTriggerSelectV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_global_trigger_select_v2_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in [
      "min_play_time", "play_time_weight", "max_play_time_limit",
      "play_ratio_weight", "time_decay_weight",
      "label_weight_map", "min_cluster_size",
      "normal_trigger_num","high_value_trigger_num", "enable_normal_shuffle",
      "user_fountain_behv"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("colossus_resp_attr"))
    attrs.add(self._config.get("hetu_map_ptr_attr"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("high_value_trigger_attr"))
    attrs.add(self._config.get("normal_trigger_attr"))
    attrs.add(self._config.get("high_value_trigger_weight_attr"))
    attrs.add(self._config.get("normal_trigger_weight_attr"))
    return attrs

class ExploreCacheStringBuildEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_build_cache_string"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["max_exp_retr_num"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_cache_string_to_attr"))
    return attrs

class ExploreEnsembleFilterScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_ensemble_filter_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["filter_function", "score_with_rank"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("tail_ratio_attr", ""))
      attrs.add(cfg.get("reverse_order", ""))
      attrs.add(cfg.get("global_quantile_value_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreRetrPersonalQuotaEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retr_personal_quota_enrich"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["register_exptag", "upper_limit", "lower_limit", "cal_ratio_mode", "power", "realshow_threshold"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("cluster_name"))
    attrs.add(self._config.get("time_out"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_data_ptr_to_attr"))
    return attrs

class ExploreTriggerSelectedEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_trigger_selected_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    dynamic_param = [
      "life_restrict_hetu_level1", "max_time_diff",
      "click_reward", "like_reward", "follow_reward",
      "forward_reward", "comment_reward", "profile_enter_reward",
      "download_reward", "collect_reward", "per_hetu_min_num",
      "reward_cal_type", "short_term_hetu_tag_item_policy",
      "long_term_hetu_tag_item_policy",
      "colossus_candidate_trigger_max_cnt",
      "enable_click_list", "per_hetu_truncate_rate",
      "enable_life_hetu_restrict"
    ]
    static_param_dict = {
      "user_info_ptr_attr": "",
      "colossus_resp_attr": ""
    }
    for name in dynamic_param:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for name, default_val in static_param_dict.items():
      attrs.add(self._config.get(name, default_val))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("final_item_list_attr", "explore_selected_trigger_list"))
    return attrs


class ExploreSimColossusStatEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_sim_colossus_stat"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ['colossus_attr_name']:
      if name in self._config:
        attrs.add(self._config.get(name))

    for name in ['lastest_n_long_view_tag', 'photo_sim_action_weight_str',
                 'photo_sim_hetu_count', 'photo_sim_exploit_count']:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for fea_type in ["Pids", "Aids", "Ts", "Ptime", "Duration"]:
      for suffix in range(4):
        attr_name = "pHetuL1ActList" + fea_type + "__photo_Top" + str(suffix+1)
        attrs.add(self._config.get(attr_name, ""))
    attrs.add(self._config.get("save_sim_hetu_list_attr", "selected_photo_sim_hetu_list"))
    return attrs

class ExploreUserColossusHistoryEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_user_colossus_history"

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["colossus_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    for name in ['search_user_history_max', 'photo_playtime_thresh', 'mix_pic_history_max', 'mix_vid_history_max']:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_user_play_pids", ""))
    attrs.add(self._config.get("save_user_play_aids", ""))
    attrs.add(self._config.get("save_user_play_rewards", ""))
    attrs.add(self._config.get("save_user_play_hetus", ""))
    attrs.add(self._config.get("save_user_pic_play_pids", ""))
    attrs.add(self._config.get("save_user_pic_play_aids", ""))
    attrs.add(self._config.get("save_user_pic_play_rewards", ""))
    attrs.add(self._config.get("save_user_pic_play_hetus", ""))
    attrs.add(self._config.get("save_user_mix_play_pids", ""))
    attrs.add(self._config.get("save_user_mix_play_aids", ""))
    attrs.add(self._config.get("save_user_mix_play_reward", ""))
    attrs.add(self._config.get("save_user_mix_play_action", ""))
    attrs.add(self._config.get("save_user_mix_play_ts", ""))
    return attrs

class ExploreConsumeTimeLtrAttrEnricher(LeafEnricher):
  """
  ExploreConsumeTimeLtrAttrEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_consume_time_ltr_attr"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["user_info_attr"])
    for key in ["consume_time_ltr_need_long_action_list",
                "long_action_list_click_length", "long_action_list_action_length",
                "consume_time_view_list_length"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set(self._config.get("user_feature", []))

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("item_attrs", []))

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set(["pHotExptag", "pAgeHour", "pPctrFractileOri", "pPctrFractile", "pPltrFractile",
                 "pPlvtrFractile", "pPsvrFractile", "pPwtrFractile", "pPftrFractile",
                 "pPptrFractile", "pPhtrFractile", "pPepstrFractile", "pPcmtrFractile",
                 "pPcmefFractile", "pPFrScore1Fractile", "pPFrScore2Fractile",
                 "pPfetrFractile", "pPFountainEffFractile"])
    for key in ["Hetu1", "Hetu2", "Hetu3", "Hetu4", "Hetu5", "HetuTag"]:
      for suffix in ["100n", "1000n"]:
        attrs.add("pShortStatShow" + key + suffix)
        attrs.add("pShortStatClick" + key + suffix)
        attrs.add("pShortStatClickRate" + key + suffix)
    return attrs

class ExploreCalcFractileScoreByMultipleBucketEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_fractile_score_by_multiple_bucket_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pxtr_fractile_redis_attr"))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("use_hetu_level_one")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_universe_frac")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_gender_multi_age_frac")))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("xtr_attr", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("fractile_value_attr", ""))
    return attrs

class ExploreCalcPf2rScoreByMultipleBucketEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_pf2r_score_by_multiple_bucket_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pxtr_fractile_map_attr"))
    attrs.add(self._config.get("active_days_attr"))
    attrs.add(self._config.get("active_days_split_conf_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("duration_ms_upper_bound")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("raw_consume_time_ptr_attr"))
    attrs.add(self._config.get("duration_ms_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pf2r_score_attr"))
    return attrs

class ExploreCalcPairwiseRankScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_pairwise_rank_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("smooth")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_key")))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("wtd_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("pairwise_rank_score_attr"))
    attrs.add(self._config.get("pairwise_rank_raw_score_attr"))
    return attrs

class ExploreCalcSharpeRatioScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_calc_sharpe_ratio_score_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("risk_free_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("std_beta_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_raw_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_risk_free_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("global_rf_weight_attr")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_rf_weight_attr")))

    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ctr_attr"))
    attrs.add(self._config.get("xtr_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("sharpe_ratio_score_attr"))
    return attrs

class ExploreClusterVariantSortEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_variant_sort_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_config", "global_cut_ratio", "min_survival", "enable_proportional", "size_limit", "use_power_calc", "use_reciprocal",
                 "use_reciprocal_v2", "check_point", "enable_user_profile_config", "cut_ratio_decay", "cut_ratio_boost", "use_power_calc_v2",
                 "user_profile_hetu_count_threshold", "neg_time_limit_second", "neg_play_sec_limit", "pos_time_limit_second", "pos_play_sec_limit",
                 "fixed_final_size", "enable_dynamic_cut_ratio", "enable_kconf_ration", "time_cluster_base_id",
                 "enable_hetu_cluster_adjust_cut_ratio", "enable_duration_cluster_adjust_hetu_score", "duration_cluster_enable_unknown_hetu_adjust",
                 "rank_smooth", "rank_value_fusion_type", "enable_ceil_keep_size", "enable_hetu_adjust_fixed_size",
                 "vv_min_rank_pow_weight", "act_min_rank_pow_weight", "play_min_rank_pow_weight",
                 "enable_vv_min_rank", "enable_act_min_rank", "enable_play_min_rank", "enable_combine_min_rank",
                 "vv_min_rank_weight", "act_min_rank_weight", "play_min_rank_weight", "combine_min_rank_pow_weight", "enable_score_adjust",
                 "enable_es_score_normal", "es_score_dist_temperature", "es_score_dist_smooth"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("power_weight_attr", ""))
      attrs.add(cfg.get("temperature_attr", ""))
      attrs.add(cfg.get("cutoff_ratio", ""))
      attrs.add(cfg.get("cluster_reweight_attr", ""))
      attrs.add(cfg.get("raw_weight_attr", ""))
      attrs.add(cfg.get("raw_bias_attr", ""))
      attrs.add(cfg.get("enable_couple_rank_value_weight", ""))
      attrs.add(cfg.get("raw_pow_weight_attr", ""))
      attrs.add(cfg.get("fractile_weight_attr", ""))
      attrs.add(cfg.get("fractile_pow_weight_attr", ""))
      attrs.add(cfg.get("use_vv_min_rank_attr", ""))
      attrs.add(cfg.get("use_act_min_rank_attr", ""))
      attrs.add(cfg.get("use_play_min_rank_attr", ""))
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("candidate_hetu_adjust_coeff_map_attr"))
    attrs.add(self._config.get("hetu_cluster_hetu_adjust_paras_attr"))
    attrs.add(self._config.get("duration_cluster_hetu_adjust_para_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    for name in ["cluster_sort_list_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.add(self._config.get("cluster_sort_list_attr_name"))
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
      attrs.add(cfg.get("fractile_value_attr", ""))
    attrs.add(self._config.get("adjust_coeff_final_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    attrs.add(self._config.get("save_filter_flag_to_attr", ""))
    if "save_adjust_score_to_attr" in self._config:
        attrs.add(self._config.get("save_adjust_score_to_attr", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["save_cluster_id_common_attr", "save_cluster_cnt_common_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreClusterPriorityScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_priority_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.update(self.extract_dynamic_params(self._config.get("weight_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_weight_map_str")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs

class ExplorePicDiversityControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_diversity_control_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["enable_interest_control", "enable_hetu_control", "interest_control_start",
                 "hetu_control_start", "hetu1_quota_coeff", "hetu2_quota_coeff", "hetu5_quota_coeff",
                 "enable_actual_hetu_control", "hetu_adjust_coef", "hetu_adjust_min_value",
                 "hetu_adjust_max_value", "enable_quota_complete", "final_quota_adjust",
                 "quota_complete_adjust_coeff", "enable_cluster_control", "cluster_control_start",
                 "cluster_quota_coeff", "old_cluster_id_interest_coef",
                 "enable_dynamic_hetu_control_start", "dynamic_hetu_control_start_alpha",
                 "dynamic_hetu_control_start_bias", "dynamic_hetu_control_start_pow",
                 "dynamic_hetu_control_start_min", "dynamic_hetu_control_start_max",
                 "enable_pic_type_control", "pic_type_control_start", "pic_type_control_single_pic_max_ratio",
                 "pic_type_control_pic_set_max_ratio", "pic_type_control_long_pic_max_ratio"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    attrs.add(self._config.get("user_hetu_stat_attr"))
    attrs.add(self._config.get("user_actual_distribution_attr"))
    attrs.add(self._config.get("old_cluster_id_interest_list_attr"))
    attrs.add(self._config.get("keep_size"))
    attrs.add(self._config.get("picture_type_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_level_one_attr"))
    attrs.add(self._config.get("hetu_level_two_attr"))
    attrs.add(self._config.get("hetu_level_five_attr"))
    attrs.add(self._config.get("cluster_id_attr"))
    attrs.add(self._config.get("es_score_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("es_score_attr"))
    return attrs

class UserHistoryCidsStatEnricher(LeafEnricher):
  """
  UserHistoryCidsStatEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_history_cids_stat_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("recent_realshow_items_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("recent_realshow_top_ratio")))
    attrs.update(self.extract_dynamic_params(self._config.get("recent_realshow_min_count")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    recent_realshow_items_attrs = set()
    attrs.add(self._config.get("cluster_id_attr", ""))
    recent_realshow_items_attrs.add(self._config.get("cluster_id_attr", ""))
    recent_realshow_items_attr = self._config.get("recent_realshow_items_attr")
    if recent_realshow_items_attr:
      t = set(gen_attr_name_with_common_attr_channel(v, recent_realshow_items_attr) for v in recent_realshow_items_attrs)
      attrs.update(t)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_recent_realshow_cids_attr", ""))
    return attrs

class ExplorePicRecentInterestAdjustEnricher(LeafEnricher):
  """
  ExplorePicRecentInterestAdjustEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_recent_interest_adjust_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("actual_hetu_stat_attr"))
    attrs.add(self._config.get("user_info_attr"))
    attrs.add(self._config.get("pic_real_show_attr"))
    attrs.add(self._config.get("pic_click_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_decay_time_gap")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_decay_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("interest_boost_coeff")))
    attrs.update(self.extract_dynamic_params(self._config.get("consec_realshow_time_gap")))
    attrs.update(self.extract_dynamic_params(self._config.get("consec_show_max")))
    attrs.update(self.extract_dynamic_params(self._config.get("consec_show_not_click_decay")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_actual_hetu_stat_attr", ""))
    attrs.add(self._config.get("save_consec_show_not_click_decay_attr", ""))
    return attrs

class ExplorePicInterestStatEnricher(LeafEnricher):
  """
  ExplorePicInterestStatEnricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_interest_stat_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    for name in ["enable_only_hot_stat", "interest_time_interval_day", "interest_distr_trans_pic_pic_weight",
                 "reward_normal_min_score", "reward_normal_max_score", "explore_interest_count",
                 "short_term_weight", "long_term_weight", "short_weight_adjust_vv_thres",
                 "short_term_adjust_coeff"]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_pic_interest_stat_attr", ""))
    attrs.add(self._config.get("save_short_term_interest_attr", ""))
    attrs.add(self._config.get("save_long_term_interest_attr", ""))
    attrs.add(self._config.get("save_explore_interest_attr", ""))
    return attrs

class ExplorePicInterestStatV2Enricher(LeafEnricher):
  """
  ExplorePicInterestStatV2Enricher
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_pic_interest_stat_enricher_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("colossus_resp_attr"))
    for name in [
      "explore_page_weight",
      "other_page_weight",
      "pic_weight",
      "video_weight",
      "stat_limit_day",
      "calc_method",
      "short_interval_thresh_day",
      "index_decay_pow_base",
      "index_decay_min",
      "index_bucket_h",
      "interest_min_thresh",
      "long_interest_weight",
      "short_interest_weight",
      "explore_interest_count",
      "explore_interest_weight",
      "smooth_or_sharp_power",
    ]:
      if name in self._config:
        attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_pic_interest_stat_attr", ""))
    attrs.add(self._config.get("save_short_term_interest_attr", ""))
    attrs.add(self._config.get("save_long_term_interest_attr", ""))
    attrs.add(self._config.get("save_explore_interest_attr", ""))
    return attrs

class ExploreHierarchicalPriorityInsertTagEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_hierarchical_priority_insert_tag_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("weight_attr", ""))
      attrs.add(cfg.get("photo_source_type", ""))
    attrs.update(self.extract_dynamic_params(self._config.get("is_cold_start")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_first_page_show")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_only_cold_start")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_only_first_page_show")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_zero_play")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_only_is_zero_play")))
    attrs.update(self.extract_dynamic_params(self._config.get("seek_num")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("queues"):
      attrs.add(cfg.get("name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_tag_to_attr", ""))
    return attrs

class ExploreClusterPriorityScoreV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_cluster_priority_score_v2"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["cluster_attr_name", "colossus_attr_name", "xtr_attr_name"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    attrs.update(self.extract_dynamic_params(self._config.get("colossus_action_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_item_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("exploitation_coef")))
    attrs.update(self.extract_dynamic_params(self._config.get("exploration_coef")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr", ""))
    return attrs

class ExploreRerankCalcPage1TriggerScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_calc_page1_trigger_score_enricher"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for name in ["candidates_embedding_item_attr"]:
      attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["embedding_list_common_attr", "pid_list_common_attr"]:
      attrs.add(self._config.get(name))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("sim_weight")))
    attrs.update(self.extract_dynamic_params(self._config.get("boost_top_n")))
    attrs.update(self.extract_dynamic_params(self._config.get("boost_score")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ["export_item_attr"]:
      attrs.add(self._config.get(name))
    return attrs

class ExploreRerankSingleScoreCandidateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_collect_single_score_candidate"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for candidate in self._config.get("candidates"):
      attrs.add(candidate.get("score_attr"))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for candidate in self._config.get("candidates"):
      attrs.update(self.extract_dynamic_params(candidate.get("enable")))
      attrs.update(self.extract_dynamic_params(candidate.get("list_size")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for candidate in self._config.get("candidates"):
      attrs.add(candidate.get("save_candidate_to_attr"))
    return attrs

class ExploreRerankDPPListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_gen_list_by_dpp"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("candidate_attrs"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_hetu_l1_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("beam_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("sim_matrix_norm_type")))
    attrs.update(self.extract_dynamic_params(self._config.get("sim_matrix_mix_param")))
    attrs.update(self.extract_dynamic_params(self._config.get("relevance_score_theta")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_list_to_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("hetu_l1_attr"))
    attrs.add(self._config.get("mmu_emb_attr"))
    attrs.add(self._config.get("mc_emb_attr"))
    return attrs

class ExploreRerankListFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_gen_list_feature"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("origin_result_list_attr"))
    table_name = self._config.get("origin_result_table", "")
    name_set = extract_attr_names(self._config.get("feature_attrs", []), "name")
    attrs.update(try_add_table_name(table_name, name_set))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    as_set = extract_attr_names(self._config.get("feature_attrs", []), "as")
    list_size = int(self._config.get("origin_result_list_size"))
    for attr in as_set:
      for i in range(list_size):
        attrs.add(attr + "_idx" + str(i))
    return attrs

class ExploreRerankDispatchListScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_dispatch_list_score"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("origin_result_list_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    table_name = self._config.get("origin_result_table", "")
    attrs.update(try_add_table_name(table_name, {self._config.get("save_list_score_to_attr", [])}))
    return attrs

class ExploreRerankSSDListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_gen_list_by_ssd"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("candidate_attrs"))
    attrs.update(self.extract_dynamic_params(self._config.get("max_hetu_l1_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("list_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("rank_score_weight")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_list_to_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    # attrs.add(self._config.get("mmu_emb_attr"))
    attrs.add(self._config.get("hetu_l1_attr"))
    attrs.add(self._config.get("mc_emb_attr"))
    return attrs

class ExploreRerankRandomEnsembleCandidateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_gen_random_ensemble_candidate"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for queue in self._config.get("queues"):
      attrs.update(self.extract_dynamic_params(queue.get("enable")))
      attrs.update(self.extract_dynamic_params(queue.get("weight")))
      attrs.update(self.extract_dynamic_params(queue.get("disturbance_range")))
      attrs.update(self.extract_dynamic_params(queue.get("pow_alpha")))
      attrs.update(self.extract_dynamic_params(queue.get("pow_beta")))
    attrs.update(self.extract_dynamic_params(self._config.get("target_candidate_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("ensemble_algorithm")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_candidate_to_attr"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for queue in self._config.get("queues"):
      attrs.add(queue.get("score_attr"))
    return attrs

class ExploreRerankSelectListEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_select_list"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("list_attrs"))
    attrs.update(self.extract_dynamic_params(self._config.get("limit_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_set_dedup")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_set_dedup_cnt")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mmu_emb_attr"))
    return attrs

class ExploreRerankGenListByModelOldEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_gen_list_by_model_old"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("candidate_attrs"))
    attrs.update(self.extract_dynamic_params(self._config.get("seq_len")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_type"))
    return attrs

class ExploreRelatedRankScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_related_rank_score_enricher"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for queue in self._config.get("queues"):
      attrs.update(self.extract_dynamic_params(queue.get("enable")))
      attrs.update(self.extract_dynamic_params(queue.get("weight")))
      attrs.update(queue.get("source_attr"))
      attrs.update(queue.get("item_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_score_to_attr"))
    return attrs
