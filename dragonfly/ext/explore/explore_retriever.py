#!/usr/bin/env python3
# coding=utf-8
"""
filename: explore_retriever.py
description: 
author: h<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-12-10 18:00:00
"""

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever

class ExploreKnowledgeMidPhotoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_by_knowledge_mid_photo"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("memory_data_ptr_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("play_time_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("duration_time_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("trigger_key_prefix")))
    return attrs

class ExploreNnUserPhotoRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_by_nn_user_photo"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_ptr_attr"))
    attrs.add(self._config.get("user_emb_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    return attrs

class ExploreEmbeddingIcfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_by_embedding_icf"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("service_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("config_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_key")))
    attrs.update(self.extract_dynamic_params(self._config.get("use_simple_interface")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms"))) 
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit")))
    return attrs

class ExploreRedisListRangeRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_by_redis_list_range"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("key_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster_name")))
    if "key_prefix" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("key_prefix")))
    if "timeout_ms" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    if "retrieve_num_per_key" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num_per_key")))
    if "retrieve_num" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("retrieve_num")))
    if "cal_score_type" in self._config:
      attrs.update(self.extract_dynamic_params(self._config.get("cal_score_type")))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "save_score_to_attr" in self._config:
      attrs.add(self._config.get("save_score_to_attr"))
    return attrs

class ExploreRecoSimilarKessI2iRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_explore_reco_similar"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(["featureSourcePId", "sourcePidAuthorId"])
    for name in ["total_limit"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class ExploreFountainRecoItemCfRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_explore_fountain_itemcf"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.add(self._config.get("user_info_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("total_limit", 2000)))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms", 300)))
    attrs.update(self.extract_dynamic_params(self._config.get("only_fountain_behavior", 0)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class ExploreCacheStringRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_retrieve_by_cache_string"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("cache_string_attr"))
    return attrs

class ExploreRedisRecommendByFriendRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_redis_recommend_by_friend_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["timeout_ms"]))
    attrs.update(self.extract_dynamic_params(self._config["retrieve_num"]))
    attrs.update(self.extract_dynamic_params(self._config["key_prefix"]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for name in ['save_friends_to_attr', 'record_attr']:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class ExploreRerankListRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "explore_rerank_list_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("list_attrs"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_list_to_attr"))
    attrs.add(self._config.get("save_key_list_to_attr"))
    attrs.add(self._config.get("save_candidate_list_source_to_attr"))
    attrs.add(self._config.get("save_list_source_to_attr"))
    return attrs
