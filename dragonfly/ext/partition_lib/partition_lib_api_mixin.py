#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .partition_lib_enricher import *
from .partition_lib_retriever import *

class PartitionLibMixin(CommonLeafBaseMixin):
  """
  PartitionLib API 接口的 Mixin 实现

  使用 partition_keeper 管理服务的数据，使 dragon 本身变为有状态服务。
  需要使用 kai-serving 的 dragon_partition_server 服务类型

  在 server flags 里需要配置
  --cloud_port_num=32
  --log_dir=../log/
  --general_shard_health_check=true

  调用需要在 delegate 相关 processor 使用 partition_service_kconf ，或者使用 teams/reco-arch/colossusdb/client/dragon/dragon_client.h 中的接口

  维护团队： xinchenghua, zhoujindong, lixutong03, zhaoyonghui
  """

  def get_item_attr_by_local_shard_index(self, **kwargs):
    """
    LocalPartitionShardIndexItemAttrEnricher
    ------
    从[本地索引]获取中台格式索引数据(同 CommonIndex 数据格式)，读取其中属性作为 ItemAttr 写入 Context 供后续 Processor 使用

    参数配置
    ------

    `attrs`: [list] 选配项，改列表中的 value 支持一种格式的值：
      - string 格式：可直接把 proto 中同名的属性填入同名 item_attr 中
      - json 格式：支持 { "name": "author_id", "type": "int", "class_name": "xxx", "as": "local_author_id"} 类型为 int, float, string, int_list, float_list, string_list 之一

    `attr_name_types`: [dict] 选配项，指定需要获取的正排属性名称 attr name 与类型 attr type 的键值对，类型为 int, float, string, int_list, float_list, string_list 之一

    `item_id_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，否则使用 item_key 解析出来的 item_id

    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `save_item_info_to_attr`: [string] 选配项，可将分布式索引返回的 PhotoInfo 直接存到指定的 extra 类型 ItemAttr 中，默认不存

    `table_name`: [string] 选配项，kv 服务的表名，默认值为空只能请求单表服务

    注意： `attrs` 与 `attr_name_types` 只能选择其中一种方式，此外 `attrs` 方式较新，无需指定类型，与其它 enricher 的配置方式类似，需要分布式索引服务也支持此接口。

    调用示例
    ------
    ``` python
    # 新方式，推荐
    .get_item_attr_by_local_shard_index(
      attrs = [
        "photo_id",
        "caption",
        # 可以强制指定类型，主要用于那些历史上同名 attr 存在多种类型的情况，此时索引服务返回的类型可能不准确
        { "name": "author_id", "type": "int" },
        "subtitle",
      ],
    )

    # 老方式
    .get_item_attr_by_local_shard_index(
      attr_name_types = {
        "photo_id" : "int",
        "caption" : "float",
        "subtitle" : "string",
      }
    )
    ```
    """

    self._add_processor(LocalPartitionShardIndexItemAttrEnricher(kwargs))
    return self

  def recall_platform_global_init(self, **kwargs):
    """
    RecallPlatformGlobalInit
    ------
    召回平台的全局初始化配置

    参数配置
    ------

    `cpu_thread_pool_thread_num`: [int] 选配项，服务使用的全局线程池线程数

    `cpu_thread_pool_queue_max_capacity`: [int] 选配项，服务使用的全局线程池任务队列数

    调用示例
    ------
    ``` python
    .recall_platform_global_init(
        cpu_thread_pool_thread_num=64,
        cpu_thread_pool_queue_max_capacity=1024
    )
    ```
    """
    self._add_processor(RecallPlatformGlobalInit(kwargs))
    return self

  def get_local_partition_embedding(self, **kwargs):
    """
    LocalPartitionEmbeddingAttrEnricher
    ------
    `parameter_input_attr`: [string] embedding sign 输入的 attr, 默认 "sharded_key_list

    `parameter_hit_attr`: [string] 命中 embedding sign 输出的 attr, 默认 "hit_key_list"

    `embedding_output_attr`: [string] 所有命中 embedding 按照 parameter_hit_attr 输出顺序拼接后输出的 attr, 默认 "embedding_output_attr"

    `is_common_attr`: [bool] 是否读写 common attr, 默认 True

    `colossusdb_table_name`: [string] colossusdb 表名，必填项

    调用示例
    ------
    ``` python
    .get_local_partition_embedding(
        parameter_input_attr="sign_input_attr",
        parameter_hit_attr="sign_hit_attr",
        embedding_output_attr="embedding_output",
        colossusdb_table_name="embeding_table0"
    )
    ```
    """
    self._add_processor(LocalPartitionEmbeddingAttrEnricher(kwargs))
    return self

  def retrieve_by_local_shard_inverted_index(self, **kwargs):
    """
    LocalShardInvertedIndexRetriever
    ------
    从本地部署的统一存储（ colossusdb ）版本的倒排索引通过 Query 语句进行 item 召回. Query语法跟retrieve_by_remote_index()一致

    参数配置
    ------
    `table_name`: [string] 本地colossusdb倒排的表名，默认值为空只能请求单表服务

    `max_query_time_ms`: [int] [动态参数] 本地倒排查询的超时时间，默认值为0，表示不设置超时时间

    `keep_incomplete_result`: [bool] [动态参数] 如果设置了max_query_time_ms，本地倒排检索超过这个时间后，会返回当前为止检索到的结果列表，表示是否需要保留此不完整结果列表，默认为false不保留。

    """
    self._add_processor(LocalShardInvertedIndexRetriever(kwargs))
    return self

  def dragon_partition_delegate_enricher(self, **kwargs):
    """
    DragonPartitionDelegateEnricher
    ------
    `request_type`: [string] 发送到下游 request_type, 默认和当前请求的 request_type 一致, 选配项

    `partition_service_kconf`: [string] kconf client path, 必填项

    `key_list_common_attr`: [string] 查询 dragon partition 使用的 key list, 必填项

    `sharded_key_list_common_attr`: [string] key_list_common_attr 中的 key 拆包后写到下游 request 的 common attr, 默认值 "sharded_key_list", 选配项

    `hit_key_list_common_attr`: [string] 命中 key 输出的 attr, 默认值 "hit_key_list", 选配项

    `returned_value_list_common_attr`: [string] 命中 value 按照命中 key 顺序拼接起来的输出 attr, 必填项

    `ttl_seconds`: [int] 成员变量 request 的 ttl 时间，单位为秒，默认值为 3600 秒, 选配项

    `random_shift_window`: [int] ttl 随机 shift 的时间窗口，单位为秒，默认值为 120 秒, 选配项

    调用示例
    ------
    ``` python
    .dragon_partition_delegate_enricher(
        request_type="embedding",
        partition_service_kconf="colossus.embedding.ann_demo",
        key_list_common_attr="user_id_list",
        sharded_key_list_common_attr=“sharded_user_id_list",
        hit_key_list_common_attr="hit_user_id_list",
        returned_value_list_common_attr="embedding_output",
    )
    ```
    """
    self._add_processor(DragonPartitionDelegateEnricher(kwargs))
    return self