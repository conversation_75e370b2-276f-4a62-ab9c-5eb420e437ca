#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, ArgumentError
from ...common_leaf_processor import LeafEnricher

class LocalPartitionShardIndexItemAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_item_attr_by_local_shard_index"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attrs" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attrs", []) }
    if "attr_name_types" in self._config:
      attrs = { key for key in self._config.get("attr_name_types", {}).keys() }
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attr = self._config.get("item_id_attr", "")
    return { attr } if attr else set()

  def is_valid_type_str(self, type_name : str) -> bool:
    return type_name in ['int', 'float', 'string', 'int_list', 'float_list', 'string_list']

  @property
  @strict_types
  def is_more_config(self) -> bool:
    """ 是否支持除了 name 和 type 外的更多配置，比如 class_name 或 其它 """
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        if not sorted(["name", "type"]) == sorted(list(v.keys())):
          return True
    return False

  @property
  @strict_types
  def flat_index_item_attrs_with_type(self) -> dict:
    attrs_with_type = {}
    for v in self._config.get("attrs", []):
      if isinstance(v, dict):
        attrs_with_type[v["name"]] = v.get("type", "auto")
      else:
        attrs_with_type[v] = "auto"
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_type[name] = t
    return attrs_with_type

  @property
  @strict_types
  def flat_index_item_attrs_with_more_config(self) -> list:
    attrs_with_more_config = []
    for v in self._config.get("attrs", []):
      t = {}
      if isinstance(v, dict):
        if "class_name" in v.keys():
          v["class_name"] = v["class_name"].replace("::", ".")
        t.update(v)
      else:
        t.update(dict(name=v))
      t.setdefault("type", "auto")
      attrs_with_more_config.append(t)
    for name, t in self._config.get("attr_name_types", {}).items():
      attrs_with_more_config.append({"name": name, "type": t})
    return attrs_with_more_config

  @strict_types
  def _check_config(self) -> None:
    if "save_item_info_to_attr" in self._config:
      raise ArgumentError(f"{self.get_type_alias()} does not support 'save_item_info_to_attr'")
    count = 0
    if "attrs" in self._config:
      count += 1
      attr_set = set()
      for v in self._config.get("attrs", []):
        attr_name = v if isinstance(v, str) else v.get("as", v["name"])
        if isinstance(v, dict) and "as" in v and v['as'] in attr_set:
          raise ArgumentError(f"{self.get_type_alias()} 包含重复的重命名 attr: {v['as']}")
        attr_set.add(attr_name)
        if isinstance(v, dict) and "type" in v.keys() and not self.is_valid_type_str(v["type"]):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v['type']}")
    if "attr_name_types" in self._config:
      count += 1
      for v in self._config.get("attr_name_types", {}).values():
        if not self.is_valid_type_str(v):
          raise ArgumentError(f"{self.get_type_alias()} invalid type name: {v}")
    if count != 1:
      raise ArgumentError(f"{self.get_type_alias()} 'attrs' 或者 'attr_name_types' 中必须配置其中一个")

class RecallPlatformGlobalInit(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "recall_platform_global_init"

class LocalPartitionEmbeddingAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_local_partition_embedding"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "is_common_attr" not in self._config or self._config["is_common_attr"]:
      attrs.add("parameter_input_attr" if "parameter_input_attr" not in self._config else self._config["parameter_input_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if "is_common_attr" not in self._config or self._config["is_common_attr"]:
      attrs.add("parameter_hit_attr" if "parameter_hit_attr" not in self._config else self._config["parameter_hit_attr"])
      attrs.add("embedding_output_attr" if "embedding_output_attr" not in self._config else self._config["embedding_output_attr"])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "is_common_attr" in self._config and not self._config["is_common_attr"]:
      attrs.add("sharded_key_list" if "parameter_input_attr" not in self._config else self._config["parameter_input_attr"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "is_common_attr" in self._config and not self._config["is_common_attr"]:
      attrs.add("hit_key_list" if "parameter_hit_attr" not in self._config else self._config["parameter_hit_attr"])
      attrs.add("embedding_output_attr" if "embedding_output_attr" not in self._config else self._config["embedding_output_attr"])
    return attrs

  @strict_types
  def depend_on_items(self) -> bool:
    return "is_common_attr" in self._config and not self._config["is_common_attr"]

  @strict_types
  def _check_config(self) -> None:
    if "colossusdb_table_name" not in self._config:
      raise ArgumentError(f"colossusdb_table_name not defined in config: {self._config}")

class DragonPartitionDelegateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dragon_partition_delegate_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["key_list_common_attr"]}

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {self._config["hit_key_list_common_attr"], self._config["returned_value_list_common_attr"]}

  @strict_types
  def _check_config(self) -> None:
    if "partition_service_kconf" not in self._config:
      raise ArgumentError(f"partition_service_kconf not defined in config: {self._config}")
    if "key_list_common_attr" not in self._config:
      raise ArgumentError(f"key_list_common_attr not defined in config: {self._config}")
    if "hit_key_list_common_attr" not in self._config:
      raise ArgumentError(f"hit_key_list_common_attr not defined in config: {self._config}")
    if "returned_value_list_common_attr" not in self._config:
      raise ArgumentError(f"returned_value_list_common_attr not defined in config: {self._config}")
