#!/usr/bin/env python3
# coding=utf-8

# import operator
# import itertools

from ...common_leaf_util import strict_types, check_arg, extract_common_attrs, \
  check_common_query_syntax, extract_attr_names
from ...common_leaf_processor import LeafRetriever
from ...ext.common.common_leaf_retriever import CommonRecoCommonQueryRetriever

class LocalShardInvertedIndexRetriever(CommonRecoCommonQueryRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_local_shard_inverted_index"

  @strict_types
  def _check_config(self) -> None:
    super()._check_config()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("browsed_item_count")))
    attrs.update(self.extract_dynamic_params(self._config.get("common_query")))
    attrs.add(self._config.get("exclude_items_in_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for key in ["save_score_to_attr", "save_query_index_to_attr"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs