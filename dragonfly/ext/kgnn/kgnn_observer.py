#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, ArgumentError
from ...common_leaf_processor import LeafObserver

class KgnnDummyObserver(LeafObserver):
  """
  KgnnDummyObserver 提供一个同步 processor 等待异步 processor 的功能
  啥都不干，等待 Kgnn 中一些异步 processor 执行完成
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dummy_wait"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "input_common_attr" in self._config:
      attrs = attrs.union(self._config["input_common_attr"] if isinstance(
          self._config["input_common_attr"], list) else [self._config["input_common_attr"]])
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "input_item_attr" in self._config:
      attrs = attrs.union(self._config["input_item_attr"] if isinstance(self._config["input_item_attr"], list)
                          else [self._config["input_item_attr"]])
    return attrs


class KgnnUpdateSI2DCObserver(LeafObserver):
  """ KgnnUpdateSI2DC 专门处理 src 为 item 节点, dst 为 context 节点时候的相关情况。
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_si2dc"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['dst_attr'])
    if "timestamp_attr" in self._config:
      ret.add(self._config["timestamp_attr"])
    if "dst_is_common_attr" in self._config and self._config['dst_is_common_attr']:
      ret.add(self._config['dst_weight_attr'])
    if "dst_node_attr" in self._config:
      for attr in self._config["dst_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["dst_node_attr"].float_config:
        ret.add(attr['name'])

    if "kess_service" in self._config:
      ret.update(self.extract_dynamic_params(self._config["kess_service"]))
    if "btq_prefix" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_prefix"]))
    if "relation_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["relation_name"]))
    if "table_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["table_name"]))
    if "shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["shard_num"]))
    if "btq_shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_shard_num"]))
    if "batching_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["batching_num"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config['src_attr'])
    if "dst_w_is_common_attr" in self._config and not self._config['dst_w_is_common_attr']:
      ret.add(self._config['dst_weight_attr'])
    optional_attrs = ["src_attr_filter", "timestamp_attr", "expire_ts_update_flag_attr", "use_item_ts_for_expire_attr"]
    for one_attr in optional_attrs:
      if one_attr in self._config:
        ret.add(self._config[one_attr])
    if "src_node_attr" in self._config:
      for attr in self._config["src_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["src_node_attr"].float_config:
        ret.add(attr['name'])
    if "dst_node_item_attr" in self._config:
      for attr in self._config["dst_node_item_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["dst_node_item_attr"].float_config:
        ret.add(attr['name'])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_common_attr" in self._config:
      ret.add(self._config["output_common_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    essential_args = ["src_attr", "dst_attr"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in config: {self._config}")

    if "timeout_ms" not in self._config:
      self._config['timeout_ms'] = 200

    if "dst_weight_attr" in self._config:
      if "dst_w_is_common_attr" not in self._config:
        raise ArgumentError(
            f"dst_weight_attr defined but dst_w_is_common_attr not defined in config: {self._config}")
      if not isinstance(self._config['dst_w_is_common_attr'], bool):
        raise ArgumentError(
            f"dst_w_is_common_attr is not bool type: {type(self._config['dst_w_is_common_attr'])}")

    if 'dst_node_attr' in self._config and 'dst_node_item_attr' in self._config:
      raise ArgumentError(f"dst_node_attr and dst_node_item_attr cannot be set both: {self._config}")

    if "batching_num" not in self._config:
      self._config["batching_num"] = 1

    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
      
    if "table_name" in self._config and "btq_shard_num" not in self._config:
      raise ArgumentError("btq_shard_num must be set when table_name defined")
    
    if "relation_name" in self._config:
      if "kess_service" in self._config and "btq_prefix" in self._config:
        raise ArgumentError("kess_service and btq_prefix cannot be set both")
      if "kess_service" not in self._config and "btq_prefix" not in self._config:
        raise ArgumentError("either kess_service or btq_prefix must be set")
      
    if "table_name" in self._config:
      if "btq_prefix" not in self._config:
        raise ArgumentError("btq_prefix must be set")

  @strict_types
  def is_async(self) -> bool:
    return True


class KgnnUpdateSC2DIObserver(LeafObserver):
  """ KgnnUpdateSC2DI 专门处理 src 为 context 节点, dst 为 item 节点时候的相关情况。
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_sc2di"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config['src_attr'])
    if "timestamp_attr" in self._config:
      ret.add(self._config["timestamp_attr"])
    if "dst_w_is_common_attr" in self._config and self._config['dst_w_is_common_attr']:
      ret.add(self._config['dst_weight_attr'])
    if "src_node_attr" in self._config:
      for attr in self._config["src_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["src_node_attr"].float_config:
        ret.add(attr['name'])

    if "kess_service" in self._config:
      ret.update(self.extract_dynamic_params(self._config["kess_service"]))
    if "btq_prefix" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_prefix"]))
    if "relation_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["relation_name"]))
    if "table_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["table_name"]))
    if "shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["shard_num"]))
    if "btq_shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_shard_num"]))
    if "batching_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["batching_num"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config['dst_attr'])
    if "dst_w_is_common_attr" in self._config and not self._config['dst_w_is_common_attr']:
      ret.add(self._config['dst_weight_attr'])
    optional_attrs = ["dst_attr_filter", "timestamp_attr", "expire_ts_update_flag_attr", "use_item_ts_for_expire_attr"]
    for one_attr in optional_attrs:
      if one_attr in self._config:
        ret.add(self._config[one_attr])
    if "dst_node_attr" in self._config:
      for attr in self._config["dst_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["dst_node_attr"].float_config:
        ret.add(attr['name'])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_common_attr" in self._config:
      ret.add(self._config["output_common_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    essential_args = ["src_attr", "dst_attr"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in config: {self._config}")

    if "timeout_ms" not in self._config:
      self._config['timeout_ms'] = 200

    if "dst_weight_attr" in self._config:
      if "dst_w_is_common_attr" not in self._config:
        raise ArgumentError(
            f"dst_weight_attr defined but dst_w_is_common_attr not defined in config: {self._config}")
      if not isinstance(self._config['dst_w_is_common_attr'], bool):
        raise ArgumentError(
            f"dst_w_is_common_attr is not bool type: {type(self._config['dst_w_is_common_attr'])}")

    if "batching_num" not in self._config:
      self._config["batching_num"] = 1

    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
      
    if "table_name" in self._config and "btq_shard_num" not in self._config:
      raise ArgumentError("btq_shard_num must be set when table_name defined")
    
    if "relation_name" in self._config:
      if "kess_service" in self._config and "btq_prefix" in self._config:
        raise ArgumentError("kess_service and btq_prefix cannot be set both")
      if "kess_service" not in self._config and "btq_prefix" not in self._config:
        raise ArgumentError("either kess_service or btq_prefix must be set")
      
    if "table_name" in self._config:
      if "btq_prefix" not in self._config:
        raise ArgumentError("btq_prefix must be set")

  @strict_types
  def is_async(self) -> bool:
    return True

class KgnnUpdateInnerItemObserver(LeafObserver):
  """ KgnnUpdateInnerItem 专门处理 src 和 dst 均为 item 节点时候的相关情况。
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "update_inner_item"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "timestamp_attr" in self._config:
      ret.add(self._config["timestamp_attr"])

    if "kess_service" in self._config:
      ret.update(self.extract_dynamic_params(self._config["kess_service"]))
    if "btq_prefix" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_prefix"]))
    if "relation_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["relation_name"]))
    if "table_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["table_name"]))
    if "shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["shard_num"]))
    if "btq_shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["btq_shard_num"]))
    if "batching_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["batching_num"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config['src_attr'])
    ret.add(self._config['dst_attr'])
    optional_attrs = ["dst_weight_attr", "timestamp_attr", "expire_ts_update_flag_attr", "use_item_ts_for_expire_attr"]
    for one_attr in optional_attrs:
      if one_attr in self._config:
        ret.add(self._config[one_attr])
    if "src_node_attr" in self._config:
      for attr in self._config["src_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["src_node_attr"].float_config:
        ret.add(attr['name'])
    if "dst_node_attr" in self._config:
      for attr in self._config["dst_node_attr"].int64_config:
        ret.add(attr['name'])
      for attr in self._config["dst_node_attr"].float_config:
        ret.add(attr['name'])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "output_common_attr" in self._config:
      ret.add(self._config["output_common_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    essential_args = ["src_attr", "dst_attr"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in config: {self._config}")

    if "timeout_ms" not in self._config:
      self._config['timeout_ms'] = 200

    if "batching_num" not in self._config:
      self._config["batching_num"] = 1

    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
      
    if "table_name" in self._config and "btq_shard_num" not in self._config:
      raise ArgumentError("btq_shard_num must be set when table_name defined")
    
    if "relation_name" in self._config:
      if "kess_service" in self._config and "btq_prefix" in self._config:
        raise ArgumentError("kess_service and btq_prefix cannot be set both")
      if "kess_service" not in self._config and "btq_prefix" not in self._config:
        raise ArgumentError("either kess_service or btq_prefix must be set")
      
    if "table_name" in self._config:
      if "btq_prefix" not in self._config:
        raise ArgumentError("btq_prefix must be set")

  @strict_types
  def is_async(self) -> bool:
    return True
