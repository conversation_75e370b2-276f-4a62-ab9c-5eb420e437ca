#!/usr/bin/env python3
# coding=utf-8

import json
from typing import List
from ...common_leaf_util import strict_types, check_arg, ArgumentError


class NodeAttrSchema:
  """ NodeAttrSchema 提供节点的属性格式表述 api.
  在 python 侧封装描述性语言, 生成 json 配置文件, 与 c++ 组件进行交互.
  c++ 侧拿到的配置格式例子:
  { 
    "attr_schema":{
      "int_max_size":4,
      "float_max_size":4,
      "int64_config":[
         {
            "name":"a1",
            "is_list":false,
            "max_size":1
         },
         {
            "name":"a2",
            "is_list":true,
            "max_size":2
         }
      ],
      "float_config":[
         {
            "name":"b1",
            "is_list":false,
            "max_size":1
         },
         {
            "name":"b2",
            "is_list":true,
            "max_size":3
         }
      ],
      "degree_output": "degree",
      "edge_num_output": "edge_num",
    }
  }
  """

  def __init__(self, int64_max_size: int, float_max_size: int) -> None:
    """ 初始化声明的 max_size 需要和存储侧的预分配对齐.
    """
    self.int_max_size = int64_max_size
    self.float_max_size = float_max_size
    # element: (attr_name, is_list, max_len)
    self.int64_config = []
    self.float_config = []
    self.degree_output = None
    self.edge_num_output = None

  @strict_types
  def add_int64_attr(self, attr_name: str) -> 'NodeAttrSchema':
    int_attr_names = set([i["name"] for i in self.int64_config])
    if attr_name in int_attr_names:
      raise ArgumentError(f"int64 attr name {attr_name} is exist, table = {int_attr_names}")
    self.int64_config.append({"name": attr_name, "is_list": False, "max_size": 1})
    return self

  @strict_types
  def add_int64_list_attr(self, attr_name: str, max_size: int) -> 'NodeAttrSchema':
    int_attr_names = set([i["name"] for i in self.int64_config])
    if attr_name in int_attr_names:
      raise ArgumentError(f"int64 attr name {attr_name} is exist, table = {int_attr_names}")
    self.int64_config.append({"name": attr_name, "is_list": True, "max_size": max_size})
    return self

  @strict_types
  def add_float_attr(self, attr_name: str) -> 'NodeAttrSchema':
    float_attr_names = set([i["name"] for i in self.float_config])
    if attr_name in float_attr_names:
      raise ArgumentError(f"float attr name {attr_name} is exist, table = {float_attr_names}")
    self.float_config.append({"name": attr_name, "is_list": False, "max_size": 1})
    return self

  @strict_types
  def add_float_list_attr(self, attr_name: str, max_size: int) -> 'NodeAttrSchema':
    float_attr_names = set([i["name"] for i in self.float_config])
    if attr_name in float_attr_names:
      raise ArgumentError(f"float attr name {attr_name} is exist, table = {float_attr_names}")
    self.float_config.append({"name": attr_name, "is_list": True, "max_size": max_size})
    return self

  @strict_types
  def set_degree(self, output_name: str) -> 'NodeAttrSchema':
    self.degree_output = output_name
    return self

  @strict_types
  def set_edge_num(self, output_name: str) -> 'NodeAttrSchema':
    self.edge_num_output = output_name
    return self

  def output_attr_names(self) -> set:
    result = set()
    result = result.union(set([i["name"] for i in self.int64_config]))
    result = result.union(set([i["name"] for i in self.float_config]))
    if self.degree_output:
      result = result.union(set([self.degree_output]))
    if self.edge_num_output:
      result = result.union(set([self.edge_num_output]))
    return result

  def check_valid(self):
    int_config_size = sum([x["max_size"] for x in self.int64_config])
    if int_config_size > self.int_max_size:
      raise ArgumentError(
          f"int max size is too small: {self.int_max_size}; now int config = {self.int64_config}")
    float_config_size = sum([x["max_size"] for x in self.float_config])
    if float_config_size > self.float_max_size:
      raise ArgumentError(
          f"float max size is too small: {self.float_max_size}; now float config = {self.float_config}")
