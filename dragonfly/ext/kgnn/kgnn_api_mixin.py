#!/usr/bin/env python3
# coding=utf-8
"""
filename: kgnn_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, kgnn api mixin
author: <EMAIL>
date: 2021-03-10 15:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .kgnn_enricher import *
from .kgnn_observer import *

class KgnnApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 Kgnn 调用相关的 processor 接口.
  """

  def fetch_kgnn_neighbors(self, **kwargs):
    """
    KgnnByRelationEnricher
    ------
    向 kgnn 的某个 relation/table 下批量请求一次邻居采样操作, 并将结果填充到指定字段.
    id_from_common_attr 和 id_from_item_attr 只能配置一个，否则请配置两个单独的 processor.
    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.

    参数配置
    ------
    `id_from_common_attr`: [string] 从指定 common_attr 获取 ids 列表，为空则不读 common_attr, 要求属性为 int 或者 intlist。

    `id_from_item_attr`: [string] 从指定的 item_attr 字段获取 ids 列表, 为空则不读 item_attr, 要求属性为 int 或者 intlist。

    `save_neighbors_to`: [string]
      结果的写入属性名, 写入结果为一个一维数组, len = sample_num * len(common_attr)
      如果配置了 id_from_common_attr, 则结果写入 common_attr, 否则写入 item_attr 内

    `save_timestamp_to`: [string]
      (可选) timestamp 写入属性名，每个source node对应一个一维数组
      如果配置了 id_from_common_attr, 则结果写入 common_attr, 否则写入 item_attr 内

    `save_weight_to`: [string]
      (可选) weight 写入属性名，每个source node对应一个一维数组
      如果配置了 id_from_common_attr, 则结果写入 common_attr, 否则写入 item_attr 内

    `attr_schema`: [NodeAttrSchema]
      (可选) 进行邻居采样的同时，也将源节点的属性取出来。作用同 get_side_info, 但是不能取 degree 等 meta 信息。
      如果配置了 id_from_common_attr, 则结果写入 common_attr, 否则写入 item_attr 内.

    `edge_attr_schema`: [NodeAttrSchema]
      (可选) 边 side info 的配置，每个 dest node 唯一地对应一条边，也唯一地对应一组数据.
      要求这个 schema 把所有 side info 属性按顺序依次声明出来, 而不能只声明其中的一部分.
      如果配置了 id_from_common_attr, 则结果写入 common_attr, 否则写入 item_attr 内.
      所有数据依次排列，即返回结果依次是 src1-dest1、src1-dest2、...、src2-dest1、... 的边属性.
      所有的输出统一写为 intlist 和 floatlist 类型（即使某个边属性是单个值）.

    `kess_service`: [string][动态参数] kgnn 请求服务的 kess 服务名 (v1 使用)

    `model_name`: [string][动态参数] colossusdb kgnn 请求服务对应的 kaiserving 上的模型名称 (v2 使用)

    `relation_name`: [string][动态参数] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string][动态参数] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int][动态参数] kgnn 存储的shard num (relation_name 设置时使用)

    `sample_num`: [int][动态参数] 每个 src_id 采样几个邻居.

    `timeout_ms`: [int][动态参数] (可选) 请求远程服务的超时时间，默认值为 200

    `timestamp_attr_is_common`: [bool] (可选) 指定下面两个 timestamp_from_attr 和 min_timestamp_from_attr 的来源是否是 common。
      不影响 attr 读取的流程，只用于上下游 processor 的依赖分析。
      如果你设置 timestamp_attr 是同步的过程（比如在本地使用 lua 脚本完成 attr 赋值），则无需设置此 bool 值。

    `timestamp_from_attr`: [string] (可选) 从指定的 attr 字段采样结果允许的最大时间戳（秒），要求属性为 int 或者 float；读不到则不受时间戳相关的影响.
      attr 是 common 或 item 都可以，优先从 common 里查找，如果找不到再找 item，并且从多个 item attr 的值中取最大作为最终值。

    `min_timestamp_from_attr`: [string] (可选) 从指定的 attr 字段采样结果允许的最小时间戳（秒），要求属性为 int 或者 float；读不到则不受时间戳相关的影响.
      attr 是 common 或 item 都可以，优先从 common 里查找，如果找不到再找 item，并且从多个 item attr 的值中取最大（没错，还是取最大）作为最终值。

    `sample_type`: [string][动态参数] (可选) 采样方式, 默认为随机采样. 合法类型：
    - random: 随机采样. <- 默认值
    - weight: 带权采样.
    - most_recent:  按照时间排序, 召回最近的边.
    - least_recent: 按照时间排序, 召回最远的边.
    - topn: 按照权重返回最大的 topN 个数据.

    `padding_type`: [string][动态参数] (可选) 采样选项，当邻居个数不足 sample_num / 节点不存在的时候的默认行为. 合法类型：
    - zero: 节点用 0 填充.
    - self: 填节点 id 本身. <- 默认值.
    - neigh_loop: 使用部分邻居做循环填充, 如果一个邻居也没有, 则会走 self 逻辑.
    - no_padding: 将有效结果尽可能填到开头的连续空间内，可能导致结果长度小于 src_id_size * sample_num。此时无法将结果与源节点相对应。

    `min_weight`: [float] (可选) 采样权重阈值，权重小于它的节点不会进入采样池，默认值为 0

    `sample_without_replacement`: [bool] (可选) 是否做无放回采样，默认值为 false。
      这意味着默认情况下，从长度为1的边表做随机或带权采样会得到 n 个一样的结果，不触发 padding。
      当 sample_type 是 topn 或 recent 类时，此参数无意义。

    `is_async`: [bool] (可选) 是否用异步模式请求，默认值为 true

    `response_id_batch_size`: [int] (可选) 当请求很大时 client 会将其拆分为多个小请求，此参数指定每个小请求返回 id 的大致个数，默认值为 10240。
      减小此设置会让 client 将请求拆分成更多个更小的请求，使得每个请求的响应速度变快；但也会提升 QPS，对服务端造成更大的压力。

    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    调用示例
    ------
    ``` python
    .fetch_kgnn_neighbors(
      id_from_common_attr="uId",
      save_neighbors_to='pId',
      save_timestamp_to='neighbors_ts',
      save_weight_to='neighbors_weight',
      attr_schema=NodeAttrSchema(1, 0).add_int64_attr('user_age'),
      edge_attr_schema=NodeAttrSchema(1, 0).add_int64_attr('watch_time'),
      kess_service="xxxxx",
      relation_name='U2I',
      shard_num=16,
      sample_num={{"attr_A"}},
      timeout_ms=100,
      timestamp_from_attr="timeAttr_sec",
      sample_type="weight",
      padding_type="zero",
      min_weight=0.1,
      sample_without_replacement=False)
    ```
    """

    self._add_processor(KgnnByRelationEnricher(kwargs))
    return self

  def random_sample(self, **kwargs):
    """
    KgnnRandomEnricher
    ------
    向 kgnn 的某个 relation/table 下批量请求一次全局随机采样，可以用作全局负采样的结果.
    id_from_common_attr 和 id_from_item_attr 最多配置一个，否则请配置两个单独的 processor,
    这里配置 id_from_common_attr 或 id_from_item_attr 不是用来从这些属性读取数据，而是需要根据
    这些 attr 中 id 的数量，采样 sample_num 倍的随机 node；也可以完全不配置 id_from_common_attr
    和 id_from_item_attr 中的任何一个，这时候会采样固定数量的 sample_num 个随机 node, 且结果保存
    到 common_attr 中。
    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.
    v2 在使用时确保 server 配置了 sample pool。

    参数配置
    ------
    `id_from_common_attr`: [string] 从指定 common_attr 获取 ids 列表长度，要求属性为 int 或者 intlist, 可不提供。

    `id_from_item_attr`: [string] 从指定的 item_attr 字段获取 ids 列表长度, 要求属性为 int 或者 intlist, 可不提供。

    `save_neighbors_to`: [string]
      结果写入属性名。如果配置了 id_from_common_attr， 写入结果为一个一维数组, len = sample_num * len(common_attr), 结果写入 common_attr;
      如果配置了 id_from_item_attr， 写入结果为一个二维数组, 其中每一行 len_i = sample_num * len(item_attr_i), 结果写入 item_attr;
      如果 id_from_common_attr 和 id_from_item_attr 都没配置, 则只采样 sample_num 个 node，结果默认写入 common_attr。

    `attr_schema`: [NodeAttrSchema]
      (可选) 进行随机采样的同时，也将节点的属性取出来。作用同 get_side_info。
      写入 common_attr 还是 item_attr 内的规则与 save_neighbors_to 相同.

    `kess_service`: [string] kgnn 请求服务的 kess 服务名 (v1 使用)

    `model_name`: [string][动态参数] colossusdb kgnn 请求服务对应的 kaiserving 上的模型名称 (v2 使用)

    `relation_name`: [string] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int] kgnn 存储的shard num (relation_name 设置时使用)

    `sample_num`: [int][动态参数] 每个 src_id 随机采样几个 node.

    `fetch_batch_size`: [int] (可选)
      每次 rpc 请求的样本个数，默认 -1 表示根据实际需求来采样，不做 buffer；如果小于实际需求，按照实际需求采样；超过实际需求，剩下的做 buffer 给下次采样使用。

    `timeout_ms`: [int][动态参数] (可选) 请求远程服务的超时时间，默认值为 200

    `sample_from_all`: [bool] (可选) 是否从全局随机采样池进行采样, 默认为 true.
      kgnn 服务端在默认情况下只维护一个采样池, 此参数的值无影响.
      如果服务端配置了两个采样池, 可通过该参数控制从哪个采样池进行采样 (v2 暂不支持)

    `sample_pool_name`: [string][动态参数] (可选) 从指定 name 的采样池采样，需要服务端配置 custom 采样池才有效果。
      v2 只按名字区分 sample pool，默认选择 "default" 采样池。

    调用示例
    ------
    ``` python
    .random_sample(
      id_from_common_attr="uId", # 不用于获取数据，只获取该特征的列表长度，最终会采样得到 sample_num * len(id_from_common_attr) 个
      save_neighbors_to='uId_neg',
      kess_service="xxxxx",
      relation_name='U2I',
      shard_num=16,
      sample_num=5,
      fetch_batch_size=1024,
      timeout_ms=100)
    ```
    或
    ``` python
    .random_sample(
      id_from_item_attr="uId", # 不用于获取数据，只获取该特征的列表长度，最终每行采样得到 sample_num * len(id_from_item_attr[row_i]) 个
      save_neighbors_to='uId_neg',
      kess_service="xxxxx",
      relation_name='U2I',
      shard_num=16,
      sample_num=5,
      fetch_batch_size=1024,
      timeout_ms=100)
    ```
    或
    ``` python
    .random_sample(
      save_neighbors_to='uId_neg', # 结果默认保存到这个名字的 common_attr 中
      kess_service="xxxxx",
      relation_name='U2I',
      shard_num=16,
      sample_num=5, # 最终只采样 sample_num 个
      fetch_batch_size=100,
      timeout_ms=100,
      sample_from_all=false)
    ```
    """

    self._add_processor(KgnnRandomEnricher(kwargs))
    return self

  def iter_nodes(self, **kwargs):
    """
    KgnnIterEnricher
    ------
    在 kgnn 的某个 relation 下进行节点遍历。注意，仅当图在遍历过程中没有被放入新节点时，它才能保证遍历到所有节点。
    当服务端有多个 shard 时，会先遍历 shard0 的所有节点，然后是 shard1，依此类推。
    遍历即将完成一轮时，该轮的最后一次遍历会返回一个非满的结果，如果节点数量恰好能整除 iter_num，则最后一次会返回空结果。
    一轮遍历完成后会从头开始重新遍历。
    colossusdb kgnn (v2) 暂未支持，如有需要请联系 kgnn 负责人.

    参数配置
    ------
    `save_result_to`: [string]
      结果写入属性名。总是写入 common_attr。

    `iter_num`: [int] 每次执行需要遍历并放在返回结果里的节点数

    `kess_service`: [string] kgnn 请求服务的 kess 服务名

    `relation_name`: [string] 采样从哪个 relation 进行

    `shard_num`: [int] kgnn 存储的shard num

    `fetch_batch_size`: [int] (可选)
      每次 rpc 请求的样本个数，默认等于 iter_num 表示根据实际需求来采样，不做 buffer；如果小于实际需求，按照实际需求采样；超过实际需求，剩下的做 buffer 给下次采样使用。

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    `is_colossusdb_graph`: [bool] (可选) 是否是统一存储，默认 false

    调用示例
    ------
    ``` python
    .iter_nodes(
      save_result_to='uId_neg',
      iter_num=5,
      kess_service="xxxxx",
      relation_name='U2I',
      shard_num=1,
      fetch_batch_size=1024,
      timeout_ms=100)
    ```
    """

    self._add_processor(KgnnIterEnricher(kwargs))
    return self

  def dummy_wait(self, **kwargs):
    """
    KgnnDummyObserver
    ------
    因为目前 kgnn 的同步 op 比较少，异步 op 不容易 debug，所以加一个同步的 dummy op，用于等待其它异步 op 执行完成

    参数配置
    ------
    `input_common_attr`: [string] 从指定 common_attr 获取属性 。

    `input_item_attr`: [string] 从指定 item_attr 获取属性。

    调用示例
    ------
    ``` python
    .dummy_wait(
      input_common_attr="uId")
    ```
    """

    self._add_processor(KgnnDummyObserver(kwargs))
    return self

  def get_side_info(self, **kwargs):
    """
    KgnnSideInfoEnricher
    ------
    从 kgnn 的某个 relation/table 获取一批节点的 side info 信息.
    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.

    参数配置
    ------
    `id_from_common_attr`: [string] 从指定 common_attr 获取 ids 列表长度，要求属性为 int 或者 intlist, 可不提供。

    `id_from_item_attr`: [string] 从指定的 item_attr 字段获取 ids 列表长度, 要求属性为 int 或者 intlist, 可不提供。

    `attr_schema`: [NodeAttrSchema]
      配置的属性描述符, 指定一组从 side info 里获取的属性.
      注意, 目前 kgnn 的实现要求这个 schema 需要把一个节点的所有属性按顺序依次声明出来, 而不能只声明其中的一部分. 因为底层实现
      是一批次 side info 全部拿出来在客户端解析的，如果不按照顺序声明，按照 offset 读的时候就会拿到错误的数据。
      结果按照指定的属性名依次写入，如果配置了 id_from_common_attr，结果写入 common_attr;
      如果配置了 id_from_item_attr，结果写入 item_attr;

      如果 id 本身是一个 intlist, 那么每个 attr 写入的 list 长度为单个 int 值长度 * list 长度, flatten 排列.
      比如 id 长度为 3, 配置读取一个 x 单值 int64 属性和一个 y 单值 int64 属性, 最后的写入 x 和 y 均为长度为 3 的 list.
      为了统一处理, 所有的输出统一写为 intlist 和 floatlist 类型.

      除了指定的自定义 side info 外, 还有几个特殊的固有字段:

        * degree: 节点的 degree 字段信息(不等于节点的边表长度, 被淘汰过的节点在 degree 内的计数不清除, 可以理解为节点的历史热度)
          输出为 intlist 类型.

        * edge_num: 节点的边表长度, 输出为 intlist 类型

    `kess_service`: [string] kgnn 请求服务的 kess 服务名 (v1 使用)

    `model_name`: [string][动态参数] colossusdb kgnn 请求服务对应的 kaiserving 上的模型名称 (v2 使用)

    `relation_name`: [string] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int] kgnn 存储的shard num (relation_name 设置时使用)

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    调用示例
    ------
    ``` python
    # 获取一批 pid 的 side info, 存了 aid 和 degree
    .get_side_info(
      id_from_item_attr="pId",
      attr_schema=NodeAttrSchema(1,0).add_int64_attr('aId').set_degree("degree").set_edge_num("edge_num"),
      kess_service="xxxxx",
      relation_name='I2U',
      shard_num=16,
      timeout_ms=100)
    ```
    """
    self._add_processor(KgnnSideInfoEnricher(kwargs))
    return self

  def update_si2dc(self, **kwargs):
    """
    KgnnUpdateSI2DCObserver
    ------
    (src item to dest common) 本接口处理 src node 为 item_attr, dst node 为 common_attr 时的各种情况。

    向 kgnn 的某个 relation/table 更新图存储的边信息，由于 update_graph 的通用接口情况特别多，
    每种情况下的参数处理和执行都不太一样，拆分为特定情况的接口。

    本接口不处理只更新 side info 的情况，但可以在更新边的时候带上 side info 一起更新.

    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.

    参数配置
    ------
    `src_attr`: [string] 从指定 item_attr 获取 ids 列表，并用其中的 id 作为边的 src 端, 要求属性为 int 或者 intlist。
      如果某个 item 的对应属性不存在, 则跳过这个 item 的写入。

    `dst_attr`: [string] 从指定 common_attr 获取 ids 列表，并用其中的 id 作为边的 dest 端, 要求属性为 int 或者 intlist。
      如果配置的是 intlist 属性, 则 src_attr 内的每个 id 对这组 ids 做笛卡尔积写入，也就是 M * N 的两两为边。

    `src_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 src 端的 side info 配置。
      其对应行为是每个 item 节点从自己的 item_attr 内抽出对应描述字段作为本节点的 side info。
      如果每个 item 内的 id attr 为 intlist, 则这些 id 共享这个 item 的 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `dst_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 dst 端的 side info 配置。
      其对应行为是从 common_attr 内抽取 side info, 一组 dst ids 公用这组 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `dst_node_item_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 dst 端的 side info 配置。
      与 dst_node_attr 类似，不过是从 item_attr 内抽取 side info。可不设置，默认空字符串。不能与 dst_node_attr 同时设置。
      e.g.: 往 I2U 的图插入，但边额外属性是 item 的属性，比如 user 观看 item 的播放时长。

    `src_attr_filter`: [string] (可选) 部分场景下, 可能一组 item 内不是全部想写边, 而是部分想写边(比如一组 item list, 只想写其中正样本的部分).
      用这个字段指定 label, 从每个 item_attr 内读取该字段，要求属性为 int 类型。当该类型值 > 0 时，认为需要写入。
      如果执行过程属性不存在或者类型不匹配, 则不写入该条 item 数据。

    `kess_service`: [string][动态参数] kgnn 请求服务的 kess 服务名, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      v2 (colossusdb kgnn) 不支持 rpc 写入，可以不配置此项。

    `btq_prefix`: [string][动态参数] kgnn 接受更新数据的 btq name 前缀, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      目前更新方式只能是 kess 和 btq 两种方式二选一，使用 btq 前请确认 kgnn storage 配置了 btq stream loader。
      配置参考 kgnn/examples/storages/common_cluster_config/cluster_config.py
      并且确认 btq 已经创建好了。 一个 kgnn shard 对应一个 btq. 如果 btq_prefix = "btq_kgnn_test-U2I", shard_num = 4
      那么需要 4 个 queue 分别是：
        btq_kgnn_test-U2I-0
        btq_kgnn_test-U2I-1
        btq_kgnn_test-U2I-2
        btq_kgnn_test-U2I-3
      其中 '-' 是自动添加的连接符。
      对于 v2 (colossusdb kgnn) 服务，必须配置 btq_prefix， v2 不会自动添加的连接符，例如 queue 如果是 btq_kgnn_test-U2I-0，
      btq_prefix 需要设置为 "btq_kgnn_test-U2I-"

    `relation_name`: [string][动态参数] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string][动态参数] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int][动态参数] kgnn 存储的shard num (relation_name 设置时使用)

    `btq_shard_num`: [int][动态参数] btq 前缀对应的 shard num (table_name 设置时使用)

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    `timestamp_attr`: [string] (可选) 从指定的字段获得插入边的时间戳信息，要求属性为 int 或 intlist.
      该接口写入时, 如果判断 ts 是毫秒等单位的, 会自动把 timestamp 转为秒单位, 不需要额外处理.
      读取顺序，首先从 item 侧读取时间戳，代表单个 item 的时间戳。如果 item 侧是一个 intlist, 且
      该 ts 属性为 intlist, 则要求长度对齐，否则这组数据不写入, 如果 ts 为 int, 则该组 id 共享这一个 ts.
      如果读不到某个 item 的时间戳, 会从 user 侧读同名时间戳字段（int 类型）, 代表这个 item 用 user 侧时间戳后备。
      如果依然读不到, 认为这个数据没有时间戳, 这个边的实际写入的 ts 设置为 0.

    `dst_weight_attr`: [string] (可选) 指定边的权重属性 attr name

    `dst_w_is_common_attr`: [bool] (可选) 如果 dst_weight_attr 配置了, 则该字段必须配置.
      定义读取 weight 的字段是否来自 common_attr，在 si2dc 的写入方式下，对应以下几种 case：
      1. 如果 weight 来自 item (也就是 src 侧, 字段为 false), 且 src_attr 为 int, 则要求 weight 字段为 float.
      2. 如果 weight 来自 item, 且 src_attr 为 intlist, 则 weight 为 float 或者 floatlist, 如果为
      floatlist, 要求 floatlist 的长度和 intlist 一致以做到一一对应。如果为 float 则该 weight broadcast
      给这一组 src_attr 的 ids.
      3. 如果 weight 来自 common(也就是 dst 侧, 字段为 true), 且 dst_attr 为 int, 则要求 weight 字段为 float.
      4. 如果 weight 来自 common, 且 dst_attr 为 intlist, 则 weight 为 float 或者 floatlist, 如果为
      floatlist, 要求 floatlist 的长度和 intlist 一致以做到一一对应。如果为 float 则该 weight broadcast
      给这一组 dst_attr 的 ids.

      以上 case 如果有一次不匹配的情况(属性不存在或者长度 / 类型不匹配), 则这一批的写入不执行。

    `batching_num`: [int][动态参数] (可选) batch 化提交参数, 默认为 1.
      部分场景下, 单次 pipeline 的 request 可能数据量很小, 导致会产生大量写图的小请求，拖慢性能。
      可以配置该参数，累积 batch 次 update 的数据一次写入 graph 内。
      不配置则默认为 1，等于单次即可触发写入。

    `insert_edge_weight_act`: [int] (可选) Cpt边权重更新方式，默认方式是ADD
      1 对应ADD，边的权重叠加该次插入的权重
      2 对应REPLACE，该次插入的权重覆盖已经有的权重

    `expire_ts_update_flag_attr`: [string] (可选) 从给定的 item attr 中获取值，指定该次插入是否更新节点的淘汰时间，
      默认为 true 进行更新，要求 attr 的类型是 int

    `use_item_ts_for_expire_attr`: [string] (可选) 从给定的 item attr 中获取值，是否尝试使用边的时间戳作为节点的最后修改时间。
      默认为 false 使用物理时间，要求 attr 的类型是 int

    `is_delete`: [bool] (可选) 是否删除操作，删除支持删除边，或者删除以某个点为起点的所有边。
      对于后者，将 dst_attr 配置为空字符串 ""。目前仅 kgnn v2 支持删除操作。

      节点的最后修改时间被用于判断过期，例如，过期时间为24小时，则连续24小时无更新的节点会过期。
      但是有的时候会追老数据，并且希望这些老数据就像是刚产生的时候就进行了插入，此时便需要用数据内的时间戳作为"最后修改时间"。
      此更新不会使过期时间变小。例如，对某暂未过期的节点插入一条N年前的边，此节点的最后修改时间不会被改为N年前。

    调用示例
    ------
    ``` python
    from dragonfly.ext.kgnn.node_attr_schema import NodeAttrSchema
    .update_si2dc(
      src_attr="uId",
      dst_attr='pId',
      src_node_attr=NodeAttrSchema(3, 1).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      kess_service="grpc_kgnn_test-U2I-I2U",
      relation_name='U2I',
      shard_num=16,
      timeout_ms=200,
      timestamp_attr='ts',
      dst_weight_attr='weight',
      dst_w_is_common_attr=True)

    .update_si2dc(
      src_attr="uId",
      dst_attr='pId',
      src_node_attr=NodeAttrSchema(3, 1).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      btq_prefix="btq_kgnn_test-U2I-I2U",
      relation_name='U2I',
      shard_num=16,
      timeout_ms=200,
      timestamp_attr='ts',
      dst_weight_attr='weight',
      dst_w_is_common_attr=True)
    ```
    """
    self._add_processor(KgnnUpdateSI2DCObserver(kwargs))
    return self

  def update_sc2di(self, **kwargs):
    """
    KgnnUpdateSC2DIObserver
    ------
    (src common to dest item) 本接口处理 src node 为 common_attr, dst node 为 item_attr 时的各种情况。

    向 kgnn 的某个 relation/table 更新图存储的边信息，由于 update_graph 的通用接口情况特别多，
    每种情况下的参数处理和执行都不太一样，拆分为特定情况的接口。

    本接口不处理只更新 side info 的情况，但可以在更新边的时候带上 side info 一起更新.

    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.

    参数配置
    ------
    `src_attr`: [string] 从指定 common_attr 获取 ids 列表，并用其中的 id 作为边的 src 端, 要求属性为 int 或者 intlist。
      如果配置的是 intlist 属性, 则每个 id 对所有 dst ids 做笛卡尔积写入，也就是 M * N 的两两为边。

    `dst_attr`: [string] 从指定 item_attr 获取 ids 列表，并用其中的 id 作为边的 dest 端, 要求属性为 int 或者 intlist。
      如果某个 item 的对应属性不存在, 则跳过这个 item 的写入。

    `src_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 src 端的 side info 配置。
      其对应行为是从 common_attr 内抽取 side info, 一组 src ids 公用这组 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `dst_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 dst 端的 side info 配置。
      其对应行为是每个 item 节点从自己的 item_attr 内抽出对应描述字段作为本条边的 side info。
      如果每个 item 内的 id attr 为 intlist, 则这些 id 共享这个 item 的 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `dst_attr_filter`: [string] (可选) 部分场景下, 可能一组 item 内不是全部想写边, 而是部分想写边(比如一组 item list, 只想写其中正样本的部分).
      用这个字段指定 label, 从每个 item_attr 内读取该字段, 要求属性为 int 类型。当该类型值 > 0 时，认为需要写入。
      如果执行过程属性不存在或者类型不匹配, 则不写入该条 item 数据。

    `kess_service`: [string][动态参数] kgnn 请求服务的 kess 服务名, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      v2 (colossusdb kgnn) 不支持 rpc 写入，可以不配置此项。

    `btq_prefix`: [string][动态参数] kgnn 接受更新数据的 btq name 前缀, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      目前更新方式只能是 kess 和 btq 两种方式二选一，使用 btq 前请确认 kgnn storage 配置了 btq stream loader。
      配置参考 kgnn/examples/storages/common_cluster_config/cluster_config.py
      并且确认 btq 已经创建好了。 一个 kgnn shard 对应一个 btq. 如果 btq_prefix = "btq_kgnn_test-U2I", shard_num = 4
      那么需要 4 个 queue 分别是：
        btq_kgnn_test-U2I-0
        btq_kgnn_test-U2I-1
        btq_kgnn_test-U2I-2
        btq_kgnn_test-U2I-3
      其中 '-' 是自动添加的连接符。
      对于 v2 (colossusdb kgnn) 服务，必须配置 btq_prefix， v2 不会自动添加的连接符，例如 queue 如果是 btq_kgnn_test-U2I-0，
      btq_prefix 需要设置为 "btq_kgnn_test-U2I-"

    `relation_name`: [string][动态参数] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string][动态参数] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int][动态参数] kgnn 存储的shard num (relation_name 设置时使用)

    `btq_shard_num`: [int][动态参数] btq 前缀对应的 shard num (table_name 设置时使用)

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    `timestamp_attr`: [string] (可选) 从指定的字段获得插入边的时间戳信息，要求属性为 int 或 intlist.
      该接口写入时, 如果判断 ts 是毫秒等单位的, 会自动把 timestamp 转为秒单位, 不需要额外处理.
      读取顺序，首先从 item 侧读取时间戳，代表单个 item 的时间戳。如果 item 侧是一个 intlist, 且
      该 ts 属性为 intlist, 则要求长度对齐，否则这组数据不写入, 如果 ts 为 int, 则该组 id 共享这一个 ts.
      如果读不到某个 item 的时间戳, 会从 user 侧读同名时间戳字段（int 类型）, 代表这个 item 用 user 侧时间戳后备。
      如果依然读不到, 认为这个数据没有时间戳, 这个边的实际写入的 ts 设置为 0.

    `dst_weight_attr`: [string] (可选) 指定边的权重属性 attr name

    `dst_w_is_common_attr`: [bool] (可选) 如果 dst_weight_attr 配置了, 则该字段必须配置.
      定义读取 weight 的字段是否来自 common_attr，在 sc2di 的写入方式下，对应以下几种 case：
      1. 如果 weight 来自 item (也就是 dst 侧, 字段为 false), 且 dst_attr 为 int, 则要求 weight 字段为 float.
      2. 如果 weight 来自 item, 且 dst_attr 为 intlist, 则 weight 为 float 或者 floatlist, 如果为
      floatlist, 要求 floatlist 的长度和 intlist 一致以做到一一对应。如果为 float 则该 weight broadcast
      给这一组 dst_attr 的 ids.
      3. 如果 weight 来自 common(也就是 src 侧, 字段为 true), 且 src_attr 为 int, 则要求 weight 字段为 float.
      4. 如果 weight 来自 common, 且 src_attr 为 intlist, 则 weight 为 float 或者 floatlist, 如果为
      floatlist, 要求 floatlist 的长度和 intlist 一致以做到一一对应。如果为 float 则该 weight broadcast
      给这一组 src_attr 的 ids.

      以上 case 如果有一次不匹配的情况(属性不存在或者长度 / 类型不匹配), 则这一批的写入不执行。

    `batching_num`: [int][动态参数] (可选) batch 化提交参数, 默认为 1.
      部分场景下, 单次 pipeline 的 request 可能数据量很小, 导致会产生大量写图的小请求，拖慢性能。
      可以配置该参数，累积 batch 次 update 的数据一次写入 graph 内。
      不配置则默认为 1，等于单次即可触发写入。

    `insert_edge_weight_act`: [int] (可选) Cpt边权重更新方式，默认方式是ADD
      1 对应ADD，边的权重叠加该次插入的权重
      2 对应REPLACE，该次插入的权重覆盖已经有的权重

    `expire_ts_update_flag_attr`: [string] (可选) 从给定的 item attr 中获取值，指定该次插入是否更新节点的淘汰时间，
      默认为 true 进行更新，要求 attr 的类型是 int

    `use_item_ts_for_expire_attr`: [string] (可选) 从给定的 item attr 中获取值，是否尝试使用边的时间戳作为节点的最后修改时间。
      默认为 false 使用物理时间，要求 attr 的类型是 int

    `is_delete`: [bool] (可选) 是否删除操作，删除支持删除边，或者删除以某个点为起点的所有边。
      对于后者，将 dst_attr 配置为空字符串 ""。目前仅 kgnn v2 支持删除操作。

      节点的最后修改时间被用于判断过期，例如，过期时间为24小时，则连续24小时无更新的节点会过期。
      但是有的时候会追老数据，并且希望这些老数据就像是刚产生的时候就进行了插入，此时便需要用数据内的时间戳作为"最后修改时间"。
      此更新不会使过期时间变小。例如，对某暂未过期的节点插入一条N年前的边，此节点的最后修改时间不会被改为N年前。

    调用示例
    ------
    ``` python
    from dragonfly.ext.kgnn.node_attr_schema import NodeAttrSchema
    .update_sc2di(
      src_attr="pId",
      dst_attr='uId',
      src_node_attr=NodeAttrSchema(3, 2).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      kess_service="grpc_kgnn_test-U2I",
      relation_name='I2U',
      shard_num=16,
      timeout_ms=200,
      timestamp_attr='ts',
      dst_weight_attr='weight',
      dst_w_is_common_attr=True)

    .update_sc2di(
      src_attr="pId",
      dst_attr='uId',
      src_node_attr=NodeAttrSchema(3, 2).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      btq_prefix="btq_kgnn_test-U2I",
      relation_name='I2U',
      shard_num=16,
      timeout_ms=200,
      timestamp_attr='ts',
      dst_weight_attr='weight',
      dst_w_is_common_attr=True)
    ```
    """
    self._add_processor(KgnnUpdateSC2DIObserver(kwargs))
    return self

  def update_inner_item(self, **kwargs):
    """
    KgnnUpdateInnerItemObserver
    ------
    本接口处理 src 和 dst node 均为 item_attr, 且不同 item 之间不进行组合的处理。即，任何关系都来自于某一个 item 内部的某两个属性。

    向 kgnn 的某个 relation/table 更新图存储的边信息，由于 update_graph 的通用接口情况特别多，
    每种情况下的参数处理和执行都不太一样，拆分为特定情况的接口。

    本接口不处理只更新 side info 的情况，但可以在更新边的时候带上 side info 一起更新.

    为方便下面的文档进行解释，约定：
    M 代表某个 item 内 src_attr 的长度,
    N 代表某个 item 内 dst_attr 的长度.

    table_name 和 relation_name 只能配置一个，配置了 table_name 代表使用 colossusdb kgnn 服务.

    参数配置
    ------
    `src_attr`: [string] 从指定 item_attr 获取 ids 列表，并用其中的 id 作为边的 src 端, 要求属性为 int 或者 intlist。
      如果配置的是 intlist 属性, 则每个 id 对所有 dst ids 做笛卡尔积写入，也就是 M * N 的两两为边。

    `dst_attr`: [string] 从指定 item_attr 获取 ids 列表，并用其中的 id 作为边的 dest 端, 要求属性为 int 或者 intlist。
      如果配置的是 intlist 属性, 则每个 id 对所有 src ids 做笛卡尔积写入，也就是 M * N 的两两为边。

    `src_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 src 端的 side info 配置。
      其对应行为是每个 item 节点从自己的 item_attr 内抽出对应描述字段作为本节点的 side info。
      如果 M > 1, 即每个 item 内的 src_attr 为 intlist, 则这些 id 共享这个 item 的 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `dst_node_attr`: [NodeAttrSchema] (可选) 更新图存储时，边的 dst 端的 side info 配置。
      其对应行为是每个 item 节点从自己的 item_attr 内抽出对应描述字段作为本 item 笛卡尔积生成出来的所有边的 side info。
      如果 M * N > 1, 则这些边共享这个 item 的 side info。可不设置，默认空字符串。
      在配置该字段的时候，如果某个属性的值没有正确被解析，会使用奇异默认值填充, 参考 learning/gnn/base/block_storage_api.cc

    `kess_service`: [string][动态参数] kgnn 请求服务的 kess 服务名, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      v2 (colossusdb kgnn) 不支持 rpc 写入，可以不配置此项。

    `btq_prefix`: [string][动态参数] kgnn 接受更新数据的 btq name 前缀, v1 模式下 kess_service 和 btq_prefix 只能二选一。
      目前更新方式只能是 kess 和 btq 两种方式二选一，使用 btq 前请确认 kgnn storage 配置了 btq stream loader。
      配置参考 kgnn/examples/storages/common_cluster_config/cluster_config.py
      并且确认 btq 已经创建好了。 一个 kgnn shard 对应一个 btq. 如果 btq_prefix = "btq_kgnn_test-U2I", shard_num = 4
      那么需要 4 个 queue 分别是：
        btq_kgnn_test-U2I-0
        btq_kgnn_test-U2I-1
        btq_kgnn_test-U2I-2
        btq_kgnn_test-U2I-3
      其中 '-' 是自动添加的连接符。
      对于 v2 (colossusdb kgnn) 服务，必须配置 btq_prefix， v2 不会自动添加的连接符，例如 queue 如果是 btq_kgnn_test-U2I-0，
      btq_prefix 需要设置为 "btq_kgnn_test-U2I-"

    `relation_name`: [string][动态参数] 采样从哪个 relation 进行 (v1 使用)

    `table_name`: [string][动态参数] 采样从哪个 table 进行 (v2 使用)

    `shard_num`: [int][动态参数] kgnn 存储的shard num (relation_name 设置时使用)

    `btq_shard_num`: [int][动态参数] btq 前缀对应的 shard num (table_name 设置时使用)

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    `timestamp_attr`: [string] (可选) 指定边的时间戳属性 attr name
      优先从 item 侧获取值，如果没有则尝试查 user 侧 attr。如果是 item 侧，必须是 int 或 int list 类型；如果是 user 侧，必须是 int。
      根据 attr 值的长度不同，有以下几种 case：
      1. 属性类型是 int 或类型是 int list 且长度为1, 则该 item 产生的 M*N 条边的时间戳都使用该值。
      2. 属性类型是 int list 且长度大于1, 则长度必须是 M*N，并将这些时间戳分配到每条边上。
      如果未配置、读不到值或者长度没对齐, 认为这个数据没有时间戳, 所有边实际写入的 ts 设置为 0.
      attr 值的单位可以是秒、毫秒或微秒，会自动转为以秒为单位来适配存储端。

    `dst_weight_attr`: [string] (可选) 指定边的权重属性 attr name
      必须是 item 侧的 attr，必须是 float 或 float list 类型。有以下几种 case：
      1. 属性类型是 float 或类型是 float list 且长度为1, 则该 item 产生的 M*N 条边的权重都使用该值。
      2. 属性类型是 float list 且长度大于1, 则长度可以是 M、N 或 M*N，并将这些权重分配到每条边上。

    `weight_on_src`: [boolean] 仅用于上述 M = N = X > 1 的情况，X 是 weight_attr 的长度。
      用于指导权重按 src 还是 dst 展开。默认值为 true，此时所有 src_id 相同的边拥有相同的 weight。
      若为 false，则所有 dst_id 相同的边拥有相同的 weight。

    `batching_num`: [int][动态参数] (可选) batch 化提交参数, 默认为 1.
      部分场景下, 单次 pipeline 的 request 可能数据量很小, 导致会产生大量写图的小请求，拖慢性能。
      可以配置该参数，累积 batch 次 update 的数据一次写入 graph 内。
      不配置则默认为 1，等于单次即可触发写入。

    `insert_edge_weight_act`: [int] (可选) Cpt边权重更新方式，默认方式是ADD
      1 对应ADD，边的权重叠加该次插入的权重
      2 对应REPLACE，该次插入的权重覆盖已经有的权重

    `expire_ts_update_flag_attr`: [string] (可选) 从给定的 item attr 中获取值，指定该次插入是否更新节点的淘汰时间，
      默认为 true 进行更新，要求 attr 的类型是 int

    `use_item_ts_for_expire_attr`: [string] (可选) 从给定的 item attr 中获取值，是否尝试使用边的时间戳作为节点的最后修改时间。
      默认为 false 使用物理时间，要求 attr 的类型是 int

    `is_delete`: [bool] (可选) 是否删除操作，删除支持删除边，或者删除以某个点为起点的所有边。
      对于后者，将 dst_attr 配置为空字符串 ""。目前仅 kgnn v2 支持删除操作。

    调用示例
    ------
    ``` python
    from dragonfly.ext.kgnn.node_attr_schema import NodeAttrSchema
    .update_inner_item(
      src_attr="uId",
      dst_attr='pId',
      src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      kess_service="grpc_kgnn_test-U2I",
      relation_name='U2I',
      shard_num=16,
      timeout_ms=200,
      dst_weight_attr='weight')

    .update_inner_item(
      src_attr="uId",
      dst_attr='pId',
      src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('photo_tags', 3).add_float_attr("some_score"),
      btq_prefix="btq_kgnn_test-U2I",
      relation_name='U2I',
      shard_num=16,
      timeout_ms=200,
      dst_weight_attr='weight')
    ```
    """
    self._add_processor(KgnnUpdateInnerItemObserver(kwargs))
    return self

  def batch_2hop_sample(self, **kwargs):
    """
    KgnnBatchSampleEnricherV1
    ------
    对二跳采样做封装，会对请求的 id 做 batching 缓存和执行一次请求，提升相关 pipeline 位于 kgnn 的瓶颈性能。
    如果你需要进行二跳采样，建议先使用两次 fetch_kgnn_neighbors 来实现。如果发现性能过差，再考虑使用该 processor 和 batch_dispatch。

    当前接口的如下限制:
    1. 同时请求 user 侧和 item 侧的二跳行为数据, 如果某次 pipe 没有 item, 则直接跳过本次 batching 累计.
    2. 定义的 user 和 item 字段仅允许为 int64 字段.
    3. timestamp 统一用 batching 的最后一次样本的时间.
    4. 请求的 relation 所在的服务，不存储其他 relation（即，不存在多 relation 存储在同一台机器、共享同一个 kess_name 的情况）
    5. 写入的 attr 名是固定的，大概像这样：user_one_hop_id_u2i_click
    6. 两跳采样的参数是共用的，它们的采样方式、padding 策略、是否放回等参数总是完全相同的

    colossusdb kgnn (v2) 暂未支持，如有需要请联系 kgnn 负责人.

    参数配置
    ------
    `id_from_common_attr`: [string] 从指定 common_attr 获取 id，属性为 int，为空则直接跳过本轮。

    `id_from_item_attr`: [string] 从指定的 item_attr 字段获取 id, 要求属性为 int，为空则直接跳过该 item。

    `cluster_prefix`: [list of string] 所有 relation 的服务集群前缀, 依次请求其中的所有服务。

    `export_attr_prefix`: [list of string] 必填，长度与 cluster_prefix 相同，用于指定每个服务的返回结果存储的 attr 的前缀。
      e.g.: 若 cluster_prefix = ["service_a", "service_b"], export_attr_prefix = ["prefix1", "prefix2_"]，则：
      请求 service_a 的结果放在 prefix1user_one_hop_id_u2i_click 中，
      请求 service_b 的结果放在 prefix2_user_one_hop_id_u2i_click 中。

    `user_2hop_relations`: [list] relation_names for user multi 2hop，与 cluster_prefix 一一对应.
      e.g.:[["U2I_CLICK", "I2U_CLICK"], ["U2I_LONGVIEW", "I2U_LONGVIEW"]]
      每个 item 一定有两个元素表示二跳, item 个数与 cluster_prefix 长度相同.
      比如上面例子对应 cluster_prefix = ["service_a", "service_a"]

    `item_2hop_relations`: [string] relation_names for item multi 2hop. 格式同上.

    `shard_nums_1hop`: [list of int] user_2hop_relations 中一跳 relation 的服务集群shard_num。
      e.g.: user_2hop_relations: [["U2I_CLICK", "I2U_CLICK"], ["U2I_LONGVIEW", "I2U_LONGVIEW"]]
      中的U2I_CLICK，U2I_LONGVIEW

    `shard_nums_2hop`: [list of int] user_2hop_relations 中二跳 relation 的服务集群shard_num。
      e.g.: user_2hop_relations: [["U2I_CLICK", "I2U_CLICK"], ["U2I_LONGVIEW", "I2U_LONGVIEW"]]
      中的I2U_CLICK，I2U_LONGVIEW

    `user_labels`: [list of string] (可选) 一组 user int attr 的名称，将每个 user 取得的结果都保存下来形成一个 int list，默认值为 0.

    `item_labels`: [list of string] (可选) 一组 item int attr 的名称，将每个 item 取得的结果都保存下来形成一个 int list，默认值为 0.

    `user_strings`: [list of string] (可选) 一组 user string attr 的名称，将每个 user 取得的结果都保存下来形成一个 string list，默认值为空 string.

    `item_strings`: [list of string] (可选) 一组 item string attr 的名称，将每个 item 取得的结果都保存下来形成一个 string list，默认值为空 string.

    `user_doubles`: [list of string] (可选) 一组 user double attr 的名称，将每个 user 取得的结果都保存下来形成一个 double list，默认值为空 0.

    `item_doubles`: [list of string] (可选) 一组 item double attr 的名称，将每个 item 取得的结果都保存下来形成一个 double list，默认值为空 0.

    `user_int_lists`: [list of string] (可选) 一组 user int list attr 的名称，将每个 user 取得的结果都保存下来形成一个 int list，默认值为空 list.

    `item_int_lists`: [list of string] (可选) 一组 item int list attr 的名称，将每个 item 取得的结果都保存下来形成一个 int list，默认值为空 list.

    `user_double_lists`: [list of string] (可选) 一组 user double list attr 的名称，将每个 user 取得的结果都保存下来形成一个 double list，默认值为空 list.

    `item_double_lists`: [list of string] (可选) 一组 item double list attr 的名称，将每个 item 取得的结果都保存下来形成一个 double list，默认值为空 list.

    `sample_num`: [int] 每个 src_id 采样几个邻居, 四组服务共用。

    `sample_2hop_num`: [int] (可选) 该参数使用场景为一跳和二跳采样的数量不相同时使用，用该参数指定第二跳的邻居量。
      当不配置时，默认使用 sample_num 值同时作为第一跳和第二跳邻居个数。如果配置该值，则 sample_num 只用于指定第一跳邻居个数。

    `batching_num`: [int] 累计多少个 user 的请求执行一次.

    `need_timestamp`: [boolean] (可选) 是否在返回结果中添加采样节点对应的时间戳属性（秒），默认为 false
      时间戳的属性名大概像这样：user_one_hop_id_u2i_click_timestamp_s

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间，默认值为 200

    `timestamp_from_attr`: [string] (可选) 从指定的 attr 字段采样结果允许的最大时间戳（秒），要求属性为 int 或者 float，在 context 内，读不到则不受时间戳相关的影响

    `sample_type`: [string] (可选) 采样方式, 默认为随机采样. 合法类型：
    - random: 随机采样. <- 默认值
    - weight: 带权采样.
    - most_recent:  按照时间拍序, 召回最近的边.
    - least_recent: 按照时间拍序, 召回最远的边.
    - topn: 按照权重返回最大的 topN 个数据.

    `padding_type`: [string] (可选) 采样选项，当邻居个数不足 sample_num / 节点不存在的时候的默认行为. 合法类型：
    - zero: 节点用 0 填充.
    - self: 填节点 id 本身. <- 默认值.
    - neigh_loop: 使用部分邻居做循环填充, 如果一个邻居也没有, 则会走 self 逻辑.

    `sample_without_replacement`: [bool] (可选) 是否做无放回采样，默认值为 false
      这意味着默认情况下，从长度为1的边表做随机或带权采样会得到 n 个一样的结果，不触发 padding。
      当 sample_type 是 topn 或 recent 类时，此参数无意义。

    `min_weight`: [float] (可选) 第一跳的采样权重阈值，权重小于它的节点不会进入采样池，默认值为 0

    `min_weight_2hop`: [float] (可选) 第二跳的采样权重阈值，权重小于它的节点不会进入采样池，默认值为 `min_weight`

    `is_colossusdb_graph`: [bool] (可选) 是否是统一存储，默认 false
    """

    self._add_processor(KgnnBatchSampleEnricherV1(kwargs))
    return self

  def batch_dispatch(self, **kwargs):
    """
    KgnnBatchDispathEnricher

    ------
    配合 batch sample 接口使用, 通过特定字段判断本次是否触发分发数据。
    如果触发，将 user 侧节点和 item 侧节点全都展开到 item 侧输出。
    要求请求到的数据已经按照 item_id 的维度在 item 上正确展开。(需要辅以部分 common op 实现)
    参数一定要和 batch sample 严格对齐否则会丢数据。

    分发成功会写入一个 kgnn_batch_dispatch_done 的 common int 属性, 值为 1, 可用于下游判断是否数据可用。

    需要注意 user_label 和 item_label 的名字不要重复, 否则展开时会冲突, 只保留下 item 的了.

    会自动检测 batch sample 是否返回了时间戳属性，并在属性存在时进行对应的展开。

    参数配置
    ------
    `user_2hop_relations`: [list] relation_names for user multi 2hop.
      eg: [["U2I_CLICK", "I2U_CLICK"], ["U2I_LONGVIEW", "I2U_LONGVIEW"]]
      每个 item 一定有两个元素表示二跳, 可以有多个 item 表示同时从多种 relation list 下采样.

    `item_2hop_relations`: [string] relation_names for user multi 2hop. 格式同上.

    `user_labels`: [list of string] (可选) 一组 user int attr 的名称，将每个 user 取得的结果都保存下来形成一个 int list，默认值为 0.

    `item_labels`: [list of string] (可选) 一组 item int attr 的名称，将每个 item 取得的结果都保存下来形成一个 int list，默认值为 0.

    `user_strings`: [list of string] (可选) 一组 user string attr 的名称，将每个 user 取得的结果都保存下来形成一个 string list，默认值为空 string.

    `item_strings`: [list of string] (可选) 一组 item string attr 的名称，将每个 item 取得的结果都保存下来形成一个 string list，默认值为空 string.

    `user_doubles`: [list of string] (可选) 一组 user double attr 的名称，将每个 user 取得的结果都保存下来形成一个 double list，默认值为空 0.

    `item_doubles`: [list of string] (可选) 一组 item double attr 的名称，将每个 item 取得的结果都保存下来形成一个 double list，默认值为空 0.

    `user_int_lists`: [list of string] (可选) 一组 user int list attr 的名称，将每个 user 取得的结果都保存下来形成一个 int list，默认值为空 list.

    `item_int_lists`: [list of string] (可选) 一组 item int list attr 的名称，将每个 item 取得的结果都保存下来形成一个 int list，默认值为空 list.

    `user_double_lists`: [list of string] (可选) 一组 user double list attr 的名称，将每个 user 取得的结果都保存下来形成一个 double list，默认值为空 list.

    `item_double_lists`: [list of string] (可选) 一组 item double list attr 的名称，将每个 item 取得的结果都保存下来形成一个 double list，默认值为空 list.

    `export_attr_prefix`: [list of string] (可选) 用于拼出要解析的属性名。
      一般应当与 batch sample 配置相同。

    `sample_num`: [int] 每个 src_id 采样几个邻居, 一般应当与 batch sample 配置相同。

    `sample_2hop_num`: [int] (可选) 每个一跳节点采样几个邻居, 一般应当与 batch sample 配置相同。
      此参数的含义、默认值的取值规则均与 batch sample 相同。
    """
    self._add_processor(KgnnBatchDispatchEnricher(kwargs))
    return self

  def kgnn_raw_key(self, **kwargs):
    """
    KgnnRawKeyEnricher
    ------
    因为目前 kgnn 使用 48 位 key，而 dragon 未引入 lua bit 库导致不支持 lua 脚本中进行位运算，因此统一将 key 格式转换放在这里

    参数配置
    ------
    `modify_common_attr`: [string] 修改指定 common_attr 的值为 int64 类型的 key，要求属性修改前是 int64 或 string。

    `modify_item_attr`: [string] 修改指定 item_attr 的值为 int64 类型的 key，要求属性修改前是 int64 或 string。

    调用示例
    ------
    ``` python
    .kgnn_raw_key(
      modify_common_attr="uId")
    ```
    """

    self._add_processor(KgnnRawKeyEnricher(kwargs))
    return self

  def get_edge_info(self, **kwargs):
    """
    KgnnEdgeInfoEnricher
    ------
    从 kgnn 的某个 relation 获取一批边的信息。每条边由 src_id + dst_id 唯一指定。
    如果 src_id 是 int 或 长度为1的 intlist, 则它与长度为 N 的 dst_id 形成 N 条边。
    如果 src_id 是 长度大于1的 intlist, 则它的长度必须与 dst_id 相等, 视为 N 条一一对应的边。
    返回结果总是与 dst_id 一一对应。如果 dst_id 是 common_attr, 则结果写入的属性也都是 common_attr。dst_id 是 item_attr 的情况同理。
    (不支持 src to dst 是 N 对 1 的查询, 不支持 item src & common dst 的组合)

    colossusdb kgnn (v2) 暂未支持，如有需要请联系 kgnn 负责人.

    参数配置
    ------
    `src_id_from_common_attr`: [string] (与下面二选一) 从指定 common_attr 获取 id 列表, 要求属性为 int 或 intlist.

    `src_id_from_item_attr`: [string] (与上面二选一) 从指定的 item_attr 获取 id 列表, 要求属性为 int 或 intlist.

    `dst_id_from_common_attr`: [string] (与下面二选一) 从指定 common_attr 获取 id 列表, 要求属性为 int 或 intlist.

    `dst_id_from_item_attr`: [string] (与上面二选一) 从指定的 item_attr 获取 id 列表, 要求属性为 int 或 intlist.

    `save_weight_to`: [string] (可选) weight 写入属性名, 最终长度与 dst_id 相同;
      如果配置了 dst_id_from_common_attr, 则结果写入 common_attr; 否则写入 item_attr 内

    `save_timestamp_to`: [string] (可选) timestamp 写入属性名, 最终长度与 dst_id 相同;
      如果配置了 dst_id_from_common_attr, 则结果写入 common_attr; 否则写入 item_attr 内

    `edge_attr_schema`: [NodeAttrSchema] (可选)
      边 side info 的配置, 每个 dest node 唯一地对应一条边, 也唯一地对应一组数据.
      要求这个 schema 把所有 side info 属性按顺序依次声明出来, 而不能只声明其中的一部分.
      如果配置了 dst_id_from_common_attr, 则结果写入 common_attr; 否则写入 item_attr 内.
      返回结果依次是 src1-dest1、src2-dest2、...、srcN-destN 的边属性 (如果 src_id 只有1个则 src1=src2=...=srcN).
      所有的输出统一写为 intlist 和 floatlist 类型（即使某个边属性是单个值）.

    `kess_service`: [string] kgnn 请求服务的 kess 服务名

    `relation_name`: [string] 采样从哪个 relation 进行

    `shard_num`: [int] kgnn 存储的shard num

    `timeout_ms`: [int] (可选) 请求远程服务的超时时间, 默认值为 200

    `is_colossusdb_graph`: [bool] (可选) 是否是统一存储，默认 false

    调用示例
    ------
    ``` python
    .get_edge_info(
      src_id_from_common_attr="uId",
      dst_id_from_item_attr="pId",
      save_weight_to="p_weight",
      save_timestamp_to="p_time_s",
      edge_attr_schema=NodeAttrSchema(1,0).add_int64_attr('aId'),
      kess_service="xxxxx",
      relation_name='I2U',
      shard_num=1)
    ```
    """
    self._add_processor(KgnnEdgeInfoEnricher(kwargs))
    return self
