#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, ArgumentError
from ...common_leaf_processor import LeafEnricher


class KgnnByRelationEnricher(LeafEnricher):
  """
  KgnnByRelationEnricher 提供简单的 get_by_id_in_relation 的功能
  向 kgnn 的某个 relation 下批量请求一次邻居采样操作, 并将结果填充到对应位置.
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fetch_kgnn_neighbors"

  @strict_types
  def is_async(self) -> bool:
    return "is_async" not in self._config or self._config["is_async"]

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "id_from_common_attr" in self._config:
      ret.add(self._config["id_from_common_attr"])
    if self._config.get("timstamp_attr_is_common", False):
      ret.add(self._config["timestamp_from_attr"])
      ret.add(self._config["min_timestamp_from_attr"])
    ret.update(self.extract_dynamic_params(self._config["sample_num"]))
    if "sample_type" in self._config:
      ret.update(self.extract_dynamic_params(self._config["sample_type"]))
    if "kess_service" in self._config:
      ret.update(self.extract_dynamic_params(self._config["kess_service"]))
    if "model_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["model_name"]))
    if "relation_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["relation_name"]))
    if "table_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["table_name"]))
    if "shard_num" in self._config:
      ret.update(self.extract_dynamic_params(self._config["shard_num"]))
    if "timeout_ms" in self._config:
      ret.update(self.extract_dynamic_params(self._config["timeout_ms"]))
    if "padding_type" in self._config:
      ret.update(self.extract_dynamic_params(self._config["padding_type"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "id_from_item_attr" in self._config:
      ret.add(self._config["id_from_item_attr"])
    if not self._config.get("timstamp_attr_is_common", True):
      ret.add(self._config["timestamp_from_attr"])
      ret.add(self._config["min_timestamp_from_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if "id_from_common_attr" in self._config:
      return self._output_attrs
    else:
      return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if "id_from_item_attr" in self._config:
      return self._output_attrs
    else:
      return set()

  @property
  @strict_types
  def _output_attrs(self) -> set:
    ret = set()
    ret.add(self._config["save_neighbors_to"])
    for key in ["save_weight_to", "save_timestamp_to"]:
      if key in self._config:
        ret.add(self._config[key])
    if "attr_schema" in self._config:
      ret = ret.union(self._config['attr_schema'].output_attr_names())
    if "edge_attr_schema" in self._config:
      ret = ret.union(self._config['edge_attr_schema'].output_attr_names())
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return 'id_from_item_attr' in self._config

  @strict_types
  def _check_config(self) -> None:
    if not (('id_from_common_attr' not in self._config) ^ ('id_from_item_attr' not in self._config)):
      raise ArgumentError(f"kgnn args id_from_common_attr and id_from_item_attr 只能定义一种: {self._config}")
    essential_args = ["sample_num"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")
    if 'save_neighbors_to' not in self._config:
      raise ArgumentError(f"save_neighbors_to not config: {self._config}")
    
    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
    
    if not (("kess_service" in self._config) ^ ("model_name" in self._config)):
      raise ArgumentError("only one of kess_service and model_name can be set")

class KgnnRandomEnricher(LeafEnricher):
  """
  KgnnRandomEnricher 提供全局随机采样功能
  向 kgnn 的某个 relation 下批量请求一次全局随机采样.
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "random_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "id_from_common_attr" in self._config:
      ret.add(self._config["id_from_common_attr"])
    ret.update(self.extract_dynamic_params(self._config["sample_num"]))
    if "timeout_ms" in self._config:
      ret.update(self.extract_dynamic_params(self._config["timeout_ms"]))
    if "sample_pool_name" in self._config:
      ret.update(self.extract_dynamic_params(self._config["sample_pool_name"]))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "id_from_item_attr" in self._config:
      ret.add(self._config["id_from_item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if ("id_from_common_attr" in self._config) or ("id_from_common_attr" not in self._config and
                                                   "id_from_item_attr" not in self._config):
      ret.add(self._config["save_neighbors_to"])
      if "attr_schema" in self._config:
        ret = ret.union(self._config['attr_schema'].output_attr_names())
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "id_from_item_attr" in self._config:
      ret.add(self._config["save_neighbors_to"])
      if "attr_schema" in self._config:
        ret = ret.union(self._config['attr_schema'].output_attr_names())
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return 'id_from_item_attr' in self._config

  @strict_types
  def _check_config(self) -> None:
    if ('id_from_common_attr' in self._config) and ('id_from_item_attr' in self._config):
      raise ArgumentError(f"kgnn args id_from_common_attr and id_from_item_attr 只能定义一种: {self._config}")
    essential_args = ["sample_num"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")
    if 'save_neighbors_to' not in self._config:
      raise ArgumentError(f"save_neighbors_to not config: {self._config}")
    
    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
    
    if not (("kess_service" in self._config) ^ ("model_name" in self._config)):
      raise ArgumentError("only one of kess_service and model_name can be set")

  @strict_types
  def is_async(self) -> bool:
    return True

class KgnnIterEnricher(LeafEnricher):
  """
  KgnnIterEnricher 提供遍历节点功能
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "iter_nodes"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if ("save_to_common_attr" not in self._config) or self._config["save_to_common_attr"]:
      ret.add(self._config["save_result_to"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if ("save_to_common_attr" in self._config) and (not self._config["save_to_common_attr"]):
      ret.add(self._config["save_result_to"])
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return False

  @strict_types
  def _check_config(self) -> None:
    essential_args = ["save_result_to", "iter_num", "kess_service", "relation_name", "shard_num"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")

  @strict_types
  def is_async(self) -> bool:
    return False

class KgnnSideInfoEnricher(LeafEnricher):
  """
  从 kgnn 的某个 relation 获取一批节点的 side info 信息
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_side_info"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "id_from_common_attr" in self._config:
      ret.add(self._config["id_from_common_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "id_from_item_attr" in self._config:
      ret.add(self._config["id_from_item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if 'id_from_common_attr' in self._config and "attr_schema" in self._config:
      ret = ret.union(self._config['attr_schema'].output_attr_names())
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if 'id_from_item_attr' in self._config and "attr_schema" in self._config:
      ret = ret.union(self._config['attr_schema'].output_attr_names())
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return 'id_from_item_attr' in self._config

  @strict_types
  def _check_config(self) -> None:
    if not (('id_from_common_attr' not in self._config) ^ ('id_from_item_attr' not in self._config)):
      raise ArgumentError(f"kgnn args id_from_common_attr and id_from_item_attr 只能定义一种: {self._config}")
    essential_args = ["attr_schema"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")
      
    if "relation_name" in self._config and "table_name" in self._config:
      raise ArgumentError("relation_name and table_name cannot be set both")

    if "relation_name" not in self._config and "table_name" not in self._config:
      raise ArgumentError("either relation_name or table_name must be set")

    if "relation_name" in self._config and "shard_num" not in self._config:
      raise ArgumentError("shard_num must be set when relation_name defined")
    
    if not (("kess_service" in self._config) ^ ("model_name" in self._config)):
      raise ArgumentError("only one of kess_service and model_name can be set")

  @strict_types
  def is_async(self) -> bool:
    return True

class KgnnBatchSampleEnricherV1(LeafEnricher):
  """
  KgnnBatchSampleEnricherV1 支持 batching sample 功能
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_2hop_sample"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # batch 的 op 不保证任何输出, 大部分情况下没有输出.
    return set()

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def _check_config(self) -> None:
    essential_args = [
        "id_from_common_attr",
        "id_from_item_attr",
        "cluster_prefix",
        "user_2hop_relations",
        "item_2hop_relations",
        "shard_nums_1hop",
        "shard_nums_2hop",
        "sample_num",
        "batching_num",
        "timeout_ms",
    ]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")
    if type(self._config["cluster_prefix"]) == type("str"):
      self._config["cluster_prefix"] = [self._config["cluster_prefix"]]

class KgnnBatchDispatchEnricher(LeafEnricher):
  """
  KgnnBatchDispathEnricher 支持 batching sample 之后的分发功能.
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "batch_dispatch"

  @strict_types
  def _check_config(self) -> None:
    essential_args = [
        "user_2hop_relations",
        "item_2hop_relations",
        "sample_num",
    ]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")

class KgnnRawKeyEnricher(LeafEnricher):
  """
  KgnnRawKeyEnricher 修改 int64 或 string key 为48位格式
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "kgnn_raw_key"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "modify_common_attr" in self._config:
      ret.add(self._config["modify_common_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "modify_item_attr" in self._config:
      ret.add(self._config["modify_item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "modify_common_attr" in self._config:
      ret.add(self._config["modify_common_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "modify_item_attr" in self._config:
      ret.add(self._config["modify_item_attr"])
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return 'modify_item_attr' in self._config

class KgnnEdgeInfoEnricher(LeafEnricher):
  """
  给定边的起点和终点ID, 返回边的属性
  """

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_edge_info"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    if "src_id_from_common_attr" in self._config:
      ret.add(self._config["src_id_from_common_attr"])
    if "dst_id_from_common_attr" in self._config:
      ret.add(self._config["dst_id_from_common_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "src_id_from_item_attr" in self._config:
      ret.add(self._config["src_id_from_item_attr"])
    if "dst_id_from_item_attr" in self._config:
      ret.add(self._config["dst_id_from_item_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if 'dst_id_from_common_attr' in self._config:
      if "save_weight_to" in self._config:
        ret.add(self._config["save_weight_to"])
      if "save_timestamp_to" in self._config:
        ret.add(self._config["save_timestamp_to"])
      if "edge_attr_schema" in self._config:
        ret = ret.union(self._config['edge_attr_schema'].output_attr_names())
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if 'dst_id_from_item_attr' in self._config:
      if "save_weight_to" in self._config:
        ret.add(self._config["save_weight_to"])
      if "save_timestamp_to" in self._config:
        ret.add(self._config["save_timestamp_to"])
      if "edge_attr_schema" in self._config:
        ret = ret.union(self._config['edge_attr_schema'].output_attr_names())
    return ret

  @strict_types
  def depend_on_items(self) -> bool:
    return 'src_id_from_item_attr' in self._config or 'dst_id_from_item_attr' in self._config

  @strict_types
  def _check_config(self) -> None:
    if not (('src_id_from_common_attr' not in self._config) ^ ('src_id_from_item_attr' not in self._config)):
      raise ArgumentError(f"kgnn args src_id_from_common_attr 和 src_id_from_item_attr 只能定义一种: {self._config}")
    if not (('dst_id_from_common_attr' not in self._config) ^ ('dst_id_from_item_attr' not in self._config)):
      raise ArgumentError(f"kgnn args dst_id_from_common_attr 和 dst_id_from_item_attr 只能定义一种: {self._config}")
    if ('save_weight_to' not in self._config) and ('save_timestamp_to' not in self._config) and ('edge_attr_schema' not in self._config):
      raise ArgumentError(f"至少需配置一种输出 attr (save_weight_to, save_timestamp_to, edge_attr_schema)")
    essential_args = ["kess_service", "relation_name", "shard_num"]
    for item in essential_args:
      if item not in self._config:
        raise ArgumentError(f"{item} not defined in kgnn mixin: {self._config}")

  @strict_types
  def is_async(self) -> bool:
    return True
