#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .local_life_observer import *
from .local_life_arranger import *
from .local_life_enricher import *
from .local_life_mixer import *
from .local_life_retriever import *

class LocalLifeApiMixin(CommonLeafBaseMixin):
  def local_life_pxtr_quantile_send(self, **kwargs):
    """
    LiveLifePxtrQuantileObserver
    ------
    记录模型 PXTRs 用以计算分位数

    参数配置
    ------
    `model_pxtrs`: [list] 必配. model/pxtr 的相关配置
      - `model`: [string] [动态参数] 必配. 模型名字
      - `pxtr`: [string] 必配. pxtr 的 attr 名字
    `city_id`: [int] [动态参数] 必配. 当前请求的 City ID
    `time_window_seconds`: [int] [动态参数] 必配. 计算分位数的时间窗口
    `sampling_ratio`: [double] [动态参数] 必配. [0.0, 1.0] 之间的数，请求的采样率。
    `top_ratio`: [double] [动态参数] 必配. [0.0, 1.0] 之间的数，保存 PXTR 最高的比率。
    `min_item_count`: [int] [动态参数] 必配. 每次请求最小采样 item 个数。
    `redis_cluster`: [string] 必配. 存储 PXTR 数据的 Redis 集群名。
    调用示例
    ------
    ``` python
    .local_life_pxtr_quantitle_send(
      model_pxtrs = [
        {
          "model": "{{ctr_model_name}}",
          "pxtr": "ctr"},
        {
          "model": "{{cvr_model_name}}",
          "pxtr": "cvr"},
        }],
      city_id="{{city_id}}",
      time_window_seconds="{{time_window_seconds}}",
      sampling_ratio=0.01,
      top_ratio=0.1,
      min_item_count=10,
      redis_cluster="recoLocalLifePxtrs")
    ```
    """
    self._add_processor(LocalLifePxtrQuantileObserver(kwargs))
    return self
  
  def local_life_limit_by_bucket(self, **kwargs):
    """
    LocalLifeBucketLimitArranger
    ------
    对结果集分桶进行排序（可选）截断缩减，对于不在配置中的任何一个桶的 items 会被丢弃掉，操作本身不改变 item 之间的相对顺序；
    支持某个 item 属于多个分桶。例如：
    对结果集分两个桶：时长桶和营收桶，每个桶取前 10 个 items 截断；某一个 items 可能在时长和营收上都会表现得比较好，
    这个 item 可以即属于营收桶也属于时长桶，在时长桶排在第 11 位，在营收桶排在第 8 位，这个 item 仍然可以透出。

    参数配置
    ------
    `buckets`: [list] 每个分桶的定义是一个包含下面字段的 dict:
     - `attr_name`: [string] 用来标志该桶的 item 属性名，仅支持 int 属性
     - `attr_value`: [int] 用来标志该桶的 item 属性值
     - `ratio`: [double] [动态参数] 该桶保留的比例，如果为 1.0 代表获取全部 items
     - `limit`: [int] [动态参数] 该桶保留的数目，-1 代表获取全部 items, `ratio` 与 `limit` 至少配置一个，同时存在时取两者的最小值
     - `leftover_picker`: [bool] 选配项，该桶是否可以多保留别的桶没用完的 quota, 默认值 false; 仅有一个桶可保留别的桶没用完的 quota
     - `sort`: [dict] 选配项，排序相关配置，不配置按现有顺序截断，这里的排序不影响结果集的顺序
       - `attr_name`: [string] 用来排序的 item 属性名，仅支持 double 类型属性
       - `sort_order`: [int] 0 为升序排序， 1 为降序排序，默认值 1
     - `diversity`: [dict] 选配项，多样性打散相关配置
       - `attr_name`: [string] [动态参数] 用来多样性打散的 item 属性名，仅支持 int 类型属性
       - `window_size`: [int] [动态参数] 窗口大小
       - `max_count`: [int] [动态参数] 窗口内最多出现多少个某属性的结果
    `diversity_attrs`: [string list] 上面 bucket diversity 所有需要用到的 Item Attrs
    `left_picker_skip_selected_item`: [string] [动态参数]  捡漏桶跳过其他桶已选中 item 的开关 属性名
    `total_item_limit`: [string] [动态参数] 总限制的 item 个数
    `enable_left_picker_fill_all`: [string] 捡漏桶是否填满 total_item_limit， 必须与 total_item_limit 配合使用
    `fix_sort_attr`: [string] 是否修复 sort 的 bug 默认 false
    `isonerec_attr`: [string] 根据该attrname获取是否为onerec结果，大于0时为真，该属性不为空时，根据该attr的值为primary key，score为secondary key进行排序
    调用示例
    ------
    🎯 [Try in Playground](http://ksurl.cn/eGHSf5P6)
    ``` python
    .local_life_limit_by_bucket(
      buckets=[
        {"attr_name": "bucket_a_flag", "attr_value": 1, "ratio": "{{bucket_a_ratio}}", "sort": {"attr_name": "bucket_a_score"}},
        {"attr_name": "bucket_b_flag", "attr_value": 1, "ratio": 0.9, "sort": {"attr_name": "bucket_b_score"}},
        {"attr_name": "bucket_c_flag", "attr_value": 1, "limit": "{{bucket_c_limit}}"},
        {"attr_name": "bucket_d_flag", "attr_value": 1, "ratio": "{{bucket_d_ratio}}", "limit": "{{bucket_d_limit}}"}
      ],
      diversity_attrs=['local_life_photo_info__poi_info__poi_cate_3_id', 'business_id'],
      left_picker_skip_selected_item='{{left_picker_skip_selected_item}}'
      total_item_limit='{{total_item_limit}}'
      fix_sort_attr='fix_sort'
      enable_left_picker_fill_all='enable_left_picker_fill_all'
    )
    ```
    """
    self._add_processor(LocalLifeBucketLimitArranger(kwargs))
    return self
  
  def local_life_counter_by_redis(self, **kwargs):
    """
    LocalLifeCounterByRedisEnricher
    ------
    使用 Redis 做个简单记计数器

    参数配置
    ------
    `redis_cluster`: [string] 必填, redis 的 kcc cluster name
    `timeout_ms`: [int] 选配项, redis client 的超时时间，单位 ms, 默认为 10 ms
    `counters`: [list] 必填，每个元素是一个 dict:
     - `key`: [string] [动态参数] 计数器的 key
     - `offset`: [int] [动态参数] 选配项，计数器计数增加值，默认值 1
     - `expire_seconds`: [int] [动态参数]，选配项，单位秒, 负数或者 0 表示不设置过期时间，默认 -1
     - `result_attr`: [string] 选配项，把计数结果写回 common attr 的名字
    调用示例
    ------
    🎯 [Try in Playground](http://ksurl.cn/eGHSf5P6)
    ``` python
    .local_life_counter_by_redis(
      redis_cluster="test_cluster",
      timeout_ms=10,
      counters=[{"key": "{{some_key}}", "offset": "{{some_value}}"}]
    )
    ```
    """
    self._add_processor(LocalLifeCounterByRedisEnricher(kwargs))

  def local_life_negative_sample(self, **kwargs):
    """
    LocalLifeNegativeSampleEnricher
    ------
    本地生活粗排负采样。
    采样来源：当前结果集 + 指定的采样空间。
    采样策略：根据配置进行采样，支持分段设置采样数。分段区间与当前结果集有交集时，优先从结果集采样，没有交集时，从指定采样空间随机采样
    采样结果：存储采样的 item key 到指定的 common attr
            存储采样的 item 在当前结果集中的位置到指定的 common attr
            存储采样的 item 指定的 item attr 到指定的 common attr
    
    参数配置
    ------
    `negative_sample_config_kconf_key`: [string] [动态参数] 采样策略配置的 kconf key
    `random_negative_sample_source_attr_name`: [string] 指定采样空间的数据源
    `extra_attr_params`: [list[dict]] 需要存储的 pxtr 列表，仅支持 double, int, string 类型
      `name`: [string] 需要发送的 item pxtr name
      `as`: [string] 对应的 pxtr 存储到结果的 common attr 中的 name
    `save_negative_sample_item_idx_to`: [string] 采样结果在结果集中的位置存储到的 common attr name
    `save_negative_sample_item_key_to`: [string] 采样结果 的 item key 存储到的 common attr name
    
    调用示例
    ------
    ``` python
    .local_life_negative_sample(
      negative_sample_config_kconf_key = "reco.localLife.leaf_negative_sample_config",
      random_negative_sample_source_attr_name = "item_list_before_cascading",
      extra_attr_params = [
        {"name": "fr_pctr", "as": "local_life_negative_sample_fr_pctr"},
        {"name": "fr_pcvr", "as": "local_life_negative_sample_fr_pcvr"}
      ],
      save_negative_sample_item_idx_to = "negative_sample_item_idx_list",
      save_negative_sample_item_key_to = "negative_sample_item_key_list"
    )
    ```
    """
    self._add_processor(LocalLifeNegativeSampleEnricher(kwargs))
    return self
  
  def local_life_lane_mix(self, **kwargs):
    """
    LocalLifeLaneMixer
    ------
    合并多个 item tables 的结果到一张表：
    - 如果一个 item_key 出现在多个表中，不会重复插入，仅取排在前面的表里面的 item
    - 需要复制的 item attrs 最好在目的表里面已经出现了，这样类型都按目的表的类型
    
    参数配置
    ------
    `dest_table`: [string] 选配项，目的表名，默认值为主表
    `src_tables`: [list] 源表名
    `copy_item_attrs`: [list] 选配项，要从源表复制的 item 属性名

    调用示例
    ------
    ``` python
    .local_life_lane_mix(
      dest_table="",
      src_tables=["hotel_tourism", "catering"],
      copy_item_attrs=["rank_score"]
    )
    ```
    """
    self._add_processor(LocalLifeLaneMixer(kwargs))
    return self

  def local_life_poi_id_fix(self, **kwargs):
    """
    LocalLifePoiIdFixEnricher
    ------
    本地生活 poi id 修正
    
    参数配置
    ------
    `poi_id_list_attr`: [string] 按离用户距离排好序的 poi id list attr name 
    `save_new_poi_id_to`: [string] 修正结果存储到的 attr name
    `poi_key_prefix`: [string] poi 做 key 时，需要拼接的 prefix
    `parallel_fix_thread_num`: [int] 并行修复的线程数， 默认 5
    `fix_batch_size`: [int] 并行修复的单 batch 的 item 个数， 默认50
    `check_brand_attr`: [string] 选配项，该 attr 指定做 POI 修复的时候是否对比 Item 与 POI 的品牌
    `poi_brand_id_list_attr`: [string] 该 attr 指定 poi_id_list_attr 中的 POI 品牌 ID, check_brand_attr 配置时必配
    `item_brand_id_attr`: [string] 该 attr 获取 Item 的品牌 ID, check_brand_attr 配置时必配
    `skip_brand_opt_category3rd_list`: [int list] [动态参数] 选配项，该 attr 指定哪些三级类目的 POI 不用品牌检查优化
    `poi_v3category3rd_id_list_attr`: [string] 该 attr 指定 poi_id_list_attr 中的 POI V3版第三级类目 ID, skip_brand_fix_category3d_list 配置时必配

    调用示例
    ------
    ``` python
    .local_life_poi_id_fix(
      poi_id_list_attr = "user_near_poi_id_ordered_list",
      poi_key_prefix = "poi_",
      save_new_poi_id_to = "fixed_poi",
      parallel_fix_thread_num=5,
      fix_batch_size=30
    )
    ```
    """

    self._add_processor(LocalLifePoiIdFixEnricher(kwargs))
    return self

  def local_life_day_frequence_control(self, **kwargs):
    """
    LocalLifeDayFrequenceControlEnricher
    ------
    本地生活 频控：N天曝光xx次后，M天不出
    
    参数配置
    ------
    `import_freq_id_list`: [string] 频控对象 id list attr name 
    `import_freq_vv_list`: [string] 频控对象 曝光计数 list attr name 
    `import_freq_ts_list`: [string] 频控对象 时间戳 list attr name
    `freq_window_day_size`: [int][动态参数] 频控窗口天数， 默认 1
    `freq_window_vv_threshold`: [int][动态参数] 频控窗口曝光阈值， 默认10
    `freq_limit_day_size`: [int][动态参数]
    `data_pdate`: [int][动态参数] 数据的生产日期
    `export_freq_id_list_name`: [string] 输出频控对象 id list attr name

    调用示例
    ------
    ``` python
    .local_life_day_frequence_control(
      import_freq_id_list = "freq_id_list",
      import_freq_vv_list = "freq_vv_list",
      import_freq_ts_list = "freq_ts_list",
      freq_window_day_size = 1，
      freq_window_vv_threshold = 10，
      freq_limit_day_size = 3,
      data_pdate = 20240228,
      export_freq_id_list_name = "export_freq_id_list"
    )
    ```
    """

    self._add_processor(LocalLifeDayFrequenceControlEnricher(kwargs))
    return self

  def local_life_after_marketing_frequence_control(self, **kwargs):
    """
    LocalLifeAfterMarketingFrequenceControlEnricher
    ------
    本地生活营销后频控：N分钟 曝光 xx次后，M分钟不出
    
    参数配置
    ------
    `import_freq_id_list`: [string] 频控对象 id list attr name 
    `import_freq_ts_list`: [string] 频控对象 时间戳 list attr name
    `freq_window_size`: [int][动态参数] 频控窗口时间长度， 默认 1
    `freq_window_vv_threshold`: [int][动态参数] 频控窗口曝光阈值， 默认1
    `freq_limit_size`: [int][动态参数] 频控限制的时间长度， 默认1
    `time_unit_size`: [int][动态参数] 时间单位长度，默认1分钟
    `export_freq_id_list_name`: [string] 输出频控对象 id list attr name

    调用示例
    ------
    ``` python
    .local_life_after_marketing_frequence_control(
      import_freq_id_list = "freq_id_list",
      import_freq_ts_list = "freq_ts_list",
      freq_window_size = 60，
      freq_window_vv_threshold = 10，
      freq_limit_size = 60,
      time_unit_size = 1,
      export_freq_id_list_name = "export_freq_id_list"
    )
    ```
    """

    self._add_processor(LocalLifeAfterMarketingFrequenceControlEnricher(kwargs))
    return self

  def local_life_photo_real_show_new_brand_id(self, **kwargs):
    """
    LocalLifePhotoRealShowNewBrandIdEnricher
    ------
    本地生活频控，生成real show品牌字段，逻辑参考曹勇的早期实现f
    
    参数配置
    ------
    `time_limit_min`: [int][动态参数] 距离当前的时间间隔
    `len_limit`: [int][动态参数] 用于频控统计的序列截断数量
    `num_limit`: [int][动态参数] 频控统计的候选次数
    `brand_id_list`: [intList][动态参数] 用户曝光过的brand id list
    `brand_ts_list`: [intList][动态参数] 用户曝光过的brand ts list
    `output_attr`: [string] 输出频控对象 id list attr name

    调用示例
    ------
    ``` python
    .local_life_photo_real_show_new_brand_id(
      time_limit_min = 60,
      len_limit = 100,
      num_limit = 2,
      brand_id_list = "uLocalLifeBrowsedNewBrandIdList",
      brand_ts_list = "uLocalLifeBrowsedNewBrandTsList",
      output_attr = "real_show_frequency_control_new_brand_id"
    )
    ```
    """

    self._add_processor(LocalLifePhotoRealShowNewBrandIdEnricher(kwargs))
    return self

  def local_life_photo_guaranteed_info_enricher(self, **kwargs):
    """
    LocalLifeGuaranteedInfoEnricher
    ------
    获取本地生活 photo 保量信息 
    
    参数配置
    ------
    `exp_name_attr`: [string] exp 组
    `guaranteed_info_attr_config`: [list] 需要抽取的属性列表，每项值为字符串或 {"name"="xxx", "path"="yyy"} 的 dict 格式，path 只支持 [LocalLifePhotoGuaranteedInfo](https://git.corp.kuaishou.com/reco-cpp/reco_proto/-/blob/master/proto/reco_local_life.proto) 中的字段

    调用示例
    ------
    ``` python
    .local_life_photo_guaranteed_info_enricher(
      exp_name_attr = "exp_name",
      guaranteed_info_attr_config=[
        "target_vv",
        {"name": "real_vv", "path": "real_vv" }
      }
    ]
    ```
    """

    self._add_processor(LocalLifeGuaranteedInfoEnricher(kwargs))
    return self

  def local_life_retrieval_filter(self, **kwargs):
    """
    LocalLifeRetrievalFilterArranger
    ------
    本地生活召回过滤

    参数配置
    ------
    `item_attr_map` : [dict] 传入的 item attr 
    `filters` : [list] 传入过滤算子的相关配置
    `truncation_map` : [list] 传入分 reason 截断数， default 为默认截断数
    `debug_mode` : [bool] 是否开启 debug 模式，如果为 true ，则导出 item 会被哪些 filter 过滤，但并未实际过滤，也不截断，默认为false。
    `export_item_attr` : [string] 指定将 filter list 导入到哪个 item attr ， debug_mode 为 true 时必须配置该项。

    调用示例
    ------
    ``` python
    ITEM_ATTR_MAP = {
      "photo_id_attr": "photo_id"
    }
    FILTERS = [
      {
        "name": "demo",
        "enable": True,
        "bad_photo_id_list_attr": "bad_photo_id_list"
      }
    ]
    self.local_life_retrieval_filter(
      name = "demo_retrieval_filter",
      traceback = True,
      item_attr_map = ITEM_ATTR_MAP,
      filters = FILTERS,
      truncation_map = {
        "default": 6
      }
    )
    ```
    """

    self._add_processor(LocalLifeRetrievalFilterArranger(kwargs))
    return self
  
  def local_life_live_crowd_enricher(self, **kwargs):
    """
    LocalLifeLiveCrowdEnricher
    ------
    获取本地生活直播人群包

    参数配置
    ------
    `crowd_name` : [string] 人群包名称, 必须是 kconf 里的
    `crowd_load_kconf_key` : [string] 人群包信息加载配置 kconf
    `item_score_key_attr_name` : [string] 拉取 item 分值时的 key 来源，只支持 int 类型的 item attr
    `default_item_score` : [double] 属于人群包时，非本人群包 item 的默认分值
    `is_crowd_user_common_attr_save_to` : [string] 是否属于人群包的 common attr name
    `crowd_item_score_attr_save_to` : [string] item score 存储的 item attr name

    调用示例
    ------
    ``` python
    self.local_life_live_crowd_enricher(
      crowd_name = "LOCAL_LIFE_AGRICULTURAL_CORE_USER",
      crowd_load_kconf_key = "reco.localLife.l2LiveCrowdLoadConfig",
      item_score_key_attr_name = "aid",
      default_item_score=0.0,
      is_crowd_user_common_attr_save_to="is_agricultural_user",
      crowd_item_score_attr_save_to="agricultural_crowd_score"
    )
    ```
    """
    self._add_processor(LocalLifeLiveCrowdEnricher(kwargs))
    return self
  
  def local_life_live_quantile_normalize_enricher(self, **kwargs):
    """
    LocalLifeLiveQuantileNormalizeEnricher
    ------
    本地生活直播分位数归一化

    参数配置
    ------
    `pxtr_names` :  [string_list] 必配项，需要归一化的 pxtr 名字

    `model_name_attr` :  [string_list] 必配项，需要归一化的 pxtr 的模型名字，需要保证与 pxtr_names 等长

    `app_name` :  [string] [动态参数] 必配项，app_name (kuaishou / nubula)

    `normalized_pxtr_prefix` : [string] 校准后分数的名字前缀

    `override_normalize_keys`: [string_list] 替换的归一化key，若配置必须与 pxtr_names 等长

    `log_trans_attr`: [string] 是否在先进行 log 变换再做归一化

    调用示例
    ------
    ``` python
    self.local_life_live_quantile_normalize_enricher(
      pxtr_names = "{{pxtr_names}}",
      model_name_attr = "{{model_names}}",
      app_name="kuaishou",
      normalized_pxtr_prefix = "normalized_",
      log_trans_attr="lll_log_trans_in_normalize"
    )
    ```
    """
    self._add_processor(LocalLifeLiveQuantileNormalizeEnricher(kwargs))
    return self
  
  def local_life_live_author_boost_enricher(self, **kwargs):
    """
    LocalLifeLiveAuthorBoostEnricher
    ------
    本地生活直播主播 boost 参数获取

    参数配置
    ------
    `author_boost_prefix` :  [string] [动态参数] 必配项，参数分组名

    `author_id_attr` :  [string] [动态参数] 必配项，author id 保存的 attr 名字

    `extra_attr_params` :  [list] 必配项，需要取的字段， 类似 name as 可省略 as/default_val
      - `name`: [string] 原始字段名
      - `as`: [string] 保存的 attr 名字
      - `default_val` [double] 默认值 默认 1.0

    调用示例
    ------
    ``` python
    self.local_life_live_author_boost_enricher(
     author_boost_prefix = "{{author_boost_prefix}}",
      author_id_attr="aid",
      extra_attr_params = [
        "authorScore",
        {"name": "pf", "as": "filter_ratio", "default_val":1.0},
        {"name": "sort", "as": "sort_ratio", "default_val":1.0},
        {"name": "wtrScore", "default_val":0.0}
      ]
    )
    ```
    """
    self._add_processor(LocalLifeLiveAuthorBoostEnricher(kwargs))
    return self
  
  def local_life_live_calibrate_cvr_enricher(self, **kwargs):
    """
    LocalLifeLiveCalibrateCvrEnricher
    ------
    本地生活直播分位数校准

    参数配置
    ------
    `kconf_key` :  [string] 必配项，校准源数据kconf

    `cvr_name` :  [string] 必配项，校准数据源kconf中对应数据的key

    `normalized_attr_name` :  [string] 必配项，归一化值所在 attr 名称

    `ouput_attr` : [string] 必配项，校准后分数存储到的 attr 名称

    调用示例
    ------
    ``` python
    self.local_life_live_calibrate_cvr_enricher(
      kconf_key = "reco.rocket.cvrCalibrationMaps",
      cvr_name = "watch_time",
      normalized_attr_name = "normalized_cvr_value",
      ouput_attr = "calibrated_cvr"
    )
    ```
    """
    self._add_processor(LocalLifeLiveCalibrateCvrEnricher(kwargs))
    return self
  
  def local_life_live_distribution_score_enricher(self, **kwargs):
    """
    LocalLifeLiveDistributionScoreEnricher
    ------
    本地生活直播分位数校准

    参数配置
    ------
    `redis_cluster` :  [string] 必配项，redis 集群名称

    `redis_timeout` :  [int] 必配项，redis 请求超时

    `calibration_name` : [string][动态参数] 必配项，校准使用的 key 名称，与 "rocket_dist:" 拼接为 key

    `monotonize` : [bool] 必配项，是否对分布做单调化处理

    `normalized_attr_name` :  [string] 必配项，归一化值所在 attr 名称

    `ouput_attr` : [string] 必配项，校准后分数存储到的 attr 名称

    Redis 缓存的可选配置项:

    `cache_bits`: [int] 选配项，cache 大小，即最多存 2^cache_bits 个 kv 值（LRU 删除），默认为 0（无 cache）

    `cache_expire_second`: [int] 选配项，cache 内的数据过期的时间，默认为 3600 秒

    `cache_delay_delete_ms`: [int] 选配项，cache 内的数据延迟删除的时间，一般使用默认值即可，默认为 10 秒

    `cache_name`: [string] 选配项，用于在 [grafana 监控](https://grafana.corp.kuaishou.com/d/0jCdxsQMk/kv_cache_client?orgId=3) 中区分不同 cache 的命中率等信息，默认跟 cluster_name 相同

    调用示例
    ------
    ``` python
    self.local_life_live_distribution_score_enricher(
      redis_cluster = "recoLocalLifeLive",
      redis_timeout = 20,
      calibration_name = "locallife_gmv",
      monotonize = true,
      normalized_attr_name = "fr_pctr",
      ouput_attr = "calibrated_cvr"
    )
    ```
    """
    self._add_processor(LocalLifeLiveDistributionScoreEnricher(kwargs))
    return self

  def local_life_dump_context(self, **kwargs):
    """
    LocalLifeDumpContextEnricher
    ------
    将当前 context 中的结果集和 attr 数据快照保存到指定的 string 类型 common_attr 中，后续可发送给 redis 等外部存储供其它使用。

    参数配置
    ------

    `common_attrs`: [list] 用以填写存储的 common_attr 的 list

    `item_attrs`: [list] 填写存储的 item_attr 的 list

    `dump_to_attr`: [string] 必配项，将 attr 和 item 数据 dump 后的 string 内容存储到指定 common_attr 下

    `stage_name`: [string] 选配项，标识当前的 stage, 默认为空字符串

    `need_record_item_id`: [bool] 选配项，是否填充 item_id，默认为 true

    调用示例
    ------
    ``` python
    .local_life_dump_context(
      common_attrs=["test1", "test2"],
      item_attrs=["test_item1", "test_item2"],
      dump_to_attr="dump_context",
      stage_name="retr"
    )
    ```
    """
    self._add_processor(LocalLifeDumpContextEnricher(kwargs))
    return self
  
  def local_life_retrieval_quota_merge_arranger(self, **kwargs):
    """
    LocalLifeRetrievalQuotaMergeArranger
    ------
    按优先级合并召回队列，截断并同时去重。

    注意: min_limit_num/max_limit_num 仅对配置权重>0的召回路生效，用于纠偏。【未配置权重】&【权重=0】的将不会有透出

    参数配置
    ------

    `min_limit_num`: [int][动态参数] 必配项，最低单路召回数，仅对配置权重>0的召回路纠偏

    `max_limit_num`: [int][动态参数] 选配项，最高单路召回数，仅对配置权重>0的召回路纠偏

    `total_limit_num`: [int][动态参数] 必配项，总截断数

    `dedup_reason_attr_name`: [string] 必配项，去重 reason_list 存储到的 attr 名

    `retrieval_weight`: [json][动态参数] 必配项，reason -> weight

    调用示例
    ------
    ``` python
    .local_life_retrieval_quota_merge_arranger(
      min_limit_num=1,
      max_limit_num=100,
      total_limit_num=8000,
      dedup_reason_attr_name="reason_list",
      retrieval_weight: {
        "100": 3.0,
        "200": 5.0,
        "300": 2.0,
        "300": "{{maybe_ab_parameter_common_attr_name}}",
      }
    )
    ```
    """
    self._add_processor(LocalLifeRetrievalQuotaMergeArranger(kwargs))
    return self

  
  def local_life_calc_degradation_ratio(self, **kwargs):
    """
    LocalLifeDegradationEnricher
    ------
    根据kconf计算当前的降级比例。

    参数配置
    ------

    `kconf_key`: [string] 降级配置 kconf

    `stage`: [string] 降级阶段

    `save_degradation_ratio_to`: [string] 降级比例存储的 attr

    调用示例
    ------
    ``` python
    .local_life_calc_degradation_ratio(
        kconf_key="reco.localLife.leafTimeBasedDegrade",
        stage="rank",
        save_degradation_ratio_to="rank_degradation_ratio"
      )
    ```
    """
    self._add_processor(LocalLifeDegradationEnricher(kwargs))
    return self
  
  def local_life_live_quantile_normalize_v2_enricher(self, **kwargs):
    """
    LocalLifeLiveQuantileNormalizeV2Enricher
    ------
    本地生活直播分位数归一化

    参数配置
    ------
    `normalize_kconf_key`: 归一化配置的 kconf key

    `normalize_config`:  [list] 必配项，归一化配置
      - `origin_pxtr_name`: [string] 必配项，原始 xtr name
      - `normalize_name`: [string] 必配项，归一化名字，用于唯一标识归一化配置
      - `normalize_xtr_save_to`: [string] 选配项，归一化分数保存的 xtr name， 默认为 normalize_name
    
    `input_common_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    `input_item_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    调用示例
    ------
    ``` python
    self.local_life_live_quantile_normalize_v2_enricher(
      normalize_kconf_key = "reco.localLife.liveNormalizeConfig",
      normalize_config = [
        {
          "origin_pxtr_name": "locallife_ctr",
          "normalize_name": "normalized_locallife_ctr",
          "normalize_xtr_save_to": "normalized_locallife_ctr"
        }
      ]
      input_common_attrs_hint=["product", "fr_model_kess"]
    )
    ```
    """
    self._add_processor(LocalLifeLiveQuantileNormalizeV2Enricher(kwargs))
    return self


  def local_life_live_calibrate_enricher(self, **kwargs):
    """
    LocalLifeLiveCalibrateEnricher
    ------
    本地生活直播校准

    参数配置
    ------
    `calibrate_kconf_key`: 校准配置的 kconf key

    `calibrate_config`:  [list] 必配项，校准配置
      - `origin_pxtr_name`: [string] 必配项，原始 xtr name
      - `calibrate_name`: [string] 必配项，校准名字，用于唯一标识归一化配置
      - `calibrate_score_save_to`: [string] 选配项，校准分数保存的 xtr name， 默认为 calibrate_name
    
    `input_common_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    `input_item_attrs_hint`: [list[string]] 提示内部依赖的 attr, 用于异步图优化

    调用示例
    ------
    ``` python
    self.local_life_live_calibrate_enricher(
      calibrate_kconf_key = "reco.localLife.liveCalibrateConfig",
      calibrate_config = [
        {
          "origin_pxtr_name": "normalized_locallife_ctr_gmv_v2",
          "calibrate_name": "cali_locallife_ctr_gmv",
          "calibrate_score_save_to": "cali_locallife_ctr_gmv"
        }
      ]
      input_common_attrs_hint=["product", "fr_model_kess"]
    )
    ```
    """
    self._add_processor(LocalLifeLiveCalibrateEnricher(kwargs))
    return self

  def local_life_same_city_enrich(self, **kwargs):
    """
    LocalLifeSameCityEnricher
    ------
    本地生活检查用户是否在item可分发城市

    参数配置
    ------
    `user_city_id_attr`: [string] 必配项， 用户的城市 id 的 common attr

    `item_city_id_bitmap_code_attr`:  [string] 必配项，item 的 city id 列表 （bitmap 编码版）的 item attr

    `save_check_res_to`: [string] 保存检查结果的 attr， 1 代表在列表里, 0 表示不在

    调用示例
    ------
    ``` python
    self.local_life_same_city_enrich(
      user_city_id_attr = 'user_city_id',
      item_city_id_bitmap_code_attr='lLocalLifeAllGoodsPoiCityIdBitMapCode',
      save_check_res_to='user_city_in_item_city_list'
    )
    ```
    """
    self._add_processor(LocalLifeSameCityEnricher(kwargs))
    return self

  def l2_live_aid_2_live_id(self, **kwargs):
    """
    L2LiveAid2LiveIdRetriever  
    ------
    本地直播主播 id 转化为直播 id。数据源是 memory data；给定主播列表，顺序查找倒排数据，将结果写到结果集

    参数配置
    ------
    `reason`: [int] 召回标识，添加到结果集的每个数据的 reason 属性取值

    `aid_list_attr`: [string] 存储 aid 列表的 common_attr，将顺序查找倒排数据获取结果并补充到结果集

    `data_map_key`: [string] 指定倒排数据内容，在线请求接口需要传递该参数内容获取指定倒排数据，可选值：kconf 对应配置中 return_type_name = UINT64_UINT64_MAP 的配置项的一级配置名称，比如：aid_2_live_id 配置由 flag 参数：reco_memory_data_map_kconf_path 指定，参见：ks/reco/memory_data_map/util.cc

    调用示例
    ------
    ``` python
    .l2_live_aid_2_live_id(
      reason = 1,
      aid_list_attr = "uid_list",
      data_map_key = "pid_list",
    )
    ```
    """
    self._add_processor(L2LiveAid2LiveIdRetriever(kwargs))
    return self

  def local_life_optimize_intermix(self, **kwargs):
    """
    LocalLifeOptimizeIntermixArranger
    ------
    根据自定义规则将不同的 item 按 item_type 或 item_attr 进行交错编排，例如用于视频和直播的混排策略
    优化CommonRecoIntermixArranger对属性的缓存
    参数配置
    ------
    `mix_on_attr`: [string] 用哪个 item_attr (仅支持 int 类型) 的值进行 pattern 匹配，缺省且不用 reason 做匹配的情况下，将使用 item_type 的值

    `mix_pattern`: [list] [动态参数] 指定的 item_type 或 item_attr 值（必须是 int 类型）交错规则，数组中的各项为具体的 item_type 值。示例：`[1, 1, 0]` 表示让结果集中的 item_type 顺序循环满足 `1, 1, 0, 1, 1, 0, ...` 的排列，规则为尽力满足

    `num_limit`: [int] [动态参数] 仅对前 `num_limit` 个 item 按规则进行重排，可缺省则对所有结果集进行处理

    `mix_on_reason`: [bool] 在 mix_on_attr 为空的情况下，是否用 reason 做 pattern 匹配。

    调用示例
    ------
    ``` python
    .local_life_optimize_intermix(
      mix_on_attr = "category",
      # 1: 视频，0: 直播，该 pattern 表示每 2 个视频跟 1 个直播，无限往复下去
      mix_pattern = [1, 1, 0],
    )
    ```
    """
    self._add_processor(LocalLifeOptimizeIntermixArranger(kwargs))
    return self


  def local_life_one_reco_cache_dump(self, **kwargs):
    """
    LocalLifeOneRecoCacheDumpEnricher
    ------
    onereco 结果缓存，配合 local_life_one_reco_cache_parse 使用

    参数配置
    ------

    `score_attr_name`: [string] 需要缓存的 item 分

    `dump_to_attr`: [string] 序列化结果存储的 attr

    `compress_level`: [int] 压缩级别，默认为 0, 目前支持压缩级别 [-5 ~ 22], 级别越低压缩速度越快，压缩量越小

    调用示例
    ------
    ``` python
    .local_life_one_reco_cache_dump(
      score_attr_name="score",
      dump_to_attr="one_reco_cache_str"
    )
    ```
    """
    self._add_processor(LocalLifeOneRecoCacheDumpEnricher(kwargs))
    return self

  def local_life_one_reco_cache_parse(self, **kwargs):
    """
    LocalLifeOneRecoCacheParseEnricher
    ------
    onereco 结果缓存解析，配合 local_life_one_reco_cache_dump 使用

    参数配置
    ------

    `origin_cache_attr`: [string] 直接从 redis 读出来的原始字符串

    `save_id_to`: [string] 存储的 item id list 的 common_attr

    `save_score_to`: [string] 存储的 item score list 的 common_attr

    调用示例
    ------
    ``` python
    .local_life_one_reco_cache_parse(
      origin_cache_attr="one_reco_cache_str",
      save_id_to="cache_id_list",
      save_score_to="cache_id_score_list"
    )
    ```
    """
    self._add_processor(LocalLifeOneRecoCacheParseEnricher(kwargs))
    return self

