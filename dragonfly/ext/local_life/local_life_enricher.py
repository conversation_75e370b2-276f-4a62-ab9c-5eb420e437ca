#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, extract_attr_names, check_arg
from ...common_leaf_processor import LeafEnricher


class LocalLifeNegativeSampleEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_negative_sample"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("negative_sample_config_kconf_key")))
    attrs.add(self._config.get("random_negative_sample_source_attr_name"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("extra_attr_params", []), "name")
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = extract_attr_names(self._config.get("extra_attr_params", []), "as")
    ret.add(self._config.get("save_negative_sample_item_key_to"))
    ret.add(self._config.get("save_negative_sample_item_idx_to"))
    return ret

class LocalLifeCounterByRedisEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_counter_by_redis'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for param in self._config.get('counters'):
      for key in ["key", "offset", "expire_seconds"]:
        if key in param.keys():
          attrs.update(self.extract_dynamic_params(param[key]))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for param in self._config.get('counters'):
      for key in ["result_attr"]:
        if key in param.keys():
          attrs.add(param[key])
    return attrs


class LocalLifePoiIdFixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_poi_id_fix'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("poi_id_list_attr"))
    for attr in ["check_brand_attr", "poi_brand_id_list_attr", "poi_v3category3rd_id_list_attr"]:
      if attr in self._config.keys():
        attrs.add(self._config.get(attr))
    for key in ["skip_brand_opt_category3rd_list"]:
      if key in self._config.keys():
        attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in ["item_brand_id_attr"]:
      if attr in self._config.keys():
        attrs.add(self._config.get(attr))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_new_poi_id_to"))
    return attrs
  
class LocalLifeDayFrequenceControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_day_frequence_control'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("import_freq_id_list"))
    attrs.add(self._config.get("import_freq_vv_list"))
    attrs.add(self._config.get("import_freq_ts_list"))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_window_day_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_window_vv_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_limit_day_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("data_pdate")))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_freq_id_list_name"))
    return attrs

class LocalLifeAfterMarketingFrequenceControlEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_after_marketing_frequence_control'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("import_freq_id_list"))
    attrs.add(self._config.get("import_freq_ts_list"))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_window_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_window_vv_threshold")))
    attrs.update(self.extract_dynamic_params(self._config.get("freq_limit_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("time_unit_size")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_freq_id_list_name"))
    return attrs

class LocalLifePhotoRealShowNewBrandIdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_photo_real_show_new_brand_id'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("time_limit_min")))
    attrs.update(self.extract_dynamic_params(self._config.get("len_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("num_limit")))
    attrs.update(self.extract_dynamic_params(self._config.get("brand_id_list")))
    attrs.update(self.extract_dynamic_params(self._config.get("brand_ts_list")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("output_attr"))
    return attrs

class LocalLifeGuaranteedInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_photo_guaranteed_info_enricher'
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("exp_name_attr"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("guaranteed_info_attr_config", []):
      if isinstance(attr, str):
        attrs.add(attr)
      elif "name" in attr:
        attrs.add(attr["name"])
    return attrs
  
class LocalLifeLiveCrowdEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_live_crowd_enricher'
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_score_key_attr_name"))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("is_crowd_user_common_attr_save_to"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("crowd_item_score_attr_save_to"))
    return attrs

class LocalLifeLiveQuantileNormalizeEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_live_quantile_normalize_enricher'
  
  @strict_types
  def _check_config(self) -> None:
    check_arg("model_name_attr" in self._config, "必须配置 model_name_attr")
    check_arg("pxtr_names" in self._config, "必须配置 pxtr_names")
    check_arg(len(self._config.get("pxtr_names")) == len(self._config.get("model_name_attr")), "model_name_attr 和 pxtr_names size 必须一致")
    if ("override_normalize_keys" in self._config):
      check_arg(len(self._config.get("pxtr_names")) == len(self._config.get("override_normalize_keys")), "override_normalize_keys 和 pxtr_names size 必须一致")
    check_arg("app_name" in self._config, "必须配置 app_name")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("model_name_attr"))
    attrs.update(self.extract_dynamic_params(self._config.get("app_name")))
    attrs.add(self._config.get("log_trans_attr"))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("pxtr_names"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("pxtr_names"):
      attrs.add(self._config.get("normalized_pxtr_prefix") + attr)
    return attrs
  
class LocalLifeLiveAuthorBoostEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_live_author_boost_enricher'
  
  @strict_types
  def _check_config(self) -> None:
    check_arg("extra_attr_params" in self._config, "必须配置 extra_attr_params")
    check_arg("author_id_attr" in self._config, "必须配置 author_id_attr")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("author_boost_prefix")))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("author_id_attr"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return extract_attr_names(self._config.get("extra_attr_params", []), "as")

class LocalLifeLiveCalibrateCvrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_live_calibrate_cvr_enricher'
  
  @strict_types
  def _check_config(self) -> None:
    check_arg("kconf_key" in self._config, "必须配置 kconf_key")
    check_arg("cvr_name" in self._config, "必须配置 cvr_name")
    check_arg("normalized_attr_name" in self._config, "必须配置 normalized_attr_name")
    check_arg("ouput_attr" in self._config, "必须配置 ouput_attr")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("normalized_attr_name"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ouput_attr"))
    return attrs

class LocalLifeLiveDistributionScoreEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return 'local_life_live_distribution_score_enricher'
  
  @strict_types
  def _check_config(self) -> None:
    check_arg("redis_cluster" in self._config, "必须配置 redis_cluster")
    check_arg("redis_timeout" in self._config, "必须配置 redis_timeout")
    check_arg("calibration_name" in self._config, "必须配置 calibration_name")
    check_arg("monotonize" in self._config, "必须配置 monotonize")
    check_arg("normalized_attr_name" in self._config, "必须配置 normalized_attr_name")
    check_arg("ouput_attr" in self._config, "必须配置 ouput_attr")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config['calibration_name']))
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("normalized_attr_name"))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("ouput_attr"))
    return attrs

class LocalLifeDumpContextEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_dump_context"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("common_attrs", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config:
      attrs.update(self._config.get("item_attrs", []))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("dump_to_attr", "")
    attrs.add(attr)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("dump_to_attr" in self._config, "必须配置 dump_to_attr")
    if "item_attrs" in self._config:
      check_arg(isinstance(self._config.get("item_attrs"), list), 'item_attrs 必须是 list')
    if "common_attrs" in self._config:
      check_arg(isinstance(self._config.get("common_attrs"), list), 'common_attrs 必须是 list')

class LocalLifeDegradationEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_calc_degradation_ratio"
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("save_degradation_ratio_to", "")
    attrs.add(attr)
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("kconf_key" in self._config, "必须配置 kconf_key")
    check_arg("stage" in self._config, "必须配置 stage")
    check_arg("save_degradation_ratio_to" in self._config, "必须配置 save_degradation_ratio_to")

class LocalLifeLiveQuantileNormalizeV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_live_quantile_normalize_v2_enricher"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("normalize_config"):
      if "normalize_xtr_save_to" in config:
        attrs.add(config["normalize_xtr_save_to"])
      else:
        attrs.add(config["normalize_name"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs_hint", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config:
      attrs.update(self._config.get("input_item_attrs_hint", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("normalize_kconf_key" in self._config, "必须配置 kconf_key")
    check_arg("normalize_config" in self._config, "必须配置 normalize_config")
    check_arg(isinstance(self._config.get("normalize_config"), list), 'normalize_config 必须是 list')
    for config in self._config.get("normalize_config"):
      check_arg("origin_pxtr_name" in config, "必须配置 origin_pxtr_name")
      check_arg("normalize_name" in config, "必须配置 normalize_name")


class LocalLifeLiveCalibrateEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_live_calibrate_enricher"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for config in self._config.get("calibrate_config"):
      if "calibrate_score_save_to" in config:
        attrs.add(config["calibrate_score_save_to"])
      else:
        attrs.add(config["calibrate_name"])
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get("input_common_attrs_hint", []))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "item_attrs" in self._config:
      attrs.update(self._config.get("input_item_attrs_hint", []))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("calibrate_kconf_key" in self._config, "必须配置 kconf_key")
    check_arg("calibrate_config" in self._config, "必须配置 calibrate_config")
    check_arg(isinstance(self._config.get("calibrate_config"), list), 'calibrate_config 必须是 list')
    for config in self._config.get("calibrate_config"):
      check_arg("origin_pxtr_name" in config, "必须配置 origin_pxtr_name")
      check_arg("calibrate_name" in config, "必须配置 calibrate_name")

class LocalLifeSameCityEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_same_city_enrich"
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("save_check_res_to", ""))
    return attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("user_city_id_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("item_city_id_bitmap_code_attr", ""))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg("user_city_id_attr" in self._config, "必须配置 user_city_id_attr")
    check_arg("item_city_id_bitmap_code_attr" in self._config, "必须配置 item_city_id_bitmap_code_attr")
    check_arg("save_check_res_to" in self._config, "必须配置 save_check_res_to")


class LocalLifeOneRecoCacheParseEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_one_reco_cache_parse"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("origin_cache_attr", "")
    attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("save_id_to", "")
    attrs.add(attr)

    attr = self._config.get("save_score_to", "")
    attrs.add(attr)
    return attrs


class LocalLifeOneRecoCacheDumpEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_one_reco_cache_dump"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("score_attr_name", "")
    attrs.add(attr)
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("dump_to_attr", "")
    attrs.add(attr)
    return attrs
