#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafObserver


class LocalLifePxtrQuantileObserver(LeafObserver):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_pxtr_quantile_send"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for param in self._config.get("model_pxtrs"):
      attrs.update(self.extract_dynamic_params(param["model"]))
    attrs.update(self.extract_dynamic_params(self._config.get("city_id")))
    attrs.update(self.extract_dynamic_params(self._config.get("time_window_seconds")))
    attrs.update(self.extract_dynamic_params(self._config.get("sampling_ratio")))
    attrs.update(self.extract_dynamic_params(self._config.get("top_ratio")))
    attrs.update(self.extract_dynamic_params(self._config.get("min_item_count")))

    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for param in self._config.get("model_pxtrs"):
      attrs.add(param["pxtr"])
    return attrs
