#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafMixer


class LocalLifeLaneMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_lane_mix"
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.update(self._config.get('copy_item_attrs', []))
    return attrs
