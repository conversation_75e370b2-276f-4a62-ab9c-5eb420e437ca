#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg
from ...common_leaf_processor import LeafRetriever

class L2LiveAid2LiveIdRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "l2_live_aid_2_live_id"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return { self._config["aid_list_attr"] }
