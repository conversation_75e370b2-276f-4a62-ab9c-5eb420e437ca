#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types, check_arg, ArgumentError, \
    gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class LocalLifeBucketLimitArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_limit_by_bucket"
  
  @strict_types
  def _check_config(self) -> None:
     if "enable_left_picker_fill_all" in self._config:
       check_arg("total_item_limit" in self._config, "enable_left_picker_fill_all 时， 必须配置 total_item_limit")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for bucket in self._config.get('buckets'):
      if 'limit' in bucket.keys():
        attrs.update(self.extract_dynamic_params(bucket['limit']))
      if 'ratio' in bucket.keys():
        attrs.update(self.extract_dynamic_params(bucket['ratio']))
      if 'diversity' in bucket.keys():
        attrs.update(self.extract_dynamic_params(bucket['diversity']['attr_name']))
        attrs.update(self.extract_dynamic_params(bucket['diversity']['window_size']))
        attrs.update(self.extract_dynamic_params(bucket['diversity']['max_count']))
    if 'left_picker_skip_selected_item' in self._config:
      attrs.update(self.extract_dynamic_params(self._config['left_picker_skip_selected_item']))
    if 'total_item_limit' in self._config:
      attrs.update(self.extract_dynamic_params(self._config['total_item_limit']))
    if 'enable_left_picker_fill_all' in self._config:
      attrs.add(self._config['enable_left_picker_fill_all'])
    if 'fix_sort_attr' in self._config:
      attrs.add(self._config['fix_sort_attr'])
    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for bucket in self._config.get('buckets'):
      attrs.add(bucket['attr_name'])
      if 'sort' in bucket.keys():
        attrs.add(bucket['sort']['attr_name'])
    for attr in self._config.get('diversity_attrs'):
      attrs.add(attr)
    return attrs

class LocalLifeRetrievalFilterArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_retrieval_filter_arranger"
  
  @strict_types
  def _check_config(self) -> None:
    if self._config.get("debug_mode", False):
      if "export_item_attr" not in self._config:
        raise ArgumentError(" debug_mode 为 true ，但未配置 export_item_attr ")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "filters" in self._config:
      for filter in self._config.get("filters"):
        attrs.update(self.extract_dynamic_params(filter.get("enable")))
        for key, value in filter.items():
          if key!= "enable" and key!= "name":
            attrs.add(value)
    if "truncation_map" in self._config:
      for num in self._config.get("truncation_map").values():
        attrs.update(self.extract_dynamic_params(num))

    return attrs
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for attr in self._config.get("item_attr_map").values():
      attrs.add(attr)

    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("export_item_attr"))
    return attrs

class LocalLifeRetrievalQuotaMergeArranger(LeafArranger):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_retrieval_quota_merge_arranger"

  @strict_types
  def _check_config(self) -> None:
    required_attrs = ['min_limit_num', 'max_limit_num', 'total_limit_num', 'dedup_reason_attr_name', 'retrieval_weight']
    for attr_name in required_attrs:
      if attr_name not in self._config:
        raise ArgumentError(f"{attr_name} 必须配置！保存去重 reason list!")
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    dynamic_attrs = ['min_limit_num', 'max_limit_num', 'total_limit_num']
    for attr_name in dynamic_attrs:
      attrs.update(self.extract_dynamic_params(self._config[attr_name]))
      
    if "retrieval_weight" in self._config:
      for reason in self._config["retrieval_weight"]:
        if isinstance(self._config["retrieval_weight"][reason], str):
          attrs.update(self.extract_dynamic_params(self._config["retrieval_weight"][reason]))
    return attrs
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("dedup_reason_attr_name"))
    return attrs

class LocalLifeOptimizeIntermixArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "local_life_optimize_intermix"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("mix_pattern")))
    attrs.update(self.extract_dynamic_params(self._config.get("num_limit")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    mix_on_attr = self._config.get("mix_on_attr")
    return { mix_on_attr } if mix_on_attr else set()