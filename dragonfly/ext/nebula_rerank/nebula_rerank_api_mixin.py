#!/usr/bin/env python3
# coding=utf-8
"""
filename: nebula_rerank_api_mixin.py
description:
author: <EMAIL>
date: 2021-12-16 18:08:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .nebula_rerank_enricher import *

class NebulaRerankApiMixin(CommonLeafBaseMixin):
  """
  该 Mixin 包含 nebula_rerank 相关的 Processor 接口
  该模块提供的 processor 接口主要用于极速版发现页重排
  """

  def rerank_sphinx_rl_param_predict(self, **kwargs):
    """
    RerankSphinxRlEnricher
    重排 ensemble sort 序列召回队列超参强化学习寻参
    
    """
    self._add_processor(RerankSphinxRlEnricher(kwargs))
    return self

  def rerank_cal_history_preference(self, **kwargs):
    """
    GenPersonalActionPreferScoreEnricher
    重排根据 colossus 计算用户历史行为偏好
    
    """
    self._add_processor(GenPersonalActionPreferScoreEnricher(kwargs))
    return self
  
  def rerank_cal_personal_duration_preference(self, **kwargs):
    """
    GenPersonalDurationPreferScoreEnricher
    重排根据 colossus 计算用户历史行为 wtd label  暂用于数据流
    
    """
    self._add_processor(GenPersonalDurationPreferScoreEnricher(kwargs))
    return self
  
  def extract_diversity_history_attr(self, **kwargs) :
    """
    ExtractDiversityHistoryAttrEnricher
    重排数据流历史多样性特征抽取
  
    """
    self._add_processor(ExtractDiversityHistoryAttrEnricher(kwargs))
    return self