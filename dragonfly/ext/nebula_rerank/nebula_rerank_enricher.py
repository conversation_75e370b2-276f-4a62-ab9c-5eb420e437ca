#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafEnricher

class RerankSphinxRlEnricher(LeafEnricher):
  """
  极速版发现页重排 ensemble sort 序列召回超参搜索
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rerank_sphinx_rl_param_predict"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for name in ["session_id"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["rl_params_name_attr", "rl_params_value_attr"]:
      if name in self._config:
        attrs.add(self._config.get(name))
    return attrs

class GenPersonalActionPreferScoreEnricher(LeafEnricher):
  """
  重排根据 colossus 计算用户历史行为偏好
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rerank_cal_history_preference"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    colossus_config = self._config.get("colossus_config", {})
    colossus_resp = colossus_config.get("colossus_resp_attr")
    if colossus_resp:
      attrs.add(colossus_resp)
    for key in ["enable_filter_nebula_history", "colossus_stat_days_thrs", "action_cnt_bias", "play_time_cnt_bias"]:
      attrs.update(self.extract_dynamic_params(colossus_config.get(key)))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["user_play_time_prefer_score", "user_like_prefer_score","user_follow_prefer_score","user_forward_prefer_score","user_comment_prefer_score","user_enter_profile_prefer_score"]:
      attrs.add(name)
    return attrs
  
class ExtractDiversityHistoryAttrEnricher(LeafEnricher):
  """
  重排数据流抽取历史多样性特征
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "extract_diversity_history_attr"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    colossus_resp = self._config.get("colossus_resp_attr")
    if colossus_resp:
      attrs.add(colossus_resp)
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for name in ["history_bucket_show_cnt", "history_bucket_valid_cnt", "history_bucket_valid_ratio", "history_bucket_entropy", "history_bucket_hetu1_cnt"]:
      attrs.add(name)
    bucket_num = self._config.get("time_bucket_num", 4)
    for name in ["top_hetu1_tag_list_bucket", "top_hetu1_ratio_list_bucket", "percent50_hetu1_cnt_bucket", "percent75_hetu1_cnt_bucket"]:
      for j in range(bucket_num):
        attrs.add(name + str(j))
    for name in ["diversity_label_valid_cnt", "diversity_label_stats_cnt", "diversity_treatment_tag"]:
      attrs.add(name)
    return attrs


class GenPersonalDurationPreferScoreEnricher(LeafEnricher):
  """
  重排根据 colossus 计算用户历史行为 wtd label  暂用于数据流
  """
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rerank_cal_personal_duration_preference"
  
  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    colossus_resp = self._config.get("colossus_resp_attr", "")
    if colossus_resp:
      attrs.add(colossus_resp)
    return attrs


  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return {"duration", "playtime"}
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    quantile_num = self._config.get("output_quantile_num", 3)
    output_prefix = self._config.get("output_wtd_attr", "personal_wtd_quantile")
    for i in range(quantile_num):
      attrs.add(output_prefix + str(i))
    return attrs
