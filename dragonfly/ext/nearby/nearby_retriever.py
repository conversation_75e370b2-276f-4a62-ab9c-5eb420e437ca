#!/usr/bin/env python3
# coding=utf-8

from ...common_leaf_util import strict_types
from ...common_leaf_processor import LeafRetriever


class NearbyUserCFRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_nearby_usercf"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["kess_service"])
    attrs.add(self._config["user_id_attr"])
    attrs.add(self._config["device_id_attr"])
    attrs.add(self._config["user_lat_attr"])
    attrs.add(self._config["user_lon_attr"])
    optional_attrs = [
      "friend_list_save_attr", 
      "contact_list_save_attr",
      "contact_reverse_list_save_attr",
      "qq_list_attr_save",
      "photo_u2u_list_save_attr",
      "wechat_list_save_attr"]
    for name in optional_attrs:
      if name in self._config:
        attrs.add(self._config[name])
    dynamic_params = ["service_shard",
                      "timeout_ms",
                      "photo_visible_city_is_on",
                      "photo_visible_province_is_on",
                      "retrieve_param_kconf_path"]
    for name in dynamic_params:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class NearbyRecoU2URetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_nearby_u2u_anchors"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["kconf_feature_key", "retrieval_item_type", "reason", "photo_u2u_list", "max_author_cnt"]:
      if key in self._config:
        attrs.add(self._config[key])
    for name in ["kess_service", "timeout_ms"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))    
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True


class NearbySlideI2IRedisRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_nearby_slide_redis"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["cluster_name", "reason", "slide_source_reason", "retrieval_item_type", "cache_name",
                "item_separator", "key_from_attr", "timeout_ms", "cache_bits", "cache_delay_delete_ms", "cache_expire_second"]:
      if key in self._config:
        attrs.add(self._config[key])
    for name in ["slide_retrieve_num", "retrieve_num", "max_valid_triger_num", "item_per_retr_count", "max_src_action_list_num", "key_prefix"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))    
    return attrs

class NearbyRecoEmbI2IRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_nearby_reco_emb_i2i"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True
  
class NearbyListSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_list_sample_retriever"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["start_index", "reason", "retrieval_item_type"]:
      if key in self._config:
        attrs.add(self._config[key])
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True  

class NearbySlideListSampleRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_slide_list_sample_retriever"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True 
  