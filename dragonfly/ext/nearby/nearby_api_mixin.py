#!/usr/bin/env python3
# coding=utf-8

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .nearby_enricher import *
from .nearby_retriever import *
from .nearby_arranger import *

"""
filename: nearby_api_mixin.py
description: 同城业务相关的API实现，包含同城业务特定的地理位置计算逻辑，以及同城特定的召回模块
author: <EMAIL>
date: 2021-11-03 12:00:00
"""

class NearbyApiMixin(CommonLeafBaseMixin):
  """
  同城 Processor API 接口的 Mixin 实现
  
  背景：相关API主要用于同城页业务

  负责人：<EMAIL> 
        tangjian@calc_location_code_attr
  """

  def calc_location_attr(self, **kwargs):
    """
    CalcLocationPoiAttrEnricher
    ------
    计算经、纬度 + IP到RegionId,ProvinceId,CityId的映射, [实验中] 如果有content_city_id 会用content_city_id 对应的province_id 经纬度替换输出的 output_item_province_id_attr, output_item_city_id_attr, 并覆盖 item_lat_attr, item_lon_attr

    参数
    ------
    `common_lat_attr` : [double] 纬度

    `common_lon_attr` : [double] 经度

    `common_ip_region_attr` : [string] IP

    `output_common_region_id_attr` : [int] region_id

    `output_common_province_id_attr` : [int] province_id

    `output_common_city_id_attr` : [int] city_id

    `target_item_reason` : 

    `item_lat_attr` : [double] item 纬度

    `item_lon_attr` : [double] item 经度

    `item_ip_region_attr` : [string] item IP

    `output_item_region_id_attr` : [int] item region_id

    `output_item_province_id_attr` : [int] item province_id

    `output_item_city_id_attr` : [int] item city_id

    示例1. 仅计算Common Location Attr
    ------
    ``` python
    .calc_location_attr(common_lat_attr="user_lat",
                        common_lon_attr="user_lon",
                        common_ip_region_attr="user_ip",
                        output_common_region_id_attr="user_region_id",
                        output_common_province_id_attr="user_province_id",
                        output_common_city_id_attr="user_city_id")

    ```
    示例2. 仅计算Item Location Attr
    ------
    ``` python
    .calc_location_attr(item_lat_attr="item_lat",
                        item_lon_attr="item_lon",
                        item_ip_region_attr="item_ip",
                        output_item_region_id_attr="item_region_id",
                        output_item_province_id_attr="item_province_id",
                        output_item_city_id_attr="item_city_id",
                        target_item_reason = 86)

    ```
    示例3. 计算Common & Item Location Attr
    ------
    ``` python
    .calc_location_attr(common_lat_attr="user_lat",
                        common_lon_attr="user_lon",
                        common_ip_region_attr="user_ip",
                        output_common_region_id_attr="user_region_id",
                        output_common_province_id_attr="user_province_id",
                        output_common_city_id_attr="user_city_id",
                        item_lat_attr="item_lat",
                        item_lon_attr="item_lon",
                        item_ip_region_attr="item_ip",
                        output_item_region_id_attr="item_region_id",
                        output_item_province_id_attr="item_province_id",
                        output_item_city_id_attr="item_city_id",
                        target_item_reason = 86)

    ```
    """
    self._add_processor(CalcLocationAttrEnricher(kwargs))
    return self
  def nearby_dpp_arrange(self, **kwargs):
    self._add_processor(NearbyDppArranger(kwargs))
    return self


  def nearby_ranki2i(self, **kwargs):
    """
    NearbyRankI2IArranger
    ------
    对召回后的结果进行打分: score = similarity x emp_xtr

    参数配置
    ------
    `无`

    调用示例
    ------
    ``` python
    .nearby_ranki2i()
    ```
    """
    self._add_processor(NearbyRankI2IArranger(kwargs))
    return self

  def nearby_truncate_by_duration(self, **kwargs):
    """
    NearbyTruncateByDurationArranger
    ------
    依据duration分桶截断

    参数配置
    ------
    `size_limit`: [int] 最终保留个数
    `duration_bucket_seg`: [string] 分桶的阈值
    `duration_bucket_cut_ratio`: [string] 每个桶保留的个数占最终保留个数的比例
    调用示例
    ------
    ``` python
    .nearby_truncate_by_duration(
      size_limit = 600,
      duration_bucket_seg = "3000:7000:9000:12000:17000:20000:58000:90000:120000:180000:300000:420000:600000",
      duration_bucket_cut_ratio = "0.0:0.03:0.0:0.0:0.0:0.03:0.03:0.13:0.13:0.1:0.08:0.08:0.05:0.01"
    )
    ```
    """
    self._add_processor(NearbyTruncateByDurationArranger(kwargs))
    return self

  def nearby_diversity_rerank(self, **kwargs):
    """
    NearbyDiversityRerankArranger
    ------
    对指定的 N 个 attr 进行多样性打散

    参数配置
    ------
    `channels`: [list] 需要用于打散的队列配置，每个队列包含以下三个配置项：
      - `name`: [string] 打散 attribute 名，同名 int 类型 item_attr 获取值
      - `alpha`: [double] [动态参数] 打散参数 alpha
      - `beta`: [beta] [动态参数] 打散参数 beta

    `default_value`: [int] 选配项，若 item 不存在指定的 item_attr 则使用该默认值，默认值为 0

    调用示例
    ------
    ``` python
    .nearby_diversity_rerank(
      channels = [
        { "name": "hetu_id", "alpha": "{{hetu_id_beta}}", "beta": "{{hetu_id_beta}}" },
        { "name": "gender", "alpha": "{{gender_beta}}", "beta": "{{gender_beta}}" },
      ],
      default_value = 0,
    )
    ```
    """
    self._add_processor(NearbyDiversityRerankArranger(kwargs))
    return self

  def nearby_slide_diversity_rerank(self, **kwargs):
    """
    NearbySlideDiversityRerankArranger
    ------
    同城内流多样性打散

    参数配置
    ------
    `diversity_max_num`: [int] 选配项，不存在指定则使用该默认值，默认值为 400
    `theta`: [double] 选配项，不存在指定则使用该默认值，默认值为 0.1
    `epsilon`: [double] 选配项，不存在指定则使用该默认值，默认值为 0.000001
    `enable_allow_empty_emb`: [int] 选配项，不存在指定则使用该默认值，默认值为 0
    `score_type_name`: [string]] 选配项，不存在指定则使用该默认值，默认值为 "final_ensemble_score_boost"
    `list_size`: [int] 选配项，不存在指定则使用该默认值，默认值为 50
    `enable_skip_origin_diversity` [int] 选配项，不存在指定则使用该默认值，默认值为 1

    调用示例
    ------
    ``` python
    .nearby_slide_diversity_rerank(
      diversity_max_num = 400,
      theta = 0.1,
      epsilon = 0.000001,
      enable_allow_empty_emb = 0,
      list_size = 50,
      enable_skip_origin_diversity = 1,
    )
    ```
    """
    self._add_processor(NearbySlideDiversityRerankArranger(kwargs))
    return self
  
  def nearby_mix_decay_calc_score(self, **kwargs):
    """
    NearbyMixDecayCalcScoreArranger
    ------
    同城内流多样性打散

    参数配置
    ------
    `photo_theta`: [double] 选配项，不存在指定则使用该默认值，默认值为 0.0
    `live_theta`: [double] 选配项，不存在指定则使用该默认值，默认值为 0.0
    `photo_weight`: [double] 选配项，不存在指定则使用该默认值，默认值为 1.0
    `live_weight`: [double] 选配项，不存在指定则使用该默认值，默认值为 1.0
    `score_type`: [string] 选配项，不存在指定则使用该默认值，默认值为 "ensemble_shift_score"
    `result_type`: [string] 选配项，不存在指定则使用该默认值，默认值为 "shift_decay_score"

    调用示例
    ------
    ``` python
    .nearby_mix_decay_calc_score(
      photo_theta = 0.0,
      live_theta = 0.0,
      photo_weight = 1.0,
      live_weight = 1.0,
      score_type = "ensemble_shift_score",
      result_type = "shift_decay_score"
    )
    ```
    """
    self._add_processor(NearbyMixDecayCalcScoreArranger(kwargs))
    return self


  def nearby_calc_ensemble_score(self, **kwargs):
    """
    NearbyEnsembleScoreEnricher
    ------
    对指定的 N 个队列计算用于 ensemble sort 的综合分，计算方式:

    每个 item 在各个队列 channels[i] 的单项分公式: `Si = func(formula_version)`

    每个 item 最后输出到 `output_attr` 的综合分数值为: `S1 + S2 +...+ Sn` (`n` 为 channels 个数)

    注意：该方法只生成 ensemble score, 如需根据该综合分进行排序, 请在之后自行调用 `.sort(score_from_attr="${output_attr}")`

    参数配置
    ------
    `formula_version`: [int] 选配项，使用哪个版本的单项分公式计算，默认为 0 (初版 ensemble 公式)
      - 0: `Si = channels[i].weight / (SEQ_ON_ATTR(channels[i].name) + smooth)`
      - 1: `乘法公式`
      - 2: `Proportion Sort`
      - 3: `channel Sort`
      - 4: `Normalization Sort`
      - 5: `新乘法公式`

    `channels`: [list] 需要用于 ensemble sort 的队列配置，每个队列包含以下三个配置项：
      - `name`: [string] 队列名，将从同名 double/int 类型 item_attr 获取值进行排序计算
      - `weight`: [double] [动态参数] 该队列的权重
      - `norm_prefix` : [string] 用于取 common attr 中各 xtr 均值和标准差的固定前缀, 仅在 formula_version = 4 的时候需要配置 

    `smooth`: [double] [动态参数] ensemble sort 各队列的平滑因子

    `output_attr`: [string] 将最后计算出的 ensemble 综合分存入指定的 item_attr

    `default_value`: [double] 选配项，若 item 不存在指定的 item_attr 则使用该默认值参与排序，默认值为 0

    `channel_sort_limit` : [int] 只在 channel sort 的时候生效, 指定进下一轮排序的个数

    `score_molecule` : [double] 最终排序结果计算分数的分子, 用于后续的 boost 逻辑

    `score_denominator` : [double] 最终排序结果计算分数的分母, 用于后续的 boost 逻辑


    调用示例
    ------
    ``` python
    .nearby_calc_ensemble_score(
      channels = [
        { "name": "pctr", "weight": "{{w_pctr}}" },
        { "name": "pltr", "weight": "{{w_pltr}}" },
        { "name": "pftr", "weight": "{{w_pftr}}" },
      ],
      smooth = "{{ensemble_smooth}}",
      output_attr = "ensemble_score",
    )
    ```
    """
    self._add_processor(NearbyEnsembleScoreEnricher(kwargs))
    return self

  def calc_location_code_attr(self, **kwargs):
    """
    CalcLocationCodeAttrEnricher
    ------
    计算经、纬度 到adcode的映射

    参数
    ------
    `common_lat_attr` : [double] 纬度

    `common_lon_attr` : [double] 经度

    `output_common_adcode_attr` : [int] adcode

    `item_lat_attr` : [double] 纬度

    `item_lon_attr` : [double] 经度

    `output_item_adcode_attr` : [int] adcode

    示例1. 仅计算Common Location Attr
    ------
    ``` python
    .calc_location_code_attr(common_lat_attr="user_lat",
                        common_lon_attr="user_lon",
                        output_common_adcode_attr="location_adcode")

    ```
    示例2. 仅计算 Item Location Attr
    ------
    ``` python
    .calc_location_code_attr(item_lat_attr="item_lat",
                        item_lon_attr="item_lon",
                        output_item_adcode_attr="location_adcode")

    ```
    """
    self._add_processor(CalcLocationCodeAttrEnricher(kwargs))
    return self

  def enrich_map_distribute_attr(self, **kwargs):
    """
    NearbyMapDistributeAttrEnricher
    ------
    输入: "lat_unit_count", "lon_unit_count",
    输出: item_mask
    ------
    ``` python
    .enrich_map_distribute_attr(lat_unit_count="lat_cnt",
                    lon_unit_count="lon_cnt", item_mask="item_should_keep")
    ```
    """
    self._add_processor(NearbyMapDistributeAttrEnricher(kwargs))
    return self

  def smember_common_attr_from_redis(self, **kwargs):
    """
    SMemberFromRedisAttrEnricher
    ------
    输入: "redis_name", "redis_key_prefix", "key_value"
    输出: output_value_list_attr, output_raw_str_list_attr
    ------
    ``` python
    .smember_common_attr_from_redis(redis_name="aa", 
                    redis_key_prefix="xx", key_value="uid",
                    output_value_list_attr="redis_value",
                    output_raw_str_list_attr="raw_redis_value")
    ```
    """

    self._add_processor(SMemberFromRedisAttrEnricher(kwargs))
    return self

  def enrich_nearby_filter_attr(self, **kwargs):
    """
    NearbyFilterAttrEnricher
    ------
    输出 item 过滤相关的 attr 

    参数
    ------
    目前没有
    输出 item attrs
    is_punished : item 是否被 punish
    ------
    ``` python
    .enrich_nearby_filter_attr()

    ```
    """
    self._add_processor(NearbyFilterAttrEnricher(kwargs))
    return self

  def enrich_nearby_boost_coeff_attr(self, **kwargs):
    """
    NearbyGenBoostCoeffEnricher
    ------
    
    参数
    ------
    输入
    enable:是否生效
    attr_name:匹配的item_attr_name
    match_value:命中value值（具体数值或common_attr_name)
    match_type:value值的类型
    match_boost_coeff:命中value时的boost系数

    输出 
    general_boost_coeff:item的最终boost系数
    ------
    ``` python
    .enrich_nearby_boost_coeff_attr()

    ```
    """
    self._add_processor(NearbyGenBoostCoeffEnricher(kwargs))
    return self

  def arranger_nearby_flow_regulation(self, **kwargs):
    """
    NearbyFlowRegulationArranger
    ------
    同城流量调控通用模块

    参数
    ------
    目前没有
    ------
    ``` python
    .arranger_nearby_relation_reason()

    ```
    """
    self._add_processor(NearbyFlowRegulationArranger(kwargs))
    return self

  def arranger_nearby_relation_reason(self, **kwargs):
    """
    NearbyRelationReasonArranger
    ------
    设置关系链相关召回的 reason

    参数
    ------
    目前没有
    ------
    ``` python
    .arranger_nearby_relation_reason()

    ```
    """
    self._add_processor(NearbyRelationReasonArranger(kwargs))
    return self
  
  def enrich_nearby_slide_rerank_score(self, **kwargs):
    """
    NearbySlideRerankScoreEnricher
    ------
    输出 内流 rerank 相关的 attr 

    参数
    `enrich_live_realtime_perfer` : [int] 计算实时直播偏好
    `enrich_similar_top_score` : [int] 计算相似置顶分
    ------
    输出 item attrs
    各种处理过的 mc_pxtr
    ------
    ``` python
    .enrich_nearby_slide_rerank_score()

    ```
    """
    self._add_processor(NearbySlideRerankScoreEnricher(kwargs))
    return self

  def enrich_nearby_waterfall_rerank_score(self, **kwargs):
    """
    NearbyWaterfallRerankScoreEnricher
    ------
    输出 内流 rerank 相关的 attr 

    参数
    `enrich_live_realtime_perfer` : [int] 计算实时直播偏好
    `enrich_enrich_variant_attr` : [int] 计算打散参数
    `enrich_enrich_auto_play_score` : [int] 计算实时自动播放分
    `enrich_fr_boost_coeff`  : [int] 计算 boost 分
    ------
    输出 item attrs
    各种处理过的用于外流的 rerank 参数
    ------
    ``` python
    .enrich_nearby_waterfall_rerank_score()

    ```
    """
    self._add_processor(NearbyWaterfallRerankScoreEnricher(kwargs))
    return self

  def enrich_nearby_mc_personal_variant_sort(self, **kwargs):
    """
    NearbyMcPersonalVariantSortEnricher
    ------

    参数
    ------
    目前没有
    输出 item attrs
    各种处理过的 粗排分
    ------
    ``` python
    .enrich_nearby_mc_personal_variant_sort()

    ```
    """
    self._add_processor(NearbyMcPersonalVariantSortEnricher(kwargs))
    return self

  def enrich_nearby_distance_filter_attr(self, **kwargs):
    """
    NearbyDistanceFilterAttrEnricher
    ------

    参数
    ------
    目前没有
    同城按距离过滤的代码
    ------
    ``` python
    .enrich_nearby_distance_filter_attr()

    ```
    """
    self._add_processor(NearbyDistanceFilterAttrEnricher(kwargs))
    return self

  def enrich_nearby_mc_sort_attr(self, **kwargs):
    """
    NearbyMcSortEnricher
    ------

    参数
    ------
    `enable_hetu_prefer` : [int] 是否有河图偏好分计算 
    `enable_photo_boost_coeff` : [int] 是否计算 photo boost 分
    `enable_live_boost_coeff` : [int] 是否计算 live boost 分
    `enable_main_extra_attr`   : [int] 是否计算 main_extra_attr
    输出 item attrs
    各种处理过的 mc_pxtr
    ------
    ``` python
    .enrich_nearby_mc_sort_attr()

    ```
    """
    self._add_processor(NearbyMcSortEnricher(kwargs))
    return self

  def nearby_perf_index_count_enricher(self, **kwargs):
    """
    NearbyPerfIndexCountEnricher
    ------

    参数
    ------
    `perf_top_k_num` : [int] 默认 10
    `index_count_subtag` : [string] 区分场景
    ------
    ``` python
    .nearby_perf_index_count_enricher(
      perf_top_k_num=10,
      index_count_subtag="nearby_main_index_count"
    )
    ```
    """
    self._add_processor(NearbyPerfIndexCountEnricher(kwargs))
    return self
  
  def enrich_nearby_full_rank_attr(self, **kwargs):
    """
    NearbyFullRankEnricher
    ------

    参数
    ------
    输出 item attrs
    各种处理过的 full_rank_attr
    ------
    ``` python
    .enrich_nearby_full_rank_attr()

    ```
    """
    self._add_processor(NearbyFullRankEnricher(kwargs))
    return self

  def enrich_nearby_tag_attr(self, **kwargs):
    """
    NearbyTagAttrEnricher
    ------
    处理 tag 相关的 item attr 

    参数
    use_hetu_tag_v1 : 是否用 v1 河图
    ------
    目前没有
    输出 item attrs
    hetu_level_one : 河图一级类目
    hetu_level_two  : 河图二级类目
    is_tag_invalid : 是否满足 tag 相似要求
    is_relation_ok : 是否关系链相似
    ------
    ``` python
    .enrich_nearby_tag_attr()

    ```
    """
    self._add_processor(NearbyTagAttrEnricher(kwargs))
    return self
  def enrich_nearby_init_common_attr(self, **kwargs):
    """
    NearbyInitCommonAttrEnricher
    ------
    处理用户相关的 common attr 

    参数
    ------
    目前没有
    输出 common attrs
    whole_ks_action_photo_ids : 大盘的长期行为 list
    sim_nearby_action_photo_ids  : 同城的长期行为 list
    ------
    ``` python
    .enrich_nearby_init_common_attr()

    ```
    """
    self._add_processor(NearbyInitCommonAttrEnricher(kwargs))
    return self
  
  def enrich_nearby_trigger(self, **kwargs):
    """
    NearbyTriggerEnricher
    ------
    填充 用户trigger
    参数
    ------
    similar_weight : [double]
    distance_weight  : [double]
    time_weight  : [double]
    infer_num  : [int] infer数量，默认50
    enable_distance  : [int] 是否计算距离
    ------
    ``` python
    .enrich_nearby_trigger(
      similar_weight=1.0,
      distance_weight=1.0,
      time_weight=1.0,
      infer_num=50,
      enable_distance=0,
    )
    ```
    """
    self._add_processor(NearbyTriggerEnricher(kwargs))
    return self

  def calc_item_count_attr(self, **kwargs):
    """
    CalcItemCountAttrEnricher
    -------
    基于dynamic索引解析Item的若干计数类属性：nearby_show,nearby_click,nearby_realshow,nearby_like,nearby_follow,...
    """
    self._add_processor(CalcItemCountAttrEnricher(kwargs))
    return self

  def nearby_gen_uescore(self, **kwargs):
    """
    NearbyGenUescoreEnricher
    -------
    计算同城的uescore
    """
    self._add_processor(NearbyGenUescoreEnricher(kwargs))
    return self

  def nearby_list_sample_retriever(self, **kwargs):
    """
    NearbyListSampleRetriever
    ------------
    同城 list sample 召回
    参数
    `reason` : [int] 召回的 reason
    `retrieval_item_type` : [int] 召回的类型
    ------

    示例
    ------
    ``` python
    .nearby_list_sample_retriever(
      reason = EXP_TAG.RETRIEVAL_SOURCE_NEARBY_SWING_PHOTO_I2I,
      retrieval_item_type = RECO_ENUM_ITEM_TYPE.ITEM_TYPE_LIST,
    )
    ```
    """
    self._add_processor(NearbyListSampleRetriever(kwargs))
    return self

  def nearby_slide_list_sample_retriever(self, **kwargs):
    """
    NearbySlideListSampleRetriever
    ------------
    同城 list sample 召回
    参数
    `reason` : [int] 召回的 reason
    `retrieval_item_type` : [int] 召回的类型
    ------

    示例
    ------
    ``` python
    .nearby_slide_list_sample_retriever(
      reason = EXP_TAG.RETRIEVAL_SOURCE_NEARBY_SWING_PHOTO_I2I,
      retrieval_item_type = RECO_ENUM_ITEM_TYPE.ITEM_TYPE_LIST,
    )
    ```
    """
    self._add_processor(NearbySlideListSampleRetriever(kwargs))
    return self
  
  def retrieve_by_nearby_reco_emb_i2i(self, **kwargs):
    """
    NearbyRecoEmbI2IRetriever
    ------------
    同城 reco emb i2i 召回
    参数

    ------

    示例
    ------
    ``` python
    .retrieve_by_nearby_reco_emb_i2i()
    ```
    """
    self._add_processor(NearbyRecoEmbI2IRetriever(kwargs))
    return self

  def retrieve_by_nearby_slide_redis(self, **kwargs):
    """
    NearbySlideI2IRedisRetriever
    ------------
    同城 reco slide redis i2i 召回
    参数
    `reason` : [int] 召回的 reason
    `retrieve_num` : [int] 普通召回的 数量
    `slide_retrieve_num` : [int] slide source 召回的 数量
    `cluster_name` : [string] redis cluster name
    `key_from_attr` : [string] action list 的 common attr
    `key_prefix` : [string] redis key 前缀
    `timeout_ms`: [int] 超时时间
    `item_separator`: [string] redis value 分隔符
    `slide_source_reason` : [int] slide source 召回的 reason
    `retrieval_item_type` : [int] 召回的类型
    ------

    示例
    ------
    ``` python
    .retrieve_by_nearby_slide_redis(
      reason = EXP_TAG.RETRIEVAL_SOURCE_NEARBY_SWING_PHOTO_I2I,
      retrieve_num = 2000,
      slide_retrieve_num = 500,
      cluster_name = "recoDataminingNearby",
      key_from_attr = "SWING_PHOTO_I2I_ACTION_LIST",
      key_prefix = "dm_swing_i2i-",
      timeout_ms = 100,
      item_separator = ",",
      slide_source_reason = 129,
      retrieval_item_type = RECO_ENUM_ITEM_TYPE.ITEM_TYPE_PHOTO,
    )
    ```
    """
    self._add_processor(NearbySlideI2IRedisRetriever(kwargs))
    return self

  def retrieve_by_nearby_usercf(self, **kwargs):
    """
    NearbyUserCFRetriever
    ------------
    同城UserCF召回
    参数
    ------
    `kess_service` : [double] service kess name

    `user_id_attr` : [double] user id common attr name

    `device_id_attr` : [double] device id common attr name

    `user_lat_attr` : [string] user latitude common attr name

    `user_lon_attr` : [int] user longitude common attr name

    示例
    ------
    ``` python
    .retrieve_by_nearby_usercf(kess_service="grpc_nearbyUserCFRetrievalServer",
                               user_id_attr="user_id",
                               device_id_attr="device_id",
                               user_lat_attr="user_lat",
                               user_lon_attr="user_lon")
    ```
    """
    self._add_processor(NearbyUserCFRetriever(kwargs))
    return self
  

  def calc_nearby_mix_score_by_local_model(self, **kwargs):
    """
    NearbyLocalModelMixScoreEnricher
    ------------
    同城直播 & 视频混排分计算
    参数
    ------
    `model_path` : [string] gbdt model path in kconf

    `inference_thread_num` : [int] model inference 并发数

    `mix_score_save_attr` : [string] mix score save attr

    示例
    ------
    ``` python
    .calc_nearby_mix_score_by_local_model(model_path="grpc_nearbyUserCFRetrievalServer",
                                          inference_thread_num=2,
                                          device_id_attr="device_id",
                                          mix_score_save_attr="gbdt_mix_score")
    ```
    """
    self._add_processor(NearbyLocalModelMixScoreEnricher(kwargs))
    return self

  def get_common_attr_by_sample_list_by_limit(self, **kwargs):
    """
    NearbySampleListCommonAttrEnricher
    ------
    从 SampleList 服务中获取用户特征（SampleAttr 格式）作为 CommonAttr 内容填入 Context 中, Nearby 相关升级, 可以传参限制 action list 的长度

    参数配置
    ------
    `kess_service`: [string] [动态参数] SampleList 服务的 kess 服务名

    `service_group`: [string] SampleList 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] SampleList 服务的超时时间，允许的设置范围为 [150, 400]，默认值 200

    `include_attrs`: [list] 可选项，显式指定需要的 attr，为空或者不设置则包含所有返回的 attr。

    `attr_config`: [string] 业务token用来动态配置需要请求的attr

    `limit_num`: [int] 返回 action list 的长度

    `no_overwrite`: [bool] 仅当 attr 不存在时写入，默认为 false。

    `save_attr_names_to_attr`: [string] (**更建议使用 include_attrs 配置项显式写明属性列表！**) 把从样本拼接服务中获取的 UserAttr 名称列表作为值，save_attr_names_to_attr 值作为 AttrName 存入 CommonAttr 中，以方便后续 Processor 可以知道 CommonAttr 中哪些 Attr 是从样本拼接服务获取的。为空或不设置将不会存储 UserAttr 的名称列表。

    调用示例
    ------
    ``` python
    .get_common_attr_by_sample_list(
      kess_service = "grpc_xxx",
      limit_num = 50,
      include_attrs = ["aaa", "bbb"]
      attr_config = "Live"
    )
    ```
    """
    self._add_processor(NearbySampleListCommonAttrEnricher(kwargs))
    return self

  def retrieve_by_nearby_u2u_anchors(self, **kwargs):
    """
    NearbyRecoU2URetriever
    ------------
    同城 reco u2u 召回, 返回的是作者id ！！！
    参数 
    ------
    `kess_service`          : [string] service kess name
    `timeout_ms`            : [int] time out
    `kconf_feature_key`     : [string] feature names 
    `retrieval_item_type`   : [int] item type
    `reason`                : [int] reason
    `photo_u2u_list`        : [string] u2u_list variable name

    示例
    ------
    ``` python
    .retrieve_by_nearby_u2u_anchors(kess_service = "",
                                    timeout_ms = "",
                                    kconf_feature_key = "",
                                    retrieval_item_type="",
                                    reason = "")
    ```
    """
    self._add_processor(NearbyRecoU2URetriever(kwargs))
    return self
  
  def get_nearby_fr_xtr_norm_value_from_redis(self, **kwargs):
    """
    GetNearbyFRXtrNormValueFromRedisEnricher
    ------
    
    从redis中读取同城精排xtr的均值和方差,用于归一化。读取出来的值将会以规定的格式存储在common_attr中,便于后面的使用
    
    common_attr内存储的格式规定示例: { "name" : "nb_mix_pm_ctr_mean", "value" : 0.0}
    
    各下划线分割区域所代表的含义：
    
      1.nb_mix为固定前缀
      
      2.p代表photo,l代表live ; m代表主站外流, s代表主站内流, n代表极速版 
    
      2.xtr的名称
    
      3.mean为均值, std为标准差
    
    参数配置
    ------
    输入: 
      "redis_name"       :    redis集群的名称
      "xtr_list_key"     :    待读取的xtr名称
      "redis_prefix_key" :    redis的前缀: nb_mix为统一前缀; p代表photo,l代表live ; m代表主站外流, s代表主站内流, n代表极速版
    
    输出: 
      "output_redis_status" : 用于判断读取是否成功
    
    ------
    ``` python
    .get_nearby_fr_xtr_norm_value_from_redis(
      redis_name="recoXcxDataTriger", 
      xtr_list_key=["ctr", "wtr", "vtr", "lvtr", "ltr", "cmtr", "gtr"], 
      redis_prefix_key=["nb_mix_pm","nb_mix_ps", "nb_mix_pn", "nb_mix_lm", "nb_mix_ls", "nb_mix_ln"],
      output_redis_status="nearby_fr_xtr_norm_redis_status"
    )
    ```
    """

    self._add_processor(GetNearbyFRXtrNormValueFromRedisEnricher(kwargs))
    return self
  

  
  def get_nearby_user_retain_info(self, **kwargs):
    """
    GetNearbyUserRetainInfoEnricher
    ------
    
    请求模型读取用户的行为权重。读取出来的值将会以规定的格式存储在common_attr中,便于后面的使用
    
    common_attr内存储的格式规定示例: { "name" : "mr_user_reatin_mp_click_weight", "value" : 0.0}
    
    各下划线分割区域所代表的含义：
    
      1.mr_user_reatin为固定前缀
      
      2.m代表主站外流, s代表主站内流, n代表极速版; p代表photo,l代表live;
    
      3.行为的名称, click/follow/like/comment
    
    参数配置
    ------
    输入:
    
    输出: 
      "output_model_status" : 用于判断读取是否成功
    
    ------
    ``` python
    .get_nearby_user_retain_info(
      output_model_status="nearby_user_retain_info_status"
    )
    ```
    """

    self._add_processor(GetNearbyUserRetainInfoEnricher(kwargs))
    return self
  
  def calc_item_xtr_norm_shift(self, **kwargs):
    """
    CalcItemXtrNormShiftEnricher
    ------
    
    将获取到的xtr进行白化处理
    
    参数配置
    ------
    输入: 
      "product_prefix"      :   产品前缀
      "smooth_threshold"    :   置信区间
      "stat_xtr_list"       :   统计的xtr
      "input_xtr_list"      :   输入的xtr
      "output_xtr_list"     :   输出的xtr
    
    输出: 
      "status_str" : 用于判断转换是否成功
    
    ------
    ``` python
    .calc_item_xtr_norm_shift(
      product_prefix="nb_mix_ps",
      smooth_threshold=2,
      stat_xtr_list=["ctr", "wtr", "lvtr", "ltr", "cmtr"],
      input_xtr_list=["fullrank_ori_ctr", "fullrank_ori_wtr", "fullrank_ori_lvtr", "fullrank_ori_ltr", "fullrank_ori_cmtr"],
      output_xtr_list=["shift_ctr", "shift_wtr", "shift_lvtr", "shift_ltr", "shift_cmtr"],
      status_str="xtr_norm_enrich_status"
    )
    ```
    """

    self._add_processor(CalcItemXtrNormShiftEnricher(kwargs))
    return self
  
  def enrich_get_nearby_inner_boost_coeff(self, **kwargs):
    """
    GetNearbyInnerBoostCoeffEnricher
    ------

    主站内流各种 boost 集合，返回 fr_boost_coeff
    
    参数配置
    ------
    
    输出: 
      "fr_boost_coeff" : item boost 系数
    
    ------
    ``` python
    .enrich_get_nearby_inner_boost_coeff()
    ```
    """

    self._add_processor(GetNearbyInnerBoostCoeffEnricher(kwargs))
    return self

  def nearby_item_lat_lon_new_enricher(self, **kwargs):
    """
    NearbyItemLatLonNewEnricher
    ------
    
    在老经纬度字段不存在的情况下使用新经纬度字段替代
    
    参数配置
    ------
    输入: 
    `item_lat_attr` : [double] 索引中的经度字段名称

    `item_lon_attr` : [double] 索引中的纬度字段名称

    `item_lat_new_attr` : [double] 索引中的新经度字段名称

    `item_lon_new_attr` : [double] 索引中的新纬度字段名称
    
    输出: 
    `out_item_lat_attr` : [string] 经度输出字段名称

    `out_item_lon_attr` : [string] 纬度输出字段名称
    
    ------
    ``` python
    .nearby_item_lat_lon_new_enricher(
      item_lat_attr="item_info.lat", 
      item_lon_attr="item_info.lat", 
      item_lat_new_attr="item_info.lat_new",
      item_lon_new_attr="item_info.lon_new",
      out_item_lat_attr="item_lat",
      out_item_lon_attr="item_lon"
    )
    ```
    """

    self._add_processor(NearbyItemLatLonNewEnricher(kwargs))
    return self

  def nearby_retrieval_filter(self, **kwargs):
    """
    NearbyRetrievalFilterArranger
    ------
    
    同城集中过滤算子，适配 flat_index 行过滤
    
    参数配置
    ------
    输入:

    `truncate_map` : [json] 传入分 reason 截断数

    `filters` : [json] 传入过滤算子的相关配置
    
    ------
    ``` python
    .nearby_retrieval_filter(
      truncate_map = {
        86: 1200,
        144: 500,
      }
      filters = [
        {
          "name": "not_in_index",
          "enable": True,
          "input_item_attrs": [
            "item_type",
            "item_dynamic_info.is_merchant_live_new"
          ],
          "input_common_attrs": [
            "photo_hetu_tag_retrieval_is_on"
          ]
        }
      ]
    )
    ```
    """

    self._add_processor(NearbyRetrievalFilterArranger(kwargs))
    return self

  def nearby_fr_sample_join(self, **kwargs):
    """
    NearbyFRSampleJoinEnricher
    ------
    同城精排阶段采样
    参数配置：
    ------
    输入:
    'kafka_topic' : [string] 样本发送的目标 kafka
    ------
    ``` python
    .nearby_fr_sample_join(
      kafka_topic = 'xxx'
    )
    ```
    """
    self._add_processor(NearbyRecoStageSampleJoinEnricher(kwargs))
    return self

  def nearby_mc_rerank(self, **kwargs):
    """
    NearbyMcRerankArranger
    ------
    粗排重排
    ------
    """
    self._add_processor(NearbyMcRerankArranger(kwargs))
    return self


  def calc_high_publish_label(self, **kwargs):
    """
    CalcHighPublishLabelEnricher
    ------
    获取publishLabel
    ------
    """
    self._add_processor(CalcHighPublishLabelEnricher(kwargs))
    return self
