#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, ArgumentError, check_arg, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafArranger

class NearbyDppArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_dpp_arrange"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("list_item_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("candidate_limit_size")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_dynamic_alpha")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_circle_distance")))
    attrs.update(self.extract_dynamic_params(self._config.get("enable_dist_similarity")))
    attrs.update(self.extract_dynamic_params(self._config.get("circle_dist_max")))
    attrs.update(self.extract_dynamic_params(self._config.get("alpha")))
    attrs.update(self.extract_dynamic_params(self._config.get("hetu_cluster_alpha")))
    attrs.update(self.extract_dynamic_params(self._config.get("mmu_emb_alpha")))
    attrs.update(self.extract_dynamic_params(self._config.get("dist_alpha")))
    attrs.update(self.extract_dynamic_params(self._config.get("nearby_user_active_level")))
    attrs.update(self.extract_dynamic_params(self._config.get("dpp_param_theta")))
    attrs.update(self.extract_dynamic_params(self._config.get("dpp_param_bias")))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("embedding_attr_name"))
    attrs.add(self._config.get("score_attr_name"))
    return attrs

class NearbyDiversityRerankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_diversity_rerank"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["default_value", "enable_same_city_rerank"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    for channel in self._config["channels"]:
      for key in ["beta", "alpha"]:
        attrs.update(self.extract_dynamic_params(channel.get(key)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return { x["name"] for x in self._config["channels"] }
class NearbySlideDiversityRerankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_slide_diversity_rerank"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["diversity_max_num", "theta", "epsilon", "enable_allow_empty_emb",
        "score_type_name", "list_size", "enable_skip_origin_diversity"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class NearbyMixDecayCalcScoreArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_mix_decay_calc_score"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["photo_theta", "live_theta", "photo_weight", "live_weight", "score_type", "result_type"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs

class NearbyFlowRegulationArranger(LeafArranger):
  def __init__(self, config:dict):
    super().__init__(config)
    self._input_common_attrs = set()
    self._input_item_attrs = set()
    if self._config["rules"]:
      for rule in self._config["rules"]:
        self.extract_config_tree(rule)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "arranger_nearby_flow_regulation"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._input_item_attrs

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._input_common_attrs

  def extract_config_tree(self, config):
    attrs = []
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("weight")))
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("remit_attr_name")))
    self._input_common_attrs.update(self.extract_dynamic_params(config.get("remit_topN")))

    return attrs

class NearbyRetrievalFilterArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_retrieval_filter_arranger"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if "filters" in self._config:
      for filter in self._config["filters"]:
        if "input_common_attrs" in filter:
          for common_attr_name in filter["input_common_attrs"]:
            attrs.add(common_attr_name)
    else:
      raise ArgumentError("nearby_retrieval_filter 中至少添加一路 filter")
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "filters" in self._config:
      for filter in self._config["filters"]:
        if "input_item_attrs" in filter:
          for item_attr_name in filter["input_item_attrs"]:
            attrs.add(item_attr_name)
    else:
      raise ArgumentError("nearby_retrieval_filter 中至少添加一路 filter")
    return attrs

class NearbyRankI2IArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_ranki2i"

class NearbyMcRerankArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_mc_rerank"

class NearbyTruncateByDurationArranger(LeafArranger):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "nearby_truncate_by_duration"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    for key in ["size_limit", "duration_bucket_seg", "duration_bucket_cut_ratio"]:
      attrs.update(self.extract_dynamic_params(self._config.get(key)))
    return attrs
