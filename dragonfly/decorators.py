#!/usr/bin/env python3
# coding=utf-8
"""
- filename: decorators.py
- description: useful dragonfly decorators
- author: fang<PERSON>an<PERSON>@kuaishou.com
- date: 2021-01-14 12:54:00
"""
import functools
import copy
from typing import Optional
from .common_leaf_dsl import LeafFlow, current_flow, has_current_flow
from .common_leaf_util import gen_dict_hash_hexstr

def apply(**d_kwargs):
  """
  自动为其范围内的所有 processor 添加若干公共配置项
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      assert flow is not None, '被 @apply 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      start = len(flow._processors)
      res = function(*args, **kwargs)
      for i in range(start, len(flow._processors)):
        for k, v in d_kwargs.items():
          if k not in flow._processors[i]._config:
            if k == "item_table":
              flow._processors[i].item_table = v
            flow._processors[i]._config[k] = v
      return res
    return wrapper
  return decorator

def stage(name: str = ''):
  """
  标记一个逻辑阶段，自动监控阶段耗时及 item 数目，可配参数：

  `name`: [str] 阶段名称，若缺省将使用被装饰的函数名作为阶段名称
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      assert flow is not None, '被 @stage 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      res = function(*args, **kwargs)
      flow.mark_stage_end(stage_name=name or function.__name__)
      return res
    return wrapper
  return decorator

def namespace(ns: str):
  """
  开启一个命名空间，自动为其范围内的所有 processor 添加一个统一的名称前缀，可嵌套使用。

  `ns`: [string] 命名空间的名称
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      assert ns, '@namespace 的 ns 参数不能为空'
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      assert flow is not None, '被 @namespace 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      flow.namespace_(ns=ns, nest=True)
      res = function(*args, **kwargs)
      flow.namespace_()
      return res
    return wrapper
  return decorator

def if_(expr: str, name: Optional[str] = None):
  """
  把被装饰函数内的所有方法调用包入一个 if 分支。

  `expr`: [string] 一个合法的 Lua `bool` 类型表达式，表达式中可直接引用 common_attr
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      assert flow is not None, '被 @if_ 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      if expr:
        flow.if_(expr=expr, name=name or f"_branch_controller_{flow.name}_{function.__name__}_{gen_dict_hash_hexstr(kwargs, 6)}")
        res = function(*args, **kwargs)
        flow.end_()
        return res
      else:
        return function(*args, **kwargs)
    return wrapper
  return decorator

def __invoke_sub_flow(wrap_func, wrap_func_args: tuple, wrap_func_kwargs: dict, main_flow, sub_flow_func: str, sub_flow_args: dict, timeout_ms: int = -1, shared: bool = False, inherit_ns = False):
  subflow_name = wrap_func_kwargs.get("name") or sub_flow_args.get("name") or wrap_func.__name__
  item_table = sub_flow_args.get("item_table")
  loop_if = sub_flow_args.pop("loop_if", "")
  loop_limit = sub_flow_args.pop("loop_limit", 0)
  loop_on = sub_flow_args.pop("loop_on", "")
  loop_index = sub_flow_args.pop("loop_index", "")
  loop_value = sub_flow_args.pop("loop_value", "")
  if shared:
    sub_flow = type(main_flow)(name=None, parent_flow=main_flow, item_table=item_table, loop_if=loop_if, loop_limit=loop_limit, loop_on=loop_on, loop_index=loop_index, loop_value=loop_value)
    sub_flow._auto_name(prefix=subflow_name)
  else:
    sub_flow = type(main_flow)(name=f"{main_flow.name}.{subflow_name}", parent_flow=main_flow, item_table=item_table, loop_if=loop_if, loop_limit=loop_limit, loop_on=loop_on, loop_index=loop_index, loop_value=loop_value)
  if inherit_ns:
    for ns in main_flow.current_namespace:
      sub_flow.namespace_(ns, True)
  for k, v in main_flow.__dict__.items():
    if k not in sub_flow.__dict__:
      setattr(sub_flow, k, copy.deepcopy(v))
  if not has_current_flow():
    # 替换 wrap_func_args 中的 flow 为 sub_flow，这样可以保留 self 参数，兼容类成员函数
    args_with_sub_flow = tuple(sub_flow if isinstance(arg, type(main_flow)) else arg for arg in wrap_func_args)
    res = wrap_func(*args_with_sub_flow, **wrap_func_kwargs)
  else:
    with sub_flow:
      res = wrap_func(*wrap_func_args, **wrap_func_kwargs)
  cfg = dict(sub_flow=sub_flow, **sub_flow_args)
  if timeout_ms > 0:
    cfg['timeout_ms'] = timeout_ms
  getattr(main_flow, sub_flow_func)(**cfg)
  return main_flow if res is sub_flow else res

def parallel(partition_size = 0, group_by: str = "", shared: bool = False, **d_kwargs):
  """
  对当前结果集中的 item 进行分片后，并行执行被装饰方法中的逻辑（底层基于 arrange_by_sub_flow 实现，每个分片交由一个 sub_flow 处理），可配参数：

  `partition_size`: [int] 并行分片大小

  `shared`: [int] 选配项，是否共用同配置的 flow 实例，默认为 False
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @parallel 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @parallel 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      assert partition_size or group_by, '@parallel() 必须配置 partition_size 或 group_by'
      if partition_size:
        assert not group_by, '@parallel() 的 partition_size 和 group_by 不能同时配置'
        d_kwargs["expected_partition_size"] = partition_size
      if group_by:
        assert not partition_size, '@parallel() 的 partition_size 和 group_by 不能同时配置'
        d_kwargs["group_by"] = group_by
      return __invoke_sub_flow(function, args, kwargs, flow, "arrange_by_sub_flow", d_kwargs, -1, shared)
    return wrapper
  return decorator

def async_enrich(timeout_ms: int = -1, shared: bool = False, inherit_ns : bool = False, **d_kwargs):
  """
  对被装饰方法中的逻辑用 enrich_by_sub_flow 进行包装，进行并行的异步处理，可配参数：

  `timeout_ms`: [int] 超时时间，单位毫秒，默认 -1，表示不设置超时

  `shared`: [bool] 选配项，是否共用同配置的 flow 实例，默认为 False

  `inherit_ns`: [bool] 选配项，是否继承 flow 的 namespace 配置，默认为 False
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @async_enrich 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @async_enrich 所装饰的方法必须有且仅有一个 LeafFlow 类型的无名参数'
      return __invoke_sub_flow(function, args, kwargs, flow, "enrich_by_sub_flow", d_kwargs, timeout_ms, shared, inherit_ns)
    return wrapper
  return decorator

def async_retrieve(timeout_ms: int = -1, shared: bool = False, inherit_ns : bool = False, **d_kwargs):
  """
  对被装饰方法中的逻辑用 retrieve_by_sub_flow 进行包装，进行并行的异步处理，可配参数：

  `timeout_ms`: [int] 超时时间，单位毫秒，默认 -1，表示不设置超时

  `shared`: [bool] 选配项，是否共用同配置的 flow 实例，默认为 False

  `inherit_ns`: [bool] 选配项，是否继承 flow 的 namespace 配置，默认为 False
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @async_retrieve 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @async_retrieve 所装饰的方法必须有且仅有一个 LeafFlow 类型的无名参数'
      return __invoke_sub_flow(function, args, kwargs, flow, "retrieve_by_sub_flow", d_kwargs, timeout_ms, shared, inherit_ns)
    return wrapper
  return decorator

def async_mix(timeout_ms: int = -1, shared: bool = False, **d_kwargs):
  """
  对被装饰方法中的逻辑用 mix_by_sub_flow 进行包装，进行并行的异步处理，可配参数：

  `timeout_ms`: [int] 超时时间，单位毫秒，默认 -1，表示不设置超时

  `shared`: [int] 选配项，是否共用同配置的 flow 实例，默认为 False
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @async_mix 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @async_mix 所装饰的方法必须有且仅有一个 LeafFlow 类型的无名参数'
      return __invoke_sub_flow(function, args, kwargs, flow, "mix_by_sub_flow", d_kwargs, timeout_ms, shared)
    return wrapper
  return decorator

def async_observe(shared: bool = False, **d_kwargs):
  """
  对被装饰方法中的逻辑用 observe_by_sub_flow 进行包装，进行并行的异步处理，可配参数：

  `shared`: [int] 选配项，是否共用同配置的 flow 实例，默认为 False
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @async_observe 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @async_observe 所装饰的方法必须有且仅有一个 LeafFlow 类型的无名参数'
      return __invoke_sub_flow(function, args, kwargs, flow, "observe_by_sub_flow", d_kwargs, -1, shared)
    return wrapper
  return decorator

def for_loop(loop_on: str = "", loop_index: str = "", loop_value: str = "",
             loop_if: str = "", loop_limit: int = 0, **d_kwargs):
  """
  对被装饰方法中的逻辑用 enrich_by_sub_flow + loop 配置进行包装，进行同步的循环处理，可配参数：

  模式一（按 list 循环）：

  `loop_on`: [str] 必配项，用于循环的 Common Attr 名称，目前仅支持 int_list | double_list | string_list 类型数据

  `loop_value`: [str] 必配项，当前循环指向元素的 Common Attr 名称，具体类型与 `loop_on` 相关，例如 `loop_on` 为 `int_list` 时，`loop_value` 为 `int` 类型

  `loop_index`: [str] 选配项，存储当前循环指向元素的 index 的 int 类型 Common Attr 名称，index 从 0 开始依次递增

  `loop_limit`: [int] 选配项，限制最多循环多少次，默认为 0 不限制

  模式二（do-while 循环）：

  `loop_if`: [str] 必配项，指定一个 Common Attr 的名称作为循环退出信号，类型为 int，flow 每轮循环执行完后会检测该 attr 值, 当为 0 或不存在时，循环结束执行，否则继续循环。备注：通常由 flow 内部改变 `loop_if` 的值来控制提前退出循环。

  `loop_limit`: [int] 必配项，限制最多循环多少次，必须设置一个大于 0 的值
  """
  def decorator(function):
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      flow = next(filter(lambda x: isinstance(x, LeafFlow), args), None)
      if flow and has_current_flow():
        assert '被 @for_loop 所装饰的方法可以通过 current_flow() 直接获取 flow，不需要传入 flow 参数'
      flow = flow or current_flow()
      assert flow is not None, '被 @for_loop 所装饰的方法参数中必须包含至少一个 LeafFlow 对象'
      assert loop_on or loop_if, '@for_loop() 必须配置 loop_on 或 loop_if'
      if loop_if:
        d_kwargs["loop_if"] = loop_if
      if loop_limit:
        d_kwargs["loop_limit"] = loop_limit
      if loop_on:
        d_kwargs["loop_on"] = loop_on
      if loop_index:
        d_kwargs["loop_index"] = loop_index
      if loop_value:
        d_kwargs["loop_value"] = loop_value
      return __invoke_sub_flow(function, args, kwargs, flow, "enrich_by_sub_flow", d_kwargs, -1, False)
    return wrapper
  return decorator

