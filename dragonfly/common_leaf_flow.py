#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_flow.py
description: common_leaf dynamic_json_config DSL intelligent builder, LeafFlowCore
author: <EMAIL>
date: 2020-01-19 16:45:00
"""

import inspect
import os
from typing import Optional
from .common_leaf_util import check_arg, strict_types, ArgumentError, LogicError, extract_attrs_from_expr, \
    gen_if_branch_control_attr, gen_else_branch_control_attr, gen_else_if_branch_control_attr, \
    gen_switch_branch_control_attr, cast_to_lua_variable_str, find_processor, gen_str_hash_hexstr, \
    gen_dict_hash_hexstr, try_add_table_name, RaiseError, BLAME_ERROR_CODE
from .common_leaf_processor import LeafProcessor, LeafRetriever, LeafEnricher, get_all_input_common_attrs, \
    get_all_output_common_attrs, get_all_input_item_attrs, get_all_output_item_attrs
from .ext.common_leaf_base_mixin import CommonLeafBaseMixin
from .ext.common.common_leaf_enricher import CommonRecoRemoteIndexItemAttrEnricher, CommonRecoLuaAttrEnricher

class BranchState:
  NONE = 0
  IF = 1
  ELSE = 2
  ELSE_IF = 3
  SWITCH = 4
  CASE = 5
  DEFAULT = 6

  def __init__(self) -> None:
    self.__current_state = BranchState.NONE
    self.__branch_control_attr = ""
    self.__exclusive_control_attrs = []
    self.__start_processor = ""

  @property
  def current_state(self):
    return self.__current_state

  @strict_types
  def get_branch_control_attr(self) -> str:
    """ 获取分支判断条件的 common_attr 名称和表达式值 """
    return self.__branch_control_attr

  @strict_types
  def get_branch_control_expr(self) -> str:
    """ 获取分支判断条件的表达式 """
    if self.__current_state == BranchState.NONE:
      raise LogicError(f"非法的分支状态: {self.__current_state}")
    if self.__current_state == BranchState.SWITCH:
      raise LogicError("switch_ 与第一个 case_ 之间不能插入其它步骤")
    return self.__current_expr

  @strict_types
  def start_if(self, if_expr: str) -> None:
    if self.__current_state != BranchState.NONE:
      raise LogicError("非法的 if_ 分支起始")
    self.__current_state = BranchState.IF
    self.__if_expr = if_expr
    self.__current_expr = if_expr
    self.__branch_control_attr = gen_if_branch_control_attr()
    self.__exclusive_control_attrs.append(self.__branch_control_attr)

  @strict_types
  def start_else_if(self, if_expr: str) -> None:
    if self.__current_state not in (BranchState.IF, BranchState.ELSE_IF):
      raise LogicError("else_if_ 必须在 if_ 或 else_if_ 分支下使用")
    self.__current_state = BranchState.ELSE_IF
    precondition = " and ".join(f"{attr} == 1" for attr in self.__exclusive_control_attrs)
    self.__current_expr = f"{precondition} and ({if_expr})"
    self.__if_expr = "else_" + if_expr
    self.__branch_control_attr = gen_else_if_branch_control_attr()
    self.__exclusive_control_attrs.append(self.__branch_control_attr)

  @strict_types
  def start_else(self) -> None:
    if self.__current_state not in (BranchState.IF, BranchState.ELSE_IF):
      raise LogicError("else_ 必须在 if_ 分支下使用")
    self.__current_state = BranchState.ELSE
    self.__branch_control_attr = gen_else_branch_control_attr()
    self.__current_expr = " and ".join(f"{attr} == 1" for attr in self.__exclusive_control_attrs)

  @strict_types
  def end_if(self) -> None:
    if self.__current_state not in (BranchState.IF, BranchState.ELSE, BranchState.ELSE_IF):
      raise LogicError("end_if_ 必须在 if_ 分支下成对使用")
    self.__current_state = BranchState.NONE
    self.__current_expr = ""
    self.__branch_control_attr = ""
    self.__exclusive_control_attrs.clear()

  @strict_types
  def start_switch(self, switch_expr: str) -> None:
    if self.__current_state != BranchState.NONE:
      raise LogicError("非法的 switch_ 分支起始")
    self.__current_state = BranchState.SWITCH
    self.__switch_expr = switch_expr

  @strict_types
  def start_case(self, *cases) -> None:
    if self.__current_state not in (BranchState.SWITCH, BranchState.CASE):
      raise LogicError("case_ 必须在 switch_ 分支内使用")
    self.__current_state = BranchState.CASE
    self.__branch_control_attr = gen_switch_branch_control_attr()
    self.__exclusive_control_attrs.append(self.__branch_control_attr)
    self.__current_expr = " or ".join(f"({self.__switch_expr}) == {cast_to_lua_variable_str(case)}" for case in cases)

  @strict_types
  def start_default(self) -> None:
    if self.__current_state in (BranchState.IF, BranchState.ELSE):
      raise LogicError("default_ 不能在 if_ 分支中使用")
    if self.__current_state != BranchState.CASE:
      raise LogicError("default_ 必须在 case_ 之后使用，且一个 switch_ 分支中只能定义一次")
    self.__current_state = BranchState.DEFAULT
    self.__branch_control_attr = gen_switch_branch_control_attr()
    self.__current_expr = " and ".join(f"{attr} == 1" for attr in self.__exclusive_control_attrs)

  @strict_types
  def end_switch(self) -> None:
    if self.__current_state not in (BranchState.CASE, BranchState.DEFAULT):
      raise LogicError("end_switch_ 必须在 case_ 之后使用，且需要与 switch_ 配对")
    self.__current_state = BranchState.NONE
    self.__current_expr = ""
    self.__branch_control_attr = ""
    self.__exclusive_control_attrs.clear()

  @property
  @strict_types
  def start_processor(self) -> str:
    return self.__start_processor

  @start_processor.setter
  def start_processor(self, processor: str) -> None:
    self.__start_processor = processor

  @property
  @strict_types
  def if_expr(self) -> str:
    return self.__if_expr

  @property
  @strict_types
  def switch_expr(self) -> str:
    return self.__switch_expr

  @property
  @strict_types
  def case_expr(self) -> str:
    return self.__current_expr

class LeafFlowStateMachine:
  """
  LeafFlow 分支控制状态机
  """
  def __init__(self):
    self.__branch_states: list[BranchState] = []

  @property
  def current_branch_state(self) -> int:
    return self.__branch_states[-1].current_state if self.__branch_states else BranchState.NONE

  @strict_types
  def get_current_branch_control_attr(self) -> str:
    """ 获取当前用于控制是否 skip 的 common_attr 名称 """
    if not self.__branch_states or self.current_branch_state in (BranchState.NONE, BranchState.SWITCH):
      return ""
    return self.__branch_states[-1].get_branch_control_attr()

  @strict_types
  def get_current_branch_control_condition(self) -> str:
    """
    获取当前分支用于生成 LuaEnricher 的条件表达式（如果存在）
    """
    assert len(self.__branch_states) > 0
    assert self.current_branch_state not in (BranchState.NONE, BranchState.SWITCH)
    expr = self.__branch_states[-1].get_branch_control_expr()
    if len(self.__branch_states) == 1:
      return expr
    # NOTE(fangjianbing): 对于嵌套的分支情况, 需要同时满足父分支条件的 control_attr 等于 0 (即不 skip)
    control_attr = self.__branch_states[-2].get_branch_control_attr()
    assert control_attr, "嵌套分支中的 control_attr 不能为空"
    return f"{control_attr} == 0 and ({expr})"

  @strict_types
  def get_current_branch_control_condition_hash(self) -> str:
    """
    获取当前分支用于生成 LuaEnricher 的条件表达式的 hash 值
    """
    if not self.__branch_states or self.current_branch_state in (BranchState.NONE, BranchState.SWITCH):
      return ""
    exprs = ""
    for branch_state in self.__branch_states:
      if branch_state.current_state in [BranchState.IF, BranchState.ELSE_IF]:
        exprs += branch_state.if_expr
      if branch_state.current_state == BranchState.ELSE:
        exprs += branch_state.if_expr + "_else"
      elif branch_state.current_state == BranchState.CASE:
        exprs += branch_state.case_expr
      elif branch_state.current_state == BranchState.DEFAULT:
        exprs += branch_state.switch_expr + "_default"
    return gen_str_hash_hexstr(exprs, 8)

  @strict_types
  def start_if_branch(self, expr: str) -> None:
    """ 开启 if 分支 """
    if self.__branch_states and self.current_branch_state == BranchState.SWITCH:
      raise LogicError("if_ 无法在 switch_ 之后调用")
    self.__branch_states.append(BranchState())
    self.__branch_states[-1].start_if(expr)

  @strict_types
  def start_else_branch(self) -> None:
    """ 开启 else 分支 """
    if not self.__branch_states:
      raise LogicError("else_ 必须在 if_ 分支下使用")
    self.__branch_states[-1].start_else()

  @strict_types
  def start_else_if_branch(self, expr: str) -> None:
    """ 开启 else if 分支 """
    if not self.__branch_states:
      raise LogicError("else_ 必须在 if_ 分支下使用")
    self.__branch_states[-1].start_else_if(expr)

  @strict_types
  def end_if_branch(self) -> None:
    """ 结束 if 分支 """
    if not self.__branch_states:
      raise LogicError("end_if_ 必须在 if_ 分支下使用")
    self.__branch_states[-1].end_if()
    self.__branch_states.pop()

  @strict_types
  def start_switch_branch(self, switch_expr: str) -> None:
    """ 开启 switch 分支 """
    if self.__branch_states and self.current_branch_state == BranchState.SWITCH:
      raise LogicError("switch_ 不能被连续调用")
    self.__branch_states.append(BranchState())
    self.__branch_states[-1].start_switch(switch_expr)

  @strict_types
  def start_case_branch(self, *cases) -> None:
    """ 开启 case 分支 """
    if not self.__branch_states:
      raise LogicError("case_ 必须在 switch_ 分支下使用")
    self.__branch_states[-1].start_case(*cases)

  @strict_types
  def start_default_branch(self) -> None:
    """ 开启 default 分支 """
    if not self.__branch_states:
      raise LogicError("default_ 必须在 switch_ 分支下使用")
    self.__branch_states[-1].start_default()

  @strict_types
  def end_switch_branch(self) -> None:
    """ 结束 switch 分支 """
    if not self.__branch_states:
      raise LogicError("end_switch_ 必须在 switch_ 分支下使用")
    self.__branch_states[-1].end_switch()
    self.__branch_states.pop()

  @property
  @strict_types
  def start_processor(self) -> str:
    return self.__branch_states[-1].start_processor

  @start_processor.setter
  def start_processor(self, processor: str) -> None:
    self.__branch_states[-1].start_processor = processor

class LeafFlowMetaClass(type):
  """ LeafFlow Meta Class """
  __existing_properties = {}

  def __new__(cls, clsname, superclasses, attributedict):
    if superclasses:
      if not issubclass(superclasses[0], LeafFlowCore):
        raise ArgumentError(f"{clsname} 的第一个继承类 {superclasses[0].__name__} 必须为 LeafFlow 或其子类")
      for super_cls in superclasses[1:]:
        if issubclass(super_cls, LeafFlowCore):
          raise ArgumentError(f"{clsname} 不能继承多个 LeafFlow: {superclasses[0].__name__}, {super_cls.__name__}")
        if not issubclass(super_cls, CommonLeafBaseMixin):
          raise ArgumentError(f"{clsname} 加载了非法的 Mixin 扩展: {super_cls.__name__}, 其必须继承自 CommonLeafBaseMixin")

    identity = f"{attributedict['__module__']}.{attributedict['__qualname__']}"
    for k, _ in attributedict.items():
      # 下划线开头的方法认为是业务方自管理内部接口, Dragonfly 不对这类方法做冲突检查
      if not k.startswith("_"):
        if k in cls.__existing_properties and identity != cls.__existing_properties[k]:
          raise ArgumentError(f"LeafFlow 扩展类 {identity} 与 {cls.__existing_properties[k]} 存在名称冲突的成员定义: {k}, 继承链路: {[clsname] + [c.__name__ for c in superclasses]}")
        else:
          cls.__existing_properties[k] = identity

    return type.__new__(cls, clsname, superclasses, attributedict)

class LeafFlowCore(metaclass=LeafFlowMetaClass):
  """
  LeafFlow 结构的内部核心, 本身只包含框架逻辑, 不包含对业务使用方的可调用 API, 只用于 API Mixin Class 的混入
  """

  _ENABLE_PROCESSOR_STABLE_NAME = False

  # 存储所有已注册的 processor 实例
  __registered_processors = {}
  # 以 config_hash 为倒排 key 存储已注册的 processor 实例
  __config_hash_to_processors = {}
  __conflict_branch_name = {}

  @strict_types
  def __init__(self, name: Optional[str], abtest_biz_name: str = "", abtest_biz_seq_num: int = -1, \
               optimize_processor_order: bool = False, parent_flow: Optional['LeafFlowCore'] = None, \
               loop_if: str = "", loop_limit: int = 0, loop_on: str = "", loop_index: str = "", loop_value: str = "", \
               item_table: Optional[str] = None):
    """ 初始化 LeafFlow """
    self.__name = ""
    self.__auto_name_prefix = ""
    self.__is_name_sealed = False
    self.__level = -1
    self.__abtest_biz_name = abtest_biz_name
    self.__abtest_biz_seq_num = abtest_biz_seq_num
    self.__current_namespace = []
    self.__processors = []
    self.__import_common_attrs = set()
    self.__export_common_attrs = set()
    self.__import_item_attrs = set()
    self.__export_item_attrs = set()
    self.__has_auto_injected_item_attrs = False
    self.__input_item_attrs_before_auto_inject = set()
    self.__output_item_attrs_after_auto_inject = set()
    self.__has_auto_adjust_injected = False
    self.__has_auto_injected_additional_local_item_attr = False
    self.__has_downstream_auto_detected = False
    self.__branch_state_machine = LeafFlowStateMachine()
    self.__optimize_processor_order = optimize_processor_order
    self.__branch_blame_map = {}
    self.__loop_control_attr = loop_if
    self.__max_loop_count = loop_limit
    self.__loop_on = loop_on
    self.__loop_index = loop_index
    self.__loop_value = loop_value
    self.__item_table = item_table or ""
    self.__post_response = False

    if name is not None:
      self.name = name

  @property
  @strict_types
  def item_table(self) -> str:
    return self.__item_table

  @property
  @strict_types
  def post_response(self) -> bool:
    return self.__post_response

  @post_response.setter
  @strict_types
  def post_response(self, val: bool):
    """ 设置 LeafFlow 是否在返回 response 之后运行 """
    self.__post_response = val

  @classmethod
  @strict_types
  def _get_processor_definitions(cls) -> dict:
    """
    内部接口，业务方请勿调用！
    ------
    返回 Processor json 结构定义
    """
    return { name: processor.definition for name, processor in cls.__registered_processors.items() }

  @strict_types
  def __register_processor(self, processor: LeafProcessor) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    注册 Processor
    """
    assert processor.name
    LeafFlowCore.__registered_processors[processor.name] = processor
    if processor.config_hash not in LeafFlowCore.__config_hash_to_processors:
      LeafFlowCore.__config_hash_to_processors[processor.config_hash] = []
    LeafFlowCore.__config_hash_to_processors[processor.config_hash].append(processor)

  @strict_types
  def __register_named_processor(self, processor: LeafProcessor) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    注册具名 Processor
    """
    assert processor.name
    if processor.name not in LeafFlowCore.__registered_processors:
      self.__register_processor(processor)
    elif processor.definition != LeafFlowCore.__registered_processors[processor.name].definition:
      RaiseError(processor.name, processor.code_line, f"存在同名但不同配置的 processor: {processor.name}\n{processor.definition}\n------\n{LeafFlowCore.__registered_processors[processor.name].definition}")

  @strict_types
  def __register_anonymous_processor(self, processor: LeafProcessor) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    注册匿名 Processor
    """
    assert not processor.name

    if processor.is_async() and processor.downstream and not processor.downstream.raw_name:
      self.__register_anonymous_processor(processor.downstream)

    for proc in LeafFlowCore.__config_hash_to_processors.get(processor.config_hash, []):
      # 如果有配置完全相同的 processor 且命名空间也相同则直接重用
      if proc.namespace == processor.namespace and proc.definition == processor.definition:
        processor.raw_name = proc.raw_name
        return
    processor.autogen_processor_name()
    if processor.name in LeafFlowCore.__registered_processors:
      RaiseError(processor.name, processor.code_line, "\n".join([
          f"generated conflict processor name: {processor.name}, config:",
          str(processor.definition),
          "--- vs ---",
          str(LeafFlowCore.__registered_processors[processor.name].definition),
          "💡 See possible solution here: https://kstack.corp.kuaishou.com/question/3340"
        ]))
    self.__register_processor(processor)

  @strict_types
  def _register_all_processors(self) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    注册 Processor json 配置
    """
    # 检查算子合法性
    for processor in self.__processors:
      processor.check_config()
    # 先注册具名同步 processor
    for processor in filter(lambda x: x.raw_name and not x.is_async(), self.__processors):
      self.__register_named_processor(processor)
    # 再逆序注册具名异步 processor, 以保证 downstream processor 会先注册上 name
    for processor in filter(lambda x: x.raw_name and x.is_async(), reversed(self.__processors)):
      # 如果其 downstream processor 还不具名, 则先对 downstream 进行注册
      if processor.downstream and not processor.downstream.raw_name:
        self.__register_anonymous_processor(processor.downstream)
      self.__register_named_processor(processor)
    # 再逆序注册无名的 processor, 以保证 downstream processor 会先注册上 name
    for processor in filter(lambda x: not x.raw_name, reversed(self.__processors)):
      self.__register_anonymous_processor(processor)
    self.__seal_name()


  @strict_types
  def _auto_name(self, prefix: str = ""):
    """
    内部接口，业务方请勿调用！
    ------
    设置 flow name 自动命名的前缀
    """
    if self.__name:
      raise LogicError(f"{self.__class__.__name__} 的 name 已经被设置为 {self.__name}, 不能再设置自动命名前缀")
    self.__auto_name_prefix = prefix

  def __seal_name(self):
    """
    内部接口，业务方请勿调用！
    ------
    封印最终的 flow name
    """
    if not self.__name and self.__auto_name_prefix:
      if self._ENABLE_PROCESSOR_STABLE_NAME:
        self.name = self.__auto_name_prefix
      else:
        self.name = f"{self.__auto_name_prefix}_{gen_dict_hash_hexstr(self._get_pipeline_config(), 6)}"
    self.__is_name_sealed = True

  @property
  @strict_types
  def name(self) -> str:
    """ 返回 LeafFlow 的名称 """
    if not self.__name and self.__is_name_sealed:
      raise ArgumentError(f"{self.__class__.__name__} 的 name 不可为空")
    return self.__name

  @name.setter
  @strict_types
  def name(self, value: str):
    if self.__name or self.__is_name_sealed:
      raise LogicError(f"{self.__class__.__name__} 不允许二次设置 name: {self.__name} => {value}")
    if not value:
      raise ArgumentError(f"{self.__class__.__name__} 的 name 不可设置为空字符串")
    self.__name = value

  @property
  @strict_types
  def level(self) -> int:
    """ 返回 LeafFlow 的层级 """
    return self.__level

  @level.setter
  @strict_types
  def level(self, val: int):
    """ 设置 LeafFlow 的层级 """
    self.__level = val

  @property
  @strict_types
  def abtest_biz_name(self) -> str:
    """ 返回 LeafFlow 的 abtest_biz_name """
    return self.__abtest_biz_name

  @property
  @strict_types
  def abtest_biz_seq_num(self) -> int:
    """ 返回 LeafFlow 的 abtest_biz_seq_num """
    return self.__abtest_biz_seq_num

  @strict_types
  def namespace_(self, ns: str = "", nest: bool = False):
    """
    Dragonfly 命名空间接口: 给在此之后添加的 processor 设置一个命名空间

    参数说明
    ------
    `ns`: [string] 若非空则以该名称开启新命名空间, 若为空或缺省则退出一级当前所在的命名空间

    `nest`: [bool] 是否在当前命名空间之上以嵌套的方式开启新一级命名空间, 默认为 False (即开启前自动清空当前已有的命名空间)
    """
    if ns:
      ns_reserved_chars = " .:[]"
      check_arg(all(x not in ns for x in ns_reserved_chars), f"namespace 名称 '{ns}' 不可包含空格及以下字符:{ns_reserved_chars}")
      if not nest:
        self.__current_namespace.clear()
      self.__current_namespace.append(ns)
    else:
      if self.__current_namespace:
        self.__current_namespace.pop()
      else:
        raise LogicError("已无命名空间可退出, 请删除多余的 .namespace_() 调用")
    return self

  @property
  @strict_types
  def current_namespace(self) -> list:
    """ 获取当前命名空间的名称 """
    return self.__current_namespace

  @property
  @strict_types
  def loop_control_attr(self) -> str:
    """ 获取 pipeline 循环控制属性的名称 """
    return self.__loop_control_attr

  @property
  @strict_types
  def loop_on(self) -> str:
    """ 获取 pipeline 循环遍历的目标 common attr """
    return self.__loop_on

  @property
  @strict_types
  def loop_value(self) -> str:
    """ 获取 pipeline 循环当前遍历到的值 """
    return self.__loop_value

  @property
  @strict_types
  def loop_index(self) -> str:
    """ 获取 pipeline 循环当前遍历到的下标 """
    return self.__loop_index

  @property
  @strict_types
  def _absent_input_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    获取该 flow 所缺失的 common_attr 输入
    """
    absent_attrs = set()
    existing_attrs = set()
    for processor in self.__processors:
      absent_attrs |= get_all_input_common_attrs(processor) - existing_attrs
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        absent_attrs |= sub_flow._absent_input_common_attrs - sub_flow._import_common_attrs
      existing_attrs |= get_all_output_common_attrs(processor)
    # 忽略框架内置生成的 common_attr
    reserved_common_attrs = {"_USER_ID_", "_DEVICE_ID_", "_REQ_ID_", "_REQ_TYPE_", "_REQ_TIME_", "_REQ_NUM_", "_BROWSE_SET_SIZE_"}
    absent_attrs -= reserved_common_attrs
    # 忽略 loop 功能会自动生成的 common_attr
    for attr in [self.__loop_index, self.__loop_value]:
      if attr:
        absent_attrs.discard(attr)
    return absent_attrs

  @property
  @strict_types
  def _absent_input_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    获取该 flow 所缺失的 item_attr 输入
    """
    absent_attrs = set()
    existing_attrs = set()
    for processor in self.__processors:
      absent_attrs |= get_all_input_item_attrs(processor) - existing_attrs
      sub_flow = processor.get_sub_flow()
      if sub_flow:
        absent_attrs |= sub_flow._absent_input_item_attrs - sub_flow._import_item_attrs
      existing_attrs |= get_all_output_item_attrs(processor)
    return absent_attrs

  @property
  @strict_types
  def _produced_output_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    获取该 flow 内部会新产生的所有 common_attr
    """
    produced_attrs = set()
    for processor in self.__processors:
      produced_attrs |= get_all_output_common_attrs(processor)
      if processor.get_sub_flow():
        produced_attrs |= processor.get_sub_flow()._produced_output_common_attrs
    for attr in [self.__loop_index, self.__loop_value]:
      if attr:
        produced_attrs.add(attr)
    return produced_attrs

  @property
  @strict_types
  def _produced_output_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    获取该 flow 内部会新产生的所有 item_attr
    """
    produced_attrs = set()
    for processor in self.__processors:
      produced_attrs |= get_all_output_item_attrs(processor)
      if processor.get_sub_flow():
        produced_attrs |= processor.get_sub_flow()._produced_output_item_attrs
    return produced_attrs

  @property
  @strict_types
  def _produced_output_item_tables(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    获取该 flow 内部会新产生的所有 item_attr
    """
    produced_output_item_tables = set()
    for processor in self.__processors:
      produced_output_item_tables |= processor.output_item_tables
    return produced_output_item_tables


  @property
  @strict_types
  def _import_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow (目前只有 sub_flow) 初始携带的 common attrs
    """
    return self.__import_common_attrs

  @_import_common_attrs.setter
  def _import_common_attrs(self, value: set):
    self.__import_common_attrs = value


  @property
  @strict_types
  def _export_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow (目前只有 sub_flow) 最终会导出到主流程的 common attrs
    """
    return self.__export_common_attrs

  @_export_common_attrs.setter
  def _export_common_attrs(self, value: set):
    self.__export_common_attrs = value

  @property
  @strict_types
  def _import_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow (目前只有 sub_flow) 初始携带的 item attrs
    """
    return self.__import_item_attrs

  @_import_item_attrs.setter
  def _import_item_attrs(self, value: set):
    self.__import_item_attrs = value

  @property
  @strict_types
  def _export_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow (目前只有 sub_flow) 最终会导出到主流程的 item attrs
    """
    return self.__export_item_attrs

  @_export_item_attrs.setter
  def _export_item_attrs(self, value: set):
    self.__export_item_attrs = value

  @property
  @strict_types
  def _unused_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 检测到的无用 common attrs
    """
    unused_common_attrs = set()
    for processor in self.__processors:
      unused_common_attrs -= get_all_input_common_attrs(processor)
      if not processor.no_check():
        unused_common_attrs |= get_all_output_common_attrs(processor)
      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        unused_common_attrs -= processor.used_common_attrs
    if self.loop_control_attr:
      unused_common_attrs.discard(self.loop_control_attr)
    if self.level > 0:
      unused_common_attrs -= self._export_common_attrs
    return unused_common_attrs

  @property
  @strict_types
  def _unused_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 检测到的无用 item attrs
    """
    unused_item_attrs = set()
    for processor in self.__processors:
      unused_item_attrs -= get_all_input_item_attrs(processor)
      if not processor.no_check():
        unused_item_attrs |= get_all_output_item_attrs(processor)
      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        unused_item_attrs -= processor.used_item_attrs
    if self.level > 0:
      unused_item_attrs -= try_add_table_name(self.item_table, self._export_item_attrs)
    return unused_item_attrs

  @property
  @strict_types
  def _used_common_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 检测到的无用 common attrs
    """
    used_common_attrs = set()
    for processor in self.__processors:
      used_common_attrs |= get_all_input_common_attrs(processor)
      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        used_common_attrs |= processor.used_common_attrs
    return used_common_attrs

  @property
  @strict_types
  def _used_item_attrs(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 检测到的无用 item attrs
    """
    used_item_attrs = set()
    for processor in self.__processors:
      used_item_attrs |= get_all_input_item_attrs(processor)
      if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
        used_item_attrs |= processor.used_item_attrs
    return used_item_attrs

  @strict_types
  def _has_auto_adjust_injected(self) -> bool:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 是否已被自动填充过 AutoAdjust 后续 GlobalHolder
    """
    return self.__has_auto_adjust_injected

  @strict_types
  def _finish_auto_adjust_inject(self) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 已被自动填充过 AutoAdjust 后续 GlobalHolder
    """
    self.__has_auto_adjust_injected = True

  @strict_types
  def _has_local_item_attr_auto_injected(self) -> bool:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 是否已被自动填充过本地索引的正排属性
    """
    return self.__has_auto_injected_item_attrs

  @strict_types
  def _finish_local_item_attr_auto_inject(self, input_item_attrs: set, output_item_attrs: set) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 已被自动填充过本地索引的正排属性, 同时记录被自动填充时的初始输入 item_attr 集合
    """
    self.__input_item_attrs_before_auto_inject = input_item_attrs
    self.__output_item_attrs_after_auto_inject = output_item_attrs
    self.__has_auto_injected_item_attrs = True

  @strict_types
  def _get_input_item_attrs_before_auto_inject(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 被自动填充本地索引的正排属性时的初始输入 item_attr 集合
    """
    return self.__input_item_attrs_before_auto_inject

  @strict_types
  def _get_output_item_attrs_after_auto_inject(self) -> set:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 被自动填充本地索引的正排属性时的初始输入 item_attr 集合
    """
    return self.__output_item_attrs_after_auto_inject

  @strict_types
  def _has_additional_local_item_attr_auto_injected(self) -> bool:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 是否已被自动填充过本地索引的正排属性
    """
    return self.__has_auto_injected_additional_local_item_attr

  @strict_types
  def _finish_additional_local_item_attr_auto_inject(self) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 已被自动填充过本地索引的正排属性
    """
    self.__has_auto_injected_additional_local_item_attr = True

  @strict_types
  def _has_remote_item_attr_auto_injected(self) -> bool:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 是否已被自动填充过本地索引的正排属性
    """
    return any(isinstance(x, CommonRecoRemoteIndexItemAttrEnricher) for x in self.__processors)

  @strict_types
  def _has_downstream_auto_detected(self) -> bool:
    """
    内部接口，业务方请勿调用！
    ------
    该 LeafFlow 是否已被自动检测并填充过异步 Processor 的 downstream
    """
    return self.__has_downstream_auto_detected

  @strict_types
  def _finish_downstream_auto_detect(self) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    标记该 LeafFlow 已被自动检测并填充过异步 Processor 的 downstream
    """
    self.__has_downstream_auto_detected = True

  @property
  @strict_types
  def _processors(self) -> list:
    """
    内部接口，业务方请勿调用！
    ------
    返回 LeafFlow 的所有 Processor 实例
    """
    return self.__processors

  @strict_types
  def _get_pipeline_config(self) -> dict:
    """
    内部接口，业务方请勿调用！
    ------
    返回 LeafFlow 的 pipeline 定义，即待执行的 processor 列表
    """
    processor_names = []
    for processor in self.__processors:
      name = processor.name
      assert name in LeafFlowCore.__registered_processors, f"processor '{name}' has not been registered!"
      processor_names.append(name)
    pipeline_config = {
      "__PARENT": "base_pipeline",
      "pipeline": processor_names,
    }

    if not self.__loop_on:
      if self.__loop_control_attr:
        check_arg(self.__max_loop_count > 0, "loop_limit 必须与 loop_if 同时设置，且不能为0")
        pipeline_config["max_loop_count"] = self.__max_loop_count
        pipeline_config["loop_control_attr"] = self.__loop_control_attr
    else:
      check_arg(self.__loop_value, "loop_value 必须与 loop_on 同时设置，且不能为空")
      pipeline_config["loop_on"] = self.__loop_on
      pipeline_config["loop_index"] = self.__loop_index
      pipeline_config["loop_value"] = self.__loop_value
      pipeline_config["loop_control_attr"] = self.__loop_control_attr
      pipeline_config["max_loop_count"] = self.__max_loop_count

    if self.__item_table:
      pipeline_config["item_table"] = self.__item_table

    if self.__post_response:
      pipeline_config["post_response"] = self.__post_response

    return pipeline_config

  @property
  @strict_types
  def _branch_blame_map(self) -> dict:
    """
    内部接口，业务方请勿调用！
    ------
    返回 LeafFlow 的 branch_blame_map，即各个 branch_control_processor 是由哪个文件哪行代码生成的
    """
    return self.__branch_blame_map

  def _add_processor(self, processor: LeafProcessor, **kwargs) -> None:
    """
    内部接口，业务方请勿调用！
    ------
    向当前 LeafFlow 增加一个 Processor
    """
    if not isinstance(processor, LeafProcessor):
      raise ArgumentError(f"当前添加的 processor: {processor} 不是一个合法的 LeafProcessor 对象，请检查它是否继承自 LeafProcessor，以及创建时是否传递了 kwargs 参数")
    if BLAME_ERROR_CODE:
      frames = inspect.stack()
      for frame in frames:
        if frame.filename.endswith('_api_mixin.py') or frame.filename.endswith('dragonfly/common_leaf_flow.py'):
          continue
        processor.code_line += f"{frame.filename}:{frame.lineno}\n"

    if not kwargs.get("is_branch_control_processor", False):
      if self.__branch_state_machine.current_branch_state == BranchState.SWITCH:
        raise LogicError(f"switch_() 与第一个 case_() 之间不能包含其它方法调用")
      control_attr = self.__branch_state_machine.get_current_branch_control_attr()
      if control_attr:
        skip_config = processor._config.get("skip", "")
        if skip_config is not True:
          if skip_config:
            raise LogicError(f"处于条件分支内的 processor 禁止手动设置 skip: {skip_config}")
          processor._config["skip"] = "{{" + control_attr + "}}"
      processor.condition_hash = self.__branch_state_machine.get_current_branch_control_condition_hash()

    processor.namespace = tuple(self.current_namespace)
    # 首先为 processor 注入 table
    if processor.item_table is None:
      processor.item_table = self.__item_table

    position = kwargs.get("position", -1)
    if position < 0 and isinstance(processor, (LeafRetriever, LeafEnricher)) and \
        processor.need_reorder(self.__optimize_processor_order):
      position = self.__find_best_position(processor)

    if position < 0:
      self.__processors.append(processor)
    else:
      self.__processors.insert(position, processor)

  @strict_types
  def __find_best_position(self, processor: LeafProcessor) -> int:
    """
    ------
    整体原则：
    1. 将异步 processor 尽可能提前
    2. 将同步 processor 尽可能插入到其最近的异步 processor 链路中
    这两个原则不能保证一定会有优化，取决于 processor 的实际情况，如下游服务访问时间等，但理论上不会更差
    """
    is_sync_retriever = lambda x: not x.is_async() and x.output_item_tables
    best_pos = len(self.__processors) - 1
    while best_pos >= 0:
      p = self.__processors[best_pos]
      if p.is_reorder_barrier():
        break
      if is_sync_retriever(processor) and is_sync_retriever(p):
        # 保证同步召回之间的相对顺序不变化
        break
      if processor.depend_on(p) or p.depend_on(processor):
        break
      best_pos -= 1
    best_pos += 1

    if processor.is_async():
      # 如果当前 processor 为异步，保持连续几个异步 processor 的相对顺序
      while best_pos < len(self.__processors):
        p = self.__processors[best_pos]
        if not p.is_async():
          break
        best_pos += 1
    else:
      # 如果当前 processor 为同步，找到在其之前且其不依赖的一个异步 processor
      async_pos = len(self.__processors) - 1
      while async_pos >= best_pos:
        p = self.__processors[async_pos]
        if p.is_async():
          break
        async_pos -= 1

      if async_pos >= best_pos:
        # 如果存在这个异步 processor，尝试查找 downstream
        downstream_pos, downstream_processor = self._find_downstream(async_pos)
        if downstream_processor:
          # 若存在 downstream ，则插入 downstream 前一个（为保证相对顺序，没有直接插入到这个异步 processor 之后）
          best_pos = downstream_pos
        else:
          best_pos = len(self.__processors)
      else:
        best_pos = len(self.__processors)

    return best_pos

  @strict_types
  def __gen_branch_control_processor_config(self, condition: str, control_attr: str, condition_hash: str, **kwargs) -> dict:
    assert condition
    func_name = "evaluate"
    config = kwargs
    config["import_common_attr"] = extract_attrs_from_expr(condition)
    config["export_common_attr"] = [control_attr]
    config["function_for_common"] = func_name
    # NOTE(fangjianbing): 因为 processor 采用判断是否 skip, 这里 condition 为 true 时返回 false (即不 skip)
    config["lua_script"] = f"function {func_name}() if ({condition}) then return false else return true end end"
    config["for_branch_control"] = True
    processor_name = "_branch_controller_" + condition_hash
    if processor_name in LeafFlowCore.__conflict_branch_name:
      LeafFlowCore.__conflict_branch_name[processor_name] += 1
      processor_name += f"_{LeafFlowCore.__conflict_branch_name[processor_name]}"
    else:
      LeafFlowCore.__conflict_branch_name[processor_name] = 0
    config["name"] = processor_name
    return config

  @strict_types
  def __get_code_info(self, processor_name, condition_hash) -> str:
    """
    获取当前分支用于生成 LuaEnricher 的 python 代码位置
    """
    frames = inspect.stack()
    out_branch_code_info = bool(os.getenv("DRAGONFLY_OUTPUT_BRANCH_CODE_INFO", 'false') == 'true')
    for i, frame in enumerate(frames):
      if frame.function in ['if_', 'else_', 'else_if_', 'case_', 'default_']:
        fr = frames[i + 1]
        if fr.function == 'wrapper':
          fr = frames[i + 2]
        if not fr.code_context:
          break
        self.__branch_blame_map[processor_name] = (fr.filename, fr.lineno)
        last_name = fr.filename.rfind('/')
        code_context = fr.code_context[fr.index].strip('\\ \n')
        if out_branch_code_info:
          with open('branch_code_info.txt','a') as f:
            file_path = os.path.abspath(fr.filename)
            print(condition_hash, file_path, fr.lineno, fr.function, frame.function, code_context, file=f)
        return f"{fr.filename[last_name+1:]} in {fr.function}(): {code_context}"
    return "not found"

  @strict_types
  def __gen_processor(self, branch_state: str, control_attr: str, name: Optional[str] = None, **kwargs) -> None:
    condition = self.__branch_state_machine.get_current_branch_control_condition()
    condition_hash = self.__branch_state_machine.get_current_branch_control_condition_hash()
    config = self.__gen_branch_control_processor_config(condition, control_attr, condition_hash, **kwargs)
    if self.__branch_state_machine.current_branch_state == BranchState.ELSE :
      raw_name_pos = self.__branch_state_machine.start_processor.rfind("::")
      raw_name = ""
      if raw_name_pos == -1:
        raw_name = self.__branch_state_machine.start_processor
      else:
        raw_name = self.__branch_state_machine.start_processor[raw_name_pos + 2:]
      config["name"] = raw_name + "_else"
    if name:
      config["name"] = name
    processor = CommonRecoLuaAttrEnricher(config)
    self._add_processor(processor, is_branch_control_processor=True)
    if not self.__branch_state_machine.start_processor:
      self.__branch_state_machine.start_processor = processor.name
    processor._config["$branch_start"] = self.__branch_state_machine.start_processor
    processor._config["$code_info"] = f"[{branch_state}] {condition_hash} {self.__get_code_info(processor.name, condition_hash)}"

  @strict_types
  def _start_if_branch(self, expr: str, name: Optional[str] = None, **kwargs) -> None:
    self.__branch_state_machine.start_if_branch(expr)
    if_control_attr = self.__branch_state_machine.get_current_branch_control_attr()
    self.__gen_processor(branch_state="if", control_attr=if_control_attr, name=name, **kwargs)

  @strict_types
  def _start_else_branch(self, name: Optional[str] = None, **kwargs) -> None:
    self.__branch_state_machine.start_else_branch()
    else_control_attr = self.__branch_state_machine.get_current_branch_control_attr()
    self.__gen_processor(branch_state="else", control_attr=else_control_attr, name=name, **kwargs)

  @strict_types
  def _start_else_if_branch(self, expr: str, name: Optional[str] = None, **kwargs) -> None:
    self.__branch_state_machine.start_else_if_branch(expr)
    else_if_control_attr = self.__branch_state_machine.get_current_branch_control_attr()
    self.__gen_processor(branch_state="else_if", control_attr=else_if_control_attr, name=name, **kwargs)

  @strict_types
  def _end_if_branch(self) -> None:
    self.__branch_state_machine.end_if_branch()

  @strict_types
  def _start_switch_branch(self, expr: str) -> None:
    self.__branch_state_machine.start_switch_branch(expr)

  @strict_types
  def _start_case_branch(self, *cases, **kwargs) -> None:
    self.__branch_state_machine.start_case_branch(*cases)
    control_attr = self.__branch_state_machine.get_current_branch_control_attr()
    assert control_attr, "case 分支中的 control_attr 不能为空"
    self.__gen_processor(branch_state="case", control_attr=control_attr, **kwargs)

  @strict_types
  def _start_default_branch(self, **kwargs) -> None:
    self.__branch_state_machine.start_default_branch()
    control_attr = self.__branch_state_machine.get_current_branch_control_attr()
    assert control_attr, "default 分支中的 control_attr 不能为空"
    self.__gen_processor(branch_state="default", control_attr=control_attr, **kwargs)

  @strict_types
  def _end_switch_branch(self) -> None:
    self.__branch_state_machine.end_switch_branch()

  @strict_types
  def _is_branch_state_clean(self) -> bool:
    return self.__branch_state_machine.current_branch_state == BranchState.NONE

  @strict_types
  def _auto_end_branch(self) -> None:
    if self.__branch_state_machine.current_branch_state in (BranchState.IF, BranchState.ELSE, BranchState.ELSE_IF):
      self._end_if_branch()
    elif self.__branch_state_machine.current_branch_state in (BranchState.SWITCH, BranchState.CASE, BranchState.DEFAULT):
      self._end_switch_branch()
    else:
      raise LogicError("存在未配对的 end_() 调用")

  @strict_types
  def _find_downstream(self, pos: int):
    processor = self.__processors[pos]
    downstream_pos, downstream_processor = find_processor(self.__processors[pos+1:], lambda x: x.depend_on(processor))
    return downstream_pos + pos + 1, downstream_processor
