#!/usr/bin/env python3
# coding=utf-8
"""
- filename: build_file_util.py
- description: infer build file 
- author: <EMAIL>
- date: 2024-06-19 15:00:00
"""


import os

USED_MODULE = {}


PROCESSOR_NAME_TO_C_MODULE_MAP = {
  "CommonRecoEmbeddingRetriever": "ann_client",
  "CommonRecoColossusRetriever": "colossus_client",
  "CommonRecoTensorFlowAttrEnricher": "tensorflow",
  "CommonRecoTfLocalPredictEnricher": "tf_local_predict",
  "CommonRecoTfServingPredictEnricher": "tf_serving",
  "CommonRecoLocalAnnRetriever": "local_ann",
  "CommonRecoLocalAnnEmbeddingAttrEnricher": "gpu_local_ann",
  "KuibaPredictItemAttrEnricher": "kuiba_tf",
  "KuibaCalcUserEmbeddingAttrEnricher": "kuiba_tower",
  "TensorrtPredictItemAttrEnricher": "mio_trt"
}

PY_MODULE_TO_C_MODULE_MAP = {
  "explore_photo_cold_start": "video_cold_start",
  "livesquare": "livestream",
  "load_control": "ks_mix_rank",
  "common": "common_processors",
  "slide_leaf_dragonfly": "slide_leaf",
  "slide_mix_rank_api": "ks_slide_mix_rank",
  "merchant_accesso_dragonfly": "merchant_reco_access_layer",
  "slide_onerec_dragonfly": "slide_onerec",
}

C_MODULE_TO_PATH_MAP = {
  "common":"src/processor/common",
}

PROCESSOR_TO_DEPEND_PROCESSOR = {
  "CommonRecoRedisCommonAttrEnricher": ["CommonRecoRedisRegexRetriever", "CommonRecoDelegateEnricher"],
  "CommonRecoWriteToRedisObserver": ["CommonRecoRedisRegexRetriever"],
  "MixRankFetchFollowLiveMixScoreFromRedisEnricher": ("common_processors", "CommonRecoRedisRegexRetriever"),
  "LiveStreamMerchantRedisRetriever": ("common_processors", "CommonRecoRedisRegexRetriever"),
  "LiveStreamRevenueRedisRetriever": ("common_processors", "CommonRecoRedisRegexRetriever"),
  "LocalLifeCounterByRedisEnricher": ("common_processors", "CommonRecoRedisRegexRetriever"),
  "LocalLifePxtrQuantileObserver":("common_processors", "CommonRecoRedisRegexRetriever"),
  "TowerFetchTopNDotProductAttrEnricher": ["TowerFetchRemotePxtrAttrEnricher"],
  "LiveStreamRevenueEnsembleScoreEnricher": ["LiveStreamEnsembleScoreEnricher"],
  "ParallelAbtestCommonAttrEnricher": ("common_processors", "CommonRecoAbtestCommonAttrEnricher"),
  "CommonRecoRedisItemAttrEnricher": ["CommonRecoDelegateEnricher"],
  "CommonRecoRedisItemAttrBatchEnricher": ["CommonRecoDelegateEnricher"],
}

MODULE_TO_DEPEND_PROCESSOR = {
  "search": "SearchTextSegmentEnricher",
  "slide_leaf": ("common_processors", "CommonRecoDistributedFlatIndexEnricher"),
}


NO_CHANGE_MODULES = ["ks_mix_rank", "ks_follow_mix_rank", "ks_slide_mix_rank", "slide_leaf", "merchant_reco_access_layer", "slide_onerec"]

def get_modules(processor2module):
  module2processors = {}

  for pro, module in processor2module.items():
    m_s = module.split(".")
    assert len(m_s) >= 2, "processor to module has not ."
    m = m_s[-2] if m_s[0] == 'dragonfly' else m_s[0]
    if pro in PROCESSOR_NAME_TO_C_MODULE_MAP:
      m = PROCESSOR_NAME_TO_C_MODULE_MAP[pro]
    if m in PY_MODULE_TO_C_MODULE_MAP:
      m = PY_MODULE_TO_C_MODULE_MAP[m]

    if m not in module2processors:
      module2processors[m] = set()
    module2processors[m].add(pro)

    if pro in PROCESSOR_TO_DEPEND_PROCESSOR:
      if type(PROCESSOR_TO_DEPEND_PROCESSOR[pro]) == tuple:
        other_m, depend_pro = PROCESSOR_TO_DEPEND_PROCESSOR[pro]
        if other_m not in module2processors:
          module2processors[other_m] = set()
        module2processors[other_m].add(depend_pro)
      else:
        module2processors[m].update(PROCESSOR_TO_DEPEND_PROCESSOR[pro])

  for m in module2processors.keys():
    if m in NO_CHANGE_MODULES:
      module2processors[m] = set()
    if m in MODULE_TO_DEPEND_PROCESSOR:
      if type(MODULE_TO_DEPEND_PROCESSOR[m]) == tuple:
        other_m, depend_pro = MODULE_TO_DEPEND_PROCESSOR[m]
        if other_m not in module2processors:
          module2processors[other_m] = set()
        module2processors[other_m].add(depend_pro)
      else:
        module2processors[m].add(MODULE_TO_DEPEND_PROCESSOR[m])
  return module2processors

def get_all_cc_file_name(path:str) -> set():
  cc_file_paths = set()
  for root , _, files in os.walk(path):
    for f in files:
      if f.endswith(".cc"):
        cc_file_paths.add(root + "/" + f)
  return cc_file_paths

def get_processor_name(file_path:str) -> str:
  processor_list = []
  with open(file_path) as f:
    nxt_line = ""
    while True:
      line = nxt_line + f.readline()
      if not line:
        break
      if "FACTORY_REGISTER" in line:
        while True:
          nxt_line = f.readline()
          if "FACTORY_REGISTER" in nxt_line:
            break
          if not nxt_line:
            break
          line += nxt_line
        lines = line.split(",")
        if len(lines) >=2:
          processor_list.append(lines[1].strip())
  return processor_list


def get_module_path(module_name:str):
  base_path = os.getcwd()
  if module_name in C_MODULE_TO_PATH_MAP:
    return base_path + "/" + C_MODULE_TO_PATH_MAP[module_name] 
  return base_path


