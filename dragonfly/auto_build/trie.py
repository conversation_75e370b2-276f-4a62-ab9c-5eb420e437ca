#!/usr/bin/env python3
# coding=utf-8

SPECIFIC_PATH = {"retrieve", "enricher", "arranger", "observer", "mixer"}

class Trie:
  def __init__(self):
    self.__root = {}

  def build(self,path_list):
    for path in path_list:
      key_list = filter(lambda x: x, path.split("/")) 
      self._insert(key_list)
  
  def duplicate(self)->list:
    dup_list = []
    self._get_leaf(self.__root, "", dup_list, False)
    return dup_list

  def _get_leaf(self, node, path:str, dup_list, is_specific_key:bool):
    keys = node.keys()
    if not keys:
      return
    for k in keys:
      if path:
        sub_path = path + "/" + k
      else:
        sub_path = k
      if(len(node[k]) == 0):
        if (len(keys) == 1 and not is_specific_key) or "*" not in k:
          dup_list.append(sub_path)
      else:
        self._get_leaf(node[k], sub_path, dup_list, k in SPECIFIC_PATH)

  def _insert(self, key_list):
    cur_node = self.__root
    for k in key_list:
      if k not in cur_node:
        cur_node[k] = {}
      cur_node = cur_node[k]

