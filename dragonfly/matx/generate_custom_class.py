#!/usr/bin/env python3
# coding=utf-8
"""
- filename: generate_custom_class.py
- description: generate custom class
- author: <EMAIL>
- date: 2025-4-11 16:20:00
"""

import ctypes
ctypes.CDLL('/data/soft/distcc/gcc-10.3.0/lib64/libstdc++.so', ctypes.RTLD_GLOBAL)
import os

def generate_ir_module(sc_ctx):
  from matx.script import _passes, _parser, _link_ir_module
  _passes(sc_ctx)
  _parser(sc_ctx)
  _link_ir_module(sc_ctx)


def check_custom_class(to_dir: str, compile_object, if_raise: bool = False) -> bool:
  # 关闭注释生成
  os.environ["MATX_ADD_SPAN"] = 'false'
  compile_object_name = compile_object.__name__

  def if_exist(path):
    if not os.path.exists(path):
      if if_raise:
        raise FileNotFoundError(f'{path} not exits')
      else:
        print(f'{path} not exits, skip check custom class: {compile_object_name}')
        return False
    return True
  
  if not if_exist(to_dir) or not if_exist(os.path.join(to_dir, f'{compile_object_name}.h')) \
      or not if_exist(os.path.join(to_dir, f'{compile_object_name}.cc')):
    return False

  from matx.script import context
  sc_ctx = context.ScriptContext()
  sc_ctx.main_node.raw = compile_object
  generate_ir_module(sc_ctx)

  tag = abs(getattr(sc_ctx.main_node.ir_schema, 'tag')) % 4294967295
  
  with open(os.path.join(to_dir, f'{compile_object_name}.cc'), 'r') as f:
    content = f.read()
    if f'{compile_object_name}::tag_s_2_71828182846_ = {tag}' not in content:
      if if_raise:
        raise RuntimeError(f'{compile_object_name} tag num is not equal, new is {tag}')
      else:
        print(f'{compile_object_name} tag num is not equal, may has been modified')
        return False

  return True


def check_custom_class_with_dir(cc_file_dir: str, compile_object, if_raise: bool = False) -> bool:
  # 关闭注释生成
  os.environ["MATX_ADD_SPAN"] = 'false'
  compile_object_name = compile_object.__name__
  cc_file_path = os.path.join(cc_file_dir, f'{compile_object_name}.cc')

  if not os.path.exists(cc_file_path):
    if if_raise:
      raise FileNotFoundError(f'{cc_file_path} not exits')
    else:
      print(f'{cc_file_path} not exits, skip check custom class: {compile_object_name}')
      return False

  from matx.script import context
  sc_ctx = context.ScriptContext()
  sc_ctx.main_node.raw = compile_object
  generate_ir_module(sc_ctx)

  tag = abs(getattr(sc_ctx.main_node.ir_schema, 'tag')) % 4294967295
  
  with open(os.path.join(cc_file_path), 'r') as f:
    content = f.read()
    if f'{compile_object_name}::tag_s_2_71828182846_ = {tag}' not in content:
      if if_raise:
        raise RuntimeError(f'{compile_object_name} tag num is not equal, new is {tag}')
      else:
        print(f'{compile_object_name} tag num is not equal, may has been modified')
        return False

  return True

def generate_custom_class(to_dir: str, compile_object, ptr_type: str):
  # 关闭注释生成
  os.environ["MATX_ADD_SPAN"] = 'false'
  if not os.path.exists(to_dir):
    os.makedirs(to_dir)
  
  compile_object_name = compile_object.__name__
  from matx.script import context, _codegen_custom_class_header, _codegen_custom_class_cc
  
  sc_ctx = context.ScriptContext()
  sc_ctx.main_node.raw = compile_object
  generate_ir_module(sc_ctx)

  _codegen_custom_class_header(sc_ctx, ptr_type)
  with open(os.path.join(to_dir, f'{compile_object_name}.h'), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())
  
  _codegen_custom_class_cc(sc_ctx, ptr_type)
  with open(os.path.join(to_dir, f"{compile_object_name}.cc"), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())

  with open(os.path.join(to_dir, 'CPPLINT.cfg'), 'w') as f:
    content = '\n'.join([
      'set noparent',
      'filter=-*',
      'exclude_files=.*'
    ])
    f.write(content)


def generate_custom_class_with_subclass(ks_dir: str, to_dir: str, compile_object, ptr_type: str, prefix: str = 'Matx'):
  # 关闭注释生成
  os.environ["MATX_ADD_SPAN"] = 'false'

  matx_include_dir = os.path.join(ks_dir, 'third_party/matx/include/matxscript/runtime/dragon/custom_classes')
  matx_cc_dir = os.path.join(ks_dir, 'third_party/matx/src/runtime/dragon/custom_classes')

  if not os.path.exists(to_dir):
    os.makedirs(to_dir)
  
  compile_object_name = compile_object.__name__
  from matx.script import context, _codegen_custom_class_header, _codegen_custom_class_cc
  
  sc_ctx = context.ScriptContext()
  sc_ctx.main_node.raw = compile_object
  generate_ir_module(sc_ctx)

  _codegen_custom_class_header(sc_ctx)
  with open(os.path.join(matx_include_dir, f'{compile_object_name}.h'), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())
  
  _codegen_custom_class_cc(sc_ctx)
  with open(os.path.join(matx_cc_dir, f"{compile_object_name}.cc"), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())

  from matx.script import  _codegen_custom_class_header_subclass, _codegen_custom_class_cc_subclass
  _codegen_custom_class_header_subclass(sc_ctx, ptr_type, prefix)
  with open(os.path.join(to_dir, f'{prefix}{compile_object_name}.h'), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())
  
  _codegen_custom_class_cc_subclass(sc_ctx, ptr_type, prefix)
  with open(os.path.join(to_dir, f'{prefix}{compile_object_name}.cc'), 'w') as f:
    f.write(sc_ctx.rt_module.get_source())

  with open(os.path.join(to_dir, 'CPPLINT.cfg'), 'w') as f:
    content = '\n'.join([
      'set noparent',
      'filter=-*',
      'exclude_files=.*'
    ])
    f.write(content)

  all_include_file_path = os.path.join(ks_dir, 'third_party/matx/include/matxscript/runtime/codegen_all_includes.h')
  include_code = f'#include "matxscript/runtime/dragon/custom_classes/{compile_object_name}.h"\n'
  with open(all_include_file_path, 'r+') as f:
    content = f.read()
    if include_code not in content:
      f.write(include_code)

