import glob
import shutil
import os

def print_info(info):
  if os.environ.get('DRAGON_MATX_COMPILE_LOG', 'false').lower() == 'true':
    print(f"[matx] {info}")


matxscript_func_registry = "__matxscript_func_registry__"

def generate_head_file(function_set_name_list: list, code: str):
  code += "#include \"dragon/src/module/matx/matx_function_manager.h\"\n\n"
  for function_set_name in function_set_name_list:
    code += "#include \"" + function_set_name + ".cxx\"\n"
  return code


def generate_start_namespace(code: str):
  code += "\nnamespace ks {\n"
  code += "namespace platform {\n"
  return code


def generate_module_ctx(code: str):
  code += "\nvoid* __matxscript_module_ctx = nullptr;  // NOLINT\n\n"
  return code


def generate_func_registry(function_set_name_list: list, code: str):
  for function_set_name in function_set_name_list:
    code += f"extern MATX_DLL MATXScriptFuncRegistry ks::platform::matx::{function_set_name}::__matxscript_func_registry__;  // NOLINT\n"
  return code


def generate_init_function(function_set_name_list: list, code: str):
  code += "\nbool MatxFunctionManager::StaticInit() {\n"
  code += "  if (static_init_success_ == true) {\n"
  code += "    return true;\n"
  code += "  }\n"
  for function_set_name in function_set_name_list:
    code += f"  AddFunction(&ks::platform::matx::{function_set_name}::__matxscript_func_registry__, \"{function_set_name}\");  // NOLINT\n"

  code += "  static_init_success_ = true;\n"
  code += "  return true;\n" 
  code += "}\n"
  return code


def generate_end_namespace(code: str):
  code += "\n}  // namespace platform\n" 
  code += "}  // namespace ks\n\n"
  return code


def generate_matx_function_manager_cc():
  # 定义待查找的文件夹路径和要匹配的文件类型
  source_folder_path = os.environ.get('DRAGON_MATX_CC_SOURCE_FOLDER', '')
  file_extension = "*.cxx"

  if not os.path.exists(source_folder_path):
    print_info("Source folder not exists: {}".format(source_folder_path))
    return

  # 切换到目标文件夹
  os.chdir(source_folder_path)

  # 通过 glob 查找所有匹配的文件
  matching_files = glob.glob(file_extension)

  # 将匹配到的文件名去掉后缀并存入列表
  function_set_name_list = [os.path.splitext(file_name)[0] for file_name in matching_files]
  function_set_name_list.sort()
  for function_set_name in function_set_name_list:
    if function_set_name.endswith("cxx11"):
      function_set_name_list.remove(function_set_name)

  if len(function_set_name_list) == 0:
    print_info("No function set")
    return
  print_info(f".cxx list:{function_set_name_list}")

  # 生成代码
  generate_code = str()
  generate_code = generate_head_file(function_set_name_list, generate_code)
  generate_code = generate_start_namespace(generate_code)
  generate_code = generate_module_ctx(generate_code)
  generate_code = generate_func_registry(function_set_name_list, generate_code)
  generate_code = generate_init_function(function_set_name_list, generate_code)
  generate_code = generate_end_namespace(generate_code)

  # 覆盖 cc 文件
  output_folder_path = os.environ.get('DRAGON_MATX_CC_OUTPUT_FOLDER', '')
  if not os.path.exists(output_folder_path):
    print_info("Output folder not exists: {}".format(output_folder_path))
    return

  matx_function_manager_cc_path = output_folder_path + "/matx_function_manager.cc"
  with open(matx_function_manager_cc_path, 'w') as f:
    f.write(generate_code)

  # mv dso 下的所有 .cc
  cc_files = os.listdir(source_folder_path)
  for cc_file in cc_files:
    if not cc_file.endswith(".cxx"):
      continue
    source_file_path = os.path.join(source_folder_path, cc_file)
    output_file_path = os.path.join(output_folder_path, cc_file)
    shutil.copy(source_file_path, output_file_path)
    print_info("mv {} to {}".format(source_file_path, output_file_path))


if __name__ == '__main__':
  generate_matx_function_manager_cc()
