#!/usr/bin/env python3
# coding=utf-8

from typing import ByteString as bytes_view
from typing import List as FTList
from .custom_classes.ptr_wrapper import PtrWrapper

class ItemAttrSetter:
  def __init__(self) -> None:
    pass

  def SetInt(self, item_index: int, value: int) -> int:
    return 0

  def SetDouble(self, item_index: int, value: float) -> int:
    return 0
  
  def SetString(self, item_index: int, value : bytes) -> int:
    return 0
  
  def SetIntList(self, item_index: int, value: FTList[int]) -> int:
    return 0
  
  def SetDoubleList(self, item_index: int, value: FTList[float]) -> int:
    return 0
  
  def SetStringList(self, item_index: int, value: FTList[bytes]) -> int:
    return 0
  
  def SetPtr(self, item_index: int, value: PtrWrapper) -> int:
    return 0

  def AppendIntList(self, item_index: int, value: int) -> int:
    return 0
  
  def AppendDoubleList(self, item_index: int, value: float) -> int:
    return 0
  
  def AppendStringList(self, item_index: int, value: bytes_view) -> int:
    return 0

