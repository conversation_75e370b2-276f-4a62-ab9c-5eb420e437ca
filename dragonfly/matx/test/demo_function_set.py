# 此文件为 DEMO
# 可以通过在你的代码中调用测试
# from dragonfly.matx.demo_function_set import DemoFunctionSet
# self.enrich_attr_by_py(
#   import_common_attr = [{"name": "a_cp", "as": "a"}],
#   export_common_attr = [{"name": "b", "as": "b_cp"}],
#   import_item_attr = [{"name": "c_cp", "as": "c"}],
#   export_item_attr = [{"name": "d", "as": "d_cp"}],
#   function_set = DemoFunctionSet,
#   py_function = DemoFunctionSet.name_as_test
# ) \

from typing import Dict, Callable, Tuple, Type
from typing import ByteString as bytes_view
from typing import List as FTList
from typing import Dict as FTDict
from typing import Set as FTSet

from dragonfly.matx.dragonfly_context import DragonflyContext

class DemoFunctionSet:
  def __init__(self) -> None:
    pass
  
  def context_api_test(self, ctx: DragonflyContext) -> None:
    test_common_attr_int = ctx.GetInt(b"test_common_attr_int")
    test_common_attr_double = ctx.GetDouble(b"test_common_attr_double")
    test_common_attr_string: bytes = ctx.GetString(b"test_common_attr_string")
    test_common_attr_int_list = ctx.GetIntList(b"test_common_attr_int_list")
    test_common_attr_double_list = ctx.GetDoubleList(b"test_common_attr_double_list")
    test_common_attr_string_list = ctx.GetStringList(b"test_common_attr_string_list")

    ctx.SetInt(b"test_out_common_attr_int", test_common_attr_int + 1)
    ctx.SetDouble(b"test_out_common_attr_double", test_common_attr_double + 1.0)
    ctx.SetString(b"test_out_common_attr_string", test_common_attr_string + b"zxc")
    int_list_value: FTList[int] = [5, 6]
    ctx.SetIntList(b"test_out_common_attr_int_list", test_common_attr_int_list + int_list_value)
    double_list_value: FTList[float] = [5.2, 6.2]
    ctx.SetDoubleList(b"test_out_common_attr_double_list", test_common_attr_double_list + double_list_value)
    string_list_value: FTList[bytes] = [b"abc", b"efg"]
    for s in test_common_attr_string_list:
      string_list_value.append(s)
    ctx.SetStringList(b"test_out_common_attr_string_list", string_list_value)

    test_item_attr_int_getter = ctx.ItemAttrGetter(b"test_item_attr_int")
    test_item_attr_double_getter = ctx.ItemAttrGetter(b"test_item_attr_double")
    test_item_attr_string_getter = ctx.ItemAttrGetter(b"test_item_attr_string")
    test_item_attr_int_list_getter = ctx.ItemAttrGetter(b"test_item_attr_int_list")
    test_item_attr_double_list_getter = ctx.ItemAttrGetter(b"test_item_attr_double_list")
    test_item_attr_string_list_getter = ctx.ItemAttrGetter(b"test_item_attr_string_list")

    test_out_item_attr_int_setter = ctx.ItemAttrSetter(b"test_out_item_attr_int")
    test_out_item_attr_double_setter = ctx.ItemAttrSetter(b"test_out_item_attr_double")
    test_out_item_attr_string_setter = ctx.ItemAttrSetter(b"test_out_item_attr_string")
    test_out_item_attr_int_list_setter = ctx.ItemAttrSetter(b"test_out_item_attr_int_list")
    test_out_item_attr_double_list_setter = ctx.ItemAttrSetter(b"test_out_item_attr_double_list")
    test_out_item_attr_string_list_setter = ctx.ItemAttrSetter(b"test_out_item_attr_string_list")

    result_size = ctx.GetItemNum()
    for i in range(result_size):
      test_item_attr_int = test_item_attr_int_getter.GetInt(i)
      test_item_attr_double = test_item_attr_double_getter.GetDouble(i)
      test_item_attr_string: bytes = test_item_attr_string_getter.GetString(i)
      test_item_attr_int_list = test_item_attr_int_list_getter.GetIntList(i)
      test_item_attr_double_list = test_item_attr_double_list_getter.GetDoubleList(i)
      test_item_attr_string_list = test_item_attr_string_list_getter.GetStringList(i)

      test_out_item_attr_int_setter.SetInt(i, test_item_attr_int + 1)
      test_out_item_attr_double_setter.SetDouble(i, test_item_attr_double + 1.0)
      test_out_item_attr_string_setter.SetString(i, test_item_attr_string + b"zxc")
      item_int_list_value: FTList[int] = [6, 7]
      test_out_item_attr_int_list_setter.SetIntList(i, test_item_attr_int_list + item_int_list_value)
      item_double_list_value: FTList[float] = [6.2, 7.2]
      test_out_item_attr_double_list_setter.SetDoubleList(i, test_item_attr_double_list + item_double_list_value)
      item_string_list_value: FTList[bytes] = [b"uvw", b"xyz"]
      for s in test_item_attr_string_list:
        item_string_list_value.append(s)
      test_out_item_attr_string_list_setter.SetStringList(i, item_string_list_value)


  def test_strftime(self, ctx: DragonflyContext) -> None:
    a: bytes = ctx.util.strftime(b"%Y-%m-%d %w %H:%M:%S", 1714320406071 // 1000)
    ctx.SetString(b"test_strftime", a)


  def mark_tandian_photo(self, ctx: DragonflyContext) -> None:
    # Common Attr 处理逻辑
    s_level_good_tandian_id = ctx.GetInt(b"s_level_good_tandian_id")
    a_level_good_tandian_id = ctx.GetInt(b"a_level_good_tandian_id")

    # Item Attr Getter
    hetu_tag_getter = ctx.ItemAttrGetter(b"hetu_tag_level_info__hetu_tag")
    
    # Item Attr Setter
    is_s_level_tandian_photo_setter = ctx.ItemAttrSetter(b"is_s_level_tandian_photo_py")
    is_a_level_tandian_photo_setter = ctx.ItemAttrSetter(b"is_a_level_tandian_photo_py")
    
    # Item For 循环
    result_size = ctx.GetItemNum()
    for i in range(result_size):
      is_s_level_tandian_photo = 0
      is_a_level_tandian_photo = 0
      hetu_tag_list = hetu_tag_getter.GetIntList(i)
      for j in range(len(hetu_tag_list)):
        if hetu_tag_list[j] == s_level_good_tandian_id:
          is_s_level_tandian_photo = 1
        elif hetu_tag_list[j]  == a_level_good_tandian_id:
          is_a_level_tandian_photo = 1
      is_s_level_tandian_photo_setter.SetInt(i, is_s_level_tandian_photo)
      is_a_level_tandian_photo_setter.SetInt(i, is_a_level_tandian_photo)


  def version_1_8_9_feature_test(self, ctx: DragonflyContext) -> None:
    test_int_list_getter = ctx.ItemAttrGetter(b"test_int_list")

    py_key_setter = ctx.ItemAttrSetter(b"py_key")
    py_id_setter = ctx.ItemAttrSetter(b"py_id")
    py_type_setter = ctx.ItemAttrSetter(b"py_type")
    py_reason_setter = ctx.ItemAttrSetter(b"py_reason")
    py_score_setter = ctx.ItemAttrSetter(b"py_score")

    test_2_setter = ctx.ItemAttrSetter(b"test_2")
    test_3_setter = ctx.ItemAttrSetter(b"test_3")
    test_4_setter = ctx.ItemAttrSetter(b"test_4")
    test_5_setter = ctx.ItemAttrSetter(b"test_5")
    test_6_setter = ctx.ItemAttrSetter(b"test_6")

    # Item 循环
    result_size = ctx.GetItemNum()
    for i in range(result_size):
      py_key_setter.SetInt(i, ctx.GetKey(i))
      py_id_setter.SetInt(i, ctx.GetId(i))
      py_type_setter.SetInt(i, ctx.GetType(i))
      py_reason_setter.SetInt(i, ctx.GetReason(i))
      py_score_setter.SetDouble(i, ctx.GetScore(i))

      test_int_list = test_int_list_getter.GetIntList(i)
      test2 = test_int_list[1:3]
      test3 = test_int_list[:2]
      test4 = test_int_list[2:]
      # test5 = test_int_list[::-1]  step 不能为负数
      test6 = test_int_list[:]
      test_2_setter.SetIntList(i, test2)
      test_3_setter.SetIntList(i, test3)
      test_4_setter.SetIntList(i, test4)
      # test_5_setter.SetIntList(i, test5)
      test_6_setter.SetIntList(i, test6)


  def name_as_test(self, ctx: DragonflyContext) -> None:
    a = ctx.GetInt(b"a")
    ctx.SetInt(b"b", a)

    c_getter = ctx.ItemAttrGetter(b"c")
    d_setter = ctx.ItemAttrSetter(b"d")

     # Item 循环
    result_size = ctx.GetItemNum()
    for i in range(result_size):
      c = c_getter.GetInt(i)
      d_setter.SetInt(i, c)


  def version_1_8_10_has_value_and_append_test(self, ctx: DragonflyContext) -> None:
    test_common_attr_int = ctx.GetInt(b"test_common_attr_int")
    test_common_attr_double = ctx.GetDouble(b"test_common_attr_double")
    test_common_attr_string: bytes = ctx.GetString(b"test_common_attr_string")
    test_common_no_attr = ctx.HasValue(b"test_common_no_attr")

    ctx.AppendIntList(b"test_common_attr_int_list", test_common_attr_int)
    ctx.AppendDoubleList(b"test_common_attr_double_list", test_common_attr_double)
    ctx.AppendStringList(b"test_common_attr_string_list", test_common_attr_string)
    ctx.SetInt(b"test_out_common_no_attr", test_common_no_attr)
    ctx.SetInt(b"test_out_common_has_attr", ctx.HasValue(b"test_common_attr_int"))

    test_item_attr_int_getter = ctx.ItemAttrGetter(b"test_item_attr_int")
    test_item_attr_double_getter = ctx.ItemAttrGetter(b"test_item_attr_double")
    test_item_attr_string_getter = ctx.ItemAttrGetter(b"test_item_attr_string")
    test_item_attr_no_attr_getter = ctx.ItemAttrGetter(b"test_item_no_attr")

    test_out_item_attr_int_list_setter = ctx.ItemAttrSetter(b"test_item_attr_int_list")
    test_out_item_attr_double_list_setter = ctx.ItemAttrSetter(b"test_item_attr_double_list")
    test_out_item_attr_string_list_setter = ctx.ItemAttrSetter(b"test_item_attr_string_list")
    test_out_item_no_attr_setter = ctx.ItemAttrSetter(b"test_out_item_no_attr")
    test_out_item_has_attr_setter = ctx.ItemAttrSetter(b"test_out_item_has_attr")

    result_size = ctx.GetItemNum()
    for i in range(result_size):
      test_item_attr_int = test_item_attr_int_getter.GetInt(i)
      test_item_attr_double = test_item_attr_double_getter.GetDouble(i)
      test_item_attr_string: bytes = test_item_attr_string_getter.GetString(i)
      test_item_no_attr = test_item_attr_no_attr_getter.HasValue(i)

      test_out_item_attr_int_list_setter.AppendIntList(i, test_item_attr_int)
      test_out_item_attr_double_list_setter.AppendDoubleList(i, test_item_attr_double)
      test_out_item_attr_string_list_setter.AppendStringList(i, test_item_attr_string)
      test_out_item_no_attr_setter.SetInt(i, test_item_no_attr)
      test_out_item_has_attr_setter.SetInt(i, test_item_attr_int_getter.HasValue(i))

  def version_1_8_10_test(self, ctx:DragonflyContext) -> None:
    ctx.SetInt(b"get_timestamp_test", ctx.util.GetTimestamp())
    ctx.SetDouble(b"get_distance_test", ctx.util.GetDistance(40.7, 74.0, 48.85, 2.35))
    ctx.util.Sleep(1)
    ctx.SetDouble(b"random_test", ctx.util.Random())
    ctx.SetInt(b"cityhash64_test", ctx.util.CityHash64(b"hello,world"))

  def version_1_8_11_test(self, ctx:DragonflyContext) -> None:
    data_int = 1234567890
    ctx.SetString(b"int_to_string_test", b"test" + ctx.util.IntToString(data_int))
    data_double = 123.456789
    ctx.SetString(b"double_to_string_test1", b"test" + ctx.util.DoubleToString(data_double))
    ctx.SetString(b"double_to_string_test2", b"test" + ctx.util.DoubleToString(data_double, 2))

  def version_1_8_12_test(self, ctx:DragonflyContext) -> None:
    int_str = b'1234567890'
    ctx.SetInt(b"string_to_int_test", ctx.util.StringToInt(int_str))
    double_str = b'123.456789'
    ctx.SetDouble(b"string_to_double_test", ctx.util.StringToDouble(double_str))

