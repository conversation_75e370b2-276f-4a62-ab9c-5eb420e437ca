#!/usr/bin/env python3
# coding=utf-8
"""
- filename: fast_set_test.py
- description: fast set test
- author: <EMAIL>
- date: 2025-5-20 11:00:00
"""

from dragonfly.matx.dragonfly_context import *

class FastSetTestFunctionSet:
  def __init__(self) -> None:
    pass

  def Uint64FastSet_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_set: Uint64FastSet = ctx.PtrWrapper(b'Uint64FastSet')

    if not test_set.New():
      print('New fail')
      return
    for i in range(10):
      test_set.insert(i)
    ctx.SetPtr(b"test_set_py", test_set)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_set_py', test_set):
      return
    
    print('size:', test_set.size())
    print("empty:", test_set.empty())
    print("size:", test_set.size())
    print("count 0:", test_set.count(0))
    print("count 11:", test_set.count(11))
    print("erase 0:", test_set.erase(0))
    print("count 0:", test_set.count(0))
    print("clear:", test_set.clear())
    print("size:", test_set.size())
  
  def DoubleFastSet_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_set: DoubleFastSet = ctx.PtrWrapper(b'DoubleFastSet')

    if not test_set.New():
      print('New fail')
      return
    for i in range(10):
      test_set.insert(float(i))
    ctx.SetPtr(b"test_set_py", test_set)
    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_set_py', test_set):
      return
    
    print('size:', test_set.size())
    print("empty:", test_set.empty())
    print("size:", test_set.size())
    print("count 0.0:", test_set.count(0.0))
    print("count 11.0:", test_set.count(11.0))
    print("erase 0:", test_set.erase(0.0))
    print("count 0:", test_set.count(0.0))
    print("clear:", test_set.clear())
    print("size:", test_set.size())
  
  
  def StringFastSet_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_set: StringFastSet = ctx.PtrWrapper(b'StringFastSet')

    if not test_set.New():
      print('New fail')
      return
    for i in range(10):
      test_set.insert(ctx.util.IntToString(i))
    ctx.SetPtr(b"test_set_py", test_set)
    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_set_py', test_set):
      return
    
    print('size:', test_set.size())
    print("empty:", test_set.empty())
    print("size:", test_set.size())
    print("count '0':", test_set.count(b'0'))
    print("count '11':", test_set.count(b'11'))
    print("erase '0':", test_set.erase(b'0'))
    print("count '0':", test_set.count(b'0'))
    print("clear:", test_set.clear())
    print("size:", test_set.size())

