#!/usr/bin/env python3
# coding=utf-8
"""
- filename: fast_map_test.py
- description: fast map test
- author: <EMAIL>
- date: 2025-5-20 11:00:00
"""

from dragonfly.matx.dragonfly_context import *

class FastMapTestFunctionSet:
  def __init__(self) -> None:
    pass

  def Uint64Uint64FastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: Uint64Uint64FastMap = ctx.PtrWrapper(b'Uint64Uint64FastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(i, i + 1)
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(0, 2)
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count 0:", test_map.count(0))
    print("at 0:", test_map.at(0))
    print("at 1:", test_map.at(1))
    print("count 11:", test_map.count(11))
    print("erase 0:", test_map.erase(0))
    print("count 0:", test_map.count(0))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at 0:", test_map.at(0))


  def Uint64DoubleFastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: Uint64DoubleFastMap = ctx.PtrWrapper(b'Uint64DoubleFastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(i, float(i + 1.1))
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(0, 2.2)
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count 0:", test_map.count(0))
    print("at 0:", test_map.at(0))
    print("at 1:", test_map.at(1))
    print("count 11:", test_map.count(11))
    print("erase 0:", test_map.erase(0))
    print("count 0:", test_map.count(0))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at 0:", test_map.at(0))


  def Uint64StringFastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: Uint64StringFastMap = ctx.PtrWrapper(b'Uint64StringFastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(i, ctx.util.IntToString(i))
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(0, b'2')
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count 0:", test_map.count(0))
    print("at 0:", test_map.at(0))
    print("at 1:", test_map.at(1))
    print("count 11:", test_map.count(11))
    print("erase 0:", test_map.erase(0))
    print("count 0:", test_map.count(0))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at 0:", test_map.at(0))


  def StringUint64FastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: StringUint64FastMap = ctx.PtrWrapper(b'StringUint64FastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(ctx.util.IntToString(i), i)
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(b'0', 2)
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count '0':", test_map.count(b'0'))
    print("at '0':", test_map.at(b'0'))
    print("at '1':", test_map.at(b'1'))
    print("count '11':", test_map.count(b'11'))
    print("erase '0':", test_map.erase(b'0'))
    print("count '0':", test_map.count(b'0'))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at '0':", test_map.at(b'0'))


  def StringDoubleFastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: StringDoubleFastMap = ctx.PtrWrapper(b'StringDoubleFastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(ctx.util.IntToString(i), i + 1.1)
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(b'0', 2.2)
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count '0':", test_map.count(b'0'))
    print("at '0':", test_map.at(b'0'))
    print("at '1':", test_map.at(b'1'))
    print("count '11':", test_map.count(b'11'))
    print("erase '0':", test_map.erase(b'0'))
    print("count '0':", test_map.count(b'0'))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at '0':", test_map.at(b'0'))


  def StringStringFastMap_test(self, ctx: DragonflyContext) -> None:
    # 使用对应类型的 Ptr 都需要先得到一个对应结构体，但是其中还没有内容
    test_map: StringStringFastMap = ctx.PtrWrapper(b'StringStringFastMap')

    if not test_map.New():
      print('New fail')
      return
    for i in range(10):
      # insert 不存在 key 时插入，存在 key 不更新 value
      test_map.insert(ctx.util.IntToString(i), ctx.util.IntToString(i + 1))
    # insert_or_assign 不存在 key 时插入，存在 key 更新 value
    test_map.insert_or_assign(b'0', b'2')
    ctx.SetPtr(b"test_map_py", test_map)

    # Get 时，将构造的结构体塞入其中，如果成功，返回 true。否则返回 false（包含多种可能取不到的情况，如 attr type 没对上、没有注册这个 type 等。
    # 可以将这个操作认为与判空一起的，这个返回 true 则一定有对应的 ptr）
    if not ctx.GetPtr(b'test_map_py', test_map):
      return
    
    print('size:', test_map.size())
    print("empty:", test_map.empty())
    print("size:", test_map.size())
    print("count '0':", test_map.count(b'0'))
    print("at '0':", test_map.at(b'0'))
    print("at '1':", test_map.at(b'1'))
    print("count '11':", test_map.count(b'11'))
    print("erase '0':", test_map.erase(b'0'))
    print("count '0':", test_map.count(b'0'))
    print("clear:", test_map.clear())
    print("size:", test_map.size())
    # 由于没有该 key，抛异常 at() did not find key
    print("at '0':", test_map.at(b'0'))
