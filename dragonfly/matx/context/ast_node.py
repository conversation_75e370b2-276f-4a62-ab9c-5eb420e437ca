#!/usr/bin/env python3
# coding=utf-8

from typing import Optional, List, Dict, Any
from ..analysis._typed_ast import ast


class Span:

    def __init__(self):
        self.file_name: str = ''
        self.lineno: int = 0
        self.func_name: str = ''
        self.source_code: str = ''


class ModuleInfo:

    def __init__(self):
        self.name: str = ''
        self.raw: Optional[type] = None
        self.globals: dict = {}
        self.imports: dict = {}


class ASTNode:

    def __init__(self, ):
        self.raw: Optional[type] = None
        self.span: Span = Span()
        self.ast: Optional[ast.AST] = None
        self.module: Optional[ModuleInfo] = None
        self.deps: Optional[List[ASTNode]] = None
        self.ir_schema = None
        self.ir = None
        self.last: Optional[ASTNode] = None
        self.extra: Dict[str, Any] = {}

    def topodeps(self):
        outputs = []
        visited_raw = set()
        visited_deps = set()

        def dfs_visit(node: ASTNode):
            if node is None or node.raw is None:
                return
            if node.deps:
                if node.raw not in visited_deps:
                    # break ring
                    visited_deps.add(node.raw)
                    for dep in node.deps:
                        dfs_visit(dep)
            if node.raw not in visited_raw:
                outputs.append(node)
                visited_raw.add(node.raw)

        dfs_visit(self)
        return list(reversed(outputs))
