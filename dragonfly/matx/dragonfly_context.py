#!/usr/bin/env python3
# coding=utf-8
"""
- filename: dragonfly_context.py
- description: dragonfly context in python
- author: <EMAIL>
- date: 2024-1-25 16:20:00
"""

from typing import ByteString as bytes_view
from typing import List as FTList
from typing import Dict as FTDict
from typing import Set as FTSet
from .item_attr_getter import ItemAttrGetter
from .item_attr_setter import ItemAttrSetter
from .dragonfly_util import DragonflyUtil
from .custom_classes import *

class DragonflyContext:
  def __init__(self) -> None:
    self.util: DragonflyUtil = DragonflyUtil()

  def GetInt(self, attr_name: bytes, default_value: int = 0) -> int:
    return 0
  
  def GetDouble(self, attr_name: bytes, default_value: float = 0.0) -> float:
    return 0.0
  
  def GetString(self, attr_name: bytes, default_value: bytes_view = b"") -> bytes_view:
    return b""
  
  def GetIntList(self, attr_name: bytes) -> FTList[int]:
    return []
  
  def GetDoubleList(self, attr_name: bytes) -> FTList[float]:
    return []
  
  def GetStringList(self, attr_name: bytes) -> FTList[bytes_view]:
    return []
  
  def GetPtr(self, attr_name: bytes_view, ptr_wrapper: PtrWrapper) -> bool:
    return False
  
  def PtrWrapper(self, class_name: bytes_view) -> PtrWrapper:
    return PtrWrapper()

  def SetInt(self, attr_name: bytes, value: int) -> int:
    return 0
  
  def SetDouble(self, attr_name: bytes, value: float) -> int:
    return 0
  
  def SetString(self, attr_name: bytes, value: bytes) -> int:
    return 0
  
  def SetIntList(self, attr_name: bytes, value: FTList[int]) -> int:
    return 0
  
  def SetDoubleList(self, attr_name: bytes, value: FTList[float]) -> int:
    return 0
  
  def SetStringList(self, attr_name: bytes, value: FTList[bytes]) -> int:
    return 0
  
  def SetPtr(self, attr_name: bytes, value: PtrWrapper) -> int:
    return 0
    
  def HasValue(self, attr_name: bytes) -> int:
    return 0
  
  def AppendIntList(self, attr_name: bytes, value: int) -> int:
    return 0
  
  def AppendDoubleList(self, attr_name: bytes, value: float) -> int:
    return 0
  
  def AppendStringList(self, attr_name: bytes, value: bytes_view) -> int:
    return 0
  
  def GetItemNum(self) -> int:
    return 0
  
  def ItemAttrGetter(self, attr_name: bytes_view) -> ItemAttrGetter:
    return ItemAttrGetter()

  def ItemAttrSetter(self, attr_name: bytes_view) -> ItemAttrSetter:
    return ItemAttrSetter()

  def GetKey(self, item_index: int) -> int:
    return 0
  
  def GetId(self, item_index: int) -> int:
    return 0
  
  def GetType(self, item_index: int) -> int:
    return 0
  
  def GetReason(self, item_index: int) -> int:
    return 0
  
  def GetScore(self, item_index: int) -> float:
    return 0.0

