#!/usr/bin/env python3
# coding=utf-8
"""
- filename: util.py
- description: py enricher util
- author: <EMAIL>
- date: 2024-05-06 19:20:00
"""

from typing import ByteString as bytes_view

class DragonflyUtil:
  def __init__(self) -> None:
    pass

  def strftime(self, format: bytes_view, time: int) -> bytes:
    return b""

  def GetTimestamp(self) -> int:
    return 0
  
  def GetDistance(self, lat_a: float, lon_a: float, lat_b: float, lon_b: float) -> float:
    return 0.0

  def Sleep(self, ms: int) -> None:
    return None
  
  def Random(self) -> float:
    return 0.0
  
  def CityHash64(self, str: bytes) -> int:
    return 0

  def IntToString(self, data: int) -> bytes:
    return b""

  def DoubleToString(self, data: float, precision: int = -1) -> bytes:
    return b""

  def StringToInt(self, data: bytes_view) -> int:
    return 0
  
  def StringToDouble(self, data: bytes_view) -> float:
    return 0.0

