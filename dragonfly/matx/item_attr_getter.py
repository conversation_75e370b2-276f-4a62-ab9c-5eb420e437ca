#!/usr/bin/env python3
# coding=utf-8

from typing import ByteString as bytes_view
from typing import List as FTList
from .custom_classes.ptr_wrapper import PtrWrapper

class ItemAttrGetter:
  def __init__(self) -> None:
    pass

  def GetInt(self, item_index: int, default_value: int = 0) -> int:
    return 0

  def GetDouble(self, item_index: int, default_value: float = 0.0) -> float:
    return 0.0
  
  def GetString(self, item_index: int, default_value: bytes_view = b"") -> bytes_view:
    return b""
  
  def GetIntList(self, item_index: int) -> FTList[int]:
    return []
  
  def GetDoubleList(self, item_index: int) -> FTList[float]:
    return []
  
  def GetStringList(self, item_index: int) -> FTList[bytes_view]:
    return []

  def GetPtr(self, item_index: int, ptr_wrapper: PtrWrapper) -> bool:
    return False

  def HasValue(self, item_index: int) -> int:
    return 0

