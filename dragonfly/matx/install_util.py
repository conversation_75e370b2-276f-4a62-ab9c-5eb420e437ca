#!/usr/bin/env python3
# coding=utf-8
"""
- filename: install_util.py
- description: matx install util
- author: <EMAIL>
- date: 2024-05-27 15:55:00
"""

import os
import traceback
import subprocess
import signal
import sys


def print_info(info):
  if os.environ.get('DRAGON_MATX_COMPILE_LOG', 'false').lower() == 'true':
    print(f"[Py UDF] {info}")


def check_matx_version():
  try:
    import pkg_resources
  except ImportError:
    print_info("try install pkg_resources...")
    pip_install_cmd = "pip3 install -U setuptools"
    ret_code, _, err = shell_execute(pip_install_cmd)
    if ret_code != 0:
      raise Exception("pip install failed!\n{}".format(err))
    import pkg_resources

  try:
    latest_matx_version = get_kconf_string_config('reco.dragon.matx_version')
  except Exception as e:
    raise Exception("Get kconf failed! key: reco.dragon.matx_version\n{}".format(e))

  if latest_matx_version == None:
    raise ValueError("Parse kconf response failed!\n")

  try:
    local_version = pkg_resources.get_distribution("ks-dragonfly-matxscript").version
    print_info(f"current version: {local_version}")
  except pkg_resources.DistributionNotFound:
    print_info(f"matx not found! start installing matx-{latest_matx_version} ...")
    return True, install_latest_matx(latest_matx_version)
  
  if latest_matx_version != local_version:
    print_info(f"start updating matx to version {latest_matx_version} ...")
    return True, install_latest_matx(latest_matx_version)
  
  return False, latest_matx_version


def get_kconf_string_config(key: str):
  try:
    import requests
  except ImportError:
    print_info("try install requests...")
    pip_install_cmd = "pip3 install -U requests"
    ret_code, _, err = shell_execute(pip_install_cmd)
    if ret_code != 0:
      raise Exception("pip install failed!{}".format(err))
    import requests

  token = '0d7d97303c04cfee0564f51671a656e3'
  url = "https://kconf.corp.kuaishou.com/api/config/get"
  headers = {"Authorization": f"Token {token}"}
  params = {"key": key}
  response = requests.get(url, headers=headers, params=params)
  res_dict = response.json()
  if "data" in res_dict:
    if "subConfigs" in res_dict["data"]:
      for conf in res_dict["data"]["subConfigs"]:
        if conf.get("stage", "") == "production":
          version = conf.get("data", None)
          return version
  return None


def install_latest_matx(version: str) -> str:
  install_cmd = f"pip3 install ks-dragonfly-matxscript=={version} --user -i https://pypi.corp.kuaishou.com/kuaishou/prod"
  import platform
  if platform.system() != 'Linux':
    install_cmd += " --break-system-packages"
  ret_code, _, err = shell_execute(install_cmd)
  if ret_code != 0:
    raise Exception("install failed!{}".format(err))

  return version

def install_python_package(package_name: str):
  install_cmd = f"pip3 install {package_name} --user -i https://pypi.corp.kuaishou.com/kuaishou/prod"
  import platform
  if platform.system() != 'Linux':
    install_cmd += " --break-system-packages"
  ret_code, _, err = shell_execute(install_cmd)
  if ret_code != 0:
    raise Exception("install failed!{}".format(err))

def shell_execute(in_cmd_line):
  """
  exec linux cmd and get response info
  :param in_cmd_line: cmd to exec
  :return: returncode, outs, errors
  """
  if len(in_cmd_line) <= 0:
    return -1, None

  try:
    p = subprocess.Popen(in_cmd_line, shell=True, universal_newlines=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=1, close_fds=True)
    try:
      outs, errs = p.communicate()
      retval = p.returncode
    except subprocess.TimeoutExpired:
      os.kill(p.pid, signal.SIGKILL)
      print_info('cmd {} process killed'.format(in_cmd_line))
      outs, errs = p.communicate()
      retval = p.returncode
      print_info('cmd {} process killed,end {}'.format(in_cmd_line, retval))

    return retval, outs, errs
  except Exception as e:
    print_info("run cmd {} error {} - {}".format(in_cmd_line, e, traceback.format_exc()))
    return -1, None, None
  

if __name__ == '__main__':
  check_matx_version()
