#!/usr/bin/env python3
# coding=utf-8
"""
- filename: remote_dso.py
- description: matx remote dso util
- author: <EMAIL>
- date: 2024-09-20 11:11:00
"""

from .install_util import print_info, shell_execute
import datetime
import hashlib
import subprocess
import os

git_dir = None
git_repo_name = None
git_branch = None
git_user_name = None
blob_client = None

def check_hash_and_upload(so_file_name: str, so_file_path: str, object_key: str, md5_new: str) -> str:
  # 获取 client
  set_blob_client()
  from botocore.exceptions import ClientError
  bucket_name = 'reco-dragon-matx-so'  # 存储桶名

  def tar_so(so_file_name: str, so_file_path: str) -> str:
    tar_file_path = so_file_path.replace('.so', '.tar.gz')
    import tarfile
    with tarfile.open(tar_file_path, "w:gz") as tar:
      tar.add(so_file_path, so_file_name)
    return tar_file_path

  try:
    response = blob_client.head_object(Bucket=bucket_name, Key=object_key)
  except blob_client.exceptions.NoSuchKey:
    now_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    metadata = {"time": now_time, "md5": md5_new, "branch": git_branch, "uploader": git_user_name}
    # so_file_name_with_hash = so_file_name.replace('.so', f"_{md5_new}.so")
    # upload_file(tar_so(so_file_name_with_hash, so_file_path), object_key, metadata)
    upload_file(so_file_path, object_key, metadata)
  except ClientError as e:
    if e.response["Error"]["Code"] == "404":
      now_time = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
      metadata = {"time": now_time, "md5": md5_new, "branch": git_branch, "uploader": git_user_name}
      # so_file_name_with_hash = so_file_name.replace('.so', f"_{md5_new}.so")
      # upload_file(tar_so(so_file_name_with_hash, so_file_path), object_key, metadata)
      upload_file(so_file_path, object_key, metadata)
    else:
      raise Exception(f"head {object_key} failed. {e}")


def get_object_key(file_path: str) -> str:
  set_git_info()
  with open(file_path, 'rb') as so_file:
    md5_new = hashlib.md5(so_file.read()).hexdigest()
  # object_key = git_repo_name + file_path.replace(git_dir, "").replace(".so", f"_{md5_new}.tar.gz")
  object_key = git_repo_name + file_path.replace(git_dir, "").replace(".so", f"_{md5_new}.so")
  return object_key, md5_new


def get_endpoint():
  def get_endpoint_by_ip():
    try:
      import socket
      ips = []
      hostname = socket.gethostname()
      for info in socket.getaddrinfo(hostname, None):
        if info[0] == socket.AF_INET:
          ips.append(info[4][0])
      ips = list(set(ips))  # 去重
      endpoint_url = 'http://bs3-hb1.internal'
      for ip in ips:
        if ip.startswith('172'):
          endpoint_url = 'http://bs3-hb1.corp.kuaishou.com'
          break
    except Exception as e:
      endpoint_url = 'http://bs3-hb1.internal'
    return endpoint_url
  
  if os.getenv('DRAGON_MATX_DEVELOP_ENV'):
    env = os.environ.get("DRAGON_MATX_DEVELOP_ENV").lower()
    if env == 'hb1-internal':
      endpoint_url = 'http://bs3-hb1.internal'
    elif env == 'hb1-corp':
      endpoint_url = 'http://bs3-hb1.corp.kuaishou.com'
    else:
      raise ValueError('DRAGON_MATX_DEVELOP_ENV value error')
  else:
    endpoint_url = get_endpoint_by_ip()
  
  return endpoint_url


def set_blob_client():
  global blob_client
  if blob_client is not None:
    return
  try:
    from botocore.config import Config
    from botocore.exceptions import ClientError
  except Exception as e:
    print_info("try install botocore...")
    pip_install_cmd = "pip3 install -U botocore -i https://pypi.corp.kuaishou.com/kuaishou/prod --user"
    ret_code, _, err = shell_execute(pip_install_cmd)
    if ret_code != 0:
      raise Exception("pip install failed!\n{}".format(err))
    from botocore.config import Config
    from botocore.exceptions import ClientError
  
  try:
    import boto3
  except Exception as e:
    print_info("try install boto3...")
    pip_install_cmd = "pip3 install -U boto3 -i https://pypi.corp.kuaishou.com/kuaishou/prod --user"
    ret_code, _, err = shell_execute(pip_install_cmd)
    if ret_code != 0:
      raise Exception("pip install failed!\n{}".format(err))
    import boto3
  
  s3_client_config = Config(
    region_name='hb1',
    s3={'addressing_style': 'path'},
  )
  
  endpoint_url = get_endpoint()
  blob_client = boto3.client(
    service_name='s3',
    aws_access_key_id='2f99b7766ff2436faaeb79cf90882972',
    aws_secret_access_key='ZWYzM2E1M2UtMzFhOS00MTY3LWExZGYtMzcwMmM2NTJhYTNk',
    endpoint_url=endpoint_url,
    config=s3_client_config,
    use_ssl=False,  # 关闭 SSL
  )

  # SDK 没有直接提供为全部请求添加 HTTP Header 的方法，通过事件实现
  def _add_service_header(request, **kwargs):
    service = f"{git_repo_name}_{git_branch}_{git_user_name}"
    request.headers.add_header('service', service)  # 一定要添加服务名标识！ 如果使用get_kws_info获取失败 请手动输入

  event_system = blob_client.meta.events
  event_system.register('before-sign.*.*', _add_service_header)


def upload_file(file_path, blob_object_key, metadata):
  global blob_client
  set_blob_client()
  from botocore.exceptions import ClientError
  file = open(file_path, 'rb')
  bucket_name = 'reco-dragon-matx-so'  # 存储桶名
  object_key = blob_object_key  # 对象名
  data = file.read()  # 要上传的对象内容
  content_type = 'application/octet-stream'  # 类型
  print_info(f"uploading {file_path} as {object_key} ...")
  upload_start = datetime.datetime.now()
  try:
    # 调用put_object方法上传文件
    blob_client.put_object(Body=data, Bucket=bucket_name, Key=object_key, Metadata=metadata,
                      ContentType=content_type)
  except ClientError as e:
    raise Exception(f"upload {file_path} failed. {e}")
  upload_end = datetime.datetime.now()
  print_info(f"time cost: {upload_end - upload_start}")


def download_file(blob_object_key, file_path):
  global blob_client
  set_blob_client()
  from botocore.exceptions import ClientError
  bucket_name = 'reco-dragon-matx-so'  # 存储桶名
  object_key = blob_object_key  # 对象名
  try:
    response = blob_client.get_object(Bucket=bucket_name, Key=object_key)
    file_content = response['Body'].read()
    with open(file_path, 'wb') as file:
      file.write(file_content)
  except ClientError as e:
      raise Exception(f"download {file_path} failed. {e}")


def set_git_info() -> str:
  global git_user_name, git_repo_name, git_branch, git_dir
  if git_user_name is None:
    # 获取 git 用户名
    cmd = 'git config user.name'
    proc = subprocess.Popen(cmd, shell=True, universal_newlines=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=1, close_fds=True)
    out, err = proc.communicate()
    if proc.returncode == 0 and out:
      git_user_name = out.strip()
    else:
      git_user_name = "none"

  if git_branch is None:
    # 获取当前分支
    cmd = 'git rev-parse --abbrev-ref HEAD'
    proc = subprocess.Popen(cmd, shell=True, universal_newlines=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=1, close_fds=True)
    out, err = proc.communicate()
    if proc.returncode == 0 and out:
      git_branch = out.strip()
    else:
      git_branch = "none"

  if git_dir is None:
    # 提取仓库名称和目录
    cmd = 'git rev-parse --show-toplevel'
    proc = subprocess.Popen(cmd, shell=True, universal_newlines=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=1, close_fds=True)
    out, err = proc.communicate()
    if proc.returncode == 0 and out:
      git_dir = out.strip()
      git_repo_name = os.path.basename(git_dir)
    else:
      raise Exception(f'无法获取 Git 仓库目录, {err}')

