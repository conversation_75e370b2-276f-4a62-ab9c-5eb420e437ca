#!/usr/bin/env python3
# coding=utf-8
"""
- filename: get_compile_tool.py
- description: matx get compile tool
- author: <EMAIL>
- date: 2024-07-17 17:05:00
"""

import os
import sys
from dragonfly.matx.install_util import shell_execute, print_info
from dragonfly.matx.remote_dso_util import download_file


def get_compile_tool(compile_tool: str):
  download_folder_path = os.environ.get('MATX_COMPILE_TOOLS_DOWNLOAD_PATH',  os.path.join(os.path.expanduser("~"), '.matx_compile_tools'))
  if not os.path.exists(download_folder_path):
    os.makedirs(download_folder_path)
  if compile_tool.lower() == "clang":
    get_clang_tool(download_folder_path)
    get_std_so_tool(download_folder_path)
  elif compile_tool.lower() == "gcc":
    get_gcc_tool(download_folder_path)
  elif compile_tool.lower() == "std":
    get_std_so_tool(download_folder_path)
  elif compile_tool.lower() == "both":
    get_clang_tool(download_folder_path)
    get_gcc_tool(download_folder_path)


def get_gcc_tool(download_folder_path):
  download_path = os.path.join(download_folder_path, 'gcc-10.3.0_.tgz')
  if os.path.exists(download_path) == False:
    print_info(f"download {download_path} ...")
    # download_file('compile_tool/gcc-10.3.0_.tgz', download_path)
    get_file_cmd = f"cd {download_folder_path} && wget http://bs3-hb1.internal/reco-dragon-matx-so/compile_tool/gcc-10.3.0_.tgz && cd -"
    ret_code, _, err = shell_execute(get_file_cmd)
    if ret_code != 0:
      raise Exception("download failed! {}".format(err))
  else:
    print_info(f"{download_path} exists")
    
  gcc_folder_path = os.path.join(download_folder_path, 'gcc-10.3.0')
  compile_tool_path = os.path.join(gcc_folder_path, 'bin/g++')
  if os.path.exists(compile_tool_path) == False:
    tar_file_cmd = f"cd {download_folder_path} && tar -xzvf gcc-10.3.0_.tgz && cd -"
    print_info(f"unzip {download_path} ...")
    ret_code, _, err = shell_execute(tar_file_cmd)
    if ret_code != 0:
      raise Exception("unzip failed! {}".format(err))
  else:
    print_info(f"{compile_tool_path} exists")
  

def get_clang_tool(download_folder_path):
  download_path = os.path.join(download_folder_path, 'clang-11.1.0_.tgz')
  if os.path.exists(download_path) == False:
    print_info(f"download {download_path} ...")
    # download_file('compile_tool/clang-11.1.0_.tgz', download_path)
    get_file_cmd = f"cd {download_folder_path} && wget http://bs3-hb1.internal/reco-dragon-matx-so/compile_tool/clang-11.1.0_.tgz && cd -"
    ret_code, _, err = shell_execute(get_file_cmd)
    if ret_code != 0:
      raise Exception("download failed! {}".format(err))
  else:
    print_info(f"{download_path} exists")
    
  clang_folder_path = os.path.join(download_folder_path, 'clang-11.1.0')
  compile_tool_path = os.path.join(clang_folder_path, 'bin/clang++')
  if os.path.exists(compile_tool_path) == False:
    tar_file_cmd = f"cd {download_folder_path} && tar -xzvf clang-11.1.0_.tgz && cd -"
    print_info(f"unzip {download_path} ...")
    ret_code, _, err = shell_execute(tar_file_cmd)
    if ret_code != 0:
      raise Exception("unzip failed! {}".format(err))
  else:
    print_info(f"{compile_tool_path} exists")


def get_std_so_tool(download_folder_path):
  lib_folder_path = os.path.join(download_folder_path, 'gcc-10.3.0/lib64')
  download_path = os.path.join(lib_folder_path, 'libstdc++.so')
  if os.path.exists(lib_folder_path) == False:
    if not os.path.exists(lib_folder_path):
      os.makedirs(lib_folder_path)
    print_info(f"download {download_path} ...")
    download_file('compile_tool/libstdc++.so', download_path)


if __name__ == '__main__':
  get_compile_tool(sys.argv[1])

