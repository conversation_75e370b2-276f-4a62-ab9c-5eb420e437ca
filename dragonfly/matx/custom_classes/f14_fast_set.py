#!/usr/bin/env python3
# coding=utf-8
"""
- filename: f14_fast_set.py
- description: F14FastSet in python
- author: <EMAIL>
- date: 2025-3-27 19:20:00
"""

from typing import ByteString as bytes_view
from builtins import int as uint64
from .ptr_wrapper import PtrWrapper

class Uint64FastSet(PtrWrapper):
  def __init__(self) -> None:
    pass

  def empty(self) -> bool:
    return True
  
  def size(self) -> int:
    return 0
  
  def count(self, value: uint64) -> int:
    return 0
  
  def insert(self, value: uint64) -> bool:
    return False
  
  def erase(self, value: uint64) -> int:
    return 0
  
  def clear(self) -> bool:
    return False


class DoubleFastSet(PtrWrapper):
  def __init__(self) -> None:
    pass

  def empty(self) -> bool:
    return True
  
  def size(self) -> int:
    return 0
  
  def count(self, value: float) -> int:
    return 0
  
  def insert(self, value: float) -> bool:
    return False
  
  def erase(self, value: float) -> int:
    return 0
  
  def clear(self) -> bool:
    return False


class StringFastSet(PtrWrapper):
  def __init__(self) -> None:
    pass

  def empty(self) -> bool:
    return True
  
  def size(self) -> int:
    return 0
  
  def count(self, value: bytes_view) -> int:
    return 0
  
  def insert(self, value: bytes_view) -> bool:
    return False
  
  def erase(self, value: bytes_view) -> int:
    return 0
  
  def clear(self) -> bool:
    return False
