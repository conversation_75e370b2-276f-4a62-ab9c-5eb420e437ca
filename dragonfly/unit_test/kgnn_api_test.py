#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="kgnn" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 设置环境变量: export PYTHONPATH=$PYTHONPATH:./dragon
# 5. 运行单测文件: python3 dragon/dragonfly/unit_test/kgnn_api_test.py
# 6. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.kgnn_api_test.TestFlowFunc.xxxx

import unittest
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.kgnn.kgnn_api_mixin import KgnnApiMixin
from dragonfly.ext.kgnn.node_attr_schema import NodeAttrSchema


# LeafService.I_AM_MASTER_DRIVER = True


class KgnnFlow(LeafFlow, KgnnApiMixin):
  pass


class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_DragonKgnnTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_fetch_kgnn_neighbors(self):
    flow = KgnnFlow(name="test_kgnn_c").fetch_kgnn_neighbors(
        id_from_common_attr="uId",
        save_neighbors_to='uId_U2I_ns',
        kess_service="grpc_kgnn_test-U2I-I2U",
        relation_name='U2I',
        shard_num=1,
        sample_num=5,
        timeout_ms=200,
    )
    leaf = self.__init_service(flow)
    leaf["uId"] = [10009, 10002, 10005]
    leaf.run("test_kgnn_c")

    flow2 = KgnnFlow(name="test_kgnn_i").fetch_kgnn_neighbors(
        id_from_item_attr="uId",
        save_neighbors_to='uId_x',
        kess_service="grpc_kgnn_test-U2I-I2U",
        relation_name='U2I',
        shard_num=1,
        sample_num=3,
        timeout_ms=200,
    )
    leaf = self.__init_service(flow2)
    for i in range(1, 4):
      item = leaf.add_item(i)
      item["uId"] = [10000 + i] * i
    leaf.run('test_kgnn_i')

    flow3 = KgnnFlow(name="test_kgnn_c2").fetch_kgnn_neighbors(
        id_from_common_attr="uId",
        save_neighbors_to='uId_U2I_ns',
        model_name="colossusdb_kgnn_test",
        table_name='test_table',
        sample_num=5,
        timeout_ms=200,
    )
    leaf = self.__init_service(flow3)
    leaf["uId"] = [10000 + i for i in range(1, 4)]
    leaf.run("test_kgnn_c2")
    # print(leaf["uId"])
    # print(leaf["uId_U2I_ns"])

    flow4 = KgnnFlow(name="test_kgnn_i2").fetch_kgnn_neighbors(
        id_from_item_attr="uId",
        save_neighbors_to='uId_x',
        model_name="colossusdb_kgnn_test",
        table_name='test_table',
        sample_num=3,
        timeout_ms=200,
    )
    leaf = self.__init_service(flow4)
    for i in range(1, 4):
      item = leaf.add_item(i)
      item["uId"] = [10000 + i] * i
    item = leaf.add_item(4)
    item["uId"] = [100000000]
    leaf.run('test_kgnn_i2')
    # for item in leaf.items: 	#获取 item 结果集
    #   print(f"item_id: {item.item_id}")
    #   print(item["uId"])
    #   print(item["uId_x"])

  def test_random_sample(self):
    flow = KgnnFlow(name="test_kgnn_random_sample_common_attr")\
     .random_sample(
       id_from_common_attr="uId",
       save_neighbors_to='uId_Random_common',
       kess_service="grpc_kgnn_test-U2I-I2U",
       relation_name='U2I',
       shard_num=1,
       sample_num=5,
       timeout_ms=200)

    leaf = self.__init_service(flow)
    leaf["uId"] = [1, 2, 10]
    leaf.run("test_kgnn_random_sample_common_attr")

    flow2 = KgnnFlow(name="test_kgnn_random_sample_item_attr")\
     .random_sample(
       id_from_item_attr="uId",
       save_neighbors_to='uId_Random_item',
       kess_service="grpc_kgnn_test-U2I-I2U",
       relation_name='U2I',
       shard_num=1,
       sample_num=3,
       timeout_ms=200)

    leaf2 = self.__init_service(flow2)
    for i in range(1, 4):
      item = leaf2.add_item(i)
      item["uId"] = [i] * i
    leaf2.run('test_kgnn_random_sample_item_attr')

    flow3 = KgnnFlow(name="test_kgnn_random_sample_fix_count")\
     .random_sample(
       save_neighbors_to='uId_Random_fix_count',
       kess_service="grpc_kgnn_test-U2I-I2U",
       relation_name='U2I',
       shard_num=1,
       sample_num=11,
       timeout_ms=200)

    leaf3 = self.__init_service(flow3)
    leaf3.run("test_kgnn_random_sample_fix_count")

    flow4 = KgnnFlow(name="test_kgnn_random_sample_common_attr2")\
     .random_sample(
       id_from_common_attr="uId",
       save_neighbors_to='uId_Random_common',
       model_name="colossusdb_kgnn_test",
       table_name='test_table',
       sample_num=5,
       timeout_ms=200)

    leaf4 = self.__init_service(flow4)
    leaf4["uId"] = [1, 2, 10]
    leaf4.run("test_kgnn_random_sample_common_attr2")
    # print(leaf4["uId"])
    # print(leaf4["uId_Random_common"])

    flow5 = KgnnFlow(name="test_kgnn_random_sample_item_attr2")\
     .random_sample(
       id_from_item_attr="uId",
       save_neighbors_to='uId_Random_item',
       model_name="colossusdb_kgnn_test",
       table_name='test_table',
       sample_num=3,
       timeout_ms=200)

    leaf5 = self.__init_service(flow5)
    for i in range(1, 4):
      item = leaf5.add_item(i)
      item["uId"] = [i] * i
    leaf5.run('test_kgnn_random_sample_item_attr2')
    # for item in leaf5.items:
    #   print(f"item_id: {item.item_id}")
    #   print(item["uId"])
    #   print(item["uId_Random_item"])

    flow6 = KgnnFlow(name="test_kgnn_random_sample_fix_count2")\
     .random_sample(
       save_neighbors_to='uId_Random_fix_count',
       model_name="colossusdb_kgnn_test",
       table_name='test_table',
       sample_num=11,
       timeout_ms=200)

    leaf6 = self.__init_service(flow6)
    leaf6.run("test_kgnn_random_sample_fix_count2")
    # print(leaf6["uId_Random_fix_count"])

  def test_get_side_info(self):
    flow = KgnnFlow(name="test_kgnn_get_common_attr").get_side_info(
      id_from_common_attr="uId",
      attr_schema=NodeAttrSchema(4,4).add_int64_attr("a1").add_int64_list_attr('a2', 2)\
        .add_float_attr('b1').add_float_list_attr('b2', 3),
      kess_service="grpc_kgnn_test-U2I-I2U",
      relation_name='U2I',
      shard_num=1,
      sample_num=5,
      timeout_ms=200,
    )
    leaf = self.__init_service(flow)
    leaf["uId"] = [1, 2, 3]
    leaf.run("test_kgnn_get_common_attr")

    flow2 = KgnnFlow(name="test_kgnn_get_common_attr2").get_side_info(
      id_from_item_attr="pId",
      attr_schema=NodeAttrSchema(4,4).add_int64_attr("a1").add_int64_list_attr('a2', 2)\
        .add_float_attr('b1').add_float_list_attr('b2', 3),
      kess_service="grpc_kgnn_test-U2I-I2U",
      relation_name='I2U',
      shard_num=1,
      sample_num=5,
      timeout_ms=200,
    )
    leaf = self.__init_service(flow2)
    for i in range(1, 4):
      item = leaf.add_item(i)
      item["pId"] = [10000 + i] * i
    leaf.run("test_kgnn_get_common_attr2")

    flow3 = KgnnFlow(name="test_kgnn_get_common_attr3").get_side_info(
      id_from_common_attr="uId",
      # attr_schema=NodeAttrSchema(4,4).add_int64_attr("a1").add_int64_list_attr('a2', 2)\
      #   .add_float_attr('b1').add_float_list_attr('b2', 3),
      attr_schema=NodeAttrSchema(4,0).add_int64_attr("a1").add_int64_list_attr('a2', 3)\
        .set_degree("degree").set_edge_num("edge_num"),
      model_name="colossusdb_kgnn_test",
      table_name='test_table',
      sample_num=5,
      timeout_ms=200,
    )
    leaf = self.__init_service(flow3)
    leaf["uId"] = [1, 2, 3]
    leaf.run("test_kgnn_get_common_attr3")
    # print(leaf["uId"])
    # print(leaf["a1"])
    # print(leaf["a2"])
    # print(leaf["degree"])
    # print(leaf["edge_num"])

    flow4 = KgnnFlow(name="test_kgnn_get_common_attr4").get_side_info(
      id_from_item_attr="pId",
      # attr_schema=NodeAttrSchema(4,4).add_int64_attr("a1").add_int64_list_attr('a2', 2)\
      #   .add_float_attr('b1').add_float_list_attr('b2', 3),
      attr_schema=NodeAttrSchema(4,0).add_int64_attr("a1").add_int64_list_attr('a2', 3)\
        .set_degree("degree").set_edge_num("edge_num"),
      model_name="colossusdb_kgnn_test",
      table_name='test_table',
      sample_num=5,
      timeout_ms=200,
    )
    leaf = self.__init_service(flow4)
    for i in range(1, 4):
      item = leaf.add_item(i)
      item["pId"] = [10000 + i] * i
    leaf.run("test_kgnn_get_common_attr4")
    # for item in leaf.items:
    #   print(f"item_id: {item.item_id}")
    #   print(item["pId"])
    #   print(item["a1"])
    #   print(item["a2"])
    #   print(item["degree"])
    #   print(item["edge_num"])

  def test_update_si2dc(self):
    # common attr -> common attr
    flow = KgnnFlow(name="update_si2dc_1")\
     .update_si2dc(
       src_attr="pId",
       dst_attr="uId",
       src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('Y', 3).add_float_attr("w"),
       src_attr_filter='click',
       kess_service="grpc_kgnn_test-U2I-I2U",
       relation_name='I2U',
       shard_num=1,
       timeout_ms=200,
       timestamp_attr='ts',
       dst_weight_attr='weight',
       dst_w_is_common_attr=True)

    # 测试: src list + filter + src_node 共享 + weight 来自 common + weight 长度一致
    leaf = self.__init_service(flow)
    leaf["uId"] = [1, 4]
    leaf["weight"] = [0.2, 0.5]
    for i in range(1, 4):
      item = leaf.add_item(i)
      item['pId'] = [120 + i, 580 + i, 960 + i]
      item['click'] = (i != 3)
      item['Y'] = [2 + i, 3 + i, 4 + i]
      item['w'] = 0.3 * i

    leaf.run("update_si2dc_1")

    flow2 = KgnnFlow(name="update_si2dc_v2")\
     .update_si2dc(
       src_attr="pId",
       dst_attr="uId",
       src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('Y', 3).add_float_attr("w"),
       src_attr_filter='click',
       table_name='test_table',
       btq_prefix='test_btq_',
       btq_shard_num=4,
       timeout_ms=200,
       timestamp_attr='ts',
       dst_weight_attr='weight',
       dst_w_is_common_attr=True)

    # 测试: src list + filter + src_node 共享 + weight 来自 common + weight 长度一致
    leaf = self.__init_service(flow2)
    leaf["uId"] = [1, 4]
    leaf["weight"] = [0.2, 0.5]
    for i in range(1, 4):
      item = leaf.add_item(i)
      item['pId'] = [120 + i, 580 + i, 960 + i]
      item['click'] = (i != 3)
      item['Y'] = [2 + i, 3 + i, 4 + i]
      item['w'] = 0.3 * i

    leaf.run("update_si2dc_v2")

  def test_update_sc2di(self):
    # common attr -> common attr
    flow = KgnnFlow(name="update_sc2di_1")\
     .update_sc2di(
       src_attr="uId",
       dst_attr="pId",
       src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('Y', 3).add_float_attr("w"),
       dst_attr_click='click',
       kess_service="grpc_kgnn_test-U2I-I2U",
       relation_name='U2I',
       shard_num=1,
       timeout_ms=200,
       timestamp_attr='ts',
       dst_weight_attr='weight',
       dst_w_is_common_attr=True)

    # 测试: src list + filter + side info + weight 来自 common + weight 长度一致
    leaf = self.__init_service(flow)
    leaf["uId"] = [1231, 1232]
    leaf["Y"] = [2, 3, 4]
    leaf["w"] = 0.4
    leaf["weight"] = [0.2, 0.5]
    for i in range(1, 4):
      item = leaf.add_item(i)
      item['pId'] = [120 + i, 580 + i, 960 + i]
      item['click'] = (i != 3)

    leaf.run("update_sc2di_1")

    flow2 = KgnnFlow(name="update_sc2di_v2")\
     .update_sc2di(
       src_attr="uId",
       dst_attr="pId",
       src_node_attr=NodeAttrSchema(4, 4).add_int64_list_attr('Y', 3).add_float_attr("w"),
       dst_attr_click='click',
       table_name='test_table',
       btq_prefix='test_btq_',
       btq_shard_num=4,
       timeout_ms=200,
       timestamp_attr='ts',
       dst_weight_attr='weight',
       dst_w_is_common_attr=True)

    # 测试: src list + filter + side info + weight 来自 common + weight 长度一致
    leaf = self.__init_service(flow2)
    leaf["uId"] = [1231, 1232]
    leaf["Y"] = [2, 3, 4]
    leaf["w"] = 0.4
    leaf["weight"] = [0.2, 0.5]
    for i in range(1, 4):
      item = leaf.add_item(i)
      item['pId'] = [120 + i, 580 + i, 960 + i]
      item['click'] = (i != 3)

    leaf.run("update_sc2di_v2")

  def test_batch_dispatch(self):
    pass

  def test_iter_nodes(self):
    pass

  def test_batch_2hop_sample(self):
    pass

  def test_get_edge_info(self):
    pass

  def test_kgnn_raw_key(self):
    pass

if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('kgnn_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
