#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6  (若用 gcc10 编译则将 gcc-8.3.0 改为 gcc-10.3.0)
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/common_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.common_api_test.TestFlowFunc.test_xxx

import copy
import os
import re
import tempfile
from typing import Optional
import unittest
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.common_leaf_processor import LeafMixerEmbedFLow, embedded_flow
from dragonfly.common_leaf_util import LogicError, set_env, strict_types
from dragonfly.modular.module import module


class EmbeddedPipelineMixer(LeafMixerEmbedFLow):
  @strict_types
  def __init__(self, config: dict):
    config.update({"type_name": "CommonRecoEmbeddedPipelineMixer"})
    super(EmbeddedPipelineMixer, self).__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "embedded_pipeline_test"


class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  @classmethod
  def __init_service(
      cls,
      flow,
      *,
      common_attrs_from_request: Optional[list] = None,
      ignore_unused_attr: Optional[list] = None):
    cls.__temp_service = copy.deepcopy(cls.__service)
    cls.__temp_service.common_attrs_from_request = common_attrs_from_request or []  # 必须非 None 才能启用无源 attr 检查
    cls.__temp_service.add_leaf_flows(leaf_flows=[flow], request_type="default")
    if ignore_unused_attr:
      backup_ignore_unused_attr = LeafService.IGNORE_UNUSED_ATTR
      LeafService.IGNORE_UNUSED_ATTR = ignore_unused_attr

    with tempfile.NamedTemporaryFile(suffix=".json") as f:
      cls.__temp_service.build(output_file=f.name)

    if ignore_unused_attr:
      LeafService.IGNORE_UNUSED_ATTR = backup_ignore_unused_attr

    return cls.__temp_service.executor()

  def test_basic(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_basic")
      (flow
        ._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1"]
        )))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["x"])
      leaf.run("test_basic")
      self.assertEqual(leaf["x"], 1)

  def test_no_source_common_attr_detection(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .log_debug_info(common_attrs=["x"])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_no_source_common_attr_detection")
      (flow
        ._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1"]
        )))
      )

      with self.assertRaisesRegex(LogicError, re.compile(r"在以上流程中无法找到以下 common_attr 来源: \['x'\]")):
        leaf = self.__init_service(flow)
      leaf = self.__init_service(flow, common_attrs_from_request=['x'])
      leaf.run("test_no_source_common_attr_detection")

  def test_dep_from_outside_to_embed(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .copy_attr(attrs=[dict(from_common="x", to_common="y")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_dep_from_outside_to_embed")
      (flow
        .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        .do(flow._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1"]
        ))))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["y"])
      leaf.run("test_dep_from_outside_to_embed")
      self.assertEqual(leaf["y"], 1)

  def test_dep_from_embed_to_outside(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_dep_from_embed_to_outside")
      (flow
        .do(flow._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1"]
        ))))
        .copy_attr(attrs=[dict(from_common="x", to_common="y")])
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["y"])
      leaf.run("test_dep_from_embed_to_outside")
      self.assertEqual(leaf["y"], 1)

  def test_embedded_flow_incorrect_ordering(self):
    class TestMixer(EmbeddedPipelineMixer):
      def embedded_flow_order(self) -> list:
        return ["ef2", "ef1"]

      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        )

      @embedded_flow
      def ef2(flow: LeafFlow):
        (flow
          .copy_attr(attrs=[dict(from_common="x", to_common="y")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_embedded_flow_incorrect_ordering")
      (flow
        .do(flow._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1", "ef2"]
        ))))
      )

      with self.assertRaisesRegex(LogicError, re.compile(r"在以上流程中无法找到以下 common_attr 来源: \['x'\]")):
        self.__init_service(flow, ignore_unused_attr=["x", "y"])

  def test_embedded_flow_ordering(self):
    class TestMixer(EmbeddedPipelineMixer):
      def embedded_flow_order(self) -> list:
        return ["ef1", "ef2"]

      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        )

      @embedded_flow
      def ef2(flow: LeafFlow):
        (flow
          .copy_attr(attrs=[dict(from_common="x", to_common="y")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_embedded_flow_ordering")
      (flow
        .do(flow._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1", "ef2"]
        ))))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["y"])
      leaf.run("test_embedded_flow_ordering")
      self.assertEqual(leaf["y"], 1)

  def test_should_not_inherit_main_table(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(item_attrs=[dict(name="x", value=1, type="int")])
        )

      @embedded_flow
      def ef2(flow: LeafFlow):
        (flow
          .fake_retrieve(item_table='table0', num=1)
          .set_attr_value(item_table='table0', item_attrs=[dict(name="x", value=666, type="int")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_should_not_inherit_main_table")
      (flow
        .fake_retrieve(num=1)
        .do(flow._add_processor(TestMixer(dict(
          item_table='table0',
          run_embedded_pipelines=["ef1", "ef2"]
        ))))
        .copy_attr(attrs=[dict(from_item='x', to_item='y')])
        .copy_attr(item_table='table0', attrs=[dict(from_item='x', to_item='y')])
      )

      leaf = self.__init_service(flow, ignore_unused_attr=['y'])
      leaf.run("test_should_not_inherit_main_table")
      self.assertEqual(leaf.items[0]["y"], 1)
      leaf.set_main_table('table0')
      self.assertEqual(leaf.items[0]["y"], 666)

  def test_early_exit(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
          .return_(2)
        )

      @embedded_flow
      def ef2(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=2, type="int")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_early_abort")
      (flow
        .do(flow._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1", "ef2"]
        ))))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=['x'])
      leaf.run("test_early_abort")
      self.assertEqual(leaf["x"], 1)

  def test_dep_from_processor_to_embed(self):
    class TestMixer(EmbeddedPipelineMixer):
      @property
      @strict_types
      def output_common_attrs(self) -> set:
        return {'x'}

      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .copy_attr(attrs=[dict(from_common="x", to_common="y")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_dep_from_processor_to_embed")
      (flow
        ._add_processor(TestMixer(dict(
          run_embedded_pipelines=["ef1"]
        )))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["y"])

  def test_inherit_ns(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow
      def ef1(flow: LeafFlow):
        (flow
          .set_attr_value(common_attrs=[dict(name="x", value=1, type="int")])
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_inherit_ns")
      with flow:
        with module("main"):
          (flow
            ._add_processor(TestMixer(dict(
              run_embedded_pipelines=["ef1"]
            )))
          )

      leaf = self.__init_service(flow, ignore_unused_attr=["x"])
      pipelines = self.__temp_service._pipeline_manager_config["pipeline_map"]
      processor_name = pipelines["test_inherit_ns.TestMixer.ef1"]["pipeline"][0]
      self.assertTrue(processor_name.startswith("main::TestMixer::ef1::set_attr_value"))
      leaf.run("test_inherit_ns")
      self.assertEqual(leaf["x"], 1)

  def test_instance_wise(self):
    class TestMixer(EmbeddedPipelineMixer):
      @embedded_flow(instance_wise=True)
      def ef1(self, flow: LeafFlow):
        (flow
          .set_attr_value(
            common_attrs=[dict(
              name=self._config["write_attr_name"],
              value=self._config["write_attr_value"],
              type="int",
            )]
          )
        )

    with set_env(CHECK_TALBE_DEPENDENCY='true', ENABLE_EMBEDDED_FLOW='true'):
      flow = LeafFlow(name="test_instance_wise")
      (flow
        ._add_processor(TestMixer(dict(
          name="test_mixer1",
          write_attr_name='xyz',
          write_attr_value=233,
          run_embedded_pipelines=["ef1"]
        )))
      )

      leaf = self.__init_service(flow, ignore_unused_attr=["xyz"])
      leaf.run("test_instance_wise")
      self.assertEqual(leaf["xyz"], 233)


if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('embedded_pipeline_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
