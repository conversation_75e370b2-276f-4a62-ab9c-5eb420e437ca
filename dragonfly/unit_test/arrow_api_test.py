#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="arrow" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD="./build_tools/prebuilt/lib/libjemallocx.so:./build_tools/gcc-8.3.0/lib64/libstdc++.so.6"
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/arrow_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.arrow_api_test.TestFlowFunc.xxxx

import unittest
import math
import random
import json
import time
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.arrow.arrow_api_mixin import ArrowApiMixin


class OfflineFlow(LeafFlow, ArrowApiMixin):
  pass


# LeafService.I_AM_MASTER_DRIVER = True

class TestFlowFunc(unittest.TestCase):
  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  def __init_service(self, *flow):
    self.__service = LeafService(kess_name="grpc_CommonLeafTest") \
      .register_proto(
        "temp.proto",
        """
          syntax = "proto3";
          package ks.reco;

          message TempMessage {
            double score1 = 1;
            double score2 = 2;
          }""") \
      .register_proto(
        "temp2.proto",
        """
          syntax = "proto3";
          package ks.reco;

          message TempMessageSecond {
            int64 id = 1;
            TempMessage msg = 2;
          }""",
        ["temp.proto"]) \
      .register_proto(
        "test_enum.proto",
        """
          syntax = "proto3";
          package ks.reco;
          message TestEnum {
            enum FrontRequestType {
              UNKNOWN_REQUEST    = 0;
              INFEED_REQUEST     = 1;
              SPLASH_REQUEST     = 2;
            }
          }""") \
      .register_proto(
        "test_sub_message.proto",
        """
            syntax = "proto3";
            package ks.reco;
            message SubTestMessage {
              bool bool_type = 1;
              TestEnum.FrontRequestType enum_type = 2;
              int32 int32_type = 3;
              uint32 uint32_type = 4;
              int64 int64_type = 5;
              uint64 uint64_type = 6;
              float float_type = 7;
              double double_type = 8;
              string string_type = 9;
              repeated bool bool_type_r = 10;
              repeated TestEnum.FrontRequestType enum_type_r = 11;
              repeated int32 int32_type_r = 12;
              repeated uint32 uint32_type_r = 13;
              repeated int64 int64_type_r = 14;
              repeated uint64 uint64_type_r = 15;
              repeated float float_type_r = 16;
              repeated double double_type_r = 17;
              repeated string string_type_r = 18;
            }""",
        ["test_enum.proto"])  \
      .register_proto(
        "test_build_from_protobuf_message.proto",
        """
          syntax = "proto3";
          package ks.reco;
          message TestBuildFromProtobufMessage {
            SubTestMessage sub_message = 1;
            repeated SubTestMessage sub_message_r = 2;
          }""",
        ["test_enum.proto", "test_sub_message.proto"])
    self.__service.add_leaf_flows(leaf_flows=list(flow))
    return self.__service.executor()

  def test_build_arrow_record_batch(self):
    dump_flow = OfflineFlow(name="build_record_batch") \
        .build_arrow_record_batch(
            common_attrs_from=["did", "time_ms", "ca0", "ca1", "ca2"],
            common_slots_from=["csl"],
            common_signs_from=["csi"],
            item_attrs_from=["pid", "ia"],
            item_slots_from=["isl"],
            item_signs_from=["isi"],
            common_attr_to="rb",
            utf8_attr_names=["ca2"]) \

    retrieve_flow = OfflineFlow(name="read_record_batch") \
      .retrieve_from_arrow_record_batch(
          record_batch_from_attr="rb",
          id_feature="pid",
          device_id_feature="did",
          request_time_feature="time_ms",
          extract_common_features=["did", "time_ms", "ca0", "ca1", "ca2", "1", "2"],
          extract_item_features=["pid", "ia", "10", "11"],
          prefix="new_",
        )
    leaf = self.__init_service(dump_flow, retrieve_flow)
    did, time_ms, ca0, ca1, ca2, csl, csi = "did", 1702352243000, 4.4, [1, 2], ["s1", "s2"], [1, 1, 2], [10, 11, 20]
    pid, ia, isl, isi = 101, 1.2345, [10, 11, 11], [100, 110, 111]
    leaf["did"] = did
    leaf["time_ms"] = time_ms
    leaf["ca0"] = ca0
    leaf["ca1"] = ca1
    leaf["ca2"] = ca2
    leaf["csl"] = csl
    leaf["csi"] = csi
    item = leaf.add_item(pid)
    item["pid"] = pid
    item["ia"] = ia
    item["isl"] = isl
    item["isi"] = isi
    leaf.run("build_record_batch")
    leaf.run("read_record_batch")
    self.assertEqual(leaf["new_did"], did)
    self.assertEqual(leaf["new_time_ms"], time_ms)
    self.assertAlmostEqual(leaf["new_ca0"], ca0, places=5)
    self.assertListEqual(leaf["new_ca1"], ca1)
    self.assertListEqual(leaf["new_ca2"], ca2)
    self.assertListEqual(leaf["new_1"], [10, 11])
    self.assertListEqual(leaf["new_2"], [20])
    items = leaf.items
    self.assertEqual(len(items), 2)
    item = items[-1]
    self.assertEqual(item["new_pid"], pid)
    self.assertAlmostEqual(item["new_ia"], ia, places=5)
    self.assertListEqual(item["new_10"], [100])
    self.assertListEqual(item["new_11"], [110, 111])

  def test_retrieve_from_arrow_record_batch(self):
    common_attrs_from = ["did", "time_ms", "ca0", "ca1", "ca2"]
    common_slots_from = ["csl"]
    common_signs_from = ["csi"]
    item_attrs_from = ["pid", "ia"]
    item_slots_from = ["isl"]
    item_signs_from = ["isi"]
    utf8_attr_names = ["ca2"]
    dump_flow = OfflineFlow(name="build_record_batch") \
        .build_arrow_record_batch(
            common_attrs_from="{{common_attrs_from}}",
            common_slots_from="{{common_slots_from}}",
            common_signs_from="{{common_signs_from}}",
            item_attrs_from="{{item_attrs_from}}",
            item_slots_from="{{item_slots_from}}",
            item_signs_from="{{item_signs_from}}",
            common_attr_to="rb",
            utf8_attr_names="{{utf8_attr_names}}") \

    extract_common_features = ["did", "time_ms", "ca0", "ca1", "ca2", "1", "2"]
    extract_item_features = ["pid", "ia", "10", "11"]
    retrieve_flow = OfflineFlow(name="read_record_batch") \
      .log_arrow_record_batch(
          record_batch_from_attr="rb",
          ) \
      .retrieve_from_arrow_record_batch(
          record_batch_from_attr="rb",
          id_feature="pid",
          device_id_feature="did",
          request_time_feature="time_ms",
          extract_common_features="{{extract_common_features}}",
          extract_item_features="{{extract_item_features}}",
          prefix="new2_",
        )
    leaf = self.__init_service(dump_flow, retrieve_flow)
    did, time_ms, ca0, ca1, ca2, csl, csi = "did", 1702352243000, 4.4, [1, 2], ["s1", "s2"], [1, 1, 2], [10, 11, 20]
    pid, ia, isl, isi = 101, 1.2345, [10, 11, 11], [100, 110, 111]
    leaf["did"] = did
    leaf["time_ms"] = time_ms
    leaf["ca0"] = ca0
    leaf["ca1"] = ca1
    leaf["ca2"] = ca2
    leaf["csl"] = csl
    leaf["csi"] = csi
    item = leaf.add_item(pid)
    item["pid"] = pid
    item["ia"] = ia
    item["isl"] = isl
    item["isi"] = isi
    # set dynamic arguments
    leaf["common_attrs_from"] = common_attrs_from
    leaf["common_slots_from"] = common_slots_from
    leaf["common_signs_from"] = common_signs_from
    leaf["item_attrs_from"] = item_attrs_from
    leaf["item_slots_from"] = item_slots_from
    leaf["item_signs_from"] = item_signs_from
    leaf["utf8_attr_names"] = utf8_attr_names
    leaf["extract_common_features"] = extract_common_features
    leaf["extract_item_features"] = extract_item_features
    # run
    leaf.run("build_record_batch")
    leaf.run("read_record_batch")
    self.assertEqual(leaf["new2_did"], did)
    self.assertEqual(leaf["new2_time_ms"], time_ms)
    self.assertAlmostEqual(leaf["new2_ca0"], ca0, places=5)
    self.assertListEqual(leaf["new2_ca1"], ca1)
    self.assertListEqual(leaf["new2_ca2"], ca2)
    self.assertListEqual(leaf["new2_1"], [10, 11])
    self.assertListEqual(leaf["new2_2"], [20])
    items = leaf.items
    self.assertEqual(len(items), 2)
    item = items[-1]
    self.assertEqual(item["new2_pid"], pid)
    self.assertAlmostEqual(item["new2_ia"], ia, places=5)
    self.assertListEqual(item["new2_10"], [100])
    self.assertListEqual(item["new2_11"], [110, 111])

  def test_serde_arrow_record_batch(self):
    build_flow = OfflineFlow(name="build_record_batch") \
      .build_arrow_record_batch(
        common_attrs_from=["did", "time_ms"],
        item_attrs_from=["pid"],
        common_attr_to="rb",
        )
    ser_flow = OfflineFlow(name="ser_record_batch") \
      .serialize_arrow_record_batch(
        record_batch_from_attr="rb",
        common_attr_to="rb_serialized",
        ) \
      .base64(
        mode="encode",
        is_common_attr=True,
        input_attr="rb_serialized",
        output_attr="rb_serialized_b64",
      )
    deser_flow = OfflineFlow(name="deser_record_batch") \
      .base64(
        mode="decode",
        is_common_attr=True,
        input_attr="b64",
        output_attr="rb_ser",
      ) \
      .deserialize_arrow_record_batch(
        record_batch_serialized_attr_from="rb_ser",
        common_attr_to="rb2",
      )
    retrieve_flow = OfflineFlow(name="read_record_batch") \
      .log_arrow_record_batch(
        record_batch_from_attr="rb2",
        ) \
      .retrieve_from_arrow_record_batch(
        record_batch_from_attr="rb2",
        id_feature="pid",
        device_id_feature="did",
        request_time_feature="time_ms",
        extract_common_features=["did", "time_ms"],
        extract_item_features=["pid", ],
        prefix="rb2_",
      )

    leaf = self.__init_service(build_flow, ser_flow, deser_flow, retrieve_flow)
    did, time_ms = "did", 1702352243000
    pid = 101
    leaf["did"] = did
    leaf["time_ms"] = time_ms
    item = leaf.add_item(pid)
    item["pid"] = pid
    leaf.run("build_record_batch")
    leaf.run("ser_record_batch")
    b64 = leaf["rb_serialized_b64"]

    def do_test_once():
      leaf.reset()
      leaf["b64"] = b64
      leaf.run("deser_record_batch")
      leaf.run("read_record_batch")
      self.assertEqual(leaf["rb2_did"], did)
      self.assertEqual(leaf["rb2_time_ms"], time_ms)
      items = leaf.items
      self.assertEqual(len(items), 1)
      item = items[-1]
      self.assertEqual(item["rb2_pid"], pid)

    for i in range(2):
      do_test_once()

if __name__ == '__main__':
  suite = unittest.TestSuite()
  suite.addTests(unittest.TestLoader().loadTestsFromName('arrow_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
