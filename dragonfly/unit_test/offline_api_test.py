#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="offline" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/offline_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.offline_api_test.TestFlowFunc.xxxx

import unittest
import math
import random
import json
import time
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.offline.offline_api_mixin import OfflineApiMixin

class OfflineFlow(LeafFlow, OfflineApiMixin):
  pass

# LeafService.I_AM_MASTER_DRIVER = True

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_lag(self):
    set_flow = OfflineFlow(name="set_clock").lag_set_clock()
    watch_flow = OfflineFlow(name="watch_clock").lag_watch_clock(lag_seconds=4, interval_seconds=1)

    set_leaf = self.__init_service(set_flow)
    watch_leaf = self.__init_service(watch_flow)

    # 启动后台线程
    watch_leaf.request_time = 10 * 1000
    watch_leaf.run_async("watch_clock")

    self.assertFalse(watch_leaf.wait(timeout_ms=2000))

    # 阻塞
    set_leaf.request_time = 1 * 1000
    set_leaf.run("set_clock")

    self.assertFalse(watch_leaf.wait(timeout_ms=2000))

    # 阻塞
    set_leaf.request_time = 11 * 1000
    set_leaf.run("set_clock")

    self.assertFalse(watch_leaf.wait(timeout_ms=2000))

    # 继续运行
    set_leaf.request_time = 15 * 1000
    set_leaf.run("set_clock")

    self.assertTrue(watch_leaf.wait(timeout_ms=2000))

  def test_dispatch_client_feature(self):
    flow = OfflineFlow(name="test_dispatch_client_feature") \
      .dispatch_client_feature(
        input_json_attr="client_feature",
        attrs=[
          dict(item_attr="float_attr", json_field="float_val", type="float"),
          dict(item_attr="int_attr", json_field="int_val", type="int"),
          dict(item_attr="string_attr", json_field="string_val", type="string"),
          dict(item_attr="float_list_attr", json_field="float_list_val", type="float_list"),
          dict(item_attr="int_list_attr", json_field="int_list_val", type="int_list"),
          dict(item_attr="string_list_attr", json_field="string_list_val", type="string_list"),
        ])

    leaf = self.__init_service(flow)

    leaf["client_feature"] = json.dumps({
      "float_val": [[], [0.1], [0.3]],
      "int_val": [[1], [], [3]],
      "string_val": [["alpha"], ["beta"], []],
      "float_list_val": [[], [0.1, 0.5, 0.2], [0.3]],
      "int_list_val": [[1, 2], [], [3, 4]],
      "string_list_val": [["alpha"], ["beta", "gamma"], []],
    })

    items = [leaf.add_item(i) for i in range(3)]

    leaf.run("test_dispatch_client_feature")

    self.assertEqual(items[0]["float_attr"], None)
    self.assertEqual(items[1]["float_attr"], 0.1)
    self.assertEqual(items[2]["float_attr"], 0.3)
    self.assertEqual(items[0]["int_attr"], 1)
    self.assertEqual(items[1]["int_attr"], None)
    self.assertEqual(items[2]["int_attr"], 3)
    self.assertEqual(items[0]["string_attr"], "alpha")
    self.assertEqual(items[1]["string_attr"], "beta")
    self.assertEqual(items[2]["string_attr"], None)

    self.assertEqual(items[0]["float_list_attr"], None)
    self.assertEqual(items[1]["float_list_attr"], [0.1, 0.5, 0.2])
    self.assertEqual(items[2]["float_list_attr"], [0.3])
    self.assertEqual(items[0]["int_list_attr"], [1, 2])
    self.assertEqual(items[1]["int_list_attr"], None)
    self.assertEqual(items[2]["int_list_attr"], [3, 4])
    self.assertEqual(items[0]["string_list_attr"], ["alpha"])
    self.assertEqual(items[1]["string_list_attr"], ["beta", "gamma"])
    self.assertEqual(items[2]["string_list_attr"], None)

  def test_extract_with_statistic_dense_feature(self):
    request_time = int(time.time())
    mock_user_match_list = [
        {"value": 1, "time": request_time - 1*60},
        {"value": 2, "time": request_time - 3*60},
        {"value": 1, "time": request_time - 5*60},
        {"value": 3, "time": request_time - 60*60},
        {"value": 5, "time": request_time - 1*12*3600},
        {"value": 3, "time": request_time - 1*24*3600},
        {"value": 1, "time": request_time - 7*24*3600},
        {"value": 5, "time": request_time - 15*24*3600},
        {"value": 5, "time": request_time - 30*24*3600},
        {"value": 1, "time": request_time - 90*24*3600},
    ]
    action_length = len(mock_user_match_list)
    action_item_path_list = [
        "author_id",
        "mmu_cluster_id",
        "mmu_cluster_v2_id",
        "mmu_primary_tag_id"
    ]

    mock_photo_match_list = [
        {"value": 1},
        {"value": 2},
        {"value": 3},
        {"value": 5},
    ]

    photo_info_path_list = [
    "author.common.user_id",
    "mmu_info.cluster_id",
    "mmu_info.cluster_v2_id",
    "mmu_info.primary_tag_id"
    ]
    trinity_mmu_name_list = [
        "p_ksib_mmu_primary_tag_id",
        "p_ksib_mmu_second_tag_id",
        "p_ksib_m32_cluster_id_v2",
        "p_ksib_m128_cluster_id_v2",
        "p_ksib_m1000_cluster_id_v2",
        "p_ksib_m4000_cluster_id_v2"
    ]

    flow = OfflineFlow(name="test_extract_with_statistic_dense_feature")
    # 构造 reader_info pb
    for i in range(action_length):
      flow\
        .build_sample_attr(
          is_common_attr=True,
          mappings=[
              dict(from_attr=f"user_value_{i}", to_attr=f"user_value_attr_{m}_{i}", rename=m) 
              for m in trinity_mmu_name_list]
        )\
        .build_protobuf(
          class_name="ksib::reco::ActionItem",
          inputs=
              [dict(common_attr=f"user_value_{i}", path=p) for p in action_item_path_list]
              + [dict(common_attr=f"user_time_{i}", path="time_ms")]
              + [dict(common_attr=f"user_value_attr_{m}_{i}", path="common_attrs", append=True) 
                  for m in trinity_mmu_name_list],
          output_common_attr=f"action_item_{i}"
        )
    flow\
      .build_protobuf(
        class_name="ksib::reco::ReaderInfo",
        inputs=[
            dict(common_attr=f"action_item_{i}", path="user_profile_v1.real_show_list", append=True)
            for i in range(action_length)
        ],
        output_common_attr="reader_info"
      )\

    # 构造 photo_info pb
    flow\
      .build_sample_attr(
        is_common_attr=False,
        mappings=[dict(from_attr="photo_value", to_attr=f"photo_value_attr_{mmu_name}", rename=mmu_name)
            for mmu_name in trinity_mmu_name_list]
      )\
      .build_protobuf(
        class_name="ksib::reco::PhotoInfo",
        inputs=[dict(item_attr="photo_value", path=p) for p in photo_info_path_list]
            + [dict(item_attr=f"photo_value_attr_{m}", path="item_attrs", append=True)
                for m in trinity_mmu_name_list],
        output_item_attr="photo_info"
      )
    # 执行
    flow\
      .extract_with_statistic_dense_feature(
        name="extract_with_statistic_dense_feature",
        item_statistic_dense_feature_output=[],
        reader_info_attr='reader_info',
        photo_info_attr='photo_info'
      )
    
    leaf = self.__init_service(flow)
    leaf.request_time = request_time * 1000
    for i in range(len(mock_user_match_list)):
        leaf[f"user_value_{i}"] = mock_user_match_list[i]["value"]
        leaf[f"user_time_{i}"] = mock_user_match_list[i]["time"] * 1000

    for i in range(len(mock_photo_match_list)):
      item = leaf.add_item(i)
      item["photo_value"] = mock_photo_match_list[i]["value"]

    leaf.run("test_extract_with_statistic_dense_feature")

    # check
    items = leaf.items
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_last1"], 1)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_last5"], 2)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_last20"], 4)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_last100"], 4)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_last200"], 4)
    self.assertEqual(items[0]["ds_upm_realshow_mmuclusterv2id_1min"], 1)
    self.assertEqual(items[0]["ds_upm_realshow_mmuclusterv2id_5min"], 2)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_1h"], 2)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_1d"], 2)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_7d"], 3)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_30d"], 3)
    self.assertEqual(items[0]["ds_upm_realshow_mmuprimarytagid_90d"], 4)

    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_last1"], 0)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_last5"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_last20"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_last100"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_last200"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuclusterv2id_1min"], 0)
    self.assertEqual(items[1]["ds_upm_realshow_mmuclusterv2id_5min"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_1h"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_1d"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_7d"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_30d"], 1)
    self.assertEqual(items[1]["ds_upm_realshow_mmuprimarytagid_90d"], 1)

  def test_ksib_reshape_attrs(self):
    reshape_configs = {
      "common": [
        {
          "input": "common_a_in",
          "output": "common_a_out",
        },
        {
          "input": "common_b_in",
          "output": "common_b_out"
        }
      ],
      "item": [
        {
          "input": "item_a_in",
          "output": "item_a_out"
        },
        {
          "input": "item_b_in",
          "output": "item_b_out"
        }
      ],
      "input_data": [1, 1.0, 1, None],
      "output_data": [1, 1.0, 1.0, 0.0],
      "type_data": [1, 2, 3, 2]
    }

    for idx in range(len(reshape_configs['input_data'])):
      common_config = reshape_configs['common']
      item_config = reshape_configs['item']
      input_data = reshape_configs['input_data'][idx]
      output_data = reshape_configs['output_data'][idx]

      flow = OfflineFlow(name=f"test_ksib_reshape_attrs_{idx}")
      flow\
        .ksib_reshape_attrs(
          name=f"ksib_reshape_attrs_common_{idx}",
          is_common_attr=True,
          reshape_type=reshape_configs['type_data'][idx],
          reshape_attrs=common_config
        )\
        .ksib_reshape_attrs(
          name=f"ksib_reshape_attrs_item_{idx}",
          is_common_attr=False,
          reshape_type=reshape_configs['type_data'][idx],
          reshape_attrs=item_config
        )
      
      leaf = self.__init_service(flow)
      for i in range(len(common_config)):
          if input_data:
            leaf[common_config[i]['input']] = (i + 1) * input_data

      for i in range(3):
        item = leaf.add_item(i)
        for j in range(len(item_config)):
          if input_data:
            item[item_config[j]['input']] = (j + 1) * input_data

      leaf.run(f"test_ksib_reshape_attrs_{idx}")

      # check
      for i in range(len(common_config)):
        out = leaf[common_config[i]['output']]
        ref = [(i + 1) * output_data]
        self.assertEqual(out, ref)
        self.assertEqual([type(i) for i in out], [type(i) for i in ref])

      items = leaf.items
      for item in items:
        for i in range(len(item_config)):
          out = item[item_config[i]['output']]
          ref = [(i + 1) * output_data]
          self.assertEqual(out, ref)
          self.assertEqual([type(i) for i in out], [type(i) for i in ref])

  def test_ksib_reco_log_convert(self):
    flow = OfflineFlow(name="ksib_reco_log_convert_test") \
        .build_protobuf(
          class_name="ksib::reco::ReaderInfo",
          inputs=[
              dict(common_attr="is_slide", path="is_slide")
          ],
          output_common_attr="reader_info"
        )\
        .build_protobuf(
          class_name="ksib::reco::PhotoInfo",
          inputs=[
              dict(common_attr="bucket", path="bucket")
          ],
          output_common_attr="photo_info"
        )\
        .ksib_reco_log_convert( # adapt snack data
          reader_info_attr="reader_info",
          photo_info_attr="photo_info",
          context_info_attr="context_info",
          new_user_threshold_attr="new_user_threshold",
        ) \
        .enrich_with_protobuf(
          name="attr_from_reader_info",
          from_extra_var="reader_info",
          is_common_attr=True,
          attrs=[
              dict(path="is_slide", name="convert_is_slide"),
          ]
        )

    leaf = self.__init_service(flow)
    leaf['is_slide'] = 0
    leaf.run("ksib_reco_log_convert_test")
    self.assertEqual(leaf['convert_is_slide'], 1)

  def test_retrieve_from_ksib_reco_log(self):
    flow = OfflineFlow(name="retrieve_from_ksib_reco_log_test") \
      .build_protobuf(
        class_name="ksib::reco::ReaderInfo",
        inputs=[
            dict(common_attr="is_slide", path="is_slide")
        ],
        output_common_attr="reader_info"
      )
    for i in range(3):
      flow.gen_common_attr_by_lua(
          attr_map={
            "photo_id": f"{100 + i}",
            "bucket": f"{i}",
          }
        )\
        .build_protobuf(
          class_name="ksib::reco::RecoPhotoInfo",
          inputs=[
              dict(common_attr="photo_id", path="photo_info.photo_id"),
              dict(common_attr="bucket", path="photo_info.bucket")
          ],
          output_common_attr=f"photo_info_{i}"
        )
    flow.build_protobuf(
        class_name="ksib::reco::FullRecoLog",
        inputs=[
            dict(common_attr="reader_info", path="reader")
        ] + [ dict(common_attr=f"photo_info_{i}", path="reco_photo_info", append=True) for i in range(3)],
        output_common_attr="ksib_reco_log"
      )\
      .retrieve_from_ksib_reco_log(
        name="retr_from_reco_log",
        from_extra_var="ksib_reco_log",
        #TODO: 对 snack 是否有影响？
        item_key_with_bucket=True,
        save_reco_photo_to="reco_photo_info",
      )
    leaf = self.__init_service(flow)
    leaf.run("retrieve_from_ksib_reco_log_test")
    for i in range(3):
      self.assertEqual(leaf.items[i].item_id, 100 + i)

  def test_extract_cross_dense_feature(self):
    cur_time_ms = 1662380788000 # 2022-09-05 20:26:28
    action_id_list = [101, 102, 101, 103, 103, 101]
    action_time_list = [cur_time_ms - r*1000 for r in [50, 360, 5000, 8*3600, 25*3600, 48*3600]]
    item_id_list = [101, 103, 102]

    time_window_config = [
      dict(time_ms=1 * 60 * 1000, suffix="1min"),
      dict(time_ms=5 * 60 * 1000, suffix="5min"),
      dict(time_ms=1 * 3600 * 1000, suffix="1h"),
      dict(time_ms=1 * 24 * 3600 * 1000, suffix="1d"),
      dict(time_ms=7 * 24 * 3600 * 1000, suffix="7d")
    ]
    index_window_config = [
      dict(index=1, suffix="last1"),
      dict(index=5, suffix="last5")
    ]

    flow = OfflineFlow(name="extract_cross_dense_feature_test") \
      .extract_cross_dense_feature(
        cross_feature_config=[
          dict(
            action_id_list_attr="action_id_list",
            action_time_list_attr='action_time_list',
            item_id_attr='action_id',
            feature_save_attr='ds_upm_test_',
          )],
        time_window_config=time_window_config,
        index_window_config=index_window_config,
        match_function_name="MatchWithId"
      )\
      .extract_cross_dense_feature(
        cross_feature_config=[
          dict(
            action_id_list_attr="action_id_list",
            action_time_list_attr='action_time_list',
            item_id_attr='action_id',
            feature_save_attr='ds_upm_hour_',
          )],
        time_window_config=time_window_config,
        index_window_config=index_window_config,
        match_function_name="MatchWithIdAndHour"
      )

    leaf = self.__init_service(flow)
    leaf['action_id_list'] = action_id_list
    leaf['action_time_list'] = action_time_list
    leaf.request_time = cur_time_ms
    for i in range(len(item_id_list)):
      item = leaf.add_item(i)
      item['action_id'] = item_id_list[i]

    leaf.run("extract_cross_dense_feature_test")

    print(action_id_list)
    print([r - cur_time_ms for r in action_time_list])

    # 按照 ID 匹配
    self.assertEqual(leaf.items[0][f"ds_upm_test_1min"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_test_5min"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_test_1d"], 2.0)
    self.assertEqual(leaf.items[0][f"ds_upm_test_7d"], 3.0)
    self.assertEqual(leaf.items[0][f"ds_upm_test_last1"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_test_last5"], 2.0)
    self.assertEqual(leaf.items[1][f"ds_upm_test_5min"], 0.0)
    self.assertEqual(leaf.items[1][f"ds_upm_test_1d"], 1.0)
    self.assertEqual(leaf.items[1][f"ds_upm_test_7d"], 2.0)
    self.assertEqual(leaf.items[1][f"ds_upm_test_last1"], 0.0)
    self.assertEqual(leaf.items[1][f"ds_upm_test_last5"], 2.0)
    self.assertEqual(leaf.items[2][f"ds_upm_test_5min"], 0.0)
    self.assertEqual(leaf.items[2][f"ds_upm_test_1d"], 1.0)
    self.assertEqual(leaf.items[2][f"ds_upm_test_last1"], 0.0)
    self.assertEqual(leaf.items[2][f"ds_upm_test_last5"], 1.0)

    # 按照 ID 和 小时匹配
    self.assertEqual(leaf.items[0][f"ds_upm_hour_1min"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_hour_5min"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_hour_1d"], 1.0)
    self.assertEqual(leaf.items[0][f"ds_upm_hour_7d"], 2.0)

  def test_reset_user_meta_info(self):
    time_ms = 1665212649000 # Sat Oct  8 15:04:09 CST 2022
    user_id = 1245061328
    device_id = "ANDROID_1ce5e3f420504f55"

    flow = OfflineFlow(name="reset_user_meta_info") \
      .reset_user_meta_info(
        timestamp_attr = "time_ms",
        time_unit = "ms",
        user_id_attr = "user_id",
        device_id_attr = "device_id",
      )

    leaf = self.__init_service(flow)
    leaf['user_id'] = user_id
    leaf['device_id'] = device_id
    leaf['time_ms'] = time_ms

    leaf.run("reset_user_meta_info")

    self.assertEqual(leaf.user_id, user_id)
    self.assertEqual(leaf.request_time, time_ms)
    self.assertEqual(leaf.device_id, device_id)

  def test_colossus_ckpt_reader(self):
    # flow = GsuFlow(name="test_colossus_ckpt_reader")
    # flow\
    #     .colossus_ckpt_reader(service_name="grpc_colossusLiveItemV4",
    #                           output_attr="colossus_ckpt_out") \
    #     .live_colossus_ckpt_seq_model_sample_retriever(input_attr="colossus_ckpt_out",
    #                                                    output_prefix="live_author_",
    #                                                    reward_weight=1000.0,
    #                                                    play_time_weight=1.0)
    # leaf = self.__init_service(flow)
    # leaf.run("test_colossus_ckpt_reader")
    # leaf.reset()
    # leaf.run("test_colossus_ckpt_reader")
    # for item in leaf.items:
    #   print(item['aId'], item['gift_amt'], item['sample_type'])
    #   print(item['live_author_author_id'])
    
    # python unit test environment do not support read hdfs, pass this test
    pass

  def test_live_colossus_ckpt_seq_model_sample_retriever(self):
    # flow = GsuFlow(name="test_live_colossus_ckpt_seq_model_sample_retriever")
    # flow\
    #     .colossus_ckpt_reader(service_name="grpc_colossusLiveItemV4",
    #                           output_attr="colossus_ckpt_out") \
    #     .live_colossus_ckpt_seq_model_sample_retriever(input_attr="colossus_ckpt_out",
    #                                                    output_prefix="live_author_",
    #                                                    reward_weight=1000.0,
    #                                                    play_time_weight=1.0)
    # leaf = self.__init_service(flow)
    # leaf.run("test_colossus_ckpt_reader")
    # leaf.reset()
    # leaf.run("test_colossus_ckpt_reader")
    # for item in leaf.items:
    #   print(item['aId'], item['gift_amt'], item['sample_type'])
    #   print(item['live_author_author_id'])

    # python unit test environment do not support read hdfs, pass this test
    pass

  def test_update_local_sample_pool(self):
    flow = OfflineFlow(name="test_update_local_sample_pool") \
      .update_local_sample_pool(
        id_from_common="ids",
        value_from_common="values",
        sample_pool_config=dict(key="sample_pool"),
      )
    leaf = self.__init_service(flow)
    leaf['ids'] = [1, 1, 1, 3, 3]
    leaf['values'] = [3, 3, 3, 1, 1]
    leaf.run("test_update_local_sample_pool")
  
  def test_retrieve_from_local_sample_pool(self):
    sample_pool_config=dict(key="sample_pool")
    update_flow = OfflineFlow(name="test_retrieve_from_local_sample_pool_update") \
      .update_local_sample_pool(
        id_from_common="ids",
        value_from_common="values",
        sample_pool_config=sample_pool_config,
      )

    test_flow = OfflineFlow(name="test_retrieve_from_local_sample_pool")\
      .retrieve_from_local_sample_pool(
        reason=1,
        retrieve_num=10,
        output_prob_attr="prob",
        output_pool_size_attr="sample_pool_size",
        sample_pool_config=sample_pool_config
      )
    self.__service.add_leaf_flows(leaf_flows=[update_flow, test_flow])
    leaf = self.__service.executor()
    leaf['ids'] = [1, 2, 3, 4, 5, 5]
    leaf['values'] = [3, 3, 3, 3, 1, 2]
    leaf.run("test_retrieve_from_local_sample_pool_update")
    # ensure update succuss
    time.sleep(0.1)
    leaf.run("test_retrieve_from_local_sample_pool")
    self.assertEqual(5, leaf["sample_pool_size"])
    self.assertEqual(10, len(leaf.items))
    for item in leaf.items:
      self.assertEqual(item["prob"], 3.0)

  def test_random_retrieve_from_common_attr(self):
    flow = OfflineFlow(name="test_random_retrieve_from_common_attr") \
      .random_retrieve_from_common_attr(
        reason=1,
        retrieve_num=10,
        id_attr="ids",
        output_index_attr="index"
      )
    leaf = self.__init_service(flow)
    leaf['ids'] = [1, 2, 3, 4, 5, 6]
    id2idx = {id: idx for idx, id in enumerate(leaf['ids'])}
    leaf.run("test_random_retrieve_from_common_attr")
    self.assertEqual(10, len(leaf.items))
    for item in leaf.items:
      id = item.item_key
      idx = item["index"]
      self.assertEqual(id2idx[id], idx)
  
  def test_group_list_attr(self):
    flow = OfflineFlow(name="test_group_list_attr") \
      .group_list_attr(
        configs=[
          dict(
            key_attr="ids",
            out_key_attr="unique_ids",
            out_count_attr="unique_ids_count",
            agg_configs=[
              dict(
                op="sum",
                src_attr="values",
                dst_attr="values_sum",
                dst_type="float_list",
              ),
              dict(
                op="max",
                src_attr="values",
                dst_attr="values_max",
              )
            ]
          )
        ]
      )
    leaf = self.__init_service(flow)
    leaf['ids'] = [1, 2, 3, 4, 5, 5]
    leaf['values'] = [3, 3, 3, 3, 1, 2]
    leaf.run("test_group_list_attr")
    self.assertEqual([1, 2, 3, 4, 5], leaf['unique_ids'])
    self.assertEqual([1, 1, 1, 1, 2], leaf['unique_ids_count'])
    print(leaf["values_sum"], type(leaf["values_sum"][0]))
    self.assertEqual([3.0, 3.0, 3.0, 3.0, 3.0], leaf['values_sum'])
    self.assertEqual([3, 3, 3, 3, 2], leaf['values_max'])
  
  def test_transform_list_attr(self):
    flow = OfflineFlow(name="test_transform_list_attr") \
      .transform_list_attr(
        configs=[
          dict(
            is_dst_common=True,
            sort_attr="timestamp",
            sort_desc=True,
            limit_size=3,
            output_sort_indices_attr="sort_indices",
            output_mask_flags_attr="mask_flags",
            filter_configs=[
              dict(
                attr="timestamp",
                lower_bound_attr="start_timestamp",
                upper_bound_attr="end_timestamp",
              ),
              dict(
                attr="author_id",
                not_equal_to_attr="mask_author_id",
              )
            ],
            attr_configs=[
              dict(
                src_attr="timestamp",
                dst_attr="new_timestamp",
              ),
              dict(
                src_attr="author_id",
                dst_attr="new_author_id",
              )
            ],
          ),
          dict(
            is_dst_common=False,
            limit_size=3,
            output_sort_indices_attr="sort_indices",
            output_mask_flags_attr="mask_flags",
            filter_configs=[
              dict(
                attr="author_id",
                not_equal_to_attr="author_id",
                is_not_equal_to_attr_common=False,
              )
            ],
            attr_configs=[
              dict(
                src_attr="timestamp",
                dst_attr="timestamp_list",
              ),
              dict(
                src_attr="author_id",
                dst_attr="author_id_list",
              )
            ]
          ),
          dict(
            is_dst_common=False,
            output_mask_flags_attr="mask_flags2",
            use_double_mask=True,
            filter_configs=[
              dict(
                attr="author_id",
                not_equal_to_attr="author_id",
                is_not_equal_to_attr_common=False,
              )
            ]
          )
        ],
      )

    leaf = self.__init_service(flow)
    leaf["timestamp"] = [1, 2, 3, 4, 5, 6]
    leaf["author_id"] = [101, 102, 103, 104, 105, 106]
    leaf["start_timestamp"] = 3
    leaf["end_timestamp"] = 5
    leaf["mask_author_id"] = 104
    item = leaf.add_item(0)
    item["author_id"] = 105
    item = leaf.add_item(1)
    item["author_id"] = 103

    leaf.run("test_transform_list_attr")
    item = leaf.items[0]
    self.assertEqual([5, 4, 3, 2, 1, 0], leaf["sort_indices"])
    self.assertEqual([0, 0, 1 , 0, 1, 0], leaf["mask_flags"])
    self.assertEqual([5, 3], leaf["new_timestamp"])
    self.assertEqual([105, 103], leaf["new_author_id"])

    item = leaf.items[0]
    self.assertEqual([1, 1, 1, 1, 0, 1], item["mask_flags"])
    self.assertTrue(isinstance(item["mask_flags2"][0], float))
    self.assertEqual([1, 1, 1, 1, 0, 1], item["mask_flags2"])
    self.assertEqual([0, 1, 2, 3, 4, 5], item["sort_indices"])
    self.assertEqual([101, 102, 103], item["author_id_list"])
    self.assertEqual([1, 2, 3], item["timestamp_list"])
    item = leaf.items[1]
    self.assertEqual([1, 1, 0, 1, 1, 1], item["mask_flags"])
    self.assertTrue(isinstance(item["mask_flags2"][0], float))
    self.assertEqual([1, 1, 0, 1, 1, 1], item["mask_flags2"])
    self.assertEqual([0, 1, 2, 3, 4, 5], item["sort_indices"])
    self.assertEqual([101, 102, 104], item["author_id_list"])
    self.assertEqual([1, 2, 4], item["timestamp_list"])
  
  def test_joint_reco_log(self):
    flow = OfflineFlow(name="joint_reco_log_test") \
        .base64(
          mode="decode",
          is_common_attr=True,
          input_attr="reco_log_str_base64",
          output_attr="reco_log",
        )\
        .base64(
          mode="decode",
          is_common_attr=False,
          input_attr="label_llsid_pid_base64",
          output_attr="label_llsid_pid",
        )\
        .base64(
          mode="decode",
          is_common_attr=False,
          input_attr="label_uid_pid_base64",
          output_attr="label_uid_pid",
        )\
        .base64(
          mode="decode",
          is_common_attr=False,
          input_attr="label_did_pid_base64",
          output_attr="label_did_pid",
        )\
        .base64(
          mode="decode",
          is_common_attr=False,
          input_attr="label_uid_aid_base64",
          output_attr="label_uid_aid",
        )\
        .parse_protobuf_from_string(
          input_attr="reco_log",
          output_attr="ks_reco_log",
          class_name="ks::reco::RecoLog")\
        .joint_reco_log( # adapt snack data
          reco_log_attr="ks_reco_log",
          label_llsid_pid_str='label_llsid_pid',
          label_uid_pid_str='label_uid_pid',
          label_did_pid_str='label_did_pid',
          label_uid_aid_str='label_uid_aid',
          kconf_path_of_action_to_label_map="reco.feedStream.recoLogAllActionLabelMap",
        )\
        .retrieve_from_ks_reco_log(
          from_extra_var="ks_reco_log",
          save_reco_photo_to="reco_photo_info") \
        .enrich_with_protobuf(
          from_extra_var="reco_photo_info",
          is_common_attr=False,
          attrs=[
            dict(name="profile_stay_time_v2", path="context_info.profile_stay_time_v2"),
            dict(name="profile_feed_mode_stay_time", path="context_info.profile_feed_mode_stay_time"),
            dict(name="search", path="context_info.search"),
            dict(name="exit_rerank_page", path="context_info.exit_rerank_page"),
            dict(name="dup_click", path="context_info.dup_click"),
            dict(name="like", path="context_info.like"),
            dict(name="follow", path="context_info.follow"),
          ]) \

    leaf = self.__init_service(flow)
    leaf['reco_log_str_base64'] = "CNcIIgwI0gnKAgYxMjM0NTYqCAoGCArwAognKggKBggL8ALYNg=="
    item_label_list = [["CggICBIECAEYAQoICAoSBAgBGAE=", "CgkIuQISBAgBGAEKCQgjEgUIAiCIJwoJCI4DEgQIARgB",
                       "CgkI2wESBAgBGAEKCQh6EgUIAiDmYA==", "CggIGBIECAEYAQoICAsSBAgBGAE="],
                       ["CgoIpQISBQgCIIU1", "CgkIkAISBAgCIAw=", "CggIHhIECAEYAQoJCDcSBQgCIOZg",
                       "CggIGBIECAEYAQoICAsSBAgBGAE="]]
    for i in range(len(item_label_list)):
        item = leaf.add_item(i+10)
        item['label_llsid_pid_base64'] = item_label_list[i][0]
        item['label_uid_pid_base64'] = item_label_list[i][1]
        item['label_did_pid_base64'] = item_label_list[i][2]
        item['label_uid_aid_base64'] = item_label_list[i][3]
    leaf.run("joint_reco_log_test")
    self.assertEqual(leaf.items[2]["profile_feed_mode_stay_time"], 12390)
    self.assertEqual(leaf.items[2]["like"], False)
    self.assertEqual(leaf.items[2]["follow"], False)
    self.assertEqual(leaf.items[3]["profile_stay_time_v2"], 12390)
    self.assertEqual(leaf.items[3]["exit_rerank_page"], 6789)
  
  def test_reco_log_joint_full_link(self):
    flow = OfflineFlow(name="test_reco_log_joint_full_link") \
        .base64(
          mode="decode",
          is_common_attr=True,
          input_attr="reco_log_str_base64",
          output_attr="ks_reco_log_str",
        )\
        .base64(
          mode="decode",
          is_common_attr=True,
          input_attr="full_link_str_base64",
          output_attr="full_link_str",
        )\
        .parse_protobuf_from_string(
          input_attr="ks_reco_log_str",
          output_attr="ks_reco_log",
          class_name="ks::reco::RecoLog")\
        .parse_protobuf_from_string(
          input_attr="full_link_str",
          output_attr="ks_full_link",
          class_name="ks::reco::RecoLog")\
        .reco_log_joint_full_link(
          joint_reco_log_attr='ks_reco_log',
          full_link_attr='ks_full_link')\
        .enrich_with_protobuf(
          from_extra_var="ks_reco_log",
          is_common_attr=True,
          attrs=["llsid",
            dict(path="user", name="user_info"), 
            dict(path="valid_retr_info.valid_exptag", name="valid_exptags"),
            dict(path="valid_retr_info.candidate_size", name="candidate_sizes")]) \
        .retrieve_from_ks_reco_log(
          from_extra_var="ks_reco_log",
          save_reco_photo_to="reco_photo_info") \

    leaf = self.__init_service(flow)
    leaf['reco_log_str_base64'] = "CNcIIgwI0gnKAgYxMjM0NTYqCQoCCAEaAzIzNCoICgYIAvAC2DY="
    leaf['full_link_str_base64'] = "CNcIKg0KAggBGgcyMzQsNTY3KgsKAggCMgXoDYSISioICgYIA/AC2DYqCAoGCATwAtg2KggKBggF8ALYNioICgYIBvAC2DaaAgQIexAB"
    leaf.run("test_reco_log_joint_full_link")
    self.assertEqual(6, len(leaf.items))

  def test_retrieve_from_session_based_rl(self):
    flow = OfflineFlow(name="retrieve_from_session_based_rl_test") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "2000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "3000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "4000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample4"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request1"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+3}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request2"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[
            dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)
        ],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_session_based_rl(
        name="retr_from_session_based_rl",
        sample_from_attr="session_based_rl",
        like_weight=1.5,
        comment_weight=2.0,
        play_time_weight=0.1,
        gamma=0.5,
      )
    leaf = self.__init_service(flow)
    leaf.run("retrieve_from_session_based_rl_test")
    self.assertEqual(leaf.items[0]["total_reward"], 2.3)
    self.assertEqual(leaf.items[1]["total_reward"], 1.9)
    # self.assertEqual(leaf.items[2]["total_reward"], 3.7)
    # self.assertEqual(leaf.items[3]["total_reward"], 4.3)
  
  def test_fulldata_retrieve_from_session_based_rl(self):
    flow = OfflineFlow(name="fulldata_retrieve_from_session_based_rl_test") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "2000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "3000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "4000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample4"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request1"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+3}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request2"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[
            dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)
        ],
        output_common_attr="session_based_rl"
      )\
      .fulldata_retrieve_from_session_based_rl(
        name="fd_retr_from_session_based_rl",
        sample_from_attr="session_based_rl",
        like_weight=1.5,
        comment_weight=2.0,
        play_time_weight=0.1,
        gamma=0.5,
      )
    leaf = self.__init_service(flow)
    leaf.run("fulldata_retrieve_from_session_based_rl_test")
    self.assertEqual(leaf.items[0]["total_reward"], 0.0)
    self.assertEqual(leaf.items[1]["total_reward"], 0.0)
    # self.assertEqual(leaf.items[2]["total_reward"], 3.7)
    # self.assertEqual(leaf.items[3]["total_reward"], 4.3)
    
  def test_fulldata_retrieve_from_session_based_rl_watch_tired(self):
    flow = OfflineFlow(name="fulldata_retrieve_from_session_based_rl_watch_tired_test") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "2000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "3000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "4000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
        ],
        output_common_attr="sample4"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request1"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+3}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request2"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[
            dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)
        ],
        output_common_attr="session_based_rl"
      )\
      .fulldata_retrieve_from_session_based_rl_watch_tired(
        name="fd_retr_from_session_based_rl_watch_tired",
        sample_from_attr="session_based_rl",
        like_weight=1.5,
        comment_weight=2.0,
        play_time_weight=0.1,
        gamma=0.5,
      )
    leaf = self.__init_service(flow)
    leaf.run("fulldata_retrieve_from_session_based_rl_watch_tired_test")
    self.assertEqual(leaf.items[0]["total_reward"], 0.0)
    self.assertEqual(leaf.items[1]["total_reward"], 0.0)
    # self.assertEqual(leaf.items[2]["total_reward"], 3.7)
    # self.assertEqual(leaf.items[3]["total_reward"], 4.3)
  
  def test_retrieve_by_random_negative_sampling(self):
    flow = OfflineFlow(name="retrieve_by_random_negative_sampling_test") \
      .fake_retrieve(num=1, reason=1) \
      .copy_item_meta_info(
        save_item_id_to_attr="item_id"
      ) \
      .set_attr_default_value(
        item_attrs=[
          {"name": "tag", "type": "string", "value":"h666"}
        ]
      ) \
      .retrieve_by_random_negative_sampling(
        sampling_rate=1.0, 
        buffer_size=1, 
        sampling_cnt_per_item=1,
        extra_int_attrs=['item_id'],
        pick_item={"tag":"h666"}
      ) \
      .retrieve_by_random_negative_sampling(
        sampling_rate=1.0, 
        buffer_size=1, 
        sampling_cnt_per_item=1,
        extra_int_attrs=['item_id'],
        pick_item={"tag":"h666"}
      ) \

    leaf = self.__init_service(flow)
    leaf.run("retrieve_by_random_negative_sampling_test")
    for item in leaf.items:
      self.assertEqual(item['item_id'], 1)
    
  def test_retrieve_by_random_negative_sampling_v2(self):
    flow = OfflineFlow(name="retrieve_by_random_negative_sampling_test_v2") \
      .fake_retrieve(num=1, reason=1) \
      .copy_item_meta_info(
        save_item_id_to_attr="item_id"
      ) \
      .set_attr_default_value(
        item_attrs=[
          {"name": "tag", "type": "string", "value":"h666"}
        ]
      ) \
      .retrieve_by_random_negative_sampling_v2(
        sampling_rate=1.0, 
        buffer_size=1, 
        sampling_cnt_per_item=1,
        extra_int_attrs=['item_id'],
        pick_item={"tag":"h666"}
      ) \
      .retrieve_by_random_negative_sampling_v2(
        sampling_rate=1.0, 
        buffer_size=1, 
        sampling_cnt_per_item=1,
        extra_int_attrs=['item_id'],
        pick_item={"tag":"h666"}
      ) \

    leaf = self.__init_service(flow)
    leaf.run("retrieve_by_random_negative_sampling_test_v2")
    for item in leaf.items:
      self.assertEqual(item['item_id'], 1)

  def test_retrieve_from_rerank_reco_rl(self):
    flow = OfflineFlow(name="test_retrieve_from_rerank_reco_rl") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample1pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample1pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "0",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample2pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample2pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "3",
          "rank_index_sample": "0",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample3pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample3pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample4pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample4pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample4"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "2",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample5pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample5pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample5"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "110",
        }
      )\
        .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(3)],
        output_common_attr="request1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "111",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+4}", path="samples", append=True) for i in range(2)],
        output_common_attr="request2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "device_id_sample": "123",
          "tab_sample": "30000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[dict(common_attr="device_id_sample", path="device_id")] +
               [dict(common_attr="tab_sample", path="tab")] +
               [dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_rerank_reco_rl(
        name="retrieve_from_rerank_reco_rl",
        sample_from_attr="session_based_rl"
      )
    leaf = self.__init_service(flow)
    leaf.run("test_retrieve_from_rerank_reco_rl")
    self.assertEqual(len(leaf.items), 1)
    self.assertEqual(len(leaf.items[0]["context_pids"]), 2)
    
  def test_retrieve_from_rerank_reco_prm(self):
    flow = OfflineFlow(name="test_retrieve_from_rerank_reco_prm") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "1",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample1pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample1pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "0",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample2pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample2pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample3pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample3pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "1",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample4pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample4pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample4"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample5pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample5pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample5"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "110",
        }
      )\
        .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(3)],
        output_common_attr="request1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "111",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+4}", path="samples", append=True) for i in range(2)],
        output_common_attr="request2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "device_id_sample": "123",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[dict(common_attr="device_id_sample", path="device_id")] +
               [dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)],
        output_common_attr="session_based_prm"
      )\
      .retrieve_from_rerank_reco_prm(
        name="retrieve_from_rerank_reco_prm",
        sample_from_attr="session_based_prm"
      )
    leaf = self.__init_service(flow)
    leaf.run("test_retrieve_from_rerank_reco_prm")
    self.assertEqual(len(leaf.items), 1)
    self.assertEqual(len(leaf.items[0]["context_pids"]), 2)

  def test_retrieve_from_rank_reco_rl_long(self):
    flow = OfflineFlow(name="retrieve_from_rank_reco_rl_long_test") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="plvtr"),
        ],
        output_common_attr="sample1pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="sample1pxtr", path="pxtr"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "0",
          "comment_sample": "1",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "play_time_ms_sample": "2000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="plvtr"),
        ],
        output_common_attr="sample2pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample2pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "0",
          "comment_sample": "1",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "play_time_ms_sample": "3000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="plvtr"),
        ],
        output_common_attr="sample3pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample3pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "play_time_ms_sample": "4000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="plvtr"),
        ],
        output_common_attr="sample4pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample4pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample4"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request1"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+3}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request2"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[
            dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)
        ],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_rank_reco_rl_long(
        name="retrieve_from_rank_reco_rl_long",
        sample_from_attr="session_based_rl",
        like_weight=1.5,
        comment_weight=2.0,
        play_time_weight=0.1,
        gamma=0.5,
      )
    leaf = self.__init_service(flow)
    leaf.run("retrieve_from_rank_reco_rl_long_test")
    self.assertEqual(leaf.items[0]["immediate_reward"], 2.3)

  def test_retrieve_from_rank_reco_rl_v2(self):
    flow = OfflineFlow(name="test_retrieve_from_rank_reco_rl_v2") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample1pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample1pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "0",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample2pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample2pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "3",
          "rank_index_sample": "0",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample3pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample3pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample4pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample4pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample4"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "2",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample5pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample5pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample5"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "110",
        }
      )\
        .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(3)],
        output_common_attr="request1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "111",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+4}", path="samples", append=True) for i in range(2)],
        output_common_attr="request2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "device_id_sample": "123",
          "tab_sample": "30000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[dict(common_attr="device_id_sample", path="device_id")] +
               [dict(common_attr="tab_sample", path="tab")] +
               [dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_rank_reco_rl_v2(
        name="retrieve_from_rank_reco_rl_v2",
        sample_from_attr="session_based_rl"
      )
    leaf = self.__init_service(flow)
    leaf.run("test_retrieve_from_rank_reco_rl_v2")
    self.assertEqual(len(leaf.items), 1)
    self.assertEqual(len(leaf.items[0]["context_pids"]), 2)
 


  def test_retrieve_from_rank_reco_rl(self):
    flow = OfflineFlow(name="retrieve_from_rank_reco_rl_test") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "2000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "0",
          "comment_sample": "1",
          "play_time_ms_sample": "3000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "4000",
          "real_show_sample": "1"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="real_show_sample", path="real_show"),
        ],
        output_common_attr="sample4"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request1"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[
            dict(common_attr=f"sample{i+3}", path="samples", append=True) for i in range(2)
        ],
        output_common_attr="request2"
      )
    flow.build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[
            dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)
        ],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_rank_reco_rl(
        name="retrieve_from_rank_reco_rl",
        sample_from_attr="session_based_rl",
        like_weight=1.5,
        comment_weight=2.0,
        play_time_weight=0.1,
        gamma=0.5,
      )
    leaf = self.__init_service(flow)
    leaf.run("retrieve_from_rank_reco_rl_test")
    self.assertEqual(leaf.items[0]["immediate_reward"], 2.3)


  def test_retrieve_from_rerank_plugin_info_log(self):
    flow = OfflineFlow(name="retrieve_from_rerank_plugin_info_log_test") \
      .set_attr_value(
        no_overwrite=True,
        common_attrs=[
          {"name": "fake_pid_list", "type": "int_list", "value": [1001, 1002, 1003]},
          {"name": "fake_pctr_list", "type": "double_list", "value": [0.5, 0.75, 0.85]},
          {"name": "fake_pltr_list", "type": "double_list", "value": [0.2, 0.1, 0.3]},
        ]) \
      .retrieve_by_common_attr(
        attr="fake_pid_list",
        reason=1) \
      .dispatch_common_attr(
        dispatch_config = [
          {"from_common_attr" : "fake_pid_list", "to_item_attr" : "pid"},
          {"from_common_attr" : "fake_pctr_list", "to_item_attr" : "pctr"},
          {"from_common_attr" : "fake_pltr_list", "to_item_attr" : "pltr"},
        ]) \
      .build_protobuf(
        class_name="ks::reco::PhotoInfo",
        inputs=[
          {"item_attr": "pid", "path": "photo_id"},
        ],
        output_item_attr="fake_photo_pb") \
      .build_protobuf(
        class_name="ks::reco::RecoPhotoInfo",
        inputs=[
          {"item_attr": "fake_photo_pb", "path": "photo"},
          {"item_attr": "pctr", "path": "pctr"},
          {"item_attr": "pltr", "path": "pltr"},
        ],
        output_item_attr="fake_reco_photo") \
      .build_protobuf(
        class_name="ks::reco::PluginItemInfo",
        inputs=[
          {"item_attr": "fake_reco_photo", "path": "plugin_items", "append": True},
        ],
        output_common_attr="fake_plugin_item_info") \
      .limit(0)
    flow \
      .retrieve_from_rerank_plugin_info_log(
        from_extra_var="fake_plugin_item_info",
        item_type=1,
        save_plugin_reco_photo_to="plugin_info") \
      .enrich_with_protobuf(
        from_extra_var="plugin_info",
        is_common_attr=False,
        attrs=[
          dict(path="photo.photo_id", name="pid"),
          "pctr", "pltr",
        ])
    leaf = self.__init_service(flow)
    leaf.run("retrieve_from_rerank_plugin_info_log_test")
    self.assertEqual(len(leaf.items), 3)
    self.assertEqual(leaf.items[0]["pid"], 1001)
    self.assertEqual(leaf.items[1]["pid"], 1002)
    self.assertEqual(leaf.items[2]["pid"], 1003)

  def test_retrieve_from_rank_reco_page(self):
    flow = OfflineFlow(name="test_retrieve_from_rank_reco_page") \
      .gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10086",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample1pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample1pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10087",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "0",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample2pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample2pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10088",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0.9",
          "pxtr_attrs_plvtr": "0.8",
          "real_show_sample": "3",
          "rank_index_sample": "0",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample3pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample3pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample3"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "1",
          "rank_index_sample": "2",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample4pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample4pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample4"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "photo_id_sample": "10089",
          "like_sample": "1",
          "comment_sample": "0",
          "play_time_ms_sample": "1000",
          "pxtr_attrs_pctr": "0",
          "pxtr_attrs_plvtr": "0",
          "real_show_sample": "2",
          "rank_index_sample": "3",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::PxtrAttrs",
        inputs=[
            dict(common_attr="pxtr_attrs_pctr", path="pctr"),
            dict(common_attr="pxtr_attrs_plvtr", path="pvtr"),
        ],
        output_common_attr="sample5pxtr"
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSample",
        inputs=[
            dict(common_attr="photo_id_sample", path="photo_id"),
            dict(common_attr="like_sample", path="like"),
            dict(common_attr="comment_sample", path="comment"),
            dict(common_attr="play_time_ms_sample", path="play_time_ms"),
            dict(common_attr="sample5pxtr", path="pxtr"),
            dict(common_attr="real_show_sample", path="real_show"),
            dict(common_attr="rank_index_sample", path="rank_index"),
        ],
        output_common_attr="sample5"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "110",
        }
      )\
        .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+1}", path="samples", append=True) for i in range(3)],
        output_common_attr="request1"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "request_time_sample": "111",
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkRequest",
        inputs=[dict(common_attr="request_time_sample", path="request_tm")] +
               [dict(common_attr=f"sample{i+4}", path="samples", append=True) for i in range(2)],
        output_common_attr="request2"
      )
    flow.gen_common_attr_by_lua(
        attr_map={
          "device_id_sample": "123",
          "tab_sample": "30000"
        }
      )\
      .build_protobuf(
        class_name="ks::reco::FullLinkSession",
        inputs=[dict(common_attr="device_id_sample", path="device_id")] +
               [dict(common_attr="tab_sample", path="tab")] +
               [dict(common_attr=f"request{i+1}", path="requests", append=True) for i in range(2)],
        output_common_attr="session_based_rl"
      )\
      .retrieve_from_rank_reco_page(
        name="retrieve_from_rank_reco_page",
        sample_from_attr="session_based_rl"
      )
    leaf = self.__init_service(flow)
    leaf.run("test_retrieve_from_rank_reco_page")
    self.assertEqual(len(leaf.items), 1)
    self.assertEqual(len(leaf.items[0]["context_pids"]), 2)
 

  @unittest.skip("to be fix")
  def test_fetch_message(self):
      pass
    
  # 取 shard 逻辑依赖 kess 环境，暂时跳过
  @unittest.skip("skip ut for kess")
  def test_retrieve_from_bt_shm_kv(self):
      pass
  
  @unittest.skip("to be fix")
  def test_batch_consume_kafka(self):
      pass
  
  @unittest.skip("to be fix")
  def test_retrieve_photo_with_emb_from_model_update_btq(self):
      pass

if __name__ == '__main__':
  suite = unittest.TestSuite()
  suite.addTests(unittest.TestLoader().loadTestsFromName('offline_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
