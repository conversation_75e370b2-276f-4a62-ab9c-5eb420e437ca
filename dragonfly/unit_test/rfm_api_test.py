#!/usr/bin/env python3
# coding=utf-8

# 该单元测试用来测试RFM特征相关的文件
# 使用方式如下，请根据自己运行代码的环境决定是否需要调整命令
# 本代码必须运行在开发机中，请勿在个人机器上使用

'''
export COMMON_LEAF_BINARY_TARGET=""
编译方式
方法一. 本地编译
ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true DRAGON_EXT="rfm gsu" kbuild build --base_tag=HEAD_GCC10_LLD dragon/server dragon/src
方法二. 云端编译 
ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true DRAGON_EXT="rfm gsu" kbuild build -r --base_tag=HEAD_GCC10_LLD dragon/server dragon/src
wget 结果地址
tar -xvf dragon_server_XXX.tgz

可以使用如下方式加在 GCC 的工具链
export LD_PRELOAD=$HOME/dragon/ks/serving_online/ad_tdm_tree_server/so/libstdc++.so.6
执行 executor 前需导入系统变量 `export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6`,
如果是 gcc10 编译则导入系统变量 `export LD_PRELOAD=./build_tools/gcc-10.3.0/lib64/libstdc++.so.6`
请自行选择可以使用的工具链

export GRPC_CPU_CORES_USE_CONF=true
export PYTHONPATH=$PYTHONPATH:$HOME/dragon/dragon
ln -s $HOME/dragon/dragon_build/.kbuild-out/.build/opt/targets/dragon/server/common_reco_pipeline_executor_pywrap.so  $HOME/dragon/dragon/dragonfly/common_reco_pipeline_executor_pywrap.so

测试方式: 
python3 dragon/dragonfly/unit_test/rfm_api_test.py
[可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.rfm_api_test.RfmTestSuite.xxxx
'''

from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.rfm.rfm_api_mixin import RfmApiMixin
from dragonfly.ext.gsu.gsu_api_mixin import GsuApiMixin
from dragonfly.ext.common.common_api_mixin import CommonApiMixin

import unittest
import json
import base64
import struct
import time


def encode_bytes(data: bytes) -> str:
  return base64.b64encode(data).decode('utf-8')

# (label << 7) & 1 True 表示关注了主播
# 关注主播   1 2 3 4 5
# 未关注主播 6 7 8 9 10 11
def test_add_follow_data():
  FOLLOW_FLAG = (1 << 7)
  NOT_FOLLOW_FLAG = 0
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [8 * 3600, 9 * 3600, 24 * 3600, 25 * 3600,  26 * 3600, 64 * 3600, 65 * 3600, 66 * 3600,  67 * 3600, 87 * 3600, 89 * 3600]
  author_id_list =  [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
  play_time_list =       [200, 200, 200, 200, 200, 200, 200, 200, 200, 200, 200]
  auto_play_time_list =  [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [FOLLOW_FLAG, FOLLOW_FLAG, FOLLOW_FLAG, FOLLOW_FLAG, FOLLOW_FLAG, NOT_FOLLOW_FLAG, NOT_FOLLOW_FLAG, NOT_FOLLOW_FLAG, NOT_FOLLOW_FLAG, NOT_FOLLOW_FLAG, NOT_FOLLOW_FLAG]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]

# 测试时区
# UTC + 8
# 666 1天
# 667 2天 56 57 58 59 79 81
def test_time_zone_data():
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [8 * 3600, 9 * 3600, 24 * 3600, 25 * 3600,  26 * 3600, 64 * 3600, 65 * 3600, 66 * 3600,  67 * 3600, 87 * 3600, 89 * 3600]
  author_id_list =  [666, 666, 666, 666, 666, 667, 667, 667, 667, 667, 667]
  play_time_list =       [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20]
  auto_play_time_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [0, 1, 4, 4, 0, 0, 0, 0, 0, 256, 0]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]

# 测试是否存在除0的问题
def safe_test_data():
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [1654458036, 1654458036, 1654458123, 1655998550, 1655998733, 1655999201, 1655999225, 1655999266, 1656314115, 1656406127, 1656739203]
  author_id_list =  [666, 666, 666, 666, 666, 666, 667, 668, 669, 670, 671]
  play_time_list =       [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  auto_play_time_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [0, 1, 4, 4, 0, 0, 0, 0, 0, 256, 0]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]
# 测试用例说明 测试 自动播放 & 播放时间是否正确设置threshold
# 正确答案 6 次
def playtime_test_data():
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [1654458036, 1654458036, 1654458123, 1655998550, 1655998733, 1655999201, 1655999225, 1655999266, 1656314115, 1656406127, 1656739203]
  author_id_list =  [666, 666, 666, 666, 666, 666, 666, 666, 666, 666, 666]
  play_time_list =       [0, 10, 17, 151, 3, 233, 224, 0,  9, 1, 1]
  auto_play_time_list =  [0, 0,   0,   0, 0,   0,   0, 20, 0, 0, 0]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [0, 1, 4, 4, 0, 0, 0, 0, 0, 256, 0]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]

# 测试用例说明 测试 24点能否正确拆分为2天
# 正确答案 6 天
def timestamp_test_data():
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [1 * 3600, 24 * 3600, 48 * 3600, 49 * 3600, 50 * 3600, 60 * 3600, 150 * 3600, 666 * 3600, 667 * 3600, 999 * 3600, 1000 * 3600]
  author_id_list =  [666, 666, 666, 666, 666, 666, 666, 666, 666, 666, 666]
  play_time_list =  [40, 40, 17, 151, 34, 233, 224, 12, 24, 244, 552]
  auto_play_time_list =  [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [0, 1, 4, 4, 0, 0, 0, 0, 0, 256, 0]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]

def default_data():
  follow_label = (1 << 7)
  live_id_list = [9611138711, 9611138711, 9611138711, 9674425995, 9674386592, 9674336243, 9674454689, 9674383969, 9686886286, 9684696721, 9704553751]
  timestamp_list = [1654458036, 1654458036, 1654458123, 1655998550, 1655998733, 1655999201, 1655999225, 1655999266, 1656314115, 1656406127, 1656739203]
  author_id_list =  [666, 666, 666, 2011777021, 1291205167, 941901644, 2899812133, 586064739, 670333339, 2470293124, 916369360]
  play_time_list =  [0, 1, 17, 151, 3, 233, 224, 12, 24, 244, 552]
  auto_play_time_list =  [0, 2, 0, 0, 0, 666, 0, 0, 0, 0, 0]
  hetu_tag_channel_list =  [39168, 39168, 39168, 506626, 39170, 39170, 165890, 87298, 50, 361989, 236034]
  cluster_id_list =  [426, 426, 426, 576, 204, 204, 243, 926, 698, 445, 675]
  label_list = [0, 1, 4, 4, follow_label, 0, 0, follow_label, 0, 256, 0]
  reward_list =  [0, 2, 0, 2, 12, 0, 999, 0, 0, 0, 0]
  reward_count_list =  [0, 1, 0, 2, 0, 0, 9, 0, 0, 0, 0]
  item_id_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  audience_count_list =  [0, 0, 0, 18, 2, 6, 11, 1, 25163, 6, 5]
  user_latitude_list =  [33.605000, 33.605000, 33.605000, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
  user_longitude_list =  [113.266068, 113.266068, 113.266068, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
  author_latitude_list =  [33.605000, 33.605000, 33.605000, 33.971607, 33.432060, 36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
  author_longitude_list =  [113.266068, 113.266068, 113.266068, 113.220009, 113.368248, 113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
  order_price_list =  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  return [live_id_list, timestamp_list, author_id_list, play_time_list, auto_play_time_list,hetu_tag_channel_list, \
      cluster_id_list, label_list, reward_list, reward_count_list, item_id_list, audience_count_list, user_latitude_list, \
      user_longitude_list, author_latitude_list, author_longitude_list, order_price_list]

  
# 该类用来 Mock grpc_colossusLiveItemV4
class MockColossusLiveItemV4():
  def make_resp_dict(self, data_gen_func):
    if data_gen_func is None:
      raise "Please add mock data generate function"
    mock_items = {}
    user_id = 666666
    cluster_id = 0
    attrList = data_gen_func()

    live_id_list = attrList[0]
    timestamp_list = attrList[1]
    author_id_list = attrList[2]
    play_time_list = attrList[3]
    auto_play_time_list = attrList[4]
    hetu_tag_channel_list = attrList[5]
    cluster_id_list = attrList[6]
    label_list = attrList[7]
    reward_list = attrList[8]
    reward_count_list = attrList[9]
    item_id_list = attrList[10]
    audience_count_list = attrList[11]
    user_latitude_list = attrList[12]
    user_longitude_list = attrList[13]
    author_latitude_list = attrList[14]
    author_longitude_list = attrList[15]
    order_price_list = attrList[16]

    mock_items["live_id"] = live_id_list
    mock_items["timestamp"] = timestamp_list
    mock_items["author_id"] = author_id_list
    mock_items["play_time"] = play_time_list
    mock_items["auto_play_time"] = auto_play_time_list
    mock_items["hetu_tag_channel"] = hetu_tag_channel_list
    mock_items["cluster_id"] = cluster_id_list
    mock_items["label"] = label_list
    mock_items["reward"] = reward_list
    mock_items["reward_count"] = reward_count_list
    mock_items["item_id"] = item_id_list
    mock_items["audience_count"] = audience_count_list
    mock_items["user_latitude"] = user_latitude_list
    mock_items["user_longitude"] = user_longitude_list
    mock_items["author_latitude"] = author_latitude_list
    mock_items["author_longitude"] = author_longitude_list
    mock_items["order_price"] = order_price_list

    item_list_len = 11
    live_item_field_and_size  = [
      ("live_id", 8, "Q"),
      ("timestamp", 4, "I"),
      ("author_id", 4, "I"),
      ("play_time", 2, "H"),
      ("auto_play_time", 2, "H"),
      ("hetu_tag_channel", 4, "I"),
      ("cluster_id", 2, "H"),
      ("label", 2, "H"),
      ("reward", 4, "I"),
      ("reward_count", 2, "H"),
      ("item_id", 8, "Q"),
      ("audience_count", 4, "I"),
      ("user_latitude", 4, "f"),
      ("user_longitude", 4, "f"),
      ("author_latitude", 4, "f"),
      ("author_longitude", 4, "f"),
      ("order_price", 2, "H"),
    ]

    item_size = 0
    for (name, size, format_str) in live_item_field_and_size:
      item_size = item_size + size
    buffer = bytearray(item_size * item_list_len)
    offset = 0
    for i in range(item_list_len):
      for (name, size, format_str) in live_item_field_and_size:
        struct.pack_into(format_str, buffer, offset, mock_items[name][i])
        offset = offset + size

    resp = dict(
      item_key = user_id,
      flatten_items = encode_bytes(buffer)
    )

    return resp, user_id, cluster_id

  def work(self, service, data_gen_func, random_data = False):
    rpc_mocker = service.rpc_mocker()
    if not random_data:
      resp, user_id, cluster_id = self.make_resp_dict(data_gen_func)
      rpc_mocker.mock_rpc_response(service_name="grpc_colossusLiveItemV4", response_json_str=json.dumps(resp))
      return user_id, cluster_id
    else:
      raise("not supported yet")

class RfmFlow(LeafFlow, GsuApiMixin, RfmApiMixin, CommonApiMixin):
  pass

# XXX 提取Mock的逻辑，暂不提取，提取的收益一般
class RfmTestSuite(unittest.TestCase):
    __service = LeafService(kess_name="grpc_CommonLeafTest")
    
    def setUp(self) -> None:
      self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
      LeafService.ENABLE_ATTR_CHECK = False

    def tearDown(self) -> None:
      LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

    @classmethod
    def __init_service(cls, *args):
      for flow in args:
        cls.__service.add_leaf_flows(leaf_flows=[flow])
      return cls.__service.executor()
    
    def test_CalcPlayDurationAndDiffDays(self):
      flow = RfmFlow(name = "light_func_flow")
      
      t = int(time.time())
      D = 24 * 60 * 60

      flow.enrich_attr_by_light_function(
        import_common_attr = ["colossus_play", "colossus_duration", "colossus_time", "request_time_ms"],
        export_common_attr = ["colossus_play_duration", "colossus_diff_days"],
        function_name = "CalcPlayDurationAndDiffDays",
        class_name = "RfmLightFunctionSet"
      ) \
      .enrich_attr_by_lua(
        import_common_attr=["colossus_play", "colossus_duration", "colossus_time", "request_time_ms"],
        export_common_attr=["colossus_play_durationG", "colossus_diff_daysG"],
        function_for_common="calc",
        lua_script="""
        function calc()
            local colossus_play = colossus_play or {}
            local colossus_duration = colossus_duration or {}
            local colossus_time = colossus_time or {}
            local request_time_s = request_time_ms / 1000
            local colossus_play_duration = {}
            local colossus_diff_days = {}
            for i=1, #colossus_play do
                local play = colossus_play[i]
                local duration = colossus_duration[i]
                colossus_play_duration[i] = (play << 24) | duration
                colossus_diff_days[i] = math.floor((request_time_s - colossus_time[i]) / 86400)
            end
            return colossus_play_duration, colossus_diff_days, os.time()*1000
        end
        """
      ) \
      .log_debug_info(
        common_attrs=["colossus_play_duration", "colossus_play_durationG", "colossus_diff_days", "colossus_diff_daysG"],
        for_debug_request_only = False
      )

      leaf = self.__init_service(flow)
      leaf["colossus_play"] = [1,12,3,55,123,53,654,756,234234,432,543] * 10
      leaf["colossus_duration"] = [12,432,534,546,547,324,436,4234,5345,21343,234] * 10
      leaf["colossus_time"] = [t - i * D for i in range(11 * 10)]
      leaf["request_time_ms"] = int(time.time())

      leaf.run("light_func_flow")
      
      print("test_CalcPlayDurationAndDiffDays! -> ", "colossus_play_duration =", leaf["colossus_play_durationG"], "colossus_play_duration =", leaf["colossus_play_duration"])
      self.assertEqual(leaf["colossus_play_durationG"], leaf["colossus_play_duration"])
      self.assertEqual(leaf["colossus_diff_daysG"], leaf["colossus_diff_daysG"])
      
    # 该函数用来 Mock grpc_colossusSimItemV2
    def mock_colossus_sim_v2(self, cur_time):
      t = cur_time
      D = 24 * 60 * 60
      mock_items = {}
      user_id = 666
      photo_id_list = [75694025719, 75823594133, 75862597365, 75990765646, 76036159567,
                        76116630125, 76250349781, 80211142862, 55676225157, 73137452716]
      author_id_list = [671995135, 671995135, 671995135, 671995135, 671995135,
                        671995135, 671995135, 1305330190, 671995135, 2268917398]
      duration_list = [17, 9, 59, 90, 140, 17, 70, 7, 66, 7]
      play_time_list = [1, 122, 26, 144, 2, 1, 2, 1, 3, 2]
      tag_list = [1042, 1097, 1097, 1097, 1097, 5, 1097, 1096, 1096, 0]
      channel_list = [2, 2, 2, 2, 2, 2, 2, 62, 2, 5]
      label_list = [2592, 0, 4864, 4608, 4608, 0, 4672, 0, 4608, 1]
      timestamp_list = [t - 1*D, t-3*D, t-2*D, t-4*D,
                        t-8*D, t-11*D, t-13*D, t-28*D, t-32*D, t-35*D]
      user_latitude_list = [33.605000, 33.605000, 33.604996, 33.604996,
                            33.604996, 33.604996, 33.604996, 33.604996, 33.604996, 33.603462]
      user_longitude_list = [113.266068, 113.266068, 113.266083, 113.266083,
                              113.266083, 113.266083, 113.266083, 113.266083, 113.266083, 113.272285]
      photo_latitude_list = [33.605000, 33.605000, 33.971607, 33.432060,
                              36.142181, 1.000000, 33.975880, 35.045761, -1.000000, 32.531445]
      photo_longitude_list = [113.266068, 113.266068, 113.220009, 113.368248,
                              113.814651, 1.000000, 113.222862, 118.300484, -1.000000, 112.375496]
      mock_items["photo_id"] = photo_id_list
      mock_items["author_id"] = author_id_list
      mock_items["duration"] = duration_list
      mock_items["play_time"] = play_time_list
      mock_items["tag"] = tag_list
      mock_items["channel"] = channel_list
      mock_items["label"] = label_list
      mock_items["timestamp"] = timestamp_list
      mock_items["user_latitude"] = user_latitude_list
      mock_items["user_longitude"] = user_longitude_list
      mock_items["photo_latitude"] = photo_latitude_list
      mock_items["photo_longitude"] = photo_longitude_list

      item_list_len = 10
      item_field_and_size = [
        ("photo_id", 8, "Q"),
        ("author_id", 4, "I"),
        ("duration", 2, "H"),
        ("play_time", 2, "H"),
        ("tag", 4, "I"),
        ("channel", 1, "B"),
        ("label", 2, "H"),
        ("timestamp", 4, "I"),
        ("user_latitude", 4, "f"),
        ("user_longitude", 4, "f"),
        ("photo_latitude", 4, "f"),
        ("photo_longitude", 4, "f"),
      ]
      item_size = 0
      for (_, size, _) in item_field_and_size:
        item_size += size
      buffer = bytearray(item_size * item_list_len)
      offset = 0
      for i in range(item_list_len):
        for (name, size, format_str) in item_field_and_size:
          struct.pack_into(format_str, buffer, offset, mock_items[name][i])
          offset = offset + size

      resp = dict(
          item_key=user_id,
          flatten_items=encode_bytes(buffer)
      )
      rpc_mocker = self.__service.rpc_mocker()
      rpc_mocker.mock_rpc_response(
          service_name="grpc_colossusSimV2", response_json_str=json.dumps(resp))
      return user_id

    # 测试 Protobuf 和 非Protobuf是否有区别
    def test_pb_vs_non_pb(self):
      # Common Attr 
      attrs = ["u_total_play_cnt_28d", "u_total_play_cnt_7d", "u_total_play_time_7d", "u_total_play_time_28d", "u_total_play_ratio_7d", "u_total_play_ratio_28d"]
      attrs += ["u_lasted_video_labels", "u_lasted_video_pts", "u_lasted_video_aids", "u_total_play_cnt_3min",  "u_total_play_cnt_4h"]
      same_time = int(time.time() * 1000)
      user_id = self.mock_colossus_sim_v2(same_time // 1000)
      flow = RfmFlow(name = "use_pb_flow")
      flow.user_id = user_id
      flow.colossus(
        service_name="grpc_colossusSimV2",
        client_type="common_item_client",
        output_attr="colossus_output",
        debug_uids=str(user_id),
        print_items=True
      ) \
      .common_video_colossus_user_author_feature_resp_enricher(
        colossus_photos_attr="colossus_output",
        use_pb_format = 1,
        gen_lasted_video = True
      )
      leaf = self.__init_service(flow)
      leaf.request_time = same_time
      author_id_list = [671995135, 671995135, 671995135, 671995135, 671995135,671995135, 671995135, 1305330190, 671995135, 2268917398]
      for i in range(len(author_id_list)):
        item = leaf.add_item(i)
        item["aId"] = author_id_list[i]
        
      result1 = []
      leaf.run("use_pb_flow")
      for attr in attrs: 
        result1.append(leaf[attr])
        
      flow2 = RfmFlow(name = "not_use_pb_flow")
      flow2.user_id = user_id
      flow2.colossus(
        service_name="grpc_colossusSimV2",
        client_type="common_item_client",
        output_attr="colossus_output2",
        debug_uids=str(user_id),
        parse_to_pb=False,
        print_items=True
      ) \
      .common_video_colossus_user_author_feature_resp_enricher(
        colossus_photos_attr="colossus_output2",
        use_pb_format = 0,
        gen_lasted_video = True
      )
      leaf2 = self.__init_service(flow2)
      leaf2.request_time = same_time
      author_id_list = [671995135, 671995135, 671995135, 671995135, 671995135,671995135, 671995135, 1305330190, 671995135, 2268917398]
      for i in range(len(author_id_list)):
        item = leaf2.add_item(i)
        item["aId"] = author_id_list[i]
        
      result2 = []
      leaf2.run("not_use_pb_flow")
      for attr in attrs: 
        result2.append(leaf2[attr])
        
      # 比较 common attr 是否计算的相等
      for i in range(len(result1)):
        print("Attr: ", attrs[i], result1[i], "VS", result2[i])
        self.assertEqual(result1[i], result2[i], "Caused by " + attrs[i])
        
    def test_same_behavior_light_vs_origin_fix_attr(self):
      mock = MockColossusLiveItemV4()
      user_id, _ = mock.work(self.__service, default_data, False)

      flow = RfmFlow(name = "test_same_behavior_candidate_fix_1")
      flow.user_id = user_id
      flow\
        .colossus(
          service_name="grpc_colossusLiveItemV4",
          client_type="common_item_client",
          output_attr="colossus_v4_output",
          debug_uids=str(user_id),
          print_items=True,
          parse_to_pb=False
        ) \
        .common_live_colossus_author_feature_resp_light_enricher(
          enable_add_fix_ua_watch_attr = 1,
          colossus_resp_attr="colossus_v4_output"
        )
      
      attrs = ["u_live_total_watch_time_28d", "u_reward_amt_3d","u_reward_cnt", "ua_live_total_watch_time", "ua_reward_cnt"]
      attrs += ["ua_live_total_watch_day", "ua_live_avg_day_watch_time", "ua_live_max_watch_time", "ua_live_total_watch_time_28d"]
      attrs += ["ua_live_total_watch_day_fix", "ua_live_avg_day_watch_time_fix"]
      
      leaf = self.__init_service(flow)
      leaf.request_time = int(time.time() * 1000)

      author_id_list =  [666, 666, 666, 2011777021, 1291205167, 941901644, 2899812133, 586064739, 670333339, 2470293124, 916369360]

      for i in range(11):
        item = leaf.add_item(i)
        item["aId"] = author_id_list[i]# 设置要访问的主播ID

      leaf.user_id = user_id
      leaf.run("test_same_behavior_candidate_fix_1")

      result1 = []
      for i in range(len(leaf.items)):
        result1.append([])
        for attr in attrs:
          result1[i].append(leaf.items[i][attr])
      # —————————— FLOW2 ————————————————
      mock = MockColossusLiveItemV4()
      user_id, _ = mock.work(self.__service, default_data, False)
      flow2 = RfmFlow(name = "test_same_behavior_candidate_fix_2")
      flow2.user_id = user_id
      flow2 \
        .colossus(
          service_name="grpc_colossusLiveItemV4",
          client_type="common_item_client",
          output_attr="colossus_v4_output2",
          debug_uids=str(user_id),
          print_items=True,
          parse_to_pb=False
        ) \
        .common_live_colossus_author_feature_resp_enricher(
          colossus_resp_attr="colossus_v4_output2",
          add_reward = 1,
          enable_add_fix_ua_watch_attr = 1,
          cur_time = time.time() * 1000,
        )
      
      leaf2 = self.__init_service(flow2)
      leaf2.request_time = int(time.time() * 1000)
      for i in range(11):
        item = leaf2.add_item(i)
        item["aId"] = author_id_list[i]
      leaf2.user_id = user_id
      leaf2.run("test_same_behavior_candidate_fix_2")

      result2 = []
      for i in range(len(leaf.items)):
        result2.append([])
        for attr in attrs:
          result2[i].append(leaf2.items[i][attr])
      
      for i in range(len(result1)):
        for j in range(len(attrs)):
          self.assertEqual(result1[i][j], result2[i][j])

    # 测试两个Processor是否有一样的表现
    def test_same_behaviour_light_vs_origin(self):
      mock = MockColossusLiveItemV4()
      user_id, _ = mock.work(self.__service, default_data, False)

      flow = RfmFlow(name = "test_same_behavior_candidate1")
      flow.user_id = user_id
      flow\
        .colossus(
          service_name="grpc_colossusLiveItemV4",
          client_type="common_item_client",
          output_attr="colossus_v4_output",
          debug_uids=str(user_id),
          print_items=True,
          parse_to_pb=False
        ) \
        .common_live_colossus_author_feature_resp_light_enricher(
          legacy_switch_attr = 0,
          colossus_resp_attr="colossus_v4_output"
        )
      
      attrs = ["u_live_total_watch_time_28d", "u_reward_amt_3d","u_reward_cnt", "ua_live_total_watch_time", "ua_reward_cnt", "ua_is_follow_attr"]
      attrs += ["ua_live_total_watch_day", "ua_live_avg_day_watch_time", "ua_live_max_watch_time", "ua_live_total_watch_time_28d"]
      
      leaf = self.__init_service(flow)
      leaf.request_time = int(time.time() * 1000)

      author_id_list =  [666, 666, 666, 2011777021, 1291205167, 941901644, 2899812133, 586064739, 670333339, 2470293124, 916369360]

      for i in range(11):
        item = leaf.add_item(i)
        item["aId"] = author_id_list[i]# 设置要访问的主播ID

      leaf.user_id = user_id
      leaf.run("test_same_behavior_candidate1")

      result1 = []
      for i in range(len(leaf.items)):
        result1.append([])
        for attr in attrs:
          result1[i].append(leaf.items[i][attr])
          print(attr,":" ,leaf.items[i][attr])
      # —————————— FLOW2 ————————————————
      mock = MockColossusLiveItemV4()
      user_id, _ = mock.work(self.__service, default_data, False)
      flow2 = RfmFlow(name = "test_same_behavior_candidate2")
      flow2.user_id = user_id
      flow2 \
        .colossus(
          service_name="grpc_colossusLiveItemV4",
          client_type="common_item_client",
          output_attr="colossus_v4_output2",
          debug_uids=str(user_id),
          print_items=True,
          parse_to_pb=False
        ) \
        .common_live_colossus_author_feature_resp_enricher(
          colossus_resp_attr="colossus_v4_output2",
          add_reward = 1,
          cur_time = time.time() * 1000,
        )
      
      leaf2 = self.__init_service(flow2)
      leaf2.request_time = int(time.time() * 1000)
      for i in range(11):
        item = leaf2.add_item(i)
        item["aId"] = author_id_list[i]
      leaf2.user_id = user_id
      leaf2.run("test_same_behavior_candidate2")

      result2 = []
      for i in range(len(leaf.items)):
        result2.append([])
        for attr in attrs:
          result2[i].append(leaf2.items[i][attr])
          print(attr, ":", leaf2.items[i][attr])
      
      for i in range(len(result1)):
        for j in range(len(attrs)):
          self.assertEqual(result1[i][j], result2[i][j], msg="Caused by attrs %s, light ver: %s, origin ver : %s" % (attrs[j], result1[i][j], result2[i][j]))


if __name__ == '__main__':
  suite = unittest.TestSuite()
  suite.addTests(unittest.TestLoader().loadTestsFromName("rfm_api_test.RfmTestSuite"))
  
  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
