#!/usr/bin/env python3
# coding=utf-8
"""
filename: dag.py
description: dag_test
author: x<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-05-24 15:20:00
"""
import unittest
import os
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.common_leaf_util import set_env, get_dynamic_param
from dragonfly.unit_test.util import str2bytes
from dragonfly.visualization.dragon_graph_pb2 import GraphDef
from dragonfly.ext.oversea.oversea_api_mixin import OverseaApiMixin

# 跑全部 testcase: python3 -m unittest dragonfly.unit_test.dag_test.TestGenGraph
# [可选] 单独跑某一个 testcase: python3 -m unittest dragonfly.unit_test.dag_test.TestGenGraph.test_switch


class OverseaFlow(LeafFlow, OverseaApiMixin):
  pass

class TestGenGraph(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __gen_graph(cls, flow, request_type, is_default=False, cfg={}):
    if not isinstance(flow, list):
      cls.__service.add_leaf_flows(leaf_flows=[flow], request_type=request_type, as_default=is_default)
    else:
      cls.__service.add_leaf_flows(leaf_flows=flow, request_type=request_type, as_default=is_default)
    with set_env(CHECK_TALBE_DEPENDENCY='true', DRAGONFLY_CONFIG_VERSION="default_version"):
      cls.__service.draw(to_dragonfly_viz = True, request_type=request_type, mode = "local", **cfg)
    graph_def = GraphDef()
    with open("grpc_CommonLeafTest::{}::default_version.bytes.pbtxt".format(request_type), "rb") as fin:
      file_content = fin.read()
      graph_def.ParseFromString(file_content)
    os.remove("grpc_CommonLeafTest::{}::default_version.bytes.pbtxt".format(request_type))
    return graph_def

  def test_service_info(self):
    flow = (
      LeafFlow(name="test_service_info")
      .fake_retrieve(name="fake_retrieve", num=100, reason=999)
    )
    graph_def = self.__gen_graph(flow, "test_service_info", is_default=True)
    self.assertEqual(graph_def.service_info.service_name, "grpc_CommonLeafTest")
    self.assertEqual(graph_def.service_info.current_request_types, ["test_service_info"])
    self.assertEqual(graph_def.service_info.is_default, True)

  def test_flow_info(self):
    retr_flow = (
      LeafFlow(name="test_flow_info_retr")
      .namespace_("test_flow_info_retr")
      .delegate_retrieve(
        name = "delegate_retrieve",
        kess_service = "grpc_xxx",
        request_num = 10,
        request_type = "test"
      ) \
      .set_attr_default_value(name = "set_retr_score", item_attrs=[{ "name": "score", "type": "double", "value": 0.1}])
    )
    rank_flow = (
      OverseaFlow(name="test_flow_info_rank")
      .namespace_("test_flow_info_rank")
      .sort(name="rank_sort", score_from_attr="score")
      .oversea_limit_by_reason(name="rank_limit", reason_limit_queue = [{"reason": 2401, "limit_size": 500}])
    )
    graph_def = self.__gen_graph([retr_flow, rank_flow], "test_flow_info")
    self.assertEqual(graph_def.node[0].name, "test_flow_info_retr/delegate_retrieve")
    self.assertEqual(graph_def.node[1].name, "test_flow_info_retr/set_retr_score")
    self.assertEqual(graph_def.node[2].name, "test_flow_info_rank/rank_sort")
    self.assertEqual(graph_def.node[3].name, "test_flow_info_rank/rank_limit")
    self.assertEqual(graph_def.node[0].attr["$flow_name"].s, str2bytes("test_flow_info_retr"))
    self.assertEqual(graph_def.node[1].attr["$flow_name"].s, str2bytes("test_flow_info_retr"))
    self.assertEqual(graph_def.node[2].attr["$flow_name"].s, str2bytes("test_flow_info_rank"))
    self.assertEqual(graph_def.node[2].attr["$flow_name"].s, str2bytes("test_flow_info_rank"))
    self.assertEqual(graph_def.node[0].input, [])
    self.assertEqual(graph_def.node[1].input, [])
    self.assertEqual(graph_def.node[2].input, ["test_flow_info_retr/set_retr_score:0", "test_flow_info_retr/delegate_retrieve:1"])
    self.assertEqual(graph_def.node[3].input, ["test_flow_info_rank/rank_sort:1"])
    self.assertEqual(graph_def.node[0].op, "delegate_retrieve")
    self.assertEqual(graph_def.node[1].op, "set_attr_default_value")
    self.assertEqual(graph_def.node[2].op, "sort_by_score")
    self.assertEqual(graph_def.node[3].op, "oversea_limit_by_reason")
    self.assertEqual(graph_def.node[0].ext_module_name, "common")
    self.assertEqual(graph_def.node[1].ext_module_name, "common")
    self.assertEqual(graph_def.node[2].ext_module_name, "common")
    self.assertEqual(graph_def.node[3].ext_module_name, "oversea")


  def test_switch(self):
    # 使用 switch_(user_type) case_(1) case_(2) default_ 的 flow
    flow = (
      LeafFlow(name="test_switch")
      .get_abtest_params(name="get_user_type", ab_params=[("user_type", 0.0)])
      .switch_("user_type")
        .case_(1)
          .fake_retrieve(name="fake_retrieve_user_type1", num=100, reason=999)
        .case_(2)
          .fake_retrieve(name="fake_retrieve_user_type2", num=100, reason=999)
        .default_()
          .fake_retrieve(name="fake_retrieve_user_type_default", num=100, reason=999)
      .end_switch_()
      .deduplicate(name="deduplicate") \
      .limit(10, name = "limit")
    )
    graph_def = self.__gen_graph(flow, "test_switch")
    # test name of each node
    self.assertEqual(graph_def.node[0].name, "get_user_type")
    self.assertEqual(graph_def.node[1].name.startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[2].name, "fake_retrieve_user_type1")
    self.assertEqual(graph_def.node[3].name.startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[4].name, "fake_retrieve_user_type2")
    self.assertEqual(graph_def.node[5].name.startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[6].name, "fake_retrieve_user_type_default")
    self.assertEqual(graph_def.node[7].name, "deduplicate")
    self.assertEqual(graph_def.node[8].name, "limit")
    # test input of each node
    self.assertEqual(graph_def.node[0].input, [])
    self.assertEqual(graph_def.node[1].input, ["get_user_type:0"])
    self.assertEqual(graph_def.node[2].input[0].startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[3].input, ["get_user_type:0"])
    self.assertEqual(graph_def.node[4].input[0].startswith("_branch_controller"), True)
    self.assertEqual(len(graph_def.node[5].input), 2)
    self.assertEqual(graph_def.node[5].input[0].startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[5].input[1].startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[6].input[0].startswith("_branch_controller"), True)
    self.assertEqual(graph_def.node[7].input, ["fake_retrieve_user_type1:1", "fake_retrieve_user_type2:1", "fake_retrieve_user_type_default:1"])
    self.assertEqual(graph_def.node[8].input, ["deduplicate:0"])

  def test_retrieve(self):
    flow = (
      LeafFlow(name="test_retrieve")
      .fake_retrieve(
        name = "fake_retrieve1",
        num=100,
        reason=999)
      .fake_retrieve(
        name = "fake_retrieve2",
        num=100,
        reason=999)
      .deduplicate(name="deduplicate") \
      .limit(10, name = "limit")
    )
    graph_def = self.__gen_graph(flow, "test_retrieve")
    self.assertEqual(len(graph_def.node[0].input), 0)
    self.assertEqual(len(graph_def.node[1].input), 0)
    self.assertEqual(len(graph_def.node[2].input), 2)
    self.assertEqual(graph_def.node[3].input, ["deduplicate:0"])

  def test_simple_graph(self):
    flow = (
      LeafFlow(name="test_simple_graph")
      .get_abtest_params(name="get_ab", ab_params=[("enable_one", 0.0)])
      .if_("enable_one > 0.0")
        .do_nothing()
      .end_if_()
    )
    graph_def = self.__gen_graph(flow, "test_simple_graph")

    self.assertEqual(graph_def.node[1].input, ["get_ab:0"])
    self.assertEqual(len(graph_def.node[2].input), 1)
    self.assertEqual(graph_def.node[2].input[0].startswith("_branch_controller"), 1)

  def test_hide_ab_param_node(self):
    flow = (
      LeafFlow(name="test_hide_ab_param_node")
      .get_abtest_params(name="get_ab", ab_params=[("enable_one", 0.0)])
      .if_("enable_one > 0.0")
        .do_nothing()
      .end_if_()
    )
    graph_def = self.__gen_graph(flow, "test_hide_ab_param_node", False, {"hide_ab_param_node": True})

    self.assertEqual(graph_def.node[0].name.startswith("_branch_controller"), True)
    self.assertEqual(
      graph_def.node[0].attr["_input_ab_params_"].list.s,
      [str2bytes("enable_one")],
    )

  def test_branch_common_attr(self):
    flow = (
      LeafFlow(name="test_branch_common_attr")
      .get_abtest_params(name="get_ab", ab_params=[("enable_one", 0.0)])
      .gen_common_attr_by_lua(name="useless_zero", attr_map={"common_attr1": "0"})
      .gen_common_attr_by_lua(name="default_one", attr_map={"common_attr1": "1"})
      .if_("enable_one > 0.0")
        .gen_common_attr_by_lua(name="set_two", attr_map={"common_attr1": "2"})
        .perflog_attr_value(
          name="use_attr0",
          check_point="default.ranking1",
          common_attrs=["common_attr1"],
        )
      .end_if_()
      .perflog_attr_value(
        name="use_attr1",
        check_point="default.ranking1",
        common_attrs=["common_attr1"],
      )
      .gen_common_attr_by_lua(
        name="modify_attr", attr_map={"common_attr1": "w_pxtr or common_attr1"}
      )
      .perflog_attr_value(
        name="use_attr2",
        check_point="default.ranking2",
        common_attrs=["common_attr1"],
      )
    )
    graph_def = self.__gen_graph(flow, "test_branch_common_attr")

    # graph_def 样子
    #                                  ---------> [use_attr0]
    #                                /\             /\
    #                                /              /
    #                               /              /
    # [get_ab] -> [_branch_controller] -> [set_two] -> [use_attr1]
    #                                             \   /\
    #                                              \  /
    #                                               \/
    #                                               /\
    #                                              /  \
    #                                             /   \/
    #                                 [default_one] -> [modify_attr] -> [use_attr2]
    #
    #         [useless_zero]
    #

    self.assertEqual(len(graph_def.node[2].input), 0)
    self.assertEqual(len(graph_def.node), 9)
    self.assertEqual(len(graph_def.node[8].input), 1)

    self.assertEqual(graph_def.node[8].input[0], "modify_attr:0")
    self.assertEqual(graph_def.node[7].input, ["default_one:0", "set_two:0"])
    self.assertEqual(graph_def.node[6].input, ["default_one:0", "set_two:0"])
    self.assertEqual(
      graph_def.node[5].input, ["_branch_controller_6812EF9C:0", "set_two:0"]
    )

  def test_branch_item_attr(self):
    # item attr 与 common attr 不一样，因为给 item attr 赋值的 processor 不一定赋值了所有的 item。
    flow = (
      LeafFlow(name="test_branch_item_attr")
      .get_abtest_params(name="get_ab", ab_params=[("enable_one", 0.0)])
      .set_attr_value(
        name="may_useless_default_score",
        item_attrs=[{"name": "score", "type": "double", "value": 0.0}],
      )
      .set_attr_value(
        name="default_score",
        item_attrs=[{"name": "score", "type": "double", "value": 0.0}],
      )
      .if_("enable_one > 0.0")
        .calc_weighted_sum(
          name="reco_score",
          channels=[
            {"name": "pctr", "weight": "{{w_pctr}}"},
          ],
          output_item_attr="score",
        )
      .else_()
        .calc_weighted_sum(
          name="ad_score",
          target_item={"is_ad": 1},
          channels=[
            {"name": "p_price", "weight": "{{w_pprice}}"},
          ],
          output_item_attr="score",
        )
      .end_if_()
      .perflog_attr_value(name="perf_score", item_attrs=["score"])
      .calc_weighted_sum(
        name="modify_score",
        channels=[
          {"name": "pxtr", "weight": "{{w_pxtr}}"},
          {"name": "score", "weight": "{{w_pscore}}"},
        ],
        output_item_attr="score",
      )
      .sort(name="score_sort", score_from_attr="score")
    )
    graph_def = self.__gen_graph(flow, "test_branch_item_attr")

    #
    # [ad_score] [reco_score] [may_useless_default_score] [default_score]
    #    ||           ||   {4 * 2条线，画不开了}    ||            ||
    #    \/           \/    {4 * 2条线，画不开了}   \/            \/
    #  [[[        [[perf_score]]   平级   [[modify_score]]        ]]]
    #                                          ||
    #                                          \/
    #                                     [[score_sort]]
    #

    self.assertEqual(len(graph_def.node), 10)
    self.assertEqual(len(graph_def.node[7].input), 4)
    self.assertEqual(graph_def.node[7].input[0], "may_useless_default_score:0")
    self.assertEqual(graph_def.node[7].input[1], "default_score:0")
    self.assertEqual(graph_def.node[7].input[2], "reco_score:0")
    self.assertEqual(graph_def.node[7].input[3], "ad_score:0")
    self.assertEqual(len(graph_def.node[9].input), 1)
    self.assertEqual(graph_def.node[9].input, ["modify_score:0"])


  def test_enrich_sub_flow(self):
    sub_flow = (
      LeafFlow(name = "enrich_sub_flow") \
      .calc_weighted_sum(
        name = "calc_score1",
        channels = [
          { "name": "ori_score", "weight": "{{w_pctr}}" },
        ],
        output_item_attr = "score1",
      ) \
      .calc_weighted_sum(
        name = "calc_final_score",
        channels = [
          { "name": "score1", "weight": "{{w_score}}" },
        ],
        output_item_attr = "final_score",
      )
    )
    flow = (
      LeafFlow(name="test_enrich_sub_flow")
      .get_abtest_params(name="get_w_pctr", ab_params=[("w_pctr", 0.2), ("w_score", 0.1)])
      .set_attr_default_value(name = "set_score", item_attrs=[{ "name": "ori_score", "type": "double", "value": 0.1}])
      .enrich_by_sub_flow(name = "ns::enrich_score", sub_flow = sub_flow)
      .sort(name = "sort_by_final_score", score_from_attr = "final_score")
    )
    graph_def = self.__gen_graph(flow, "test_enrich_sub_flow")

    self.assertEqual(len(graph_def.node), 7)
    self.assertEqual(graph_def.node[2].name, "ns/enrich_score/_ENTER_")
    self.assertEqual(set(graph_def.node[2].input), set(["get_w_pctr:0", "get_w_pctr:1", "set_score:0"]))
    self.assertEqual(graph_def.node[3].name, "ns/enrich_score/calc_score1")
    self.assertEqual(graph_def.node[4].name, "ns/enrich_score/calc_final_score")
    self.assertEqual(graph_def.node[5].name, "ns/enrich_score/_EXIT_")
    self.assertEqual(graph_def.node[5].input, ["ns/enrich_score/calc_final_score:0"])
    self.assertEqual(graph_def.node[6].name, "sort_by_final_score")
    self.assertEqual(graph_def.node[6].input, ["ns/enrich_score/_EXIT_:0"])
    # test flow_name of each node
    self.assertEqual(graph_def.node[0].attr["$flow_name"].s, str2bytes("test_enrich_sub_flow"))
    self.assertEqual(graph_def.node[1].attr["$flow_name"].s, str2bytes("test_enrich_sub_flow"))
    self.assertEqual(graph_def.node[2].attr["$flow_name"].s, str2bytes("test_enrich_sub_flow"))
    self.assertEqual(graph_def.node[3].attr["$flow_name"].s, str2bytes("enrich_sub_flow"))
    self.assertEqual(graph_def.node[4].attr["$flow_name"].s, str2bytes("enrich_sub_flow"))
    self.assertEqual(graph_def.node[5].attr["$flow_name"].s, str2bytes("test_enrich_sub_flow"))
    self.assertEqual(graph_def.node[6].attr["$flow_name"].s, str2bytes("test_enrich_sub_flow"))


  # 这个 test case 同时包括了 arrange sub flow
  def test_sub_flow(self):
    sub_flow = (
      LeafFlow(name = "retr_sub_flow1") \
      .fake_retrieve(name = "fake_retrieve1", num=100, reason=999) \
      .set_attr_default_value(name = "set_score1", item_attrs=[{ "name": "score", "type": "double", "value": 0.1}])
    )
    sub_flow2 = (
      LeafFlow(name = "retr_sub_flow2") \
      .fake_retrieve(name = "fake_retrieve2", num=100, reason=999) \
      .set_attr_default_value(name = "set_score2", item_attrs=[{ "name": "score", "type": "double", "value": 0.1}])
    )
    filter_flow = (
      LeafFlow(name = "retr_filter_flow") \
      .filter_by_attr(
        name = "filter_by_p_id",
        attr_name="p_id",
        remove_if="<=",
        compare_to=0,
        remove_if_attr_missing=True,
      )
    )
    flow = (
      LeafFlow(name="test_sub_flow")
      .get_abtest_params(name="get_retr_num", ab_params=[("retr_num1", 2), ("retr_num2", 0)])
      .fake_retrieve(name = "fake_retrieve2", num=100, reason=999)
      .retrieve_by_sub_flow(name = "ns::sub_flow_retr1", sub_flow = sub_flow, retrieve_num=get_dynamic_param("retr_num1"))
      .retrieve_by_sub_flow(name = "ns::sub_flow_retr2", sub_flow = sub_flow2, retrieve_num=get_dynamic_param("retr_num2"))
      .fake_retrieve(name = "fake_retrieve3", num=100, reason=999)
      .deduplicate(name = "rank::dedup")
      .get_item_attr_by_local_index(name = "rank::get_index_attr", attrs = ["p_id"])
      .arrange_by_sub_flow(name = "rank::arrange", sub_flow = filter_flow, expected_partition_size = 100)
      .sort(name="rank::score_sort", score_from_attr="score")
    )
    graph_def = self.__gen_graph(flow, "test_sub_flow")

    expected_nodes = [
      'get_retr_num',
      'fake_retrieve2',
      'ns/sub_flow_retr1/_ENTER_', 'ns/sub_flow_retr1/fake_retrieve1', 'ns/sub_flow_retr1/set_score1', 'ns/sub_flow_retr1/_EXIT_',
      'ns/sub_flow_retr2/_ENTER_', 'ns/sub_flow_retr2/fake_retrieve2', 'ns/sub_flow_retr2/set_score2', 'ns/sub_flow_retr2/_EXIT_',
      'fake_retrieve3',
      'rank/dedup',
      'rank/get_index_attr',
      'rank/arrange/_ENTER_', 'rank/arrange/filter_by_p_id', 'rank/arrange/_EXIT_',
      'rank/score_sort']

    expected_inputs = [
      [], [],
      ['get_retr_num:0'], [], [], ['ns/sub_flow_retr1/set_score1:0', 'ns/sub_flow_retr1/fake_retrieve1:1'],
      ['get_retr_num:1'], [], [], ['ns/sub_flow_retr2/set_score2:0', 'ns/sub_flow_retr2/fake_retrieve2:1'],
      [],
      ['fake_retrieve2:1', 'ns/sub_flow_retr1/_EXIT_:2', 'ns/sub_flow_retr2/_EXIT_:2', 'fake_retrieve3:1'],
      [],
      ['rank/get_index_attr:0', 'rank/dedup:0'], ['rank/arrange/_ENTER_:0', 'rank/arrange/_ENTER_:1'], ['rank/arrange/filter_by_p_id:0'],
      ['ns/sub_flow_retr1/_EXIT_:1', 'ns/sub_flow_retr2/_EXIT_:1', 'rank/arrange/_EXIT_:0']]

    self.assertEqual([node.name for node in graph_def.node], expected_nodes)
    self.assertEqual(
      [node.input for node in graph_def.node],
      [expected_input for expected_input in expected_inputs])

  def test_simple_table(self):
    flow = (
      LeafFlow(name="test_simple_table")
      .fake_retrieve(name = "retr1::fake_retrieve", num=100, reason=999, item_table = "table1") \
      .fake_retrieve(name = "retr2::fake_retrieve", num=100, reason=999, item_table = "table2") \
      .set_attr_default_value(name = "es1::set_score", item_attrs=[{ "name": "ori_score", "type": "double", "value": 0.1}], item_table= "table1")
      .set_attr_default_value(name = "es2::set_score", item_attrs=[{ "name": "ori_score", "type": "double", "value": 0.1}], item_table= "table2")
      .sort(name = "rank::sort1", score_from_attr = "ori_score", item_table = "table1")
      .sort(name = "rank::sort2", score_from_attr = "ori_score", item_table = "table2")
      .limit(1, name = "rank::limit1", item_table = "table1")
      .limit(2, name = "rank::limit2", item_table = "table2")
    )
    graph_def = self.__gen_graph(flow, "test_simple_table")

    expected_inputs = [
      [], [], [], [],
      ['es1/set_score:0', 'retr1/fake_retrieve:1'],
      ['es2/set_score:0', 'retr2/fake_retrieve:1'],
      ['rank/sort1:1'],
      ['rank/sort2:1']
    ]

    self.assertEqual(
      [node.input for node in graph_def.node],
      [expected_input for expected_input in expected_inputs])


  def test_sub_flow_table(self):
    sub_flow = (
      LeafFlow(name = "retr_table1", item_table = "table1") \
      .fake_retrieve(name = "fake_retrieve1", num=100, reason=999) \
      .set_attr_default_value(name = "set_score1", item_attrs=[{ "name": "score", "type": "double", "value": 0.1}])
    )
    sub_flow2 = (
      LeafFlow(name = "retr_table2", item_table = "table2") \
      .fake_retrieve(name = "fake_retrieve2", num=100, reason=999) \
      .set_attr_default_value(name = "set_score2", item_attrs=[{ "name": "score", "type": "double", "value": 0.1}])
    )
    filter_flow1 = (
      LeafFlow(name = "filter_table1", item_table = "table1") \
      .get_item_attr_by_local_index(name = "get_index_attr1", attrs = ["p_id"])
      .filter_by_attr(
        name = "filter_by_p_id1",
        attr_name="p_id",
        remove_if="<=",
        compare_to=0,
        remove_if_attr_missing=True,
      )
    )
    filter_flow2 = (
      LeafFlow(name = "filter_table2", item_table = "table2") \
      .get_item_attr_by_local_index(name = "get_index_attr2", attrs = ["p_id"])
      .filter_by_attr(
        name = "filter_by_p_id2",
        attr_name="p_id",
        remove_if="<=",
        compare_to=0,
        remove_if_attr_missing=True,
      )
    )
    flow = (
      LeafFlow(name="test_sub_flow_table")
      .get_abtest_params(name="get_retr_num", ab_params=[("retr_num1", 2), ("retr_num2", 0)])
      .retrieve_by_sub_flow(name = "ns::retr_table1", sub_flow = sub_flow, retrieve_num=get_dynamic_param("retr_num1"), item_table = "table1")
      .retrieve_by_sub_flow(name = "ns::retr_table2", sub_flow = sub_flow2, retrieve_num=get_dynamic_param("retr_num2"), item_table = "table2")
      .deduplicate(name = "rank::dedup1", item_table = "table1")
      .deduplicate(name = "rank::dedup2", item_table = "table2")
      .arrange_by_sub_flow(name = "rank::filter_arrange1", sub_flow = filter_flow1, expected_partition_size = 100, item_table = "table1")
      .arrange_by_sub_flow(name = "rank::filter_arrange2", sub_flow = filter_flow2, expected_partition_size = 100, item_table = "table2")
      .sort(name="rank::score_sort1", score_from_attr="score", item_table = "table1")
      .sort(name="rank::score_sort2", score_from_attr="score", item_table = "table2")
    )
    graph_def = self.__gen_graph(flow, "test_sub_flow_table")

    expected_nodes = [
      'get_retr_num',
      'ns/retr_table1/_ENTER_', 'ns/retr_table1/fake_retrieve1', 'ns/retr_table1/set_score1', 'ns/retr_table1/_EXIT_',
      'ns/retr_table2/_ENTER_', 'ns/retr_table2/fake_retrieve2', 'ns/retr_table2/set_score2', 'ns/retr_table2/_EXIT_',
      'rank/dedup1', 'rank/dedup2',
      'rank/filter_arrange1/_ENTER_', 'rank/filter_arrange1/get_index_attr1', 'rank/filter_arrange1/filter_by_p_id1', 'rank/filter_arrange1/_EXIT_',
      'rank/filter_arrange2/_ENTER_', 'rank/filter_arrange2/get_index_attr2', 'rank/filter_arrange2/filter_by_p_id2', 'rank/filter_arrange2/_EXIT_',
      'rank/score_sort1', 'rank/score_sort2']
    expected_inputs = [
      [],
      ['get_retr_num:0'], [], [], ['ns/retr_table1/set_score1:0', 'ns/retr_table1/fake_retrieve1:1'],
      ['get_retr_num:1'], [], [], ['ns/retr_table2/set_score2:0', 'ns/retr_table2/fake_retrieve2:1'],
      ['ns/retr_table1/_EXIT_:2'], ['ns/retr_table2/_EXIT_:2'],
      ['rank/dedup1:0'], [], ['rank/filter_arrange1/get_index_attr1:0', 'rank/filter_arrange1/_ENTER_:0'], ['rank/filter_arrange1/filter_by_p_id1:0'],
      ['rank/dedup2:0'], [], ['rank/filter_arrange2/get_index_attr2:0', 'rank/filter_arrange2/_ENTER_:0'], ['rank/filter_arrange2/filter_by_p_id2:0'],
      ['ns/retr_table1/_EXIT_:1', 'rank/filter_arrange1/_EXIT_:0'], ['ns/retr_table2/_EXIT_:1', 'rank/filter_arrange2/_EXIT_:0']]

    self.assertEqual([node.name for node in graph_def.node], expected_nodes)
    self.assertEqual(
      [node.input for node in graph_def.node],
      [expected_input for expected_input in expected_inputs])
    # test flow_name for each node
    print([node.attr["$flow_name"].s for node in graph_def.node])
    for i in range(0, 2):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("test_sub_flow_table"))
    for i in range(2, 4):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("retr_table1"))
    for i in range(4, 6):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("test_sub_flow_table"))
    for i in range(6, 8):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("retr_table2"))
    for i in range(8, 12):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("test_sub_flow_table"))
    for i in range(12, 14):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("filter_table1"))
    for i in range(14, 16):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("test_sub_flow_table"))
    for i in range(16, 18):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("filter_table2"))
    for i in range(18, 21):
      self.assertEqual(graph_def.node[i].attr["$flow_name"].s, str2bytes("test_sub_flow_table"))

if __name__ == "__main__":
  suite = unittest.TestSuite()

  suite.addTests(
    unittest.TestLoader().loadTestsFromName("dag_test.TestGenGraph")
  )

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
