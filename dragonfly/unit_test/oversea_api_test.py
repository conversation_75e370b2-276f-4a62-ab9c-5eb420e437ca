#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="oversea" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/mio_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.oversea_api_test.TestFlowFunc.xxxx

import time
import copy
import unittest
# import numpy as np
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.oversea.oversea_api_mixin import OverseaApiMixin

# LeafService.I_AM_MASTER_DRIVER = True

class MyFlow(LeafFlow, OverseaApiMixin):
  pass

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_oversea_audit_mark_match(self):
    flow = MyFlow(name="test_oversea_audit_mark_match") \
      .oversea_audit_mark_match(
        audit_type_name = "p_audit_mark_type",
        audit_mark_name = "p_audit_mark_mark",
        audit_bucket_name = "p_audit_mark_bucket",
        bucket_attr = "bucket",
        match_type = "num",
        match_value_config = [
          {
            "type_value": 5,
            "mark_set": "{{dynamic_set5}}"
          },
          {
            "mark_set": "{{all_dynamic_set}}"
          },
          {
            "type_value": 3,
            "mark_set": [3]
          },
        ],
        output_item_attr = "out1"
      ) \
      .oversea_audit_mark_match(
          audit_type_name = "p_audit_mark_type",
          audit_mark_name = "p_audit_mark_mark",
          match_type = "bit",
          match_value_config = [
            {
              "type_value": 5,
              "mark_set": [2]
            }
          ],
          output_item_attr = "out2"
      )

    leaf = self.__init_service(flow)
    leaf["bucket"] = 8
    leaf["dynamic_set5"] = [1, 2, 6, 8]
    leaf["all_dynamic_set"] = [2]

    for i in range(5):
      item = leaf.add_item(i)
      item["p_audit_mark_bucket"] = [8]
      item["p_audit_mark_type"] = [i]
      item["p_audit_mark_mark"] = [i]

    # 固定 type
    for i in range(5, 10):
      item = leaf.add_item(i)
      item["p_audit_mark_bucket"] = [8]
      item["p_audit_mark_type"] = [5]
      item["p_audit_mark_mark"] = [i]

    leaf.run("test_oversea_audit_mark_match")
    results1 = [None, None,    1,    1, None, None,    1, None,    1, None]
    results2 = [None, None, None, None, None, None,    1,    1, None, None]

    i = 0
    for item in leaf.items:
      self.assertEqual(item["out1"], results1[i])
      self.assertEqual(item["out2"], results2[i])
      i += 1

  def test_oversea_mix_rank_beam_search_retrieve(self):
    flow = MyFlow(name = "test_oversea_mix_rank_beam_search_retrieve") \
      .oversea_mix_rank_beam_search(
        reco_list_attr = "reco_item_key_list",
        extra_lists = [
          {
            "type": "live",
            "item_key_attr": "live_item_key_list"
          },
          {
            "type": "inno_live",
            "item_key_attr":"inno_live_item_key_list"
          },
          {
            "type": "ad",
            "item_key_attr":"ad_item_key_list",
            "item_type_attr": "ad_item_type_list",
            "type_enum_count" : 2,
            "type_enums" : [2, 5]
          },
        ],
        is_first_request_attr = "is_first_request",
        min_gap_attr = "min_gap_str",
        min_start_idx_attr = "min_start_idx_str",
        last_item_gap_attr = "last_item_gap_str",
        reason = 666,
        output_item_attr = "beam_search_res_list"
      )
    leaf = self.__init_service(flow)
    leaf["reco_item_key_list"] = [1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007]
    leaf["live_item_key_list"] = [2000]
    leaf["inno_live_item_key_list"] = [3000]
    leaf["ad_item_key_list"] = [4000,5000,4001,4002,5001]
    leaf["ad_item_type_list"] = [2,5,2,2,5]
    leaf["is_first_request"] = 0
    leaf["min_gap_str"] = "live-live:2,ad#2-live:2,live-ad#5:1,ad#2-ad#2:4,ad#2-ad#5:2,ad#5-ad#5:2,inno_live-inno_live:4,inno_live-live:2,inno_live-ad#2:2,inno_live-ad#5:2"
    # leaf["min_gap_str"] = "live-live:0,ad#2-live:0,live-ad#5:0,ad#2-ad#2:4,ad#2-ad#5:1,ad#5-ad#5:1"
    # leaf["min_gap_str"] = "live-live:2,ad-live:2,ad-ad:4"
    leaf["min_start_idx_str"] = "live:4,ad#2:0,ad#5:0,inno_live:0"
    # leaf["min_start_idx_str"] = "live:2,ad#2:3,ad#5:3"
    # leaf["min_start_idx_str"] = "live:4,ad:0"
    leaf["last_item_gap_str"] = "live:4,ad#2:2,ad#5:2,inno_live:2"
    # leaf["last_item_gap_str"] = "live:1,ad#2:11,ad#5:0"
    # leaf["last_item_gap_str"] = "live:4,ad:2"

    leaf.run("test_oversea_mix_rank_beam_search_retrieve")
    for item in leaf.items:
      print(item.item_key, " : ", item["beam_search_res_list"])
    self.assertEqual(len(leaf.items), 492)
  
  def test_refactor_rr_bucket_ensemble_score(self):
    flow = MyFlow(name = "test_refactor_rr_bucket_ensemble_score") \
      .calc_rr_bucket_ensemble_score(
        formula_version = 2,
        bucket_attr = "p_duration_ms",
        bucket_num_attr = "rr_des_bucket_num",
        weight_string_attr = "rr_des_bucket_weight",
        bucket_points_attr = "rr_des_bucket_points",
        channels = [
            {
                "name": "p_mmu_score_br_evr",
                "weight": "{{w_go_mmu_evr}}",
                "hyper_scala": "{{go_climb_es_hyper_scala}}"
            }, {
                "name": "p_mmu_score_br_avg_play_duration",
                "weight": "{{w_go_mmu_dur}}",
                "hyper_scala": "{{go_climb_es_hyper_scala}}"
            }
        ],
        output_attr = "rr_des_score",
      )
    leaf = self.__init_service(flow)

    p_duration_ms=[58978,130125,50016,24125,17230]
    evr = [0.1, 0.2, 0.3, 0.4, 0.5]
    duration = [0.1, 0.2, 0.3, 0.4, 0.5]
    res = [0.0, 0.09353228019882195, 0.2, 0.0, 0.2]
    leaf["rr_des_bucket_num"] = 2
    leaf["rr_des_bucket_weight"]="0:0.2,1:0.2,2:0.4"
    leaf["rr_des_bucket_points"]="0,7,14,30,60,90"
    leaf["w_go_mmu_evr"] = 0.4
    leaf["w_go_mmu_dur"] = 0.6
    leaf["go_climb_es_hyper_scala"] = 4

    for i in range(5):
      item = leaf.add_item(i)
      item["p_duration_ms"]=p_duration_ms[i]
      item["p_mmu_score_br_evr"] = evr[i]
      item["p_mmu_score_br_avg_play_duration"] = duration[i]

    leaf.run("test_refactor_rr_bucket_ensemble_score")
    for i in range(5):
      self.assertAlmostEqual(leaf.items[i]["rr_des_score"], res[i], delta=1e-6)

  def test_oversea_save_topk_to_item(self):
    # 利用 item_key 的高 8 位 item_type, 判断元素类型，避免本 retriever 插入的非常规元素进入 topk_list 中
    fake_item_keys = [(11 << 56) | 123, (12 << 56) | 456, (13 << 56) | 789, (14 << 56) | 789, (15 << 56) | 789]

    # 测例1: 测试 list_length 动态参数的使用 & 只定义 to_table ，使用默认 from_table 的情况
    flow_1 = MyFlow(name = "test_oversea_save_topk_to_item_1") \
      .fake_retrieve(
        item_keys = fake_item_keys,
      ) \
      .set_attr_value(
        common_attrs = [
          {
            "name": "topk",
            "type": "int",
            "value": 4
          }
        ]
      ) \
      .oversea_save_topk_to_item(
        reason = 777,
        attr_name = "item_keys",
        list_length = "{{topk}}",
        to_table = "to_table"
      ) \

    leaf = self.__init_service(flow_1)
    leaf.run("test_oversea_save_topk_to_item_1")

    leaf.set_main_table(table_name="to_table")
    recorded_item_list = copy.deepcopy(leaf.items[0]["item_keys"])
    leaf.set_main_table(table_name="")
    real_item_list = [item.item_key for item in leaf.items][:4]

    self.assertEqual(recorded_item_list, real_item_list)

    # 测例2: 测试 shuffle 后能否正常跳过对非常规 item & 同时定义 from_table 与 to_table
    flow_2 = MyFlow(name = "test_oversea_save_topk_to_item_2") \
      .fake_retrieve(
        item_keys = fake_item_keys,
        item_table = "some_table"
      ) \
      .oversea_save_topk_to_item(
        reason = 123,
        attr_name = "item_keys",
        list_length = 3,
        from_table = "some_table",
        to_table = "some_table"
      ) \
      .shuffle(item_table = "some_table") \
      .oversea_save_topk_to_item(
        reason = 456,
        attr_name = "item_keys",
        list_length = 5,
        from_table = "some_table",
        to_table = "another_table"
      )

    leaf = self.__init_service(flow_2)
    leaf.run("test_oversea_save_topk_to_item_2")

    leaf.set_main_table(table_name="another_table")
    recorded_item_list = copy.deepcopy(leaf.items[0]["item_keys"])
    leaf.set_main_table(table_name="some_table")
    real_item_list = [item.item_key for item in leaf.items if item["item_keys"] == None]

    self.assertEqual(recorded_item_list, real_item_list)

  def test_oversea_aggregate_tag_weight(self):
    flow = MyFlow(name="oversea_aggregate_tag_weight_test") \
      .oversea_aggregate_tag_weight(
        tag_item_attr = "tag",
        weight_item_attr = "weight",
        output_tag_common_attr = "aggr_tag_list",
        output_weight_common_attr = "aggr_weight_list",
        tag_count_threshold = 3,
      )

    leaf = self.__init_service(flow)

    tags = [1001, 1002, 1001, 1001, 1002]
    weights = [1.0, 2.0, 3.0, 4.0, 5.0]

    for i in range(5):
      item = leaf.add_item(i)
      item["tag"] = tags[i]
      item["weight"] = weights[i]

    leaf.run("oversea_aggregate_tag_weight_test")
    self.assertEqual(len(leaf['aggr_tag_list']), 1)
    self.assertEqual(len(leaf['aggr_weight_list']), 1)
    self.assertEqual(leaf['aggr_tag_list'][0], 1001)
    self.assertAlmostEqual(leaf['aggr_weight_list'][0], 8.0)

  def test_oversea_tag_extract_by_type(self):
    flow = MyFlow(name="oversea_tag_extract_by_type_test") \
      .oversea_tag_extract_by_type(
        tag_item_attr="tag_list",
        type_item_attr="type_list",
        extract_type_list=[3, 5],
        output_item_attr="extract_tag_list"
      )

    leaf = self.__init_service(flow)

    for i in range(5):
      item = leaf.add_item(i)
      item["tag_list"] = [1000001, 1000002, 1000003]
      item["type_list"] = [3, 3, 4]

    leaf.run("oversea_tag_extract_by_type_test")
    for item in leaf.items:
      self.assertEqual(len(item["extract_tag_list"]), 2)
      self.assertEqual(item["extract_tag_list"][0], 1000001)
      self.assertEqual(item["extract_tag_list"][1], 1000002)

  def test_oversea_inject_context_info(self):
    flow = MyFlow(name = "test_oversea_inject_context_info") \
      .fake_retrieve(num=500, reason=999) \
      .set_attr_default_value(
        item_attrs=[
          { "name": "fr_unify_score", "type": "double", "value": 0.5 },
          {
            "name": "int_value",
            "type": "int",
            "value": 123
          },
          {
            "name": "float_value",
            "type": "double",
            "value": 0.5
          },
          {
            "name": "string_value",
            "type": "string",
            "value": "h119"
          },
          {
            "name": "int_list_value",
            "type": "int_list",
            "value": [1, 2, 3]
          },
          {
            "name": "float_list_value",
            "type": "double_list",
            "value": [0.5, 1.5, 2.5]
          },
          {
            "name": "string_list_value",
            "type": "string_list",
            "value": ["1s", "2s", "3s"]
          }
        ]) \
      .build_protobuf(
        inputs=(
          [
            { "item_attr": "fr_unify_score", "path": "debug_score.final_score" },
          ]
        ),
        output_item_attr="SerializedContextInfo",
        as_string=True,
        class_name="ksib.reco.ContextInfo",
      ) \
      .oversea_inject_context_info(
        context_info_attr = "SerializedContextInfo",
        inject_configs = [
          { "context_attr": "int_value", "rename_to": "int_v" },
          { "context_attr": "float_value"},
          { "context_attr": "string_value" },
          { "context_attr": "int_list_value" },
          { "context_attr": "float_list_value" },
          { "context_attr": "string_list_value" }
        ],
        output_item_attr = "SerializedContextInfo"
      ) \
      .parse_protobuf_from_string(
        is_common_attr = False,
        input_attr="SerializedContextInfo",
        output_attr="SerializedContextInfoPb",
        class_name="ksib::reco::ContextInfo",
      ) \
      .enrich_with_protobuf(
        is_common_attr = False,
        from_extra_var = "SerializedContextInfoPb",
        attrs = [
          dict(name="int_val", path="context_attrs", sample_attr_name="int_v"),
          dict(name="float_val", path="context_attrs", sample_attr_name="float_value"),
          dict(name="string_val", path="context_attrs", sample_attr_name="string_value"),
          dict(name="int_list_val", path="context_attrs", sample_attr_name="int_list_value"),
          dict(name="float_list_val", path="context_attrs", sample_attr_name="float_list_value"),
          dict(name="string_list_val", path="context_attrs", sample_attr_name="string_list_value"),
        ]
      )
    leaf = self.__init_service(flow)
    leaf.run("test_oversea_inject_context_info")
    for i, item in enumerate(leaf.items):
      self.assertEqual(item["int_val"], item["int_value"])
      self.assertEqual(item["float_val"], item["float_value"])
      self.assertEqual(item["string_val"], item["string_value"])
      self.assertListEqual(item["int_list_val"], item["int_list_value"])
      self.assertListEqual(item["float_list_val"], item["float_list_value"])
      self.assertListEqual(item["string_list_val"], item["string_list_value"])


  def test_ksib_profile_filter(self):
    request_time = int(time.time())
    mock_slide_enter_list = [
        {"value": 1, "time": request_time - 1*60},
        {"value": 2, "time": request_time - 3*60},
        {"value": 1, "time": request_time - 30*60},
        {"value": 3, "time": request_time - 21*60},
        {"value": 5, "time": request_time - 19*60},
        {"value": 3, "time": request_time - 6*60},
        {"value": 1, "time": request_time - 7*60},
        {"value": 5, "time": request_time - 4*60},
        {"value": 5, "time": request_time - 30*24*3600},
        {"value": 1, "time": request_time - 90*24*3600},
    ]
    slide_enter_length = len(mock_slide_enter_list)
    mock_video_stat_list = [
        {"value": 1, "time": request_time - 1*60},
        {"value": 2, "time": request_time - 3*60},
        {"value": 1, "time": request_time - 30*60},
        {"value": 3, "time": request_time - 18*60},
        {"value": 5, "time": request_time - 30*60},
        {"value": 3, "time": request_time - 30*60},
        {"value": 1, "time": request_time - 30*60},
        {"value": 5, "time": request_time - 30*60},
        {"value": 5, "time": request_time - 7*60},
        {"value": 1, "time": request_time - 90*24*3600},
    ]
    play_length = len(mock_video_stat_list)

    flow = MyFlow(name="test_ksib_profile_filter")
    # 构造 reader_info pb
    for i in range(slide_enter_length):
      flow\
        .build_protobuf(
          class_name="ksib::reco::ActionItem",
          inputs=
              [dict(common_attr=f"user_time_{i}", path="time_ms")],
          output_common_attr=f"action_item_{i}"
        )
    for i in range(play_length):
      flow\
        .build_protobuf(
          class_name="ksib::reco::VideoPlayingStat",
          inputs=
              [dict(common_attr=f"user_time_play_{i}", path="server_timestamp")],
          output_common_attr=f"video_stat_{i}"
        )
    flow\
      .build_protobuf(
        class_name="ksib::reco::ReaderInfo",
        inputs=[
            dict(common_attr=f"action_item_{i}", path="user_profile_v1.slide_enter_list", append=True)
            for i in range(slide_enter_length)
        ] + [
          dict(common_attr=f"video_stat_{i}", path="user_profile_v1.video_playing_stat", append=True)
            for i in range(play_length)
        ],
        output_common_attr="reader_info"
      )\
      .ksib_profile_filter( # adapt snack data
        reader_info_attr="reader_info",
        filter_time_s=1200,
      )\
      .enrich_with_protobuf(
        name="ksib_pf_attr_from_reader_info",
        from_extra_var="reader_info",
        is_common_attr=True,
        attrs=[
            dict(path="user_profile_v1.slide_enter_list.time_ms",
                 name="slide_enter_list_time"),
            dict(path="user_profile_v1.video_playing_stat.server_timestamp",
                 name="video_playing_stat_time"),
        ]
      )

    leaf = self.__init_service(flow)
    leaf.request_time = request_time * 1000
    for i in range(len(mock_slide_enter_list)):
        leaf[f"user_time_{i}"] = mock_slide_enter_list[i]["time"] * 1000
        leaf[f"user_time_play_{i}"] = mock_video_stat_list[i]["time"] * 1000
    leaf.run("test_ksib_profile_filter")
    self.assertLess(leaf['slide_enter_list_time'][0], 1000*(request_time - 1200))
    self.assertLess(leaf['video_playing_stat_time'][0], 1000*(request_time - 1200))
    self.assertLess(leaf['video_playing_stat_time'][1], 1000*(request_time - 1200))

  def test_oversea_select_topn_index(self):
    flow = MyFlow(name="oversea_select_topn_index_test") \
      .oversea_select_topn_index(
        select_type = "hamming",
        source_fingerprint_list_attr = "fingerprint_list",
        item_fingerprint_attr = "fingerprint",
        top_n = 2,
        output_attr = "fg_topn_hamming",
        output_type = 5,
        save_to_item_context = True,
      ) \
      .oversea_select_topn_index(
        select_type = "hamming",
        source_fingerprint_list_attr = "fingerprint_list",
        item_fingerprint_attr = "fingerprint",
        top_n = 2,
        output_attr = "fg_topn_hamming_common",
        output_type = 5,
        save_to_item_context = False,
      ) \
      .oversea_select_topn_index(
        select_type = "match",
        source_fingerprint_list_attr = "fingerprint_list",
        item_fingerprint_attr = "fingerprint",
        top_n = 2,
        output_attr = "fg_topn_match",
        output_type = 5,
        save_to_item_context = True,
      ) \

    leaf = self.__init_service(flow)

    leaf["fingerprint_list"] = [1, 2, 3, 4, 5]

    fingerprints = [1, 2, 3, 4, 5]
    topn_hamming = [[0, 2], [1, 2], [2, 0], [3, 4], [4, 0]]
    topn_hamming_common = [0, 2, 1, 2, 2, 0, 3, 4, 4, 0]
    topn_match = [[0], [1], [2], [3], [4]]

    for i in range(5):
      item = leaf.add_item(i)
      item["fingerprint"] = fingerprints[i]

    leaf.run("oversea_select_topn_index_test")
    print("fg_topn_hamming_common", leaf["fg_topn_hamming_common"])
    self.assertListEqual(leaf["fg_topn_hamming_common"], topn_hamming_common)
    for i, item in enumerate(leaf.items):
      print("fg_topn_hamming", i, item["fg_topn_hamming"])
      self.assertListEqual(item["fg_topn_hamming"], topn_hamming[i])
      print("fg_topn_match", i, item["fg_topn_match"])
      self.assertListEqual(item["fg_topn_match"], topn_match[i])

  def test_oversea_pxtrs_moving_avg(self):
    debias_attrs = ["fr_es_lvr", "fr_es_vtr", "fr_es_epsr",
                        "fr_es_ltr", "fr_es_wtr", "fr_es_cmr"]
    flow = MyFlow(name="oversea_pxtrs_moving_avg") \
          .enrich_attr_by_light_function(
              name="durationDebiasPxtrCalc",
              import_item_attr=debias_attrs,
              import_common_attr=['fr_duration_debias_window_size'],
              export_item_attr=['dur_debias_'+attr for attr in debias_attrs],
              function_name="KsibFrDurDebiasScore",
              class_name="KsibSefoLightFunction",
          )
    leaf = self.__init_service(flow)
    for i in range(10):
      item = leaf.add_item(i)
      for pxtr in debias_attrs:
        item[pxtr] = (10-i) / 10
    leaf['fr_duration_debias_window_size'] = 2

    leaf.run("oversea_pxtrs_moving_avg")
    for i, item in enumerate(leaf.items):
      for pxtr in debias_attrs:
        if i < 9:
          self.assertEqual(round(item['dur_debias_'+pxtr], 5), 0.05)
        else:
          self.assertEqual(round(item['dur_debias_'+pxtr], 5), 0.0)

  def test_oversea_fr_vtr_reshpe(self):
    flow = MyFlow(name="oversea_fr_vtr_reshape") \
          .enrich_attr_by_light_function(
              name="overseaFrVtrReshapeCalc",
              import_item_attr=['fr_vtr'],
              import_common_attr=['fr_vtr_influence'],
              export_item_attr=['reshape_fr_vtr'],
              function_name="GenerateFrVtrReshapeScore",
              class_name="KsibSefoLightFunction",
          )
    leaf = self.__init_service(flow)
    reshape_vtrs = [1,2.00081,2.29851,2.6405,3.29665,3.78716,4.35065,7.57736,8.7048,10]
    fr_vtrs = [0.5,0.55,0.56,0.57,0.586,0.596,0.606,0.646,0.656,0.666]
    leaf['fr_vtr_influence'] = 10.0
    for i in range(10):
      item = leaf.add_item(i)
      item["fr_vtr"] = fr_vtrs[i]

    leaf.run("oversea_fr_vtr_reshape")
    for i, item in enumerate(leaf.items):
      print(item['fr_vtr'],i, item['reshape_fr_vtr'])
      self.assertEqual(round(item["reshape_fr_vtr"], 2), round(reshape_vtrs[i], 2))

  def test_oversea_fr_wdsr_reshape(self):
    flow = MyFlow(name="oversea_fr_wdsr_reshape") \
          .enrich_attr_by_light_function(
              name="overseaFrWdsrReshape",
              import_item_attr=['fr_wdsr'],
              import_common_attr=['fr_wdsr_influence'],
              export_item_attr=['reshape_fr_wdsr'],
              function_name="GenerateFrWdsrReshapeScore",
              class_name="KsibSefoLightFunction",
          )
    leaf = self.__init_service(flow)
    reshape_wdsrs = [1,2.00081,2.29851,2.6405,3.29665,3.78716,4.35065,7.57736,8.7048,10]
    fr_wdsrs = [0.5,0.55,0.56,0.57,0.586,0.596,0.606,0.646,0.656,0.666]
    leaf['fr_wdsr_influence'] = 10.0
    for i in range(10):
      item = leaf.add_item(i)
      item["fr_wdsr"] = fr_wdsrs[i]

    leaf.run("oversea_fr_wdsr_reshape")
    for i, item in enumerate(leaf.items):
      print(item['fr_wdsr'],i,item["reshape_fr_wdsr"])
      self.assertEqual(round(item["reshape_fr_wdsr"], 2), round(reshape_wdsrs[i], 2))

  def test_oversea_mix_rank_brand_safety(self):
    flow = MyFlow(name="test_oversea_mix_rank_brand_safety") \
          .enrich_attr_by_light_function(
              import_item_attr=['bs_seq_item_key', 'bs_seq_item_type',"brand_safety_inventory_list"],
              import_common_attr=['country_code', 'reco_item_key_list', 'ad_item_key_list', 'reco_p_audit_mark_mark_list', 'brand_safety_360_1', 'brand_safety_360_2', 'brand_safety_76_1', 'brand_safety_76_2'],
              export_item_attr=['bs_invalid_brand_safety_seq_key'],
              function_name="GetBrandSafetyFilterKey",
              class_name="KsibMixRankLightFunction",
          )
    leaf = self.__init_service(flow)
    
    leaf["country_code"] = 360
    leaf["reco_item_key_list"] = [72207698896573861, 72207701302223181, 72207701272339145, 72207701308332498, 72207701263895479, 72207701304413112, 72207699697048950, 72207700178183001]
    leaf["ad_item_key_list"] = [144265278088333611, 144265278088088614, 144265278087444517, 144265295246426878]
    leaf["reco_p_audit_mark_mark_list"] = ["1,1,1,1,1", "1,1,1", "1,1,1", "1,1,300009", "1,0,1,1", "1,1,1,1", "1,1,0,1,1", "1,1,1,1"]
    leaf["brand_safety_360_1"] = "8,13,16,21,25,100,102,35,36,23,108,106,103,104,105,19,32,111,8819,0"
    leaf["brand_safety_360_2"] = "8,13,16,21,25,100,102,35,36,23,108,106,0"
    leaf["brand_safety_76_1"] = "13,17,16,8932,8,14,35,36,21,25,19,8890,103,8891,8819,0"
    leaf["brand_safety_76_2"] = "13,17,16,8932,8,14,35,36,21,25,19,0"
    bs_seq_item_key_list = [[72207698896573861, 72207701302223181, 144265278088333611, 72207701272339145, 72207701308332498, 72207701263895479, 72207701304413112, 144265278088088614, 72207699697048950, 72207700178183001],
                            [72207700223898193, 72207701249389455, 144265278088333611, 72207700337624485, 144265294803719223, 72207701293576919, 72207701285077564, 72207701301026937, 144265278088309544, 72207701296574006, 72207701281923812],
                            [72207700223898193, 72207701249389455, 144265278088333611, 72207700337624485, 72207701293576919, 144265294803719223, 72207701285077564, 144265278088309544, 72207701301026937, 72207701296574006, 72207701281923812],
                            [72207700223898193, 72207701249389455, 144265278088333611, 72207700337624485, 144265294803719223, 72207701293576919, 216172782332530495, 72207701285077564, 72207701301026937, 144265278088309544, 72207701296574006, 72207701281923812],
                            [72207700223898193, 72207701249389455, 144265278088333611, 72207700337624485, 144265294803719223, 72207701293576919, 72207701285077564, 72207701301026937, 72207701296574006, 144265278088309544, 72207701281923812]]
    bs_seq_item_type_list = [[1, 1, 2, 1, 1, 1, 1, 2, 1, 1],
                            [1, 1, 2, 1, 2, 1, 1, 1, 2, 1, 1],
                            [1, 1, 2, 1, 1, 2, 1, 2, 1, 1, 1],
                            [1, 1, 2, 1, 2, 1, 3, 1, 1, 2, 1, 1],
                            [1, 1, 2, 1, 2, 1, 1, 1, 1, 2, 1]]
    brand_safety_inventory_lists = [[0, 0, 2, 0, 0, 0, 0, 0, 0, 0], 
                                  [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0],
                                  [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0],
                                  [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                                  [0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0]]
    for i in range(5):
      item = leaf.add_item(i)
      item['bs_seq_item_key'] = bs_seq_item_key_list[i]
      item['bs_seq_item_type'] = bs_seq_item_type_list[i]
      item['brand_safety_inventory_list'] = brand_safety_inventory_lists[i]

    leaf.run("test_oversea_mix_rank_brand_safety")

    for item in leaf.items:
      print(item.item_key, " : ", item["bs_invalid_brand_safety_seq_key"])

  def test_mix_rank_calc_list_scrore(self):
    flow = MyFlow(name="test_mix_rank_calc_list_scrore") \
          .enrich_attr_by_light_function(
              import_item_attr=['seq_pos_calib_list', 'seq_rank_score_list'],
              export_item_attr=['seq_score'],
              function_name="CalcItemListMutiAndSum",
              class_name="KsibMixRankLightFunction",
          )
    leaf = self.__init_service(flow)
    pos_discount_list = [[1.0, 1.0, 2.0, 1.0, 1.0],
                          [1.0, 1.0, 2.0, 1.0, 2.0]]
    seq_item_score = [[1.0, 1.0, 2.0, 1.0, 1.0],
                      [1.0, 1.0, 2.0, 1.0, 2.0]]
    for i in range(2):
      item = leaf.add_item(i)
      item['seq_pos_calib_list'] = pos_discount_list[i]
      item['seq_rank_score_list'] = seq_item_score[i]

    leaf.run("test_mix_rank_calc_list_scrore")
    for item in leaf.items:
      print(item.item_key, " : ", item["seq_score"])

  def test_oversea_scaling_boost(self):

    def rescale(score_list, lower_bound, upper_bound):
      scale_range = upper_bound - lower_bound
      max_score = max(score_list)
      rescaled_list = copy.copy(score_list)
      for idx,score in enumerate(score_list):
        if(score > lower_bound and max_score > upper_bound):
          rescaled_list[idx] = lower_bound + scale_range * (score - 1) / (max_score - 1)
      return rescaled_list
    
    config_1 = {
      "boost_queue_configs": {
          # 消费队列
          "consume": {
            "factors": ["consume_factor_1", "consume_factor_2"]
          },
          "ecommerce": {
            "factors": ["ecommerce_factor_1"]
          },
        },
        "scale_bound": [5.0, 10.0],
        "boost_score_attr": "pre_rank_score"
    }
    flow = MyFlow(name="test_oversea_scaling_boost") \
      .oversea_scaling_boost(
        **config_1
      )
    consume_list = [i*2.0 for i in range(1,11)]
    ecom_list = [i*3.0 for i in range(1,11)]
    rescaled_consume_list = rescale(consume_list, 5.0, 10.0)
    rescale_ecom_list = rescale(ecom_list, 5.0, 10.0)
    final_score = [rescaled_consume_list[i]*rescale_ecom_list[i] for i in range(10)]
    leaf = self.__init_service(flow)
    for i in range(1,11):
      item = leaf.add_item(i)
      item["pre_rank_score"] = 1.0
      item["consume_factor_1"] = 1.0
      item["consume_factor_2"] = i * 2.0
      item["ecommerce_factor_1"] = i * 3.0
    
    leaf.run("test_oversea_scaling_boost")
    for i, item in enumerate(leaf.items):
      self.assertEqual(item["pre_rank_score"], final_score[i])

  def test_oversea_multi_reason_random_select(self):
    flow = MyFlow("test_oversea_multi_reason_random_select") \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=1) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=2) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=3) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=4) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=5) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=6) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=7) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=8) \
                .fake_retrieve(item_keys=[111, 112, 113, 114, 115, 116, 117, 118, 119], reason=9) \
                .deduplicate(append_reason_to="reason_list") \
                .oversea_multi_reason_random_select(
                  reason_list_attr="reason_list",
                  default_reason=999
                )
    leaf = self.__init_service(flow)
    leaf.run("test_oversea_multi_reason_random_select")
    diff_reason_num = 0
    for item in leaf.items:
      print(f"reason: {item.reason}")
      print(f"reason_list: {item['reason_list']}")
      print(f"item_key: {item.item_key}, item_type: {item.item_type}")
      print('')
      self.assertTrue(item.reason in item["reason_list"])
      if item.reason != item["_REASON_"]:
        diff_reason_num = diff_reason_num + 1
    self.assertTrue(diff_reason_num > 0)    # 随机结果不便于验证，于是判断至少有一个结果和初值不同，全相同概率为 1 / 9**9

  def test_oversea_embedding_similarity_calculate(self):
    import random
    flow = MyFlow("test_oversea_embedding_similarity_calculate") \
            .oversea_embedding_similarity_calculate(
              candidate_embedding_attr_type = "item_attr",
              candidate_embedding_attr = "candidate_embedding",
              target_embedding_attr_type = "common_attr",
              target_embedding_attr = "target_embedding",
              output_similarity_attr = "similarity_score_cross",
              embedding_dim = 128,
            ) \
            .oversea_embedding_similarity_calculate(
              candidate_embedding_attr_type = "common_attr",
              candidate_embedding_attr = "target_embedding",
              target_embedding_attr_type = "item_attr",
              target_embedding_attr = "candidate_embedding",
              output_similarity_attr = "similarity_score_cross_reverse",
              embedding_dim = 128,
            ) \
            .oversea_embedding_similarity_calculate(
              candidate_embedding_attr_type = "common_attr",
              candidate_embedding_attr = "target_embedding",
              target_embedding_attr_type = "common_attr",
              target_embedding_attr = "target_embedding",
              output_similarity_attr = "similarity_score_inner_common",
              embedding_dim = 128,
            ) \
            .oversea_embedding_similarity_calculate(
              candidate_embedding_attr_type = "item_attr",
              candidate_embedding_attr = "candidate_embedding",
              target_embedding_attr_type = "item_attr",
              target_embedding_attr = "candidate_embedding",
              output_similarity_attr = "similarity_score_inner_item",
              embedding_dim = 128,
            )
    leaf = self.__init_service(flow)
    for i in range(3):
      item = leaf.add_item(i)
      item["candidate_embedding"] = [random.uniform(0.0, 1.0) for _ in range(128)]
    leaf["target_embedding"] = [random.uniform(0.0, 1.0) for _ in range(384)]
    leaf.run("test_oversea_embedding_similarity_calculate")

    self.assertEqual(len(leaf["similarity_score_cross_reverse"]), 9)
    self.assertEqual(len(leaf["similarity_score_inner_common"]), 9)
    self.assertTrue(abs(max(leaf["similarity_score_inner_common"]) - 1.0) < 1e-4)
    for i, item in enumerate(leaf.items):
      self.assertEqual(len(item["similarity_score_inner_item"]), 3)
      self.assertTrue(abs(max(item["similarity_score_inner_item"]) - 1.0) < 1e-4)
      self.assertEqual(len(item["similarity_score_cross"]), 3)
      for j in range(3):
        self.assertTrue(abs(item["similarity_score_cross"][j] - leaf["similarity_score_cross_reverse"][i + j * 3]) < 1e-4)

  def test_oversea_double_list_analyse(self):
    import numpy as np
    flow = MyFlow("test_oversea_double_list_analyse") \
          .oversea_double_list_analyse(
            is_common_attr = True,
            input_attr = "similarity_score",
            mappings = [
              { "metric": "mean", "output_attr": "similarity_score_mean"},
              { "metric": "topN_mean", "output_attr": "similarity_score_topN_mean", "topN": "{{topN}}"},
              { "metric": "max", "output_attr": "similarity_score_max"},
              { "metric": "min", "output_attr": "similarity_score_min"},
            ]
          ) \
          .oversea_double_list_analyse(
            is_common_attr = False,
            input_attr = "similarity_score",
            mappings = [
              { "metric": "greater_than", "output_attr": "similarity_score_greater_than", "compared_to": "{{compared_to}}"},
              { "metric": "not_greater_than", "output_attr": "similarity_score_not_greater_than", "compared_to": "{{compared_to}}"},
              { "metric": "less_than", "output_attr": "similarity_score_less_than","compared_to": "{{compared_to}}"},
              { "metric": "not_less_than", "output_attr": "similarity_score_not_less_than", "compared_to": "{{compared_to}}"},
            ]
          )
    leaf = self.__init_service(flow)
    leaf["compared_to"] = 0.5
    leaf["topN"] = 3
    leaf["similarity_score"] = [0.1, 0.3, 0.5, 0.7, 0.9]
    item = leaf.add_item(0)
    item["similarity_score"] = [0.2, 0.4, 0.6, 0.8, 1.0]
    leaf.run("test_oversea_double_list_analyse")

    common_data = np.array(leaf["similarity_score"])
    item_data = np.array(leaf.items[0]["similarity_score"])
    sorted_common_data = sorted(common_data, reverse=True)

    self.assertEqual(leaf["similarity_score_mean"], np.mean(common_data))
    self.assertEqual(leaf["similarity_score_topN_mean"], np.mean(sorted_common_data[:leaf["topN"]]))
    self.assertEqual(leaf["similarity_score_max"], np.max(common_data))
    self.assertEqual(leaf["similarity_score_min"], np.min(common_data))
    self.assertEqual(leaf.items[0]["similarity_score_greater_than"], np.sum(item_data > leaf["compared_to"]))
    self.assertEqual(leaf.items[0]["similarity_score_not_greater_than"], np.sum(item_data <= leaf["compared_to"]))
    self.assertEqual(leaf.items[0]["similarity_score_less_than"], np.sum(item_data < leaf["compared_to"]))
    self.assertEqual(leaf.items[0]["similarity_score_not_less_than"], np.sum(item_data >= leaf["compared_to"]))
    
  def test_oversea_multi_constraints_optimize(self):
    flow = MyFlow(name="test_oversea_multi_constraints_optimize") \
          .copy_item_meta_info(
            save_item_seq_to_attr="rank",
          ) \
          .enrich_attr_by_lua(
            import_item_attr = ["rank"],
            function_for_item = "item",
            export_item_attr = ["rank_score", "rank_score_v1" ],
            lua_script = """
              function item()
                return 5000.0 - rank, 4000.0 - rank
              end
            """
          ) \
          .gen_random_item_attr(
            attr_name = "opt_score",
            attr_type = "double"
          ) \
          .gen_random_item_attr(
            attr_name = "opt_score_v1",
            attr_type = "double"
          ) \
          .enrich_attr_by_lua(
            import_item_attr = ["opt_score", "opt_score_v1"],
            function_for_item = "item",
            export_item_attr = ["opt_score", "opt_score_v1"],
            lua_script = """
              function item()
                return 1000 * opt_score, 1000 * opt_score_v1
              end
            """
          ) \
          .enrich_attr_by_lua(
            import_item_attr = ["rank"],
            function_for_item = "item",
            export_item_attr = ["flag"],
            lua_script = """
              function item()
                if rank < 2000 then
                    return 1
                  else 
                    return 0
                  end
              end
            """
          ) \
          .oversea_multi_constraints_optimize(
            optimize_attr = "opt_score",
            limit_size = 2000,
            max_loop_times = 4,
            epsilon = 0.00001,
            constraints = [{
              "name" : "rank_score",
              "type" : "roi",
              "value" : 2.0
            }],
            output_attr = "go_flag",
            find_optimal = "find_optimal"
          ) \
          .oversea_multi_constraints_optimize(
            optimize_attr = "opt_score",
            limit_size = 2000,
            max_loop_times = 4,
            epsilon = 0.00001,
            constraints = [{
              "name" : "rank_score",
              "type" : "roi",
              "value" : 2.0
            }],
            output_attr = "go_flag_v1",
            find_optimal = "find_optimal_v1",
            output_opt_lambdas = "output_opt_lambdas",
            is_strict = 1
          ) \
          .oversea_multi_constraints_optimize(
            optimize_attr = "opt_score_v1",
            limit_size = 1999,
            max_loop_times = 5,
            epsilon = 0.00001,
            constraints = [{
              "name" : "rank_score",
              "type" : "roi",
              "value" : 3.0
            },
            {
              "name" : "rank_score_v1",
              "type" : "roi",
              "value" : 3.0
            }],
            output_attr = "go_flag_v2",
            find_optimal = "find_optimal_v2",
            output_opt_lambdas = "output_opt_lambdas1",
            is_strict = 1
          )\
          .pack_item_attr(
            item_source = {
              "reco_results": True
            },
            mappings = [{
              "from_item_attr": "rank_score",
              "to_common_attr": "go_booster_consume_score_list",
            },
            {
              "from_item_attr": "opt_score",
              "to_common_attr": "go_booster_eco_flag_list",
            },
            {
              "from_item_attr": "flag",
              "to_common_attr": "go_boost_score_list",
            },{
              "from_item_attr": "go_flag",
              "to_common_attr": "go_flag_list",
            }]
          ) \
          .enrich_attr_by_lua(
            import_common_attr = [],
            export_common_attr = ["truncate_num","eco_go_booster_roi_thres"],
            function_for_common = "cal",
            lua_script = """
                function cal()
                return 2000,2.0
                end
            """
          ) \
          .enrich_attr_by_light_function(
            import_common_attr=[
                "truncate_num", 
                "eco_go_booster_roi_thres",
                "go_booster_consume_score_list", 
                "go_booster_eco_flag_list", 
                "go_boost_score_list"
            ],
            export_common_attr=[
                "go_boost_score_list", 
                "go_booster_eco_gain", 
                "go_booster_consume_loss", 
                "go_booster_roi", 
                "go_booster_trade_num"
            ],
            function_name="KsibEnrichGoBoostV3Reuslt",
            class_name="KsibSefoLightFunction",
          ) \

    leaf = self.__init_service(flow)
    for i in range(5000):
      leaf.add_item(i)
    
    leaf.run("test_oversea_multi_constraints_optimize")
    for i in range(5000):
      self.assertEqual(leaf["go_boost_score_list"][i],leaf["go_flag_list"][i])
    self.assertEqual(len(leaf['output_opt_lambdas']), 2)
    self.assertEqual(len(leaf['output_opt_lambdas1']), 3)

  def test_oversea_multi_constraints_optimize_v2(self):
    flow = MyFlow(name="test_oversea_multi_constraints_optimize_v2") \
          .copy_item_meta_info(
            save_item_seq_to_attr="rank",
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr",
              limit_size = 300,
              max_loop_times = 10,
              epsilon = 0.00001,
              constraints = [
              {
                "name" : "ltr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              },
              {
                "name" : "fpr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              }],
              is_strict = 1.0,
              sub_lambda_version = 0,
              tolerance_version = 0,
              output_attr = "output_flag_v0",
              output_opt_lambdas = "output_opt_lambdas_v0",
              find_solution = "find_solution_v0",
              extra_output_attrs = "extra_output_attrs_v0",
          )\
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav0",
              import_common_attr=[
                  "extra_output_attrs_v0"
              ],
              export_common_attr=['find_optimal_v0', 'find_suboptimal_v0', 'upgrade_opt_v0'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v0 = extra_output_attrs_v0 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v0 >= 3 then 
                          find_optimal = extra_output_attrs_v0[1]
                          find_suboptimal = extra_output_attrs_v0[2]
                          upgrade_opt = extra_output_attrs_v0[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr1",
              limit_size = 300,
              max_loop_times = 10,
              epsilon = 0.00001,
              constraints = [
              {
                "name" : "ltr1",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              },
              {
                "name" : "fpr1",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              }],
              is_strict = 1.0,
              sub_lambda_version = 0,
              tolerance_version = 0,
              output_attr = "output_flag_v1",
              output_opt_lambdas = "output_opt_lambdas_v1",
              find_solution = "find_solution_v1",
              extra_output_attrs = "extra_output_attrs_v1",
          ) \
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav1",
              import_common_attr=[
                  "extra_output_attrs_v1"
              ],
              export_common_attr=['find_optimal_v1', 'find_suboptimal_v1', 'upgrade_opt_v1'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v1 = extra_output_attrs_v1 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v1 >= 3 then 
                          find_optimal = extra_output_attrs_v1[1]
                          find_suboptimal = extra_output_attrs_v1[2]
                          upgrade_opt = extra_output_attrs_v1[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr2",
              limit_size = 300,
              max_loop_times = 10,
              epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume",
                "type" : "roi",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              },
              {
                "name" : "ltr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              },
              {
                "name" : "fpr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                "beta_param": 0.1
              }],
              is_strict = 1.0,
              sub_lambda_version = 1,
              tolerance_version = 1,
              output_attr = "output_flag_v2",
              output_opt_lambdas = "output_opt_lambdas_v2",
              find_solution = "find_solution_v2",
              extra_output_attrs = "extra_output_attrs_v2",
          ) \
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav2",
              import_common_attr=[
                  "extra_output_attrs_v2"
              ],
              export_common_attr=['find_optimal_v2', 'find_suboptimal_v2', 'upgrade_opt_v2'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v2 = extra_output_attrs_v2 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v2 >= 3 then 
                          find_optimal = extra_output_attrs_v2[1]
                          find_suboptimal = extra_output_attrs_v2[2]
                          upgrade_opt = extra_output_attrs_v2[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr2",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume",
                "type" : "roi",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ltr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              sub_lambda_version = 1,
              tolerance_version = 1,
              output_attr = "output_flag_v3",
              output_opt_lambdas = "output_opt_lambdas_v3",
              find_solution = "find_solution_v3",
              extra_output_attrs = "extra_output_attrs_v3",
          ) \
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav3",
              import_common_attr=[
                  "extra_output_attrs_v3"
              ],
              export_common_attr=['find_optimal_v3', 'find_suboptimal_v3', 'upgrade_opt_v3'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v3 = extra_output_attrs_v3 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v3 >= 3 then 
                          find_optimal = extra_output_attrs_v3[1]
                          find_suboptimal = extra_output_attrs_v3[2]
                          upgrade_opt = extra_output_attrs_v3[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr2",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume",
                "type" : "roi",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ltr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 1,
              # tolerance_version = 1,
              output_attr = "output_flag_v4",
              output_opt_lambdas = "output_opt_lambdas_v4",
              find_solution = "find_solution_v4",
              extra_output_attrs = "extra_output_attrs_v4",
          ) \
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav4",
              import_common_attr=[
                  "extra_output_attrs_v4"
              ],
              export_common_attr=['find_optimal_v4', 'find_suboptimal_v4', 'upgrade_opt_v4'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v4 = extra_output_attrs_v4 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v4 >= 3 then 
                          find_optimal = extra_output_attrs_v4[1]
                          find_suboptimal = extra_output_attrs_v4[2]
                          upgrade_opt = extra_output_attrs_v4[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr3",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume3",
                "type" : "roi",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ltr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 1,
              # tolerance_version = 1,
              output_attr = "output_flag_v5",
              output_opt_lambdas = "output_opt_lambdas_v5",
              find_solution = "find_solution_v5",
              extra_output_attrs = "extra_output_attrs_v5",
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr3",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "ltr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ftr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 1,
              # tolerance_version = 1,
              output_attr = "output_flag_v6",
              output_opt_lambdas = "output_opt_lambdas_v6",
              find_solution = "find_solution_v6",
              extra_output_attrs = "extra_output_attrs_v6",
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr3",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume3",
                "type" : "roi",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ltr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ftr3",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 1,
              # tolerance_version = 1,
              output_attr = "output_flag_v7",
              output_opt_lambdas = "output_opt_lambdas_v7",
              find_solution = "find_solution_v7",
              extra_output_attrs = "extra_output_attrs_v7",
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "ltr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.99,
              #   "beta_param": 0.1
              },
              {
                "name" : "fpr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.99,
              #   "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 0,
              # tolerance_version = 0,
              output_attr = "output_flag_v8",
              output_opt_lambdas = "output_opt_lambdas_v8",
              find_solution = "find_solution_v8",
              extra_output_attrs = "extra_output_attrs_v8",
          ) \
          .enrich_attr_by_lua(
              name="test_constraints_v2_luav8",
              import_common_attr=[
                  "extra_output_attrs_v8"
              ],
              export_common_attr=['find_optimal_v8', 'find_suboptimal_v8', 'upgrade_opt_v8'],
              function_for_common="split_extra_output",
              lua_script="""
                  function split_extra_output()
                      local extra_output_attrs_v8 = extra_output_attrs_v8 or {}
                      local find_optimal
                      local find_suboptimal
                      local upgrade_opt
                      if #extra_output_attrs_v8 >= 3 then 
                          find_optimal = extra_output_attrs_v8[1]
                          find_suboptimal = extra_output_attrs_v8[2]
                          upgrade_opt = extra_output_attrs_v8[3]
                      end
                      return find_optimal, find_suboptimal, upgrade_opt
                  end
              """
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr",
              limit_size = 600,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "ltr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
              #   "beta_param": 0.1
              },
              {
                "name" : "fpr",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
              #   "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 0,
              # tolerance_version = 0,
              output_attr = "output_flag_v9",
              output_opt_lambdas = "output_opt_lambdas_v9",
              find_solution = "find_solution_v9",
              extra_output_attrs = "extra_output_attrs_v9",
          ) \
          .oversea_multi_constraints_optimize_v2(
              optimize_attr = "vtr2",
              limit_size = 300,
              max_loop_times = 10,
              # epsilon = 0.00001,
              constraints = [
              {
                "name" : "consume",
                "type" : "roi",
                # value置为0, 不生效
                "value" : 0,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "ltr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              },
              {
                "name" : "fpr2",
                "type" : "delta",
                "value" : 0.98,
                "tolerance_value" : 0.97,
                # "beta_param": 0.1
              }],
              is_strict = 1.0,
              # sub_lambda_version = 1,
              # tolerance_version = 1,
              output_attr = "output_flag_v10",
              output_opt_lambdas = "output_opt_lambdas_v10",
              find_solution = "find_solution_v10",
              extra_output_attrs = "extra_output_attrs_v10",
          )

    vtrlist = [1.13872, 0.437731, 1.29792, 0.930305, 0.849535, 0.497348, 0.645977, 0.735212, 0.683287, 0.354096, 0.447019, 1.0552, 0.352629, 1.09616, 0.48005, 0.763754, 1.03884, 1.2753, 0.413475, 0.516781, 0.412366, 0.464145, 1.12326, 0.743152, 0.40528, 0.957025, 0.450745, 0.49408, 0.620927, 0.397441, 0.533967, 0.410858, 0.515143, 0.693032, 0.771248, 1.00295, 0.440733, 0.766369, 0.950505, 0.566465, 0.491553, 0.408209, 0.541847, 0.416769, 0.31544, 0.813012, 0.525047, 0.528778, 0.85286, 0.351898, 0.623053, 0.449646, 0.649773, 0.352457, 0.662912, 0.304729, 0.718532, 0.805896, 0.328847, 0.798064, 0.605359, 0.236831, 0.914987, 0.358488, 0.627021, 1.00688, 0.399679, 0.151794, 0.40139, 0.65487, 0.695405, 0.707046, 0.467329, 0.533185, 0.223953, 0.646293, 0.463466, 0.260903, 0.66746, 0.889871, 0.53033, 0.430629, 0.649456, 0.329611, 0.344166, 0.851195, 0.676978, 0.476081, 0.280216, 0.221398, 0.140729, 0.484406, 0.406369, 0.740254, 0.820989, 0.208188, 0.591336, 0.454169, 0.242862, 0.547698, 0.720992, 0.73593, 0.317875, 0.672037, 0.304394, 0.392523, 0.857454, 0.809841, 0.263624, 0.372632, 0.791856, 0.71538, 0.336399, 0.430103, 0.771248, 0.243277, 0.473069, 0.326208, 0.475849, 0.272824, 0.397829, 0.308999, 0.431576, 0.726646, 0.818189, 0.65391, 0.509764, 0.643459, 0.111326, 0.539734, 0.414384, 0.217647, 0.317023, 0.469502, 0.662589, 0.58075, 0.338004, 0.224665, 0.254082, 0.522362, 0.270502, 0.3525, 0.43645, 0.710508, 0.457173, 0.465053, 0.287632, 0.343285, 0.246626, 0.653591, 0.641263, 0.300958, 0.303318, 0.40439, 0.751545, 0.512509, 0.398315, 0.734495, 0.454502, 0.656471, 0.510886, 0.18283, 0.631938, 0.796508, 0.605359, 0.587021, 0.857037, 0.234988, 0.630397, 0.624576, 0.659683, 0.754118, 0.373589, 0.505057, 0.771248, 0.605951, 0.525176, 0.3217, 0.484997, 0.363556, 0.615494, 0.352801, 0.595102, 0.62886, 0.45273, 0.554967, 0.482164, 0.547698, 0.445058, 0.575948, 0.698127, 0.34404, 0.34712, 0.351383, 0.613993, 0.642203, 0.262917, 0.2557, 0.518804, 0.353189, 0.247954, 0.268627, 0.361918, 0.327925, 0.718181, 0.419372, 0.594231, 0.381144, 0.658718, 0.800406, 0.624576, 0.47042, 0.553613, 0.291948, 0.302062, 0.288546, 0.556867, 0.296617, 0.51439, 0.616697, 0.783393, 0.679628, 0.653273, 0.4184, 0.212917, 0.427904, 0.386013, 0.549171, 0.252783, 0.605951, 0.454058, 0.267645, 0.308622, 0.301436, 0.375005, 0.512384, 0.611301, 0.205361, 0.516529, 0.367932, 0.201487, 0.562332, 0.755961, 0.300517, 0.525432, 0.50555, 0.225765, 0.313903, 0.38343, 0.266797, 0.570072, 0.696084, 0.604769, 0.394444, 0.783393, 0.600356, 0.537499, 0.45762, 0.673024, 0.309678, 0.291734, 0.72949, 0.367752, 0.683956, 0.27289, 0.411762, 0.649456, 0.296943, 0.625186, 0.350013, 0.649456, 0.267776, 0.559867, 0.290987, 0.399484, 0.468586, 0.442458, 0.316056, 0.573143, 0.491193, 0.638764, 0.49674, 0.569517, 0.407164, 0.692694, 0.540789, 0.570909, 0.356917, 0.186618, 0.35207, 0.281931, 0.321151, 0.11839, 0.569794, 0.372269, 0.33817, 0.482046, 0.828238, 0.577638, 0.688647, 0.64724, 0.528907, 0.355092, 0.488324, 0.292661, 0.297851, 0.22032, 0.272092, 0.286056, 0.493477, 0.595975, 0.163888, 0.2193, 0.630705, 0.328566, 0.675987, 0.39749, 0.49674, 0.20687, 0.335006, 0.65519, 0.588169, 0.53033, 0.355959, 0.520199, 0.428322, 0.661619, 0.37583, 0.268659, 0.454724, 0.523256, 0.315286, 0.419218, 0.288617, 0.244229, 0.424211, 0.506539, 0.186254, 0.378039, 0.208849, 0.223462, 0.588744, 0.201585, 0.344713, 0.507529, 0.400314, 0.443107, 0.561508, 0.283104, 0.229993, 0.153208, 0.636895, 0.538681, 0.296003, 0.281931, 0.244468, 0.270403, 0.302172, 0.204111, 0.493597, 0.224336, 0.191791, 0.598892, 0.330901, 0.22582, 0.271197, 0.377578, 0.214534, 0.122387, 0.360463, 0.330578, 0.293162, 0.211415, 0.446582, 0.285464, 0.382309, 0.166631, 0.152835, 0.282138, 0.261062, 0.193201, 0.267188, 0.308132, 0.159156, 0.447346, 0.243515, 0.343998, 0.275534, 0.323472, 0.428636, 0.432209, 0.306856, 0.352587, 0.486895, 0.212709, 0.375739, 0.518551, 0.215479, 0.473299, 0.316597, 0.244289, 0.179732, 0.242388, 0.237294, 0.172762, 0.0987258, 0.381935, 0.291165, 0.313673, 0.206567, 0.321975, 0.232647, 0.142527, 0.351126, 0.310587, 0.154863, 0.361918, 0.221614, 0.393482, 0.174713, 0.114636, 0.198945, 0.24417, 0.200408, 0.188266, 0.188357, 0.143856, 0.194147, 0.328807, 0.160718, 0.357353, 0.263753, 0.282448, 0.332764, 0.329048, 0.115422, 0.17574, 0.203067, 0.231458, 0.287913, 0.317448, 0.384884, 0.399191, 0.210282, 0.234758, 0.0955949, 0.12191, 0.209872, 0.141972, 0.126087, 0.323314, 0.0651264, 0.318535, 0.133957, 0.169586, 0.276883, 0.216481, 0.228538, 0.209002, 0.256075, 0.217328, 0.345092, 0.146621, 0.23242, 0.31352, 0.190485, 0.262949, 0.179031, 0.08544, 0.0926538, 0.195002, 0.101661, 0.0754006, 0.175312, 0.140044, 0.127138, 0.172509, 0.113855, 0.215637, 0.172257, 0.158613, 0.123287, 0.172425, 0.146621, 0.191417, 0.147555, 0.10176, 0.0919327, 0.0727958, 0.167611, 0.138683, 0.11897, 0.13285, 0.142945, 0.0858582, 0.0698023, 0.126396, 0.103161, 0.139907, 0.106852, 0.114189, 0.110137, 0.092473, 0.0857745, 0.0595885, 0.0614201, 0.076588, 0.05576, 0.0625703, 0.0524328]
    ltrlist =  [0.00431122, 0.0021894, 0.00410583, 0.00351204, 0.00346436, 0.00235805, 0.00242815, 0.00313597, 0.00214706, 0.00226773, 0.00228998, 0.00340401, 0.00240925, 0.00331221, 0.00198185, 0.00306933, 0.00288341, 0.00264601, 0.00256461, 0.00226773, 0.00275139, 0.00356733, 0.00347791, 0.00244241, 0.00215968, 0.00278926, 0.00198185, 0.00262542, 0.00235345, 0.00225448, 0.00322289, 0.00316057, 0.00203281, 0.00276756, 0.0037678, 0.00283871, 0.00187639, 0.00439623, 0.00298073, 0.0019974, 0.00201699, 0.00233514, 0.00220227, 0.00152256, 0.00127964, 0.00322919, 0.00387222, 0.00203679, 0.00345086, 0.00310551, 0.00259991, 0.0017835, 0.00280017, 0.00308134, 0.00245197, 0.0023215, 0.00186908, 0.00303951, 0.00151071, 0.00268767, 0.00337094, 0.00353958, 0.00313597, 0.00209733, 0.0019134, 0.00284982, 0.00431965, 0.00479998, 0.00161128, 0.00170184, 0.00289469, 0.00300411, 0.00277297, 0.00292881, 0.00208916, 0.00216813, 0.0104189, 0.00237191, 0.00348471, 0.00276756, 0.00236266, 0.00252486, 0.00253969, 0.00303358, 0.00175243, 0.00279471, 0.00203281, 0.00268767, 0.0018618, 0.00210554, 0.00417047, 0.00172526, 0.00233514, 0.0038122, 0.00254466, 0.00142196, 0.00226773, 0.00210554, 0.00216813, 0.00258472, 0.00246157, 0.00210143, 0.00233058, 0.00356037, 0.00270346, 0.00253969, 0.00238119, 0.00227216, 0.00171518, 0.00145284, 0.00293453, 0.00642064, 0.00302176, 0.00246157, 0.00208508, 0.00202489, 0.00226773, 0.00220227, 0.00190594, 0.00298073, 0.00266155, 0.00166892, 0.00314825, 0.00203679, 0.00233514, 0.00181512, 0.0037094, 0.00237191, 0.00398731, 0.00252979, 0.000799242, 0.00261008, 0.00263055, 0.00274067, 0.00179048, 0.00294601, 0.00309945, 0.00148149, 0.00151959, 0.00270346, 0.00280017, 0.00191714, 0.00270874, 0.00213036, 0.00202489, 0.00274067, 0.00189481, 0.00353958, 0.00174559, 0.0031544, 0.00269292, 0.00171518, 0.00187273, 0.00415421, 0.00288341, 0.00206482, 0.0017354, 0.00241396, 0.00217661, 0.00289469, 0.00240455, 0.00358829, 0.00173879, 0.00171184, 0.00192089, 0.00239986, 0.00166242, 0.00265636, 0.00194353, 0.00387222, 0.00288341, 0.00234428, 0.00133321, 0.00226773, 0.00208508, 0.00287778, 0.0018437, 0.00215546, 0.00139174, 0.00205276, 0.00184731, 0.00162709, 0.00204077, 0.00204875, 0.00205276, 0.00175928, 0.00195495, 0.00229894, 0.00251501, 0.00152553, 0.00182936, 0.00448291, 0.00197028, 0.0120378, 0.00202094, 0.00267719, 0.00184731, 0.00133321, 0.00225889, 0.00140815, 0.00200521, 0.00262029, 0.00166242, 0.00214706, 0.00192464, 0.00197413, 0.00256962, 0.00268242, 0.00250521, 0.00195113, 0.00271404, 0.00170516, 0.00295177, 0.00129472, 0.00192464, 0.00221954, 0.00242815, 0.00242815, 0.0017085, 0.00201699, 0.00272466, 0.00196643, 0.00233058, 0.00259991, 0.00179749, 0.00290602, 0.0016952, 0.00182223, 0.00129725, 0.00196643, 0.00208101, 0.00121629, 0.00191714, 0.00242815, 0.00243765, 0.00197028, 0.00236728, 0.0188925, 0.00196643, 0.00208916, 0.00106295, 0.00227216, 0.00223694, 0.00190967, 0.00162709, 0.00241868, 0.00311766, 0.00264084, 0.00261518, 0.00326087, 0.00138091, 0.00263569, 0.00250032, 0.00194733, 0.00204077, 0.00138091, 0.00208916, 0.00190222, 0.00233058, 0.00130743, 0.0021639, 0.00171518, 0.00142753, 0.00239051, 0.00227216, 0.00204875, 0.00185454, 0.00172526, 0.00208101, 0.00174559, 0.00172189, 0.00220227, 0.00192464, 0.00221521, 0.00215968, 0.00214287, 0.00177655, 0.0019935, 0.00193217, 0.00174219, 0.00225889, 0.00207695, 0.00163985, 0.00131255, 0.00132026, 0.00182223, 0.00220657, 0.00238119, 0.0167076, 0.00215546, 0.00138632, 0.00190594, 0.00638315, 0.00125244, 0.0032166, 0.00152553, 0.00183293, 0.00182936, 0.00253474, 0.00206886, 0.00104647, 0.00171853, 0.00167545, 0.00198961, 0.00192464, 0.00114483, 0.00220227, 0.00164948, 0.00138903, 0.00196643, 0.00144435, 0.00123303, 0.0121321, 0.00159562, 0.00138903, 0.00181512, 0.00145853, 0.00250521, 0.00133843, 0.00161443, 0.00211791, 0.00152553, 0.00272466, 0.00208101, 0.00161758, 0.00163027, 0.00159874, 0.00174559, 0.00111613, 0.00205677, 0.0014786, 0.00135421, 0.00222822, 0.000992719, 0.0034576, 0.00246157, 0.00136216, 0.00102423, 0.00130488, 0.00162074, 0.00118811, 0.00147572, 0.00165271, 0.00112709, 0.00219368, 0.00156476, 0.00200521, 0.00200913, 0.00112709, 0.00109028, 0.00334471, 0.00160813, 0.00149896, 0.00299825, 0.00123062, 0.00177308, 0.00166892, 0.00279471, 0.00137285, 0.00163027, 0.00148729, 0.00092532, 0.00146424, 0.0015315, 0.0019284, 0.00134367, 0.0011426, 0.00154955, 0.00456238, 0.00179398, 0.00123303, 0.00145001, 0.00215126, 0.00166567, 0.0020729, 0.00164948, 0.00130488, 0.00102423, 0.00132284, 0.00130743, 0.00157088, 0.00148729, 0.00143312, 0.00129472, 0.00112269, 0.00111832, 0.00139174, 0.00142474, 0.00195495, 0.0014671, 0.00106087, 0.00268767, 0.00122105, 0.00166242, 0.00113371, 0.00136216, 0.00172526, 0.00130743, 0.00154351, 0.00131768, 0.00475334, 0.00137017, 0.00109241, 0.00114707, 0.001801, 0.00532329, 0.00126721, 0.0017085, 0.000921713, 0.00200913, 0.00151071, 0.000543982, 0.00136749, 0.00126721, 0.00104852, 0.00198573, 0.0016853, 0.00250521, 0.00113593, 0.00135951, 0.0124196, 0.00123785, 0.00107548, 0.00221088, 0.00111396, 0.00346436, 0.00115833, 0.00142753, 0.00144435, 0.00127964, 0.00197028, 0.00149311, 0.00116514, 0.00119743, 0.00135951, 0.00158011, 0.0009344, 0.00297492, 0.000746434, 0.00198961, 0.00163027, 0.00178699, 0.00146997, 0.00114483, 0.000905654, 0.00209733, 0.0015863, 0.000962178, 0.000910976, 0.00151662, 0.00136749, 0.00643319, 0.000777685, 0.00121866, 0.00097733, 0.00152256, 0.00138091, 0.000847469, 0.00126227, 0.000992719, 0.0011315, 0.00101626, 0.00171853, 0.00127714, 0.00134367, 0.00116059, 0.000898606, 0.00120212, 0.00145568, 0.00238584, 0.000582467, 0.00091812, 0.00139719, 0.000709478, 0.000849126, 0.00103025, 0.000864183, 0.00108603, 0.00115381, 0.000839234, 0.00115833, 0.000936227, 0.000648518, 0.000577934, 0.000734863, 0.000747894, 0.000882949, 0.00106087, 0.000717841, 0.000895103, 0.00107129, 0.000368081, 0.000686309, 0.000675669, 0.00068765, 0.000429488, 0.000635975, 0.000818194, 0.00053346, 0.000789931, 0.000675669, 0.000598613, 0.00060213, 0.000577934, 0.000472622, 0.000546111, 0.000399547, 0.000611612, 0.000295762, 0.000311777]
    fprlist = [0.674332, 2.74842, 0.00300691, 1.8414, 0.977091, 2.83708, 2.42739, 0.698485, 2.06346, 2.58763, 2.87359, 0.0268494, 2.82658, 0.010823, 3.11089, 1.19315, 0.953137, 0.0169181, 2.74043, 2.50838, 2.26072, 0.665617, 0.266277, 0.23274, 2.54722, 0.627772, 0.954556, 2.49259, 2.44692, 2.56479, 0.0924812, 0.00421438, 2.37114, 0.270926, 0.0379062, 0.0363967, 2.35035, 0.0130287, 0.778083, 1.60746, 0.0607562, 2.49946, 1.26463, 3.47397, 3.15401, 0.373388, 0.84274, 1.96607, 0.0316445, 1.30133, 0.00354081, 2.42506, 0.319271, 1.26062, 0.545513, 1.77127, 0.787614, 0.233169, 1.40747, 0.586545, 0.0981588, 2.38385, 0.0218029, 3.04757, 1.62957, 0.000322134, 0.0302943, 0.343603, 2.69491, 0.985408, 0.0404434, 0.357024, 0.0013129, 0.515853, 1.91485, 0.708393, 0.100189, 1.94703, 0.00296612, 0.0152608, 1.20196, 2.27462, 0.0168034, 2.1826, 2.61428, 0.304118, 0.226714, 1.31606, 1.01433, 2.28085, 0.220529, 0.42975, 0.00798215, 0.000670016, 0.0285825, 0.0160292, 0.00319445, 2.49076, 1.11401, 0.917278, 0.225257, 0.598871, 1.41684, 0.418458, 2.70135, 0.0750443, 0.144608, 0.210476, 1.26696, 1.21823, 0.164843, 0.00774836, 1.17281, 0.0541025, 0.352132, 0.892154, 0.402806, 0.802857, 1.31886, 2.50998, 0.279814, 1.33456, 0.0312484, 0.578043, 0.141044, 0.435212, 0.00393235, 0.227971, 0.146319, 0.892154, 1.59246, 2.24486, 1.0912, 0.362781, 0.361816, 0.22818, 1.1093, 1.2286, 1.99854, 0.0437572, 1.83461, 1.27394, 0.00114183, 0.452944, 0.318841, 0.134675, 1.32675, 0.24446, 1.73521, 0.0143744, 0.591696, 1.01027, 2.11559, 0.00406498, 0.579493, 0.00977078, 0.00301426, 0.0158972, 1.21534, 0.0134867, 0.534339, 1.72945, 0.145564, 0.0198036, 0.119549, 0.0130034, 0.450108, 0.553381, 0.0113907, 0.00153485, 0.00502818, 0.00136052, 1.9729, 0.138541, 0.53615, 0.248071, 0.0375412, 0.657261, 0.330493, 0.822779, 0.0934121, 1.51453, 0.31063, 0.0331666, 0.0491781, 0.554663, 0.126356, 0.0205584, 0.130729, 0.401923, 0.00132643, 0.066023, 0.0410539, 0.0593982, 0.435401, 0.0382376, 1.0681, 2.23668, 0.132406, 0.458464, 1.74433, 0.161405, 1.06581, 0.053198, 0.468872, 1.15227, 0.0050973, 0.000948969, 0.00857906, 0.0340452, 0.00168071, 0.661294, 0.433321, 1.86763, 0.769854, 0.00100475, 0.00220465, 0.0849462, 0.0865853, 0.52603, 0.179392, 0.546549, 0.152713, 0.0113354, 1.25957, 0.0980653, 1.35887, 0.498767, 1.37381, 0.0931452, 0.909877, 0.00218644, 1.62721, 1.09391, 0.41554, 0.534791, 0.0069132, 0.0836568, 0.0089157, 0.0509399, 0.843715, 0.000511487, 0.0249408, 1.65367, 1.12958, 0.0927463, 0.048099, 0.0467716, 0.1596, 1.17281, 0.361334, 0.0394585, 0.23274, 0.0133431, 0.133852, 0.235755, 0.0161308, 0.438251, 0.0273223, 0.985771, 1.32372, 0.091472, 1.15127, 0.106935, 1.10286, 0.00159052, 0.225464, 1.73425, 0.067368, 0.193624, 0.133915, 1.42041, 0.55782, 0.0745069, 0.179643, 0.395264, 0.000718814, 0.026513, 0.0012894, 0.0301624, 0.104722, 0.00154839, 0.0439478, 0.22955, 0.0438418, 0.377541, 0.0130732, 0.26206, 0.0629867, 0.00176477, 0.00127438, 1.1142, 0.0240027, 0.483205, 0.217497, 0.0980187, 0.125878, 0.0217711, 0.21992, 0.0208805, 0.0705778, 0.125225, 0.125998, 0.65085, 0.0393252, 1.10266, 0.0672387, 1.18723, 0.493463, 0.0110574, 0.257788, 0.981063, 0.0727775, 0.00287212, 0.00914446, 0.00115472, 0.446706, 0.0382191, 0.277032, 0.612675, 0.00674346, 0.0839372, 0.134548, 0.0462776, 0.434644, 0.275024, 0.344064, 0.17395, 0.772285, 0.000259732, 0.0617279, 0.266034, 0.0166003, 0.797544, 0.0148941, 0.0138998, 0.000679238, 0.59145, 0.369439, 0.00487842, 0.796609, 0.20873, 0.400337, 0.378376, 0.118871, 0.00181988, 0.00766574, 0.0770845, 0.00237208, 0.390937, 0.0187455, 0.207959, 0.0537907, 0.256028, 0.750613, 0.959176, 0.0400349, 0.0653291, 0.0360637, 0.0696696, 1.1797, 1.18011, 0.339788, 0.000117361, 0.0356818, 1.41193, 0.257083, 0.00776727, 0.0290716, 0.317126, 0.010445, 0.0339794, 0.00285535, 0.0721175, 0.00249923, 0.213317, 0.0380899, 0.496427, 0.0303972, 0.764103, 0.0274152, 0.757185, 0.0569356, 1.58497, 0.194164, 1.25957, 0.0628355, 0.430125, 0.0194602, 0.0581261, 0.0582941, 0.00244018, 0.384611, 0.0855573, 0.86537, 0.0737608, 0.0391923, 1.02882, 0.150293, 0.189792, 0.0254543, 0.15945, 0.00678964, 0.850562, 0.0624141, 0.070375, 0.329754, 0.00632012, 0.0680832, 0.205756, 0.2181, 1.22943, 0.34514, 0.000531081, 0.512353, 0.0148724, 0.198264, 0.10038, 0.19534, 0.609641, 0.0241664, 0.0121231, 0.137759, 0.0107599, 0.00819498, 0.0995238, 1.38484, 0.218201, 0.00245691, 0.135696, 0.0545736, 0.46605, 0.301794, 0.0488943, 0.011171, 0.0803625, 0.579251, 0.0209211, 0.0145857, 0.0224474, 0.00442072, 0.0156822, 0.019555, 0.00210683, 0.482791, 0.135248, 0.430687, 0.00460785, 0.192547, 0.00719511, 0.00765454, 0.0257399, 0.0996661, 0.362942, 1.01138, 0.0683779, 0.553149, 0.129806, 0.0121172, 0.0159514, 0.0627149, 0.0868339, 0.0227326, 0.145358, 0.0242722, 0.104275, 0.546894, 0.936225, 0.0996186, 0.0869999, 0.0684436, 0.208344, 0.0761671, 0.0425278, 0.417361, 0.13576, 0.298945, 0.0136784, 0.130606, 0.150435, 0.000293308, 0.00198699, 0.0262061, 0.00168646, 0.104077, 0.0305893, 0.000990623, 0.0816803, 0.00696394, 0.0407968, 0.031309, 0.0565804, 0.0408362, 0.0611666, 0.0142629, 0.0165359, 0.0119707, 0.00567454, 0.0547579, 0.0147714, 0.277158, 0.0898711, 0.0017908, 0.0112365, 0.000859857, 0.00306468, 0.0157663, 0.000769284, 0.00322105, 0.00918911]
    vtrlist1 = [5.59402, 5.59402, 4.19795, 5.86247, 3.79252, 3.92825, 4.61052, 4.13283, 3.61178, 4.19795, 3.03549, 4.21434, 0.411863, 4.2891, 3.66865, 5.22442, 3.56972, 2.79643, 4.10068, 3.56275, 2.96516, 3.26935, 1.67963, 2.79097, 3.59066, 3.55578, 3.22494, 2.68403, 2.67357, 3.01776, 3.5281, 3.43965, 3.49384, 3.45309, 2.52143, 3.30141, 3.92825, 2.79097, 3.92825, 3.79994, 4.53018, 3.39959, 3.15025, 1.56253, 2.64243, 2.58122, 3.88245, 2.95362, 4.23086, 3.18737, 2.93634, 3.0653, 3.61178, 3.23126, 3.64724, 1.50562, 3.41956, 1.1365, 2.93634, 3.77038, 2.9135, 2.98259, 4.23086, 2.44863, 3.34689, 3.48702, 2.33649, 2.90214, 3.28214, 3.9359, 3.43293, 2.00241, 3.32735, 2.34107, 1.91072, 2.01026, 2.80188, 1.30427, 0.757069, 1.34174, 3.51436, 2.19922, 1.28154, 2.27793, 2.90781, 3.41956, 1.2853, 1.20038, 1.67635, 2.33649, 2.81835, 2.17786, 3.25659, 1.34043, 3.95128, 2.7855, 1.38704, 3.15025, 2.48233, 0.899043, 2.48233, 3.43293, 1.68621, 2.66315, 2.66315, 2.55614, 1.06868, 1.81966, 3.6259, 2.76383, 1.99657, 2.26682, 3.69745, 3.77771, 3.23126, 1.77058, 2.29355, 1.27282, 1.08445, 1.94461, 2.4367, 2.96516, 3.5767, 3.33384, 1.92194, 3.59772, 3.08327, 3.08933, 0.619715, 3.32084, 3.5767, 2.87958, 1.89031, 1.26291, 3.08327, 3.50066, 2.08218, 2.5116, 0.81779, 2.48233, 1.41993, 3.9979, 2.30026, 2.95939, 1.77404, 1.19921, 2.68403, 2.65798, 2.09647, 1.21572, 4.0057, 2.3733, 0.857037, 1.84832, 3.79994, 2.07407, 0.804716, 1.09724, 2.58627, 1.66982, 1.55948, 1.9849, 2.90214, 3.18737, 2.94211, 0.8877, 1.54132, 2.3733, 2.30252, 1.16004, 4.09272, 4.23086, 4.02923, 1.38028, 2.85716, 1.93325, 1.20626, 2.36404, 3.01776, 0.945877, 2.94211, 3.75569, 3.54886, 2.80188, 2.14201, 2.93061, 0.706356, 1.60742, 0.589031, 3.51436, 3.28214, 2.54121, 1.81434, 1.55189, 1.05933, 0.810237, 3.02956, 2.61674, 2.55114, 0.558228, 2.59639, 3.39294, 0.949579, 2.41066, 1.46072, 0.464825, 2.00046, 1.80727, 0.581033, 1.60429, 3.07128, 0.675987, 1.12107, 3.45987, 2.3733, 2.80188, 1.11997, 2.18212, 1.00002, 1.14429, 0.743152, 1.43667, 2.59639, 1.41301, 0.63814, 0.747518, 0.81779, 0.721696, 3.08933, 2.81285, 1.42131, 1.25431, 0.373134, 0.825007, 1.21691, 2.99427, 1.83036, 1.06452, 3.0653, 2.83492, 1.23365, 1.36553, 1.41163, 1.001, 1.04699, 1.72789, 2.90781, 1.88478, 0.590182, 0.996121, 1.06037, 1.2916, 1.39792, 0.692694, 0.484051, 0.657111, 1.29665, 1.62478, 0.933032, 1.40339, 2.49693, 0.831478, 0.604769, 0.853694, 1.35623, 1.38975, 0.432843, 0.84169, 2.38723, 3.54192, 1.65682, 0.814601, 0.979721, 0.845398, 0.90964, 1.4536, 0.510013, 1.85374, 0.984517, 0.66065, 0.936685, 2.83492, 1.95412, 1.26909, 0.532406, 0.829451, 0.46528, 1.95412, 0.660006, 0.668764, 1.62796, 0.635031, 2.41539, 1.70275, 1.02674, 0.513636, 0.462675, 1.66007, 1.41163, 0.919466, 0.794954, 0.864179, 0.342323, 1.85374, 0.728779, 0.536057, 0.579899, 0.591336, 0.739532, 1.36153, 1.22645, 0.970201, 0.476197, 1.01082, 2.55614, 1.05314, 0.271462, 0.398315, 0.79031, 0.850366, 1.01577, 2.07002, 1.16345, 1.04597, 0.739532, 0.445928, 0.933945, 0.966419, 2.19279, 0.432843, 0.789923, 0.470994, 0.957961, 1.50709, 0.434324, 1.25185, 0.224885, 0.977809, 0.896413, 1.03884, 1.08657, 0.857037, 0.401783, 1.42688, 0.784542, 0.612197, 1.16345, 1.81434, 1.13761, 0.674339, 0.70223, 0.482635, 0.319158, 0.538813, 1.11778, 0.613693, 0.961711, 1.11234, 1.16914, 0.921263, 0.769744, 2.03395, 0.636584, 0.473299, 0.511384, 1.60742, 1.3274, 0.914092, 0.787229, 0.473416, 1.24576, 0.365782, 1.10476, 0.453726, 1.06556, 1.24454, 0.719584, 1.01975, 0.31287, 1.78971, 0.837182, 1.41026, 1.07917, 0.312832, 0.940349, 0.65391, 0.669744, 0.75818, 0.623357, 0.765995, 1.37222, 0.469846, 0.390611, 0.191417, 1.64715, 0.986442, 1.60742, 0.293771, 0.42199, 0.736649, 0.952364, 1.38704, 0.630089, 0.448988, 1.12875, 0.235103, 0.211983, 0.733776, 0.834732, 0.536843, 0.719584, 0.445058, 0.586448, 0.544764, 0.563156, 1.23486, 1.07917, 0.914092, 1.71946, 0.283969, 1.23245, 0.7645, 0.554967, 3.10748, 0.334842, 0.405923, 0.201389, 0.236196, 0.783775, 0.635341, 1.03681, 0.429893, 0.388803, 1.14317, 0.441163, 0.495772, 0.65168, 0.370004, 0.197204, 0.561508, 0.414789, 0.537105, 0.625186, 0.354399, 0.381702, 0.472953, 0.533576, 0.479464, 0.644403, 0.210848, 0.347162, 0.588169, 0.490715, 0.591048, 0.685293, 0.692694, 0.478879, 1.08022, 0.222156, 0.367438, 0.966419, 0.999045, 0.512009, 0.317023, 0.216904, 0.307456, 0.33078, 0.964534, 0.8877, 0.250388, 0.67996, 0.552803, 0.623661, 0.186618, 0.189372, 0.41136, 0.997096, 0.554696, 0.721696, 0.344965, 0.514515, 0.299382, 0.210333, 0.203663, 0.23225, 0.587881, 0.380632, 0.519819, 0.591336, 0.857872, 1.09938, 1.08234, 0.185347, 0.16909, 0.181496, 0.681954, 0.16712, 0.24056, 0.154939, 0.155925, 0.186892, 0.496984, 0.156078, 0.219622, 0.25229, 0.227258, 0.461884, 0.220643, 0.286931, 0.289252, 0.22379, 0.418758, 0.278239, 0.169504, 0.193674, 0.181053, 0.075548, 0.0975756, 0.0909505, 0.0693267, 0.0730807]
    ltrlist1 = [0.00276031, 0.00263584, 0.00266683, 0.00363051, 0.00323034, 0.00553339, 0.00279038, 0.00295394, 0.00199359, 0.00238131, 0.0100767, 0.0023226, 0.208662, 0.0061146, 0.00505235, 0.00336186, 0.00205449, 0.00474308, 0.00655232, 0.00642718, 0.00385905, 0.00392507, 0.0049726, 0.00228611, 0.00214072, 0.00209748, 0.00398034, 0.00347496, 0.00222499, 0.00177786, 0.00295664, 0.00197378, 0.00199505, 0.042206, 0.011631, 0.00177678, 0.00307163, 0.00313622, 0.00228806, 0.00195321, 0.00491077, 0.00392245, 0.00330468, 0.00127122, 0.0121179, 0.0277989, 0.00194324, 0.00199676, 0.00333842, 0.00151534, 0.019712, 0.00311871, 0.00271366, 0.00252849, 0.00327665, 0.00106743, 0.00343545, 0.0299617, 0.00412895, 0.00444834, 0.00412042, 0.0019258, 0.00300434, 0.00459392, 0.0038013, 0.0017932, 0.0099894, 0.00145108, 0.0031642, 0.0021385, 0.00279208, 0.0102759, 0.00197835, 0.00159668, 0.00648434, 0.00582067, 0.00235048, 0.00167562, 0.0168117, 0.0247451, 0.00251759, 0.00159551, 0.00320333, 0.00572747, 0.00169287, 0.00236542, 0.00276872, 0.00359448, 0.00723013, 0.0116521, 0.00192861, 0.00125613, 0.00169617, 0.00141484, 0.00337867, 0.00344885, 0.00174056, 0.00134275, 0.00252526, 0.00452631, 0.00164808, 0.00510881, 0.0011882, 0.00195535, 0.00200284, 0.00170259, 0.00371041, 0.00201312, 0.00299157, 0.001919, 0.013897, 0.0157827, 0.0056467, 0.00244032, 0.00241107, 0.0153266, 0.00186302, 0.0296194, 0.0395414, 0.00348258, 0.0023683, 0.00184001, 0.00229686, 0.00260236, 0.00471952, 0.00216841, 0.00204575, 0.00287695, 0.00192392, 0.00446947, 0.00196084, 0.00155709, 0.00128494, 0.0060438, 0.00313431, 0.00426982, 0.00158003, 0.00128901, 0.000708805, 0.00797944, 0.00094454, 0.00727672, 0.00217635, 0.00185306, 0.00166829, 0.00640928, 0.00357268, 0.00376633, 0.00155937, 0.003584, 0.00341836, 0.00170987, 0.0223368, 0.00162534, 0.00352348, 0.00184225, 0.00193238, 0.00123818, 0.00171738, 0.0308761, 0.000746976, 0.00110343, 0.00620954, 0.00302489, 0.00279004, 0.00049869, 0.0240306, 0.00153748, 0.00219445, 0.00392006, 0.00251728, 0.00171237, 0.00231413, 0.0148353, 0.00130101, 0.00113108, 0.00191317, 0.00217264, 0.00204376, 0.00215209, 0.00180646, 0.00243735, 0.00220409, 0.0045928, 0.0013315, 0.00211673, 0.000497717, 0.00107724, 0.130562, 0.00265097, 0.00330106, 0.00198681, 0.0103269, 0.00129879, 0.00331555, 0.00200822, 0.00159163, 0.00127122, 0.00168504, 0.0857476, 0.00359579, 0.00457553, 0.0291598, 0.00239907, 0.0131704, 0.000404679, 0.00128933, 0.0018508, 0.112759, 0.00497109, 0.00200529, 0.114171, 0.0107963, 0.00172409, 0.00184675, 0.0051772, 0.00327466, 0.00199505, 0.0501675, 0.0127642, 0.00995565, 0.00509548, 0.00312137, 0.00107357, 0.115411, 0.121686, 0.00539934, 0.0234982, 0.00168874, 0.00177613, 0.021295, 0.000872554, 0.019625, 0.0560681, 0.00974493, 0.00213226, 0.0100342, 0.00809231, 0.00265614, 0.00150687, 0.00663709, 0.00574696, 0.00140008, 0.00449671, 0.00091506, 0.0044847, 0.00387174, 0.0429098, 0.149749, 0.0570451, 0.00682065, 0.0147907, 0.0069475, 0.00610126, 0.0743993, 0.0890973, 0.0521825, 0.0019034, 0.000667679, 0.00898978, 0.00266521, 0.0310262, 0.000523376, 0.00311606, 0.00256586, 0.0306395, 0.0558489, 0.0795493, 0.00124606, 0.0105884, 0.00171906, 0.0245777, 0.0346964, 0.0053569, 0.0599764, 0.00102221, 0.0577717, 0.00373622, 0.0788372, 0.0103757, 0.0453843, 0.00168998, 0.00613131, 0.00690551, 0.0291046, 0.0105999, 0.107625, 0.00270706, 0.0906147, 0.000377029, 0.00201214, 0.0162384, 0.00239556, 0.00100785, 0.0052043, 0.0191195, 0.0673317, 0.00115309, 0.000943389, 0.00559417, 0.000784708, 0.000465754, 0.00942626, 0.00174183, 0.0558489, 0.0509409, 0.14731, 0.0914227, 0.00461237, 0.0165802, 0.00968738, 0.00058241, 0.117521, 0.0195172, 0.00135046, 0.0441833, 0.00720739, 0.117673, 0.0855564, 0.00197811, 0.0127534, 0.0348029, 0.0340161, 0.0120073, 0.0116689, 0.105256, 0.0138903, 0.0185355, 0.00329063, 0.104067, 0.048092, 0.0744329, 0.0834412, 0.0128398, 0.0142408, 0.0260006, 0.104477, 0.00734583, 0.0165435, 0.0148353, 0.000838541, 0.00730322, 0.0299723, 0.00241313, 0.03394, 0.00142349, 0.00937397, 0.0017822, 0.00190712, 0.0491993, 0.039278, 0.00736365, 0.0238538, 0.114963, 0.002285, 0.0125824, 0.00173336, 0.0123272, 0.00329785, 0.00101724, 0.045884, 0.00143184, 0.013802, 0.0468774, 0.0861696, 0.0123212, 0.000784708, 0.00118213, 0.00371696, 0.0289669, 0.00150778, 0.0077349, 0.00610645, 0.107625, 0.0122, 0.00209212, 0.00245821, 0.000689195, 0.00886879, 0.00602768, 0.0575462, 0.00104592, 0.00104643, 0.0188964, 0.00872827, 0.00120836, 0.012153, 0.010545, 0.075653, 0.0255424, 0.0128971, 0.0697949, 0.139455, 0.0619043, 0.00114524, 0.00132955, 0.00120013, 0.0332739, 0.0148281, 0.0592375, 0.00146075, 0.00219017, 0.00631743, 0.0814832, 0.0120016, 0.00723276, 0.0966205, 0.00157235, 0.00834401, 0.0819963, 0.0434774, 0.0302289, 0.0808641, 0.00858995, 0.00176319, 0.0074715, 0.00795339, 0.00647255, 0.00131088, 0.0348809, 0.00311909, 0.039676, 0.021802, 0.00128431, 0.0198802, 0.0797104, 0.0834038, 0.0385385, 0.00580585, 0.00573581, 0.00245462, 0.0454584, 0.0195219, 0.0406548, 0.0678549, 0.0647421, 0.00581149, 0.0810457, 0.00572191, 0.00232203, 0.00575813, 0.00963365, 0.00103374, 0.0412493, 0.00909038, 0.00602695, 0.0238027, 0.000350069, 0.0171643, 0.0502607, 0.10393, 0.0240192, 0.0130205, 0.000840589, 0.011158, 0.0419894, 0.00839264, 0.00981705, 0.00131986, 0.0184579, 0.0011095, 0.0026049, 0.0102536, 0.00566387, 0.035178, 0.054651, 0.00721176, 0.00174183, 0.00653565, 0.0166411, 0.00973433, 0.00423159, 0.0260935, 0.0405028, 0.0204201, 0.0926057, 0.00123216, 0.00680743, 0.00129248, 0.00519483, 0.0123838, 0.0202547, 0.00320996, 0.0165296, 0.0480027, 0.0104385, 0.0396203, 0.00768075, 0.000846763, 0.00365888, 0.000884339, 0.00793415, 0.0559778, 0.0256383, 0.00687044, 0.00540853, 0.0121941, 0.000299153, 0.0177804, 0.00728908, 0.0308505, 0.0103682, 0.0153653, 0.0385023, 0.0266324, 0.000229384, 0.00559825, 0.0295353, 0.00966165, 0.000436056, 0.00439087, 0.000717329, 0.00205074, 0.00137773, 0.000408051, 0.00120895, 0.000326308, 0.000822336, 0.000243936, 0.000255141, 0.000184504]
    fprlist1 = [0.00184405, 0.00118907, 0.000708633, 0.00112586, 0.00181374, 0.00139531, 0.00151885, 0.00144305, 0.000771798, 0.000662486, 0.00186484, 0.000564225, 0.00199651, 0.00157695, 0.00122378, 0.000605006, 0.000630628, 0.000472393, 0.0010603, 0.00114022, 0.000819133, 0.000633403, 0.000311521, 0.00133866, 0.000610045, 0.000659584, 0.000465527, 0.00102771, 0.00220624, 0.000678517, 0.000444218, 0.000942239, 0.00059447, 0.000854649, 0.00137773, 0.000596504, 0.000933091, 0.00169493, 0.000667679, 0.000667353, 0.000440763, 0.000436269, 0.000925836, 0.000389372, 0.00204975, 0.000970225, 0.000420177, 0.0009594, 0.000597086, 0.000781651, 0.0010388, 0.000448576, 0.000487857, 0.000474936, 0.00115477, 0.000460106, 0.000553859, 0.011185, 0.00117294, 0.000864293, 0.000862608, 0.000736481, 0.00059418, 0.00068718, 0.00083324, 0.000339633, 0.0011041, 0.000588696, 0.000759101, 0.000398408, 0.000929456, 0.00157465, 0.000550894, 0.000749897, 0.00176147, 0.000552509, 0.00155103, 0.000500885, 0.000394152, 0.00394733, 0.000387098, 0.00062207, 0.000934002, 0.00536145, 0.000301645, 0.000647782, 0.000504565, 0.000641491, 0.000542093, 0.00112971, 0.00053709, 0.000931272, 0.00055467, 0.0231645, 0.000459209, 0.000500396, 0.000303417, 0.000595341, 0.000660872, 0.00981705, 0.000507281, 0.000856319, 0.000284761, 0.000451211, 0.000523376, 0.000635881, 0.00515712, 0.000705183, 0.000559018, 0.000688187, 0.000310914, 0.000846351, 0.000466209, 0.000576472, 0.000269479, 0.000491442, 0.000458089, 0.000453418, 0.00108489, 0.00105926, 0.000302826, 0.000495536, 0.000940861, 0.000503336, 0.00149242, 0.000466664, 0.000349898, 0.000605892, 0.0133316, 0.000513759, 0.000535258, 0.000432875, 0.000606187, 0.000610045, 0.000309551, 0.000406262, 0.000374644, 0.00045497, 0.00028981, 0.000782414, 0.000208555, 0.00100442, 0.00103829, 0.000933546, 0.000517029, 0.00414, 0.000836498, 0.000656373, 0.000203228, 0.00091819, 0.000314885, 0.00026311, 0.00340964, 0.000934458, 0.000414879, 0.000596213, 0.0113591, 0.0060211, 0.00071071, 0.000835683, 0.000286295, 0.000499908, 0.000699358, 0.000880895, 0.000715931, 0.000189432, 0.000219953, 0.00027199, 0.000435205, 0.000244175, 0.000366147, 0.000510759, 0.000656373, 0.000669964, 0.000463713, 0.000251555, 0.000745156, 0.000305946, 0.000485956, 0.00534585, 0.000276407, 0.000375559, 0.000298569, 0.000463713, 0.000300763, 0.000677856, 0.000235972, 0.0005413, 0.00270706, 0.000541564, 0.000296536, 0.000497232, 0.000569203, 0.000369919, 0.00379299, 0.00926353, 0.000319374, 0.000504073, 0.000384087, 0.00151146, 0.000543948, 0.000521845, 0.0016834, 0.00052032, 0.000996122, 0.00160722, 0.000538139, 0.000434144, 0.00182172, 0.000418948, 0.000283098, 0.00141933, 0.000490483, 0.000370462, 0.000633712, 0.000485719, 0.00173336, 0.000341295, 0.000708978, 0.000542093, 0.0013617, 0.000363831, 0.000550356, 0.000715931, 0.00117523, 0.00202, 0.000440548, 0.00344466, 0.000365968, 0.00041407, 0.000344307, 0.000276542, 0.000549551, 0.0014035, 0.00180492, 0.000547143, 0.000524911, 0.00146146, 0.000307443, 0.000372638, 0.000737919, 0.000455414, 0.000223744, 0.000258274, 0.000241095, 0.000701408, 0.000554941, 0.00419267, 0.000864715, 0.00158003, 0.000321407, 0.00152107, 0.00112695, 0.000442055, 0.00154349, 0.00186939, 0.0008904, 0.000307743, 0.000108261, 0.000798612, 0.000390895, 0.00531737, 0.000169809, 0.00917877, 0.00117408, 0.00255713, 0.00490302, 0.00180845, 0.000168982, 0.000393575, 0.000467805, 0.000576472, 0.00242255, 0.000789701, 0.00215839, 0.000112408, 0.00610348, 0.000437975, 0.00179352, 0.000530318, 0.000755774, 0.000299884, 0.00044661, 0.00155178, 0.00190991, 0.00114134, 0.00144587, 0.000560657, 0.000966446, 0.000183695, 0.00120836, 0.00153, 0.000467805, 0.000124666, 0.000251678, 0.00150778, 0.00606804, 0.000217391, 0.000312283, 0.0010686, 0.000460106, 6.73548e-05, 0.0192896, 0.000301204, 0.00118964, 0.000844288, 0.00127744, 0.000762813, 0.000170224, 0.000556568, 0.000184414, 0.000200274, 0.00123698, 0.000502599, 0.000495294, 0.000294229, 0.000546876, 0.00155027, 0.000984528, 0.000270006, 0.00262272, 0.000268429, 0.000272921, 0.00261889, 0.000273321, 0.00131408, 0.000194588, 0.000637434, 0.000609748, 0.000957063, 0.00102072, 0.00159629, 0.00136836, 0.000168159, 0.00372352, 0.00223981, 0.00110249, 0.000843465, 0.00116951, 0.000785091, 0.000293225, 0.0003094, 0.00567763, 0.000438617, 0.0010449, 0.00037373, 0.00120072, 0.00030401, 0.000545809, 0.00142835, 0.000174516, 0.00457831, 0.000355753, 0.00158698, 0.0016793, 0.000387476, 0.000585259, 0.000236895, 0.000630936, 0.000179967, 0.00031061, 0.000342463, 0.00637594, 0.00122796, 0.000845113, 0.000350753, 0.000470552, 0.000162351, 0.000330475, 0.00444456, 0.00129816, 0.000390705, 0.000478659, 0.00160096, 0.000775573, 0.000212564, 0.000306843, 0.0110082, 0.000621463, 0.000285039, 0.0023311, 0.000268167, 0.00105154, 0.000346499, 0.00151441, 0.00790729, 0.000142858, 0.00278868, 0.0022235, 0.000335023, 0.000306394, 0.00177008, 0.00112805, 0.000482412, 0.000112189, 0.000533693, 0.000291228, 0.000314271, 0.00150191, 0.000377581, 0.00111058, 0.000123636, 0.00167277, 0.000821935, 0.000622373, 0.00377458, 0.000735045, 0.000670618, 0.000505798, 0.00118849, 9.73777e-05, 0.00441226, 0.000344475, 0.00289803, 0.000677194, 0.000560111, 0.000865136, 0.00110896, 0.0010886, 0.000225829, 0.000985489, 0.000113234, 0.000939944, 0.000205022, 0.000172315, 0.0017283, 0.000659584, 0.000293941, 0.000297987, 0.000474473, 0.00143393, 0.000531095, 0.000675214, 0.00285323, 0.000762069, 0.000864715, 0.00328264, 0.00120189, 0.000368838, 0.00325401, 0.0032084, 0.000273588, 0.00188954, 0.00161981, 0.000420587, 0.00257839, 0.000480532, 9.84292e-05, 0.000105341, 0.000559291, 0.00166139, 0.000108578, 0.00154349, 0.000452534, 0.000312741, 0.00283523, 0.000541564, 0.000226712, 0.000985489, 0.000374461, 0.000260172, 0.000852567, 0.00148011, 0.00123457, 0.000124545, 0.00121842, 0.000228937, 0.000201943, 0.000999529, 0.000257142, 0.0001517, 0.00139599, 0.000470782, 0.000393, 0.000339302, 0.000577316, 0.000130712, 0.000525423, 0.000477959, 0.000673569, 0.000377765, 0.000153488, 7.95176e-05, 0.000169809, 0.000434144, 0.000298278, 0.000699699, 0.00109873, 0.00033535, 0.000166444, 0.000163226, 0.000955664, 0.000323927, 0.000344139, 0.00022298, 0.000400944, 0.000166444, 0.00087618, 0.000494328, 0.00333578, 0.000248746, 0.000430978, 0.000344812, 0.00120424, 0.000916847, 7.81321e-05, 0.000632785, 0.000492642, 0.000311674, 0.000389752, 0.000255017, 0.000108314, 0.000136584, 0.000135653, 9.57278e-05, 0.000119482, 0.000208148, 7.37579e-05, 5.25337e-05, 2.54167e-05, 2.13718e-05]
    fr_ranklist = [2.0920653656736756, 2.084189208599573, 2.0763710645179363, 2.068610472588584, 2.0609069753637437, 2.0532601187610884, 2.0456694520369694, 2.0381345277598486, 2.030654901783924, 2.02323013322295, 2.015859784424248, 2.008543420942912, 2.0012806115161963, 1.9940709280380982, 1.9869139455341212, 1.9798092421362252, 1.9727563990579606, 1.9657550005697821, 1.9588046339745433, 1.9519048895831705, 1.9450553606905148, 1.938255643551376, 1.9315053373567075, 1.9248040442099876, 1.9181513691037675, 1.9115469198963857, 1.9049903072888554, 1.898481144801916, 1.892019048753252, 1.885603638234877, 1.8792345350906818, 1.8729113638941435, 1.8666337519261953, 1.860401329153258, 1.8542137282054267, 1.8480705843548182, 1.8419715354940709, 1.8359162221149998, 1.8299042872874067, 1.82393537663804, 1.8180091383297072, 1.8121252230405342, 1.806283283943376, 1.8004829766853732, 1.7947239593676518, 1.7890058925251733, 1.7833284391067226, 1.7776912644550409, 1.7720940362870998, 1.7665364246745152, 1.7610181020240991, 1.7555387430585494, 1.7500980247972766, 1.744695626537367, 1.7393312298346768, 1.7340045184850625, 1.728715178505743, 1.72346289811679, 1.7182473677227519, 1.7130682798944044, 1.7079253293506276, 1.702818212940413, 1.697746629624994, 1.6927102804601004, 1.6877088685783372, 1.682742099171687, 1.6778096794741306, 1.6729113187443925, 1.6680467282487996, 1.6632156212442657, 1.6584177129613868, 1.6536527205876563, 1.6489203632507943, 1.6442203620021922, 1.6395524398004695, 1.6349163214951437, 1.6303117338104116, 1.6257384053290416, 1.6211960664763743, 1.6166844495044324, 1.6122032884761388, 1.607752319249641, 1.6033312794627408, 1.5989399085174294, 1.5945779475645272, 1.5902451394884247, 1.5859412288919281, 1.5816659620812037, 1.5774190870508256, 1.573200353468919, 1.5690095126624068, 1.5648463176023495, 1.5607105228893854, 1.556601884739265, 1.5525201609684816, 1.5484651109799947, 1.54443649574905, 1.5404340778090877, 1.536457621237747, 1.5325068916429585, 1.5285816561491288, 1.524681683383413, 1.5208067434620762, 1.5169566079769443, 1.5131310499819395, 1.5093298439797023, 1.5055527659083017, 1.5017995931280257, 1.498070104408259, 1.4943640799144422, 1.4906813011951137, 1.4870215511690335, 1.4833846141123863, 1.4797702756460671, 1.476178322723043, 1.4726085436157972, 1.469060727903846, 1.4655346664613387, 1.4620301514447283, 1.4585469762805212, 1.4550849356531, 1.4516438254926225, 1.4482234429629919, 1.4448235864499002, 1.4414440555489447, 1.4380846510538159, 1.4347451749445532, 1.431425430375875, 1.428125221665574, 1.4248443542829832, 1.4215826348375096, 1.4183398710672348, 1.415115871827581, 1.4119104470800456, 1.4087234078809991, 1.4055545663705464, 1.4024037357614547, 1.3992707303281435, 1.3961553653957364, 1.393057457329175, 1.389976823522396, 1.3869132823875658, 1.3838666533443784, 1.3808367568094102, 1.377823414185534, 1.3748264478513925, 1.371845681150929, 1.368880938382972, 1.3659320447908816, 1.3629988265522455, 1.3600811107686352, 1.3571787254554142, 1.3542914995315993, 1.3514192628097768, 1.348561845986071, 1.3457190806301653, 1.3428907991753718, 1.340076834908757, 1.3372770219613124, 1.3344911952981788, 1.3317191907089183, 1.328960844797834, 1.3262159949743397, 1.3234844794433753, 1.3207661371958699, 1.3180608079992515, 1.3153683323880019, 1.3126885516542566, 1.3100213078384504, 1.307366443720006, 1.3047238028080674, 1.3020932293322742, 1.2994745682335813, 1.2968676651551176, 1.2942723664330884, 1.291688519087717, 1.289115970814228, 1.2865545699738685, 1.2840041655849719, 1.281464607314056, 1.2789357454669632, 1.2764174309800351, 1.2739095154113285, 1.2714118509318626, 1.2689242903169076, 1.2664466869373052, 1.263978894750826, 1.261520768293561, 1.2590721626713468, 1.2566329335512263, 1.2542029371529386, 1.251782030240446, 1.2493700701134904, 1.2469669145991806, 1.244572422043614, 1.242186451303525, 1.2398088617379666, 1.237439513200019, 1.2350782660285309, 1.232724981039884, 1.2303795195197915, 1.2280417432151207, 1.225711514325743, 1.2233886954964128, 1.221073149808669, 1.2187647407727669, 1.2164633323196299, 1.2141687887928316, 1.2118809749405983, 1.2095997559078364, 1.2073249972281836, 1.2050565648160831, 1.2027943249588795, 1.2005381443089373, 1.198287889875781, 1.196043429018255, 1.1938046294367068, 1.191571359165187, 1.189343486563671, 1.1871208803102997, 1.1849034093936393, 1.1826909431049573, 1.1804833510305193, 1.178280503043901, 1.1760822692983186, 1.1738885202189744, 1.1716991264954189, 1.1695139590739287, 1.1673328891499002, 1.1651557881602566, 1.162982527775869, 1.1608129798939935, 1.1586470166307195, 1.1564845103134314, 1.154325333473283, 1.1521693588376842, 1.1500164593227988, 1.1478665080260533, 1.145719378218657, 1.1435749433381313, 1.1414330769808503, 1.1392936528945898, 1.137156544971085, 1.1350216272385967, 1.1328887738544873, 1.1307578590978016, 1.1286287573618565, 1.1265013431468383, 1.1243754910524026, 1.122251075770286, 1.1201279720769162, 1.1180060548260338, 1.115885198941313, 1.1137652794089914, 1.111646171270499, 1.1095277496150937, 1.107409889572498, 1.1052924663055386, 1.1031753550027883, 1.1010584308712081, 1.098941569128792, 1.0968246449972119, 1.0947075336944616, 1.0925901104275022, 1.0904722503849065, 1.0883538287295011, 1.0862347205910088, 1.0841148010586872, 1.0819939451739664, 1.079872027923084, 1.0777489242297142, 1.0756245089475975, 1.073498656853162, 1.0713712426381434, 1.0692421409021986, 1.0671112261455127, 1.0649783727614035, 1.0628434550289152, 1.0607063471054103, 1.0585669230191497, 1.056425056661869, 1.0542806217813432, 1.052133491973947, 1.0499835406772013, 1.047830641162316, 1.0456746665267171, 1.0435154896865688, 1.0413529833692807, 1.0391870201060067, 1.0370174722241312, 1.0348442118397438, 1.0326671108501, 1.0304860409260717, 1.0283008735045813, 1.0261114797810258, 1.0239177307016816, 1.0217194969560992, 1.019516648969481, 1.017309056895043, 1.015096590606361, 1.0128791196897002, 1.0106565134363292, 1.008428640834813, 1.0061953705632933, 1.003956570981745, 1.0017121101242192, 0.9994618556910628, 0.9972056750411208, 0.994943435183917, 0.9926750027718165, 0.9904002440921638, 0.9881190250594019, 0.9858312112071685, 0.9835366676803704, 0.9812352592272334, 0.9789268501913311, 0.9766113045035874, 0.9742884856742571, 0.9719582567848795, 0.9696204804802087, 0.9672750189601163, 0.9649217339714694, 0.9625604867999811, 0.9601911382620336, 0.9578135486964752, 0.9554275779563861, 0.9530330854008195, 0.9506299298865097, 0.948217969759554, 0.9457970628470614, 0.9433670664487739, 0.9409278373286531, 0.9384792317064393, 0.9360211052491741, 0.9335533130626951, 0.9310757096830926, 0.9285881490681377, 0.9260904845886718, 0.923582569019965, 0.921064254533037, 0.9185353926859441, 0.9159958344150282, 0.9134454300261317, 0.9108840291857724, 0.9083114809122833, 0.9057276335669118, 0.9031323348448825, 0.9005254317664189, 0.8979067706677257, 0.8952761971919327, 0.8926335562799941, 0.8899786921615499, 0.8873114483457436, 0.8846316676119983, 0.8819391920007484, 0.8792338628041303, 0.8765155205566248, 0.8737840050256604, 0.8710391552021661, 0.868280809291082, 0.8655088047018213, 0.862722978038688, 0.8599231650912432, 0.8571092008246284, 0.8542809193698349, 0.8514381540139291, 0.8485807371902234, 0.845708500468401, 0.8428212745445859, 0.8399188892313649, 0.8370011734477547, 0.8340679552091188, 0.8311190616170281, 0.8281543188490712, 0.8251735521486077, 0.8221765858144663, 0.8191632431905901, 0.8161333466556215, 0.8130867176124343, 0.8100231764776042, 0.8069425426708252, 0.8038446346042638, 0.8007292696718566, 0.7975962642385455, 0.794445433629454, 0.791276592119001, 0.7880895529199545, 0.7848841281724193, 0.7816601289327656, 0.7784173651624905, 0.775155645717017, 0.7718747783344262, 0.7685745696241252, 0.765254825055447, 0.7619153489461844, 0.7585559444510553, 0.7551764135500999, 0.7517765570370083, 0.7483561745073773, 0.7449150643469002, 0.741453023719479, 0.7379698485552718, 0.7344653335386613, 0.7309392720961542, 0.727391456384203, 0.7238216772769571, 0.7202297243539331, 0.7166153858876139, 0.7129784488309666, 0.7093186988048865, 0.705635920085558, 0.7019298955917412, 0.6982004068719745, 0.6944472340916985, 0.6906701560202979, 0.6868689500180609, 0.6830433920230559, 0.6791932565379242, 0.6753183166165873, 0.6714183438508714, 0.6674931083570417, 0.6635423787622531, 0.6595659221909125, 0.6555635042509502, 0.6515348890200054, 0.6474798390315186, 0.6433981152607352, 0.6392894771106147, 0.6351536823976508, 0.6309904873375933, 0.6267996465310811, 0.6225809129491746, 0.6183340379187965, 0.6140587711080722, 0.6097548605115756, 0.605422052435473, 0.6010600914825708, 0.5966687205372594, 0.5922476807503593, 0.5877967115238614, 0.583315550495568, 0.5788039335236259, 0.5742615946709586, 0.5696882661895886, 0.5650836785048566, 0.5604475601995307, 0.5557796379978077, 0.5510796367492058, 0.5463472794123437, 0.5415822870386132, 0.5367843787557343, 0.5319532717512006, 0.5270886812556077, 0.5221903205258694, 0.5172579008283131, 0.5122911314216629, 0.5072897195398998, 0.5022533703750062, 0.49718178705958704, 0.4920746706493727, 0.4869317201055958, 0.4817526322772483, 0.4765371018832101, 0.4712848214942573, 0.4659954815149375, 0.4606687701653236, 0.4553043734626331, 0.4499019752027237, 0.4444612569414509, 0.43898189797590126, 0.43346357532548496, 0.42790596371290024, 0.4223087355449594, 0.4166715608932775, 0.4109941074748268, 0.40527604063234823, 0.39951702331462713, 0.39371671605662395, 0.38787477695946593, 0.38199086167029295, 0.3760646233619601, 0.37009571271259345, 0.36408377788500046, 0.3580284645059292, 0.351929415645182, 0.34578627179457333, 0.33959867084674256, 0.3333662480738049, 0.3270886361058569, 0.32076546490931834, 0.3143963617651233, 0.3079809512467483, 0.3015188551980843, 0.29500969271114474, 0.2884530801036146, 0.2818486308962328, 0.2751959557900123, 0.2684946626432927, 0.2617443564486238, 0.2549446393094855, 0.2480951104168293, 0.241195366025457, 0.23424499943021793, 0.22724360094203955, 0.22019075786377484, 0.21308605446587914, 0.2059290719619018, 0.19871938848380388, 0.19145657905708824, 0.18414021557575222, 0.17676986677705042, 0.16934509821607635, 0.16186547224015146, 0.15433054796303103, 0.1467398812389118, 0.1390930246362565, 0.13138952741141618, 0.12362893548206422, 0.11581079140042727, 0.10793463432632466, 0.10000000000000009]
    fr_ranklist3 = [2.0921996153549203, 2.084455746442129, 2.0767679521705236, 2.0691357946430147, 2.0615588391315858, 2.054036654052528, 2.04656881094186, 2.03915488443092, 2.031794452222139, 2.024487095064985, 2.017232396732084, 2.01002994399551, 2.0028793266032494, 1.9957801372558306, 1.988731971583127, 1.981734428121322, 1.9747871082900428, 1.9678896163696555, 1.9610415594787276, 1.9542425475516472, 1.9474921933164058, 1.940790112272539, 1.9341359226692254, 1.9275292454835427, 1.9209697043988765, 1.9144569257834887, 1.9079905386692315, 1.9015701747304203, 1.8951954682628525, 1.8888660561629758, 1.8825815779072081, 1.8763416755314009, 1.870145993610449, 1.8639941792380472, 1.8578858820065869, 1.8518207539871985, 1.8457984497099313, 1.8398186261440779, 1.833880942678634, 1.8279850611028963, 1.8221306455872, 1.816317362663788, 1.810544881207818, 1.8048128724185006, 1.7991210098003714, 1.7934689691446934, 1.7878564285109908, 1.7822830682087107, 1.7767485707790125, 1.7712526209766875, 1.7657949057522013, 1.760375114233863, 1.7549929377101163, 1.7496480696119585, 1.7443402054954746, 1.7390690430244993, 1.7338342819533938, 1.7286356241099452, 1.723472773378381, 1.7183454356825045, 1.7132533189689423, 1.70819613319051, 1.7031735902896905, 1.6981854041822264, 1.693231290740825, 1.6883109677789738, 1.683424155034868, 1.6785705741554455, 1.6737499486805334, 1.6689620040270992, 1.664206467473612, 1.6594830681445074, 1.6547915369947588, 1.6501316067945524, 1.645503012114066, 1.6409054893083492, 1.636338776502308, 1.6318026135757866, 1.6272967421487514, 1.622820905566575, 1.6183748488854146, 1.613958318857693, 1.609571063917672, 1.6052128341671232, 1.6008833813610952, 1.5965824588937716, 1.592309821784426, 1.588065226663467, 1.583848431758576, 1.5796591968809355, 1.5754972834115488, 1.571362454287648, 1.5672544739891905, 1.5631731085254432, 1.559118125421656, 1.5550892937058187, 1.5510863838955058, 1.5471091679848037, 1.5431574194313256, 1.5392309131433053, 1.535329425466778, 1.5314527341728397, 1.527600618444989, 1.523772858866551, 1.5199692374081764, 1.5161895374154257, 1.5124335435964265, 1.5087010420096112, 1.5049918200515304, 1.501305666444744, 1.4976423712257854, 1.4940017257332034, 1.4903835225956759, 1.486787555720198, 1.4832136202803439, 1.479661512704599, 1.4761310306647641, 1.4726219730644328, 1.4691341400275344, 1.4656673328869514, 1.462221354173201, 1.4587960076031894, 1.45539109806903, 1.4520064316269317, 1.4486418154861491, 1.4452970579980042, 1.4419719686449681, 1.43866635802981, 1.4353800378648085, 1.4321128209610274, 1.4288645212176527, 1.425634953611393, 1.42242393418594, 1.4192312800414908, 1.4160568093243293, 1.412900341216469, 1.4097616959253514, 1.4066406946736079, 1.4035371596888742, 1.4004509141936656, 1.3973817823953076, 1.3943295894759222, 1.391294161582471, 1.3882753258168523, 1.385272910226052, 1.382286743792351, 1.3793166564235815, 1.3763624789434414, 1.3734240430818554, 1.3705011814653916, 1.3675937276077286, 1.3647015159001707, 1.361824381602216, 1.3589621608321727, 1.3561146905578236, 1.3532818085871414, 1.3504633535590482, 1.3476591649342258, 1.3448690829859706, 1.3420929487910966, 1.339330604220882, 1.3365818919320622, 1.3338466553578685, 1.3311247386991094, 1.3284159869152963, 1.3257202457158122, 1.3230373615511237, 1.3203671816040348, 1.3177095537809822, 1.3150643267033721, 1.3124313496989577, 1.3098104727932565, 1.3072015467010087, 1.3046044228176732, 1.3020189532109632, 1.2994449906124197, 1.296882388409024, 1.294331000634845, 1.2917906819627272, 1.2892612876960106, 1.2867426737602905, 1.2842346966952096, 1.2817372136462881, 1.279250082356785, 1.2767731611595963, 1.2743063089691853, 1.2718493852735462, 1.2694022501262012, 1.2669647641382278, 1.2645367884703207, 1.2621181848248824, 1.2597088154381466, 1.2573085430723303, 1.254917231007818, 1.2525347430353722, 1.250160943448377, 1.2477956970351067, 1.2454388690710254, 1.2430903253111116, 1.240749931982213, 1.2384175557754264, 1.2360930638385035, 1.2337763237682848, 1.2314672036031573, 1.2291655718155383, 1.2268712973043834, 1.2245842493877184, 1.2223042977951972, 1.2200313126606799, 1.2177651645148366, 1.2155057242777727, 1.213252863251676, 1.2110064531134865, 1.2087663659075876, 1.2065324740385164, 1.2043046502636972, 1.2020827676861932, 1.1998666997474778, 1.1976563202202275, 1.1954515032011304, 1.193252123103715, 1.1910580546511973, 1.1888691728693455, 1.1866853530793597, 1.184506470890772, 1.1823324021943602, 1.180163023155079, 1.1779982102050062, 1.1758378400363036, 1.173681789594195, 1.1715299360699551, 1.169382156893916, 1.1672383297284845, 1.1650983324611737, 1.1629620431976486, 1.1608293402547818, 1.1587001021537229, 1.1565742076129792, 1.1544515355415073, 1.1523319650318153, 1.1502153753530775, 1.1481016459442552, 1.1459906564072317, 1.1438822864999532, 1.14177641612958, 1.1396729253456461, 1.1375716943332275, 1.1354726034061158, 1.1333755330000026, 1.1312803636656688, 1.1291869760621795, 1.127095250950088, 1.1250050691846432, 1.1229163117090024, 1.1208288595474512, 1.1187425937986257, 1.11665739562874, 1.1145731462648178, 1.1124897269879268, 1.1104070191264162, 1.1083249040491574, 1.1062432631587873, 1.1041619778849523, 1.1020809296775547, 1.1, 1.0979190703224455, 1.0958380221150479, 1.0937567368412129, 1.0916750959508428, 1.089592980873584, 1.0875102730120734, 1.0854268537351823, 1.0833426043712602, 1.0812574062013744, 1.079171140452549, 1.0770836882909978, 1.074994930815357, 1.0729047490499122, 1.0708130239378206, 1.0687196363343314, 1.0666244669999976, 1.0645273965938844, 1.0624283056667727, 1.060327074654354, 1.0582235838704201, 1.056117713500047, 1.0540093435927684, 1.051898354055745, 1.0497846246469227, 1.0476680349681848, 1.045548464458493, 1.043425792387021, 1.0412998978462773, 1.0391706597452184, 1.0370379568023516, 1.0349016675388265, 1.0327616702715157, 1.0306178431060842, 1.028470063930045, 1.0263182104058053, 1.0241621599636965, 1.022001789794994, 1.0198369768449211, 1.01766759780564, 1.0154935291092282, 1.0133146469206404, 1.0111308271306547, 1.0089419453488029, 1.0067478768962852, 1.0045484967988698, 1.0023436797797727, 1.0001333002525223, 0.997917232313807, 0.995695349736303, 0.9934675259614838, 0.9912336340924127, 0.9889935468865136, 0.9867471367483243, 0.9844942757222276, 0.9822348354851635, 0.9799686873393203, 0.977695702204803, 0.9754157506122818, 0.9731287026956169, 0.9708344281844619, 0.9685327963968429, 0.9662236762317153, 0.9639069361614967, 0.9615824442245737, 0.9592500680177871, 0.9569096746888887, 0.9545611309289749, 0.9522043029648936, 0.9498390565516233, 0.947465256964628, 0.9450827689921824, 0.9426914569276699, 0.9402911845618537, 0.9378818151751178, 0.9354632115296796, 0.9330352358617725, 0.9305977498737992, 0.928150614726454, 0.925693691030815, 0.923226838840404, 0.9207499176432152, 0.9182627863537122, 0.9157653033047906, 0.9132573262397098, 0.9107387123039896, 0.908209318037273, 0.9056689993651552, 0.9031176115909765, 0.9005550093875805, 0.8979810467890371, 0.895395577182327, 0.8927984532989915, 0.8901895272067438, 0.8875686503010427, 0.8849356732966281, 0.882290446219018, 0.8796328183959654, 0.8769626384488766, 0.8742797542841882, 0.871584013084704, 0.8688752613008909, 0.8661533446421318, 0.8634181080679381, 0.8606693957791183, 0.8579070512089035, 0.8551309170140295, 0.8523408350657744, 0.849536646440952, 0.8467181914128588, 0.8438853094421765, 0.8410378391678277, 0.8381756183977842, 0.8352984840998297, 0.8324062723922716, 0.8294988185346086, 0.826575956918145, 0.823637521056559, 0.8206833435764187, 0.8177132562076493, 0.8147270897739483, 0.811724674183148, 0.8087058384175292, 0.805670410524078, 0.8026182176046925, 0.7995490858063345, 0.796462840311126, 0.7933593053263923, 0.7902383040746489, 0.7870996587835315, 0.7839431906756709, 0.7807687199585096, 0.7775760658140602, 0.7743650463886071, 0.7711354787823474, 0.7678871790389726, 0.7646199621351915, 0.7613336419701899, 0.7580280313550318, 0.7547029420019958, 0.751358184513851, 0.7479935683730685, 0.7446089019309698, 0.7412039923968107, 0.7377786458267991, 0.7343326671130488, 0.7308658599724656, 0.7273780269355672, 0.7238689693352359, 0.7203384872954012, 0.7167863797196561, 0.713212444279802, 0.7096164774043243, 0.7059982742667966, 0.7023576287742146, 0.6986943335552561, 0.6950081799484696, 0.691298957990389, 0.6875664564035735, 0.6838104625845745, 0.6800307625918237, 0.6762271411334493, 0.672399381555011, 0.6685472658271605, 0.664670574533222, 0.6607690868566947, 0.6568425805686746, 0.6528908320151964, 0.6489136161044944, 0.6449107062941815, 0.6408818745783442, 0.636826891474557, 0.6327455260108097, 0.628637545712352, 0.6245027165884512, 0.6203408031190648, 0.6161515682414243, 0.6119347733365331, 0.607690178215574, 0.6034175411062286, 0.599116618638905, 0.594787165832877, 0.5904289360823284, 0.5860416811423071, 0.5816251511145857, 0.5771790944334253, 0.5727032578512488, 0.5681973864242137, 0.5636612234976922, 0.559094510691651, 0.5544969878859343, 0.5498683932054477, 0.5452084630052413, 0.5405169318554928, 0.5357935325263882, 0.531037995972901, 0.5262500513194668, 0.5214294258445545, 0.5165758449651322, 0.5116890322210264, 0.5067687092591753, 0.5018145958177739, 0.4968264097103098, 0.4918038668094902, 0.4867466810310578, 0.48165456431749565, 0.47652722662161906, 0.4713643758900551, 0.46616571804660634, 0.4609309569755008, 0.45565979450452554, 0.45035193038804167, 0.44500706228988374, 0.43962488576613723, 0.43420509424779874, 0.4287473790233126, 0.4232514292209877, 0.41771693179128955, 0.41214357148900926, 0.40653103085530673, 0.4008789901996288, 0.39518712758149965, 0.3894551187921822, 0.3836826373362121, 0.37786935441280023, 0.3720149388971039, 0.3661190573213663, 0.3601813738559223, 0.35420155029006894, 0.3481792460128018, 0.3421141179934132, 0.33600582076195296, 0.3298540063895512, 0.3236583244685993, 0.31741842209279203, 0.3111339438370244, 0.3048045317371477, 0.29842982526957973, 0.29200946133076866, 0.2855430742165115, 0.2790302956011236, 0.27247075451645764, 0.2658640773307749, 0.25920988772746123, 0.2525078066835944, 0.245757452448353, 0.23895844052127269, 0.23211038363034464, 0.2252128917099575, 0.21826557187867812, 0.2112680284168731, 0.2042198627441696, 0.19712067339675088, 0.18997005600449013, 0.1827676032679162, 0.17551290493501515, 0.16820554777786134, 0.16084511556908032, 0.15343118905814035, 0.14596334594747218, 0.13844116086841451, 0.1308642053569855, 0.12323204782947672, 0.11554425355787101, 0.1078003846450798, 0.10000000000000009]

    vtrlist3 = [3.33333e-05] * len(fr_ranklist3)
    ltrlist3 = [0] * len(fr_ranklist3)
    fprlist3 = [0] * len(fr_ranklist3)
    ftrlist3 = [0] * len(fr_ranklist3)

    leaf = self.__init_service(flow)
    for i in range(len(vtrlist)):
      item = leaf.add_item(i)
      item['vtr'] = vtrlist[i]
      item['fpr'] = fprlist[i]
      item['ltr'] = ltrlist[i]

      item['vtr1'] = vtrlist1[i]
      item['fpr1'] = fprlist1[i]
      item['ltr1'] = ltrlist1[i]

      item['consume'] = fr_ranklist[i]
      item['vtr2'] = vtrlist1[i]
      item['fpr2'] = fprlist1[i]
      item['ltr2'] = ltrlist1[i]

      item['consume3'] = fr_ranklist3[i]
      item['vtr3'] = vtrlist3[i]
      item['fpr3'] = fprlist3[i]
      item['ltr3'] = ltrlist3[i]
      item['ftr3'] = ftrlist3[i]
    
    leaf.run("test_oversea_multi_constraints_optimize_v2")

    # test0: 两个约束，有解，有最优解，有次优解，sub_lambda_version = 0, tolerance_version = 0
    print("------\n")
    print(f"\n leaf['output_opt_lambdas_v0']: {leaf['output_opt_lambdas_v0']}")
    print(f"\n leaf['extra_output_attrs_v0']: {leaf['extra_output_attrs_v0']}")
    print(f"\n leaf['find_solution_v0']: {leaf['find_solution_v0']}")
    print(f"\n leaf['find_optimal_v0']: {leaf['find_optimal_v0']}")
    print(f"\n leaf['find_suboptimal_v0']: {leaf['find_suboptimal_v0']}")
    self.assertEqual(leaf['find_solution_v0'], 1)
    self.assertEqual(leaf['find_optimal_v0'], 1)
    self.assertEqual(leaf['find_suboptimal_v0'], 1)
    # lambda [0.958984375, 0.004849624070192199, 0.03616600092980853]
    # extra_output [1.0, 1.0, 0.051408117632458734]

    # test1: 两个约束，有解，无最优解，有次优解，sub_lambda_version = 0, tolerance_version = 0
    print(f"\n leaf['output_opt_lambdas_v1']: {leaf['output_opt_lambdas_v1']}")
    print(f"\n leaf['extra_output_attrs_v1']: {leaf['extra_output_attrs_v1']}")
    self.assertEqual(leaf['find_solution_v1'], 1)
    self.assertEqual(leaf['find_optimal_v1'], 0)
    self.assertEqual(leaf['find_suboptimal_v1'], 1)
    # lambda [0.11083234708687673, 0.8689193725585938, 0.020248280354528754]
    # extra_output[:3] [0.0, 1.0, 0.03675083556609754]

    # test2: 三个约束，有解，无最优解，有次优解，带roi类型的约束，sub_lambda_version = 1, tolerance_version = 1
    print(f"\n leaf['output_opt_lambdas_v2']: {leaf['output_opt_lambdas_v2']}")
    print(f"\n leaf['extra_output_attrs_v2']: {leaf['extra_output_attrs_v2']}")
    self.assertEqual(leaf['find_solution_v2'], 1)
    self.assertEqual(leaf['find_optimal_v2'], 0)
    self.assertEqual(leaf['find_suboptimal_v2'], 1)
    # lambda [7.272609551227818e-06, 0.10569346209945422, 0.875, 0.019299265290994544]
    # extra_output[:3] [0.0, 1.0, 0.036021248646106646]

    # test3: 三个约束，有解，无最优解，有次优解，带roi类型的约束，sub_lambda_version = 1, tolerance_version = 1，其余所有选填的取默认值
    print(f"\n leaf['output_opt_lambdas_v3']: {leaf['output_opt_lambdas_v3']}")
    print(f"\n leaf['extra_output_attrs_v3']: {leaf['extra_output_attrs_v3']}")
    self.assertEqual(leaf['find_solution_v3'], 1)
    self.assertEqual(leaf['find_optimal_v3'], 0)
    self.assertEqual(leaf['find_suboptimal_v3'], 1)
    # lambda [7.272609551227818e-06, 0.10569346209945422, 0.875, 0.019299265290994544]
    # extra_output[:3] [0.0, 1.0, 0.036021248646106646]
    # 新增这俩判断
    self.assertEqual(leaf['upgrade_opt_v3'], leaf['upgrade_opt_v2'])
    self.assertEqual(leaf['output_opt_lambdas_v3'], leaf['output_opt_lambdas_v2'])

    # test4: 三个约束，有解，无最优解，有次优解，带roi类型的约束，所有选填的取默认值
    print(f"\n leaf['output_opt_lambdas_v4']: {leaf['output_opt_lambdas_v4']}")
    print(f"\n leaf['extra_output_attrs_v4']: {leaf['extra_output_attrs_v4']}")
    self.assertEqual(leaf['find_solution_v4'], 1)
    self.assertEqual(leaf['find_optimal_v4'], 0)
    self.assertEqual(leaf['find_suboptimal_v4'], 1)
    # lambda [0.0001220703125, 0.11084961909726652, 0.8687894177543638, 0.020238892835868993]
    # extra_output[:3] [0.0, 1.0, 0.03675565489884815]

    # test5: 异常输入，三个约束，无解，带roi类型的约束，所有选填的取默认值
    print(f"\n leaf['output_opt_lambdas_v5']: {leaf['output_opt_lambdas_v5']}")
    print(f"\n leaf['extra_output_attrs_v5']: {leaf['extra_output_attrs_v5']}")
    print(f"\n leaf['find_solution_v5']: {leaf['find_solution_v5']}")
    self.assertEqual(leaf['find_solution_v5'], 0)
    # lambda [-1, -1, -1, -1]
    # extra_output[:3] [0.0, 0.0, 0.0]

    # test6: 异常输入，三个约束，无解，不带roi类型的约束，所有选填的取默认值
    print(f"\n leaf['output_opt_lambdas_v6']: {leaf['output_opt_lambdas_v6']}")
    print(f"\n leaf['extra_output_attrs_v6']: {leaf['extra_output_attrs_v6']}")
    print(f"\n leaf['find_solution_v6']: {leaf['find_solution_v6']}")
    self.assertEqual(leaf['find_solution_v6'], 0)
    # lambda [-1, -1, -1, -1]
    # extra_output[:3] [0.0, 0.0, 0.0]
    
    # test7: 异常输入，三个约束，无解，不带roi类型的约束，所有选填的取默认值
    print(f"\n leaf['output_opt_lambdas_v7']: {leaf['output_opt_lambdas_v7']}")
    print(f"\n leaf['extra_output_attrs_v7']: {leaf['extra_output_attrs_v7']}")
    print(f"\n leaf['find_solution_v7']: {leaf['find_solution_v7']}")
    self.assertEqual(leaf['find_solution_v7'], 0)
    # lambda [-1, -1, -1, -1]
    # extra_output[:3] [0.0, 0.0, 0.0]

    # test8: 两个约束，有解，有最优解，有次优解，sub_lambda_version = 0, tolerance_version = 0, tolerancevalue=value
    print(f"\n leaf['output_opt_lambdas_v8']: {leaf['output_opt_lambdas_v8']}")
    print(f"\n leaf['extra_output_attrs_v8']: {leaf['extra_output_attrs_v8']}")
    print(f"\n leaf['find_solution_v8']: {leaf['find_solution_v8']}")
    print(f"\n leaf['find_optimal_v8']: {leaf['find_optimal_v8']}")
    print(f"\n leaf['find_suboptimal_v8']: {leaf['find_suboptimal_v8']}")
    self.assertEqual(leaf['find_solution_v8'], 1)
    self.assertEqual(leaf['find_optimal_v8'], 1)
    self.assertEqual(leaf['find_suboptimal_v8'], 1)
    # lambda [0.958984375, 0.004849624070192199, 0.03616600092980853]
    # extra_output [1.0, 1.0, 0.051408117632458734]

    # test9: 两个约束，无解，sub_lambda_version = 0, tolerance_version = 0, limitsize > len(vtr)
    print(f"\n leaf['output_opt_lambdas_v9']: {leaf['output_opt_lambdas_v9']}")
    print(f"\n leaf['extra_output_attrs_v9']: {leaf['extra_output_attrs_v9']}")
    print(f"\n leaf['find_solution_v9']: {leaf['find_solution_v9']}")
    print(f"\n leaf['find_optimal_v9']: {leaf['find_optimal_v9']}")
    print(f"\n leaf['find_suboptimal_v9']: {leaf['find_suboptimal_v9']}")

    # test10: 3个约束，但是第一个约束的value=0
    print(f"\n leaf['output_opt_lambdas_v10']: {leaf['output_opt_lambdas_v10']}")
    print(f"\n leaf['extra_output_attrs_v10']: {leaf['extra_output_attrs_v10']}")
    print(f"\n leaf['find_solution_v10']: {leaf['find_solution_v10']}")
    

if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('oversea_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
