#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/embed_calc_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.embed_calc_api_test.TestFlowFunc.test_xxx

import unittest
import math
import random
import json
import base64
import struct
from itertools import chain
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.embed_calc.embed_calc_api_mixin import EmbedCalcApiMixin

# LeafService.I_AM_MASTER_DRIVER = True

def encode_bytes(data: bytes) -> str:
  """
  PB bytes field need to be base64-encoded in json format. 
  """
  return base64.b64encode(data).decode('utf-8')

def str2bytes(data: str) -> bytes:
  return bytes(data, 'utf-8')

def int2bytes(data: int) -> bytes:
  return struct.pack('<q', data)

def float2bytes(data: float) -> bytes:
  return struct.pack('<d', data)

def gen_key_sign(item_type: int, item_id: int) -> int:
  return item_type << 56 | item_id

class EmbedCalcFlow(LeafFlow, EmbedCalcApiMixin):
  pass

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_trans_local_embedding(self):
    # flow
    # 该 processor 涉及的输入都是 c++ 指针类型输入，python 无法构造输入，故在这里只是走个形式
    flow = EmbedCalcFlow(name="test_trans_local_embedding")\
        .trans_local_embedding(
          trans_attrs = [
            dict(
              input_attr = "one_photo_input",
              as_attr = "one_photo_input_t",
              trans_type = "c2c"
            ),
            dict(
              input_attr = "one_photo_input2",
              as_attr = "one_photo_input2_t",
              trans_type = "c2i",
            ),
          ]
        )

    # mocking running data

    # generate assert cases

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_trans_local_embedding")

    # assert
    self.assertEqual(leaf["nonsense"], 1)

  def test_predict_dot_product_xtr(self):
    # flow
    # 该 processor 主要输入都是 c++ 指针类型输入，python 无法构造输入，故在这里只是走个形式
    flow = EmbedCalcFlow(name="test_predict_dot_product_xtr")\
      .predict_dot_product_xtr(
        embedding_type="float_ptr",
        predict_target_num=14,
        common_embedding_dim=128,
        common_embedding_attr="user_embedding",
        load_from_item_context=False,
        item_embedding_attr="item_embedding",
        miss_embedding_mark="miss_embedding",
        output_type=0,
        pxtr_value_attr="pxtr_value",
      )\


    # mocking running data

    # generate assert cases

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_dot_product_xtr")

    # assert
    self.assertEqual(leaf["nonsense"], 1)
    
  def test_predict_dot_product_xtr_v_float(self):
    flow = EmbedCalcFlow(name="test_predict_dot_product_xtr_v_float")\
      .predict_dot_product_xtr_v_float(
        predict_target_num=14,
        common_embedding_dim=128,
        common_embedding_attr="user_embedding",
        load_from_item_context=False,
        item_embedding_attr="item_embedding",
        miss_embedding_mark="miss_embedding",
        output_type=0,
        pxtr_value_attr="pxtr_value",
      )

    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_dot_product_xtr_v_float")
    self.assertEqual(leaf["nonsense"], 1)

  def test_predict_dot_product_xtr_v_int16(self):
    flow = EmbedCalcFlow(name="test_predict_dot_product_xtr_v_int16")\
      .predict_dot_product_xtr_v_int16(
        predict_target_num=14,
        common_embedding_dim=128,
        common_embedding_attr="user_embedding",
        load_from_item_context=False,
        item_embedding_attr="item_embedding",
        miss_embedding_mark="miss_embedding",
        output_type=0,
        pxtr_value_attr="pxtr_value",
      )

    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_dot_product_xtr_v_int16")
    self.assertEqual(leaf["nonsense"], 1)

  def test_predict_dot_product_xtr_v_hetu_tag_float(self):
    flow = EmbedCalcFlow(name="test_predict_dot_product_xtr_v_hetu_tag_float")\
      .predict_dot_product_xtr_v_hetu_tag_float(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=0,
          pxtr_value_attr="pxtr_value",
          item_hetu_tag_attr="hetu_tags",
          reg_hetu_tags=[1,2,3,4,5],
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_dot_product_xtr_v_hetu_tag_float")
    self.assertEqual(leaf["nonsense"], 1)

  def test_sim_embedding_dot_product(self):
    flow = EmbedCalcFlow(name="test_sim_embedding_dot_product")\
      .sim_embedding_dot_product(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=0,
          pxtr_value_attr="pxtr_value",
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_sim_embedding_dot_product")
    self.assertEqual(leaf["nonsense"], 1)

  def test_predict_mvke_pxtr(self):
    flow = EmbedCalcFlow(name="test_predict_mvke_pxtr")\
      .predict_mvke_pxtr(
        pemb_dim=64,
        vk_num=8,
        predict_target_num=13,
        common_embedding_dim=512,
        common_embedding_attr="user_embedding",
        load_from_item_context=False,
        item_embedding_attr="item_embedding",
        miss_embedding_mark="miss_embedding",
        output_type=0,
        pxtr_value_attr="pxtr_value",
      )

    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_dot_product_xtr_v_int16")
    self.assertEqual(leaf["nonsense"], 1)

  def test_sim_embedding_topn_dot_product(self):
    flow = EmbedCalcFlow(name="test_sim_embedding_topn_dot_product")\
      .sim_embedding_topn_dot_product(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=4,
          pxtr_value_attr="pxtr_value",
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_sim_embedding_topn_dot_product")
    self.assertEqual(leaf["nonsense"], 1)

  def test_sim_embedding_topn_dot_product_multi_head_bias(self):
    flow = EmbedCalcFlow(name="test_sim_embedding_topn_dot_product_multi_head_bias")\
      .sim_embedding_topn_dot_product_multi_head_bias(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=0,
          pxtr_value_attr="pxtr_value",
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_sim_embedding_topn_dot_product_multi_head_bias")
    self.assertEqual(leaf["nonsense"], 1)

  def test_float_embedding_dot_product_pxtr(self):
    flow = EmbedCalcFlow(name="test_float_embedding_dot_product_pxtr")\
      .float_embedding_dot_product_pxtr(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=0,
          pxtr_value_attr="pxtr_value",
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_float_embedding_dot_product_pxtr")
    self.assertEqual(leaf["nonsense"], 1)

  def test_predict_cluster_xtr_v_float(self):
    flow = EmbedCalcFlow(name="test_predict_cluster_xtr_v_float")\
      .predict_cluster_xtr_v_float(
          predict_target_num=14,
          common_embedding_dim=128,
          common_embedding_attr="user_embedding",
          load_from_item_context=False,
          item_embedding_attr="item_embedding",
          miss_embedding_mark="miss_embedding",
          output_type=0,
          pxtr_value_attr="pxtr_value",
          need_map_cluster=True,
          common_cluster_attr="user_cluster",
          item_cluster_attr="item_cluster",
          req_common_embedding="req_common_embedding",
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_predict_cluster_xtr_v_float")
    self.assertEqual(leaf["nonsense"], 1)

  def test_emb_matmul_reduce_sum_int16(self):
    flow = EmbedCalcFlow(name="test_emb_matmul_reduce_sum_int16")\
      .emb_matmul_reduce_sum_int16(
          predict_target_num=1,
          load_from_item_context=False,
          common_embedding_dim=32,
          common_embedding_attr="M_embedding_value",
          item_embedding_attr="N_embedding_value",
          miss_embedding_mark="N_miss",
          output_type=3,
          req_common_embedding="M_embedding_value.placeholder",
          predict_labels=["ctr"],
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_emb_matmul_reduce_sum_int16")
    self.assertEqual(leaf["nonsense"], 1)

  def test_emb_matmul_reduce_sum_float(self):
    flow = EmbedCalcFlow(name="test_emb_matmul_reduce_sum_float")\
      .emb_matmul_reduce_sum_float(
          predict_target_num=1,
          load_from_item_context=False,
          common_embedding_dim=32,
          common_embedding_attr="M_embedding_value",
          item_embedding_attr="N_embedding_value",
          miss_embedding_mark="N_miss",
          output_type=3,
          req_common_embedding="M_embedding_value.placeholder",
          predict_labels=["ctr"],
         )
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_emb_matmul_reduce_sum_float")
    self.assertEqual(leaf["nonsense"], 1)

  def test_list_add(self):
    flow = EmbedCalcFlow(name="list_add")\
      .list_add(
          item_list_attr="item_list",
          item_add_attr="item_add",
          output_item_list_attr="item_list_add"
      )
    leaf = self.__init_service(flow)
    item = leaf.add_item(111)
    item["item_list"] = [1,1]
    item["item_add"] = 1
    leaf.run("list_add")
    self.assertEqual(item["item_list_add"], [2, 2])

  def test_list_norm(self):
    flow = EmbedCalcFlow(name="list_norm")\
      .list_norm(
          item_list_attr="item_list",
          output_item_list_attr="item_list_norm"
      )
    leaf = self.__init_service(flow)
    item = leaf.add_item(111)
    item["item_list"] = [1.0,1.0,1.0,1.0]
    item2 = leaf.add_item(222)
    item2["item_list"] = [0.0, 0.0]
    leaf.run("list_norm")
    self.assertEqual(item["item_list_norm"], [0.5, 0.5, 0.5, 0.5])
    self.assertEqual(item2["item_list_norm"], [])

  def test_simple_filter_by_redis_ids(self):
    # 涉及 redis rpc，先略过本 test
    # mock running data

    # define flow
    flow = EmbedCalcFlow(name="test_simple_filter_by_redis_ids")\
        .simple_filter_by_redis_ids(
          filter_key_attr="item_id",
          filter_type=0,
          redis_keys=["XXX_IDS"],
          redis_key_num=50,
          redis_value_get_type=0,
          redis_value_parse_type=0,
          redis_cluster="recoNewUserOffline",
          redis_timeout_ms=1000,
          redis_cycle_sec=60,
          )

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_simple_filter_by_redis_ids")

    # assert
    self.assertEqual(leaf["nonsense"], 1)

  def test_simple_mark_by_redis_ids(self):
    # 涉及 redis rpc，先略过本 test
    # mock running data

    # define flow
    flow = EmbedCalcFlow(name="test_simple_mark_by_redis_ids")\
        .simple_mark_by_redis_ids(
          filter_key_attr="item_id",
          filter_mark_attr="item_filter_mark",
          redis_keys=["XXX_IDS"],
          redis_key_num=50,
          redis_value_get_type=0,
          redis_value_parse_type=0,
          redis_cluster="recoNewUserOffline",
          redis_timeout_ms=1000,
          redis_cycle_sec=60,
          )

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_simple_mark_by_redis_ids")

    # assert
    self.assertEqual(leaf["nonsense"], 1)

  def test_calc_item_side_distance(self):
    # define flow
    flow = EmbedCalcFlow(name="test_calc_item_side_distance")\
        .calc_item_side_distance(
            photo_lat_attr="pLat",
            photo_lon_attr="pLong",
            neighbour_photos_lat_attr='neighbours_pLat',
            neighbour_photos_lon_attr='neighbours_pLong',
            output_distance_attr='output_distances',
            distance_limit_common_attr="distance_limit", # 距离限制m
            neighbour_photos_attr="geo_neighbor_pid_list_raw",
            output_filterd_neighbour_photos_attr="geo_neighbor_pid_list",
            output_filterd_distance_attr="output_distances_filter")

    # run test
    leaf = self.__init_service(flow)
    leaf["distance_limit"] = 0
    item = leaf.add_item(111)
    item["pLat"] = 10.0
    item["pLong"] = 10.0
    item["neighbours_pLat"] = [10.0]
    item["neighbours_pLong"] = [10.0]
    item["geo_neighbor_pid_list_raw"] = [6999000]
    leaf.run("test_calc_item_side_distance")

    print(item["output_distances"])
    # assert
    self.assertAlmostEqual(item["output_distances"][0], 0.0)
  

  def test_fetch_tower_topn_dot_product_pxtr(self):
    # 涉及 redis rpc，先略过本 test
    # mock running data

    # define flow
    flow = EmbedCalcFlow(name="test_fetch_tower_topn_dot_product_pxtr")\
        .fetch_tower_topn_dot_product_pxtr(
            debug_log=False,
            user_embedding_attr="model_query_embedding",
            item_list_from_attr="filterd_segsu_photo_id",
            use_item_key_as_embed_key=True,
            predict_labels=["dp"],
            kess_service="grpc_videosearch_gsu_online",
            shards=8,
            timeout_ms=20000,
            sub_req_num_in_shard=1,
            server_request_type="calc_topn_dot_product",
            req_common_embedding_attr="req_item_emb",
            return_pxtr_value_attr="distance",
            return_sorted_item_ids_attr="sorted_item_ids_vec",
            sorted_item_idx_attr="search_sorted_segsu_item_index",
            sorted_item_pxtrs_attr="search_sorted_item_pxtr",
            pxtr_type=1,
            emb_dim=64,
            output_type=5,
          )

    # run test
    leaf = self.__init_service(flow)
    leaf["nonsense"] = 1
    leaf.run("test_fetch_tower_topn_dot_product_pxtr")

    # assert
    self.assertEqual(leaf["nonsense"], 1)
  
  def test_doc_product_pxtr_item_attr(self):
    flow = EmbedCalcFlow(name="test_doc_product_pxtr_item_attr")\
    .doc_product_pxtr_item_attr(
      use_cosine = True,
      gsu_embedding_attr = 'gsu_embedding',
      target_embedding_attr = 'target_embedding',
      return_pxtr_value_attr = 'return_pxtr'
    )

    leaf = self.__init_service(flow)
    for i in range(1):
      item = leaf.add_item(i)
      item["gsu_embedding"] = [1.1,2.0,3.0,4.0]
      item["target_embedding"] = [3.0,4.0]
    leaf["nonsense"] = 1
    leaf.run("test_doc_product_pxtr_item_attr")

    # assert
    self.assertEqual(leaf["nonsense"], 1)
  
  def test_norm_btq_emb(self):
      flow = EmbedCalcFlow(name="norm_btq_emb")
      leaf = self.__init_service(flow)
      leaf.user_id = 666
       # 暂时不支持 btq 先跳过，等支持了再打开
      return
  
  def test_doc_product_pxtr_common_attr(self):
      flow = EmbedCalcFlow(name="test_doc_product_pxtr_common_attr")\
      .doc_product_pxtr_common_attr(
        use_cosine = True,
        gsu_embedding_attr = 'gsu_embedding',
        target_embedding_attr = 'target_embedding',
        return_pxtr_value_attr = 'return_pxtr'
      )
  
      leaf = self.__init_service(flow)
      leaf["gsu_embedding"] = [1.1,2.0,3.0,4.0]
      leaf["target_embedding"] = [3.0,4.0]
      leaf["nonsense"] = 1
      leaf.run("test_doc_product_pxtr_common_attr")
  
      # assert
      self.assertEqual(leaf["nonsense"], 1)
  
  def test_convert_embedding_format(self):
    flow = EmbedCalcFlow(name="test_convert_embedding_format") \
      .convert_embedding_format(
        is_common = False,
        input_type = "double_list",
        output_type = "float_ptr",
        input_attr = "input_embedding",
        output_attr = "output_embedding",
      ) \
      .trans_local_embedding(
        trans_attrs = [
          dict(
            input_attr = "output_embedding",
            as_attr = "item_embedding",
            trans_type = "i2i",
            emb_type = "float",  # float/double/int16/int/int64
            dim = 4,
          ),
        ]
      ) \

    leaf = self.__init_service(flow)
    item = leaf.add_item(1)
    item["input_embedding"] = [1.1, 2.2, 3.3, 4.4]

    leaf.run("test_convert_embedding_format")

    for a, b in zip(item["item_embedding"], item["input_embedding"]):
      self.assertAlmostEqual(a, b, places=6)

if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('embed_calc_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
