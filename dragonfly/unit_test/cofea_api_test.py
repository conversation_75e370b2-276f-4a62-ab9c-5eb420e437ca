#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="cofea" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/cofea_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.cofea_api_test.TestFlowFunc.xxxx

import unittest
import math
import random
import json
import time
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.cofea.cofea_api_mixin import CofeaApiMixin
from dragonfly.ext.offline.offline_api_mixin import OfflineApiMixin


class OfflineFlow(LeafFlow,OfflineApiMixin, CofeaApiMixin):
  pass


# LeafService.I_AM_MASTER_DRIVER = True

INNER_SAMPLE_LENGTH_ATTR = "__inner_sample_length__"

class TestFlowFunc(unittest.TestCase):
  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  __service = LeafService(kess_name="grpc_CommonLeafTest") \
      .register_proto(
      "temp.proto",
      """
        syntax = "proto3";
        package ks.reco;

        message TempMessage {
          double score1 = 1;
          double score2 = 2;
        }""") \
      .register_proto(
      "temp2.proto",
      """
        syntax = "proto3";
        package ks.reco;

        message TempMessageSecond {
          int64 id = 1;
          TempMessage msg = 2;
        }""",
      ["temp.proto"]) \
      .register_proto(
        "test_enum.proto",
        """
          syntax = "proto3";
          package ks.reco;
          message TestEnum {
            enum FrontRequestType {
              UNKNOWN_REQUEST    = 0;
              INFEED_REQUEST     = 1;
              SPLASH_REQUEST     = 2;
            }
          }""") \
      .register_proto(
        "test_sub_message.proto",
        """
            syntax = "proto3";
            package ks.reco;
            message SubTestMessage {
              bool bool_type = 1;
              TestEnum.FrontRequestType enum_type = 2;
              int32 int32_type = 3;
              uint32 uint32_type = 4;
              int64 int64_type = 5;
              uint64 uint64_type = 6;
              float float_type = 7;
              double double_type = 8;
              string string_type = 9;
              repeated bool bool_type_r = 10;
              repeated TestEnum.FrontRequestType enum_type_r = 11;
              repeated int32 int32_type_r = 12;
              repeated uint32 uint32_type_r = 13;
              repeated int64 int64_type_r = 14;
              repeated uint64 uint64_type_r = 15;
              repeated float float_type_r = 16;
              repeated double double_type_r = 17;
              repeated string string_type_r = 18;
            }""",
        ["test_enum.proto"])  \
      .register_proto(
        "test_build_from_protobuf_message.proto",
        """
          syntax = "proto3";
          package ks.reco;
          message TestBuildFromProtobufMessage {
            SubTestMessage sub_message = 1;
            repeated SubTestMessage sub_message_r = 2;
          }""",
        ["test_enum.proto", "test_sub_message.proto"])

  @classmethod
  def __init_service(cls, *flow):
    cls.__service.add_leaf_flows(leaf_flows=list(flow))
    return cls.__service.executor()

  def test_dump_cofea_sample(self):
    dump_flow = OfflineFlow(name="dump_cofea_sample") \
        .copy_user_meta_info(save_result_size_to_attr=INNER_SAMPLE_LENGTH_ATTR) \
        .dump_cofea_sample(
            dump_to_attr="bs",
            dump_common_attrs=["uId", "dId", INNER_SAMPLE_LENGTH_ATTR],
            dump_item_attrs=["pId", "ctr"],
            pk_from="pk",
            timestamp_from="timeMs") \
        .base64(
            mode="encode",
            is_common_attr=True,
            input_attr="bs",
            output_attr="bs_b64")
    retrieve_flow = OfflineFlow(name="read_cofea_sample").base64(
        mode="decode",
        is_common_attr=True,
        input_attr="bs_b64_new",
        output_attr="bs_new") \
        .read_cofea_sample(
        sample_from_attr="bs_new",
        save_primary_key_to="pk",
        id_feature="pId",
        extract_common_features=["uId", "dId"],
        extract_item_features=["pId", "ctr"],)
    leaf = self.__init_service(dump_flow, retrieve_flow)
    uid, did, pid, ctr, time_ms, pk = 1, "did", 3, 4.4, 5, 6
    leaf["uId"] = uid
    leaf["dId"] = did
    leaf["timeMs"] = time_ms
    leaf["pk"] = pk
    item = leaf.add_item(pid)
    item["pId"] = pid
    item["ctr"] = ctr
    leaf.run("dump_cofea_sample")
    bs_b64 = leaf["bs_b64"]
    self.assertTrue(bs_b64)
    leaf.reset()
    leaf["bs_b64_new"] = bs_b64
    leaf.run("read_cofea_sample")
    self.assertEqual(leaf["uId"], uid)
    self.assertEqual(leaf["dId"], did)
    self.assertEqual(leaf["pk"], pk)
    items = leaf.items
    self.assertEqual(len(items), 1)
    item = items[0]
    self.assertEqual(item["pId"], pid)
    self.assertAlmostEqual(item["ctr"], ctr, places=5)

  def test_fetch_colossus_feature(self):
    pass

  def test_cofea_sample_combine_pair(self):
    common_slots = [222]
    non_common_slots = [223]
    labels = ["click", "like", "follow", "forward", "feedback_negative", "left_slide",
          "comment", "wtd_label"]
    input_common_attrs = list(map(str, common_slots)) + ["tab_bits", "user_id", "device_id", "tab_id"] + [
    "user_avg_month_duration_level"]
    input_item_attrs = list(map(str, non_common_slots)) + ["photo_id", "author_id", "audit_b_second_tag"] + labels + [
        "profile_stay_time_v2", "profile_feed_mode_stay_time_v2"]
    dump_flow = OfflineFlow(name="dump_cofea_sample") \
        .copy_user_meta_info(save_result_size_to_attr=INNER_SAMPLE_LENGTH_ATTR) \
        .dump_cofea_sample(
            dump_to_attr="bs",
            dump_common_attrs=["uId", "dId", INNER_SAMPLE_LENGTH_ATTR],
            dump_item_attrs=["pId", "ctr"],
            pk_from="pk",
            timestamp_from="timeMs") \
        .base64(
            mode="encode",
            is_common_attr=True,
            input_attr="bs",
            output_attr="bs_b64")

    test_cofea_sample_combine_pair_flow = OfflineFlow(name="test_cofea_sample_combine_pair_flow") \
      .base64(
        mode="decode",
        is_common_attr=True,
        input_attr="bs_b64_new",
        output_attr="bs_new") \
        .read_cofea_sample(
        sample_from_attr="bs_new",
        save_primary_key_to="pk",
        id_feature="pId",
        extract_common_features=input_common_attrs,
        extract_item_features=input_item_attrs,
      #   ).fetch_message(
      #   group_id="mio_slide_reco_mc_one_xiabin_pcr_no_pic_7322835_train_1708503939000",
      #   kafka_topic="universe_feature_mc_exp",
      #   output_attr="batched_sample",
      #   save_begin_time_ms_to_common_attr="begin_time") \
      # .read_cofea_sample(
      #   sample_from_attr="batched_sample",
      #   id_feature="photo_id",
      #   user_id_feature="user_id",
      #   device_id_feature="device_id",
      #   extract_common_features=input_common_attrs,
      #   extract_item_features=input_item_attrs,
      ).log_debug_info(
        common_attrs = input_common_attrs,
        print_all_item_attrs=True,
        for_debug_request_only=False
      ).cofea_sample_combine_pair(
        slot_id_list=non_common_slots,
        label_attr_list=labels,
        slot_offset_list=[0,10000],
        filter_same_item_pair=True,
        save_combine_item_flag_to="is_combine_result"
      ).log_debug_info(
        common_attrs = input_common_attrs,
        print_all_item_attrs=True,
        for_debug_request_only=False
      )

    leaf = self.__init_service(dump_flow, test_cofea_sample_combine_pair_flow)
    leaf.run("dump_cofea_sample")
    leaf.run("test_cofea_sample_combine_pair_flow")

if __name__ == '__main__':
  suite = unittest.TestSuite()
  suite.addTests(unittest.TestLoader().loadTestsFromName('cofea_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
