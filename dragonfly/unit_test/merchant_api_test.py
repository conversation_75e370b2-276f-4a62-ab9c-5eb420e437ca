#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="merchant" kbuild build dragon/src dragon/server
             #ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true DRAGON_EXT="merchant" kbuild build dragon/server/
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6

    #GCC_10 
    #ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true DRAGON_EXT="merchant" kbuild build --base_tag=HEAD_GCC10_LLD dragon/server/
    #export LD_PRELOAD=./build_tools/gcc-10.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/merchant_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.merchant_api_test.TestFlowFunc.xxxx

import unittest
import os
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.merchant.merchant_api_mixin import MerchantApiMixin


class MerchantFlow(LeafFlow, MerchantApiMixin):
    pass

class TestFlowFunc(unittest.TestCase):
    __service = LeafService(kess_name="grpc_CommonLeafTest")

    def setUp(self) -> None:
        self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
        LeafService.ENABLE_ATTR_CHECK = False

    def tearDown(self) -> None:
        LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

    @classmethod
    def __init_service(cls, flow):
        cls.__service.add_leaf_flows(leaf_flows=[flow])
        return cls.__service.executor()

    #### 测试 ranking算子
    # def test_merchant_consistent_ranking_ranker(self):
    #     flow = MerchantFlow(name = "test_merchant_consistent_ranking_ranker")\
    #             .enrich_attr_by_light_function(
    #                 import_item_attr = ["b2c_item_id_list"],
    #                 export_common_attr = ["b2c_users_seq", "b2c_user_items_seq"],
    #                 function_name = "B2CRetrieveReverse",
    #                 class_name = "MerchantLightFunctionSet"
    #             )\
    #             .log_debug_info(
    #                 for_debug_request_only = False,
    #                 common_attrs = ["b2c_users_seq", "b2c_user_items_seq"],
    #                 item_num_limit = 0
    #             )
        
    #     leaf = self.__init_service(flow)
    #     for i in range(3):
    #         item = leaf.add_item(i)
    #         if 0 == i:
    #             item["b2c_item_id_list"] = [10, 20, 30]
    #             item["b2c_item_distance_list"] = [0.1, 0.3, 0.6]
    #         elif 1 == i:
    #             item["b2c_item_id_list"] = [10, 15, 20]
    #             item["b2c_item_distance_list"] = [0.2, 0.5, 0.15]
    #         else:
    #             item["b2c_item_id_list"] = [100, 150, 200]
    #             item["b2c_item_distance_list"] = [0.8, 0.4, 0.7]

    #     leaf.run("test_merchant_consistent_ranking_ranker")

    
    # def test_merchant_merge_ranking_ranker(self):
    #     flow = MerchantFlow(name = "test_merchant_merge_ranking_ranker")\
    #             .enrich_attr_by_light_function(
    #                 import_common_attr = ["b2c_user_items_record", "b2c_user_items_new"],
    #                 export_common_attr = ["b2c_merge_user_items_seq"],
    #                 function_name = "B2CDataMerge",
    #                 class_name = "MerchantLightFunctionSet"
    #             )
        
    #     leaf = self.__init_service(flow)
    #     leaf["b2c_user_items_record"] = "3,0.15;2,0.1"
    #     leaf["b2c_user_items_new"] = "1,0.1;2,0.3"

    #     leaf.run("test_merchant_merge_ranking_ranker")



    # def test_merchant_consistent_ranking_ranger(self):
    #     flow = MerchantFlow(name = "test_merchant_consistent_ranking_ranger")\
    #             .retrieve_by_common_attr(attr = "item_list", reason = 9998)\
    #             .enrich_attr_by_lua(
    #                 export_item_attr = ["traffic_worth_score"],
    #                 function_for_item = "set_score_value",
    #                 lua_script = """
    #                     function set_score_value(seq)
    #                         local score_val = 10.2 + seq
    #                         return score_val
    #                     end
    #                 """
    #             )\
    #             .merchant_consistent_ranking_ranger(
    #                 limit_size = "limit_size",
    #                 desc = False
    #             )\
    #             .log_debug_info(
    #                 for_debug_request_only = False,
    #                 item_attrs = ["traffic_worth_score"]
    #             )

    #     leaf = self.__init_service(flow)
    #     leaf["item_list"] = [1, 2, 3, 4, 5, 6 ,7, 8]
    #     leaf.run("test_merchant_consistent_ranking_ranger")


    # def test_merchant_mix_ad_trace_log_enricher(self):
    #     flow = MerchantFlow(name = "test_merchant_mix_ad_trace_log_enricher")\
    #             .merchant_mix_ad_trace_log_enricher(
    #                 ad_result_attr = 'ad_result',
    #                 filter_reason_attr = 'ad_filter_reason',
    #                 pos_attr = 'ad_pos',
    #                 mix_score_attr = 'ad_mix_score',
    #                 ad_request_attr = 'ad_request',
    #                 output_attr = 'ad_mix_rank_trace_log'
    #             )
    #     ad_result = 'CkQKIAiyx8sJEJCwiaAIGM7Y56YQIJOEu6u7ATgKyAEA2AEAiQS4HoXrUbi+P5EEexSuR+F6hD+ZBPyp8dJNYlA/+AToBzITCAAgACgAMLAHOApAAEgBUABgAHEAAAAAAAAAAA=='
    #     ad_request = 'EgQIsagDgAKtr+IEkAmsr+IEqAmVmu86'

    #     leaf = self.__init_service(flow)
    #     for i in range(2):
    #         item = leaf.add_item(i)
    #         item["ad_filter_reason"] = 1
    #         item["ad_pos"] = 2
    #         item["ad_mix_score"] = 1.23
    #         item["ad_result"] = ad_result

    #     leaf["ad_request"] = ad_request
    #     leaf.run("test_merchant_mix_ad_trace_log_enricher")
    #     self.assertNotEqual(leaf["ad_mix_rank_trace_log"], None)

    if __name__ == '__main__':
        suite = unittest.TestSuite()

        suite.addTests(unittest.TestLoader().loadTestsFromName('merchant_api_test.TestFlowFunc'))

        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        if result.failures or result.errors:
            exit(1)
        else:
            exit(0)