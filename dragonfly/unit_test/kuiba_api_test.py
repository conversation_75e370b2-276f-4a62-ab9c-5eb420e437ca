#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="kuiba" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/kuiba_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.kuiba_api_test.TestFlowFunc.xxxx

import unittest
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.kuiba.kuiba_api_mixin import KuibaApiMixin

# LeafService.I_AM_MASTER_DRIVER = True

class KuibaFlow(LeafFlow, KuibaApiMixin):
  pass

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_extract_kuiba_parameter(self):
    flow = KuibaFlow(name="test_extract_kuiba_parameter") \
      .extract_kuiba_parameter(
      config={
        "uid1": {"attrs": [{"key_type": 3, "attr": ["_USER_ID_"], "converter": "id"}]},
      },
      is_common_attr=True,
      slots_output='user_slots',
      parameters_output='user_parameters',
      fused_slot_sign_remap=True,
      sign_prefix_bit_num_input=10,
      sign_prefix_bit_num_output=12,
      item_map_list = [[
        "3",
        630,
        630,
        50000001
      ]])
    leaf = self.__init_service(flow)
    leaf.user_id = 1
    leaf.run("test_extract_kuiba_parameter")
    self.assertEqual([2837267765243412481], leaf['user_parameters'])

  def test_build_sample_attr(self):
    flow = KuibaFlow(name="test_build_sample_attr") \
      .build_sample_attr(
        is_common_attr=True,
        mappings=[
          { "from_attr": "_USER_ID_", "rename": "uId", "to_attr": "uid_sample_attr" },
          { "from_attr": "_DEVICE_ID_", "rename": "dId", "to_attr": "did_sample_attr" }
        ]
      ) \
      .build_protobuf(
        inputs=[
          { "common_attr": "uid_sample_attr", "path": "attr", "append": True },
          { "common_attr": "did_sample_attr", "path": "attr", "append": True },
        ],
        output_common_attr="kuiba_user_attr",
        class_name="kuiba::PredictItem",
      ) \
      .extract_kuiba_sample_attr(
        output_attrs = ["uId", "dId"],
        predict_item = "kuiba_user_attr",
        is_common_attr = True)

    leaf = self.__init_service(flow)
    leaf.user_id = 10086
    leaf.device_id = 'abcdef'
    leaf.run("test_build_sample_attr")
    self.assertEqual(leaf.user_id, leaf["uId"])
    self.assertEqual(leaf.device_id, leaf["dId"])

  def test_build_raw_sample_package(self):
    flow = KuibaFlow(name="test_build_raw_sample_package") \
      .build_raw_sample_package(
        common_attrs = ["uuid"],
        save_result_to = "raw_sample_package_str") \
      .parse_protobuf_from_string(
        input_attr="raw_sample_package_str",
        output_attr="raw_sample_package",
        class_name="kuiba::RawSamplePackage") \
      .retrieve_from_raw_sample_package(
        skip_sample_without_labels=False,
        from_extra_var="raw_sample_package",
        labels=[],
      ) \

    leaf = self.__init_service(flow)
    leaf["uuid"] = 10086
    leaf["common_str"] = ["uuid"]
    leaf.run("test_build_raw_sample_package")
    self.assertEqual(10086, leaf["uuid"])

if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('kuiba_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
