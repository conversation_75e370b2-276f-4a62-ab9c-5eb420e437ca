#!/usr/bin/env python3
# coding=utf-8

# 注意: Python 单测需在 **云开发机** 上执行（因为 rpc mock 接口依赖 kess 环境）
# 配置的步骤说明（以下命令均在 dragon 的 kbuild workspace 根目录下执行）:
# 1. 二进制编译: ENABLE_COMMON_LEAF_PYTHON_WRAPPER=true COMMON_RECO_LEAF_EXTRA_PROCESSORS="mio" kbuild build dragon/src dragon/server
# 2. 加载动态库: export LD_PRELOAD=./build_tools/gcc-8.3.0/lib64/libstdc++.so.6
# 3. 设置环境变量: export GRPC_CPU_CORES_USE_CONF=true
# 4. 运行单测文件: python3 dragon/dragonfly/unit_test/mio_api_test.py
# 5. [可选] 单独跑某一个 testcase: python3 -m unittest dragon.dragonfly.unit_test.mio_api_test.TestFlowFunc.xxxx

import unittest
import numpy as np
from dragonfly.common_leaf_dsl import LeafService, LeafFlow
from dragonfly.ext.mio.mio_api_mixin import MioApiMixin

# LeafService.I_AM_MASTER_DRIVER = True

class MyFlow(LeafFlow, MioApiMixin):
  pass

class TestFlowFunc(unittest.TestCase):
  __service = LeafService(kess_name="grpc_CommonLeafTest")

  def setUp(self) -> None:
    self.enable_attr_check_backup = LeafService.ENABLE_ATTR_CHECK
    LeafService.ENABLE_ATTR_CHECK = False

  def tearDown(self) -> None:
    LeafService.ENABLE_ATTR_CHECK = self.enable_attr_check_backup

  @classmethod
  def __init_service(cls, flow):
    cls.__service.add_leaf_flows(leaf_flows=[flow])
    return cls.__service.executor()

  def test_deep_hash_encode(self):
    flow = MyFlow(name="ksib_live_deep_hash_encode_test") \
      .mio_ksib_live_deep_hash_encode(
        input_common_list_attr_name='play_list',
        input_item_attr_name='live_id',
        output_item_attr_name='deephash',
        embedding_size=128
      )

    leaf = self.__init_service(flow)
    leaf['play_list'] = [1,2,3]
    for i in range(5):
      item = leaf.add_item(i)
      item['live_id'] = i
    leaf.run("ksib_live_deep_hash_encode_test")
    for item in leaf.items:
      self.assertEqual(len(item['deephash']), 128)

  def test_ksib_rewrite_slot_in_sign(self):
    flow = MyFlow(name="ksib_rewrite_slot_in_sign_test") \
      .ksib_rewrite_slot_in_sign(
        sign_format="kuiba",
        slot_as_attr_name=True,
        slot_mapping={
          "119": "101:1",
        })

    leaf = self.__init_service(flow)
    leaf['119'] = [2143713423423735895]
    leaf.run("ksib_rewrite_slot_in_sign_test")
    self.assertEqual(leaf['101'][0], 1819454250253060183)
  
  def test_decompress_embedding_from_string(self):
    flow = MyFlow(name="decompress_embedding_from_string_test") \
        .decompress_embedding_from_string(
        decompress_configs=[
            dict(
                input_attr="common_emb_str",
                output_attr="common_emb",
                is_common=True,
                compress_type="mio_int16"
            ),
            dict(
                input_attr="item_emb_str",
                output_attr="item_emb",
                is_common=False,
                compress_type="mio_int16"
            )])
    leaf = self.__init_service(flow)
    common_emb = np.array([0.1, 0.2, 0.3], dtype=np.float32)
    leaf['common_emb_str'] = (common_emb * 3000).astype(np.int16).tobytes()
    for i in range(10):
      item = leaf.add_item(i)
      item["item_emb_str"] = ((np.ones([3]) * i) * 3000).astype(np.int16).tobytes()
    leaf.run("decompress_embedding_from_string_test")
    self.assertTrue(np.allclose(leaf['common_emb'], common_emb))
    for i, item in enumerate(leaf.items):
      self.assertTrue(np.allclose(item['item_emb'], np.ones([3]) * i))

  def test_extract_with_ksib_sign_feature(self):
    flow = MyFlow(name="extract_with_ksib_sign_feature_test") \
        .build_protobuf(
          class_name="ksib::reco::ReaderInfo",
          inputs=[
              dict(common_attr="bucket", path="bucket")
          ],
          output_common_attr="reader_info"
        )\
        .build_protobuf(
          class_name="ksib::reco::PhotoInfo",
          inputs=[
              dict(common_attr="bucket", path="bucket")
          ],
          output_common_attr="photo_info"
        )\
        .extract_with_ksib_sign_feature(
          feature_list=[
            """ExtractKsibSignUnitTest,ExtractKsibFromSampleAttrWithInt,888,1,2,48,0,0,,0,{"attr_name":"test_attr","attr_from":"common_attrs","sign_with_bucket":false}"""
          ],
          reader_info_attr="reader_info",
          photo_info_attr="photo_info",
          slot_as_attr_name=True,
          user_attrs=["test_attr"],
          save_all_proto_attr=False
        )

    leaf = self.__init_service(flow)
    leaf['bucket'] = 11
    leaf['test_attr'] = [5555]
    leaf.run("extract_with_ksib_sign_feature_test")
    print(leaf['101'])
    self.assertEqual(leaf['888'][0], ((888 - 16) << 48) | 5555)

  def test_fetch_mio_embedding_opt(self):
    flow = MyFlow(name="fetch_mio_embedding_opt_test") \
        .fetch_mio_embedding_opt(
          name="fetch_mio_embedding",
          kess_service="a",
          shards=8,
          protocol=0,
          slots_inputs=['gsu_item_slots'],
          parameters_inputs=['gsu_item_signs'],
          common_slots_inputs=['lt_slots'],
          common_parameters_inputs=['lt_signs'],
          slots_config=[],
          timeout_ms=100,
          direct_write=True,
          max_signs_per_request=1000,
          client_side_shard=True,
          save_result_as_tensor_output=True,
        )

    leaf = self.__init_service(flow)
    leaf['bucket'] = 11
    leaf['test_attr'] = [5555]
    # leaf.run("fetch_mio_embedding_opt_test")
    print(leaf['101'])

  def test_extract_with_ks_sign_feature(self):
    # flow = MyFlow(name="extract_with_ks_sign_feature_test") \
    #     .extract_with_ks_sign_feature(
    #       feature_list = [
    #        "ExtractSignSlideUserId",
    #        "ExtractSignUserDeviceId",
    #        "ExtractSignUserId",
    #        "ExtractSignUserContext",
    #        "ExtractSignUserLike",
    #        "ExtractEmbeddingUserForward",
    #        "ExtractSignUserApplistTotal",
    #        "ExtractSignUserAllGender",
    #        "ExtractSignUserIp",
    #        "ExtractSignUserMod",
    #        "ExtractSignUserProvCity",
    #        "ExtractSignUserYear",
    #       ],
    #       common_slots_output = "consume_user_feature_slots",
    #       common_parameters_output = "consume_user_feature_signs",
    #       #user_info_attr = "user_info_ptr",
    #     )
    # leaf = self.__init_service(flow)
    # leaf.run("extract_with_sign_feature_test")
    pass
    
  def test_fetch_mio_embedding_lite(self):
    pass

  def test_get_common_attr_from_redis_by_lrange(self):
    pass

  def test_mio_unique(self):
    flow = MyFlow(name="mio_unique_test") \
      .mio_unique(
          output_attr="unique_int",
          input_common_attrs=["int_list1", "int_value1", "int_list2"],
          output_common_index_attrs=["int_list1_index", "int_value1_index", "int_list2_index"],
          input_item_attrs=["int_item_list1", "int_item_value1", "int_item_list2"],
          output_item_index_attrs=["int_item_list1_index", "int_item_value1_index", "int_item_list2_index"])

    leaf = self.__init_service(flow)
    leaf['int_list1'] = [1, 2, 3]
    leaf['int_value1'] = 2
    leaf['int_list2'] = [3, 4, 5]

    item1 = leaf.add_item(1)
    item1['int_item_list1'] = [2, 4, 8]
    item1['int_item_value1'] = 3
    item1['int_item_list2'] = [2, 3, 4]

    item2 = leaf.add_item(2)
    item2['int_item_list1'] = [1, 5, 8]
    item2['int_item_value1'] = 2
    item2['int_item_list2'] = [2, 2, 2]

    leaf.run("mio_unique_test")
    self.assertEqual(leaf['unique_int'], [1, 2, 3, 4, 5, 8])
    self.assertEqual(leaf['int_list1_index'], [0, 1, 2])
    self.assertEqual(leaf['int_value1_index'], 1)
    self.assertEqual(leaf['int_list2_index'], [2, 3, 4])
    self.assertEqual(item1['int_item_list1_index'], [1, 3, 5])
    self.assertEqual(item1['int_item_value1_index'], 2)
    self.assertEqual(item1['int_item_list2_index'], [1, 2, 3])
    self.assertEqual(item2['int_item_list1_index'], [0, 4, 5])
    self.assertEqual(item2['int_item_value1_index'], 1)
    self.assertEqual(item2['int_item_list2_index'], [1, 1, 1])


if __name__ == '__main__':
  suite = unittest.TestSuite()

  suite.addTests(unittest.TestLoader().loadTestsFromName('mio_api_test.TestFlowFunc'))

  runner = unittest.TextTestRunner(verbosity=2)
  result = runner.run(suite)
  if result.failures or result.errors:
    exit(1)
  else:
    exit(0)
