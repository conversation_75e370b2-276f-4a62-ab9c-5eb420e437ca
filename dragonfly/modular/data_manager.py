#!/usr/bin/env python3
# coding=utf-8
"""
- filename: data_manager.py
- description: data manager for common_leaf functional programming
- author: <PERSON><PERSON><PERSON>@kuaishou.com
- date: 2023-02-18 14:00:00
"""
import sys
from collections import defaultdict
from typing import Dict, List
from copy import deepcopy

from ..ext.common.common_leaf_enricher import CommonRecoAbtestCommonAttrEnricher, \
  CommonRecoKconfCommonAttrEnricher, CommonRecoItemAttrDefaultValueEnricher, \
  CommonRecoDistributedIndexAttrKVItemAttrEnricher, CommonRecoDistributedIndexFlatKvItemAttrEnricher, \
  CommonRecoLocalIndexItemAttrEnricher
from ..common_leaf_dsl import LeafFlow, current_flow

class Data:
  """
  存储单个上下文内所需的外部参数的数据容器，并自动为 flow 添加获取外部参数的 processor。
  
  初始化参数
  ------
  `flow`: [str] LeafFlow 对象，每个 Data 实例只能绑定一个 flow。

  `is_origin`: [dict] 是否为首个进入 DataManager 的数据容器
  ```
  """
  def __init__(self, flow: LeafFlow, is_origin: bool = False):
    # ab 参数列表 格式 {"biz_name": [ab_params]}
    self.ab_params_map = defaultdict(list)
    # kconf 参数列表
    self.kconf_params = []
    # 常量参数列表
    self.const_params = []
    # 索引字段列表
    self.index_attrs = []
    # 上下文内的 LeafFlow
    self.flow: LeafFlow = flow
    # flow 当前的 namespace
    self.namespace = deepcopy(flow.current_namespace)
    # 是否为初始 data
    self.is_origin = is_origin
    
  def __enter__(self):
    """
    进入上下文，记录 flow 在 当前 module 初始阶段的最后一个 processor 的位置，并存在成员变量 pos 中。

    """
    self.pos = len(self.flow._processors)
    return self

  def __exit__(self, *args):
    """
    结束上下文，将上下文中使用的 ab 参数, kconf 参数，常量参数，索引字段，在成员变量 pos 所对应的位置获取。

    """
    position = self.pos

    # 由于上下文退出的时间可能在 flow 结尾，暂时将 flow 的 namespace 切换成进入该上下文时的 namespace。
    temp_namespace = deepcopy(self.flow.current_namespace)
    self.namespace_(self.namespace)

    if self.ab_params_map != {}:
      for biz_name, ab_params in self.ab_params_map.items():
        ab_args = {"ab_params": ab_params}
        if biz_name != "no_biz":
          ab_args["biz_name"] = biz_name
        if DataManager.AB_SUFFIX != "":
          ab_args["prioritized_suffix"] = "{{" + DataManager.AB_SUFFIX + "}}"
        if DataManager.BIZ_NAME != "":
          ab_args["biz_name"] = "{{" + DataManager.BIZ_NAME + "}}"
        if DataManager.USER_ID_ATTR:
          ab_args["user_id"] = "{{" + DataManager.USER_ID_ATTR + "}}"
        self.flow._add_processor(CommonRecoAbtestCommonAttrEnricher(ab_args), position=position)
    if self.kconf_params != []:
      kconf_args = {"kconf_configs": self.kconf_params}
      self.flow._add_processor(CommonRecoKconfCommonAttrEnricher(kconf_args), position=position)
    if self.const_params != []:
      const_args = {"common_attrs": self.const_params}
      self.flow._add_processor(CommonRecoItemAttrDefaultValueEnricher(const_args), position=position)
    if self.index_attrs != []:
      index_args = {"attrs": self.index_attrs}
      if DataManager.PHOTO_STORE_KCONF_KEY != "":
        index_args["photo_store_kconf_key"] = DataManager.PHOTO_STORE_KCONF_KEY
      if "--distributed-index" in sys.argv:
        self.flow._add_processor(CommonRecoDistributedIndexAttrKVItemAttrEnricher(index_args), position=position)
      elif "--distributed-flat-index" in sys.argv:
        self.flow._add_processor(CommonRecoDistributedIndexFlatKvItemAttrEnricher(index_args), position=position)
      else:
        self.flow._add_processor(CommonRecoLocalIndexItemAttrEnricher(index_args), position=position)  

    # 恢复 namespace
    self.namespace_(temp_namespace)  

  def ab_param(self, param_name, default_value, attr_name="", biz_name=""):
    """
    返回 ab 参数的 attr 名称，并将参数添加在成员变量 ab_params 中

    参数说明
    ------
    `param_name`: [string|dict] ab 参数的名称

    `default_value`: [int|double|string|bool] ab 参数获取失败时取用的默认值
    
    `attr_name`: [string] 可缺省，要写入的 CommonAttr 名称，若缺省或为空将直接取 `param_name` 的值

    `biz_name`: [string] 可缺省，业务 biz 信息，若缺省使用 flag 中的默认 biz
    """
    config = {"param_name": param_name, "default_value": default_value}
    config["attr_name"] = attr_name if attr_name else param_name
    if biz_name == "":
      self.ab_params_map["no_biz"].append(config)
    else:
      self.ab_params_map[biz_name].append(config)
    output_attr_name = attr_name if attr_name else param_name
    return output_attr_name

  def kconf_param(self, kconf_key, default_value, attr_name, json_path=""):
    """
    返回 kconf 参数的 attr 名称，并将参数添加在成员变量 kconf_params 中

    参数说明
    ------
    `kconf_key`: [string] [动态参数] kconf 中的 key

    `default_value`: [int|double|string|bool|list] kconf 配置获取失败时取用的默认值, 该值的类型需与 kconf_key 实际配置的值类型一致！
    
    `attr_name`: [string] 将 kconf 值写入指定的 CommonAttr

    `json_path`: [动态参数] 可缺省，若 kconf_key 对应的值为 json 类型，可通过该项配置获取指定 json_path 下的值
    """
    config = {"kconf_key": kconf_key, "export_common_attr": attr_name, "default_value": default_value}
    if json_path != "":
      config["json_path"] = json_path
    self.kconf_params.append(config)
    return attr_name
  
  def const_param(self, name, data_type, value):
    """
    返回常量参数的 attr 名称，并将参数添加在成员变量 const_params 中

    参数说明
    ------
    `name`: [string] 待设置的 CommonAttr 名称

    `data_type`: [string] 待设置的 CommonAttr 值类型，可选值：int, double, string, int_list, double_list, string_list
    
    `value`: [int|double|string|int_list|double_list|string_list] 待设置的 CommonAttr 值，需要和 type 保持类型一致
    """
    config = {"name": name, "type": data_type, "value": value}
    self.const_params.append(config)
    return name

  def index_attr(self, attr):
    """
    返回索引字段的 attr 名称，并将参数添加在成员变量 index_attrs 中

    参数说明
    ------
    `attr`: [string] 待获取的字段的名称
    """
    self.index_attrs.append(attr)
    return attr
  
  def namespace_(self, ns_list: list = []):
    """
    为 flow 的 namespace 赋值

    参数说明
    ------
    `ns_list`: [list] 表达可嵌套命名空间的一组字符串列表
    """
    if ns_list:
      for idx, ns in enumerate(ns_list):
        if idx == 0:
          self.flow.namespace_(ns)
        else:
          self.flow.namespace_(ns, nest = True)
  
class DataManager:
  """
  通过 with 为 LeafFLow 创造一个数据处理的上下文环境，自动获取当前模块内 flow 所需要的 ab 参数，kconf 参数等。

  使用示例
  ------

  ```
  from dragonfly.modular.data_manager import data_manager, ab_param as ab
  from dragonfly.common_leaf_dsl import current_flow as flow

  # 通过 with 语句创建一个 data_manager 的上下文环境
  with data_manager:
    flow() \
      .delegate_enrich(
          for_predict=True,
          kess_service="{{" + ab("embedding_server_v1", "embedding-server-name") + "}}",
          shard_num=1,
          timeout_ms=50,
          recv_common_attrs=["user_embedding"]
        ) 
  ```
  """
  
  # ab 参数的后缀配置
  AB_SUFFIX = ""

  # 默认 biz name
  BIZ_NAME = ""

  # 存放 user_id 的 common attr，用于指定获取 ab 参数时使用的 user_id，缺省时，使用当前请求的 user_id 
  USER_ID_ATTR = ""

  # 分布式索引服务的 kconf 配置
  PHOTO_STORE_KCONF_KEY = ""

  # 每个 flow 对应一组 data，用于存储各 flow 的上下文状态。
  FLOW_TO_DATA_STACK_MAP: Dict[str, List[Data]] = defaultdict(list)

  @classmethod
  def get_or_create_data(cls, is_enter=False):
    flow = current_flow()
    is_origin_data = DataManager.FLOW_TO_DATA_STACK_MAP == {}
    data_stack = DataManager.FLOW_TO_DATA_STACK_MAP[flow.name]
    if data_stack == [] or is_enter:
      data_stack.append(Data(flow, is_origin_data))
      if not is_enter:
        data_stack[-1].__enter__()

    return data_stack[-1]

  @staticmethod
  def set_ab_suffix(ab_suffix):
      DataManager.AB_SUFFIX = ab_suffix

  @staticmethod
  def set_biz_name(biz_name):
      DataManager.BIZ_NAME = biz_name

  @staticmethod
  def set_user_id_attr(user_id_attr):
      DataManager.USER_ID_ATTR = user_id_attr

  @staticmethod
  def set_photo_store_kconf_key(kconf_key):
      DataManager.PHOTO_STORE_KCONF_KEY = kconf_key

  @classmethod
  def ab_param(cls, *args, **kwargs):
    data: Data = cls.get_or_create_data()
    return data.ab_param(*args, **kwargs)
  
  @classmethod
  def kconf_param(cls, *args, **kwargs):
    data: Data = cls.get_or_create_data()
    return data.kconf_param(*args, **kwargs)

  @classmethod
  def const_param(cls, *args, **kwargs):
    data: Data = cls.get_or_create_data()
    return data.const_param(*args, **kwargs)

  @classmethod
  def index_attr(cls, *args, **kwargs):
    data: Data = cls.get_or_create_data()
    return data.index_attr(*args, **kwargs)

  def __enter__(self):
    data: Data = DataManager.get_or_create_data(True)
    data.__enter__()

  def __exit__(self, *args):
    flow = current_flow()
    data_stack = DataManager.FLOW_TO_DATA_STACK_MAP[flow.name]
    data = data_stack.pop()
    data.__exit__()
    
    if data.is_origin:
      self.exit_all()

  def exit_all(self):
    for data_stack in DataManager.FLOW_TO_DATA_STACK_MAP.values():
      while data_stack:
        data_stack.pop().__exit__()

data_manager = DataManager()
ab_param = DataManager.ab_param
kconf_param = DataManager.kconf_param
const_param = DataManager.const_param
index_attr = DataManager.index_attr