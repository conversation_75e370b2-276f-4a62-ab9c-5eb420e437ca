#!/usr/bin/env python3
# coding=utf-8
"""
- filename: module.py
- description: module decorator for common_leaf functional programming
- author: <PERSON><PERSON><PERSON>@kuaishou.com
- date: 2023-02-18 14:00:00
"""
import sys
import functools
from collections import defaultdict
from typing import Callable, TypeVar
from typing_extensions import ParamSpec
from ..common_leaf_util import dump_to_json
from ..common_leaf_dsl import current_flow

P = ParamSpec('P')
T = TypeVar('T')

class module():
  # 当前业务的全量 module 信息，用于写入文件记录。
  MODULE_INFO = defaultdict(dict)
  """
  模块化装饰器基类，负责将函数体整合为 LeafFlow 中的独立模块。
  Module 功能：
    1. 为函数体内的 processors 增加 namespace，通过 processor 命名来区分不同模块，并在函数体结束后去除当前 namespace。
    2. 统计函数体的耗时，

  初始化参数
  ------
  `function`: [function] 被装饰的函数。

  `module_name`: [str] 可缺省，函数的 module 名称，如果缺省直接使用函数名作为 module name。
  """

  @classmethod
  def write_to_file(cls, filename):
    """
    将 module 信息写入文件

    可配参数：

    `filename`: [string] 写入的文件名。

    """
    content = dump_to_json(module.MODULE_INFO)
    with open(filename, 'w') as f:
      f.write(content)

  def __init__(self, module_name = "", owner_list = [], enable_module_time_cost = None):
    self.module_name = module_name
    self.owner_list = owner_list or []
    if enable_module_time_cost is None:
      self.enable_module_time_cost = "--enable_module_time_cost" in sys.argv
    else:
      self.enable_module_time_cost = enable_module_time_cost

  def __enter__(self, *args, **kwargs):
    """
    进入上下文时对 module 做预处理

    可配参数：

    `name`: [string] module 的名称，可缺省，直接用 function name 为 module 命名。

    """
    self.module_name = kwargs.get("name", None) or kwargs.get("module_name", None) or self.module_name
    self.owner_list = kwargs.get("owner_list", []) or self.owner_list
    self._module_pre_process()

  def __exit__(self, *args):
    """
    退出上下文时对 module 做后处理

    """
    self._module_post_process()

  def __call__(self, function: Callable[P, T]) -> Callable[P, T]:
    """
    module 作为装饰器时被调用，执行流程为 模块预处理 --> 函数 --> 模块后处理。

    可配参数：

    `function`: [function] 被封装的函数。

    `name`: [string] module 的名称，可缺省，直接用 function name 为 module 命名。
    """
    @functools.wraps(function)
    def wrapper(*args, **kwargs):
      self.module_name = kwargs.get("name", None) or kwargs.get("module_name", None) or self.module_name or function.__name__
      self.owner_list = kwargs.get("owner_list", []) or self.owner_list
      flow = self._module_pre_process()

      if function.__name__ == function.__qualname__:
        flow.do(function(*args, **kwargs))
      else:
        if "<locals>" in function.__qualname__:
          raise Exception(f"❌ module {function.__name__} 不可以被定义在嵌套函数中")

        flow = function(*args, **kwargs)
      self._module_post_process()
      return flow
    return wrapper

  def _module_pre_process(self):
    """
    模块预处理，先为模块指定 namespace，再记录当前时间到 common attr，用于计算模块耗时

    """
    flow = current_flow()
    flow.namespace_(self.module_name, nest = True)

    full_module_name = "__".join(flow.current_namespace)
    self.add_module_info()
    if self.enable_module_time_cost:
      flow.copy_user_meta_info(
        name = "calc_time_cost_s",
        save_elapsed_time_to_attr = f"{full_module_name}TimeCostStart"
      )
    return flow

  def _module_post_process(self):
    """
    模块后处理，计算模块总耗时并打点，然后结束当前模块的 namespace

    """
    flow = current_flow()

    full_module_name = "__".join(flow.current_namespace)
    if self.enable_module_time_cost:
      flow.copy_user_meta_info(
          name = "calc_time_cost_e",
          save_elapsed_time_to_attr = f"{full_module_name}TimeCostEnd"
        ) \
        .gen_common_attr_by_lua(
          name = "calc_time_cost",
          attr_map = {
            f"{full_module_name}TimeCost": f"{full_module_name}TimeCostEnd - {full_module_name}TimeCostStart"
          }
        ) \
        .perflog_attr_value(
          name = "perf_time_cost",
          check_point = f"module:{full_module_name}",
          perf_base = 1,
          common_attrs = [f"{full_module_name}TimeCost"],
        )
    flow.namespace_()
    return flow

  def add_module_info(self):
    """
    为 MODULE_INFO 增加当前 module 信息。

    """
    flow = current_flow()
    flow_namespace = flow.current_namespace
    current_module = "::".join(flow_namespace)
    if current_module in module.MODULE_INFO:
      raise Exception(f"❌ module name {current_module} 已经被使用，请使用不同的 module name")
    module.MODULE_INFO[current_module] = {
      "module_name": self.module_name,
      "processor_prefix": current_module,
      "owner_list": self.owner_list
    }

    parent_module = "::".join(flow_namespace[:-1])
    if parent_module:
      children = module.MODULE_INFO[parent_module].get("children", [])
      children.append(current_module)
      module.MODULE_INFO[parent_module]["children"] = children
