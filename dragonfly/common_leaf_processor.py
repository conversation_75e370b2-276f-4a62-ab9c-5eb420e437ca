#!/usr/bin/env python3
# coding=utf-8
"""
filename: common_leaf_processor.py
description: common_leaf dynamic_json_config DSL intelligent builder, processor module
author: <EMAIL>
date: 2020-01-09 10:45:00
"""

import os
from abc import ABC, abstractmethod
from typing import Optional
from .common_leaf_util import ArgumentError, strict_types, check_arg, gen_attr_name_with_common_attr_channel, \
    gen_attr_name_with_item_attr_channel, gen_attr_name_with_browse_set_channel, \
    gen_dict_hash_hexstr, extract_common_attrs, is_dynamic_expr, has_intersection, \
    extract_attrs_from_expr, extract_table_and_attr_name, strip_channel_name, try_add_table_name


class EmbeddedFlowGenerator:
  def __init__(self, func, *, instance_wise: bool = False):
    self.func = func
    self.instance_wise = instance_wise

  def check_instance_wise(self, processor):
    check_arg(processor.name != "", "使用 instance_wise 嵌套 flow 必须手动指定算子名称")

  def flow_ns(self, processor):
    if self.instance_wise:
      self.check_instance_wise(processor)
      return processor.raw_name

    return processor.__class__.__name__

  def generate(self, processor, flow):
    if self.instance_wise:
      self.check_instance_wise(processor)
      return self.func(processor, flow)

    return self.func(flow)


def embedded_flow(*args, **kwargs):
  if len(args) == 1 and callable(args[0]):
    # 直接装饰
    return EmbeddedFlowGenerator(args[0])

  # 返回一个装饰器
  def decorator(func):
    return EmbeddedFlowGenerator(func, *args, **kwargs)

  return decorator


class LeafProcessor(ABC):
  """ 所有 Processor 的基类 """

  # 保留配置项, 给 LeafProcessor 子类使用, 无特殊情况用户无需修改
  _SAMPLE_LIST_COMMON_ATTR_KEY = "_sample_list_user_attr_name_list"
  _ENABLE_PROCESSOR_STABLE_NAME = False
  _CHECK_DYNAMIC_PARAM_FORMAT = True

  def __init__(self, config: dict):
    self.__name = config.pop("name", "")
    self.__alias = config.pop("alias", "")
    self.__item_table = config.get("item_table", None)
    self.__no_check = bool(config.pop("no_check", False))
    self.__namespace = tuple()
    self.__downstream = None
    self.__preceding_output_common_attrs = None
    self.__preceding_output_item_attrs = None
    self.__succeeding_absent_common_attrs = None
    self.__succeeding_absent_item_attrs = None
    self.__config_hash = None
    self.__condition_hash = None
    self.__need_reorder = config.pop("OPT_REORDER", None)
    self.__is_reorder_barrier = config.pop("REORDER_BARRIER", False)
    self._config = config
    self.__is_merged = False
    self.__code_line = ""

  def extract_common_attrs_in_select_item(self, config):
    input_common_attrs_this_rule = set()
    input_common_attrs_this_rule.update(self.extract_dynamic_params(config.get("enable")))
    input_common_attrs_this_rule.update(self.extract_dynamic_params(config.get("limit")))
    input_common_attrs_this_rule.update(self.extract_dynamic_params(config.get("compare_to"), check_format=False))

    compare_to = config.get("compare_to")
    if compare_to and isinstance(compare_to, dict):
      range_cfg = compare_to.get("range")
      if range_cfg and isinstance(range_cfg, dict):
        input_common_attrs_this_rule.update(self.extract_dynamic_params(range_cfg.get("lower_bound")))
        input_common_attrs_this_rule.update(self.extract_dynamic_params(range_cfg.get("upper_bound")))

    for ft in config.get("filters", []):
      input_common_attrs_this_rule.update(self.extract_common_attrs_in_select_item(ft))
    return input_common_attrs_this_rule

  def extract_item_attrs_in_select_item(self, config):
    input_item_attrs_this_rule = set()
    if config.get("attr_name"):
      input_item_attrs_this_rule.add(config["attr_name"])
    for ft in config.get("filters", []):
      input_item_attrs_this_rule.update(self.extract_item_attrs_in_select_item(ft))
    return input_item_attrs_this_rule

  def check_select_config(self, config):
    if "join" in config:
      for attr in ["attr_name", "compare_to", "select_if", "select_if_attr_missing"]:
        check_arg(attr not in config, f" join 参数不应与 {attr} 参数同时配置")

      filters = config.get("filters")
      check_arg(filters, f"规则组合模式下应配置 filters 参数")
      for ft in filters:
        self.check_select_config(ft)
    else:
      check_arg(("attr_name" in config) ^ ("check_reason" in config), "单个选择模式下，必须配置 attr_name 或 check_reason（且只能配置其中一个）")
      check_arg(config.get("select_if_attr_missing", False) or ("select_if" in config),
                "单个选择模式下，若没有配置 select_if_attr_missing, 则必须配置 select_if")

  @strict_types
  def _check_cache_config(self) -> None:
    if not self.cache_outputs:
      return
    cache_config = self._config.get("cache_outputs", {})

    # attr cache
    is_common_cache = isinstance(cache_config.get("common_cache_key"), str) and len(cache_config.get("common_cache_key")) > 0
    is_item_cache = isinstance(cache_config.get("item_cache_key"), str) and len(cache_config.get("item_cache_key")) > 0
    item_cache_key_use_item_key = cache_config.get("item_cache_key_use_item_key", False)

    # retrieve cache
    is_retrieve_cache = isinstance(cache_config.get("retrieve_cache_key"), str) and len(cache_config.get("retrieve_cache_key")) > 0

    if item_cache_key_use_item_key and is_item_cache:
      check_arg(False, "启用 item cache 时, item_cache_key 和 item_cache_key_use_item_key 只能生效一个")
    is_item_cache = is_item_cache or item_cache_key_use_item_key

    check_arg((is_common_cache and not is_item_cache and not is_retrieve_cache) or 
              (is_item_cache and not is_common_cache and not is_retrieve_cache) or 
              (is_retrieve_cache and not is_common_cache and not is_item_cache), 
              "启用 cache 时, 必须配置 common_cache_key 或者 item_cache_key 或者 is_retrieve_cache, 且不能同时配置")

    if is_common_cache or is_item_cache:
        if len(get_all_output_common_attrs(self)) > 0 and len(get_all_output_item_attrs(self)) > 0:
          check_arg(False, "启用 cache 的算子不能同时输出 item attr 和 common attr")
        if len(get_all_output_common_attrs(self)) == 0 and len(get_all_output_item_attrs(self)) == 0:
          check_arg(False, "启用 cache 的算子不能没有输出的 attr")
        if is_common_cache and len(get_all_output_common_attrs(self)) == 0:
          check_arg(False, "启用 cache 时, 指定 common_cache_key 的算子必须只输出 common attr")
        if is_item_cache and len(get_all_output_item_attrs(self)) == 0:
          check_arg(False, "启用 cache 时, 指定 item_cache_key 的算子必须只输出 item attr")

  @strict_types
  def _check_config(self) -> None:
    """ 子类配置检查 """
    pass

  @strict_types
  def check_config(self) -> None:
    """ 检查配置是否合法 """
    if "select_item" in self._config:
      self.check_select_config(self._config["select_item"])
    self._check_cache_config()
    self._check_config()

  @strict_types
  def no_check(self) -> bool:
    """ 是否跳过一些合法性检查 """
    return self.__no_check

  @property
  @strict_types
  def is_merged(self) -> bool:
    return self.__is_merged

  @is_merged.setter
  @strict_types
  def is_merged(self, is_merged: bool) -> None:
    self.__is_merged = is_merged 

  @strict_types
  def depend_on_all_item_attrs(self) -> bool:
    """ 是否依赖当前目标 item_table 中的所有 attr 数据 """
    return False

  @strict_types
  def depend_on_all_common_attrs(self) -> bool:
    """ 是否依赖所有 common attr 数据 """
    return False

  @property
  @strict_types
  def condition_hash(self) -> str:
    return self.__condition_hash

  @condition_hash.setter
  @strict_types
  def condition_hash(self, con_hash: str) -> None:
    self.__condition_hash = con_hash

  @strict_types
  def autogen_processor_name(self) -> None:
    """ 自动生成一个全局唯一的 processor name """
    self.__name = f"{self.alias or self.get_type_alias()}_{self.config_hash}"

  @classmethod
  @abstractmethod
  @strict_types
  def get_type_alias(cls) -> str:
    """ 获取当前类型 Processor 的类型别称, 用于自动命名 Processor 实例 """

  @property
  @strict_types
  def alias(self) -> str:
    """ 自定义当前 Processor 的自动命名前缀 """
    return self.__alias

  @property
  @strict_types
  def name(self) -> str:
    """ 获取当前 Processor 的完整名称 """
    if self.__namespace and self.__name:
      names = list(self.__namespace)
      names.append(self.__name)
      return "::".join(names)
    else:
      return self.__name

  @property
  @strict_types
  def raw_name(self) -> str:
    """ 获取当前 Processor 的原始名称 """
    return self.__name

  @raw_name.setter
  @strict_types
  def raw_name(self, name: str) -> None:
    """ 设置当前 Processor 的原始名称 """
    name_reserved_chars = " .:[]"
    check_arg(all(x not in name for x in name_reserved_chars), f"非法的 processor name: '{name}', 不可包含空格及以下字符:{name_reserved_chars}")
    self.__name = name

  @property
  def downstream(self):
    """ 获取当前 Processor 的 downstream processor 实例 """
    return self.__downstream

  @downstream.setter
  def downstream(self, processor) -> None:
    """ 设置当前 Processor 的 downstream processor 实例 """
    assert self.is_async(), "downstream setter is available for async processor only"
    self.__downstream = processor

  def get_sub_flow(self):
    return None

  def get_embedded_flows(self):
    return None

  def generate_embedded_flows(self, main_flow):
    pass

  @property
  @strict_types
  def need_preceding_output_info(self) -> bool:
    """ 该 Processor 是否需要知晓之前所有 Processor 已产出过的 common/item attr 信息 """
    if self.get_sub_flow():
      return True
    return False

  @property
  @strict_types
  def preceding_output_common_attrs(self) -> set:
    """ 获取所有先于该 Processor 执行的所有 Processor 所产出的 common attr 列表 """
    return self.__preceding_output_common_attrs or set()

  @preceding_output_common_attrs.setter
  @strict_types
  def preceding_output_common_attrs(self, attrs: set):
    """ 获取所有先于该 Processor 执行的所有 Processor 所产出的 common attr 列表 """
    self.__preceding_output_common_attrs = attrs.copy()

  @property
  @strict_types
  def preceding_output_item_attrs(self) -> set:
    """ 获取所有先于该 Processor 执行的所有 Processor 所产出的 item attr 列表 """
    assert self.__preceding_output_item_attrs is not None
    return self.__preceding_output_item_attrs

  @preceding_output_item_attrs.setter
  @strict_types
  def preceding_output_item_attrs(self, attrs: set):
    """ 设置所有先于该 Processor 执行的所有 Processor 所产出的 item attr 列表 """
    self.__preceding_output_item_attrs = attrs.copy()

  @property
  @strict_types
  def succeeding_absent_common_attrs(self) -> set:
    """ 获取所有后于该 Processor 执行的所有 Processor 所缺失的 common attr 列表 """
    assert self.__succeeding_absent_common_attrs is not None
    return self.__succeeding_absent_common_attrs

  @succeeding_absent_common_attrs.setter
  @strict_types
  def succeeding_absent_common_attrs(self, attrs: set):
    """ 设置所有后于该 Processor 执行的所有 Processor 所产出的 common attr 列表 """
    self.__succeeding_absent_common_attrs = attrs.copy()

  @property
  @strict_types
  def succeeding_absent_item_attrs(self) -> set:
    """ 获取所有后于该 Processor 执行的所有 Processor 所缺失的 item attr 列表 """
    assert self.__succeeding_absent_item_attrs is not None
    return self.__succeeding_absent_item_attrs

  @succeeding_absent_item_attrs.setter
  @strict_types
  def succeeding_absent_item_attrs(self, attrs: set):
    """ 设置所有后于该 Processor 执行的所有 Processor 所产出的 item attr 列表 """
    self.__succeeding_absent_item_attrs = attrs.copy()

  @strict_types
  def need_reorder(self, default_value: bool = False) -> bool:
    """ 是否需要优化当前 Processor 的执行顺序 """
    if self.__need_reorder is None:
      return default_value
    else:
      return self.__need_reorder

  @strict_types
  def is_reorder_barrier(self) -> bool:
    """ 是否禁止后续 Processor 提前执行 """
    return self.__is_reorder_barrier

  @strict_types
  def is_async(self) -> bool:
    """ 是否为异步 Processor """
    return False

  @strict_types
  def pass_common_attrs_in_request(self) -> bool:
    """ 获取 pass_common_attrs_in_request 配置值 (仅对有 subflow 的 processor 有意义) """
    return self._config.get("pass_common_attrs_in_request", False)

  @classmethod
  @strict_types
  def is_for_predict(cls) -> bool:
    """ 是否为预估 xtr 的 Processor """
    return False

  @strict_types
  def depend_on(self, processor) -> bool:
    """ 判断当前 Processor 是否有数据依赖另一个前置 Processor """
    if self is processor.downstream or self.name == processor._config.get("downstream_processor"):
      return True
    if has_intersection(self.input_item_tables, processor.output_item_tables | processor.modify_item_tables, False):
      return True
    if has_intersection(get_all_input_item_attrs(self), get_all_output_item_attrs(processor)):
      return True
    if has_intersection(get_all_input_common_attrs(self), get_all_output_common_attrs(processor)):
      return True
    if self.depend_on_all_common_attrs() and len(get_all_output_common_attrs(processor)) > 0:
      return True
    if self.depend_on_all_item_attrs() and any(self.item_table == extract_table_and_attr_name(x)[0] for x in get_all_output_item_attrs(processor)):
      return True
    return False

  @strict_types
  def depend_on_items(self) -> bool:
    """ 当前 Processor 工作是否依赖当前 item 结果集的召回, 例如只依赖 user 侧信息则可为 false """
    if self._config.get("item_list_from_attr", ""):
      return False
    range_start = self._config.get("range_start", 0)
    range_end = self._config.get("range_end", 0)
    return range_start == 0 or range_end == 0 or range_start != range_end

  @strict_types
  def depend_on_sample_list_user_info(self) -> bool:
    """ 当前 Processor 工作是否依赖 sample_list 服务返回的 user info 数据 """
    return False

  @strict_types
  def handle_subset_items(self) -> bool:
    """ 当前 Processor 工作是否只对某个 item 子集进行处理 """
    range_start = self._config.get("range_start", 0)
    range_end = self._config.get("range_end", 0)
    return (range_start != 0 or range_end != 0) and range_start != range_end

  @classmethod
  @strict_types
  def config_hash_length(cls) -> int:
    """ json config 的哈希字符串默认长度 """
    return 6

  @property
  @strict_types
  def config_hash_fields(self) -> list:
    return []

  @property
  @strict_types
  def config_hash(self) -> str:
    """ 获取当前 json config 的哈希字符串 """
    if not self.__config_hash:
      hash_len = int(os.environ.get("DRAGONFLY_CONFIG_HASH_LENGTH", self.config_hash_length()))
      if hash_len == 0:
        return self.__config_hash
      if self._ENABLE_PROCESSOR_STABLE_NAME:
        config_hash_field = dict()
        config_hash_field["$condition_hash"] = self.__condition_hash
        if self.config_hash_fields:
          for field in self.config_hash_fields:
            field_config = self.definition.get(field)
            if field_config != None:
              config_hash_field[field] = field_config
          self.__config_hash = gen_dict_hash_hexstr(config_hash_field, hash_len)
        else:
          config_hash_field.update(self.definition)
          config_hash_field.pop("skip", None)
          config_hash_field.pop("$metadata", None)
          config_hash_field.pop("downstream_processor", None)
          self.__config_hash = gen_dict_hash_hexstr(config_hash_field, hash_len)
      else:
        self.__config_hash = gen_dict_hash_hexstr(self.definition, hash_len)
    return self.__config_hash

  @property
  @strict_types
  def definition(self) -> dict:
    """ 获取完整的 json config 定义 """
    if "type_name" not in self._config:
      self._config["type_name"] = self.__class__.__name__
    sub_flow = self.get_sub_flow()
    if sub_flow is not None:
      assert sub_flow.name, "sub_flow name 不可为空"
      self._config["flow_name"] = sub_flow.name
    if self.is_async() and self.downstream and self._config.get("downstream_processor") is None:
      assert self.downstream.name, f"{self.name or self.get_type_alias()} 当前绑定了无名 downstream:\n{self.downstream._config}"
      self._config["downstream_processor"] = self.downstream.name
    return self._config

  @property
  @strict_types
  def namespace(self) -> tuple:
    return self.__namespace

  @namespace.setter
  @strict_types
  def namespace(self, ns: tuple) -> None:
    self.__namespace = ns

  @property
  @strict_types
  def item_table(self) -> Optional[str]:
    return self.__item_table

  @item_table.setter
  @strict_types
  def item_table(self, item_table: str) -> None:
    self.__item_table = item_table

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    """ 定义当前 Processor 依赖的 common_attr 输入 """
    attrs = set()
    if "select_item" in self._config:
      attrs.update(self.extract_common_attrs_in_select_item(self._config["select_item"]))
    for name in ["skip", "degrade_strategy"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    if self.cache_outputs:
      if "common_cache_key" in self.cache_config:
        attrs.add(self.cache_config["common_cache_key"])
      if "retrieve_cache_key" in self.cache_config:
        attrs.add(self.cache_config["retrieve_cache_key"])
      if "enable" in self.cache_config:
        attrs.update(self.extract_dynamic_params(self.cache_config.get("enable")))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    """ 定义当前 Processor 产生的 common_attr 输出 """
    attrs = set()
    if self.is_async():
      attrs.add(self._config.get("save_async_status_to"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    """ 定义当前 Processor 依赖的 item_attr 输入 """
    attrs = set()
    if "select_item" in self._config:
      attrs.update(self.extract_item_attrs_in_select_item(self._config["select_item"]))
    if self.cache_outputs and "item_cache_key" in self.cache_config:
      attrs.add(self.cache_config["item_cache_key"])
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    """ 定义当前 Processor 产生的 item_attr 输出 """
    return set()

  @property
  @strict_types
  def input_item_tables(self) -> set:
    """ 定义当前 Processor 依赖结果集的 tables """
    return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 定义当前 Processor 增加结果集的 tables """
    return set()

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    """ 获取当前 Processor 修改结果集的 tables """
    return set()

  @property
  @strict_types
  def used_common_attrs(self) -> set:
    """ 当前 Processor 本身产出但又使用了的 common_attr，带 embedded flow 的 Mixer 特有 """
    check_arg(os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true", "使用嵌套 flow 需要开启 ENABLE_EMBEDDED_FLOW")
    return set()

  @property
  @strict_types
  def used_item_attrs(self) -> set:
    """ 当前 Processor 本身产出但又使用了的 item_attr，带 embedded flow 的 Mixer 特有 """
    check_arg(os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true", "使用嵌套 flow 需要开启 ENABLE_EMBEDDED_FLOW")
    return set()

  @strict_types
  def extract_dynamic_params(self, expr, check_format: bool = True) -> set:
    attrs = set()
    if not isinstance(expr, str):
      return attrs
    if not expr.startswith("{{") or not expr.endswith("}}"):
      if check_format and self._CHECK_DYNAMIC_PARAM_FORMAT and not self.no_check() and expr in self.preceding_output_common_attrs:
        raise ArgumentError(f"在 {self.name or self.get_type_alias()} 中检测到错误的动态参数引用，动态参数需要用双花括号包裹！请将 {expr} 更改为 {{{{{expr}}}}}。（如果使用的字面量跟 common attr 同名也可能误触发该检查报错）")
      return attrs
    if is_dynamic_expr(expr):
      script = expr[2:-2]
      attrs = set(extract_attrs_from_expr(script))
      # TODO: 重新完善检查变量的初值逻辑，兼容数字和 list 类型的使用场景
      # for attr in attrs:
      #   script = f"local {attr} = {attr} or 0 \n {script}"
      # check_lua_script(script)
      attrs.update(self._config.get("$eval_common_attrs", []))
      self._config["$eval_common_attrs"] = attrs.copy()
      return attrs
    else:
      res = extract_common_attrs(expr)
      assert len(res) <= 1, f"在动态参数 {expr} 中检测到多个 attr: {res}"
      if res:
        attrs.add(res[0])
      return attrs

  @property
  @strict_types
  def cache_config(self) -> dict:
    return self._config.get("cache_outputs", {})

  @property
  @strict_types
  def cache_outputs(self) -> bool:
    return self.cache_config.get("enable", False)

  @property
  @strict_types
  def enable_auto_merge(self) -> bool:
    return False

  @strict_types
  def auto_merge_config(self, other_config: dict) -> bool:
    """ 对两个配置进行合并 """
    raise ArgumentError(f"开启合并去重功能的算子必须自行实现配置合并接口: {self.get_type_alias()}")
    return False

  @property
  def code_line(self) ->str:
    """ 获取当前 Processor inspect 所得到的代码位置 """
    return self.__code_line

  @code_line.setter
  def code_line(self, code_line: str) -> None:
    """ 设置当前 Processor 的 downstream processor 实例 """
    self.__code_line = code_line

class LeafRetriever(LeafProcessor):
  """ 所有 Retriever 的基类 """
  @property
  @strict_types
  def input_item_tables(self) -> set:
    """ 获取当前 Processor 依赖结果集的 tables """
    if is_depend_on_item_results(self):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 获取当前 Processor 增加结果集的 tables """
    if not self._config.get("save_result_to_common_attr"):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    """ 获取当前 Processor 修改结果集的 tables """
    return set()

  @strict_types
  def depend_on_items(self) -> bool:
    return "short_circuit_threshold" in self._config

  @strict_types
  def handle_subset_items(self) -> bool:
    return False

  @strict_types
  def like_an_enricher(self) -> bool:
    return not self.output_item_tables

  @strict_types
  def reset_existing_item_attrs(self) -> bool:
    return self._config.get("reset_existing_item_attrs", True)

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    """ 获取当前 Processor 依赖的 common_attr 输入 """
    attrs = set()
    for name in ["reset_item_type", "short_circuit_threshold"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attr = self._config.get("save_result_to_common_attr")
    return { attr } if attr else set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set() if self.like_an_enricher() else { "_REASON_" }

class LeafEnricher(LeafProcessor):
  """ 所有 Enricher 的基类 """
  @property
  @strict_types
  def input_item_tables(self) -> set:
    """ 获取当前 Processor 依赖结果集的 tables """
    if is_depend_on_item_results(self):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 获取当前 Processor 增加结果集的 tables """
    return set()

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    """ 获取当前 Processor 修改结果集的 tables """
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    item_list_from_attr = self._config.get("item_list_from_attr", "")
    if item_list_from_attr:
      attrs.add(item_list_from_attr)
    for name in ["range_start", "range_end", "partition_size"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    if "degrade_limit" in self._config: attrs.add(self._config.get("degrade_limit"))
    target_items = self._config.get("target_item")
    if isinstance(target_items, dict):
      for value in target_items.values():
        if isinstance(value, list):
          for list_value in value:
            attrs.update(self.extract_dynamic_params(list_value, check_format=False))
        else:
          attrs.update(self.extract_dynamic_params(value, check_format=False))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("target_item", {}).keys())

class LeafArranger(LeafProcessor):
  """ 所有 Arranger 的基类 """
  @property
  @strict_types
  def input_item_tables(self) -> set:
    """ 获取当前 Processor 依赖结果集的 tables """
    if is_depend_on_item_results(self):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 获取当前 Processor 增加结果集的 tables """
    return set()

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    """ 获取当前 Processor 修改结果集的 tables """
    if is_depend_on_item_results(self):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    item_list_from_attr = self._config.get("item_list_from_attr", "")
    if item_list_from_attr:
      attrs.add(item_list_from_attr)
    for name in ["range_start", "range_end"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    target_items = self._config.get("target_item")
    if isinstance(target_items, dict):
      for value in target_items.values():
        if isinstance(value, list):
          for list_value in value:
            attrs.update(self.extract_dynamic_params(list_value, check_format=False))
        else:
          attrs.update(self.extract_dynamic_params(value, check_format=False))
    return attrs
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    item_list_from_attr = self._config.get("item_list_from_attr", "")
    if item_list_from_attr:
      attrs.add(item_list_from_attr)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("target_item", {}).keys())

class LeafObserver(LeafProcessor):
  """ 所有 Observer 的基类 """
  @property
  @strict_types
  def input_item_tables(self) -> set:
    """ 获取当前 Processor 依赖结果集的 tables """
    if is_depend_on_item_results(self):
      return {self.item_table}
    else:
      return set()

  @property
  @strict_types
  def output_item_tables(self) -> set:
    """ 获取当前 Processor 增加结果集的 tables """
    return set()

  @property
  @strict_types
  def modify_item_tables(self) -> set:
    """ 获取当前 Processor 修改结果集的 tables """
    return set()

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    item_list_from_attr = self._config.get("item_list_from_attr", "")
    if item_list_from_attr:
      attrs.add(item_list_from_attr)
    for name in ["range_start", "range_end"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    target_items = self._config.get("target_item")
    if isinstance(target_items, dict):
      for value in target_items.values():
        if isinstance(value, list):
          for list_value in value:
            attrs.update(self.extract_dynamic_params(list_value, check_format=False))
        else:
          attrs.update(self.extract_dynamic_params(value, check_format=False))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set(self._config.get("target_item", {}).keys())

class LeafMixer(LeafProcessor):
  """ 所有 Mixer 的基类 """
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    check_arg(os.environ.get("CHECK_TALBE_DEPENDENCY", "false") == "true", "使用 LeafMixer 需要开启 CHECK_TALBE_DEPENDENCY")

  @strict_types
  def handle_subset_items(self) -> bool:
    return False

  @strict_types
  def like_an_enricher(self) -> bool:
    return False

  @strict_types
  def reset_existing_item_attrs(self) -> bool:
    return self._config.get("reset_existing_item_attrs", True)

class LeafMixerEmbedFLow(LeafMixer):
  """ 含 embedded flow 的 Mixer 的基类 """
  @strict_types
  def __init__(self, config: dict):
    config.update({"embedded_flow": {}})
    super(LeafMixerEmbedFLow, self).__init__(config)
    check_arg(os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true", "使用嵌套 flow 需要开启 ENABLE_EMBEDDED_FLOW")

    self.__embedded_flows = []

  def get_embedded_flows(self):
    return self.__embedded_flows

  def embedded_flow_order(self) -> list:
    return None

  def generate_embedded_flows(self, main_flow):
    generators =  {k: v for k, v in self.__class__.__dict__.items() if isinstance(v, EmbeddedFlowGenerator)}

    embedded_flows = []
    for name, gen in generators.items():
      flow_ns = gen.flow_ns(processor=self)
      embedded_flow = type(main_flow)(name=f'{main_flow.name}.{flow_ns}.{name}', parent_flow=main_flow)
      # NOTE(weiyilong): 模仿 sub flow 的 inherit_ns 逻辑
      for ns in self.namespace:
        embedded_flow.namespace_(ns, nest=True)
      embedded_flow.namespace_(flow_ns, nest=True)
      embedded_flow.namespace_(name, nest=True)
      gen.generate(processor=self, flow=embedded_flow)
      embedded_flows.append((name, embedded_flow))

    order = self.embedded_flow_order()
    if order:
      try:
        embedded_flows.sort(key=lambda t: order.index(t[0]))
      except ValueError:
        raise ArgumentError(f"embedded_flow_order() 必须覆盖所有名字")

    self.__embedded_flows = embedded_flows
    self._config.setdefault('embedded_pipelines', {}).update({
      name: embedded_flow.name for name, embedded_flow in embedded_flows
    })

  @property
  def disable_add_table_name(self) -> bool:
    """ 防止嵌套 flow 内的属性被添加上 processor 本身的 table """
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return self._config["embedded_flow"].get("input_common_attrs", set())

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return self._config["embedded_flow"].get("output_common_attrs", set())

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return self._config["embedded_flow"].get("input_item_attrs", set())

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return self._config["embedded_flow"].get("output_item_attrs", set())

  @property
  @strict_types
  def used_common_attrs(self) -> set:
    return self._config["embedded_flow"].get("used_common_attrs", set())

  @property
  @strict_types
  def used_item_attrs(self) -> set:
    return self._config["embedded_flow"].get("used_item_attrs", set())

def is_depend_on_item_results(processor) -> bool:
  if not getattr(processor, "depend_on_items")():
    return False
  for t in type(processor).mro():
    parent = super(t, processor)
    if not hasattr(parent, "depend_on_items"):
      break
    if not getattr(parent, "depend_on_items")():
      return False
  return True

def __inherit_all_set_results(processor, prop: str) -> set:
  res = set()
  res.update(getattr(processor, prop))
  for t in type(processor).mro():
    parent = super(t, processor)
    if not hasattr(parent, prop):
      break
    res.update(getattr(parent, prop))
  return set(filter(lambda x: x and isinstance(x, str), res))

def __inherit_all_set_results_alt(processor, prop: str, alt: str) -> "tuple[set, set]":
  """ 合并继承链上的所有 prop 属性，但是会根据 alt 属性的 True/False 收集到两个集合中用于特殊处理 """
  res, res_alt = set(), set()
  (res_alt if getattr(processor, alt, False) else res).update(getattr(processor, prop))
  for t in type(processor).mro():
    parent = super(t, processor)
    if not hasattr(parent, prop):
      break
    (res_alt if getattr(processor, alt, False) else res).update(getattr(parent, prop))
  return set(filter(lambda x: x and isinstance(x, str), res)), set(filter(lambda x: x and isinstance(x, str), res_alt))

def __get_attr_name_in_all_channels(processor, base_attrs: set, for_output_attr: bool) -> set:
  attrs = set()
  if isinstance(processor, LeafRetriever):
    if for_output_attr:
      save_result_to_common_attr = processor._config.get("save_result_to_common_attr", "")
      if save_result_to_common_attr:
        attrs.update(gen_attr_name_with_common_attr_channel(v, save_result_to_common_attr) for v in base_attrs)
  else:
    item_list_from_attr = processor._config.get("item_list_from_attr", "")
    if item_list_from_attr:
      attrs.update(gen_attr_name_with_common_attr_channel(v, item_list_from_attr) for v in base_attrs)
    if isinstance(processor, LeafEnricher):
      item_source = processor._config.get("item_source")
      default_reco_results_val = False
      if not item_source:
        item_source = processor._config.get("additional_item_source")
        default_reco_results_val = bool(item_source)
      if item_source:
        if not item_list_from_attr and item_source.get("reco_results", default_reco_results_val):
          attrs.update(base_attrs)
        if "latest_browse_set_item" in item_source:
          attrs.update(map(lambda x: gen_attr_name_with_browse_set_channel(x, item_source["latest_browse_set_item"]), base_attrs))
        for attr in item_source.get("common_attr", []):
          attrs.update(map(lambda x, attr=attr: gen_attr_name_with_common_attr_channel(x, attr), base_attrs))
        for attr in item_source.get("item_attr", []):
          attrs.update(map(lambda x, attr=attr: gen_attr_name_with_item_attr_channel(x, attr), base_attrs))
  return attrs if attrs else base_attrs

def get_all_input_common_attrs(processor) -> set:
  return __inherit_all_set_results(processor, "input_common_attrs")

def get_all_output_common_attrs(processor) -> set:
  return __inherit_all_set_results(processor, "output_common_attrs")

def get_all_input_item_attrs(processor, with_channel_name: bool = True, with_table_name: bool = True) -> set:
  if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
    return get_all_input_item_attrs_support_embedded_flow(processor, with_channel_name=with_channel_name, with_table_name=with_table_name)
  base_attrs = __inherit_all_set_results(processor, "input_item_attrs")
  if not with_channel_name:
    if with_table_name:
      return try_add_table_name(processor.item_table, strip_channel_name(base_attrs))
    else:
      return strip_channel_name(base_attrs)
  else:
    if with_table_name:
      return try_add_table_name(processor.item_table, __get_attr_name_in_all_channels(processor, base_attrs, False))
    else:
      return __get_attr_name_in_all_channels(processor, base_attrs, False)

def get_all_input_item_attrs_support_embedded_flow(processor, with_channel_name: bool = True, with_table_name: bool = True) -> set:
  # NOTE(weiyilong): 继承链上标记了 disable_add_table_name 的，其 item attr 排除在 try_add_table_name 之外
  base_attrs, base_attrs_disable_add_table = __inherit_all_set_results_alt(processor, "input_item_attrs", "disable_add_table_name")
  if not with_channel_name:
    if with_table_name:
      return try_add_table_name(processor.item_table, strip_channel_name(base_attrs)) | strip_channel_name(base_attrs_disable_add_table)
    else:
      return strip_channel_name(base_attrs | base_attrs_disable_add_table)
  else:
    if with_table_name:
      return try_add_table_name(processor.item_table, __get_attr_name_in_all_channels(processor, base_attrs, False)) | __get_attr_name_in_all_channels(processor, base_attrs_disable_add_table, False)
    else:
      return __get_attr_name_in_all_channels(processor, base_attrs | base_attrs_disable_add_table, False)

def get_all_output_item_attrs(processor, with_channel_name: bool = True, with_table_name: bool = True) -> set:
  if os.environ.get("ENABLE_EMBEDDED_FLOW", "false") == "true":
    return get_all_output_item_attrs_support_embedded_flow(processor, with_channel_name=with_channel_name, with_table_name=with_table_name)
  base_attrs = __inherit_all_set_results(processor, "output_item_attrs")
  if not with_channel_name:
    if with_table_name:
      return try_add_table_name(processor.item_table, base_attrs)
    else:
      return base_attrs
  else:
    if with_table_name:
      return try_add_table_name(processor.item_table, __get_attr_name_in_all_channels(processor, base_attrs, True))
    else:
      return __get_attr_name_in_all_channels(processor, base_attrs, True)

def get_all_output_item_attrs_support_embedded_flow(processor, with_channel_name: bool = True, with_table_name: bool = True) -> set:
  # NOTE(weiyilong): 继承链上标记了 disable_add_table_name 的，其 item attr 排除在 try_add_table_name 之外
  base_attrs, base_attrs_disable_add_table = __inherit_all_set_results_alt(processor, "output_item_attrs", "disable_add_table_name")
  if not with_channel_name:
    if with_table_name:
      return try_add_table_name(processor.item_table, base_attrs) | base_attrs_disable_add_table
    else:
      return base_attrs | base_attrs_disable_add_table
  else:
    if with_table_name:
      return try_add_table_name(processor.item_table, __get_attr_name_in_all_channels(processor, base_attrs, True)) | __get_attr_name_in_all_channels(processor, base_attrs_disable_add_table, True)
    else:
      return __get_attr_name_in_all_channels(processor, base_attrs | base_attrs_disable_add_table, True)
