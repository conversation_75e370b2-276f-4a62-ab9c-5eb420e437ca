# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: dragon_graph.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12\x64ragon_graph.proto\x12\x0c\x64ragon_graph\"f\n\x10TensorShapeProto\x12/\n\x03\x64im\x18\x02 \x03(\x0b\x32\".dragon_graph.TensorShapeProto.Dim\x1a!\n\x03\x44im\x12\x0c\n\x04size\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xef\x01\n\tAttrValue\x12\x0b\n\x01s\x18\x02 \x01(\x0cH\x00\x12\x0b\n\x01i\x18\x03 \x01(\x03H\x00\x12\x0b\n\x01\x66\x18\x04 \x01(\x02H\x00\x12\x0b\n\x01\x62\x18\x05 \x01(\x08H\x00\x12\x31\n\x04list\x18\x01 \x01(\x0b\x32!.dragon_graph.AttrValue.ListValueH\x00\x1ar\n\tListValue\x12\t\n\x01s\x18\x02 \x03(\x0c\x12\r\n\x01i\x18\x03 \x03(\x03\x42\x02\x10\x01\x12\r\n\x01\x66\x18\x04 \x03(\x02\x42\x02\x10\x01\x12\r\n\x01\x62\x18\x05 \x03(\x08\x42\x02\x10\x01\x12-\n\x05shape\x18\x07 \x03(\x0b\x32\x1e.dragon_graph.TensorShapeProtoB\x07\n\x05value\"\xf3\x01\n\x07NodeDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02op\x18\x02 \x01(\t\x12\r\n\x05input\x18\x03 \x03(\t\x12\x0e\n\x06\x64\x65vice\x18\x04 \x01(\t\x12-\n\x04\x61ttr\x18\x05 \x03(\x0b\x32\x1f.dragon_graph.NodeDef.AttrEntry\x12\x15\n\roriginal_name\x18\x08 \x01(\t\x12\x17\n\x0f\x65xt_module_name\x18\t \x01(\t\x1a\x44\n\tAttrEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.dragon_graph.AttrValue:\x02\x38\x01J\x04\x08\x06\x10\x07J\x04\x08\x07\x10\x08\"\xf1\x01\n\x0bServiceInfo\x12\x14\n\x0cservice_name\x18\x01 \x01(\t\x12\x1d\n\x15\x63urrent_request_types\x18\x02 \x03(\t\x12\x12\n\nis_default\x18\x03 \x01(\x08\x12\x1b\n\x13other_request_types\x18\x04 \x03(\t\x12\x39\n\x0bnaming_mode\x18\x05 \x01(\x0e\x32$.dragon_graph.ServiceInfo.NamingMode\"A\n\nNamingMode\x12\t\n\x05UNSET\x10\x00\x12\x11\n\rSTABLE_NAMING\x10\x01\x12\x15\n\x11NOT_STABLE_NAMING\x10\x02\"u\n\x08GraphDef\x12#\n\x04node\x18\x01 \x03(\x0b\x32\x15.dragon_graph.NodeDef\x12\x13\n\x07version\x18\x03 \x01(\x05\x42\x02\x18\x01\x12/\n\x0cservice_info\x18\x05 \x01(\x0b\x32\x19.dragon_graph.ServiceInfob\x06proto3')



_TENSORSHAPEPROTO = DESCRIPTOR.message_types_by_name['TensorShapeProto']
_TENSORSHAPEPROTO_DIM = _TENSORSHAPEPROTO.nested_types_by_name['Dim']
_ATTRVALUE = DESCRIPTOR.message_types_by_name['AttrValue']
_ATTRVALUE_LISTVALUE = _ATTRVALUE.nested_types_by_name['ListValue']
_NODEDEF = DESCRIPTOR.message_types_by_name['NodeDef']
_NODEDEF_ATTRENTRY = _NODEDEF.nested_types_by_name['AttrEntry']
_SERVICEINFO = DESCRIPTOR.message_types_by_name['ServiceInfo']
_GRAPHDEF = DESCRIPTOR.message_types_by_name['GraphDef']
_SERVICEINFO_NAMINGMODE = _SERVICEINFO.enum_types_by_name['NamingMode']
TensorShapeProto = _reflection.GeneratedProtocolMessageType('TensorShapeProto', (_message.Message,), {

  'Dim' : _reflection.GeneratedProtocolMessageType('Dim', (_message.Message,), {
    'DESCRIPTOR' : _TENSORSHAPEPROTO_DIM,
    '__module__' : 'dragon_graph_pb2'
    # @@protoc_insertion_point(class_scope:dragon_graph.TensorShapeProto.Dim)
    })
  ,
  'DESCRIPTOR' : _TENSORSHAPEPROTO,
  '__module__' : 'dragon_graph_pb2'
  # @@protoc_insertion_point(class_scope:dragon_graph.TensorShapeProto)
  })
_sym_db.RegisterMessage(TensorShapeProto)
_sym_db.RegisterMessage(TensorShapeProto.Dim)

AttrValue = _reflection.GeneratedProtocolMessageType('AttrValue', (_message.Message,), {

  'ListValue' : _reflection.GeneratedProtocolMessageType('ListValue', (_message.Message,), {
    'DESCRIPTOR' : _ATTRVALUE_LISTVALUE,
    '__module__' : 'dragon_graph_pb2'
    # @@protoc_insertion_point(class_scope:dragon_graph.AttrValue.ListValue)
    })
  ,
  'DESCRIPTOR' : _ATTRVALUE,
  '__module__' : 'dragon_graph_pb2'
  # @@protoc_insertion_point(class_scope:dragon_graph.AttrValue)
  })
_sym_db.RegisterMessage(AttrValue)
_sym_db.RegisterMessage(AttrValue.ListValue)

NodeDef = _reflection.GeneratedProtocolMessageType('NodeDef', (_message.Message,), {

  'AttrEntry' : _reflection.GeneratedProtocolMessageType('AttrEntry', (_message.Message,), {
    'DESCRIPTOR' : _NODEDEF_ATTRENTRY,
    '__module__' : 'dragon_graph_pb2'
    # @@protoc_insertion_point(class_scope:dragon_graph.NodeDef.AttrEntry)
    })
  ,
  'DESCRIPTOR' : _NODEDEF,
  '__module__' : 'dragon_graph_pb2'
  # @@protoc_insertion_point(class_scope:dragon_graph.NodeDef)
  })
_sym_db.RegisterMessage(NodeDef)
_sym_db.RegisterMessage(NodeDef.AttrEntry)

ServiceInfo = _reflection.GeneratedProtocolMessageType('ServiceInfo', (_message.Message,), {
  'DESCRIPTOR' : _SERVICEINFO,
  '__module__' : 'dragon_graph_pb2'
  # @@protoc_insertion_point(class_scope:dragon_graph.ServiceInfo)
  })
_sym_db.RegisterMessage(ServiceInfo)

GraphDef = _reflection.GeneratedProtocolMessageType('GraphDef', (_message.Message,), {
  'DESCRIPTOR' : _GRAPHDEF,
  '__module__' : 'dragon_graph_pb2'
  # @@protoc_insertion_point(class_scope:dragon_graph.GraphDef)
  })
_sym_db.RegisterMessage(GraphDef)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['i']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['i']._serialized_options = b'\020\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['f']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['f']._serialized_options = b'\020\001'
  _ATTRVALUE_LISTVALUE.fields_by_name['b']._options = None
  _ATTRVALUE_LISTVALUE.fields_by_name['b']._serialized_options = b'\020\001'
  _NODEDEF_ATTRENTRY._options = None
  _NODEDEF_ATTRENTRY._serialized_options = b'8\001'
  _GRAPHDEF.fields_by_name['version']._options = None
  _GRAPHDEF.fields_by_name['version']._serialized_options = b'\030\001'
  _TENSORSHAPEPROTO._serialized_start=36
  _TENSORSHAPEPROTO._serialized_end=138
  _TENSORSHAPEPROTO_DIM._serialized_start=105
  _TENSORSHAPEPROTO_DIM._serialized_end=138
  _ATTRVALUE._serialized_start=141
  _ATTRVALUE._serialized_end=380
  _ATTRVALUE_LISTVALUE._serialized_start=257
  _ATTRVALUE_LISTVALUE._serialized_end=371
  _NODEDEF._serialized_start=383
  _NODEDEF._serialized_end=626
  _NODEDEF_ATTRENTRY._serialized_start=546
  _NODEDEF_ATTRENTRY._serialized_end=614
  _SERVICEINFO._serialized_start=629
  _SERVICEINFO._serialized_end=870
  _SERVICEINFO_NAMINGMODE._serialized_start=805
  _SERVICEINFO_NAMINGMODE._serialized_end=870
  _GRAPHDEF._serialized_start=872
  _GRAPHDEF._serialized_end=989
# @@protoc_insertion_point(module_scope)
