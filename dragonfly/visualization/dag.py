#!/usr/bin/env python3
# coding=utf-8
"""
filename: dag.py
description: dag graph_def gen_graph_def
author: x<PERSON><PERSON><PERSON><PERSON>@kuaishou.com
date: 2023-05-23 15:20:00
"""
from collections import defaultdict
import copy
import boto3
from botocore.config import Config
import datetime
import os

import urllib.request
import urllib.parse
from ..common_leaf_util import get_config_version, get_current_user, strict_types, extract_attr_names
from ..common_leaf_processor import get_all_output_item_attrs, get_all_input_item_attrs, get_all_output_common_attrs, get_all_input_common_attrs, try_add_table_name
from ..common_leaf_processor import <PERSON><PERSON><PERSON>riever, <PERSON><PERSON><PERSON><PERSON>, LeafProcessor, LeafMixer
from ..ext.common.common_leaf_arranger import CommonRecoPipelineArranger
from ..ext.common.common_leaf_enricher import CommonRecoAbtestCommonAttrEnricher, CommonRecoPipelineEnricher
from ..ext.common.common_leaf_mixer import CommonRecoPipelineMixer
from ..ext.common.common_leaf_retriever import CommonRecoPipelineRetriever
from ..ext.common.common_leaf_observer import CommonRecoPipelineObserver
from ..common_leaf_dsl import LeafService, OfflineRunner
from .dragon_graph_pb2 import GraphDef, NodeDef, TensorShapeProto, AttrValue, ServiceInfo

# 需要 protobuf 最低版本为 3.19.4
# 安装方式
# wget https://github.com/protocolbuffers/protobuf/releases/download/v3.19.4/protobuf-all-3.19.4.tar.gz
# tar zxvf protobuf-all-3.19.4.tar.gz
# cd protobuf-all-3.19.4
# ./configure
# sudo make && sudo make check && sudo make install
# 卸载方式 sudo make uninstall

_sub_flow_processor_names = set()

def gen_graph_def(leaf_flows: list, **kwargs):
  """
  gen_graph_def
  ------
  用于生成序列化的 graphDef 文件的 api。

  输出文件名为 {service_name}::{request_type}

  如果上传到云存储，则配置 mode = "remote"

  参数配置
  ------
  `leaf_flows`: [list] 需要画图的 flow 数组

  `service_name`: [string] 服务名称。

  `request_type`: [string] request_type。

  `mode`: [string] 选配项 可配置为 local 或者 remote。默认为 local

  `draw_branch_controller_output`: [bool] 选配项，不画 branch controller 下游的线。避免 branch controller 下游太多导致图层次变乱。默认为 true

  `hide_ab_param_node`: [bool] 选配项，将获取 ab 参数的节点简化, 避免连线太多。在 processor 信息中添加 _input_ab_params_ 的信息，默认为 false。

  调用示例
  ------
  ``` python
  .gen_graph_def(leaf_flows = [flow])
  .gen_graph_def(leaf_flows = [flow], draw_branch_controller_output = True)
  ```
  """
  # branch controller 输出置为空
  for config_key in ["service_name", "request_type"]:
    if config_key not in kwargs or not kwargs[config_key]:
      raise Exception(f"❌ [没有配置{config_key}] {kwargs}")
  config_version = get_config_version()

  output_file_name = kwargs["service_name"] + "::" + kwargs["request_type"] + "::" + config_version
  graph_def = _gen_graph_def_from_flow(leaf_flows, None, None, **kwargs)

  if "$service_info" in kwargs:
    service_info = kwargs["$service_info"]
    graph_def.service_info.CopyFrom(service_info)

  if kwargs.get("mode", "remote") == "local":
    with open(f"{output_file_name}.bytes.pbtxt", "wb") as fout:
      fout.write(graph_def.SerializeToString())
  elif kwargs.get("mode", "remote") == "remote":
    url = 'http://dragonfly-viz.corp.kuaishou.com/data/plugin/graphs/upload_service_info'
    data = {
      'service_name': kwargs["service_name"],
      'request_type': kwargs["request_type"],
      'version': config_version,
      "user_name": get_current_user(),
      "timestamp": int(datetime.datetime.now().timestamp() * 1000),
      "project": os.environ.get("GIT_REPO_URL", ""),
      "branch": os.environ.get("GIT_BRANCH", ""),
      "commit_id": os.environ.get("COMMIT_ID", ""),
      "processor_naming_mode": service_info.naming_mode
    }
    params = urllib.parse.urlencode(data)
    url = f'{url}?{params}'
    try :
      response = urllib.request.urlopen(url)
    except Exception as e:
      raise Exception(f"❌ [上传 service_info 失败]，可以尝试将 mode 改为 local，采用本地上传. {e}")
    if response.status != 200:
      raise Exception(f"❌ [上传 service_info 失败]，可以尝试将 mode 改为 local，采用本地上传. {response.status}, {response.read()}")
    _push_file2_blobstore(graph_def.SerializeToString(), output_file_name)
  return graph_def

# 递归处理，如果 指定 sub_flow_processor 则为子图。
def _gen_graph_def_from_flow(leaf_flows: list, sub_flow_processor: LeafProcessor, parent_flow, **kwargs):
  draw_branch_controller_output = kwargs.get("draw_branch_controller_output", True)
  hide_ab_param_node = kwargs.get("hide_ab_param_node", False)

  all_processors = []
  # [{["common_attr" | "item_attr"][attr] => attr_index}]
  # 为了生成 input => processor:attr_index
  processor_attr_index = []
  graph_def = GraphDef()
  # key: attr, value: indexes;
  # 输出 item attr| common attr| result。下面三个变量结构一致。
  item_attr_to_indexes = defaultdict(list)
  result_to_indexes = defaultdict(list)
  common_attr_to_indexes = defaultdict(list)
  # 简化 ab 用的。
  all_ab_params = set()
  # 记录 subflow processor 位置，为了之后将子图展开。
  sub_flow_processor_to_index = []

  name_prefix = _append_enter_to_graph(sub_flow_processor, parent_flow, graph_def, all_processors, item_attr_to_indexes, common_attr_to_indexes, result_to_indexes, processor_attr_index)

  # 第一遍遍历，将每个 attr 的 输出位置记录下来，输出位置可能是多个。
  for flow in leaf_flows:
    for processor in flow._processors:
      if isinstance(processor, CommonRecoPipelineEnricher) or isinstance(processor, CommonRecoPipelineRetriever) or isinstance(processor, CommonRecoPipelineArranger) or isinstance(processor, CommonRecoPipelineMixer)  or isinstance(processor, CommonRecoPipelineObserver):
        _sub_flow_processor_names.add(processor.name)
        sub_flow_processor_to_index.append((processor, len(all_processors), flow))

      if processor.raw_name in ['calc_time_cost_s', 'calc_time_cost', 'calc_time_cost_e', "perf_time_cost"]:
        continue

      if hide_ab_param_node and isinstance(processor, CommonRecoAbtestCommonAttrEnricher) and not processor._config.get("for_item_level", False):
        all_ab_params.update(get_all_output_common_attrs(processor))
        continue

      _append_processor_to_graph(
        processor, flow, graph_def, all_processors,
        item_attr_to_indexes, common_attr_to_indexes, result_to_indexes, processor_attr_index,
        name_prefix, draw_branch_controller_output)

  _append_exit_to_graph(sub_flow_processor, parent_flow, graph_def, all_processors, name_prefix)

  # 第二遍遍历，根据每个 attr 的获取位置，及之前记录的输出位置，判断哪些是上游。
  # 如果前文有多个地方生成，均认为是上游。
  # 如果当前 processor 即输入又输出，则认为该 processor 承上启下，后续 processor 如果还使用了这个 attr，则计算上游时，不包括该承上启下 processor 之前的 processor。

  for index, processor in enumerate(all_processors):
    input_ab_params = _find_input_by_common_attrs(processor, index, common_attr_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def, all_ab_params)

    if hide_ab_param_node and input_ab_params:
      _gen_config_value(input_ab_params, graph_def.node[index].attr.get_or_create("_input_ab_params_"))

    _find_input_by_item_attrs(processor, index, item_attr_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def)

    _find_input_by_item_result(processor, index, result_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def)

  # sub flow processor 替换为完整 flow
  _replace_subflow_processor_with_module(sub_flow_processor_to_index, graph_def, **kwargs)

  return graph_def


def _gen_graph_node_name(name: str):
  return name.replace("::", "/")


def item_table_with_default(item_table):
  if item_table == "":
    return "_RESULT_"
  return item_table

# name_prefix + node_name. 或者 name_prefix _ node_name + "_EXIT_" 或者 "_ENTER_"
def _get_node_input_name(name_prefix, name: str):
  node_name = _gen_graph_node_name(name)
  if name_prefix == node_name + "/":
    return name_prefix + "_ENTER_"
  if name in _sub_flow_processor_names:
    return name_prefix + node_name + "/" + "_EXIT_"
  return name_prefix + node_name

white_list_attr = set(["$branch_start"])

# 将 processor._config 转存到 node.attr 里面。
def _gen_node_attr_from_processor_config(processor, node_def):
  for key in processor._config:
    if not key.startswith('$') and not key.startswith('_') and processor._config[key] is not None:
      _gen_config_value(processor._config[key], node_def.attr.get_or_create(key))
    elif key in white_list_attr:
      _gen_config_value(processor._config[key], node_def.attr.get_or_create(key))


def _write_attr_to_tensor(attrs, attr_type, attr_to_indexes, processor_index, output_tensor_value):
  for attr in attrs:
    attr_to_indexes[attr].append(processor_index)
    attr_tensor = TensorShapeProto()
    attr_tensor.dim.add()
    attr_tensor.dim[0].name = attr
    attr_tensor.dim[0].size = attr_type
    output_tensor_value.list.shape.append(attr_tensor)


# 某个 attr 生成的 processors' index [1,2,3,4], 当前 index 为 3 => [2,1], 为了往前遍历 processor 用。
def _get_pre_reversed_indexes(indexes, index):
  pr_indexes = list(filter(lambda n: n < index, indexes))
  pr_indexes.reverse()
  return pr_indexes


# 往前遍历哪个 processor 没有 skip
def _get_index_of_no_skip_processor(pr_indexes, all_processors):
  item = next((output_index for output_index in pr_indexes if all_processors[output_index]._config.get("skip", None) == None), 0)
  return item


# 往前遍历哪个 processor 具有相同 skip
def _get_index_of_same_skip_processor(pr_indexes, all_processors, curr_index):
  if "skip" in all_processors[curr_index]._config:
    skip_str = all_processors[curr_index]._config["skip"]
    return next((output_index for output_index in pr_indexes if all_processors[output_index]._config.get("skip", None) == skip_str), 0)
  return 0


# 给 sub flow module 添加 "_ENTER_"
def _append_enter_to_graph(sub_flow_processor, parent_flow, graph_def, all_processors, item_attr_to_indexes, common_attr_to_indexes, result_to_indexes, processor_attr_index):
  name_prefix = ""
  if sub_flow_processor != None:
    processor_index = len(all_processors)
    name_prefix = _gen_graph_node_name(sub_flow_processor.name + "/")
    node_def = NodeDef()
    output_tensor_value = AttrValue()
    enter_processor = copy.deepcopy(sub_flow_processor)
    all_processors.append(enter_processor)
    node_def.name = name_prefix + "_ENTER_"
    node_def.original_name = enter_processor.name
    node_def.op = enter_processor.get_type_alias()
    _gen_node_attr_from_processor_config(enter_processor, node_def)
    _gen_config_value(parent_flow.name, node_def.attr.get_or_create("$flow_name"))

    # 对于 subflow 来说 _ENTER_ 的 output 是 传入 sub flow 的 attr
    output_common_attrs = try_add_table_name(enter_processor.item_table, enter_processor._config.get('pass_common_attrs', []))
    output_item_attrs = try_add_table_name(enter_processor.item_table, enter_processor._config.get('pass_item_attrs', []))

    _write_attr_to_tensor(output_item_attrs, 1, item_attr_to_indexes, processor_index, output_tensor_value)
    _write_attr_to_tensor(output_common_attrs, 2, common_attr_to_indexes, processor_index, output_tensor_value)

    attr_to_index = {}
    attr_to_index["item_attr"] = {attr: index for index, attr in enumerate(output_item_attrs)}
    attr_to_index["common_attr"] = {attr: index + len(output_item_attrs) for index, attr in enumerate(output_common_attrs)}

    if isinstance(enter_processor, LeafArranger) or isinstance(enter_processor, LeafMixer):
      attr_to_index["result"] = {}
      for table_index, table_name in enumerate(enter_processor.input_item_tables):
        table_name = item_table_with_default(table_name)
        _write_attr_to_tensor([table_name], 3, result_to_indexes, processor_index, output_tensor_value)
        attr_to_index["result"][table_name] = len(output_item_attrs) + len(output_common_attrs) + table_index

    if len(output_tensor_value.list.shape) > 0:
      node_def.attr["_output_shapes"].CopyFrom(output_tensor_value)

    graph_def.node.append(node_def)
    processor_attr_index.append(attr_to_index)
  return name_prefix


# sub flow 的 exit 类型，主要是借用 get_all_input_item|common_attr.
class _SubFlowExitProcessor(LeafProcessor):
  @strict_types
  def __init__(self, config: dict):
    self.processor = config.pop("processor")
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls):
    return "sub_flow_exit"

  @property
  @strict_types
  def input_item_attrs(self):
    if isinstance(self.processor, LeafMixer):
      return self.processor.output_item_attrs
    else:
      return extract_attr_names(self.processor._config.get("merge_item_attrs", []), "name")

  @property
  @strict_types
  def input_common_attrs(self):
    return extract_attr_names(self.processor._config.get("merge_common_attrs", []), "name")


# retrieve_by_sub_flow 或者 arrange_by_sub_flow 的 exit 相当于是 LeafArranger
class _SubFlowResultExitProcessor(LeafArranger, _SubFlowExitProcessor):
  @property
  @strict_types
  def input_item_tables(self) -> set:
    tables = set()
    tables.update(self.processor.output_item_tables)
    tables.update(self.processor.modify_item_tables)
    return tables

# 给 sub flow module 添加 "_EXIT_"
def _append_exit_to_graph(sub_flow_processor, parent_flow, graph_def, all_processors, name_prefix):
  if sub_flow_processor != None:
    node_def = NodeDef()
    if isinstance(sub_flow_processor, LeafRetriever) or isinstance(sub_flow_processor, LeafArranger):
      exit_processor = _SubFlowResultExitProcessor({"processor": sub_flow_processor, "item_table": sub_flow_processor.item_table})
    else:
      exit_processor = _SubFlowExitProcessor({"processor": sub_flow_processor, "item_table": sub_flow_processor.item_table})
    all_processors.append(exit_processor)
    node_def.name = name_prefix + "_EXIT_"
    node_def.op = exit_processor.get_type_alias()
    _gen_config_value(parent_flow.name, node_def.attr.get_or_create("$flow_name"))

    graph_def.node.append(node_def)


# 将某个普通 processor 节点添加到图中。
def _append_processor_to_graph(
    processor, flow, graph_def, all_processors,
    item_attr_to_indexes, common_attr_to_indexes, result_to_indexes, processor_attr_index,
    name_prefix, draw_branch_controller_output):

  processor_index = len(all_processors)
  all_processors.append(processor)

  # 生成 output|input item|common attrs
  output_item_attrs = sorted(list(get_all_output_item_attrs(processor)))
  output_common_attrs = sorted(list(get_all_output_common_attrs(processor)))
  if not draw_branch_controller_output and "for_branch_control" in processor._config and processor._config["for_branch_control"]:
    output_common_attrs = []

  node_def = NodeDef()
  output_tensor_value = AttrValue()
  node_def.name = _gen_graph_node_name(name_prefix + processor.name)
  node_def.original_name = processor.name
  node_def.op = processor.get_type_alias()
  module_split_list = processor.__class__.__module__.split('.')
  if len(module_split_list) < 3:
    if module_split_list[-1].startswith('ad') or module_split_list[-1].startswith('front'):
      node_def.ext_module_name = module_split_list[-1][:-len('_mixer')]
  else:
    node_def.ext_module_name = module_split_list[2]
  _gen_node_attr_from_processor_config(processor, node_def)
  _gen_config_value(flow.name, node_def.attr.get_or_create("$flow_name"))

  # attr 写入 output tensor
  _write_attr_to_tensor(output_item_attrs, 1, item_attr_to_indexes, processor_index, output_tensor_value)
  _write_attr_to_tensor(output_common_attrs, 2, common_attr_to_indexes, processor_index, output_tensor_value)

  attr_to_index = {}
  attr_to_index["item_attr"] = {attr: index for index, attr in enumerate(output_item_attrs)}
  attr_to_index["common_attr"] = {attr: index + len(output_item_attrs) for index, attr in enumerate(output_common_attrs)}

  if isinstance(processor, LeafRetriever) or isinstance(processor, LeafArranger) or isinstance(processor, LeafMixer):
    attr_to_index["result"] = {}
    for table_index, table_name in enumerate(processor.output_item_tables.union(processor.modify_item_tables)):
      table_name = item_table_with_default(table_name)
      _write_attr_to_tensor([table_name], 3, result_to_indexes, processor_index, output_tensor_value)
      attr_to_index["result"][table_name] = len(output_item_attrs) + len(output_common_attrs) + table_index

  if len(output_tensor_value.list.shape) > 0:
    node_def.attr["_output_shapes"].CopyFrom(output_tensor_value)

  graph_def.node.append(node_def)
  processor_attr_index.append(attr_to_index)


# 通过 common attr 依赖关系生成 input
def _find_input_by_common_attrs(processor, index, common_attr_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def, all_ab_params):
  input_ab_params = []
  for common_attr in sorted(list(get_all_input_common_attrs(processor))):
    if common_attr in common_attr_to_indexes:
      output_indexes = common_attr_to_indexes[common_attr]
      change_exist_attr = False

      pre_reversed_indexes = _get_pre_reversed_indexes(output_indexes, index)
      output_indexes = list(filter(lambda n: n >= _get_index_of_no_skip_processor(pre_reversed_indexes, all_processors), output_indexes))
      output_indexes = list(filter(lambda n: n >= _get_index_of_same_skip_processor(pre_reversed_indexes, all_processors, index), output_indexes))

      for output_index in output_indexes:
        if output_index < index:
          # input 格式为 {processor}:{attr_index}
          graph_def.node[index].input.append(_get_node_input_name(name_prefix, all_processors[output_index].name) + ":" + str(processor_attr_index[output_index]["common_attr"][common_attr]))
        if output_index == index:
          change_exist_attr = True
      # >= index的留下来
      if change_exist_attr:
        common_attr_to_indexes[common_attr] = list(filter(lambda n:n >= index, common_attr_to_indexes[common_attr]))
    else:
      if common_attr in all_ab_params:
        input_ab_params.append(common_attr)
  return input_ab_params


# 通过 item attr 依赖关系生成 input
def _find_input_by_item_attrs(processor, index, item_attr_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def):
  for item_attr in sorted(list(get_all_input_item_attrs(processor))):
    if item_attr in item_attr_to_indexes:
      output_indexes = item_attr_to_indexes[item_attr]
      change_exist_attr = False
      for output_index in output_indexes:
        if output_index < index:
          graph_def.node[index].input.append(_get_node_input_name(name_prefix, all_processors[output_index].name) + ":" + str(processor_attr_index[output_index]["item_attr"][item_attr]))
        if output_index == index:
          change_exist_attr = True
      # >= index的留下来
      if change_exist_attr:
        item_attr_to_indexes[item_attr] = list(filter(lambda n:n >= index, item_attr_to_indexes[item_attr]))


# 通过 结果集 依赖关系生成 input.
def _find_input_by_item_result(processor, index, result_to_indexes, all_processors, processor_attr_index, name_prefix, graph_def):
  if isinstance(processor, LeafArranger):
    for table_name in processor.input_item_tables:
      table_name = item_table_with_default(table_name)
      change_exist_attr = False
      for output_index in result_to_indexes[table_name]:
        if output_index < index:
          graph_def.node[index].input.append(_get_node_input_name(name_prefix, all_processors[output_index].name) + ":" + str(processor_attr_index[output_index]["result"][table_name]))
        if output_index == index:
          change_exist_attr = True
      # >= index的留下来
      if change_exist_attr:
        result_to_indexes[table_name] = list(filter(lambda n:n >= index, result_to_indexes[table_name]))


# 将 sub_flow processor 单个节点展开成 sub flow 的子图
def _replace_subflow_processor_with_module(sub_flow_processor_to_index, graph_def, **config_kwargs):
  sub_flow_processor_to_index.reverse()
  for sub_flow_processor, index, parent_flow in sub_flow_processor_to_index:
    son_graph = _gen_graph_def_from_flow([sub_flow_processor.get_sub_flow()], sub_flow_processor, parent_flow, **config_kwargs)
    for input in graph_def.node[index].input:
      son_graph.node[0].input.append(input)
    for n in son_graph.node:
      graph_def.node.insert(index, n)
      index += 1
    graph_def.node[index - 1].attr["_output_shapes"].CopyFrom(graph_def.node[index].attr["_output_shapes"])
    graph_def.node.pop(index)


# config 转存为 AttrValue
def _gen_config_value(config_value, attr_value):
  if type(config_value) is str:
    attr_value.s = bytes(config_value, 'utf-8')
  elif type(config_value) is int:
    attr_value.i = config_value
  elif type(config_value) is float:
    attr_value.f = config_value
  elif type(config_value) is bool:
    attr_value.b = config_value
  elif type(config_value) is list:
    if len(config_value) > 0:
      for v in config_value:
        attr_value.list.s.append(bytes(str(v), 'utf-8'))
  elif type(config_value) is dict:
    attr_value.s = bytes(str(config_value), 'utf-8')
  else:
    attr_value.s = bytes(str(config_value), 'utf-8')


# 通过 blob 上传文件
def _push_file2_blobstore(file_content, blob_file_name, is_sgp_env = False, online = False):
  """
  要求 pthon 版本 低于3.10 且需要安装 boto3
  blob桶地址: https://halo.corp.kuaishou.com/blobstore/bucket/oversea-module-file-list/root/
  file_content: 序列化之后的文件
  blob_file_name: 云存储上的文件名
  """
  accsess_key = "91a2a161a6d34156ace9106a76bc3fe2"
  secret_key = "NDhhNDM2MDYtMzQ4Yy00YjQ5LWJjYzEtYzQxN2YxNTVlYzFm"
  if (is_sgp_env):
    endpoint_url = "http://bs3-sgp.corp.kuaishou.com" 
    if (online):
      endpoint_url = "http://bs3-sgp.internal"  # sgp 线上环境
    s3_client_config = Config(
      s3={'addressing_style': 'path'},
      region_name='sgp'
    )
    bucket = "oversea-module-file-list"
    client = boto3.client("s3", 
                          endpoint_url=endpoint_url, 
                          config=s3_client_config,
                          aws_access_key_id=accsess_key, 
                          aws_secret_access_key=secret_key)
  else:
    # 默认是这个环境!!!
    endpoint_url = "http://bs3-hb1.corp.kuaishou.com"
    if (online):
      endpoint_url = "http://bs3-hb1.internal"   # hb1 线上环境
    s3_client_config = Config(
      s3={'addressing_style': 'path'},
      region_name='hb1'
    )
    bucket = "oversea-dragon-viz"
    client = boto3.client("s3", 
                          endpoint_url=endpoint_url, 
                          config=s3_client_config,
                          aws_access_key_id=accsess_key, 
                          aws_secret_access_key=secret_key)
  #https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html#S3.Client.put_object
  res = client.put_object(Bucket=bucket, Body=file_content, Key=blob_file_name)
