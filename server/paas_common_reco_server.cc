#include <algorithm>
#include <memory>

#include "base/time/timestamp.h"
#include "dragon/src/common_reco_server.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/task_register.h"
#include "ks/common_reco/util/common.h"
#include "ks/reco_pub/reco/util/kcs.h"
#include "ks/serving_util/grpc/grpc_tcp_user_timeout_util.h"
#include "ks/serving_util/kess_server_register.h"
#include "learning/kuiba/base/paas_manager.h"
#include "serving_base/linux_system/linux_system.h"
#include "serving_base/thp_component/kthp.h"

DEFINE_bool(enable_kcs_auto_port, true, "fetch port from json file for cloud pod");
DEFINE_int32(web_server_port, 9080, "http server port");
DEFINE_int32(grpc_server_port, 9082, "grpc server port");
DEFINE_int32(grpc_thread_num, 0, "grpc thread num");
DEFINE_double(grpc_thread_num_per_core, 2.0, "grpc thread num per core");
DEFINE_int64(sample_timestamp, 1000000L * 3600L, "sample_timestamp");
DEFINE_string(dynamic_json_config_kconf_key, "", "kconf key for json config");
DEFINE_string(cloud_shard_config_filename, "SHARD_CONFIG.json", "cloud shard config json file name");
DEFINE_bool(enable_kthp_process, false, "enable kthp process");
DEFINE_bool(enable_baseline_cpu_num, false, "enable use baseline cpu number");
void startup_print() {
  LOG(INFO) << "startup print";
}

DRAGON_REGISTER_TASK("startup", startup_print)

int main(int argc, char *argv[]) {
  int64 starting_ts = base::GetTimestamp();
  base::InitApp(&argc, &argv, "dragonfly server");
  // 强制设置 grpc 超时时间（tcp)，解决 grpc bug (#15983)，默认是 1s
  ks::serving_util::grpc::GrpcTcpUserTimeoutUtil::SetupTcpTimeout();

  if (FLAGS_enable_kthp_process) {
    CHECK(serving_base::KTHP::Init()) << "The preparation for large-sized pages is failed.";
  }
  if (!FLAGS_dynamic_json_config_kconf_key.empty()) {
    ks::platform::GlobalHolder::SetupKconfJsonConfigListener(FLAGS_dynamic_json_config_kconf_key);
  }

  // 读取 server 配置
  auto config = ks::platform::GlobalHolder::GetDynamicJsonConfig();
  CHECK(config) << ", get null dynamic_json_config!";
  auto kess_config = config->Get(ks::platform::kKessConfigKey);
  CHECK(kess_config) << ", get null kess_config!";
  std::string kess_service = kess_config->GetString("service_name", "");

  // 读取分片配置
  std::string host_name = serving_base::GetHostName();
  auto paas_manager = std::make_unique<kuiba::KConfPaasManager>(kuiba::FLAGS_paas_kconf_key);
  paas_manager->MergeTo(host_name, config.get());
  CHECK(config);

  // 检查容器云环境下的分片配置
  base::FilePath cwd;
  base::file_util::GetCurrentDirectory(&cwd);
  LOG(INFO) << "current working dir: " << cwd.value();
  std::string cloud_shard_config_path =
      cwd.DirName().value() + "/config/" + FLAGS_cloud_shard_config_filename;
  LOG(INFO) << "detecting cloud shard config file: " << cloud_shard_config_path;
  if (base::file_util::PathExists(cloud_shard_config_path)) {
    auto cloud_paas_manager = std::make_unique<kuiba::CloudPaasManager>(cloud_shard_config_path);
    cloud_paas_manager->MergeTo(host_name, config.get());
  }

  VLOG(100) << "using merged dynamic json config:\n" << config->ToString(2);

  int shard_no = kess_config->GetInt("shard_no", 0);
  int shard_num = kess_config->GetInt("shard_num", 1);
  ks::platform::GlobalHolder::SetServiceShardNo(shard_no);
  ks::platform::GlobalHolder::SetServiceShardNum(shard_num);
  ks::platform::SetServiceName(kess_service);

  CHECK(serving_base::LinuxSystem::Init()) << ", init serving_base::LinuxSystem failed";
  int rpc_thread_num = FLAGS_grpc_thread_num;
  if (rpc_thread_num <= 0) {
    if (FLAGS_enable_baseline_cpu_num) {
      float baseline_core_num = serving_base::LinuxSystem::GetBaselineCpuCores();
      LOG(INFO) << "detected baseline cpu core num: " << baseline_core_num;
      rpc_thread_num = std::max<int>(1, static_cast<int>(baseline_core_num * FLAGS_grpc_thread_num_per_core));
    } else {
      int core_num = serving_base::LinuxSystem::GetCpuCores();
      LOG(INFO) << "detected cpu core num: " << core_num;
      rpc_thread_num = std::max<int>(1, core_num * FLAGS_grpc_thread_num_per_core);
    }
  }
  ks::platform::GlobalHolder::SetWorkerThreadNum(rpc_thread_num);
  ks::platform::TaskRegistrationManager::Instance().RunRegisteredTasks("startup");

  // 开启 server
  // 支持 kcs 自动分配端口
  int64 web_server_port = FLAGS_web_server_port;
  int64 grpc_server_port = FLAGS_grpc_server_port;
  if (FLAGS_enable_kcs_auto_port) {
    ks::reco::FetchPodPort(&web_server_port, &grpc_server_port);
  }
  CHECK(web_server_port > 0 && grpc_server_port > 0);
  ks::platform::GlobalHolder::SetWebServicePort(web_server_port);

  LOG(INFO) << "using grpc_server_port: " << grpc_server_port << ", web_server_port: " << web_server_port;
  ks::platform::CommonRecoServer common_reco_server;
  common_reco_server.InitPaasServer(kess_service, web_server_port, 10, rpc_thread_num, grpc_server_port);
  ks::platform::GlobalHolder::SetScheduler(common_reco_server.GetScheduler());

  int64 startup_time = base::GetTimestamp() - starting_ts;
  ks::infra::PerfUtil::IntervalLogStash(startup_time, ks::platform::kPerfNs, "startup_time",
                                        ks::platform::GlobalHolder::GetServiceIdentifier());
  LOG(INFO) << "common leaf server started, service name: "
            << ks::platform::GlobalHolder::GetServiceIdentifier()
            << ", time_cost: " << (startup_time / 1000000.0) << " seconds";
  ::google::FlushLogFiles(::google::INFO);

  // 等待 SIGTERM 信号，收到信号后，终止服务
  common_reco_server.StopPaasServer();
  ks::platform::TaskRegistrationManager::Instance().RunRegisteredTasks("cleanup");

  return 0;
}
