import os

ldflags = ["-rdynamic"]

if os.environ.get("COMMON_RECO_LEAF_LINK_MKL", "false") == "true":
  ldflags.append("-lmkl_rt")
if os.environ.get("COMMON_RECO_LEAF_LINK_PYTHON", "false") == "true":
  ldflags.append("-lpython3.6m")

if os.environ.get("COMMON_RECO_LEAF_LINK_FPGA", "false") == "true":
  ldflags.append("-lxilinxopencl -Wl,-Bdynamic -lboost_filesystem -L./teams/hetero/third_party/xilinx/OpenCL/lib")
  ldflags.append("-lgmp -lIp_floating_point_v7_0_bitacc_cmodel -Lteams/hetero/third_party/xilinx/ap/lnx64/tools/fpo_v7_0/")

# 各业务添加自己的 link 库
extra_processor_str = os.environ.get("COMMON_RECO_LEAF_EXTRA_PROCESSORS", "")
extra_processor_str = os.environ.get("DRAGON_EXT", extra_processor_str)
target_extra_processors = set(extra_processor_str.split())
link_gomp = set(["tdm_server", "live_cluster"]) & target_extra_processors
if link_gomp:
  ldflags.append("-lgomp")

# ---------------------------------
# runnable binary
# ---------------------------------
binary_target_str = "server"
binary_target_str = os.environ.get("COMMON_LEAF_BINARY_TARGET", binary_target_str)
binary_target_str = os.environ.get("DRAGON_BINARY_TARGET", binary_target_str)
binary_targets = set(binary_target_str.split())

if "pywrap" in binary_targets or os.environ.get("ENABLE_COMMON_LEAF_PYTHON_WRAPPER", "false") == "true":
  cc_pyext(
    name = "common_reco_pipeline_executor_pywrap",
    srcs = [
      "common_reco_pipeline_executor_pywrap.cc",
    ],
    cppflags = [
      "-Ithird_party/prebuilt/include/python3.6m/",
      "-std=gnu++17",
    ],
    deps = [
      "//dragon/src/BUILD:common_reco_pipeline_executor",
      "//dragon/src/BUILD:common_reco_redis_mocker",
      "//dragon/src/BUILD:common_reco_rpc_mocker",
      "//dragon/src/BUILD:common_reco_kafka_mocker",
      "//third_party/gflags/BUILD:gflags",
      "//third_party/pybind11/BUILD:pybind11",
    ],
    fix_pyext_link_all_symbols=True,
  )

if "jnawrap" in binary_targets or os.environ.get("ENABLE_COMMON_LEAF_JNA_WRAPPER", "false") == "true":
  cc_pyext(
    name = "common_reco_pipeline_executor_jnawrap",
    srcs = [
      "common_reco_pipeline_executor_jnawrap.cc",
    ],
    deps = [
      "//dragon/src/BUILD:arrow_module",
    ] + (["//infra/protobuf-tracker/BUILD:pb_tracker"] if os.getenv("ENABLE_PB_TRACKER", "False").lower() == "true" else []),
    cppflags = [
      "-Wno-unknown-warning-option",
      "-std=gnu++17",
    ],
    fix_pyext_link_all_symbols=True,
  )

if "server" in binary_targets:
  cppflags = []
  if os.getenv("ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA", "False").lower() == "true":
    cppflags = cppflags + ["-DDRAGON_ENABLE_DIRECT_ACCESS_PARENT_FLOW_DATA"]
  cc_binary(
    name = "paas_common_reco_leaf",
    srcs = [
      "paas_common_reco_server.cc",
    ],
    deps = [
      "//ks/serving_util/BUILD:kess_helper",
      "//ks/reco_proto/rodis_v2_proto/BUILD:rodis_v2_proto",
      "//ks/reco_pub/reco/util/BUILD:util",
      "//base/time/BUILD:time",
      "//learning/kuiba/base/BUILD:base",
      "//dragon/src/BUILD:framework",
      "//serving_base/utility/BUILD:signal",
      "//infra/traffic_record/BUILD:grpc_traffic_record",
      "//infra/kess_grpc-v1100/BUILD:kess-framework",
      "//infra/kess_grpc-v1100/BUILD:kess-brpc",
      "//infra/kess_grpc-v1100/BUILD:kess-krpc",
      "//serving_base/linux_system/BUILD:linux_system",
      "//serving_base/thp_component/BUILD:kthp",
    ],
    ldflags = ldflags,
    cppflags = cppflags + ["-std=gnu++17"],
  )

if "runner" in binary_targets:
  cc_binary(
    name = "paas_common_reco_runner",
    srcs = [
      "paas_common_reco_runner.cc",
    ],
    deps = [
      "//ks/reco_pub/reco/util/BUILD:util",
      "//ks/serving_util/BUILD:serving_util",
      "//learning/kuiba/base/BUILD:base",
      "//base/time/BUILD:time",
      "//serving_base/server_base/BUILD:server_base",
      "//third_party/boost/BUILD:boost",
      "//dragon/src/BUILD:common_reco_pipeline_executor",
      "//dragon/src/BUILD:framework_base",
      "//serving_base/utility/BUILD:signal",
      "//infra/kess_grpc-v1100/BUILD:kess-rpc",
    ],
    ldflags = ldflags + ["-static-libgcc"],
    cppflags = [
      "-Wno-unknown-warning-option",
      "-std=gnu++17",
    ],
  )

if "retrieve" in binary_targets:
  cc_binary(
    name = "paas_common_reco_retrieve",
    srcs = [
      "paas_common_reco_retrieve.cc",
    ],
    deps = [
      "//ks/reco_pub/reco/util/BUILD:util",
      "//ks/common_reco/util/BUILD:util",
      "//ks/serving_util/BUILD:serving_util",
      "//learning/kuiba/base/BUILD:base",
      "//base/time/BUILD:time",
      "//dragon/src/BUILD:framework",
      "//serving_base/utility/BUILD:signal",
      "//infra/traffic_record/BUILD:grpc_traffic_record",
      "//serving_base/thp_component/BUILD:kthp",
    ],
    ldflags = ldflags,
    cppflags = [
      "-std=gnu++17",
    ],
  )
