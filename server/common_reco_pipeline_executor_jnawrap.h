#pragma once
#include "base/common/gflags.h"
#include "dragon/src/module/arrow/arrow_c_interface.h"

DECLARE_string(kess_caller_group);

extern "C" {

// 进程初始化函数, 只能调用一次, 调用后才可以使用其他接口
DRAGON_ARROW_EXPORT bool CommonRecoLeafPipelineExecutorJnaInit(const char *flag_file_contents);

// 设置进程的 id, 用于打点和监控, 非线程安全
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineSetServiceIdentifier(const char *identifier);

// 创建 executor, 线程安全
// NOTE: 返回的 executor 只能被创建的线程拥有, 不能转移给其他线程
// NOTE: 使用完后, 请调用 DeleteCommonRecoLeafPipelineExecutor, 否则内存泄漏
DRAGON_ARROW_EXPORT void *NewCommonRecoLeafPipelineExecutor(const char *json_content);

// 销毁 executor
DRAGON_ARROW_EXPORT bool DeleteCommonRecoLeafPipelineExecutor(void *executor);

// 设置 executor int64 类型的状态
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineExecutorSetInt(void *executor, const char *name, int64_t val);

// 从 executor 查询 int64 类型的状态
DRAGON_ARROW_EXPORT struct OptionalInt64 CommonRecoLeafPipelineExecutorGetInt(void *executor,
                                                                                 const char *name);

// 设置 executor string 类型的状态, string 会被拷贝一份
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineExecutorSetString(void *executor, const char *name,
                                                                 const char *val);

// 从 executor 查询 string 类型的状态, string 不会被拷贝
DRAGON_ARROW_EXPORT const char *CommonRecoLeafPipelineExecutorGetString(void *executor, const char *name);

// 设置 executor 指针类型的状态
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineExecutorSetPtrCommonAttr(void *executor, const char *name,
                                                                        void *ptr);

// 从 executor 查询指针类型的状态
DRAGON_ARROW_EXPORT void *CommonRecoLeafPipelineExecutorGetPtrCommonAttr(void *executor, const char *name);

// 执行 pipeline
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineExecutorRun(void *executor, const char *pipeline_name);

// 批式执行, pipeline 列表由 NewCommonRecoLeafPipelineExecutor 的初始化 json 指定,
// 内部将 request 的 RecordBatch 按照 context 拆成多个 RecordBatch 一次顺序执行 pipeline 列表,
// 最后将结果 concat 后通过 response 返回
// NOTE: 执行 CommonRecoLeafPipelineExecutorRunBatch 后, 之前的 response 失效, 确保消费完结果后,
//  才能再次调用该方法
// NOTE: request 和 response 只能被拥有 executor 的线程所拥有, 在线程内可以复用
DRAGON_ARROW_EXPORT bool CommonRecoLeafPipelineExecutorRunBatch(void *executor,
                                                                const RunBatchRequest *request,
                                                                RunBatchResponse *response);

// NOTE: 清理 executor 内部维护的状态
DRAGON_ARROW_EXPORT void CommonRecoLeafPipelineExecutorReset(void *executor);

}  // end extern "C"

#undef DRAGON_ARROW_EXPORT
