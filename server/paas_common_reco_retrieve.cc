#include <iostream>
#include <memory>

#include "base/time/timestamp.h"
#include "dragon/src/common_reco_server.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/processor/common/retriever/common_reco_local_ann_retriever.h"
#include "dragon/src/util/common_util.h"
#include "ks/common_reco/util/common.h"
#include "ks/reco_pub/reco/util/kcs.h"
#include "ks/serving_util/grpc/grpc_tcp_user_timeout_util.h"
#include "ks/serving_util/kess_server_register.h"
#include "learning/kuiba/base/paas_manager.h"
#include "serving_base/thp_component/kthp.h"
#include "serving_base/utility/cpu_hierarchy.h"

DEFINE_int32(web_server_port, 9080, "http server port");
DEFINE_int32(grpc_server_port, 9082, "grpc server port");
DEFINE_int32(grpc_thread_num, 0, "grpc thread num");
DEFINE_int64(sample_timestamp, 1000000L * 3600L, "sample_timestamp");
DEFINE_string(ann_config_key, "ann_config", "ann config key in config");
DEFINE_string(dynamic_json_config_kconf_key, "", "kconf key for json config");
DEFINE_double(grpc_thread_num_per_core, 2.0, "grpc thread num per core");
DEFINE_bool(enable_kthp_process, false, "enable kthp process");

int main(int argc, char *argv[]) {
  int64 starting_ts = base::GetTimestamp();
  base::InitApp(&argc, &argv, "dragonfly retrieve server");
  auto host = serving_base::GetHostName();
  // 强制设置 grpc 超时时间（tcp)，解决 grpc bug (#15983)，默认是 1s
  ks::serving_util::grpc::GrpcTcpUserTimeoutUtil::SetupTcpTimeout();

  if (FLAGS_enable_kthp_process) {
    CHECK(serving_base::KTHP::Init()) << "The preparation for large-sized pages is failed.";
  }
  if (!FLAGS_dynamic_json_config_kconf_key.empty()) {
    ks::platform::GlobalHolder::SetupKconfJsonConfigListener(FLAGS_dynamic_json_config_kconf_key);
  }

  // 读取 server 配置
  auto config = ks::platform::GlobalHolder::GetDynamicJsonConfig();
  CHECK(config) << ", get null dynamic_json_config!";
  // 读取分片配置
  auto paas_manager = std::make_unique<kuiba::KConfPaasManager>(kuiba::FLAGS_paas_kconf_key);
  paas_manager->MergeTo(host, config.get());
  CHECK(config);
  VLOG(100) << "using merged dynamic json config:\n" << config->ToString(2);

  auto kess_config = config->Get(ks::platform::kKessConfigKey);
  CHECK(kess_config);
  std::string kess_service = kess_config->GetString("service_name", "");
  ks::platform::SetServiceName(kess_service);

  int shard_no = kess_config->GetInt("shard_no", 0);
  int shard_num = kess_config->GetInt("shard_num", 1);
  ks::platform::GlobalHolder::SetServiceShardNo(shard_no);
  ks::platform::GlobalHolder::SetServiceShardNum(shard_num);

  auto local_ann_conf = config->Get(FLAGS_ann_config_key);

  // NOTE(yangtao03): 暂时还没有统一的 Run(), 先放在这
  // if (nullptr != local_ann_conf) {
  //   ks::platform::CommonRecoAnnRetrieve::Singleton()->LoadAnn(
  //       local_ann_conf, ks::platform::GlobalHolder::GetServiceShardNo(),
  //       ks::platform::GlobalHolder::GetServiceShardNum());
  //   ks::platform::CommonRecoAnnRetrieve::Singleton()->Run();
  // }

  int rpc_thread_num = FLAGS_grpc_thread_num;
  if (rpc_thread_num <= 0) {
    int core_num = std::thread::hardware_concurrency();
    if (core_num <= 0) {
      // NOTE(fangjianbing): serving_base::GetCpuCoreNum() 有点不准, 实测在 64 核机器下返回的值是 32
      core_num = serving_base::GetCpuCoreNum();
    }
    rpc_thread_num = std::max<int>(1, core_num * FLAGS_grpc_thread_num_per_core);
  }
  ks::platform::GlobalHolder::SetWorkerThreadNum(rpc_thread_num);

  // 开启 server
  // 支持 kcs 自动分配端口
  int64 web_server_port = FLAGS_web_server_port;
  int64 grpc_server_port = FLAGS_grpc_server_port;
  ks::reco::FetchPodPort(&web_server_port, &grpc_server_port);
  CHECK(web_server_port > 0 && grpc_server_port > 0);
  ks::platform::GlobalHolder::SetWebServicePort(web_server_port);

  LOG(INFO) << "using grpc_server_port: " << grpc_server_port << ", web_server_port: " << web_server_port;
  ks::platform::CommonRecoServer common_reco_server;
  common_reco_server.InitPaasServer(kess_service, web_server_port, 10, rpc_thread_num, grpc_server_port);

  // ks::platform::CommonRecoAnnRetrieve::Singleton()->WaitAvailable();

  int64 startup_time = base::GetTimestamp() - starting_ts;
  ks::infra::PerfUtil::IntervalLogStash(startup_time, ks::platform::kPerfNs, "startup_time",
                                        ks::platform::GlobalHolder::GetServiceIdentifier());
  LOG(INFO) << "common reco retrieve server started, service name: "
            << ks::platform::GlobalHolder::GetServiceIdentifier()
            << ", time_cost: " << (startup_time / 1000000.0) << " seconds";
  ::google::FlushLogFiles(::google::INFO);

  // 等待 SIGTERM 信号，收到信号后，终止服务
  common_reco_server.StopPaasServer();
  ks::platform::CommonRecoAnnRetrieve::Singleton()->StopAndWait();

  return 0;
}
