#include <functional>

#include "Python.h"
#include "dragon/src/module/common_reco_kafka_mocker.h"
#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/module/common_reco_redis_mocker.h"
#include "dragon/src/module/common_reco_rpc_mocker.h"

#include "gflags/gflags.h"
#include "pybind11/pybind11.h"
#include "pybind11/stl.h"

namespace py = pybind11;
using namespace py::literals;  // NOLINT

PYBIND11_MODULE(common_reco_pipeline_executor_pywrap, m) {
  // default flags
  FLAGS_logtostderr = true;

  // passing flagfile by environment variable FLAGS_flagfile
  std::vector<char *> args = {const_cast<char *>(""), const_cast<char *>("--tryfromenv"),
                              const_cast<char *>("flagfile")};
  int argc = args.size();
  char **argv = args.data();
  gflags::ParseCommandLineNonHelpFlags(&argc, &argv, false);

  // initialize glog
  google::InitGoogleLogging("");
  google::InstallFailureSignalHandler();

  py::class_<ks::platform::CommonRecoLeafRpcMocker>(m, "CommonRecoLeafRpcMocker")
      .def(py::init<>())
      .def("mock_rpc_response", &ks::platform::CommonRecoLeafRpcMocker::MockRpcResponse, "mock rpc response",
           "service_name"_a, "method_name"_a = "", "shard"_a = "", "response_json_str"_a);

  py::class_<ks::platform::CommonRecoLeafRedisMocker>(m, "CommonRecoLeafRedisMocker")
      .def(py::init<>())
      .def("mock_redis_response", &ks::platform::CommonRecoLeafRedisMocker::MockRedisResponse,
           "mock redis response", "cluster_names"_a, "responses"_a);

  py::class_<ks::platform::CommonRecoLeafKafkaMocker>(m, "CommonRecoLeafKafkaMocker")
      .def(py::init<>())
      .def("mock_kafka_message", &ks::platform::CommonRecoLeafKafkaMocker::MockKafkaMessage,
           "mock kafka message", "topic_name"_a, "value"_a = "");

  m.def("set_service_name", &ks::platform::CommonRecoLeafPipelineExecutor::SetServiceIdentifier,
        "设置服务名");
  m.def(
      "set_gflag",
      [](const std::string &flag, const std::string &value) -> std::string {
        return gflags::SetCommandLineOption(flag.data(), value.data());
      },
      "设置 gflag 配置", "flag"_a, "value"_a);

  py::class_<ks::platform::CommonRecoLeafPipelineExecutor>(m, "CommonRecoLeafPipelineExecutor")
      .def(py::init<const std::string &, bool>())
      .def(
          "set_gflag",
          [](const ks::platform::CommonRecoLeafPipelineExecutor &exec, const std::string &flag,
             const std::string &value) -> std::string {
            return gflags::SetCommandLineOption(flag.data(), value.data());
          },
          "设置 gflag 配置", "flag"_a, "value"_a)
      .def("add_item", &ks::platform::CommonRecoLeafPipelineExecutor::AddItem, "创建一个新 item",
           "item_key"_a, "reason"_a = 0, "score"_a = 0.0)
      .def("add_item_by_type", &ks::platform::CommonRecoLeafPipelineExecutor::AddItemByType,
           "根据指定的 type 创建一个新 item", "item_type"_a, "item_id"_a, "reason"_a = 0, "score"_a = 0.0)
      .def("new_item", &ks::platform::CommonRecoLeafPipelineExecutor::NewItem,
           "创建一个新 item，但不加入到 items", "item_key"_a, "reason"_a = 0, "score"_a = 0.0)
      .def("new_item_by_type", &ks::platform::CommonRecoLeafPipelineExecutor::NewItemByType,
           "根据指定的 type 创建一个新 item，但不加入到 items", "item_type"_a, "item_id"_a, "reason"_a = 0,
           "score"_a = 0.0)
      .def_property("user_id", &ks::platform::CommonRecoLeafPipelineExecutor::GetUserId,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetUserId)
      .def_property("device_id", &ks::platform::CommonRecoLeafPipelineExecutor::GetDeviceId,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetDeviceId)
      .def_property("request_time", &ks::platform::CommonRecoLeafPipelineExecutor::GetRequestTime,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetRequestTime)
      .def_property("request_num", &ks::platform::CommonRecoLeafPipelineExecutor::GetRequestNum,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetRequestNum)
      .def_property("request_type", &ks::platform::CommonRecoLeafPipelineExecutor::GetRequestType,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetRequestType)
      .def_property("browse_set", &ks::platform::CommonRecoLeafPipelineExecutor::GetBrowseSet,
                    &ks::platform::CommonRecoLeafPipelineExecutor::SetBrowseSet)
      .def("run", &ks::platform::CommonRecoLeafPipelineExecutor::Run, "执行指定的Pipeline", "name"_a)
      .def("reset", &ks::platform::CommonRecoLeafPipelineExecutor::Reset, "清空数据")
      .def("run_async", &ks::platform::CommonRecoLeafPipelineExecutor::RunAsync, "后台执行指定的Pipeline",
           "name"_a)
      .def("wait", &ks::platform::CommonRecoLeafPipelineExecutor::Wait, "等待后台执行的Pipeline结束",
           "timeout_ms"_a = -1)
      .def_property_readonly("item_keys", &ks::platform::CommonRecoLeafPipelineExecutor::GetItemKeyList)
      .def_property_readonly("items", &ks::platform::CommonRecoLeafPipelineExecutor::GetItemList)
      .def("set_main_table", &ks::platform::CommonRecoLeafPipelineExecutor::SetMainTable,
           "切换当前上下文的 table", "table_name"_a = "")
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetInt)
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetDouble)
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetString)
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetIntList)
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetDoubleList)
      .def("__setitem__", &ks::platform::CommonRecoLeafPipelineExecutor::SetStringList)
      .def("__getitem__",
           [](const ks::platform::CommonRecoLeafPipelineExecutor &exec,
              const std::string &name) -> py::object {
             if (auto val = exec.GetInt(name)) {
               return py::int_(*val);
             } else if (auto val = exec.GetDouble(name)) {
               return py::float_(*val);
             } else if (auto val = exec.GetString(name)) {
               return py::str(val->data(), val->size());
             } else if (auto val = exec.GetIntList(name)) {
               py::list lst;
               for (auto v : *val) {
                 lst.append(py::int_(v));
               }
               return lst;
             } else if (auto val = exec.GetDoubleList(name)) {
               py::list lst;
               for (auto v : *val) {
                 lst.append(py::float_(v));
               }
               return lst;
             } else if (auto val = exec.GetStringList(name)) {
               py::list lst;
               for (auto sv : *val) {
                 lst.append(py::str(sv.data(), sv.size()));
               }
               return lst;
             } else if (auto *p = exec.GetProtoMessagePtrAttr<::google::protobuf::Message>(name)) {
               std::string info = "PB <" + p->GetTypeName() + "> " + p->ShortDebugString();
               return py::str(info);
             } else if (exec.HasAttr(name)) {
               return py::str(exec.GetAttrDebugString(name));
             } else {
               return py::none();
             }
           })
      .def(
          "get_bytes",
          [](const ks::platform::CommonRecoLeafPipelineExecutor &exec,
             const std::string &name) -> py::object {
            if (auto val = exec.GetString(name)) {
              return py::bytes(val->data(), val->size());
            } else {
              return py::none();
            }
          },
          "用 bytes 格式获取 string 类型的 attr 值")
      .def(
          "get_bytes_list",
          [](const ks::platform::CommonRecoLeafPipelineExecutor &exec,
             const std::string &name) -> py::object {
            if (auto val = exec.GetStringList(name)) {
              py::list lst;
              for (auto sv : *val) {
                lst.append(py::bytes(sv.data(), sv.size()));
              }
              return lst;
            } else {
              return py::none();
            }
          },
          "用 list[bytes] 格式获取 string_list 类型的 attr 值");

  py::class_<ks::platform::CommonRecoLeafPipelineExecutor::Item>(m, "CommonRecoLeafPipelineExecutor_Item")
      .def_property_readonly("item_key", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetItemKey)
      .def_property_readonly("item_id", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetItemId)
      .def_property_readonly("item_type", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetItemType)
      .def_property_readonly("score", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetScore)
      .def_property_readonly("reason", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetReason)
      .def("__str__", &ks::platform::CommonRecoLeafPipelineExecutor::Item::GetItemInfo)
      .def("__setitem__", py::overload_cast<const std::string &, int64_t>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetInt))
      .def("__setitem__", py::overload_cast<const std::string &, double>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetDouble))
      .def("__setitem__", py::overload_cast<const std::string &, std::string>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetString))
      .def("__setitem__", py::overload_cast<const std::string &, std::vector<int64_t>>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetIntList))
      .def("__setitem__", py::overload_cast<const std::string &, std::vector<double>>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetDoubleList))
      .def("__setitem__", py::overload_cast<const std::string &, std::vector<std::string>>(
                              &ks::platform::CommonRecoLeafPipelineExecutor::Item::SetStringList))
      .def("__getitem__",
           [](const ks::platform::CommonRecoLeafPipelineExecutor::Item &item,
              const std::string &name) -> py::object {
             if (auto val = item.GetInt(name)) {
               return py::int_(*val);
             } else if (auto val = item.GetDouble(name)) {
               return py::float_(*val);
             } else if (auto val = item.GetString(name)) {
               return py::str(val->data(), val->size());
             } else if (auto val = item.GetIntList(name)) {
               py::list lst;
               for (auto v : *val) {
                 lst.append(py::int_(v));
               }
               return lst;
             } else if (auto val = item.GetDoubleList(name)) {
               py::list lst;
               for (auto v : *val) {
                 lst.append(py::float_(v));
               }
               return lst;
             } else if (auto val = item.GetStringList(name)) {
               py::list lst;
               for (auto sv : *val) {
                 lst.append(py::str(sv.data(), sv.size()));
               }
               return lst;
             } else if (auto *p = item.GetProtoMessagePtrAttr<::google::protobuf::Message>(name)) {
               std::string info = "PB <" + p->GetTypeName() + "> " + p->ShortDebugString();
               return py::str(info);
             } else if (item.HasAttr(name)) {
               return py::str(item.GetAttrDebugString(name));
             } else {
               return py::none();
             }
           })
      .def(
          "get_bytes",
          [](const ks::platform::CommonRecoLeafPipelineExecutor::Item &item,
             const std::string &name) -> py::object {
            if (auto val = item.GetString(name)) {
              return py::bytes(val->data(), val->size());
            } else {
              return py::none();
            }
          },
          "用 bytes 格式获取 string 类型的 attr 值")
      .def(
          "get_bytes_list",
          [](const ks::platform::CommonRecoLeafPipelineExecutor::Item &item,
             const std::string &name) -> py::object {
            if (auto val = item.GetStringList(name)) {
              py::list lst;
              for (auto sv : *val) {
                lst.append(py::bytes(sv.data(), sv.size()));
              }
              return lst;
            } else {
              return py::none();
            }
          },
          "用 list[bytes] 格式获取 string_list 类型的 attr 值");
}
