#include "dragon/server/common_reco_pipeline_executor_jnawrap.h"

#include <mutex>
#include <string>
#include <vector>

#include "base/common/gflags.h"
#include "base/common/logging.h"
#include "dragon/src/module/arrow/arrow_record_batch_executor.h"
#include "dragon/src/util/common_util.h"
#include "kess/rpc/perf.h"
#include "serving_base/jansson/json.h"
#include "serving_base/utility/system_util.h"

using ks::platform::ArrowRecordBatchExecutor;
DEFINE_string(kess_caller_group, "dragonfly_jna", "perf caller gronp name");

std::once_flag &GetInitFlag() {
  static std::once_flag g_init_flag;
  return g_init_flag;
}

extern "C" {

bool CommonRecoLeafPipelineExecutorJnaInit(const char *flag_file_contents) {
  static bool success = false;
  std::call_once(GetInitFlag(), [flag_file_contents]() {
    // default flags
    FLAGS_logtostderr = true;

    if (flag_file_contents == nullptr) {
      // passing flagfile by environment variable FLAGS_flagfile
      std::vector<char *> args = {const_cast<char *>(""), const_cast<char *>("--tryfromenv"),
                                  const_cast<char *>("flagfile")};
      int argc = args.size();
      char **argv = args.data();
      google::ParseCommandLineNonHelpFlags(&argc, &argv, false);
    } else {
      google::ReadFlagsFromString(flag_file_contents, nullptr, /*errors_are_fatal=*/true);
    }
    // initialize glog
    google::InitGoogleLogging("");
    google::InstallFailureSignalHandler();
    LOG(INFO) << "flag_file_contents:\n" << (flag_file_contents ? std::string(flag_file_contents) : "");
    google::FlushLogFiles(google::INFO);
    success = true;
  });
  return success;
}

void CommonRecoLeafPipelineSetServiceIdentifier(const char *identifier) {
  ks::platform::GlobalHolder::SetServiceIdentifier(identifier);
  ks::kess::rpc::Perf::SetCaller({FLAGS_kess_caller_group, identifier, "", serving_base::GetHostName(), "0"});
}

void *NewCommonRecoLeafPipelineExecutor(const char *json_content) {
  std::string json_str(json_content);
  ArrowRecordBatchExecutor *executor = new ArrowRecordBatchExecutor(json_str);
  return executor;
}

bool DeleteCommonRecoLeafPipelineExecutor(void *executor) {
  delete static_cast<ArrowRecordBatchExecutor *>(executor);
  return true;
}

void CommonRecoLeafPipelineExecutorSetInt(void *executor, const char *name, int64_t val) {
  static_cast<ArrowRecordBatchExecutor *>(executor)->SetInt(name, val);
}

struct OptionalInt64 CommonRecoLeafPipelineExecutorGetInt(void *executor, const char *name) {
  absl::optional<int64> value = static_cast<ArrowRecordBatchExecutor *>(executor)->GetInt(name);
  struct OptionalInt64 res;
  res.has_value = value.has_value();
  if (res.has_value) {
    res.value = *value;
  }
  return res;
}

void CommonRecoLeafPipelineExecutorSetString(void *executor, const char *name, const char *val) {
  static_cast<ArrowRecordBatchExecutor *>(executor)->SetString(name, val);
}

const char *CommonRecoLeafPipelineExecutorGetString(void *executor, const char *name) {
  return static_cast<ArrowRecordBatchExecutor *>(executor)->GetString(name);
}

void CommonRecoLeafPipelineExecutorSetPtrCommonAttr(void *executor, const char *name, void *ptr) {
  static_cast<ArrowRecordBatchExecutor *>(executor)->SetPtrCommonAttr(name, static_cast<char *>(ptr));
}

void *CommonRecoLeafPipelineExecutorGetPtrCommonAttr(void *executor, const char *name) {
  return const_cast<void *>(static_cast<const void *>(
      static_cast<ArrowRecordBatchExecutor *>(executor)->GetPtrCommonAttr<char>(name)));
}

void CommonRecoLeafPipelineExecutorRun(void *executor, const char *pipeline_name) {
  static_cast<ArrowRecordBatchExecutor *>(executor)->Run(pipeline_name);
}

bool CommonRecoLeafPipelineExecutorRunBatch(void *executor, const RunBatchRequest *request,
                                            RunBatchResponse *response) {
  return static_cast<ArrowRecordBatchExecutor *>(executor)->RunBatch(request, response);
}

void CommonRecoLeafPipelineExecutorReset(void *executor) {
  static_cast<ArrowRecordBatchExecutor *>(executor)->Reset();
}

}  // end extern "C"
