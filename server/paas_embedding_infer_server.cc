#include <iostream>
#include <memory>

#include "base/time/time.h"
#include "base/time/timestamp.h"
#include "dragon/src/common_reco_server.h"
#include "dragon/src/core/common_reco_statics.h"
#include "dragon/src/util/common_util.h"
#include "ks/reco/bt_embedding_server/src/embedding_table.h"
#include "ks/reco_pub/reco/util/kcs.h"
#include "ks/serving_util/grpc/grpc_tcp_user_timeout_util.h"
#include "ks/serving_util/kess_grpc_helper.h"
#include "ks/serving_util/kess_server_register.h"
#include "learning/kuiba/base/paas_manager.h"
#include "serving_base/utility/cpu_hierarchy.h"

DEFINE_int32(web_server_port, 9080, "http server port");
DEFINE_int32(grpc_server_port, 9082, "grpc server port");
DEFINE_int32(grpc_thread_num, 0, "grpc thread num");
DEFINE_int64(sample_timestamp, 1000000L * 3600L, "sample_timestamp");
DEFINE_string(dynamic_json_config_kconf_key, "", "kconf key for json config");
DEFINE_string(embedding_table_config_key, "embedding_table", "embedding table json config");
DEFINE_bool(need_embedding_table, false, "load embedding table from btq");

void LoadEmbeddingTable(std::shared_ptr<base::Json> root_config, const std::string &service_name) {
  std::string host_name = serving_base::GetHostName();
  int shard_id = root_config->Get(ks::platform::kKessConfigKey)->GetInt("shard_no", -1);
  CHECK_GE(shard_id, 0) << ", invalid shard_id: " << shard_id;
  LOG(INFO) << "embedding shard id: " << shard_id;

  int shard_num = root_config->Get(ks::platform::kKessConfigKey)->GetInt("shard_num", 0);
  CHECK_GE(shard_num, 0) << ", invalid shard_num: " << shard_num;

  auto embed_json_config = root_config->Get(FLAGS_embedding_table_config_key);
  CHECK(embed_json_config) << ", get json config:" << FLAGS_embedding_table_config_key << " failed";
  LOG(INFO) << "using embedding config:\n" << embed_json_config->ToString(2);

  ks::reco::bt_embd_s::EmbeddingTable::Config embed_config;
  embed_config.queue_prefix = embed_json_config->GetString("queue_prefix");
  if (embed_json_config->Get("queue_suffixes") != nullptr) {
    embed_config.queue_suffixes =
        embed_json_config->Get("queue_suffixes")->GetString(base::IntToString(shard_id));
  } else {
    embed_config.queue_suffixes = "";
  }
  embed_config.read_slots = embed_json_config->GetString("read_slots");
  embed_config.sign_format = embed_json_config->GetString("sign_format", "kuiba");
  embed_config.shard_id = shard_id;
  embed_config.shard_num = shard_num;
  // embed_config.shard_num = embed_json_config->GetInt("shard_num", 16);
  embed_config.queue_shard_num = embed_json_config->GetInt("queue_shard_num", 16);
  embed_config.thread_num = embed_json_config->GetInt("thread_num", 10);
  embed_config.shard_offset = embed_json_config->GetInt("shard_offset", 0);
  embed_config.service_name = service_name;
  embed_config.force_expire_timet = embed_json_config->GetInt("force_expire_timet", -1);
  ks::reco::bt_embd_s::EmbeddingTable *embedding_table = ks::reco::bt_embd_s::EmbeddingTable::Instance();
  embedding_table->Initialize(embed_config);
  LOG(INFO) << " loading embedding table finished ...";
}

int main(int argc, char *argv[]) {
  int64 starting_ts = base::GetTimestamp();
  base::InitApp(&argc, &argv, "dragonfly embedding infer server");
  // 强制设置 grpc 超时时间（tcp)，解决 grpc bug (#15983)，默认是 1s
  ks::serving_util::grpc::GrpcTcpUserTimeoutUtil::SetupTcpTimeout();

  if (!FLAGS_dynamic_json_config_kconf_key.empty()) {
    ks::platform::GlobalHolder::SetupKconfJsonConfigListener(FLAGS_dynamic_json_config_kconf_key);
  }

  // 读取 server 配置
  auto config = ks::platform::GlobalHolder::GetDynamicJsonConfig();
  CHECK(config) << ", get null dynamic_json_config!";
  auto kess_config = config->Get(ks::platform::kKessConfigKey);
  CHECK(kess_config) << ", get null kess_config!";
  std::string kess_service = kess_config->GetString("service_name", "");

  // 读取分片配置
  std::string host_name = serving_base::GetHostName();
  auto paas_manager = std::make_unique<kuiba::KConfPaasManager>(kuiba::FLAGS_paas_kconf_key);
  paas_manager->MergeTo(host_name, config.get());
  CHECK(config);
  VLOG(100) << "using merged dynamic json config:\n" << config->ToString(2);

  int shard_no = kess_config->GetInt("shard_no", 0);
  int shard_num = kess_config->GetInt("shard_num", 1);
  ks::platform::GlobalHolder::SetServiceShardNo(shard_no);
  ks::platform::GlobalHolder::SetServiceShardNum(shard_num);

  int rpc_thread_num = FLAGS_grpc_thread_num;
  if (rpc_thread_num <= 0) {
    rpc_thread_num = std::max(1, serving_base::GetCpuCoreNum() * 3);
  }
  ks::platform::GlobalHolder::SetWorkerThreadNum(rpc_thread_num);

  if (FLAGS_need_embedding_table) {
    LoadEmbeddingTable(config, kess_service);
  }

  // 开启 server
  // 支持 kcs 自动分配端口
  int64 web_server_port = FLAGS_web_server_port;
  int64 grpc_server_port = FLAGS_grpc_server_port;
  ks::reco::FetchPodPort(&web_server_port, &grpc_server_port);
  CHECK(web_server_port > 0 && grpc_server_port > 0);
  ks::platform::GlobalHolder::SetWebServicePort(web_server_port);

  LOG(INFO) << "using grpc_server_port: " << grpc_server_port << ", web_server_port: " << web_server_port;
  ks::platform::CommonRecoServer common_reco_server;
  common_reco_server.InitPaasServer(kess_service, web_server_port, 10, rpc_thread_num, grpc_server_port);

  int64 running_ts = base::GetTimestamp();
  LOG(INFO) << "common reco embedding infer server started, service name: "
            << ks::platform::GlobalHolder::GetServiceIdentifier()
            << ", time_cost: " << (running_ts - starting_ts) / 1000000.0 << " seconds";
  ::google::FlushLogFiles(::google::INFO);

  // 等待 SIGTERM 信号，收到信号后，终止服务
  common_reco_server.StopPaasServer();

  return 0;
}
