#include <boost/noncopyable.hpp>
#include <boost/thread.hpp>

#include <atomic>

#include "base/time/timestamp.h"
#include "dragon/src/common_reco_web_service.h"
#include "dragon/src/core/sub_flow_thread_pool_manager.h"
#include "dragon/src/module/common_reco_pipeline_executor.h"
#include "dragon/src/util/common_util.h"
#include "dragon/src/util/global_thread_pool.h"
#include "kess/rpc/perf.h"
#include "ks/reco_pub/reco/util/kcs.h"
#include "ks/serving_util/dynamic_config.h"
#include "learning/kuiba/base/paas_manager.h"
#include "serving_base/server_base/server_status.h"
#include "serving_base/utility/cpu_hierarchy.h"
#include "serving_base/utility/diag_util.h"
#include "serving_base/utility/signal.h"

DEFINE_int32(web_server_port, 9080, "http server port");
DEFINE_int32(thread_num, 1, "runner thread num");
DEFINE_bool(enable_fast_stop, false, "raise SIGK<PERSON><PERSON> to kill server immediately");
DEFINE_string(dynamic_json_config_kconf_key, "", "kconf key for json config");
DEFINE_string(common_reco_runner_pipelines_key, "pipeline_to_run", "key for pipelines to run");
DEFINE_string(common_reco_runner_group_pipelines_key, "runner_pipeline_group",
              "key for multi runners pipeline");
DEFINE_string(common_reco_runner_service_identifier_key, "service_identifier", "key for service_identifier");
DEFINE_string(common_reco_runner_stop_key, "MESSAGE_END",
              "stop the pipeline when given common attr is larger than zero");
DEFINE_string(kess_caller_group, "zt_runner", "runner 不注册 kess，需要主动调用 SetCaller 设置主调信息");
DEFINE_string(cloud_shard_config_filename, "SHARD_CONFIG.json", "cloud shard config json file name");

class CommonRecoLeafPipelineMultiThreadRunner : private boost::noncopyable {
 public:
  explicit CommonRecoLeafPipelineMultiThreadRunner(const base::Json *config, int thread_num,
                                                   const std::vector<std::string> pipelines)
      : config_(CHECK_NOTNULL(config)), pipelines_(pipelines), thread_num_(thread_num), executor_ready_(0) {}

  void Start(std::function<void()> done_callback) {
    for (int i = 0; i < thread_num_; i++) {
      // create_thread 返回的 thread pointer 由 thread_group 内部持有，
      // thread_group 对象析构的时候会被自动释放。
      thread_group_.create_thread([this, done_callback]() mutable {
        Run(std::move(done_callback));
        CHECK(!done_callback) << "Callback must be moved away!";
      });
    }

    int ready;
    while ((ready = executor_ready_.load()) < thread_num_) {
      LOG(INFO) << "Waiting for " << thread_num_ << " executors ready, currently only " << ready;
      std::this_thread::sleep_for(std::chrono::seconds(1));
    }
  }

  ~CommonRecoLeafPipelineMultiThreadRunner() {
    thread_group_.interrupt_all();
    thread_group_.join_all();
  }

  int GetThreadNum() const {
    return thread_num_;
  }

 private:
  void Run(std::function<void()> done_callback) {
    CHECK(thread_group_.is_this_thread_in());
    CHECK(boost::this_thread::interruption_enabled());

    LOG(INFO) << "CommonRecoLeafPipelineMultiThreadRunner thread created: " << boost::this_thread::get_id();

    auto *executor = NewExecutor();
    LOG(INFO) << "CommonRecoLeafPipelineMultiThreadRunner executor created: " << boost::this_thread::get_id();

    while (!boost::this_thread::interruption_requested()) {
      // 参考 server 输出线程数，有时候配置的不对, 需要打出来
      base::perfutil::PerfUtilWrapper::IntervalLogStash(ks::platform::GlobalHolder::GetWorkerThreadNum(),
                                                        ks::platform::kPerfNs, "rpc_thread_num",
                                                        ks::platform::GlobalHolder::GetServiceIdentifier());
      executor->Reset();
      for (const std::string &p : pipelines_) {
        executor->Run(p);
      }

      if (auto int_val = executor->GetInt(FLAGS_common_reco_runner_stop_key)) {
        if (*int_val > 0) {
          LOG(INFO) << "non-zero stop key[" << FLAGS_common_reco_runner_stop_key << "] found: " << *int_val;
          break;
        }
      }
      executor->OnExit(pipelines_);
    }
    LOG(INFO) << "CommonRecoLeafPipelineMultiThreadRunner thread exit: " << boost::this_thread::get_id();
    done_callback();
  }

  ks::platform::CommonRecoLeafPipelineExecutor *NewExecutor() {
    std::lock_guard<std::mutex> lock(executors_mutex_);
    executors_.emplace_back(std::make_unique<ks::platform::CommonRecoLeafPipelineExecutor>(config_));
    executor_ready_++;
    return executors_.back().get();
  }

 private:
  const base::Json *const config_;
  const std::vector<std::string> pipelines_;
  int thread_num_;
  boost::thread_group thread_group_;

  std::mutex executors_mutex_;
  std::vector<std::unique_ptr<ks::platform::CommonRecoLeafPipelineExecutor>> executors_;
  std::atomic_int executor_ready_;
};

std::vector<std::string> ParseStringArray(const base::Json *config) {
  std::vector<std::string> ret;
  for (auto *c : CHECK_NOTNULL(config)->array()) {
    ret.emplace_back(CHECK_NOTNULL(c)->StringValue());
    CHECK(!ret.back().empty());
  }
  return ret;
}

std::unique_ptr<CommonRecoLeafPipelineMultiThreadRunner> CreateOneRunner(const base::Json *root_config,
                                                                         const base::Json *config,
                                                                         int thread_num) {
  if (config && config->IsArray()) {
    return std::make_unique<CommonRecoLeafPipelineMultiThreadRunner>(
        root_config->Get("pipeline_manager_config"), thread_num, ParseStringArray(config));
  }
  return nullptr;
}

std::unique_ptr<CommonRecoLeafPipelineMultiThreadRunner> TryCreateSingleRunner(
    const base::Json *root_config) {
  static std::string config_key = "pipeline_to_run";
  auto runner_pipeline = root_config->Get(config_key);
  if (runner_pipeline) {
    LOG(INFO) << " start one runner " << config_key << ", thread: " << FLAGS_thread_num;
    return CreateOneRunner(root_config, runner_pipeline, FLAGS_thread_num);
  }
  return nullptr;
}

std::vector<std::unique_ptr<CommonRecoLeafPipelineMultiThreadRunner>> CreateRunnerGroup(
    const base::Json *root_config) {
  static std::string config_key = "runner_pipeline_group";
  std::vector<std::unique_ptr<CommonRecoLeafPipelineMultiThreadRunner>> runner_group;
  auto runner_pipeline_group = root_config->Get(config_key);

  CHECK(runner_pipeline_group && runner_pipeline_group->IsObject())
      << " json config[" << FLAGS_common_reco_runner_group_pipelines_key << "] error ...";

  auto core_num = serving_base::GetCpuCoreNum();
  auto pipeline_group_size = runner_pipeline_group->objects().size();
  int fallback_thread_num = pipeline_group_size > 0 ? core_num / pipeline_group_size : core_num;
  for (auto &iter : runner_pipeline_group->objects()) {
    auto pipeline_config = iter.second->Get("pipeline");
    auto thread_num = iter.second->GetInt("thread_num", -1);
    double core_num_thread_ratio = iter.second->GetNumber("core_num_thread_ratio", 0.0);
    if (thread_num > 0) {
      LOG(INFO) << "runner:" << iter.first << " set thread_num to " << thread_num;
    } else if (core_num_thread_ratio > 0) {
      thread_num = core_num_thread_ratio * core_num;
      LOG(INFO) << "runner:" << iter.first << " set thread_num by core_num:" << core_num << " * "
                << core_num_thread_ratio << " = " << thread_num;
    } else {
      thread_num = FLAGS_thread_num;
      LOG(INFO) << "runner:" << iter.first << " set thread_num by gflag:" << thread_num;
    }
    thread_num = thread_num > 0 ? thread_num : fallback_thread_num;
    auto runner = CreateOneRunner(root_config, pipeline_config, thread_num);
    CHECK(runner.get()) << " fail to start runner " << iter.first << ", thread:" << thread_num;
    runner_group.push_back(std::move(runner));
    LOG(INFO) << " start runner " << iter.first << ", thread:" << thread_num;
  }
  return std::move(runner_group);
}

int main(int argc, char *argv[]) {
  int64 starting_ts = base::GetTimestamp();
  base::InitApp(&argc, &argv, "dragonfly runner");

  if (!FLAGS_dynamic_json_config_kconf_key.empty()) {
    ks::platform::GlobalHolder::SetupKconfJsonConfigListener(FLAGS_dynamic_json_config_kconf_key);
  }

  auto config = ks::platform::GlobalHolder::GetDynamicJsonConfig();
  CHECK(config) << ", get null dynamic_json_config!";

  std::string host_name = serving_base::GetHostName();
  auto paas_manager = std::make_unique<kuiba::KConfPaasManager>(kuiba::FLAGS_paas_kconf_key);
  paas_manager->MergeTo(host_name, config.get());
  CHECK(config);

  // 检查容器云环境下的分片配置
  base::FilePath cwd;
  base::file_util::GetCurrentDirectory(&cwd);
  LOG(INFO) << "current working dir: " << cwd.value();
  std::string cloud_shard_config_path =
      cwd.DirName().value() + "/config/" + FLAGS_cloud_shard_config_filename;
  LOG(INFO) << "detecting cloud shard config file: " << cloud_shard_config_path;
  if (base::file_util::PathExists(cloud_shard_config_path)) {
    auto cloud_paas_manager = std::make_unique<kuiba::CloudPaasManager>(cloud_shard_config_path);
    cloud_paas_manager->MergeTo(host_name, config.get());
  }

  VLOG(100) << "using merged dynamic json config:\n" << config->ToString(2);

  // 读取分片配置
  auto kess_config = config->Get(ks::platform::kKessConfigKey);
  CHECK(kess_config) << ", get null kess_config!";
  int shard_no = kess_config->GetInt("shard_no", 0);
  int shard_num = kess_config->GetInt("shard_num", 1);
  ks::platform::GlobalHolder::SetServiceShardNo(shard_no);
  ks::platform::GlobalHolder::SetServiceShardNum(shard_num);

  const std::string service_identifier = config->GetString(FLAGS_common_reco_runner_service_identifier_key);
  CHECK(!service_identifier.empty()) << FLAGS_common_reco_runner_service_identifier_key << " is required";
  ks::platform::GlobalHolder::SetServiceIdentifier(service_identifier);

  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::STARTING);
  auto runner = TryCreateSingleRunner(config.get());
  auto runner_group = CreateRunnerGroup(config.get());

  {
    int total_main_worker_thread_num = 0;
    if (runner) {
      total_main_worker_thread_num += runner->GetThreadNum();
    }
    for (auto &runner : runner_group) {
      total_main_worker_thread_num += runner->GetThreadNum();
    }
    ks::platform::GlobalHolder::SetWorkerThreadNum(total_main_worker_thread_num);
  }
  if (FLAGS_parallel_init_pipeline_concurrency > 0) {
    CHECK(ks::platform::DualLevelThreadPool::GetInstance()->Initialize());
  }
  ks::platform::SubFlowThreadPoolManager::GetInstance()->Initialize();

  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::RUNNING);
  // start web server
  int64 web_server_port = FLAGS_web_server_port;
  int64 fake_grpc_server_port = 0;
  ks::reco::FetchPodPort(&web_server_port, &fake_grpc_server_port);
  CHECK_GT(web_server_port, 0);
  auto web_service = ks::platform::CommonRecoWebService::Instance();
  net::WebServer::Options web_server_option;
  web_server_option.port = web_server_port;
  web_server_option.backlog = 10;
  auto web_server = std::make_unique<net::WebServer>(web_server_option, web_service);
  web_server->Start();
  ks::platform::GlobalHolder::SetWebServicePort(web_server_port);
  LOG(INFO) << "web service started, thread_num: " << web_service->thread_num()
            << ", http port: " << web_server_port;

  ks::kess::rpc::Perf::SetCaller(
      {FLAGS_kess_caller_group, service_identifier, "", serving_base::GetHostName(), "0"});

  // quit handler
  serving_base::SignalCatcher::Initialize();
  auto on_quit = std::shared_ptr<char>(nullptr, [](void *) { serving_base::SignalCatcher::Shutdown(); });

  // start runner
  int64 startup_time = base::GetTimestamp() - starting_ts;
  if (runner) runner->Start([on_quit]() { LOG(INFO) << "Runner Quit!"; });
  for (auto &r : runner_group) r->Start([on_quit]() mutable { LOG(INFO) << "Runner Group Quit!"; });
  ks::infra::PerfUtil::IntervalLogStash(startup_time, ks::platform::kPerfNs, "startup_time",
                                        ks::platform::GlobalHolder::GetServiceIdentifier());
  LOG(INFO) << "common reco runner started, service name: "
            << ks::platform::GlobalHolder::GetServiceIdentifier()
            << ", time_cost: " << (startup_time / 1000000.0) << " seconds";

  // enable quit handler
  on_quit.reset();

  serving_base::SignalCatcher::WaitForSignal();

  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::STOPPING);
  LOG(INFO) << "stopping web_server.";
  ::google::FlushLogFiles(::google::INFO);
  web_server->Stop();
  LOG(INFO) << "web_server stopped.";
  ::google::FlushLogFiles(::google::INFO);

  bool enable_fast_stop = false;
#if KS_SANITIZE_ADDRESS == 1
  enable_fast_stop = false;
  if (FLAGS_enable_fast_stop) {
    LOG(INFO) << "FLAG enable_fast_stop will be ignored in diag build, no fast stop!";
    ::google::FlushLogFiles(::google::INFO);
  }
#else
  enable_fast_stop = FLAGS_enable_fast_stop;
#endif
  if (enable_fast_stop) {
    LOG(INFO) << "ENDING: fast stop enabled, raise SIGKILL to kill runner immediately!";
    ::google::FlushLogFiles(::google::INFO);
    raise(SIGKILL);
  }

  LOG(INFO) << "stopping runner";
  ::google::FlushLogFiles(::google::INFO);
  int64 stopping_ts = base::GetTimestamp();

  runner.reset();
  runner_group.clear();

  int64 stopped_ts = base::GetTimestamp();
  base::ServerStatus::Singleton()->SetStatusCode(base::ServerStatusCode::STOPPED);
  LOG(INFO) << "common reco runner stopped, time_cost: " << (stopped_ts - stopping_ts) / 1000.0 << " ms";
  ::google::FlushLogFiles(::google::INFO);

  return 0;
}
